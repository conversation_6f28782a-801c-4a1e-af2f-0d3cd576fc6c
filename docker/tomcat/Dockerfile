FROM registry.gitlab.com/plataformazw/docker-pacto/tomcat:8

RUN apt-get update && apt-get install -y cron \
    && rm -rf /var/lib/apt/lists/*

ENV APP_DIR="/usr/local/tomcat/webapps/ZillyonWeb"
ADD ZillyonWeb $APP_DIR

RUN rm -rf $APP_DIR/WEB-INF/lib/*jdk14-1.38.jar
RUN rm -rf $APP_DIR/WEB-INF/lib/*jdk14-138.jar

RUN mkdir /opt/robo
RUN mkdir /var/log/robo
ADD crontab/robo/*.sh /opt/robo/
RUN chmod +x /opt/robo/*.sh
RUN ln -s /opt/robo/*.sh /usr/local/bin
ADD crontab/crontab /etc/crontab
ADD keys/* /root/.ssh/

ENV CONTEXT=${CONTEXT:-"ZillyonWeb"}
ENV URL_DATABASE_OAMD=${URL_DATABASE_OAMD:-"************************************"}
ENV DEBUG=${DEBUG:-"true"}
ENV DEBUG_JDBC=${DEBUG_JDBC:-"false"}
ENV AMBIENTE_DESENVOLVIMENTO_TESTE=${AMBIENTE_DESENVOLVIMENTO_TESTE:-"true"}
ENV DISCOVERY_URL=${DISCOVERY_URL:-"http://discovery:8080"}

# Refatorar para o discovery
ENV URL_HTTPS_PLATAFORMA_PACTO=${URL_HTTPS_PLATAFORMA_PACTO:-"https://dev.pactosolucoes.com.br:8094"}
ENV URL_HTTP_PLATAFORMA_PACTO=${URL_HTTP_PLATAFORMA_PACTO:-"http://dev.pactosolucoes.com.br:8094"}
ENV URL_OAMD=${URL_OAMD:-"http://app.pactosolucoes.com.br/oamd"}
ENV URL_OAMD_SEGURA=${URL_OAMD_SEGURA:-"http://app.pactosolucoes.com.br/oamd"}
ENV SMTP_EMAIL_ROBO=${SMTP_EMAIL_ROBO:-"<EMAIL>"}
ENV SMTP_EMAIL_NOREPLY=${SMTP_EMAIL_NOREPLY:-"<EMAIL>"}
ENV SMTP_LOGIN_ROBO=${SMTP_LOGIN_ROBO:-"pactosolucoes2017"}
ENV SMTP_SENHA_ROBO=${SMTP_SENHA_ROBO:-"RlOHgUGw8905"}
ENV SMTP_SERVER_ROBO=${SMTP_SERVER_ROBO:-"smtplw.com.br"}
ENV SMTP_SERVER_ROBO_CONEXAO_SEGURA=${SMTP_SERVER_ROBO_CONEXAO_SEGURA:-"true"}
ENV SMTP_SERVER_ROBO_INICIAR_TLS=${SMTP_SERVER_ROBO_INICIAR_TLS:-"true"}
ENV URL_VENDAS_ONLINE=${URL_VENDAS_ONLINE:-"http://devi9.pactosolucoes.com.br:8189"}
ENV URL_BASE_OPTIN=${URL_BASE_OPTIN:-"https://vendas.online.sistemapacto.com.br"}
ENV MY_URL_UP_BASE=${MY_URL_UP_BASE:-"https://app.pactosolucoes.com.br/ucp"}
ENV URL_JENKINS=${URL_JENKINS:-"http://devzw.pactosolucoes.com.br:8782/jk"}
ENV URL_MAILING=${URL_MAILING:-"http://devzw.pactosolucoes.com.br:8781/app/ms"}
ENV URL_APLICACAO=${URL_APLICACAO:-"http://zw:8481/ZillyonWeb"}
ENV URL_NOTIFICAR=${URL_NOTIFICAR:-"http://zw:8481/ZillyonWeb"}
ENV URL_ZW_AUTO=${URL_ZW_AUTO:-"http://swarm3.pactosolucoes.com.br:8080/zw-auto"}
ENV URL_RECURSO_EMPRESA=${URL_RECURSO_EMPRESA:-"http://dev.pactosolucoes.com.br:8202"}
ENV USAR_URL_RECURSO_EMPRESA=${USAR_URL_RECURSO_EMPRESA:-"false"}
ENV HABILITAR_NICHO=${HABILITAR_NICHO:-"false"}
ENV HABILITAR_CACHE_INIT_NICHO=${HABILITAR_CACHE_INIT_NICHO:-"false"}
ENV VALIDADE_CACHE_NICHO_EM_MINUTOS=${VALIDADE_CACHE_NICHO_EM_MINUTOS:-"2880"}

ENV URL_API=${URL_API:-"http://swarm3.pactosolucoes.com.br:8010/API-ZillyonWeb"}
ENV URL_BI_MS=${URL_BI_MS:-"http://bi-ms:28091/bi-ms"}
ENV URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR=${URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR:-"false"}
ENV URL_SERV_NOTA_FISCAL=${URL_SERV_NOTA_FISCAL:-"http://naoTemHomologacao/servicoNotaFiscal"}
ENV ENABLE_MENU_ZW_UI=${ENABLE_MENU_ZW_UI:-"false"}
ENV TOKENS_ACESSO_API_CLIENTE=${TOKENS_ACESSO_API_CLIENTE:-"gP6pV2pS6lC8sY7nH6vG8tN4xT0vR9tU,teste,1"}
ENV PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA=${PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA:-"true"}
ENV TOKEN_MAIL_GUN=${TOKEN_MAIL_GUN:-"Xzhjdnmdendumdh"}
ENV HABILITA_MARKETING=${HABILITA_MARKETING:-"false"}
ENV HABILITA_CLUBE_DE_BENEFICIOS=${HABILITA_CLUBE_DE_BENEFICIOS:-"false"}
ENV TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS=${TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS:-"432000"}
ENV SERVIDOR_MEMCACHED=${SERVIDOR_MEMCACHED:-"DISABLED"}
ENV URL_MARKETING_MS=${URL_MARKETING_MS:-"https://ms1.pactosolucoes.com.br/marketing-ms"}
ENV DOMAIN_MAIL=${DOMAIN_MAIL:-"teste.con.br"}
ENV VALIDAR_VERSAO_BD=${VALIDAR_VERSAO_BD:-"true"}
ENV UTEIS_EMAIL_SEND=${UTEIS_EMAIL_SEND:-"true"}
ENV ZAW_URL_NOTF_ACESSO=${ZAW_URL_NOTF_ACESSO:-"http://zw:8080/ZillyonWeb/UpdateServlet"}
ENV CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO=${CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO:-"false"}
ENV TIPO_MIDIA=${TIPO_MIDIA:-"ZW_INTERNAL"}
ENV URL_FOTOS_NUVEM=${URL_FOTOS_NUVEM:-"zw-photos"}
ENV TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO=${TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO:-"60"}
ENV HABILITAR_NICHO=${HABILITAR_NICHO:-"true"}
ENV VERIFY_CONTROLLERS_AFTER_PHASE=${VERIFY_CONTROLLERS_AFTER_PHASE:-"false"}
ENV HABILITAR_FUNCIONALIDADES_BETA=${HABILITAR_FUNCIONALIDADES_BETA:-"true"}
ENV BUSCAR_CONHECIMENTO_UCP=${BUSCAR_CONHECIMENTO_UCP:-"true"}
ENV MAX_GPT=${MAX_GPT:-"true"}
ENV REDIRECT_URI_CONNECT_PAGBANK=${REDIRECT_URI_CONNECT_PAGBANK:-"https://zw74.pactosolucoes.com.br/api/prest/pagbank/authRedirect"}
ENV MOCK_ARAGORN_CARDS=${MOCK_ARAGORN_CARDS:-"false"}

ENV JAVA_OPTS="${JAVA_OPTS}:-Xms128m -Xmx1g -Xss256k -Duser.timezone=America/Sao_Paulo -Duser.language=pt -Duser.region=BR -Dpacto.service=zw"

ENV OTEL_EXPORTER_OTLP_ENDPOINT=${OTEL_EXPORTER_OTLP_ENDPOINT:-"http://jaeger:4317"}
ENV OTEL_SERVICE_NAME=${OTEL_SERVICE_NAME:-"zw-service"}
ENV ENABLE_OTEL_JAVA_AGENT=${ENABLE_OTEL_JAVA_AGENT:-"false"}

ADD ./certs/gd_bundle-g2-g1.crt $JAVA_HOME/lib/security/gd_bundle-g2-g1.crt
ADD ./certs/bbsandbox.crt $JAVA_HOME/lib/security/bbsandbox.crt
RUN cd $JAVA_HOME/lib/security/ \
    && keytool -import -alias gd_bundle-g2-g1 -file gd_bundle-g2-g1.crt -keystore cacerts -trustcacerts -storepass changeit
RUN cd $JAVA_HOME/lib/security/ \
    && keytool -import -alias bbsandbox -file bbsandbox.crt -keystore cacerts -trustcacerts -storepass changeit

ADD ./bin/* /usr/local/bin/
RUN chmod +x /usr/local/bin/*.sh


EXPOSE 8080
ENTRYPOINT ["/usr/bin/dumb-init","entrypoint.sh"]

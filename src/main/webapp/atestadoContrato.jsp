<%-- 
    Document   : atestadoContrato
    Created on : 03/08/2009, 09:21:27
    Author     : pedro
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
    .to-uper-case{
        text-transform: uppercase;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-lancar-um-atestado-medico/"/>
        <c:set var="titulo" scope="session" value="Atestado"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>

    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="form" styleClass="overflow-visible">
        <h:panelGrid columns="2"  width="100%">
            <h:panelGrid columns="1" cellpadding="0" style="margin-left: auto; margin-right: auto;">
                <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" />
                <h:outputText id="nomeSolicitanteAtestado" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{AtestadoContratoControle.atestadoContratoVO.contratoVO.pessoa.nome}"/>
                <br>
                <h:panelGroup styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">DETALHES DO CONTRATO</h:panelGroup>
                <rich:dataTable id="Atestado" width="100%" rowClasses="tablelistras textsmall" columnClasses="col-text-align-left" styleClass="tabelaSimplesCustom" headerClass="col-text-align-left"
                                value="#{AtestadoContratoControle.listaContratoVOs}" var="contrato">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_numeroContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_plano}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.plano.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"  value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_dataInicio}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaDe_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_dataTermino}"/>
                        </f:facet>
                        <h:outputText rendered="#{contrato.situacao == 'AT'}" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada_Apresentar}"/>
                        <h:outputText rendered="#{contrato.situacao == 'IN'}" styleClass="red texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada_Apresentar}*"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_valorBaseContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.valorBaseCalculo}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>
                <h:outputText  rendered="#{AtestadoContratoControle.atestadoContratoVO.contratoVencido}" id="msgContratoVencido" styleClass="fonteTrebuchet" style="color: red"  value="#{msg_aplic.prt_afastamentoContrato_Atestado_ContratoVencido}"/>
					<P> </P>
                <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-right:20px;margin-bottom:5px;padding:5px;">
                    <tr>
                        <td align="left" valign="top" >
                            <div class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">
                                OPERAÇÕES</p>
                            </div>
                            <div class="sep" ><img src="images/shim.gif"></div>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top">
                            <rich:dataTable id="listaContratoOperacao" width="100%" border="0" rows="5" cellspacing="0" cellpadding="2"
                                            columnClasses="col-text-align-left" styleClass="tabelaSimplesCustom" headerClass="col-text-align-left" value="#{AtestadoContratoControle.listaContratoOperacaoVOs}" var="contratoOperacao">
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_ContratoOperacao_tipoOperacao}"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contratoOperacao.tipoOperacao_Apresentar}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Data Início"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contratoOperacao.dataInicioEfetivacaoOperacao_Apresentar}">
                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                    </h:outputText>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Data Término"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contratoOperacao.dataFimEfetivacaoOperacao_Apresentar}">
                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                    </h:outputText>
                                </rich:column>
                            </rich:dataTable>
                            <rich:datascroller align="center" for="listaContratoOperacao" maxPages="10" id="scResultadoContratoOperacao"  />
                        </td>
                    </tr>
                </table>
                <h:panelGrid>
                    <div class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">
                        DADOS DO ATESTADO
                    </div>
                    <h:panelGrid columns="3" id="datas">
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="INÍCIO" />
                            <h:panelGroup styleClass="dateTimeCustom" layout="block" style="height: 40px;">
                                <rich:calendar id="dataInicio"
                                               value="#{AtestadoContratoControle.atestadoContratoVO.dataInicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               onchanged="validar_Data(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return validar_Data(this.id);"
                                               oninputchange="validar_Data(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               zindex="2"
                                               showWeeksBar="false" >
                                </rich:calendar>
                                <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                                <%--ajax function para fazer o binding das datas inicio e fim executando os javascripts antes de cada componente--%>
                                <a4j:jsFunction name="gerarPeriodoRetorno" action="#{AtestadoContratoControle.confirmarPermissaoAtestadoComAcesso}"
                                                reRender="datas,panelPeridoRetorno,panelMensagem,panelAutorizacaoFuncionalidade,panelBotoes" oncomplete="#{AtestadoContratoControle.mensagemNotificar}">
                                    <a4j:actionparam name="dtinicio" assignTo="#{AtestadoContratoControle.atestadoContratoVO.dataInicio}"/>
                                    <a4j:actionparam name="dtfim" assignTo="#{AtestadoContratoControle.atestadoContratoVO.dataTermino}"/>
                                    <a4j:actionparam name="numero" assignTo="#{AtestadoContratoControle.atestadoContratoVO.nrDiasASomar}"/>
                                </a4j:jsFunction>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="NR. DE DIAS"/>
                            <h:panelGroup layout="block" style="height: 38px;">
                                <h:inputText size="5" maxlength="3"
                                             styleClass="inputTextClean"
                                             value="#{AtestadoContratoControle.atestadoContratoVO.nrDiasASomar}"
                                             onblur="gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),document.getElementById('form:nrDias'));"
                                             id="nrDias">
                                </h:inputText>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="FIM" />
                                <h:panelGroup styleClass="dateTimeCustom" layout="block" style="height: 34px;">
                                    <rich:calendar id="dataTermino"
                                                   value="#{AtestadoContratoControle.atestadoContratoVO.dataTermino}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   onchanged="validar_Data(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return validar_Data(this.id);"
                                                   oninputchange="validar_Data(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false" >
                                    </rich:calendar>
                                </h:panelGroup>
                                <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="panelPeridoRetorno">
                    <h:panelGrid rendered="#{AtestadoContratoControle.apresentarPeriodoRetorno}">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" style="font-weight:bold;" value="PERÍODO DE RETORNO" />
                        <h:panelGroup>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DE" />
                            <rich:spacer width="10"/>
                            <h:outputText id="dataRetornoIni" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AtestadoContratoControle.atestadoContratoVO.dataInicioRetorno_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                            <rich:spacer width="10"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value=" ATÉ " />
                            <rich:spacer width="10"/>
                            <h:outputText id="dataRetornoFim" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AtestadoContratoControle.atestadoContratoVO.dataTerminoRetorno_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" >
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_tipoJustificativa}" />
                    <rich:spacer width="20"/>
                    <h:panelGroup styleClass="font-size-em-max">
                        <div class="cb-container margenVertical">
                            <h:selectOneMenu  id="justificativa" onblur="blurinput(this);" styleClass="form texto-size-12-real texto-cor-cinza texto-font" value="#{AtestadoContratoControle.atestadoContratoVO.tipoJustificativa}" >
                                <f:selectItems  value="#{AtestadoContratoControle.listaJustificativaOperacaoVOs}" />
                                <a4j:support event="onchange" action="#{AtestadoContratoControle.limparMensagem}" reRender="panelMensagem"/>
                            </h:selectOneMenu>
                        </div>

                        <rich:spacer width="3"/>
                        <a4j:commandLink id="atualizar_justificativaDeOperacao" action="#{AtestadoContratoControle.montarDadosListaJustificativaOperacaoVOs}" immediate="true" ajaxSingle="true" reRender="form:justificativa">
                            <i class="fa-icon-refresh texto-size-14-real texto-cor-cinza " ></i>
                        </a4j:commandLink>
                        <rich:spacer width="5"/>
                        <a4j:commandLink id="adicionarTipoOperacao" action="#{JustificativaOperacaoControle.reset}" oncomplete="abrirPopup('justificativaOperacaoCons.jsp', 'JustificativaOperacao', 800, 595);">
                            <i class="fa-icon-plus-sign texto-size-14-real texto-cor-cinza "></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGroup id="atestadoArquivo">
                    <h:panelGroup rendered="#{AtestadoContratoControle.existeArquivo}">
                        <h:outputText value="ATESTADO ANEXADO: "
                                      title="O tamanho do arquivo deve ser menor ou igual a 512KB"
                                      rendered="#{AtestadoContratoControle.existeArquivo}"
                                      styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                        <h:commandLink id="atestado" style="margin-left: 12px"
                                       rendered="#{AtestadoContratoControle.existeArquivo}"
                                       actionListener="#{AtestadoContratoControle.downloadAtestadoListener}"
                                       value="DOWNLOAD"/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{!AtestadoContratoControle.existeArquivo}">
                        <h:outputText value="ANEXAR ATESTADO"
                                      styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                        <rich:fileUpload id="upload"
                                         listHeight="60"
                                         listWidth="650"
                                         noDuplicate="false"
                                         fileUploadListener="#{AtestadoContratoControle.upload}"
                                         maxFilesQuantity="1"
                                         allowFlash="false"
                                         immediateUpload="false"
                                         acceptedTypes="jpg, jpeg, gif, png, bmp, pdf, JPG, JPEG, GIF, PNG, BMP, PDF"
                                         addControlLabel="Adicionar"
                                         cancelEntryControlLabel="Cancelar"
                                         doneLabel="Pronto"
                                         sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido. 512KB"
                                         progressLabel="Enviando"
                                         clearControlLabel="Limpar"
                                         clearAllControlLabel="Limpar todos"
                                         stopControlLabel="Parar"
                                         uploadControlLabel="Enviar"
                                         transferErrorLabel="Falha de Transmissão"
                                         stopEntryControlLabel="Parar">
                            <a4j:support event="onadd" reRender="panelMensagem"/>
                            <a4j:support event="onerror" oncomplete="#{AtestadoContratoControle.mensagemNotificar}" reRender="pnlUpload, panelMensagem"/>
                            <a4j:support event="onupload" reRender="panelMensagem"/>
                            <a4j:support event="onuploadcomplete" reRender="panelMensagem"/>
                            <a4j:support event="onclear" reRender="atestadoArquivo"/>
                        </rich:fileUpload>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGrid columns="1" style="height: 70px">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="OBSERVAÇÃO:"/>
                    <h:inputTextarea id="observacaoContrato" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AtestadoContratoControle.atestadoContratoVO.observacao}"  rows="4" cols="90"/>

                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="3" width="100%" style="max-width: 650px">
                      <h:panelGrid columns="1" width="100%">

                          <h:outputText value=" "/>

                      </h:panelGrid>
                  </h:panelGrid>
                <h:panelGrid id="panelBotoes" width="100%" columns="2" >
                   
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup rendered="#{AtestadoContratoControle.apresentarBotoes && !AtestadoContratoControle.processandoOperacao}">
                            <a4j:commandLink id="confirmar" reRender="panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade"
                                               action="#{AtestadoContratoControle.confirmar}"
                                              styleClass="pure-button pure-button-primary"
                                               oncomplete="#{AtestadoContratoControle.mensagemNotificar}">
                                               <i class="fa-icon-ok"></i>&nbsp;Confirmar
                            </a4j:commandLink>
                            <rich:spacer width="7"/>
                            <h:commandLink id="cancelar"  onclick="fecharJanela();executePostMessage({close: true});" styleClass="pure-button">
                                <i class="fa-icon-remove"></i>&nbsp;Cancelar
                            </h:commandLink>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{!AtestadoContratoControle.apresentarBotoes && !AtestadoContratoControle.processandoOperacao}">
                            <a4j:commandLink id="comprovanteOpAt" rendered="#{AtestadoContratoControle.operacaoRealizada}"
                                             title="Imprimir Comprovante da Operação de Atestado"
                                             action="#{AtestadoContratoControle.imprimirComprovanteOperacao}"
                                             oncomplete="abrirPopupPDFImpressao('relatorio/#{AtestadoContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                             styleClass="pure-button">
                                <i class="fa-icon-print"></i> &nbsp Imprimir Comprovante
                            </a4j:commandLink>
                            <rich:spacer width="7"/>
                            <h:commandLink id="fechar" value="Fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});" styleClass="pure-button pure-button-primary"/>
                        </h:panelGroup>

                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid rendered="#{AtestadoContratoControle.processandoOperacao}" columns="1">
                        <h:outputText id="msgProcessando" styleClass="mensagem"  value="Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                        <rich:spacer height="7"/>
                        <a4j:commandLink id="atualizar" title="Atualizar" onclick="window.location.reload();fireElementFromParent('form:btnAtualizaCliente');" styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                        </a4j:commandLink>
               </h:panelGrid>
            </h:panelGrid>


        </h:panelGrid>
        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />

    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>


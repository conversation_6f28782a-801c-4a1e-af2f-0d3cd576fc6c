<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <rich:modalPanel id="panelLog" autosized="true" shadowOpacity="false" width="650" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Log_tituloForm}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink6"/>
                <rich:componentControl for="panelLog" attachTo="hidelink6" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formLog" ajaxSubmit="true">
            <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_operacao}"/>
                <h:inputTextarea id="operacao" rows="2" cols="70" styleClass="campos" readonly="true" value="#{LoginControle.logVO.operacao} #{LoginControle.logVO.nomeEntidade_Apresentar}" />
                <h:outputText styleClass="tituloCampos" value="chave(codigo)"/>
                <h:inputTextarea rows="1" styleClass="campos" readonly="true" cols="70" id="chaveprimaria" value="#{LoginControle.logVO.chavePrimaria}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_nomeCampo}"/>
                <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="descricao" value="#{LoginControle.logVO.nomeCampo}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAnterior}"/>
                <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="valorCampoAnterior" value="#{LoginControle.logVO.valorCampoAnterior}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAlterado}"/>
                <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="valorCampoAlterado" value="#{LoginControle.logVO.valorCampoAlterado}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_responsavel}"/>
                <h:outputText id="responsavel" styleClass="tituloCampos" value="#{LoginControle.logVO.responsavelAlteracao}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_dataHora}"/>
                <h:outputText id="dataAlteracao" styleClass="tituloCampos" value="#{LoginControle.logVO.dataAlteracao_Apresentar} ás #{LoginControle.logVO.horaAlteracao_Apresentar}"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <title>
        <h:outputText value="#{msg_aplic.prt_Log_tituloForm}"/>
    </title>

        <f:facet name="header">

            <jsp:include page="topoReduzido_material.jsp"/>

        </f:facet>
        <hr style="border-color: #e6e6e6;"/>
        <h:form id="form">
            <h:commandLink action="#{LogControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">

                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <rich:dataTable id="resultadoConsultaLog" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                    value="#{LoginControle.listaConsultaLog}" rows="15" var="log">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Log_operacao}"/>
                            </f:facet>
                            <a4j:commandLink action="#{LoginControle.selecionarDadosLog}" reRender="form, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="operacao" value="#{log.operacao} - chave(codigo) #{log.chavePrimaria}" />
                        </rich:column>

                        <rich:column width="55%" style="text-align: left;">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Log_descricao}"/>
                            </f:facet>
                            <a4j:commandLink  action="#{LoginControle.selecionarDadosLog}" reRender="form, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="descricao">
                                <h:outputText escape="false" value="#{log.descricao}"/>
                            </a4j:commandLink>
                        </rich:column>


                    </rich:dataTable>
                    <rich:datascroller align="center" for="form:resultadoConsultaLog" maxPages="10" id="scResultadoLog" />
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
</f:view>

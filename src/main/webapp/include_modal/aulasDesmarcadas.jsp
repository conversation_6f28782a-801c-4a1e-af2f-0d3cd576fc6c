<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalAulasDesmarcadas" autosized="true" shadowOpacity="true" styleClass="novaModal">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalAulasDesmarcadas" value="Aulas Desmarcadas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hAulasDesmarcadas"/>
            <rich:componentControl for="modalAulasDesmarcadas" attachTo="hAulasDesmarcadas" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formListaAulasDesmarcadas">

        <h:panelGroup>
            <rich:dataTable value="#{ClienteControle.aulaDesmarcadasVO}"
                            rows="10"
                            id="listaAulaDesmarcadas"
                            rowKeyVar="status"
                            var="item">

                <rich:column sortBy="#{item.dataLancamento}">
                    <f:facet name="header">
                        <h:outputText value="Dt.Lanc."/>
                    </f:facet>
                    <div style="width:88px;">
                        <h:outputText value="#{item.dataLancamento}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                        </h:outputText>
                    </div>
                </rich:column>

                <rich:column sortBy="#{item.dataOrigem}">
                    <f:facet name="header">
                        <h:outputText value="Aula Desmarcada"/>
                    </f:facet>
                    <div style="width:300px;">
                        <h:outputText value="#{item.descricaoOrigem}"/>
                    </div>
                </rich:column>

                <rich:column sortBy="#{item.usuarioVO.username}">
                    <f:facet name="header">
                        <h:outputText value="Usu�rio"/>
                    </f:facet>
                    <h:outputText value="#{item.usuarioVO.username}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Op��es"/>
                    </f:facet>

                    <a4j:commandButton onclick="if (!confirm('Deseja fazer a reposi��o desta aula?')){return false;}" id="agendarReposicaoAula"
                                       image="/images/agendar_reposicao.png"
                                       style="vertical-align:middle;padding: 1 1 1 1;"
                                       title="Agendar Reposi��o"
                                       reRender="formListaAulasDesmarcadas,form:panelOpracoesContrato"
                                       oncomplete="#{ClienteControle.onCompleteReposicao}#{ClienteControle.msgAlert}"
                                       actionListener="#{ClienteControle.reposicaoAulaDesmarcada}">
                        <f:attribute name="aulaDesmarcada" value="#{item}"/>
                    </a4j:commandButton>

                    <a4j:commandButton
                            onclick="if (!confirm('Confirma exclus�o desta Aula Desmarcada?')){return false;}" id="deletarReposicaoAula"
                            image="/imagens/delete.png"
                            style="vertical-align:middle;padding: 1 1 1 1;"
                            title="Excluir Aula Desmarcada"
                            reRender="formListaAulasDesmarcadas,form:panelTurmas,form:panelOpracoesContrato"
                            oncomplete="#{fn:length(ClienteControle.aulaDesmarcadasVO) <= 0 ? 'Richfaces.hideModalPanel(\"modalAulasDesmarcadas\");' : ''}#{ClienteControle.msgAlert}"
                            actionListener="#{ClienteControle.deleteListener}">
                        <f:attribute name="aulaDesmarcada" value="#{item}"/>
                    </a4j:commandButton>
                </rich:column>
                <f:facet name="footer">
                    <rich:datascroller for="listaAulaDesmarcadas"/>
                </f:facet>
            </rich:dataTable>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

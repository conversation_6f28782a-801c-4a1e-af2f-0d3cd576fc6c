<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel domElementAttachment="parent" id="modalEnviarNotaEmail"
                 styleClass="novaModal"
                 autosized="true" width="400"
                 height="150" shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalEnviarNotaEmail" value="Solicitar envio de #{TelaClienteControle.notasFiscaisTOSelecionada.tipoNotaApresentar} por Email"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkNotaEmail"/>
            <rich:componentControl for="modalEnviarNotaEmail" attachTo="hidelinkNotaEmail" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formModalNotaEmail">

        <h:panelGroup id="panelGeralNotaEmail" style="text-align: center">

            <h:panelGroup layout="block" styleClass="tituloDemonstrativo"
                          style="padding-top: 7px; padding-bottom: 7px; width: 100%">
                <h:inputText style="width: 100%" value="#{TelaClienteControle.notasFiscaisTOSelecionada.email}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" id="btnModalEnviarNota">
                <a4j:commandLink value="Solicitar Envio"
                                 styleClass="pure-button pure-button-primary"
                                 action="#{TelaClienteControle.solicitarEnvioEmailNota}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteNota}"
                                 reRender="formModalNotaEmail"/>

                <a4j:commandLink style="margin-left: 8px"
                                 styleClass="pure-button"
                                 value="Fechar"
                                 onclick="Richfaces.hideModalPanel('modalEnviarNotaEmail');"/>
            </h:panelGroup>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

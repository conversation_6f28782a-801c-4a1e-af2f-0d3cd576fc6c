<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="panelParcelasRemessas" styleClass="novaModal" autosized="true" width="600" height="100">
    <a4j:form id="formMensagemAviso">
        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
            <h:outputText id="lblParcelasRemessas" styleClass="tituloFormulario" value="Parcela(s) enviada(s) para Remessa"/>
        </h:panelGrid>
        <div style="margin:10px 0 10px 0; text-align: center;">
            <rich:dataTable id="listaParcelas"  width="100%" styleClass="tabelaSimplesCustom " value="#{ClienteControle.listaParcelasRemessa}" var="parcela">

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold " value="Remessa"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{parcela.remessa}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold " value="Data Gera��o"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{parcela.dtGeracao_Apresentar}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold " value="Data Envio"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{parcela.dtEnvio_Apresentar}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold " value="Parcela"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{parcela.descricao}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold " value="Valor"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{parcela.valorParcela}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold " value="Data Vencimento"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{parcela.dataVencimento_Apresentar}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-bold texto-cor-cinza texto-font" value="Situa��o"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{parcela.situacao_Apresentar}"/>
                </rich:column>


            </rich:dataTable>
        </div>
        <h:panelGrid columns="1" width="100%">
            <h:outputText styleClass="tituloCamposVermelho" value="#{ClienteControle.tituloModalParcelaRemessa}"/>
        </h:panelGrid>
        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink id="btnFechar" styleClass="botaoPrimario texto-size-14-real" action="#{ClienteControle.inicializaListaParcelas}" oncomplete="#{rich:component('panelParcelasRemessas')}.hide();" value="Fechar"/>
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>

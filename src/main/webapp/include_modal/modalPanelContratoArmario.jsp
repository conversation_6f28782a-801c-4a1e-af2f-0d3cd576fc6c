<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalPanelContratoArmario" autosized="true" width="500" height="250"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblPanelContratoArmario" value="Assinar Contrato Arm�rio"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkarconarm"/>
            <rich:componentControl for="modalPanelContratoArmario" attachTo="hidelinkarconarm"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formContratoArmario">

        <h:panelGroup  id="panelContratoAssinado" layout="block" style="margin-top: 22px;margin-left:10%;display: inline-block">

            <h:panelGroup layout="block" style="display: inline-block;float: left" >

                <h:panelGroup layout="block" style="display: block;"  >
                    <h:outputText style="font-weight: bold" styleClass="cinza font14" value="Arm�rio:" />
                    <h:outputText styleClass="cinza font14" value="#{ClienteControle.aluguelSelecionado.armario.descricao}" />
                </h:panelGroup>

                <h:panelGroup layout="block" style="display: block;" >
                    <h:outputText style="font-weight: bold" styleClass="cinza font14" value="Inicio Loca��o:" />
                    <h:outputText styleClass="cinza font14"
                                  style="font-weight: bold" value="#{ClienteControle.aluguelSelecionado.dataCadastro_Apresentar}" />
                </h:panelGroup>
            </h:panelGroup>
            <rich:spacer width="20px"/>
            <h:panelGroup layout="block" style="float: right" >
                <h:panelGroup layout="block"  style="display: block;">
                    <h:outputText styleClass="cinza font14"
                                  style="font-weight: bold" value="Aluguel Arm�rio:" />
                    <h:outputText styleClass="cinza font14" value="#{ClienteControle.aluguelSelecionado.codigo}" />
                </h:panelGroup>

                <h:panelGroup layout="block" style="display: inline-block;" >
                    <h:outputText styleClass="cinza font14"
                                  style="font-weight: bold" value="Fim Loca��o:" />
                    <h:outputText styleClass="cinza font14" value="#{ClienteControle.aluguelSelecionado.dataFimOriginalApresentar}" />
                </h:panelGroup>
            </h:panelGroup>


        </h:panelGroup>

        <h:panelGroup layout="block" style="width: 100%; margin-top: 10px; text-align: center;" id="panelAssinarContrato">
            <rich:spacer height="10"/>
            <h:outputText style="font-weight: bold" styleClass="cinza font14" value="Assinar Contrato:" />
            <rich:spacer width="5"/>
            <a4j:commandLink  styleClass="botaoAssinarCotrato" action="#{ClienteControle.assinarContratoArmario}"  reRender="panelAssinarContrato">
                <h:outputText style="font-weight: bold" styleClass=" #{ClienteControle.aluguelSelecionado.contratoAssinadoApresentar}" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup style="width: 100%; margin-top: 10px; text-align: center;" layout="block"
                      styleClass="container-botoes">
            <a4j:commandLink action="#{ClienteControle.gravarContratoAsssinadoArmario}"    style="text-decoration: none;" styleClass="botoes nvoBt" value="Confirmar" oncomplete="#{rich:component('modalPanelContratoArmario')}.hide();" reRender="panelArmarioCliente"/>
            <a4j:commandLink action="#{ClienteControle.cancelarContratoAsssinadoArmario}"  style="text-decoration: none;" styleClass="botoes nvoBt btSec" value="Cancelar" oncomplete="#{rich:component('modalPanelContratoArmario')}.hide();" reRender="panelArmarioCliente"/>
        </h:panelGroup>


    </a4j:form>
</rich:modalPanel>

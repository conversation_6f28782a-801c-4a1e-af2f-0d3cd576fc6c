<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="panelUsuarioSenhaObservacaoGeral" autosized="true"
                 shadowOpacity="true" width="450" height="250"
                 onshow="document.getElementById('formUsuarioSenhaObservacao:senhaObservacao').focus()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblUsuarioSenhaObservacaoGeral" value="Confirma��o Lan�amento de Mensagem"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hidelink10" />
            <rich:componentControl for="panelUsuarioSenhaObservacaoGeral"
                                   attachTo="hidelink10" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formUsuarioSenhaObservacao">
        <h:panelGrid columns="1" width="100%"
                     columnClasses="colunaCentralizada">
            <h:panelGrid columns="1"
                         style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                         columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario"
                              value="Confirma��o Lan�amento de Mensagem" />
            </h:panelGrid>
            <h:panelGrid id="panelConfimacaoObservacao" columns="1" width="100%"
                         columnClasses="colunaEsquerda" styleClass="tabForm">
                <h:panelGroup>
                    <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                    <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText styleClass="text" value="C�digo:" />
                    <h:inputText id="codigoUsuarioObservacao" size="5" maxlength="7"
                                 style="margin-left:5px"
                                 value="#{ClienteControle.clienteVO.usuarioVO.codigo}">
                        <a4j:support event="onchange"
                                     focus="formUsuarioSenhaObservacao:senhaObservacao"
                                     action="#{ClienteControle.consultarResponsavel}"
                                     reRender="usuarioObservacao, mensagemObservacaoErro, mensagemObservacaoGeral" />
                    </h:inputText>
                    <h:inputText id="autoCompleteHiddenObs" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{ClienteControle.clienteVO.usuarioVO.username}"/>
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText styleClass="text" value="Usu�rio:" />
                    <h:outputText id="usuarioObservacao" styleClass="text"
                                  style="margin-left:6px"
                                  value="#{ClienteControle.clienteVO.usuarioVO.username}" />
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText styleClass="text" value="Senha:" />
                    <h:inputSecret autocomplete="off" id="senhaObservacao" size="14" maxlength="64"
                                   style="margin-left:8px"
                                   value="#{ClienteControle.clienteVO.usuarioVO.senha}" onkeypress="validarEnter(event,'formUsuarioSenhaObservacao:autorizarObservacao')"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">

                    <h:outputText value=" " />

                </h:panelGrid>
                <h:panelGroup id="mensagemObservacaoErro">
                    <a4j:commandButton rendered="#{ClienteControle.sucesso}"
                                       image="./imagens/sucesso.png" />
                    <a4j:commandButton rendered="#{ClienteControle.erro}"
                                       image="./imagens/erro.png" />
                </h:panelGroup>
                <h:panelGrid id="mensagemObservacaoGeral" columns="1" width="100%">
                    <h:outputText styleClass="mensagem"
                                  value="#{ClienteControle.mensagem}" />
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{ClienteControle.mensagemDetalhada}" />
                </h:panelGrid>
            </h:panelGrid>
            <a4j:commandButton id="autorizarObservacao"
                               value="#{msg_bt.btn_confirmar}"
                               action="#{ClienteControle.gravarClienteMensagemObservacaoGeral}"
                               oncomplete="#{ClienteControle.mostrarRichModalPanelUsuarioSenhaObservacao}"
                               reRender="formUsuarioSenhaObservacao"
                               image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}" />
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

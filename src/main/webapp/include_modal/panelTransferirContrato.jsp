<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="pnlTransferirContrato" domElementAttachment="parent" autosized="true" shadowOpacity="false"
                 width="550" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblTransferirContrato"
                          value="Transferir o contrato #{TransferenciaContratoEVOControle.contratoVO.codigo}"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formTransferirContrato" ajaxSubmit="true">
        <h:panelGrid columns="1" width="100%" footerClass="colunaCentralizada" headerClass="subordinado"
                     styleClass="paginaFontResponsiva">
            <h:panelGrid columns="2" styleClass="paginaFontResponsiva" columnClasses="w30, w70" width="100%">
                <h:outputText styleClass="font14 cinza negrito" value="Cliente atual: "/>
                <h:outputText styleClass="font14 cinza"
                              value=" #{TransferenciaContratoEVOControle.clienteVO.nome_Apresentar}"/>

                <h:outputText styleClass="font14 cinza negrito" value="Transferir para: "/>
                <h:panelGroup layout="block">
                    <h:inputText id="inputNovoCliente" size="40" style="width: 350px;" maxlength="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="inputTextClean"/>
                    <rich:suggestionbox height="200" width="490" for="inputNovoCliente"
                                        fetchValue="#{cliente.pessoa.nome}"
                                        suggestionAction="#{TransferenciaContratoEVOControle.consultarClientes}"
                                        minChars="3" rowClasses="20" status="statusSuggestionBox"
                                        nothingLabel="Nenhum Cliente encontrado !"
                                        var="cliente" id="suggestionIndicadoPor">
                        <a4j:support event="onselect"
                                     action="#{TransferenciaContratoEVOControle.selecionarClienteDestino}"/>
                        <h:column>
                            <h:outputText styleClass="texto-font texto-size-14-real" value="#{cliente.matricula}"/>
                        </h:column>
                        <h:column>
                            <h:outputText styleClass="texto-font texto-size-14-real" value="#{cliente.pessoa.nome}"/>
                        </h:column>
                        <h:column>
                            <h:outputText styleClass="texto-font texto-size-14-real"
                                          value="#{cliente.situacao_Apresentar}"/>
                        </h:column>
                    </rich:suggestionbox>

                </h:panelGroup>

                <h:outputText styleClass="font14 cinza negrito" value="Parcelas em aberto:"/>
                <h:panelGroup layout="block" style="width: 100%">
                    <c:if test="${not empty TransferenciaContratoEVOControle.parcelasEmAberto}">
                        <rich:dataTable value="#{TransferenciaContratoEVOControle.parcelasEmAberto}" var="parcela">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MovParcela_codigo}"/>
                                </f:facet>
                                <h:outputText styleClass="texto-font texto-size-14-real" value="#{parcela.codigo}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MovParcela_descricao}"/>
                                </f:facet>
                                <h:outputText styleClass="texto-font texto-size-14-real" value="#{parcela.descricao}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MovParcela_dataVencimento}"/>
                                </f:facet>
                                <h:outputText styleClass="texto-font texto-size-14-real"
                                              value="#{parcela.dataVencimento_Apresentar}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MovParcela_valorParcela}"/>
                                </f:facet>
                                <h:outputText styleClass="texto-font texto-size-14-real"
                                              value="#{parcela.valorParcela_Apresentar_Moeda}"/>
                            </rich:column>
                        </rich:dataTable>
                    </c:if>

                    <c:if test="${empty TransferenciaContratoEVOControle.parcelasEmAberto}">
                        <h:outputText styleClass="font14 cinza" value="N�o h� parcelas para transferir."/>
                    </c:if>
                </h:panelGroup>


            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink reRender="panelMensagemAlterarMatricula"
                                 action="#{TransferenciaContratoEVOControle.transferirContrato}"
                                 oncomplete="#{TransferenciaContratoEVOControle.mensagemNotificar}"
                                 styleClass="botaoPrimario texto-size-14">
                    <i style="font-size: 14px" class="fa-icon-save"></i> &nbsp
                    <h:outputText style="font-size: 14px" value="Confirmar"/>
                </a4j:commandLink>


                <a4j:commandLink id="btnCancelarTransferirContrato" value="Sair" style="margin-left: 8px"
                                 styleClass="botaoSecundario texto-size-14"
                                 oncomplete="Richfaces.hideModalPanel('pnlTransferirContrato');fireElement('form:btnAtualizaCliente');"
                                 reRender="form,panelAlterarMatricula"/>
            </h:panelGroup>

            <h:panelGrid id="pnlMensagemTransferirContrato" columns="3" width="100%" styleClass="tabMensagens">
                <h:commandButton rendered="#{TransferenciaContratoEVOControle.sucesso}" image="./imagens/sucesso.png"/>
                <h:commandButton rendered="#{TransferenciaContratoEVOControle.erro}" image="./imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{TransferenciaContratoEVOControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{TransferenciaContratoEVOControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>

        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>


<rich:modalPanel id="pnlTransferirDireitoUsoContrato" domElementAttachment="parent" autosized="true"
                 shadowOpacity="false" width="550" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText
                    value="Transferir os direitos de uso do contrato #{TransferirDireitoUsoContratoControle.contratoVO.codigo}"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formTransferirDireitoUsoContrato" ajaxSubmit="true">
        <h:panelGrid columns="1" width="100%" footerClass="colunaCentralizada" headerClass="subordinado"
                     styleClass="paginaFontResponsiva">
            <h:panelGrid columns="2" styleClass="paginaFontResponsiva" columnClasses="w30, w70" width="100%">
                <h:outputText styleClass="font14 cinza negrito" value="Transferir para: "/>
                <h:panelGroup layout="block">
                    <h:inputText id="inputNovoCliente" size="40" style="width: 350px;" maxlength="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="inputTextClean"/>
                    <rich:suggestionbox height="200" width="490" for="inputNovoCliente"
                                        fetchValue="#{cliente.pessoa.nome}"
                                        suggestionAction="#{TransferirDireitoUsoContratoControle.consultarClientes}"
                                        minChars="3" rowClasses="20" status="statusSuggestionBox"
                                        nothingLabel="Nenhum Cliente encontrado !"
                                        var="cliente" id="suggestionIndicadoPor">
                        <a4j:support event="onselect"
                                     action="#{TransferirDireitoUsoContratoControle.selecionarClienteDestino}"/>
                        <h:column>
                            <h:outputText styleClass="texto-font texto-size-14-real" value="#{cliente.matricula}"/>
                        </h:column>
                        <h:column>
                            <h:outputText styleClass="texto-font texto-size-14-real" value="#{cliente.pessoa.nome}"/>
                        </h:column>
                        <h:column>
                            <h:outputText styleClass="texto-font texto-size-14-real"
                                          value="#{cliente.situacao_Apresentar}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink reRender="panelMensagemAlterarMatricula"
                                 action="#{TransferirDireitoUsoContratoControle.transferirDireitoDeUsoContrato}"
                                 oncomplete="#{TransferirDireitoUsoContratoControle.mensagemNotificar}"
                                 styleClass="botaoPrimario texto-size-14">
                    <i style="font-size: 14px" class="fa-icon-save"></i> &nbsp
                    <h:outputText style="font-size: 14px" value="Confirmar"/>
                </a4j:commandLink>


                <a4j:commandLink id="btnCancelarTransferirDireitoUsoContrato" value="Sair" style="margin-left: 8px"
                                 styleClass="botaoSecundario texto-size-14"
                                 oncomplete="Richfaces.hideModalPanel('pnlTransferirDireitoUsoContrato');fireElement('form:btnAtualizaCliente');"
                                 reRender="form,panelAlterarMatricula"/>
            </h:panelGroup>

            <h:panelGrid id="pnlMensagemTransferirDireitoUsoContrato" columns="3" width="100%"
                         styleClass="tabMensagens">
                <h:commandButton rendered="#{TransferirDireitoUsoContratoControle.sucesso}"
                                 image="./imagens/sucesso.png"/>
                <h:commandButton rendered="#{TransferirDireitoUsoContratoControle.erro}" image="./imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{TransferirDireitoUsoContratoControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{TransferirDireitoUsoContratoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="pnlRecuperarDireitoUsoContrato" domElementAttachment="parent" autosized="true"
                 shadowOpacity="false" width="550" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText
                    value="Recuperar os direitos de uso do contrato #{TransferirDireitoUsoContratoControle.contratoVO.codigo}"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formRecuperarDireitoUsoContrato" ajaxSubmit="true">
        <h:panelGrid columns="1" width="100%" footerClass="colunaCentralizada" headerClass="subordinado"
                     styleClass="paginaFontResponsiva">
            <h:panelGrid columns="2" styleClass="paginaFontResponsiva" columnClasses="w30, w70" width="100%">
                <h:outputText styleClass="font14 cinza negrito" value="Recuperar para: "/>
                <h:panelGroup layout="block">
                    <h:outputText id="inputNovoCliente" style="width: 350px;"
                                  value="#{TransferirDireitoUsoContratoControle.contratoVO.pessoaOriginal.nome}"/>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink reRender="panelMensagemAlterarMatricula"
                                 action="#{TransferirDireitoUsoContratoControle.recuperarDireitoDeUsoContrato}"
                                 oncomplete="#{TransferirDireitoUsoContratoControle.mensagemNotificar}"
                                 styleClass="botaoPrimario texto-size-14">
                    <i style="font-size: 14px" class="fa-icon-save"></i> &nbsp
                    <h:outputText style="font-size: 14px" value="Confirmar"/>
                </a4j:commandLink>


                <a4j:commandLink id="btnCancelarRecuperarDireitoUsoContrato" value="Sair" style="margin-left: 8px"
                                 styleClass="botaoSecundario texto-size-14"
                                 oncomplete="Richfaces.hideModalPanel('pnlRecuperarDireitoUsoContrato');fireElement('form:btnAtualizaCliente');"
                                 reRender="form,panelAlterarMatricula"/>
            </h:panelGroup>

            <h:panelGrid id="pnlMensagemRecuperarDireitoUsoContrato" columns="3" width="100%" styleClass="tabMensagens">
                <h:commandButton rendered="#{TransferirDireitoUsoContratoControle.sucesso}"
                                 image="./imagens/sucesso.png"/>
                <h:commandButton rendered="#{TransferirDireitoUsoContratoControle.erro}" image="./imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{TransferirDireitoUsoContratoControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{TransferirDireitoUsoContratoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

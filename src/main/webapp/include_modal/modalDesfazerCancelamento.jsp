<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel domElementAttachment="parent" id="modalDesfazerCancelamento"
                 styleClass="novaModal"
                 autosized="true" width="400"
                 height="150" shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Desfazer cancelamento"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkDesfazerCancelamento"/>
            <rich:componentControl for="modalDesfazerCancelamento" attachTo="hidelinkDesfazerCancelamento" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formModalDesfazerCancelamento">

        <h:panelGroup id="panelGeralDesfazerCancelamento" style="text-align: center">

            <h:panelGroup styleClass="margin-box">
                <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                    <h:outputText value="Deseja desfazer o cancelamento?"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" id="btnDesfazerCancelamento">
                <a4j:commandLink value="Confirmar"
                                 styleClass="pure-button pure-button-primary"
                                 action="#{TelaClienteControle.desfazerCancelamentoProporcional}"
                                 oncomplete="#{TelaClienteControle.msgAlert};#{TelaClienteControle.mensagemNotificar}"
                                 reRender="formModalDesfazerCancelamento"/>

                <a4j:commandLink style="margin-left: 8px"
                                 styleClass="pure-button"
                                 value="Cancelar"
                                 onclick="Richfaces.hideModalPanel('modalDesfazerCancelamento');"/>
            </h:panelGroup>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
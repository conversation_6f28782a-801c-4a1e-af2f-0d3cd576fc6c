<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalControleCreditoTreino" autosized="true"
                 onshow="document.getElementById('formControleCreditoTreino:quantidadeCredTreinoLancar').focus()"
                 shadowOpacity="true" width="600" height="250" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalControleCreditoTreino" value="Controle #{TelaClienteControle.configNomenclaturaVendaCredito}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkControleCredito"/>
            <rich:componentControl for="modalControleCreditoTreino"
                                   attachTo="hidelinkControleCredito" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formControleCreditoTreino">
        <h:panelGrid columns="1" width="100%"
                     footerClass="colunaCentralizada" headerClass="subordinado">
            <h:panelGrid columns="2" headerClass="subordinado" width="100%" cellpadding="9" styleClass="font-size-Em-max">

                <h:outputText styleClass="rotuloCampos"  value="Opera��o:" />
                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{TelaClienteControle.controleCreditoTreinoVO.tipoOperacaoCreditoTreinoEnum.descricao}"/>

                <h:outputText styleClass="rotuloCampos" style=" vertical-align: middle;"  value="Tipo Opera��o:" />
                <h:selectOneRadio id="radioTipoAjusteManual" styleClass="textsmall"
                                  disabled="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}"
                                  value="#{TelaClienteControle.controleCreditoTreinoVO.codigoTipoAjusteManualCreditoTreino}">
                    <f:selectItem  itemLabel="ADICIONAR CR�DITO" itemValue="1"/>
                    <f:selectItem  itemLabel="RETIRAR CR�DITO" itemValue="2"/>
                </h:selectOneRadio>

                <h:outputText styleClass="rotuloCampos" rendered="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}"  value="Data Lan�amento:" />
                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" rendered="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}" value="#{TelaClienteControle.controleCreditoTreinoVO.dataLancamento_Apresentar}" />

                <h:outputText styleClass="rotuloCampos" rendered="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}"  value="Data Opera��o:" />
                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"    rendered="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}" value="#{TelaClienteControle.controleCreditoTreinoVO.dataOperacao_Apresentar}" />

                <h:outputText styleClass="rotuloCampos" rendered="#{TelaClienteControle.controleCreditoTreinoVO.sistemaOrigem != null}"  value="Sistema Utilizado:" />
                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  rendered="#{TelaClienteControle.controleCreditoTreinoVO.sistemaOrigem != null}" value="#{TelaClienteControle.controleCreditoTreinoVO.sistemaOrigem}" />

                <h:outputText styleClass="rotuloCampos" rendered="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}"  value="Usu�rio:" />
                <h:outputText styleClass="textsmall" rendered="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}" value="#{TelaClienteControle.controleCreditoTreinoVO.usuarioVO.nome}" />

                <h:outputText styleClass="rotuloCampos" rendered="#{(!(TelaClienteControle.controleCreditoTreinoVO.aulaDesmarcada_Apresentar eq '')) and TelaClienteControle.controleCreditoTreinoVO.tipoOperacaoCreditoTreinoEnum != 'MARCOU_AULA'}"
                              value="Aula desmarcada:" />
                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  rendered="#{(!(TelaClienteControle.controleCreditoTreinoVO.aulaDesmarcada_Apresentar eq '')) and TelaClienteControle.controleCreditoTreinoVO.tipoOperacaoCreditoTreinoEnum != 'MARCOU_AULA'}" value="#{TelaClienteControle.controleCreditoTreinoVO.aulaDesmarcada_Apresentar}" />
                <h:outputText styleClass="rotuloCampos" rendered="#{(!(TelaClienteControle.controleCreditoTreinoVO.aulaMarcada_Apresentar eq ''))}"  value="Aula marcada:" />
                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  rendered="#{(!(TelaClienteControle.controleCreditoTreinoVO.aulaMarcada_Apresentar eq ''))}" value="#{TelaClienteControle.controleCreditoTreinoVO.aulaMarcada_Apresentar}" />


                <h:outputText styleClass="tituloCampos" value="Quantidade:" />
                <h:panelGroup>
                    <h:inputText  size="3"
                                  maxlength="4"
                                  id="quantidadeCredTreinoLancar"
                                  readonly="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}"
                                  value="#{TelaClienteControle.controleCreditoTreinoVO.quantidade}" >
                    </h:inputText>
                    <%-- <h:outputText styleClass="textsmall" style="padding-left: 5px"  value="valor positivo concede e valor negativo retira cr�dito." /> --%>
                </h:panelGroup>
                <h:outputText styleClass="rotuloCampos" style="font-weight: bold"  value="Observa��es:" />
                <h:inputTextarea id="observacoes"  readonly="#{TelaClienteControle.controleCreditoTreinoVO.codigo != null}" rows="5" styleClass="textsmall" cols="70" value="#{TelaClienteControle.controleCreditoTreinoVO.observacao}" />
            </h:panelGrid>

        </h:panelGrid>
        <h:panelGrid id="mensagemControleCredito" columns="3"
                     rendered="#{TelaClienteControle.controleCreditoTreinoVO.codigo == null}"
                     width="100%" styleClass="tabMensagens">
            <h:panelGrid columns="1" width="100%">

                <h:outputText value=" " />

            </h:panelGrid>
            <h:panelGroup >
                <a4j:commandButton rendered="#{TelaClienteControle.sucesso}"
                                   image="./imagens/sucesso.png" />
                <a4j:commandButton rendered="#{TelaClienteControle.erro}"
                                   image="./imagens/erro.png" />
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagem"
                              value="#{TelaClienteControle.mensagem}" />
                <h:outputText styleClass="mensagemDetalhada"
                              value="#{TelaClienteControle.mensagemDetalhada}" />
            </h:panelGrid>
        </h:panelGrid>
        <h:panelGroup id="botoesControleCredito" layout="block" rendered="#{TelaClienteControle.controleCreditoTreinoVO.codigo == null}"  styleClass="container-botoes">
            <a4j:commandLink id="gravarControleCredito" rendered="#{TelaClienteControle.apresentarBotaoGrvControleCredito}"
                             action="#{TelaClienteControle.validarPermisaoAjusteManualCredito}"
                             reRender="listaHistoricoCreditoTreino, mensagemControleCredito, pgInfoCreditoTreinoContrato, scControleCreditoTreino, panelAutorizacaoFuncionalidade"
                             oncomplete="#{TelaClienteControle.msgAlert}"
                             value="Gravar"
                             accesskey="2" styleClass="botaoPrimario texto-size-14-real"/>
            <a4j:commandLink id="fecharControleCredito" rendered="#{!TelaClienteControle.apresentarBotaoGrvControleCredito}"
                             action="#{TelaClienteControle.fecharModalControleCreditoTreino}"
                             reRender="modalControleCreditoTreino"
                             oncomplete="#{TelaClienteControle.msgAlert}"
                             value="Fechar"
                             accesskey="2" styleClass="botaoPrimario texto-size-14-real"/>

        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>

<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalTotalPass" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="510"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalTotalPass" value="Total Pass"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkTotalPass"/>
            <rich:componentControl for="modalTotalPass" attachTo="hidelinkTotalPass"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formTotalPass" styleClass="pure-form" ajaxSubmit="true">
        <div style="width: 100%;">
            <fieldset>
                <h:panelGroup layout="block" styleClass="container-botoes" >
                    <a4j:commandLink reRender="formTotalPass"
                                     id="validarTotalPass"
                                     action="#{TelaClienteControle.validarTotalPass}"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteGymPass};"
                                     styleClass="botaoPrimario texto-size-14" style="margin-top: 10px">
                        <h:outputText style="font-size: 14px;" value="Autorizar Acesso"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </fieldset>
        </div>
    </a4j:form>
</rich:modalPanel>

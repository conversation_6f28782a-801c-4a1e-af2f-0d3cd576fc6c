<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="pnlListaAfastamentos" domElementAttachment="parent" shadowOpacity="false" width="800" styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:outputText id="lblListaAfastamentosDependente" value="Afastamentos lan�ados"/>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup layout="block">
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign " id="hideLnkListaAfastamento"/>
            <rich:componentControl for="pnlListaAfastamentos" attachTo="hideLnkListaAfastamento" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formListaAfastamentosDependente" ajaxSubmit="true">

        <h:panelGroup layout="block" id="pnlContainer" style="max-height: 440px; overflow-y: auto">

            <rich:dataTable id="afastamentos" width="100%" rowClasses="textsmall" columnClasses="col-text-align-left"
                            styleClass="tabelaSimplesCustom" headerClass="col-text-align-left"
                            value="#{AfastamentoContratoDependenteControle.listaAfastamentos}" var="afastamento" rowKeyVar="count">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                      value="Tipo afastamento"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{afastamento.tipoAfastamentoApresentar}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                      value="Data da opera��o"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{afastamento.dataRegistro}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                      value="Data In�cio"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{afastamento.dataInicio}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                      value="Data T�rmino"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{afastamento.dataTermino}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                      value="Observa��o"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{afastamento.observacao}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                      value="A��es"/>
                    </f:facet>
                    <a4j:commandLink reRender="pnlListaAfastamentos"
                                     rendered="#{count == 0}"
                                     style="margin-left: 10px; width: 20px"
                                     id="excluirAfastamento"
                                     oncomplete="#{AfastamentoContratoDependenteControle.mensagemNotificar}#{AfastamentoContratoDependenteControle.msgAlert}"
                                     styleClass="texto-cor-azul texto-size-14 linkPadrao tooltipster"
                                     action="#{AfastamentoContratoDependenteControle.excluirAfastamento}"
                                     title="Excluir afastamento">
                        <i id="imprimirContrato" class="fa-icon-remove" style="color: red"></i>
                        <f:setPropertyActionListener value="#{afastamento}" target="#{AfastamentoContratoDependenteControle.afastamentoVO}"/>
                    </a4j:commandLink>

                    <a4j:commandLink rendered="#{count != 0}" style="margin-left: 10px; width: 20px"/>
                </rich:column>
            </rich:dataTable>

        </h:panelGroup>
    </a4j:form>

</rich:modalPanel>


<rich:modalPanel id="pnlAfastamentoDependente" domElementAttachment="parent" shadowOpacity="false" width="800" height="500" styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:outputText id="lblAfastamentoDependente" value="Lan�ar afastamento"/>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup layout="block">
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign " id="hideLnkAfastamentoDependente"/>
            <rich:componentControl for="pnlAfastamentoDependente" attachTo="hideLnkAfastamentoDependente" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formAfastamentoDependente" ajaxSubmit="true">

        <h:panelGroup layout="block" id="pnlContainer" style="max-height: 440px; overflow-y: auto">

            <rich:dataTable id="carencia" width="100%" rowClasses="tablelistras textsmall"  columnClasses="col-text-align-left" styleClass="tabelaSimplesCustom" headerClass="col-text-align-left"
                            value="#{AfastamentoContratoDependenteControle.listaContratoVOs}" var="contrato" >
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_numeroContrato}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.codigo}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_plano}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.plano.descricao}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_dataInicio}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.contratoDependenteVO.dataInicio}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_dataTermino}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.contratoDependenteVO.dataFinalAjustada}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>
            </rich:dataTable>


                <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="formNovo"
                       style="margin-right:20px;margin-bottom:5px;padding:5px;margin-top: 20px;">
                    <tr>
                        <td align="left" valign="top">
                            <div class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">AFASTAMENTO</div>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top">
                            <h:panelGrid id= "panelAfastamento" width="100%" style="margin-top: 10px;">
                                <h:panelGroup style="padding-left: 10px;">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Tipo de afastamento: " />
                                    <rich:spacer width="10px"/>
                                    <h:selectOneMenu id="tipoAfastamento"
                                                     onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     styleClass="form texto-size-12-real texto-cor-cinza texto-font"
                                                     value="#{AfastamentoContratoDependenteControle.afastamentoVO.tipoAfastamento}">
                                        <f:selectItems value="#{AfastamentoContratoDependenteControle.listSelectItemTipoAfastamento}"/>
                                        <a4j:support event="onchange" action="#{AfastamentoContratoDependenteControle.montarListaJustificativaOperacao}" reRender="formAfastamentoDependente:pnlContainer"/>
                                    </h:selectOneMenu>

                                </h:panelGroup>

                                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

                                <c:if test="${AfastamentoContratoDependenteControle.afastamentoVO.ferias}">
                                    <h:panelGroup style="padding-left: 10px;">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Quantidade de dias permitidos:" />
                                        <rich:spacer width="10px"/>
                                        <h:outputText id="numCarenciaPer"
                                                      styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                      value="#{AfastamentoContratoDependenteControle.afastamentoVO.nrDiasFeriasPermitido}"/>

                                    </h:panelGroup>
                                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    <h:panelGroup style="padding-left: 10px;">
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Quantidade de dias utilizados:" />
                                        <rich:spacer width="10px"/>
                                        <h:outputText id="numCarenciaUsa" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.afastamentoVO.nrDiasUsados}" />
                                    </h:panelGroup>
                                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    <h:panelGroup style="padding-left: 10px;">
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Quantidade de dias restantes:" />
                                        <rich:spacer width="10px"/>
                                        <h:outputText id="numCarenciaRes" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.afastamentoVO.nrDiasRestam}" />
                                    </h:panelGroup>
                                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    <h:panelGroup style="padding-left: 10px;">
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Quantidade m�nima de dias para solicitar:" />
                                        <rich:spacer width="10px"/>
                                        <h:outputText id="numCarenciaMin" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.carencia}" />
                                    </h:panelGroup>
                                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                </c:if>
                            </h:panelGrid>
                        </td>
                    </tr>
                </table>
                <br/>


            <c:if test="${AfastamentoContratoDependenteControle.afastamentoVO.ferias or AfastamentoContratoDependenteControle.afastamentoVO.atestado}">

                <h:panelGrid>
                    <div class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">
                        PER�ODO DE AFASTAMENTO
                    </div>
                    <h:panelGrid columns="3" id="datas">
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="IN�CIO" />
                            <h:panelGroup styleClass="dateTimeCustom" layout="block" style="height: 40px;">
                                <rich:calendar id="dataInicio"
                                               value="#{AfastamentoContratoDependenteControle.afastamentoVO.dataInicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               onchanged="validar_Data(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return validar_Data(this.id);"
                                               oninputchange="validar_Data(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               zindex="2"
                                               showWeeksBar="false" >
                                </rich:calendar>
                                <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                                <%--ajax function para fazer o binding das datas inicio e fim executando os javascripts antes de cada componente--%>
                                <a4j:jsFunction name="gerarPeriodoRetorno" action="#{AfastamentoContratoDependenteControle.gerarPeriodoRetornoCarencia}"
                                                reRender="datas,panelPeridoRetorno,panelMensagem,panelAutorizacaoFuncionalidade,panelBotoes" oncomplete="#{AfastamentoContratoDependenteControle.mensagemNotificar}">
                                    <a4j:actionparam name="dtinicio" assignTo="#{AfastamentoContratoDependenteControle.afastamentoVO.dataInicio}"/>
                                    <a4j:actionparam name="dtfim" assignTo="#{AfastamentoContratoDependenteControle.afastamentoVO.dataTermino}"/>
                                    <a4j:actionparam name="numero" assignTo="#{AfastamentoContratoDependenteControle.afastamentoVO.nrDiasSomar}"/>
                                </a4j:jsFunction>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="NR. DE DIAS"/>
                            <h:panelGroup layout="block" style="height: 38px;">
                                <h:inputText size="5" maxlength="3"
                                             styleClass="inputTextClean"
                                             value="#{AfastamentoContratoDependenteControle.afastamentoVO.nrDiasSomar}"
                                             onblur="gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),document.getElementById('formAfastamentoDependente:nrDiasSomar'));"
                                             id="nrDiasSomar"/>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="1">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="FIM" />
                            <h:panelGroup styleClass="dateTimeCustom" layout="block" style="height: 34px;">
                                <rich:calendar id="dataTermino"
                                               value="#{AfastamentoContratoDependenteControle.afastamentoVO.dataTermino}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               onchanged="validar_Data(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return validar_Data(this.id);"
                                               oninputchange="validar_Data(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" >
                                </rich:calendar>
                            </h:panelGroup>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        </h:panelGrid>
                    </h:panelGrid>

                </h:panelGrid>

                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

                <h:panelGrid id="panelPeridoRetorno">
                    <h:panelGrid>
                        <h:panelGroup layout="block" >
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" value="RETORNO: "/>
                        </h:panelGroup>

                        <h:panelGroup rendered="#{AfastamentoContratoDependenteControle.afastamentoVO.periodoCarencia > 0}"   style="padding-left: 10px;">
                            <h:panelGrid id= "panelDadosContrato" >
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Os valores abaixo levam em considera��o a data de inicio da opera��o e n�o consideram dias de b�nus positivo:" />
                                <h:panelGroup>
                                    <h:outputText style="font-weight: bold;" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Dias Contrato: " />
                                    <rich:spacer width="10px"/>
                                    <h:outputText id="numDiasContrato" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.afastamentoVO.nrDiasContrato}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText style="font-weight: bold;" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Dias Utilizados Pelo Cliente: " />
                                    <rich:spacer width="10px"/>
                                    <h:outputText id="numDiasUsados" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.afastamentoVO.nrDiasUsadoContrato}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText style="font-weight: bold;" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Dias Restante do Contrato: " />
                                    <rich:spacer width="10px"/>
                                    <h:outputText id="numDiasRestantesCon" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.afastamentoVO.nrDiasRestanteContrato}" />
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGroup>

                        <h:panelGroup layout="block">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="IN�CIO:" />
                            <rich:spacer width="10"/>
                            <h:outputText id="dataInicioRetorno" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.afastamentoVO.dataInicioRetorno_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                            <rich:spacer width="10"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value=" AT�: " />
                            <rich:spacer width="10"/>
                            <h:outputText id="dataFimRetorno"  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AfastamentoContratoDependenteControle.afastamentoVO.dataTerminoRetorno_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

                <h:panelGrid columns="1" >
                    <h:panelGrid>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_tipoJustificativa}" />
                        <h:panelGroup styleClass="font-size-em-max">
                            <div class="cb-container margenVertical" style="width: 300px;">
                                <h:selectOneMenu  id="justificativa"
                                                  onblur="blurinput(this);"
                                                  onfocus="focusinput(this);"
                                                  styleClass="form texto-size-12-real texto-cor-cinza texto-font"
                                                  value="#{AfastamentoContratoDependenteControle.afastamentoVO.justificativa}" >
                                    <f:selectItems value="#{AfastamentoContratoDependenteControle.listSelectItemJustificativa}"/>
                                    <a4j:support event="onchange" reRender="panelMensagem"/>
                                </h:selectOneMenu>
                            </div>
                            <rich:spacer width="3"/>
                            <a4j:commandLink id="atualizar_justificativaDeOperacao" action="#{AfastamentoContratoDependenteControle.montarListaJustificativaOperacao}"
                                             immediate="true" ajaxSingle="true" reRender="formAfastamentoDependente:justificativa">
                                <i class="fa-icon-refresh texto-size-14-real texto-cor-cinza " ></i>
                            </a4j:commandLink>
                            <rich:spacer width="5"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="1" >
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="OBSERVA��O: "/>
                    <h:inputTextarea value="#{AfastamentoContratoDependenteControle.afastamentoVO.observacao}" styleClass="texto-size-14-real texto-cor-cinza texto-font" rows="3" cols="93" style="width: 100%;height: 35px"/>
                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="4" width="100%" >
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText value=""/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="panelBotoes" width="100%" columns="2" >
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup rendered="#{AfastamentoContratoDependenteControle.apresentarBotoes && !AfastamentoContratoDependenteControle.processandoOperacao}">
                            <a4j:commandLink reRender="form,panelAutorizacaoFuncionalidade,panel"
                                             id="confirmar"
                                             action="#{AfastamentoContratoDependenteControle.validarDadosCarencia}"
                                             styleClass="pure-button pure-button-primary"
                                             oncomplete="#{AfastamentoContratoDependenteControle.mensagemNotificar}">
                                <i class="fa-icon-ok"></i>&nbsp;Confirmar
                            </a4j:commandLink>

                            <rich:spacer width="7"/>
                            <a4j:commandLink id="cancelar"
                                             onclick="Richfaces.hideModalPanel('pnlAfastamentoDependente');"
                                             styleClass="pure-button">
                                <i class="fa-icon-remove"></i>&nbsp;Cancelar
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{!AfastamentoContratoDependenteControle.apresentarBotoes && !AfastamentoContratoDependenteControle.processandoOperacao}">
                            <h:commandLink id="fechar" title="Fechar Janela"
                                           onclick="Richfaces.hideModalPanel('pnlAfastamentoDependente');fireElement('form:btnAtualizaCliente');"
                                           styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-remove"></i>&nbsp;Fechar
                            </h:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

            </c:if>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

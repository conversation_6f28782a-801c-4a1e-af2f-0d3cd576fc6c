<%@page pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalCarteirinhaHistorico" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="700"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalCarteirinhaHistorico" value="Histórico Carteirinha"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkCarteirinhaHistorico"/>
            <rich:componentControl for="modalCarteirinhaHistorico" attachTo="hidelinkCarteirinhaHistorico"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCarteirinhaHisto" ajaxSubmit="true">
        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="max-width: 700px; text-align: center; max-height: 250px; overflow: auto">

            <table class="tblHeaderLeft semZebra" style="margin-bottom: 5px;">
                <thead>
                <tr>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="CÓDIGO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="DATA DE LANÇAMENTO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="DATA DE IMPRESSÃO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="DATA DE VALIDADE"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="VIA"/>
                    </th>
                </tr>
                </thead>

                <tbody>
                <a4j:repeat var="carteirinha" value="#{TelaClienteControle.listaCarteirinhas}">
                    <tr>
                        <td>
                            <h:outputText
                                    value="#{carteirinha.codigo}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{carteirinha.dataLancamento_Apresentar}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{carteirinha.dataImpressao_Apresentar}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{carteirinha.dataValidadeCarteirinha_Apresentar}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{carteirinha.via_Apresentar}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                    </tr>
                </a4j:repeat>
                </tbody>
            </table>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

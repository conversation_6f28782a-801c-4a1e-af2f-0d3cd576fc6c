<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalConvidado" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="510"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalConvidado" value="Adicionar convidado"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkConvidado"/>
            <rich:componentControl for="modalConvidado" attachTo="hidelinkConvidado"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConvidado" styleClass="pure-form" ajaxSubmit="true">



        <h:panelGrid columns="1" width="100%" rendered="#{not ConvidadoControle.cadastrandoConvidado}"
                     footerClass="colunaCentralizada" headerClass="subordinado" styleClass="paginaFontResponsiva">

            <h:panelGrid columns="2"  styleClass="paginaFontResponsiva" cellpadding="8"
                         width="100%">

                <h:outputText  value="Convites utilizados:" />
                <h:outputText  value="#{ConvidadoControle.convitesUsados} de #{TelaClienteControle.convitesDireito} dispon�veis por m�s." />
                <h:outputText rendered="#{ConvidadoControle.convidado.codigo gt 0}" value="Nome do convidado:" />
                <h:outputText rendered="#{ConvidadoControle.convidado.codigo gt 0}" value="#{ConvidadoControle.convidado.pessoa.nome}" />
                <h:outputText rendered="#{ConvidadoControle.convidado.codigo gt 0}" value="CPF do convidado:" />
                <h:outputText rendered="#{ConvidadoControle.convidado.codigo gt 0}" value="#{ConvidadoControle.convidado.pessoa.cfp}" />

                <h:outputText rendered="#{ConvidadoControle.convidado.codigo eq 0}" value="Buscar convidado:" />
                <h:panelGroup rendered="#{ConvidadoControle.convidado.codigo eq 0}">
                    <%-- O onkeypress � para n�o dar submit no form quando apertar Enter --%>
                    <h:inputText id="convidado" size="50" maxlength="50" style="width: 300px"
                                 onfocus="focusinput(this);" styleClass="form"
                                 onkeypress="desValidarEnter(event,'formConvidado:novoConvidado')"
                                 value="#{ConvidadoControle.convidado.pessoa.nome}">
                    </h:inputText>
                    <rich:suggestionbox height="200" width="400"
                                        for="convidado"
                                        fetchValue="#{result}"
                                        suggestionAction="#{ConvidadoControle.executarAutocomplete}"
                                        minChars="1" rowClasses="20"
                                        status="true"
                                        nothingLabel="Nenhum aluno encontrado!"
                                        var="result" id="suggestionConvidado">
                        <a4j:support event="onselect" ignoreDupResponses="true"
                                     action="#{ConvidadoControle.selecionarConvidadoSuggestionBox}"
                                     reRender="formConvidado"/>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText styleClass="textverysmall" value="Nome"/>
                            </f:facet>
                            <h:outputText value="#{result.pessoa.nome}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText styleClass="textverysmall" value="CPF"/>
                            </f:facet>
                            <h:outputText value="#{result.pessoa.cfp}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>

            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink reRender="formConvidado"
                                 id="limparConvidado"
                                 action="#{ConvidadoControle.limparConvidado}"
                                 rendered="#{ConvidadoControle.convidado.codigo gt 0  and not ConvidadoControle.cadastrandoConvidado}"
                                 styleClass="botaoSecundario texto-size-14">
                    <h:outputText style="font-size: 14px" value="Limpar convidado"/>
                </a4j:commandLink>

                <a4j:commandLink reRender="formConvidado"
                                 id="novoConvidado"
                                 action="#{ConvidadoControle.cadastrarConvidado}"
                                 rendered="#{ConvidadoControle.convidado.codigo eq 0 and not ConvidadoControle.cadastrandoConvidado}"
                                 styleClass="botaoSecundario texto-size-14">
                    <h:outputText style="font-size: 14px" value="Cadastrar convidado"/>
                </a4j:commandLink>


                <a4j:commandLink reRender="formConvidado"
                                 id="cadastrarConvite" style="margin-left: 10px; "
                                 rendered="#{ConvidadoControle.convidado.codigo gt 0 and not ConvidadoControle.cadastrandoConvidado}"
                                 action="#{ConvidadoControle.lancarConvite}"
                                 oncomplete="#{ConvidadoControle.msgAlert}"
                                 styleClass="botaoPrimario texto-size-14">
                    <h:outputText style="font-size: 14px" value="Lan�ar convite"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

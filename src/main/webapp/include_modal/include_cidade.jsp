<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/imports.jsp" %>

<a4j:outputPanel>
    <rich:modalPanel id="panelCidade" autosized="true" width="550" height="250"
                     styleClass="novaModal"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:outputText value="Cadastro de Cidade"/>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText id="hidelinkpanelCidade"
                              styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"/>
                <rich:componentControl for="panelCidade" attachTo="hidelinkpanelCidade" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formCidade">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_codigo}"/>
                    <h:panelGroup>
                        <h:inputText id="cidade_codigo" size="10" maxlength="10" readonly="true"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="camposSomenteLeitura" value="#{CidadeControle.cidadeVO.codigo}"/>
                        <h:message for="cidade_codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_nome}"/>
                    <h:inputText id="cidade_nome" size="40" maxlength="40" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" value="#{CidadeControle.cidadeVO.nome}"/>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_estado}"/>
                    <h:outputText id="cidade_estado" value="#{CidadeControle.cidadeVO.estado.descricao}"/>
                </h:panelGrid>
                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{CidadeControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{CidadeControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <a4j:commandButton id="salvarCidade" reRender="panelMensagem"
                                           action="#{CidadeControle.gravarSemRetorno}"
                                           oncomplete="Richfaces.hideModalPanel('panelCidade');#{CidadeControle.onCompleteModal}"
                                           alt="#{msg.msg_gravar_dados}" accesskey="2" value="Gravar"
                                           styleClass="botoes nvoBt btSec btPerigo"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</a4j:outputPanel>


<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="panelAlterarMatricula" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="400"
                 styleClass="novaModal" >
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblPanelAlterarMatricula" value="Alterar Matr�cula"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formAlterarMatricula" ajaxSubmit="true">
        <h:panelGrid columns="1" width="100%"
                     footerClass="colunaCentralizada" headerClass="subordinado" styleClass="paginaFontResponsiva">
            <h:panelGrid columns="2"  styleClass="paginaFontResponsiva" cellpadding="8"
                         width="100%">
                <h:outputText styleClass="font14 cinza negrito"
                              value="Cliente: "/>
                <h:outputText styleClass="font14 cinza"
                              value=" #{TelaClienteControle.clienteVO.nome_Apresentar}"/>

                <h:outputText styleClass="font14 cinza negrito"
                              value="Atual Matr�cula: "/>
                <h:outputText styleClass="font14 cinza" value="#{TelaClienteControle.clienteVO.codigoMatricula}"/>

                <h:outputText styleClass="font14 cinza negrito"
                              value="Nova Matr�cula: "/>
                <h:inputText size="10" value="#{TelaClienteControle.clienteVO.novaMatricula}"/>

            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="container-botoes">

                <a4j:commandLink reRender="panelMensagemAlterarMatricula"
                                 action="#{TelaClienteControle.alterarNumeroMatricula}"
                                 styleClass="botaoPrimario texto-size-14">
                    <i style="font-size: 14px" class="fa-icon-save"></i> &nbsp <h:outputText style="font-size: 14px" value="Confirmar"/>
                </a4j:commandLink>


                <a4j:commandLink id="cancelarAlteraMatricula"
                                 value="Sair"
                                 style="margin-left: 8px"
                                 styleClass="botaoSecundario texto-size-14"
                                 oncomplete="Richfaces.hideModalPanel('panelAlterarMatricula');fireElementFromParent('form:btnAtualizaCliente');"
                                 reRender="form,panelAlterarMatricula">
                </a4j:commandLink>
            </h:panelGroup>

            <h:panelGrid id="panelMensagemAlterarMatricula" columns="3" width="100%" styleClass="tabMensagens">
                <h:commandButton  rendered="#{TelaClienteControle.sucesso}" image="./imagens/sucesso.png"/>
                <h:commandButton rendered="#{TelaClienteControle.erro}" image="./imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem"  value="#{TelaClienteControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{TelaClienteControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>

        </h:panelGrid>
        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </a4j:form>
</rich:modalPanel>

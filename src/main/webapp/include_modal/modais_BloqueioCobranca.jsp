<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalConfirmarBloquearCobrancas" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="400"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblConfirmarBloquearCobrancas" value="Bloquear cobran�a autom�tica"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkModalConfirmarBloquearCobrancas"/>
            <rich:componentControl for="modalConfirmarBloquearCobrancas"
                                   attachTo="hidelinkModalConfirmarBloquearCobrancas"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalBloqueioCobrancas" ajaxSubmit="true">
        <h:panelGroup layout="block" style="padding: 15px">

            <h:panelGroup layout="block" id="panelTitleBloqueio"
                          style="padding-top: 10px;padding-bottom: 10px;">
                <h:outputText value="Tipo de bloqueio:"
                              style="vertical-align: middle;"
                              styleClass="texto-size-20 cinza"/>
                <div class="cb-container"
                     style="font-size: 11px !important;">
                    <h:selectOneMenu id="tipoBloqueioCobrancaAutomatica" styleClass="form" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     value="#{TelaClienteControle.cliente.pessoa.tipoBloqueioCobrancaAutomatica}">
                        <f:selectItems value="#{TelaClienteControle.listSelectItemTipoBloqueioCobrancaEnum}"/>
                        <a4j:support event="onchange" reRender="formModalBloqueioCobrancas"/>
                    </h:selectOneMenu>
                </div>
            </h:panelGroup>

            <c:if test="${TelaClienteControle.selecionouParcelasFuturas}">
                <h:panelGroup styleClass="dateTimeCustom" layout="block">
                    <h:outputText style="vertical-align: middle;"
                                  value="Vencimento maior que:  "
                                  styleClass="texto-size-20 cinza"/>
                    <rich:calendar id="idInputdataBloqueioCobrancas"
                                   inputSize="10"
                                   value="#{TelaClienteControle.cliente.pessoa.dataBloqueioCobrancaAutomatica}"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="false"
                                   zindex="2"
                                   showWeeksBar="false"
                                   buttonIcon="imagens_flat/calendar-button.svg"/>

                    <h:message for="idInputdataBloqueioCobrancas" styleClass="mensagemDetalhada"/>
                    <rich:jQuery id="mskDataBloqueio" selector=".rich-calendar-input" timing="onload"
                                 query="mask('99/99/9999')"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="padding-top: 15px; text-align: left">
                    <h:outputText value="Todas as parcelas cujo vencimento tem data superior � data selecionada acima ser�o bloqueadas para cobran�a autom�tica."
                                  style="font-style: italic"
                                  styleClass="texto-size-18 cinza"/>
                </h:panelGroup>
            </c:if>

            <c:if test="${TelaClienteControle.selecionouTodasParcelas}">
                <h:panelGroup layout="block" style="padding-top: 5px; text-align: left">
                    <h:outputText value="Todas as parcelas independente da data de vencimento ser�o bloqueadas para cobran�a autom�tica."
                                  style="font-style: italic"
                                  styleClass="texto-size-18 cinza"/>
                </h:panelGroup>
            </c:if>

            <h:panelGroup layout="block" id="panelBtnModalConfirmarBloqueioCobranca"
                          style="text-align: center; padding: 25px 0 20px 0;">
                <a4j:commandLink id="btnConfirmarBloqueioCobranca"
                                 value="Confirmar"
                                 action="#{TelaClienteControle.confirmarBloquearCobrancasAutomatica}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBloqueioCobrancas}"
                                 styleClass="botaoPrimario texto-size-16"/>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalDesbloquearBloquearCobrancas" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="400"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Desbloquear cobran�a autom�tica"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkModalDesbloquearBloquearCobrancas"/>
            <rich:componentControl for="modalDesbloquearBloquearCobrancas"
                                   attachTo="hidelinkModalDesbloquearBloquearCobrancas"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalDesbloquearBloquearCobrancas" ajaxSubmit="true">
        <h:panelGroup layout="block" style="padding: 10px" id="panelModalDesbloquearBloquearCobrancas">

            <h:panelGroup layout="block" id="panelTitleDesbloqueioAaaa"
                          style="padding-top: 10px;padding-bottom: 15px;">
                <h:outputText value="Liberar as cobran�as autom�ticas?"
                              styleClass="texto-size-20 cinza"/>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelBtnModalDesbloquearBloquearCobrancas"
                          style="text-align: center; padding: 20px 0 20px 0px;">
                <a4j:commandLink id="btnConfirmarDesbloqueioCobranca"
                                 value="Confirmar"
                                 action="#{TelaClienteControle.confirmarDesbloquearCobrancasAutomatica}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBloqueioCobrancas}"
                                 styleClass="botaoPrimario texto-size-16"/>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

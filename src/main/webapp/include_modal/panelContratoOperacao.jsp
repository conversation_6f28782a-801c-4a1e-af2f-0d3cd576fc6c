<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="panelContratoOperacao" autosized="true"
                 styleClass="novaModal"
                 shadowOpacity="true" width="500" height="350">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblPanelContratoOperacao" value="Visualiza��o Opera��o Contrato"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelink1"/>
            <rich:componentControl for="panelContratoOperacao" attachTo="hidelink1"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formContratoOperacao">
        <h:panelGrid columns="1" width="100%"
                     footerClass="colunaCentralizada" headerClass="subordinado">
            <h:panelGrid columns="2" headerClass="subordinado" width="100%">

                <h:outputText styleClass="cinza" style="font-weight: bold"
                              value="Cliente Transferido "
                              rendered="#{ClienteControle.contratoOperacaoVO.tipoOperacao == 'TS' || ClienteControle.contratoOperacaoVO.tipoOperacao == 'TE'}" />
                <h:inputText id="clienteRecebe" readonly="true" styleClass="camposSomenteLeitura"
                             size="90"
                             rendered="#{ClienteControle.contratoOperacaoVO.tipoOperacao == 'TS'}"
                             value="#{ClienteControle.contratoOperacaoVO.clienteRecebeDias.pessoa.nome}" />
                <h:inputText id="clienteTransfere" readonly="true" styleClass="camposSomenteLeitura"
                             size="90"
                             rendered="#{ClienteControle.contratoOperacaoVO.tipoOperacao == 'TE'}"
                             value="#{ClienteControle.contratoOperacaoVO.clienteTransfereDias.pessoa.nome}" />

                <h:outputText styleClass="cinza" style="font-weight: bold"
                              value="Observa��es " />
                <h:inputTextarea id="observacoes" styleClass="camposSomenteLeitura"
                                 readonly="true" rows="5" cols="90"
                                 value="#{ClienteControle.contratoOperacaoVO.observacao}" />

                <h:outputText styleClass="cinza" style="font-weight: bold"
                              value="Descri��o C�lculo " />
                <h:inputTextarea id="descricaoCalculo"
                                 styleClass="camposSomenteLeitura" readonly="true" rows="7"
                                 cols="90"
                                 value="#{ClienteControle.contratoOperacaoVO.descricaoCalculo}" />

                <h:outputText styleClass="cinza" style="font-weight: bold"
                              value="Origem " />
                <h:outputText id="origemSistema" styleClass="cinza"
                              value="#{ClienteControle.contratoOperacaoVO.origemSistema.descricao}" />

                <h:outputText styleClass="cinza" style="font-weight: bold"
                              value="Respons�vel " />
                <h:outputText id="responsavel" styleClass="cinza"
                              value="#{ClienteControle.contratoOperacaoVO.responsavel.nome}" />

                <h:outputText styleClass="cinza" style="font-weight: bold"
                              value="Justificativa " />
                <h:outputText id="justificativa" styleClass="cinza"
                              value="#{ClienteControle.contratoOperacaoVO.tipoJustificativa.descricao}" />
            </h:panelGrid>

        </h:panelGrid>
        <center>
            <a4j:commandLink id="btnImprimirReciboCan"
                             action="#{ClienteControle.imprimirReciboDevolucao}"
                             rendered="#{ClienteControle.contratoOperacaoVO.possuiReciboDevolucao}"
                             oncomplete="abrirPopupPDFImpressao('relatorio/#{ClienteControle.nomeArquivoReciboDevolucao}','', 780, 595);"
                             style="font-size:14px; margin-top: 20px;"
                             styleClass="pure-button pure-button-small pure-button-primary">
                <i class="fa-icon-print"></i> &nbsp; Imprimir Recibo
            </a4j:commandLink>

        </center>

    </a4j:form>
</rich:modalPanel>

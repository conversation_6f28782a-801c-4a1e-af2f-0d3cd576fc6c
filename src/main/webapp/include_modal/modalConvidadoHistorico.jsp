<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalConvidadoHistorico" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="700"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalConvidadoHistorico" value="Hist�rico de Convidados"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkConvidadoHistorico"/>
            <rich:componentControl for="modalConvidadoHistorico" attachTo="hidelinkConvidadoHistorico"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formConvidadoHisto" ajaxSubmit="true">
        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="width: 100%; text-align: center; height: 500px; overflow-y: auto">

            <c:if test="${empty ConvidadoControle.historico}">
                <h:outputText style="margin-top: 50px; font-weight: bold; display: block;" value="Este aluno n�o possui hist�rico de convidados."/>
            </c:if>
            <c:if test="${not empty ConvidadoControle.historico}">
                <table class="tblHeaderLeft semZebra" style="margin-bottom: 5px;">
                    <thead>
                    <tr>
                        <th>
                            <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                          value="C�DIGO"/>
                        </th>
                        <th>
                            <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                          value="CONVIDADO"/>
                        </th>

                        <th>
                            <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                          value="DIA DO CONVITE"/>
                        </th>
                        <th>
                            <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                          value="SITUA��O"/>
                        </th>
                    </tr>
                    </thead>

                    <tbody>
                    <a4j:repeat var="conv" value="#{ConvidadoControle.historico}">

                        <tr>

                            <td>
                                <h:outputText
                                        value="#{conv.codigo}"
                                        style="text-decoration: none; text-align: left; font-size: 14px; "/>
                            </td>
                            <td>
                                <h:outputText
                                        value="#{conv.convidado.pessoa.nome}"
                                        style="text-decoration: none; text-align: left; font-size: 14px; "/>
                            </td>
                            <td>
                                <h:outputText
                                        value="#{conv.diaApresentar}"
                                        style="text-decoration: none; text-align: left; font-size: 14px; "/>
                            </td>
                            <td>
                                <h:outputText
                                        value="#{conv.situacao}"
                                        style="text-decoration: none; text-align: left; font-size: 14px; "/>
                            </td>
                        </tr>
                    </a4j:repeat>
                    </tbody>
                </table>

                <h:outputText style="margin-top: 50px; font-weight: bold; display: block; text-align: left; margin-left: 20px;" value="Total: #{fn:length(ConvidadoControle.historico)} #{fn:length(ConvidadoControle.historico) > 1 ? 'convites' : 'convite'}"/>
            </c:if>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<style>
    .btnVerificacaoCartaoOk {
        display: block;
        color: #FFFFFF;
        background: #094771;
        height: 23px;
        width: 117px;
        text-align: center;
        line-height: 24px;
        border-radius: 4px !important;
        font-size: 14px !important;
        margin: 0 !important;
    }

    .btnVerificacaoCartaoSalvar {
        display: block;
        color: #FFFFFF !important;
        background: #28AB45 !important;
        height: 23px;
        width: 117px;
        text-align: center;
        line-height: 24px;
        border-radius: 4px !important;
        font-size: 14px !important;
        margin: 0 !important;
    }

    .btnVerificacaoCartaoCancelar {
        display: block;
        color: #9D9D9D !important;
        background: #E5E5E5 !important;
        height: 23px;
        width: 117px;
        text-align: center;
        line-height: 24px;
        border-radius: 4px !important;
        font-size: 14px !important;
        margin: 0 0 0 24px !important;
    }
</style>

<rich:modalPanel domElementAttachment="parent" id="modalVerificacaoCartao"
                 styleClass="novaModal"
                 autosized="true" width="420" height="290" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Cobran�a de verifica��o"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup rendered="#{!AutorizacaoCobrancaControle.verificacaoCartaoSucesso}">
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelink1"/>
            <rich:componentControl for="modalVerificacaoCartao" attachTo="hidelink1" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:panelGroup layout="block"
                  id="panelModalVerificacaoCartao"
                  style="text-align: center;">

        <%-------------SUCESSO-------------%>
        <h:panelGroup layout="block" id="divVerificaCardSucesso"
                      rendered="#{AutorizacaoCobrancaControle.verificacaoCartaoSucesso}">
            <div style="padding-top: 22px;">
                <h:graphicImage value="/faces/imagens/icon-check.svg"
                                width="30"
                                style="font-size: 40px;"
                                styleClass="texto-size-30"/>
            </div>
            <div style="padding-top: 24px">
                <h:outputText styleClass="texto-bold texto-size-14-real"
                              value="Transa��o Autorizada"/>
            </div>
            <div style="padding-top: 24px">
                <h:outputText styleClass="texto texto-size-14-real"
                              value="Cart�o validado com sucesso!"/>
            </div>
        </h:panelGroup>

        <%-------------ERRO-------------%>
        <h:panelGroup layout="block" id="divVerificaCardErro"
                      rendered="#{!AutorizacaoCobrancaControle.verificacaoCartaoSucesso && AutorizacaoCobrancaControle.permiteCadastrarCartaoMesmoAssim}">
            <div style="padding-top: 22px;">
                <h:graphicImage value="/faces/imagens/icon-times.svg"
                                width="30"
                                style="font-size: 40px;"
                                styleClass="texto-size-30"/>
            </div>
            <div style="padding-top: 24px">
                <h:outputText styleClass="texto-bold texto-size-14-real"
                              value="Transa��o n�o autorizada!"/>
            </div>
            <div style="padding-top: 24px">
                <h:outputText styleClass="texto-bold texto-size-14-real"
                              value="#{AutorizacaoCobrancaControle.verificacaoCartaoMsg}"/>
            </div>
            <div style="padding-top: 24px">
                <h:outputText styleClass="texto texto-size-14-real"
                              value="Deseja salvar o cart�o mesmo assim?"/>
            </div>
        </h:panelGroup>

        <%-------------N�O PERMITE PROSSEGUIR DEVIDO A CONFIGURA��O-------------%>
        <h:panelGroup layout="block" id="divPermiteProsseguir"
                      rendered="#{!AutorizacaoCobrancaControle.verificacaoCartaoSucesso && !AutorizacaoCobrancaControle.permiteCadastrarCartaoMesmoAssim}">
            <div style="padding-top: 22px;">
                <h:graphicImage value="/faces/imagens/icon-times.svg"
                                width="30"
                                style="font-size: 40px;"
                                styleClass="texto-size-30"/>
            </div>
            <div style="padding-top: 24px">
                <h:outputText styleClass="texto-bold texto-size-14-real"
                              value="Transa��o n�o autorizada!"/>
            </div>
            <div style="padding-top: 24px">
                <h:outputText styleClass="texto-bold texto-size-14-real"
                              value="#{AutorizacaoCobrancaControle.verificacaoCartaoMsg}"/>
            </div>
            <div style="padding-top: 24px">
                <h:outputText styleClass="texto texto-size-14-real"
                              value="N�o ser� poss�vel prosseguir, revise as informa��es do cart�o e tente novamente."/>
            </div>
        </h:panelGroup>

        <h:panelGroup layout="block"
                      id="divBtnVerificaCard"
                      style="padding: 32px 0 14px 0;justify-content: center;display: flex;">
            <a4j:commandLink id="btnVerificaCartaoOk" value="Salvar cart�o"
                             rendered="#{AutorizacaoCobrancaControle.verificacaoCartaoSucesso}"
                             action="#{AutorizacaoCobrancaControle.inserirAutorizacaoCobranca}"
                             styleClass="botoes nvoBt btnVerificacaoCartaoSalvar"
                             oncomplete="Richfaces.hideModalPanel('modalVerificacaoCartao');#{AutorizacaoCobrancaControle.msgAlert}"
                             reRender="panelAutorizacaoCobrancaCliente, dados"/>

            <a4j:commandLink id="btnOkNaoPermitiuCadastrarOcartaoTelaCliente" value="Ok"
                             rendered="#{!AutorizacaoCobrancaControle.verificacaoCartaoSucesso && !AutorizacaoCobrancaControle.permiteCadastrarCartaoMesmoAssim && !AutorizacaoCobrancaControle.origemVendaDePlano}"
                             styleClass="botoes nvoBt btnVerificacaoCartaoOk"
                             oncomplete="Richfaces.hideModalPanel('modalVerificacaoCartao')"
                             reRender="panelAutorizacaoCobrancaCliente, dados, panelAutorizacaoCobranca"/>

            <a4j:commandLink id="btnOkNaoPermitiuCadastrarOcartaoVendaPLano" value="Ok"
                             rendered="#{!AutorizacaoCobrancaControle.verificacaoCartaoSucesso && !AutorizacaoCobrancaControle.permiteCadastrarCartaoMesmoAssim && AutorizacaoCobrancaControle.origemVendaDePlano}"
                             styleClass="botoes nvoBt btnVerificacaoCartaoOk"
                             oncomplete="Richfaces.hideModalPanel('modalVerificacaoCartao');Richfaces.showModalPanel('panelAutorizacaoCobranca')"
                             reRender="panelAutorizacaoCobrancaCliente, dados, panelAutorizacaoCobranca"/>

            <a4j:commandLink id="btnVerificaCartaoMesmo" value="Salvar cart�o"
                             rendered="#{!AutorizacaoCobrancaControle.verificacaoCartaoSucesso && AutorizacaoCobrancaControle.permiteCadastrarCartaoMesmoAssim}"
                             action="#{AutorizacaoCobrancaControle.inserirAutorizacaoCobranca}"
                             styleClass="botoes nvoBt btnVerificacaoCartaoSalvar"
                             oncomplete="Richfaces.hideModalPanel('modalVerificacaoCartao');#{AutorizacaoCobrancaControle.msgAlert}"
                             reRender="panelAutorizacaoCobrancaCliente, dados"/>

            <a4j:commandLink id="btnVerificaCartaoCancelar" value="Cancelar"
                             rendered="#{!AutorizacaoCobrancaControle.verificacaoCartaoSucesso && !AutorizacaoCobrancaControle.origemVendaDePlano && AutorizacaoCobrancaControle.permiteCadastrarCartaoMesmoAssim}"
                             styleClass="botoes nvoBt btnVerificacaoCartaoCancelar"
                             oncomplete="Richfaces.hideModalPanel('modalVerificacaoCartao')"
                             reRender="panelAutorizacaoCobrancaCliente, dados, panelAutorizacaoCobranca"/>

            <a4j:commandLink id="btnVerificaCartaoCancelarVendaPlano" value="Cancelar"
                             rendered="#{!AutorizacaoCobrancaControle.verificacaoCartaoSucesso && AutorizacaoCobrancaControle.origemVendaDePlano && AutorizacaoCobrancaControle.permiteCadastrarCartaoMesmoAssim}"
                             styleClass="botoes nvoBt btnVerificacaoCartaoCancelar"
                             oncomplete="Richfaces.hideModalPanel('modalVerificacaoCartao');Richfaces.showModalPanel('panelAutorizacaoCobranca')"
                             reRender="panelAutorizacaoCobrancaCliente, dados, panelAutorizacaoCobranca"/>
        </h:panelGroup>
    </h:panelGroup>
</rich:modalPanel>

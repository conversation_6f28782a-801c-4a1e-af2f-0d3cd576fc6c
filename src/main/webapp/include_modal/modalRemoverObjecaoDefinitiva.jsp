<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalRemoverObjecaoDefinitiva" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="510"
                 styleClass="novaModal" >
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblRemoverObjecaoDefinitiva" value="Remover Obje��o Definitiva"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formRemoverObjecao" ajaxSubmit="true">
        <h:panelGrid columns="1" width="100%"
                     footerClass="colunaCentralizada" headerClass="subordinado" styleClass="paginaFontResponsiva">

            <h:panelGrid columns="2"  styleClass="paginaFontResponsiva" cellpadding="8"
                         columnClasses="classEsquerda, classDireita"
                         width="100%">
                <h:outputText styleClass="font14 cinza negrito"
                              value="Cliente: "/>
                <h:outputText styleClass="font14 cinza"
                              value="#{TelaClienteControle.nomeClienteObjecao}"/>

                <h:outputText styleClass="font14 cinza negrito"
                              value="Obje��o: "/>
                <h:outputText styleClass="font14 cinza" value="#{TelaClienteControle.objecao}"/>
            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="paginaFontResponsiva">
                <h:outputText styleClass="font14 negrito"
                              style="color: red;"
                              value="Ap�s remover a obje��o definitiva o aluno ir� voltar a entrar nas metas do CRM."/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="container-botoes">

                <a4j:commandLink reRender="paineldadosalunolateral"
                                 id="btnRemoverObjecao"
                                 action="#{TelaClienteControle.removerObjecaoDefinitiva}"
                                 oncomplete="#{TelaClienteControle.onCompleteRemoverObjecao}"
                                 styleClass="botaoPrimario texto-size-14">
                    <h:outputText style="font-size: 14px" value="Remover"/>
                </a4j:commandLink>

                <a4j:commandLink id="cancelarRemoverObjecao"
                                 value="Cancelar"
                                 style="margin-left: 8px"
                                 styleClass="botaoSecundario texto-size-14"
                                 oncomplete="Richfaces.hideModalPanel('modalRemoverObjecaoDefinitiva');"
                                 reRender="paineldadosalunolateral">
                </a4j:commandLink>
            </h:panelGroup>

            <h:panelGrid id="panelMensagemRemoverObjecao" columns="3" width="100%" styleClass="tabMensagens" rendered="#{not empty ClienteControle.mensagemDetalhada}">
                <h:commandButton  rendered="#{ClienteControle.sucesso}" image="./imagens/sucesso.png"/>
                <h:commandButton rendered="#{ClienteControle.erro}" image="./imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>

        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

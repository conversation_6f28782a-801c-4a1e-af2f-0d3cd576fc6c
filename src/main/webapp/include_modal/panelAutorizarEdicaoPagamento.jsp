<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="panelAutorizarEdicaoPagamento" autosized="true"
                 styleClass="novaModal"
                 shadowOpacity="true" width="656"
                 onshow="document.getElementById('formAutorizarEdicaoPagamento:senha').focus()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblPanelAutorizarEdicaoPagamento" value="Confirma��o de Edi��o de Pagamento" style="margin-left: 7px;"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink0"/>
            <rich:componentControl for="panelAutorizarEdicaoPagamento" attachTo="hidelink0"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formAutorizarEdicaoPagamento" styleClass="font-size-Em-max">
        <h:panelGroup layout="block" styleClass="margin-box">
            <h:outputText styleClass="rotuloCampos" value="Para confirma a venda, por favor insira os dados abaixo." style="font-weight: normal !important; margin-left: 5px;"/>

            <h:panelGrid id="panelAutorizarEdicao" width="100%" columns="2" cellpadding="5"
                         styleClass="font-size-Em-max" style="margin-top: 10px;">
                <h:inputText style="opacity:0;height: 0px;" size="5" maxlength="7"/>
                <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                <h:outputText styleClass="rotuloCampos" value="Usu�rio:" style="font-weight: normal !important;"/>
                <h:outputText styleClass="rotuloCampos" value="Insira a senha ou o PIN:"
                              style="font-weight: normal !important; margin-left: 20px;"/>
                <h:inputText styleClass="rotuloCampos" style="font-weight: normal; width: 268px;"
                             value="#{ClienteControle.responsavelEdicaoPagamento.username}">
                    <a4j:support event="onchange"
                                 focus="formUsuarioSenha:senha"
                                 action="#{ClienteControle.consultarResponsavelEdicaoPagamento}"
                                 reRender="usuario, mensagemErro, mensagemGeral"/>
                </h:inputText>

                <h:inputSecret autocomplete="off" id="senha" styleClass="inputTextClean" maxlength="64"
                               style="width: 268px; margin-left: 20px;"
                               value="#{ClienteControle.responsavelEdicaoPagamento.senha}"
                               onkeypress="validarEnter(event,'formAutorizarEdicaoPagamento:autorizarEdicao')"/>
            </h:panelGrid>
            <h:panelGroup layout="block" styleClass="margin-box">
                <h:outputText styleClass="text" value="#{ClienteControle.mensagem}"/>
                <h:outputText id="mensagemAutorizacaoFuncionalidade" style="display: block;margin-top: 5px;"
                              styleClass="text texto-size-16 texto-font texto-cor-vermelho"
                              value="#{ClienteControle.mensagemDetalhada}"/>
            </h:panelGroup>


            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink id="autorizarEdicao"
                                 value="#{msg_bt.btn_confirmar}"
                                 action="#{ClienteControle.autorizarEdicaoPagamento}"
                                 oncomplete="#{ClienteControle.mensagemNotificar}"
                                 reRender="formAutorizarEdicaoPagamento, tabMensagens, panelMensagemInferior, panelMensagemSuperior"
                                 styleClass="botaoPrimario texto-size-16"/>
            </h:panelGroup>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalAgendaExcecao" autosized="true" shadowOpacity="true" width="950"
                 height="450" onshow="focusAt('okButton');"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Hist�rico de Sess�es Justificadas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkmodalAgendaExcecao"/>
            <rich:componentControl for="modalAgendaExcecao"
                                   attachTo="hidelinkmodalAgendaExcecao" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form>
        <div class="inlineBlock" style="padding: 10px 10px 10px 0px;">
            <h:outputText style="font-weight: bold;" value="Ver todos" styleClass="text"/>
            <div class="chk-fa-container inline ">
                <h:selectBooleanCheckbox
                        id="selecionado-verTodosModalAgenda"
                        value="#{clienteEstudioControle.verTodosModalAgenda}">
                    <a4j:support event="onclick" action="#{clienteEstudioControle.acaoListarAgendaExcecao}"
                                 ajaxSingle="true"
                                 reRender="clienteEstudioControle-listaAgendaExcecao-modal"/>
                </h:selectBooleanCheckbox>
                <span style="top: 0px;"/>
            </div>
        </div>
        <h:panelGrid id="mainPanelModalAgendaExcecao" columns="1" width="100%">
            <h:panelGroup layout="block" style="height:300px; overflow-x: hidden; overflow-y: scroll;">
                <style>
                    .tblestudio .rich-table-cell{
                        font-size: 12px !important;
                    }
                </style>
                <rich:dataTable
                        id="clienteEstudioControle-listaAgendaExcecao-modal"
                        width="100%"
                        styleClass="tabelaDados tblestudio"
                        style="width: 100% !important; margin: 0px !important; font-size: 12px !important;"
                        value="#{clienteEstudioControle.listaAgendaExcecao}"
                        var="item">

                    <rich:column
                            id="item-dataLancamento"
                            sortable="true"
                            width="110px"
                            selfSorted="true"
                            sortBy="#{item.dataLancamento}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Data Justificada"/>
                        </f:facet>
                        <h:outputText value="#{item.dataLancamento}" converter="dataConverter"/>
                        -
                        <h:outputText value="#{item.dataLancamento}" converter="timeConverter"/>
                    </rich:column>

                    <rich:column
                            id="item-dataAula"
                            sortable="true"
                            width="150px"
                            selfSorted="true"
                            sortBy="#{item.dataAula}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Data Aula"/>
                        </f:facet>
                        <h:outputText value="#{item.dataAula}" converter="dataConverter" escape="false"/>
                        <h:outputText value="- #{item.diaSemana_Apresentar}"/>
                    </rich:column>

                    <rich:column
                            id="item-horario"
                            sortable="true"
                            width="20px"
                            selfSorted="true"
                            sortBy="#{item.horaInicio}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Hor�rio"/>
                        </f:facet>
                        <h:outputText value="#{item.horaInicio_Apresentar}"/>
                    </rich:column>
                    <rich:column
                            id="item-produto-venda-excecao"
                            width="105px"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.produtoVO.descricao}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Produto"/>
                        </f:facet>
                        <h:outputText value="#{item.produtoVO.descricao}"/>
                    </rich:column>
                    <rich:column
                            id="item-professor-excecao"
                            width="200px"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.colaboradorVO.pessoa.nome}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Colaborador"/>
                        </f:facet>
                        <h:outputText value="#{item.colaboradorVO.pessoa.nome}"/>
                    </rich:column>
                    <rich:column
                            id="item-ambiente-excecao"
                            width="120px"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.ambienteVO.descricao}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Ambiente"/>
                        </f:facet>
                        <h:outputText value="#{item.ambienteVO.descricao}"/>
                    </rich:column>
                    <rich:column
                            id="item-status-excecao"
                            width="90px"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.resolvidoComo}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Status"/>
                        </f:facet>
                        <h:outputText
                                value="#{item.resolvidoComo == '0' ? 'Pendente' : item.resolvidoComo == '1' ? 'Remarca��o' : 'Prescreveu Remarca��o'}"/>
                    </rich:column>
                    <rich:column
                            id="item-dataHoraNovoAgendamento"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.dataNovoAgendamento}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Data Remarca��o"/>
                        </f:facet>
                        <h:outputText value="#{item.dataNovoAgendamento}" converter="dataConverter" escape="false"/> -
                        <h:outputText value="#{item.horaNovoAgendamento}" converter="timeConverter"/>
                    </rich:column>
                    <rich:column
                            id="item-tipoHorarioVO-excecao"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.tipoHorarioVO.descricao}"
                            filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Tipo Hor�rio"/>
                        </f:facet>
                        <h:outputText value="#{item.tipoHorarioVO.descricao}"/>
                    </rich:column>

                    <rich:column
                            width="30px"
                            id="item-pendente">

                        <div class="chk-fa-container inline ">
                            <h:selectBooleanCheckbox
                                    id="itemSolicitacao-selecionado-pendente"
                                    value="#{item.selecionarPendente}">
                                <a4j:support
                                        action="#{clienteEstudioControle.acaoSelecionarUmPendente}"
                                        event="onclick"
                                        status="statusHora"
                                        ajaxSingle="true"
                                        reRender="clienteEstudioControle-listaAgendaExcecao-modal">
                                    <f:setPropertyActionListener
                                            value="#{item}"
                                            target="#{clienteEstudioControle.agendaEstudio}"/>
                                </a4j:support>
                            </h:selectBooleanCheckbox>
                            <span style="top: 0px;"/>
                        </div>


                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <h:panelGroup layout="block" style="margin-top: 15px;">
                <a4j:commandLink styleClass="pure-button pure-button-small texto-size-14"
                                 style="float: left;"
                                 oncomplete="#{rich:component('modalAgendaExcecao')}.hide();"
                                 status="statusHora">
                    Fechar
                </a4j:commandLink>
                <a4j:commandLink style="float: right;"
                                 id="btnRemarcar2"
                                 styleClass="pure-button pure-button-small pure-button-primary texto-size-14"
                                 reRender="panelAgendaAluno,modalPanelErro"
                                 ajaxSingle="true"
                                 action="#{clienteEstudioControle.acaoPanelAgendaAluno}"
                                 oncomplete="#{rich:component('modalAgendaExcecao')}.hide(); "
                                 title="Remarcar" status="statusHora">
                    <i class="fa-icon-calendar-check-o"></i> Remarcar
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>






    </a4j:form>
</rich:modalPanel>


<rich:modalPanel id="modalPanelErro" autosized="true" shadowOpacity="true"
                 showWhenRendered="#{clienteEstudioControle.apresentarRichModalErro}" width="450"
                 height="80" onshow="focusAt('fecharButton');"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Aten��o!"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <a4j:form>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagemDetalhada" value="#{clienteEstudioControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:panelGrid>
        <rich:spacer height="05px"/>
        <h:panelGrid style="position: relative; float:right; ">



            <a4j:commandLink styleClass="pure-button pure-button-small texto-size-14"
                             id="fecharButton"
                             ajaxSingle="true"
                             title="Fechar" status="statusHora"
                             action="#{clienteEstudioControle.acaoFecharModalErro}" reRender="modalPanelErro">
                Fechar
            </a4j:commandLink>
        </h:panelGrid>
    </a4j:form>

</rich:modalPanel>

<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="panelAgendaAluno" autosized="true" shadowOpacity="true"
                 showWhenRendered="#{clienteEstudioControle.mostrarPanelAluno}"
                 styleClass="novaModal"
                 width="420" height="290" onshow="focusAt('panelAluno-matricula');">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Agenda Aluno" />
        </h:panelGroup>
    </f:facet>
    <h:form>
        <%@include  file="../includes/include_identificadorModuloEstudio.jsp" %>
        <a4j:keepAlive beanName="clienteEstudioControle" />
        <h:panelGrid columns="2" >
            <h:outputText value="Matr�cula*" styleClass="texto_agenda"/>
            <h:outputText value="Aluno*" styleClass="texto_agenda"/>
            <h:inputText value="#{clienteEstudioControle.agendaEstudio.clienteVO.codigoMatricula}"
                         onblur="blurinput(this);"
                         id="panelAluno-matricula"
                         onfocus="focusinput(this);"
                         styleClass="form inputAntigo"
                         style="background-color: #D2D2D2; color: #666666; background-image: none;"
                         disabled="true"
                         autocomplete="off"
                         onkeydown="return tabOnEnter(event, 'panelAluno-nome');"
                         size="12">
            </h:inputText>
            <h:inputText value="#{clienteEstudioControle.agendaEstudio.clienteVO.pessoa.nome}"
                         disabled="true"
                         style="width:200px; background-color: #D2D2D2; color: #666666; background-image: none;"
                         onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         id="panelAluno-nome"
                         styleClass="form inputAntigo"
                         onkeydown="return tabOnEnter(event, 'panelAluno-servico');"/>
        </h:panelGrid>


        <h:panelGrid columns="2" cellpadding="2">
            <h:outputLabel
                    id="lb-servico"
                    style="width:200px"
                    styleClass="classLabel texto_agenda"
                    value="Servi�o*"/>

            <h:outputLabel id="lbl-status" styleClass="classLabel texto_agenda" value="Status:"/>


            <h:selectOneMenu
                    id="panelAluno-servico"
                    style="width:200px; background-color: #D2D2D2; color: #666666;"
                    value="#{clienteEstudioControle.agendaEstudio.produtoVO}"
                    title="Servi�o"
                    disabled="true"
                    converter="produtoConverter"
                    onkeydown="return tabOnEnter(event, 'listaAmbienteModal');"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    styleClass="form">
                <f:selectItem itemLabel="#{clienteEstudioControle.agendaEstudio.produtoVO.descricao}" itemValue="#{clienteEstudioControle.agendaEstudio.produtoVO.codigo}"/>
            </h:selectOneMenu >

            <h:selectOneMenu
                    id="panelAluno-status"
                    disabled="true"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    styleClass="form"
                    onkeydown="return tabOnEnter(event, 'panelAluno-horario');"
                    style="width:140px; background-color: #D2D2D2; color: #666666;"
                    value="#{clienteEstudioControle.agendaEstudio.status}"
                    title="Status" >
                <f:selectItem itemLabel="#{clienteEstudioControle.agendaEstudio.status.descricao}" itemValue="#{clienteEstudioControle.agendaEstudio.status.id}" />
            </h:selectOneMenu>
            <h:selectOneMenu
                    id="panelAluno-servico-generico"
                    style="width:200px;"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    onkeydown="return tabOnEnter(event, 'panelAluno-horario');"
                    rendered="#{clienteEstudioControle.agendaEstudio.codigo > 0 && clienteEstudioControle.agendaEstudio.produtoVO.descricao=='PRODUTO GEN�RICO'}"
                    value="#{clienteEstudioControle.agendaEstudio.produtoGenericoEscolhidoVO.codigo}"
                    title="Servi�o"
                    styleClass="form">
                <f:selectItem itemLabel="Selecione um Servi�o!" />
                <f:selectItems value="#{clienteEstudioControle.servicoGenericoCombo}"  />
                <a4j:support event="onchange" />
            </h:selectOneMenu >
            <h:outputText value="" rendered="#{clienteEstudioControle.agendaEstudio.produtoVO.descricao=='PRODUTO GEN�RICO'}"/>

            <h:outputText value="Ambiente*:" styleClass="texto_agenda"  />
            <h:outputLabel id="lbl-data" styleClass="classLabel texto_agenda" value="Data da Aula*:"/>

            <h:selectOneMenu
                    style="width:200px;"
                    value="#{clienteEstudioControle.agendaEstudio.ambienteVO}"
                    id="listaAmbienteModal"
                    onblur="blurinput(this);"
                    converter="ambienteConverter"
                    onfocus="focusinput(this);"
                    styleClass="form"
                    onkeydown="return tabOnEnter(event, 'tipoHorario');"
                    title="Ambiente">
                <f:selectItems  id="selectItemAmbiente" value="#{clienteEstudioControle.ambienteCombo}"/>
                <a4j:support event="onchange" reRender="panelAluno-horario"
                             action="#{clienteEstudioControle.listarHorarios}"/>
            </h:selectOneMenu>


            <rich:calendar
                    locale="pt/BR"
                    inputSize="10"
                    inputClass="form inputAntigo"
                    oninputblur="blurinput(this);"
                    oninputfocus="focusinput(this);"
                    oninputchange="return validar_Data(this.id);"
                    oninputkeyup="return tabOnEnter(event, 'panelAluno-horario');"
                    datePattern="dd/MM/yyyy"
                    enableManualInput="true"
                    zindex="2"
                    showWeeksBar="false"
                    value="#{clienteEstudioControle.agendaEstudio.dataAula}"
                    id="panelAluno-dataAula"
                    popup="true" >
                <a4j:support event="onchanged"
                             action="#{clienteEstudioControle.mudancaData}"
                             reRender="selectItemProfissional, listaAmbienteModal, panelAluno-ambienteSuggestion, panelAluno-horario"/>
            </rich:calendar>

            <h:outputText value="Profissional*:" styleClass="texto_agenda" />

            <h:outputLabel
                    id="lb-tipoHorario"
                    style="width:200px"
                    styleClass="classLabel texto_agenda"
                    value="Tipo Hor�rio*:"/>

            <h:selectOneMenu
                    style="width:200px;"
                    value="#{clienteEstudioControle.agendaEstudio.colaboradorVO}"
                    id="listaProfissionalModal"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    styleClass="form"
                    converter="colaboradorConverter"
                    onkeydown="return tabOnEnter(event, 'tipoHorario');"
                    title="Profissional">
                <f:selectItems  id="selectItemProfissional" value="#{clienteEstudioControle.profissionalCombo}"/>
                <a4j:support event="onchange" reRender="panelAluno-horario"
                             action="#{clienteEstudioControle.listarHorarios}" />
            </h:selectOneMenu>

            <h:selectOneMenu
                    id="tipoHorario"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    styleClass="form"
                    onkeydown="return tabOnEnter(event, 'panelAluno-observacao');"
                    value="#{clienteEstudioControle.agendaEstudio.tipoHorarioVO.codigo}"
                    title="Tipo de Hor�rio" >
                <f:selectItems value="#{clienteEstudioControle.buscarListaTipoHorarios}" />
            </h:selectOneMenu>

            <h:panelGrid columns="1" style="float: right;">
                <h:outputLabel
                        id="lb-observacoes"
                        style="width:150px"
                        styleClass="classLabel texto_agenda"
                        value="Observa��es:"/>

                <h:inputTextarea
                        onblur="blurinput(this);"
                        id="panelAluno-observacao"
                        onfocus="focusinput(this);"
                        disabled="true"
                        styleClass="form"
                        value="#{clienteEstudioControle.agendaEstudio.observacao}"
                        style="width:200px; min-width: 200px; height:50px; min-height: 30px;
                    background-color: #D2D2D2; color: #666666; background-image: none;">
                </h:inputTextarea>
            </h:panelGrid>

            <h:panelGrid columns="1">
                <h:outputLabel
                        id="lb-hora"
                        styleClass="classLabel texto_agenda"
                        rendered="#{clienteEstudioControle.apresentarBotaoRetirarHorario!='S'
                                && clienteEstudioControle.apresentarBotaoRetirarHorario!=''}"
                        value="Hora*:">
                    <a4j:commandButton id="addHorario"
                                       rendered="#{clienteEstudioControle.apresentarBotaoRetirarHorario!='S'
                                                   && clienteEstudioControle.apresentarBotaoRetirarHorario!=''}"
                                       action="#{clienteEstudioControle.adicionarHorario}"
                                       title="Adicionar hor�rio com intervalo diferente de uma hora"
                                       image="#{context}/imagens/estudio/adicionar_mais.png" reRender="panelAgendaAluno,panelAluno-horario"/>
                </h:outputLabel>

                <h:selectOneMenu
                        id="panelAluno-horario"
                        onblur="blurinput(this);"
                        onfocus="focusinput(this);"
                        styleClass="form"
                        onkeydown="return tabOnEnter(event, 'panelAluno-salvar');"
                        style="width:140px"
                        rendered="#{clienteEstudioControle.apresentarBotaoRetirarHorario!='S'
                                && clienteEstudioControle.apresentarBotaoRetirarHorario!=''}"
                        value="#{clienteEstudioControle.timeSelecionado}"
                        title="Hor�rio" >
                    <f:selectItem itemLabel="Selecione um hor�rio!" itemValue="-1"/>
                    <f:selectItems value="#{clienteEstudioControle.selectItemHorarios}" />
                </h:selectOneMenu>
                <a4j:commandButton id="retHorario" rendered="#{clienteEstudioControle.apresentarBotaoRetirarHorario=='S'}"
                                   reRender="panelAgendaAluno,panelAluno-horario"
                                   action="#{clienteEstudioControle.retirarHorario}"
                                   image="#{context}/imagens/estudio/adicionar_menos.png"/>

                <h:panelGrid columns="2" rendered="#{clienteEstudioControle.apresentarBotaoRetirarHorario=='S'}">
                    <h:outputLabel id="lb-horainicial"
                                   styleClass="classLabel texto_agenda"
                                   value="Hora Inicial*:"/>
                    <h:inputText id="horarioInicial" size="7" maxlength="5"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{clienteEstudioControle.timeHorarioInicial}" >
                    </h:inputText>
                    <h:outputLabel id="lb-horafinal"
                                   styleClass="classLabel texto_agenda"
                                   value="Hora Final*:"/>
                    <h:inputText id="horarioFinal" size="7" maxlength="5"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{clienteEstudioControle.timeHorarioFinal}">
                    </h:inputText>
                </h:panelGrid>

                <h:outputLabel  rendered="#{clienteEstudioControle.apresentarBotaoRetirarHorario!='S'
                                            && clienteEstudioControle.apresentarBotaoRetirarHorario!=''}"

                                id="lb-hora-desc"
                                styleClass="classLabel texto_agenda"
                                value="60min"/>
            </h:panelGrid>
        </h:panelGrid>

        <h:panelGrid columns="1" style="float: left;">
            <a4j:commandLink styleClass="pure-button pure-button-small texto-size-14"
                             id="okButtonAgenda"
                             action="#{clienteEstudioControle.acaoFecharPanelAgendaAluno}"
                             oncomplete="#{rich:component('panelAgendaAluno')}.hide();"
                             title="Fechar" status="statusHora">
                Fechar
            </a4j:commandLink>
        </h:panelGrid>

        <h:panelGrid columns="1" style="float: right;">
            <a4j:commandLink styleClass="pure-button pure-button-small pure-button-primary texto-size-14"
                             id="panelAluno-salvar"
                             reRender="modalPanelErro,outLista"
                             action="#{clienteEstudioControle.acaoSalvarAgenda}"
                             oncomplete="#{rich:component('panelAgendaAluno')}.hide();" >

                <i class="fa-icon-save"></i> &nbsp;Salvar

                <f:setPropertyActionListener
                        value="#{clienteEstudioControle.agendaEstudio}"
                        target="#{clienteEstudioControle.agendaEstudio}" />
            </a4j:commandLink>
        </h:panelGrid>
    </h:form>
</rich:modalPanel>
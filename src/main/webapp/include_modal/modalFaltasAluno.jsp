<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalFaltasAluno" autosized="true" shadowOpacity="true" styleClass="novaModal noMargin" width="500" >

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="pnlModalFaltasAluno" value="Faltas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hFaltasAluno"/>
            <rich:componentControl for="modalFaltasAluno" attachTo="hFaltasAluno" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formListaFaltasAluno">
        <rich:dataTable value="#{ClienteControle.listaAulasComFaltas}"
                        rows="10"
                        id="listaAlunoFaltas"
                        rowKeyVar="status"
                        width="87%"
                        var="item" styleClass="tabelaSimplesCustom">
            <rich:column sortBy="#{item.dataAula}" >
                <f:facet name="header">
                    <h:outputText value="Descri��o da aula"/>
                </f:facet>
                <div  style="width:400px;text-align: center;">
                    <h:outputText value="#{item.descricaoAulaApresentar}"/>
                </div>
            </rich:column>
            <rich:column>
                <a4j:commandLink value="Desmarcar" styleClass="linkAzul"
                                 rendered="#{TelaClienteControle.contratoSelecionado.situacao == 'AT'}"
                                 ajaxSingle="true"
                                 id="demasmarAula1"
                                 actionListener="#{ClienteControle.prepararConsultaTurmaListenerDesmarcarNovo}"
                                 reRender="formAulaDesmarcada, form:panelOpracoesContrato"
                                 oncomplete="Richfaces.showModalPanel('modalPanelAulaDesmarcada');Richfaces.hideModalPanel('modalFaltasAluno');">
                    <f:attribute name="horarioTurma"
                                 value="#{item.id}"/>
                    <f:attribute name="horario"
                                 value="#{item.inicio}"/>
                </a4j:commandLink>
            </rich:column>
        </rich:dataTable>

        <h:panelGroup layout="block" style="margin: 20px auto;">
            <rich:datascroller align="center"
                               for="listaAlunoFaltas" maxPages="100"
                               id="scResultadoListaAlunoFaltas" />
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>

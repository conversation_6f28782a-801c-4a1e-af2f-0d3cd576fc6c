<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_OperadoraCartao_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_OperadoraCartao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Config._Financeiras:Operadora_de_Cartão"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
        <hr style="border-color: #e6e6e6;"/>
        <input type="hidden" value="${modulo}" name="modulo"/>
        
            <h:commandLink action="#{OperadoraCartaoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" width="100%"  >

                <h:panelGrid id="dados" columns="2" width="100%" rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_OperadoraCartao_descricao}" />
                    <h:inputText  id="descricao" size="70" maxlength="100" disabled="true"
                                  title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                  value="#{OperadoraCartaoControle.operadoraCartaoVO.descricao}" />


                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_OperadoraCartao_ativo}:"/>
                    <h:selectBooleanCheckbox id="ativo" value="#{OperadoraCartaoControle.operadoraCartaoVO.ativo}"/>

                    <h:outputText rendered="#{OperadoraCartaoControle.operadoraCartaoVO.credito}"
                            styleClass="tituloCampos" value="Padrão para recebimento em cartão de crédito:"
                            title="Essa configuração define qual Operadora será selecinada por padrão no Caixa em Aberto ao escolher uma Forma de Pagamento do tipo Cartão de Crédito."/>
                    <h:outputText rendered="#{!OperadoraCartaoControle.operadoraCartaoVO.credito}"
                                  styleClass="tituloCampos" value="Padrão para recebimento em cartão de débito:"/>
                    <h:selectBooleanCheckbox id="padraoRecebimento"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.padraoRecebimento}"/>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_OperadoraCartao_credito}" />
                    <h:selectBooleanCheckbox id="tipocartao" value="#{OperadoraCartaoControle.operadoraCartaoVO.credito}">
                        <a4j:support event="onclick" reRender="dados" action="#{OperadoraCartaoControle.metodoVazio}"/>
                    </h:selectBooleanCheckbox>

                    <h:outputText id="qtdemaxparcelas1" rendered="#{OperadoraCartaoControle.operadoraCartaoVO.credito}" styleClass="tituloCampos" value="#{msg_aplic.prt_OperadoraCartao_qtdeparcelas}" />
                    <h:inputText id="qtdemaxparcelas2" rendered="#{OperadoraCartaoControle.operadoraCartaoVO.credito}" size="10" maxlength="10"  value="#{OperadoraCartaoControle.operadoraCartaoVO.qtdeMaxParcelas}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracao}" />
<%--                    <c:if test="${!OperadoraCartaoControle.isUruguay()}">--%>
                        <h:selectOneMenu  id="codigoIntegracao"
                                          disabled="true"
                                          title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                          value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracao}" >
                            <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasExternas}"/>
<%--                            <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracao}"--%>
<%--                                     reRender="form" event="onchange"/>--%>
                        </h:selectOneMenu>
<%--                    </c:if>--%>
<%--                    <c:if test="${OperadoraCartaoControle.isUruguay()}">--%>
<%--                        <h:selectOneMenu  id="codigoIntegracaoGeoitd"  value="#{OperadoraCartaoControle.operadoraCartaoVO.bandeirasGeoitd}"--%>
<%--                                          onblur="blurinput(this);" onfocus="focusinput(this);">--%>
<%--                            <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasGeoitd}"/>--%>
<%--                        </h:selectOneMenu>--%>
<%--                    </c:if>--%>
                    <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoAPF}" />
                        <h:panelGroup id="dadosAPF">
                            <h:selectOneMenu id="codigoIntegracaoAPF"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoAPF}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasExternasAprovaFacil}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoVindi}"/>
                        <h:panelGroup id="dadosVindi">
                            <h:selectOneMenu id="codigoIntegracaoVindi"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoVindi}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasExternasAprovaFacil}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoVindi}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoERede}"/>
                        <h:panelGroup id="dadosERede">
                            <h:selectOneMenu id="codigoIntegracaoERede"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoERede}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasExternasAprovaFacil}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoERede}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoMaxiPago}"/>
                        <h:panelGroup id="dadosMaxiPago">
                            <h:selectOneMenu id="codigoIntegracaoMaxiPago"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoMaxiPago}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasExternasAprovaFacil}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoMaxiPago}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoFitnessCard}"/>
                        <h:panelGroup id="dadosCodigoIntegracaoFitnessCard">
                            <h:selectOneMenu id="codigoIntegracaoFitnessCard"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoFitnessCard}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasExternasAprovaFacil}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoFitnessCard}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" value="Bandeira GetNet Online:"/>
                        <h:panelGroup id="dadosGetNet">
                            <h:selectOneMenu id="codigoIntegracaoGetNet"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoGetNet}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasGetNet}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoGetNet}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoCielo}"/>
                        <h:panelGroup id="dadosCielo">
                            <h:selectOneMenu id="codigoIntegracaoCielo"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoCielo}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasExternasAprovaFacil}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoCielo}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" value="Tipo Débito Online:"/>
                        <h:panelGroup id="panelTipoDebitoOnline">
                            <h:selectOneMenu id="codigoTipoDebitoOnline"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.tipoDebitoOnline}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaTipoDebitoOnline}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" value="Operadora Débito Online:"/>
                        <h:panelGroup id="panelCodigoIntegracaoCieloDebito">
                            <h:selectOneMenu id="codigoIntegracaoCieloDebito"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoDebito}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaOperadorasExternasAprovaFacil}"/>
                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoDebito}"
                                             reRender="form" event="onchange"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" value="Bandeira Cappta:"/>
                        <h:panelGroup id="panelCodigoIntegracaoCappta">
                            <h:selectOneMenu id="codigoIntegracaoCappta"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.bandeiraCappta}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasCappta}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoStone}"/>
                        <h:panelGroup id="panelCodigoIntegracaoStoneOnline">
                            <h:selectOneMenu id="codigoIntegracaoStoneOnline"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoStoneOnline}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasStone}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoStoneOnline}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoMundiPagg}"/>
                        <h:panelGroup id="panelCodigoIntegracaoMundiPagg">
                            <h:selectOneMenu id="codigoIntegracaoMundiPagg"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoMundiPagg}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasMundiPagg}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoMundiPagg}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_OperadoraCartao_codigoIntegracaoPagarMe}"/>
                        <h:panelGroup id="panelCodigoIntegracaoPagarMe">
                            <h:selectOneMenu id="codigoIntegracaoPagarMe"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoPagarMe}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasPagarMe}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoPagarMe}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="Bandeira Stripe:"/>
                        <h:panelGroup id="panelCodigoIntegracaoStripe">
                            <h:selectOneMenu id="codigoIntegracaoStripe"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoStripe}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasStripe}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoStripe}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="Bandeira FacilitePay:"/>
                        <h:panelGroup id="panelCodigoIntegracaoFacilitePay">
                            <h:selectOneMenu id="codigoIntegracaoFacilitePay"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoFacilitePay}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasFacilitePay}"/>
<%--                                <a4j:support action="#{OperadoraCartaoControle.selecionarCodigoIntegracaoFacilitePay}"--%>
<%--                                             reRender="form" event="onchange"/>--%>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="Bandeira DCC Caixa Online:"/>
                        <h:panelGroup id="panelCodigoIntegracaoDCCCaixaOnline">
                            <h:selectOneMenu id="codigoIntegracaoDCCCaixaOnline"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoDCCCaixaOnline}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasDCCCaixaOnline}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="Bandeira Pagolivre:"/>
                        <h:panelGroup id="panelCodigoIntegracaoPagolivre">
                            <h:selectOneMenu id="codigoIntegracaoPagolivre"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoPagoLivre}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasPagolivre}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="Bandeira PinBank:"/>
                        <h:panelGroup id="panelCodigoIntegracaoPinBank">
                            <h:selectOneMenu id="codigoIntegracaoPinBank"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoPinBank}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasPinBank}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="Bandeira PagBank:"/>
                        <h:panelGroup id="panelCodigoIntegracaoPagBank">
                            <h:selectOneMenu id="codigoIntegracaoPagBank"
                                             disabled="true"
                                             title="#{OperadoraCartaoControle.mensagemAjusteBandeiras}"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoPagBank}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasPagBank}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      value="Bandeira Stone Online V5:"/>
                        <h:panelGroup id="panelCodigoIntegracaoStoneV5">
                            <h:selectOneMenu id="codigoIntegracaoStoneV5"
                                             value="#{OperadoraCartaoControle.operadoraCartaoVO.codigoIntegracaoStoneOnlineV5}">
                                <f:selectItems value="#{OperadoraCartaoControle.listaBandeirasPagBank}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </c:if>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{OperadoraCartaoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{OperadoraCartaoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{OperadoraCartaoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{OperadoraCartaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                            <!--A situação da Banderira ser um Cadastro, está gerando problemas, pois fica gerando vários cadastros para mesma Bandeira, atrapalhando integrações com outros sistemas.
                            O ideal é ter apenas um para cada Bandeira. Então, geramos no Banco de Importação um cadastro para cada Operadora (Bandeira).
                            Agora, bloqueamos as opções de Cadastrar ou Editar para apenas o Admin da Pacto.-->
<%--                                <a4j:commandButton id="novo" immediate="true" action="#{OperadoraCartaoControle.novo}" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>--%>
<%--                                <rich:spacer width="10"/>--%>
                                <a4j:commandButton id="salvar"
                                                   action="#{OperadoraCartaoControle.gravar}"
                                                   reRender="panelGridMensagens"
                                                   value="#{msg_bt.btn_gravar}"
                                                   title="#{msg.msg_gravar_dados}"
                                                   accesskey="2"
                                                   styleClass="botoes nvoBt"/>
<%--                                <rich:spacer width="10"/>--%>
<%--    --%>
<%--                                <h:panelGroup id="grupoBtnExcluir">--%>
<%--                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"--%>
<%--                                                   oncomplete="#{OperadoraCartaoControle.msgAlert}" action="#{OperadoraCartaoControle.confirmarExcluir}"--%>
<%--                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>--%>

                                <rich:spacer width="10"/>
                                <a4j:commandButton id="consultar" immediate="true" action="#{OperadoraCartaoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                                <h:outputText value="    "/>
                                <a4j:commandLink action="#{OperadoraCartaoControle.realizarConsultaLogObjetoSelecionado}"
                                                   reRender="form"
                                                   oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                   title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                    <i class="fa-icon-list"></i>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </c:if>
                        
                        <!-- ------------------CE------------------------------ -->
                        <c:if test="${modulo eq 'centralEventos'}">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{OperadoraCartaoControle.novo}"
                            value="#{msg_bt.btn_novo}"
                            title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="10"/>
                            
                            <a4j:commandButton id="salvar" action="#{OperadoraCartaoControle.gravarCE}"
                            value="#{msg_bt.btn_gravar}"
                            reRender="panelGridMensagens"
                            title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                            actionListener="#{OperadoraCartaoControle.autorizacao}">
							<!-- entidade.OperadoraCartao  -->
							<f:attribute name="entidade" value="124"/>
							<!-- operacao.gravar  -->
							<f:attribute name="operacao" value="G"/>
                            </a4j:commandButton>
                            
                            <rich:spacer width="10"/>
                            <a4j:commandButton id="excluir"   onclick="if(!confirm('#{msg.msg_ConfirmaExclusao}')){return true;}"
                            action="#{OperadoraCartaoControle.excluirCE}" value="#{msg_bt.btn_excluir}" 
                            title="#{msg.msg_excluir_dados}"
                            accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                            actionListener="#{OperadoraCartaoControle.autorizacao}">
							<!-- entidade.OperadoraCartao  -->
							<f:attribute name="entidade" value="124"/>
							<!-- operacao.excluir  -->
							<f:attribute name="operacao" value="E"/>							
							</a4j:commandButton>
                            
                            <rich:spacer width="10"/>
                            <a4j:commandButton id="consultar" immediate="true"
                            action="#{OperadoraCartaoControle.inicializarConsultar}" 
                            value="#{msg_bt.btn_consultar}"
                            title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"
                            actionListener="#{OperadoraCartaoControle.autorizacao}">
							<!-- entidade.OperadoraCartao  -->
							<f:attribute name="entidade" value="124"/>
							<!-- operacao.consulta  -->
							<f:attribute name="operacao" value="C"/>							
							</a4j:commandButton>
                            
                        </h:panelGroup>
                        </c:if>
                        
                        </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:codigo").focus();
</script>

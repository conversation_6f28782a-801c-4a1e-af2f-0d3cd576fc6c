<%-- 
    Document   : doUpdateServlet
    Created on : 14/08/2012, 16:30:57
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
    <%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
    <%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
    <%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
    <%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
    <%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp"%>
    <%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@include file="includes/imports.jsp" %>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
        <head>
            <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
            <title>SQL Service</title>
            <style type="text/css">
                .text{
                    margin-left: 5px;
                    font-size:11px;
                    font-family:Arial;
                }
            </style>
        </head>
        <body style="">
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
            <h:form>
                <h:panelGrid columns="2" columnClasses="text,text" id="tudo"
                             style="margin: 20px;">
                    
                    <h:outputText value="Início" />
                    <rich:calendar value="#{ExcluirFinanceiroControle.diaInicio}"
                                   inputSize="10"
                                   inputClass="form"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   oninputchange="return validar_Data(this.id);"
                                   showWeeksBar="false" />
                    <h:outputText value="Fim" />
                    <rich:calendar value="#{ExcluirFinanceiroControle.dataLimite}"
                                   inputSize="10"
                                   oninputchange="return validar_Data(this.id);"
                                   inputClass="form"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <h:outputText value="Todas as contas" />
                    <h:selectBooleanCheckbox value="#{ExcluirFinanceiroControle.detodasascontas}">
                        <a4j:support event="onclick"
                                     reRender="tudo"/>
                    </h:selectBooleanCheckbox>
                    <h:outputText value="Contas" rendered="#{!ExcluirFinanceiroControle.detodasascontas}"/>
                    <h:panelGroup rendered="#{!ExcluirFinanceiroControle.detodasascontas}">
                        <h:selectOneMenu value="#{ExcluirFinanceiroControle.contaSelecionada}">
                            <f:selectItems value="#{ExcluirFinanceiroControle.contas}"/>
                        </h:selectOneMenu>
                        <a4j:commandLink action="#{ExcluirFinanceiroControle.adicionarConta}" value="Adicionar" 
                                         reRender="tudo"/>
                    </h:panelGroup>
                    <h:outputText value="Contas adicionadas" />
                    <h:panelGroup>
                        <rich:dataTable value="#{ExcluirFinanceiroControle.contasSelecionadas}"
                                        var="conta">
                            <rich:column>
                                <h:outputText value="#{conta.descricao}" />
                            </rich:column>
                            <rich:column>
                                <a4j:commandLink action="#{ExcluirFinanceiroControle.removerConta}" value="REMOVER"
                                                 reRender="tudo,idlog">

                                </a4j:commandLink>

                            </rich:column>
                        </rich:dataTable>
                    </h:panelGroup>
                    <h:outputText value="Usuário OAMD" />
                    <h:inputText value="#{ExcluirFinanceiroControle.user}"></h:inputText>
                    <h:outputText value="Senha OAMD" />
                    <h:inputSecret value="#{ExcluirFinanceiroControle.senha}"></h:inputSecret>
                    
                    
                    <h:outputText value="MENSAGEM: "/>
                    <h:outputText value="#{ExcluirFinanceiroControle.mensagem}"/>
                    
                    <a4j:commandButton reRender="tudo" action="#{ExcluirFinanceiroControle.excluir}" value="EXCLUIR"
                                       onclick="if(!confirm('Certeza?')){return false;};"/>

                </h:panelGrid>
                <h:outputText value="LOG:" style="font-weight: bold; margin: 20px;" />
                <rich:dataTable value="#{ExcluirFinanceiroControle.logs}"
                                var="log"
                                id="idlog">
                    <rich:column>
                        <h:outputText value="#{log.responsavelAlteracao}" />
                    </rich:column>
                    <rich:column>
                        <h:outputText value="#{log.dataHoraAlteracao_Apresentar}" />
                    </rich:column>
                    <rich:column>
                        <h:outputText value="#{log.valorCampoAlterado}" />
                    </rich:column>

                </rich:dataTable>
                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
            </h:form>
        </body>
    </html>
</f:view>

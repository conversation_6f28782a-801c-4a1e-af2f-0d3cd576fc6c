<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="rich" uri="http://richfaces.org/rich" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-ext.css" rel="stylesheet" type="text/css"/>
<jsp:include page="include_head.jsp" flush="true" />
<link href="beta/css/pure-ext.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<head>
    <link href="bootstrap/bootplus.css" rel="stylesheet">
    <style>
        /* TOPO MENU */

        .rich-ddmenu-label, .rich-ddmenu-label-disabled {
            padding: 0px !important;
        }

        /* CUSTOMIZE THE CAROUSEL
        -------------------------------------------------- */

        /* Carousel base class */
        .carousel {
            margin-bottom: 0px;
            height: 100%;
        }

        .carousel .container {
            position: relative;
            z-index: 9;
            height: 100%;
        }

        .carousel-control {
            height: 80px;
            margin-top: 0;
            font-size: 36px;
            text-shadow: 0 1px 1px rgba(0, 0, 0, .4);
            background-color: transparent;
            border: 0;
            z-index: 10;
        }

        .carousel .item {
            height: 100%
        }

        .carousel img {
            margin-left: auto;
            margin-right: auto;
        }

        .carousel-caption {
            background-color: transparent;
            position: static;
            max-width: 550px;
            padding: 0 20px;
            margin-top: 200px;
        }

        .carousel-caption h1,
        .carousel-caption .lead {
            margin: 0;
            line-height: 1.25;
            color: #fff;
            text-shadow: 0 1px 1px rgba(0, 0, 0, .4);
        }

        .carousel-caption .btn {
            margin-top: 10px;
        }

    </style>

    <!-- Le javascript -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script src="bootstrap/bootstrap-transition.js"></script>
    <script src="bootstrap/bootstrap-carousel.js"></script>
    <script>
        jQuery(document).ready(function ($) {
            $("#myCarousel").carousel({interval: 10000});
        });
    </script>
</head>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form" style="margin: 0;">

     <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
             <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <script type="text/javascript" language="javascript">
                setDocumentCookie('popupsImportante', 'close',1);
            </script>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central" style="position:relative;">
                            <c:if test="${SuperControle.bannerRetro}">
                                <a href="https://game.pactosolucoes.com.br/core/retrospectiva/2023/myyear.html?chave=${SuperControle.key}&empresa=${SuperControle.empresaLogado.codigo}" class="bannerPadrao" target="_blank" >
                                    <img border="none" class="img-responsive imagemApresentacao"
                                         src="images/RETROSPECTIVA-2023.png"/>
                                </a>
                            </c:if>
                            <c:if test="${not SuperControle.bannerRetro}">
                                <a href="#" class="bannerPadrao">
                                    <img border="none" class="img-responsive imagem-blur-bottom imagemApresentacao"
                                         src="images/ZW2016-dummyImg-06.jpg"/>
                                </a>
                                <div id="myCarousel" class="carousel slide container-conteudo-central">
                                    <div class="carousel-inner">
                                        <c:forEach items="${SuporteControle.banners}" var="ban" varStatus="ind">
                                            <div class="item ${ind.count == 1 ? 'active' : ''}">
                                                <a
                                                        onclick="clickNotificar()"
                                                        <c:if test="${not empty ban.urlLink}">href="${ban.urlLink}"</c:if>
                                                        target="_blank">
                                                    <img border="none" style="max-width:100%"
                                                         src="${ban.urlImagem}">
                                                </a>
                                            </div>
                                        </c:forEach>
                                    </div>

                                    <ol class="carousel-indicators">
                                        <c:forEach items="${SuporteControle.banners}" var="ban"
                                                   varStatus="ind">
                                            <li style="cursor: pointer;" data-target="#myCarousel"
                                                data-slide-to="${ind.count -1}"
                                                class="${ind.count == 1 ? 'active' : ''}"></li>
                                        </c:forEach>
                                    </ol>
                                </div>
                                <script>
                                    validarTamanhoBanner(true);
                                </script>

                                <h:commandLink
                                        id="notificarClickBanner"
                                        style="display: none"
                                        action="#{SuporteControle.notificarClick}">
                                </h:commandLink>
                            </c:if>

                            <h:panelGroup layout="block" styleClass="container-cards" rendered="#{not empty BlogControle.itemsCampanhaBlog}">
                                <h:outputText styleClass="headerBlog" value="Novidades do Blog"/>
                                <a4j:repeat value="#{BlogControle.itemsCampanhaBlog}" var="itemCampanha">

                                    <h:panelGroup layout="block" styleClass="card-blog">
                                        <h:outputLink onclick="window.open('#{itemCampanha.url}','_blank')">
                                            <h:graphicImage value="#{itemCampanha.urlImagem}"/>
                                            <h:panelGroup layout="block" styleClass="conteudoArtigo">
                                                <h:outputText styleClass="titulo-artigo" value="#{itemCampanha.titulo}"/>
                                                <h:outputText styleClass="texto-artigo" value="#{itemCampanha.texto}"/>
                                            </h:panelGroup>
                                        </h:outputLink>
                                    </h:panelGroup>

                                </a4j:repeat>
                            </h:panelGroup>
                            <a4j:commandButton style="display: none;" reRender="panelExpiracaoSenha"
                                               id="btnAtualizaPagina">
                            </a4j:commandButton>
                        </h:panelGroup>

                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-CADASTROS" />
                        </jsp:include>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
         <jsp:include page="include_rodape_flat.jsp" flush="true" />
       </h:panelGroup>

    </h:form>
      <%@include file="/include_load_configs.jsp" %>
    
</f:view>



<%@page contentType="text/html" pageEncoding="UTF-8" %>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@include file="include_imports.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${root}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="${root}/css/ce.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">var contexto = '${root}';</script>
<script type="text/javascript" language="javascript" src="${root}/script/ajuda.js"></script>
<%@ taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>


<%@include file="includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_operacaoColetiva_titulo}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_operacaoColetiva_titulo}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}operacoes-coletivas-_-a-academia-esta-em-recesso-ferias-tem-como-lancar-dias-no-contrato-de-todos-os-alunos-de-uma-unica-vez/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:panelGrid id="panelDados" columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%" id="panelOperacaoColetiva">
                    <h:outputLabel value="Empresa: "/>
                    <h:selectOneMenu value="#{OperacaoColetivaControle.operacaoColetivaVO.empresa.codigo}"
                                     style="min-width: 400px"
                                     id="empresaOperacaoColetiva">
                        <f:selectItems value="#{OperacaoColetivaControle.listaEmpresas}"/>
                        <a4j:support event="onchange"
                                     action="#{OperacaoColetivaControle.montarListasPlanosOperacaoColetiva}"
                                     reRender="panelOperacaoColetiva"/>
                    </h:selectOneMenu>
                    <h:outputLabel value="Operação coletiva:"/>
                    <h:selectOneMenu value="#{OperacaoColetivaControle.operacaoColetivaVO.tipo}"
                                     style="min-width: 400px"
                                     id="tipoOperacaoColetiva">
                        <f:selectItems value="#{OperacaoColetivaControle.listaSelectItemOperacoesColetivas}"/>
                        <a4j:support event="onchange"
                                     reRender="panelOperacaoColetiva"/>
                    </h:selectOneMenu>
                    <h:outputLabel value="Contratos do plano: " rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo  != 4 }"/>
                    <h:selectOneMenu value="#{OperacaoColetivaControle.operacaoColetivaVO.planoVO.codigo}" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo  != 4 }"
                                     style="min-width: 400px"
                                     id="plaOperacaoColetiva">
                        <f:selectItems value="#{OperacaoColetivaControle.listaSelectItemPlanosOperacaoColetiva}"/>
                    </h:selectOneMenu>
                    <h:outputLabel value="Idade Mínima: "
                                   rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 4
                                   and OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 5}"/>
                    <h:inputText rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 4
                    and OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 5}"
                                 id="idadeMinima"
                                 onkeypress="return Tecla(event);"
                                 size="5"
                                 maxlength="5"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{OperacaoColetivaControle.operacaoColetivaVO.idadeMinima}" />

                    <h:outputLabel value="Idade Máxima: "
                                   rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 4
                                   and OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 5}"/>
                    <h:inputText rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 4
                    and OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 5}"
                                 id="idadeMaxima"
                                 onkeypress="return Tecla(event);"
                                 size="5"
                                 maxlength="5"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{OperacaoColetivaControle.operacaoColetivaVO.idadeMaxima}" />

                    <h:outputLabel value="Mensagem na catraca:" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo == 5}"/>
                    <h:inputText size="50"
                                     style="min-width: 400px"
                                     id="inputMensagemCatraca"
                                     maxlength="100"
                                     value="#{OperacaoColetivaControle.operacaoColetivaVO.mensagemCatraca}"
                                 rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo == 5}"

                    />
                    <h:outputLabel value="Observação:" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 5}"/>
                    <h:inputText size="50"
                                 style="min-width: 400px"
                                 id="inputObservacaoOperacao"
                                 rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 5}"
                                 value="#{OperacaoColetivaControle.operacaoColetivaVO.observacao}"/>
                    <h:outputLabel value="Inicio Recesso: " rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 5}"/>
                    <h:outputLabel value="Inicio: " rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo == 5}"/>
                    <h:panelGroup>
                    <rich:calendar id="dataInicioOperacao"
                                   value="#{OperacaoColetivaControle.operacaoColetivaVO.dataInicio}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   onchanged="validar_Data(this.id);atualizarMsgOperacaoRetroativa();return validar_Data(this.id);"
                                   oninputchange="validar_Data(this.id);atualizarMsgOperacaoRetroativa();return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                        <a4j:jsFunction name="atualizarMsgOperacaoRetroativa" action="#{OperacaoColetivaControle.operacaoColetivaVO.validarDataRetroativa}" reRender="panelDados"/>
                    </h:panelGroup>
                    <h:outputLabel styleClass="mensagemDetalhada" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.operacaoRetroativa}" value="Operação Retroativa: "/>
                    <h:outputLabel styleClass="mensagemDetalhada" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.operacaoRetroativa}" value="Contratos que foram renovados e suas renovações já tiveram alguma operação de contrato, serão ignorados no processamento dessa operação"/>
                    <h:outputLabel value="Fim Recesso: " rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo != 5}"/>
                    <h:outputLabel value="Fim: " rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo == 5}"/>
                    <rich:calendar id="dataFinalOperacao"
                                   value="#{OperacaoColetivaControle.operacaoColetivaVO.dataFinal}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>

                    <h:outputLabel value="Modalidade:" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo  == 4 }"/>

                    <h:selectOneMenu value="#{OperacaoColetivaControle.operacaoColetivaVO.modalidadeVO.codigo}" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo  == 4 }"
                                     style="min-width: 400px"
                                     id="modalidadeOperacaoColetiva">
                        <f:selectItems value="#{OperacaoColetivaControle.listaSelectItemModalidadesOperacaoColetiva}"/>
                        <a4j:support event="onchange" action="#{OperacaoColetivaControle.montarListasTurmasOperacaoColetiva}"
                                     reRender="panelOperacaoColetiva"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Turma:" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo  == 4 }"/>
                    <h:selectOneMenu value="#{OperacaoColetivaControle.operacaoColetivaVO.turmaVO.codigo}" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo  == 4 }"
                        style="min-width: 400px" id="turmaOperacaoColetiva">
                        <f:selectItems value="#{OperacaoColetivaControle.listaSelectItemTurmasOperacaoColetiva}"/>
                    </h:selectOneMenu>
                    <h:outputLabel for="checkboxBloquearAcessoAniversario" value="Bloquear no aniversário:" rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo == 5}"/>
                    <h:selectBooleanCheckbox id="checkboxBloquearAcessoAniversario"
                                             value="#{OperacaoColetivaControle.operacaoColetivaVO.bloquearAcessoAniversario}"
                                             rendered="#{OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo == 5}"/>
                    <c:if test="${OperacaoColetivaControle.operacaoColetivaVO.tipo.codigo == 4 && OperacaoColetivaControle.configuracaoSistema.sesc}">
                        <h:outputLabel value="Cancelar parcelas em aberto" />
                        <h:selectBooleanCheckbox id="checkboxCancelarParcelasEmAbertoAlunosDesmarcados"
                                                 value="#{OperacaoColetivaControle.operacaoColetivaVO.cancelarParcelasEmAbertoAlunosDesmarcados}"/>
                    </c:if>
                </h:panelGrid>

                <rich:spacer height="10"/>

                <h:outputLabel style="color: red;"
                               id="pgResultadoReprocessoOperacao"
                               rendered="#{!OperacaoColetivaControle.manutencaoAjusteGeralTO.sucesso}"
                               value="#{OperacaoColetivaControle.manutencaoAjusteGeralTO.msgResultado}"/>

                <h:outputLabel style="color: green;"
                               id="pgResultadoReprocessoOperacao2"
                               rendered="#{OperacaoColetivaControle.manutencaoAjusteGeralTO.sucesso}"
                               value="#{OperacaoColetivaControle.manutencaoAjusteGeralTO.msgResultado}"/>

            </h:panelGrid>


            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <f:verbatim>
                            <h:outputText value=" "/>
                        </f:verbatim>
                    </h:panelGrid>
                    <h:commandButton id="icAmbienteSuc" rendered="#{OperacaoColetivaControle.sucesso}"
                                     image="./imagens/sucesso.png"/>
                    <h:commandButton id="icAmbienteFal" rendered="#{OperacaoColetivaControle.erro}"
                                     image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgAmbiente" styleClass="mensagem" value="#{OperacaoColetivaControle.mensagem}"/>
                        <h:outputText id="msgAmbienteDet" styleClass="mensagemDetalhada"
                                      value="#{OperacaoColetivaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>

                        <a4j:commandButton id="salvar" value="#{msg_bt.btn_gravar}"
                                           alt="#{msg.msg_gravar_dados}" accesskey="2"
                                           oncomplete="#{OperacaoColetivaControle.mensagemNotificar}"
                                           action="#{OperacaoColetivaControle.gravarOperacaoColetiva}"
                                           styleClass="botoes nvoBt"
                                           reRender="infoOperacoesColetivas, panelOperacaoColetiva"/>

                        <f:verbatim>
                            <h:outputText value="    "/>
                        </f:verbatim>

                        <a4j:commandLink action="#{OperacaoColetivaControle.realizarConsultaLogObjetoGeral}"
                                         reRender="form"
                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                         style="display: inline-block;padding-bottom: 10px;">
                            <i class="fa-icon-list"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>

            <rich:spacer height="10"/>

            <h:panelGrid id="infoOperacoesColetivas" styleClass="textsmall" width="100%" columns="1">
                <rich:dataTable id="tblOperacoesColetivas" var="operacao" width="100%" rows="10" rowKeyVar="status"
                                value="#{OperacaoColetivaControle.listaOperacoesColetivas}"
                                rendered="#{not empty OperacaoColetivaControle.listaOperacoesColetivas}">
                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column sortBy="#{operacao.empresa.nome}">
                        <f:facet name="header">
                            <h:outputText value="Empresa"/>
                        </f:facet>
                        <h:outputText value="#{operacao.empresa.nome}"/>
                    </rich:column>

                    <rich:column sortBy="#{operacao.tipo.descricao}">
                        <f:facet name="header">
                            <h:outputText value="Operação Coletiva"/>
                        </f:facet>
                        <h:outputText value="#{operacao.tipo.descricao}"/>
                    </rich:column>

                    <rich:column sortBy="#{operacao.planoVO.descricao}">
                        <f:facet name="header">
                            <h:outputText value="Plano"/>
                        </f:facet>
                        <h:outputText rendered="#{operacao.planoVO.codigo > 0}"
                                      value="#{operacao.planoVO.descricao}"/>
                        <h:outputText rendered="#{operacao.planoVO.codigo == 0}" value="Todos"/>
                    </rich:column>
                    <rich:column sortBy="#{operacao.idadeMinima}">
                        <f:facet name="header">
                            <h:outputText value="Faixa Etaria"/>
                        </f:facet>
                        <h:outputText
                                      value="#{operacao.descricaoFaixaEtaria}"/>
                    </rich:column>
                    <rich:column sortBy="#{operacao.dataCadastro}">
                        <f:facet name="header">
                            <h:outputText value="Cadastro em"/>
                        </f:facet>
                        <h:outputText value="#{operacao.dataCadastro_Apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{operacao.usuario}">
                        <f:facet name="header">
                            <h:outputText value="Responsável"/>
                        </f:facet>
                        <h:outputText value="#{operacao.usuario}"/>
                    </rich:column>

                    <rich:column sortBy="#{operacao.dataInicio}">
                        <f:facet name="header">
                            <h:outputText value="Dt. Início"/>
                        </f:facet>
                        <h:outputText value="#{operacao.dataInicio_Apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{operacao.dataFinal}">
                        <f:facet name="header">
                            <h:outputText value="Dt. Final"/>
                        </f:facet>
                        <h:outputText value="#{operacao.dataFinal_Apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{operacao.modalidadeVO}">
                        <f:facet name="header">
                            <h:outputText value="Modalidade"/>
                        </f:facet>
                        <h:outputText value="#{operacao.modalidadeVO.nome}"/>
                    </rich:column>
                    <rich:column sortBy="#{operacao.turmaVO}">
                        <f:facet name="header">
                            <h:outputText value="Turma"/>
                        </f:facet>
                        <h:outputText value="#{operacao.turmaVO.descricao}"/>
                    </rich:column>
                    <rich:column sortBy="#{operacao.status.descricao}">
                        <f:facet name="header">
                            <h:outputText value="Status"/>
                        </f:facet>
                        <h:outputText value="#{operacao.status.descricao}"/>
                    </rich:column>
                    <rich:column sortBy="#{operacao.dataprocessamento}">
                        <f:facet name="header">
                            <h:outputText value="Dt. processamento"/>
                        </f:facet>
                        <h:outputText value="#{operacao.dataprocessamento_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Resultado"/>
                        </f:facet>
                        <h:outputText value="#{operacao.resultado}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Opções"/>
                        </f:facet>
                        <a4j:commandButton value="Estornar"
                                           oncomplete="#{OperacaoColetivaControle.mensagemNotificar}"
                                           onclick="if(!confirm('Confirma exclusão da Operação Coletiva?')){return false;}"
                                           action="#{OperacaoColetivaControle.excluirOperacaoColetiva}"
                                           rendered="#{operacao.status.codigo != 3 && operacao.status.codigo != 5}"
                                           reRender="infoOperacoesColetivas, panelOperacaoColetiva"/>
                    </rich:column>
                </rich:dataTable>

                <rich:datascroller align="center" for="form:tblOperacoesColetivas" maxPages="10"
                                   id="sctblOperacoesColetivasa"/>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
</f:view>

<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_LocalImpressao_titulo}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_LocalImpressao_titulo}"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="Código" />

                    <h:panelGroup>
                        <f:verbatim>
                            <h:outputText value="                      "/>
                        </f:verbatim>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{LocalImpressaoControle.localImpressaoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="Nome" />
                    <h:panelGroup>
                        <h:inputText  id="nome" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{LocalImpressaoControle.localImpressaoVO.nome}" />
                        <h:message for="nome" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="Nome Computador" />
                    <h:panelGroup>
                        <h:inputText  id="nomecomputador" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{LocalImpressaoControle.localImpressaoVO.nomeComputador}" />
                        <h:message for="nomecomputador" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText rendered="#{LocalImpressaoControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="Empresa" />
                    <h:panelGroup rendered="#{LocalImpressaoControle.usuarioLogado.administrador}">
                        <h:selectOneMenu  id="LocalImp_empresa" onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          styleClass="form"
                                          value="#{LocalImpressaoControle.localImpressaoVO.empresaVO.codigo}" >
                            <f:selectItems  value="#{LocalImpressaoControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_Compra_empresa" action="#{LocalImpressaoControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:LocalImp_empresa"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{LocalImpressaoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{LocalImpressaoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgLocalImp" styleClass="mensagem"  value="#{LocalImpressaoControle.mensagem}"/>
                            <h:outputText id="msgLocalImpDet" styleClass="mensagemDetalhada" value="#{LocalImpressaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{LocalImpressaoControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                            <h:commandButton id="salvar" action="#{LocalImpressaoControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                   oncomplete="#{LocalImpressaoControle.msgAlert}" action="#{LocalImpressaoControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                                   alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec" style="border: 0px" />
                            </h:panelGroup>

                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                            <h:commandButton id="consultar" immediate="true" action="#{LocalImpressaoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4"    styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="15px"/>
                            <a4j:commandLink  action="#{LocalImpressaoControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                              oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                              style="display: inline-block; padding: 8px 15px;"
                                              styleClass="botoes nvoBt btSec"  title="Visualizar Log">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>

                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>

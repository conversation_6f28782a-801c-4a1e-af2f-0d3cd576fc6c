<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>   
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title>
        <h:outputText value="Cardex"/>
    </title>
    <c:set var="titulo" scope="session" value="Cardex - Movimentações do Produto"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-acompanhar-as-movimentacoes-do-meu-estoque/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">


        <h:form id="form">
            <h:commandLink action="#{CardexControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1"  width="100%" >
                <h:panelGrid columns="1" width="100%" bgcolor="#EEEEEE" columnClasses="centralizado">
                    <h:panelGroup layout="block" id="panel1">
                        <h:outputText styleClass="tituloCampos" style="padding-right:5px;padding-left:5px;vertical-align: baseline;" value="Período movimentações:"/>

                        <h:panelGroup>
                            <rich:calendar id="dataInicial"
                                           value="#{CardexControle.periodoDe}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <h:message for="dataInicial" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" value=" à " style="    vertical-align: baseline;"/>

                        <h:panelGroup>
                            <rich:calendar id="dataFinal"
                                           value="#{CardexControle.periodoAte}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <h:message for="dataFinal" styleClass="mensagemDetalhada"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        </h:panelGroup>


                        <!-- BOTAO CONSULTA -->
                        <h:panelGroup>
                            <a4j:commandLink reRender="tableDados"  id="consultar" type="submit" style="vertical-align: baseline" styleClass="botoes nvoBt btSec" value="#{msg_bt.btn_consultar}" action="#{CardexControle.irPaginaInicial}" title="#{msg.msg_consultar_dados}" accesskey="2"/>
                            <a4j:commandButton id="exportarExcel"
                                               image="imagens/btn_excel.png"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty CardexControle.listaConsulta}"
                                               value="Excel"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{CardexControle.listaConsulta}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="data=Data,operacao_Apresentar=Operação,totalEntrada=Entrada,totalSaida=Saída,saldoAnterior=Saldo Anterior,saldoAtual=Saldo Atual"/>
                                <f:attribute name="prefixo" value="Cardex"/>
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGroup>



                    <h:panelGroup layout="block" style="padding: 5 5 5 5;">


                        <h:outputText style="padding-right:5px; padding-left:5px; vertical-align: middle; " rendered="#{CardexControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="Empresa:" />
                        <h:selectOneMenu  id="Cardex_empresa" onblur="blurinput(this);"
                                          rendered="#{CardexControle.usuarioLogado.administrador}"
                                          onfocus="focusinput(this);"
                                          style = "vertical-align: middle; width: 150px;"
                                          styleClass="form"
                                          value="#{CardexControle.codigoEmpresa}" >
                            <f:selectItems  value="#{CardexControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                        <a4j:commandButton rendered="#{CardexControle.usuarioLogado.administrador}" id="atualizar_Cardex_empresa" style="vertical-align: middle; " action="#{CardexControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:Cardex_empresa"/>

                        <h:outputText style="padding-right:5px; padding-left:5px; vertical-align: middle; " styleClass="tituloCampos" value="Produto:" />
                        <h:inputText  id="nomeProdutoSelecionado"
                                      size="50"
                                      maxlength="50"
                                      onblur="blurinput(this);"
                                      onfocus="focusinput(this);"
                                      styleClass="form"
                                      value="#{CardexControle.produtoSelecionado}" />

                        <rich:suggestionbox   height="200" width="400"
                                              for="nomeProdutoSelecionado"
                                              status="statusInComponent"
                                              suggestionAction="#{CardexControle.executarAutocompletePesqProduto}"
                                              minChars="1"
                                              reRender="panelMensagem"
                                              rowClasses="linhaImpar, linhaPar"
                                              ajaxSingle="false"
                                              var="result"
                                              id="suggestionProduto">
                            <a4j:support event="onselect"
                                         reRender="form, panelMensagem, ProdEst_empresa"
                                         action="#{CardexControle.selecionarProduto}">
                            </a4j:support>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Nome"  styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText styleClass="textverysmall" value="#{result.descricao}" />
                            </h:column>
                            <h:column >
                                <f:facet name="header">
                                    <h:outputText value="Categoria" styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText  styleClass="textverysmall" value="#{result.categoriaProduto.descricao}" />
                            </h:column>
                        </rich:suggestionbox>



                    </h:panelGroup>

                </h:panelGrid>
               </h:panelGrid>

        <h:panelGrid id="tableDados" columns="1"  width="100%" >
                <rich:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita,  colunaDireita, colunaDireita, colunaDireita "
                                value="#{CardexControle.listaConsulta}" rendered="#{CardexControle.apresentarResultadoConsulta}" rows="10" var="cardex">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Data"/>
                        </f:facet>
                        <h:outputText id = "dataCad"  value="#{cardex.data}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss" />
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Operação"/>
                        </f:facet>
                        <a4j:commandLink   reRender="form, panelDetalhes" action="#{CardexControle.visualizarDetalhes}" id="idlinkOperacao" value="">
                            <h:outputText  value="#{cardex.operacao_Apresentar}"/>
                        </a4j:commandLink>

                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Entrada(+)"/>
                        </f:facet>
                        <h:outputText  value="#{cardex.totalEntrada}">
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Saída(-)"/>
                        </f:facet>
                        <h:outputText  value="#{cardex.totalSaida}">
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Saldo Anterior"/>
                        </f:facet>
                        <h:outputText  value="#{cardex.saldoAnterior}">
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Saldo Atual"/>
                        </f:facet>
                        <h:outputText  value="#{cardex.saldoAtual}">
                        </h:outputText>
                    </rich:column>



                </rich:dataTable>
                <h:panelGrid width="100%" footerClass="colunaCentralizada">
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{CardexControle.apresentarResultadoConsulta}" binding="#{CardexControle.apresentarLinha}">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false" binding="#{CardexControle.apresentarPrimeiro}" action="#{CardexControle.irPaginaInicial}"/>
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false" binding="#{CardexControle.apresentarAnterior}" action="#{CardexControle.irPaginaAnterior}"/>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{CardexControle.paginaAtualDeTodas}" rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false" binding="#{CardexControle.apresentarPosterior}" action="#{CardexControle.irPaginaPosterior}"/>
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false" binding="#{CardexControle.apresentarUltimo}" action="#{CardexControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>
                <h:panelGrid id ="panelMensagem"  columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{CardexControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{CardexControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{CardexControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{CardexControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                <a4j:commandLink id="imprimirRelatorio"
                                   title="Imprimir Relatório"
                                   reRender="panelMensagem"
                                   styleClass="botaoPrimario texto-size-16 texto-cor-azul"
                                   style="display: inline-block"
                                   action="#{RelatorioCardexControle.imprimirRelatorioPDF}"
                                   oncomplete="#{!CardexControle.erro} ? abrirPopupPDFImpressao('relatorio/#{RelatorioCardexControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595) : '' ;">
                <i class="fa-icon-print" ></i>
                </a4j:commandLink>

            </h:panelGrid>

        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="panelDetalhes" autosized="true"
                     showWhenRendered="#{CardexControle.mostrarDetalhesOperacao}"
                     width="450" height="200">
        <f:facet name="header" >
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Detalhes da Movimentação"/>
            </h:panelGroup>
        </f:facet>



        <a4j:form id="formDetalhes" ajaxSubmit="true" prependId="false">
            <h:panelGrid columns="1"   width="100%" bgcolor="#EEEEEE" >
                <h:panelGroup  >
                    <h:outputText style="padding-right:5px; vertical-align:middle; width:10px "  styleClass="tituloCampos" value="Produto:" />
                    <h:outputText style="padding-right:5px; vertical-align:middle; width:10px "  styleClass="tituloCampos" value="#{CardexControle.produtoVO.descricao}" />
                </h:panelGroup>
                <h:panelGroup  >
                    <h:outputText style="padding-right:5px; vertical-align:middle; width:10px "  styleClass="tituloCampos" value="#{CardexControle.descricaoDetalhes}" />
                </h:panelGroup>

            </h:panelGrid>


            <rich:dataTable id="tableOperacoes" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                            columnClasses="colunaEsquerda, colunaEsquerda, colunaDireita,  colunaDireita "
                            value="#{CardexControle.listaDetalhesOperacao}" rows="10" var="cardex2">

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Data"/>
                    </f:facet>
                    <a4j:commandLink action="#{CardexControle.abrirTelaDetalhes}"  oncomplete="#{CardexControle.telaVisualizarDetalhes}">
                        <h:outputText id = "dataCad2"  value="#{cardex2.data}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss" />
                        </h:outputText>
                    </a4j:commandLink>

                </rich:column>
                <c:if test="${CardexControle.mostrarColunaDataCancelVenda}" >
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Data Venda"/>
                        </f:facet>

                        <h:outputText id = "dataVenda"  value="#{cardex2.dataAux}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss" />
                        </h:outputText>
                    </rich:column>

                </c:if>
                <c:if test="${CardexControle.mostrarColunaDataEstorno}" >
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Data Estorno"/>
                        </f:facet>

                        <h:outputText id = "dataEstorno"  value="#{cardex2.dataAux}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss" />
                        </h:outputText>
                    </rich:column>

                </c:if>
                <c:if test="${CardexControle.mostrarColunaNome}" >
                    <rich:column >
                        <f:facet name="header">
                            <h:outputText value="#{CardexControle.descricaoColuna}"/>
                        </f:facet>

                        <h:outputText id = "txtNome"  value="#{cardex2.nomeAux}">
                        </h:outputText>
                    </rich:column>
                </c:if>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Entrada(+)"/>
                    </f:facet>
                    <h:outputText  value="#{cardex2.totalEntrada}">
                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Saída(-)"/>
                    </f:facet>
                    <h:outputText  value="#{cardex2.totalSaida}">
                    </h:outputText>
                </rich:column>
            </rich:dataTable>

            <h:panelGrid columns="1" columnClasses="rich-table-cell colunaDireita" width="100%" >
                <h:panelGroup>
                    <h:outputText value="Total Entrada(+):"/>
                    <h:outputText  value="#{CardexControle.totalEntrada}">
                    </h:outputText>
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText value="Total Saida(-):"/>
                    <h:outputText  value="#{CardexControle.totalSaida}">
                    </h:outputText>
                </h:panelGroup>

            </h:panelGrid>

            <rich:datascroller align="center" for="formDetalhes:tableOperacoes" maxPages="5" id="scdetalhe"/>
            <h:panelGrid id="panelfechar" columns="1" width="100%" bgcolor="#EEEEEE" columnClasses="colunaCentralizada">
                <a4j:commandButton reRender="panelDetalhes"  style = "vertical-align: middle; padding-left:5px" id="idfechar" type="submit" styleClass="botoes"  action="#{CardexControle.fecharModalDetalhe}" image="./imagens/botaoFechar.png"  accesskey="2"/>
            </h:panelGrid>


        </a4j:form>
    </rich:modalPanel>
</f:view>
<script>
    document.getElementById("form:nomeProdutoSelecionado").focus();
</script>

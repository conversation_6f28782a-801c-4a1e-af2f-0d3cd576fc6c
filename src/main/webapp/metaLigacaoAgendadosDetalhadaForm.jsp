<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
</style>
<script type="text/javascript">
    function trocarTotal(show) {
        var totalHoje = document.getElementById('totalHoje');
        var totalPesquisa = document.getElementById('totalPesquisa');

        totalHoje.style.display = 'none';
        totalPesquisa.style.display = 'none';

        var filtros = document.getElementById('filtros');
        var mostrar = document.getElementById(show);
        mostrar.style.display = 'block';

        if (show == 'totalPesquisa') {
            filtros.style.display = 'block';
        } else {
            filtros.style.display = 'none';
        }
    }

    function trocarAba(valor) {
        var abaSelecionada = document.getElementById('form:abaSelecionada');
        abaSelecionada.value = valor;
    }

</script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_Ligacao_Agendados_tituloForm}" /></title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp" />
        </f:facet>

    </h:panelGrid>
    <h:form id="form">
        <h:panelGrid width="2000px" columns="1">
            <h:commandLink action="#{AberturaMetaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Ligacao_Agendados_tituloForm}" />
                </h:panelGrid>
            </h:panelGrid>


            <!-- ---------------------------- TOTALIZADORES --------------------------------- -->


            <h:panelGrid columns="2" columnClasses="colunaEsquerda" styleClass="tabForm" width="100%">
                <h:panelGroup id="totalizadores" >


                    <div id="totalHoje" style="display: block;">
                        <h:panelGrid columns="1" id="totalHojeNrs">
                            <h:panelGroup>


                                <h:outputText id="ttHojeLista" styleClass="tituloCamposDestaqueNegrito" title="Total de Renovações na lista" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencial}" />
                                <rich:spacer width="12px" />
                                <h:outputText id="ttHoje" styleClass="tituloCamposDestaqueNegrito" title="Total de Renovações"  value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.totalizadorHoje}" />
                                <rich:spacer width="30px" />
                                <h:outputText id="ttHojeSelecionadoEmail" styleClass="tituloCamposDestaqueNegrito" title="Total de Renovações selecionados" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencialEmail}" />
                                <rich:spacer width="12px" />
                                <h:outputText id="ttHojeSelecinado" styleClass="tituloCamposDestaqueNegrito" title="Total de Renovações selecionados" value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.totalizadorSelecionadoHoje}" />
                            </h:panelGroup>
                        </h:panelGrid>
                    </div>

                    <div id="totalPesquisa" style="display: none;">
                        <h:panelGrid columns="1" id="totalPesquisaNrs">
                            <h:panelGroup>

                                <h:outputText id="ttPesquisaLista" styleClass="tituloCamposDestaqueNegrito" title="Total de Agendados" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencial}" />
                                <rich:spacer width="12px" />
                                <h:outputText id="ttPesquisa" styleClass="tituloCamposDestaqueNegrito" title="Total de Agendados" value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.totalizadorHistorico}" />
                                <rich:spacer width="30px" />
                                <h:outputText id="ttPesquisaSelecionadoEmail" styleClass="tituloCamposDestaqueNegrito" title="Total de Agendados selecionados" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencialEmail}" />
                                <rich:spacer width="12px" />
                                <h:outputText id="ttPesquisaSelecionado" styleClass="tituloCamposDestaqueNegrito" title="Total de Agendados selecionados" value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.totalizadorSelecionadoHistorico}" />
                            </h:panelGroup>
                        </h:panelGrid>
                    </div>
                </h:panelGroup>
                <h:panelGroup>
                    <rich:spacer width="30px" />

                    <a4j:commandButton id="btnAtualizar" value="Atualizar" reRender="totalHojeNrs, itemsClientePotencial"
                                       actionListener="#{AberturaMetaControle.atualizarFecharMetaDetalhada}" styleClass="botaoPesquisar" image="./imagensCRM/atualizarCRM.png">
                        <f:attribute name="identificador" value="LA"/>
                    </a4j:commandButton>&nbsp;

                    <a4j:commandButton  id="btnEnviarEmail"
                                        styleClass="botaoPesquisar"
                                        reRender="form:panelGridMensagens, mensagemNotificacaoColetivoForm, totalLista"
                                        image="./imagensCRM/botaoEnviarEmail.png" title="Enviar E-mail"
                                        oncomplete="#{AberturaMetaControle.executarAberturaPopupMalaDireta}"
                                        action="#{AberturaMetaControle.executarAberturaPopupEmailColetivoLigacaoAgendados}" />
                    <a4j:commandButton id="btnEnviarSMS" styleClass="botaoPesquisar" reRender="form:panelGridMensagens, mensagemNotificacaoColetivoForm, totalLista"
                                       title="Enviar SMS" value="Enviar SMS"  image="./imagensCRM/enviar-sms.png"
                                       oncomplete="#{AberturaMetaControle.executarAberturaPopupMalaDireta}"
                                       action="#{AberturaMetaControle.executarAberturaPopupSMSColetivoLigacaoAgendados}" />
                    &nbsp;
                    <a4j:commandLink id="btnImprimirLista"
                                     action="#{AberturaMetaControle.executarImpressaoLigacaoAgendados}"
                                     styleClass="botaoPesquisar" oncomplete="abrirPopupPDFImpressao('relatorio/#{AberturaMetaControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                        <h:graphicImage id="btnImgImprimirLista" style="border:none;" url="./imagensCRM/botaoImprimirLista.png" title="Imprimir Lista(PDF)" />
                    </a4j:commandLink>

                    <a4j:commandLink id="btnExportarExcel" action="#{AberturaMetaControle.executarImpressaoExcelLigacaoAgendados}" oncomplete="location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=MetaDetalhadoExcelClienteRel.xlsx'" styleClass="botaoPesquisar">
                        <h:graphicImage id="btnImgExportarExcel" url="./imagensCRM/botaoGerarExcel.png" style="border:none;" title="Imprimir Lista(Excel)" />
                    </a4j:commandLink>

                    <rich:spacer width="50px" />
                    <h:outputText value="#{msg_aplic.prt_HistoricoIndicado_consProf}" styleClass="tituloCampos" />
                    <rich:spacer width="10px" />
                    <h:outputText id="nomeResponsavel" value="#{AberturaMetaControle.nomeParticipanteSelecionadoVenda}" styleClass="tituloCampos" />
                </h:panelGroup>

            </h:panelGrid>

            <!-- ---------------------------- FILTROS --------------------------------- -->

            <table id="filtros" style="display: none" class="tabForm" width="100%">
                <tr><td width="100%">


                        <h:panelGroup style="width: 100%">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agendados_nome}" />
                            <rich:spacer width="5px" />
                            <h:inputText id="consultaNome" value="#{AberturaMetaControle.valorConsultaNomeLigacaoAgendados}" size="50" styleClass="camposAgendado" />

                            <rich:spacer width="10px" />

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agendados_periodo}" />
                            <rich:spacer width="12px" />
                            <rich:calendar id="dataInicio" value="#{AberturaMetaControle.dataInicio}" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                            <h:message for="dataInicio" styleClass="mensagemDetalhada" />
                            <rich:spacer width="12px" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_ate}" />

                            <rich:spacer width="12px" />
                            <rich:calendar id="dataTermino" value="#{AberturaMetaControle.dataTermino}" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="true" />
                            <h:message for="dataTermino" styleClass="mensagemDetalhada" />
                            <rich:spacer width="12px" />
                            <a4j:commandButton id="btnPesquisar" oncomplete="trocarTotal('totalPesquisa');"
                                               styleClass="botaoPesquisar"
                                               action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVendaLigacaoAgendadosPorHistorico}"
                                               image="./imagensCRM/botaoPesquisar.png"
                                               reRender="totalPesquisaNrs, itemsClientePotencialHistorico" />
                        </h:panelGroup>


                    </td></tr>
            </table>

            <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                <h:outputText id="msgAgendamentos" styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
                <h:outputText id="msgAgendamentosDet" styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
            </h:panelGrid>

            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <rich:tabPanel id="panelAbas" width="100%" activeTabClass="true" switchType="client">
                    <rich:tab onlabelclick="trocarTotal('totalHoje');trocarAba('HJ');"  onclick="trocarTotal('totalHoje');trocarAba('HJ');"  id="hojeClientePotencial" label="Hoje -  #{AberturaMetaControle.aberturaMetaVO.dia_Apresentar}" action="#{AberturaMetaControle.inicializarDadosAbaHojeAgendados}">
                        <h:panelGrid columns="1" styleClass="tabForm" width="100%">

                            <rich:dataTable id="itemsClientePotencial" width="100%"
                                            headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno"
                                            columnClasses="colunaEsquerda"
                                            value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.fecharMetaDetalhadoVOs}" var="fecharMetaDetalhado">

                                <rich:column>
                                    <f:facet name="header">
                                        <h:panelGroup>
                                            <h:outputText  value="#{msg_aplic.prt_Agendados_opcoes}" />
                                            <h:selectBooleanCheckbox id="selectTodos" title="Marca todos os Agendados" value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.marcaTodosHoje}">
                                                <a4j:support event="onclick" action="#{AberturaMetaControle.executarSelecaoTodosFecharMetaDetalhadosLigacaoAgendados}" reRender="panelAbas, totalHojeNrs" />
                                            </h:selectBooleanCheckbox>
                                        </h:panelGroup>
                                    </f:facet>
                                    <h:panelGroup>
                                        <a4j:commandButton id="btnRealizarContato" image="./imagensCRM/icontelefonista.png"
                                                           title="Realizar novo Contato"
                                                           actionListener="#{HistoricoContatoControle.selecionarAgendadosRealizacaoContato}"
                                                           ajaxSingle="true" oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}" >
                                            <f:attribute value="LA" name="faseCRM" />
                                        </a4j:commandButton>
                                        <rich:spacer width="5px" />
                                        <a4j:commandButton id="consultaHistorico" image="./imagensCRM/icon_Lupa.png" title="Consultar Histórico Contato" action="#{HistoricoContatoControle.consultarHistoricoAgendadosVindoTelaMetaAgendadosDetalhada}" ajaxSingle="true" oncomplete="#{HistoricoContatoControle.validarQualPopUpAbrirHistoricoContato}" />
                                        <rich:spacer width="5px" />
                                        <a4j:commandButton id="btnReagendarCom" image="./imagensCRM/reagendamento.png"
                                                           actionListener="#{HistoricoContatoControle.executarReagendamento}"
                                                           rendered="#{fecharMetaDetalhado.agenda.dataComparecimento == null }"
                                                           title="Reagendar Pessoa"
                                                           reRender="panelAgenda"
                                                           ajaxSingle="true"
                                                           oncomplete="#{HistoricoContatoControle.msgAlert}" >
                                            <f:attribute value="LA" name="faseCRM" />
                                        </a4j:commandButton>
                                        <rich:spacer width="5px" />
                                        <a4j:commandButton id="visualizarAluno" image="./imagensCRM/iconeCliente_menor.png" action="#{AberturaMetaControle.editarPessoaListaFecharMetaDetalhado}" oncomplete="#{AberturaMetaControle.validarQualPopUpAbrirMetaAgendado}" title="Visualizar Cliente" ajaxSingle="true" />
                                        <rich:spacer width="5px" />
                                        <h:selectBooleanCheckbox id="selectAluno" title="Enviar E-mail" value="#{fecharMetaDetalhado.enviarEmailSMS}">
                                            <a4j:support event="onclick" action="#{AberturaMetaControle.executarTotalizadorSelecionadoAgendadosHoje}" reRender="totalHojeNrs" />
                                        </h:selectBooleanCheckbox>
                                    </h:panelGroup>
                                </rich:column>
                                <rich:column id="filtroMatricula" sortBy="#{fecharMetaDetalhado.cliente.matricula}" filterBy="#{fecharMetaDetalhado.cliente.matricula}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_matricula}" />
                                    </f:facet>
                                    <h:outputText id="matricula" value="#{fecharMetaDetalhado.cliente.matricula}" rendered="#{fecharMetaDetalhado.validarAgendadosClienteApresentar}" />
                                </rich:column>
                                <rich:column id="filtroNome" filterBy="#{fecharMetaDetalhado.nomePessoaRel}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_nomePessoa}" />
                                    </f:facet>
                                    <h:outputText id="nomePessoaHoje" value="#{fecharMetaDetalhado.nomePessoaRel}" />
                                </rich:column>
                                <rich:column id="filtroSituacao" filterBy="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar}" filterEvent="onkeyup" >
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Cliente_situacao}" />
                                    </f:facet>
                                    <h:graphicImage id="sitAtivo" value="./images/botaoAtivo.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativo}" />
                                    <h:graphicImage id="sitInativo" value="./images/botaoInativo.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.inativo}" />
                                    <h:graphicImage id="sitVisitante" value="./images/botaoVisitante.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.visitante}" />
                                    <h:graphicImage id="sitTrancado" value="./images/botaoTrancamento.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.trancado}" />
                                    <h:graphicImage id="sitNormal" value="./images/botaoNormal.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativoNormal}" />
                                    <h:graphicImage id="sitTrancVencido" value="./images/botaoTrancadoVencido.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.trancadoVencido}" />
                                    <h:graphicImage id="sitFreePass" value="./images/botaoFreePass.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.visitanteFreePass}" />
                                    <h:graphicImage id="sitAulaAvulsa" value="./images/botaoAulaAvulsa.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.visitanteAulaAvulsa}" />
                                    <h:graphicImage id="sitDiaria" value="./images/botaoDiaria.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.visitanteDiaria}" />
                                    <h:graphicImage id="sitCancelamento" value="./images/botaoCancelamento.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.inativoCancelamento}" />
                                    <h:graphicImage id="sitDesistente" value="./images/botaoDesistente.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.inativoDesistente}" />
                                    <h:graphicImage id="sitAVencer" value="./images/botaoAvencer.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativoAvencer}" />
                                    <h:graphicImage id="sitVencido" value="./images/botaoVencido.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.inativoVencido}" />
                                    <h:graphicImage id="sitCarencia" value="./images/botaoCarencia.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativoCarencia}" />
                                    <h:graphicImage id="sitAtestado" value="./images/botaoAtestado.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativoAtestado}" />
                                    <h:outputText value="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar}" style="display:none;"/>
                                </rich:column>
                                <rich:column id="filtroTipoAgendamento" sortBy="#{fecharMetaDetalhado.agenda.tipoAgendamento_Apresentar}" filterBy="#{fecharMetaDetalhado.agenda.tipoAgendamento_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_tipoAgendamento}" />
                                    </f:facet>
                                    <h:outputText id="tipoAgendamento" value="#{fecharMetaDetalhado.agenda.tipoAgendamento_Apresentar}" />
                                </rich:column>

                                <rich:column id="filtroResultado" sortBy="#{fecharMetaDetalhado.qualResultado}" filterBy="#{fecharMetaDetalhado.qualResultado}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="Result. Ligação" />
                                    </f:facet>
                                    <h:outputText id="resultado" value="#{fecharMetaDetalhado.qualResultado}" />
                                </rich:column>

                                <rich:column id="filtroLancamento" sortBy="#{fecharMetaDetalhado.agenda.dataLancamento_Apresentar}" filterBy="#{fecharMetaDetalhado.agenda.dataLancamento_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_dataLancamento}" />
                                    </f:facet>
                                    <h:outputText id="dataLancamento" value="#{fecharMetaDetalhado.agenda.dataLancamento_Apresentar}" />
                                </rich:column>
                                <rich:column id="filtroUltimoContato" sortBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" filterBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ClientePotencial_dataUltimoContato}" />
                                    </f:facet>
                                    <h:outputText id="dataUltimoContato" value="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" />
                                </rich:column>
                                <rich:column id="filtroDataAgendamento" sortBy="#{fecharMetaDetalhado.agenda.dataAgendamentoComparecimento_Apresentar}" filterBy="#{fecharMetaDetalhado.agenda.dataAgendamentoComparecimento_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_dataAgendamento}" />
                                    </f:facet>
                                    <h:outputText id="dataAgendamento" value="#{fecharMetaDetalhado.agenda.dataAgendamentoComparecimento_Apresentar}" />
                                </rich:column>
                                <rich:column id="filtroDataComparecimento" sortBy="#{fecharMetaDetalhado.agenda.dataComparecimento_Apresentar}" filterBy="#{fecharMetaDetalhado.agenda.dataComparecimento_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_dataComparecimento}" />
                                    </f:facet>
                                    <h:outputText id="dataComparecimento" value="#{fecharMetaDetalhado.agenda.dataComparecimento_Apresentar}" />
                                </rich:column>
                                <rich:column id="filtroResponsavelCom" sortBy="#{fecharMetaDetalhado.agenda.responsavelComparecimento.nome}" filterBy="#{fecharMetaDetalhado.agenda.responsavelComparecimento.nome}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_responsavelComparecimento}" />
                                    </f:facet>
                                    <h:outputText id="responsavelComparecimento" value="#{fecharMetaDetalhado.agenda.responsavelComparecimento.nome}" />
                                </rich:column>
                                <rich:column id="filtroResponsavelAgen" sortBy="#{fecharMetaDetalhado.agenda.colaboradorResponsavel.primeiroNomeConcatenado}" filterBy="#{fecharMetaDetalhado.agenda.colaboradorResponsavel.primeiroNomeConcatenado}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_responsavel}" />
                                    </f:facet>
                                    <h:outputText id="responsavel" value="#{fecharMetaDetalhado.agenda.colaboradorResponsavel.primeiroNomeConcatenado}" />
                                </rich:column>





                            </rich:dataTable>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab onlabelclick="trocarTotal('totalPesquisa'); trocarAba('HI');"  onclick="trocarTotal('totalPesquisa'); trocarAba('HI');"  id="historicoClientePotencial" label="Histórico">
                        <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                            <rich:dataTable id="itemsClientePotencialHistorico" width="100%" headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno" columnClasses="colunaEsquerda" value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.listaHistoricoContatoHistorico}" var="fecharMetaDetalhado">

                                <rich:column>
                                    <f:facet name="header">
                                        <h:panelGroup>
                                            <h:outputText  value="#{msg_aplic.prt_Agendados_opcoes}" />
                                            <h:selectBooleanCheckbox id="selectTodos" title="Marca todos os Agendados" value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.marcaTodosHistorico}">
                                                <a4j:support event="onclick" action="#{AberturaMetaControle.executarSelecaoTodosFecharMetaDetalhadosLigacaoAgendados}" reRender="panelAbas, totalPesquisaNrs" />
                                            </h:selectBooleanCheckbox>
                                        </h:panelGroup>
                                    </f:facet>
                                    <a4j:commandButton id="btnRealizarContato" image="./imagensCRM/icontelefonista.png" title="Realizar novo Contato" actionListener="#{HistoricoContatoControle.selecionarAgendadosRealizacaoContato}" ajaxSingle="true" oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}" >
                                            <f:attribute value="LA" name="faseCRM" />
                                    </a4j:commandButton>
                                    <rich:spacer width="5px" />
                                    <a4j:commandButton id="consultaHistorico" image="./imagensCRM/icon_Lupa.png" title="Consultar Histórico Contato" action="#{HistoricoContatoControle.consultarHistoricoAgendadosVindoTelaMetaAgendadosDetalhada}" ajaxSingle="true" oncomplete="#{HistoricoContatoControle.validarQualPopUpAbrirHistoricoContato}" />
                                    <rich:spacer width="5px" />
                                    <a4j:commandButton id="btnReagendarCom" image="./imagensCRM/reagendamento.png"
                                                       actionListener="#{HistoricoContatoControle.executarReagendamento}"
                                                       title="Reagendar Pessoa" ajaxSingle="true"
                                                       rendered="#{fecharMetaDetalhado.agenda.dataComparecimento == null }"
                                                       reRender="panelAgenda"
                                                       oncomplete="#{HistoricoContatoControle.msgAlert}" >
                                        <f:attribute value="LA" name="faseCRM" />
                                    </a4j:commandButton>
                                    <rich:spacer width="5px" />
                                    <a4j:commandButton id="visualizarAluno" image="./imagensCRM/iconeCliente_menor.png" action="#{AberturaMetaControle.editarPessoaListaFecharMetaDetalhado}" oncomplete="#{AberturaMetaControle.validarQualPopUpAbrirMetaAgendado}" title="Visualizar Cliente" ajaxSingle="true" />
                                    <rich:spacer width="5px" />
                                    <h:selectBooleanCheckbox id="selectAluno" title="Enviar E-mail" value="#{fecharMetaDetalhado.enviarEmailSMS}">
                                        <a4j:support event="onclick" action="#{AberturaMetaControle.executarTotalizadorSelecionadoAgendadosHistorico}" reRender="totalPesquisaNrs" />
                                    </h:selectBooleanCheckbox>
                                </rich:column>
                                <rich:column id="filtroMatricula" sortBy="#{fecharMetaDetalhado.cliente.matricula}" filterBy="#{fecharMetaDetalhado.cliente.matricula}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_matricula}" />
                                    </f:facet>
                                    <h:outputText id="matricula" value="#{fecharMetaDetalhado.cliente.matricula}" rendered="#{fecharMetaDetalhado.validarAgendadosClienteApresentar}" />
                                </rich:column>
                                <rich:column id="filtroNome" filterBy="#{fecharMetaDetalhado.nomePessoaRel}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_nomePessoa}" />
                                    </f:facet>
                                    <h:outputText id="nomePessoaHistorico" value="#{fecharMetaDetalhado.nomePessoaRel}" />
                                </rich:column>
                                <rich:column id="filtroSituacao" filterBy="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar}" filterEvent="onkeyup" >
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Cliente_situacao}" />
                                    </f:facet>
                                    <h:graphicImage id="sitAtivo" value="./images/botaoAtivo.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativo}" />
                                    <h:graphicImage id="sitInativo" value="./images/botaoInativo.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.inativo}" />
                                    <h:graphicImage id="sitVisitante" value="./images/botaoVisitante.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.visitante}" />
                                    <h:graphicImage id="sitTrancado" value="./images/botaoTrancamento.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.trancado}" />
                                    <h:graphicImage id="sitNormal" value="./images/botaoNormal.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativoNormal}" />
                                    <h:graphicImage id="sitTrancVencido" value="./images/botaoTrancadoVencido.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.trancadoVencido}" />
                                    <h:graphicImage id="sitFreePass" value="./images/botaoFreePass.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.visitanteFreePass}" />
                                    <h:graphicImage id="sitAulaAvulsa" value="./images/botaoAulaAvulsa.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.visitanteAulaAvulsa}" />
                                    <h:graphicImage id="sitDiaria" value="./images/botaoDiaria.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.visitanteDiaria}" />
                                    <h:graphicImage id="sitCancelamento" value="./images/botaoCancelamento.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.inativoCancelamento}" />
                                    <h:graphicImage id="sitDesistente" value="./images/botaoDesistente.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.inativoDesistente}" />
                                    <h:graphicImage id="sitAVencer" value="./images/botaoAvencer.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativoAvencer}" />
                                    <h:graphicImage id="sitVencido" value="./images/botaoVencido.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.inativoVencido}" />
                                    <h:graphicImage id="sitCarencia" value="./images/botaoCarencia.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativoCarencia}" />
                                    <h:graphicImage id="sitAtestado" value="./images/botaoAtestado.png" rendered="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.ativoAtestado}" />
                                    <h:outputText value="#{fecharMetaDetalhado.cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar}" style="display:none;"/>
                                </rich:column>
                                <rich:column id="filtroTipoAgendamento" sortBy="#{fecharMetaDetalhado.agenda.tipoAgendamento_Apresentar}" filterBy="#{fecharMetaDetalhado.agenda.tipoAgendamento_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_tipoAgendamento}" />
                                    </f:facet>
                                    <h:outputText id="tipoAgendamentoHistorico" value="#{fecharMetaDetalhado.agenda.tipoAgendamento_Apresentar}" />
                                </rich:column>

                                <rich:column id="filtroResultado" sortBy="#{fecharMetaDetalhado.qualResultado}" filterBy="#{fecharMetaDetalhado.qualResultado}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="Result. Ligação" />
                                    </f:facet>
                                    <h:outputText id="resultado" value="#{fecharMetaDetalhado.qualResultado}" />
                                </rich:column>

                                <rich:column id="filtroLancamento" sortBy="#{fecharMetaDetalhado.agenda.dataLancamento_Apresentar}" filterBy="#{fecharMetaDetalhado.agenda.dataLancamento_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_dataLancamento}" />
                                    </f:facet>
                                    <h:outputText id="dataLancamento" value="#{fecharMetaDetalhado.agenda.dataLancamento_Apresentar}" />
                                </rich:column>
                                <rich:column id="filtroUltimoContato" sortBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" filterBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ClientePotencial_dataUltimoContato}" />
                                    </f:facet>
                                    <h:outputText id="dataUltimoContato" value="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" />
                                </rich:column>

                                <rich:column id="filtroDataAgendamento" sortBy="#{fecharMetaDetalhado.agenda.dataAgendamentoComparecimento_Apresentar}" filterBy="#{fecharMetaDetalhado.agenda.dataAgendamentoComparecimento_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_dataAgendamento}" />
                                    </f:facet>
                                    <h:outputText id="dataAgendamento" value="#{fecharMetaDetalhado.agenda.dataAgendamentoComparecimento_Apresentar}" />
                                </rich:column>
                                <rich:column id="filtroDataComparecimento" sortBy="#{fecharMetaDetalhado.agenda.dataComparecimento_Apresentar}" filterBy="#{fecharMetaDetalhado.agenda.dataComparecimento_Apresentar}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_dataComparecimento}" />
                                    </f:facet>
                                    <h:outputText id="dataComparecimento" value="#{fecharMetaDetalhado.agenda.dataComparecimento_Apresentar}" />
                                </rich:column>
                                <rich:column id="filtroResponsavelCom" sortBy="#{fecharMetaDetalhado.agenda.responsavelComparecimento.nome}" filterBy="#{fecharMetaDetalhado.agenda.responsavelComparecimento.nome}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_responsavelComparecimento}" />
                                    </f:facet>
                                    <h:outputText id="responsavelComparecimento" value="#{fecharMetaDetalhado.agenda.responsavelComparecimento.nome}" />
                                </rich:column>
                                <rich:column id="filtroResponsavelAgen" sortBy="#{fecharMetaDetalhado.agenda.colaboradorResponsavel.primeiroNomeConcatenado}" filterBy="#{fecharMetaDetalhado.agenda.colaboradorResponsavel.primeiroNomeConcatenado}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Agendados_responsavel}" />
                                    </f:facet>
                                    <h:outputText id="responsavel" value="#{fecharMetaDetalhado.agenda.colaboradorResponsavel.primeiroNomeConcatenado}" />
                                </rich:column>


                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_Observacao}" />
                                    </f:facet>
                                    <h:outputText escape="false" value="#{fecharMetaDetalhado.observacao}" />
                                </rich:column>

                            </rich:dataTable>
                        </h:panelGrid>
                    </rich:tab>
                </rich:tabPanel>
            </h:panelGrid>

        </h:panelGrid>

        <h:inputHidden id="abaSelecionada" value="#{AberturaMetaControle.fecharMetaLigacaoAgendadosVO.abaSelecionada}"></h:inputHidden>
    </h:form>
    <%@include file="/includes/crm/include_modal_reagendamento.jsp" %>
</f:view>
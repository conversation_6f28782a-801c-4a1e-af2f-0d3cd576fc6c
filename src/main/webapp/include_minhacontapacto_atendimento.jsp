<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="tituloBoxCanalPacto">
    <h:outputText value="Atendimento" style="font-size: 0.9em!important;" styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
</div>

<table width="100%" style="padding: 1vw;">
    <tr>
        <td width="40%" valign="top">
            <h:panelGrid
                    border="0"
                    columnClasses="colunaEsquerda, colunaDireita"
                    style="border-spacing: 0px!important;width: 100%;"
                    styleClass="tabelaSimplesCustom noHover"  columns="2">
                <a4j:commandLink styleClass="texto-size-16"
                                 action="#{CanalPactoControle.abrirTelaSolEmAberta}">
                    <h:outputText styleClass="texto-size-16" value="Solicitações em aberto"/>
                </a4j:commandLink>

                <a4j:commandLink styleClass="texto-size-16" action="#{CanalPactoControle.abrirTelaSolEmAberta}">
                    <h:outputText styleClass="" style="font-weight:bold;" value="#{CanalPactoControle.estatisticaSolicitacaoJSON.ticketsAbertos}"/>
                </a4j:commandLink>

                <a4j:commandLink styleClass="texto-size-16" action="#{CanalPactoControle.abrirTelaSolConcluidas}">
                  <h:outputText styleClass="texto-size-16"  value="Solicitações concluídas"/>
                </a4j:commandLink>

                <a4j:commandLink styleClass="texto-size-16 texto-cor-cinza" action="#{CanalPactoControle.abrirTelaSolConcluidas}">
                    <h:outputText styleClass="" style="font-weight:bold;" value="#{CanalPactoControle.estatisticaSolicitacaoJSON.ticketsConcluidos}"/>
                </a4j:commandLink>

                <h:outputText styleClass="texto-size-16 texto-cor-cinza"  value="Tempo médio de resposta "/>
                <h:outputText styleClass="texto-size-16 texto-cor-cinza tooltipster"
                              title="O tempo médio de resposta é calculado em relação à data/hora da solicitação do atendimento <br> até  a data/hora em que um atendende começa o atendimento, considerando somente as horas úteis. <br> Foram consideradas as #{CanalPactoControle.limitSolicitacoesEstatistica} últimas solicitações concluídas. "
                              style="font-weight:bold;" value="#{CanalPactoControle.estatisticaSolicitacaoJSON.tempoMedioRespostaApresentar}"/>

            </h:panelGrid>
        </td>

        <td width="60%" colspan="2" >
           <jsp:include page="include_minhacontapacto_atendimento_grafico.jsp" flush="true" />
        </td>


    </tr>
</table>





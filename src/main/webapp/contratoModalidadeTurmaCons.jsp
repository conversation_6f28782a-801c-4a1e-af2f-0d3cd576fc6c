<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ContratoModalidadeTurma_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ContratoModalidadeTurma_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/> 
                    <h:selectOneMenu styleClass="campos" id="consulta" required="true" value="#{ContratoModalidadeTurmaControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{ContratoModalidadeTurmaControle.tipoConsultaCombo}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" styleClass="campos" value="#{ContratoModalidadeTurmaControle.controleConsulta.valorConsulta}"/>
                    <h:commandButton id="consultar" type="submit" styleClass="botoes" value="#{msg_bt.btn_consultar}" action="#{ContratoModalidadeTurmaControle.irPaginaInicial}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="2"/>
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{ContratoModalidadeTurmaControle.apresentarResultadoConsulta}" binding="#{ContratoModalidadeTurmaControle.apresentarLinha}">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false" binding="#{ContratoModalidadeTurmaControle.apresentarPrimeiro}" action="#{ContratoModalidadeTurmaControle.irPaginaInicial}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false" binding="#{ContratoModalidadeTurmaControle.apresentarAnterior}" action="#{ContratoModalidadeTurmaControle.irPaginaAnterior}"/> 
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{ContratoModalidadeTurmaControle.paginaAtualDeTodas}" rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false" binding="#{ContratoModalidadeTurmaControle.apresentarPosterior}" action="#{ContratoModalidadeTurmaControle.irPaginaPosterior}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false" binding="#{ContratoModalidadeTurmaControle.apresentarUltimo}" action="#{ContratoModalidadeTurmaControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{ContratoModalidadeTurmaControle.listaConsulta}" rendered="#{ContratoModalidadeTurmaControle.apresentarResultadoConsulta}" rows="10" var="contratoModalidadeTurma">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ContratoModalidadeTurma_codigo}"/>
                        </f:facet>
                        <h:commandLink action="#{ContratoModalidadeTurmaControle.editar}" id="codigo" value="#{contratoModalidadeTurma.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ContratoModalidadeTurma_contratoModalidade}"/>
                        </f:facet>
                        <h:commandLink action="#{ContratoModalidadeTurmaControle.editar}" id="contratoModalidade" value="#{contratoModalidadeTurma.contratoModalidade}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ContratoModalidadeTurma_turma}"/>
                        </f:facet>
                        <h:commandLink action="#{ContratoModalidadeTurmaControle.editar}" id="turma" value="#{contratoModalidadeTurma.turma.codigo}"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:commandButton action="#{ContratoModalidadeTurmaControle.editar}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ContratoModalidadeTurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ContratoModalidadeTurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{ContratoModalidadeTurmaControle.novo}" value="#{msg_bt.btn_novo}" styleClass="botoes" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>
<%@page contentType="text/html"%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        vertical-align:middle
    }
    .panelImagemTrancamento > tbody > tr > td:first-child {
        vertical-align: baseline;
    }
</style>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        vertical-align:middle
    }
    .to-uper-case{
        text-transform: uppercase;
    }
</style>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_trancamentoContrato_tituloForm}"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="form">
        <h:panelGrid columns="2" styleClass="panelImagemTrancamento" width="100%">
            <h:panelGrid style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;" >
                <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" />
                <h:outputText id="nomeClienteTranc" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.contratoVO.pessoa.nome}"/>
                <br>
                <h:panelGroup styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">DETALHES DO CONTRATO</h:panelGroup>
                <rich:dataTable id="parcela" width="100%"  rowClasses="tablelistras textsmall" columnClasses="col-text-align-left" styleClass="tabelaSimplesCustom" headerClass="col-text-align-left"
                                value="#{TrancamentoContratoControle.listaContratoVOs}" var="contrato" >

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_numeroContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_plano}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.plano.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_dataInicio}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaDe_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_dataTermino}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_valorBaseContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.valorBaseCalculo}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>
                <br>
                <h:panelGrid  id="valorBase" columns="1" columnClasses="left, right" width="100%" border="0" cellspacing="0" cellpadding="0" styleClass="textsmall"  >

                    <h:panelGroup>
                        <h:outputText value="DIAS CONTRATO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold "/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.nrDiasContrato}"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" dia(s)" />
                    </h:panelGroup>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:panelGroup>
                        <h:outputText value="DIAS UTILIZADOS PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.nrDiasUtilizadosPeloClienteContrato}"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" dia(s)" />
                    </h:panelGroup>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <%--h:panelGroup>
                        <h:outputText value="Dias Restantes: " styleClass="tituloCampos"  />
                        <h:outputText styleClass="tituloCampos" value="#{TrancamentoContratoControle.trancamentoContratoVO.nrDiasRestamContrato}"/>
                        <h:outputText styleClass="tituloCampos" value=" dia(s)" />
                    </h:panelGroup--%>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR DIA: "/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" #{MovPagamentoControle.empresaLogado.moeda}" style="padding: 3px"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" #{TrancamentoContratoControle.trancamentoContratoVO.valorDiaContratoValorBase}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UTILIZADO PELO CLIENTE: "/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" #{TrancamentoContratoControle.trancamentoContratoVO.valorUtilizadoPeloClienteBase}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  style="font-weight: bold;" value="DIAS DE BÔNUS: "  />
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.nrDiasBonus} dia(s)"/>

                    </h:panelGroup>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  style="font-weight: bold;" value="VALOR CONGELADO DO CONTRATO:  #{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{TrancamentoContratoControle.trancamentoContratoVO.valorCongelado}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="NÚMERO DIA(S) CONGELADO(s) DO CONTRATO: " />
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.nrDiasCongelado} dia(s)"/>
                    </h:panelGroup>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                </h:panelGrid>
                <rich:spacer height="10px;"/>
                <h:panelGrid columns="1" >
                    <h:panelGroup styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" >DETALHES DO TRANCAMENTO</h:panelGroup>
                    <rich:spacer height="10px;"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_produtoTrancamento}" />
                    <h:panelGroup styleClass="font-size-em-max">
                        <div class="cb-container margenVertical" style="width: 300px;">
                            <h:selectOneMenu  id="produtoTrancamento" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.produtoTrancamento.codigo}" >
                                <f:selectItems  value="#{TrancamentoContratoControle.listaProdutoTrancamentoVOs}" />
                                <a4j:support event="onchange" action="#{TrancamentoContratoControle.obterValorProduto}" reRender="totalLancado,panelPeriodoTrancamento,panelMensagem"/>
                            </h:selectOneMenu>
                        </div>
                        <rich:spacer width="3"/>
                        <a4j:commandLink id="atualizar_produtoTrancamento" action="#{TrancamentoContratoControle.montarDadosListaProdutoTrancamentoVOs}" immediate="true" ajaxSingle="true" reRender="form:produtoTrancamento">
                            <i class="fa-icon-refresh texto-size-14-real texto-cor-cinza " ></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="gridAlteracaoParcela" columns="1" >
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="alterarParcelas" value="#{TrancamentoContratoControle.trancamentoContratoVO.alterarVencimentoparcelas}">
                            <a4j:support event="onclick" reRender="panelAutorizacaoFuncionalidade"
                                         action="#{TrancamentoContratoControle.autorizarVencimentoParcelas}"/>
                        </h:selectBooleanCheckbox>
                        <rich:spacer width="10px"/>
                        <h:outputText value="Alterar vencimento das parcelas com vencimento superior ao início do trancamento" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        <h:panelGroup layout="block" style="padding: 0px 30px 0px;">
                            <h:outputText
                                    value="Esta opção permite que o sistema não realize cobrança das parcelas do contrato que pagam o plano(parcelas de anuidade e produtos são desconsideras) durante o período de trancamento."
                                    styleClass="classInfCadUsuario"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_tipoJustificativa}" />
                    <h:panelGroup styleClass="font-size-em-max">
                        <div class="cb-container margenVertical" style="width: 300px;">
                            <h:selectOneMenu  id="justificativa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.tipoJustificativa}" >
                                <f:selectItems  value="#{TrancamentoContratoControle.listaJustificativaOperacaoVOs}" />
                                <a4j:support event="onchange" action="#{TrancamentoContratoControle.limparMensagem}" reRender="panelMensagem"/>
                            </h:selectOneMenu>
                        </div>
                        <rich:spacer width="3"/>
                        <a4j:commandLink id="atualizar_justificativaDeOperacao" action="#{TrancamentoContratoControle.montarDadosListaJustificativaOperacaoVOs}" immediate="true" ajaxSingle="true" reRender="form:justificativa">
                            <i class="fa-icon-refresh texto-size-14-real texto-cor-cinza " ></i>
                        </a4j:commandLink>
                        <rich:spacer width="5"/>
                        <a4j:commandLink id="adicionarTipoOperacao" action="#{JustificativaOperacaoControle.reset}" oncomplete="abrirPopup('justificativaOperacaoCons.jsp', 'JustificativaOperacao', 800, 595);" >
                            <i class="fa-icon-plus-sign texto-size-14-real texto-cor-cinza "/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" id="panelPeriodoTrancamento" >
                    <h:panelGroup rendered="#{TrancamentoContratoControle.trancamentoContratoVO.apresentarPeriodoTrancamento}">
                        <h:panelGrid columns="1">
                            <h:outputText id="ttlPeriodoTranc" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="PERÍODO DE TRANCAMENTO: " />
                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="INÍCIO" />
                                <rich:spacer width="10"/>
                                <h:outputText id="inicioPeriodoTranc" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.dataTrancamento_Apresentar}">
                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:outputText>
                                <rich:spacer width="10"/>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value=" ATÉ " />
                                <rich:spacer width="10"/>
                                <h:outputText id="fimPeriodoTranc" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.dataFimTrancamento_Apresentar}">
                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:outputText>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DATA DO RETORNO:" />
                                <rich:spacer width="10"/>
                                <h:outputText id="retornoPeriodoTranc" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{TrancamentoContratoControle.trancamentoContratoVO.dataRetorno_Apresentar}">
                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:outputText>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="2">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{msg_aplic.prt_trancamentoContrato_valorTrancamento}  #{MovPagamentoControle.empresaLogado.moeda} " />
                            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font " value="#{TrancamentoContratoControle.trancamentoContratoVO.valorTrancamento}" id="totalLancado" >
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" >
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="OBSERVAÇÃO: "/>
                    <h:inputTextarea value="#{TrancamentoContratoControle.trancamentoContratoVO.observacao}"  rows="6" cols="93"/>
                </h:panelGrid>

                <h:panelGrid id="panelBotoes" width="100%" columns="2" >
                    <h:panelGroup layout="block" style="float:left;">
                        <h:panelGrid id="panelMensagem" columns="3" width="100%">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText value=" "/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGroup>
                    <h:panelGroup style="float: right" layout="block">
                        <h:panelGroup rendered="#{TrancamentoContratoControle.apresentarBotoes && !TrancamentoContratoControle.processandoOperacao}">
                            <h:commandLink id="voltar" title="Voltar Passo" action="#{TrancamentoContratoControle.voltar}" styleClass="pure-button">
                                <i class="fa-icon-arrow-left"></i>
                            </h:commandLink>
                            <rich:spacer width="7"/>
                            <a4j:commandLink id="confirmar"
                                             styleClass="pure-button pure-button-primary"
                                             reRender="panelAutorizacaoFuncionalidade"
                                             oncomplete="#{TrancamentoContratoControle.mensagemNotificar}"
                                             action="#{TrancamentoContratoControle.validarDadosTrancamento}">
                                <i class="fa-icon-ok"></i>&nbsp;Confirmar
                            </a4j:commandLink>
                            <rich:spacer width="7"/>
                            <h:commandLink id="cancelar"  styleClass="pure-button" onclick="fecharJanela();executePostMessage({close: true});">

                                <i class="fa-icon-remove"></i>&nbsp;Cancelar
                            </h:commandLink>
                        </h:panelGroup>
                            <h:panelGroup rendered="#{!TrancamentoContratoControle.apresentarBotoes && !TrancamentoContratoControle.processandoOperacao}">
                            <a4j:commandLink id="comprovanteOpTr"
                                             title="Imprimir Comprovante da Operação de Trancamento"
                                             action="#{TrancamentoContratoControle.imprimirComprovanteOperacao}"
                                             oncomplete="abrirPopupPDFImpressao('relatorio/#{TrancamentoContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                             styleClass="pure-button">
                                <i class="fa-icon-print"></i> &nbsp Imprimir Comprovante
                            </a4j:commandLink>
                            <rich:spacer width="7"/>
                            <h:commandLink id="fechar"  value="Fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});" styleClass=" pure-button pure-button-primary">
                            </h:commandLink>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
                 <h:panelGrid rendered="#{TrancamentoContratoControle.processandoOperacao}" columns="1">
                    <h:outputText id="msgProcessando" styleClass="mensagem"  value="Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                    <rich:spacer height="7"/>
                    <a4j:commandLink id="atualizar" title="Atulaizar" onclick="window.location.reload();fireElementFromParent('form:btnAtualizaCliente');"  styleClass="pure-button pure-button-primary">
                        <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                    </a4j:commandLink>
               </h:panelGrid>                         
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:produtoTrancamento").focus();
</script>

<%-- 
    Document   : viewChavesValidacaoAcessoWS
    Created on : 08/12/2011, 23:50:56
    Author     : Waller
--%>


<%@page import="acesso.webservice.AcessoControle"%>
<%@page import="acesso.webservice.DaoAuxiliar"%>
<%@page import="java.util.Set"%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="include_imports.jsp" %>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <!DOCTYPE html>
    <html>
        <head>
            <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
            <title>View Sessions</title>
            <link href="css/otimize.css" rel="stylesheet" type="text/css"/>
        </head>
        <body>
            <h:form>
                <h:panelGrid id="panelConteudo" columns="5" columnClasses="text,text,text,text">
                    <a4j:commandButton value="Atualizar" action="#{SessionViewControle.atualizar}"
                                       reRender="panelConteudo,tblSessions"/>

                    <a4j:commandButton value="Invalidate ALL Idles" action="#{SessionViewControle.invalidateAllIdles}"
                                       reRender="panelConteudo,tblSessions"/>

                    <h:selectBooleanCheckbox title="Atualizar automaticamente" 
                                             value="#{SessionViewControle.updateAuto}">

                        <a4j:support event="onclick" reRender="panelConteudo"/>
                    </h:selectBooleanCheckbox>

                    <a4j:commandButton title="Expandir tudo" value="+" action="#{SessionViewControle.expandirTudo}"
                                       reRender="tblSessions"/>
                    <a4j:commandButton title="Retrair tudo" value="-" action="#{SessionViewControle.retrairTudo}"
                                       reRender="tblSessions"/>

                    <h:panelGroup styleClass="tituloCampos">
                        <h:outputText style="vertical-align:middle;" value="Filtrar: " />
                        <h:inputText style="vertical-align:middle;" title="Digite algo para filtrar..." size="33"
                                     id="searchInput" value="#{SessionViewControle.valorPesquisa}">

                            <a4j:queue name="queueFiltro" ignoreDupResponses="true" requestDelay="1000" timeout="60000" />

                            <a4j:support eventsQueue="queueFiltro" event="onkeyup"
                                         oncomplete="textSelect(document.getElementById('form:searchInput'), document.getElementById('form:searchInput').value.length);"
                                         action="#{SessionViewControle.filtrarPorTexto}" reRender="panelConteudo,tblSessions"/>

                        </h:inputText>

                        <h:graphicImage id="imageLoading" style="visibility: hidden;vertical-align: middle;align:center" url="images/loading.gif"/>
                        <h:outputText style="padding-left:6px;" value="#{fn:length(SessionViewControle.listaManipulavel)} objetos na lista"/>
                    </h:panelGroup>

                    <a4j:poll id="update" interval="5000" enabled="#{SessionViewControle.updateAuto}"
                              action="#{SessionViewControle.atualizar}" reRender="panelConteudo,tblSessions"
                              limitToList="true" />

                </h:panelGrid>

                <rich:dataTable id="tblSessions" value="#{SessionViewControle.listaManipulavel}"
                                var="s" rowKeyVar="status">
                    <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="ID"/>
                        </f:facet>
                        <h:outputText value="#{s.id}"/>
                    </rich:column>

                    <rich:column sortBy="#{s.dataCriacao}">
                        <f:facet name="header">
                            <h:outputText value="Data Criação"/>
                        </f:facet>
                        <h:outputText value="#{s.dataCriacao}">
                            <f:convertDateTime type="both" pattern="dd/MM/yyyy HH:mm:ss" locale="br"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column sortBy="#{s.ultAcesso}">
                        <f:facet name="header">
                            <h:outputText value="Ult. Acesso"/>
                        </f:facet>
                        <h:outputText style="color:blue;" value="#{s.ultAcesso}">
                            <f:convertDateTime type="both" pattern="dd/MM/yyyy HH:mm:ss" locale="br"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column sortBy="#{s.lastURI}">
                        <f:facet name="header">
                            <h:outputText value="LastURI"/>
                        </f:facet>
                        <h:outputText style="color:red;" value="#{s.lastURI}"/>
                    </rich:column>

                    <rich:column sortBy="#{s.empresa}">
                        <f:facet name="header">
                            <h:outputText value="Empresa"/>
                        </f:facet>
                        <h:outputText value="#{s.empresa}"/>
                    </rich:column>

                    <rich:column sortBy="#{s.usuario}">
                        <f:facet name="header">
                            <h:outputText value="Usuário"/>
                        </f:facet>
                        <h:outputText value="#{s.usuario}"/>
                    </rich:column>

                    <rich:column sortBy="#{s.ip}">
                        <f:facet name="header">
                            <h:outputText value="IP"/>
                        </f:facet>
                        <a4j:commandLink title="Whois" actionListener="#{SessionViewControle.consultarWhois}"
                                         reRender="tblSessions">
                            <f:attribute name="session" value="#{s}"/>
                            <h:outputText value="#{s.ip}"/>
                        </a4j:commandLink>
                        <h:outputText id="teste" escape="false" value="#{s.whois}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Objetos"/>
                        </f:facet>
                        <a4j:commandButton value="#{s.expandido ? '-' : '+'}" actionListener="#{SessionViewControle.actionSession}"
                                           reRender="tblSessions">
                            <f:attribute name="obj" value="#{s}"/>
                            <f:attribute name="op" value="exp"/>
                        </a4j:commandButton>

                        <rich:dataTable rendered="#{s.expandido}" value="#{s.objetos}" var="obj">
                            <rich:column sortBy="#{obj.atributo}">
                                <f:facet name="header">
                                    <h:outputText value="Atributo"/>
                                </f:facet>
                                <h:outputText value="#{obj.atributo}"/>
                            </rich:column>

                            <rich:column sortBy="#{obj.valor}">
                                <f:facet name="header">
                                    <h:outputText value="Valor"/>
                                </f:facet>
                                <h:outputText value="#{obj.valor}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="Opção"/>
                                </f:facet>
                                <a4j:commandButton value="Delete" actionListener="#{SessionViewControle.actionAttributes}"
                                                   reRender="tblSessions">
                                    <f:attribute name="obj" value="#{s}"/>
                                    <f:attribute name="objGen" value="#{obj}"/>
                                    <f:attribute name="op" value="del"/>
                                </a4j:commandButton>
                            </rich:column>
                        </rich:dataTable>                        
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Invalidate"/>
                        </f:facet>
                        <a4j:commandButton value="Cabum!"
                                           actionListener="#{SessionViewControle.invalidate}"
                                           reRender="tblSessions"
                                           onclick="if (!confirm('Você está prestes a finalizar a Sessão desse Usuário. Deseja continuar?')){return false;}">
                            <f:attribute name="obj" value="#{s}"/>
                        </a4j:commandButton>
                        <a4j:commandButton value="Cabum Alguns!"
                                           actionListener="#{SessionViewControle.invalidateTeste}"
                                           reRender="tblSessions"
                                           onclick="if (!confirm('Você está prestes a remover alguns atributos da Sessão desse Usuário. Deseja continuar?')){return false;}">
                            <f:attribute name="obj" value="#{s}"/>
                        </a4j:commandButton>
                    </rich:column>
                </rich:dataTable>


            </h:form>

            <%
                        Set<String> s = DaoAuxiliar.getMapaControladores().keySet();
                        out.println(System.getProperty("com.sun.aas.instanceName", ""));
                        out.println("<table class=\"text\">");
                        out.println("<tr><td><b>Chave</b></td><td><b>Conexão</b></td><td><b>Fechada</b></td></tr>");

                        for (String chave : s) {
                            AcessoControle acc = DaoAuxiliar.getMapaControladores().get(chave);
                            if (acc.getCon() != null) {
                                out.println("<tr><td>" + chave + "</td><td>" + acc.getCon() + "</td><td>" + acc.getCon().isClosed() + "</td></tr>");
                            }
                        }
                        out.println("<table>");
                        out.println("Total: " + s.size());
            %>

            <%
                        if (request.getParameter("resetControllers") != null) {
                            if (request.getParameter("resetControllers").equals("s")) {
                                DaoAuxiliar.resetMapaControladores();
                            }
                        }
            %>
        </body>
    </html>



</f:view>

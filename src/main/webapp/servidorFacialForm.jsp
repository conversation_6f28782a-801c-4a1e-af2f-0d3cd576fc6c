<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title>
        <h:outputText value="#{msg_aplic.prt_ReconhecimentoFacial_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ServidorFacial_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-configurar-o-servidor-facial/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{ServidorFacialControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
                           style="display: none"/>
            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens" id="gridMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText value=" "/>
                </h:panelGrid>
                <h:commandButton rendered="#{ServidorFacialControle.sucesso}" image="./imagens/sucesso.png"/>
                <h:commandButton rendered="#{ServidorFacialControle.erro}" image="./imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" id="mensagem2" value="#{ServidorFacialControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada2"
                                  value="#{ServidorFacialControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="servidorFacial" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_empresa}"/>
                <h:panelGroup>
                    <h:selectOneMenu disabled="#{!ServidorFacialControle.usuarioLogado.administrador}"
                                     id="empresa" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="color:black;"
                                     styleClass="form"
                                     value="#{ServidorFacialControle.servidorFacial.empresa.codigo}">
                        <f:selectItems value="#{ServidorFacialControle.listaEmpresas}"/>
                    </h:selectOneMenu>
                    <a4j:commandButton rendered="#{ServidorFacialControle.usuarioLogado.administrador}"
                                       id="atualizar_empresa"
                                       action="#{ServidorFacialControle.montarListaSelectItemEmpresa}"
                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                       reRender="form:empresa"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_codigo}"/>
                <h:panelGroup>
                    <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                 value="#{ServidorFacialControle.servidorFacial.codigo}"/>
                    <h:message for="codigo" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_descricao}"/>
                <h:panelGroup>
                    <h:inputText id="nome" size="40" maxlength="40" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{ServidorFacialControle.servidorFacial.descricao}"/>
                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ServidorBancoDadosFacial}"/>
                <h:panelGroup>
                    <h:inputText id="servidorBDFacial" size="40" maxlength="40" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 title="Digite o nome do servidor de banco de dados facial"
                                 value="#{ServidorFacialControle.servidorFacial.servidorBDFacial}"/>
                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                </h:panelGroup>


                <h:outputText styleClass="tituloCampos" value="Concentrador de Câmeras:"/>
                <h:inputText size="20" maxlength="20" styleClass="form" id="concentradorCamerasFacial"
                             title="Digite o nome do computador a controlar câmeras"
                             value="#{ServidorFacialControle.servidorFacial.nomecomputador}"/>

                <h:outputText value="Falso positivo:" styleClass="tituloCampos"/>
                <h:panelGroup layout="block">
                    <h:inputText id="falsoPositivo" size="5" maxlength="3" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form falsoPositivo" onkeyup="setIntervalo()" value="#{ServidorFacialControle.servidorFacial.falsoPositivo}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>    
                </h:panelGroup>
                <h:outputText value="Tempo mínimo de acesso:" styleClass="tituloCampos"/>
                <h:inputText size="5" maxlength="5" onblur="blurinput(this);" onfocus="focusinput(this);"
                             styleClass="form" value="#{ServidorFacialControle.servidorFacial.tempoMinimoReenvio}"/>
                <h:outputText value="Distância de Identificação:" styleClass="tituloCampos"/>
                <h:inputText size="5" maxlength="3" onblur="blurinput(this);" onfocus="focusinput(this);"
                             styleClass="form" value="#{ServidorFacialControle.servidorFacial.distanciaIdentificacao}"/>

            </h:panelGrid>

            <%@include file="include_CadCamera.jsp" %>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens" id="gridMensagens1">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText value=" "/>
                    </h:panelGrid>
                    <h:commandButton rendered="#{ServidorFacialControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{ServidorFacialControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" id="mensagem" value="#{ServidorFacialControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada"
                                      value="#{ServidorFacialControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="novo" immediate="true" action="#{ServidorFacialControle.novo}"
                                           value="#{msg_bt.btn_novo}" alt="#{msg_bt.btn_novoLocal}"
                                           title="#{msg_bt.btn_novoLocal}" accesskey="1"
                                           styleClass="botoes nvoBt btSec"/>

                        <h:outputText value="    "/>

                        <a4j:commandButton id="salvar" action="#{ServidorFacialControle.gravar}"
                                           value="#{msg_bt.btn_gravar}"
                                           alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                        <h:outputText value="    "/>

                        <h:panelGroup id="grupoBtnExcluir">
                            <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                             oncomplete="#{ServidorFacialControle.msgAlert}" action="#{ServidorFacialControle.confirmarExcluir}"
                                             value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                        </h:panelGroup>


                        <h:outputText value="    "/>

                        <a4j:commandButton id="consultar" immediate="true"
                                           action="#{ServidorFacialControle.inicializarConsultar}"
                                           value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                           accesskey="4"
                                           styleClass="botoes nvoBt btSec"/>

                        <h:outputText value="    "/>

                        <%--<a4j:commandLink action="#{ServidorFacialControle.realizarConsultaLogObjetoSelecionado}"--%>
                        <%--reRender="form"--%>
                        <%--oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"--%>
                        <%--title="Visualizar Log" styleClass="botoes nvoBt btSec"--%>
                        <%--style="display: inline-block; padding: 8px 15px;">--%>
                        <%--<i style="text-decoration: none" class="fa-icon-list"></i>--%>
                        <%--</a4j:commandLink>--%>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

<script>
    document.getElementById("form:nome").focus();

    jQuery(function ($) {
        $("#form\\:falsoPositivo").mask("9,99");

    });
</script>

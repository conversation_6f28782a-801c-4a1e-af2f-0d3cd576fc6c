<%--
  Created by IntelliJ IDEA.
  User: anderson
  Date: 22/01/19
  Time: 20:44
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@include file="/includes/imports.jsp" %>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script src="./script/gestao_Turma.js" type="text/javascript"></script>
<head>

    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="../css/telaCliente.css" rel="stylesheet" type="text/css"/>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript">
        jQuery.noConflict();
    </script>

    <script>
        function carregarTooltipsterGestaoTurma() {
            carregarTooltipGestaoTurma(jQuery('.tooltipster'));
        }

        function carregarTooltipGestaoTurma(el) {
            el.tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        }
    </script>

</head>

<style type="text/css">
    .textoCarregando {
        margin-left: 0px !important;
    }

</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Turma"/>
    </title>
    <h:form id="form">
        <a4j:keepAlive beanName="GestaoTurmaControle"/>
        <html>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup id="panelConteudo">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:panelGrid columns="2" style="width: 100%;">
                                                <h:panelGroup>
                                                    <h:outputText value="Gestão de Turma"
                                                                  style="float: left; margin-top: 13px"
                                                                  styleClass="container-header-titulo"/>
                                                    <h:outputLink styleClass="linkWiki"
                                                                  style="float: left; margin-top: 11px; margin-left: 0.8em"
                                                                  value="#{SuperControle.urlBaseConhecimento}para-que-serve-o-gestao-de-turma/"
                                                                  title="Clique e saiba mais: Gestão de Turma" target="_blank">
                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                    </h:outputLink>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="painelTipoManutencao" styleClass="margin-box"
                                                  style="padding-bottom: 0px">

                                        <h:selectOneMenu id="comboTipoManutencaoTurma" onblur="blurinput(this);"
                                                         styleClass="inputTextClean"
                                                         onfocus="focusinput(this);"
                                                         value="#{GestaoTurmaControle.tipoManutencaoTurma}">
                                            <f:selectItems
                                                    value="#{GestaoTurmaControle.selectItemTipoManutencaoTurma}"/>
                                            <a4j:support event="onchange" reRender="panelConteudo"
                                                         action="#{GestaoTurmaControle.alterarTipoManutencao}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="painelManutencaoProfessor"
                                                  rendered="#{GestaoTurmaControle.manutencaoProfessor}">
                                        <jsp:include page="include_gestaoTurma_Professor.jsp" flush="true"/>
                                    </h:panelGroup>

                                    <%--                                    <h:panelGroup layout="block" id="painelManutencaoAluno"--%>
                                    <%--                                                  rendered="#{GestaoTurmaControle.manutencaoAlunos}">--%>
                                    <%--                                        <jsp:include page="include_gestaoTurma_AlunoRemover.jsp" flush="true"/>--%>
                                    <%--                                    </h:panelGroup>--%>

                                    <h:panelGroup layout="block" id="painelTransferirAlunos"
                                                  rendered="#{GestaoTurmaControle.manutencaoTransferencia}">
                                        <jsp:include page="include_gestaoTurma_AlunoTransferencia.jsp" flush="true"/>
                                    </h:panelGroup>

                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

        </body>
        </html>

    </h:form>

    <rich:modalPanel id="modalTransferencia" styleClass="novaModal noMargin" shadowOpacity="true"
                     width="600" height="280">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText style="text-align: center" value="TRANSFERÊNCIA DE HORÁRIOS"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <a4j:form>
                <h:panelGroup>
                    <a4j:commandLink
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            style="color: #fff"
                            action="#{GestaoTurmaControle.btnVoltar}"
                            reRender="painelDadosTurma, painelSelecionados, modalTransferencia"
                            id="voltarNoX"/>
                    <rich:componentControl for="modalTransferencia" attachTo="voltarNoX" operation="hide"
                                           event="onclick"/>
                </h:panelGroup>
            </a4j:form>
        </f:facet>
        <a4j:form ajaxSubmit="true">
            <h:panelGrid id="modalTransferencia">
                <h:panelGroup layout="block" style="margin: 10 20 10 20;">
                    <h:outputText
                            styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                            value="Selecione o professor que irá receber os horarios,"/>
                    <div class="col-md-8 texto-size-12 line-space cb-container margenVertical">
                        <h:selectOneMenu disabled="#{GestaoTurmaControle.profReceberHorariosSelecionado != 0}"
                                         value="#{GestaoTurmaControle.profReceberHorariosSelecionado}">
                            <f:selectItems
                                    value="#{GestaoTurmaControle.selectItensProfReceberHorarios}"/>
                        </h:selectOneMenu>
                    </div>
                    <a4j:commandLink rendered="#{!GestaoTurmaControle.transferenciaOK}"
                                     disabled="#{GestaoTurmaControle.horariosComConflitoSize != 0}"
                                     styleClass="botaoSecundario texto-size-12"
                                     style="text-align: center;float:left;width: 20%;margin-left: 2%;margin-top: 6px"
                                     action="#{GestaoTurmaControle.transferirHorarios}"
                                     reRender="modalTransferencia, btnTransfere, painelSelecionados"
                                     value="VALIDAR"/>
                    <a4j:commandLink
                            rendered="#{GestaoTurmaControle.transferenciaOK && GestaoTurmaControle.horariosComConflitoSize == 0}"
                            id="btnTransfere"
                            styleClass="botaoPrimario texto-size-12"
                            style="text-align: center;float:left;width: 20%;margin-left: 2%;margin-top: 6px"
                            action="#{GestaoTurmaControle.btnTransfere}"
                            onclick="iniciarStatusTurma(3.5);jQuery('.growl-container').empty();"
                            reRender="modalTransferencia, painelDadosTurma, painelSelecionados, panelConteudo"
                            oncomplete="Richfaces.hideModalPanel('modalTransferencia');fecharStatus();"
                            value="TRANSFERIR"/>
                </h:panelGroup>
                <h:panelGroup rendered="#{GestaoTurmaControle.horariosSelecionadosSize eq '0'}"
                              id="selecionados0" layout="block" style="margin: 10 20 10 20;">

                    <h:outputText
                            styleClass="texto-size-12 texto-font texto-cor-vermelho"
                            value="Selecione ao menos um horário para validar uma transferência."/>

                </h:panelGroup>
                <h:panelGroup rendered="#{GestaoTurmaControle.horariosComConflitoSize > 0}"
                              id="msgConflito" layout="block" style="margin: 10 20 10 20;">

                    <h:outputText
                            styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                            value="Encontramos alguns problemas:"/>
                    </br>
                    <h:outputText
                            styleClass="texto-size-12 texto-font texto-cor-vermelho"
                            value="#{GestaoTurmaControle.stringHorariosConflitantesSize}"/>

                </h:panelGroup>
                <h:panelGroup rendered="#{GestaoTurmaControle.horariosComConflitoSize > 0}"
                              id="botoesConflito" layout="block" styleClass="texto-size-12"
                              style="text-align: center;display: flex; margin: 10 20 10 20;">
                    <a4j:commandLink id="btn1"
                                     styleClass="botaoSecundario"
                                     style="margin-top: 8px; width: 20%; margin-right: 10px; display: inline-block"
                                     value="VOLTAR"
                                     action="#{GestaoTurmaControle.btnVoltar}"
                                     reRender="modalTransferencia, painelDadosTurma, painelSelecionados, panelConteudo"
                                     oncomplete="Richfaces.hideModalPanel('modalTransferencia');fecharStatus();">
                    </a4j:commandLink>
                    <a4j:commandLink
                            rendered="#{GestaoTurmaControle.empresaLogado.permiteHorariosConcorrentesParaProfessor && GestaoTurmaControle.empresaLogado.professorEmAmbientesDiferentesMesmoHorario}"
                            id="btn2"
                            styleClass="botaoSecundario"
                            style="margin-top: 8px; width: 30%; margin-right: 10px; display: inline-block"
                            onclick="iniciarStatusTurma(3.5);jQuery('.growl-container').empty();"
                            value="TRANSFERIR ASSIM MESMO"
                            action="#{GestaoTurmaControle.btnTransferirMesmoAssim}"
                            reRender="modalTransferencia, painelDadosTurma, painelSelecionados, panelConteudo"
                            oncomplete="Richfaces.hideModalPanel('modalTransferencia');fecharStatus();">
                    </a4j:commandLink>
                    <a4j:commandLink id="btn3"
                                     styleClass="botaoPrimario"
                                     style="margin-top: 8px; width: 30%; display: inline-block"
                                     onclick="iniciarStatusTurma(3.5);jQuery('.growl-container').empty();"
                                     value="APENAS SEM CONFLITO"
                                     action="#{GestaoTurmaControle.btnApenasSemConflitos}"
                                     reRender="modalTransferencia, painelDadosTurma, painelSelecionados, panelConteudo"
                                     oncomplete="Richfaces.hideModalPanel('modalTransferencia');fecharStatus();">
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>

    </rich:modalPanel>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>

<script>
    carregarTooltipsterGestaoTurma();
</script>

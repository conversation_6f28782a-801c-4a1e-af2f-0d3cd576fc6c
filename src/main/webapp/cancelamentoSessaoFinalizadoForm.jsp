<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Cancelamento" />
    </title>
    <c:set var="titulo" scope="session" value="Cancelamento"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
    </h:panelGrid>


    <rich:modalPanel id="panelConfirmacaoCancelamento" autosized="true" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formConfirmacaoCancelamento:senha').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Cancelamento"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkConfirmacaoCancelamento"/>
                <rich:componentControl for="panelConfirmacaoCancelamento" attachTo="hidelinkConfirmacaoCancelamento" operation="hide"  event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoCancelamento">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Confirmação"/>
                </h:panelGrid>
                <h:panelGrid id="panelConfirmacaoCancelamento" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Código:" />
                        <h:inputText id="codigoUsuario" size="5" maxlength="7" style="margin-left:6px" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.responsavelCancelamento.codigo}">
                            <a4j:support event="onchange" focus="formConfirmacaoCancelamento:senha" action="#{CancelamentoSessaoControle.consultarResponsavelCancelamento}" reRender="panelConfirmacaoCancelamento, msg"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.responsavelCancelamento.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text"  value="Usuário:" />
                        <h:outputText styleClass="text" style="margin-left:5px"  value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.responsavelCancelamento.username}" />
                    </h:panelGroup>
                   <h:panelGroup>
                        <h:outputText
                            value="Senha:"
                            styleClass="text" />
                        <h:inputSecret id="senha" size="14" maxlength="64" autocomplete="off"
                            style="margin-left:8px" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.responsavelCancelamento.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('loginConfirmacao')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid id="msg" columns="2" width="100%" >
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{CancelamentoSessaoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{CancelamentoSessaoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <a4j:commandButton id="loginConfirmacao" value="#{msg_bt.btn_confirmar}"
                                   image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                                   action="#{CancelamentoSessaoControle.gravar}"
                                   reRender="form,panelBotoes,panelMensagem"
                                   oncomplete="Richfaces.hideModalPanel('panelConfirmacaoCancelamento');fireElementFromParent('form:btnAtualizaCliente');" />
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form">
        <h:panelGrid columns="2" styleClass="tabMensagens">
            <h:panelGrid columns="1">
                <img src="./imagens/lateralWizardCancelamentoMaior.png" >
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" cellpadding="10">
                <h:panelGroup>
                    <h:outputText value="Nome do Cliente: " styleClass="tituloCampos" style="font-weight: bold;"  />
                    <rich:spacer width="12"/>
                    <h:outputText styleClass="tituloCampos" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                </h:panelGroup>
                <h:panelGrid columns="2" columnClasses="colunaEsquerda, colunaDireita">
                    <h:outputText value="Valor Pago pelo Cliente no Contrato" styleClass="tituloCamposNegrito"/>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorPago" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.somaValorPagoPeloClienteComCheque}" styleClass="tituloCampos">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:outputText value="Valor do Cancelamento do Cliente " styleClass="tituloCamposNegrito"/>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorCobrado" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorUtilizadoCliente}" styleClass="tituloCampos">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:outputText rendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo < 0}" value="Valor cobrado do Cliente " styleClass="tituloCamposNegrito"/>
                    <h:outputText rendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo > 0}" value="Valor devolvido em dinheiro " styleClass="tituloCamposNegrito"/>
                    <h:panelGroup rendered="#{!(CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo == 0)}">
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                            <h:outputText id="valorDevolvido" styleClass="tituloCampos" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo_Apresentar}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                    </h:panelGroup>
                    
                    <h:panelGroup rendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorRecebiveis > 0}">
                            
                            <h:outputLink styleClass="linkWiki"
                                                                  value="#{SuperControle.urlWiki}Informa%C3%A7%C3%B5es_do_Cliente:Contrato:Opera%C3%A7%C3%B5es_Contrato:Afastamento:Cancelamento#Valor_devolvido_em_Receb.C3.ADveis"
                                                                  title="Clique e saiba mais: Devolução de Recebíveis"
                                                                  target="_blank" >
                                                       <h:outputText styleClass="tituloCamposNegrito" value="Valor devolvido em recebíveis">
                            </h:outputText>
                             </h:outputLink>
                             <h:outputLink styleClass="linkWiki"
                                                                  value="#{SuperControle.urlWiki}Informa%C3%A7%C3%B5es_do_Cliente:Contrato:Opera%C3%A7%C3%B5es_Contrato:Afastamento:Cancelamento#Valor_devolvido_em_Receb.C3.ADveis"
                                                                  title="Clique e saiba mais: Devolução de Recebíveis"
                                                                  target="_blank" >
                                 <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                             </h:outputLink>
                        </h:panelGroup>
                       <h:panelGroup rendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorRecebiveis > 0}">
                       
                       <h:outputLink styleClass="linkWiki"
                                                                  value="#{SuperControle.urlWiki}Informa%C3%A7%C3%B5es_do_Cliente:Contrato:Opera%C3%A7%C3%B5es_Contrato:Afastamento:Cancelamento#Valor_devolvido_em_Receb.C3.ADveis"
                                                                  title="Clique e saiba mais: Devolução de Recebíveis"
                                                                  target="_blank" >
                                                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                            <h:outputText styleClass="tituloCampos" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorRecebiveis}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                                                    </h:outputLink>
                       
                       
                           
                        </h:panelGroup>
                    
                </h:panelGrid>
                
                <h:panelGroup rendered="#{CancelamentoSessaoControle.apresentarBoteos
                							&& CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo > 0}">
	                <img src="imagens/atencao.png"/>
	                <h:outputText id="msgDevolucao" value="#{msg.msg_devolucao}"
	                			  styleClass="mensagemGrandeVermelha">
	                			  </h:outputText>
	                <h:outputText id="msgDevManual" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo_Apresentar}"
	                			  styleClass="mensagemGrandeVermelha">
	                </h:outputText>
	            
	                <h:outputText value="."
	                			  styleClass="mensagemGrandeVermelha">			  			  
	                </h:outputText>
                </h:panelGroup>
                
                <h:panelGroup rendered="#{CancelamentoSessaoControle.apresentarBoteos
                							&& CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo < 0}">
	                <img src="imagens/atencao.png"/>
	                <h:outputText id="msgQuitacao" value="#{msg.msg_quitacaoCancelamento}"
	                			  styleClass="mensagemGrandeVermelha"></h:outputText>
	                <h:outputText value=" (#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo_Apresentar})."
	                			  styleClass="mensagemGrandeVermelha">			  
	                </h:outputText>
                </h:panelGroup>
                
                <h:panelGroup>
                    <h:outputText style="font-weight: bold" value="Observação: "/>
                    <h:inputTextarea value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.observacao}" id="descricaoCalculo" styleClass="camposSomenteLeitura"  rows="7" cols="80"/>
                </h:panelGroup>

                <h:panelGrid id="panelMensagem" columns="1" width="100%">
                    <%--<h:outputText styleClass="mensagem"  value="#{CancelamentoSessaoControle.mensagem}"/>--%>
                    <h:commandButton  rendered="#{CancelamentoSessaoControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{CancelamentoSessaoControle.erro}" image="./imagens/erro.png"/>
                    <h:outputText id="msgCancelamentoVerde" styleClass="tituloCamposVerde" value="#{CancelamentoSessaoControle.mensagem}"/>
                    <h:outputText id="msgCancelamentoErro" styleClass="tituloCamposNegritoMaiorVermelho" value="#{CancelamentoSessaoControle.mensagemDetalhada}"/>
                </h:panelGrid>

			<h:panelGrid width="100%" columns="3" columnClasses="colunaDireita">
			<h:commandButton id="voltar" 
                            				 alt="Voltar Passo" 
                            				 rendered="#{CancelamentoSessaoControle.apresentarBotaoCancelar}"
                            				 action="#{CancelamentoSessaoControle.voltarTelaCancelamento}" 
                            				 image="./imagens/botaoVoltar.png" />
                           
                            <h:commandButton id="fechar" alt="Fechar Janela" onclick="fecharJanela();" 
                            				 rendered="#{!CancelamentoSessaoControle.apresentarBotaoCancelar}"
                            				 image="./images/btn_fecharcancelamento.gif" />
                            <rich:spacer width="7"/>
                            <a4j:commandButton id="confirmar"  
                            				   rendered="#{CancelamentoSessaoControle.apresentarBotaoCancelar}" 
                            				   alt="Finalizar" reRender="form,formConfirmacaoCancelamento,panelMensagem,panelBotoes" 
                            				   action="#{CancelamentoSessaoControle.limparMensagem}"
                            				   onclick="Richfaces.showModalPanel('panelConfirmacaoCancelamento')" 
                            				   image="./images/btn_confirmar.gif" />
                        	
                        	<a4j:commandButton id="imprimir" rendered="#{!CancelamentoSessaoControle.apresentarBotaoCancelar 
                        									&& CancelamentoSessaoControle.mostrarBotaoRecibo}"
                   					           action="#{CancelamentoSessaoControle.imprimirReciboDevolucao}"
                    			               image="imagens/imprimir_recibo.png"
                    				           oncomplete="abrirPopupPDFImpressao('relatorio/#{CancelamentoSessaoControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);"/>
                    
			</h:panelGrid>
				
			</h:panelGrid>
		</h:panelGrid>
    </h:form>
</f:view>

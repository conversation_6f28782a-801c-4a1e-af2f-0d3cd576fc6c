<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>Observações</title>

    <%-- INICIO HEADER --%>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario"
                          value="Observações">
                <h:outputLink
                        value="#{SuperControle.urlWiki}" title="Clique e saiba mais: " target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:outputText>
        </h:panelGrid>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">

                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblClienteObservacao"
                       class="tabelaClienteObservacao pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>Código</th>
                    <th>Usuário</th>
                    <th>Data Cadastro</th>
                    <th>Observação</th>
                    </thead>
                    <tbody></tbody>
                </table>

            </h:panelGroup>
            <%-- FIM CONTENT --%>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="./imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>
        <a4j:status onstart="Richfaces.showModalPanel('panelStatus');"
                    onstop="Richfaces.hideModalPanel('panelStatus');">
        </a4j:status>

        <%@include file="/pages/ce/includes/include_modal_exibeLogEntidade.jsp" %>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script type="text/javascript">
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaClienteObservacao", "${contexto}/prest/basico/clienteObservacao?codCliente=${ClienteControle.clienteVO.codigo}", 0, "desc", "", false);
        });
    </script>
</f:view>
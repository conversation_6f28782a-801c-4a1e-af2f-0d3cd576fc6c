<%@include file="includes/imports.jsp" %>
	<h:form>
		<rich:panel id="dados" style="height:400px;width:450px">
			<h:panelGrid columns="3" width="100%" id="tabMensagens"
				styleClass="tabMensagens">
				<!-- MENSAGENS -->
				<h:panelGrid columns="1" width="100%">
					<h:outputText styleClass="mensagem"
						value="#{PlanoContasControle.mensagem}" escape="false" />
					<h:outputText styleClass="mensagemDetalhada"
						value="#{PlanoContasControle.mensagemDetalhada}" escape="false" />
				</h:panelGrid>
			</h:panelGrid>
			<h:panelGrid columns="2" id="formPlano">

				<h:outputText value="C�digo do Plano de Contas:" />
				<h:panelGrid columns="2" id="codigoPlano">
					<h:outputText value="#{PlanoContasControle.codigoPai}" />
					<h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
						styleClass="form" size="5" maxlength="3"
						style="border: 1px solid #8eb3c3"
						value="#{PlanoContasControle.codigoEditavel}" id="codigo"></h:inputText>

				</h:panelGrid>

				<h:outputText value="Descri��o:" />
				<h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
					styleClass="form" size="30" maxlength="30"
					style="border: 1px solid #8eb3c3"
					value="#{PlanoContasControle.planoExibicao.descricao}"
					id="descricao"></h:inputText>
			</h:panelGrid>
			<!-- BOT�ES -->
			<h:panelGrid columns="3" id="botoesPlanoConta">
				<h:commandButton value="Salvar"
					action="#{PlanoContasControle.salvar}"
					rendered="#{PlanoContasControle.btSalvar}"></h:commandButton>
				<a4j:commandButton value="Editar"
					action="#{PlanoContasControle.editar}" reRender="dados"
					rendered="#{PlanoContasControle.btEditar}"></a4j:commandButton>
				<a4j:commandButton value="Incluir"
					action="#{PlanoContasControle.incluir}"
					rendered="#{PlanoContasControle.btIncluir}" reRender="dados"></a4j:commandButton>
				<a4j:commandButton action="#{PlanoContasControle.copiar}"
					rendered="#{PlanoContasControle.btCopiar}" value="Copiar"></a4j:commandButton>
				<h:commandButton action="#{PlanoContasControle.colar}"
					rendered="#{PlanoContasControle.btColar}" value="Colar"></h:commandButton>
				<a4j:commandButton value="Excluir" reRender="treeview, dados"
					onclick="if(!confirm('Deseja realmente excluir este plano de contas?')) {return false;}"
					action="#{PlanoContasControle.excluir}"
					rendered="#{PlanoContasControle.btExcluir}"></a4j:commandButton>
				<a4j:commandButton value="Cancelar"
					action="#{PlanoContasControle.cancelar}" reRender="dados"
					rendered="#{PlanoContasControle.btCancelar}"></a4j:commandButton>
			</h:panelGrid>
		</rich:panel>
	</h:form>	
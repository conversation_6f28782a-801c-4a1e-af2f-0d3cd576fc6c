<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>



<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_JustificativaOperacao_tituloForm}"/>
    </title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_JustificativaOperacao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Config._de_Contrato:Justificativa_de_Operação"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{JustificativaOperacaoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1"  width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                <%--<h:panelGrid columns="2" styleClass="tabForm" width="100%">--%>

                    <%-- <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_JustificativaOperacao_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{JustificativaOperacaoControle.justificativaOperacaoVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>--%>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_JustificativaOperacao_descricao}" />
                    <h:panelGroup>
                        <h:inputText onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" id="descricao" size="80" maxlength="50"  value="#{JustificativaOperacaoControle.justificativaOperacaoVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_JustificativaOperacao_tipoOperacao}" />
                    <h:selectOneMenu  id="tipoOperacao" styleClass="form" value="#{JustificativaOperacaoControle.justificativaOperacaoVO.tipoOperacao}" >
                        <f:selectItems  value="#{JustificativaOperacaoControle.listaSelectItemTipoOperacaoJustificativaOperacao}" />
                        <a4j:support event="onchange" reRender="panelGridForm"/>
                    </h:selectOneMenu>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_JustificativaOperacao_ativo}"/>
                    <h:selectBooleanCheckbox id="ativo" styleClass="form"
                                             value="#{JustificativaOperacaoControle.justificativaOperacaoVO.ativa}"/>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_JustificativaOperacao_apresentarTodasEmpresas}"/>
                    <h:selectBooleanCheckbox id="apresentarTodasEmpresas" styleClass="form"
                                             value="#{JustificativaOperacaoControle.justificativaOperacaoVO.apresentarTodasEmpresas}"/>

                    <c:if test="${JustificativaOperacaoControle.justificativaOperacaoVO.tipoOperacao eq 'CA'}">
                        <h:outputText styleClass="tituloCampos"
                                      title="Essa configuração apenas aplicará para o modelo de cancelamento 'Cancelamento avaliando parcelas'"
                                      value="#{msg_aplic.prt_JustificativaOperacao_isentarMultaCancelamento}"/>
                        <h:selectBooleanCheckbox id="isentarMultaCancelamento" styleClass="form"
                                                 title="Essa configuração apenas aplicará para o modelo de cancelamento 'Cancelamento avaliando parcelas'"
                                                 value="#{JustificativaOperacaoControle.justificativaOperacaoVO.isentarMultaCancelamento}"/>

                        <h:outputText styleClass="tituloCampos"
                                      title="Essa configuração apenas aplicará para o modelo de cancelamento 'Cancelamento avaliando parcelas'"
                                      value="#{msg_aplic.prt_JustificativaOperacao_naoCobrarParcelasAtrasadasCancelamento}"/>
                        <h:selectBooleanCheckbox id="naoCobrarParcelasAtrasadasCancelamento" styleClass="form"
                                                 title="Essa configuração apenas aplicará para o modelo de cancelamento 'Cancelamento avaliando parcelas'"
                                                 value="#{JustificativaOperacaoControle.justificativaOperacaoVO.naoCobrarParcelasAtrasadasCancelamento}"/>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_JustificativaOperacao_necessarioAnexarComprovante}"/>
                        <h:selectBooleanCheckbox id="necessarioAnexarComprovante" styleClass="form"
                                                 value="#{JustificativaOperacaoControle.justificativaOperacaoVO.necessarioAnexarComprovante}"/>
                    </c:if>

                    <h:outputText
                            rendered="#{JustificativaOperacaoControle.justificativaOperacaoVO.usuarioVO.administrador}"
                            styleClass="tituloCampos" value="#{msg_aplic.prt_JustificativaOperacao_empresa}"/>
                    <h:panelGroup
                            rendered="#{JustificativaOperacaoControle.justificativaOperacaoVO.usuarioVO.administrador}">
                        <h:selectOneMenu id="empresa" styleClass="form"
                                         value="#{JustificativaOperacaoControle.justificativaOperacaoVO.empresa.codigo}">
                            <f:selectItems value="#{JustificativaOperacaoControle.listaEmpresas}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_empresa"
                                           action="#{JustificativaOperacaoControle.montarListaEmpresas}"
                                           image="imagens/atualizar.png" ajaxSingle="true" reRender="form:empresa"/>
                        <h:message for="empresa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>



                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                   <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                         <f:verbatim>
                            <h:outputText value=" "/>
                        </f:verbatim>
                         </h:panelGrid>
                         <h:commandButton  rendered="#{JustificativaOperacaoControle.sucesso}" image="./imagens/sucesso.png"/>
                         <h:commandButton rendered="#{JustificativaOperacaoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgJustificativa" styleClass="mensagem"  value="#{JustificativaOperacaoControle.mensagem}"/>
                            <h:outputText id="msgJustificativaDet" styleClass="mensagemDetalhada" value="#{JustificativaOperacaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{JustificativaOperacaoControle.novo}" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="10"/>
                            <a4j:commandButton id="salvar" action="#{JustificativaOperacaoControle.gravar}" value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <rich:spacer width="10"/>
                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{JustificativaOperacaoControle.msgAlert}" action="#{JustificativaOperacaoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>
                            <rich:spacer width="10"/>
                            <a4j:commandButton id="consultar" immediate="true" action="#{JustificativaOperacaoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <a4j:commandLink action="#{JustificativaOperacaoControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>

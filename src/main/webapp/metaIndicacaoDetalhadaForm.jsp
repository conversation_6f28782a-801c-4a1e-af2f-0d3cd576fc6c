<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
</style>
<script type="text/javascript">
    function trocarTotal(show) {
        var totalHoje = document.getElementById('totalHoje');
        var totalPesquisa = document.getElementById('totalPesquisa');

        totalHoje.style.display = 'none';
        totalPesquisa.style.display = 'none';

        var filtros = document.getElementById('filtros');
        var mostrar = document.getElementById(show);
        mostrar.style.display = 'block';

        if (show == 'totalPesquisa') {
            filtros.style.display = 'block';
        } else {
            filtros.style.display = 'none';
        }
    }

    function trocarAba(valor) {
        var abaSelecionada = document.getElementById('form:abaSelecionada');
        abaSelecionada.value = valor;
    }

</script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_HistoricoIndicado_tituloForm}" /></title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp" />
        </f:facet>

    </h:panelGrid>
    <h:form id="form" >
        <h:commandLink action="#{AberturaMetaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
        <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
            <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_HistoricoIndicado_tituloForm}" />
            </h:panelGrid>
        </h:panelGrid>

        <!-- ---------------------------- TOTALIZADORES --------------------------------- -->


        <h:panelGrid columns="2" columnClasses="colunaEsquerda" styleClass="tabForm" width="100%">
            <h:panelGroup id="totalizadores">


                <div id="totalHoje" style="display: block;">
                    <h:panelGrid columns="1" id="totalHojeNrs">
                        <h:panelGroup>
                            <h:outputText id="ttHojeLista" styleClass="tituloCamposDestaqueNegrito" title="Total de Indicados na lista" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencial}" />
                            <rich:spacer width="12px" />
                            <h:outputText id="ttHoje" styleClass="tituloCamposDestaqueNegrito" title="Total de Indicados"  value="#{AberturaMetaControle.fecharMetaIndicadoVO.totalizadorHoje}" />
                            <rich:spacer width="30px" />
                            <h:outputText id="ttHojeSelecionadoEmail" styleClass="tituloCamposDestaqueNegrito" title="Total de Indicados selecionados" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencialEmail}" />
                            <rich:spacer width="12px" />
                            <h:outputText id="ttHojeSelecinado" styleClass="tituloCamposDestaqueNegrito" title="Total de Indicados selecionados" value="#{AberturaMetaControle.fecharMetaIndicadoVO.totalizadorSelecionadoHoje}" />
                        </h:panelGroup>
                    </h:panelGrid>
                </div>

                <div id="totalPesquisa" style="display: none;">
                    <h:panelGrid columns="1" id="totalPesquisaNrs">
                        <h:panelGroup>
                            <h:outputText id="ttPesquisaLista" styleClass="tituloCamposDestaqueNegrito" title="Total de Indicados na lista" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencial}" />
                            <rich:spacer width="12px" />
                            <h:outputText id="ttPesquisa" styleClass="tituloCamposDestaqueNegrito" title="Total de Indicados" value="#{AberturaMetaControle.fecharMetaIndicadoVO.totalizadorHistorico}" />
                            <rich:spacer width="30px" />
                            <h:outputText id="ttPesquisaSelecionadoEmail" styleClass="tituloCamposDestaqueNegrito" title="Total de Indicados selecionados" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencialEmail}" />
                            <rich:spacer width="12px" />
                            <h:outputText id="ttPesquisaSelecionado" styleClass="tituloCamposDestaqueNegrito" title="Total de Indicados selecionados" value="#{AberturaMetaControle.fecharMetaIndicadoVO.totalizadorSelecionadoHistorico}" />
                        </h:panelGroup>
                    </h:panelGrid>
                </div>
            </h:panelGroup>
            <h:panelGroup>
                <rich:spacer width="30px" />

                <a4j:commandButton id="btnAtualizar" value="Atualizar" reRender="totalHojeNrs, itemsClientePotencial"
                                   actionListener="#{AberturaMetaControle.atualizarFecharMetaDetalhada}" styleClass="botaoPesquisar" image="./imagensCRM/atualizarCRM.png">
                    <f:attribute name="identificador" value="IN"/>
                </a4j:commandButton>&nbsp;

                <a4j:commandButton id="btnEnviarEmail" styleClass="botaoPesquisar"
                                   reRender="form:panelGridMensagens, mensagemNotificacaoColetivoForm, totalLista"
                                   image="./imagensCRM/botaoEnviarEmail.png" title="Enviar E-mail"
                                   oncomplete="#{AberturaMetaControle.executarAberturaPopupMalaDireta}"
                                   action="#{AberturaMetaControle.executarAberturaPopupEmailColetivoIndicado}" />
                <a4j:commandButton id="btnEnviarSMS" styleClass="botaoPesquisar" reRender="form:panelGridMensagens, mensagemNotificacaoColetivoForm, totalLista"
                                   title="Enviar SMS" value="Enviar SMS"  image="./imagensCRM/enviar-sms.png"
                                   oncomplete="#{AberturaMetaControle.executarAberturaPopupMalaDireta}"
                                   action="#{AberturaMetaControle.executarAberturaPopupSMSColetivoIndicado}" />
                &nbsp;
                <a4j:commandLink  id="btnImprimirLista" action="#{AberturaMetaControle.executarImpressaoPdfIndicado}"
                                  styleClass="botaoPesquisar"
                                  oncomplete="abrirPopupPDFImpressao('relatorio/#{AberturaMetaControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                    <h:graphicImage id="btnImgImprimirLista" style="border:none;" url="./imagensCRM/botaoImprimirLista.png" title="Imprimir Lista(PDF)" />
                </a4j:commandLink>
                <a4j:commandLink id="btnExportarExcel" action="#{AberturaMetaControle.executarImpressaoExcelIndicado}" oncomplete="location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=MetaDetalhadoExcelClienteRel.xlsx'" styleClass="botaoPesquisar">
                    <h:graphicImage id="btnImgExportarExcel" url="./imagensCRM/botaoGerarExcel.png" style="border:none;" title="Imprimir Lista(Excel)" />
                </a4j:commandLink>

                <rich:spacer width="50px" />
                <h:outputText value="#{msg_aplic.prt_HistoricoIndicado_consProf}" styleClass="tituloCampos" />
                <rich:spacer width="10px" />
                <h:outputText id="nomeResponsavel" value="#{AberturaMetaControle.nomeParticipanteSelecionadoVenda}" styleClass="tituloCampos" />
            </h:panelGroup>

        </h:panelGrid>

        <!-- ---------------------------- FILTROS --------------------------------- -->

        <table id="filtros" style="display: none" class="tabForm" width="100%">
            <tr><td width="100%">


                    <h:panelGroup style="width: 100%">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePotencial_nome}" />
                        <rich:spacer width="5px" />
                        <h:inputText id="consultaNome" value="#{AberturaMetaControle.valorConsultaNomeIndicado}" size="50" styleClass="camposAgendado" />

                        <rich:spacer width="10px" />

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_periodo}" />
                        <rich:spacer width="12px" />
                        <rich:calendar id="dataInicio" value="#{AberturaMetaControle.dataInicio}" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                        <h:message for="dataInicio" styleClass="mensagemDetalhada" />
                        <rich:spacer width="12px" />
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_ate}" />

                        <rich:spacer width="12px" />
                        <rich:calendar id="dataTermino" value="#{AberturaMetaControle.dataTermino}" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="true" />
                        <h:message for="dataTermino" styleClass="mensagemDetalhada" />
                        <rich:spacer width="12px" />
                        <a4j:commandButton id="btnPesquisar" oncomplete="trocarTotal('totalPesquisa');"
                                           styleClass="botaoPesquisar"
                                           action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVendaIndicadoPorHistorico}"
                                           image="./imagensCRM/botaoPesquisar.png"
                                           reRender="totalPesquisaNrs, itemsIndicadoHistorico" />
                    </h:panelGroup>


                </td></tr>
        </table>



        <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
            <h:outputText id="msgIndicacao" styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
            <h:outputText id="msgIndicacaoDet" styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
        </h:panelGrid>

        <h:panelGrid columns="1" styleClass="tabForm" width="100%">
            <rich:tabPanel id="panelAbas" width="100%" activeTabClass="true" switchType="client">
                <rich:tab  onlabelclick="trocarTotal('totalHoje'); trocarAba('HJ');" onclick="trocarTotal('totalHoje'); trocarAba('HJ');" id="hojeIndicado" label="Hoje -  #{AberturaMetaControle.aberturaMetaVO.dia_Apresentar}">
                    <h:panelGrid columns="1" styleClass="tabForm" width="100%">

                        <rich:dataTable id="itemsClientePotencial" width="100%" headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno" columnClasses="colunaEsquerda" value="#{AberturaMetaControle.fecharMetaIndicadoVO.fecharMetaDetalhadoVOs}" var="fecharMetaDetalhado">

                            <rich:column>
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_opcoes}" />
                                        <h:selectBooleanCheckbox id="selectTodos" title="Marca todos os Indicados" value="#{AberturaMetaControle.fecharMetaIndicadoVO.marcaTodosHoje}">
                                            <a4j:support event="onclick" action="#{AberturaMetaControle.executarSelecaoTodosFecharMetaDetalhadosIndicacao}" reRender="panelAbas, totalHojeNrs" />
                                        </h:selectBooleanCheckbox>
                                    </h:panelGroup>
                                </f:facet>
                                <a4j:commandButton id="btnRealizarContato" image="./imagensCRM/icontelefonista.png" title="Realizar novo Contato"
                                                   action="#{HistoricoContatoControle.selecionarIndicadoRealizacaoContato}" ajaxSingle="true"
                                                   oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}" />
                                <rich:spacer width="10px" />
                                <a4j:commandButton id="consultaHistorico" image="./imagensCRM/icon_Lupa.png" title="Consultar Histórico Contato" 
                                                   action="#{HistoricoContatoControle.consultarHistoricoIndicadoVindoTelaMetaIndicadoDetalhada}"
                                                   ajaxSingle="true" oncomplete="abrirPopup('historicoContatoIndicadoForm.jsp', 'HistoricoContatoIndicado', 512, 530);" />
                                <rich:spacer width="10px" />
                                <a4j:commandButton id="visualizarCadastro" image="./imagensCRM/indicacao_menor.png" action="#{IndicacaoControle.editarIndicadosListaFecharMetaDetalhado}" title="Visualizar Cadastro" ajaxSingle="true" oncomplete="abrirPopup('indicacaoForm.jsp', 'configuracaoSistemaCRM', 780, 595);" />
                                <rich:spacer width="10px" />
                                <h:selectBooleanCheckbox id="selectIndicado" title="Enviar E-mail" value="#{fecharMetaDetalhado.enviarEmailSMS}">
                                    <a4j:support event="onclick" action="#{AberturaMetaControle.executarTotalizadorSelecionadoIndicacaoHoje}" reRender="totalHojeNrs" />
                                </h:selectBooleanCheckbox>
                            </rich:column>
                            <rich:column  id="filtroNome" filterBy="#{fecharMetaDetalhado.indicado.nomeIndicado}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.nomeIndicado}" >
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_nomePessoa}" />
                                </f:facet>
                                <h:outputText id="nomeIndicado" value="#{fecharMetaDetalhado.indicado.nomeIndicado}" />
                            </rich:column>
                            <rich:column id="filtroDia" filterBy="#{fecharMetaDetalhado.indicado.indicacaoVO.dia_Apresentar}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.indicacaoVO.dia_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_dataLancamento}" />
                                </f:facet>
                                <h:outputText id="dia" value="#{fecharMetaDetalhado.indicado.indicacaoVO.dia_Apresentar}" />
                            </rich:column>
                            <rich:column id="filtroUltimoContato" filterBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_dataUltimoContato}" />
                                </f:facet>
                                <h:outputText id="dataUltimoContato" value="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" />
                            </rich:column>
                            <rich:column  id="filtroTelefoneIndicado" filterBy="#{fecharMetaDetalhado.indicado.telefoneIndicado}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.telefoneIndicado}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_telIndicado}" />
                                </f:facet>
                                <h:outputText id="telefoneIndicado" value="#{fecharMetaDetalhado.indicado.telefoneIndicado}" />
                            </rich:column>
                            <rich:column  id="filtroTelefone" filterBy="#{fecharMetaDetalhado.indicado.telefone}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.telefone}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_telefone}" />
                                </f:facet>
                                <h:outputText id="telefone" value="#{fecharMetaDetalhado.indicado.telefone}" />
                            </rich:column>
                            <rich:column id="filtroEmail" filterBy="#{fecharMetaDetalhado.indicado.email}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.email}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_email}" />
                                </f:facet>
                                <h:outputText id="email" value="#{fecharMetaDetalhado.indicado.email}" />
                            </rich:column>
                            <rich:column id="filtroEvento" filterBy="#{fecharMetaDetalhado.indicado.indicacaoVO.evento.descricao}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.indicacaoVO.evento.descricao}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_evento}" />
                                </f:facet>
                                <h:outputText id="evento" value="#{fecharMetaDetalhado.indicado.indicacaoVO.evento.descricao}" />
                            </rich:column>
                            <rich:column id="filtroResponsavel" filterBy="#{fecharMetaDetalhado.indicado.indicacaoVO.colaboradorResponsavel.nome}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.indicacaoVO.colaboradorResponsavel.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_colaboradorResponsavel}" />
                                </f:facet>
                                <h:outputText id="colabordorResponsavel" value="#{fecharMetaDetalhado.indicado.indicacaoVO.colaboradorResponsavel.nome}" />
                            </rich:column>
                            <rich:column id="filtroResultado" filterBy="#{fecharMetaDetalhado.historicoContatoVO.resultado}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.historicoContatoVO.resultado}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_resultado}" />
                                </f:facet>
                                <h:outputText id="resultado" value="#{fecharMetaDetalhado.historicoContatoVO.resultado}" />
                            </rich:column>

                        </rich:dataTable>
                    </h:panelGrid>
                </rich:tab>
                <rich:tab id="historicoIndicado"  onlabelclick="trocarTotal('totalPesquisa'); trocarAba('HI');" onclick="trocarTotal('totalPesquisa'); trocarAba('HI');" label="Histórico">
                    <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                        <rich:dataTable id="itemsIndicadoHistorico" width="100%" headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno" columnClasses="colunaEsquerda" value="#{AberturaMetaControle.fecharMetaIndicadoVO.listaHistoricoContatoHistorico}" var="fecharMetaDetalhado">

                            <rich:column>
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_opcoes}" />
                                        <h:selectBooleanCheckbox id="selectTodos" title="Marca todos os Indicados" value="#{AberturaMetaControle.fecharMetaIndicadoVO.marcaTodosHistorico}">
                                            <a4j:support event="onclick" action="#{AberturaMetaControle.executarSelecaoTodosFecharMetaDetalhadosIndicacao}" reRender="panelAbas, totalPesquisaNrs" />
                                        </h:selectBooleanCheckbox>
                                    </h:panelGroup>
                                </f:facet>
                                <a4j:commandButton id="btnRealizarContato" image="./imagensCRM/icontelefonista.png" title="Realizar novo Contato" 
                                                   action="#{HistoricoContatoControle.selecionarIndicadoRealizacaoContato}" ajaxSingle="true"
                                                   oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}" />
                                <rich:spacer width="10px" />
                                <a4j:commandButton id="consultaHistorico" image="./imagensCRM/icon_Lupa.png" title="Consultar Histórico Contato"
                                                   action="#{HistoricoContatoControle.consultarHistoricoIndicadoVindoTelaMetaIndicadoDetalhada}" ajaxSingle="true" oncomplete="abrirPopup('historicoContatoIndicadoForm.jsp', 'HistoricoContatoIndicado', 512, 530);" />
                                <rich:spacer width="10px" />
                                <a4j:commandButton id="visualizarCadastro" image="./imagensCRM/indicacao_menor.png" action="#{IndicacaoControle.editarIndicadosListaFecharMetaDetalhado}" title="Visualizar Cadastro" ajaxSingle="true" oncomplete="abrirPopup('indicacaoForm.jsp', 'configuracaoSistemaCRM', 780, 595);" />
                                <rich:spacer width="10px" />
                                <h:selectBooleanCheckbox id="selectAluno" title="Enviar E-mail" value="#{fecharMetaDetalhado.enviarEmailSMS}">
                                    <a4j:support event="onclick" action="#{AberturaMetaControle.executarTotalizadorSelecionadoIndicacaoHistorico}" reRender="totalPesquisaNrs" />
                                </h:selectBooleanCheckbox>
                            </rich:column>
                            <rich:column id="filtroNome" filterBy="#{fecharMetaDetalhado.indicado.nomeIndicado}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.nomeIndicado}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_nomePessoa}" />
                                </f:facet>
                                <h:outputText id="nomeIndicadoHistorico" value="#{fecharMetaDetalhado.indicado.nomeIndicado}" />
                            </rich:column>
                            <rich:column id="filtroDia" filterBy="#{fecharMetaDetalhado.indicado.indicacaoVO.dia_Apresentar}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.indicacaoVO.dia_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_dataLancamento}" />
                                </f:facet>
                                <h:outputText id="diaHistorico" value="#{fecharMetaDetalhado.indicado.indicacaoVO.dia_Apresentar}" />
                            </rich:column>
                            <rich:column id="filtroUltimoContato" filterBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_dataUltimoContato}" />
                                </f:facet>
                                <h:outputText id="dataUltimoContato" value="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" />
                            </rich:column>
                            <rich:column id="filtroTelefoneIndicado" filterBy="#{fecharMetaDetalhado.indicado.telefoneIndicado}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.telefoneIndicado}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_telIndicado}" />
                                </f:facet>
                                <h:outputText id="telefoneIndicadoHistorico" value="#{fecharMetaDetalhado.indicado.telefoneIndicado}" />
                            </rich:column>
                            <rich:column id="filtroTelefone" filterBy="#{fecharMetaDetalhado.indicado.telefone}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.telefone}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_telefone}" />
                                </f:facet>
                                <h:outputText id="telefoneHistorico" value="#{fecharMetaDetalhado.indicado.telefone}" />
                            </rich:column>
                            <rich:column id="filtroEmail" filterBy="#{fecharMetaDetalhado.indicado.email}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.email}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_email}" />
                                </f:facet>
                                <h:outputText id="emailHistorico" value="#{fecharMetaDetalhado.indicado.email}" />
                            </rich:column>
                            <rich:column id="filtroEvento" filterBy="#{fecharMetaDetalhado.indicado.indicacaoVO.evento.descricao}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.indicacaoVO.evento.descricao}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_evento}" />
                                </f:facet>
                                <h:outputText id="eventoHistorico" value="#{fecharMetaDetalhado.indicado.indicacaoVO.evento.descricao}" />
                            </rich:column>
                            <rich:column id="filtroResponsavel" filterBy="#{fecharMetaDetalhado.indicado.indicacaoVO.colaboradorResponsavel.nome}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.indicado.indicacaoVO.colaboradorResponsavel.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_colaboradorResponsavel}" />
                                </f:facet>
                                <h:outputText id="colabordorResponsavelHistorico" value="#{fecharMetaDetalhado.indicado.indicacaoVO.colaboradorResponsavel.nome}" />
                            </rich:column>
                            <rich:column id="filtroResultado" filterBy="#{fecharMetaDetalhado.historicoContatoVO.resultado}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.historicoContatoVO.resultado}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_resultado}" />
                                </f:facet>
                                <h:outputText id="resultadoHistorico" value="#{fecharMetaDetalhado.historicoContatoVO.resultado}" />
                            </rich:column>

                        </rich:dataTable>

                    </h:panelGrid>
                </rich:tab>
            </rich:tabPanel>
        </h:panelGrid>

        <h:inputHidden id="abaSelecionada" value="#{AberturaMetaControle.fecharMetaIndicadoVO.abaSelecionada}"/>

    </h:form>
</f:view>
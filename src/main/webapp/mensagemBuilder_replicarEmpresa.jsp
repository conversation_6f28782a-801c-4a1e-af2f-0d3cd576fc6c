<%@include file="includes/imports.jsp" %>

<h:panelGroup rendered="#{not MensagemBuilderControle.building and MensagemBuilderControle.replicarEmpresa}"
              layout="block"
              style="margin-top: 20px;">

            <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                         columnClasses="colunaCentralizada">
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_PlanoReplicarEmpresa_tituloForm}"/>
                </f:facet>
                <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaPlano"
                             columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                             width="100%">
                    <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
                    <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
                    <h:outputText value="N�o Replicadas" styleClass="botoes nvoBt"/>
                    <h:outputText value="#{MalaDiretaControle.listaMalaDiretaRedeEmpresaSize}"
                                  style="font-size: 20pt; font-weight: bold;"/>
                    <h:outputText value="#{MalaDiretaControle.listaMalaDiretaRedeEmpresaSincronizado}"
                                  style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
                    <h:outputText
                            value="#{MalaDiretaControle.listaMalaDiretaRedeEmpresaSize - MalaDiretaControle.listaMalaDiretaRedeEmpresaSincronizado}"
                            style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
                </h:panelGrid>
                <h:panelGrid columns="1" id="contadorReplicaPlano2"
                             columnClasses="colunaDireita"
                             width="100%"
                             style="margin-top: 20px; margin-bottom: 1px">
                    <h:panelGroup layout="block">
                        <a4j:commandButton value="Replicar Todas" styleClass="botoes nvoBt"
                                           action="#{MalaDiretaControle.replicarTodas}"
                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                           ajaxSingle="true" immediate="true"/>
                        <a4j:commandButton value="Replicar Selecionadas" styleClass="botoes nvoBt btSec"
                                           action="#{MalaDiretaControle.replicarSelecionadas}"
                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                           ajaxSingle="true" immediate="true"/>
                        <a4j:commandButton value="Limpar Selecionadas" styleClass="botoes nvoBt btSec"
                                           action="#{MalaDiretaControle.limparReplicar}"
                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                           ajaxSingle="true" immediate="true"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                             columnClasses="colunaCentralizada" width="100%">

                    <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                                 styleClass="tabFormSubordinada"
                                 rowClasses="linhaImpar, linhaPar"
                                 columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                                 style="text-align: center;"
                                 value="#{MalaDiretaControle.listaMalaDiretaRedeEmpresa}"
                                 var="malaDiretaRedeEmpresaReplicacao">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value=""/>
                            </f:facet>
                            <h:selectBooleanCheckbox id="check" styleClass="form"
                                                     rendered="#{!malaDiretaRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                     value="#{malaDiretaRedeEmpresaReplicacao.selecionado}">
                                <a4j:support event="onchange" reRender="listaEmpresasReplicar"/>
                            </h:selectBooleanCheckbox>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                            </f:facet>
                            <h:outputText value="#{malaDiretaRedeEmpresaReplicacao.nomeUnidade}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                            </f:facet>
                            <h:outputText value="#{malaDiretaRedeEmpresaReplicacao.chaveDestino}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value=""/>
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandButton id="replicarPlano"
                                                   reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                   ajaxSingle="true" immediate="true"
                                                   rendered="#{!malaDiretaRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                   action="#{MalaDiretaControle.replicarMalaDiretaRedeEmpresaGeral}"
                                                   value="Replicar"/>
                                <h:graphicImage url="./images/check.png"
                                                rendered="#{malaDiretaRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                            </h:panelGroup>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText
                                        value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                            </f:facet>
                            <h:outputText value="#{malaDiretaRedeEmpresaReplicacao.mensagemSituacao}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText
                                        value="V�nculo"/>
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandButton
                                        rendered="#{malaDiretaRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                        reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                        ajaxSingle="true" immediate="true"
                                        action="#{MalaDiretaControle.retirarVinculoReplicacao}"
                                        value="Retirar"/>
                            </h:panelGroup>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>
            </h:panelGrid>
</h:panelGroup>


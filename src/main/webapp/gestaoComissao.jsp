<%-- 
    Document   : gestaoComissao
    Created on : 05/07/2012, 10:49:25
    Author     : Waller
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="css/jquery.treeTable.css" rel="stylesheet" type="text/css">

<%@include file="/includes/imports.jsp" %>
<script type="text/javascript" src="script/jquery.treeTable.js"></script>
<script type="text/javascript" src="./script/cursor.js"></script>

<script type="text/javascript" src="script/gestaoComissao.js"></script>

<head>
    <jsp:include page="include_head.jsp" flush="true"/>
</head>

<style type="text/css">
    .titulo {
        font-size: 12px;
        padding: 4px 4px 4px 4px;
        color:#005bab !important;
    }

    .titulo:hover {
        text-decoration: none;
    }

    .rich-table-cell {
        padding: 1px 1px 1px 1px;
    }

    .borda_azul {
        border-bottom: 2px solid #CEDFFF;
    }

    .Passo .Texto {
        color: #FFFFFF;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        line-height: normal;
        font-weight: bold;
        float: left;
        padding-left: 8px;
        padding-top: 19px;
        text-align: left;
    }

    .Passo .Imagem {
        float: left;
        height: 38px;
        overflow-x: hidden;
        overflow-y: hidden;
    }

    .Passo .Info {
        color: #C0C0C0;
        padding-right: 3px;
        padding-top: 25px;
        text-transform: uppercase;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        line-height: normal;
        text-transform: uppercase;
        font-weight: bold;

    }

    .Passo {
        background: no-repeat 25px 0;
        border-bottom: 1px solid #D3D3D3;
        color: #FFFFFF;
        cursor: pointer;
        font-weight: bold;
        height: 38px;
        text-align: right;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Comissão para Professor"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">

        <html>

        <body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Comissão para Professor" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-configurar-comissao-para-professor-no-sistema-pacto/"
                                                      title="Clique e saiba mais: Comissão para Professor" target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>

                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGroup id="panelConteudo">
                                        <h:inputHidden id="CControle"
                                                       value="#{GestaoComissaoControle.openConsulta}"/>
                                        <h:inputHidden id="CDatas"
                                                       value="#{GestaoComissaoControle.openDatas}"/>
                                        <h:inputHidden id="CFiltros"
                                                       value="#{GestaoComissaoControle.openFiltros}"/>
                                        <div id="CExpandido" class="Passo"
                                             onclick="esconderMostrar('consulta', true, 'CExpandido','CRetraido', 'form:CControle');"
                                             style="cursor: pointer; background: url(./imagens/folder/Passo1.jpg) no-repeat;">
                                            <div class="Imagem">
                                                <img id="ctl00_cph_imgPasso1-exp"
                                                     src="./imagens/folder/PassoExpandir.jpg"
                                                     style="border-width: 0;" title="Ocultar"/>
                                            </div>
                                            <div class="Texto">Tipo Consulta</div>
                                            <div class="Info">
                                                <span id="ctl00_cph_lblPasso1-exp">Ocultar</span>
                                            </div>
                                        </div>
                                        <div id="CRetraido" class="Passo"
                                             onclick="esconderMostrar('consulta', false, 'CRetraido','CExpandido', 'form:CControle');"
                                             style="display: none; cursor: pointer; background: url(./imagens/folder/Passo1.jpg) no-repeat;">
                                            <div class="Imagem">
                                                <img id="ctl00_cph_imgPasso1-ret"
                                                     src="./imagens/folder/PassoOcultar.jpg"
                                                     style="border-width: 0;"
                                                     title="Ocultar" alt="passo1"/>
                                            </div>
                                            <div class="Texto">Tipo Consulta</div>
                                            <div class="Info">
                                                <span id="ctl00_cph_lblPasso1-ret">Expandir</span>
                                            </div>
                                        </div>
                                        <table bgcolor="#F5F5F5" id="consulta" width="100%"
                                               style="display: block;">
                                            <tr>
                                                <td>
                                                    <h:selectOneRadio id="TipoRelatorio"
                                                                      styleClass="tituloDemonstrativo"
                                                                      value="#{GestaoComissaoControle.ehCompetencia}">
                                                        <f:selectItems
                                                                value="#{GestaoComissaoControle.tiposRelatorios}"/>
                                                        <a4j:support event="onclick"
                                                                     action="#{GestaoComissaoControle.alterarFormatoDoCalendario}"
                                                                     reRender="form:panelDatas"/>
                                                    </h:selectOneRadio>
                                                </td>
                                            </tr>
                                        </table>

                                        <div id="CFiltrosExpandido" class="Passo"
                                             onclick="esconderMostrar('rFiltros', true, 'CFiltrosExpandido','CFiltrosRetraido','form:CFiltros');"
                                             style="cursor: pointer; background: url(./imagens/folder/Passo2.jpg) no-repeat;">
                                            <div class="Imagem">
                                                <img id="ctl00_cph_imgPasso2-exp"
                                                     src="./imagens/folder/PassoExpandir.jpg"
                                                     style="border-width: 0;" title="Ocultar"/>
                                            </div>
                                            <div class="Texto">Filtros</div>
                                            <div class="Info">
                                                <span>Ocultar</span>
                                            </div>
                                        </div>
                                        <div id="CFiltrosRetraido"
                                             class="Passo"
                                             onclick="esconderMostrar('rFiltros', false, 'CFiltrosRetraido','CFiltrosExpandido','form:CFiltros');"
                                             style="display: none; cursor: pointer; background: url(./imagens/folder/Passo2.jpg) no-repeat;">
                                            <div class="Imagem">
                                                <img id="ctl00_cph_imgPasso2-ret"
                                                     src="./imagens/folder/PassoOcultar.jpg"
                                                     style="border-width: 0;" title="Ocultar" alt=""/>
                                            </div>
                                            <div class="Texto">Filtros</div>
                                            <div class="Info">
                                                <span>Expandir</span>
                                            </div>
                                        </div>
                                        <table bgcolor="#F5F5F5" id="rFiltros" width="100%"
                                               style="display: block;">
                                            <tr>
                                                <td width="100%">
                                                    <h:panelGrid cellpadding="0" cellspacing="0" columns="1">
                                                        <h:panelGroup>
                                                            <rich:spacer width="10px"/>
                                                            <h:selectBooleanCheckbox
                                                                    styleClass="tituloCampos"
                                                                    id="somenteTurmas"
                                                                    value="#{GestaoComissaoControle.somenteComTurmas}">
                                                            </h:selectBooleanCheckbox>

                                                            <rich:toolTip for="somenteTurmas" followMouse="true"
                                                                          direction="top-right"
                                                                          style="width:300px; height:60px; "
                                                                          showDelay="200">
                                                                <h:outputText styleClass="tituloCampos"
                                                                              escape="false"
                                                                              value="#{msg.msg_somenteTurmas}"/>
                                                            </rich:toolTip>
                                                            <h:outputText styleClass="tituloCampos"
                                                                          value="#{msg_aplic.prt_Gestao_Comissao_somenteTurmas}"/>
                                                        </h:panelGroup>

                                                        <h:panelGroup id="pnlProfessores">
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="tituloCampos"
                                                                          value="#{msg_aplic.prt_Gestao_Comissao_professor}"/>
                                                            <h:selectOneMenu id="cbxProfessor"
                                                                             value="#{GestaoComissaoControle.professorSelecionado}">
                                                                <f:selectItems
                                                                        value="#{GestaoComissaoControle.listaProfessores}"/>
                                                            </h:selectOneMenu>
                                                            <%--
                                                            <rich:toolTip for="cbxProfessor" followMouse="true" direction="top-right"
                                                                              style="width:240px; height:20px; " showDelay="200">
                                                                    <h:outputText styleClass="tituloCampos" escape="false"
                                                                                  value="Este só se aplica ao relatório sintético."/>
                                                                </rich:toolTip>
                                                                --%>
                                                        </h:panelGroup>

                                                    </h:panelGrid>
                                                </td>
                                            </tr>
                                        </table>

                                        <div id="RCExpandido" class="Passo"
                                             onclick="esconderMostrar('rConsulta', true, 'RCExpandido','RCRetraido','form:CDatas');"
                                             style="cursor: pointer; background: url(./imagens/folder/Passo3.jpg) no-repeat;">
                                            <div class="Imagem">
                                                <img id="ctl00_cph_imgPasso3-exp"
                                                     src="./imagens/folder/PassoExpandir.jpg"
                                                     style="border-width: 0;"
                                                     title="Ocultar" alt="passo3-expandir"/>
                                            </div>
                                            <div class="Texto">Intervalo da Consulta</div>
                                            <div class="Info">
                                                <span>Ocultar</span>
                                            </div>
                                        </div>
                                        <div id="RCRetraido"
                                             class="Passo"
                                             onclick="esconderMostrar('rConsulta', false, 'RCRetraido','RCExpandido','form:CDatas');"
                                             style="display: none; cursor: pointer; background: url(./imagens/folder/Passo3.jpg) no-repeat;">
                                            <div class="Imagem">
                                                <img id="ctl00_cph_imgPasso3-ret"
                                                     src="./imagens/folder/PassoOcultar.jpg"
                                                     style="border-width: 0;" title="Ocultar" alt=""/>
                                            </div>
                                            <div class="Texto">Intervalo da Consulta</div>
                                            <div class="Info">
                                                <span>Expandir</span>
                                            </div>
                                        </div>
                                        <table bgcolor="#F5F5F5" id="rConsulta" width="100%"
                                               style="display: block;">
                                            <tr>
                                                <td width="100%">
                                                    <h:panelGrid cellpadding="0" cellspacing="0" columns="1">
                                                        <h:panelGroup>
                                                            <h:panelGroup id="panelDatas" layout="block"
                                                                          style="vertical-align:top; margin-left: 7px; margin-top: 7px">
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  style="vertical-align:middle;"
                                                                                  value="De: "/>
                                                                    <rich:calendar id="dataInicioCompensacao"
                                                                                   rendered="#{!GestaoComissaoControle.ehCompetencia}"
                                                                                   value="#{GestaoComissaoControle.dataInicio}"
                                                                                   inputSize="10"
                                                                                   inputClass="form"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"/>
                                                                    <rich:calendar id="dataInicioCompetencia"
                                                                                   rendered="#{GestaoComissaoControle.ehCompetencia}"
                                                                                   value="#{GestaoComissaoControle.dataInicio}"
                                                                                   inputSize="7"
                                                                                   inputClass="form MMyyyy"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputkeypress="return mascara(this.form, this.id, '99/9999', event);"
                                                                                   datePattern="MM/yyyy"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"/>
                                                                </h:panelGroup>
                                                                <rich:spacer width="20px"/>
                                                                <h:outputText styleClass="tituloCampos"
                                                                              style="vertical-align:middle;"
                                                                              value="Até: "/>

                                                                <h:panelGroup>
                                                                    <rich:calendar id="dataFimCompensacao"
                                                                                   rendered="#{!GestaoComissaoControle.ehCompetencia}"
                                                                                   value="#{GestaoComissaoControle.dataFim}"
                                                                                   inputSize="10"
                                                                                   inputClass="form"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"
                                                                                   mode="client"/>

                                                                    <rich:calendar id="dataFimCompetencia"
                                                                                   rendered="#{GestaoComissaoControle.ehCompetencia}"
                                                                                   value="#{GestaoComissaoControle.dataFim}"
                                                                                   inputSize="7"
                                                                                   inputClass="form MMyyyy"
                                                                                   oninputblur="blurinput(this);"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputkeypress="return mascara(this.form, this.id, '99/9999', event);"
                                                                                   datePattern="MM/yyyy"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false"
                                                                                   mode="client"/>
                                                                </h:panelGroup>
                                                                <a4j:commandLink id="consultarTransacao"
                                                                                 value="Consultar"
                                                                                 action="#{GestaoComissaoControle.consultarInformacoes}"
                                                                                 styleClass="botoes nvoBt"
                                                                                 style="vertical-align: text-bottom"
                                                                                 reRender="panelConteudo"/>
                                                            </h:panelGroup>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                </td>
                                            </tr>
                                        </table>

                                        <h:panelGrid id="mensagem" columns="1" width="100%"
                                                     styleClass="tabMensagens" style="margin:5 5 5 5;">
                                            <h:outputText styleClass="mensagem"
                                                          value="#{GestaoComissaoControle.mensagem}"/>
                                            <h:outputText styleClass="mensagemDetalhada"
                                                          value="#{GestaoComissaoControle.mensagemDetalhada}"/>
                                        </h:panelGrid>

                                        <h:panelGrid columns="1" width="100%">
                                            <rich:tabPanel activeTabClass="true" switchType="ajax">
                                                <rich:tab label="Sintético">
                                                    <h:panelGrid cellpadding="0" cellspacing="0" columns="1"
                                                                 style="vertical-align:middle;">

                                                        <h:panelGroup
                                                                rendered="#{!empty GestaoComissaoControle.listaTree}">

                                                            <fieldset style="height: 100%;vertical-align: top;">
                                                                <legend>Opções</legend>
                                                                <a4j:commandButton reRender="modalFiltros"
                                                                                   value="Filtrar Professores"
                                                                                   oncomplete="Richfaces.showModalPanel('modalFiltros');">
                                                                </a4j:commandButton>
                                                                &nbsp;
                                                                <a4j:commandButton
                                                                        action="#{GestaoComissaoControle.validarPermissaoExportacao()}"
                                                                        rendered="#{!empty GestaoComissaoControle.listaTree}"
                                                                        oncomplete="abrirPopup('relatorioComissao.jsp', 'GestaoComissao', 780, 595);"
                                                                        value="Visualizar Impressão">
                                                                </a4j:commandButton>
                                                            </fieldset>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <h:panelGroup
                                                            rendered="#{!empty GestaoComissaoControle.listaTree}">
                                                        <%@include
                                                                file="include_gestaoComissao_Sintetico.jsp" %>
                                                        <script type="text/javascript">
                                                            atualizarTreeViewComissao();
                                                        </script>
                                                    </h:panelGroup>


                                                </rich:tab>

                                                <rich:tab label="Analítico"
                                                          action="#{GestaoComissaoControle.verificarFiltroProfessorTrazerFiltrado}">
                                                    <h:panelGrid cellpadding="0" cellspacing="0" columns="1"
                                                                 style="vertical-align:middle;">

                                                        <h:panelGroup>
                                                            <fieldset style="height: 100%;vertical-align: top;">
                                                                <legend>Opções</legend>
                                                                <a4j:commandButton id="imprimirPDF"
                                                                                   ajaxSingle="false"
                                                                                   rendered="#{!empty GestaoComissaoControle.listaObjetos}"
                                                                                   action="#{GestaoComissaoControle.imprimirRelatorio}"
                                                                                   value="Imprimir"
                                                                                   reRender="mensagem"
                                                                                   oncomplete="#{GestaoComissaoControle.mensagemNotificar}#{GestaoComissaoControle.msgAlert}"
                                                                                   accesskey="2"
                                                                                   styleClass="botoes"/>
                                                                <rich:spacer width="10px"/>
                                                                <a4j:commandLink target="_blank"
                                                                                 title="Exportar EXCEL"
                                                                                 rendered="#{!empty GestaoComissaoControle.listaObjetos}"
                                                                                 reRender="mensagem"
                                                                                 style="vertical-align:middle;"
                                                                                 action="#{GestaoComissaoControle.imprimirExcel}"
                                                                                 oncomplete="#{GestaoComissaoControle.mensagemNotificar}#{GestaoComissaoControle.msgAlert}">
                                                                    <h:graphicImage url="/imagens/btn_excel.png"
                                                                                    style="border:none;vertical-align:middle;"/>
                                                                </a4j:commandLink>
                                                                <rich:spacer width="10px"/>
                                                                <h:panelGroup styleClass="tituloCampos">
                                                                    <h:outputText style="vertical-align:middle;"
                                                                                  value="Filtrar: "/>
                                                                    <h:inputText style="vertical-align:middle;"
                                                                                 title="Digite algo para filtrar..."
                                                                                 size="33"
                                                                                 id="searchInput"
                                                                                 value="#{GestaoComissaoControle.nomeConsulta}">

                                                                        <a4j:queue name="queueFiltro"
                                                                                   ignoreDupResponses="true"
                                                                                   requestDelay="1000"
                                                                                   timeout="60000"/>

                                                                        <a4j:support status="statusInComponent"
                                                                                     eventsQueue="queueFiltro"
                                                                                     event="onkeyup"
                                                                                     oncomplete="textSelect(document.getElementById('form:searchInput'), document.getElementById('form:searchInput').value.length);"
                                                                                     action="#{GestaoComissaoControle.filtrarPorTexto}"
                                                                                     reRender="panelConteudo"/>

                                                                    </h:inputText>

                                                                    <h:graphicImage id="imageLoading"
                                                                                    style="visibility: hidden;vertical-align: middle;align:center"
                                                                                    url="images/loading.gif"/>
                                                                    <h:outputText style="padding-left:6px;"
                                                                                  rendered="#{GestaoComissaoControle.professorSelecionado != 0}"
                                                                                  value="#{fn:length(GestaoComissaoControle.listaObjetos)} objetos na lista"/>
                                                                    <h:outputText style="padding-left:6px;"
                                                                                  rendered="#{GestaoComissaoControle.professorSelecionado == 0}"
                                                                                  value="#{fn:length(GestaoComissaoControle.listaManipulavel)} objetos na lista"/>
                                                                </h:panelGroup>
                                                            </fieldset>
                                                        </h:panelGroup>

                                                    </h:panelGrid>
                                                    <br/>
                                                    <h:panelGrid columns="1" width="100%" id="painelLista">
                                                        <rich:dataTable
                                                                value="#{GestaoComissaoControle.listaObjetos}"
                                                                rowKeyVar="status"
                                                                rows="#{DataScrollerControle.nrRows}"
                                                                id="tblComissao"
                                                                width="100%"
                                                                var="comissao">

                                                            <%--<%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>--%>

                                                            <rich:column width="15">
                                                                <f:facet name="header">
                                                                    <h:outputText value="S."/>
                                                                </f:facet>
                                                                <h:outputText value="#{status + 1}"/>
                                                            </rich:column>


                                                            <rich:column
                                                                    sortBy="#{comissao.cliente.pessoa.nome}">
                                                                <f:facet name="header">
                                                                    <h:outputText value="Aluno"/>
                                                                </f:facet>
                                                                <h:outputText
                                                                        value="#{comissao.cliente.matricula}"/>

                                                                <a4j:commandLink
                                                                        actionListener="#{ClienteControle.atualizarCliente}"
                                                                        styleClass="titulo"
                                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                                                                        action="#{ClienteControle.acaoAjax}">
                                                                    <f:attribute name="cliente"
                                                                                 value="#{comissao.cliente}"/>
                                                                    <h:outputText
                                                                            value=" - #{comissao.cliente.nome_Apresentar}"/>
                                                                </a4j:commandLink>

                                                                <h:outputText
                                                                        value=" - #{comissao.cliente.situacao}"/>

                                                                <h:outputText
                                                                        value=" / #{comissao.cliente.situacaoContrato}"/>

                                                                <rich:dataTable
                                                                        value="#{comissao.listaDetalheLancamento}"
                                                                        columnClasses="centralizado, centralizado"
                                                                        var="detalhe" style="border:none;"
                                                                        width="100%">

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Vl. Pago"/>
                                                                        </f:facet>
                                                                        <h:outputText
                                                                                value="#{detalhe.totalPagoPlano_Apresentar}"/>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputText
                                                                                    value="Id. Pagamento"/>
                                                                        </f:facet>
                                                                        <h:outputText
                                                                                value="#{detalhe.movPagamento}"/>
                                                                    </rich:column>

                                                                    <rich:column>
                                                                        <f:facet name="header">
                                                                            <h:outputLabel
                                                                                    value="Detalhamento"/>
                                                                        </f:facet>

                                                                        <rich:dataTable width="100%"
                                                                                        columnClasses="centralizado, centralizado, centralizado"
                                                                                        value="#{detalhe.listaContratoModalidade}"
                                                                                        var="cm">
                                                                            <rich:column>
                                                                                <f:facet name="header">
                                                                                    <h:outputLabel
                                                                                            value="Modalidade"/>
                                                                                </f:facet>
                                                                                <h:outputText
                                                                                        style="font-size: 9px;"
                                                                                        value="#{cm.nomeModalidade} "/>
                                                                            </rich:column>
                                                                            <rich:column>
                                                                                <f:facet name="header">
                                                                                    <h:outputLabel
                                                                                            value="Fração Pg. %"/>
                                                                                </f:facet>
                                                                                <h:outputText
                                                                                        style="font-size: 9px;"
                                                                                        value="#{cm.percentagem_Apresentar} "/>
                                                                            </rich:column>
                                                                            <rich:column>
                                                                                <f:facet name="header">
                                                                                    <h:outputLabel
                                                                                            value="Fração Pg. R$"/>
                                                                                </f:facet>
                                                                                <h:outputText
                                                                                        style="font-size: 9px;"
                                                                                        value="#{cm.valorPago_Apresentar}"/>
                                                                            </rich:column>
                                                                            <rich:column>
                                                                                <f:facet name="header">
                                                                                    <h:outputLabel
                                                                                            value="Turmas"/>
                                                                                </f:facet>

                                                                                <rich:dataTable
                                                                                        value="#{cm.matriculasDesteContratoModalidade}"
                                                                                        rendered="#{fn:length(cm.matriculasDesteContratoModalidade) > 0}"
                                                                                        width="100%"
                                                                                        columnClasses="centralizado, centralizado, centralizado
                                    centralizado, centralizado, centralizado, centralizado, centralizado, centralizado,"
                                                                                        var="matTurma"
                                                                                        style="border:none;">
                                                                                    <rich:column width="20"
                                                                                                 style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Ctr."/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{matTurma.contrato.codigo}"/>
                                                                                    </rich:column>
                                                                                    <rich:column width="20"
                                                                                                 style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Dia"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{matTurma.horarioTurma.diaSemana}"/>
                                                                                    </rich:column>
                                                                                    <rich:column width="100"
                                                                                                 style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Horário"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{matTurma.horarioTurma.horaInicial}/"/>
                                                                                        <h:outputText
                                                                                                value="#{matTurma.horarioTurma.horaFinal}"/>
                                                                                    </rich:column>

                                                                                    <rich:column width="100"
                                                                                                 style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Nome Turma"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{matTurma.horarioTurma.identificadorTurma}"/>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Professor"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{matTurma.horarioTurma.professor.pessoa.nome}"/>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Comissão"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{matTurma.porcComissao_Apresentar}"/>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Inic. Matrícula"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                style="#{(matTurma.vigente ? 'color:blue;' : 'color:red;')}"
                                                                                                value="#{matTurma.dataInicio_Apresentar}"/>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Fim Matrícula"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                style="#{(matTurma.vigente ? 'color:blue;' : 'color:red;')}"
                                                                                                value="#{matTurma.dataFim_Apresentar}"/>
                                                                                    </rich:column>

                                                                                    <rich:column
                                                                                            style="border:none;">
                                                                                        <f:facet name="header">
                                                                                            <h:outputText
                                                                                                    value="Vl. Comissão"/>
                                                                                        </f:facet>
                                                                                        <h:outputText
                                                                                                value="#{matTurma.valorComissao_Apresentar}"/>
                                                                                    </rich:column>

                                                                                </rich:dataTable>
                                                                            </rich:column>
                                                                        </rich:dataTable>
                                                                    </rich:column>
                                                                </rich:dataTable>
                                                            </rich:column>
                                                        </rich:dataTable>


                                                    </h:panelGrid>

                                                    <h:panelGrid width="100%"
                                                                 columnClasses="colunaEsquerda, colunaDireita"
                                                                 title="Registros por página" columns="2">
                                                        <h:panelGrid columns="2" width="420px">
                                                            <rich:datascroller reRender="contador"
                                                                               for="tblComissao"
                                                                               binding="#{DataScrollerControle.dataScroller}"
                                                                               id="ds"/>

                                                            <rich:inputNumberSpinner id="numPainel"
                                                                                     inputSize="4"
                                                                                     styleClass="form"
                                                                                     enableManualInput="true"
                                                                                     minValue="1"
                                                                                     value="#{DataScrollerControle.nrRows}">
                                                                <a4j:support event="onchange"
                                                                             reRender="panelConteudo"/>
                                                            </rich:inputNumberSpinner>
                                                        </h:panelGrid>

                                                    </h:panelGrid>
                                                </rich:tab>
                                                <jsp:include page="gestaoComissaoTabAnaliticoProfessor.jsp" flush="true" />
                                            </rich:tabPanel>
                                        </h:panelGrid>
                                        <rich:jQuery id="mskData" selector=".rich-calendar-input:not(.MMyyyy)" timing="onload" query="mask('99/99/9999')" />
                                        <rich:jQuery selector=".rich-calendar-input.MMyyyy" timing="onload" query="mask('99/9999')" />
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>

        <h:inputHidden id="clienteHidden" value="#{GestaoComissaoControle.cliente}" />
        <a4j:commandButton style="visibility: hidden; height: 0px; padding: 0px; border: 0px;" id="btClienteVis"
                           oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                           action="#{GestaoComissaoControle.abrirTelaCliente}">
        </a4j:commandButton>
        </body>
        </html>

    </h:form>
    <rich:modalPanel id="modalFiltros" width="220" height="230" autosized="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Filtrar Professores"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="modalFiltros" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form>
            <h:dataTable var="colaborador" value="#{GestaoComissaoControle.filtros}">
                <h:column>
                    <h:selectBooleanCheckbox value="#{colaborador.colaboradorEscolhido}">
                    </h:selectBooleanCheckbox>
                    <h:outputText styleClass="tituloBold" value="#{colaborador.pessoa.nome}"/>
                </h:column>
            </h:dataTable>
            <div style="text-align: center">
                <a4j:commandButton value="Atualizar"
                                   action="#{GestaoComissaoControle.filtrarPorColaborador}"
                                   reRender="form:panelConteudo"
                                   oncomplete="Richfaces.hideModalPanel('modalFiltros');atualizarTreeViewComissao();"/>
            </div>
        </h:form>
    </rich:modalPanel>
    <%@include file="includes/transacoes/include_paramspanel_transacao.jsp" %>
    <%@include file="include_modal_trocarCartaoRecorrencia.jsp" %>
    <%@include file="includes/parcelas/include_modal_alterarVencimentoParcelas.jsp" %>
</f:view>


<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Balanço"/>
    </title>
    <c:set var="titulo" scope="session" value="Balanço"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Controle_de_Estoque:Balanço"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <h:form id="form">
            <h:commandLink action="#{BalancoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel width="100%" ontabchange="fireElementFromAnyParent('form:btnAtualizaTempo')" >
                    <rich:tab id="dadosBalanco" label="Dados Balanco">
                        <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText rendered="#{BalancoControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="Empresa" />
                            <h:panelGroup rendered="#{BalancoControle.usuarioLogado.administrador}">
                                <h:selectOneMenu  id="Balanco_empresa" onblur="blurinput(this);"
                                                  disabled="#{BalancoControle.balancoVO.cancelado}"
                                                  onfocus="focusinput(this);"
                                                  styleClass="form"
                                                  value="#{BalancoControle.balancoVO.empresa.codigo}" >
                                    <f:selectItems  value="#{BalancoControle.listaSelectItemEmpresa}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_Balanco_empresa" action="#{BalancoControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:Balanco_empresa"/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" style="padding-right:5px;padding-left:5px" value="Descrição"></h:outputText>
                            <h:inputText  id="descricaoBalanco"
                                          size="50"
                                          maxlength="50"
                                          title="Descrição do Produto."
                                          onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          styleClass="form"
                                          value="#{BalancoControle.balancoVO.descricao}" />

                            <h:outputText styleClass="tituloCampos" style="padding-right:5px;padding-left:5px" value="Data Balanço"></h:outputText>
                            <h:panelGroup>
                                <rich:calendar  readonly="true" value="#{BalancoControle.balancoVO.dataCadastro}"
                                                showInput="true" datePattern="dd/MM/yyyy" zindex="2" showWeeksBar="false">
                                </rich:calendar>
                                <h:outputText id="inpCancelado" rendered="#{BalancoControle.balancoVO.cancelado}" value="Cancelado"  />
                                <h:selectBooleanCheckbox id="chkCancelado" rendered="#{BalancoControle.balancoVO.cancelado}" disabled="true" styleClass="campos" value="#{BalancoControle.balancoVO.cancelado}"/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="Observações" />
                            <h:inputTextarea readonly="#{!BalancoControle.balancoVO.permiteAlterarItens}"  style="height: 63px; resize: none;" id="observacaoRichEditor"
                                             value="#{BalancoControle.balancoVO.observacoes}" rows="3" cols="60"/>
                        </h:panelGrid>

                    </rich:tab>
                    <rich:tab id="balancoItens" label="Produtos do Balanço" >
                        <h:panelGrid id="gridItens" columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">
                            <f:facet name="header">
                                <h:outputText styleClass="tituloCampos" value="Adicionar Produtos do Balanço"/>
                            </f:facet>

                            <h:outputText   value="Produto" />
                            <h:panelGroup>
                                <h:inputText  id="nomeProdutoSelecionado"
                                              size="50"
                                              maxlength="50"
                                              title="Para pesquisar, informe o código do produto ou o código de barras ou nome do produto."
                                              onblur="blurinput(this);"
                                              disabled="#{!BalancoControle.balancoVO.permiteAlterarItens}"
                                              onfocus="focusinput(this);"
                                              styleClass="form"
                                              value="#{BalancoControle.produtoSelecionado}" />

                                <rich:suggestionbox   height="200" width="400"
                                                      for="nomeProdutoSelecionado"
                                                      status="statusInComponent"
                                                      immediate="true"
                                                      suggestionAction="#{BalancoControle.executarAutocompletePesqProduto}"
                                                      minChars="1"
                                                      reRender="panelMensagemErro"
                                                      rowClasses="linhaImpar, linhaPar"
                                                      var="result"  id="suggestionResponsavel">
                                    <a4j:support event="onselect"
                                                 reRender="form, panelMensagemErro, nomeProdutoSelecionado"
                                                 oncomplete="#{BalancoControle.msgAlert}"
                                                 action="#{BalancoControle.selecionarProduto}">
                                    </a4j:support>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Nome"  styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText value="#{result.descricao}" />
                                    </h:column>
                                    <h:column >
                                        <f:facet name="header">
                                            <h:outputText value="Categoria" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText  value="#{result.categoriaProduto.descricao}" />
                                    </h:column>
                                </rich:suggestionbox>
                            </h:panelGroup>

                            <h:outputText value="Quantidade" />
                            <h:inputText  id="quantidade"
                                          disabled="#{!BalancoControle.balancoVO.permiteAlterarItens}"
                                          size="50"
                                          maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{BalancoControle.balancoItensVO.qtdeBalanco}" />

                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" styleClass="centralizado">
                            <a4j:commandButton rendered="#{BalancoControle.balancoVO.permiteAlterarItens}"  reRender="form"
                                               focus="form:nomeProdutoSelecionado"
                                               onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               oncomplete="#{BalancoControle.msgAlert}"
                                               action="#{BalancoControle.adicionarItensBalanco}"
                                               value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="6" styleClass="botoes"/>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <rich:dataTable id="tbBalancoItens" width="100%" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="esquerda, centralizado, centralizado, centralizado, centralizado"
                                         value="#{BalancoControle.balancoVO.itensList}" var="balancoItem" style="border: none">

                                <rich:column sortBy="#{balancoItem.produto.descricao}"  style="border: none;font-size: 12px!important;">
                                    <f:facet name="header" >
<%--                                        <a4j:commandLink value="Produto"  action="#{RelatorioBalancoControle.sortByColunm}" />--%>
                                        <h:outputText  value="Produto" />
                                    </f:facet>
                                    <h:outputText  value="#{balancoItem.produto.descricao}" />
                                </rich:column>

                                <rich:column sortBy="#{balancoItem.qtdeBalanco}" style="border: none;font-size: 12px!important;">
                                    <f:facet name="header">
                                        <h:outputText  value="Quantidade Balanço" />
                                    </f:facet>
                                    <h:outputText   value="#{balancoItem.qtdeBalanco}" />
                                </rich:column>

                                <rich:column sortBy="#{balancoItem.qtdeEstoqueAnterior}" style="border: none;font-size: 12px!important;" rendered="#{!BalancoControle.balancoVO.permiteAlterarItens}">
                                    <f:facet name="header">
                                        <h:outputText  value="Estoque Anterior" />
                                    </f:facet>
                                    <h:outputText   value="#{balancoItem.qtdeEstoqueAnterior}" />
                                </rich:column>
                                <rich:column sortBy="#{balancoItem.diferencaBalanco}"  style="border: none;font-size: 12px!important;" rendered="#{!BalancoControle.balancoVO.permiteAlterarItens}">
                                    <f:facet name="header">
                                        <h:outputText  value="Diferença Balanço" />
                                    </f:facet>
                                    <h:outputText   value="#{balancoItem.diferencaBalanco}" />
                                </rich:column>
                                <rich:column style="border: none;font-size: 12px!important;">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>

                                    <h:panelGroup rendered="#{BalancoControle.balancoVO.permiteAlterarItens}" >
                                        <h:outputText value="    "/>
                                        <a4j:commandButton reRender="tbBalancoItens,gridItens, panelMensagemErro"
                                                           id="removerItem" immediate="true" action="#{BalancoControle.removerItensBalanco}"
                                                           value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"
                                                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                                        <a4j:commandButton reRender="tbBalancoItens,gridItens, panelMensagemErro"
                                                           id="linkEditarBalancoItem" action="#{BalancoControle.editarBalancoItem}"
                                                           value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png"
                                                           alt="#{msg.msg_editar_dados}" styleClass="botoes"
                                                            onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                                    </h:panelGroup>
                                </rich:column>

                            </rich:dataTable>
                        </h:panelGrid>

                    </rich:tab>
                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{BalancoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{BalancoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgBalanco" styleClass="mensagem"  value="#{BalancoControle.mensagem}"/>
                            <h:outputText id="msgBalancoDet" styleClass="mensagemDetalhada" value="#{BalancoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{BalancoControle.novo}"
                                             value="#{msg_bt.btn_novo}"  alt="#{msg.msg_novo_dados}"
                                             accesskey="1" styleClass="botoes nvoBt btSec"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"/>

                            <a4j:commandButton id="salvar" reRender="form" rendered="#{BalancoControle.balancoVO.permiteAlterarItens}"
                                             action="#{BalancoControle.gravar}" value="#{msg_bt.btn_gravar}"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"/>


                            <a4j:commandButton id="clonar" rendered="#{BalancoControle.balancoVO.codigo > 0}"
                                               onclick="fireElementFromAnyParent('form:btnAtualizaTempo');if (!confirm('Confirma criar cópia do balanço?')){return false;}"
                                               oncomplete=""
                                               action="#{BalancoControle.clonar}"
                                               reRender="form"
                                               value="#{msg_bt.btn_Clonar}"
                                               alt="#{msg.msg_clonar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                            <a4j:commandButton onclick="fireElementFromAnyParent('form:btnAtualizaTempo');if (!confirm('Confirma cancelamento do balanco?')){return false;}"
                                               id="btnCancelar"
                                               action="#{BalancoControle.cancelarBalanco}"
                                               reRender="form"
                                               rendered="#{BalancoControle.balancoVO.permiteCancelarBalanco}"
                                               value="#{msg_bt.btn_cancelar}"
                                               alt="Cancelar Balanço" accesskey="4" styleClass="botoes nvoBt btSec btPerigo"/>

                            <a4j:commandButton id="imprimirBalanco"
                                               rendered="#{BalancoControle.balancoVO.codigo != 0}"
                                               value="Imprimir Balanço"
                                               styleClass="botoes nvoBt btSec"
                                               action="#{RelatorioBalancoControle.imprimirRelatorioBalancoPDF}"
                                               oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo');abrirPopupPDFImpressao('relatorio/#{RelatorioBalancoControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                            </a4j:commandButton>
                            <h:commandButton id="consultar" immediate="true"
                                             action="#{BalancoControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                             accesskey="4" styleClass="botoes nvoBt btSec"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink id="visualizarLog" action="#{BalancoControle.realizarConsultaLogObjetoSelecionadoBalanco}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>

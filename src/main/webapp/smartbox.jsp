<%-- 
    Document   : smartbox
    Created on : 02/01/2012, 16:00:02
    Author     : Waller
--%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/imports.jsp" %>
<%@taglib prefix="jsfChart"
          uri="http://sourceforge.net/projects/jsf-comp"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<link href="css_pacto.css" rel="stylesheet" type="text/css">
<link href="css/otimize.css" rel="stylesheet" type="text/css">
<link href="css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="hoverform.js"></script>
<jsp:include page="include_head.jsp" flush="true" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <c:if test="${LoginControle.apresentarLinkSmartbox}">
        <h:form id="form" style="padding: 0 0 0 0;margin: 0 0 0 0">
            <html>
                <head>
                    <script type="text/javascript" language="javascript" src="script/smartbox/smartbox.js"></script>
                </head>
                <body>
                    <table width="100%" border="0" cellpadding="0"
                           cellspacing="0">

                        <tr>
                            <td height="77" align="left" valign="top" class="bgtop">
                                <jsp:include page="include_top.jsp" flush="true" />
                            </td>
                        </tr>

                        <tr>
                            <td height="48" align="left" valign="top" class="bgmenu">
                                <jsp:include page="include_menu.jsp" flush="true" />
                            </td>
                        </tr>

                        <tr>
                            <td id="lateralRetrair" align="left" valign="top">
                                <c:if test="${LoginControle.permissaoAcessoMenuVO.selecionarMultiplosColaboradores || LoginControle.usuarioLogado.administrador}">
                                    <div class="menulateral_retraido"
                                         title="Esconder op��es"
                                         id="expansorLateral"
                                         onclick="expandir(['expansivel'], this.id);">
                                    </div>
                                </c:if>
                                <h:panelGrid cellpadding="0" cellspacing="0" columns="2" style="position:absolute;right:0;top:117px;margin-right:20px" id="totalSmartBox" columnClasses="colunaDireita">
                                    <h:outputText styleClass="text" value="Contratos Ativos"/>
                                    <h:outputText styleClass="titulo" style="font-size:18px;" value="#{SmartBoxControle.totalContratosAtivos}"/>
                                </h:panelGrid>


                                <table width="100%" align="center" border="0"
                                       cellpadding="0" cellspacing="0">
                                    <tr>
                                        <c:if test="${LoginControle.permissaoAcessoMenuVO.selecionarMultiplosColaboradores || LoginControle.usuarioLogado.administrador}">
                                            <td id="expansivel" width="306" align="left" valign="top" class="boxmiddle" style="padding: 15px 15px 15px 15px;">
                                                <div class="cantos_t">
                                                    <div class="cantos_l">
                                                        <div class="cantos_r">
                                                            <div class="cantos_b">
                                                                <div class="cantos_tl">
                                                                    <div class="cantos_tr">
                                                                        <div class="cantos_bl">
                                                                            <div class="cantos_br">
                                                                                <div class="cantos">

                                                                                    <h:panelGrid columnClasses="tituloCamposTop" columns="1"
                                                                                                 width="100%"
                                                                                                 cellpadding="0" cellspacing="0">


                                                                                        <h:panelGrid width="250" columnClasses="tituloSmart"
                                                                                                     style="padding: 6px 0px 0px 6px;font-weight:bold;margin:2 2 2 2;"
                                                                                                     columns="1">
                                                                                            <h:panelGroup  layout="block" style="width:100%;">
                                                                                                <a4j:commandButton style="vertical-align:middle;" status="statusInComponent"
                                                                                                                   id="btnExibirOpcoes"
                                                                                                                   image="images/chevron-small.png"
                                                                                                                   onclick="mostraEsconde('form:esconderOpcoes', this.id);"/>
                                                                                                <h:outputText style="font-weight:bold; margin-right: 5px;" value="Op��es"/>

                                                                                                <h:panelGroup id="totalSelecionado" rendered="#{TreeViewControle.exibirListaNodesMarcados &&
                                                                                                                                                LoginControle.permissaoAcessoMenuVO.selecionarMultiplosColaboradores}"
                                                                                                              style="vertical-align:middle;">
                                                                                                    <h:outputText value="#{fn:length(TreeViewControle.nodesMarcados)} Selecionado(s)"/>
                                                                                                    <rich:spacer width="5"/>
                                                                                                    <a4j:commandButton style="vertical-align:middle;"
                                                                                                                       image="images/database_refresh.png" title="Atualizar"
                                                                                                                       value="Atualizar"
                                                                                                                       reRender="outputCaixas,mensagem,totalSmartBox"
                                                                                                                       action="#{SmartBoxControle.carregarDadosCaixas}"/>
                                                                                                    <h:panelGroup layout="block">
                                                                                                        <h:graphicImage id="imageLoading" style="visibility: hidden;vertical-align: middle;align:center" url="images/loading.gif"/>
                                                                                                    </h:panelGroup>
                                                                                                </h:panelGroup>
                                                                                            </h:panelGroup>
                                                                                            <h:panelGroup id="esconderOpcoes" layout="block" style="width:100%;">
                                                                                                <a4j:outputPanel rendered="#{SmartBoxControle.usuarioLogado.administrador}">
                                                                                                    <%@include file="includes/empresa/include_combo_empresa.jsp"%>
                                                                                                </a4j:outputPanel>
                                                                                                <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.selecionarMultiplosColaboradores}">

                                                                                                    <%--ajax function para ser referenciada pelo TreeViewControle atrav�s de um gatilho gen�rico--%>
                                                                                                    <a4j:jsFunction name="gatilhoAtualizarArvoreEmpresaSelecionada" action="#{SmartBoxControle.montarArvoreColaboradores}"
                                                                                                                    status="statusInComponent"
                                                                                                                    reRender="esconderOpcoes"/>

                                                                                                    <%@include file="includes/crm/include_arvore_grupo_colaboradores.jsp" %>
                                                                                                </h:panelGroup>
                                                                                            </h:panelGroup>
                                                                                        </h:panelGrid>
                                                                                    </h:panelGrid>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </c:if>
                                        <td align="left" valign="top" style="padding-top:6px;padding-left: 0px">

                                            <a4j:commandButton style="padding-left:6px;vertical-align:middle;"
                                                               image="images/database_refresh.png" title="Atualizar"
                                                               value="Atualizar"
                                                               rendered="#{!LoginControle.permissaoAcessoMenuVO.selecionarMultiplosColaboradores}"
                                                               reRender="outputCaixas,mensagem,totalSmartBox"
                                                               action="#{SmartBoxControle.carregarDadosCaixas}"/>
                                            <jsp:include
                                                page="includes/smartbox/include_smartbox_conteudo.jsp" flush="true" />

                                            <h:panelGrid style="border:none;" id="mensagem" width="100%">
                                                <h:outputText styleClass="mensagem"
                                                              value="#{SmartBoxControle.mensagem}" />
                                                <h:outputText styleClass="mensagemDetalhada"
                                                              value="#{SmartBoxControle.mensagemDetalhada}" />
                                            </h:panelGrid>

                                        </td>
                                    </tr>
                                </table>

                            </td>
                        </tr>

                    </table>
                    <%--
    <div id="site-bottom-bar" class="fixed-position">
    <div id="site-bottom-bar-frame">
    <div id="site-bottom-bar-content">



                            <%@include file="include_box_descricao_inline.jsp" %>

                        </div>
                    </div>
                </div>
                    --%>
                </body>
            </html>


        </h:form>


        <jsp:include page="includes/smartbox/include_listaClientes.jsp" flush="true" />
        <jsp:include page="includes/smartbox/include_comparativo.jsp" flush="true" />
    </c:if>
</f:view>

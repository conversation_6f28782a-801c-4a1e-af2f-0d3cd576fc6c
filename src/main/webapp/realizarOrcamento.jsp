<%--
  Created by IntelliJ IDEA.
  User: anderson
  Date: 07/06/19
  Time: 13:50
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script language="javascript" src="script/telaCliente1.3.js" type="text/javascript"></script>
    <script language="javascript" src="script/required.js" type="text/javascript"></script>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <style>
        .linhainformacoesaluno td{
            background-color: transparent !important;
            padding-left: 3px;
        }
    </style>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" src="script/validador-cpf.js"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<jsp:include page="include_head.jsp" flush="true"/>


<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:keepAlive beanName="Criar - Orçamento"/>
    <title>
        <h:outputText value="Clientes"/>
    </title>
    <h:form id="form" onkeypress="return event.keyCode != 13; " style="overflow: hidden !important;">
        <html>
        <body>
        <h:panelGroup layout="block" styleClass="bgtop topoZW">
            <jsp:include page="include_topo_novo.jsp" flush="true"/>
            <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            <link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>
            <link href="${root}/css/required.css" rel="stylesheet" type="text/css"/>
        </h:panelGroup>


        <table width="100%" border="0" cellpadding="0" cellspacing="0">


            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="180" align="left" valign="top" class="bglateraltop"
                                style="padding: 0 !important;">
                                <jsp:include page="include_box_menulateral.jsp" flush="true"/>

                            </td>
                            <td align="center" valign="top">
                                <jsp:include page="/includes/cliente/include_camposOrcamento.jsp" flush="true"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <jsp:include page="include_rodape_flat.jsp" flush="true" />
                </td>
            </tr>
        </table>


        </body>
        </html>

        <script>
            carregarTooltipster();
        </script>
    </h:form>

    <%@include file="/include_load_configs.jsp" %>
    <%@include file="includes/include_envio_orcamento_email.jsp"%>
</f:view>


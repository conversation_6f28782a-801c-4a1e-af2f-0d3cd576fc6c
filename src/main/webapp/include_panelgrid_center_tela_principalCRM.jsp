<%@page pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
<link href="./css/crm.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<style type="text/css">
    .lf-crmCenter .linhaPar, .lf-crmCenter .linhaImpar {
        cursor: pointer;
    }
</style>

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<h:panelGroup layout="block" id="panelGridCenter" style="height: 100%; position: relative">

    <h:panelGrid id="superiorFiltroTotal" columns="2"
                 style="width: 100%; padding-bottom: 4px; border-bottom: 1px #C0C0C0 solid;">

        <h:panelGroup layout="block" id="botoesImpressaoLista" style="float: left;">
            <%--BOTÃO EXCEL--%>
            <a4j:commandLink
                id="exportarExcel"
                styleClass="exportadores margin-h-10 linkPadrao"
                style="padding-left: 10px;"
                actionListener="#{ExportadorListaControle.exportar}"
                rendered="#{not empty MetaCRMControle.tipoMetaSelecionada.listaMetaDetalhadaFiltradaSemPaginacao}"
                oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                <f:attribute name="lista" value="#{MetaCRMControle.tipoMetaSelecionada.listaMetaDetalhadaFiltradaSemPaginacao}"/>
                <f:attribute name="tipo" value="xls"/>
                <f:attribute name="atributos" value="#{MetaCRMControle.atributos}"/>
                <f:attribute name="prefixo" value="Meta_#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum}"/>
                <f:attribute name="titulo" value="Meta #{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.descricao}"/>
                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
            </a4j:commandLink>

            <%--BOTÃO PDF--%>
            <a4j:commandLink
                id="exportarPDF"
                styleClass="exportadores margin-h-10 linkPadrao"
                style="margin-left: 10px;"
                actionListener="#{ExportadorListaControle.exportar}"
                rendered="#{not empty MetaCRMControle.tipoMetaSelecionada.listaMetaDetalhadaFiltradaSemPaginacao}"
                oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                <f:attribute name="lista" value="#{MetaCRMControle.tipoMetaSelecionada.listaMetaDetalhadaFiltradaSemPaginacao}"/>
                <f:attribute name="tipo" value="pdf"/>
                <f:attribute name="atributos" value="#{MetaCRMControle.atributos}"/>
                <f:attribute name="prefixo" value="Meta_#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum}"/>
                <f:attribute name="titulo" value="Meta #{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.descricao}"/>
                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGrid columns="2" id="btnMostrarCampoBusca" rendered="#{!MetaCRMControle.apresentarBusca}"
                     styleClass="tituloCampos" style="width: 100%">

            <h:panelGroup layout="block" style="padding-right: 11px;padding-bottom: 1%">

                <h:outputText id="totalClienteFiltro1"
                              rendered="#{!MetaCRMControle.metaPassivo}"
                              style="color: #333333;  vertical-align:middle; padding-right: 10%;"
                              value="Total #{MetaCRMControle.totalListaClientes}"/>

                <h:outputText id="totalPassivoFiltro1"
                              rendered="#{MetaCRMControle.metaPassivo}"
                              style="color: #333333;  vertical-align:middle; padding-right: 10%;"
                              value="Total #{MetaCRMControle.sizeListaPassivoFiltrada}"/>


                <a4j:commandLink id="mostrarCampoBusca"
                                 styleClass="pure-button pure-button-small"
                                 style="font-size: 11px; color: #333;"
                                 reRender="superiorFiltroTotal"
                                 action="#{MetaCRMControle.mostrarCampoBusca}"
                                 oncomplete="adicionarPlaceHolderCRM();">
                    <i class="fa-icon-search"></i> &nbsp Pesquisar aluno
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>


        <h:panelGrid id="buscarAluno" columns="1" rendered="#{MetaCRMControle.apresentarBusca}"
                     styleClass="tituloCampos" style="width: 100%; padding-bottom: 1%">

            <h:panelGroup layout="block" id="filtro" style="padding-right: 11px;">

                <h:outputText id="totalClienteFiltro"
                              rendered="#{!MetaCRMControle.metaPassivo}"
                              style="color: #333333;  vertical-align:middle; padding-right: 2%;"
                              value="Total #{MetaCRMControle.totalListaClientes}"/>

                <h:outputText id="totalPassivoFiltro"
                              rendered="#{MetaCRMControle.metaPassivo}"
                              style="color: #333333;  vertical-align:middle; padding-right: 2%;"
                              value="Total #{MetaCRMControle.sizeListaPassivoFiltrada}"/>

                <h:graphicImage id="imageLoading" style="visibility: hidden; vertical-align: middle;"
                                url="images/loading.gif"/>

                <%-- FILTRO DE CLIENTES FECHARMETADETALHADO--%>
                <h:inputText rendered="#{!MetaCRMControle.metaPassivo || MetaCRMControle.metaIndicacaoSemContato}"
                             style="width: 50%; background: #F6F6F6; vertical-align:middle;"
                             title="Digite o nome para buscar..."
                             id="searchInput" value="#{MetaCRMControle.filtroClientes}"
                             onkeypress="bloquearEnter()">

                    <a4j:queue name="queueFiltro" ignoreDupResponses="true" requestDelay="500" timeout="60000"/>

                    <a4j:support status="statusInComponent" eventsQueue="queueFiltro" event="onkeyup"
                                 oncomplete="textSelect(document.getElementById('form:searchInput'), document.getElementById('form:searchInput').value.length);"
                                 action="#{MetaCRMControle.listaClientesFiltrada}"
                                 reRender="listaClientes, totalClienteFiltro"/>
                </h:inputText>

                <%-- FILTRO DE PASSIVO--%>
                <h:inputText rendered="#{MetaCRMControle.metaPassivo}"
                             style="width: 50%; background: #F6F6F6; vertical-align:middle;"
                             title="Digite o nome para buscar..."
                             id="searchInputPassivo" value="#{MetaCRMControle.filtroClientes}"
                             onkeypress="bloquearEnter()">

                    <a4j:queue name="queueFiltro" ignoreDupResponses="true" requestDelay="500" timeout="60000"/>

                    <a4j:support status="statusInComponent" eventsQueue="queueFiltro" event="onkeyup"
                                 oncomplete="textSelect(document.getElementById('form:searchInputPassivo'), document.getElementById('form:searchInputPassivo').value.length);"
                                 action="#{MetaCRMControle.listaPassivoFiltrada}"
                                 reRender="listaPassivos, totalPassivoFiltro"/>
                </h:inputText>
            </h:panelGroup>


            <%--FILTRAR EM TODAS AS METAS--%>
            <h:panelGroup id="filtrarEmTodasFases" style="float: right;"
                          styleClass="tituloCampos"
                          layout="block">

                <a4j:commandLink id="voltarBusca"
                                 style="font-size: 13px; color: #333; text-decoration: none; padding-right: 3%;"
                                 title="Fechar busca de aluno"
                                 reRender="superiorFiltroTotal"
                                 action="#{MetaCRMControle.mostrarCampoBusca}">
                    <i class="fa-icon-reply"></i>
                </a4j:commandLink>

                <h:selectBooleanCheckbox
                    id="buscarTodasFases"
                    title="Buscar pelo nome do aluno em todas as fases no período da busca."
                    value="#{MetaCRMControle.buscarEmTodasFases}">
                    <a4j:support event="onclick" action="#{MetaCRMControle.buscarAoMarcarTodasFases}"
                                 reRender="filtrarEmTodasFases, listaClientes, superiorFiltroTotal"/>
                </h:selectBooleanCheckbox>

                <h:outputText style="color: #333333;  vertical-align:middle; margin-right: 15px;"
                              title="Buscar pelo nome do aluno em todas as fases no período da busca."
                              value="Buscar em todas as fases"/>

            </h:panelGroup>
        </h:panelGrid>
    </h:panelGrid>


    <%--BOTÕES ENVIAR EMAIL E SMS--%>
    <h:panelGrid id="botoesInferiores" columns="2"
                 style="width: 100%; border-bottom: 1px #C0C0C0 solid;"
                 rendered="#{(MetaCRMControle.metaCorreta && ((MetaCRMControle.dataInicio == MetaCRMControle.dataFim) || MetaCRMControle.dataFim == null) && !MetaCRMControle.metaIndicacaoSemContato)}">

        <%--MARCAR TODOS--%>
        <h:panelGroup layout="block" style="padding-left:10px">
            <h:selectBooleanCheckbox id="selectTodos"
                                     title="Selecionar todos"
                                     value="#{MetaCRMControle.marcarTodosMetaDetalhado}">
                <a4j:support event="onclick" action="#{MetaCRMControle.selecionarTodosMetaDetalhado}"
                             reRender="listaClientes, listaPassivos"/>
            </h:selectBooleanCheckbox>

            <%--<h:outputText value="Selecionar Todos" style="vertical-align: middle" styleClass="tituloCampos"/>--%>
        </h:panelGroup>

        <h:panelGroup layout="block" id="botoesDireita"
                      style="text-align: right; padding-top: 1%;padding-bottom: 1%; padding-right: 11px">
            <a4j:commandLink id="enviarEmailTodos"
                             value="Enviar Email"
                             style="font-size: 13px; margin-right: 5%;padding-left: 5px;padding-right: 5px;"
                             styleClass="pure-button pure-button-small"
                             action="#{MetaCRMControle.executarAberturaEmailColetivo}"
                             oncomplete="#{MetaCRMControle.onComplete}"
                             reRender="mdlMensagemGenerica, colunaCRMDireita, listaClientes, listaPassivos"/>

            <a4j:commandLink id="enviarSMSTodos"
                             value="Enviar SMS"
                             style="font-size: 13px; margin: 0;padding-left: 5px;padding-right: 5px;"
                             styleClass="pure-button pure-button-small"
                             action="#{MetaCRMControle.executarAberturaSMSColetivo}"
                             oncomplete="#{MetaCRMControle.onComplete}"
                             reRender="mdlMensagemGenerica, colunaCRMDireita, listaClientes, listaPassivos"/>
        </h:panelGroup>
    </h:panelGrid>


    <%--ADICIONAR NOVA INDICAÇÃO SOMENTE PARA META DE INDICAÇÃO--%>
    <h:panelGroup rendered="#{MetaCRMControle.metaIndicacao}"
                  layout="block"
                  style="padding-bottom: 3px; padding-top: 3px; text-align: center">
        <a4j:commandLink id="incluirIndicaoMeta" styleClass="pure-button pure-button-primary"
                         value="Adicionar nova indicação"
                         action="#{MetaCRMControle.adicionarNovaIndicacaoMetaIndicacao}"
                         reRender="panelGridCenter, panelGridRight"
                         oncomplete="adicionarPlaceHolderCRM();"/>
    </h:panelGroup>


    <h:panelGroup id="divScroll" styleClass="divScroll" layout="block"
                  style="width: 100%; height: 85%; position: absolute; overflow-y: auto;">

        <rich:dataGrid rendered="#{!MetaCRMControle.metaPassivo}"
                       id="listaClientes"
                       value="#{MetaCRMControle.tipoMetaSelecionada.listaMetaDetalhadaFiltrada}"
                       styleClass="semBorda tblClientesNaFase"
                       columnClasses="semBorda"
                       rowClasses="linhaImpar"
                       width="100%"
                       cellpadding="0"
                       cellspacing="0" var="metaDetalhada">
            <%--onRowClick="document.getElementById(jQuery(this).find('.lf-crmCenter-botaoCliente').attr('id')).click();"--%>

            <h:panelGrid id="panelListaClientes" columns="3"
                         columnClasses="#{metaDetalhada.situacaoAtualMetaEnum.styleClassPanel}  aluno#{metaDetalhada.codigo}"
                         style="#{metaDetalhada.situacaoAtualMetaEnum.stylePanel}" width="100%">

                <h:panelGroup id="colunaCheckBox" layout="block">
                    <h:selectBooleanCheckbox
                        rendered="#{(MetaCRMControle.metaCorreta && ((MetaCRMControle.dataInicio == MetaCRMControle.dataFim) || MetaCRMControle.dataFim == null))}"
                        value="#{metaDetalhada.enviarEmailSMS}"
                        id="marcarCliente"/>
                </h:panelGroup>

                <h:panelGroup id="panelFotoCliente" layout="block">
                    <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                        <a4j:mediaOutput element="img" id="panelfotoBanco"
                                         style="left:0px;width:40px;height:40px;border-radius: 100%"
                                         cacheable="false" session="true"
                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                         createContent="#{MetaCRMControle.paintFotoCliente}"
                                         value="#{ImagemData}" mimeType="image/jpeg">
                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                            <f:param name="largura" value="40"/>
                            <f:param name="altura" value="40"/>
                            <f:param name="pessoa" value="#{metaDetalhada.cliente.pessoa.codigo}"/>
                        </a4j:mediaOutput>
                        <h:graphicImage id="panelfotoNuvem" rendered="#{SuperControle.fotosNaNuvem}"
                                        style="left:0px;width:40px;height:40px;border-radius: 100%"
                                        url="#{metaDetalhada.cliente.pessoa.urlFoto}">
                        </h:graphicImage>
                    </h:panelGrid>

                    <h:panelGrid id="situacaoCliente" columns="2" columnClasses="colunaCentralizada, colunaCentralizada"
                                 width="100%">
                        <h:graphicImage id="sitAtivo" value="./images/botaoAtivo.png" title="Ativo"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.ativo}"/>
                        <h:graphicImage id="sitInativo" value="./images/botaoInativo.png" title="Inativo"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.inativo}"/>
                        <h:graphicImage id="sitVisitante" value="./images/botaoVisitante.png" title="Visitante"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.visitante}"/>
                        <h:graphicImage id="sitTrancado" value="./images/botaoTrancamento.png" title="Trancado"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.trancado}"/>
                        <h:graphicImage id="sitNormal" value="./images/botaoNormal.png" title="Normal"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.ativoNormal}"/>
                        <h:graphicImage id="sitTrancVencido" value="./images/botaoTrancadoVencido.png"
                                        title="Trancado Vencido"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.trancadoVencido}"/>
                        <h:graphicImage id="sitFreePass" value="./images/botaoFreePass.png" title="FreePass"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.visitanteFreePass}"/>
                        <h:graphicImage id="sitAulaAvulsa" value="./images/botaoAulaAvulsa.png" title="Aula Avulsa"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.visitanteAulaAvulsa}"/>
                        <h:graphicImage id="sitDiaria" value="./images/botaoDiaria.png" title="Diária"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.visitanteDiaria}"/>
                        <h:graphicImage id="sitCancelamento" value="./images/botaoCancelamento.png" title="Cancelado"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.inativoCancelamento}"/>
                        <h:graphicImage id="sitDesistente" value="./images/botaoDesistente.png" title="Desistente"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.inativoDesistente}"/>
                        <h:graphicImage id="sitAVencer" value="./images/botaoAvencer.png" title="A Vencer"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.ativoAvencer}"/>
                        <h:graphicImage id="sitVencido" value="./images/botaoVencido.png" title="Vencido"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.inativoVencido}"/>
                        <h:graphicImage id="sitCarencia" value="./images/botaoCarencia.png" title="Carência"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.ativoCarencia}"/>
                        <h:graphicImage id="sitAtestado" value="./images/botaoAtestado.png" title="Atestado"
                                        rendered="#{metaDetalhada.cliente.clienteSituacaoVO_Apresentar.ativoAtestado}"/>
                    </h:panelGrid>
                </h:panelGroup>


                <h:panelGroup id="panelCliente" layout="block">
                    <a4j:commandLink
                        styleClass="#{metaDetalhada.situacaoAtualMetaEnum.styleClassLabel} lf-crmCenter-botaoCliente"
                        style="#{metaDetalhada.situacaoAtualMetaEnum.styleLabel}"
                        value="#{metaDetalhada.nomePessoaRel}"
                        id="cliente"
                        onclick="salvarPosicaoScroll(#{metaDetalhada.codigo});"
                        title="#{metaDetalhada.situacaoAtualMetaEnum.descricao}"
                        action="#{MetaCRMControle.consultarHistoricoContatoClienteManual}"
                        oncomplete="mostrarTelaDireita();adicionarPlaceHolderCRM();voltarPosicaoScroll();"
                        reRender="colunaCRMDireita, panelFotoCliente, panelCliente, panelGridRight, panelGridCenter, listaClientes, panelListaClientes, statusMeta">
                    </a4j:commandLink>

                    <h:outputText styleClass="#{metaDetalhada.taxaEvasao}"
                                  rendered="#{not empty metaDetalhada.taxaEvasao}"
                                  title="Taxa Evasao"
                                  style="padding-right: 15px; float: right"/>

                    <h:outputText styleClass="#{metaDetalhada.situacaoAtualMetaEnum.styleClassIcon}"
                                  title="#{metaDetalhada.situacaoAtualMetaEnum.descricao}"
                                  style="padding-right: 15px; float: right"/>

                    <h:outputText rendered="#{metaDetalhada.repescagem}"
                                  style="float: right; font-weight: bold; font-size: 12pt; color: red; padding-right: 2%"
                                  title="Contato de repescagem"
                                  value="R"/>

                    <%--MOSTRAR A META SOMENTE QUANDO FOR FILTRADO NA META INTEIRA--%>
                    <h:panelGroup layout="block" id="metaClienteTodasFases">
                        <h:outputText rendered="#{MetaCRMControle.buscarEmTodasFases}"
                                      styleClass="#{metaDetalhada.situacaoAtualMetaEnum.styleClassLabel} lf-crmCenter-botaoCliente"
                                      style="#{metaDetalhada.situacaoAtualMetaEnum.styleLabel};font-size: 13px; color: crimson;"
                                      value="Meta #{metaDetalhada.faseMeta} - #{metaDetalhada.dataMeta}"/>
                    </h:panelGroup>


                    <%--INFORMAÇÕES DO CLIENTE NÃO SELECIONADO--%>
                    <h:panelGroup layout="block" style="padding-top: 3px;"
                                  rendered="#{metaDetalhada.situacaoAtualMetaEnum.codigo != 1}">

                        <%--MATRICULA NÃO SELECIONADO--%>
                        <h:outputText rendered="#{not empty metaDetalhada.matricula}"
                                      style="font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                                      value="Mat:"/>
                        <h:outputText rendered="#{not empty metaDetalhada.matricula}"
                                      style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                                      value="#{metaDetalhada.matricula}"/>

                        <%--IDADE NÃO SELECIONADO--%>
                        <h:outputText rendered="#{metaDetalhada.cliente.pessoa.idadePessoa != '00'}"
                                      style="font-size: 8pt;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                      value="Idade"/>
                        <h:outputText rendered="#{metaDetalhada.cliente.pessoa.idadePessoa != '00'}"
                                      style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                                      value="#{metaDetalhada.cliente.pessoa.idadePessoa}"/>

                        <%--DATA NASC NÃO SELECIONADO--%>
                        <h:outputText rendered="#{not empty metaDetalhada.dataNascParcialPessoa && MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'AN'}"
                        style="font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                        value="Dt Nasc:"/>
                        <h:outputText rendered="#{not empty metaDetalhada.dataNascParcialPessoa && MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'AN'}"
                        style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                        value="#{metaDetalhada.dataNascParcialPessoa}"/>

                        <h:panelGroup layout="block">
                            <%--PROBABILIDADE DE EVASAO NÃO SELECIONADO--%>
                            <h:outputText rendered="#{MetaCRMControle.metaExtra && metaDetalhada.taxaEvasao != ''}"
                                          style="font-size: 8pt;font-family: arial, helvetica, sans-serif; font-weight: bold; float: left; margin-bottom: 10px; margin-top: 5px;"
                                          value="Probabilidade de Evasão:"/>
                            <h:outputText rendered="#{MetaCRMControle.metaExtra && metaDetalhada.taxaEvasao != ''}"
                                          style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif; float: left; margin-bottom: 10px; margin-top: 5px;"
                                          value="#{metaDetalhada.taxaEvasao}"/>
                        </h:panelGroup>

                    </h:panelGroup>

                    <%--INFORMAÇÕES DO CLIENTE SELECIONADO--%>
                    <h:panelGroup layout="block" style="padding-top: 3px;"
                                  rendered="#{metaDetalhada.situacaoAtualMetaEnum.codigo == 1}">

                        <%--MATRICULA SELECIONADO--%>
                        <h:outputText rendered="#{not empty metaDetalhada.matricula}"
                                      style="font-size: 8pt;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                      value="Mat:"/>
                        <h:outputText rendered="#{not empty metaDetalhada.matricula}"
                                      style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                                      value="#{metaDetalhada.matricula}"/>

                        <%--IDADE SELECIONADO--%>
                        <h:outputText rendered="#{metaDetalhada.cliente.pessoa.idadePessoa != '00'}"
                                      style="font-size: 8pt;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                      value="Idade"/>
                        <h:outputText rendered="#{metaDetalhada.cliente.pessoa.idadePessoa != '00'}"
                                      style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                                      value="#{metaDetalhada.cliente.pessoa.idadePessoa}"/>

                        <h:panelGroup layout="block">
                            <%--PROBABILIDADE DE EVASAO SELECIONADO--%>
                            <h:outputText rendered="#{MetaCRMControle.metaExtra && metaDetalhada.taxaEvasao != ''}"
                                          style="font-size: 8pt;font-family: arial, helvetica, sans-serif; font-weight: bold; float: left; margin-bottom: 10px; margin-top: 5px;"
                                          value="Probabilidade de Evasão:"/>
                            <h:outputText rendered="#{MetaCRMControle.metaExtra && metaDetalhada.taxaEvasao != ''}"
                                          style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif; float: left; margin-bottom: 10px; margin-top: 5px;"
                                          value="#{metaDetalhada.taxaEvasao}"/>
                        </h:panelGroup>
                        <%--&lt;%&ndash;ESTADO CIVIL SELECIONADO&ndash;%&gt;--%>
                        <%--<h:outputText rendered="#{not empty metaDetalhada.cliente.pessoa.estadoCivil_Apresentar}"--%>
                        <%--style="font-size: 8pt;font-family: arial, helvetica, sans-serif; font-weight: bold"--%>
                        <%--value="Estado Civil"/>--%>
                        <%--<h:outputText rendered="#{not empty metaDetalhada.cliente.pessoa.estadoCivil_Apresentar}"--%>
                        <%--style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif;"--%>
                        <%--value="#{metaDetalhada.cliente.pessoa.estadoCivil_Apresentar}"/>--%>


                        <%--DATA NASC SELECIONADO--%>
                        <h:outputText rendered="#{not empty metaDetalhada.dataNascParcialPessoa && MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'AN'}"
                                      style="font-size: 8pt;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                      value="Dt Nasc:"/>
                        <h:outputText rendered="#{not empty metaDetalhada.dataNascParcialPessoa && MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'AN'}"
                                      style="padding-right: 5%; padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                                      value="#{metaDetalhada.dataNascParcialPessoa}"/>

                    </h:panelGroup>


                    <%--INFORMAÇÕES DO AGENDAMENTO--%>
                    <h:panelGroup layout="block" style="padding-top: 3px;">
                        <h:outputText rendered="#{not empty metaDetalhada.agenda.tipoAgendamento_Apresentar}"
                                      style="font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                                      value="#{metaDetalhada.agenda.tipoAgendamento_Apresentar} -"/>
                        <h:outputText rendered="#{not empty metaDetalhada.agenda.tipoAgendamento_Apresentar}"
                                      style="padding-left: 3px; font-size: 8pt;font-family: arial, helvetica, sans-serif;"
                                      value="#{metaDetalhada.agenda.apresentarMinutoEmHora} horas"/>

                        <%--BOTÃO PARA ACESSAR A TELA DO CLIENTE--%>
                        <a4j:commandLink
                            styleClass="clienteSelecionado"
                            rendered="#{!MetaCRMControle.metaIndicacao && metaDetalhada.situacaoAtualMetaEnum.codigo == 1 && metaDetalhada.cliente.codigo != 0}"
                            style="padding-right: 15px; float: right; #{metaDetalhada.situacaoAtualMetaEnum.styleLabel}"
                            action="#{MetaCRMControle.irParaTelaCliente}"
                            oncomplete="#{MetaCRMControle.onComplete}"
                            value="Tela Cliente">
                            <%--<i class="fa-icon-external-link"></i>--%>
                        </a4j:commandLink>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGrid>
        </rich:dataGrid>

        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">

                    <a4j:outputPanel id="painelPaginacao">
                        <h:panelGroup id="painelPaginacaoManual" rendered="#{MetaCRMControle.confPaginacao.paginarBanco}">

                            <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="listaClientes,paginaAtual,paginaAtualTop,painelPaginacaoTop,painelPaginacao" rendered="#{MetaCRMControle.confPaginacao.apresentarPrimeiro}" actionListener="#{MetaCRMControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagInicial" />
                            </a4j:commandLink>
                            <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="listaClientes,paginaAtual,paginaAtualTop,painelPaginacaoTop,painelPaginacao" rendered="#{MetaCRMControle.confPaginacao.apresentarAnterior}" actionListener="#{MetaCRMControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagAnterior" />
                            </a4j:commandLink>
                            <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{MetaCRMControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                            <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="listaClientes,paginaAtual,paginaAtualTop,painelPaginacaoTop,painelPaginacao" rendered="#{MetaCRMControle.confPaginacao.apresentarPosterior}" actionListener="#{MetaCRMControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagPosterior" />
                            </a4j:commandLink>
                            <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="listaClientes,paginaAtual,paginaAtualTop,painelPaginacaoTop,painelPaginacao" rendered="#{MetaCRMControle.confPaginacao.apresentarUltimo}" actionListener="#{MetaCRMControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagFinal" />
                            </a4j:commandLink>

                            <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{MetaCRMControle.confPaginacao.numeroTotalItens}]" rendered="true"/>

                        </h:panelGroup>

                        <h:panelGroup id="painelPaginacaoScroller" rendered="#{!MetaCRMControle.confPaginacao.paginarBanco}">
                            <rich:datascroller align="center" for="form:items" maxPages="100" id="scitems" reRender="listaClientes,paginaAtual,paginaAtualTop,painelPaginacaoTop,painelPaginacao"/>
                        </h:panelGroup>
                    </a4j:outputPanel>

                </td>
            </tr>
        </table>


        <%-- LISTA DE PASSIVOS --%>
        <rich:dataGrid rendered="#{MetaCRMControle.metaPassivo}"
                       id="listaPassivos"
                       value="#{MetaCRMControle.listaPassivoFiltradaVO}"
                       styleClass="semBorda tblClientesNaFase lf-crmCenter"
                       columnClasses="semBorda"
                       rowClasses="linhaImpar"
                       width="100%"
                       cellpadding="0"
                       cellspacing="0" var="passivo"
                       onRowClick="document.getElementById(jQuery(this).find('.lf-crmCenter-botaoPassivo').attr('id')).click();">

            <h:panelGroup id="panelPassivo" layout="block">

                <a4j:commandLink style="display: none;"
                                 styleClass="lf-crmCenter-botaoPassivo"
                                 action="#{MetaCRMControle.selecionarPassivo}"
                                 oncomplete="mostrarTelaDireita();adicionarPlaceHolderCRM();"
                                 reRender="colunaCRMDireita, panelGridCenter, listaPassivos, statusMeta"/>

                <h:panelGrid rendered="#{passivo.selecionado}"
                             id="panelListaPassivos"
                             columns="1" columnClasses="colunaDireitaCRMSel"
                             style="color: #333333" width="100%">
                    <h:outputText value="#{passivo.nome}"
                                  style="color: white; text-align: left; font-weight: bold; font-size: 11px">
                        <i class="fa-icon-arrow-right"
                           style="font-size: small; padding-right: 15px; float: right; text-decoration: none; color: white"></i>
                    </h:outputText>
                </h:panelGrid>

                <h:panelGrid rendered="#{!passivo.selecionado}"
                             columns="1" columnClasses="colunaDireitaCRM"
                             style="color: #333333" width="100%">

                    <h:outputText value="#{passivo.nome}"
                                  style="color: #333333; text-align: left; font-weight: bold; font-size: 11px"/>
                </h:panelGrid>
            </h:panelGroup>
        </rich:dataGrid>
    </h:panelGroup>
</h:panelGroup>


<script type="text/javascript">
                                 var posicaoScroll = 0;
                                 var posicaoScrollAtual = 0;

                                 function salvarPosicaoScroll(aluno) {
                                     posicaoScrollAtual = jQuery('.divScroll').scrollTop();
                                 }

                                 function voltarPosicaoScroll() {
                                     jQuery('.divScroll').scrollTop(posicaoScrollAtual);
                                 }

                                 function ajustarScrollAlunoSelecionado() {
                                     try{
                                         var alunoAtual = jQuery('.colunaEsquerdaCRMSel').offset().top;
                                         var ondeEstaScroll = jQuery('.divScroll').scrollTop();
                                         var offSetScroll = jQuery('.divScroll').scroll().offset().top;
                                         if (alunoAtual > ondeEstaScroll) {
                                             var moverPara = alunoAtual - offSetScroll;
                                             jQuery('.divScroll').animate({scrollTop: moverPara}, 0);
                                         }
                                     }catch (e) {
                                         console.log("ERRO ajustarScrollAlunoSelecionado: "+e);
                                     }
                                 }

                                 adicionarPlaceHolderCRM();
                                 //    function selecionarAluno(codigo) {
                                 //        jQuery('.alunoLista .colunaEsquerdaCRMSel').addClass('colunaEsquerdaCRM').removeClass('colunaEsquerdaCRMSel');
                                 //        jQuery('.alunoLista .colunaCentroCRMSel').addClass('colunaCentroCRM').removeClass('colunaCentroCRMSel');
                                 //        jQuery('.alunoLista .colunaDireitaCRMSel').addClass('colunaDireitaCRM').removeClass('colunaDireitaCRMSel');
                                 //        jQuery('.aluno' + codigo + ' .colunaEsquerdaCRM').addClass('colunaEsquerdaCRMSel').removeClass('colunaEsquerdaCRM');
                                 //        jQuery('.aluno' + codigo + ' .colunaCentroCRM').addClass('colunaCentroCRMSel').removeClass('colunaCentroCRM');
                                 //        jQuery('.aluno' + codigo + ' .colunaDireitaCRM').addClass('colunaDireitaCRMSel').removeClass('colunaDireitaCRM');
                                 //    }
</script>

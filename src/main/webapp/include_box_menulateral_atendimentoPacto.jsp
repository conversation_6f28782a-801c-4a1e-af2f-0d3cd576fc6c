<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<!-- inicio box -->
<h:panelGroup layout="block" styleClass="menuLateral tudo">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-briefcase"></i> Canal Pacto
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="linkSolicitacoes" styleClass="titulo3 linkFuncionalidade"
                             action="#{CanalPactoControle.abrirTelaAtendimentoPacto}">Solicita��es</a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="linkSolicitacoesEmAberto" styleClass="titulo3 linkFuncionalidade"
                             action="#{CanalPactoControle.abrirTelaSolicitacoesEmAberto}">Solicita��es em aberto</a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="linkSolicitacoesConcluidas" styleClass="titulo3 linkFuncionalidade"
                             action="#{CanalPactoControle.abrirTelaSolicitacoesConcluidas}"
                             oncomplete="#{CanalPactoControle.mensagemNotificar}">Solicita��es conclu�das</a4j:commandLink>
        </h:panelGroup>

    </h:panelGroup>


</h:panelGroup>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Ajustar Boletos</title>
        <style type="text/css">
            .text {
                margin-left: 5px;
                font-size: 11px;
                font-family: Arial, serif;
            }
        </style>
    </head>
    <body style="">
    <h:form id="form">
        <h:panelGrid columns="2" columnClasses="text,text" rowClasses="linhaImpar,linhaPar">
            <h:outputText value="Usar código como identificador para Item de Partida"/>
            <h:selectBooleanCheckbox value="#{FixBoletoControle.usarCodigoPartida}" label="Nosso Número é o código"/>

            <h:outputText value="Código Partida: "/>
            <h:panelGroup layout="block">
                <h:inputText value="#{FixBoletoControle.codigoPartida}"/>
                <a4j:commandButton value="Consultar" actionListener="#{FixBoletoControle.consultarItem}"
                                   reRender="dadosPartida, acoesPartida, msg">
                    <f:attribute name="tipoItem" value="PARTIDA"/>
                </a4j:commandButton>
            </h:panelGroup>

            <h:outputText value="Item Partida: "/>
            <h:panelGroup id="dadosPartida" layout="block">
                <h:panelGrid columns="2" columnClasses="text,text" style="border: solid 1px"
                             rendered="#{FixBoletoControle.itemPartida.codigo>0}">
                    <h:outputText value="Convênio:"/>
                    <h:outputText value="#{FixBoletoControle.itemPartida.remessa.convenioCobranca.descricao}"/>

                    <h:outputText value="Código:"/>
                    <h:outputText value="#{FixBoletoControle.itemPartida.codigo}"/>

                    <h:outputText value="Identificador:"/>
                    <h:outputText value="#{FixBoletoControle.itemPartida.identificador}"/>

                    <h:outputText value="Nome Pagador:"/>
                    <h:outputText value="#{FixBoletoControle.itemPartida.pessoa.nome}"/>

                    <h:outputText value="Retornos"/>
                    <h:outputText value="#{FixBoletoControle.itemPartida.codigosRetorno}"/>

                    <h:outputText value="Vencimento"/>
                    <h:outputText value="#{FixBoletoControle.itemPartida.dataVencimentoBoleto}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>

                    <h:outputText value="Valor Boleto"/>
                    <h:outputText value="#{FixBoletoControle.itemPartida.valorBoleto}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </h:panelGrid>
            </h:panelGroup>

            <h:outputText value="Ações para item de Partida: "/>
            <h:panelGroup id="acoesPartida" layout="block">
                <h:panelGrid columns="2" columnClasses="text,text" style="border: solid 1px"
                             rendered="#{FixBoletoControle.itemPartida.codigo>0}">
                    <a4j:commandButton value="Estornar" action="#{FixBoletoControle.realizarEstornoPagamento}"
                                       reRender="msg"/>
                </h:panelGrid>
            </h:panelGroup>

            <h:outputText value="Usar código como identificador para Item Destino"/>
            <h:selectBooleanCheckbox value="#{FixBoletoControle.usarCodigoDestino}" label="Nosso Número é o código"/>

            <h:outputText value="Código Destino: "/>
            <h:panelGroup layout="block">
                <h:inputText value="#{FixBoletoControle.codigoDestino}"/>
                <a4j:commandButton value="Consultar" actionListener="#{FixBoletoControle.consultarItem}"
                                   reRender="dadosDestino, acoesDestino">
                    <f:attribute name="tipoItem" value="DESTINO"/>
                </a4j:commandButton>
            </h:panelGroup>
            <h:outputText value="Item Destino: "/>
            <h:panelGroup id="dadosDestino" layout="block">
                <h:panelGrid columns="2" columnClasses="text,text" style="border: solid 1px"
                             rendered="#{FixBoletoControle.itemDestino.codigo>0}">
                    <h:outputText value="Convênio:"/>
                    <h:outputText value="#{FixBoletoControle.itemDestino.remessa.convenioCobranca.descricao}"/>

                    <h:outputText value="Código:"/>
                    <h:outputText value="#{FixBoletoControle.itemDestino.codigo}"/>

                    <h:outputText value="Identificador:"/>
                    <h:outputText value="#{FixBoletoControle.itemDestino.identificador}"/>

                    <h:outputText value="Nome Pagador:"/>
                    <h:outputText value="#{FixBoletoControle.itemDestino.pessoa.nome}"/>

                    <h:outputText value="Retornos"/>
                    <h:outputText value="#{FixBoletoControle.itemDestino.codigosRetorno}"/>

                    <h:outputText value="Vencimento"/>
                    <h:outputText value="#{FixBoletoControle.itemDestino.dataVencimentoBoleto}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>

                    <h:outputText value="Valor Boleto"/>
                    <h:outputText value="#{FixBoletoControle.itemDestino.valorBoleto}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </h:panelGrid>
            </h:panelGroup>

            <h:outputText value="Ações para item de Destino: "/>
            <h:panelGroup id="acoesDestino" layout="block">
                <h:panelGrid columns="2" columnClasses="text,text" style="border: solid 1px"
                             rendered="#{FixBoletoControle.itemDestino.codigo>0}">
                    <a4j:commandButton value="Atualizar Retorno" action="#{FixBoletoControle.alterarArquivoRetorno}"
                                       oncomplete="#{FixBoletoControle.msgAlertAuxiliar}"
                                       reRender="msg"/>
                </h:panelGrid>
            </h:panelGroup>

            <h:outputText value="Códigos para estorno em massa: "/>
            <h:panelGroup id="estornoMassa" layout="block">
                <h:inputText value="#{FixBoletoControle.codigosEmMassa}"/>
                <a4j:commandButton value="Estornar em Massa" action="#{FixBoletoControle.estornarEmMassa}"
                                   reRender="msg"/>
            </h:panelGroup>

        </h:panelGrid>
        <h:panelGroup id="msg">
            <h:outputText value="#{FixBoletoControle.mensagemDetalhada}"/>
        </h:panelGroup>
    </h:form>
    </body>
    </html>


</f:view>

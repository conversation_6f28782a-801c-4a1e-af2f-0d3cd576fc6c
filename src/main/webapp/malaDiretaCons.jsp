<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_MalaDireta_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp"/>
        </f:facet>

        <h:form id="form">
        
        
            <h:commandLink action="#{MalaDiretaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                      <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_MalaDireta_tituloForm}">
                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-realizar-um-contato-avulso-contato-pessoal/"
                                      title="Clique e saiba mais: Mailing" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>
                </h:panelGrid>
                
                <h:panelGrid columns="4" footerClass="colunaCentralizada">
	                <h:outputText styleClass="tituloCampos" value="Tipo:"></h:outputText>
	               	<h:selectOneRadio styleClass="text" value="#{MalaDiretaControle.codigoTipoAgendamento}">
	               		<f:selectItems value="#{MalaDiretaControle.tipoConsultaCombo}"/>
	               		<a4j:support reRender="form" event="onchange"></a4j:support>
	               	</h:selectOneRadio>
	               	
	               	<h:outputText  styleClass="tituloCampos" value="Meio de Envio:" />
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" id="meioDeEnvio"
                                     value="#{MalaDiretaControle.consultarMeioEnvio}">
                        <f:selectItems value="#{MalaDiretaControle.listaSelectItemMeioDeEnvio}" />
                    </h:selectOneMenu>
                    
                </h:panelGrid>	
                <h:panelGrid columns="4" footerClass="colunaCentralizada">
                	<h:outputText styleClass="tituloCampos" value="Código:"></h:outputText>
                	<h:inputText styleClass="form" size="10" value="#{MalaDiretaControle.codigoMailing}"
                				 onkeypress="return mascara(this.form, this.id, '9999999', event);"></h:inputText>
                	
                	<h:outputText styleClass="tituloCampos" value="Título:"></h:outputText>
                	<h:inputText styleClass="form" size="40" value="#{MalaDiretaControle.consultarDescricao}"></h:inputText>
                	
                	<h:outputText styleClass="tituloCampos" value="Período de Execução:" 
                				  rendered="#{MalaDiretaControle.consultaAgendamentos}"></h:outputText>
                	<h:outputText styleClass="tituloCampos" value="Período de Envio:" 
                				  rendered="#{MalaDiretaControle.consultaAvulsos}"></h:outputText>
                	<h:panelGroup rendered="#{MalaDiretaControle.consultaAgendamentos || MalaDiretaControle.consultaAvulsos}">
                		<rich:calendar value="#{MalaDiretaControle.dataInicial}" 
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                		<h:outputText styleClass="tituloCampos" value="até"></h:outputText>
                		<rich:calendar value="#{MalaDiretaControle.dataFinal}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                	</h:panelGroup>
                	
                	<h:outputText styleClass="tituloCampos" value="Remetente:"></h:outputText>
                	<h:inputText styleClass="form" size="40" value="#{MalaDiretaControle.consultarRemetente}"></h:inputText>
                	
                </h:panelGrid>
                <center>
                	<a4j:commandButton id="consultar" styleClass="botoes" value="#{msg_bt.btn_consultar}" 
                	                 action="#{MalaDiretaControle.consultarInicial}" image="./imagensCRM/botaoConsultar.png" 
                	                 title="#{msg.msg_consultar_dados}" accesskey="2"
                	                 reRender="itens, painelPaginacaoOUT, panelGridMensagens"/>
                </center>

                <rich:dataTable id="itens" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" 
                                columnClasses="colunaCentralizada"
                                value="#{MalaDiretaControle.listaConsulta}" rows="10" var="malaDireta">

					<rich:column sortBy="#{malaDireta.codigo}">
                        <f:facet name="header">
                            <h:outputText value="ID"/>
                        </f:facet>
                        <a4j:commandLink action="#{MalaDiretaControle.editar}" id="id" value="#{malaDireta.codigo}"/>
                    </rich:column>

                    <rich:column sortBy="#{malaDireta.titulo}">
                        <f:facet name="header">
                            <h:outputText value="Título"/>
                        </f:facet>
                        <a4j:commandLink action="#{MalaDiretaControle.editar}" id="titulo" value="#{malaDireta.titulo}"/>
                    </rich:column>
                    
                    <rich:column sortBy="#{malaDireta.remetente.nome}">
                        <f:facet name="header">
                            <h:outputText value="Remetente"/>
                        </f:facet>
                        <a4j:commandLink action="#{MalaDiretaControle.editar}" id="remetente" value="#{malaDireta.remetente.nome}"/>
                    </rich:column>
                    
                    <rich:column sortBy="#{malaDireta.modeloMensagem.titulo}">
                        <f:facet name="header">
                            <h:outputText value="Modelo Mensagem"/>
                        </f:facet>
                        <a4j:commandLink action="#{MalaDiretaControle.editar}" id="modeloMensagem" value="#{malaDireta.modeloMensagem.titulo}"/>
                    </rich:column>
                    
                    <rich:column sortBy="#{malaDireta.dataEnvio}">
                        <f:facet name="header">
                            <h:outputText value="#{MalaDiretaControle.labelData}"/>
                            
                        </f:facet>
                        <a4j:commandLink action="#{MalaDiretaControle.editar}" id="envioData">
	                        <h:outputText id="dataEnvio" value="#{malaDireta.dataApresentar}">
	                        </h:outputText>
                        </a4j:commandLink>
                    </rich:column>

                    
                    
                </rich:dataTable>
                
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="painelPaginacaoOUT">
                    <h:panelGroup id="painelPaginacao" rendered="#{MalaDiretaControle.confPaginacao.existePaginacao}">
                        <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="itens, paginaAtual, painelPaginacao" rendered="#{MalaDiretaControle.confPaginacao.apresentarPrimeiro}" actionListener="#{MalaDiretaControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagInicial" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacao.apresentarAnterior}" actionListener="#{MalaDiretaControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagAnterior" />
                        </a4j:commandLink>
                        <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{MalaDiretaControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                        <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacao.apresentarPosterior}" actionListener="#{MalaDiretaControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagPosterior" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacao.apresentarUltimo}" actionListener="#{MalaDiretaControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagFinal" />
                        </a4j:commandLink>
                        <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{MalaDiretaControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
                    </h:panelGroup>
                </h:panelGrid>
                

                <h:panelGrid id="panelGridMensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgMailingPrin" styleClass="mensagem" value="#{MalaDiretaControle.mensagem}"/>
                        <h:outputText id="msgMailingPrinDet"  styleClass="mensagemDetalhada" value="#{MalaDiretaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{MalaDiretaControle.novo}" value="#{msg_bt.btn_novo}" styleClass="botoes" image="./imagensCRM/botaoNovo.png" title="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            
        </h:form>
    </h:panelGrid>

    
</f:view>
<script type="text/javascript">
    document.getElementById("form:valorConsulta").focus();
</script>
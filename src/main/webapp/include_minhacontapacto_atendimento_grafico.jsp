<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<script>
    function carregarGrafico() {
        var chart = AmCharts.makeChart("chartdiv", {
            "type": "pie",
            "dataProvider": [{
                "title": "No mesmo dia",
                "color": "#073555",
                "value": ${CanalPactoControle.estatisticaSolicitacaoJSON.ticketsMesmoDia},
                "mesmoDia": ${CanalPactoControle.estatisticaSolicitacaoJSON.ticketsMesmoDia},
                "diaSeg": ${CanalPactoControle.estatisticaSolicitacaoJSON.ticketsDiaSeguinte}
            }, {
                "title": "No dia seguinte",
                "color": "#F06D29",
                "value": ${CanalPactoControle.estatisticaSolicitacaoJSON.ticketsDiaSeguinte},
                "mesmoDia": ${CanalPactoControle.estatisticaSolicitacaoJSON.ticketsMesmoDia},
                "diaSeg": ${CanalPactoControle.estatisticaSolicitacaoJSON.ticketsDiaSeguinte}
            }],
            "graphs": [{
                "balloonText": "[[value]]",
                "title": "atendidos no mesmo dia",
                "valueField": "mesmoDia"
            }, {
                "balloonText": "[[value]]",
                "title": "atendidos no dia seguinte",
                "valueField": "diaSeg"
            }],
            "titleField": "title",
            "valueField": "value",
            "colorField": "color",
            "labelRadius": 5,
            "radius": "42%",
            "labelText": "[[title]] [[value]]",
            "export": {
                "enabled": false
            }
        });
    }

</script>

<div style="width:100%; text-align: center;">
    <h:outputText styleClass="texto-size-16 texto-cor-cinza tooltipster" value="Tempo de atendimento"
                  title="São consideras as últimas 30 solicitações"/>
</div>

<h:panelGroup layout="block" id="panelGrafico">
    <div id="chartdiv" style="height: 300px; width: 100%;"></div>
</h:panelGroup>

<script>
    carregarGrafico();
    carregarTooltipster();
</script>



<style type="text/css">
    <!--
    #apDiv1 {
        position:absolute;
        left:96px;
        top:77px;
        width:171px;
        height:554px;
        z-index:1;
    }
    -->
    </style>

<table width="100%" height="48" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td align="left" valign="top" class="bgmenuleft" style="padding:9px 0 0 80px;">
            
            <script src="Scripts/AC_RunActiveContent.js" type="text/javascript"></script>
            <div id="apDiv1">
                <script type="text/javascript">
                    AC_FL_RunContent( 'codebase','http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=9,0,28,0','width','428','height','360','title','menuDropDown','src','imagens/menuDropDown','quality','high','pluginspage','http://www.adobe.com/shockwave/download/download.cgi?P1_Prod_Version=ShockwaveFlash','wmode','transparent','movie','imagens/menuDropDown' ); //end AC code
                </script><noscript>
                    <object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=9,0,28,0" width="428" height="39" title="menuDropDown">
                        <param name="movie" value="imagens/menuDropDown.swf" />
                        <param name="quality" value="high" />
                        <param name="wmode" value="transparent" />
                        <embed src="imagens/menuDropDown.swf" width="428" height="360" quality="high" pluginspage="http://www.adobe.com/shockwave/download/download.cgi?P1_Prod_Version=ShockwaveFlash" type="application/x-shockwave-flash" wmode="transparent"></embed>
                    </object>
                </noscript>
            </div>   
        </td>
        <td width="312" align="right" valign="top" style="padding:9px 13px 0 0;">
            <input style="width:150px;vertical-align:middle;" class="form" type="text" name="textfield2" value="Palavra-chave" onFocus="if(this.value=='Palavra-chave')this.value=''" onBlur="if(this.value=='')this.value='Palavra-chave'">
            <select style="vertical-align:middle;" class="form2" name="">
                <option>Selecione:</option>
                <option>Alunos</option>
                <option>Professores</option>
                <option>Academias</option>			
            </select>
            <input style="vertical-align:middle;" name="imageField" type="image" src="images/btn_buscar.png" alt="Buscar">
        </td>
    </tr>
</table>
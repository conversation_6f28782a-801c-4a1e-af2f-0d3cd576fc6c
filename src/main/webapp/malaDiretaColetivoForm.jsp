
<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<script type="text/javascript" src="script/scriptSMS.js">

</script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_MalaDireta_tituloForm}" /></title>

    <rich:modalPanel id="listaEmail" autosized="true" shadowOpacity="true" width="550" height="250" onshow="document.getElementById('formListaEmail:consultarEmail').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_MalaDireta_listaEmail}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkRemetente" />
                <rich:componentControl for="listaEmail" attachTo="hiperlinkRemetente" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formListaEmail" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">

                <rich:dataTable id="resultadoConsultaListaEmail" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaCentralizada" value="#{AberturaMetaControle.listaEmailPessoa}" rows="10" var="malaDireta">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_MalaDireta_email}" />
                        </f:facet>
                        <h:outputText value="#{malaDireta.email}" styleClass="campos" />
                    </rich:column>
                </rich:dataTable>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelModeloMensagem" autosized="true" shadowOpacity="true" width="550" height="300"
                     onshow="document.getElementById('formModeloMensagem:consultarModeloMensagem').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkModeloMensagem" />
                <rich:componentControl for="panelModeloMensagem" attachTo="hiperlinkModeloMensagem" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formModeloMensagem" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}" />
                    <h:selectOneMenu styleClass="campos" id="consultarModeloMensagem" value="#{AberturaMetaControle.campoConsultarModeloMensagem}">
                        <f:selectItems value="#{AberturaMetaControle.tipoConsultarComboModeloMensagem}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarModeloMensagem" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 value="#{AberturaMetaControle.valorConsultarModeloMensagem}" />
                    <a4j:commandButton id="btnConsultarModeloMensagem" 
                                       reRender="formModeloMensagem:mensagemConsultarModeloMensagem, formModeloMensagem:resultadoConsultaModeloMensagem ,
                                       formModeloMensagem:scResultadoModeloMensagem , formModeloMensagem"
                                       action="#{AberturaMetaControle.consultarModeloMensagem}" styleClass="botoes"
                                       value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaModeloMensagem" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento" value="#{AberturaMetaControle.listaConsultarModeloMensagem}" rows="10" var="modeloMensagem">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_codigo}" />
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.codigo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_titulo}" />
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.titulo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <a4j:commandButton action="#{AberturaMetaControle.selecionarModeloMensagem}" focus="modeloMensagem"
                                           reRender="formF, formModeloMensagem" oncomplete="Richfaces.hideModalPanel('panelModeloMensagem')"
                                           value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagensCRM/botaoEditar.png" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModeloMensagem:resultadoConsultaModeloMensagem" maxPages="10" id="scResultadoModeloMensagem" />
                <h:panelGrid id="mensagemConsultaModeloMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelEmail" autosized="true" shadowOpacity="true" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores de Email"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink4" />
                <rich:componentControl for="panelEmail" attachTo="hidelink4" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorEmail" ajaxSubmit="true">
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <rich:dataTable id="MarcadoEmail" width="440px" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento" var="marcadorEmail" rows="40"
                                value="#{AberturaMetaControle.listaSelectItemMarcadoEmail}">
                    <f:facet name="header">
                        <h:outputText value="Email" />
                    </f:facet>
                    <rich:column width="170px">
                        <f:facet name="header">
                            <h:outputText value="Tags" />
                        </f:facet>
                        <h:outputText styleClass="campos" value="#{marcadorEmail.nome}" />
                    </rich:column>
                    <rich:column width="240px">
                        <f:facet name="header">
                            <h:outputText value="Opções" />
                        </f:facet>
                        <a4j:commandButton action="#{AberturaMetaControle.executarInsercaoTag}" reRender="formF:imputMensagem"
                                           oncomplete="Richfaces.hideModalPanel('panelEmail');" image="./imagens/botaoAlteracaoIncluir.gif"
                                           value="Adicionar" styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp" />
        </f:facet>

        <h:form id="formF">
            <h:commandLink action="#{AberturaMetaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_MalaDireta_tituloForm}" />
                </h:panelGrid>

                <h:panelGroup id="totalLista" style="float: right; margin-right: 10px;">
                    <h:outputText styleClass="tituloCampos" style="font-weight: bold;" value="#{msg_aplic.prt_AberturaMeta_totalizadorListaMailing}:" />
                    <rich:spacer width="10px;" />
                    <h:outputText styleClass="tituloCampos" style="font-weight: bold;" value="#{AberturaMetaControle.malaDiretaVO.totalPessoaMalaDireta}" />
                </h:panelGroup>
                <h:panelGroup  style="float: right; margin-right: 10px;">
                    <h:outputText styleClass="tituloCampos" style="font-weight: bold;" value="#{msg_aplic.prt_AberturaMeta_totalizadorEnviados}:" />
                    <rich:spacer width="10px;" />
                    <h:outputText styleClass="tituloCampos" style="font-weight: bold;" value="#{AberturaMetaControle.malaDiretaVO.totalPessoaMalaDiretaEnviada}" />
                </h:panelGroup>
                <rich:tabPanel width="100%" switchType="client">
                    <rich:tab id="dadosEmail" label="Dados Envio" switchType="client">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_meioEnvio}" />
                            <h:outputText styleClass="tituloCampos" value="#{AberturaMetaControle.malaDiretaVO.meioDeEnvioEnum.descricao}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_dataEnvio}" />
                            <h:inputText  id="dataEnvio" onkeypress="return mascara(this.formF, 'formF:dataEnvio', '99/99/9999', event);" readonly="true" size="10" maxlength="10" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{AberturaMetaControle.malaDiretaVO.dataEnvio}" >
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:inputText>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_remetente}" />
                            <h:panelGroup id="panelResponsavelGrupo">
                                <h:inputText id="textColaboradorResponsavel" size="50" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{AberturaMetaControle.malaDiretaVO.remetente.nome}" />
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}" />
                            <h:panelGroup>
                                <h:inputText id="modeloMensagem" size="70" readonly="true" maxlength="100" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form" value="#{AberturaMetaControle.malaDiretaVO.modeloMensagem.titulo}" />
                                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"  image="imagensCRM/informacao.gif"
                                                   alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
                                <rich:spacer width="5" />
                                <a4j:commandButton id="limparModeloMensagem"  action="#{AberturaMetaControle.limparCampoModeloMensagem}"
                                                   image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="panelGridForm" />
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_titulo}" />
                            <h:panelGroup >
                                <h:inputText id="titulo" size="70" maxlength="100" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{AberturaMetaControle.malaDiretaVO.titulo}" />
                                <rich:toolTip for="titulo" followMouse="true" direction="top-right"  style="width:300px; height:#{AberturaMetaControle.tamanhoToolTip}; " showDelay="200">
                                    <h:outputText styleClass="tituloCampos" escape="false"
                                                  value="#{msg.msg_tip_tituloMail}#{AberturaMetaControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}" />
                                </rich:toolTip>             
                            </h:panelGroup>
                            <h:outputText rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==2}" styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_adicionarTag}" />
                            <h:panelGroup rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==2}">
                                <h:panelGroup layout="block" style="padding-top: 10px; padding-bottom: 10px;">
                                    <a4j:commandLink styleClass="pure-button pure-button-small"
                                                     value="Tag Primeiro Nome"
                                                     action="#{AberturaMetaControle.incluirTagPNome}"
                                                     reRender="textComentario"/>

                                    <a4j:commandLink styleClass="pure-button pure-button-small"
                                                     style="margin-left: 10px"
                                                     value="Tag Nome"
                                                     action="#{AberturaMetaControle.incluirTagNome}"
                                                     reRender="textComentario"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==1}" columns="1" styleClass="tabForm" columnClasses="colunaCentralizada" width="100%">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_mensagem}" />
                            <rich:editor configuration="editorpropriedades" viewMode="visual" theme="advanced" id="imputMensagem" height="500" width="700"
                                         value="#{AberturaMetaControle.malaDiretaVO.mensagem}" />
                        </h:panelGrid>
                        <h:panelGrid columns="1" rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==2}"  id="textComentario" styleClass="tabForm"
                                     style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                                     columnClasses="colunaCentralizada" width="100%" >
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_comentario}" />
                            <rich:spacer height="20"/>
                            <h:outputLink value="#{SuperControle.urlWikiCRM}Recursos_Extras:Mailing#SMS"
                                          title="Clique e saiba mais: Mailing SMS" target="_blank" >
                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                <h:outputText value="Mailing - SMS" ></h:outputText>
                            </h:outputLink>
                            <h:inputTextarea
                                onkeypress="soma(this.value);" onkeyup="soma(this.value);"
                                style="align:center;" id="comentarioTextArea" cols="100" rows="2"
                                value="#{AberturaMetaControle.malaDiretaVO.mensagem}"/>

							<rich:toolTip for="comentarioTextArea" followMouse="true" 
											  rendered="#{AberturaMetaControle.toolTipSMS}"
											  direction="top-right"  style="width:300px; height:#{AberturaMetaControle.tamanhoToolTipSMS}; " showDelay="200">
                                    <h:outputText styleClass="tituloCampos" escape="false"
                                                  value="#{AberturaMetaControle.termosFiscalizados}" />
                                </rich:toolTip>

                            <h:panelGroup id="panelTamanhoRestante" style="align:center;" layout="block" rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==2}">
                                <h:inputText style="align:center;" disabled="true" size="3" id="tamanhoRestante"/>
                            
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="emailsEnviados" label="Enviar Para:" switchType="client">

                        <rich:dataTable id="malaDiretaEnviadaVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                        rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                        value="#{AberturaMetaControle.malaDiretaVO.malaDiretaEnviadaVOs}" var="malaDiretaEnviada">

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_pessoa}" />
                                </f:facet>
                                <h:outputText value="#{malaDiretaEnviada.clienteVO.pessoa.nome}" rendered="#{malaDiretaEnviada.apresentarColunaPessoa}" />
                                <h:outputText value="#{malaDiretaEnviada.passivoVO.nome}" rendered="#{malaDiretaEnviada.apresentarColunaPassivo}" />
                                <h:outputText value="#{malaDiretaEnviada.indicadoVO.nomeIndicado}" rendered="#{malaDiretaEnviada.apresentarColunaIndicado}" />
                            </rich:column>

                            <rich:column rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==1}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_Email}"  />
                                </f:facet>
                                <%--emails--%>
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPassivo}" value="#{malaDiretaEnviada.passivoVO.email}" />
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaIndicado}" value="#{malaDiretaEnviada.indicadoVO.email}"  />
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPessoa}" value="#{malaDiretaEnviada.emails}" />
                            </rich:column>
                            <rich:column rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==2}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_Telefone_Celular}"  />
                                </f:facet>
                                <%--telefones--%>
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPessoa}" value="#{malaDiretaEnviada.telefonesCelulares}" />
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaIndicado}"  value="#{malaDiretaEnviada.indicadoVO.telefones}" />
                                <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPassivo}" value="#{malaDiretaEnviada.passivoVO.telefones}"/>
                            </rich:column>
                        </rich:dataTable>
                    </rich:tab>
                </rich:tabPanel>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="enviarEmail" rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==1}"
                                               action="#{AberturaMetaControle.enviarEmailColetivo}"
                                               reRender="formF"
                                               oncomplete="#{AberturaMetaControle.msgAlert}recarregarMetas();"
                                               value="#{msg_bt.btn_gravar}"
                                               image="./imagensCRM/botaoEnviar.png"
                                               title="#{msg.msg_gravar_dados}"
                                               accesskey="2" styleClass="botoes" />
                            <a4j:commandLink id="enviarSMS" styleClass="pure-button pure-button-small" rendered="#{AberturaMetaControle.malaDiretaVO.meioDeEnvio==2}"
                                             value="Enviar"
                                             action="#{AberturaMetaControle.enviarSMSColetivo}"
                                             reRender="formF"
                                             oncomplete="#{AberturaMetaControle.msgAlert}recarregarMetas();"
                                             title="#{msg.msg_gravar_dados}"
                                             accesskey="2"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("formF:dataEnvio").focus();
</script>


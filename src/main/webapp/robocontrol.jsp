<%--
    Document   : robocontrol
    Created on : 14/08/2012, 16:30:57
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <f:loadBundle var="msg" basename="propriedades.Mensagens" />
    <html>
        <head>
            <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
            <title>RobotControl</title>
            <style type="text/css">
                .text{
                    margin-left: 5px;
                    font-size:11px;
                    font-family:Arial;
                }
            </style>
        </head>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
            <a4j:form id="form">
                <h:panelGrid columnClasses="colunaCentralizada" width="100%" style=" background-image:url('../imagens/fundoBarraTopo.png');">
                    <h:outputText styleClass="text" style="color:white;font-size:15px;" value="Robot Control"/>
                </h:panelGrid>
                <h:panelGrid columnClasses="text" columns="1" width="100%">
                    <h:outputText style="color:blue;" value="#{msg.ajuda_robot_control}"/>
                </h:panelGrid>
                <h:panelGroup style="padding: 5px 5px 5px 5px;">                    
                    <h:panelGrid columns="2" columnClasses="text,text">
                        <h:outputText value="Data Base:"/>
                         <h:panelGroup>
                            <rich:calendar
                                id="dataSimulada" styleClass="form"
                                value="#{RoboControle.dataSimulada}" datePattern="dd/MM/yyyy"
                                inputSize="10" inputClass="form"
                                style="background: white;
                                border: 1px solid #CCC;
                                border-bottom-color: #999;
                                border-right-color: #999;
                                height:26px;
                                color: black;
                                margin: 5px;
                                padding: 5px 8px 0 6px;
                                padding-right: 5px;
                                vertical-align: middle;"
                                oninputblur="blurinput(this);"
                                oninputfocus="focusinput(this);"
                                enableManualInput="true" zindex="2" showWeeksBar="false"
                                tabindex="1">
                            </rich:calendar>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        </h:panelGroup>
                        <h:outputText value="Matrículas separadas por vírgula:"/>
                        <h:inputText id="matriculas" size="40"  value="#{RoboControle.matriculas}"/>

                        <h:outputText value="Contratos separados por vírgula:"/>
                        <h:inputText id="contratos" size="40" value="#{RoboControle.contratos}"/>

                        <h:panelGroup>
                            <h:selectBooleanCheckbox style="vertical-align:middle;" id="chkForcarGerarHistorico" value="#{RoboControle.apagarHistorioContratoAntes}"/>
                            <h:outputText style="vertical-align:middle;" value="Limpar históricos temporais dos contratos?"/>
                        </h:panelGroup>

                    </h:panelGrid>

                    <a4j:commandButton value="Process!" action="#{RoboControle.processarLote}" reRender="form"/>

                    <h:panelGrid id="mensagem" columns="1" width="100%" styleClass="tabMensagens" style="margin:5 5 5 5;">
                        <h:outputText styleClass="mensagem" value="#{RoboControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{RoboControle.mensagemDetalhada}"/>
                        <h:message styleClass="mensagemDetalhada" for="dataSimulada"></h:message>
                    </h:panelGrid>
                </h:panelGroup>

            </a4j:form>
        </body>
    </html>
</f:view>

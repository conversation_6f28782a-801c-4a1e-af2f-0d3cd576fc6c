<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Cancelamento" />
    </title>

    <c:set var="titulo" scope="session" value="Cancelamento"/>
    <rich:modalPanel id="panel" width="350" height="100" showWhenRendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.mensagemErro}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panel" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup>
                <h:graphicImage value="/imagens/erro.png"/>
                <rich:spacer width="10" />
                <h:outputText id="msgCancelamentoDet" styleClass="mensagemDetalhada" value="#{CancelamentoSessaoControle.mensagemDetalhada}"/>
            </h:panelGroup>
        </h:panelGrid>
    </rich:modalPanel>

    <h:form id="form">
        <h:panelGrid columns="2" width="100%" styleClass="tabMensagens">

            <h:panelGrid columns="1" >
                <img src="./imagens/lateralWizardCancelamentoMaior.png" >
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" cellpadding="2" >
                <h:panelGrid columns="2">
                    <h:outputText value="Nome do Cliente: " styleClass="tituloCampos" style="font-weight: bold;"  />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{CancelamentoSessaoControle.clienteVO.pessoa.nome}"/>
                    </h:panelGroup>
                    <h:outputText value="Valor Pago pelo Cliente " styleClass="tituloCamposNegrito" />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                        <h:outputText id="valorPago" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.somaValorPagoPeloClienteComCheque}" style="font-weight: bold; color:green" styleClass="tituloCampos">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:outputText value="Valor Utilizado pelo Cliente " styleClass="tituloCamposNegrito" />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                        <h:outputText id="valorCobrado" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorUtilizadoCliente}" style="font-weight: bold; color: red" styleClass="tituloCampos">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup id="labelValor">
                        <h:outputText value="Valor a ser Devolvido ao Cliente " styleClass="tituloCamposNegrito" rendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo >= 0}"/>
                        <h:outputText value="Valor a ser Cobrado do Cliente " styleClass="tituloCamposNegrito" rendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo < 0}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                        <h:outputText id="valorDevolvido" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorASerDevolvidoBaseCalculo_Apresentar}" styleClass="#{CancelamentoSessaoControle.cancelamentoSessaoVO.definirCorCampoBase}" style="font-weight: bold;">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="panelListaCheque" rendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.apresentaListaCheque}">
                    <h:outputText value="Lista de Cheques do Último Pagamento do Cliente" styleClass="tituloCampos" />
                    <rich:dataTable id="listaCheque" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                    value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.listaMovPagamentos}" var="movPagamento">
                        <rich:column rendered="#{movPagamento.movPagamentoEscolhida and movPagamento.formaPagamento.tipoFormaPagamento == 'CH'}">
                            <h:dataTable id="MovPagamentoCheque" headerClass="consulta" 
                            			 rowClasses="tablelistras textsmall" width="100%" 
                            			 columnClasses="centralizado, centralizado,colunaEsquerda, colunaDireita, colunaDireita,colunaDireita, centralizado, colunaDireita"
                                         value="#{movPagamento.chequeVOs}" var="cheque">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Compensar" />
                                    </f:facet>
                                    <h:selectBooleanCheckbox id="compensarCheque" 
                                    rendered="#{cheque.apresentarCheque}"  value="#{cheque.chequeCompensar}">
                                        <a4j:support event="onclick" action="#{CancelamentoSessaoControle.compensarCheque}" 
                                                     reRender="valorDevolvido,panelListaCheque,panelLancaProdutoLiberacao,valorPago,labelValor,devolverCheque"/>
                                    </h:selectBooleanCheckbox>
                                </h:column>
                                <h:column >
                                    <f:facet name="header">
                                        <h:outputText value="Devolver" />
                                    </f:facet>
                                    <h:selectBooleanCheckbox id="devolverCheque" 
                                    			rendered="#{cheque.apresentarCheque 
                                    			           && (!cheque.temLote 
                                    			           || !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas) && !cheque.pagaOutroContrato}"  value="#{cheque.chequeEscolhido}">
                                        <a4j:support event="onclick" action="#{CancelamentoSessaoControle.devolverCheque}" 
                                                     reRender="valorDevolvido,panelListaCheque,valorPago,labelValor,compensarCheque,panelLancaProdutoLiberacao,panelmensagemDet"/>
                                    </h:selectBooleanCheckbox>
                                    
                                    <h:outputLink value="#{SuperControle.urlWikiFIN}"
			                         title="Clique e saiba mais: "
			                         target="_blank" 
			                         rendered="#{cheque.apresentarCheque && (cheque.temLote && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas) && !cheque.pagaOutroContrato}">
			           				   <h:graphicImage styleClass="linkWiki" url="/imagensCRM/informacao.gif" 
			               				   title="Este cheque não pode ser editado ou excluído pois já está no lote #{cheque.loteVO.codigo}. Clique para mais informações."/>
			          				 </h:outputLink> 
			          				 
			          				  <h:graphicImage rendered="#{cheque.situacao == 'CA'}" styleClass="linkWiki" url="/imagensCRM/informacao.gif" 
			               				   title="Este cheque já foi devolvido"/>
			               				<h:graphicImage rendered="#{cheque.pagaOutroContrato && cheque.apresentarCheque}" styleClass="linkWiki" url="/imagensCRM/informacao.gif" 
			               				   title="Este cheque paga outro produto."/>      
																	           
                                </h:column>
                              
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Banco"  />
                                    </f:facet>
                                    <h:outputText id="bancoPreenchido" value="#{cheque.banco.nome}" styleClass="form"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Agência" />
                                    </f:facet>
                                    <h:outputText id="agencia" value="#{cheque.agencia}" styleClass="form" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Conta"/>
                                    </f:facet>
                                    <h:outputText id="conta" value="#{cheque.conta}" styleClass="form" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="N° Cheque"/>
                                    </f:facet>
                                    <h:outputText id="nDoc" value="#{cheque.numero}" styleClass="form"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Compensação"/>
                                    </f:facet>
                                    <h:outputText value="#{cheque.dataCompensacao}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Valor"/>
                                    </f:facet>
                                    <h:outputText id="valor" value="#{cheque.valor}" styleClass="form" >
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                </h:column>
                            </h:dataTable>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>
                <h:panelGrid id="panelListaCartao" rendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.apresentaListaCartao}">
                    <h:outputText value="Lista de Cartões do Último Pagamento do Cliente" styleClass="tituloCampos"/>
                    <rich:dataTable id="listaCartao" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                    value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.listaMovPagamentos}" var="movPagamento">
                        <rich:column rendered="#{movPagamento.movPagamentoEscolhida and movPagamento.formaPagamento.tipoFormaPagamento == 'CA'}">
                            <h:dataTable id="MovPagamentoCartao" headerClass="consulta" rowClasses="tablelistras textsmall" width="100%" columnClasses="colunaEsquerda"
                                         value="#{movPagamento.cartaoCreditoVOs}" var="cartao">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Compensar"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="compensarCartao"  rendered="#{cartao.apresentarCartao}"  value="#{cartao.cartaoCompensar}">
                                            <a4j:support event="onclick" action="#{CancelamentoSessaoControle.compensarCartao}"
                                                         reRender="valorDevolvido,panelListaCartao,panelLancaProdutoLiberacao,valorPago,labelValor,devolverCartao"/>
                                        </h:selectBooleanCheckbox>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Estornar"/>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="devolverCartao" 
                                        rendered="#{cartao.apresentarCartao && (!cartao.temLote
                                        			|| !ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas) && !cartao.pagaOutroContrato}"  
                                        			value="#{cartao.cartaoEscolhido}">
                                            <a4j:support event="onclick" action="#{CancelamentoSessaoControle.devolverCartao}"
                                                         reRender="valorDevolvido,panelListaCartao,valorPago,labelValor,compensarCartao,panelLancaProdutoLiberacao,panelmensagemDet"/>
                                        </h:selectBooleanCheckbox>
                                        
                                        <h:outputLink value="#{SuperControle.urlWikiFIN}"
							                         title="Clique e saiba mais: "
							                         target="_blank" 
							                         rendered="#{cartao.apresentarCartao && (cartao.temLote
							                         && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas) && !cartao.pagaOutroContrato}">
							               <h:graphicImage styleClass="linkWiki" url="/imagensCRM/informacao.gif" 
							               				   title="Este cartão não pode ser editado ou excluído pois já está no lote #{cartao.lote.codigo}. Clique para mais informações."/>
							           </h:outputLink> 
							            <h:graphicImage rendered="#{cartao.situacao == 'CA'}" styleClass="linkWiki" url="/imagensCRM/informacao.gif" 
			               				   title="Esta parcela do cartão já foi estornada"/>
			               				<h:graphicImage rendered="#{cartao.pagaOutroContrato && cartao.apresentarCartao}" styleClass="linkWiki" url="/imagensCRM/informacao.gif" 
			               				   title="Este parcela do cartão paga outro contrato."/>   
                                    </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                                    </f:facet>
                                    <h:outputText value="#{movPagamento.nomePagador}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.operadora.descricao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.dataCompensacao}">
                                        <f:convertDateTime pattern="dd/MM/yyyy" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Cheque_valor}" />
                                    </f:facet>
                                    <h:outputText value="#{cartao.valor}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                            </h:dataTable>
                        </rich:column>
                    </rich:dataTable>
                    <h:outputText value="* Se for necessário estornar algum pagamento com cartão de crédito tenha em mãos o CÓDIGO DE AUTORIZAÇÃO da operação.
                                  Em seguida, ENTRE EM CONTATO com a operadora de cartão de crédito para estornar as parcelas que ainda não entraram na conta da empresa." styleClass="tituloCampos red"/>
                </h:panelGrid>
             
                <h:panelGrid width="550px" columns="2"  style="position:relative; top:50px;" >
                    <h:panelGrid width="350px"/>
                    <h:panelGrid width="50px">
                        <%--h:panelGroup style="position:relative; top:190px; left:60px;"--%>
                        <h:panelGroup>
                            <h:commandButton id="voltar" alt="Voltar Passo" action="#{CancelamentoSessaoControle.voltarTelaCancelamento}" image="./imagens/botaoVoltar.png" />
                            <rich:spacer width="7"/>
                            <a4j:commandButton id="proximo"  
                            	value="Próximo Passo" 
                            	action="#{CancelamentoSessaoControle.finalizarCancelamentoComCheque}"
                            	image="./imagens/botaoProximo.png"
                                reRender="panelmensagemDet">
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:modalPanel id="panelmensagemDet" width="350" height="100" showWhenRendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.mensagemErro}">
                    <f:facet name="header">
                        <h:panelGroup>
                            <h:outputText value="Atenção!"></h:outputText>
                        </h:panelGroup>
                    </f:facet>
                    <f:facet name="controls">
                        <h:panelGroup>
                            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                            <rich:componentControl for="panelmensagemDet" attachTo="hidelink" operation="hide" event="onclick"/>
                        </h:panelGroup>
                    </f:facet>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:graphicImage value="/imagens/erro.png"/>
                            <rich:spacer width="10" />
                            <h:outputText id="msgCancelamentoDet" styleClass="mensagemDetalhada" value="#{CancelamentoSessaoControle.mensagemDetalhada}"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </rich:modalPanel>
            </h:panelGrid>

        </h:panelGrid>
    </h:form>

</f:view>
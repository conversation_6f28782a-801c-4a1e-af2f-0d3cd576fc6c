<%@page import="controle.arquitetura.SuperControle" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<meta name="robots" content="noindex,nofollow"/>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" src="script/jquery.js"></script>
<script type="text/javascript" src="script/w.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>


    <head>

        <style type="text/css">
            html, body {
                width: 100%;
                height: 100%;
                margin: 0 0 0 0;
                padding: 0 0 0 0;
                text-align: center;
            }

            <c:if test="${InicioControle.NFe}">
            body {
                background: url("images/nfe/bg.png");
            }

            #container-nfe {
                width: 1006px;
                margin: 0 auto;
                height: 501px;
            }

            .login-esquerda {
                background: url("./images/nfe/suporte-esq.png");
                height: 501px;
                width: 437px;
                float: left;
            }

            .login-direita {
                background: url("./images/nfe/suporte-dir.png");
                height: 501px;
                width: 569px;
                float: left;
            }

            .logoNFeLogin {
                margin-top: 81px;
                margin-left: 124px;
            }

            #formLogin {
                margin-left: 125px;
                margin-top: 81px;
            }

            .tituloNFe {
                font-family: helvetica, arial, sans-serif;
                color: #abffff;
                font-size: 20px;
                text-align: left
            }

            .enfaseTitulo {
                font-weight: bold;
                font-size: 20px;
            }

            .inputsLoginNFe {
                margin-top: 24px;
                width: 320px;
                height: 162px
            }

            .inputLoginNFe {
                margin-bottom: 18px;
            }

            #botaoLogar {
                background: url("./images/nfe/login-button-3px-entre-estados.png") no-repeat 0 0;
                height: 42px;
                width: 131px;
            }

            #botaoLogar:hover {
                background: url("./images/nfe/login-button-3px-entre-estados.png") no-repeat 0 -45;
                height: 42px;
                width: 131px;
            }

            #botaoLogar:active {
                background: url("./images/nfe/login-button-3px-entre-estados.png") no-repeat 0 -90;
                height: 42px;
                width: 131px;
            }

            </c:if>

            #table {
                width: 100%;
                height: 100%;
                display: table;
            }

            #cell {
                vertical-align: middle;
                display: table-cell;
                _position: absolute;
                _top: 50%;
            }

            #conteudo {
                width: 500px;
                margin: 0 auto;
                _left: -50%;
                padding: 0 0 0 0;
                _position: relative;
                _top: -50%;
                text-align: left;
            }

            .rich-mpnl-body {
                padding: 0;
                margin: 0;
            }

            .textGray:hover {
                background: #e3e3e3;
                text-decoration: none;
            }

            .colunaCentralizada:hover > a {
                text-decoration: none;
            }

            .textoPadraoLabel {
                font-family: Arial;
                font-size: 12px;
                font-weight: normal;
                font-style: normal;
                font-stretch: normal;
                line-height: 1.43;
                letter-spacing: normal; /*text-align: center;*/
                color: #777777;
            }

            .botoesLink {
                margin-left: 12px;
                padding: 10px 15px 10px 15px !important;
                border-radius: 5px;
                box-shadow: 0px 2px 3px 0px #777;
            }
        </style>
    </head>

    <h:inputHidden id="atualizarDB" value="#{LoginControle.exibirModalAtualizacaoBD}"/>

    <rich:modalPanel id="panelDiasParaBloqueio" styleClass="novaModal" autosized="true" shadowOpacity="true" width="768"
                     height="500" showWhenRendered="#{LoginControle.abrirRichModalDiasParaBloqueio and !LoginControle.concederMaisUmDia}">

        <f:facet name="header">
            <h:outputText value="#{LoginControle.tituloModalBloqueio}" escape="false"
                          style="font-size: 32px; vertical-align: middle; font-weight: lighter"/>
        </f:facet>

        <h:form id="formDiasParaBloqueio">

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" style="margin-top: 10px">
            <h:panelGroup layout="block" style="text-align: center; margin: 20px 0;">
                <img src="imagens/icon_bloqueio_agendado.png"
                     style="width: 103.25px; height: 106px; vertical-align: bottom;">
            </h:panelGroup>
            <h:panelGroup layout="block" style="text-align: center; margin: 10px 0;font-style: normal;
                            font-weight: 700;
                            font-size: 18px;
                            line-height: 21px;">
                <h:outputText value="#{LoginControle.tituloMensagemBloqueio}" escape="false"/>
            </h:panelGroup>
            <h:panelGroup layout="block" style="padding: 10px 10px;">
                <h:outputText escape="false"
                              value="#{LoginControle.mensagemBloqueio}"
                              style="font-weight: 400;font-size: 14px;line-height: 130%;"/>
            </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block"
                          style="text-align: center; width: 100%; padding-bottom: 20px"
                          styleClass="colunaCentralizada">


                <a4j:commandLink value="Continuar usando o sistema"
                                 styleClass="botaoPrimarioSmall texto-size-16-real botoesLink"
                                 style="background-color:  #FFFFFF; color: #0380E3;font-weight: 700;font-family: 'Nunito Sans';
                                    position: absolute;
                                    height: 40px;
                                    left: 13px;
                                    bottom: 32px;
                                    width: 226px;
                                    box-sizing: border-box;
                                    border: 1px solid #0380E3;
                                    padding: 8px 20px;"
                                 rendered="#{!LoginControle.concederMaisUmDia}"
                                 action="#{LoginControle.verificarDiasParaBloqueio}"/>
                <a href="${LoginControle.whatsFinanceiro}" target="_blank"
                   class="botaoPrimarioSmall texto-size-16-real botoesLink"
                   style="background-color: #FFFFFF;position: absolute; color: #28AB45;
                                width: 208px;
                                height: 40px;
                                left: 253px;
                                bottom: 32px; box-sizing: border-box;
                                padding: 8px 20px;border: 1px solid #28AB45;font-weight: 700;">
                Falar no WhatsApp <img src="imagens/icon_whats_white.png" style="width: 18px; height: 20px; vertical-align: middle;"/>
                </a>
                <a4j:commandLink
                        style="background-color: #0380E3;font-weight: 700;font-family: Arial, sans-serif;
                                position: absolute;
                                width: 276px;
                                height: 40px;
                                left: 469px;
                                bottom: 32px;
                                box-sizing: border-box;
                                padding: 8px 20px;"
                        styleClass="botaoPrimarioSmall texto-size-16-real botoesLink"
                        rendered="#{LoginControle.apresentarLinkBoletos}"
                        action="#{LoginControle.inicializarBoletos}">
                    Ver boletos no Canal do Cliente
                </a4j:commandLink>

            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelBloqueadoParcial" styleClass="novaModal" autosized="true" shadowOpacity="true" width="768"
                     height="450" showWhenRendered="#{LoginControle.abrirRichModalDiasParaBloqueio and LoginControle.concederMaisUmDia}">

        <f:facet name="header">
                               <h:outputText value="Bloqueio Total" escape="false"
                                  style="font-size: 32px; vertical-align: middle; font-weight: lighter"/>
        </f:facet>

        <h:form id="formBloqueadoParcial">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" style="margin-top: 10px">
                <h:panelGroup layout="block" style="text-align: center; margin: 20px 0;">
                    <img src="imagens/icon_bloqueio_total.png"
                         style="width: 103.25px; height: 106px; vertical-align: bottom;">
                </h:panelGroup>
                <h:panelGroup layout="block" style="text-align: center; margin: 20px 0;font-style: normal;
                            font-weight: 700;
                            font-size: 18px;
                            line-height: 21px;">
                    <h:outputText value="#{LoginControle.tituloMensagemBloqueio}" escape="false"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="padding: 10px 10px;">
                    <h:outputText escape="false"
                                  value="#{LoginControle.mensagemBloqueio}"
                                  style="font-weight: 400;font-size: 14px;line-height: 130%;"/>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block"
                          style="text-align: center; width: 100%; padding-bottom: 20px"
                          styleClass="colunaCentralizada">

                <a4j:commandLink
                        styleClass="botaoPrimarioSmall texto-size-16-real botoesLink"
                        value="Sair"
                        style="background-color:  #FFFFFF; color: #BA202F;font-weight: 700;font-family: Nunito sans-serif;
                                    position: absolute;width: 108px;
                                    height: 40px;
                                    left: 326px;
                                    bottom: 24px;
                                    box-sizing: border-box;
                                    border: 1px solid #BA202F"
                        action="#{LogoutControle.updateCookiesLogado}"
                        oncomplete="document.location.href = '#{LogoutControle.redirectLogout}'"
                        />

            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelDiasParaDesbloqueio" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     width="768"
                     height="500" showWhenRendered="#{LoginControle.abrirRichModalSolicitarDesbloqueio}">
        <f:facet name="header">

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText style="font-size: 32px;" value="Bloqueio total"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formDiasParaDesbloqueio">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" style="margin-top: 10px">
                <h:panelGroup layout="block" style="text-align: center; margin: 10px 0;">
                    <img src="imagens/icon_bloqueio_total.png"
                         style="width: 103.25px; height: 106px; vertical-align: bottom;">
                </h:panelGroup>
                <h:panelGroup layout="block" style="text-align: center; margin: 10px 0;font-style: normal;
                            font-weight: 700;
                            font-size: 18px;
                            line-height: 21px;">
                    <h:outputText value="#{LoginControle.tituloMensagemBloqueio}" escape="false"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="padding: 10px 10px;">
                    <h:outputText escape="false"
                                  value="#{LoginControle.mensagemBloqueio}"
                                  style="font-weight: 400;font-size: 14px;line-height: 130%;"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGroup layout="block"
                          style="padding-top: 5px; text-align: center; width: 100%; padding-bottom: 20px"
                          styleClass="colunaCentralizada">
                <h:outputLabel value="#{LoginControle.mensagemDetalhada}" style="color: red; font-size: 12px;"
                               id="mensagemValidacaoForm"/>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          style="text-align: center; width: 100%; padding-bottom: 20px"
                          styleClass="colunaCentralizada">

                <a href="${LoginControle.whatsFinanceiro}" target="_blank"
                   class="botaoPrimarioSmall texto-size-16-real botoesLink"
                   style="background-color: #FFFFFF;position: absolute; color: #28AB45;
                                width: 250px;
                                height: 40px;
                                left: 80px;
                                bottom: 32px; box-sizing: border-box;
                                padding: 8px 20px;border: 1px solid #28AB45;font-weight: 700;">
                    Falar com nossa equipe <img src="imagens/icon_whats_white.png" style=" height: 20px; vertical-align: middle;"/>
                </a>

                <a4j:commandLink
                        style="background-color: #0380E3;font-weight: 700;font-family: Arial, sans-serif;
                                position: absolute;
                                width: 120px;
                                height: 40px;
                                left: 354px;
                                bottom: 32px;
                                box-sizing: border-box;
                                padding: 8px 20px;"
                        styleClass="botaoPrimarioSmall texto-size-16-real botoesLink"
                        rendered="#{LoginControle.apresentarLinkBoletos}"
                        action="#{LoginControle.inicializarBoletos}">
                    Ver boletos
                </a4j:commandLink>

                <a4j:commandLink value="Sair do sistema"
                                 styleClass="botaoPrimarioSmall texto-size-16-real botoesLink"
                                 style="background-color: #BA202F;font-weight: 700;font-family: Arial, sans-serif;
                                        position: absolute;
                                        width: 150px;
                                        height: 40px;
                                        left: 497px;
                                        bottom: 32px;
                                        box-sizing: border-box;
                                        padding: 8px 20px;"
                                 action="#{LoginControle.verificarDiasParaBloqueio}"/>

            </h:panelGroup>
        </h:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelModulo" autosized="true" styleClass="novaModal"
                     shadowOpacity="true"
                     width="310" height="150"
                     showWhenRendered="#{LoginControle.abrirRichModalModulo}">
        <f:facet name="header">
            <h4>
                <h:outputText value="Bem Vindo" styleClass="text-muted">

                </h:outputText>

            </h4>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkPanelModulo"/>
                <rich:componentControl for="panelModulo" attachTo="hidelinkPanelModulo" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formModulo">
            <h:panelGrid columns="1" width="100%"
                         cellpadding="0" cellspacing="0"
                         style="padding: 20px 20px 20px;vertical-align:middle;"
                         columnClasses="colunaCentralizada,colunaCentralizada">
                <h:panelGroup style="margin-left: 20px; margin-top: auto;margin-right: auto">
                    <a4j:commandLink style="float: left;text-decoration: none "
                                     rendered="#{LoginControle.apresentarLinkZW}"
                                     action="#{LoginControle.abrirZillyonWeb}">
                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" styleClass="textGray">
                            <h:graphicImage id="linkAbrirZillyon" width="70px" value="imagens_flat/pct-icone-fundo-administrativo.svg"
                                            style="text-align: center;vertical-align:middle;"/>
                            <h:panelGroup layout="block" style="margin-top: 10px">
                                <a4j:commandLink styleClass="texto-size-14-real texto-cor-cinza" value="ZW"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </a4j:commandLink>

                    <a4j:commandLink style="margin-left: 20px; float:left; text-decoration: none"
                                     rendered="#{LoginControle.apresentarLinkFinanceiro}"
                                     action="#{LoginControle.abrirModuloFinanceiro}">

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" styleClass="textGray">
                            <h:graphicImage id="linkAbrirFinanceiro" width="70px" value="imagens_flat/pct-icone-fundo-financeiro.svg"
                                            style="text-align: center;vertical-align:middle;"/>
                            <h:panelGroup layout="block" style="margin-top: 10px">
                                <a4j:commandLink styleClass="texto-size-14-real texto-cor-cinza" value="Financeiro"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </a4j:commandLink>

                    <a4j:commandLink style="margin-left: 20px; float:left; text-decoration: none"
                                     rendered="#{LoginControle.apresentarLinkFinanceiroPropagandaFinanceiro}"
                                     onclick="abrirPopup('https://app.pactosolucoes.com.br/banner/popup_financeiro.jpg', 'Novo Módulo Financeiro', 760, 590);">
                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" styleClass="textGray">
                            <h:graphicImage id="linkAbrirPropagandaFinanceiro" width="70px" value="imagens_flat/pct-icone-fundo-financeiro.svg"
                                            style="text-align: center;vertical-align:middle;"/>
                            <h:panelGroup layout="block" style="margin-top: 10px">
                                <a4j:commandLink styleClass="texto-size-14-real texto-cor-cinza" value="Financeiro"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </a4j:commandLink>

                    <a4j:commandLink style="margin-left: 20px; float: left; text-decoration: none "
                                     rendered="#{LoginControle.apresentarLinkCRM}"
                                     action="#{LoginControle.abrirModuloCRM}">

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" styleClass="textGray">
                            <h:graphicImage id="linkAbrirCRM" width="70px" value="imagens_flat/pct-icone-fundo-crm.svg"
                                            style="text-align: center;vertical-align:middle;"/>
                            <h:panelGroup layout="block" style="margin-top: 10px">
                                <a4j:commandLink styleClass="texto-size-14-real texto-cor-cinza" value="CRM"/>
                            </h:panelGroup>

                        </h:panelGrid>
                    </a4j:commandLink>

                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>


    <h:form id="formLogin">

        <jsp:include page="include_head.jsp" flush="true"/>
        <c:choose>
            <c:when test="${InicioControle.NFe}">

                <div id="container-nfe">
                    <div class="login-esquerda">
                        <div class="logoNFeLogin">
                            <img src="./beta/imagens/pacto-nfe-topo.png" alt="logoNFe"/>
                        </div>
                    </div>
                    <div class="login-direita">
                        <div id="formLogin" style="position:absolute;">
                            <div class="tituloNFe">
                                Busque e Administre
                            </div>
                            <div class="tituloNFe">
                                suas <span class="enfaseTitulo">Notas Fiscais</span>
                            </div>
                            <div class="tituloNFe">
                                com facilidade
                            </div>

                            <h:inputHidden value="#{InicioControle.resetParam}"/>
                            <h:inputHidden value="#{InicioControle.logout}"/>

                            <div class="inputsLoginNFe">
                                <div class="inputLoginNFe">
                                    <h:inputText id="userKey"
                                                 onkeydown="if (event.keyCode == 13) document.getElementById('botaoLogar').click()"
                                                 size="30" maxlength="100"
                                                 style="background: white;
                                                 height:36px;
                                                 width: 320px;
                                                 color: black;
                                                 vertical-align: middle;"
                                                 value="#{InicioControle.userKey}"/>
                                </div>
                                <div class="inputLoginNFe">
                                    <h:inputText id="userName"
                                                 onkeydown="if (event.keyCode == 13) document.getElementById('botaoLogar').click()"
                                                 size="20" maxlength="100"
                                                 style="background: white;
                                                 height:36px;
                                                 width: 320px;
                                                 color: black;
                                                 vertical-align: middle;"
                                                 value="#{InicioControle.userName}"/>
                                </div>
                                <div class="inputLoginNFe">
                                    <h:inputSecret id="userPassword"
                                                   onkeydown="if (event.keyCode == 13) document.getElementById('botaoLogar').click()"
                                                   size="20" maxlength="64"
                                                   style="background: white;
                                                   height:36px;
                                                   width: 320px;
                                                   color: black;
                                                   vertical-align: middle;"
                                                   value="#{InicioControle.userPassword}"/>
                                </div>
                                <div style="float: right;">
                                    <div class="botaoLoginNFe">

                                        <h:commandLink id="descobrir"
                                                       style="margin:5px;vertical-align:middle;"
                                                       action="#{InicioControle.descobrir}">
                                            <div id="botaoLogar">
                                            </div>
                                        </h:commandLink>
                                    </div>
                                </div>
                                <c:if test="${!empty InicioControle.mensagemDetalhada}">
                                    <div style="width: 189px">
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                               style="margin-top: -4px; margin-left: -10px">
                                            <tr style="background: url('./images/nfe/warning-10.png') no-repeat; height: 12px">
                                                <td></td>
                                            </tr>
                                            <tr style="background: url('./images/nfe/warning-11.png') repeat-y; height: 2px">
                                                <td>
                                                    <div style="margin-left: 15px; margin-right: 15px">
                                                        <h:outputText id="mensagem" styleClass="mensagem"
                                                                      value="#{InicioControle.mensagem}"/>
                                                        <h:outputText id="mensagemDetalhada"
                                                                      styleClass="mensagemDetalhada"
                                                                      value="#{InicioControle.mensagemDetalhada}"/>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style="background: url('./images/nfe/warning-12.png') no-repeat; height: 18px">
                                                <td></td>
                                            </tr>
                                        </table>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                    </div>
                </div>
            </c:when>
            <c:otherwise>

                <div id="table">
                    <div id="cell">
                        <div id="conteudo">
                            <div id="login">
                                <div class="top"></div>
                                <div class="middle">

                                    <div style="text-align:center; margin-bottom: 8px">
                                        <img src="${InicioControle.imagemLogo}" alt="ZillyonWeb">
                                    </div>


                                    <table width="477" border="0" cellpadding="0" cellspacing="0"
                                           class="tabletext text">
                                        <tr>
                                            <td width="32" align="left" valign="top"><img style="margin-top:3px;"
                                                                                          src="images/tick.png" alt=""
                                                                                          width="16" height="13"></td>
                                            <td align="left" valign="top">Este &eacute; um sistema moderno e de alta
                                                tecnologia,
                                                resultado de mais de 17 anos de experi&ecirc;ncia em informatiza&ccedil;&atilde;o
                                                de academias e muito recomendado por importantes consultores da &aacute;rea
                                                fitness.
                                            </td>
                                        </tr>
                                    </table>
                                    <table width="477" border="0" cellpadding="0" cellspacing="0"
                                           class="tabletext text">
                                        <tr>
                                            <td width="32" align="left" valign="top"><img style="margin-top:3px;"
                                                                                          src="images/tick.png" alt=""
                                                                                          width="16" height="13"></td>
                                            <td align="left" valign="top">Destacando-se pelo alto grau de contentamento
                                                dos
                                                clientes, este sistema tem obtido grande sucesso!
                                            </td>
                                        </tr>
                                    </table>

                                    <div class="sep" style="margin-bottom:15px;"><img src="images/shim.gif"></div>

                                    <table width="477" border="0" cellpadding="1" cellspacing="5" class="text">
                                        <tr>
                                            <td align="right" valign="middle">Empresa:</td>
                                            <td align="left" valign="middle" style="display: inline">
                                                <h:selectOneMenu id="empresa" onblur="blurinput(this);"
                                                                 style="background: white;
                                                                 border: 1px solid #CCC;
                                                                 border-bottom-color: #999;
                                                                 border-right-color: #999;
                                                                 height:26px;
                                                                 color: black;
                                                                 margin: 5px;
                                                                 padding: 5px 8px 0 6px;
                                                                 padding-right: 5px;
                                                                 width:187px;
                                                                 vertical-align: middle;"
                                                                 onfocus="focusinput(this);"
                                                                 value="#{LoginControle.empresa.codigo}">
                                                    <f:selectItems value="#{LoginControle.listaSelectItemEmpresa}"/>
                                                </h:selectOneMenu>
                                            </td>
                                            <td align="left" valign="middle" style="display: contents">
                                                <a4j:commandLink action="#{InicioControle.limparChave}"
                                                                 styleClass="linkPadrao texto-size-18"
                                                                 id="btnLimparChaveDeslogado"
                                                                 title="Limpar chave"
                                                                 reRender="form">
                                                    <i class="fa-icon-eraser"></i>
                                                </a4j:commandLink>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width="166" align="right" valign="middle">Usuário:</td>
                                            <td align="left" valign="middle">


                                                <h:inputText id="username"
                                                             style="background: white;
                                                             border: 1px solid #CCC;
                                                             border-bottom-color: #999;
                                                             border-right-color: #999;
                                                             height:26px;
                                                             color: black;
                                                             margin: 5px;
                                                             width:187px;
                                                             padding: 5px 8px 0 6px;
                                                             padding-right: 5px;
                                                             vertical-align: middle;"
                                                             size="30" maxlength="30"
                                                             value="#{LoginControle.username}"/>


                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="right" valign="middle">Senha:</td>
                                            <td align="left" valign="middle">
                                                <h:inputSecret id="senha" size="17" maxlength="64"
                                                               style="background: white;
                                                               border: 1px solid #CCC;
                                                               border-bottom-color: #999;
                                                               border-right-color: #999;
                                                               height:26px;
                                                               color: black;
                                                               margin: 5px;
                                                               padding: 5px 8px 0 6px;
                                                               padding-right: 5px;
                                                               vertical-align: middle;"
                                                               value="#{LoginControle.senha}"/>

                                                <a4j:commandButton
                                                        id="login"
                                                        image="./imagens/botaoLogin1.png"
                                                        style="margin:5px;vertical-align:middle;"
                                                        type="submit"
                                                        action="#{LoginControle.login}">
                                                </a4j:commandButton>
                                            </td>
                                        </tr>

                                        <c:if test="${LoginControle.numDeTentativas >= CaptchaControle.numTentativasInvalidas}">
                                            <tr>
                                                <td align="right" valign="middle">
                                                    <a4j:commandLink value="Trocar imagem"
                                                                     reRender="formLogin:captcha"/>
                                                </td>
                                                <td valign="middle" style="padding-left: 6px">
                                                    <a4j:mediaOutput id="captcha"
                                                                     element="img" cacheable="false" session="true"
                                                                     createContent="#{CaptchaControle.paintCaptcha}"
                                                                     value="#{CaptchaControle.mediaData}"
                                                                     mimeType="image/jpeg"/>
                                                </td>
                                            </tr>

                                            <tr>
                                                <td align="right" valign="middle">
                                                    <h:outputText value="Inforque o código de verificação "/>
                                                </td>
                                                <td valign="middle" style="padding-left: 3px; padding-top: 3px">
                                                    <h:inputText size="20" maxlength="20"
                                                                 value="#{CaptchaControle.valor}"
                                                                 style="background: white;
                                                                 border: 1px solid #CCC;
                                                                 border-bottom-color: #999;
                                                                 border-right-color: #999;
                                                                 height:26px;
                                                                 color: black;
                                                                 margin: 3px;
                                                                 padding: 5px 8px 0 6px;
                                                                 padding-right: 5px;
                                                                 vertical-align: middle;"/>
                                                </td>
                                            </tr>
                                        </c:if>

                                        <tr>
                                            <td></td>
                                            <td align="center"><h:outputText id="mensagem" styleClass="mensagem"
                                                                             value="#{LoginControle.mensagem}"/></td>
                                        </tr>
                                        <tr>
                                            <td align="center" colspan="2"><h:outputText id="mensagemDetalhada"
                                                                             styleClass="mensagemDetalhada"
                                                                             value="#{LoginControle.mensagemDetalhada}"/></td>
                                        </tr>

                                    </table>
                                    <div class="sep" style="padding-bottom:15px;margin-top:10px">
                                        <img src="images/shim.gif">
                                    </div>

                                    <div class="textsmall" style="padding:10px 5px 7px 0;text-align:right;">
                                        <img border="0" style="margin-right:4px;" src="images/arrow.gif" alt=""
                                             width="4"
                                             height="7">
                                        <h:outputText styleClass="tituloCamposReduzidos"
                                                      value="#{SuperControle.versaoSistema}"
                                                      title="#{SuperControle.dataVersaoComoString}"/>
                                    </div>
                                </div>
                                <div class="bottom"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </c:otherwise>
        </c:choose>

    </h:form>

    <rich:modalPanel id="panelAtualizacaoBD" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500"
                     height="280"
                     showWhenRendered="#{LoginControle.exibirModalAtualizacaoBD}">
        <h:panelGrid style="margin-left: 160px">
            <f:facet name="header">

                <jsp:include page="topoReduzido.jsp"/>
            </f:facet>
        </h:panelGrid>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic['operacoes.atualizar.bd']}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>

                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelinkAtualizacaoBD"/>
                <rich:componentControl for="panelAtualizacaoBD" attachTo="hidelinkAtualizacaoBD" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formAtualizacaoBD" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada,textverysmall">
                <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font"
                              value="#{LoginControle.mensagemAtualizacaoBD}"/>
                <h:panelGroup style="margin-left: 20px; margin-top: auto;margin-right: auto">
                    <h:panelGrid columns="2" style="margin-top: 20px;margin-right: auto; margin-left: auto">
                        <a4j:commandLink id="atualizarBD" action="#{LoginControle.atualizarBD}" style="float:left "
                                         styleClass="botaoPrimario texto-size-16"
                                         title="#{msg_aplic['operacoes.atualizar.bd']}"
                                         value="#{msg_aplic['operacoes.atualizar']}"
                                         rendered="#{LoginControle.bancoDesatualizado}" reRender="scTableAtualizacao"/>

                        <a4j:commandLink id="btnEntrar" action="#{LoginControle.abrirModalModulos}" style="float: left;"
                                         styleClass="botaoSecundario texto-size-16"
                                         value="Entrar no Sistema"/>
                    </h:panelGrid>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelExibicaoAtualizacoesBD" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     width="900" height="480"
                     showWhenRendered="#{LoginControle.exibirModalExibicaoAtualizacoesBD}">
        <f:facet name="header">
            <%--<jsp:include page="topoReduzido.jsp"/>--%>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText style="margin-top: 10px" value="#{msg_aplic['operacoes.atualizar.bd.executado']}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <%--<h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkExibicaoAtualizacaoBD"/>--%>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelinkExibicaoAtualizacaoBD"/>
                <rich:componentControl for="panelExibicaoAtualizacoesBD" attachTo="hidelinkExibicaoAtualizacaoBD"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formExibicaoAtualizacoesBD" style="margin-top: 20px" styleClass="overflow-visible"
                  ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <%--<h:outputText id="msgBDAtualizacao" value="#{LoginControle.mensagemAtualizacaoBD}"/>--%>
                <h:dataTable id="tableAtualizacao" value="#{LoginControle.atualizacoesBD}" var="atualizacao"
                             width="100%"
                             rowClasses="tablelistras textsmall" columnClasses="col-text-align-left"
                             rows="5" styleClass="tabelaSimplesCustom" headerClass="col-text-align-left">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          value="#{msg_aplic['entidade.atualizacao.versao']}"/>
                        </f:facet>
                        <h:outputText
                                style="#{fn:containsIgnoreCase(atualizacao.mensagem, 'erro') ? 'color:F7ADA3' : 'color:A3AAF7'}"
                                value="#{atualizacao.versao}"/>
                    </h:column>
                    <h:column>

                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          style="margin-left: 50px" value="#{msg_aplic['entidade.atualizacao.data']}"/>
                        </f:facet>

                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                      value="#{atualizacao.dataFormatada}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          value="#{msg_aplic['entidade.atualizacao.resultado']}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                      value="#{atualizacao.resultado.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          value="#{msg_aplic['entidade.atualizacao.usuario']}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                      value="#{atualizacao.nomeUsuario}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          value="#{msg_aplic['entidade.atualizacao.descricao']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{LoginControle.exibirDescricaoAtualizacao}" value="Exibir"
                                           styleClass="botaoPrimario texto-size-10"
                                           reRender="panelDetalhesAtualizacoes"
                                           oncomplete="Richfaces.showModalPanel('panelDetalhesAtualizacoes');"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          value="#{msg_aplic['entidade.atualizacao.script']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{LoginControle.exibirScriptAtualizacao}" value="Exibir"
                                           styleClass="botaoPrimario texto-size-12"
                                           reRender="panelDetalhesAtualizacoes"
                                           oncomplete="Richfaces.showModalPanel('panelDetalhesAtualizacoes');"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          value="#{msg_aplic['entidade.atualizacao.mensagem']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{LoginControle.exibirMensagemAtualizacao}" value="Exibir"
                                           styleClass="botaoPrimario texto-size-12"
                                           reRender="panelDetalhesAtualizacoes"
                                           oncomplete="Richfaces.showModalPanel('panelDetalhesAtualizacoes');"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          value="#{msg_aplic['entidade.atualizacao.stackTrace']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{LoginControle.exibirStackTraceAtualizacao}" value="Exibir"
                                           styleClass="botaoPrimario texto-size-12"
                                           reRender="panelDetalhesAtualizacoes"
                                           oncomplete="Richfaces.showModalPanel('panelDetalhesAtualizacoes');"/>
                    </h:column>
                </h:dataTable>
                <%--<rich:datascroller align="center" for="formExibicaoAtualizacoesBD:tableAtualizacao"--%>
                <%--id="scTableAtualizacao" reRender="tableAtualizacao"/>--%>

                <rich:datascroller align="center" for="formExibicaoAtualizacoesBD:tableAtualizacao" maxPages="10"
                                   styleClass="scrollPureCustom" renderIfSinglePage="false" reRender="tableAtualizacao"
                                   id="scTableAtualizacao"/>
                <h:panelGroup layout="block" style="height: 100px; overflow: auto;">
                    <h:panelGroup style="margin-top: 5px">
                        <h:panelGrid
                                style="margin-top: 10px; margin-right: auto; margin-right: auto; margin-left: auto">
                            <h:outputText id="msgBDAtualizacao"
                                          styleClass="texto-size-16 texto-cor-cinza texto-font texto-bold"
                                          value="#{LoginControle.mensagemAtualizacaoBD}"/>
                        </h:panelGrid>
                        <h:panelGrid
                                style="margin-top: 10px; margin-right: auto; margin-right: auto; margin-left: auto">
                            <a4j:commandLink id="btnOk" action="#{LoginControle.abrirModalModulos}"
                                             styleClass="botaoPrimario texto-size-16-real " value="OK"/>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelDetalhesAtualizacoes" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     width="450" height="250">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic['operacoes.atualizar.bd.executado']}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <%--<h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkDetalhesAtualizacoes"/>--%>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelinkDetalhesAtualizacoes"/>
                <rich:componentControl for="panelDetalhesAtualizacoes" attachTo="hidelinkDetalhesAtualizacoes"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:panelGroup layout="block" style="height: 200px; overflow: auto;">
            <h:outputText value="#{LoginControle.detalheAtualizacao}" escape="false"/>
        </h:panelGroup>

    </rich:modalPanel>
    <rich:modalPanel id="panelBoletosInicio" styleClass="novaModal" autosized="true" shadowOpacity="true" width="1000"
                     showWhenRendered="#{LoginControle.abrirRichModalBoletosInicio}">
        <f:facet name="header">
            <h:panelGroup style="text-align: center">
                <h:outputText value="Boletos"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formBoletos">
            <h:panelGroup layout="block" id="panelBoletos">
                <h:panelGrid id="panelListboletos" columns="1" width="100%"
                             rendered="#{LoginControle.abrirRichModalBoletosInicio}">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <table width="100%" style="height: 550px" border="0" cellpadding="0" cellspacing="0">
                            <tr>
                                <td align="left" valign="top"
                                    style="padding-left: 5px; padding-right: 0px; overflow-y: auto;height: 100px;"
                                    class="scrollInfinito">
                                    <div id="divInfoFinan" class="painelMetadeTelaBoxCanalPacto"
                                         style="width: 96%; height: 21vh; margin-top: 20px!important;">
                                        <jsp:include page="include_minhacontapacto_infoFinan.jsp" flush="true"/>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center; margin-bottom: 50px;padding-top: 20px"
                                  styleClass="colunaCentralizada">
                        <a4j:commandLink value="OK" styleClass="botaoPrimario texto-size-16-real"
                                         action="#{LoginControle.abrirModalModulos}"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
</f:view>
<script type="text/javascript">
    document.getElementById("formLogin:empresa").focus();
</script>

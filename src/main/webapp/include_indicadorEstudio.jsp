<%--
    Document   : include_bi_indicerenovacao
    Created on : 03/02/2011, 16:32:31
    Author     : Waller
--%>
<%@include file="includes/imports.jsp" %>
<h:panelGrid id="estudioMetas" columns="1" rendered="#{LoginControle.apresentarLinkEstudio}" 
		     width="100%" style="clear:both;" cellpadding="0" cellspacing="0">
    <table id="estudioMetasTable" width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text">
        <tr>
            <td width="100%">
                <table id="teste3" width="95%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right: 0px; margin-bottom: 0px;">
                    <tr>
                        <td><h:panelGrid id="panelEstudioMetas" width="100%" cellpadding="0" cellspacing="0" columns="3" columnClasses="alinhamentoSuperior,tituloboxcentro2, tituloboxcentro2" style="margin-right:5px;margin-bottom:5px;">
                                <table width="100%" height="420" border="0" align="left" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50" /></td>
                                        <td width="100%" align="left" valign="top"
                                            background="images/box_centro_top.gif"
                                            class="tituloboxcentro"
                                            style="padding: 11px 0 0 0;">
                                            Indicador de Vendas - Agenda Est�dio
                                            <a href="${SuperControle.urlWikiCRM}Indicador_de_Vendas-Agenda_Est%C3%BAdio" class="linkWiki"  title="Clique e saiba mais: BI - Indicador de Vendas - Agenda Est�dio" target="_blank" >
                                                <img src="imagens/wiki_link2.gif" alt="Clique e saiba mais: Indicador de Vendas - Agenda Est�dio" class="linkWiki"/>
                                            </a>
                                            <rich:calendar id="dataBasePanelIndicadorEstudio" disabled="false" 
                                                           value="#{AberturaMetaControle.aberturaMetaVO.dia}" 
                                                           showInput="false" direction="bottom-left" zindex="1000" 
                                                           showWeeksBar="false">
                                                <a4j:support event="onchanged" 
                                                             action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorVenda}" 
                                                             ajaxSingle="true" reRender="panelGridIndicadorRetencao,panelGridIndicadorVendas, panelEstudioMetas" />
                                            </rich:calendar>

                                        </td>
                                        
                                        <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50" /></td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif" /></td>
                                        <td align="left" colspan="3" valign="top" bgcolor="#ffffff">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 10px;">
                                                <tr>
                                                    <td><h:panelGrid id="panelGridIndicadorEstudio" columnClasses="colunaCentralizada" columns="1" style="position:relative; top:0px;" width="100%">
                                                            <h:panelGrid columnClasses="colunaCentralizada" columns="1" width="100%">
                                                                <h:panelGroup layout="block" id="panelDataSelecionadaRotatividadeEstudio" style="text-align:right;">
                                                                    <h:outputText styleClass="textverysmall" value="Data base: " style="text-align:right;color:#0f4c6b;" />
                                                                    <h:outputText styleClass="textverysmall" value="#{AberturaMetaControle.aberturaMetaVO.dia}" style="text-align:right;color:#0f4c6b;">
                                                                        <f:convertDateTime type="date" dateStyle="short" locale="pt" timeZone="America/Sao_Paulo" pattern="dd/MM/yyyy" />
                                                                    </h:outputText>
                                                                </h:panelGroup>
                                                            </h:panelGrid>
<!-- INICIO--------------------------------------------------------------------- -->  
												<rich:dataTable id="itemsFecharMeta" width="100%" headerClass="consulta"
                                                                        styleClass="semBorda" style="border:none;" 
                                                                        value="#{AberturaMetaControle.aberturaMetaVO.fecharMetaVosEstudio}" 
                                                                         rendered="#{AberturaMetaControle.aberturaMetaVO.existeMetaParaParticipante}"
                                                                         var="fecharMeta">

                                                            <!-- INICIO - Icones -->
                                                            <rich:column  style="border:none;">
                                                                <h:graphicImage url="./imagensCRM/#{fecharMeta.fase.imagem}" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}"/>
                                                            </rich:column>
                                                            <!-- FIM - Icones -->

                                                            <!-- INICIO - Links para wiki e Label -->
                                                            <rich:column  style="border:none;">
                                                                <h:outputLink value="#{SuperControle.urlWikiCRM}#{fecharMeta.fase.urlWiki}"
                                                                              title="Clique e saiba mais: #{fecharMeta.fase.descricao}" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink styleClass="camposIndicadorVendas" 
                                                                                 value="#{fecharMeta.fase.descricao}" 
                                                                                 action="#{AberturaMetaControle.consultarFecharMetaDetalhadoGenerico}" 
                                                                                 oncomplete="abrirPopup('faces/metaDetalhadaForm.jsp', 'MetaForm', 1024, 700);"/>
                                                            </rich:column>
                                                            <!-- FIM - Links para wiki e Label -->

                                                            <!-- INICIO - Meta -->
                                                            <rich:column style="border:none;">
                                                                <h:outputText id="metaTotal"  styleClass="camposIndicadorVendasMetaCRM" value="#{fecharMeta.meta}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                            </rich:column>
                                                            <!-- FIM - Meta -->

                                                            <!-- INICIO - Resultado -->
                                                            <rich:column style="border:none;">
                                                                <h:outputText styleClass="camposIndicadorVendas" value="#{msg_aplic.prt_AberturaMeta_resultado}" />
                                                                <rich:spacer width="5px" />
                                                                <h:outputText id="metaAtingidaBaixa1"  style="color: red;"  rendered="#{fecharMeta.resultadoPercentualMetaAtingido <=40}" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                                <h:outputText id="metaAtingidaMedia1" style="color: #0078D0;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido > 41 && fecharMeta.resultadoPercentualMetaAtingido <= 79 }" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                                <h:outputText id="metaAtingidaAtingida1" style="color:#006633;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido > 80 && fecharMeta.resultadoPercentualMetaAtingido <= 100 }" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                            </rich:column>
                                                            <!-- FIM - Resultado -->

                                                            <!-- INICIO - Porcentagem -->
                                                            <rich:column  style="border:none;">
                                                                <h:outputText  title="Meta atingida + Repescagem"  style="color: red;font-weight: bold;"  rendered="#{fecharMeta.resultadoPercentualMetaAtingido <=40}"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                                <h:outputText  title="Meta atingida + Repescagem" style="color: #0078D0;font-weight: bold;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido > 41 && fecharMeta.resultadoPercentualMetaAtingido <= 79 }"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                                <h:outputText title="Meta atingida + Repescagem" style="color:#006633;font-weight: bold;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido > 80 && fecharMeta.resultadoPercentualMetaAtingido <= 100 }"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                            </rich:column>
                                                            <rich:column  style="border:none;">
                                                                <h:outputText id="porcetagemMeta1" styleClass="camposIndicadorVendas" value="#{msg_aplic.prt_AberturaMeta_metaPorcentagem}" />
                                                            </rich:column>
                                                            <!-- FIM - Porcentagem -->
                                                            
                                                            <rich:column style="border:none;" colspan="6" breakBefore="true">
                                                                <div class="sep" style=""><img src="images/shim.gif"></div>
                                                                </rich:column>

                                                            

                                                        </rich:dataTable>

                                                          
                                                            

                                                            <h:panelGrid columnClasses="colunaEsquerda" columns="1" 
                                                                         rendered="#{!empty AberturaMetaControle.aberturaMetaVO.grupoColaboradorListaEstudio  && AberturaMetaControle.mostrarGruposColaboradoresEstudio}" width="100%">
                                                                <rich:dataTable id="itemsEstudio" width="100%" headerClass="consulta" 
                                                                columnClasses="colunaEsquerda" styleClass="semBorda" 
                                                                value="#{AberturaMetaControle.aberturaMetaVO.grupoColaboradorListaEstudio}" 
                                                                var="grupoColaborador">
                                                                    <f:facet name="header">
                                                                        <h:panelGroup layout="block">
                                                                            <h:panelGroup layout="block" style="float: left">
                                                                                <h:panelGroup rendered="#{!AberturaMetaControle.mostrarGruposEstudio}" layout="block">
                                                                                    <i class="fa-icon-angle-right"></i>
                                                                                </h:panelGroup>
                                                                                <h:panelGroup rendered="#{AberturaMetaControle.mostrarGruposEstudio}" layout="block">
                                                                                    <i class="fa-icon-angle-down"></i>
                                                                                </h:panelGroup>
                                                                                <a4j:commandLink styleClass="titulo3"
                                                                                                 action="#{AberturaMetaControle.toggleMostrarGruposEstudio}"
                                                                                                 reRender="panelEstudioMetas">
                                                                                    <h:outputText styleClass="tituloCamposAberturaMeta" value="#{msg_aplic.prt_AberturaMeta_descricaoGrupos}" />
                                                                                    <h:outputText styleClass="tituloCamposAberturaMeta" value="#{AberturaMetaControle.aberturaMetaVO.nomeGrupoColaboradorEstudio}" />
                                                                                </a4j:commandLink>
                                                                            </h:panelGroup>
                                                                            <h:panelGroup style="float: right"
                                                                                          layout="block">
                                                                                <h:selectBooleanCheckbox
                                                                                        id="selectUsuarioLogadoPendencia"
                                                                                        rendered="#{AberturaMetaControle.mostrarCheckboxEstudio}"
                                                                                        value="#{AberturaMetaControle.marcarUsuarioEstudio}">
                                                                                    <a4j:support event="onclick" action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorEstudio}" reRender="panelEstudioMetas"/>
                                                                                </h:selectBooleanCheckbox>
                                                                                <h:outputText styleClass="titulo3"
                                                                                              value="#{AberturaMetaControle.usuarioLogado.nomeAbreviado}"/>
                                                                            </h:panelGroup>
                                                                        </h:panelGroup>
                                                                    </f:facet>

                                                                    <rich:column rendered="#{AberturaMetaControle.mostrarGruposEstudio}">
                                                                        <a4j:commandButton styleClass="botoes" action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorEstudio}" image="./imagensCRM/botaoAdicionarGrupos.png" reRender="panelEstudioMetas" />
                                                                        <rich:spacer width="5px;" />
                                                                        <a4j:commandLink styleClass="botoes" title="Visualizar Participantes do Grupo" reRender="panelEstudioMetas" action="#{AberturaMetaControle.selecionarGrupoColaboradorParticipanteEstudio}">
                                                                            <h:outputText styleClass="tituloCamposAberturaMeta" value="#{grupoColaborador.descricao}" />
                                                                        </a4j:commandLink>
                                                                        <rich:dataGrid value="#{grupoColaborador.grupoColaboradorParticipanteVOs}"
                                                                        var="grupoColaboradorParticipante" width="100%"
                                                                        columns="3" columnClasses="semBorda" styleClass="semBorda" rendered="#{!empty grupoColaborador.grupoColaboradorParticipanteVOs}" cellpadding="0" cellspacing="0">
                                                                            <h:panelGrid columns="2" width="100%" cellpadding="0" cellspacing="0">
                                                                                <h:selectBooleanCheckbox value="#{grupoColaboradorParticipante.grupoColaboradorParticipanteEscolhido}">
                                                                                    <a4j:support event="onclick" action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorEstudio}" reRender="panelEstudioMetas " />
                                                                                </h:selectBooleanCheckbox>
                                                                                <h:outputText styleClass="titulo3" value="#{grupoColaboradorParticipante.usuarioParticipante.primeiroNomeConcatenado}" />
                                                                            </h:panelGrid>
                                                                        </rich:dataGrid>
                                                                    </rich:column>
                                                                </rich:dataTable>
                                                            </h:panelGrid>
                                                                    <h:panelGrid id="panelAtualizarListaEstudio" columnClasses="colunaCentralizada" columns="1" width="100%" rendered="#{!empty AberturaMetaControle.aberturaMetaVO.fecharMetaVosEstudio}">
                                                                <a4j:commandButton styleClass="botoes" action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorEstudio}" image="./imagensCRM/atualizar.png" 
                                                                					id="atualizarBIEstudio" reRender="panelGridIndicadorVendas, panelIndicadorDeRetencao, panelEstudioMetas">
                                                                    <rich:toolTip followMouse="true" direction="top-right" style="width:350px; height:80px; " showDelay="500">
                                                                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_descricaoBotaoAtualizar}" />
                                                                    </rich:toolTip>
                                                                </a4j:commandButton>
                                                            </h:panelGrid>
                                                                    <h:panelGrid columns="1" width="100%" rendered="#{!empty AberturaMetaControle.aberturaMetaVO.fecharMetaVosEstudio}">
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="tituloCampos" value="Colaborador Respons�vel: " />
                                                                    <rich:spacer width="10px" />
                                                                    <h:outputText styleClass="tituloCampos" value="#{AberturaMetaControle.aberturaMetaVO.colaboradorResponsavel.nome}" />
                                                                </h:panelGroup>
                                                                <h:panelGroup>
                                                                    <h:outputText styleClass="tituloCampos" value="Respons�vel Cadastro:" />
                                                                    <rich:spacer width="10px" />
                                                                    <h:outputText styleClass="tituloCampos" value="#{AberturaMetaControle.aberturaMetaVO.responsavelCadastro.nome}" />
                                                                </h:panelGroup>
                                                            </h:panelGrid>
                                                            <h:panelGrid id="panelMensagemEstudio" columns="1" width="100%">
                                                                <h:outputText styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
                                                                <h:outputText styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
                                                            </h:panelGrid>


                                                        </h:panelGrid></td>
                                                </tr>
                                            </table>

                                        </td>

                                        <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif" /></td>
                                    </tr>
                                    <tr>
                                        <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                        <td align="left" colspan="3" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif" /></td>
                                        <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20" /></td>
                                    </tr>
                                </table>
                            </h:panelGrid></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</h:panelGrid>

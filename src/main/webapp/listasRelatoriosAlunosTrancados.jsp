<%-- 
    Document   : listasRelatoriosAlunosTrancados
    Created on : 27/06/2013, 14:23:42
    Author     : <PERSON><PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<jsp:include page="include_head.jsp" flush="true"/>

<style type="text/css">
    .rich-stglpanel-header {
        color: #0f4c6b;
    }

    .rich-stglpanel-header {
        background-color: #ACBECE;
        border-color: #ACBECE;
        font-size: 12px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemRelatorios" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Clientes Trancados" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}onde-consigo-gerar-um-relatorio-que-apresente-o-total-de-clientes-que-estao-trancados/"
                                                      title="Clique e saiba mais: Relatório Clientes Trancados"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup  layout="block" styleClass="margin-box">

                                        <h:outputText styleClass="text" value="Filtros"
                                                      style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
                                        <h:panelGroup rendered="#{LRAlunosTrancadosControle.permissaoConsultaTodasEmpresas}"
                                                      style="padding-left: 10px;padding-top: 15px;padding-bottom: 15px;border: 1px solid #ACBECE;"
                                                      layout="block">
                                            <h:outputText style="margin-left: 7px" styleClass="text" value="Empresas: "/>
                                            <h:selectOneMenu value="#{LRAlunosTrancadosControle.filtroEmpresa}">
                                                <f:selectItems  value="#{LRAlunosTrancadosControle.listaEmpresas}" />
                                                <a4j:support event="onchange"
                                                             action="#{LRAlunosTrancadosControle.recarregarTela}"
                                                             reRender="consultores, professores"/>
                                            </h:selectOneMenu>
                                        </h:panelGroup>
                                    <rich:simpleTogglePanel switchType="client" label="Consultores"
                                                            style="margin-top: 8px" opened="true"
                                                            onexpand="false">
                                        <h:panelGroup id="consultores">
                                            <rich:dataGrid id="grupoConsultoresRelTrancado"
                                                    value="#{LRAlunosTrancadosControle.consultores}"
                                                    var="consultor" width="100%" columns="4">
                                                <h:selectBooleanCheckbox id="selectConsultorRelTrancados"
                                                        value="#{consultor.colaboradorEscolhido}"/>
                                                <h:outputText value="#{consultor.pessoa.nome}"/>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <%-- ------------------------------- ABA PROFESSORES   ------------------------------------ --%>
                                    <rich:simpleTogglePanel  style="margin-bottom: 10px;" switchType="client" label="Professores"
                                                            opened="false" onexpand="true">
                                        <h:panelGroup id="professores">
                                            <rich:dataGrid value="#{LRAlunosTrancadosControle.professores}" id="grupoProfessoresTrancados"
                                                           var="professor" width="100%" columns="4">
                                                <h:selectBooleanCheckbox
                                                        value="#{professor.colaboradorEscolhido}" id="selectProfTrancados"/>
                                                <h:outputText value="#{professor.pessoa.nome} "/>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>

                                    <a4j:commandLink styleClass="botoes nvoBt" id="btnConsultarTrancados"
                                                     style="margin-left: 0px"
                                                     action="#{LRAlunosTrancadosControle.consultarAlunosTrancados}"
                                                     oncomplete="#{LRAlunosTrancadosControle.mensagemNotificar}"
                                                     reRender="groupResultados,imprimir">
                                        Consultar&nbsp<i class="fa-icon-search"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink style="margin-left:5px;margin-top: 8px; padding:5px 7px;" id="limparFiltrosRelTrancados"
                                                     action="#{LRAlunosTrancadosControle.limparFiltros}"
                                                     styleClass="botoes nvoBt btSec"
                                                     reRender="form">
                                        Limpar filtros&nbsp <i class="fa-icon-eraser"></i>
                                    </a4j:commandLink>

                                    <h:panelGroup id="imprimir">
                                        <a4j:commandLink id="exportarExcel" style="margin-left:5px;"
                                                           actionListener="#{ExportadorListaControle.exportar}"
                                                           rendered="#{not empty LRAlunosTrancadosControle.resultado}"
                                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                           accesskey="2" styleClass="botoes linkPadrao">
                                            <f:attribute name="lista"
                                                         value="#{LRAlunosTrancadosControle.resultado}"/>
                                            <f:attribute name="tipo" value="xls"/>
                                            <f:attribute name="itemExportacao" value="relTrancados"/>
                                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,telefone=Telefones,email=E-mail,dataOperacaoApresentar=Lançamento Operação,dataInicioOperacaoApresentar=Inicio Trancamento,dataFimOperacaoApresentar=Fim Trancamento,justificativa=Justificativa,observacao=Observação,dataNascimentoApresentar=Nascimento
                                                                     ,situacaoClienteApresentar=Situação,sexo=Sexo Biológico,dataMatriculaApresentar=Data Matrícula,dataInicioPlanoApresentar=Início Plano
                                                                     ,dataVencimentoPlanoApresentar=Venc. Plano,dataUltimoAcessoApresentar=Últ.Acesso,plano=Plano,dataCadastroApresentar=Data Cadastro
                                                                     ,consultor=Consultor/Professor,estadoCivil=Estado Civil,profissao=Profissão,nomeEmpresa=Empresa"/>
                                            <f:attribute name="prefixo" value="ClientesTrancados"/>
                                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                        </a4j:commandLink>
                                        <a4j:commandLink style="margin-left:5px;margin-top: 8px;"
                                                           rendered="#{not empty LRAlunosTrancadosControle.resultado}"
                                                           reRender="relatorioImprimir"
                                                           action="#{LRAlunosTrancadosControle.prepararImpr}"
                                                           oncomplete="Richfaces.showModalPanel('relatorioImprimir');" styleClass="linkPadrao">
                                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                        </a4j:commandLink>

                                    </h:panelGroup>
                                    <%---------------------------------TABELA DO RELATORIO---------------------------------------%>
                                    <h:panelGroup id="groupResultados">
                                        <rich:dataTable width="100%"
                                                        value="#{LRAlunosTrancadosControle.resultado}"
                                                        id="resultados"
                                                        var="item"
                                                        rows="30"
                                                        style="margin-top: 8px"
                                                        rendered="#{not empty LRAlunosTrancadosControle.resultado}">

                                            <rich:column id="matricula" sortBy="#{item.matricula}">
                                                <f:facet name="header">
                                                    <h:outputText value="Matricula"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.matricula}"
                                                        actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                        action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="nome" sortBy="#{item.nome}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nome"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nome}"
                                                        actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                        action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="telefone" sortBy="#{item.telefone}">
                                                <f:facet name="header">
                                                    <h:outputText value="Telefone"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.telefone}"
                                                        actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                        action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <%--<rich:column id="email" sortBy="#{item.email}">--%>
                                            <%--<f:facet name="header">--%>
                                            <%--<h:outputText value="E-mail"/>--%>
                                            <%--</f:facet>--%>
                                            <%--<a4j:commandLink--%>
                                            <%--value="#{item.email}"--%>
                                            <%--actionListener="#{LRAlunosTrancadosControle.prepareEditar}"--%>
                                            <%--action="#{LRAlunosTrancadosControle.irParaTelaCliente}"--%>
                                            <%--oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>--%>
                                            <%--</rich:column>--%>

                                            <rich:column id="dataCadastro" sortBy="#{item.dataCadastro}">
                                                <f:facet name="header">
                                                    <h:outputText value="Cadastro"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataCadastroApresentar}"
                                                        actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                        action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataNascimento"
                                                         sortBy="#{item.dataNascimento}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nascimento"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataNascimentoApresentar}"
                                                        actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                        action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="dataUltimoAcesso"
                                                         sortBy="#{item.dataUltimoAcesso}">
                                                <f:facet name="header">
                                                    <h:outputText value="Último Acesso"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataUltimoAcessoApresentar}"
                                                        actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                        action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="dataOperacao" sortBy="#{item.dataOperacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Lançamento Operação"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataOperacaoApresentar}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataInicioOperacao"
                                                         sortBy="#{item.dataInicioOperacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Inicio Trancamento"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataInicioOperacaoApresentar}"
                                                        actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                        action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataFimOperacao"
                                                         sortBy="#{item.dataFimOperacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Fim Trancamento"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataFimOperacaoApresentar}"
                                                        actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                        action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="justificativa" sortBy="#{item.justificativa}">
                                                <f:facet name="header">
                                                    <h:outputText value="Justificativa"/>
                                                </f:facet>
                                                <a4j:commandLink value="#{item.justificativa}"
                                                                 actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                                 action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                                 oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="observacao" sortBy="#{item.observacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Observação"/>
                                                </f:facet>
                                                <a4j:commandLink value="#{item.observacao}"
                                                                 actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                                 action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                                 oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="empresa" sortBy="#{item.nomeEmpresa}" rendered="#{LRAlunosTrancadosControle.permissaoConsultaTodasEmpresas}">
                                                <f:facet name="header">
                                                    <h:outputText value="Empresa"/>
                                                </f:facet>
                                                <a4j:commandLink value="#{item.nomeEmpresa}"
                                                                 actionListener="#{LRAlunosTrancadosControle.prepareEditar}"
                                                                 action="#{LRAlunosTrancadosControle.irParaTelaCliente}"
                                                                 oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                        </rich:dataTable>
                                        <%--<table align="right" border="0" cellspacing="0" cellpadding="0">--%>
                                        <%--<tr>--%>
                                        <%--<td align="center" valign="middle"><h5>--%>
                                        <%--<h:outputText rendered="#{not empty LRAlunosTrancadosControle.resultado}" value=" [Itens:#{LRAlunosTrancadosControle.totalItens}]"> </h:outputText>--%>
                                        <%--</h5>   </td>--%>
                                        <%--</tr>--%>
                                        <%--</table>--%>
                                        <rich:datascroller for="resultados"
                                                           rendered="#{not empty LRAlunosTrancadosControle.resultado}"
                                                           id="scrollResultados"></rich:datascroller>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true" />

        </h:panelGroup>

    </h:form>

    <%------------------------- MODAL 'IMPRIMIR' ----------------------------------------%>
    <rich:modalPanel id="relatorioImprimir" autosized="true" shadowOpacity="true" width="350">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados da Impressão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="imprimir"/>
                <rich:componentControl for="relatorioImprimir" attachTo="imprimir" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>

            <h:outputText styleClass="text" style="font-weight: bold"
                          value="Por Favor, selecione as informações que deseja imprimir:"/><br/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosTrancadosControle.apresentarLinha1}"/>
            <h:outputText styleClass="text" value="Dados Cadastrais"/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosTrancadosControle.apresentarLinha3}"/>
            <h:outputText styleClass="text" value="Informações de Plano"/><br/><br/>
            <center>
                <a4j:commandButton id="imprimirPDF" ajaxSingle="false"
                                   action="#{LRAlunosTrancadosControle.imprimirListaRelatorio}"
                                   value="Imprimir"
                                   reRender="mensagem"
                                   image="../imagens/imprimirContrato.png"
                                   oncomplete="#{LRAlunosTrancadosControle.mensagemNotificar}#{LRAlunosTrancadosControle.msgAlert}"
                                   accesskey="2" styleClass="botoes"/>
            </center>
        </a4j:form>
    </rich:modalPanel>

    <%------------------------- MODAL 'CONSULTAR' ----------------------------------------%>
    <rich:modalPanel id="listasRelatorios" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Relátorio de Alunos Cancelados"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="relatorios"/>
                <rich:componentControl for="listasRelatorios" attachTo="relatorios" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <jsp:include page="topoReduzido.jsp"/>
        <br/>
        <a4j:form>
            <h:panelGrid columns="2" columnClasses="colunaDireita, colunaEsquerda">

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.nome}" value="Nome: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.nome}"
                              value="#{LRAlunosTrancadosControle.itemRelatorio.nome}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.sexo}" value="Sexo Biológico: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.sexo}"
                              value="#{LRAlunosTrancadosControle.itemRelatorio.sexo}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.dataMatriculaApresentar}"
                              value="Data Matrícula: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.dataMatriculaApresentar}"
                              value="#{LRAlunosTrancadosControle.itemRelatorio.dataMatriculaApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.dataInicioPlanoApresentar}"
                              value="Inicio Plano: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.dataInicioPlanoApresentar}"
                              value="#{LRAlunosTrancadosControle.itemRelatorio.dataInicioPlanoApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.dataVencimentoPlanoApresentar}"
                              value="Vencimento Plano: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.dataVencimentoPlanoApresentar}"
                              value="#{LRAlunosTrancadosControle.itemRelatorio.dataVencimentoPlanoApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.plano}" value="Plano: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.plano}"
                              value="#{LRAlunosTrancadosControle.itemRelatorio.plano}"/>

                <h:outputText styleClass="text" style="font-weight: bold;"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.consultores}"
                              value="Consultor: "/>
                <h:panelGroup>
                    <c:forEach items="${LRAlunosTrancadosControle.itemRelatorio.consultores}" var="consultor">
                        <span rendered="${not empty LRAlunosTrancadosControle.itemRelatorio.consultores}"
                              class="text">${consultor.pessoa.nome}</span><br/>
                    </c:forEach>
                </h:panelGroup>

                <h:outputText styleClass="text" style="font-weight: bold;"
                              rendered="#{not empty LRAlunosTrancadosControle.itemRelatorio.professores}"
                              value="Professor: "/>
                <h:panelGroup>
                    <c:forEach items="${LRAlunosTrancadosControle.itemRelatorio.professores}" var="professor">
                        <span rendered="${not empty LRAlunosTrancadosControle.itemRelatorio.professores}"
                              class="text">${professor.pessoa.nome}</span><br/>
                    </c:forEach>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>

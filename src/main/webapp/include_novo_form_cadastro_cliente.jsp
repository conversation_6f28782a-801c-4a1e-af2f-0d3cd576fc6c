<%-- 
    Created on : 08/05/2012, 16:56:25
    Author     : Waller
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/imports.jsp" %>
<script type="text/javascript" src="script/jquery.maskedinput-1.2.2.js"></script>
<input type="hidden" name="modulo" value="${modulo}"/>
<script type="text/javascript" language="javascript">
  function buildZebraTable(tableId) {
    var table=document.getElementById(tableId);
    if(!table){return};

    // get all <tr> table elements
    var trows=table.getElementsByTagName('tr');
    //table.className = 'tablelistras textsmall';
    for(var j = 0; j < trows.length; j++){
      // assign CSS class to even and odd rows
      trows[j].className = j % 2 == 0 ? 'par' : '';
      if (j % 2 == 0){
        for (var k = 0; k < trows[j].childNodes.length; k ++){
          trows[j].childNodes[k].className = 'par';
        }

      }
    }
  }

  function validar(){

    if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
            && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
            && document.getElementById('formConsultarCEP:bairroCEP').value == ""
            && document.getElementById('formConsultarCEP:logradouroCEP').value == ""){

      alert("Ao menos um parâmetro deve ser informado!");
      return false;
    }
    return true;
  }

  // run 'buildZebraTable()' function when web page is loaded
  window.onload=function zebra() {
    buildZebraTable('tabela1');
    buildZebraTable('tabela2');
    buildZebraTable('tabela3');
  }
</script>
<h:panelGroup layout="block" style="width: 100%">
      <!-- inicio botões -->
      <h:panelGroup id="botesSuperior" style="padding-bottom: 10px;height: 50px;line-height: 50px;" layout="block">
        <h:panelGroup layout="block" style="width: 250px;height: 100%;text-align: right;display: inline-block;">
          <a4j:commandLink id="salvar"
                           value="Confirmar"
                           styleClass="botaoPrimario texto-size-16 linkPadrao"
                           action="#{ClienteControle.novoQuestionario}"
                           rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                           oncomplete="#{ClienteControle.mensagemNotificar}"
                           title="#{msg.msg_gravar_dados}" accesskey="2">
          </a4j:commandLink>
        </h:panelGroup>
        <rich:spacer width="5px"/>
        <h:panelGroup layout="block" style="width: 250px;height: 100%;text-align: left;display: inline-block;">
          <a4j:commandLink id="cancelar"
                           value="Cancelar"
                           styleClass="botaoSecundario texto-size-16 linkPadrao"
                           action="cancelar"
                           rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                           title="Cancelar inclusão de um Cliente">
          </a4j:commandLink>
        </h:panelGroup>
      </h:panelGroup>
      <!-- fim botões -->
      <jsp:include page="include_form_novo_cliente.jsp" flush="true"/>
      <!-- inicio botões -->
      <h:panelGroup id="botesInferior" style="padding-bottom: 10px;height: 50px;line-height: 50px;" layout="block">
        <h:panelGroup layout="block" style="width: 250px;height: 100%;text-align: right;display: inline-block;">
          <a4j:commandLink id="salvar1"
                           value="Confirmar"
                           styleClass="botaoPrimario texto-size-16 linkPadrao"
                           action="#{ClienteControle.novoQuestionario}"
                           rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                           oncomplete="#{ClienteControle.mensagemNotificar}"
                           title="#{msg.msg_gravar_dados}" accesskey="2">
          </a4j:commandLink>
        </h:panelGroup>
        <rich:spacer width="5px"/>
        <h:panelGroup layout="block" style="width: 250px;height: 100%;text-align: left;display: inline-block;">
          <a4j:commandLink id="cancelar1"
                           value="Cancelar"
                           styleClass="botaoSecundario texto-size-16 linkPadrao"
                           action="cancelar"
                           rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                           title="Cancelar inclusão de um Cliente">
          </a4j:commandLink>
        </h:panelGroup>
      </h:panelGroup>
      <!-- fim botões -->
</h:panelGroup>
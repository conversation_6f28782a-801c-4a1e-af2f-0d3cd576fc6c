<%--
    Document   : include_bi_indicerenovacao
    Created on : 03/02/2011, 16:32:31
    Author     : Waller
--%>
<%@include file="includes/imports.jsp" %>
<h:panelGrid id="indVendas" columns="1" rendered="#{LoginControle.permissaoAcessoMenuVO.indicadorVenda}"
              width="100%" style="clear:both;" cellpadding="0" cellspacing="0">
    <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text">
        <tr>
            <td width="100%">
                <table width="95%"  border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:5px;margin-bottom:5px;">
                    <tr>
                        <td>
                            <h:panelGrid  id="panelGridIndicadorVendas"  width="100%" cellpadding="0" cellspacing="0" columns="3" columnClasses="alinhamentoSuperior, tituloboxcentro2, tituloboxcentro2" style="margin-right:5px;margin-bottom:5px;">
                                <table  id="tabMeta" width="100%" height="420" border="0" align="left" cellpadding="0" cellspacing="0" class="text" >
                                    <tr>
                                        <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"></td>
                                        <td align="left" valign="top" class="tituloboxcentro" background="images/box_centro_top.gif" style="padding:11px 0 0 0;">
                                            Relacionamento de Vendas
                                            <rich:calendar
                                                id="dataBasePanelIndicadorDeRetencao"
                                                disabled="false"
                                                value="#{AberturaMetaControle.aberturaMetaVO.dia}"
                                                showInput="false" direction="bottom-left"
                                                zindex="1000" showWeeksBar="false">
                                                <a4j:support event="onchanged" action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorVenda}"
                                                             ajaxSingle="true"
                                                             reRender="panelGridIndicadorVendas, panelIndicadorDeRetencao, estudioMetas" />
                                            </rich:calendar>
                                        </td>

                                        <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"></td>
                                    </tr>
                                    <tr >
                                        <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif"></td>
                                        <td colspan="3" align="left" valign="top" bgcolor="#ffffff" style="padding:0 0 0 0;">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom:5px;">
                                                <tr>
                                                    <td>
                                                        <h:panelGrid style="background-color:white;" columnClasses="colunaCentralizada" columns="1" width="100%">
                                                            <h:panelGroup layout="block" id="panelDataSelecionadaRotatividade" style="text-align:right;">

                                                                <h:outputText styleClass="textverysmall"
                                                                              value="Meta aberta"
                                                                              rendered="#{AberturaMetaControle.aberturaMetaVO.existeMetaParaParticipante
                                                                                          && AberturaMetaControle.aberturaMetaVO.metaEmAberto}" style="text-align:right;color:#0f4c6b;" />&nbsp;
                                                                <h:outputText styleClass="textverysmall"
                                                                              value="Meta fechada."
                                                                              rendered="#{AberturaMetaControle.aberturaMetaVO.existeMetaParaParticipante
                                                                                          && !AberturaMetaControle.aberturaMetaVO.metaEmAberto}" style="text-align:right;color:#0f4c6b;" />&nbsp;
                                                                <h:outputText styleClass="textverysmall" value="Data base: " style="text-align:right;color:#0f4c6b;" />
                                                                <h:outputText styleClass="textverysmall" value="#{AberturaMetaControle.aberturaMetaVO.dia}" style="text-align:right;color:#0f4c6b;">
                                                                    <f:convertDateTime type="date" dateStyle="short" locale="pt" timeZone="America/Sao_Paulo" pattern="dd/MM/yyyy" />
                                                                </h:outputText>
                                                            </h:panelGroup>
                                                        </h:panelGrid>
                                                    </td>
                                                </tr>
                                            </table>
                                            <table  width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td bgcolor="#ffffff" align="left" valign="middle">
                                                        <rich:dataTable id="itemsFecharMeta" width="100%" headerClass="consulta" columnClasses="w5,w30,w5,w5,w20,w20,w10,w5"
                                                                        styleClass="semBorda" style="border:none;" rendered="#{AberturaMetaControle.aberturaMetaVO.existeMetaParaParticipante}"
                                                                        value="#{AberturaMetaControle.aberturaMetaVO.fecharMetaVosVendaApresentar}" var="fecharMeta">

                                                            <!-- INICIO - Icones -->
                                                            <rich:column  style="border:none;">
                                                                <h:graphicImage url="./imagensCRM/vinteQuatroHoras.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemVinteQuatroHoras}"/>
                                                                <h:graphicImage url="./imagensCRM/ex-alunos.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemExAlunos}"  />
                                                                <h:graphicImage url="./imagensCRM/agendamentos_hoje.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemAgendamentos}"  />
                                                                <h:graphicImage url="./imagensCRM/visitantes_antigos.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemVisitantesAntigos}"/>
                                                                <h:graphicImage url="./imagensCRM/renovacoes.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemRenovacoes}"/>
                                                                <h:graphicImage url="./imagensCRM/icone_faturamento.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemFaturamento}"/>
                                                                <h:graphicImage url="./imagensCRM/icone_vendas.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemQtdVenda}" />
                                                                <h:graphicImage url="./imagensCRM/indicacoes.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemIndicacoes}" />
                                                                <h:graphicImage url="./imagensCRM/conversao_agendados.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemConversaoAgendados}" width="21px" height="21px"/>
                                                                <h:graphicImage url="./imagensCRM/conversao_agendados.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemConversaoExAlunos}" width="21px" height="21px"/>
                                                                <h:graphicImage url="./imagensCRM/ligacao_amanha.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemLigacaoAgendadosAmanha}"/>
                                                                <h:graphicImage url="./imagensCRM/perdas.png" title="#{AberturaMetaControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes  == true ? 'Realizar qualquer contato' : fecharMeta.fase.objetivo}" rendered="#{fecharMeta.apresentarImagemPerdas}"/>
                                                            </rich:column>
                                                            <!-- FIM - Icones -->

                                                            <!-- INICIO - Links para wiki e Label -->
                                                            <rich:column style="border:none;">
                                                                <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                              rendered="#{fecharMeta.apresentarImagemVinteQuatroHoras}" title="Clique e saiba mais: 24 Horas" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="link24hs"  style="font-weight: bold;" styleClass="camposIndicadorVendasCRM"
                                                                                 rendered="#{fecharMeta.apresentarImagemVinteQuatroHoras}"
                                                                                 value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                 action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}"
                                                                                 oncomplete="#{AberturaMetaControle.validarAbrirPopUpVinteQuatroHoras}"/>

                                                                <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                              rendered="#{fecharMeta.apresentarImagemExAlunos}" title="Clique e saiba mais: Ex-Alunos" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkExAlunos" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM"
                                                                                 value="#{fecharMeta.fase.descricao}"
                                                                                 rendered="#{fecharMeta.apresentarImagemExAlunos}"
                                                                                 action="#{AberturaMetaControle.consultarFecharMetaDetalhadoGenerico}"
                                                                                 oncomplete="abrirPopup('faces/metaDetalhadaForm.jsp', 'MetaForm', 1024, 700);"/>


                                                                <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                              rendered="#{fecharMeta.apresentarImagemAgendamentos}" title="Clique e saiba mais: Agendamentos" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkAgendamentos" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM" rendered="#{fecharMeta.apresentarImagemAgendamentos}" value="#{fecharMeta.identificadorMeta_Apresentar}" action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}" oncomplete="#{AberturaMetaControle.validarAbrirPopUpAgendados}"/>


                                                                <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                              rendered="#{fecharMeta.apresentarImagemRenovacoes}" title="Clique e saiba mais: Renova��o" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkRenovacao" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM" rendered="#{fecharMeta.apresentarImagemRenovacoes}" value="#{fecharMeta.identificadorMeta_Apresentar}" action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}" oncomplete="#{AberturaMetaControle.validarAbrirPopUpRenovacao}" />



                                                                <h:outputLink value="#{SuperControle.urlWikiCRM}#{fecharMeta.fase.urlWiki}"
                                                                        rendered="#{fecharMeta.apresentarImagemVisitantesAntigos}"
                                                                        title="Clique e saiba mais: #{fecharMeta.fase.descricao}" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkVisitantesAntigos" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM"
                                                                                 value="#{fecharMeta.fase.descricao}"
                                                                                 rendered="#{fecharMeta.apresentarImagemVisitantesAntigos}"
                                                                                 action="#{AberturaMetaControle.consultarFecharMetaDetalhadoGenerico}"
                                                                                 oncomplete="abrirPopup('faces/metaDetalhadaForm.jsp', 'MetaForm', 1024, 700);"/>

                                                                <h:outputLink value="#{SuperControle.urlWikiCRM}Indicador_de_Vendas:Faturamento"
                                                                              rendered="#{fecharMeta.apresentarImagemFaturamento}" title="Clique e saiba mais: Faturamento" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkFaturamento"  style="font-weight: bold;" styleClass="camposIndicadorVendasCRM" rendered="#{fecharMeta.apresentarImagemFaturamento}" value="#{fecharMeta.identificadorMeta_Apresentar}"  action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}" oncomplete="#{AberturaMetaControle.validarAbrirPopUpFaturamento}" />

                                                                <h:outputLink value="#{SuperControle.urlWikiCRM}Indicador_de_Vendas:Quantidade_de_Vendas"
                                                                              rendered="#{fecharMeta.apresentarImagemQtdVenda}" title="Clique e saiba mais: Quantidade de Vendas" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink  id="linkQuantVendas" style="font-weight: bold;"  styleClass="camposIndicadorVendasCRM" rendered="#{fecharMeta.apresentarImagemQtdVenda}"
                                                                                  value="#{fecharMeta.identificadorMeta_Apresentar}" 
                                                                                  action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}"
                                                                                  oncomplete="#{AberturaMetaControle.validarAbrirPopUpVendas}" />

                                                                <h:outputLink value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                                                                              rendered="#{fecharMeta.apresentarImagemIndicacoes}" title="Clique e saiba mais: Indica��o" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkIndicacoes" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM" rendered="#{fecharMeta.apresentarImagemIndicacoes}" value="#{fecharMeta.identificadorMeta_Apresentar}" action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}" oncomplete="#{AberturaMetaControle.validarAbrirPopUpIndicado}" />

                                                                <h:outputLink value="#{SuperControle.urlWikiCRM}Indicador_de_Vendas:Passivo"
                                                                              rendered="#{fecharMeta.apresentarImagemClientePotencial}" title="Clique e saiba mais: Passivo" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkPassivo" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM" rendered="#{fecharMeta.apresentarImagemClientePotencial}" value="#{fecharMeta.identificadorMeta_Apresentar}"  action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}" oncomplete="#{AberturaMetaControle.validarAbrirPopUpPassivo}" />

                                                                <h:outputLink value="#{SuperControle.urlWikiCRM}Indicador_de_Vendas:Convers�o_Agendados"
                                                                              rendered="#{fecharMeta.apresentarImagemConversaoAgendados}" title="Clique e saiba mais: Convers�o de Agendados" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkConversaoAgendados" style="font-weight: bold;"
                                                                                 styleClass="camposIndicadorVendasCRM"
                                                                                 rendered="#{fecharMeta.apresentarImagemConversaoAgendados}"
                                                                                 value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                 action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}"
                                                                                 oncomplete="#{AberturaMetaControle.validarAbrirPopUpConversaoAgendados}"/>

                                                                <h:outputLink value="#{SuperControle.urlWikiCRM}Indicador_de_Vendas:Convers�o_Ex_Alunos"
                                                                              rendered="#{fecharMeta.apresentarImagemConversaoAgendados}" title="Clique e saiba mais: Convers�o de Ex-Alunos" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink styleClass="camposIndicadorVendasCRM" style="font-weight: bold;"
                                                                                 value="#{fecharMeta.fase.descricao}"
                                                                                 rendered="#{fecharMeta.apresentarImagemConversaoExAlunos}"
                                                                                 action="#{AberturaMetaControle.consultarFecharMetaDetalhadoGenerico}"
                                                                                 oncomplete="abrirPopup('faces/metaDetalhadaForm.jsp', 'MetaForm', 1024, 700);"/>

                                                                <h:outputLink
                                                                        value="#{SuperControle.urlWikiCRM}Indicador_de_Vendas:"
                                                                        rendered="#{fecharMeta.apresentarImagemLigacaoAgendadosAmanha}"
                                                                        title="Clique e saiba mais: Liga��o para Agendados de Amanh�"
                                                                        target="_blank">
                                                                    <h:graphicImage styleClass="linkWiki"
                                                                                    url="imagens/wiki_link2.gif"/>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkLigacaoAgendadosAmanha" style="font-weight: bold;"
                                                                                 styleClass="camposIndicadorVendasCRM"
                                                                                 rendered="#{fecharMeta.apresentarImagemLigacaoAgendadosAmanha}"
                                                                                 value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                 action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}"
                                                                                 oncomplete="#{AberturaMetaControle.validarAbrirPopUpLigacaoAgendadosAmanha}"/>
                                                                <a4j:commandLink
                                                                        value=" (#{fecharMeta.contLigacao} liga��o agendada)"
                                                                        style="font-weight:bold;"
                                                                        actionListener="#{AberturaMetaControle.filtrarMetaDetalhadaVenda}"
                                                                        title="Liga��es n�o s�o computadas na meta, mas aparecem na listagem de agendamentos do dia."
                                                                        rendered="#{fecharMeta.apresentarImagemAgendamentos && fecharMeta.contLigacao == 1 }"
                                                                        oncomplete="#{AberturaMetaControle.validarAbrirPopUpAgendados}">
                                                                    <f:attribute name="identificador" value="LIG"/>
                                                                </a4j:commandLink>
                                                                <a4j:commandLink
                                                                        value=" (#{fecharMeta.contLigacao} liga��es agendadas)"
                                                                        style="font-weight:bold;"
                                                                        actionListener="#{AberturaMetaControle.filtrarMetaDetalhadaVenda}"
                                                                        title="Liga��es n�o s�o computadas na meta, mas aparecem na listagem de agendamentos do dia."
                                                                        rendered="#{fecharMeta.apresentarImagemAgendamentos && fecharMeta.contLigacao > 1 }"
                                                                        oncomplete="#{AberturaMetaControle.validarAbrirPopUpAgendados}">
                                                                    <f:attribute name="identificador" value="LIG"/>
                                                                </a4j:commandLink>

                                                                <h:outputLink value="#{SuperControle.urlWikiCRM}Indicador_de_Reten��o:Perdas"
                                                                              rendered="#{fecharMeta.apresentarImagemPerdas}"
                                                                              title="Clique e saiba mais: Perdas" target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a4j:commandLink id="linkPerdas" style="font-weight: bold;" styleClass="camposIndicadorVendasCRM"
                                                                                 title="Perdas" rendered="#{fecharMeta.apresentarImagemPerdas}"
                                                                                 value="#{fecharMeta.identificadorMeta_Apresentar}"
                                                                                 action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVenda}"
                                                                                 oncomplete="#{AberturaMetaControle.validarAbrirPopUpPerda}" />
                                                            </rich:column>
                                                            <!-- FIM - Links para wiki e Label -->

                                                            <!-- INICIO - Meta -->
                                                            <rich:column style="border:none;" rendered="#{!fecharMeta.apresentarImagemFaturamento}">
                                                                <h:outputText id="metaTotal"  styleClass="camposIndicadorVendasMetaCRM" value="#{fecharMeta.meta}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                            </rich:column>

                                                            <rich:column style="border:none;" rendered="#{fecharMeta.apresentarImagemFaturamento}">
                                                                <h:outputText id="metaTotal1" styleClass="camposIndicadorVendasMetaCRM" value="#{fecharMeta.meta}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                            </rich:column>
                                                            <!-- FIM - Meta -->

                                                            <!-- INICIO - Resultado -->
                                                            <rich:column style="border:none;" rendered="#{!fecharMeta.apresentarImagemFaturamento}">
                                                                <h:outputText styleClass="camposIndicadorVendasCRM" value="#{msg_aplic.prt_AberturaMeta_resultado}" />
                                                                <rich:spacer width="5px" />
                                                                <h:outputText id="metaAtingidaBaixa"  style="color: red;"  rendered="#{fecharMeta.resultadoPercentualMetaAtingido <=40}" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                                <h:outputText id="metaAtingidaMedia" style="color: #0078D0;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 41 && fecharMeta.resultadoPercentualMetaAtingido <= 79 }" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                                <h:outputText id="metaAtingidaAtingida" style="color:#006633;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 80}" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                                <h:outputText rendered="#{fecharMeta.repescagem > 0}" styleClass="camposIndicadorVendasMetaAtingidaCRM" value=" - "/>
                                                                <h:outputText title="Repescagem" rendered="#{fecharMeta.repescagem > 0}" id="metaRepescagem" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.repescagem}">
                                                                      <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                            </rich:column>
                                                            <rich:column style="border:none;" rendered="#{fecharMeta.apresentarImagemFaturamento}">
                                                                <h:outputText styleClass="camposIndicadorVendasCRM" value="#{msg_aplic.prt_AberturaMeta_resultado}" />
                                                                <rich:spacer width="5px" />
                                                                <h:outputText id="metaAtingidaBaixa1"  style="color: red;"  rendered="#{fecharMeta.resultadoPercentualMetaAtingido <=40}" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                <f:convertNumber pattern="0" />
                                                            </h:outputText>
                                                                <h:outputText id="metaAtingidaMedia1" style="color: #0078D0;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 41 && fecharMeta.resultadoPercentualMetaAtingido <= 79 }" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                                <h:outputText id="metaAtingidaAtingida1" style="color:#006633;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 80}" title="Meta Atingida" styleClass="camposIndicadorVendasMetaAtingidaCRM" value="#{fecharMeta.metaAtingida}">
                                                                    <f:convertNumber pattern="0" />
                                                                </h:outputText>
                                                            </rich:column>
                                                            <!-- FIM - Resultado -->

                                                            <!-- INICIO - Porcentagem -->
                                                            <rich:column  style="border:none;">
                                                                 <h:outputText  title="Meta atingida + Repescagem"  style="color: red;font-weight: bold;"  rendered="#{fecharMeta.resultadoPercentualMetaAtingido <=40}"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                                <h:outputText  title="Meta atingida + Repescagem" style="color: #0078D0;font-weight: bold;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 41 && fecharMeta.resultadoPercentualMetaAtingido <= 79 }"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                                <h:outputText title="Meta atingida + Repescagem" style="color:#006633;font-weight: bold;" rendered="#{fecharMeta.resultadoPercentualMetaAtingido >= 80}"  styleClass="camposIndicadorVendasMetaPorcentagemCRM" value="#{fecharMeta.porcentagem}">
                                                                    <f:converter converterId="FormatadorNumerico" />
                                                                </h:outputText>
                                                            </rich:column>
                                                            <rich:column  style="border:none;">
                                                                <h:outputText id="porcetagemMeta1" styleClass="camposIndicadorVendasCRM" value="#{msg_aplic.prt_AberturaMeta_metaPorcentagem}" />
                                                            </rich:column>
                                                            <!-- FIM - Porcentagem -->
                                                            <!-- --------------------------------------- DETALHAMENTOS ------------------------------------- -->
                                                            <!-- --------------------------------------- AGENDADOS INICIO ------------------------------------- -->
                                                            <rich:column style="border:none;" colspan="6"
                                                                         breakBefore="true"
                                                                         rendered="#{fecharMeta.apresentarImagemAgendamentos && fecharMeta.metaCalculada}">

                                                                <rich:spacer width="2px;"/>
                                                                <h:panelGroup>
                                                                    <h:outputText value=" Sendo: "/>
                                                                    <a4j:commandLink id="agenComparecimento"
                                                                                     value="#{fecharMeta.contAgendaComparecimento}"
                                                                                     actionListener="#{AberturaMetaControle.filtrarMetaDetalhadaVenda}"
                                                                                     oncomplete="#{AberturaMetaControle.validarAbrirPopUpAgendados}">
                                                                        <f:attribute name="identificador" value="COM"/>
                                                                    </a4j:commandLink>
                                                                    <h:outputText value=" comparecimentos ("/>
                                                                    <a4j:commandLink id="agenFechamentos"
                                                                                     value="#{fecharMeta.contFechamentos}"
                                                                                     actionListener="#{AberturaMetaControle.filtrarMetaDetalhadaVenda}"
                                                                                     oncomplete="#{AberturaMetaControle.validarAbrirPopUpAgendados}">
                                                                        <f:attribute name="identificador" value="FEC"/>
                                                                    </a4j:commandLink>
                                                                    <h:outputText value=" fechamentos) e "/>
                                                                    <a4j:commandLink id="agenReagendados"
                                                                                     value="#{fecharMeta.contReAgendados}"
                                                                                     actionListener="#{AberturaMetaControle.filtrarMetaDetalhadaVenda}"
                                                                                     oncomplete="#{AberturaMetaControle.validarAbrirPopUpAgendados}">
                                                                        <f:attribute name="identificador" value="REA"/>
                                                                    </a4j:commandLink>
                                                                    <h:outputText value=" reagendados. "/>
                                                                </h:panelGroup>
                                                                <div class="sep" style=""><img src="images/shim.gif">
                                                                </div>
                                                            </rich:column>
                                                            <!-- --------------------------------------- POS VENDA INICIO ------------------------------------- -->
                                                            <rich:column style="border:none;" colspan="6"
                                                                         breakBefore="true"
                                                                         rendered="#{fecharMeta.apresentarImagemPosVenda  && fecharMeta.metaCalculada}">

                                                                <rich:spacer width="2px;"/>
                                                                <h:panelGroup
                                                                        rendered="#{fecharMeta.apresentarMensagemRetroativo}">

                                                                    <h:outputText value="#{msg.msg_meta_retroativa}"
                                                                                  styleClass="titulo5"/>
                                                                </h:panelGroup>
                                                                <div class="sep" style=""><img src="images/shim.gif">
                                                                </div>
                                                            </rich:column>

                                                            <rich:column rendered="#{!fecharMeta.apresentarImagemAgendamentos && !fecharMeta.apresentarImagemPosVenda && fecharMeta.metaCalculada}" style="border:none;" colspan="6" breakBefore="true">
                                                                <div class="sep" style=""><img src="images/shim.gif"></div>
                                                                </rich:column>

                                                            <rich:column id="gridMetaNaoCalculada" style="border:none;" colspan="6" breakBefore="true" rendered="#{!fecharMeta.metaCalculada}">

                                                                <rich:spacer width="2px;" />
                                                                <h:outputText value="#{msg.msg_meta_nao_calculada}" styleClass="metaNaoCalculada" />
                                                                <div class="sep" style=""><img src="images/shim.gif"></div>

                                                                <rich:toolTip for="gridMetaNaoCalculada" followMouse="true" direction="top-right" style="width:300px; height:90px; " showDelay="200">
                                                                    <h:outputText styleClass="tituloCampos"
                                                                                  value="#{msg.msg_tip_meta_nao_calculada}" />
                                                                </rich:toolTip>
                                                            </rich:column>

                                                        </rich:dataTable>
                                                    </td>

                                                </tr>

                                                <tr>
                                                    <td class="colunaCentralizada">

                                                        <!-- INICIO - Descri��o dos Grupos -->
                                                        <h:panelGrid id="panelListaParticipante" style="background-color:white;" 
                                                                     columnClasses="colunaEsquerda" columns="1"

                                                                     width="100%">

                                                            <rich:dataTable id="items"
                                                                            rendered="#{!empty AberturaMetaControle.aberturaMetaVO.grupoColaboradorListaVenda && AberturaMetaControle.mostrarGruposColaboradoresVenda}"
                                                                            width="100%" headerClass="consulta" columnClasses="colunaEsquerda" styleClass="semBorda"
                                                                            value="#{AberturaMetaControle.aberturaMetaVO.grupoColaboradorListaVenda}" var="grupoColaborador">

                                                                <f:facet name="header">
                                                                    <h:panelGroup layout="block">
                                                                        <h:panelGroup layout="block" style="float: left">
                                                                            <h:panelGroup rendered="#{!AberturaMetaControle.mostrarGruposVendas}" layout="block">
                                                                                <i class="fa-icon-angle-right"></i>
                                                                            </h:panelGroup>
                                                                            <h:panelGroup rendered="#{AberturaMetaControle.mostrarGruposVendas}" layout="block">
                                                                                <i class="fa-icon-angle-down"></i>
                                                                            </h:panelGroup>
                                                                            <a4j:commandLink styleClass="titulo3"
                                                                                             action="#{AberturaMetaControle.toggleMostrarGruposVendas}"
                                                                                             reRender="indVendas">
                                                                                <h:outputText styleClass="tituloCamposAberturaMeta" value="#{msg_aplic.prt_AberturaMeta_descricaoGrupos}" />
                                                                                <h:outputText styleClass="tituloCamposAberturaMeta" value="#{AberturaMetaControle.aberturaMetaVO.nomeGrupoColaboradorVenda}" />
                                                                            </a4j:commandLink>
                                                                        </h:panelGroup>
                                                                        <h:panelGroup style="float: right"
                                                                                      layout="block">
                                                                            <h:selectBooleanCheckbox
                                                                                    id="selectUsuarioLogadoVendas"
                                                                                    rendered="#{AberturaMetaControle.mostrarCheckboxVendas}"
                                                                                    value="#{AberturaMetaControle.marcarUsuarioVendas}">
                                                                                <a4j:support event="onclick" action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorVenda}"
                                                                                             reRender="indVendas"/>
                                                                            </h:selectBooleanCheckbox>
                                                                            <h:outputText styleClass="titulo3"
                                                                                          value="#{AberturaMetaControle.usuarioLogado.nomeAbreviado}"/>
                                                                        </h:panelGroup>
                                                                    </h:panelGroup>
                                                                </f:facet>

                                                                <rich:column rendered="#{AberturaMetaControle.mostrarGruposVendas}">
                                                                    <a4j:commandButton id="visualizarTodosBotao" styleClass="botoes" title="Visualizar Participantes do Grupo" action="#{AberturaMetaControle.selecionarGrupoColaboradorParticipanteVenda}" image="./imagensCRM/botaoAdicionarGrupos.png" reRender="panelListaParticipante" />
                                                                    <rich:spacer width="5px;" />
                                                                    <a4j:commandLink id="visualizarTodos" styleClass="botoes" title="Visualizar Participantes do Grupo" reRender="panelListaParticipante" action="#{AberturaMetaControle.selecionarGrupoColaboradorParticipanteVenda}">
                                                                        <h:outputText id="nomeGrupo" styleClass="tituloCamposAberturaMeta" value="#{grupoColaborador.descricao}" />
                                                                    </a4j:commandLink>
                                                                    <!-- INICIO - Lista de colaboradores do grupo -->
                                                                    <rich:dataGrid id="gridParticipantes"  value="#{grupoColaborador.grupoColaboradorParticipanteVOs}" var="grupoColaboradorParticipante" width="100%" columns="3"
                                                                                   elements="#{grupoColaborador.totalParticipantes}" columnClasses="semBorda" styleClass="semBorda"
                                                                                   rendered="#{!empty grupoColaborador.grupoColaboradorParticipanteVOs}" cellpadding="0" cellspacing="0">

                                                                        <h:panelGrid columns="2" width="100%" cellpadding="0" cellspacing="0">
                                                                            <h:selectBooleanCheckbox disabled="#{grupoColaboradorParticipante.colaboradorIndisponivel}" title="#{grupoColaboradorParticipante.colaboradorIndisponivel  == true ? 'Colaborador desabilitado por estar indispon�vel!' : ''}" id="selectParticipante" value="#{grupoColaboradorParticipante.grupoColaboradorParticipanteEscolhido}">
                                                                                <a4j:support event="onclick" action="#{AberturaMetaControle.selecionarMetaColaboradorVenda}" reRender="panelGridIndicadorVendas"/>
                                                                            </h:selectBooleanCheckbox>
                                                                            <h:outputText id="participanteNome"  title="#{grupoColaboradorParticipante.colaboradorIndisponivel  == true ? 'Colaborador desabilitado por estar indispon�vel!' : ''}" styleClass="titulo3" value="#{grupoColaboradorParticipante.usuarioParticipante.primeiroNomeConcatenado}" />
                                                                        </h:panelGrid>

                                                                    </rich:dataGrid>
                                                                    <!-- FIM - Lista de colaboradores do grupo -->
                                                                </rich:column>

                                                            </rich:dataTable>

                                                            <h:panelGrid width="100%" columns="2" columnClasses="colunaCentralizada" id="panelAtualizarLista"
                                                                         rendered="#{!empty AberturaMetaControle.aberturaMetaVO.fecharMetaVosVenda}">
                                                                <a4j:commandButton styleClass="botoes" id="atualizarBIVenda"
                                                                                   action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorVenda}"
                                                                                   image="./imagensCRM/atualizar.png" reRender="panelGridIndicadorVendas, panelIndicadorDeRetencao, estudioMetas">
                                                                    <rich:toolTip followMouse="true" direction="top-right" style="width:350px; height:80px; " showDelay="500">
                                                                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_descricaoBotaoAtualizar}" />
                                                                    </rich:toolTip>
                                                                </a4j:commandButton>
                                                                <!-- BOTAO DE IMPRESSAO  -->

                                                                <a4j:commandButton id="imprimirBIVenda" rendered="#{AberturaMetaControle.aberturaMetaVO.existeMetaParaParticipante
                                                                                                                    && !AberturaMetaControle.aberturaMetaVO.metaEmAberto}"
                                                                                   action="#{AberturaMetaControle.prepararImpressao}"
                                                                                   styleClass="botoes"  image="./images/imprimir.png" reRender="panelGridIndicadorVendas"
                                                                                   oncomplete="abrirPopup('fechamentoDiaAberturaMetaCons.jsp', 'FechamentoDiaAberturaMetaCons', 780, 595);">
                                                                </a4j:commandButton>


                                                            </h:panelGrid>

                                                            <h:panelGrid id="gridResponsavel" style="background-color:white;" columns="1" width="100%" rendered="#{!empty AberturaMetaControle.aberturaMetaVO.fecharMetaVosVenda}">
                                                                <h:panelGroup layout="block">
                                                                    <h:outputText styleClass="tituloCampos" value="Colaborador Respons�vel: " />
                                                                    <rich:spacer width="10px" />
                                                                    <h:outputText id="responsavelNome" styleClass="tituloCampos" value="#{AberturaMetaControle.aberturaMetaVO.colaboradorResponsavel.nome}" />
                                                                </h:panelGroup>
                                                                <h:panelGroup layout="block">
                                                                    <h:outputText styleClass="tituloCampos" value="Respons�vel Cadastro:" />
                                                                    <rich:spacer width="10px" />
                                                                    <h:outputText id="RespCadastroNome" styleClass="tituloCampos" value="#{AberturaMetaControle.aberturaMetaVO.responsavelCadastro.nome}" />
                                                                </h:panelGroup>

                                                                <h:panelGrid columns="1">
                                                                    <h:outputText id="msgBIVendas" styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
                                                                    <h:outputText id="msgBIVendasDet" styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
                                                                </h:panelGrid>
                                                            </h:panelGrid>
                                                        </h:panelGrid>
                                                    </td>
                                                </tr>                                                
                                            </table>
                                        </td>
                                        <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>
                                    </tr>
                                    <tr>
                                        <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                        <td align="left" colspan="3" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                        <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                    </tr>
                                </table>
                            </h:panelGrid>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</h:panelGrid>
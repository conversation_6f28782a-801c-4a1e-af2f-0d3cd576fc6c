<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 02/02/2016
  Time: 09:46
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="H" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="ui" uri="http://richfaces.org/a4j" %>

<link href="${root}/css_pacto.css" rel="stylesheet" type="text/css"/>
<rich:modalPanel domElementAttachment="parent" id="panelRenovarSessao" styleClass="novaModal"
                 width="550"
                 onshow="setTotalProgressiveBar();"
                 autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Para sua segurança, sua sessão finalizará em.."/>
        </h:panelGroup>

    </f:facet>
    <h:panelGroup layout="block">
        <h:panelGroup id="regressiveBar" layout="block" styleClass="bg-vermelho progressiveBar"/>
        <h:panelGroup layout="block" id="tblRenovarSessao" style="text-align: center;margin-top: 15px;">

            <h:outputText id="regressiveTime" styleClass="texto-cor-vermelho texto-size-60-real texto-font" value=""/>
            <h:panelGroup layout="block" styleClass="container-botoes"
                          style="text-align: right;border-top: 1px solid #E5E5E5;margin-top: 25px;line-height: 40px;height: 29px;">
                <a4j:commandLink status="statusHora"
                                 title="Clique aqui apra renovar sua sessão"
                                 reRender="horaSistema"
                                 action="#{SuporteControle.poll}"
                                 styleClass="linkPadrao texto-size-16-real texto-cor-azul rotate-hover"
                                 oncomplete="resetTime(tempoEmMillis);#{SuperControle.enableSetLastActionTime ? 'setLastActionTime();' : ''}Richfaces.hideModalPanel('panelRenovarSessao');">
                    <i class="fa-icon-refresh"></i> Retornar minha sessão

                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</rich:modalPanel>
<h:panelGroup styleClass="container-navbar" layout="block">
    <h:panelGroup layout="block" styleClass="navbar navbar-default">
        <h:panelGroup layout="block" styleClass="container-fluid" id="navBarTopo">
            <h:panelGroup layout="block" styleClass="nav navbar-nav">
            </h:panelGroup>
            <script>
                jQuery(window).scroll(function () {
                    topAtual = jQuery(window).scrollTop();
                    if (topAtual > 82) {
                        jQuery('.navbar').addClass('barraFixa');
                    } else if (topAtual <= 82) {
                        jQuery('.navbar').removeClass('barraFixa');
                    }

                });
            </script>
            <jsp:include page="include_menu_campoBusca.jsp" flush="true"/>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
<jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
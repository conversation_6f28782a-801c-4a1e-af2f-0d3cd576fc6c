<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>

<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<style type="text/css">
    .rich-tabpanel-content {
        border-right: none;
        border-left: none;
        border-bottom: none;
    }

    .button, input, select, textarea {
        font-size: 11px;
    }
</style>

<%@include file="includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>

    <title>
        <h:outputText value="#{msg_aplic.prt_PerfilAcesso_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_PerfilAcesso_tituloForm}"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="topoReduzido_material.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="pages/ce/includes/topoReduzido.jsp"/>
            </c:if>

        </f:facet>
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" switchType="ajax">
                        <rich:tab id="abaPerfilAcesso" label="Perfil de Acesso" switchType="client">
                            <h:panelGrid columns="1" width="100%" rowClasses="linhaPar, linhaImpar">
                                <h:panelGroup>
                                    <table width="100%">
                                        <tr>
                                            <td width="10%"></td>
                                            <td width="50%">
                                                <h:panelGroup>
                                                    <h:panelGrid columns="2" styleClass="font-size-Em">
                                                        <h:outputText styleClass="text"
                                                                      value="#{msg_aplic.prt_PerfilAcesso_nome} "/>
                                                        <h:inputText id="nome" size="50" maxlength="50"
                                                                     onblur="blurinput(this);"
                                                                     onfocus="focusinput(this);" styleClass="form"
                                                                     value="#{PerfilAcessoControle.perfilAcessoVO.nome}"/>
                                                        <h:outputText styleClass="text" value="Tipo: "
                                                                      id="outputTipos"/>
                                                        <h:selectOneMenu
                                                                value="#{PerfilAcessoControle.perfilAcessoVO.tipo}"
                                                                styleClass="form"
                                                                id="selectTipos">
                                                            <f:selectItem itemValue="" itemLabel=""/>
                                                            <f:selectItems value="#{PerfilAcessoControle.tipos}"/>
                                                        </h:selectOneMenu>
                                                        <rich:toolTip for="outputTipos">
                                                            <h:outputText escape="false"
                                                                          value=""/>
                                                        </rich:toolTip>
                                                        <rich:toolTip for="selectTipos">
                                                            <h:outputText escape="false"
                                                                          value="O tipo do Perfil de Acesso faz parte do novo recurso de <b>Assistente de Gestão</b>. <br/>O <b>Assistente de Gestão</b> do ZillyonWeb exibirá dicas direcionadas a cada tipo específico de usuário. <br/> Um gestor, por exemplo, receberá dicas de como melhorar seus indicadores de gestão, enquanto um consultor verá dicas relacionadas a vendas, CRM, etc. Lembre-se que ao alterar este tipo, você altera de todos os usuários que tem este Perfil de Acesso."/>
                                                        </rich:toolTip>
                                                    </h:panelGrid>

                                                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                                                </h:panelGroup>
                                            </td>
                                            <td width="40%">
                                                <a4j:commandLink value="#{msg_aplic.prt_PerfilAcesso_copiarPermissoes}"
                                                                 oncomplete="Richfaces.showModalPanel('panelConsultaPerfilAcesso')">
                                                </a4j:commandLink>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td><a4j:commandLink value="#{msg_aplic.prt_PerfilAcesso_expandirModulos}"
                                                                 action="#{PerfilAcessoControle.expandirModulos}"
                                                                 reRender="corpo, centralEventosCadastro, centralEventosFuncionalidades">
                                            </a4j:commandLink>&nbsp;
                                                <a4j:commandLink value="#{msg_aplic.prt_PerfilAcesso_retrairModulos}"
                                                                 action="#{PerfilAcessoControle.retrairModulos}"
                                                                 reRender="corpo, centralEventosCadastro, centralEventosFuncionalidades">
                                                </a4j:commandLink>&nbsp;
                                                <c:if test="${modulo eq 'zillyonWeb'}">
                                                    <h:selectBooleanCheckbox id="marcarTodosGeral"
                                                                             value="#{PerfilAcessoControle.marcarTudo}">
                                                        <a4j:support event="onclick" reRender="entidades"
                                                                     action="#{PerfilAcessoControle.selecionarTudo}"/>
                                                    </h:selectBooleanCheckbox>
                                                </c:if>
                                                <c:if test="${modulo eq 'centralEventos'}">
                                                    <h:selectBooleanCheckbox value="#{PerfilAcessoControle.marcarTudo}">
                                                        <a4j:support event="onclick"
                                                                     reRender="entidades,centralEventosCadastro,centralEventosFuncionalidades"
                                                                     action="#{PerfilAcessoControle.selecionarTudo}"/>
                                                    </h:selectBooleanCheckbox>
                                                </c:if>
                                                <h:outputText styleClass="text"
                                                              value=" #{msg_aplic.prt_PerfilAcesso_marcarTodos}"/></td>
                                        </tr>
                                    </table>
                                    <h:panelGroup>
                                        <br/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                                    <h:panelGrid columns="1" width="100%">

                                        <h:outputText value=" "/>

                                    </h:panelGrid>
                                    <h:commandButton rendered="#{PerfilAcessoControle.sucesso}"
                                                     image="./imagens/sucesso.png"/>
                                    <h:commandButton rendered="#{PerfilAcessoControle.erro}"
                                                     image="./imagens/erro.png"/>
                                    <h:panelGrid columns="1" width="100%">
                                        <h:outputText styleClass="mensagem" value="#{PerfilAcessoControle.mensagem}"/>
                                        <h:outputText styleClass="mensagemDetalhada"
                                                      value="#{PerfilAcessoControle.mensagemDetalhada}"/>
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGrid>


                            <rich:dataTable id="corpo" width="100%" var="permissoes"
                                            value="#{PerfilAcessoControle.listaPermissoes}">
                                <rich:column>
                                    <rich:simpleTogglePanel id="modulo" switchType="client"
                                                            opened="#{permissoes.expandir}"
                                                            width="100%">
                                        <f:facet name="header">
                                            <h:panelGroup>
                                                <h:outputText styleClass="tituloModuloPreto"
                                                              value="#{permissoes.modulo} "/>
                                            </h:panelGroup>
                                        </f:facet>
                                        <h:selectBooleanCheckbox id="marcarTodos" value="#{permissoes.selecionarTodos}">
                                            <a4j:support event="onclick" reRender="entidades"
                                                         action="#{PerfilAcessoControle.selecionarTudoModulo}"/>
                                        </h:selectBooleanCheckbox><h:outputText styleClass="text"
                                                                                value=" #{msg_aplic.prt_PerfilAcesso_marcarTodos}"/>
                                        <rich:dataTable id="entidades" rowClasses="linhaImpar, linhaPar" width="100%"
                                                        var="permissao"
                                                        value="#{permissoes.entidades}">
                                            <rich:column>
                                                <h:panelGroup rendered="#{permissao.tipoPermissao == 1}">
                                                    <table width="100%">
                                                        <tr>
                                                            <td width="10%"></td>
                                                            <td width="50%"><h:selectBooleanCheckbox
                                                                    id="permissao" value="#{permissao.selecionado}">
                                                                <a4j:support event="onclick"
                                                                             reRender="entidades"/>
                                                            </h:selectBooleanCheckbox> <h:outputText styleClass="text"
                                                                                                     value=" #{permissao.apresentarPermissao}"/></td>
                                                            <td width="40%">
                                                                <h:panelGroup rendered="#{permissao.selecionado}">
                                                                    <h:selectOneMenu
                                                                            onblur="blurinput(this);"
                                                                            onfocus="focusinput(this);"
                                                                            styleClass="form"
                                                                            value="#{permissao.permissoes}">
                                                                        <f:selectItems
                                                                                value="#{PerfilAcessoControle.listaSelectItemPermissoesPermissao}"/>
                                                                    </h:selectOneMenu>
                                                                </h:panelGroup>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{permissao.tipoPermissao > 1}">
                                                    <table width="100%">
                                                        <tr>
                                                            <td width="10%"></td>
                                                            <td width="90%"><h:selectBooleanCheckbox
                                                                    value="#{permissao.selecionado}">
                                                                <a4j:support event="onclick"
                                                                             reRender="entidade"/>
                                                            </h:selectBooleanCheckbox> <h:outputText styleClass="text"
                                                                                                     value=" #{permissao.apresentarPermissao}"/></td>
                                                        </tr>
                                                    </table>
                                                </h:panelGroup>
                                            </rich:column>
                                        </rich:dataTable>
                                    </rich:simpleTogglePanel>
                                </rich:column>
                            </rich:dataTable>

                            <c:if test="${modulo eq 'centralEventos'}">
                                <!-- -------------------------------------- ENTIDADES -------------------------------------- -->
                                <table id="entidadesCE" style="width: 100%;">
                                    <tr>
                                        <td>

                                            <rich:simpleTogglePanel id="centralEventosCadastro"
                                                                    opened="#{PerfilAcessoControle.expandirRetrairEntidades}"
                                                                    switchType="client" width="100%">
                                                <f:facet name="header">
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="tituloModuloPreto"
                                                                      value="#{CElabels['entidade.centralEventos.cadastro']}"/>
                                                    </h:panelGroup>
                                                </f:facet>
                                                <h:selectBooleanCheckbox
                                                        value="#{PerfilAcessoControle.marcarTudoOperacoesCE}">
                                                    <a4j:support event="onclick" reRender="centralEventosCadastro"
                                                                 action="#{PerfilAcessoControle.selecionarTudoOperacoesCE}"/>
                                                </h:selectBooleanCheckbox><h:outputText styleClass="text"
                                                                                        value=" #{msg_aplic.prt_PerfilAcesso_marcarTodos}"/>

                                                <h:panelGrid cellpadding="0" cellspacing="0" style="width: 100%;"
                                                             columns="2"
                                                             columnClasses="classColunaEsquerda"
                                                             rowClasses="linhaImparAcesso, linhaParAcesso">
                                                    <c:forEach var="entidade"
                                                               items="${PerfilAcessoControle.entidadesAutorizacoes}">


                                                        <h:panelGroup styleClass="text">&nbsp;<c:out
                                                                value='${entidade.descricao}'/>
                                                        </h:panelGroup>
                                                        <h:panelGrid style="width: 100%;" columns="8"
                                                                     columnClasses="text">
                                                            <c:forEach var="operacao"
                                                                       items="${PerfilAcessoControle.operacoes}">
                                                                <h:panelGroup>

                                                                    <c:choose>
                                                                        <c:when test="${fn:containsIgnoreCase(entidade.operacao,operacao.codigo)}">
                                                                            <input type="checkbox"
                                                                                   onclick="adicionarOperacao('${entidade.entidade}','${operacao.codigo}');"
                                                                                   checked="checked"/>
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <input type="checkbox"
                                                                                   onclick="adicionarOperacao('${entidade.entidade}','${operacao.codigo}');"/>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                    <c:out value='${operacao.descricao}'/>
                                                                </h:panelGroup>

                                                            </c:forEach>
                                                        </h:panelGrid>

                                                    </c:forEach>
                                                </h:panelGrid>

                                                <h:inputHidden id="operacaoConcat"
                                                               value="#{PerfilAcessoControle.operacoesTela}"/>


                                            </rich:simpleTogglePanel>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>

                                            <!-- -------------------------------------- FUNCIONALIDADES-------------------------------------- -->

                                            <rich:simpleTogglePanel id="centralEventosFuncionalidades"
                                                                    opened="#{PerfilAcessoControle.expandirRetrairEntidades}"
                                                                    switchType="client" width="100%">
                                                <f:facet name="header">
                                                    <h:panelGroup>
                                                        <h:outputText styleClass="tituloModuloPreto"
                                                                      value="#{CElabels['entidade.centralEventos.funcoes']}"/>
                                                    </h:panelGroup>
                                                </f:facet>
                                                <h:selectBooleanCheckbox
                                                        value="#{PerfilAcessoControle.marcarTudoFuncionalidadesCE}">
                                                    <a4j:support event="onclick" reRender="funcionalidades"
                                                                 action="#{PerfilAcessoControle.selecionarTudoFuncionalidadesCE}"/>
                                                </h:selectBooleanCheckbox><h:outputText styleClass="text"
                                                                                        value=" #{msg_aplic.prt_PerfilAcesso_marcarTodos}"/>
                                                <rich:dataTable id="funcionalidades" rowClasses="linhaImpar, linhaPar"
                                                                width="100%"
                                                                var="funcionalidade"
                                                                value="#{PerfilAcessoControle.funcionalidades}">
                                                    <rich:column>

                                                        <table width="100%">
                                                            <tr>
                                                                <td width="10%"></td>
                                                                <td width="90%"><h:selectBooleanCheckbox
                                                                        value="#{funcionalidade.selecionado}"></h:selectBooleanCheckbox>
                                                                    <h:outputText styleClass="text"
                                                                                  value="#{funcionalidade.descricao}"></h:outputText></td>

                                                            </tr>
                                                        </table>


                                                    </rich:column>
                                                </rich:dataTable>

                                            </rich:simpleTogglePanel>


                                        </td>
                                    </tr>
                                </table>
                            </c:if>
                            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                                    <h:panelGrid columns="1" width="100%">

                                        <h:outputText value=" "/>

                                    </h:panelGrid>
                                    <h:commandButton rendered="#{PerfilAcessoControle.sucesso}"
                                                     image="./imagens/sucesso.png"/>
                                    <h:commandButton rendered="#{PerfilAcessoControle.erro}"
                                                     image="./imagens/erro.png"/>
                                    <h:panelGrid columns="1" width="100%">
                                        <h:outputText styleClass="mensagem" value="#{PerfilAcessoControle.mensagem}"/>
                                        <h:outputText styleClass="mensagemDetalhada"
                                                      value="#{PerfilAcessoControle.mensagemDetalhada}"/>
                                    </h:panelGrid>
                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                    <h:panelGroup>

                                        <c:if test="${modulo eq 'zillyonWeb'}">
                                            <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo == 0}">
                                                <a4j:commandButton id="novo" immediate="true"
                                                                 action="#{PerfilAcessoControle.novo}"
                                                                 value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}"
                                                                 accesskey="1"
                                                                 styleClass="botoes nvoBt btSec"/>
                                            </h:panelGroup>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="salvar" action="#{PerfilAcessoControle.gravar}"
                                                             value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}"
                                                             accesskey="2"
                                                             styleClass="botoes nvoBt"/>

                                            <h:outputText value="    "/>

                                            <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo gt 0}">
                                                <a4j:commandButton id="excluir"
                                                                 onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                                 action="#{PerfilAcessoControle.excluir}"
                                                                 value="#{msg_bt.btn_excluir}"
                                                                   oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                                                 alt="#{msg.msg_excluir_dados}" accesskey="3"
                                                                 styleClass="botoes nvoBt btSec btPerigo"/>
                                            </h:panelGroup>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="consultar" immediate="true"
                                                             action="#{PerfilAcessoControle.inicializarConsultar}"
                                                             value="#{msg_bt.btn_voltar_lista}"
                                                             alt="#{msg.msg_consultar_dados}"
                                                             accesskey="4" styleClass="botoes nvoBt btSec"/>

                                            <h:outputText value="    "/>

                                            <a4j:commandLink id="visualizarLog"
                                                           immediate="true"
                                                           action="#{PerfilAcessoControle.realizarConsultaLogObjetoSelecionado}"
                                                           style="display: inline-block; padding: 8px 15px;"
                                                           accesskey="5"
                                                           styleClass="botoes nvoBt btSec"
                                                           oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                                <i style="text-decoration: none" class="fa-icon-list"/>
                                            </a4j:commandLink>

                                        </c:if>

                                        <c:if test="${modulo eq 'centralEventos'}">
                                            <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo == 0}">
                                                <a4j:commandButton id="novo" immediate="true"
                                                                 action="#{PerfilAcessoControle.novo}"
                                                                 value="#{msg_bt.btn_novo}"
                                                                 image="./imagens/botoesCE/btnNovo.png"
                                                                 alt="#{msg.msg_novo_dados}"
                                                                 accesskey="1" styleClass="botoes"/>
                                            </h:panelGroup>

                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="salvar" action="#{PerfilAcessoControle.gravarCE}"
                                                             value="#{msg_bt.btn_gravar}"
                                                             image="/imagens/botoesCE/gravar.png"
                                                             alt="#{msg.msg_gravar_dados}"
                                                             accesskey="2" styleClass="botoes"
                                                             actionListener="#{PerfilAcessoControle.autorizacao}">
                                                <!-- Entidade.PERFIL ACESSO -->
                                                <f:attribute name="entidade" value="120"/>
                                                <!-- operacao.GRAVAR -->
                                                <f:attribute name="operacao" value="G"/>
                                            </a4j:commandButton>


                                            <h:outputText value="    "/>

                                            <h:panelGroup rendered="#{PerfilAcessoControle.perfilAcessoVO.codigo gt 0}">
                                                <a4j:commandButton id="excluir"
                                                                 onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                                 action="#{PerfilAcessoControle.excluirCE}"
                                                                 value="#{msg_bt.btn_excluir}"
                                                                   oncomplete="#{PerfilAcessoControle.mensagemNotificar}"
                                                                 image="/imagens/botoesCE/excluir.png"
                                                                 accesskey="3" styleClass="botaoExcluir"
                                                                 actionListener="#{PerfilAcessoControle.autorizacao}">
                                                    <!-- Entidade.PERFIL ACESSO -->
                                                    <f:attribute name="entidade" value="120"/>
                                                    <!-- operacao.EXCLUIR -->
                                                    <f:attribute name="operacao" value="E"/>
                                                </a4j:commandButton>
                                            </h:panelGroup>


                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="consultar" immediate="true"
                                                             action="#{PerfilAcessoControle.inicializarConsultar}"
                                                             value="#{msg_bt.btn_voltar_lista}"
                                                             image="/imagens/botoesCE/buscar.png"
                                                             alt="#{msg.msg_consultar_dados}" accesskey="4"
                                                             styleClass="botoes"
                                                             actionListener="#{PerfilAcessoControle.autorizacao}">
                                                <!-- Entidade.PERFIL ACESSO -->
                                                <f:attribute name="entidade" value="120"/>
                                                <!-- operacao.CONSULTAR -->
                                                <f:attribute name="operacao" value="C"/>
                                            </a4j:commandButton>


                                        </c:if>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>

                        <rich:tab id="abaUsuariosPerfilAcesso" label="Usuários com este perfil" switchType="client">
                            <h:form id="form">
                                <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
                                <h:panelGrid id="teste" columns="1" width="98%" columnClasses="colunaCentralizada"
                                             style="background: gainsboro;margin: 1%;">
                                    <h:panelGroup layout="block" id="titulousuario" style="margin: 5px;">
                                        <h:outputText value="Usuários que tem o perfil de acesso "/>
                                        <h:outputText style="font-weight: bold;"
                                                      value="#{PerfilAcessoControle.perfilAcessoVO.nome}"/>
                                    </h:panelGroup>
                                </h:panelGrid>

                                <h:panelGroup>
                                    <table id="tblUsuarioPerfilAcesso"
                                           class="tabelaUsuarioPerfilAcesso pure-g-r pure-u-11-12 margin-0-auto">
                                        <thead>
                                        <th>${"Código Usuário"}</th>
                                        <th>${"Usuário"}</th>
                                        <th>${"Nome"}</th>
                                        <th>${"Perfil Acesso"}</th>
                                        <th>${"Empresa"}</th>
                                        </thead>
                                        <tbody></tbody>
                                    </table>

                                    <a4j:jsFunction name="jsEditar"
                                                    rendered="#{LoginControle.permissaoAcessoMenuVO.usuario}"
                                                    action="#{PerfilAcessoControle.abrirUsuario}"
                                                    oncomplete="#{PerfilAcessoControle.onComplete}"
                                                    reRender="mensagem"/>
                                </h:panelGroup>

                                <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                                    <h:graphicImage id="iconSucesso" rendered="#{PerfilAcessoControle.sucesso}"
                                                    value="./imagens/sucesso.png"/>
                                    <h:graphicImage id="iconErro" rendered="#{PerfilAcessoControle.erro}"
                                                    value="./imagens/erro.png"/>

                                    <h:outputText styleClass="mensagem"
                                                  rendered="#{not empty PerfilAcessoControle.mensagem}"
                                                  value=" #{PerfilAcessoControle.mensagem}"/>
                                    <h:outputText styleClass="mensagemDetalhada"
                                                  rendered="#{not empty PerfilAcessoControle.mensagemDetalhada}"
                                                  value=" #{PerfilAcessoControle.mensagemDetalhada}"/>
                                </h:panelGroup>


                                <script src="${contexto}/beta/js/ext-funcs.js" type="text/javascript"></script>

                                <script>
                                    iniciarTabela("tabelaUsuarioPerfilAcesso", "${contexto}/prest/arquitetura/usuarioPerfilAcesso?codigoPerfilAcesso=${PerfilAcessoControle.perfilAcessoVO.codigo}", 1, "asc", "", true);
                                </script>
                            </h:form>
                        </rich:tab>
                    </rich:tabPanel>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%--MODAIS--%>
    <rich:modalPanel id="panelConsultaPerfilAcesso" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="topoReduzido.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="pages/ce/includes/topoReduzido.jsp"/>
            </c:if>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_PerfilAcesso_tituloForm}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkServico"/>
                <rich:componentControl for="panelConsultaPerfilAcesso" attachTo="hidelinkServico" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConsultaPerfilAcesso" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="3" footerClass="colunaCentralizada" width="100%">
                    <h:outputText value="#{msg_aplic.prt_PerfilAcesso_tituloForm}: "/>
                    <h:inputText id="valorConsultaServico" size="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form" value="#{PerfilAcessoControle.controleConsulta.valorConsulta}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandButton id="btnConsultarServico" reRender="formConsultaPerfilAcesso"
                                           action="#{PerfilAcessoControle.consultarPerfilCopiar}" styleClass="botoes"
                                           value="#{msg_bt.btn_consultar}" image="/imagens/botaoConsultar.png"
                                           title="#{msg_bt.btn_consultar}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandButton id="btnConsultarServico" reRender="formConsultaPerfilAcesso"
                                           action="#{PerfilAcessoControle.consultarPerfilCopiar}" styleClass="botoes"
                                           value="#{msg_bt.btn_consultar}" image="/imagens/botoesCE/buscar.png"
                                           title="#{msg_bt.btn_consultar}"/>
                    </c:if>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaPerfilAcesso" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento" value="#{PerfilAcessoControle.perfis}" rows="5"
                                var="perfil">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{perfil.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['operacoes.opcoes']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{PerfilAcessoControle.selecionarPerfilCopiar}"
                                           reRender="corpo"
                                           oncomplete="Richfaces.hideModalPanel('panelConsultaPerfilAcesso')"
                                           value="#{CElabels['operacoes.selecionar']}"
                                           image="/imagens/botaoEditar.png"
                                           title="#{CElabels['operacoes.selecionar.selecionarDado']}"
                                           styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formConsultaPerfilAcesso:resultadoConsultaPerfilAcesso"
                                   maxPages="10" id="scresultadoConsultaPerfilAcesso"/>
                <h:panelGrid id="mensagemConsultaPerfilAcesso" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{PerfilAcessoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PerfilAcessoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>
<script>
    document.getElementById("form:nome").focus();
    function adicionarOperacao(entidade, operacao) {

        var operacaoConcat = document.getElementById('form:operacaoConcat');
        var opEntidade = entidade + operacao;
        // verificar se ainda não foi selecionada
        if (operacaoConcat.value.indexOf(opEntidade) < 0) {
            operacaoConcat.value = operacaoConcat.value + opEntidade;
        } else {
            operacaoConcat.value = operacaoConcat.value.replace(opEntidade, '');
        }
    }

</script>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">


<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <rich:modalPanel id="panelRecibo" autosized="true" shadowOpacity="true" width="300" height="100"
                     onshow="document.getElementById('formRecibo:empresa').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Recibo_tituloPanel}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkRecibo"/>
                <rich:componentControl for="panelRecibo" attachTo="hiperlinkRecibo" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formRecibo">
            <h:panelGrid columns="1" width="100%">
                <h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Recibo_empresa}"/>
                    <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{ReciboControle.empresaVO.codigo}"
                                     onchange="#{ReciboControle.metodoVazio}">
                        <f:selectItems value="#{ReciboControle.listaEmpresas}"/>
                    </h:selectOneMenu>
                    <rich:spacer width="5"/>
                    <a4j:commandButton id="atualizar_empresa" action="#{ReciboControle.montarListaSelectItemEmpresa}"
                                       style="vertical-align:middle;" image="imagens/atualizar.png" immediate="true"
                                       ajaxSingle="true" reRender="form:empresa"/>
                </h:panelGroup>
                <h:outputText value=" "/>
                <h:panelGrid columns="1" width="100%" columnClasses="centralizado">
                    <a4j:commandLink id="imprimir" action="#{ReciboControle.validaEimprime}"
                                     oncomplete="#{ReciboControle.irPara}">
                        <h:graphicImage url="imagens/imprimirContrato.png" title="Imprimir Recibo em Branco"/>
                    </a4j:commandLink>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form">
        <html>
        <jsp:include page="include_head.jsp" flush="true"/>
        <body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box form-flat">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Config. Contrato" styleClass="container-header-titulo"/>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                                        <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%"
                                                     cellpadding="0" cellspacing="0">
                                            <h:panelGrid columns="2" style="height:100%" width="100%"
                                                         cellpadding="5" cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="planoTextoPadraoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.planoTextoPadrao}"
                                                                         onclick="abrirPopup('planoTextoPadraoCons.jsp', 'PlanoTextoPadrao', 800, 595);"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Modelo de Contrato">
                                                            <f:attribute name="funcionalidade" value="MODELO_CONTRATO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="planoTextoPadraoDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.planoTextoPadrao}"
                                                                      value="Modelo de Contrato"/>
                                                    </div>
                                                    <rich:spacer height="25px"/>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Nesta tela de Cadastro você poderá definir como será o Contrato de Prestação de Serviços de sua Academia. Poderá também, definir como serão os Recibos de venda de produtos e serviços. Utilize o editor para criar seus layouts de acordo com suas necessidades."/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="justificativaope"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.justificativaOperacao}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Justificativa de Operação">
                                                            <f:attribute name="funcionalidade" value="JUSTIFICATIVA_OPERACAO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="justificativaDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.justificativaOperacao}"
                                                                      value="Justificativa de Operação"/>
                                                    </div>
                                                    <rich:spacer height="25px"/>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text" value="Os procedimentos de Lançamento de Bônus, Atestado, Cancelamento, Trancamento e Troca de Planos, necessitam de lançamento de justificativas, a fim de se mapear os motivos pelos quais os Alunos se afastam ou abandonam a atividade física, mas, para conseguirmos tal nível de acompanhamento, precisamos cadastrar as Justificativas mais comuns através desta tela de cadastro.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Poderíamos cadastrar a justificativa ?Mudança de Localidade? para o procedimento de Cancelamento."/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-CADASTROS_CONFIG_CONTRATO" />
                        </jsp:include>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>

        </h:panelGroup>
    </h:form>
</f:view>



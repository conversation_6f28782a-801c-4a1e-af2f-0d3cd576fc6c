/**
 * Created by <PERSON> on 04/04/2016.
 */


function atualizarEventosRenderizar() {
    eventosProduto();
    eventosModalidade();
    adicionarMaskData();
    carregarTooltipster();
}
function adicionarMaskData() {
    jQuery('.rich-calendar-input').mask('99/99/9999');
}
function obterIconeBandeiraCartao(e) {
    var bandeira = obterBandeiraCartaoPorNumero(e.value);
    jQuery(e).css('background-image', "url('imagens/bandeiras/" + bandeira + ".png')");
}
function eventosModalidade() {
    // jQuery('.accodionPanel div.accordionHeader,.accodionPanel div.accordionHeader div.btnHandler').click(function(e) {
    //       var body;
    //       if(jQuery(e.target).attr('class').indexOf('accordionHeader') >= 0 ||
    //           jQuery(e.target).attr('class').indexOf('fa-icon-chevron-up') >= 0 || jQuery(e.target).attr('class').indexOf('btnHandler') >= 0 ) {
    //
    //             if (jQuery(this).parent().hasClass('.accordionPanel')) {
    //
    //                   body = jQuery(this).parent().find('.accordionBody');
    //                   var header = jQuery(this).parent().find('.accordionHeader');
    //                   header.toggleClass('open');
    //                   var altura = 0;
    //                   if (body.hasClass('semBorda')) {
    //                       altura = '165';
    //                   }
    //
    //                   body.animate({
    //                         height: altura
    //                   }, 'fast', 'linear', function () {
    //                         jQuery(this).toggleClass('semBorda');
    //                         jQuery(this).find('.btnHandler').toggleClass('rotate180');
    //                   });
    //
    //             } else {
    //
    //                   body = jQuery(this).parent().parent().find('.accordionBody');
    //                   var altura = 0;
    //                   var header = jQuery(this).parent().parent().find('.accordionHeader');
    //                   header.toggleClass('open');
    //                   if (body.hasClass('semBorda')) {
    //                       altura = '165';
    //                   }
    //                   body.animate({
    //                         height: altura
    //                   }, 'fast', 'linear', function () {
    //                         jQuery(this).toggleClass('semBorda');
    //                         jQuery(this).parent().find('.btnHandler').toggleClass('rotate180');
    //                   });
    //
    //             }
    //       }
    // });
}
emAnimacao = false;
function toggleAccordion(el, e) {
    if (!emAnimacao) {
        emAnimacao = true;
        var body;
        el = jQuery(el);
        if (jQuery(e.target).attr('class').indexOf('accordionHeader') >= 0 ||
                jQuery(e.target).attr('class').indexOf('fa-icon-chevron-up') >= 0 || jQuery(e.target).attr('class').indexOf('btnHandler') >= 0) {

            body = el.parent().find('.accordionBody');
            var header = el;
            header.toggleClass('open');
            var altura = 0;
            if (body.hasClass('semBorda')) {
                altura = '165';
            }

            body.animate({
                height: altura
            }, 'fast', 'linear', function() {
                body.toggleClass('semBorda');
                el.find('.btnHandler').toggleClass('rotate180');
            });
        }
        emAnimacao = false;
    }
}

function mostrarOcultarMsgRenovacaoPlanoCredito() {
    var planoCredito = document.getElementById('form:idPlanoCredito');
    var msgRenovacaoPlanoCredito = document.getElementById('form:idMsgRenovacaoPlanoCredito');
    var situacaoContrato = document.getElementById('form:idSituacaoContrato');
    if (situacaoContrato.value == 'RN') {
        if ((eval(planoCredito.value)) && (msgRenovacaoPlanoCredito.value != '')) {
            //Notifier.info(msgRenovacaoPlanoCredito.value, 'Aten��o');
            Notifier.custom(msgRenovacaoPlanoCredito.value, 'Aten��o', Notifier.MENS_INFO, false);
        } else {
            Notifier.cleanAllOnType(Notifier.MENS_INFO);
        }
    }
}

function limparTodasMsgNotificacao() {
    Notifier.cleanAllOnType(Notifier.MENS_INFO);
    Notifier.cleanAllOnType(Notifier.MENS_ERRO);
    Notifier.cleanAllOnType(Notifier.MENS_SUCESS);
}

function eventosProduto() {
    adicionarIconeQtdProduto();

}
function adicionarIconeQtdProduto() {
    jQuery('.spinerFontAwesome table tbody tr:first-child .rich-spinner-btn').attr('src', 'imagens_flat/caret-up.png');
    jQuery('.spinerFontAwesome table tbody tr:last-child .rich-spinner-btn').attr('src', 'imagens_flat/caret-down.png');
}
function incrementarQtdProduto(e) {
    var i = jQuery(e).parent().parent().find('.rich-spinner-input');
    var valor = Number(i.val());
    console.log(valor + 1);
    if ((valor + 1) > 99) {
        return false;
    } else {
        valor = valor + 1;
    }
    i.val(valor)
    jQuery(e).parent().find('.btnAtualizarQtdProduto').trigger('click');
}
function decrementarQtdProduto(e) {
    var i = jQuery(e).parent().parent().find('.rich-spinner-input');
    var valor = i.val();
    if ((valor - 1) < 0) {
        return false;
    } else {
        valor = valor - 1;
    }
    i.val(valor)
    jQuery(e).parent().find('.btnAtualizarQtdProduto').trigger('click');
}
function verificarPlaceHolder(el, placeholder) {
    var i = jQuery(el);
    if (i.val() == '0' || i.val() == '' || i.val() == '00,00' || i.val() == '0,00' || i.val == null) {
        i.val(placeholder);
    }
}
function carregarTooltipster() {
    carregarTooltip(jQuery('.tooltipster'));
}
function carregarTooltip(el) {
    el.tooltipster({
        theme: 'tooltipster-light',
        position: 'bottom',
        animation: 'grow',
        contentAsHTML: true
    });
}
function mostrarTooltipster(e) {
    carregarTooltipster();
    jQuery(e).tooltipster('show');
}
function esconderToltipster(e) {
    jQuery(e).tooltipster('hide');
}
function exibirElementoTooltip(eCall, e) {
    if (!jQuery(eCall).hasClass('tooltipstered')) {
        carregarTooltip(eCall);
    }
    if (!jQuery(eCall).hasClass('tooltipsteredEl')) {
        e = jQuery(e);
        e.css('display', 'block');
        jQuery(e).remove();
        jQuery(eCall).tooltipster('content', jQuery(e));
        jQuery(eCall).tooltipster('show');
        jQuery(eCall).addClass('tooltipsteredEl');
    }
}
function adicionarTooltipster(eCall, text) {
    carregarTooltipster();
    if (!jQuery(eCall).hasClass('tooltipsteredEl')) {
        jQuery(eCall).tooltipster('content', text);
        jQuery(eCall).tooltipster('show');
        jQuery(eCall).addClass('tooltipsteredEl');
    }
}
function carregarEventosRadio() {
    var el = jQuery('.grupoRadioButton');
    el.find('input[type="checkbox"]:checked').parent().parent().parent().addClass('rowSelecionada');
    el.find('.radioButton').click(function(e) {
        var val;
        var chk;
        var containerCheck = jQuery(e.target).attr('class').indexOf('radioButton') >= 0 ? jQuery(e.target) : (jQuery(e.target).parent().hasClass('radioButton') ? jQuery(e.target).parent() : jQuery(e.target).parent().parent());
        if (jQuery(this).children('a')) {
            chk = jQuery(this).children('input[type="checkbox"]');
            val = chk.is(":checked");
        }
        el.find('tr').removeClass('rowSelecionada');

        if (containerCheck.parent('td').parent('tr')) {
            if (val) {
                containerCheck.parent('td').parent('tr').removeClass('rowSelecionada');
            } else {
                containerCheck.parent('td').parent('tr').addClass('rowSelecionada');
            }
        }
    });
}
function checkBoxClick(el, e) {
/*    var val;
    el = jQuery(el);
    var containerCheck = el;
    if (containerCheck.parent('td')) {
        containerCheck.parent('td').toggleClass('colSelecionada');
    }*/
    submterForm();
}

function marcarRadio(target, evt) {
    jQuery( target ).closest( 'table' ).each(function() {
        var brother = jQuery( this ).find('tbody tr td div.chk-fa-container div.font-size-em');
        var boolvalue = jQuery( brother ).find( 'input[type="checkbox"]' );
        if(boolvalue.is( ":checked" )) {
            marcarCheckBox(brother, evt);
        }
    });
    marcarCheckBox(target, evt);
}

function marcarCheckBox(el, e) {
    var val;
    el = jQuery(el);
    var containerCheck = el;
    var chk = jQuery(containerCheck).children('input[type="checkbox"]');
    val = chk.is(":checked");
    jQuery(chk).attr("checked", !val);
    if (!val) {
        jQuery(containerCheck).children('span').addClass('fa-icon-check');
        jQuery(containerCheck).children('span').removeClass('fa-icon-check-empty');
    } else {
        jQuery(containerCheck).children('span').addClass('fa-icon-check-empty');
        jQuery(containerCheck).children('span').removeClass('fa-icon-check');
    }
}
function carregarEventosCheckBox() {

    jQuery('.checkbox-fa:not(.semEventoClasse)').click(function(e) {
        var val
        if (jQuery(this).children('span')) {

            var chk = jQuery(this).children('input[type="checkbox"]');
            val = chk.is(":checked");
            jQuery(chk).attr("checked", !val);
            jQuery(this).children('span').toggleClass('fa-icon-check');
            jQuery(this).children('span').toggleClass('fa-icon-check-empty');
        }
        var containerCheck = jQuery(e.target).attr('class').indexOf('checkbox-fa') >= 0 ? jQuery(e.target) : jQuery(e.target).parent();
        if (containerCheck.hasClass('checkHorarioTurma')) {
            var valLabel = containerCheck.find('span').text();
            valLabel = Number(valLabel);
            if (val) {
                valLabel++;
            } else {
                valLabel--;
            }
            containerCheck.find('span').text(valLabel);
        }
        if (containerCheck.parent('td')) {
            if (val) {
                containerCheck.parent('td').removeClass('colSelecionada');
            } else {
                containerCheck.parent('td').addClass('colSelecionada');
            }
        }
    });

}

function scrollDadosNegociacao() {

    topAtual = jQuery(window).scrollTop();
    var pai = jQuery('.containerDetalhesNegociacao').parent();
    var inicioPai = pai.offset().top;
    var hPai = pai.height();

    var expandido = !jQuery('.detalhesEsconder').hasClass('esconderDetalhes');
    var inicioScroll = inicioPai + jQuery('.containerDetalhesNegociacao').height();
    var containerExcedeView = (!expandido ? jQuery('.containerDetalhesNegociacao').height() - 469 : jQuery('.containerDetalhesNegociacao').height()) > window.innerHeight;
    if (topAtual > inicioScroll) {
        jQuery('.detalhesEsconder').addClass('esconderDetalhes').queue();

        if (topAtual + jQuery('.containerDetalhesNegociacao').height() + 50 <= inicioPai + hPai) {
            jQuery('.containerDetalhesNegociacao').animate({top: topAtual - inicioPai - (expandido ? 50 : 20)}, {
                duration: 100});
        } else {
            jQuery('.containerDetalhesNegociacao').animate({top: (hPai - (jQuery('.containerDetalhesNegociacao').height() - jQuery('.detalhesEsconder').height()) - 115)}, {
                duration: 200});
        }
    } else if (topAtual <= inicioScroll) {
        jQuery('.containerDetalhesNegociacao').animate({top: 0}, 100);
        jQuery('.detalhesEsconder').removeClass('esconderDetalhes');
    }
}

function formatar_percentuallimite(campo, separador_milhar, separador_decimal, tecla, limite) {
    var sep = 0;
    var key = '';
    var i = j = 0;
    var len = len2 = 0;
    var strCheck = '0123456789';
    var aux = aux2 = '';
    var whichCode = (window.Event) ? tecla.which : tecla.keyCode;

    if (navigator.appName.indexOf("Netscape") == -1) {
        whichCode = event.keyCode;
    }

    if (whichCode == 13)
        return true; // Tecla Enter
    if (whichCode == 8)
        return true; // Tecla Delete
    if (whichCode == 0)
        return true; //Tecla TAB

    key = String.fromCharCode(whichCode); // Pegando o valor digitado
    if (strCheck.indexOf(key) == -1)
        return false; // Valor inv�lido (n�o inteiro)

    len = campo.value.length;
    if (len > limite) {
        len = limite;
    }

    for (i = 0; i < len; i++)
        if ((campo.value.charAt(i) != '0') && (campo.value.charAt(i) != separador_decimal))
            break;
    aux = '';

    for (; i < len; i++)
        if (strCheck.indexOf(campo.value.charAt(i)) != -1)
            aux += campo.value.charAt(i);

    aux += key;
    len = aux.length;
    if (len == 0)
        campo.value = '';
    if (len == 1)
        campo.value = '0' + separador_decimal + '0' + aux;
    if (len == 2)
        campo.value = '0' + separador_decimal + aux;

    if (len > 2) {
        aux2 = '';

        for (j = 0, i = len - 3; i >= 0; i--) {
            if (j == 3) {
                aux2 += separador_milhar;
                j = 0;
            }
            aux2 += aux.charAt(i);
            j++;
        }

        campo.value = '';
        len2 = aux2.length;
        for (i = len2 - 1; i >= 0; i--)
            campo.value += aux2.charAt(i);
        campo.value += separador_decimal + aux.substr(len - 2, len);
    }

    return false;
}


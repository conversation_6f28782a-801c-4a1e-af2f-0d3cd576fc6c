function validar() {
	limparMensagem( [ 'descricao', 'dataInicio', 'dataTermino', 'descricaoProduto',
	        'textoPadrao', 'ambientes', 'modelosContrato', 'dataInicio2',
	        'dataTermino2', 'textoPadrao2', 'descricaoProduto2' ]);

	var descricao = document.getElementById('form:descricao');
	var dataInicio = document.getElementById('form:dataInicioInputDate');
	var dataTermino = document.getElementById('form:dataTerminoInputDate');
	var descricaoProduto = document.getElementById('form:descricaoProduto');
	var textoPadrao = document.getElementById('form:textoPadraoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
	var ambientes = document.getElementById('form:tablePerfilEventoAmbiente');
	var modelosContrato = document.getElementById('form:tableModeloContrato');

	var validade = true;
	if (descricao == null || descricao.value == null || descricao.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="${CElabels[\'entidade.perfilEvento.nome\']}"/>'),
				'descricao');
		validade = false;
	}

	if (dataInicio == null || dataInicio.value == null
			|| dataInicio.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.dataInicio\']}"/>'),
				'dataInicio');
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.dataInicio\']}"/>'),
				'dataInicio2');
		validade = false;
	}

	if (dataTermino == null || dataTermino.value == null
			|| dataTermino.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.dataTermino\']}"/>'),
				'dataTermino');
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.dataTermino\']}"/>'),
				'dataTermino2');
		validade = false;
	}
	
	if (descricaoProduto == null || descricaoProduto.value == null
			|| descricaoProduto.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.produto\']}"/>'),
				'descricaoProduto');
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.produto\']}"/>'),
				'descricaoProduto2');
		validade = false;
	}

	if (textoPadrao == null || textoPadrao == '<br mce_bogus="1">') {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.perfilEvento.textoPadrao\']}"/>'),
				'textoPadrao');
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.perfilEvento.textoPadrao\']}"/>'),
				'textoPadrao2');
		validade = false;
	}

	try{
	if (ambientes.tBodies[0].rows.length == 0) {
		exibirMensagem(
				'<h:outputText value="#{Mensagens[\'campoObrigatorio.perfilEvento.ambiente\']}"/>',
				'ambientes');
		validade = false;
	}

	if (modelosContrato.tBodies[0].rows.length == 0) {
		exibirMensagem(
				'<h:outputText value="#{Mensagens[\'campoObrigatorio.perfilEvento.modeloContrato\']}"/>',
				'modelosContrato');
		validade = false;
	}
	}catch(e){}
	return validade;
}

function limparMsgObrigTextoPadrao() {
	var textoPadrao = document.getElementById('form:textoPadraoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
	if (textoPadrao != null && textoPadrao != '<br mce_bogus="1">') {
		limparMensagem('textoPadrao');
		limparMensagem('textoPadrao2');
	}
}

function limparMsgObrigAmbiente() {
	var ambientes = document.getElementById('form:tablePerfilEventoAmbiente');
	if (ambientes.tBodies[0].rows.length > 0) {
		limparMensagem('ambientes');
	}
}

function limparMsgObrigModeloContrato() {
	var modelosContrato = document.getElementById('form:tableModeloContrato');
	if (modelosContrato.tBodies[0].rows.length > 0) {
		limparMensagem('modelosContrato');
	}
}

function setarNumMaxConvidados(numero){
	document.getElementById('form:numMaxConvidados').value = numero.value;
}

function validarSazonalidade() {
	limparMensagem('ambientePerfilEventoSazonalidade');
	limparMensagem('dataInicioSazonalidade');
	limparMensagem('dataFimSazonalidade');
	limparMensagem('tipoOperacaoSazonalidade');
	limparMensagem('formaCalculoSazonalidade');
	limparMensagem('valorPerfilEventoSazonalidade');

	var ambientePerfilEventoSazonalidade = document
			.getElementById('form:ambientePerfilEventoSazonalidade');
	var dataInicioSazonalidade = document
			.getElementById('form:dataInicioSazonalidadeInputDate');
	var dataFimSazonalidade = document
			.getElementById('form:dataFimSazonalidadeInputDate');
	var tipoOperacaoSazonalidade = document
			.getElementById('form:tipoOperacaoSazonalidade');
	var formaCalculoSazonalidade = document
			.getElementById('form:formaCalculoSazonalidade');
	var valorPerfilEventoSazonalidade = document
			.getElementById('form:valorPerfilEventoSazonalidade');

	var validade = true;

	if (ambientePerfilEventoSazonalidade == null
			|| ambientePerfilEventoSazonalidade.value == null
			|| ambientePerfilEventoSazonalidade.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.perfilEvento.ambienteEsc\']}"/>'),
				'ambientePerfilEventoSazonalidade');
		validade = false;
	}

	if (dataInicioSazonalidade == null || dataInicioSazonalidade.value == null
			|| dataInicioSazonalidade.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.dataInicio\']}"/>'),
				'dataInicioSazonalidade');
		validade = false;
	}

	if (dataFimSazonalidade == null || dataFimSazonalidade.value == null
			|| dataFimSazonalidade.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.dataFim\']}"/>'),
				'dataFimSazonalidade');
		validade = false;
	}

	if (tipoOperacaoSazonalidade == null
			|| tipoOperacaoSazonalidade.value == null
			|| tipoOperacaoSazonalidade.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.tipoOperacao\']}"/>'),
				'tipoOperacaoSazonalidade');
		validade = false;
	}

	if (formaCalculoSazonalidade == null
			|| formaCalculoSazonalidade.value == null
			|| formaCalculoSazonalidade.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.formaCalculo\']}"/>'),
				'formaCalculoSazonalidade');
		validade = false;
	}

	if (valorPerfilEventoSazonalidade == null
			|| valorPerfilEventoSazonalidade.value == null
			|| valorPerfilEventoSazonalidade.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.valor\']}"/>'),
				'valorPerfilEventoSazonalidade');
		validade = false;
	}

	return validade;
}

function validarLayout() {
	limparMensagem('arquivoLayout');
	limparMensagem('descricaoLayout');

	var arquivoLayout = document.getElementById('form:arquivoLayoutCarregado');
	var descricaoLayout = document
			.getElementById('form:descricaoLayoutTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;

	var validade = true;

	if (arquivoLayout == null || arquivoLayout.value == null
			|| arquivoLayout.value == "false") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.arquivo\']}"/>'),
				'arquivoLayout');
		validade = false;
	}

	if (descricaoLayout == null || descricaoLayout == '<br mce_bogus="1">') {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.descricao\']}"/>'),
				'descricaoLayout');
		validade = false;
	}

	return validade;
}

function validarServico() {
	limparMensagem('descricaoServico');

	var descricaoServico = document.getElementById('form:descricaoServico');
	
	
	
	var validade = true;

	if (descricaoServico == null || descricaoServico.value == null
			|| descricaoServico.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.servico\']}"/>'),
				'descricaoServico');
		validade = false;
	}

	return validade;
}

function validarBemConsumo() {
	limparMensagem('descricaoBemConsumo');
	
	

	var descricaoBemConsumo = document.getElementById('form:descricaoBemConsumo');
	
	

	var validade = true;
	
	if (descricaoBemConsumo == null || descricaoBemConsumo.value == null
			|| descricaoBemConsumo.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.bemConsumo\']}"/>'),
				'descricaoBemConsumo');
		validade = false;
	}

	return validade;
}

function validarUtensilio() {
	limparMensagem('descricaoUtensilio');
	
	

	var descricaoUtensilio = document.getElementById('form:descricaoUtensilio');
	

	var validade = true;

	if (descricaoUtensilio == null || descricaoUtensilio.value == null
			|| descricaoUtensilio.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.utensilio\']}"/>'),
				'descricaoUtensilio');
		validade = false;
	}

	return validade;
}

function validarBrinquedo() {
	limparMensagem('descricaoBrinquedo');
	


	var descricaoBrinquedo = document.getElementById('form:descricaoBrinquedo');
	
	
	var validade = true;

	if (descricaoBrinquedo == null || descricaoBrinquedo.value == null
			|| descricaoBrinquedo.value == "") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.brinquedo\']}"/>'),
				'descricaoBrinquedo');
		validade = false;
	}

	return validade;
}

function validarModeloContrato() {
	limparMensagem('arquivoModeloContrato');
	limparMensagem('descricaoModeloContrato');

	var arquivoModeloContrato = document
			.getElementById('form:arquivoCarregado');
	var descricaoModeloContrato = document
			.getElementById('form:descricaoModeloContratoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;

	var validade = true;

	if (arquivoModeloContrato == null || arquivoModeloContrato.value == null
			|| arquivoModeloContrato.value == "false") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.arquivo\']}"/>'),
				'arquivoModeloContrato');
		validade = false;
	}

	if (descricaoModeloContrato == null
			|| descricaoModeloContrato == '<br mce_bogus="1">') {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.descricao\']}"/>'),
				'descricaoModeloContrato');
		validade = false;
	}

	return validade;
}

function validarModeloContratoImagem() {
	limparMensagem('arquivoModeloContratoImagem');

	var arquivoModeloContratoImagem = document
			.getElementById('form:arquivoCarregado');

	var validade = true;

	if (arquivoModeloContratoImagem == null
			|| arquivoModeloContratoImagem.value == null
			|| arquivoModeloContratoImagem.value == "false") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.arquivo\']}"/>'),
				'arquivoModeloContratoImagem');
		validade = false;
	}

	return validade;
}

function validarModeloOrcamento() {
	limparMensagem('arquivoModeloOrcamento');
	limparMensagem('descricaoModeloOrcamento');

	var arquivoModeloOrcamento = document
			.getElementById('form:arquivoOrcamentoCarregado');
	var descricaoModeloOrcamento = document
			.getElementById('form:descricaoModeloOrcamentoTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;

	var validade = true;

	if (arquivoModeloOrcamento == null || arquivoModeloOrcamento.value == null
			|| arquivoModeloOrcamento.value == "false") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.arquivo\']}"/>'),
				'arquivoModeloOrcamento');
		validade = false;
	}

	if (descricaoModeloOrcamento == null
			|| descricaoModeloOrcamento == '<br mce_bogus="1">') {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.descricao\']}"/>'),
				'descricaoModeloOrcamento');
		validade = false;
	}

	return validade;
}

function validarModeloOrcamentoImagem() {
	limparMensagem('arquivoModeloOrcamentoImagem');

	var arquivoModeloOrcamentoImagem = document
			.getElementById('form:arquivoOrcamentoCarregado');

	var validade = true;

	if (arquivoModeloOrcamentoImagem == null
			|| arquivoModeloOrcamentoImagem.value == null
			|| arquivoModeloOrcamentoImagem.value == "false") {
		exibirMensagem(
				montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.arquivo\']}"/>'),
				'arquivoModeloOrcamentoImagem');
		validade = false;
	}

	return validade;
}
function ativarAjuda(id) {
	if (typeof id == "string") {
		var hint = document.getElementById('hint-' + id);
		hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
	} else {
		for (var i = 0; i < id.length; i++) {
			var hint = document.getElementById('hint-' + id[i]);
			hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
		}
	}
}
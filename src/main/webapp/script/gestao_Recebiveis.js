function atualizar() {
}
var ultimoBlocoTrocado = {
    bloco: '.filtrosPrincipais',
    container: '.painelFiltros',
    tipoBotao: '.botaoModoTimeLine'
};

function trocarUltimoBloco(){
    trocarBloco(ultimoBlocoTrocado.bloco, ultimoBlocoTrocado.container, ultimoBlocoTrocado.tipoBotao);
}
function trocarBloco(bloco, container, tipoBotao) {
    ultimoBlocoTrocado.bloco = bloco;
    ultimoBlocoTrocado.container = container;
    ultimoBlocoTrocado.tipoBotao = tipoBotao;
    jQuery(container + '.visivel').slideUp();
    jQuery(container + bloco).slideDown();
    jQuery(container + bloco).addClass('visivel');
    jQuery(tipoBotao).removeClass('ativo');
    jQuery(tipoBotao + bloco).addClass('ativo');
}
var functionTrocarMensagem;
var mensagem = 0;
var msgs = ['Consultando banco de dados...', 'Processando resultado da consulta', 'Verificando pagamentos a vista...', 'Listando parcelas de cart�o de cr�dito...', 'Listando cheques', 'Verificando lotes', 'Carregando hist�rico de cheques', 'Procurando por devolu��es', 'Preenchendo contas'];
function iniciarStatus(multiplicador) {
    mensagem = 0;
    addMsg('Consultando banco de dados...');
    functionTrocarMensagem = window.setInterval('trocarMensagem()', multiplicador * 1000);
}
function addMsgPorTipo(tipo) {
    if (tipo === 'CH') {
        addMsg('Listando cheques...');
    } else if (tipo === 'CA') {
        addMsg('Listando parcelas de cart\u00E3o...');
    } else if (tipo === 'CD') {
        addMsg('Listando pagamentos em cart\u00E3o de d\u00E9bito...');
    } else {
        addMsg('Listando pagamentos...');
    }
}
function fecharStatus() {
    window.clearInterval(functionTrocarMensagem);
}
function trocarMensagem() {
    if (mensagem < msgs.length) {
        addMsg(msgs[mensagem]);
        mensagem++;
    }
}
function addMsg(txt) {
    jQuery('.textoCarregando').empty();
    jQuery('.textoCarregando').text(txt);
}



function formatReal(int) {
    var tmp = int + '';
    tmp = tmp.replace(/([0-9]{2})$/g, ",$1");
    if (tmp.length > 6)
        tmp = tmp.replace(/([0-9]{3}),([0-9]{2}$)/g, ".$1,$2");

    return tmp;
}
function abrirLote(codigoLote) {
    document.getElementById('form:idcodigolote').value = codigoLote;
    document.getElementById('form:idabrirlote').click();
}

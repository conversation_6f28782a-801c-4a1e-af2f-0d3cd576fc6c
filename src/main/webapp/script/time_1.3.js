var agora = new Date();
var diferenca = 59 * 1000;
var dataFinal = new Date(agora.getTime() + diferenca);
var urlLogoutRedirect = "";
var tempoEmMillis = 30 * 60 * 1000;
var offSetServer = 0;
var ultimoUcpShake = new Date();
//
function two(x) {
    return ((x > 9) ? "" : "0") + x
}
function three(x) {
    return ((x > 99) ? "" : "0") + ((x > 9) ? "" : "0") + x
}

function time(ms) {
    var sec = Math.floor(ms / 1000);
    var min = Math.floor(sec / 60);
    var hrs = Math.floor(min / 60);
    var days = Math.floor(hrs / 24);
    sec = (sec % 60).toString();
    if (sec.length < 2)
    {
        sec = "0" + sec;
    }
    min = (min % 60).toString();
    if (min.length < 2)
    {
        min = "0" + min;
    }
    hrs = (hrs % 24).toString();
    if (hrs.length < 2)
    {
        hrs = "0" + hrs;
    }

    days = (days).toString();

    var counter = /*days + ":" + hrs + ":" +*/ min + ":" + sec;

    if (min <= 3 && sec == 59) {
        getOffsetAjax();
        if (offSetServer == 0 || offSetServer >= ((tempoEmMillis/1000) - 4)) {
            Richfaces.showModalPanel('panelRenovarSessao');
        }
    }

    return counter;
}

/*
 * Waller Maciel (11/08/2017)
 * Armazenar a diferenca de tempo sem fuso horario em millis
 * e atualizada a cada minuto, da ultima operacao feita no servidor e manter do lado cliente (browser)
 * para evitar que mesmo o usuario estando e mais de uma aba, o tempo da sess�o
 * n�o seja expirado incorretamente for�ando o logout do usu�rio, pois, o usu�rio est� logado usando mais de uma aba.
 */

function getOffsetAjax() {    
    jQuery.ajax({
        type: 'POST',
        url: ctxForJS + '/UpdateServlet?op=getLastActionTime',
        complete: function(lastAction) {
            var ultimaAcaoMillis = new Number(lastAction.responseText);
            if (ultimaAcaoMillis > 0) {               
                var dataServidor = new Date(lastAction.getResponseHeader('date'));
                var dataUltimaAcao = new Date(ultimaAcaoMillis);
                console.log("data/hora servidor GMT: " + dataServidor.toUTCString());
                console.log("data/hora ultima acao GMT: " + dataUltimaAcao);
                offSetServer = (new Date(dataServidor.toUTCString()).getTime() + new Date().getTimezoneOffset() * 60000) - ultimaAcaoMillis;
                offSetServer = offSetServer / 1000;//segundos
                console.log("offsetLastAction: " + offSetServer);
            }
        }
    });
}
;

function setLastActionTime() {
    if (window.location.href.indexOf("detalheBI.jsp?m=web") == -1) {
        jQuery.ajax({
            type: 'POST',
            url: ctxForJS + '/UpdateServlet?op=setLastActionTime',
            complete: function (setLastAction) {
                var ultimaAcaoMillis = new Number(setLastAction.responseText);
                var dataUltimaAcao = new Date(ultimaAcaoMillis);
                console.log("SETTING data/hora ultima acao GMT: " + dataUltimaAcao);
            }
        });
    }
};

function timeSeconds(ms) {
    var sec = Math.floor(ms / 1000);
    var min = Math.floor(sec / 60);
    var hrs = Math.floor(min / 60);
    var days = Math.floor(hrs / 24);
    sec = (sec % 60).toString();
    if (sec.length < 2)
    {
        sec = "0" + sec;
    }
    min = (min % 60).toString();
    if (min.length < 2)
    {
        min = "0" + min;
    }
    hrs = (hrs % 24).toString();
    if (hrs.length < 2)
    {
        hrs = "0" + hrs;
    }

    days = (days).toString();

    var counter = (min * 60) + Number(sec);


    return counter;
}

function minutes(ms) {
    var sec = Math.floor(ms / 1000);
    var min = Math.floor(sec / 60);
    var hrs = Math.floor(min / 60);
    var days = Math.floor(hrs / 24);
    sec = (sec % 60).toString();
    if (sec.length < 2)
    {
        sec = "0" + sec;
    }
    min = (min % 60).toString();
    if (min.length < 2)
    {
        min = "0" + min;
    }
    hrs = (hrs % 24).toString();
    if (hrs.length < 2)
    {
        hrs = "0" + hrs;
    }
    var counter = (hrs * 60) + min + (sec / 60);



    return counter;
}

function getComponentTime(name) {
    var component = document.getElementById('form:' + name);
    if (component == null) {
        component = document.getElementById(name);
        if (component == null) {
            component = document.getElementById('formTopo:' + name);
        }
    }
    return component;
}
function setTotalProgressiveBar() {
    var regressiveBar = getComponentTime('regressiveBar');
    agora = new Date();
    diferenca = dataFinal.getTime() - agora.getTime();
    regressiveBar.setAttribute("total", timeSeconds(diferenca));
}
function startCountdown() {

    var numberCountdown = getComponentTime('tempo');
    var regressiveTime = getComponentTime('regressiveTime');
    var regressiveBar = getComponentTime('regressiveBar');

    agora = new Date();
    diferenca = dataFinal.getTime() - agora.getTime();

    var texto = time(diferenca);
    var segundos = timeSeconds(diferenca);
    //alert(texto);
    //alert(diferenca);
    if (diferenca > 0) {

        if (document.all) {
            numberCountdown.innerText = texto;
            regressiveTime.innerText = texto;
        } else {
            numberCountdown.textContent = texto;
            regressiveTime.textContent = texto;
        }
        var totalBarra = regressiveBar.getAttribute('total');
        var totalAtual = (segundos * 100) / totalBarra;
        regressiveBar.style = 'width:' + (550 * (totalAtual / 100)) + 'px;';
        setTimeout('startCountdown()', 1000);
    } else {        
        getOffsetAjax();
        if (offSetServer == 0 || offSetServer >= (tempoEmMillis / 1000)) {
            numberCountdown.innerText = "Aguarde, redirecionando...";
            document.location.href = document.location.href = urlLogoutRedirect;
        }
    }

    //---------------------- Calcular shake ucp
    diferenca = agora.getTime() - ultimoUcpShake.getTime();
    if (getComponentTime('iconeUCP')) {
        if (minutes(diferenca) == 0) {
            getComponentTime('iconeUCP').className = getComponentTime('iconeUCP').className.replace(' shakeThat', '') + ' shakeThat';
            ultimoUcpShake = agora;
        } else if (minutes(diferenca) >= 10) {
            getComponentTime('iconeUCP').className = getComponentTime('iconeUCP').className.replace(' shakeThat', '') + ' shakeThat';
            ultimoUcpShake = agora;
        } else {
            getComponentTime('iconeUCP').className = getComponentTime('iconeUCP').className.replace(' shakeThat', '');
        }
    }
}

function resetTime(diff) {
    dataFinal = new Date(agora.getTime() + diff);
}

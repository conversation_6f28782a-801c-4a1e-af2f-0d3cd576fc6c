function atualizar() {
}

var functionTrocarMensagem;

function iniciarMsgCarregando() {
    jQuery('.textoCarregando').empty();
    functionTrocarMensagem = window.setInterval('atualizarMensagem()', 200);
}

function limparMsgCarregando() {
    jQuery('.textoCarregando').empty();
}

function finalizarMsgCarregando() {
    jQuery('.textoCarregando').empty();
    window.clearInterval(functionTrocarMensagem);
}

function atualizarMensagem() {
    try {
        var msgCarregando = document.getElementById('msgCarregando').innerHTML;
        // console.log('atualizarMensagem | msgCarregando ' + msgCarregando);
        if (msgCarregando) {
            addMsg(msgCarregando);
        }
    } catch (e) {
        // console.log(e);
    }
}

function addMsg(txt) {
    // console.log('addMsg ' + txt);
    jQuery('.textoCarregando').empty();
    jQuery('.textoCarregando').text(txt);
}
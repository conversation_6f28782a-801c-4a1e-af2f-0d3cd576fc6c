function listen(evnt, elem, func) {
    if (elem.addEventListener)  // W3C DOM
        elem.addEventListener(evnt, func, false);
    else if (elem.attachEvent) { // IE DOM
        var r = elem.attachEvent("on" + evnt, func);
        return r;
    } else {
        console.log('Nao foi possivel adicionar um Listen para o elemento ' + elem);
    }
    console.log("1. Listen binding for element: " + elem);
}

function hideCuboMagico() {
    var elem = document.getElementById('cuboMagico');
    if (elem !== null){
        elem.className = 'fadeout';
        console.log("2. Hide elem: " + elem);
    }
}

function atualizarTreeViewFD() {
    
    jQuery(document).ready(function($) {
        $(".fechamento").treeTable({
            });
                
        $(".fechamento").expandirUmNivel("tree-fechamento");
    });
    
    jQuery(document).ready(function($){
        $(".expandir").click(function() {
            $(".fechamento").expandirTudo();
        });

        $(".expandirUm").click(function() {
            $(".fechamento").expandirUmNivel("tree-fechamento");
        });
 
        $(".retrairUm").click(function() {
            $(".fechamento").retrairUmNivel("tree-fechamento");
        });

        $(".retrair").click(function() {
            $(".fechamento").retrairTudo();
        });
    });
}

function atualizarTreeViewFD2() {
    
    jQuery(document).ready(function($) {
        $(".fechamento").treeTable({
            });
    });
    
    jQuery(document).ready(function($){
        $(".expandir").click(function() {
            $(".fechamento").expandirTudo();
        });

        $(".expandirUm").click(function() {
            $(".fechamento").expandirUmNivel("tree-fechamento");
        });
 
        $(".retrairUm").click(function() {
            $(".fechamento").retrairUmNivel("tree-fechamento");
        });

        $(".retrair").click(function() {
            $(".fechamento").retrairTudo();
        });
    });
}


function mudar_cor(celula, cor){
    celula.style.backgroundColor = cor;
}

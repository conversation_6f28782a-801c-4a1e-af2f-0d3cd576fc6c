/* 
 * Document   : include_modal_capfoto_html5
 * Created on : 14/08/2017, 16:06:59
 * Author     : <PERSON><PERSON><PERSON><PERSON>.
 */

var jQuery_capFoto = jQuery.noConflict(true);

// Global variables to hold important modal-hide-persistent information,
// cross-function data and "constants"
var selectionCoords = {x: 0, y: 0, x2: 0, y2: 0, w: 0, h: 0};
var canvasSize = {width: 0, height: 0};
var selectBoxMaxSize = 260;
var videoBoxMaxSize = 290;
var finishedEdit = false;
var imgMaxSize = 150;
var whereToGoBack = "edit";
var originalImage = new Image();
var initialized = false;
var modalSize = {width: "730px", height: "600px"};
var allowedFileTypes = ["image/jpeg", "image/png", "image/bmp", "image/gif"];

// Vari�veis correspondentes aos dados da academia, c�digo pessoa(aluno/usu�rio) e etc, necess�rios para o upload.
// Inicializados atrav�s da fun��o initializePhotoCap(<chave>, <codigo>, <contexto>, <usuario>)
var chaveAcademia, codigoPessoa, contexto, usuarioLogado;

function handleFileSelect(evt) {
    evt.stopPropagation();
    evt.preventDefault();

    // FileList object.
    try {
        var files = evt.dataTransfer.files;
    } catch (err) {
        var files = evt.target.files;
    }
    var file = files[0];
    var type = file.type;
    if(allowedFileTypes.indexOf(type) === -1){
        try{
            Notifier.error(
                "Formato de arquivo inv�lido!",
                "N�o Foi Poss�vel Realizar esta Opera��o"
            );
        } catch(e){}
        return;
    }
    
    var reader = new FileReader();
    reader.onload = function (e) {
        showImageCanvas(e.target.result);
    };
    reader.readAsDataURL(files[0]);
}

function handleDragOver(evt) {
    evt.stopPropagation();
    evt.preventDefault();
    evt.dataTransfer.dropEffect = 'copy'; // Explicitly show this is a copy.
}

function registerSelectedCoords(c) {
    selectionCoords = c;
}

function clearElement(elem) {
    while (elem.firstChild) {
        elem.removeChild(elem.firstChild);
    }
}

function hideButtons() {
    jQuery_capFoto("a.capFotoButton").css("display", "none");
}

function resetImageCanvas() {
    var imageDiv = document.getElementById("imageDiv");
    clearElement(imageDiv);
    var newCanvas = createCanvas();
    imageDiv.appendChild(newCanvas);
    return newCanvas;
}

function createCanvas() {
    var canvasImg = document.createElement("canvas");
    canvasImg.id = "canvas_img";
    jQuery_capFoto(canvasImg).addClass("imageUploaded");
    return canvasImg;
}

function resizeImage(canvasImg) {
    var tmpImg = new Image();
    tmpImg.onload = function () {
        canvasImg.width = canvasImg.height = selectBoxMaxSize;
        canvasImg.getContext('2d').drawImage(tmpImg, 0, 0, selectBoxMaxSize, selectBoxMaxSize);
    };
    tmpImg.src = canvasImg.toDataURL();
    return canvasImg;
}

function toggleModalSize(finished) {
    var modalPhotoContentTbl = document.getElementById("modalCapFotoHTML5ContentTable");
    var modalPhotoCDiv = document.getElementById("modalCapFotoHTML5CDiv");

    if (finished) {
        modalPhotoContentTbl.style.width = "calc(1 * " + modalSize.width + " / 2)";
        modalPhotoContentTbl.style.height = "calc(4 * " + modalSize.height + " / 5)";
    } else {
        modalPhotoContentTbl.style.width = modalSize.width;
        modalPhotoContentTbl.style.height = modalSize.height;
    }

    // Centralize it anyway
    modalPhotoCDiv.style.top = "calc(50vh - " + modalPhotoCDiv.getHeight() + "px / 2)";
    modalPhotoCDiv.style.left = "calc(50vw - " + modalPhotoCDiv.getWidth() + "px / 2)";
}

function takeSnapshot() {
    Webcam.snap(function (data_uri) {
        var canvasImg = document.getElementById("canvas_img");
        var ctx = canvasImg.getContext("2d");
        var imgTmp = new Image();

        imgTmp.onload = function () {
            canvasImg.width = imgTmp.width;
            canvasImg.height = imgTmp.height;
            selectBoxMaxSize = canvasImg.height;
            ctx.drawImage(imgTmp, 0, 0, canvasImg.width, canvasImg.height);
            canvasCrop("webcam");
        };
        imgTmp.src = data_uri;
    });
}

function canvasCrop(from) {
    var canvasImg = document.getElementById('canvas_img');
    var ctx = canvasImg.getContext('2d');
    var imgSelectedData = ctx.getImageData(selectionCoords.x, selectionCoords.y, selectionCoords.w, selectionCoords.h);
    canvasImg.width = selectionCoords.w;
    canvasImg.height = selectionCoords.h;
    ctx.putImageData(imgSelectedData, 0, 0);
    if (jQuery_capFoto("#canvas_img").data("Jcrop")) {
        jQuery_capFoto("#canvas_img").data("Jcrop").destroy();
    }

    whereToGoBack = from;
    showFinishTab(canvasImg);
}

function uploadCroppedImage() {
    var canvasImg = document.getElementById("canvas_img");
    var src = canvasImg.toDataURL("jpeg");
    var imageFotoAluno64 = src.replace(/^data:image\/(png|jpg|jpeg);base64,/, "");

    var data = {
        token: "TelaCliente:" + chaveAcademia,
        operacao: "alterarFotoAluno",
        codigoaluno: codigoPessoa,
        fotoAluno: imageFotoAluno64
    };

    if (usuarioLogado !== "")
        data.usuariologado = usuarioLogado;

    jQuery_capFoto.post(contexto + "/prest/contratoassinatura",
        data
    ).done(function (data) {
        resetImageCanvas();
        Richfaces.hideModalPanel('modalCapFotoHTML5');
        try {
            atualizarPagina();
        } catch (err) {
        }
        updateFoto();
        location.reload(true);
    });
}

function goBack() {
    if (whereToGoBack === "webcam") {
        showWebcamTab();
    } else if (whereToGoBack === "edit") {
        showImageCanvas(originalImage.src, true);
    } else { // Should never get to this case, but...
        showUploadTab();
    }
}

function hideImageCanvas() {
    hideButtons();
    var imageDiv = document.getElementById('imageDiv');

    imageDiv.style = "display: none;";
    if (jQuery_capFoto("#canvas_img").data('Jcrop')) {
        jQuery_capFoto("#canvas_img").data('Jcrop').destroy();
    }
}

function showImageCanvas(imgSrc, goingBack) {
    hideUploadTab();
    hideWebcamTab();
    var cropBtn = document.getElementById('crop_image_btn');
    var cancelBtn = document.getElementById('cancel_btn');
    var imageDiv = document.getElementById('imageDiv');
    cropBtn.style = "display: inline-block;";
    cancelBtn.style = "display: inline-block;";
    imageDiv.style = "display: block;";
    var canvasImg = document.getElementById("canvas_img");
    if (finishedEdit || canvasImg === null) { //Obviously it didn't
        toggleModalSize(false);
        canvasImg = resetImageCanvas();
        finishedEdit = false;
    }
    originalImage.onload = function () {
        canvasImg.style = "display: block;";
        selectBoxMaxSize = canvasImg.clientHeight;
        var scaledHeight = 0, scaledWidth = 0;
        var imageOriginalAspectRatio = originalImage.width / originalImage.height;
        var imageBoxAspectRatio = canvasImg.clientWidth / canvasImg.clientHeight;
        if (imageOriginalAspectRatio > imageBoxAspectRatio) {
            scaledWidth = canvasImg.clientWidth;
            scaledHeight = scaledWidth / imageOriginalAspectRatio;
        } else {
            scaledHeight = canvasImg.clientHeight;
            scaledWidth = scaledHeight * imageOriginalAspectRatio;
        }
        canvasImg.style = "display: block; width: auto; height: auto;";
        canvasImg.width = scaledWidth;
        canvasImg.height = scaledHeight;
        canvasSize.width = canvasImg.width;
        canvasSize.height = canvasImg.height;
        canvasImg.getContext('2d').drawImage(originalImage, 0, 0, scaledWidth, scaledHeight);

        var half_width, half_height, posXi, posYi, posXf, posYf;
        if (!goingBack) {
            half_width = (canvasImg.width / 2);
            half_height = (canvasImg.height / 2);
            posXi = half_width - (selectBoxMaxSize / 2);
            posYi = half_height - (selectBoxMaxSize / 2);
            posXf = half_width + (selectBoxMaxSize / 2);
            posYf = half_height + (selectBoxMaxSize / 2);
        } else {
            posXi = selectionCoords.x;
            posYi = selectionCoords.y;
            posXf = selectionCoords.x2;
            posYf = selectionCoords.y2;
        }
        jQuery_capFoto("#canvas_img").Jcrop({
            onSelect: registerSelectedCoords,
            bgColor: 'black',
            bgOpacity: .4,
            setSelect: [posXi, posYi, posXf, posYf],
            maxSize: [selectBoxMaxSize, selectBoxMaxSize],
            maxSize: [selectBoxMaxSize, selectBoxMaxSize],
            aspectRatio: 1,
            addClass: 'jcrop-dark'
        });
    };
    originalImage.src = imgSrc;
}

function hideWebcamTab() {
    hideButtons();
    var capturaFotoDiv = document.getElementById("capturaFotoDiv");
    var webcamContainer = document.getElementById('video_container');
    var loadingWebcam = document.getElementById("loading_webcam");
    var loadingWebcamLbl = document.getElementById("loading_webcam_lbl");
    jQuery_capFoto('#link_capturar_webcam').removeClass("active");

    Webcam.reset();
    if (jQuery_capFoto("video").data('Jcrop')) {
        jQuery_capFoto("video").data('Jcrop').destroy();
    }

    loadingWebcam.style.display = "none";
    loadingWebcamLbl.style.display = "none";
    webcamContainer.style = "display: none;";
    clearElement(webcamContainer);
    capturaFotoDiv.style = "display: none;";
}

function showWebcamTab() {
    hideImageCanvas();
    hideUploadTab();
    if (finishedEdit) { //showFinishTab was called
        toggleModalSize(false);
        resetImageCanvas();
        finishedEdit = false;
    }
    var capturaFotoDiv = document.getElementById("capturaFotoDiv");
    var webcamContainer = document.getElementById('video_container');
    var snapBtn = document.getElementById('snapshot_btn');
    jQuery_capFoto('#link_capturar_webcam').addClass("active");
    webcamContainer.style = "display: block;";
    capturaFotoDiv.style = "display: block;";
    
    var loadingWebcam = document.getElementById("loading_webcam");
    var loadingWebcamLbl = document.getElementById("loading_webcam_lbl");
    loadingWebcam.style.display = "block";
    loadingWebcamLbl.style.display = "block";
    loadingWebcamLbl.style.left = "calc(50% - calc(" + loadingWebcamLbl.clientWidth+"px / 2))";
    loadingWebcamLbl.style.top = "calc(50% + calc((" + loadingWebcam.clientHeight+"px * 1.5)/ 2) + 35px)";
    loadingWebcam.style.left = "calc(50% - calc(" + loadingWebcam.clientWidth+"px / 2))";
    loadingWebcam.style.top = "calc(50% - calc(" + loadingWebcam.clientHeight + "px / 2) + 50px)";
    
    var attachJcrop = function (videoElem) {
        //GTC: Decidindo por usar o tamanho m�ximo do v�deo sendo  a altura do box da webcam
        videoBoxMaxSize = jQuery_capFoto(videoElem).height();

        var half_width = (jQuery_capFoto(videoElem).width() / 2);
        var half_height = (jQuery_capFoto(videoElem).height() / 2);
        var posXi = half_width - (videoBoxMaxSize / 2);
        var posYi = half_height - (videoBoxMaxSize / 2);
        var posXf = half_width + (videoBoxMaxSize / 2);
        var posYf = half_height + (videoBoxMaxSize / 2);
        registerSelectedCoords({
            x: posXi, y: posYi, x2: posXf, y2: posYf, w: videoBoxMaxSize, h: videoBoxMaxSize
        });
        jQuery_capFoto(videoElem).Jcrop({
            bgColor: 'black',
            bgOpacity: .4,
            setSelect: [posXi, posYi, posXf, posYf],
            maxSize: [videoBoxMaxSize, videoBoxMaxSize],
            aspectRatio: 1,
            addClass: 'jcrop-dark'
        });
        jQuery_capFoto(".jcrop-tracker").addClass("jcrop-tracker noDrag");
        jQuery_capFoto(".jcrop-dragbar").addClass("jcrop-dragbar disabled");
        jQuery_capFoto(".jcrop-handle").addClass("jcrop-handle disabled");
        //work-around weird glitch that makes the left and right get inverted showing the radio input.
        jQuery_capFoto(".jcrop-keymgr").css("left", "10000px");
    };

    Webcam.on("error", function (err) {
        console.log(err);
        loadingWebcam.style.display = "none";
        loadingWebcamLbl.style.display = "none";
        if (jQuery_capFoto("video") != null && jQuery_capFoto("video").data("Jcrop") != null) {
            jQuery_capFoto("video").data("Jcrop").destroy();
        }
        Webcam.reset();
        clearElement(webcamContainer);
        var msgErrorVideo_hiddenVerticalAligner = document.createElement("div");
        msgErrorVideo_hiddenVerticalAligner.content = '';
        msgErrorVideo_hiddenVerticalAligner.style = "display: block; height: 49%;";
        var msgErrorVideo = document.createElement("span");
        var detailedErrorMsg = document.createElement("div");
        if (err.name === "DevicesNotFoundError") {
            detailedErrorMsg.innerHTML = "N�o foi encontrada nenhuma c�mera conectada ao computador!";
            detailedErrorMsg.style = "color: black; font-style: italic; display: block;";
        }
        else if(err.name === "PermissionDeniedError"){
            detailedErrorMsg.innerHTML = "O acesso � c�mera foi negado! Por favor, permita o acesso no seu navegador."
                +"<br>Obs: Ap�s habilitar a permiss�o, clique aqui para recarregar o v�deo!";
            detailedErrorMsg.style = 
                    "color: black;"
                   +"font-style: italic;"
                   +"display: block;";
           detailedErrorMsg.addEventListener("click", function(){
                showWebcamTab();
           });
        }
        msgErrorVideo.textContent = "Houve um problema durante o carregamento do v�deo.";
        msgErrorVideo.style = "color: red; font-weight: bold; display:block;";
        webcamContainer.appendChild(msgErrorVideo_hiddenVerticalAligner);
        webcamContainer.appendChild(msgErrorVideo);
        webcamContainer.appendChild(detailedErrorMsg);
    });

    Webcam.on("load", function () {
        jQuery_capFoto("video")[0].id = "webcamVideo";
    });
    Webcam.on("live", function(){
        loadingWebcam.style.display = "none";
        loadingWebcamLbl.style.display = "none";
        snapBtn.style.display = "inline-block";
    });
    Webcam.set({
        image_format: 'jpeg',
        jpeg_quality: 90,
        enable_flash: false,
        flip_horiz: true
    });

    Webcam.set('constraints', {optional: [{ minWidth: 700 }]});

    /*  O plugin webcam.js foi modificado. Na fun��o attach foi adicionado 
     * um par�metro. Este par�metro � ent�o invocado antes de setar o "src" 
     * do elemento v�deo gerado passando o v�deo como par�metro. O c�digo 
     * des-minificado abaixo mostra a modifica��o feita,
     * n=video, z=novo par�metro;
     * 
     *  if(z !== null){
            z(n);
        }
     */
    Webcam.attach('video_container', attachJcrop);
}

function hideUploadTab() {
    hideButtons();
    var capturaFotoDiv = document.getElementById("capturaFotoDiv");
    var uploadTab = document.getElementById('drop_zone');
    jQuery_capFoto('#link_enviar_foto').removeClass("active");
    capturaFotoDiv.style = "display: none;";
    uploadTab.style = "display: none;";
}

function showUploadTab() {
    hideImageCanvas();
    hideWebcamTab();
    toggleModalSize(false);
    var capturaFotoDiv = document.getElementById("capturaFotoDiv");
    var uploadTab = document.getElementById('drop_zone');
    jQuery_capFoto('#link_enviar_foto').addClass("active");
    jQuery_capFoto('#upload_image_btn').val('');
    capturaFotoDiv.style = "display: block;";
    uploadTab.style = "display: block;";
}

function showFinishTab(canvasImg) {
    hideButtons();
    hideImageCanvas();
    hideUploadTab();
    hideWebcamTab();
    finishedEdit = true;

    var imageDiv = document.getElementById("imageDiv");
    var finishBtn = document.getElementById("finish_btn");
    var backBtn = document.getElementById("back_btn");

    clearElement(imageDiv);

    var previewSize = 0.4 * jQuery_capFoto(imageDiv).height(); // Show preview occupying 40% of the parent div height
    var previewCanvas = document.createElement("canvas");
    var tmpImg = new Image();
    tmpImg.onload = function () {
        previewCanvas.width = previewCanvas.height = previewSize;
        previewCanvas.getContext('2d').drawImage(tmpImg, 0, 0, previewSize, previewSize);
    };
    tmpImg.src = canvasImg.toDataURL();
    var finishMsg = document.createElement("span");
    var circleOverlayDiv = document.createElement("div");

    finishMsg.textContent = "Gostou?";
    finishMsg.style =
            "font-weight: bold;"
            + "font-style: italic;"
            + "color: #094771;"
            + "position: absolute;";
    circleOverlayDiv.id = "circleOverlay";
    circleOverlayDiv.style =
            "height: auto;"
            + "width: auto;"
            + "top: calc(50% - " + Math.floor(previewSize / 2) + "px);"
            + "left: calc(50% - " + Math.floor(previewSize / 2) + "px);"
            + "overflow: hidden;"
            + "border-radius: 50%;"
            + "position: absolute;";
    previewCanvas.style = "display: block; width: auto; height: auto;";
    imageDiv.style.position = "relative";
    canvasImg.style.display = "none";
    imageDiv.style.display = "block";
    finishBtn.style = "display: inline-block;";
    backBtn.style = "display: inline-block;";

    toggleModalSize(true);

    imageDiv.appendChild(finishMsg);
    imageDiv.appendChild(circleOverlayDiv);
    imageDiv.appendChild(resizeImage(canvasImg));
    circleOverlayDiv.appendChild(previewCanvas);

    finishMsg.style.left = "calc(50% - " + Math.floor(jQuery_capFoto(finishMsg).width() / 2) + "px)";
    finishMsg.style.marginTop = jQuery_capFoto(finishMsg).height() + "px";
}

function setAttributesModalCapFoto(key, code, context, user) {
    chaveAcademia = key;
    codigoPessoa = code;
    contexto = context;
    usuarioLogado = user;
}

function initializeModalCapFoto() {
    if(!initialized){
        jQuery_capFoto(document).ajaxStart(function () {
            Richfaces.showModalPanel('panelStatus1');
            resetTime(tempoEmMillis);
            setLastActionTime();
            jQuery_capFoto('body').css({'overflow': 'hidden'});
        }).ajaxStop(function () {
            Richfaces.hideModalPanel('panelStatus1');
            document.getElementById('idtextoCarregando').innerHTML = '';
            jQuery_capFoto('body').css({'overflow': 'auto'});
            executarFuncoesJs();
        });

        /* Private function to get a style class object from it's name
         * Usage: _initi_initializeModalCapFoto_getStyleClass(".className");
         */
        var _initializeModalCapFoto_getStyleClass = function (name) {
            var ix, sheet;
            for (var i = document.styleSheets.length - 1; i >= 0; i--) {
                sheet = document.styleSheets[i];
                for (ix = sheet.cssRules.length - 1; ix >= 0; ix--) {
                    if (sheet.cssRules[ix].selectorText === name)
                        return sheet.cssRules[ix].style;
                }
            }
            return null;
        };

        // Setup the dnd listeners.
        var dropZone = document.getElementById('drop_zone');
        dropZone.addEventListener('dragover', handleDragOver, false);
        dropZone.addEventListener('drop', handleFileSelect, false);
        // Setup the upload listener
        var uploadButton = document.getElementById('upload_image_btn');
        uploadButton.addEventListener('change', handleFileSelect, false);
        // Setup the tabs handlers
        var webcamTab = document.getElementById('link_capturar_webcam');
        var uploadTab = document.getElementById('link_enviar_foto');
        webcamTab.addEventListener("click", showWebcamTab, false);
        uploadTab.addEventListener("click", showUploadTab, false);
        // Setup button listeners
        var snapBtn = document.getElementById('snapshot_btn');
        var cropBtn = document.getElementById('crop_image_btn');
        var cancelBtn = document.getElementById('cancel_btn');
        var finishBtn = document.getElementById('finish_btn');
        var backBtn = document.getElementById('back_btn');

        snapBtn.addEventListener("click", takeSnapshot, false);
        cropBtn.addEventListener("click", function () {
            canvasCrop("edit");
        }, false);
        cancelBtn.addEventListener("click", showUploadTab, false);
        finishBtn.addEventListener("click", uploadCroppedImage, false);
        backBtn.addEventListener("click", goBack, false);

        // Set dynamic height to the uploadBox style class
        var uploadBoxStyleClass = _initializeModalCapFoto_getStyleClass(".uploadBox");
        var menuTabs = document.getElementById("menu_captura");
        var menuHeight = parseInt(window.getComputedStyle(menuTabs).getPropertyValue("margin-bottom"))
                + menuTabs.offsetHeight;
        var btnMaxHeight = Math.max(snapBtn.getHeight(), cropBtn.getHeight(),
                cancelBtn.getHeight(), finishBtn.getHeight(), backBtn.getHeight());
        uploadBoxStyleClass.height = "calc(100% - " + (menuHeight + btnMaxHeight + 10) + "px)";
        
        initialized = true;
    }

    //showUploadTab();
    showWebcamTab();
}
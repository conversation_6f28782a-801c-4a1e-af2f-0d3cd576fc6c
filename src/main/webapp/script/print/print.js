var eventosPrintRegistrados = false;

function enviarPrints(){
    if(temSuporteDbLocal() && getGerarPrints()){

        getPrintsImagem(function (imagens) {
            getPrintsHtml(function (htmls) {

                if(imagens.length > 0 || htmls.length > 0){
                    var urlApiPrint = window.location.origin + contexto + '/monitor/print';
                    fetch(urlApiPrint, {
                        method: 'POST',
                        body:  JSON.stringify({"imagens": imagens, "htmls": htmls})
                    }).then(function(res){
                        if(res.ok){
                            return res.json();
                        }else{
                            console.log('N�o foi poss�vel enviar os prints para monitoriamento de erros: ', res)
                        }
                    }).then(function (json) {
                        if(json !== undefined && json.sucesso === true){
                            console.log('Os Prints para monitoriamento de erros foram registrados: ', json)
                        }
                    })
                }

            });
        });
    }
}
function gravarCopiaHtml(minified){
    if(temSuporteDbLocal() && getGerarPrints()){
        var minify = require('html-minifier').minify;
        var html = '<html>'+
            document.documentElement.innerHTML +
            '<html>';

        var nomeArquivo = montarNomeArquivo() +  '.html';

        if(minified){
            minified = minify(html, {removeAttributeQuotes: true});
            addPrintsHtml(minified, nomeArquivo);
        }else{
            addPrintsHtml(html, nomeArquivo);
        }
    }
}
function gravarPrintPagina(download){
    if(temSuporteDbLocal() && getGerarPrints()){
        var element = document.getElementsByTagName('body')[0];
        html2canvas(element, { allowTaint : false }).then(function(canvas){
            var base64 = canvas.toDataURL("image/png");
            var nomeArquivo = montarNomeArquivo() +  '.png';
            if(download === true){
                var link = document.createElement('a');
                link.download = nomeArquivo;
                link.id = "print-donwload";
                link.href =  base64;

                link.click();
            }

            addPrintImagem(base64, nomeArquivo);
        });
    }
}
function registrarEventosClick(nodes){
    for (var i = 0; i < nodes.length; i++) {
        jQuery(nodes[i]).bind('click', function(e){
            var identificacaoElemento = "";
            if(e.target !== undefined){
                if(e.target.id !== undefined && e.target.id.length > 0){
                    identificacaoElemento += ":"+e.target.id;
                }else if(e.target.name != undefined){
                    identificacaoElemento += ":"+e.target.name;
                }
            }
            gravarPrintPagina(false, "ao_clicar_em_"+identificacaoElemento);
        });
    }
}
function registrarEventosPrintEmCiclo(){
    jQuery( document ).ajaxComplete(function(e) {
        if(getCicloPrintIniciado() === true){
            setTimeout(function(){
                gravarPrintPagina(true, "depois_clicar_"+getIdentificacaoElemento());
                finalizarCicloPrint();
            }, 1000);
        }
    });

    var acoes = jQuery("a[onclick*='A4J.AJAX.Submit");
    for (var i = 0; i < acoes.length; i++) {
        jQuery(acoes[i]).bind('click', function(e){
            var identificacaoElemento = "";
            if(e.target !== undefined){
                if(e.target.id !== undefined && e.target.id.length > 0){
                    identificacaoElemento += ":"+e.target.id;
                }else if(e.target.name != undefined){
                    identificacaoElemento += ":"+e.target.name;
                }
            }
            iniciarCicloPrint();
            setIdentificacaoElemento(identificacaoElemento);
            gravarPrintPagina(true, "antes_clicar_"+identificacaoElemento);
        });
    }
}
function iniciarEventosPrint(e){
    document.addEventListener("DOMContentLoaded", function(){
        if(eventosPrintRegistrados === false){
            var links = jQuery("a[onclick*=\"A4J.AJAX.Submit");
            registrarEventosClick(links);
        }

        gravarPrintPagina(true, "ao_carregar_pagina_"+e.documentURI);
    }, false);

}
function montarNomeArquivo(){
    var nome = empresa +"-"+usuario;
    nome += "-"+document.location.pathname.replace(/\/|:|\./g, '-');
    nome += "-"+ new Date().toISOString().replace(/:|\.|-|T|Z/g, '');

    return nome;
}
function getCicloPrintIniciado() {
    return (localStorage.getItem("print:cicloPrintIniciado") === 'true');
}
function iniciarCicloPrint() {
    return localStorage.setItem("print:cicloPrintIniciado", true);
}
function finalizarCicloPrint() {
    return localStorage.setItem("print:cicloPrintIniciado", false);
}
function getIdentificacaoElemento() {
    return localStorage.getItem("print:identificacaoElemento");
}
function setIdentificacaoElemento(identificacao) {
    return localStorage.setItem("print:identificacaoElemento", identificacao);
}
function getGerarPrints(){
    return gerarPrintsAcoesUsuario === true ? true : false;
}
function getLimitePrints(){
    return limitPrintsAcoesUsuario === undefined ? 0 : limitPrintsAcoesUsuario;
}
function limitarPrintsHtml(){
    limitar("html");
}
function limitarPrintsImagens(){
    limitar("imagem");
}
function limitar(nomeObjectStore){
    connectIndexDb(function (db, request) {
        var countRequest = db.transaction([nomeObjectStore], 'readonly').objectStore(nomeObjectStore).count();
        countRequest.onsuccess = function() {
            var totalImagens = countRequest.result;

            if(totalImagens > limitPrintsAcoesUsuario){

                var imagemStore = db.transaction([nomeObjectStore], 'readwrite').objectStore(nomeObjectStore);
                imagemStore.openCursor().onsuccess = function(ev) {
                    var cursor = event.target.result;
                    if (cursor) {
                        cursor.delete();
                    }
                }
            }
        }
    });
}
function addPrintsHtml(html, nomeArquivo){
    if(nomeArquivo === undefined){
        nomeArquivo = montarNomeArquivo();
    }

    connectIndexDb(function(db){
        var transaction = db.transaction(["html"], "readwrite");
        var htmlStore = transaction.objectStore("html");
        transaction.oncomplete = function(event) {
            limitarPrintsHtml();
        };
        htmlStore.add({
            html: html,
            nome: nomeArquivo,
            usuario: usuario,
            empresa: empresa,
            chave: chave
        },nomeArquivo);
    });
}
function addPrintImagem(imagem, nomeArquivo){
    if(nomeArquivo === undefined){
        nomeArquivo = montarNomeArquivo();
    }

    connectIndexDb(function(db){
        var transaction = db.transaction(["imagem"], "readwrite");
        var imagemStore = transaction.objectStore("imagem");
        transaction.oncomplete = function(event) {
            limitarPrintsImagens();
        };
        imagemStore.add({
            imagem: imagem,
            nome: nomeArquivo,
            usuario: usuario,
            empresa: empresa,
            chave: chave
        }, nomeArquivo);
    });
}
function getPrintsHtml(callback){
    connectIndexDb(function(db, request){
        var htmlStore = db.transaction(["html"], 'readonly').objectStore("html");
        var htmls = [];
        htmlStore.openCursor().onsuccess = function(ev) {
            var cursor = event.target.result;
            if (cursor) {
                htmls.push(cursor.value)
                cursor.continue();
            }else{
                callback(htmls);
            }
        }
    });
}
function getPrintsImagem(callback){
    connectIndexDb(function(db, request){
        var imagemStore = db.transaction(["imagem"], 'readonly').objectStore("imagem");
        var imagens = [];
        imagemStore.openCursor().onsuccess = function(ev) {
            var cursor = event.target.result;
            if (cursor) {
                imagens.push(cursor.value)
                cursor.continue();
            }else{
                callback(imagens);
            }
        }
    });
}
function temSuporteDbLocal(){
    return (typeof window.indexedDB === 'object');
}
function connectIndexDb(callback){
    var db;
    var request = window.indexedDB.open("prints", 3);

    request.onsuccess = function(event) {
        db = event.target.result;
        callback(db, request);
    };

    request.onupgradeneeded = function(event) {
        var db = event.target.result;
        var imagemStore = db.createObjectStore("imagem", { autoincrement: true });
        var htmlStore = db.createObjectStore("html", { autoincrement: true });
    };
}

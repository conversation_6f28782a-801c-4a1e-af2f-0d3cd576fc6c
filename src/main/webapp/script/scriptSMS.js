var limite = 140;

function soma() {
    var elemento = (document.getElementById('formF:comentarioTextArea'));
    var tamanhoRestante = document.getElementById('formF:tamanhoRestante');

    var mais_um = eval(elemento.value.length - 1);
    mais_um++;

    if (elemento.value.length > limite) {
        elemento.value = '';
        elemento.value = valor_limite;
    }
    else {
        valor_limite = elemento.value;
        tamanhoRestante.value = '';
        tamanhoRestante.value = (limite - mais_um);
    }
    elemento.value = retirarAcentuacao(elemento.value);
    elemento.focus();
}

function somaCRM() {
    var elemento = (document.getElementById('form:mensagemSMS'));
    var tamanhoRestante = document.getElementById('form:tamanhoMensagemSMS');

    var mais_um = eval(elemento.value.length - 1);
    mais_um++;

    if (elemento.value.length > limite) {
        elemento.value = '';
        elemento.value = valor_limite;
    }
    else {
        valor_limite = elemento.value;
        tamanhoRestante.value = '';
        tamanhoRestante.value = (limite - mais_um);
    }
    elemento.value = retirarAcentuacao(elemento.value);
    elemento.focus();
}

function somaCRMCustom(idElement, idMessage, size, inner) {
    var elemento = (document.getElementById(idElement));
    var tamanhoRestante = document.getElementById(idMessage);

    var mais_um = eval(elemento.value.length - 1);
    mais_um++;

    if (elemento.value.length > size) {
        elemento.value = valor_limite;
    }
    else {
        valor_limite = elemento.value;
        if(inner){
            tamanhoRestante.innerHTML = (size - mais_um);
        }else{
            tamanhoRestante.value = (size - mais_um);
        }
    }
    elemento.focus();
}

function somaCRMColetivo() {
    var elemento = (document.getElementById('form:mensagemSMSColetivo'));
    var tamanhoRestante = document.getElementById('form:tamanhoRestanteSMSColetivo');

    var mais_um = eval(elemento.value.length - 1);
    mais_um++;

    if (elemento.value.length > limite) {
        elemento.value = '';
        elemento.value = valor_limite;
    }
    else {
        valor_limite = elemento.value;
        tamanhoRestante.value = '';
        tamanhoRestante.value = (limite - mais_um);
    }
    elemento.value = retirarAcentuacao(elemento.value);
    elemento.focus();
}

function retirarAcentuacao(texto) {
    var msgSemAcento = texto;
    msgSemAcento = msgSemAcento.toString().replace("�", "a", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "a", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "a", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "a", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "a", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "A", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "A", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "A", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "A", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "A", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "e", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "e", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "e", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "e", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "E", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "E", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "E", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "E", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "i", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "i", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "i", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "i", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "I", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "I", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "I", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "I", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "o", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "o", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "o", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "o", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "o", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "O", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "O", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "O", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "O", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "O", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "u", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "u", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "u", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "u", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "U", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "U", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "U", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "U", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "C", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "c", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "y", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "y", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "Y", "");

    msgSemAcento = msgSemAcento.toString().replace("�", "n", "");
    msgSemAcento = msgSemAcento.toString().replace("�", "N", "");

    return msgSemAcento;
}
function mostra_tamanho() {
    tamanhoRestante.value = limite;
}
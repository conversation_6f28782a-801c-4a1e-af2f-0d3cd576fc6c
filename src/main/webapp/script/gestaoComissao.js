function atualizarTreeViewComissao() {
    jQuery(document).ready(function($) {

        $(".comissao").treeTable({
            initialState: "collapsed"
        });
    });

    jQuery(document).ready(function($){
    	$(".expandir").click(function() {
       	 $(".comissao").expandirTudo();
       	});

    	$(".expandirUm").click(function() {
         	 $(".comissao").expandirUmNivel("dnd-comissao");
       });

    	$(".retrairUm").click(function() {
        	 $(".comissao").retrairUmNivel("dnd-comissao");
      });

    	$(".retrair").click(function() {
          	 $(".comissao").retrairTudo();
        });

           });
}

function trocarAba(valor){
	var filtroSin = document.getElementById('form:filtroSin');
    var impSin = document.getElementById('form:impSin');
    var impAn = document.getElementById('form:impAn');
    var excelAn = document.getElementById('form:excelAn');
    var filtroAn = document.getElementById('form:filtroAn');

    filtroSin.style.display='none';
    impSin.style.display='none';
    impAn.style.display='none';
    filtroAn.style.display='none';
    excelAn.style.display='none';

    if(valor == 'sintetico'){
    	filtroSin.style.display='block';
    	impSin.style.display='block';
    }else{
        impAn.style.display='block';
        filtroAn.style.display='block';
        excelAn.style.display='block';
    }
}
function preencherValorChamarBotao(idBotao, idClienteHidden, valorClienteHidden){
    var clienteHidden = document.getElementById(idClienteHidden);
    var botao = document.getElementById(idBotao);
    clienteHidden.value = valorClienteHidden;
    botao.click();
}

function esconderMostrar(caixa, retrair, retrairClicado, expandirNovo, controle){
    var table = document.getElementById(caixa);
    var aRetrair = document.getElementById(retrairClicado);
    var aExpandir = document.getElementById(expandirNovo);
    var controleOpened = document.getElementById(controle);
    if(retrair){
        controleOpened.value = false;
        table.style.display="none";
    }else{
        controleOpened.value = true;
        table.style.display="block";
    }
    aRetrair.style.display="none";
    aExpandir.style.display="block";
}


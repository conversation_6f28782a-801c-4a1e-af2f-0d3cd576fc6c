    function consistirObrigatorio(check, tipo){
    var marcando = check.checked;
    var idCheckUtilizada = check.id;
    var splitIdPorDoisPontos = idCheckUtilizada.split(":");
    var terceiroElemento = splitIdPorDoisPontos[2];
                                                
    var mostrar = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ terceiroElemento +':mostrar');
    var pendencia = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ terceiroElemento +':pendente');
    if(marcando){
        mostrar.checked = "checked";
        pendencia.checked = null;
    }
}

function consistirPendente(check, tipo){
    var marcando = check.checked;
    var idCheckUtilizada = check.id;
    var splitIdPorDoisPontos = idCheckUtilizada.split(":");
    var terceiroElemento = splitIdPorDoisPontos[2];

    var obrigatorio = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ terceiroElemento +':obrigatorio');
    var mostrar = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ terceiroElemento +':mostrar');
    if(marcando){
        mostrar.checked = "checked";
        obrigatorio.checked = null;
    }
}

function consistirApresentar(check, tipo){
    var marcando = check.checked;
    var idCheckUtilizada = check.id;
    var splitIdPorDoisPontos = idCheckUtilizada.split(":");
    var terceiroElemento = splitIdPorDoisPontos[2];

    var obrigatorio = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ terceiroElemento +':obrigatorio');
    var pendencia = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ terceiroElemento +':pendente');
    if(marcando == false){
        pendencia.checked = null;
        obrigatorio.checked = null;
    }
}

function marcarTodosObrigatorios(tipo){
    var hiddenQuantRegistrosCliente = document.getElementById('form:hiddenQuantRegistros'+tipo);
    var todos = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:obrigatorioTodos');
    var mostrar = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:mostrarTodos');
    var pendentes = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:pendenteTodos');
    if(todos.checked) {
        mostrar.checked = todos.checked;
        pendentes.checked = !todos.checked;
    }
    for (var i = 0; i < hiddenQuantRegistrosCliente.value; i++){
        var idCheckObrigatorio = 'form:listaConfiguracaoCampos'+tipo+':'+ i +':obrigatorio';
        var checkDoLoop = document.getElementById(idCheckObrigatorio);
        var pendencia = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':pendente');
        var obrigatorio = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':obrigatorio');
        mostrar = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':mostrar');

        checkDoLoop.checked = todos.checked;
        obrigatorio.checked = todos.checked;
        if(todos.checked) {
            mostrar.checked = todos.checked;
            pendencia.checked = null;
        }
    }
}

function marcarTodosApresentar(tipo){
    var hiddenQuantRegistrosCliente = document.getElementById('form:hiddenQuantRegistros'+tipo);
    var todos = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:mostrarTodos');
    var obrigatorios = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:obrigatorioTodos');
    var pendentes = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:pendenteTodos');
    if(!todos.checked) {
        pendentes.checked = todos.checked;
        obrigatorios.checked = todos.checked;
    }
    for (var i = 0; i < hiddenQuantRegistrosCliente.value; i++){
        var idCheckMostrar = 'form:listaConfiguracaoCampos'+tipo+':'+ i +':mostrar';
        var checkDoLoop = document.getElementById(idCheckMostrar);
        var pendencia = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':pendente');
        var obrigatorio = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':obrigatorio');
        var mostrar = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':mostrar');

        checkDoLoop.checked = todos.checked;
        mostrar.checked = todos.checked;
        if(!todos.checked) {
            pendencia.checked = null;
            obrigatorio.checked = null;
        }
    }
}

function marcarTodosPendentes(tipo){
    var hiddenQuantRegistrosCliente = document.getElementById('form:hiddenQuantRegistros'+tipo);
    var todos = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:pendenteTodos');
    var mostrar = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:mostrarTodos');
    var obrigatorios = document.getElementById('form:listaConfiguracaoCampos'+tipo+'Teste:0:obrigatorioTodos');
    if(todos.checked) {
        mostrar.checked = todos.checked;
        obrigatorios.checked = !todos.checked;
    }
    for (var i = 0; i < hiddenQuantRegistrosCliente.value; i++){
        var idCheckPendente = 'form:listaConfiguracaoCampos'+tipo+':'+ i +':pendente';
        var checkDoLoop = document.getElementById(idCheckPendente);
        var pendencia = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':pendente');
        var obrigatorio = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':obrigatorio');
        mostrar = document.getElementById('form:listaConfiguracaoCampos'+tipo+':'+ i +':mostrar');

        checkDoLoop.checked = todos.checked;
        pendencia.checked = todos.checked;
        if(todos.checked) {
            mostrar.checked = todos.checked;
            obrigatorio.checked = null;
        }
    }
}

    

				function setaMensagemConsulta(){
					var periodo = alternarMensagemFiltro();
					var ambiente = alternarMensagemAmbiente();
					var mensagem ='';
					mensagem = '<h:outputText value="Consulta de Eventos e Visitas '+periodo+', '+ambiente+'."/>';
					exibirMensagem(mensagem, 'mensagemBusca');
					

					
				}
        		function setaFiltroIntervalo(){
        			var itens = document.getElementsByName('form:tipoFiltroPeriodo');
        			itens[1].checked=true;
        			alternarFiltro();
				
        		}
				function alternarFiltro() {
					limparMensagensFiltros();
					var itens = document.getElementsByName('form:tipoFiltroPeriodo');
					for (var i = 0; i < itens.length; i++) {
						if (itens[i].checked) {
							var divFiltroData = document.getElementById('filtroData');
							var divFiltroIntervalo = document.getElementById('filtroIntervalo');
							var divFiltroMesSemana = document.getElementById('filtroMesSemana');
							switch (itens[i].value) {
							case '1':
								divFiltroData.style.visibility='visible';
								divFiltroIntervalo.style.visibility='hidden';
								divFiltroMesSemana.style.visibility='hidden';
								break;
							case '2':
								divFiltroData.style.visibility='hidden';
								divFiltroIntervalo.style.visibility='visible';
								divFiltroMesSemana.style.visibility='hidden';
								break;
							case '3':
								divFiltroData.style.visibility='hidden';
								divFiltroIntervalo.style.visibility='hidden';
								divFiltroMesSemana.style.visibility='visible';
								break;
							}
						}
					}
				}
				
				function alternarMensagemFiltro() {
	            	limparMensagem('mensagemFiltro');
					var itens = document.getElementsByName('form:tipoFiltroPeriodo');
					var mensagem='';
					var retorno='';
					for (var i = 0; i < itens.length; i++) {
						if (itens[i].checked) {
							var data = document.getElementById('form:dataInputDate');
							var dataInicio = document.getElementById('form:dataInicioInputDate');
							var dataFim = document.getElementById('form:dataFimInputDate');
							var mes = document.getElementById('form:meses');
							var semana = document.getElementById('form:semanas');
							var intIndex = mes.selectedIndex;
							var labelMes = mes.options[intIndex].text;
							
							switch (itens[i].value) {
							case '1':
								mensagem = '<h:outputText value="Eventos e Visitas no dia '+data.value+'"/>';
								exibirMensagem(mensagem, 'mensagemFiltro');
								retorno= 'no dia '+ data.value;
								
								break;
							case '2':
								if ((dataInicio != null && dataInicio.value != null && dataInicio.value != "")&&(dataFim != null && dataFim.value != null && dataFim.value != "")) {
								mensagem = '<h:outputText value="Eventos e Visitas do dia '+dataInicio.value+' ao dia ' +dataFim.value+'"/>';
								exibirMensagem(mensagem, 'mensagemFiltro');
								retorno='do dia ' +dataInicio.value+' ao dia '+dataFim.value;
								}
								break;
							case '3':
								
								if (semana != null && semana.value != null && semana.value != ""){
								mensagem = '<h:outputText value="Eventos e Visitas do m�s de '+labelMes+ ', '+semana.value+'� semana"/>';
								retorno = 'no m�s de '+labelMes+',  '+semana.value+'�semana';
								}else{
									mensagem = '<h:outputText value="Eventos e Visitas do m�s de '+labelMes+'"/>';
									retorno = 'no m�s de '+labelMes;
								}
								exibirMensagem(mensagem, 'mensagemFiltro');
								break;
							}
						}
					}
					return retorno;
				}
				function alternarMensagemAmbiente() {
					
					limparMensagem('mensagemAmbiente');
					var ambiente = document.getElementById('form:ambientes');
					var tipo = document.getElementById('form:tiposAmbiente');
					var mensagem='';
					var retorno='';
					
					if ((tipo == null || tipo.value == null || tipo.value == "")&&(ambiente == null || ambiente.value == null || ambiente.value == "")){
							mensagem = '<h:outputText value="Eventos e Visitas de todos os ambientes."/>';
							exibirMensagem(mensagem, 'mensagemAmbiente');
							retorno = 'de todos os ambientes';
					}else{
						if (ambiente == null || ambiente.value == null || ambiente.value == ""){
							var intIndexTipo = tipo.selectedIndex;
							var labelTipo = tipo.options[intIndexTipo].text;
							mensagem = '<h:outputText value="Eventos e Visitas de todos os ambientes do tipo '+labelTipo+'."/>';
							exibirMensagem(mensagem, 'mensagemAmbiente');
							retorno = 'de todos os ambientes do tipo '+labelTipo;
						}else{
							var intIndexAmb = ambiente.selectedIndex;
							var labelAmbiente = ambiente.options[intIndexAmb].text;
							mensagem = '<h:outputText value="Eventos e Visitas do ambiente '+labelAmbiente+'."/>';
							exibirMensagem(mensagem, 'mensagemAmbiente');
							retorno= 'do ambiente '+labelAmbiente;
						}


				    }
				    return retorno;
								
				}
				function validarFiltros() {
					limparMensagensFiltros();
					var itens = document.getElementsByName('form:tipoFiltroPeriodo');
					var data = document.getElementById('form:dataInputDate');
					var dataInicio = document.getElementById('form:dataInicioInputDate').value;
					var dataFim = document.getElementById('form:dataFimInputDate').value;
					
					var validade=true;
					
					for (var i = 0; i < itens.length; i++) {
						if (itens[i].checked) {
							switch (itens[i].value) {
							case '1':
								if (data == null || data.value == null || data.value == "") {
									exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.data\']}"/>', 'filtroData');
									validade = false;
								}	
								break;
							case '2':
								if ((dataInicio == null || dataInicio == null || dataInicio == "")||(dataFim == null || dataFim == null || dataFim == "")) {
									exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.intervalo\']}"/>', 'filtroIntervalo');
									validade = false;
								}
								break;
							
							}
						}
					}
					if (dataInicio[2] > dataFim[2]){
						validade = true;
					}						
						if (dataInicio > dataFim){
							exibirMensagem('<h:outputText value="Data inicial maior a data final"/>','dataFim');
							validade = false;
						}
					return validade;
				}
				
				
				function limparMensagensFiltros() {
					limparMensagem('filtroData');
					limparMensagem('filtroIntervalo');
					limparMensagem('dataFim');
				}

				function ativarAjuda(id) {
					if (typeof id == "string") {
						var hint = document.getElementById('hint-' + id);
						hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
					} else {
						for (var i = 0; i < id.length; i++) {
							var hint = document.getElementById('hint-' + id[i]);
							hint.style.visibility = hint.style.visibility == 'hidden' ? 'visible' : 'hidden';
						}
					}
				}
				function ajustarTamanhos(){
					var divGeral= document.getElementById('geral');
					var tabelaResultado = document.getElementById('tabelaResultados');
					var linha = tabelaResultado.getElementsByTagName('tr')[1];
					var colunas = linha.getElementsByTagName('td');
					var widthDiv = ((colunas.length * 120)+45) ;
					var heightDiv = ((tabelaResultado.getElementsByTagName('tr').length * 82)+31);

					
					
					if (widthDiv >925)
						widthDiv = 925;
					divGeral.style.width = widthDiv;
				}
				

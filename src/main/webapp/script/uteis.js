function phoneNumberFormat(elementId) {
    let element = document.getElementById(elementId);
    let number = element.value;
    if(number){
        let r = number.replace(/\D/g,"");
        r = r.replace(/^0/,"");
        if (r.length > 10) {
            r = r.replace(/^(\d\d)(\d{5})(\d{4}).*/,"(0$1) $2-$3");
        }
        else if (r.length > 5) {
            r = r.replace(/^(\d\d)(\d{4})(\d{0,4}).*/,"(0$1) $2-$3");
        }
        else if (r.length > 2) {
            r = r.replace(/^(\d\d)(\d{0,5})/,"(0$1) $2");
        }
        else {
            r = r.replace(/^(\d*)/, "(0$1");
        }
        element.value = r;
    }
}

			function validar() {
   				limparMensagem(['condicaoPagamento','condicaoPagamento-msgs','condicaoPagamento-msgs-2']);
   				

   				var validade = true;
   	   			
				
				var condicaoPagamentoSelecionada = document.getElementById('form:condicaoPagamentoSelec');
				
        		
				if (condicaoPagamentoSelecionada.value == 'false') {
					exibirMensagem(montarMsgObrigatoriedadeSelecao('Condi��o de pagamento'), ['condicaoPagamento','condicaoPagamento-msgs','condicaoPagamento-msgs-2']);
					
					validade = false;
				}

				return validade;
   			}

   			function limparMsgObrigAmbiente() {
   				var ambienteSelecionado = document.getElementById('form:ambienteSelec');
   				if (ambienteSelecionado.value == 'true') {
   					limparMensagem(['ambiente','ambiente-msgs']);
				}
   			}
   			function limparMensagens(){
   				limparMensagem(['perfilEvento','perfilEvento-msgs',
   				   				'ambiente','ambiente-msgs',
   				   				'condicaoPagamento','condicaoPagamento-msgs',
   				   				'dataValidade','condicaoPagamento-msgs-2']);
   				
   			}

   			function limparMsgObrigCondPag() {
   				var condicaoPagamentoSelecionada = document.getElementById('form:condicaoPagamentoSelec');
   				if (condicaoPagamentoSelecionada.value == 'true') {
   					limparMensagem(['condicaoPagamento','condicaoPagamento-msgs','condicaoPagamento-msgs-2']);
				}
   			}

   			function validarDisponibilidade() {
   				limparMensagem(['perfilEvento','perfilEvento-msgs', 'ambiente','ambiente-msgs',
   				   				'dataValidade']);

				var validade = true;
   	   			
				var ambienteSelecionado = document.getElementById('form:ambienteSelec');
				var dataValidade = document.getElementById('form:dataEventoInputDate');
				var perfilEventoSelecionado = document.getElementById('form:comboPerfilEvento');

				if (perfilEventoSelecionado == null || perfilEventoSelecionado.value == null || perfilEventoSelecionado.value == "") {
					exibirMensagem(montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.perfilEvento\']}"/>'), ['perfilEvento','perfilEvento-msgs']);
					validade = false;
				}
				
				if (ambienteSelecionado.value == 'false') {
					exibirMensagem(montarMsgObrigatoriedadeSelecao('<h:outputText value="#{CElabels[\'entidade.ambiente\']}"/>'), ['ambiente','ambiente-msgs']);
					validade = false;
				}

				if (dataValidade == null || dataValidade.value == null || dataValidade.value == "") {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.dataHorario\']}"/>', ['dataValidade']);
					validade = false;
				}

				return validade;
   			}

   			function validarHorarioFinal() {
   				limparMensagem('terminoDiaPosterior');
   	   			
   				var horarioInicial = document.getElementById('form:horarioInicial');
   				var horarioFinal = document.getElementById('form:horarioFinal');

   				if (horarioInicial != null && horarioInicial.value != null && horarioFinal != null && horarioFinal.value != null) {
   					if (horarioInicial.value.length == 5 && horarioFinal.value.length == 5) {
						if (horarioFinal.value == "00:00") {
							horarioFinal.value = "23:59";
						} else {
							var horIn = horarioInicial.value.replace(":", "");
							var horFin = horarioFinal.value.replace(":", "");
							horIn = parseInt(horIn, 10);
							horFin = parseInt(horFin, 10);
							if (horFin < horIn) {
								if (horFin > 600) {
									horarioFinal.value = "06:00";
								}
								exibirMensagem('<h:outputText value="#{Mensagens[\'dados.horario.evento.terminoDiaPosterior\']}"/>', 'terminoDiaPosterior');
							}
						}
   					} else {
   	   					if (horarioInicial.value.length != 5)
   	   						horarioInicial.value = "";
						if (horarioFinal.value.length != 5)
							horarioFinal.value = "";
   					}
				}
   			}
   			
   			function preencherHorarios(horaInicial, horaFinal){
   	   			var campoInicial = document.getElementById('form:horarioInicial');
   	   			var campoFinal = document.getElementById('form:horarioFinal');

   	   			campoInicial.value = horaInicial;
   	   			campoFinal.value = horaFinal;


   	   		}
   			
   			function VerificaData(digData) 
   			{	
   				var aux = document.getElementById('form:dataEventoInputDate');
   				var bissexto = 0;
   				var data = digData; 
   				var tam = data.length;
   				
   				if (aux.value == ""){
						return true;
					}
   				
   				if (tam == 10) 
   				{
   					var dia = data.substr(0,2);
   					var mes = data.substr(3,2);
   					var ano = data.substr(6,4);
   					
   					
   					if ((ano > 1)||(ano < 9999))
   					{
   						switch (mes) 
   						{
   							case '01':
   							case '03':
   							case '05':
   							case '07':
   							case '08':
   							case '10':
   							case '12':
   								if  (dia <= 31) 
   								{
   									return true;
   								}
   								break;
   							
   							case '04':		
   							case '06':
   							case '09':
   							case '11':
   								if  (dia <= 30) 
   								{
   									return true;
   								}
   								break;
   							case '02':
   								/* Validando ano Bissexto / fevereiro / dia */ 
   								if ((ano % 4 == 0) || (ano % 100 == 0) || (ano % 400 == 0)) 
   								{ 
   									bissexto = 1; 
   								} 
   								if ((bissexto == 1) && (dia <= 29)) 
   								{ 
   									return true;				 
   								} 
   								if ((bissexto != 1) && (dia <= 28)) 
   								{ 
   									return true; 
   								}			
   								break;						
   						}
   					}
   				}	
   				alert("A Data "+data+" � inv�lida");
   				return false;
   			}

   		    function mascarahora(field, sMask, evtKeyPress) {
   		        var i, nCount, sValue, fldLen, mskLen, bolMask, sCod, nTecla;

   		        var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);
   		        if (evtKeyPress.keyCode != 0
   		            && ((nTecla == 8) || (nTecla == 9) || (nTecla == 18) || (nTecla == 27) || (nTecla == 33) || (nTecla == 34) || (nTecla == 35) || (nTecla == 36)
   		            || (nTecla == 37) || (nTecla == 38) || (nTecla == 39) || (nTecla == 40) || (nTecla == 45) || (nTecla == 46))) {
   		            return true;
   		        }
   		  
   		        sValue = field.value;
   		  
   		        // Limpa todos os caracteres de formata��o que j� estiverem no campo.
   		        sValue = sValue.toString().replace(/[_\W]/g, "" );
   		        fldLen = sValue.length;
   		        mskLen = sMask.length;
   		  
   		 
   		        i = 0;
   		        nCount = 0;
   		        sCod = "";
   		        mskLen = fldLen;

   		        while (i <= mskLen) {
   		            bolMask = sMask.charAt(i).search(/[_\W]/) >= 0;

   		            if (bolMask) {
   		                sCod += sMask.charAt(i);
   		                mskLen++;
   		            } else {
   		                sCod += sValue.charAt(nCount);
   		                nCount++;
   		            }
   		      
   		            i++;
   		        }
   		        if (sMask.length == sCod.length) {
   		            return false;
   		        }
   		  
   		        field.value = sCod;
   		  
   		        if (sMask.charAt(i-1) == "9") { // apenas n�meros...
   		            return ((nTecla > 47) && (nTecla < 58));
   		        } // n�meros de 0 a 9
   		        else { // qualquer caracter...
   		            return true;
   		        }
   		    }

   		
   			
var functionTrocarMensagem;
var mensagem = 0;
var msgs = ['Consultando banco de dados...', 'Processando transfer�ncia', 'Verificando conflitos...', 'Listando hor�rios selecionados...', 'Organizando carteira de professores', 'Organizando turmas', 'Carregando ambientes', 'Procurando por turma', 'Preenchendo horarios'];
function iniciarStatusTurma(multiplicador) {
    mensagem = 0;
    addMsg('Consultando banco de dados...');
    functionTrocarMensagem = window.setInterval('trocarMensagem()', multiplicador * 1000);
}
function trocarMensagem() {
    if (mensagem < msgs.length) {
        addMsg(msgs[mensagem]);
        mensagem++;
    }
}
function addMsg(txt) {
    jQuery('.textoCarregando').empty();
    jQuery('.textoCarregando').text(txt);
}
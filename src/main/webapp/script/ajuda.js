var offsetfromcursorX=12 //Customize x offset of tooltip
var offsetfromcursorY=10 //Customize y offset of tooltip

var offsetdivfrompointerX=10 //Customize x offset of tooltip DIV relative to pointer image
var offsetdivfrompointerY=14 //Customize y offset of tooltip DIV relative to pointer image. Tip: Set it to (height_of_pointer_image-1).

document.write('<div id="dhtmltooltip"></div>') //write out tooltip DIV
if (!typeof contexto === "undefined"){
    document.write('<img id="dhtmlpointer" src="' + contexto + '/imagens/arrow2.gif">') //write out pointer image
}


var ie=document.all
var ns6=document.getElementById && !document.all
var enabletip=false
if (ie||ns6)
    var tipobj=document.all? document.all["dhtmltooltip"] : document.getElementById? document.getElementById("dhtmltooltip") : ""

var pointerobj=document.all? document.all["dhtmlpointer"] : document.getElementById? document.getElementById("dhtmlpointer") : ""

function shimit(obj){
    if (typeof shim=='undefined')
        return;
    var shimobj=document.getElementById('shim').style;
    if (!nondefaultpos){
        var shimobj2=document.getElementById('shim2').style;
        shimobj2.width=pointerobj.offsetWidth+'px'
        shimobj2.height=pointerobj.offsetHeight+'px'
        shimobj2.top=pointerobj.style.top
        shimobj2.left=pointerobj.style.left
        shimobj2.zIndex=99;
        shimobj2.display='block'
    }
    shimobj.height=obj.offsetHeight+'px'
    shimobj.width=obj.offsetWidth+'px'
    var obj=obj.style
    shimobj.left=obj.left
    shimobj.top=obj.top
    shimobj.zIndex=99
    shimobj.display='block'
}

function noshim(){
    if (typeof shim=='undefined')
        return;
    document.getElementById('shim').style.display='none';
    document.getElementById('shim2').style.display='none';
}

function ietruebody(){
    return (document.compatMode && document.compatMode!="BackCompat")? document.documentElement : document.body
}

function toolTip(thetext, thewidth, thecolor){
    if (ns6||ie){
        if (typeof thewidth!="undefined") tipobj.style.width=thewidth+"px"
        if (typeof thecolor!="undefined" && thecolor!=""){
            tipobj.style.color=thecolor
        }else{
            tipobj.style.color='#006684';
        }
        tipobj.innerHTML=thetext
        enabletip=true
        return false
    }
}

function positiontip(e){
    if (enabletip){
        nondefaultpos=false
        var curX=(ns6)?e.pageX : event.clientX+ietruebody().scrollLeft;
        var curY=(ns6)?e.pageY : event.clientY+ietruebody().scrollTop;
        //Find out how close the mouse is to the corner of the window
        var winwidth=ie&&!window.opera? ietruebody().clientWidth : window.innerWidth-20
        var winheight=ie&&!window.opera? ietruebody().clientHeight : window.innerHeight-20

        var rightedge=ie&&!window.opera? winwidth-event.clientX-offsetfromcursorX : winwidth-e.clientX-offsetfromcursorX
        var bottomedge=ie&&!window.opera? winheight-event.clientY-offsetfromcursorY : winheight-e.clientY-offsetfromcursorY

        var leftedge=(offsetfromcursorX<0)? offsetfromcursorX*(-1) : -1000

        //if the horizontal distance isn't enough to accomodate the width of the context menu
        if (rightedge<tipobj.offsetWidth){
            //move the horizontal position of the menu to the left by it's width
            tipobj.style.left=curX-tipobj.offsetWidth+"px"
            nondefaultpos=true
        }
        else if (curX<leftedge)
            tipobj.style.left="5px"
        else{
            //position the horizontal position of the menu where the mouse is positioned
            tipobj.style.left=curX+offsetfromcursorX-offsetdivfrompointerX+"px"
            pointerobj.style.left=curX+offsetfromcursorX+"px"
        }

        //same concept with the vertical position
        if (bottomedge<tipobj.offsetHeight){
            tipobj.style.top=curY-tipobj.offsetHeight-offsetfromcursorY+"px"
            nondefaultpos=true
        }
        else{
            tipobj.style.top=curY+offsetfromcursorY+offsetdivfrompointerY+"px"
            pointerobj.style.top=curY+offsetfromcursorY+"px"
        }
        tipobj.style.visibility="visible"

        shimit(tipobj);
        if (!nondefaultpos)
            pointerobj.style.visibility="visible"
        else
            pointerobj.style.visibility="hidden"
    }
}

function hideToolTip(){
    if (ns6||ie){
        enabletip=false
        tipobj.style.visibility="hidden"

        noshim();
        pointerobj.style.visibility="hidden"
        tipobj.style.left="-1000px"
        tipobj.style.backgroundColor=''
        tipobj.style.width=''
    }
}

document.onmousemove=positiontip
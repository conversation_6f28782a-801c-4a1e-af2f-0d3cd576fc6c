/* GET IP*/
//author: waller maciel
//date: 26/05/2011

var enviado=false;
var xmlHttp = null;
var pubIP = "";

function criaXmlHttpRequestObject(){
    var objXmlHttp = null;
    //trabalha com todos os browsers, menos IE
    try{
        //tenta criar o objeto XMLHttpRequest
        objXmlHttp = new XMLHttpRequest();
        return objXmlHttp;
    }
    catch(e){
        var XmlHttpVersoes =[
        "MSXML2.XMLHttp.6.0", "MSXML2.XMLHttp.5.0",
        "MSXML2.XMLHttp.4.0", "MSXML2.XMLHttp.3.0",
        "MSXML2.XMLHttp","Microsoft.XMLHttp"
        ];
        //procura a melhor vers�o para o IE
        for (var i=0; i < XmlHttpVersoes.length; i++){
            try{
                //tenta criar o objeto XMLHttpRequest
                objXmlHttp = new ActiveXObject(XmlHttpVersoes[i]);
            }
            catch(e){
            //faz nada
            }
        }
        //retorna o objeto criado ou um erro
        if (!objXmlHttp)
            alert("Erro ao criar Objeto XMLHttpRequest.");
        else
            return (objXmlHttp);
    }
	
}

function obterIP(){	
    try{
        if (!enviado){
            var url = "http://jsonip.appspot.com/?";
            xmlHttp = criaXmlHttpRequestObject();
            xmlHttp.open("GET", url, true);
            xmlHttp.onreadystatechange = chamarServidor;            
            enviado = true;
            xmlHttp.send(null);            
        }
    }
    catch(erro){
        alert(erro);
		
    }
				
} 

function chamarServidor(){
    if (xmlHttp.readyState == 4){
        
        if (xmlHttp.status == 200){
            enviado = false;
            var ip = xmlHttp.responseText;            
            enviar(ip);
        }
    }
	
}

//m�todo que aborta o recebimento de dados
function timesUp(){
    xmlHttp.abort();
}

function camposRequeridos(c){
    camposRequeridos(c, true);
}

function camposRequeridos(c, scroll){
    jQuery('.required').removeClass('missing');
    console.log(c);
    var campos = c.split(',');
    var l = campos.length;
    for (var i = 0; i < l; i++) {
        jQuery('.'+campos[i]).addClass('missing');
    }

    if (scroll == true) {
        var $container = jQuery("html,body");
        var $scrollTo = jQuery('.' + campos[0]);
        jQuery('.' + campos[0]).focus();
        var scroltop = ($scrollTo.offset().top - $container.offset().top + $container.scrollTop()) - 200;
        console.log(scroltop);
        $container.animate({scrollTop: scroltop, scrollLeft: 0},700);
    }
    jQuery('.required').change(function () {
        for (var i = 0; i < l; i++) {
            jQuery('.'+campos[i]).removeClass('missing');
        }
    })
}

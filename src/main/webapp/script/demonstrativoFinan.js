
	function atualizarTreeViewDF() {
            //window.location.reload();
            //alert('atualizarTreeView');
           // $.noConflict();
            jQuery(document).ready(function($) {
                $(".example").treeTable({
                    initialState: "expanded"
                });
            });

            jQuery(document).ready(function($){

                $(".expandirUm").click(function() {
                         $(".example").expandirUmNivel("dnd-example");
               });

                $(".retrairUm").click(function() {
                         $(".example").retrairUmNivel("dnd-example");
               });
                

                $(".expandir").click(function() {
                 $(".example").expandirTudo();
                });

                $(".retrair").click(function() {
                         $(".example").retrairTudo();
                });
               });
	}


    function adicionarFiltro(valorFiltro, marcado, tipoFiltro){

    	var recipienteFiltro = document.getElementById(tipoFiltro);
    	var filtro = valorFiltro+';';
    	// verificar se ainda n�o foi selecionada
    	if(marcado.checked == 1){
    		recipienteFiltro.value = recipienteFiltro.value + filtro;
        }else{
        	recipienteFiltro.value = recipienteFiltro.value.replace(filtro,'');
    	}
     }

    function selecionarFilhos(codigoNode, marcado) {
        var idPai = '#'+codigoNode;
        var child = '.child-of-'+codigoNode;

        var filhos = jQuery(document).find(child);
        for (var i = 0; i < filhos.length; i++) {
            var idFilho = '#'+filhos[i].id;
            if (jQuery(idFilho).find('input')[0].checked != marcado) {
                jQuery(idFilho).find('input')[0].click();
            }
        }
    }

  function atualizarTreeViewFiltros() {
    jQuery(document).ready(function($) {
        $(".filtroCentros").treeTable({
            initialState: "expanded"
        });
    });

    jQuery(document).ready(function($){
        	$(".expandirPlano").click(function() {
           	 $(".filtroPlanos").expandirTudo();
           	});

        	$(".expandirUmPlano").click(function() {
             	 $(".filtroPlanos").expandirUmNivel("dnd-filtroPlanos");
           });

        	$(".retrairUmPlano").click(function() {
            	 $(".filtroPlanos").retrairUmNivel("dnd-filtroPlanos");
          });

        	$(".retrairPlano").click(function() {
              	 $(".filtroPlanos").retrairTudo();
            });

        	$(".expandirCentro").click(function() {
              	 $(".filtroCentros").expandirTudo();
              	});

           	$(".expandirUmCentro").click(function() {
                	 $(".filtroCentros").expandirUmNivel("dnd-filtroCentros");
              });

           	$(".retrairUmCentro").click(function() {
               	 $(".filtroCentros").retrairUmNivel("dnd-filtroCentros");
             });

           	$(".retrairCentro").click(function() {
                 	 $(".filtroCentros").retrairTudo();
               });
	});

  }


    function reorganizarAreasFiltros(){
    	var CControle = document.getElementById('formDF:CControle').value;
		var OCControle = document.getElementById('formDF:OCControle').value;


		if(OCControle == 'true'){
			document.getElementById('OCExpandido').style.display = 'block';
	    	document.getElementById('OCRetraido').style.display = 'none';
	    	document.getElementById('TVconsulta').style.display = 'block';
	    }else{
	    	document.getElementById('OCExpandido').style.display = 'none';
	    	document.getElementById('OCRetraido').style.display = 'block';
	    	document.getElementById('TVconsulta').style.display = 'none';
		}
		if(CControle == 'true'){
			document.getElementById('CExpandido').style.display = 'block';
	    	document.getElementById('CRetraido').style.display = 'none';
	    	document.getElementById('consulta').style.display = 'block';
	    }else{
	    	document.getElementById('CExpandido').style.display = 'none';
	    	document.getElementById('CRetraido').style.display = 'block';
	    	document.getElementById('consulta').style.display = 'none';
		}


	    }


	function setarUltimaData(id){

		var campo = document.getElementById(id);
		var mes = campo.value.substr(3,2);
		var ano = campo.value.substr(6,4);

			if (campo.value != null && campo.value != "") {
			 if(mes == '01' || mes == '03' || mes == '05' || mes == '07' || mes == '08' || mes == '10' || mes == '12' ){
				 campo.value = '31/'+campo.value.substr(3);
			 }else
			 if(mes == '02'){
				 if(ano % 4 ==0){
					 campo.value = '29/'+campo.value.substr(3);
				 }else{
					 campo.value = '28/'+campo.value.substr(3);
				 }
			 }else{
				 campo.value = '30/'+campo.value.substr(3);
			 }

		}
	}



	function mudar_cor(celula, cor){
	   celula.style.backgroundColor= cor;
	}


    function preencherValorChamarBotao(idBotao, idMesHidden, valorMesHidden, idCodigoHidden, valorCodigoHidden, idTipoLista, valorTipo){
        var hiddenMes = document.getElementById(idMesHidden);
        var hiddenCodigo = document.getElementById(idCodigoHidden);
        var hiddenTipo = document.getElementById(idTipoLista);
        var botao = document.getElementById(idBotao);
        hiddenMes.value = valorMesHidden;
        hiddenCodigo.value = valorCodigoHidden;
        hiddenTipo.value = valorTipo;
        botao.click();
    }
    
    function esconderMostrar(caixa, retrair, retrairClicado, expandirNovo, controle){
            var table = document.getElementById(caixa);
            var aRetrair = document.getElementById(retrairClicado);
            var aExpandir = document.getElementById(expandirNovo);
            var controleOpened = document.getElementById(controle);
            if(retrair){
                    controleOpened.value = false;
                    table.style.display="none";
            }else{
                    controleOpened.value = true;
                    table.style.display="block";
            }
            aRetrair.style.display="none";
            aExpandir.style.display="block";
    }

    function alterarLabelAgruparMM(){
        var xmostrarCentroCusto = document.getElementById('formDRE:chkMostraCentroCustos');
        var xoutAgruparProdMM = document.getElementById('formDRE:outDreAgruparProdutoMM');
        if (xmostrarCentroCusto.checked){
            xoutAgruparProdMM.innerHTML='Agrupar valor da manuten��o de modalidades ao centro de custos das modalidades equivalentes';
        }else{
            xoutAgruparProdMM.innerHTML='Agrupar valor da manuten��o de modalidades ao plano de contas das modalidades equivalentes';
        }

    }

    function esconderMostrarFiltro(){
           var xDivFiltros = document.getElementById('tabelaFiltros');
           var xradio = document.getElementById('formDF:radioTipoVisualizacao:0');
           var xoutAgruparProdMM = document.getElementById('formDF:outDFAgruparProdutoMM');

           if (xradio.valueOf().checked){
              //alert('Plano de contas');
              // Mostrar os filtros
              xoutAgruparProdMM.innerHTML='Agrupar valor da manuten��o de modalidades ao plano de contas das modalidades equivalentes';

             xDivFiltros.style.display='';
             esconderMostrar('divFiltros', false, 'FMRetraido','FMExpandido','formDF:FMControle' );
           }else{
             //alert('Centro de custos');
             // Ocultar os filtros
             xDivFiltros.style.display='none';
             xoutAgruparProdMM.innerHTML='Agrupar valor da manuten��o de modalidades ao centro de custos das modalidades equivalentes';
             document.getElementById('formDF:centrosSelecionados').value = '';
             document.getElementById('formDF:planosSelecionados').value = '';
             var form = document.formDF;
             // Desmarcar todos os checkbox selecionados.
             for (i=0;i<form.length;i++){
                 if (form.elements[i].type == 'checkbox'){
                     form.elements[i].checked = false;
                 }
              }
           }
    }

function TeclasAtalho(e) {
    if (e.shiftKey) {
        if (e.keyCode == 43) {
            event.keyCode = 0;
            document.forms[0].incluir.click();
        } else if (e.keyCode == 45) {
            event.keyCode = 0;
            document.forms[0].excluir.click();
        } else if (e.keyCode == 46) {
            event.keyCode = 0;
            document.forms[0].gravar.click();
        } else if (e.keyCode == 42) {
            event.keyCode = 0;
            document.forms[0].consultar.click();
        }
    }
}


function mascara2(objForm, strField, sMask, evtKeyPress) {
    //alert(objForm[strField].value);
    var i, nCount, sValue, fldLen, mskLen,bolMask, sCod;
    var textoSel = getSelText();
    if (textoSel != '') {
        return true;
    }
    //aqui obtem o nr da tecla digitada
    var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);

    if (evtKeyPress.keyCode != 0 && ((nTecla == 8) || (nTecla == 9) || (nTecla == 46) 
    							|| (nTecla == 37) || (nTecla == 39) || (nTecla == 35) || (nTecla == 36))){
    	return true;
    }

    TeclasAtalho(evtKeyPress);

    sValue = objForm[strField].value;

    // Limpa todos os caracteres de formata��o que
    // j� estiverem no campo.
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( ":", "" );
    sValue = sValue.toString().replace( ":", "" );
    sValue = sValue.toString().replace( " ", "" );
    sValue = sValue.toString().replace( " ", "" );
    sValue = sValue.toString().replace( " ", "" );
    sValue = sValue.toString().replace( " ", "" );
    fldLen = sValue.length;
    mskLen = sMask.length;


    i = 0;
    nCount = 0;
    sCod = "";
    mskLen = fldLen;

    while (i <= mskLen) {
        bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ":") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/"));
        bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " "));

        if (bolMask) {
            sCod += sMask.charAt(i);
            mskLen++;
        } else {
            sCod += sValue.charAt(nCount);
            nCount++;
        }

        i++;
    }
    if (sMask.length == sCod.length) {
    	//event.keyCode = 0;
    	//Joao Alcides : esta modificacao impedira que a funcao permita que o usuario digite mais
    	//numeros do que o definido na mascara.
    	return false;
    }

    objForm[strField].value = sCod;
    if (nTecla != 8) { // backspace
        if (sMask.charAt(i-1) == "9") { // apenas n�meros...
            return ((nTecla > 47) && (nTecla < 58));
        } // n�meros de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    } else {
        return true;
    }
}

function preencherHiddenChamarBotao(idBotao, idHidden, valorHidden) {
    var hidden = document.getElementById(idHidden);
    var botao = document.getElementById(idBotao);
    hidden.value = valorHidden;
    botao.click();
}

function preencher3HiddenChamarBotao(idBotao, idHidden, valorHidden, idHidden2,
		valorHidden2, idHidden3, valorHidden3) {
	var hidden = document.getElementById(idHidden);
	var hidden2 = document.getElementById(idHidden2);
	var hidden3 = document.getElementById(idHidden3);
	var botao = document.getElementById(idBotao);
	hidden.value = valorHidden;
	hidden2.value = valorHidden2;
	hidden3.value = valorHidden3;
	botao.click();
}
function selecionarPlano(descricao) {
	document.getElementById('formRateioEdicao:nomePlanoSelecionado').value = descricao;
}
function selecionarCentro(descricao) {
	document.getElementById('formRateioEdicao:nomeCentroSelecionado').value = descricao;
}
function trocarAba(nomeAba) {
	document.getElementById('form:abaSelecionada').value = nomeAba;
}


/**
 * Joao Alcides, funcao de atualizar as tree views da tela de rateio integracao do financeiro
 */
function atualizarTreeViewRateio() {
    //window.location.reload();
    jQuery(document).ready(function($) {

        $(".categoria").treeTable({
            initialState: "collapsed"
        });
    });

    jQuery(document).ready(function($) {

        $(".modalidades").treeTable({
            initialState: "collapsed"
        });
    });
    
    jQuery(document).ready(function($) {

        $(".centraleventos").treeTable({
            initialState: "collapsed"
        });
    });

    jQuery(document).ready(function($){
        $(".expandirCategoria").click(function() {
            $(".categoria").expandirTudo();
       	});

        $(".expandirUmCategoria").click(function() {
            $(".categoria").expandirUmNivel("dnd-categoria");
        });

        $(".retrairUmCategoria").click(function() {
            $(".categoria").retrairUmNivel("dnd-categoria");
        });

        $(".retrairCategoria").click(function() {
            $(".categoria").retrairTudo();
        });

        $(".expandirModalidade").click(function() {
            $(".modalidades").expandirTudo();
        });

       	$(".retrairModalidade").click(function() {
            $(".modalidades").retrairTudo();
        });
       	$(".expandirUmModalidade").click(function() {
            $(".modalidades").expandirUmNivel("dnd-modalidades");
        });

        $(".retrairUmModalidade").click(function() {
            $(".modalidades").retrairUmNivel("dnd-modalidades");
        });
        
        $(".expandirCentralEventos").click(function() {
            $(".centraleventos").expandirTudo();
        });

       	$(".retrairCentralEventos").click(function() {
            $(".centraleventos").retrairTudo();
        });
       	$(".expandirUmCentralEventos").click(function() {
            $(".centraleventos").expandirUmNivel("dnd-centraleventos");
        });

        $(".retrairUmCentralEventos").click(function() {
            $(".centraleventos").retrairUmNivel("dnd-centraleventos");
        });
    });
}


/**
 * Joao Alcides, funcao de atualizar as tree views da tela de rateio integracao do financeiro
 */
function atualizarTreeViewPlanoContas(codigoPlanoPai) {
    //window.location.reload();
    jQuery(document).ready(function($) {

        $(".planoContasTable").treeTable({
            initialState: "collapsed"
        });
    });
    
    jQuery(document).ready(function($){
        $(".expandirPlano").click(function() {
            $(".planoContasTable").expandirTudo();
       	});

        $(".expandirUmPlano").click(function() {
            $(".planoContasTable").expandirUmNivel("dnd-planoContasTable");
        });

        $(".retrairUmPlano").click(function() {
            $(".planoContasTable").retrairUmNivel("dnd-planoContasTable");
        });

        $(".retrairPlano").click(function() {
            $(".planoContasTable").retrairTudo();
        });
    });
    
    jQuery(document).ready(function($) {

        $(".planoContasTable").expandirNivelExpecifico(codigoPlanoPai);
    });

    
} 
/**
 * Joao Alcides, funcao que seta o id do plano pai do item num input hiden selecionado na �rvore para expandi-lo futuramente
 */
function setarCodigoNivelPai(idPai, idHidden){
	var hidden = document.getElementById(idHidden);
	hidden.value = idPai;
}

/**
 * Joao Alcides, funcao de atualizar as tree views da tela de rateio integracao do financeiro
 */
function atualizarTreeViewCentroCustos(codigoCentroPai) {
    jQuery(document).ready(function($) {

        $(".centroCustosTable").treeTable({
            initialState: "collapsed"
        });
    });
    
    jQuery(document).ready(function($){
        $(".expandirCentro").click(function() {
            $(".centroCustosTable").expandirTudo();
       	});

        $(".expandirUmCentro").click(function() {
            $(".centroCustosTable").expandirUmNivel("dnd-centroCustosTable");
        });

        $(".retrairUmCentro").click(function() {
            $(".centroCustosTable").retrairUmNivel("dnd-centroCustosTable");
        });

        $(".retrairCentro").click(function() {
            $(".centroCustosTable").retrairTudo();
        });
    });
    
    jQuery(document).ready(function($) {
    	
        $(".centroCustosTable").expandirNivelExpecifico(codigoCentroPai);
    });

    
}

function botaoatualizarViewPlanoContas(){
		var botao = document.getElementById('form:atualizaTreeDepoisRateio');
		if(botao!=null){
			botao.click();
		}
}

function atualizarTreeViewFiltrosLancamento() {
    jQuery(document).ready(function($) {
        $(".filtroPlanosLan").treeTable({
            initialState: "collapsed"
        });
    });

    jQuery(document).ready(function($){
        	$(".expandirPlanoLan").click(function() {
           	 $(".filtroPlanosLan").expandirTudo();
           	});

        	$(".expandirUmPlanoLan").click(function() {
             	 $(".filtroPlanosLan").expandirUmNivel("dnd-filtroPlanosLan");
           });

        	$(".retrairUmPlanoLan").click(function() {
            	 $(".filtroPlanosLan").retrairUmNivel("dnd-filtroPlanosLan");
          });

        	$(".retrairPlanoLan").click(function() {
              	 $(".filtroPlanosLan").retrairTudo();
            });
	});
  }

function validar_Data_horas_minutosDifVazio(Ncampo){
    if ( document.getElementById(Ncampo).value != ''){
        if (document.getElementById(Ncampo).value.length == 10){
            validar_Data(Ncampo);
        }else{
            validar_Data_horas_minutos(Ncampo);
        }
    }
}

function validar_Data_horas_minutos(Ncampo){
    var er = /^(([0-2]\d|[3][0-1])\/([0]\d|[1][0-2])\/[1-2][0-9]\d{2})$/;
    //bSemMascara = document.getElementById(Ncampo).value;
    //b = aplicarMascaraValor(bSemMascara, '99/99/9999');
    //document.getElementById(Ncampo).value = b;
    b = document.getElementById(Ncampo).value;
    if (b != "" && b != "__/__/____") {
        //if (er.test(b)){
        if (b.length == 21){
            var dia = b.substring(0,2);
            var mes = b.substring(3,5);
            var ano = b.substring(6,10);

            var hora = b.substring(13,15);
            var min = b.substring(16,18);
            var seg = b.substring(19,21);

            if ((seg < 00) || (seg > 59)) {
                alert("Segundos especificados n�o s�o v�lidos.");
                document.getElementById(Ncampo).focus();
                //  fecharJanela();
                return false;
            }

            if ((min < 00) || (min > 59)) {
                alert("Minutos especificados n�o s�o v�lidos.");
                document.getElementById(Ncampo).focus();
                //  fecharJanela();
                return false;
            }

            if ((hora < 00) || (hora > 23)) {
                alert("A hora especificada n�o � valida.");
                document.getElementById(Ncampo).focus();
                //  fecharJanela();
                return false;
            }

            if ((ano < 1900) || (ano > 2099)) {
                alert("O ano especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                //  fecharJanela();
                return false;
            }
            if ((mes <= 0) || (mes > 12)) {
                alert("O m�s especificado n�o � valido.");
                document.getElementById(Ncampo).focus();
                //  fecharJanela();
                return false;
            }
            if (dia <= 0) {
                alert("Dia.");
                document.getElementById(Ncampo).focus();
                return false;
            }
            if ((mes=="01" || mes=="03" || mes=="05" || mes=="07" || mes=="08" || mes=="10" || mes=="12") && (dia > 31)) {
                document.getElementById(Ncampo).focus();
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 31 dias");
                return false;
            } else if ((mes=="04" || mes=="06" || mes=="09" || mes=="11") && (dia > 30)) {
                document.getElementById(Ncampo).focus();
                alert("Data incorreta! O m�s especificado cont�m no m�ximo 30 dias");
                return false;
            } else {
                if ((ano%4!=0) && (mes=="02") && (dia>28)) {
                    document.getElementById(Ncampo).focus();
                    alert("Data incorreta! O m�s especificado cont�m no m�ximo 28 dias.");
                    return false;
                } else{
                    if ((ano%4==0) && (mes=="02") && (dia>29)) {
                        document.getElementById(Ncampo).focus();
                        alert("Data incorreta! O m�s especificado cont�m no m�ximo 29 dias.");
                        return false;
                    } else{
                        return true;
                    }
                }
            }
        } else {
            alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa - HH:mm:ss).");
            document.getElementById(Ncampo).focus();
            // fecharJanela();
            return false;
        }
    }else {
        alert("Formato ou data inv�lida " + b + " (Exemplo de data: dd/mm/aaaa - HH:mm:ss).");
        document.getElementById(Ncampo).focus();
        // fecharJanela();
        return false;
    }
    return true;

}

    



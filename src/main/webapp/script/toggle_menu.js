/* Autor: <PERSON>iel
 * Data: 17/01/2012
 * Esses arrays precisam ter sempre o mesmo tamanho
 * O array 'arrayMenusRoot' tem o menu onde � clicado para aparecer as op��es.
 * O array 'arrayMenus' tem a lista de itens do menu
 */

var arrayMenusRoot = ['menuRootModulos'];
var arrayItensMenu = ['menuModulos'];

jQuery(function( $ ){    

    for (var i=0;i<arrayMenusRoot.length; i++){
        var idRoot = arrayMenusRoot[i];
        var idItem = arrayItensMenu[i];        

        
        var menuRoot = $("#"+idRoot);
        var menu = $( "#"+idItem);

        // Hook up menu root click event.
        menuRoot
        .attr( "href", "javascript:void( 0 )" )
        .attr( "menu", idItem )       
        .hover(
            function(event){
                // exibir o menu ao passar o mouse sobre o link do menu
                var target = event.target || event.srcElement;
                
                var mRoot = document.getElementById(target.id);
                var nomeMenu = mRoot.getAttribute("menu");
                var component = document.getElementById(nomeMenu);                
                
                component.style.left = event.clientX + 50;
                
                var menu = $( "#"+nomeMenu);
                
                menu.toggle();
                
                menuRoot.blur();

                // Cancelar o retorno do evento
                return( false );
            }
            )
        .click(                
            function(event){
                // Toggle the menu display.                
                var target = event.target || event.srcElement;                
                
                var mRoot = document.getElementById(target.id);
                var nomeMenu = mRoot.getAttribute("menu");
                var component = document.getElementById(nomeMenu);                
                
                component.style.left = event.clientX + 50;
                
                var menu = $( "#"+nomeMenu);
                
                menu.toggle();

                // Blur the link to remove focus.
                menuRoot.blur();

                // Cancel event (and its bubbling).
                return( false );
            
                    
            });



       

    }

    // Hook up a click handler on the document so that
    // we can hide the menu if it is not the target of
    // the mouse click.
    $( document ).click(
        function( event ){
            // Check to see if this came from the menu.
            for (var i=0;i<arrayItensMenu.length; i++){
                var id = arrayItensMenu[i];
                var menu = $( "#"+id);
                
                if (menu.is( ":visible" ) &&
                    !$( event.target ).closest( "#"+id ).size()){
                    
                    // The click came outside the menu, so
                    // close the menu.
                    menu.hide();

                }

            }



        });
    

});
window.getForm = function(){
    return document.forms[0];
};

window.getFormId = function(with2Dots){
    return getForm().id + (with2Dots ? ":" : "");
};

window.getById = function(id){
    var obj = null;
    obj = document.getElementById(id);
    if(!obj){
        obj = document.getElementById(getFormId(true) + id);
    }

    return obj;
};

window.focusAt = function(id){
    getById(id).focus();
    getById(id).select();
};

function tabOnEnter(evt, id) {
    var keyCode = document.layers ? evt.which : document.all ?
    evt.keyCode : evt.keyCode;
    if (keyCode != 13)
        return true;
    else {
        getById(id).focus();
        getById(id).select();
        return false;
    }
}

function abrirPopupPDFImpressao(caminhoRelativoPDF, nomeJanela, comprimento, altura) {
    var posTopo = 0;//((screen.height / 2) - (altura / 2));
    var posEsquerda = 0;//((screen.width / 2) -(comprimento / 2));
    var comprimento = screen.width;
    var altura = screen.height;
    var atributos = 'left=' + posEsquerda + ', screenX=' + posEsquerda + ', top=' + posTopo + ', screenY=' + posTopo + ', width=' + comprimento + ", height=" + altura + ' dependent=yes, menubar=no, toolbar=no, resizable=yes , scrollbars=yes' ;
    window.open(caminhoRelativoPDF,nomeJanela,atributos);
    return true;
}

var idUltimaImagem = 'preview_web1';
var nrUltimaImagem = 1;
var imagem1, imagem2, imagem3, imagem4, imagem5, imagem6;
var imgOriginal1, imgOriginal2, imgOriginal3, imgOriginal4, imgOriginal5, imgOriginal6;
var chaveAcademia = null;
var codigoContrato = null;
var contexto = null;
var jQuery_capFoto = jQuery.noConflict(true);

function initializeModalFotoUploadQrcode() {
    var snapBtn = document.getElementById('snapshot_btn');
    var webcamLink = document.getElementById('link_doc_webcam');
    var uploadLink = document.getElementById('link_doc_upload');
    var qrcodeLink = document.getElementById('link_doc_qrcode');
    document.getElementById('areaErro_doc').style.display = 'none';
    document.getElementById('areaSucesso_doc').style.display = 'none';

    snapBtn.addEventListener("click", takeSnapshot, false);
    webcamLink.addEventListener("click", showDocWebcamTab, false);
    uploadLink.addEventListener("click", showDocUploadTab, false);
    qrcodeLink.addEventListener("click", showDocQrcodeTab, false);

    initImagens();
    showDocUploadTab();
}

function initImagens() {
    imgOriginal1 = imagem1 = document.getElementById('doc_documento').value;
    imgOriginal2 = imagem2 = document.getElementById('doc_endereco').value;
    imgOriginal3 = imagem3 = document.getElementById('doc_atestado').value;
    imgOriginal4 = imagem4 = document.getElementById('doc_anexo1').value;
    imgOriginal5 = imagem5 = document.getElementById('doc_anexo2').value;
    imgOriginal6 = imagem6 = document.getElementById('doc_anexoCancelamento').value;
}

function ampliar(numero) {
    var src = null;
    switch (numero) {
        case 1:
            src = imagem1;
            break;
        case 2:
            src = imagem2;
            break
        case 3:
            src = imagem3;
            break
        case 4:
            src = imagem4;
            break
        case 5:
            src = imagem5;
            break
        case 6:
            src = imagem6;
            break
    }
    verImagem(src);
}

function verImagem(img) {
    img = img === null ? '../imagens/image_icon.jpg' : img;
    document.getElementById('idcontratoview').style.display = "block";
    document.getElementById('doccontratoview').src = img;
    document.body.scrollTop = document.documentElement.scrollTop = 0;
}

function fecharImagem() {
    document.getElementById('idcontratoview').style.display = "none";
}

function hideDocWebcamTab() {
    var webcamTab = document.getElementById('webcam_tab');
    var webcamLink = document.getElementById('link_doc_webcam');
    webcamTab.style.display = "none";
    jQuery(webcamLink).removeClass('active');
    Webcam.reset();
}

function hideDocUploadTab() {
    var uploadTab = document.getElementById('upload_tab');
    var uploadLink = document.getElementById('link_doc_upload');
    uploadTab.style.display = "none";
    jQuery(uploadLink).removeClass('active');
}

function hideDocQrcodeTab() {
    var qrcodeTab = document.getElementById('qrcode_tab');
    var qrcodeLink = document.getElementById('link_doc_qrcode');
    qrcodeTab.style.display = 'none';
    jQuery(qrcodeLink).removeClass('active');
}

function showDocWebcamTab() {
    var webcamTab = document.getElementById('webcam_tab');
    var webcamLink = document.getElementById('link_doc_webcam');
    var videoContainer = document.getElementById('video_doc_container');
    hideDocUploadTab();
    hideDocQrcodeTab();
    jQuery(webcamLink).addClass('active');
    selectImage('preview_web1',1);
    webcamTab.style.display = 'block';
    videoContainer.style.display = 'block';

    Webcam.reset();

    Webcam.on("error", function (err) {
        console.log(err);
        var msgErro = '';
        if (err.name === "NotFoundError") {
            msgErro = "N�o foi encontrada nenhuma c�mera conectada ao computador!";
        } else if (err.name === "PermissionDeniedError") {
            msgErro = "O acesso � c�mera foi negado! Por favor, permita o acesso no seu navegador."
                + "<br>Obs: Ap�s habilitar a permiss�o, clique aqui para recarregar o v�deo!";
        } else {
            msgErro = "Houve um problema durante o carregamento do v�deo.";
        }
        document.getElementById('msg_erro_webcam').textContent = msgErro;
        document.getElementById('doc_snapshot_btn').style.display = 'none';
        document.getElementById('doc_rotulos_imagens_webcam').style.display = 'none';
        document.getElementById('doc_imagens_webcam').style.display = 'none';
        document.getElementById('doc_botoes_imagens_webcam').style.display = 'none';
        document.getElementById('doc_botoes_webcam').style.display = 'none';
        videoContainer.style.display = 'none';
    });

    Webcam.on("load", function () {
        document.getElementById('doc_snapshot_btn').style.display = 'block';
        document.getElementById('doc_rotulos_imagens_webcam').style.display = 'inline-block';
        document.getElementById('doc_imagens_webcam').style.display = 'block';
        document.getElementById('doc_botoes_imagens_webcam').style.display = 'block';
        document.getElementById('doc_botoes_webcam').style.display = 'block';
    });

    Webcam.set({
        height: 300,
        image_format: 'jpeg',
        jpeg_quality: 90
    });

    Webcam.attach('video_doc_container');
}

function showDocUploadTab() {
    var uploadTab = document.getElementById('upload_tab');
    var uploadLink = document.getElementById('link_doc_upload');
    hideDocWebcamTab();
    hideDocQrcodeTab();
    jQuery(uploadLink).addClass('active');
    uploadTab.style.display = "block";
}

function showDocQrcodeTab() {
    var qrcodeTab = document.getElementById('qrcode_tab');
    var qrcodeLink = document.getElementById('link_doc_qrcode');
    hideDocUploadTab();
    hideDocWebcamTab();
    jQuery(qrcodeLink).addClass('active');
    qrcodeTab.style.display = "block";
}

function limparImagem(id, inputId, numero) {
    document.getElementById(id).src = '../imagens/image_icon.jpg';
    if (inputId != '') {
        document.getElementById(inputId).value = '';
    }
    if (numero === 1) {
        imagem1 = null;
    } else if (numero === 2) {
        imagem2 = null;
    } else if (numero === 3) {
        imagem3 = null;
    } else if (numero === 4) {
        imagem4 = null;
    } else if (numero === 5) {
        imagem5 = null;
    } else if (numero === 6) {
        imagem6 = null;
    }
}

function PreviewImage(idUpLoad, idPreview, numero, size) {
    var oFReader = new FileReader();
    resizeDocImage({
        file: document.getElementById(idUpLoad).files[0],
        maxSize: size
    }).then(function (resizedImage) {
        oFReader.readAsDataURL(resizedImage);
        oFReader.onload = function (oFREvent) {
            setaImagemNoCampo(idPreview, oFREvent.target.result, numero);
        };
    }).catch(function (err) {
        console.log(err);
    });
}

function setaImagemNoCampo(idPreview, imagem, numero) {
    document.getElementById(idPreview).src = imagem;
    if (numero === 1) {
        imagem1 = imagem;
    } else if (numero === 2) {
        imagem2 = imagem;
    } else if (numero === 3) {
        imagem3 = imagem;
    } else if (numero === 4) {
        imagem4 = imagem;
    } else if (numero === 5) {
        imagem5 = imagem;
    } else if (numero === 6) {
        imagem6 = imagem;
    }
}

function resizeDocImage(settings) {
    var file = settings.file;
    var maxSize = settings.maxSize;
    var reader = new FileReader();
    var image = new Image();
    var canvas = document.createElement('canvas');
    var dataURItoBlob = function (dataURI) {
        var bytes = dataURI.split(',')[0].indexOf('base64') >= 0 ?
            atob(dataURI.split(',')[1]) :
            unescape(dataURI.split(',')[1]);
        var mime = dataURI.split(',')[0].split(':')[1].split(';')[0];
        var max = bytes.length;
        var ia = new Uint8Array(max);
        for (var i = 0; i < max; i++)
            ia[i] = bytes.charCodeAt(i);
        return new Blob([ia], {type: mime});
    };
    var resize = function () {
        var width = image.width;
        var height = image.height;
        if (width > height) {
            if (width > maxSize) {
                height *= maxSize / width;
                width = maxSize;
            }
        } else {
            if (height > maxSize) {
                width *= maxSize / height;
                height = maxSize;
            }
        }
        canvas.width = width;
        canvas.height = height;
        canvas.getContext('2d').drawImage(image, 0, 0, width, height);
        var dataUrl = canvas.toDataURL('image/jpeg');
        return dataURItoBlob(dataUrl);
    };
    return new Promise(function (ok, no) {
        reader.onload = function (readerEvent) {
            image.onload = function () {
                return ok(resize());
            };
            image.src = readerEvent.target.result;
        };
        reader.readAsDataURL(file);
    });
}

function replaceImageData64(image) {
    try {
        return image.replace(/^data:image\/(png|jpg|jpeg);base64,/, "");
    } catch (e) {
        return '';
    }
}

function salvarImagens() {
    imagem1 === null ? imgOriginal1 : imagem1;
    imagem2 === null ? imgOriginal2 : imagem2;
    imagem3 === null ? imgOriginal3 : imagem3;
    imagem4 === null ? imgOriginal4 : imagem4;
    imagem5 === null ? imgOriginal5 : imagem5;
    imagem6 === null ? imgOriginal6 : imagem6;

    var docs64 = replaceImageData64(imagem1);
    var endereco64 = replaceImageData64(imagem2);
    var atestado64 = replaceImageData64(imagem3);
    var anexo164 = replaceImageData64(imagem4);
    var anexo264 = replaceImageData64(imagem5);
    var anexoCancelamento64 = replaceImageData64(imagem6);

    var data = {
        token: "TelaCliente:" + chaveAcademia,
        operacao: "salvarImagens",
        contrato: codigoContrato,
        documentos: docs64,
        documentosUpdate: imagem1 != imgOriginal1,
        endereco: endereco64,
        enderecoUpdate: imagem2 != imgOriginal2,
        atestado: atestado64,
        atestadoUpdate: imagem3 != imgOriginal3,
        anexo1: anexo164,
        anexo1Update: imagem4 != imgOriginal4,
        anexo2: anexo264,
        anexo2Update: imagem5 != imgOriginal5,
        anexoCancelamento: anexoCancelamento64,
        anexoCancelamentoUpdate:  imagem6 != imgOriginal6
    };

    jQuery_capFoto.post(contexto + "/prest/contratoassinatura", data)
        .done(function (data) {
            console.log(data);
            document.getElementById('doc_foto_upload_qrcode').style.display = "none";
            if (data.hasOwnProperty('sucesso') && data.sucesso.indexOf('sucesso') != -1) {
                document.getElementById('msg_sucesso_doc').textContent = "Imagens salvas com sucesso!";
                document.getElementById('areaSucesso_doc').style.display = "block";
            } else if (data.hasOwnProperty('erro') && data.erro.indexOf('err') != -1) {
                document.getElementById('msg_erro_doc').textContent = "Erro ao salvar as imagens!";
                document.getElementById('areaErro_doc').style.display = "block";
            }
        });
}

function desfazerAlteracoes() {
    imagem1 = document.getElementById("doc_documento").value;
    imagem2 = document.getElementById("doc_endereco").value;
    imagem3 = document.getElementById("doc_atestado").value;
    imagem4 = document.getElementById("doc_anexo1").value;
    imagem5 = document.getElementById("doc_anexo2").value;
    imagem6 = document.getElementById("doc_anexoCancelamento").value;
}

function desfazerAlteracoesUpload() {
    desfazerAlteracoes();
    document.getElementById("preview1").src = imagem1;
    document.getElementById("preview2").src = imagem2;
    document.getElementById("preview3").src = imagem3;
    document.getElementById("preview4").src = imagem4;
    document.getElementById("preview5").src = imagem5;
    document.getElementById("preview6").src = imagem6;
}

function desfazerAlteracoesWebcam() {
    desfazerAlteracoes();
    document.getElementById("preview_web1").src = imagem1;
    document.getElementById("preview_web2").src = imagem2;
    document.getElementById("preview_web3").src = imagem3;
    document.getElementById("preview_web4").src = imagem4;
    document.getElementById("preview_web5").src = imagem5;
    document.getElementById("preview_web6").src = imagem6;
}

function selectImage(elementPreview, numeroImagem) {
    var ultimoPreview = document.getElementById(idUltimaImagem);
    var preview = document.getElementById(elementPreview);

    jQuery(ultimoPreview).removeClass('active');
    jQuery(preview).addClass('active');
    idUltimaImagem = elementPreview;
    nrUltimaImagem = numeroImagem;
}

function capturarFoto() {
    Webcam.snap(function (data_uri) {
        setaImagemNoCampo(idUltimaImagem, data_uri, nrUltimaImagem);
    });
}

function setAttributesModalDocFoto(key, contrato, context) {
    chaveAcademia = key;
    codigoContrato = contrato;
    contexto = context;
}

function sair() {
    Richfaces.hideModalPanel('visualizacaoDocs');
}

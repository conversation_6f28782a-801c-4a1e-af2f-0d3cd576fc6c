/***
 * Valida n�mero de contas banc�rias
 *
 * Exemplo de utiliza��o:
 *
 * Definda o evento onchange nos elementos inputs da conta
 * <h:inputText  id="agenciaCobranca" onchange="validarConta()"/>
 * <h:inputText  id="agenciaCobrancaDV" onchange="validarConta()"/>
 * <h:inputText  id="contaCorrenteCobranca" onchange="validarConta()"/>
 * <h:inputText  id="contaCorrenteCobrancaDV" onchange="validarConta()"/>
 *
 * Fa?a o bind para valida??o no servidor e exibi??o de mensagens
 * <h:hidden  id="#{SeuControlador.contaValida}" >
 * <div id="erroValidacaoDcoConta" />
 *
 * function validarConta() {
 *     var selectorInputCodigoBanco = "formCadastroAutorizacaoCobranca:bancoCodigo";
 *     var selectorInputAgencia = "formCadastroAutorizacaoCobranca:agenciaCobranca";
 *     var selectorInputAgenciaDV = "formCadastroAutorizacaoCobranca:agenciaCobrancaDV";
 *     var selectorInputConta = "formCadastroAutorizacaoCobranca:contaCorrenteCobranca";
 *     var selectorInputContaDV = "formCadastroAutorizacaoCobranca:contaCorrenteCobrancaDV";
 *     var selectorInputContaValida = "formCadastroAutorizacaoCobranca:contaValida";
 *
 *     validarContaBancaria(selectorInputCodigoBanco,
 *         selectorInputAgencia,
 *         selectorInputAgenciaDV,
 *         selectorInputConta,
 *         selectorInputContaDV,
 *         selectorInputContaValida,
 *         selectorOutputMensagemErro
 *     );
 * }
 *
 * @param idInputCodigoBanco
 * @param idInputAgencia
 * @param idInputAgenciaDV
 * @param idInputConta
 * @param idInputContaDV
 * @param idInputContaValida
 * @param idOutputMensagemErro
 */
function validarContaBancaria(
    idInputCodigoBanco,
    idInputAgencia,
    idInputAgenciaDV,
    idInputConta,
    idInputContaDV,
    idInputContaValida,
    idOutputMensagemErro,
    idInputCodOperacao
){
    var codigoBanco = jQuery("[id='"+idInputCodigoBanco+"']").val();
    codigoBanco = "000"+codigoBanco;
    codigoBanco = codigoBanco.substring(codigoBanco.length - 3);

    var valorInputConta;
    if(idInputCodOperacao && idInputCodOperacao.length > 0){
        var operacao = zerosEsquerda(jQuery("[id='"+idInputCodOperacao+"']").val(), 3);
        var conta = zerosEsquerda(jQuery("[id='"+idInputConta+"']").val(), 8);
        valorInputConta = operacao + conta;
    }else{
        valorInputConta = jQuery("[id='"+idInputConta+"']").val();
    }

    var inputs = {
        banco: codigoBanco,
        agencia: jQuery("[id='"+idInputAgencia+"']").val(),
        agenciaDV: jQuery("[id='"+idInputAgenciaDV+"']").val(),
        conta:  valorInputConta,
        contaDV: jQuery("[id='"+idInputContaDV+"']").val()
    };

    inputs = formatarNumeroConta(inputs);

    if(inputs.banco.length > 0 &&
        inputs.agencia.length > 1 &&
        inputs.conta.length > 1 ){

        Moip.BankAccount.validate({
            bankNumber         : inputs.banco,
            agencyNumber       : inputs.agencia,
            agencyCheckNumber  : inputs.agenciaDV,
            accountNumber      : inputs.conta,
            accountCheckNumber : inputs.contaDV,
            valid: function() {
                jQuery("[id='"+idInputContaValida+"']").val(true);
                jQuery("[id='"+idOutputMensagemErro+"']").text("");
                console.log("Conta banc?ria v?lida: ");
            },
            invalid: function(data) {
                jQuery("[id='"+idInputContaValida+"']").val(false);
                var erro = "";
                for (i=0; i < data.errors.length; i++){
                    // console.log(data.errors[i].description);
                     erro += "<span class='col-md-12 texto-size-14 texto-cor-vermelho texto-font'>"+
                            data.errors[i].description
                        "</span><br>";
                }
                jQuery("[id='"+idOutputMensagemErro+"']").html(erro);
            }
        });
    }
}
function formatarNumeroConta(inputs){
    switch (inputs.banco) {
        case "104":
            // Caixa
            inputs.agencia = zerosEsquerda(inputs.agencia, 4);
            inputs.conta = zerosEsquerda(inputs.conta, 11);
            break;
        case "001":
            // BB
            inputs.agencia = zerosEsquerda(inputs.agencia, 4);
            inputs.conta = zerosEsquerda(inputs.conta, 6);
            break;
        case "237":
            // BRADESCO
            inputs.agencia = zerosEsquerda(inputs.agencia, 4);
            inputs.conta = zerosEsquerda(inputs.conta, 7);
            break;
        case "341":
            // ITAU
            inputs.agencia = zerosEsquerda(inputs.agencia, 4);
            inputs.conta = zerosEsquerda(inputs.conta, 5);
            break;
        case "033":
            // SANTANDER
            inputs.agencia = zerosEsquerda(inputs.agencia, 4);
            inputs.conta = zerosEsquerda(inputs.conta, 8);
            break;
    }

    return inputs;
}
function zerosEsquerda(str, max) {
    str = str.toString();
    return str.length < max ? zerosEsquerda("0" + str, max) : str;
}
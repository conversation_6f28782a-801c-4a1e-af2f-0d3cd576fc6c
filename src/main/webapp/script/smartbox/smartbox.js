/* 
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
function expandir(vetor, idExpansor) {
    var expansor = document.getElementById(idExpansor);
    for (var i = 0; i < vetor.length; i++) {
        var expansivel = document.getElementById(vetor[i]);
        if (expansivel.style.display == 'none') {
            expansivel.style.display = 'block';
            expansor.className = 'menulateral_retraido';
            expansor.title = "Esconder op��es";
        } else {
            expansivel.style.display = 'none';
            expansor.className = 'menulateral_restaurado';
            expansor.title = "Exibir op��es";
        }

    }
}

function mostraEsconde(idComp, idBtn) {
    var comp = document.getElementById(idComp);
    var btn = document.getElementById(idBtn);
    if (comp.style.display == 'none') {
        comp.style.display = 'block';
        btn.src = 'images/chevron-small.png';
    } else {
        comp.style.display = 'none';
        btn.src = 'images/chevron-small-expand.png';

    }
}

function mostraEscondePorSimilaridade(idCompClicado, idPanelEsconder, idCompReferencia, idBotaoTrocar) {
    var idcomp = idCompReferencia.toString().replace(idCompClicado,
            idPanelEsconder);

    var idCompBotaoTrocar = idCompReferencia.toString().replace(idCompClicado,
            idBotaoTrocar);

    var comp = document.getElementById(idcomp);
    var btn = document.getElementById(idCompBotaoTrocar);

    if (comp.style.display == 'none') {
        comp.style.display = 'block';
        btn.src = 'images/chevron-small.png';
    } else {
        comp.style.display = 'none';
        btn.src = 'images/chevron-small-expand.png';

    }

}

function tabelaSmartBox(idTabela) {
    //retirar styles do richfaces da table, se resumem as bordas
    var tabela = document.getElementById(idTabela);
    if (tabela != null) {
        tabela.className = '';

        //fazer o mesmo com as linhas
        var linhas = tabela.getElementsByTagName("tr");
        linhas[0].style.backgroundImage = 'url(images/back_header_smartbox.png)';
        linhas[0].style.backgroundRepeat = 'repeat-x';


        //com o header da tabela, manter o style, apenas modificar as bordas, background e alinhamento
        var headers = tabela.getElementsByTagName("th");
        headers[0].style.borderLeft = '1px solid silver';
        for (var i = 0; i < headers.length; i++) {
            headers[i].style.borderTop = '1px solid silver';
            headers[i].style.borderBottom = '1px solid silver';
            headers[i].style.borderRight = 'none';
            headers[i].style.textAlign = 'center';
            headers[i].style.padding = '4px';
        }
        headers[headers.length - 1].style.borderRight = '1px solid silver';
    }

}
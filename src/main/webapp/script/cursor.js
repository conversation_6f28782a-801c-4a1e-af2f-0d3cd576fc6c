/*
 * @author: <PERSON> (<EMAIL>)
 * @date: 06/04/2012
 * Manipular o texto selecionado de um componente INPUT
 */

function textSelect(inp, s, e) {
    e = e || s;
    if (inp.createTextRange) {
        var r = inp.createTextRange();
        r.collapse(true);
        r.moveEnd('character', e);
        r.moveStart('character', s);
        r.select();
    }else if(inp.setSelectionRange) {
        inp.focus();
        inp.setSelectionRange(s, e);
    }
}

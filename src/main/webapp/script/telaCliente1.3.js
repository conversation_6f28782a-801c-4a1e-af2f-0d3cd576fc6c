
var timeOutInfiniteScroll;
jQuery(function() {
    jQuery(window).scroll(function () {
        window.clearTimeout(timeOutInfiniteScroll);
        timeOutInfiniteScroll = window.setTimeout(function() {
            try {
                if(isElementInViewport(document.getElementById('divfundo'))){
                    carregaMais();
                }
            }catch (e){
            }
        }, 400);

    });

    jQuery('.form-scroll').scroll(function () {
        window.clearTimeout(timeOutInfiniteScroll);
        timeOutInfiniteScroll = window.setTimeout(function() {
            try {
                if(isElementInViewport(document.getElementById('divfundo'))){
                    carregaMais();
                }
            }catch (e){
            }
        }, 400);

    });

});
function isElementInViewport (el) {

    //special bonus for those using jQuery
    if (typeof jQuery === "function" && el instanceof jQuery) {
        el = el[0];
    }

    var rect = el.getBoundingClientRect();

    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
    );
}

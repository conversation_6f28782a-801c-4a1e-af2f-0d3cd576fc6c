//O recurso de notifica��o, ir� continuar na forma de c�digo, por�m n�o estar� acess�vel por nenhuma parte do sistema.
//Ticket: IN-520

if (!NotificacoesCRM) {// Mant�m apenas uma inst�ncia do m�dulo
    var NotificacoesCRM = (function () {
        var handlerIntervalo = null;
        var exibirNotificacao = function (notificacaoJSON) {
            var notificacao = new Notification(notificacaoJSON.titulo, {
                icon: notificacaoJSON.urlFotoCliente,
                body: notificacaoJSON.conteudo,
                tag: notificacaoJSON.codigoAgendamento,
                data: JSON.stringify(notificacaoJSON)
            });
            notificacao.onclick = function () {
                //console.log('Usu�rio reconheceu a notifica��o');
                this.reconhecida = true;
                notificacao.close();
                irParaAgendamento(this.data);

            };
            notificacao.onclose = function () {
                //console.log('Usu�rio fechou a notificacao');
                if (!this.reconhecida) {
                    //console.log('Mensagem n�o foi reconhecida')
                }
            };
        };
        var removerIntervalo = function () {
            if (handlerIntervalo) {
                clearInterval(handlerIntervalo);
                handlerIntervalo = null;
            }
        };
        var registrarIntervalo = function () {
            var iniciarModulo = function () {
                carregarNotificacoes();
                handlerIntervalo = setInterval(function () {
                    carregarNotificacoes();
                }, 45000);
                //console.log('O m�dulo de notifica��es foi iniciado!!!');
            };
            if (Notification.permission === "granted") {
                iniciarModulo();
                return;
            }

            //console.log('Usu�rio ainda n�o concedeu a permiss�o para exibi��o das notifica��es desktop.');
            Notification.requestPermission(function (permissao) {
                if (permissao === 'granted') {
                    //console.log('Foi autorizada a exibi��o das notifica��es de autoriza��o. Iniciando m�dulo...');
                    iniciarModulo();
                    return;
                }
                //console.log('Permiss�o solicitada n�o foi concedida. A requisi��o de registro do m�dulo ser� ignorada.');
            });


        };
        // Retorna um JSON expondo as fun��es do m�dulo
        return {
            iniciarModulo: function () {
                removerIntervalo();
                registrarIntervalo();
            },

            exibirNotificacoes: function (json) {
                var criarNotificacoes = function (notificacoes) {
                    var notificacoesJSON = JSON.parse(notificacoes);
                    for (var i = 0; i < notificacoesJSON.length; i++) {
                        exibirNotificacao(notificacoesJSON[i]);
                    }
                }
                if (Notification.permission === "denied") {
                    //console.log('Usu�rio desabilitou de forma expl�cita a exibi��o de notifica��es desktop.');
                    //console.log('O m�dulo de notifica��es ser� desativado at� o usu�rio recarregar a p�gina.');
                    removerIntervalo();
                    return;
                }
                if (Notification.permission === "granted") {
                    criarNotificacoes(json);
                    return;
                }
                Notification.requestPermission(function (permissao) {
                    //console.log('Permiss�o definida pelo usu�rio:' + permissao);
                    if (permissao === "granted") {
                        criarNotificacoes(json);
                        return;
                    }
                    // Permiss�o n�o concedida, desativando m�dulo
                    removerIntervalo();
                });
            }
        }
    })();
}


// Documenta��o da API - https://www.w3.org/TR/notifications/
document.addEventListener('DOMContentLoaded', function () {
    //Verifica se h� suporte para o tipo de notifica��o a ser exibida
    if (!Notification) {
        console.log('Notifica��es de �rea de trabalho n�o s�o suportadas por este navegador.');
        return;
    }
    if (!NotificacoesCRM) {
        console.log('M�dulo de ger�nciamento de notifica��es CRM n�o foi encontrado');
        return;
    }

    if (Notification.permission === "denied") {
        console.log('As notifica��es foram desativadas de forma expl�cita pelo usu�rio');
        return;
    }
    NotificacoesCRM.iniciarModulo();
});

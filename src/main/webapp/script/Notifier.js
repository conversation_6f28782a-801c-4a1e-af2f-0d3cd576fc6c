var Notifier;
function  carregarNotifier() {
    Notifier = (function() {
        var MENS_ERRO = 0, MENS_INFO = 1, MENS_SUCESS = 3, MENS_WARN = 4;
        function tipoNotificacao(e) {
                if(e == MENS_ERRO){
                    return 'mens-erro';
                }else if(e == MENS_INFO){
                    return 'mens-info';
                }else if(e == MENS_SUCESS){
                    return 'mens-sucess';
                }else if(e == MENS_WARN){
                    return 'mens-warn';
                }
            return '';
        }
        function removerElemento(el) {
            el.className += ' hideNotificationMessage';
            setTimeout(function(){
                if(el != null) {
                    el.parentNode.removeChild(el);
                }
            },1000);
        }
        var fade_out = function(element) {
            if (element.style.opacity && element.style.opacity > 0.05) {
                element.style.opacity = element.style.opacity - 0.05;
            } else if (element.style.opacity && element.style.opacity <= 0.1) {
                if (element.parentNode) {
                    removerElemento(element);
                }
            } else {
                element.style.opacity = 0.9;
            }
            setTimeout(function() {
                fade_out.apply(this, [element]);
            }, 1000 / 30);
        };
        container_box = document.createElement('div');
        container_box.className = 'growl-container';
        var config = { /* How long the notification stays visible */
            default_timeout: 5000,
            /* container for the notifications */
            container: container_box
        };
        document.body.appendChild(container_box);
        function removerMesmoTipo(tipo) {
           for(var e = 0 ; e < container_box.childNodes.length ; e++){
                var node =  container_box.childNodes[e];
                if(node.className == tipoNotificacao(tipo)){
                    container_box.removeChild(node);
                }
            }
        }
        return {
            MENS_ERRO : 0,
            MENS_INFO : 1,
            MENS_SUCESS : 3,
            MENS_WARN : 4,
            notify: function(message, title,type,autoRemove) {

                var notification = document.createElement('div');
                notification.className = tipoNotificacao(type);
                removerMesmoTipo(type);
                var notification_fundo = document.createElement('div');
                notification_fundo.className = 'growl-notification-fundo';

                notification.appendChild(notification_fundo);
                
                notification.onclick = function() {
                    removerElemento(notification);
                };

                var container_icon = document.createElement('div');
                container_icon.className = 'growl-box-icon';
                
                var icon = document.createElement('span');
                container_icon.appendChild(icon);
                
                notification.appendChild(container_icon);

                var text = document.createElement('div');
                text.className = 'growl-box-text';
                notification.appendChild(text);

                if (title) {
                    var title_text = document.createElement('div');
                    title_text.id = "titleInfo";
                    title_text.className = "growl-box-title";
                    title_text.appendChild(document.createTextNode(title));
                    text.appendChild(title_text);
                }

                if (message) {
                    var message_text = document.createElement('div');
                    message_text.id = "messageInfo";
                    message_text.className =  "growl-box-message";
                    message_text.appendChild(document.createTextNode(message));
                    text.appendChild(message_text);
                }

                config.container.insertBefore(notification, config.container.firstChild);
                if(autoRemove) {
                    setTimeout(function () {
                        fade_out(notification);
                    }, config.default_timeout);
                }

            },
            info: function(message, title) {
                this.notify(message, title,MENS_INFO,true);
            },
            warning: function(message, title) {
                this.notify(message, title,MENS_WARN,true);
            },
            success: function(message, title) {
                this.notify(message, title,MENS_SUCESS,true);
            },
            error: function(message, title) {
                this.notify(message, title ,MENS_ERRO,false);
            },
            errorRemove: function(message, title) {
                this.notify(message, title ,MENS_ERRO,true);
            },
            custom: function(message, title,type,autoRemove) {
                this.notify(message, title ,type,autoRemove);
            },
            cleanAll : function() {
                for(var e = 0 ; e < container_box.childNodes.length ; e++){
                    var node =  container_box.childNodes[e];
                        container_box.removeChild(node);
                }
            },
            cleanAllOnType : function(tipo) {
                for(var e = 0 ; e < container_box.childNodes.length ; e++){
                    var node =  container_box.childNodes[e];
                    if(node.className == tipoNotificacao(tipo)) {
                        container_box.removeChild(node);
                    }
                }
            }
        };
    }());
}
document.addEventListener('DOMContentLoaded', carregarNotifier, false);

var janela;
var returnURL;

function abrirUrlVideosService(usuario, senha) {
    abrirUrlService('http://intranet.pactosolucoes.com.br:81/service/portal/IT.sp?acao=categoriaSite&idCategoria=6&setarParametros=true&pagingPage=1', usuario, senha);
}

function abrirUrlMinhaPaginaService(usuario, senha) {
    abrirUrlService('http://intranet.pactosolucoes.com.br:81/service/portal/PRminhapagina.sp', usuario, senha);
}

function abrirUrlService(url, usuario, senha) {
    if (url != '') {
        if (usuario != '' && senha != '') {
            var urlLogin = 'http://intranet.pactosolucoes.com.br:81/service/portal/login.sp?acao=logar&login=' + usuario + '&senha=' + senha;
            janela = window.open(urlLogin, '_blank', 'width=5, height=5,screenX=0,screenY=0;');
            returnURL = url;
            setTimeout(openReturnURL, 2000);
        } else {
            abrirUrl(url);
        }
    }
}

function openReturnURL() {
    janela.close();
    if (returnURL != '') {
        abrirUrl(returnURL);
    }
}

function abrirUrl(url) {
    window.open(url, '_blank');
}
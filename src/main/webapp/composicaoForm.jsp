<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/vanilla-masker.min.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Composicao_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Composicao_tituloForm}"/>
    <rich:modalPanel id="panelModalidade" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_semUCP.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta de Modalidade"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panelModalidade" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalidade" ajaxSubmit="true">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText  value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" id="consultaModalidade"
                                     value="#{ComposicaoControle.campoConsultaModalidade}">
                        <f:selectItems value="#{ComposicaoControle.tipoConsultaComboModalidade}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultaModalidade" size="10" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                 styleClass="form" value="#{ComposicaoControle.valorConsultaModalidade}"/>
                    <a4j:commandButton  id="btnConsultar" reRender="formModalidade:mensagemConsultaModalidade,
                                        formModalidade:resultadoConsultaModalidade, formModalidade:scResultadoModalidade"
                                        action="#{ComposicaoControle.consultarModalidade}" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                        image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaModalidade" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" 
                                columnClasses="colunaAlinhamento" value="#{ComposicaoControle.listaConsultaModalidade}" rows="5" var="modalidade">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Modalidade_nome}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ComposicaoControle.selecionarModalidade}" focus="valorMensalComposicao" reRender="form"
                                             oncomplete="Richfaces.hideModalPanel('panelModalidade')" value="#{modalidade.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Modalidade_valorMensal}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ComposicaoControle.selecionarModalidade}" focus="valorMensalComposicao" reRender="form"
                                             oncomplete="Richfaces.hideModalPanel('panelModalidade')" >
                                <h:outputText value="#{modalidade.valorMensal}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Modalidade_vezesSemana}"/>
                        </f:facet>
                        <a4j:commandLink action="#{ComposicaoControle.selecionarModalidade}" focus="valorMensalComposicao" reRender="form"
                                         oncomplete="Richfaces.hideModalPanel('panelModalidade')" value="#{modalidade.nrVezes}"/>

                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="selecianarModalidade" action="#{ComposicaoControle.selecionarModalidade}" focus="valorMensalComposicao" 
                                           reRender="form" oncomplete="Richfaces.hideModalPanel('panelModalidade')" value="#{msg_bt.btn_selecionar}"
                                           image="./imagens/botaoEditar.png" alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModalidade:resultadoConsultaModalidade" maxPages="10"
                                   id="scResultadoModalidade" />
                <h:panelGrid id="mensagemConsultaModalidade" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ComposicaoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ComposicaoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{ComposicaoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText   value="#{msg_aplic.prt_Composicao_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                      styleClass="camposSomenteLeitura" value="#{ComposicaoControle.composicaoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText rendered="#{ComposicaoControle.composicaoVO.usuarioVO.administrador}" styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_empresa}" />
                    <h:panelGroup rendered="#{ComposicaoControle.composicaoVO.usuarioVO.administrador}">
                        <h:selectOneMenu  id="empresa"  disabled="#{ComposicaoControle.composicaoVO.composicaoNova}" onblur="blurinput(this);"
                                          onfocus="focusinput(this);" styleClass="form" value="#{ComposicaoControle.composicaoVO.empresa.codigo}" >
                            <a4j:support event="onchange" focus="descricao" action="#{ComposicaoControle.validarEmpresa}" ajaxSingle="true"
                                         reRender="form:ComposicaoModalidade_modalidade,form:composicaoModalidadeVO"/>
                            <f:selectItems  value="#{ComposicaoControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_empresa" action="#{ComposicaoControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png"
                                           immediate="true" ajaxSingle="true" reRender="form:empresa"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_Composicao_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao"  size="45" maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                      value="#{ComposicaoControle.composicaoVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_Composicao_composicaoAdicional}" />
                    <h:selectBooleanCheckbox title="#{msg.msg_composicao_Adicional}" id="composicaoAdicional" styleClass="campos" 
                                             value="#{ComposicaoControle.composicaoVO.composicaoAdicional}"/>
                    <h:outputText value="#{msg_aplic.prt_Composicao_composicaoDefault}" />
                    <h:selectBooleanCheckbox title="#{msg.msg_composicao_Default}" id="composicaoDefault" styleClass="campos"
                                                 value="#{ComposicaoControle.composicaoVO.composicaoDefault}"/>
                    <h:outputText value="#{msg_aplic.prt_Composicao_precoComposicao}" />
                    <h:inputText id="precoComposicao" size="20" maxlength="20" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="camposSomenteLeitura" value="#{ComposicaoControle.composicaoVO.precoComposicao}"
                                 readonly="#{ComposicaoControle.composicaoVO.modalidadesEspecificas}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>
                    <h:outputText value="#{msg_aplic.prt_Composicao_escolherModalidades}" />
                    <h:selectBooleanCheckbox title="#{msg.msg_composicao_limite}" id="chkEscolherModalidades" styleClass="campos" value="#{ComposicaoControle.composicaoVO.modalidadesEspecificas}">
                        <a4j:support event="onclick" action="#{ComposicaoControle.atualizaComposicao}" reRender="precoComposicao, modalidades"/>
                    </h:selectBooleanCheckbox>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada" id="modalidades">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ComposicaoModalidade_tituloForm}"/>
                    </f:facet>

                    <h:panelGroup>
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada" >
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                                <h:outputText value="#{msg_aplic.prt_ComposicaoModalidade_qtdeModalidade}" 
                                              rendered="#{!ComposicaoControle.composicaoVO.modalidadesEspecificas}"/>
                                <h:inputText id="qtdeModalidades" onkeypress="return Tecla(event);" size="5" maxlength="5"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{ComposicaoControle.composicaoVO.qtdeModalidades}"
                                             rendered="#{!ComposicaoControle.composicaoVO.modalidadesEspecificas}"/>
                                <h:outputText value="#{msg_aplic.prt_ComposicaoModalidade_modalidade}" />
                                <h:panelGroup>
                                    <h:selectOneMenu id="ComposicaoModalidade_modalidade" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                     value="#{ComposicaoControle.composicaoModalidadeVO.modalidade.codigo}" >
                                        <f:selectItems value="#{ComposicaoControle.listaSelectItemModalidade}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_ComposicaoModalidade_modalidade" action="#{ComposicaoControle.montarListaSelectItemModalidade}"
                                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:ComposicaoModalidade_modalidade"/>
                                </h:panelGroup>

                                <h:outputText value="#{msg_aplic.prt_ComposicaoModalidade_vezesSemanaNoPacote}" />
                                <h:panelGroup>
                                    <h:inputText  id="nrVezesSemana" styleClass="form"  
                                                  onkeypress="return Tecla(event);" 
                                                  onblur="blurinput(this);"  
                                                  onfocus="focusinput(this);"  
                                                  size="10" maxlength="10"  
                                                  value="#{ComposicaoControle.composicaoModalidadeVO.nrVezes}" />
                                </h:panelGroup>

                                <h:outputText value="#{msg_aplic.prt_ComposicaoModalidade_valorMensalComposicao}"
                                              rendered="#{ComposicaoControle.composicaoVO.modalidadesEspecificas}"/>
                                <h:inputText id="valorMensalComposicao2" onkeypress="return Tecla(event);" size="8" maxlength="8" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form" value="#{ComposicaoControle.composicaoModalidadeVO.valorMensalComposicao}"
                                             rendered="#{ComposicaoControle.composicaoVO.modalidadesEspecificas}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>

                                <script>
                                    VMasker(document.getElementById("form:valorMensalComposicao2")).maskMoney({
                                        precision: 2,
                                        separator: ',',
                                        delimiter: '.',
                                        zeroCents: false
                                    });
                                </script>
                            </script>
                        </h:panelGrid>
                        <a4j:commandButton id="addModalidade" action="#{ComposicaoControle.adicionarComposicaoModalidade}"
                                           reRender="panelMensagemErro,precoComposicao,composicaoModalidadeVO"
                                           focus="form:ComposicaoModalidade_modalidade" value="#{msg_bt.btn_adicionar}"
                                           image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="composicaoModalidadeVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{ComposicaoControle.composicaoVO.composicaoModalidadeVOs}" var="composicaoModalidade">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_ComposicaoModalidade_modalidade}" />
                                    </f:facet>
                                    <h:outputText value="#{composicaoModalidade.modalidade.nome}" />
                                    <h:outputText rendered="#{!composicaoModalidade.modalidade.ativo}" 
                                                              title="Significa que não vai aparecer na Negociação"  value=" (inativa)" />
                                </h:column>                                
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ComposicaoModalidade_precoModalidade}" />
                                    </f:facet>
                                    <h:outputText value="#{composicaoModalidade.modalidade.valorMensal}" >
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ComposicaoModalidade_valorMensalComposicao}" />
                                    </f:facet>
                                    <h:outputText  value="#{composicaoModalidade.valorMensalComposicao}" >
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_ComposicaoModalidade_vezesSemanaNoPacote}"/>
                                    </f:facet>
                                    <h:outputText value="#{composicaoModalidade.nrVezes == 0 ? '<vazio>' : composicaoModalidade.nrVezes}"/>
                                </h:column>                                
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <h:commandButton id="editarItemVenda" immediate="true" action="#{ComposicaoControle.editarComposicaoModalidade}"
                                                         value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                        <h:outputText value="    "/>

                                        <h:commandButton id="removerItemVenda" immediate="true" action="#{ComposicaoControle.removerComposicaoModalidade}"
                                                         value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                    </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">

                        <h:outputText value=" "/>

                    </h:panelGrid>
                    <h:commandButton  rendered="#{ComposicaoControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{ComposicaoControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgPacote" styleClass="mensagem"  value="#{ComposicaoControle.mensagem}"/>
                        <h:outputText id="msgPacoteDet" styleClass="mensagemDetalhada" value="#{ComposicaoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="novo" immediate="true" action="#{ComposicaoControle.novo}" value="#{msg_bt.btn_novo}"
                                         alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                        <h:outputText value="    "/>

                        <a4j:commandButton reRender="form" id="salvar" action="#{ComposicaoControle.gravar}" value="#{msg_bt.btn_gravar}"
                                         alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                        <h:outputText value="    "/>

                        <a4j:commandButton id="excluir"
                                           action="#{ComposicaoControle.confirmarExcluir}"
                                           value="#{msg_bt.btn_excluir}"
                                           oncomplete="#{ComposicaoControle.msgAlert}"
                                           reRender="mdlMensagemGenerica"
                                           alt="#{msg.msg_excluir_dados}" accesskey="3"
                                           styleClass="botoes nvoBt btSec btPerigo"/>

                        <h:outputText value="    "/>

                        <a4j:commandButton id="consultar" immediate="true" action="#{ComposicaoControle.consultar}"
                                         value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                         accesskey="4" styleClass="botoes nvoBt btSec"/>
                        
                        <h:outputText value="    "/>
                        <a4j:commandLink action="#{ComposicaoControle.realizarConsultaLogObjetoSelecionado}"
                                           reRender="form"
                                           oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                           title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                            <i class="fa-icon-list"></i>
                        </a4j:commandLink>
                        
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
</h:panelGrid>

    <jsp:include page="includes/include_modal_mensagem_generica.jsp"/>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
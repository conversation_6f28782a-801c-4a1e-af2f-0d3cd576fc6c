<%@include file="includes/imports.jsp" %>
<rich:modalPanel id="modalsave" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="650" top="55" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Novo modelo de mensagem"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkmodalsave"/>
            <rich:componentControl for="modalsave" attachTo="hidelinkmodalsave" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form ajaxSubmit="true" id="modalsaveform" style="padding: 20px">

        <h:panelGrid width="100%" columns="2" columnClasses="w20, w80" cellpadding="10" style="margin-top: 20px">
            <h:outputText styleClass="texto-size-14 cinza" value="T�tulo: "></h:outputText>
            <h:inputText value="#{MensagemBuilderControle.modelo.titulo}"
                         id="campoTitulo"
                         onblur="blurinput(this);" style="width: 90%;"
                         onfocus="focusinput(this);" styleClass="form"/>

            <h:outputText styleClass="texto-size-14 cinza" value="Tipo: "></h:outputText>
            <h:panelGroup layout="block" styleClass="cb-container" style="width: 90%; font-size: 14px">
                <h:selectOneMenu value="#{MensagemBuilderControle.modelo.tipoMensagem}" id="campoTipo">
                    <f:selectItems value="#{MensagemBuilderControle.tipos}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGrid>


        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink action="#{MensagemBuilderControle.gravar}"
                             id="btnGravar"
                             oncomplete="#{MensagemBuilderControle.msgAlert}"
                             style="margin-right: 10px"
                             reRender="mensagembuilder"
                             styleClass="botaoSecundario texto-size-14-real">
                <i class="fa-icon-save"></i>
                <h:outputText value="Gravar"/>
            </a4j:commandLink>

            <a4j:commandLink action="#{MensagemBuilderControle.gravarEnviando}"
                             id="btnGravarEProsseguir"
                             oncomplete="#{MensagemBuilderControle.msgAlert}"
                             reRender="mensagembuilder"
                             styleClass="botaoPrimario texto-size-14-real">
                <h:outputText value="Gravar e prosseguir com o envio"/>
                <i class="fa-icon-chevron-right"></i>
            </a4j:commandLink>
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<link
    href="./css/otimize.css"
    rel="stylesheet"
    type="text/css">
<script
    type="text/javascript"
    language="javascript"
src="hoverform.js"></script>
<%@taglib
    prefix="f"
    uri="http://java.sun.com/jsf/core"%>
    <%@taglib
        prefix="h"
        uri="http://java.sun.com/jsf/html"%>
        <%@taglib
            prefix="a4j"
            uri="https://ajax4jsf.dev.java.net/ajax"%>
            <%@taglib
                prefix="rich"
                uri="http://richfaces.ajax4jsf.org/rich"%>
                <%@taglib
                    prefix="jsfChart"
                    uri="http://sourceforge.net/projects/jsf-comp"%>
                    <f:loadBundle
                        var="msg_bt"
                        basename="propriedades.Botoes" />
                    <f:loadBundle
                        var="msg_aplic"
                        basename="propriedades.Aplicacao" />
                    <f:loadBundle
                        var="msg"
                        basename="propriedades.Mensagens" />
                    <head><%@include file="/includes/include_import_minifiles.jsp" %></head>
                    <f:view>
                        <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
                        <title><h:outputText value="Relatório de Cliente Sintético" /></title>
                        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
                        <h:form id="form">
                            <html>
                                <body>
                                    <table
                                        width="100%"
                                        align="center"
                                        height="100%"
                                        border="0"
                                        cellpadding="0"
                                        cellspacing="0">
                                        <tr>
                                            <td
                                                height="77"
                                                align="left"
                                                valign="top"
                                                >
                                                <jsp:include page="topoReduzido.jsp"/>
                                                <jsp:include page="include_head.jsp" flush="true"/>
                                                <h:panelGrid columns="1" width="100%" >
                                                    <h:panelGrid columns="1" style=" background-image:url('../imagens/fundoBarraTopo.png');" columnClasses="colunaCentralizada" width="100%">
                                                        <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_SituacaoContratoSinteticoDW_tituloGrafico}"/>
                                                    </h:panelGrid>
                                                </h:panelGrid>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                align="center"
                                                valign="top">
                                                <table
                                                    width="100%"
                                                    height="100%"
                                                    border="0"
                                                    cellpadding="0"
                                                    cellspacing="0">
                                                    <tr>
                                                        <td
                                                            align="left"
                                                            valign="top"
                                                            style="padding-top: 6px; padding-left: 15px;"><!-- inicio box -->
                                                            <table
                                                                width="100%"
                                                                border="0"
                                                                cellspacing="0"
                                                                cellpadding="0"
                                                                style="padding-bottom: 10px;">
                                                                <tr>
                                                                    <td
                                                                        width="20"
                                                                        align="left"
                                                                        valign="top"></td>
                                                                    <!-- inicio item-->
                                                                    <td
                                                                        align="left"
                                                                        width="100%"
                                                                        valign="top"><rich:tabPanel
                                                                            id="tabPanelFiltros"
                                                                            width="100%">
                                                                            <rich:tab
                                                                                styleClass="titulo3"
                                                                                label="Data">
                                                                                <h:panelGrid width="100%"  style="border:0;" id="panelFiltrosUtilizadosData">
                                                                                        <h:outputText styleClass="tituloCamposAzul" value="Veja os Filtros Utilizados "/>
                                                                                        <rich:toolTip
                                                                                            onclick="true"
                                                                                            followMouse="true"
                                                                                            direction="top-right"
                                                                                            style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                                                            showDelay="500"
                                                                                            >
                                                                                            ${SituacaoContratoSinteticoDWControle.filtros}
                                                                                        </rich:toolTip>
                                                                                </h:panelGrid>
                                                                                <h:panelGrid columns="1">
                                                                                    <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.usuario.administrador}">
                                                                                        <h:outputText
                                                                                            styleClass="titulo3"
                                                                                            value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_empresa}" />
                                                                                        <rich:spacer width="10px" />
                                                                                        <h:selectOneMenu
                                                                                            id="empresa"
                                                                                            styleClass="form"
                                                                                            onblur="blurinput(this);"
                                                                                            onfocus="focusinput(this);"
                                                                                            value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.empresa.codigo}">
                                                                                            <f:selectItems value="#{SituacaoContratoSinteticoDWControle.listaSelectItemEmpresa}" />                                                                  
                                                                                        </h:selectOneMenu>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup>
                                                                                        <h:outputText
                                                                                            styleClass="titulo3"
                                                                                            value="Data" />
                                                                                        <rich:spacer width="10px" />
                                                                                        <h:panelGroup>
                                                                                            <rich:calendar
                                                                                                id="dataFinal"
                                                                                                value="#{SituacaoContratoSinteticoDWControle.dataFinal}"
                                                                                                inputSize="10"
                                                                                                inputClass="form"
                                                                                                oninputblur="blurinput(this);"
                                                                                                oninputfocus="focusinput(this);"
                                                                                                oninputchange="return validar_Data(this.id);"
                                                                                                datePattern="dd/MM/yyyy"
                                                                                                enableManualInput="true"
                                                                                                zindex="2"
                                                                                                showWeeksBar="false" />
                                                                                            <h:message
                                                                                                for="dataFinal"
                                                                                                styleClass="mensagemDetalhada" />
                                                                                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                                                        </h:panelGroup>
                                                                                        <rich:spacer width="10px" />
                                                                                        <a4j:commandButton
                                                                                            id="consultarConsultor"
                                                                                            reRender="tabPanelFiltros,panelRelatorioGraficoCliente"
                                                                                            value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_consultar}"
                                                                                            title="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_consultar}"
                                                                                            action="#{SituacaoContratoSinteticoDWControle.consultarClientesPorFiltros}"
                                                                                            image="../imagens/botaoConsultar.png" />
                                                                                    </h:panelGroup>
                                                                                </h:panelGrid>
                                                                                <h:panelGrid
                                                                                    id="panelMensagem"
                                                                                    columns="3"
                                                                                    width="100%"
                                                                                    styleClass="tabMensagens">
                                                                                    <h:panelGrid
                                                                                        columns="1"
                                                                                        width="100%">
                                                                                        <h:outputText
                                                                                            styleClass="mensagem"
                                                                                            value="#{SituacaoContratoSinteticoDWControle.mensagem}" />
                                                                                        <h:outputText
                                                                                            styleClass="mensagemDetalhada"
                                                                                            value="#{SituacaoContratoSinteticoDWControle.mensagemDetalhada}" />
                                                                                    </h:panelGrid>
                                                                                </h:panelGrid>
                                                                            </rich:tab>
                                                                            <rich:tab
                                                                                styleClass="titulo3"
                                                                                label="Situação">
                                                                                <h:panelGrid width="100%" style="border:0;" id="panelFiltrosUtilizadosSituacao">
                                                                                        <h:outputText styleClass="tituloCamposAzul" value=" Veja os Filtros Utilizados "/>
                                                                                        <rich:toolTip
                                                                                            onclick="true"
                                                                                            followMouse="true"
                                                                                            direction="top-right"
                                                                                            style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                                                            showDelay="500"
                                                                                            >
                                                                                            ${SituacaoContratoSinteticoDWControle.filtros}
                                                                                        </rich:toolTip>
                                                                                </h:panelGrid>
                                                                                <rich:dataGrid
                                                                                    width="100%"
                                                                                    id="resultadoConsultaSituacao"
                                                                                    columnClasses="semBorda"
                                                                                    styleClass="semBorda"
                                                                                    value="#{SituacaoContratoSinteticoDWControle.listaSituacao}"
                                                                                    columns="#{SituacaoContratoSinteticoDWControle.nrColunaSituacao}"
                                                                                    elements="#{SituacaoContratoSinteticoDWControle.tamanhoListaSituacao}"
                                                                                    var="situacao">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                            styleClass="titulo3"
                                                                                            value="Filtrar Por: " />
                                                                                    </f:facet>
                                                                                    <h:panelGrid columns="2">
                                                                                        <h:selectBooleanCheckbox value="#{situacao.marcado}" />
                                                                                        <h:outputText
                                                                                            styleClass="titulo3"
                                                                                            value="#{situacao.nome_Apresentar}" />
                                                                                    </h:panelGrid>
                                                                                </rich:dataGrid>
                                                                            </rich:tab>
                                                                            <rich:tab
                                                                                styleClass="titulo3"
                                                                                label="Plano">
                                                                                <h:panelGrid width="100%" style="border:0;" id="panelFiltrosUtilizadosPlano">
                                                                                        <h:outputText styleClass="tituloCamposAzul" value="Veja os Filtros Utilizados "/>
                                                                                        <rich:toolTip
                                                                                            onclick="true"
                                                                                            followMouse="true"
                                                                                            direction="top-right"
                                                                                            style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                                                            showDelay="500"
                                                                                            >
                                                                                            ${SituacaoContratoSinteticoDWControle.filtros}
                                                                                        </rich:toolTip>
                                                                                </h:panelGrid>
                                                                                <rich:dataGrid
                                                                                    width="100%"
                                                                                    id="resultadoConsultaPlano"
                                                                                    columnClasses="semBorda"
                                                                                    styleClass="semBorda"
                                                                                    value="#{SituacaoContratoSinteticoDWControle.listaPlano}"
                                                                                    columns="#{SituacaoContratoSinteticoDWControle.nrColunaPlano}"
                                                                                    elements="#{SituacaoContratoSinteticoDWControle.tamanhoListaPlano}"
                                                                                    var="plano">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                            styleClass="titulo3"
                                                                                            value="Filtrar Por: " />
                                                                                    </f:facet>
                                                                                    <h:panelGrid columns="2">
                                                                                        <h:selectBooleanCheckbox value="#{plano.marcado}" />
                                                                                        <h:outputText
                                                                                            styleClass="titulo3"
                                                                                            value="#{plano.nome}" />
                                                                                    </h:panelGrid>
                                                                                </rich:dataGrid>
                                                                            </rich:tab>
                                                                            <rich:tab
                                                                                styleClass="titulo3"
                                                                                label="Vínculo de Carteira">
                                                                                <h:panelGrid width="100%" style="border:0;" id="panelFiltrosUtilizadosVinculoCarteira">
                                                                                      <h:outputText styleClass="tituloCamposAzul" value="Veja os Filtros Utilizados "/>
                                                                                        <rich:toolTip
                                                                                            onclick="true"
                                                                                            followMouse="true"
                                                                                            direction="top-right"
                                                                                            style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                                                            showDelay="500"
                                                                                            >
                                                                                            ${SituacaoContratoSinteticoDWControle.filtros}
                                                                                        </rich:toolTip>
                                                                                </h:panelGrid>
                                                                                <rich:dataGrid
                                                                                    width="100%"
                                                                                    id="resultadoConsultaConsultor"
                                                                                    columnClasses="semBorda"
                                                                                    styleClass="semBorda"
                                                                                    value="#{SituacaoContratoSinteticoDWControle.listaVinculoCarteira}"
                                                                                    columns="#{SituacaoContratoSinteticoDWControle.nrColunaVinculoCarteira}"
                                                                                    elements="#{SituacaoContratoSinteticoDWControle.tamanhoListaVinculoCarteira}"
                                                                                    var="consultor">
                                                                                    <f:facet name="header">
                                                                                        <h:outputText
                                                                                            styleClass="titulo3"
                                                                                            value="Filtrar Por: " />
                                                                                    </f:facet>
                                                                                    <h:panelGrid columns="2">
                                                                                        <h:selectBooleanCheckbox value="#{consultor.marcado}" />
                                                                                        <h:outputText
                                                                                            styleClass="titulo3"
                                                                                            value="#{consultor.nome}" />
                                                                                    </h:panelGrid>
                                                                                </rich:dataGrid>
                                                                            </rich:tab>
                                                                        </rich:tabPanel></td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td
                                                            align="center"
                                                            valign="top"><h:panelGrid
                                                                id="panelMenuRelatorio"
                                                                columns="2"
                                                                width="50%"
                                                                columnClasses="colunaCentralizada">
                                                                <a4j:commandButton
                                                                    title="Atualizar Lista de Cliente"
                                                                    action="#{SituacaoContratoSinteticoDWControle.atualizarLista}"
                                                                    image="../imagens/botaoAtualizarGrande.png"
                                                                    reRender="form:panelRelatorioGraficoCliente,form:mensagem, form:tabPanelFiltros" />
                                                            </h:panelGrid></td>
                                                    </tr>
                                                    <tr>
                                                        <td
                                                            align="left"
                                                            valign="top"
                                                            width="100%"
                                                            style="padding: 7px 20px 0 20px;"><h:panelGroup id="panelRelatorioGraficoCliente">
                                                                <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.pizza || SituacaoContratoSinteticoDWControle.barra}">
                                                                    <table
                                                                        width="100%"
                                                                        height="100%"
                                                                        border="0"
                                                                        align="left"
                                                                        cellpadding="0"
                                                                        cellspacing="0"
                                                                        class="text"
                                                                        style="margin-bottom: 20px;">
                                                                        <tr>
                                                                            <td
                                                                                width="19"
                                                                                height="50"
                                                                                align="left"
                                                                                valign="top"><img
                                                                                    src="images/box_centro_top_left.gif"
                                                                                    width="19"
                                                                                    height="50"></td>
                                                                            <td
                                                                                align="left"
                                                                                valign="top"
                                                                                background="images/box_centro_top.gif"
                                                                                class="tituloboxcentro"
                                                                                style="padding: 11px 0 0 0;">Relatório Sintético Cliente</td>
                                                                            <td
                                                                                width="19"
                                                                                align="left"
                                                                                valign="top"><img
                                                                                    src="images/box_centro_top_right.gif"
                                                                                    width="19"
                                                                                    height="50"></td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td
                                                                                align="left"
                                                                                valign="top"
                                                                                background="images/box_centro_left.gif"></td>
                                                                            <td
                                                                                align="left"
                                                                                valign="top"
                                                                                bgcolor="#ffffff"
                                                                                style="padding: 5px 15px 5px 15px;"><h:panelGrid
                                                                                    id="panelGrafico"
                                                                                    columns="2"
                                                                                    width="100%"
                                                                                    columnClasses="colunaCentralizada">
                                                                                    <h:panelGrid>
                                                                                        <jsfChart:chart
                                                                                            id="chart"
                                                                                            rendered="#{SituacaoContratoSinteticoDWControle.barra}"
                                                                                            datasource="#{SituacaoContratoSinteticoDWControle.dataSetBarra}"
                                                                                            type="bar"
                                                                                            is3d="true"
                                                                                            background="#B4CDCD"
                                                                                            foreground="#FFFAFA"
                                                                                            depth="35"
                                                                                            styleClass="tabConsulta"
                                                                                            colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                            lineStokeWidth="2"
                                                                                            alpha="65"
                                                                                            antialias="true"
                                                                                            title="#{msg_aplic.prt_SituacaoContratoSinteticoDW_tituloGrafico}"
                                                                                            xlabel="#{msg_aplic.prt_SituacaoContratoSinteticoDW_situacaoCliente}"
                                                                                            ylabel="#{msg_aplic.prt_SituacaoContratoSinteticoDW_qtdeClientes}"
                                                                                            height="300"
                                                                                            width="480"
                                                                                            legend="true">
                                                                                        </jsfChart:chart>
                                                                                        <jsfChart:chart
                                                                                            rendered="#{SituacaoContratoSinteticoDWControle.pizza}"
                                                                                            id="chart1"
                                                                                            datasource="#{SituacaoContratoSinteticoDWControle.dataSetPizza}"
                                                                                            type="pie"
                                                                                            is3d="true"
                                                                                            background="#B4CDCD"
                                                                                            foreground="#FFFAFA"
                                                                                            depth="24"
                                                                                            colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                            alpha="65"
                                                                                            antialias="true"
                                                                                            title="#{msg_aplic.prt_SituacaoContratoSinteticoDW_tituloGrafico}"
                                                                                            legend="false"
                                                                                            height="300"
                                                                                            width="480">
                                                                                        </jsfChart:chart>
                                                                                    </h:panelGrid>
                                                                                    <h:panelGrid>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdVisitante}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoVisitante}"
                                                                                                value="> Visitantes:  #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesVisitantes}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdVisitanteFreePass}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoVisitanteFreePass}"
                                                                                                value="> Visitantes - Fress Pass: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesVisitantesFreePass}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdVisitanteAulaAvulsa}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoVisitanteAulaAvulsa}"
                                                                                                value="> Visitantes - Aula Avulsa: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesVisitantesAulaAvulsa}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdVisitanteDiaria}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoVisitanteDiaria}"
                                                                                                value="> Visitantes - Diária: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesVisitantesDiaria}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdAtivo}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoAtivoNormal}"
                                                                                                value="> Ativos - Normais: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesAtivosNormal}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdAtivoTrancado}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoAtivoTrancado}"
                                                                                                value="> Trancados: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesAtivosTrancados}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdAtivoTrancadoVencido}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoAtivoTrancadoVencido}"
                                                                                                value="> Trancados Vencidos: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesAtivosTrancadosVencidos}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdAtivoAvencer}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoAtivoAVencer}"
                                                                                                value="> Ativos - A Vencer: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesAtivosAvencer}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdAtivoAtestado}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoAtivoAtestado}"
                                                                                                value="> Ativos - Atestado: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesAtivosAtestado}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdAtivoCarencia}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoAtivoCarencia}"
                                                                                                value="> Ativos - Férias: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesAtivosCarencia}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdAtivoVencido}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoAtivoVencido}"
                                                                                                value="> Inativos - Vencidos: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesAtivosVencido}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdInativo}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoInativo}"
                                                                                                value="> Inativos: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesInativos}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdInativoCancelado}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoInativoCancelado}"
                                                                                                value="> Inativos - Cancelados: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesInativosCancelados}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdInativoDesistente}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoInativoDesistente}"
                                                                                                value="> Inativos - Desistentes: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesInativosDesistencia}" />
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarQtdTotal}">
                                                                                            <a4j:commandLink
                                                                                                styleClass="titulo3"
                                                                                                title="Ver Detalhes"
                                                                                                action="#{SituacaoContratoSinteticoDWControle.montarListaClienteAnaliticoTotalClientes}"
                                                                                                value="> Total de Clientes: #{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.qtdClientesTotal}" />
                                                                                        </h:panelGroup>
                                                                                    </h:panelGrid>
                                                                                </h:panelGrid> <h:panelGrid
                                                                                    id="panelMenuGrafico"
                                                                                    columns="1"
                                                                                    width="100%"
                                                                                    columnClasses="colunaCentralizada">
                                                                                    <h:panelGroup>
                                                                                        <a4j:commandButton
                                                                                            title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoBarra}"
                                                                                            immediate=""
                                                                                            action="#{SituacaoContratoSinteticoDWControle.alterarParaBarra}"
                                                                                            image="../imagens/botaoBarraGrande.png"
                                                                                            reRender="form:panelGrafico,form:panelMenuGrafico,form:mensagem, form:tabPanelFiltros" />
                                                                                        <rich:spacer width="8" />
                                                                                        <a4j:commandButton
                                                                                            title="#{msg_aplic.prt_SituacaoRenovacaoSinteticoDW_graficoPizza}"
                                                                                            action="#{SituacaoContratoSinteticoDWControle.alterarParaPizza}"
                                                                                            image="../imagens/botaoPizzaGrande.png"
                                                                                            reRender="form:panelGrafico,form:panelMenuGrafico,form:mensagem, form:tabPanelFiltros" />
                                                                                    </h:panelGroup>
                                                                                </h:panelGrid></td>
                                                                            <td
                                                                                align="left"
                                                                                valign="top"
                                                                                background="images/box_centro_right.gif"><img src="images/shim.gif"></td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td
                                                                                height="20"
                                                                                align="left"
                                                                                valign="top"><img
                                                                                    src="images/box_centro_bottom_left.gif"
                                                                                    width="19"
                                                                                    height="20"></td>
                                                                            <td
                                                                                align="left"
                                                                                valign="top"
                                                                                background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                                                            <td
                                                                                align="left"
                                                                                valign="top"><img
                                                                                    src="images/box_centro_bottom_right.gif"
                                                                                    width="19"
                                                                                    height="20"></td>
                                                                        </tr>
                                                                    </table>
                                                                </h:panelGroup>
                                                            </h:panelGroup></td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                height="93"
                                                align="left"
                                                valign="top"
                                                class="bgrodape"><jsp:include
                                                    page="include_rodape_1.jsp"
                                                    flush="true" /></td>
                                        </tr>
                                    </table>
                                </h:form>
                            </f:view>

<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="includes/imports.jsp" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
</style>
<h:panelGroup layout="block" id="panelGeralImportacaoColaborador">

    <h:panelGroup layout="block" styleClass="panelObjetivo">
        <h:outputLabel value="Objetivo:" styleClass="textoObjetivo"
                       style="font-weight: bold; font-style: italic;"/>
        <h:outputLabel styleClass="textoObjetivo"
                       value="Realizar importação de colaboradores através de uma planilha modelo disponibilizada para download."/>
    </h:panelGroup>

    <h:panelGroup layout="block" style="padding: 15px;">


        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="1º passo:" styleClass="passosImportacao"/>
            <a4j:commandLink target="_blank"
                             style="padding-left: 5px; font-size: 15px;"
                             value="Baixar planilha modelo"
                             oncomplete="location.href='../DownloadSV?mimeType=application/vnd.ms-excel&diretorio=modelo&relatorio=modelo_importacao_colaborador.xlsx'"/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="2º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Informe as configurações:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno"
                          style="display: inline-flex; width: 100%"
                          id="panelConfiguracoesImportacaoColaborador">
                <h:panelGrid columns="2" width="100%"
                             columnClasses="colunaEsquerdaImport, colunaDireitaImport">

                    <h:outputLabel value="DDD Padrão:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:inputText styleClass="form tooltipster"
                                 title="DDD Padrão para os números sem o DDD informado. (Somente números)"
                                 maxlength="3" size="3"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.configColaboradorTO.padraoDDD}"/>

                    <h:outputLabel value="Validar CPF já cadastrado:"
                                   styleClass="passosImportacaoDescricao tooltipster"/>
                    <h:selectBooleanCheckbox styleClass="form tooltipster"
                                             style="padding-left: 5px"
                                             title="Caso exista um colaborador com o mesmo CPF o cadastro não será importado."
                                             value="#{ImportacaoControle.configColaboradorTO.validarCpfCnpjJaCadastrado}"/>

                    <h:outputLabel value="Máscara campos de data:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.configColaboradorTO.mascaraDataEnum}">
                        <f:selectItems value="#{ImportacaoControle.listaSelectItemMascaraData}"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Criar Usuário:"
                                   styleClass="passosImportacaoDescricao tooltipster"/>
                    <h:selectBooleanCheckbox styleClass="form tooltipster"
                                             style="padding-left: 5px"
                                             value="#{ImportacaoControle.configColaboradorTO.criarUsuario}">
                        <a4j:support event="onchange" reRender="panelGeralImportacaoColaborador"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectBooleanCheckbox>

                    <c:if test="${ImportacaoControle.configColaboradorTO.importarContratos}">

                        <h:outputLabel value="Plano:"
                                       styleClass="passosImportacaoDescricao"/>
                        <h:selectOneMenu onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         value="#{ImportacaoControle.configColaboradorTO.plano}">
                            <f:selectItems value="#{ImportacaoControle.listaPlano}"/>
                            <a4j:support event="onchange" status="false"
                                         oncomplete="atualizarTempoImportacao()"/>
                        </h:selectOneMenu>

                        <h:outputLabel value="Modalidade:"
                                       styleClass="passosImportacaoDescricao"/>

                        <h:selectOneMenu onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         title="São apresentado somente modalidades que não utilizam turma."
                                         value="#{ImportacaoControle.configColaboradorTO.modalidade}">
                            <f:selectItems value="#{ImportacaoControle.listaModalidade}"/>
                            <a4j:support event="onchange" status="false"
                                         oncomplete="atualizarTempoImportacao()"/>
                        </h:selectOneMenu>

                        <h:outputLabel value="Horário:"
                                       styleClass="passosImportacaoDescricao"/>

                        <h:selectOneMenu onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         value="#{ImportacaoControle.configColaboradorTO.horario}">
                            <f:selectItems value="#{ImportacaoControle.listaHorarios}"/>
                            <a4j:support event="onchange" status="false"
                                         oncomplete="atualizarTempoImportacao()"/>
                        </h:selectOneMenu>

                        <h:outputLabel value="Dias Carência:"
                                       styleClass="passosImportacaoDescricao"/>
                        <h:inputText styleClass="form tooltipster"
                                     title="Número de dias de carência"
                                     maxlength="2" size="2"
                                     onkeypress="bloquearEnter()"
                                     style="padding-left: 5px"
                                     value="#{ImportacaoControle.configColaboradorTO.diasCarencia}"/>


                    </c:if>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="3º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Preencher a planilha seguindo as seguintes regras:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno" style="font-size: 15px;">
                       <span>
                            <ul style="padding: 5px;margin: 0;">
                                <c:if test="${!ImportacaoControle.configColaboradorTO.criarUsuario}">
                                    <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Campos Obrigatórios para importar o cadastro: <b>ID_EXTERNO, NOME e TIPO_COLABORADOR</b></li>
                                </c:if>

                                <c:if test="${ImportacaoControle.configColaboradorTO.criarUsuario}">
                                    <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Campos Obrigatórios para importar o cadastro: <b>ID_EXTERNO, NOME, CPF, DATA_NASCIMENTO, EMAIL e TIPO_COLABORADOR</b></li>
                                </c:if>
                            </ul>
                           <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Coluna ATIVO informar:
                                  <ol style="padding-top: 5px;"><b>"SIM"</b> para Ativo</ol>
                                  <ol><b>"NÃO"</b> para Inativo</ol>
                                  <ol style="padding-top: 5px;"><i>*Caso não seja informado será importado como <b>ATIVO</b></i></ol>
                                </li>
                            </ul>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Coluna ESTADO CIVIL informar a Letra:
                                  <ol style="padding-top: 5px;"><b>"S"</b> para Solteiro(a)</ol>
                                  <ol><b>"C"</b> para Casado(a)</ol>
                                  <ol><b>"A"</b> para Amasiado(a)</ol>
                                  <ol><b>"V"</b> para Viúvo(a)</ol>
                                  <ol><b>"D"</b> para Divorciado(a)</ol>
                                  <ol><b>"P"</b> para Separado(a)</ol>
                                  <ol><b>"U"</b> para União estável</ol>
                                </li>
                            </ul>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px">
                                    Coluna SEXO BIOLÓGICO informar a letra:
                                    <ol style="padding-top: 5px;"><b>"M"</b> para Masculino</ol>
                                    <ol><b>"F"</b> para Feminino</ol>
                                </li>
                            </ul>
                       </span>
            </h:panelGroup>

            <c:if test="${ImportacaoControle.configColaboradorTO.criarUsuario}">
                <br/>
                <h:panelGroup layout="block" styleClass="panelPassosInterno" style="font-size: 15px; padding-left: 0; padding-top: 0;">
                    <h:outputText styleClass="observacoesImportantes" value="* Observações importantes:"/>
                    <br/>
                    <li style="margin-left: 2%;line-height: 15px; padding-top: 10px"> Será criado usuário somente para
                        colaborador <b>ATIVO</b>.
                    </li>
                    <li style="margin-left: 2%;line-height: 15px; padding-top: 5px"> A senha do usuário será gerada
                        aleatóriamente e será enviada por email.
                    </li>
                </h:panelGroup>
            </c:if>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="4º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Fazer o Upload da planilha baixada e realizar a importação:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelUploadFileColaborador" style="padding-top: 5px; padding-left: 30px">
            <rich:fileUpload
                    fileUploadListener="#{ImportacaoControle.uploadArquivoColaborador}"
                    immediateUpload="true" id="imagemModeloUploadColaborador"
                    acceptedTypes="xls,xlsx" allowFlash="false"
                    listHeight="58px"
                    cancelEntryControlLabel="Cancelar"
                    addControlLabel="Adicionar"
                    clearControlLabel="Remover"
                    clearAllControlLabel="Remover Todos"
                    doneLabel="Concluído"
                    sizeErrorLabel="Limite de tamanho atingido"
                    uploadControlLabel="Carregar"
                    transferErrorLabel="Erro na transferência"
                    stopControlLabel="Parar"
                    stopEntryControlLabel="Parar"
                    progressLabel="Carregando"
                    maxFilesQuantity="1">
                <a4j:support event="onerror" reRender="panelBotoesImportacaoColaborador"
                             action="#{ImportacaoControle.removerArquivo}"/>
                <a4j:support event="onupload" reRender="panelBotoesImportacaoColaborador"/>
                <a4j:support event="onuploadcomplete" reRender="panelBotoesImportacaoColaborador"/>
                <a4j:support event="onclear" reRender="panelBotoesImportacaoColaborador, panelUploadFileColaborador"
                             action="#{ImportacaoControle.removerArquivo}"/>
            </rich:fileUpload>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" id="panelBotoesImportacaoColaborador"
                  styleClass="panelBotoesImportacao">
        <a4j:commandLink id="btnImportarColaborador" value="Ler Arquivo"
                         style="padding-left: 15px"
                         onclick="atualizarTempoImportacao()"
                         rendered="#{ImportacaoControle.apresentarImportar}"
                         action="#{ImportacaoControle.processarArquivoColaborador}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         title="Processar Importação de Dados"
                         reRender="panelGeralModalConfirmarImportacao, formModImpo"
                         styleClass="botoes nvoBt"/>
    </h:panelGroup>
</h:panelGroup>

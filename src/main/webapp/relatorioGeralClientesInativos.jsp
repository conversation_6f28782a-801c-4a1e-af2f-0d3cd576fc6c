<%@include file="pages/estudio/includes/include_imports.jsp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<c:set var="moduloSession" value="1" scope="session"/>

<html>

<head>
    <script type="text/javascript" src="${contexto}/script/basico.js"></script>
    <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
    <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
    <link href="${contexto}/css/otimizeCRM.css" rel="stylesheet" type="text/css">
    <jsp:include page="pages/estudio/includes/include_head.jsp"/>

    <style>
        .rich-table, .rich-table-header, .rich-table-headercell, .rich-table-cell, .rich-subtable-cell,
        .rich-table-footercell, .rich-subtable-footercell {
            border-width: 1px;
        }
    </style>
</head>

<body>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gest�o de Agendamentos"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Clientes sem Sess�o" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}relatorio-clientes-sem-sessao-agenda-studio/"
                                                      title="Clique e saiba mais: Cliente sem Sess�o"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" columnClasses="column1" style="width:100%;">
                                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                                     columnClasses="classEsquerda, classDireita" style="width: 100%">
                                            <h:outputText value="Per�odo:" styleClass="texto_disponibilidade"/>
                                            <h:panelGroup>
                                                <rich:calendar
                                                        locale="pt_BR"
                                                        inputSize="10"
                                                        inputClass="form"
                                                        oninputblur="blurinput(this);"
                                                        oninputfocus="focusinput(this);"
                                                        oninputchange="return validar_Data(this.id);"
                                                        datePattern="dd/MM/yyyy"
                                                        enableManualInput="true"
                                                        zindex="2"
                                                        showWeeksBar="false"
                                                        value="#{RelatorioClientesInativosControle.dataInicio}"
                                                        popup="true" styleClass="texto_disponibilidade">
                                                </rich:calendar>
                                                <h:outputLabel value=" a " styleClass="texto_disponibilidade"/>

                                                <rich:calendar
                                                        locale="pt_BR"
                                                        inputSize="10"
                                                        inputClass="form"
                                                        oninputblur="blurinput(this);"
                                                        oninputfocus="focusinput(this);"
                                                        oninputchange="return validar_Data(this.id);"
                                                        datePattern="dd/MM/yyyy"
                                                        enableManualInput="true"
                                                        zindex="2"
                                                        showWeeksBar="false"
                                                        value="#{RelatorioClientesInativosControle.dataFinal}"
                                                        popup="true" styleClass="texto_disponibilidade">
                                                </rich:calendar>
                                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                            </h:panelGroup>

                                            <h:outputText value="Profissional:"
                                                          styleClass="texto_disponibilidade"/>
                                            <h:selectOneMenu
                                                    value="#{RelatorioClientesInativosControle.professor.codigo}">
                                                <f:selectItems
                                                        value="#{RelatorioClientesInativosControle.listaSelectItemProfessores}"/>
                                            </h:selectOneMenu>

                                            <h:outputText value="Produto:" styleClass="texto_disponibilidade"/>
                                            <h:selectOneMenu
                                                    value="#{RelatorioClientesInativosControle.produto.codigo}">
                                                <f:selectItems
                                                        value="#{RelatorioClientesInativosControle.listaSelectItemProdutos}"/>
                                            </h:selectOneMenu>

                                            <h:outputText value="Tipo da Busca:"
                                                          styleClass="texto_disponibilidade"/>
                                            <h:selectOneRadio styleClass="texto_disponibilidade"
                                                              value="#{RelatorioClientesInativosControle.tipoConsulta}">
                                                <f:selectItem itemLabel="Pela �ltima Venda" itemValue="VENDA"/>
                                                <f:selectItem itemLabel="Pelo �ltimo Agendamento"
                                                              itemValue="AGENDAMENTO"/>
                                            </h:selectOneRadio>
                                        </h:panelGrid>


                                        <h:panelGrid columns="2" style="width:100%;" id="acoes">
                                            <a4j:commandButton value="Consultar"
                                                               image="imagens/estudio/pesquisar.png"
                                                               action="#{RelatorioClientesInativosControle.imprimirRelatorio}"
                                                               reRender="resultado, mensagemConsulta, acoes"/>


                                            <h:panelGrid id="Impressao" columns="3" style="float:right;">
                                                <a4j:commandButton id="imprimirPDF"
                                                                   image="imagens/estudio/imprimir.png"
                                                                   value="Imprimir"
                                                                   ajaxSingle="false"
                                                                   styleClass="botoes"
                                                                   style="float:right;"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty RelatorioClientesInativosControle.lista}"
                                                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','ClientesInativos', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2">
                                                    <f:attribute name="lista"
                                                                 value="#{RelatorioClientesInativosControle.lista}"/>
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <f:attribute name="atributos"
                                                                 value="cliente=Cliente,telefone=Telefones,qtdDias=Qtd. Dias,data=�ltimo Evento"/>
                                                    <f:attribute name="prefixo" value="ClientesInativosStudio"/>
                                                </a4j:commandButton>

                                                <a4j:commandButton id="exportarExcel"
                                                                   styleClass="botoes"
                                                                   style="float:right;"
                                                                   image="imagens/estudio/excel.png"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty RelatorioClientesInativosControle.lista}"
                                                                   value="Excel"
                                                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','ClientesInativos', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2">
                                                    <f:attribute name="lista"
                                                                 value="#{RelatorioClientesInativosControle.lista}"/>
                                                    <f:attribute name="tipo" value="xlsx"/>
                                                    <f:attribute name="atributos"
                                                                 value="cliente=Cliente,telefone=Telefones,qtdDias=Qtd. Dias,data=�ltimo Evento"/>
                                                    <f:attribute name="prefixo" value="ClientesInativosStudio"/>
                                                </a4j:commandButton>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGrid>

                                    <h:panelGrid id="mensagemConsulta" columns="3" width="100%"
                                                 styleClass="tabMensagens">
                                        <a4j:commandButton rendered="#{RelatorioClientesInativosControle.sucesso}"
                                                           image="./imagens/sucesso.png"/>
                                        <a4j:commandButton rendered="#{RelatorioClientesInativosControle.erro}"
                                                           image="./imagens/erro.png"/>
                                        <h:panelGrid columns="1" width="100%">
                                            <h:outputText id="msgNormal" styleClass="mensagem"
                                                          value="#{RelatorioClientesInativosControle.mensagem}"/>
                                            <h:outputText id="msgDetalhada" styleClass="mensagemDetalhada"
                                                          value="#{RelatorioClientesInativosControle.mensagemDetalhada}"/>
                                        </h:panelGrid>
                                    </h:panelGrid>

                                    <h:panelGroup id="resultado">
                                        <h:panelGrid columns="1" width="100%">
                                            <rich:dataTable width="100%"
                                                            value="#{RelatorioClientesInativosControle.lista}"
                                                            var="cliente" rowKeyVar="status">
                                                <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                                                <rich:column sortBy="#{cliente.cliente}">
                                                    <f:facet name="header">
                                                        <h:outputText value="Cliente"/>
                                                    </f:facet>
                                                    <h:outputText value="#{cliente.cliente}"/>
                                                </rich:column>

                                                <rich:column sortBy="#{cliente.telefone}">
                                                    <f:facet name="header">
                                                        <h:outputText value="Telefones"/>
                                                    </f:facet>
                                                    <h:outputText value="#{cliente.telefone}"/>
                                                </rich:column>

                                                <rich:column sortBy="#{cliente.qtdDias}">
                                                    <f:facet name="header">
                                                        <h:outputText value="Qtd. Dias"/>
                                                    </f:facet>
                                                    <h:outputText value="#{cliente.qtdDias}"/>
                                                </rich:column>

                                                <rich:column sortBy="#{cliente.data}">
                                                    <f:facet name="header">
                                                        <h:outputText value="�ltimo evento"/>
                                                    </f:facet>
                                                    <h:outputText value="#{cliente.dataApresentar}"/>
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Op��o"/>
                                                    </f:facet>
                                                    <a4j:commandButton image="../imagens/botaoVisualizar.png"
                                                                       action="#{RelatorioClientesInativosControle.irParaTelaCliente}"
                                                                       oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                        <f:param name="state" value="AC"/>
                                                    </a4j:commandButton>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                    <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                                       id="btnAtualizaPagina"/>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
    </h:form>

    <%@include file="pages/estudio/includes/include_modal_dados_aluno.jsp" %>
    <%@include file="pages/estudio/includes/include_modal_agenda_aluno.jsp" %>
    <%@include file="pages/estudio/includes/include_modal_erro.jsp" %>
    <%@include file="pages/estudio/includes/include_modal_sucesso.jsp" %>

</f:view>
</body>
</html>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_LogControleUsabilidade_tituloForm}"/>
    </title>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{LogControleUsabilidadeControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_LogControleUsabilidade_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_LogControleUsabilidade_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{LogControleUsabilidadeControle.logControleUsabilidadeVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_LogControleUsabilidade_entidade}" />
                    <h:panelGroup>
                        <h:inputText  id="entidade"  size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{LogControleUsabilidadeControle.logControleUsabilidadeVO.entidade}" />
                        <h:message for="entidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_LogControleUsabilidade_maquina}" />
                    <h:panelGroup>
                        <h:inputText  id="maquina" required="true" size="70" maxlength="80" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{LogControleUsabilidadeControle.logControleUsabilidadeVO.maquina}" />
                        <h:message for="maquina" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_LogControleUsabilidade_acao}" />
                    <h:inputText  id="acao" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{LogControleUsabilidadeControle.logControleUsabilidadeVO.acao}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_LogControleUsabilidade_dataRegistro}" />
                    <h:panelGroup>
                        <rich:calendar id="dataRegistro"
                                       value="#{LogControleUsabilidadeControle.logControleUsabilidadeVO.dataRegistro}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataRegistro" styleClass="mensagemDetalhada"/>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_LogControleUsabilidade_usuario}" />
                    <h:panelGroup>
                        <h:inputText  id="usuario" required="true" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{LogControleUsabilidadeControle.logControleUsabilidadeVO.usuario.nome}" />
                        <h:message for="usuario" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_LogControleUsabilidade_empresa}" />
                    <h:panelGroup>
                        <h:inputText  id="empresa" required="true" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{LogControleUsabilidadeControle.logControleUsabilidadeVO.empresa.nome}" />
                        <h:message for="empresa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{LogControleUsabilidadeControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{LogControleUsabilidadeControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{LogControleUsabilidadeControle.novo}" image="./imagens/botaoNovo.png" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="salvar" action="#{LogControleUsabilidadeControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="excluir" onclick="return confirm('#{msg.msg_ConfirmaExclusao}');" action="#{LogControleUsabilidadeControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="consultar" immediate="true" action="#{LogControleUsabilidadeControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:codigo").focus();
</script>
<%-- 
    Document   : include_venda_avulsa_form
    Created on : 03/06/2012, 12:55:41
    Author     : Waller
--%>
<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="./includes/imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>
<style>
    .btnAdicionar:hover,
    .btnAdicionar:focus {
        border: 2px solid black !important;
    }
    .botoes.nvoBt.btnExperimente{
        background-color: transparent;
        border: 1px solid #1998fc;
        color: #1998fc;
        padding: 0px 20px;
        box-shadow: none;
        border-radius: 4px;
        cursor: pointer;
        position: relative;
        line-height: 40px;
        font-weight: 400;
        font-size: 16px;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 30px;
        height: 17px;
    }

    /* Hide default HTML checkbox */
    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    /* The slider */
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #2196F3;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 17px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .padrao-va {
        display: flex;
        color: #797D86;
        font-size: 16px;
        font-weight: 400;
        line-height: 16px;
        /*padding: 30px 0px 0 40px;*/
        /*width: calc(100% - 80px);*/
        /*justify-content: flex-end;*/
    }
    .padrao-va label{
        margin-left: 8px;
    }

    .padrao-va .clicavel {
        cursor: pointer;
    }
</style>

<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
<h:panelGroup id="vendaAvulsaContainer" layout="block" styleClass="container-box zw_ui especial">
    <h:panelGroup layout="block">

        <h:panelGroup layout="block" styleClass="margin-box">
            <div layout="block" style="margin-top: 6px;width: 100%;display: inline-flex;margin-bottom: -35px;">
                <span style="background-color: #FEE6E6;font-family: 'Nunito Sans';font-size: 14px;font-weight: bold;padding: 10px 10px 10px 10px;border-radius: 5px;width: 100%;"
                      class="fa-icon-exclamation-sign texto-cor-vermelho">
                    Nos próximos meses essa tela será descontinuada. Experimente a nova versão e teste as melhorias. Você poderá nos enviar feedbacks e ajudar a construir um sistema melhor para o seu dia a dia.
                </span>
            </div>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: 30px;">
            <h:panelGroup layout="block" id="divGeralVendaAvulsa"
                          style="display: flex; align-items: center;">
                <h:panelGroup layout="block" id="divTituloVendaAvulsa"
                              style="width: 60%;">
                    <h:outputText value="Venda Avulsa" styleClass="container-header-titulo"/>
                    <h:outputLink styleClass="linkWiki"
                                  value="#{SuperControle.urlBaseConhecimento}como-fazer-uma-venda-de-produto-para-aluno-venda-avulsa/"
                                  title="Clique e saiba mais: Venda Avulsa" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                </h:panelGroup>

                <h:panelGroup layout="block" id="divNovaVersaoVendaAvulsa"
                              style="width: 40%; justify-content: flex-end; display: flex;"
                              rendered="#{LoginControle.possuiModuloNZW}">
                    <span class="padrao-va"
                          id="div-switch-nova-versao">
                        <span class="clicavel" onclick="usarNovaVendaAvulsa()">Usar nova versão</span>
                        <label class="switch clicavel" onclick="usarNovaVendaAvulsa()">
                            <input type="checkbox"
                                   id="switch-nova-versao">
                            <span class="slider round"></span>
                        </label>
                    </span>
                </h:panelGroup>

                <a4j:jsFunction name="usarNovaVendaAvulsa"
                                oncomplete="#{VendaAvulsaControle.msgAlert}"
                                action="#{VendaAvulsaControle.usarNovaVendaAvulsa}">
                </a4j:jsFunction>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="margin-box" style="margin-top: 0">
          <h:panelGrid id="panelGeral" columns="1"  width="100%" rowClasses="linhaImpar, linhaPar">
                    <h:panelGroup rendered="#{VendaAvulsaControle.vendaAvulsaVO.apresentarEmpresa}">
                        <h:outputText  styleClass="tituloCampos"  value="#{msg_aplic.prt_VendaAvulsa_empresa}" />
                        <rich:spacer width="10"/>
                        <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{VendaAvulsaControle.vendaAvulsaVO.empresa.codigo}" >
                            <f:selectItems  value="#{VendaAvulsaControle.listaSelectItemEmpresa}" />
                            <a4j:support event="onchange"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_empresa" action="#{VendaAvulsaControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:empresa"/>
                    </h:panelGroup>
 
                <h:panelGroup id="painelDataVenda">
                    <h:outputText  styleClass="tituloCampos"  value="Data Lançamento: " />
                    <h:outputText  styleClass="tituloCampos" rendered="#{!VendaAvulsaControle.editarData}"
                                   value="#{VendaAvulsaControle.vendaAvulsaVO.dataRegistro}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>

                    <a4j:commandButton image="./images/icon_chave.png" title="Editar data de lançamento"
                                       action="#{VendaAvulsaControle.editarCampoData}"
                                       oncomplete="#{VendaAvulsaControle.mensagemNotificar}"
                                       rendered="#{!VendaAvulsaControle.editarData}"
                                       reRender="panelAutorizacaoFuncionalidade"/>

                    <rich:calendar rendered="#{VendaAvulsaControle.editarData}"
                                   value="#{VendaAvulsaControle.dataNova}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <rich:spacer width="10"></rich:spacer>
                    <a4j:commandButton image="./images/tick.png" title="Aplicar Data" alt="Aplicar Data" rendered="#{VendaAvulsaControle.editarData}"
                                       action="#{VendaAvulsaControle.aplicarData}" reRender="form:vendaAvulsaContainer"/>
                    <rich:spacer width="5" />
                    <a4j:commandButton rendered="#{VendaAvulsaControle.editarData}" title="Limpar Altera??o"
                                       action="#{VendaAvulsaControle.limparAlterarData}"
                                       image="images/limpar.gif" alt="Limpar Altera??o"
                                       reRender="form:vendaAvulsaContainer"/>
                </h:panelGroup>

              <h:panelGroup>
                  <h:outputText value="Tipo Venda:" styleClass="tituloCampos"/>
                  <rich:spacer width="10"/>
                  <h:selectOneMenu  id="tipoVendaAvulsa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" disabled="#{!VendaAvulsaControle.alterarTipoVenda}"
                                    value="#{VendaAvulsaControle.tipoVenda}" >
                      <f:selectItems  value="#{VendaAvulsaControle.listaTipoVenda}" />
                  </h:selectOneMenu>
                  <rich:spacer width="10"/>
                  <a4j:commandLink id="alterarTipoVenda"
                                   styleClass="pure-button pure-button-primary pure-button-small" value="Alterar Tipo" action="#{VendaAvulsaControle.alterarTipoVenda}" reRender="form:vendaAvulsaContainer" />
              </h:panelGroup>


                <h:selectOneRadio id="tipoComprador" onfocus="focusinput(this);"  onblur="blurinput(this);" styleClass="tituloCampos" value="#{VendaAvulsaControle.vendaAvulsaVO.tipoComprador}">
                    <f:selectItems   value="#{VendaAvulsaControle.listaSelectItemTipoComprador}"/>
                    <a4j:support event="onclick"
                                 oncomplete="document.getElementById('form:nomeComprador').focus();"
                                 action="#{VendaAvulsaControle.vendaAvulsaVO.apresentarComprador}"
                                 reRender="form:panelGroupComprador,mensagem,panelParcelamento, panelBotoesControle"/>
                </h:selectOneRadio>
                <h:panelGroup id="panelGroupComprador">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_VendaAvulsa_nomeComprador}" />
                    <rich:spacer width="10"/>
                    <h:inputText  id="nomeComprador" size="50"
                                  maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" tabindex="1"
                                  value="#{VendaAvulsaControle.vendaAvulsaVO.nomeComprador}"/>

                    <rich:suggestionbox rendered="#{VendaAvulsaControle.vendaAvulsaVO.apresentarCliente}" height="200" width="200"
                                        for="nomeComprador" requestDelay="500"
                                        fetchValue="#{result.pessoa.nome}"
                                        suggestionAction="#{VendaAvulsaControle.executarAutocompleteConsultaCliente}"
                                        minChars="1" rowClasses="20"
                                        status="statusHora"
                                        nothingLabel="Nenhum Cliente encontrado !"
                                        var="result"  id="suggestionNomeComprador" reRender="mensagem">
                        <a4j:support event="oncomplete" status="false"
                                     oncomplete="#{VendaAvulsaControle.onCompleteSuggestionCliente}"/>
                        <a4j:support event="onselect"
                                     oncomplete="#{rich:element('itemVendaAvulsa_produto')}.focus();"
                                     action="#{VendaAvulsaControle.selecionarClienteSuggestionBox}"/>
                        <h:column>
                            <h:outputText value="#{result.pessoa.nome}" />
                        </h:column>
                    </rich:suggestionbox>
                    <rich:suggestionbox rendered="#{VendaAvulsaControle.vendaAvulsaVO.apresentarColaborador}" height="200" width="200"
                                        for="nomeComprador"
                                        fetchValue="#{result.pessoa.nome}"
                                        suggestionAction="#{VendaAvulsaControle.executarAutocompleteConsultaColaborador}"
                                        minChars="1" rowClasses="20"
                                        status="statusHora"
                                        nothingLabel="Nenhum Colaborador encontrado !"
                                        var="result"  id="suggestionNomeColaborador">
                        <a4j:support event="onselect" oncomplete="#{rich:element('itemVendaAvulsa_produtoCodigo')}.focus();"
                                     action="#{VendaAvulsaControle.selecionarColaboradorSuggestionBox}"/>
                        <h:column>
                            <h:outputText value="#{result.pessoa.nome}" />
                        </h:column>
                    </rich:suggestionbox>
                    <rich:spacer width="5" />
                </h:panelGroup>

                <h:panelGrid columns="1"
                             id="panelItemVendaAvulsa"
                             width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ItemVendaAvulsa_tituloForm}"/>
                    </f:facet>
                    <h:panelGrid id="panelItemVenda" columns="6" width="100%" >
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ItemVendaAvulsa_codigoProduto}"/>
                            <h:panelGroup>
                                <h:inputText id="itemVendaAvulsa_produtoCodigo" tabindex="2" size="5" maxlength="10"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{VendaAvulsaControle.itemVendaAvulsaVO.produto.codigo}" onkeypress="bloquearTeclaEnter(event);">
                                    <a4j:support event="onchange" ajaxSingle="true" focus="itemVendaAvulsa_produto"
                                                 action="#{VendaAvulsaControle.consultarCodigoProduto}"
                                                 reRender="form:panelItemVenda, mensagem"/>
                                </h:inputText>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ItemVendaAvulsa_nomeProduto}"/>
                            <h:panelGroup>
                                <h:inputText id="itemVendaAvulsa_produto" tabindex="3" size="30"
                                             autocomplete="new-password"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{VendaAvulsaControle.itemVendaAvulsaVO.produto.descricao}"
                                             onkeydown="bloquearCtrlJ();" onkeypress="bloquearTeclaEnter(event);">
                                </h:inputText>
                                <rich:suggestionbox height="200" width="300"
                                                    for="itemVendaAvulsa_produto"
                                                    suggestionAction="#{VendaAvulsaControle.executarAutocompleteConsultaProduto}"
                                                    minChars="1" rowClasses="20"
                                                    status="statusHora"
                                                    nothingLabel="Nenhum Produto encontrado !" reRender="itemVendaAvulsa_produtoCodigo,
                                                     itemVendaAvulsa_quantidade,itemVendaAvulsa_preco,itemVendaAvulsa_tabelaDesconto"
                                                    var="result" id="suggestionNomeProduto">
                                    <a4j:support event="oncomplete" status="false"
                                                 oncomplete="#{VendaAvulsaControle.onCompleteSuggestionProduto}"/>
                                    <a4j:support event="onselect" reRender="panelGeral,totalLancado,mensagem"
                                                 id="selecionarProdutoSuggestionBox"
                                                 oncomplete="#{rich:element('itemVendaAvulsa_quantidade')}.focus();#{rich:element('itemVendaAvulsa_quantidade')}.select();"
                                                 action="#{VendaAvulsaControle.selecionarProdutoSuggestionBox}"/>
                                    <h:column>
                                        <h:outputText value="#{result.descricao}" title="#{result.observacao}"/>
                                    </h:column>
                                </rich:suggestionbox>
                                <rich:spacer width="5"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" columnClasses="colunaEsquerda">
                            <h:outputText  styleClass="tituloCampos"
                                           value="#{VendaAvulsaControle.itemVendaAvulsaVO.produto.quantidadeExplicacaoUnidadeMedida}"/>
                            <h:inputText size="7" maxlength="7" tabindex="4"
                                         id="itemVendaAvulsa_quantidade"
                                         styleClass="form tooltipster"
                                         onfocus="focusinput(this);"
                                         onkeypress="enterAdicionar(event);"
                                         onkeyup="somenteNumeros(this);"
                                         value="#{VendaAvulsaControle.itemVendaAvulsaVO.quantidade}">
                                <a4j:support event="onblur" status="false" reRender="form:panelItemVenda"/>
                            </h:inputText>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ItemVendaAvulsa_precoProduto}" />
                            <h:panelGroup layout="block" style="display: flex;align-items: flex-end;">
                                <h:outputText id="itemVendaAvulsa_preco"
                                              value="#{VendaAvulsaControle.itemVendaAvulsaVO.produto.valorFinalConvertidoUnidadeMedida_Apresentar}"/>
                                <h:outputText styleClass="tituloCampos"
                                              style="padding-left: 2px"
                                              value="#{VendaAvulsaControle.itemVendaAvulsaVO.produto.valorExplicacaoUnidadeMedida}"/>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ItemVendaAvulsa_descontoProduto}"
                                           rendered="#{VendaAvulsaControle.itemVendaAvulsaVO.pacotePersonal == 0}"/>
                            <h:panelGroup rendered="#{VendaAvulsaControle.itemVendaAvulsaVO.pacotePersonal == 0}">
                                <h:panelGroup rendered="#{!VendaAvulsaControle.itemVendaAvulsaVO.tabelaDesconto.apresentarDescontoValor
                                                          && !VendaAvulsaControle.itemVendaAvulsaVO.tabelaDesconto.apresentarDescontoPorcentagem
                                                          && !VendaAvulsaControle.itemVendaAvulsaVO.apresentarDescontoManual
                                                          && !VendaAvulsaControle.itemVendaAvulsaVO.descontoManual}">
                                    <h:inputText  id="itemVendaAvulsa_tabelaDesconto"
                                                  tabindex="6"
                                                  readonly="true" size="10" maxlength="10"
                                                  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                                  value="#{VendaAvulsaControle.itemVendaAvulsaVO.tabelaDesconto.valor}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{!VendaAvulsaControle.itemVendaAvulsaVO.apresentarDescontoManual
                                                          && VendaAvulsaControle.itemVendaAvulsaVO.descontoManual}">
                                    <h:inputText  readonly="true" size="10"
                                                  maxlength="10" onblur="blurinput(this);"
                                                  onfocus="focusinput(this);" styleClass="form"
                                                  value="#{VendaAvulsaControle.itemVendaAvulsaVO.valorDescontoManual}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{VendaAvulsaControle.itemVendaAvulsaVO.apresentarDescontoManual}">
                                    <h:inputText maxlength="10" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 id="novovalor"
                                                 value="#{VendaAvulsaControle.itemVendaAvulsaVO.valorDescontoManualRec}" size="10">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>

                                    <a4j:commandButton image="./images/tick.png" title="Aplicar Novo Valor"
                                                       id="confirmarvalor"
                                                       action="#{VendaAvulsaControle.editarDescontoProduto}"
                                                       reRender="form:vendaAvulsaContainer"
                                                       oncomplete="#{VendaAvulsaControle.onCompleteDesconto}"/>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{VendaAvulsaControle.itemVendaAvulsaVO.tabelaDesconto.apresentarDescontoValor
                                                          && !VendaAvulsaControle.itemVendaAvulsaVO.apresentarDescontoManual}">
                                    <h:inputText  id="itemVendaAvulsa_tabelaDescontoValor" readonly="true" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{VendaAvulsaControle.itemVendaAvulsaVO.tabelaDesconto.valor}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:inputText>
                                </h:panelGroup>
                                <h:panelGroup rendered="#{VendaAvulsaControle.itemVendaAvulsaVO.tabelaDesconto.apresentarDescontoPorcentagem
                                                          && !VendaAvulsaControle.itemVendaAvulsaVO.apresentarDescontoManual}">
                                    <h:inputText  id="itemVendaAvulsa_tabelaDescontoPercentual"
                                                  readonly="true" size="10"
                                                  maxlength="10" onblur="blurinput(this);"
                                                  onfocus="focusinput(this);" styleClass="form"
                                                  value="#{VendaAvulsaControle.itemVendaAvulsaVO.tabelaDesconto.valor}">
                                        <f:converter converterId="FormatadorNumerico7Casa" />
                                    </h:inputText>
                                </h:panelGroup>
                                <a4j:commandButton rendered="#{!VendaAvulsaControle.itemVendaAvulsaVO.apresentarDescontoManual}" id="btnChave" image="./images/icon_chave.png" title="Informar Valor do Desconto"
                                                   action="#{VendaAvulsaControle.validarModalAbrir}"
                                                   oncomplete="#{VendaAvulsaControle.mensagemNotificar}"
                                                   reRender="panelAutorizacaoFuncionalidade"/>
                                <rich:spacer width="5" />
                                <a4j:commandButton rendered="#{!VendaAvulsaControle.itemVendaAvulsaVO.apresentarDescontoManual}" id="btnConsultarDesconto" oncomplete="Richfaces.showModalPanel('panelDesconto')" reRender="formDesconto" image="imagens/informacao.gif" alt="Consultar Desconto" action="#{VendaAvulsaControle.consultarTabelaDesconto}"/>
                                <rich:spacer width="5" />
                                <a4j:commandButton rendered="#{!VendaAvulsaControle.itemVendaAvulsaVO.apresentarDescontoManual}" id="limparTabelaDesconto" immediate="true" action="#{VendaAvulsaControle.limparCampoTabelaDesconto}" image="images/limpar.gif" alt="Limpar Desconto" reRender="panelItemVenda,produtoPacial"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_ItemVendaAvulsa_valorParcial}" />
                            <h:outputText id="produtoPacial"
                                          value="#{VendaAvulsaControle.itemVendaAvulsaVO.valorParcialApresentar}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="2" width="100%" columnClasses="w50 colunaDireita,w50 colunaEsquerda">


                        <a4j:commandLink id="botaoAdicionar"
                                           styleClass="pure-button pure-button-primary pure-button-small btnAdicionar"
                                           action="#{VendaAvulsaControle.adicionarItemVendaAvulsa}"
                                           reRender="form:panelGeral, form:totalLancado, form:mensagem"
                                           value="#{msg_bt.btn_adicionar}"
                                           oncomplete="#{rich:element('itemVendaAvulsa_produto')}.focus();"/>

                        <a4j:commandLink id="botaoAdicionarPacote" styleClass="pure-button pure-button-small"
                                           value="Adicionar Pacote"
                                           action="#{pacoteControle.acaoEntrar}"
                                           rendered="#{!VendaAvulsaControle.creditoPersonal && LoginControle.apresentarLinkEstudio}"/>
                        <h:outputText value="" rendered="#{!VendaAvulsaControle.creditoPersonal && !LoginControle.apresentarLinkEstudio}"/>
                        <a4j:commandLink id="botaoAdicionarPacotePersonal"
                                           styleClass="pure-button pure-button-small"
                                           value="Pacote Crédito Personal"
                                           rendered="#{VendaAvulsaControle.creditoPersonal}"
                                           reRender="formPacotesPersonal"
                                           oncomplete="Richfaces.showModalPanel('panelPacotesCreditoPersonal');"/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <h:dataTable id="itemVendaAvulsaVO" width="100%" headerClass="subordinado"
                                 rowClasses="linhaImpar, linhaPar" rows="10"
                                 columnClasses="colunaAlinhamento,colunaAlinhamento,colunaAlinhamento,colunaAlinhamento,colunaAlinhamento,colunaAlinhamento,colunaAlinhamento"
                                 value="#{VendaAvulsaControle.vendaAvulsaVO.itemVendaAvulsaVOs}" var="itemVendaAvulsa">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_ItemVendaAvulsa_nomeProduto}" />
                            </f:facet>
                            <h:outputText  value="#{itemVendaAvulsa.produto.descricao}" title="#{itemVendaAvulsa.produto.observacao}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_ItemVendaAvulsa_quantidade}" />
                            </f:facet>
                            <h:outputText value="#{itemVendaAvulsa.quantidade}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_ItemVendaAvulsa_precoProduto}" />
                            </f:facet>
                            <h:outputText value="#{itemVendaAvulsa.produto.valorFinalConvertidoUnidadeMedida}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText style="padding-left: 2px"
                                          value="#{itemVendaAvulsa.produto.valorExplicacaoUnidadeMedida}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_ItemVendaAvulsa_valorParcial}" />
                            </f:facet>
                            <h:outputText  value="#{itemVendaAvulsa.valorParcial}" >
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_ItemVendaAvulsa_tabelaDesconto}" />
                            </f:facet>
                            <h:outputText  value="#{itemVendaAvulsa.tabelaDesconto.valor}" >
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_ItemVendaAvulsa_DescontoManual}" />
                            </f:facet>
                            <h:outputText  value="#{itemVendaAvulsa.valorDescontoManual}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:column>
                        <h:column rendered="#{LoginControle.apresentarLinkEstudio}">
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_ItemVendaAvulsa_pacote}" />
                            </f:facet>
                            <h:outputText  value="#{itemVendaAvulsa.pacoteVO.titulo}">
                            </h:outputText>
                        </h:column>
                        <h:column rendered="#{LoginControle.empresaLogado.trabalharComPontuacao}">
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.PONTOS}" />
                            </f:facet>
                            <h:outputText  value="#{itemVendaAvulsa.quantidade > 1 ? (itemVendaAvulsa.produto.pontos * itemVendaAvulsa.quantidade) : itemVendaAvulsa.produto.pontos}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandButton id="editarItemVenda" reRender="form:panelGeral, form:totalLancado,mensagem" action="#{VendaAvulsaControle.editarItemVendaAvulsa}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="removerItemVenda" reRender="form:panelGeral, form:totalLancado, mensagem" action="#{VendaAvulsaControle.removerItemVendaAvulsa}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                            </h:panelGroup>
                        </h:column>
                    </h:dataTable>
                    <rich:datascroller align="center" for="form:itemVendaAvulsaVO" maxPages="10"
                                       rendered="#{VendaAvulsaControle.vendaAvulsaVO.itemVendaAvulsaVOsSize > 10}"
                                       id="scResultadoItemVenda"/>
                </h:panelGrid>

                    <h:panelGroup id="panelParcelamento">
                        <h:panelGrid rendered="#{VendaAvulsaControle.vendaAvulsaVO.apresentarParcelamento}" columns="1"
                                     width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_VendaAvulsa_parcelamento}"/>
                            </f:facet>
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <h:outputText value="Vezes"/>
                                <rich:inputNumberSpinner minValue="1" maxValue="72"
                                                         value="#{VendaAvulsaControle.vendaAvulsaVO.nrVezesParcelamento}">
                                    <a4j:support event="onchange" reRender="panelBotoesControle"/>
                                </rich:inputNumberSpinner>

                                <h:outputText value="Data primeira parcela"/>
                                <rich:calendar inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2" showWeeksBar="false"
                                               value="#{VendaAvulsaControle.vendaAvulsaVO.vencimentoPrimeiraParcela}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGroup>

            </h:panelGrid>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2" >
                <tr>
                    <td align="left" valign="top"><div class="sep" style="margin:2px 0;"><img src="images/shim.gif"></div></td>
                </tr>
                <c:if test="${VendaAvulsaControle.existeItemVendaSessao}">
                <tr>
                    <td align="left" valign="top">
                    <h:panelGrid columns="4">
                        <h:outputText styleClass="titulo2" style="text-decoration: none;" value="Período Agendar Pacote:"/>
                        <rich:calendar inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2" showWeeksBar="false"
                                       value="#{VendaAvulsaControle.dataIniAgendar}"/>
                        <h:outputText styleClass="textsmall" value="até"/>
                        <rich:calendar inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2" showWeeksBar="false"
                                       value="#{VendaAvulsaControle.dataFimAgendar}"/>
                    </h:panelGrid>
                    </td>
                </tr>
                </c:if>
                <tr>
                    <td align="left" valign="top">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreviewtotal">

                            <tr>
                                <td width="50%" align="left" valign="middle">Total Lan&ccedil;ado = <span class="verde"><h:outputText value="#{VendaAvulsaControle.vendaAvulsaVO.empresa.moeda} "/><h:outputText id="totalLancado" value="#{VendaAvulsaControle.vendaAvulsaVO.valorTotal}"><f:converter converterId="FormatadorNumerico"/></h:outputText></span></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top"><div class="sep" style="margin:2px 0;"><img src="images/shim.gif"></div></td>
                </tr>

            </table>
            <h:panelGrid id="mensagem" columns="2" width="100%" styleClass="tabMensagens" style="margin:0 0 5px 0;">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText id="msgVendaAvulsa" styleClass="mensagem"  value="#{VendaAvulsaControle.mensagem}"/>
                    <h:outputText id="msgVendaAvulsaDet" styleClass="mensagemDetalhada" value="#{VendaAvulsaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
            <!-- inicio botões -->
            <h:panelGrid id="panelBotoesControle"  style="margin-right:0px" columns="1">
                <h:panelGrid   style="margin-right:0px" columns="4">

                    <a4j:commandLink id="botaoConfirmarcao" action="#{VendaAvulsaControle.validarDadosVendaAvulsaConfirmar}"
                                     reRender="form:panelGeral, form:totalLancado,mensagem, panelAutorizacaoFuncionalidade, form:botaoConfirmarcao"
                                     oncomplete="#{VendaAvulsaControle.mensagemNotificar}"
                                     onclick="#{VendaAvulsaControle.onclickConfirmar}"
                                     rendered="#{!LoginControle.apresentarLinkEstudio && !VendaAvulsaControle.processandoOperacao}"
                                     styleClass="pure-button pure-button-primary">
                        <i class="fa-icon-plus" ></i>&nbsp;Confirmar
                    </a4j:commandLink>

                    <!-- UM BOTÃO PARA EMPRESAS USAM O GESTÃO DE ESTUDIO, PARA QUE POSSA SER VALIDADO O BV DO ALUNO -->
                    <a4j:commandLink id="botaoConfirmarcaoEstudio" styleClass="pure-button pure-button-primary"
                                     action="#{VendaAvulsaControle.validarDadosVendaAvulsaEstudio}"
                                     reRender="form:panelGeral, form:totalLancado,mensagem, panelAutorizacaoFuncionalidade, form:botaoConfirmarcaoEstudio"
                                     oncomplete="#{VendaAvulsaControle.mensagemNotificar}"
                                     rendered="#{LoginControle.apresentarLinkEstudio && !VendaAvulsaControle.processandoOperacao}">
                        <i class="fa-icon-plus" ></i>&nbsp;Confirmar
                    </a4j:commandLink>


                    <a4j:commandLink id="botaoPagar" rendered="#{!LoginControle.apresentarLinkEstudio && VendaAvulsaControle.vendaAvulsaVO.nrVezesParcelamento == 1 && !VendaAvulsaControle.processandoOperacao}"
                                     action="#{VendaAvulsaControle.validarDadosVendaAvulsaReceber}"
                                     styleClass="pure-button"
                                     reRender="form:panelGeral, form:totalLancado,mensagem, panelAutorizacaoFuncionalidade, form:botaoPagar"
                                     oncomplete="#{VendaAvulsaControle.mensagemNotificar}"
                                     onclick="#{VendaAvulsaControle.onclickConfirmar}">
                        <i class="fa-icon-money" ></i>&nbsp;Receber
                    </a4j:commandLink>


                    <a4j:commandLink id="botaoPagarEstudio" rendered="#{LoginControle.apresentarLinkEstudio && VendaAvulsaControle.vendaAvulsaVO.nrVezesParcelamento == 1 && !VendaAvulsaControle.processandoOperacao}"
                                     styleClass="pure-button"
                                     action="#{VendaAvulsaControle.validarDadosVendaAvulsaEstudioPagar}"
                                     reRender="form:panelGeral, form:totalLancado,mensagem, panelAutorizacaoFuncionalidade, form:botaoPagarEstudio"
                                     oncomplete="#{VendaAvulsaControle.mensagemNotificar}"
                                     onclick="#{VendaAvulsaControle.onclickConfirmar}">
                        <i class="fa-icon-money" ></i>&nbsp;Receber
                    </a4j:commandLink>

                    <a href="tela1.jsp" class="pure-button">
                        <i class="fa-icon-remove" ></i>&nbsp;Cancelar
                    </a>

                    <rich:hotKey rendered="#{!LoginControle.apresentarLinkEstudio && VendaAvulsaControle.vendaAvulsaVO.nrVezesParcelamento == 1}" key="ctrl+r"
                                 handler="#{rich:element('botaoPagar')}.onclick();return false;"/>
                    <rich:hotKey rendered="#{LoginControle.apresentarLinkEstudio && VendaAvulsaControle.vendaAvulsaVO.nrVezesParcelamento == 1}" key="ctrl+r"
                                 handler="#{rich:element('botaoPagarEstudio')}.onclick();return false;"/>
                </h:panelGrid>
            </h:panelGrid>
    </h:panelGroup>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />

    <a4j:commandLink id="cadastrarClienteVendaAvulsa"
                     styleClass="cadastrarClienteVendaAvulsa"
                     action="#{VendaAvulsaControle.limparSuggestionCliente}"
                    reRender="formModalCadastrarClienteVendaAvulsa"
                    oncomplete="Richfaces.showModalPanel('modalCadastrarClienteVendaAvulsa');">
    </a4j:commandLink>

    <a4j:commandLink id="atualizarProdutoTela"
                     status="false"
                     oncomplete="focusQuantidade()"
                     reRender="form:panelItemVendaAvulsa">
    </a4j:commandLink>
</h:panelGroup>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/crm.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="script/scriptSMS.js"></script>
<style type="text/css">

    .rich-table-thead {
        background: none !important;
        border: none !important;
    }

    .rich-table-subheader {
        background: none !important;
        border: none !important;
    }

    .rich-table-subheadercell {
        background: none !important;
        border: none !important;
        text-align: left !important;
        padding-left: 7px !important;
    }

    .rich-tabpanel-content {
        background: none !important;
        border: none !important;
        padding-top: 7px !important;
        padding-left: 0px !important;
        padding-right: 0px !important;
        text-align: left !important;
    }

    .rich-table {
        background: none !important;
        border: none !important;
        text-align: left !important;
    }

    .rich-table-cell {
        border: none !important;
        text-align: left !important;
    }

    .rich-table-row {
        padding-top: 10px !important;
    }

    .rich-tab-active {
        background: none !important;
        border-color: #C0C0C0;
        font-weight: bold;
        padding: 5px;
        border-bottom-color: white;
        /*border-bottom-width: 1px;*/
    }

    .rich-tab-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tab-header {
        font-size: 14px;
        font-family: arial, helvetica, sans-serif;
    }

    .rich-tabhdr-cell-disabled {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-cell-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-cell {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-border {
        background: none !important;
        border: none !important;
    }

    .rich-tab-bottom-line {
        border-width: 1px;
        border-color: #C0C0C0;
    }

    .btnsOptAPP {
        margin-left: 10px;
        float: left;
        display: none;
    }

    .textareaResize {
        width: 100%;
        resize: none;
        box-shadow: none !important;
        border: none !important;
        background: transparent !important;
        margin: 0px !important;
        padding: 0px !important;
        font-size: 10pt !important;
    }
</style>

<script type="text/javascript">
    function mostrarProximo(item) {
        jQuery('.labelOp' + (item + 1)).show();
        jQuery('.btnOp' + item).hide();
        jQuery('.btnOp' + (item + 1)).show();
    }
    function sumirItem(item) {
        jQuery('.labelOp' + item).hide();
        jQuery('.form.labelOp' + item).val('');
        jQuery('.btnOp' + (item - 1)).show();
        jQuery('.btnOp' + item).hide();
    }
</script>


<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>


<h:panelGroup id="panelGridRight" layout="block"
              style="width: 100%; height: 100%; max-height: 800px; overflow-y: auto;">

    <h:panelGroup style="padding: 20px; background: #e6e6e6;" layout="block"
                  rendered="#{not empty MetaCRMControle.mensagemRealizarContato}">
        <h:outputText
                style="color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                value="#{MetaCRMControle.mensagemRealizarContato}"/>
    </h:panelGroup>


    <%--&lt;%&ndash;MENSAGEM DETALHADA&ndash;%&gt;--%>
    <%--<h:panelGrid columns="1" width="100%" rendered="#{not empty MetaCRMControle.mensagemDetalhada}">--%>
    <%--<h:outputText id="msgRealizarContatoSuperior" styleClass="mensagem"--%>
    <%--value="#{MetaCRMControle.mensagem}"/>--%>
    <%--<h:outputText id="msgRealizarContatoDetSuperior" styleClass="mensagemDetalhada"--%>
    <%--value="#{MetaCRMControle.mensagemDetalhada}"/>--%>
    <%--</h:panelGrid>--%>

    <%---------------------------------------- PASSIVO (RECEPTIVO) ----------------------------------------------------------%>
    <h:panelGrid id="panelCadastroPassivo" columns="1" width="100%" style="min-height: 600px; background: #e6e6e6;"
                 rendered="#{MetaCRMControle.metaPassivo && !MetaCRMControle.mostrarPanelRealizarContato}">

        <h:panelGroup style="text-align: center; padding-top: 20px; padding-bottom: 10px;"
                      layout="block">
            <h:outputText
                    style="font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                    value="Receptivo"/>
        </h:panelGroup>
        <h:panelGrid columns="1" width="100%">
            <h:panelGroup style="padding-bottom: 10px; padding-left: 15px; padding-right: 15px" layout="block">
                <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                              value="Objetivo"/>
                <h:outputText style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                              value="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.objetivo}"/>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 10px;">
            <h:inputText id="nomePassivo" maxlength="50"
                         onkeypress="tabenter('form:telCelularPassivo')"
                         style="background: #F6F6F6; width: 58%"
                         value="#{MetaCRMControle.passivoVO.nome}"/>
        </h:panelGroup>
        <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">

                <h:inputText id="telCelularPassivo" maxlength="13"
                             style="background: #F6F6F6; width: 28%"
                             onblur="blurinput(this);"
                             onkeypress="mascara(this.form, this.id , '(99)999999999', event); tabenter('form:telResidencialPassivo'); return event.charCode >= 48 && event.charCode <= 57"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.passivoVO.telefoneCelular}"/>
            </h:panelGroup>
            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <h:inputText id="telResidencialPassivo" maxlength="13"
                             style="background: #F6F6F6; width: 28%"
                             onblur="blurinput(this);"
                             onkeypress="mascara(this.form, this.id , '(99)999999999', event); tabenter('form:telTrabalhoPassivo');"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.passivoVO.telefoneResidencial}"/>
            </h:panelGroup>
            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <h:inputText id="telTrabalhoPassivo" maxlength="13"
                             style="background: #F6F6F6; width: 28%"
                             onblur="blurinput(this);"
                             onkeypress="mascara(this.form, this.id , '(99)999999999', event);tabenter('form:emailPassivo');"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.passivoVO.telefoneTrabalho}"/>
            </h:panelGroup>
        </c:if>
        <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">

                <h:inputText id="telCelularPassivo" maxlength="14"
                             style="background: #F6F6F6; width: 28%"
                             onblur="blurinput(this);"
                             onkeypress="tabenter('form:telResidencialPassivo')"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.passivoVO.telefoneCelular}"/>
            </h:panelGroup>
            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <h:inputText id="telResidencialPassivo" maxlength="14"
                             style="background: #F6F6F6; width: 28%"
                             onblur="blurinput(this);"
                             onkeypress="tabenter('form:telTrabalhoPassivo');"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.passivoVO.telefoneResidencial}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <h:inputText id="telTrabalhoPassivo" maxlength="13"
                             style="background: #F6F6F6; width: 28%"
                             onblur="blurinput(this);"
                             onkeypress="tabenter('form:emailPassivo');"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.passivoVO.telefoneTrabalho}"/>
            </h:panelGroup>
        </c:if>
        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
            <h:inputText id="emailPassivo" maxlength="50"
                         onkeypress="tabenter('form:eventoPassivo')"
                         style="background: #F6F6F6; width: 58%"
                         value="#{MetaCRMControle.passivoVO.email}"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
            <h:inputText id="eventoPassivo"
                         onkeypress="tabenter('form:obsPassivo')"
                         onblur="blurinput(this);" onfocus="focusinput(this);"
                         style="background: #F6F6F6; width: 60%"
                         value="#{MetaCRMControle.passivoVO.evento.descricao}"/>
            <rich:suggestionbox
                    id="suggestionEventoPassivo"
                    style="height: 5%; width: 26%"
                    for="eventoPassivo"
                    fetchValue="#{eventos.descricao}"
                    suggestionAction="#{MetaCRMControle.executarAutocompleteConsultaEvento}"
                    minChars="1" rowClasses="5"
                    status="statusHora"
                    nothingLabel="Nenhum Evento encontrado !"
                    var="eventos"
                    reRender="mensagem">
                <a4j:support event="onselect"
                             action="#{MetaCRMControle.selecionarEventoPassivo}"/>
                <h:column>
                    <h:outputText value="#{eventos.descricao}"/>
                </h:column>
            </rich:suggestionbox>
        </h:panelGroup>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-right: 20px; padding-bottom: 10px">

            <h:inputTextarea id="obsPassivo"
                             style="width: 100%" rows="3"
                             value="#{MetaCRMControle.historicoContatoVO.observacao}"/>
        </h:panelGroup>

        <h:panelGrid columns="3" width="100%">
            <h:panelGroup style="padding-top: 0px; padding-right: 20px; padding-bottom: 5px;  float: right"
                          layout="block">

                <a4j:commandLink id="cancelarPassivo" styleClass="pure-button"
                                 value="Novo"
                                 action="#{MetaCRMControle.novoPassivo}"
                                 reRender="panelGridRight"
                                 oncomplete="adicionarPlaceHolderCRM();"/>

                <rich:spacer width="13px" rendered="#{MetaCRMControle.somenteEditarPassivo}"/>
                <a4j:commandLink id="gravarAlteracoesPassivo"
                                 rendered="#{MetaCRMControle.somenteEditarPassivo}"
                                 styleClass="pure-button pure-button-primary"
                                 value="Gravar"
                                 action="#{MetaCRMControle.gravarAlteracoesPassivo}"
                                 reRender="panelGridRight"
                                 oncomplete="#{MetaCRMControle.msgAlert};adicionarPlaceHolderCRM();"/>

                <rich:spacer width="13px" rendered="#{MetaCRMControle.somenteEditarPassivo}"/>
                <a4j:commandLink id="excluirPassivo"
                                 rendered="#{MetaCRMControle.somenteEditarPassivo}"
                                 value="Excluir"
                                 action="#{MetaCRMControle.excluirPassivo}"
                                 styleClass="pure-button"
                                 reRender="panelGridLeft, panelGridCenter, panelGridRight"
                                 oncomplete="#{MetaCRMControle.msgAlert};adicionarPlaceHolderCRM();"/>

                <rich:spacer width="13px" rendered="#{!MetaCRMControle.somenteEditarPassivo}"/>
                <a4j:commandLink id="apresentarObjecaoPassivo"
                                 rendered="#{!MetaCRMControle.somenteEditarPassivo}"
                                 styleClass="pure-button"
                                 value="Objeção"
                                 action="#{MetaCRMControle.apresentarObjecaoPassivo}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica};adicionarPlaceHolderCRM();"
                                 reRender="mdlMensagemGenerica, panelGridRight"/>

                <rich:spacer width="13px" rendered="#{!MetaCRMControle.somenteEditarPassivo}"/>
                <a4j:commandLink id="apresentarAgendarPassivo"
                                 rendered="#{!MetaCRMControle.somenteEditarPassivo}"
                                 styleClass="pure-button pure-button-primary"
                                 value="Agendar"
                                 action="#{MetaCRMControle.apresentarAgendaPassivo}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica};adicionarPlaceHolderCRM();"
                                 reRender="mdlMensagemGenerica, panelGridRight"/>
            </h:panelGroup>
        </h:panelGrid>


        <%--OBJEÇÃO PASSIVO (RECEPTIVO)--%>
        <h:panelGroup rendered="#{MetaCRMControle.mostrarPanelObjecaoPassivo}"
                      style="float: left; width: 100%; background: white">
            <h:panelGroup id="panelObjecaoPassivoSeta" layout="block" styleClass="triangleCSS"
                          style="float: left; margin-left: 74%; background: white"/>
        </h:panelGroup>
        <h:panelGrid id="panelObjecaoPassivo" columns="1" width="100%" style="text-align: center; background: #e6e6e6"
                     rendered="#{MetaCRMControle.mostrarPanelObjecaoPassivo}">

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Objeção"/>
                </h:panelGroup>
                <h:panelGroup
                        style="padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:selectOneMenu id="objecaoPassivo" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; font-size: 14px;"
                                     value="#{MetaCRMControle.historicoContatoVO.objecaoVO.codigo}">
                        <f:selectItems value="#{MetaCRMControle.listaObjecao}"/>
                        <a4j:support event="onchange" action="#{MetaCRMControle.alterarObjecao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                <h:panelGroup style="padding: 5px" layout="block">
                    <a4j:commandLink id="gravarObjecaoPassivo"
                                     styleClass="pure-button pure-button-primary"
                                     value="Concluir"
                                     action="#{MetaCRMControle.gravarObjecaoPassivo}"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica};ajustarScrollAlunoSelecionadoDireita()"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

        <%--AGENDAR PASSIVO (RECEPTIVO)--%>
        <h:panelGroup rendered="#{MetaCRMControle.mostrarPanelAgendarPassivo}"
                      style="float: left; width: 100%; background: white">
            <h:panelGroup id="panelAgendarPassivoSeta" layout="block" styleClass="triangleCSS"
                          style="float: left; margin-left: 90%; background: white"/>
        </h:panelGroup>
        <h:panelGrid id="panelAgendarPassivo" columns="1" width="100%" style="text-align: center; background: #e6e6e6"
                     rendered="#{MetaCRMControle.mostrarPanelAgendarPassivo}">

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="float: left; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Tipo de Agendamento"/>
                    <rich:spacer width="10px" style="float: left"/>
                    <h:selectOneRadio id="agendamentoPassivo" onblur="blurinput(this);" onfocus="focusinput(this);"
                                      style="float: left; background: #e6e6e6; color: #333333; font-size: 14px;"
                                      value="#{MetaCRMControle.agendaVO.tipoAgendamento}">
                        <f:selectItems value="#{MetaCRMControle.listaSelectItemTipoAgendamentoAgenda}"/>
                        <a4j:support event="onclick" reRender="panelAgendarPassivo"
                                     oncomplete="adicionarPlaceHolderCRM();"/>
                    </h:selectOneRadio>
                </h:panelGroup>
            </h:panelGrid>

            <rich:spacer width="10px" style="float: left"/>
            <h:panelGrid id="selecionarConsultorPassivo" columns="2" width="100%" style="background: #e6e6e6;" rendered="#{LoginControle.permissaoAcessoMenuVO.selecionarColaboradorMetas}"
                         styleClass="tabFormSubordinada">
                <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px" layout="block">
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold" value="Colaboradores"/>

                    <rich:spacer width="10px"/>

                    <h:inputText  id="nomeConsultorPassivo"
                                  style="width: 320px; font-size: 12px !important;"
                                  styleClass="campos"
                                  size="50"
                                  maxlength="50"
                                  onblur="blurinput(this);"
                                  onfocus="toggleSuggestions ( #{rich:component('suggestionConsultorPassivo')} )"
                                  value="#{MetaCRMControle.nomeUsuario}"/>

                    <rich:suggestionbox for="nomeConsultorPassivo"
                                        width="320"
                                        fetchValue="#{result.nome}"
                                        status="statusInComponent"
                                        style="background: #F6F6F6; color: #333333; padding: 3px;"
                                        suggestionAction="#{MetaCRMControle.executarAutocompleteCol}"
                                        minChars="1"
                                        rowClasses="linhaImpar, linhaPar"
                                        ajaxSingle="false"
                                        var="result"
                                        id="suggestionConsultorPassivo">
                        <a4j:support event="onselect"
                                     focus="telasCRM"
                                     oncomplete="#{MetaCRMControle.mensagemNotificar}"
                                     action="#{MetaCRMControle.selecionarUsuario}"/>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText value="#{result.nome}"/>
                        </h:column>
                    </rich:suggestionbox>
                    <script>
                        function toggleSuggestions(suggestionBox) {
                            if (suggestionBox.active)
                                suggestionBox.hide();
                            else
                                suggestionBox.callSuggestion(true);
                        }
                    </script>
                </h:panelGroup>
            </h:panelGrid>


            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.apresentarInputTextAulaExperimental}">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Modalidade"/>
                    <rich:spacer width="10px"/>

                    <h:selectOneMenu id="modalidadeAgendaPassivo" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; padding: 3px;"
                                     value="#{MetaCRMControle.agendaVO.modalidade.codigo}">
                        <f:selectItems value="#{MetaCRMControle.listaModalidade}"/>
                        <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModalidade}"/>
                    </h:selectOneMenu>

                    <%--<h:inputText id="textModalidadePassivo" size="50"--%>
                    <%--onblur="blurinput(this);"--%>
                    <%--onfocus="focusinput(this);"--%>
                    <%--styleClass="form"--%>
                    <%--value="#{MetaCRMControle.agendaVO.modalidade.nome}"/>--%>

                    <%--<rich:suggestionbox id="suggestionModalidadePassivo"--%>
                    <%--height="200" width="300"--%>
                    <%--for="textModalidadePassivo"--%>
                    <%--status="statusInComponent"--%>
                    <%--immediate="true"--%>
                    <%--suggestionAction="#{MetaCRMControle.autocompleteModalidade}"--%>
                    <%--nothingLabel="Nenhuma Modalidade encontrada !" var="modalidade">--%>
                    <%--<a4j:support event="onselect"--%>
                    <%--action="#{MetaCRMControle.selecionarModalidade}"/>--%>
                    <%--<h:column>--%>
                    <%--<h:outputText value="#{modalidade.nome}"/>--%>
                    <%--</h:column>--%>
                    <%--</rich:suggestionbox>--%>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                                MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}">
                <h:panelGroup id="selTipoColaboradorRecep" layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                  value="Tipo Professor"/>
                    <rich:spacer width="10px"/>
                    <h:selectOneMenu id="selTipoColabRecep" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; padding: 3px;"
                                     value="#{MetaCRMControle.tipoProfessor}">
                        <f:selectItems value="#{MetaCRMControle.listaTiposProfessores}"/>
                        <a4j:support event="onchange" actionListener="#{MetaCRMControle.alterarTipoColaborador}"
                                     reRender="selColaboradorRecep, infoColaboradorRecep, selTipoColaboradorRecep">
                        </a4j:support>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                                MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}">
                <h:panelGroup id="infoColaboradorRecep">
                    <h:panelGroup id="selColaboradorRecep" rendered="#{MetaCRMControle.mostrarSeletorNome}"
                                  layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                        <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                      value="Professor"/>
                        <rich:spacer width="10px"/>
                        <h:selectOneMenu id="selColabRecep" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="background: #F6F6F6; color: #333333; padding: 3px;"
                                         value="#{MetaCRMControle.codigoProfessor}">
                            <f:selectItems value="#{MetaCRMControle.listaColaboradores}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Agendar para"/>
                    <rich:spacer width="10px"/>
                    <rich:calendar id="dataAgendamentoPassivo"
                                   value="#{MetaCRMControle.agendaVO.dataAgendamento}"
                                   inputSize="10"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <rich:spacer style="width: 10px"/>
                    <h:inputText id="horaMinutoAgendamentoPassivo"
                                 onkeypress="return dois_pontos(this);"
                                 size="10" maxlength="5" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" value="#{MetaCRMControle.agendaVO.horaMinuto}"/>
                    <rich:spacer style="width: 10px"/>
                    <a4j:commandLink
                            id="buscarAulasRecep"
                            rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                                MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}"
                            style="line-height: 20px"
                            styleClass="botoes nvoBt btSec"
                            value="Buscar aulas"
                            action="#{MetaCRMControle.montarAulasAgenda}"
                            oncomplete="#{MetaCRMControle.mensagemNotificar}"
                            reRender="mensagens, panelAgendarPassivo"/>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                             MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}">
                <h:panelGroup id="infoAulasRecep">
                    <h:panelGroup id="selAulasRecep" rendered="#{MetaCRMControle.listaAulasAgenda.size() > 1}"
                                  layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                        <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                      value="Aula"/>
                        <rich:spacer width="10px"/>
                        <h:selectOneMenu id="selAulaRecep" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="background: #F6F6F6; color: #333333; padding: 3px;"
                                         value="#{MetaCRMControle.codigoAula}">
                            <f:selectItems value="#{MetaCRMControle.listaAulasAgenda}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                <h:panelGroup style="padding: 5px" layout="block">
                    <a4j:commandLink id="concluirAgendaPassivo"
                                     styleClass="pure-button pure-button-primary"
                                     value="Concluir"
                                     action="#{MetaCRMControle.gravarAgendaPassivo}"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica};ajustarScrollAlunoSelecionadoDireita()"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight, nomeConsultorPassivo"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

    </h:panelGrid>


    <%---------------------------------------- META INDICAÇÃO ----------------------------------------------------------%>
    <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block"
                  rendered="#{(!MetaCRMControle.metaCorreta && ((MetaCRMControle.metaIndicacao && !MetaCRMControle.mostrarPanelRealizarContato) || MetaCRMControle.metaIndicacaoSemContato)) && !MetaCRMControle.mostrarPanelEmailSMSColetivo}">
        <h:outputText
                style="text-align: center; color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                value="A meta a ser realizada deve ser #{MetaCRMControle.metaAtual.fasesCRMEnum.descricao}"/>
    </h:panelGroup>

    <h:panelGrid id="panelFazerIndicaoMeta" columns="1" width="100%" style="background: #e6e6e6; min-height: 600px"
                 rendered="#{(MetaCRMControle.metaCorreta && ((MetaCRMControle.metaIndicacao && !MetaCRMControle.mostrarPanelRealizarContato) || MetaCRMControle.metaIndicacaoSemContato)) && !MetaCRMControle.mostrarPanelEmailSMSColetivo}">

        <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block">
            <h:outputText
                    style="font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                    value="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.descricao}"/>
        </h:panelGroup>
        <h:panelGrid columns="1" width="100%">
            <h:panelGroup style="padding-bottom: 10px; padding-left: 15px; padding-right: 15px" layout="block">
                <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                              value="Objetivo"/>
                <h:outputText style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                              value="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.objetivo}"/>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 10px">
            <h:inputText id="nomeIndicadoMeta" maxlength="50"
                         style="background: #F6F6F6; width: 58%"
                         value="#{MetaCRMControle.indicadoVO.nomeIndicado}"
                         onkeypress="tabenter('form:clienteOUColaboradorIndicou')"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 10px" >
            <h:inputText id="cpfindicado" maxlength="16"
                         style="background: #F6F6F6; width: 28%"
                         onchange="return validar_Telefone(this.id);"
                         onblur="blurinput(this);"
                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                         onfocus="focusinput(this);"
                         value="#{MetaCRMControle.indicadoVO.cpf}"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
            <h:inputText id="clienteOUColaboradorIndicou"
                         style="background: #F6F6F6; width: 58%"
                         value="#{MetaCRMControle.indicacaoVO.pessoaIndicouVO.nome}"/>
            <rich:suggestionbox
                    id="suggestionClienteColaboradorIndicou"
                    height="200" width="400"
                    for="clienteOUColaboradorIndicou"
                    fetchValue="#{clienteColaborador}"
                    suggestionAction="#{MetaCRMControle.executarAutocompleteConsultaPessoaQueIndicouIndicao}"
                    minChars="1"
                    status="false"
                    rowClasses="20"
                    nothingLabel="Nenhuma Pessoa encontrada !"
                    var="clienteColaborador">
                <a4j:support event="onselect"
                             action="#{MetaCRMControle.selecionarPessoaSuggestionBoxIndicacao}"/>

                <h:column>
                    <f:facet name="header">
                        <h:outputText styleClass="textverysmall" value="Nome"/>
                    </f:facet>
                    <h:outputText value="#{clienteColaborador.nome}"/>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText styleClass="textverysmall" value="Tipo"/>
                    </f:facet>
                    <h:outputText value="#{clienteColaborador.tipoPessoa}"/>
                </h:column>
            </rich:suggestionbox>
        </h:panelGroup>

        <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <h:inputText id="telIndicado01Meta" maxlength="13"
                             style="background: #F6F6F6; width: 28%"
                             onchange="return validar_Telefone(this.id);"
                             onblur="blurinput(this);"
                             onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.indicadoVO.telefoneIndicado}"/>
                <rich:spacer width="13px"/>
                <h:inputText id="telIndicado02Meta" maxlength="13"
                             style="background: #F6F6F6; width: 28%"
                             onchange="return validar_Telefone(this.id);"
                             onblur="blurinput(this);"
                             onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.indicadoVO.telefone}"/>
            </h:panelGroup>
        </c:if>

        <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <h:inputText id="telIndicado01Meta" maxlength="13"
                             style="background: #F6F6F6; width: 28%"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.indicadoVO.telefoneIndicado}"/>
                <rich:spacer width="13px"/>
                <h:inputText id="telIndicado02Meta" maxlength="13"
                             style="background: #F6F6F6; width: 28%"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.indicadoVO.telefone}"/>
            </h:panelGroup>
        </c:if>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
            <h:inputText id="emailIndicadoMeta" maxlength="50"
                         onkeypress="tabenter('form:eventoIndicadoMeta')"
                         style="background: #F6F6F6; width: 58%"
                         value="#{MetaCRMControle.indicadoVO.email}"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
            <h:inputText id="eventoIndicadoMeta"
                         onkeypress="tabenter('form:obsIndicadoMeta')"
                         onblur="blurinput(this);" onfocus="focusinput(this);"
                         style="background: #F6F6F6; width: 58%"
                         value="#{MetaCRMControle.indicacaoVO.evento.descricao}"/>
            <rich:suggestionbox
                    id="suggestionEventoIndicacaoMeta"
                    style="height: 5%; width: 26%"
                    for="eventoIndicadoMeta"
                    fetchValue="#{eventos.descricao}"
                    suggestionAction="#{MetaCRMControle.executarAutocompleteConsultaEvento}"
                    minChars="1" rowClasses="5"
                    status="statusHora"
                    nothingLabel="Nenhum Evento encontrado !"
                    var="eventos"
                    reRender="mensagem">
                <a4j:support event="onselect"
                             action="#{MetaCRMControle.selecionarEventoIndicacao}"/>
                <h:column>
                    <h:outputText value="#{eventos.descricao}"/>
                </h:column>
            </rich:suggestionbox>
        </h:panelGroup>

        <h:panelGroup layout="block" style="padding-left: 20px; padding-right: 20px; padding-bottom: 20px; text-align: center;">
            <h:inputTextarea id="obsIndicadoMeta" rows="3"
                             style="width: 100%; background: white;"
                             onkeypress="somaCRMCustom('form:obsIndicadoMeta', 'form:obsIndicadoMetaMSG', 256);" onkeyup="somaCRMCustom('form:obsIndicadoMeta', 'form:obsIndicadoMetaMSG', 256);"
                             value="#{MetaCRMControle.indicacaoVO.observacao}"/>
            <h:inputText disabled="true" size="3" title="Caracteres restantes"
                         style="text-align: center; color: #000000" id="obsIndicadoMetaMSG"/>
        </h:panelGroup>

        <h:panelGrid columns="3" width="100%">
            <h:panelGroup style="padding-right: 20px; padding-bottom: 5px;  float: right"
                          layout="block">
                <a4j:commandLink id="novoIndicacaoMeta" styleClass="pure-button"
                                 value="Novo"
                                 action="#{MetaCRMControle.apresentarIndicacao}"
                                 reRender="panelGridRight"
                                 oncomplete="adicionarPlaceHolderCRM();"/>
                <rich:spacer width="13px"/>
                <a4j:commandLink id="salvarIndicacaoMeta" styleClass="pure-button"
                                 value="Salvar"
                                 action="#{MetaCRMControle.gravarIndicacaoMetaIndicacaoSalvar}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica} adicionarPlaceHolderCRM();"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                <rich:spacer width="13px"/>
                <a4j:commandLink id="objecaoIndicacaoMeta" styleClass="pure-button"
                                 value="Objeção"
                                 action="#{MetaCRMControle.apresentarObjecaoIndicacao}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica};adicionarPlaceHolderCRM();"
                                 reRender="mdlMensagemGenerica, panelGridRight"/>
                <rich:spacer width="13px"/>
                <a4j:commandLink id="agendarIndicacaoMeta" styleClass="pure-button pure-button-primary"
                                 value="Agendar"
                                 action="#{MetaCRMControle.apresentarAgendaIndicacao}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica};adicionarPlaceHolderCRM();"
                                 reRender="mdlMensagemGenerica, panelGridRight"/>
            </h:panelGroup>
        </h:panelGrid>


        <%-- OBJEÇÃO INDICAÇÃO --%>
        <h:panelGroup rendered="#{MetaCRMControle.mostrarPanelObjecaoIndicacao}"
                      style="float: left; width: 100%; background: white">
            <h:panelGroup id="panelObjecaoIndicacaoSeta" layout="block" styleClass="triangleCSS"
                          style="float: left; margin-left: 74%; background: white"/>
        </h:panelGroup>
        <h:panelGrid id="panelObjecaoIndicacao" columns="1" width="100%" style="text-align: center; background: #e6e6e6"
                     rendered="#{MetaCRMControle.mostrarPanelObjecaoIndicacao}">

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="float: left; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold; padding-right: 1%;"
                            value="Tipo de Contato"/>
                    <h:selectOneMenu id="tipoContatoObjecaoIndicacao"
                                     style="color: #333333; font-size: 12px; float: left; padding: 5px;"
                                     value="#{MetaCRMControle.historicoContatoVO.tipoContato}">
                        <f:selectItems value="#{MetaCRMControle.listaTipoContatoIndicacaoPassivo}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>


            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold; padding-right: 1%;"
                            value="Objeção"/>
                </h:panelGroup>
                <h:panelGroup
                        style="padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:selectOneMenu id="objecaoIndicacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; font-size: 14px;"
                                     value="#{MetaCRMControle.historicoContatoVO.objecaoVO.codigo}">
                        <f:selectItems value="#{MetaCRMControle.listaObjecao}"/>
                        <a4j:support event="onchange" action="#{MetaCRMControle.alterarObjecao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                <h:panelGroup style="padding: 5px" layout="block">
                    <a4j:commandLink id="gravarObjecaoIndicacao"
                                     styleClass="pure-button pure-button-primary"
                                     value="Concluir"
                                     action="#{MetaCRMControle.gravarIndicacaoMetaIndicacaoObjecao}"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica};ajustarScrollAlunoSelecionadoDireita()"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

        <%-- AGENDAR INDICAÇÃO --%>
        <h:panelGroup rendered="#{MetaCRMControle.mostrarPanelAgendarIndicacao}"
                      style="float: left; width: 100%; background: white">
            <h:panelGroup id="panelAgendarIndicacaoSeta" layout="block" styleClass="triangleCSS"
                          style="float: left; margin-left: 90%; background: white"/>
        </h:panelGroup>
        <h:panelGrid id="panelAgendarIndicacao" columns="1" width="100%" style="text-align: center; background: #e6e6e6"
                     rendered="#{MetaCRMControle.mostrarPanelAgendarIndicacao}">

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="float: left; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold; padding-right: 3%;"
                            value="Tipo de Contato"/>
                    <h:selectOneMenu id="tipoContatoAgendaIndicacao"
                                     style="color: #333333; font-size: 12px; float: left; padding: 5px;"
                                     value="#{MetaCRMControle.historicoContatoVO.tipoContato}">
                        <f:selectItems value="#{MetaCRMControle.listaTipoContatoIndicacaoPassivo}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>


            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="float: left; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Tipo de Agendamento"/>
                    <rich:spacer width="10px" style="float: left"/>
                    <h:selectOneRadio id="agendamentoIndicacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                      style="float: left; background: #e6e6e6; color: #333333; font-size: 14px;"
                                      value="#{MetaCRMControle.agendaVO.tipoAgendamento}">
                        <f:selectItems value="#{MetaCRMControle.listaSelectItemTipoAgendamentoAgenda}"/>
                        <a4j:support event="onclick" reRender="panelAgendarIndicacao"
                                     oncomplete="adicionarPlaceHolderCRM();"/>
                    </h:selectOneRadio>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="selecionarConsultorIndicacao" columns="2" width="100%" style="background: #e6e6e6;" rendered="#{LoginControle.permissaoAcessoMenuVO.selecionarColaboradorMetas}"
                         styleClass="tabFormSubordinada">
                <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px" layout="block">
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold" value="Colaboradores"/>

                    <rich:spacer width="10px"/>

                    <h:inputText  id="nomeConsultorIndicacao"
                                  style="width: 320px; font-size: 12px !important;"
                                  styleClass="campos"
                                  size="50"
                                  maxlength="50"
                                  onblur="blurinput(this);"
                                  onfocus="toggleSuggestions ( #{rich:component('suggestionConsultorIndicacao')} )"
                                  value="#{MetaCRMControle.nomeUsuario}"/>

                    <rich:suggestionbox for="nomeConsultorIndicacao"
                                        width="320"
                                        fetchValue="#{result.nome}"
                                        status="statusInComponent"
                                        style="background: #F6F6F6; color: #333333; padding: 3px;"
                                        suggestionAction="#{MetaCRMControle.executarAutocompleteCol}"
                                        minChars="1"
                                        rowClasses="linhaImpar, linhaPar"
                                        ajaxSingle="false"
                                        var="result"
                                        id="suggestionConsultorIndicacao">
                        <a4j:support event="onselect"
                                     focus="telasCRM"
                                     oncomplete="#{MetaCRMControle.mensagemNotificar}"
                                     action="#{MetaCRMControle.selecionarUsuario}"/>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText value="#{result.nome}"/>
                        </h:column>
                    </rich:suggestionbox>
                    <script>
                        function toggleSuggestions(suggestionBox) {
                            if (suggestionBox.active)
                                suggestionBox.hide();
                            else
                                suggestionBox.callSuggestion(true);
                        }
                    </script>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.apresentarInputTextAulaExperimental}">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Modalidade"/>
                    <rich:spacer width="10px"/>

                    <h:selectOneMenu id="modalidadeAgendaIndicacao" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; padding: 3px;"
                                     value="#{MetaCRMControle.agendaVO.modalidade.codigo}">
                        <f:selectItems value="#{MetaCRMControle.listaModalidade}"/>
                        <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModalidade}"/>
                    </h:selectOneMenu>

                    <%--<h:inputText id="textModalidadeIndicacao" size="50"--%>
                    <%--onblur="blurinput(this);"--%>
                    <%--onfocus="focusinput(this);"--%>
                    <%--styleClass="form"--%>
                    <%--value="#{MetaCRMControle.agendaVO.modalidade.nome}"/>--%>

                    <%--<rich:suggestionbox id="suggestionModalidadeIndicacao"--%>
                    <%--height="200" width="300"--%>
                    <%--for="textModalidadeIndicacao"--%>
                    <%--status="statusInComponent"--%>
                    <%--immediate="true"--%>
                    <%--suggestionAction="#{MetaCRMControle.autocompleteModalidade}"--%>
                    <%--nothingLabel="Nenhuma Modalidade encontrada !" var="modalidade">--%>
                    <%--<a4j:support event="onselect"--%>
                    <%--action="#{MetaCRMControle.selecionarModalidade}"/>--%>
                    <%--<h:column>--%>
                    <%--<h:outputText value="#{modalidade.nome}"/>--%>
                    <%--</h:column>--%>
                    <%--</rich:suggestionbox>--%>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                                MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}">
                <h:panelGroup id="selTipoColaboradorIndic" layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                  value="Tipo Professor"/>
                    <rich:spacer width="10px"/>
                    <h:selectOneMenu id="selTipoColabIndic" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; padding: 3px;"
                                     value="#{MetaCRMControle.tipoProfessor}">
                        <f:selectItems value="#{MetaCRMControle.listaTiposProfessores}"/>
                        <a4j:support event="onchange" actionListener="#{MetaCRMControle.alterarTipoColaborador}"
                                     reRender="selColaboradorIndic, infoColaboradorIndic, selTipoColaboradorIndic">
                        </a4j:support>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                                MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}">
                <h:panelGroup id="infoColaboradorIndic">
                    <h:panelGroup id="selColaboradorIndic" rendered="#{MetaCRMControle.mostrarSeletorNome}"
                                  layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                        <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                      value="Professor"/>
                        <rich:spacer width="10px"/>
                        <h:selectOneMenu id="selColabIndic" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="background: #F6F6F6; color: #333333; padding: 3px;"
                                         value="#{MetaCRMControle.codigoProfessor}">
                            <f:selectItems value="#{MetaCRMControle.listaColaboradores}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup
                        style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                        layout="block">
                    <h:outputText
                            style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Agendar para"/>
                    <rich:spacer width="10px"/>
                    <rich:calendar id="dataAgendamentoIndicacao"
                                   value="#{MetaCRMControle.agendaVO.dataAgendamento}"
                                   inputSize="10"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <rich:spacer style="width: 10px"/>
                    <h:inputText id="horaMinutoAgendamentoIndicacao"
                                 onkeypress="return dois_pontos(this);"
                                 size="10" maxlength="5" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" value="#{MetaCRMControle.agendaVO.horaMinuto}"/>
                    <a4j:commandLink
                            id="buscarAulasIndSemContato"
                            rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                                MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}"
                            style="line-height: 20px"
                            styleClass="botoes nvoBt btSec"
                            value="Buscar aulas"
                            action="#{MetaCRMControle.montarAulasAgenda}"
                            oncomplete="#{MetaCRMControle.mensagemNotificar}"
                            reRender="mensagens, panelAgendarIndicacao"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                             MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}">
                <h:panelGroup id="infoAulasIndic">
                    <h:panelGroup id="selAulasIndic" rendered="#{MetaCRMControle.listaAulasAgenda.size() > 1}"
                                  layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                        <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                      value="Aula"/>
                        <rich:spacer width="10px"/>
                        <h:selectOneMenu id="selAulaIndic" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="background: #F6F6F6; color: #333333; padding: 3px;"
                                         value="#{MetaCRMControle.codigoAula}">
                            <f:selectItems value="#{MetaCRMControle.listaAulasAgenda}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                <h:panelGroup style="padding: 5px" layout="block">
                    <a4j:commandLink
                            id="concluirAgendaIndicacao"
                            styleClass="pure-button pure-button-primary"
                            value="Concluir"
                            action="#{MetaCRMControle.gravarIndicacaoMetaIndicacaoAgendar}"
                            oncomplete="#{MetaCRMControle.modalMensagemGenerica};ajustarScrollAlunoSelecionadoDireita()"
                            reRender="panelGridLeft, panelGridCenter, panelGridRight, nomeConsultorIndicacao,mdlMensagemGenerica"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>
    </h:panelGrid>


    <h:panelGroup id="operacoesHistorico" layout="block"
                  rendered="#{MetaCRMControle.mostrarPanelRealizarContato}">

        <h:panelGrid rendered="#{MetaCRMControle.alunoMenorIdade}" width="100%"
                     style="padding-left: 20px; padding-top: 10px; padding-right: 10px; background: #e6e6e6;">
            <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-right: 10px;"
                          layout="block">
                <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                              value="#{MetaCRMControle.rotuloResponsavel}"/>
                <h:outputText style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                              value="#{MetaCRMControle.nomeResponsaveis}"/>
            </h:panelGroup>
        </h:panelGrid>


        <%-- INFORMAÇÕES DO CLIENTE --%>
        <h:panelGrid columns="4" width="100%" style="background: #e6e6e6;">
            <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-right: 10px;"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Código do item:"/>
                <h:outputText style="padding-left: 3px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                              value="#{MetaCRMControle.metaDetalhadoVOSelecionado.codigo}"/>
            </h:panelGroup>
            <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-right: 10px;"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Cadastro:"/>
                <h:outputText style="padding-left: 3px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                              value="#{MetaCRMControle.historicoContatoVO.dataCadastro_Apresentar}"/>
            </h:panelGroup>
            <h:panelGroup style="padding-left: 10px; padding-top: 10px; padding-right: 10px;"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Último Acesso:"/>
                <h:outputText style="padding-left: 3px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                              value="#{MetaCRMControle.historicoContatoVO.diaUltimoAcessoComHora_Apresentar}"/>
            </h:panelGroup>
            <h:panelGroup style="padding-left: 10px; padding-top: 10px; padding-right: 20px;"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Vencimento:"/>
                <h:outputText
                        style="padding-left: 3px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.historicoContatoVO.vencimentoContrato_Apresentar}"/>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGrid columns="5" width="100%" style="background: #e6e6e6">
            <h:panelGroup style="padding-left: 20px" layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Ligações:"/>
                <a4j:commandLink
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.historicoContatoVO.totalLigacao}" ajaxSingle="true"
                        action="#{MetaCRMControle.abrirLigacoes}"
                        oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                        reRender="modalHistoricoContadorCliente"/>
            </h:panelGroup>
            <h:panelGroup style="padding-left: 10px; padding-top: 10px; padding-right: 10px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Email:"/>
                <a4j:commandLink
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.historicoContatoVO.qtdEmail}" ajaxSingle="true"
                        action="#{MetaCRMControle.abrirEmail}"
                        oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                        reRender="modalHistoricoContadorCliente"/>
            </h:panelGroup>
            <h:panelGroup style="padding-left: 10px; padding-top: 10px; padding-right: 10px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Contato Pessoal:"/>
                <a4j:commandLink
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.historicoContatoVO.qtdPessoal}" ajaxSingle="true"
                        action="#{MetaCRMControle.abrirContatoPessoal}"
                        oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                        reRender="modalHistoricoContadorCliente"/>
            </h:panelGroup>
            <h:panelGroup style="padding-left: 10px; padding-top: 10px; padding-right: 10px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="SMS:"/>
                <a4j:commandLink
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.historicoContatoVO.qtdSMS}" ajaxSingle="true"
                        action="#{MetaCRMControle.abrirSMS}"
                        oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                        reRender="modalHistoricoContadorCliente"/>
            </h:panelGroup>
            <h:panelGroup style="padding-left: 10px; padding-top: 10px; padding-right: 10px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="APP:"/>
                <a4j:commandLink
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.historicoContatoVO.qtdAPP}" ajaxSingle="true"
                        action="#{MetaCRMControle.abrirAPP}"
                        oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                        reRender="modalHistoricoContadorCliente"/>

            </h:panelGroup>
        </h:panelGrid>

        <%--INFORMAÇÕES DE CREDITO TREINO--%>
        <h:panelGrid columns="1" width="100%" style="background: #e6e6e6"
        rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.clienteCreditoTreino}">
            <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-right: 10px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold; color: red"
                        value="Contrato de Crédito"/>

                <h:outputText
                        style="margin-left : 20px; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Quantidade de crédito restante:"/>
                <h:outputText
                        style="margin-left : 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.metaDetalhadoVOSelecionado.saldoCreditoTreino}"/>
            </h:panelGroup>
        </h:panelGrid>

        <%-- MOTIVO POS-VENDA --%>
        <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                     rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'PV'}">
            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Motivo"/>
                <h:outputText
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.metaDetalhadoVOSelecionado.configuracaoDiasPosVendaVO.descricao}"/>
            </h:panelGroup>
        </h:panelGrid>

        <%-- OBS TURMA LISTA ESPERA CRM --%>
        <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                     rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'FT'}">
            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Observação"/>
                <h:outputText
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.metaDetalhadoVOSelecionado.observacaoFilaEsperaTurmaCrm}"/>
            </h:panelGroup>
        </h:panelGrid>

        <%-- MOTIVO GYMPASS --%>
        <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                     rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'UG'}">
            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Motivo"/>
                <h:outputText
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.metaDetalhadoVOSelecionado.descconfiguracaodiasmetas}"/>
            </h:panelGroup>
        </h:panelGrid>

        <%--FALTOSOS--%>
        <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                     rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'FA'}">
            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        rendered="#{MetaCRMControle.historicoContatoVO.nrDiasUltimoAcessoNovoCRM > 0}"
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; color:red; font-weight: bold"
                        value="#{MetaCRMControle.historicoContatoVO.nrDiasUltimoAcessoNovoCRM} dias sem acesso"/>
                <h:outputText
                        rendered="#{MetaCRMControle.historicoContatoVO.nrDiasUltimoAcessoNovoCRM <= 0}"
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; color:red; font-weight: bold"
                        value="Aluno já teve acesso após a geração dessa meta"/>
            </h:panelGroup>
        </h:panelGrid>

        <%-- EX-ALUNO --%>
        <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                     rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'EX'}">
            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: #333333; font-weight: bold;"
                        value="Ex-Aluno "/>
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: #333333;"
                        value="#{MetaCRMControle.metaDetalhadoVOSelecionado.diasSemAgendamento} Dias"/>
            </h:panelGroup>


            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Motivo"/>
                <h:outputText
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.metaDetalhadoVOSelecionado.descconfiguracaodiasmetas}"/>
            </h:panelGroup>

        </h:panelGrid>


        <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                     rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'VA'}">

            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                          layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                        value="Motivo"/>
                <h:outputText
                        style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                        value="#{MetaCRMControle.metaDetalhadoVOSelecionado.descconfiguracaodiasmetas}"/>
            </h:panelGroup>

        </h:panelGrid>

        <%-- GRUPO DE RISCO --%>
        <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                     rendered="#{MetaCRMControle.historicoContatoVO.clienteVO.clienteSituacaoVO_Apresentar.ativo && MetaCRMControle.historicoContatoVO.clienteVO.situacaoClienteSinteticoVO.pesoRisco >= 6}">
            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                          layout="block">
                <img style="vertical-align: middle; margin-right: 4px;" src="imagens/ico_ani_telefone.gif">
                <h:outputText
                        id="msgCliente"
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: red; font-weight: bold;"
                        value="Cliente com peso #{MetaCRMControle.historicoContatoVO.clienteVO.situacaoClienteSinteticoVO.pesoRisco} em RISCO de sair da academia. Faça algo urgente."/>
            </h:panelGroup>
        </h:panelGrid>

        <%-- FASES DO ALUNO --%>
        <h:panelGrid id="fasesDoContato" rendered="#{MetaCRMControle.apresentarFasesDoContato}" columns="1" width="100%"
                     style="background: #e6e6e6;">
            <h:panelGroup style="padding-left: 20px;" layout="block">
                <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: red;"
                              value="O Aluno está nas seguintes Fases: "/>
                <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: #333333;"
                              value="#{MetaCRMControle.fasesDoContato}"/>
            </h:panelGroup>
            <h:panelGroup style="padding-left: 20px; padding-bottom: 10px" layout="block">
                <h:outputText style="font-size: 12px;font-family: arial, helvetica, sans-serif; color: #333333;"
                              value="Lembrando que: O contato não necessariamente irá bater meta para as duas fases."/>
            </h:panelGroup>
        </h:panelGrid>


        <h:panelGrid columns="2" width="100%">
            <%--OBJETIVO--%>
            <h:panelGrid rendered="#{!MetaCRMControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes || MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'AG' || MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'LH' || MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'LC'}"
                         columns="1" width="100%" style="background: white">
                <h:panelGroup style="padding-top: 10px; padding-bottom: 10px" layout="block">
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                  value="Objetivo"/>
                    <h:outputText style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                                  value="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.objetivo}"/>

                </h:panelGroup>
            </h:panelGrid>



            <%--OBJETIVO - TODA AÇÃO BATE META--%>
            <h:panelGrid rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes && MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla != 'AG' && MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla != 'LH'  && MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla != 'LC'}"
                         columns="1" width="100%" style="background: white">
                <h:panelGroup style="padding-top: 10px; padding-bottom: 10px" layout="block">
                    <h:outputText
                            style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                            value="Sistema está configurado para que toda ação seja contabilizado meta."/>
                </h:panelGroup>
            </h:panelGrid>

            <a4j:commandLink id="textoPadrao"
                             styleClass="pure-button pure-button-small"
                             style="float: right"
                             title="Script"
                             action="#{MetaCRMControle.preencherTextoPadrao}"
                             oncomplete="#{MetaCRMControle.onComplete};adicionarPlaceHolderCRM()"
                             reRender="mdlMensagemGenerica, panelTextoPadrao">
                <i class="fa-icon-list-alt"></i> &nbsp Script
            </a4j:commandLink>

        </h:panelGrid>
        <h:panelGrid rendered="#{not MetaCRMControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes and (MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'AG' or MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'LH' or MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'LC')}"
                     columns="1" width="100%" style="background: white">
            <h:panelGroup style="padding-top: 10px; padding-bottom: 10px" layout="block">

                <h:outputText  style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold; color:red"
                              value="Lembrete: Agendamento de ligações não contam como meta."/>

            </h:panelGroup>
        </h:panelGrid>
        <h:panelGrid rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.urlRD !=''}" width="100%">
            <h:panelGrid  columns="1" width="100%" style="background: white">
                <h:panelGroup style="padding-top: 10px; padding-bottom: 10px" layout="block">
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                  value="Link RD Station:"/>
                    <h:outputLink  value="#{MetaCRMControle.metaDetalhadoVOSelecionado.urlRD}" target="_blank"
                              ><h:outputText style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                                  value="#{MetaCRMControle.metaDetalhadoVOSelecionado.urlRD}"/>
                    </h:outputLink>
                    
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>
        <h:panelGrid rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.conversaoLeadVO.identificador !=''}" width="100%">
            <h:panelGrid rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.conversaoLeadVO.identificador != ''}"  columns="1" width="100%" style="background: white">
                <h:panelGroup style="padding-top: 10px; padding-bottom: 10px" layout="block">
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                  rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.urlRD !=''}"
                                  value="Identificador RD:"/>
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                  rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.urlRD =='' && MetaCRMControle.metaDetalhadoVOSelecionado.conversaoLeadVO.identificador != null}"
                                  value="Identificador:"/>
                    <h:outputText style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                                  value="#{MetaCRMControle.metaDetalhadoVOSelecionado.conversaoLeadVO.identificador}"/>

                    <c:if test="${SuporteControle.desv}">
                        <a4j:commandLink
                                style="margin-left: 8px"
                                rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.buzzlead}"
                                action="#{MetaCRMControle.acaoWonBuzzlead}" value="Won"/>
                        <a4j:commandLink
                                style="margin-left: 8px"
                                rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.buzzlead}"
                                action="#{MetaCRMControle.acaoLostBuzzlead}" value="Lost"/>
                    </c:if>

                </h:panelGroup>
            </h:panelGrid>    
        </h:panelGrid>

        <h:panelGrid rendered="#{MetaCRMControle.metaDetalhadoVOSelecionado.indicado.indicacaoVO.observacao !=''}" width="100%">
            <h:panelGrid columns="1" width="100%" style="background: white">
                <h:panelGroup style="padding-top: 10px; padding-bottom: 10px" layout="block">
                    <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                  value="Mensagem:"/>
                    <h:outputText style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                                  value="#{MetaCRMControle.metaDetalhadoVOSelecionado.indicado.indicacaoVO.observacao}"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>



        <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block"
                      rendered="#{!MetaCRMControle.metaCorreta}">
            <h:outputText
                    style="text-align: center; color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                    value="A meta a ser realizada deve ser #{MetaCRMControle.metaAtual.fasesCRMEnum.descricao}"/>
        </h:panelGroup>

        <h:panelGrid columns="2" width="100%" rendered="#{MetaCRMControle.metaCorreta}">

            <%--TIPO CONTATO--%>
            <h:selectOneMenu id="tipoOperacao"
                             style="background: #F6F6F6; color: #333333; font-size: 14px; float: left"
                             value="#{MetaCRMControle.historicoContatoVO.tipoContato}">
                <f:selectItems value="#{MetaCRMControle.listaTipoContato}"/>
                <a4j:support event="onchange" action="#{MetaCRMControle.alterarMeioEnvio}"
                             oncomplete="adicionarPlaceHolderCRM()" reRender="panelGridRight"/>
            </h:selectOneMenu>

            <h:panelGroup layout="block" style="display: inline-flex; float: right;">

                <%--PESQUISA--%>
                <h:selectOneMenu id="pesquisa"
                                 rendered="#{MetaCRMControle.existePesquisa}"
                                 style="background: #F6F6F6; color: #333333; font-size: 14px; margin-right: 10px"
                                 value="#{MetaCRMControle.pesquisa}">
                    <f:selectItems value="#{MetaCRMControle.listaSelectItemPesquisa}"/>
                    <a4j:support event="onchange" action="#{MetaCRMControle.adicionarLinkPesquisa}"
                                 status="false" oncomplete="adicionarPlaceHolderCRM();#{MetaCRMControle.mensagemNotificar}"
                                 reRender="panelGridRight"/>
                </h:selectOneMenu>

                <%--FAZER INDICACAO--%>
                <a4j:commandLink id="fazerIndicacao"
                                 rendered="#{!MetaCRMControle.metaIndicacao || MetaCRMControle.metaIndicacaoSemContato}"
                                 styleClass="pure-button"
                                 value="Fazer Indicação"
                                 action="#{MetaCRMControle.apresentarIndicacao}"
                                 oncomplete="adicionarPlaceHolderCRM()"
                                 reRender="panelGridRight"/>
            </h:panelGroup>
        </h:panelGrid>


        <%---------------------------------------- FAZER INDICAÇÃO ----------------------------------------------------------%>
        <h:panelGroup layout="block" styleClass="triangleCSS" style="float: right; margin-right: 7%;"
                      rendered="#{MetaCRMControle.mostrarPanelIndicacao}"/>
        <h:panelGrid id="panelFazerIndicao" columns="1" width="100%" style="background: #e6e6e6"
                     rendered="#{MetaCRMControle.mostrarPanelIndicacao}">

            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 15px; padding-top: 15px">
                <h:inputText id="nomeIndicado" maxlength="50"
                             onkeypress="tabenter('form:telIndicado01')"
                             style="background: #F6F6F6; width: 58%"
                             value="#{MetaCRMControle.indicadoVO.nomeIndicado}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 10px" >
                <h:inputText id="cpfindicadoMeta" maxlength="16"
                             style="background: #F6F6F6; width: 28%"
                             label="Teste"
                             onblur="blurinput(this);"
                             onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.indicadoVO.cpf}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                    <h:inputText id="telIndicado01" maxlength="13"
                                 style="background: #F6F6F6; width: 28%"
                                 onchange="return validar_Telefone(this.id);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 value="#{MetaCRMControle.indicadoVO.telefoneIndicado}"/>
                    <rich:spacer width="13px"/>
                    <h:inputText id="telIndicado02" maxlength="13"
                                 style="background: #F6F6F6; width: 28%"
                                 onchange="return validar_Telefone(this.id);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 value="#{MetaCRMControle.indicadoVO.telefone}"/>
                </c:if>
                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                    <h:inputText id="telIndicado01" maxlength="14"
                                 style="background: #F6F6F6; width: 28%"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 value="#{MetaCRMControle.indicadoVO.telefoneIndicado}"/>
                    <rich:spacer width="13px"/>
                    <h:inputText id="telIndicado02" maxlength="14"
                                 style="background: #F6F6F6; width: 28%"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 value="#{MetaCRMControle.indicadoVO.telefone}"/>
                </c:if>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <h:inputText id="emailIndicado" maxlength="50"
                             onkeypress="tabenter('form:eventoIndicado')"
                             style="background: #F6F6F6; width: 58%"
                             value="#{MetaCRMControle.indicadoVO.email}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                <h:inputText id="eventoIndicado"
                             onkeypress="tabenter('form:obsIndicado')"
                             onblur="blurinput(this);" onfocus="focusinput(this);"
                             style="background: #F6F6F6; width: 58%"
                             value="#{MetaCRMControle.indicacaoVO.evento.descricao}"/>
                <rich:suggestionbox
                        id="suggestionEventoIndicacao"
                        style="height: 5%; width: 26%"
                        for="eventoIndicado"
                        fetchValue="#{eventos.descricao}"
                        suggestionAction="#{MetaCRMControle.executarAutocompleteConsultaEvento}"
                        minChars="1" rowClasses="5"
                        status="statusHora"
                        nothingLabel="Nenhum Evento encontrado !"
                        var="eventos"
                        reRender="mensagem">
                    <a4j:support event="onselect"
                                 action="#{MetaCRMControle.selecionarEventoIndicacao}"/>
                    <h:column>
                        <h:outputText value="#{eventos.descricao}"/>
                    </h:column>
                </rich:suggestionbox>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-left: 20px; padding-right: 20px; padding-bottom: 20px">
                <h:inputTextarea id="obsIndicado" rows="3"
                                 style="width: 100%; background: white;"
                                 value="#{MetaCRMControle.indicacaoVO.observacao}"/>
            </h:panelGroup>

            <h:panelGrid columns="3" width="100%">
                <h:panelGroup style="padding-top: 0px; padding-right: 20px; padding-bottom: 20px;  float: right"
                              layout="block">
                    <a4j:commandLink id="cancelarIndicacao" styleClass="pure-button"
                                     value="Cancelar"
                                     action="#{MetaCRMControle.apresentarIndicacao}"
                                     reRender="panelGridRight"/>
                    <rich:spacer width="13px"/>
                    <a4j:commandLink id="gravarFazerNovaIndicacao"
                                     styleClass="pure-button"
                                     value="Salvar e fazer nova indicação"
                                     action="#{MetaCRMControle.gravarIndicacaoTela}"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                    <rich:spacer width="13px"/>
                    <a4j:commandLink id="gravarFecharPanelIndicacao"
                                     styleClass="pure-button pure-button-primary"
                                     value="Salvar"
                                     action="#{MetaCRMControle.gravarIndicacaoFecharPanel}"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

        <%-- BOTÕES PARA META DE AGENDAMENTOS CONFIRMAR AGENDAMENTO E REAGENDAR --%>
        <h:panelGrid columns="2" width="100%" rendered="#{MetaCRMControle.metaCorreta}">
            <h:panelGroup id="botoesAgendamento" style="padding-top: 5px; float: left" layout="block"
                          rendered="#{MetaCRMControle.apresentarBotaoConfirmarAgendamento || MetaCRMControle.apresentarBotaoCancelarAgendamento || MetaCRMControle.apresentarBotaoReagendarAgendamento}">

                <a4j:commandLink id="reagendar"
                                 rendered="#{MetaCRMControle.apresentarBotaoReagendarAgendamento}"
                                 styleClass="pure-button pure-button-secundary"
                                 title="Reagendar"
                                 value="Reagendar"
                                 action="#{MetaCRMControle.apresentarPanelReagendamento}"
                                 oncomplete="adicionarPlaceHolderCRM()"
                                 reRender="panelGridRight">
                    <i class="fa-icon-calendar"
                       style="color: #333; float: left;padding-right: 6px;"></i>
                </a4j:commandLink>

                <rich:spacer width="10px"
                             rendered="#{MetaCRMControle.apresentarBotaoReagendarAgendamento && (MetaCRMControle.apresentarBotaoCancelarAgendamento || MetaCRMControle.apresentarBotaoConfirmarAgendamento)}"/>

                <a4j:commandLink id="confirmarComparecimento"
                                 rendered="#{MetaCRMControle.apresentarBotaoConfirmarAgendamento}"
                                 styleClass="pure-button pure-button-primary"
                                 style="background: darkgreen !important"
                                 title="Confirmar Comparecimento"
                                 value="Confirmar Comparecimento"
                                 action="#{MetaCRMControle.confirmarComparecimento}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                 reRender="botoesAgendamento, mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight">
                    <i class="fa-icon-check"
                       style="color: white; float: left;padding-right: 6px;"></i>
                </a4j:commandLink>

                <a4j:commandLink id="cancelarComparecimento"
                                 rendered="#{MetaCRMControle.apresentarBotaoCancelarAgendamento}"
                                 styleClass="pure-button pure-button-primary"
                                 style="background: darkred !important"
                                 title="Cancelar Comparecimento"
                                 value="Cancelar Comparecimento"
                                 action="#{MetaCRMControle.cancelarComparecimento}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                 reRender="botoesAgendamento, mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight">
                    <i class="fa-icon-remove-sign"
                       style="color: white; float: left;padding-right: 6px;"></i>
                </a4j:commandLink>

            </h:panelGroup>
        </h:panelGrid>


        <%---------------------------------------- REAGENDAMENTO ----------------------------------------------------------%>
        <h:panelGroup id="panelReagendamentoSeta" layout="block" styleClass="triangleCSS"
                      style="float: left; margin-left: 7%;"
                      rendered="#{MetaCRMControle.mostrarPanelReagendamento}"/>
        <h:panelGrid id="panelReagendamento" columns="1" width="100%" style="text-align: center; background: #e6e6e6"
                     rendered="#{MetaCRMControle.mostrarPanelReagendamento}">

            <h:panelGroup layout="block"
                          style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px; padding-right: 20px">
                <h:inputTextarea id="observacaoReagendamento"
                                 style="width: 100%" rows="3"
                                 value="#{MetaCRMControle.historicoContatoVO.observacao}"/>
            </h:panelGroup>

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px" layout="block">
                    <h:outputText
                            style="float: left; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Tipo de Reagendamento"/>
                    <rich:spacer width="10px" style="float: left"/>
                    <h:selectOneRadio id="tipoReagendamento" onblur="blurinput(this);" onfocus="focusinput(this);"
                                      style="float: left; background: #e6e6e6; color: #333333; font-size: 14px;"
                                      value="#{MetaCRMControle.agendaVO.tipoAgendamento}">
                        <f:selectItems value="#{MetaCRMControle.listaSelectItemTipoAgendamentoReagendamento}"/>
                        <a4j:support event="onclick" reRender="panelReagendamento"
                                     oncomplete="adicionarPlaceHolderCRM();"/>
                    </h:selectOneRadio>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                         rendered="#{MetaCRMControle.apresentarInputTextAulaExperimental}">
                <h:panelGroup style="float: left; padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                              layout="block">
                    <h:outputText
                            style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Modalidade"/>
                    <rich:spacer width="10px"/>
                    <h:selectOneMenu id="modalidadeAgendaReagendamento" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; padding: 3px;"
                                     value="#{MetaCRMControle.agendaVO.modalidade.codigo}">
                        <f:selectItems value="#{MetaCRMControle.listaModalidade}"/>
                        <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModalidade}"/>
                    </h:selectOneMenu>
                    <%--<h:inputText id="textModalidadeReagendamento" size="50"--%>
                    <%--onblur="blurinput(this);"--%>
                    <%--onfocus="focusinput(this);"--%>
                    <%--styleClass="form"--%>
                    <%--value="#{MetaCRMControle.agendaVO.modalidade.nome}"/>--%>
                    <%--<rich:suggestionbox id="suggestionModalidadeReagendamento"--%>
                    <%--height="200" width="300"--%>
                    <%--for="textModalidadeReagendamento"--%>
                    <%--status="statusInComponent"--%>
                    <%--immediate="true"--%>
                    <%--suggestionAction="#{MetaCRMControle.autocompleteModalidade}"--%>
                    <%--nothingLabel="Nenhuma Modalidade encontrada !" var="modalidade">--%>
                    <%--<a4j:support event="onselect"--%>
                    <%--action="#{MetaCRMControle.selecionarModalidade}"/>--%>
                    <%--<h:column>--%>
                    <%--<h:outputText value="#{modalidade.nome}"/>--%>
                    <%--</h:column>--%>
                    <%--</rich:suggestionbox>--%>
                </h:panelGroup>
            </h:panelGrid>


            <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                <h:panelGroup style="float: left; padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                              layout="block">
                    <h:outputText
                            style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                            value="Reagendar para"/>
                    <rich:spacer width="10px"/>

                    <rich:calendar id="dataReagendamento"
                                   value="#{MetaCRMControle.agendaVO.dataAgendamento}"
                                   inputSize="10"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <rich:spacer style="width: 10px"/>
                    <h:inputText id="horaMinutoReagendamento"
                                 onkeypress="return dois_pontos(this);"
                                 size="10" maxlength="5" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" value="#{MetaCRMControle.agendaVO.horaMinuto}"/>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                <h:panelGroup style="padding: 5px" layout="block">
                    <a4j:commandLink
                            id="gravarReagendamento"
                            styleClass="pure-button pure-button-primary"
                            value="Reagendar"
                            action="#{MetaCRMControle.reagendarAgendamento}"
                            oncomplete="#{MetaCRMControle.modalMensagemGenerica};adicionarPlaceHolderCRM();"
                            reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                    <rich:spacer width="10px"/>
                    <a4j:commandLink
                            id="cancelarReagendamento"
                            styleClass="pure-button pure-button-secundary"
                            value="Cancelar"
                            action="#{MetaCRMControle.apresentarPanelReagendamento}"
                            reRender="panelGridRight"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>


        <%---------------------------------------- TELEFONES ----------------------------------------------------------%>
        <h:panelGrid id="tabelaTelefones" columns="1" width="100%"
                     rendered="#{MetaCRMControle.apresentarTelefoneCliente && MetaCRMControle.metaCorreta}">

            <h:dataTable id="resTelefone" width="100%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                         style="text-align: left;"
                         value="#{MetaCRMControle.listaTelefoneClientePorTipoContato}" var="telefone">

                <h:column rendered="#{MetaCRMControle.historicoContatoVO.tipoContato == 'CS'}">
                    <h:selectBooleanCheckbox id="telSelecionado" style="margin: 10%;"
                                             value="#{telefone.selecionado}">
                        <a4j:support event="onchange"
                                     action="#{MetaCRMControle.selecionarTelefoneSMS}"
                                     reRender="tabelaTelefones"/>
                    </h:selectBooleanCheckbox>
                </h:column>

                <h:column>
                    <h:outputText
                            style="font-size: 13px; font-weight: bold; font-family: arial, helvetica, sans-serif"
                            value="#{telefone.ddi} #{telefone.numero}"/>
                </h:column>

                <h:column>
                    <h:outputText style="font-size: 13px; font-family: arial, helvetica, sans-serif;"
                                  value="#{telefone.tipoTelefone_Apresentar}"/>
                </h:column>

                <h:column>
                    <h:outputText style="font-size: 13px; font-family: arial, helvetica, sans-serif; color: #999"
                                  value="#{telefone.descricao}"/>
                </h:column>

                <h:column>
                    <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                        <a4j:commandLink status="none" reRender="resTelefone, tipoOperacao"
                                         oncomplete="#{MetaCRMControle.msgAlert}"
                                         rendered="#{telefone.numeroCelular}"
                                         title="Clique aqui para abrir o Whatsapp Web já no contato selecionado"
                                         actionListener="#{MetaCRMControle.abrirWppListener}"
                                         style="float: inherit; margin-left: 20px; background-color: #00C350 !important; color: #fff"
                                         styleClass="pure-button texto-size-12 texto-cor-cinza texto-font texto-bold">

                            <f:attribute name="ddiWpp" value="#{telefone.ddi}"/>
                            <f:attribute name="foneWpp" value="#{telefone.numeroSemMascara}"/>
                            <f:attribute name="usarNonoDigitoWApp" value="#{telefone.usarNonoDigitoWApp}"/>

                            <i class="fa-icon-whatsapp"></i> WhatsApp

                        </a4j:commandLink>

                        <a4j:commandLink title="WhatsApp" status="none"
                                         style="float: inherit; margin-left: 20px; background-color: #00C350 !important; color: #fff"
                                         action="#{MetaCRMControle.abrirWpp}"
                                         rendered="#{false and telefone.numeroCelular}"
                                         actionListener="#{MetaCRMControle.abrirWppListener}"
                                         reRender="mdlWhatsapp, tipoOperacao" styleClass="pure-button text-size-12"
                                         oncomplete="Richfaces.showModalPanel('mdlWhatsapp')">

                            <f:attribute name="ddiWpp" value="#{telefone.ddi}"/>
                            <f:attribute name="foneWpp" value="#{telefone.numeroSemMascara}"/>
                            <f:attribute name="usarNonoDigitoWApp" value="#{telefone.usarNonoDigitoWApp}"/>

                            <i class="fa-icon-whatsapp"></i> WhatsApp
                        </a4j:commandLink>
                    </c:if>

                    <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                        <a4j:commandLink status="none" reRender="resTelefone, tipoOperacao"
                                         oncomplete="#{MetaCRMControle.msgAlert}"
                                         rendered="#{telefone.tipoTelefone == 'CE'}"
                                         title="Clique aqui para abrir o Whatsapp Web já no contato selecionado"
                                         actionListener="#{MetaCRMControle.abrirWppListener}"
                                         style="float: inherit; margin-left: 20px; background-color: #00C350 !important; color: #fff"
                                         styleClass="pure-button texto-size-12 texto-cor-cinza texto-font texto-bold">

                            <f:attribute name="foneWpp" value="#{telefone.numeroSemMascara}"/>
                            <f:attribute name="ddiWpp" value="#{telefone.ddi}"/>
                            <f:attribute name="usarNonoDigitoWApp" value="#{telefone.usarNonoDigitoWApp}"/>
                            <i class="fa-icon-whatsapp"></i> WhatsApp

                        </a4j:commandLink>

                        <a4j:commandLink title="WhatsApp" status="none"
                                         style="float: inherit; margin-left: 20px; background-color: #00C350 !important; color: #fff"
                                         action="#{MetaCRMControle.abrirWpp}"
                                         rendered="#{false and telefone.tipoTelefone == 'CE'}"
                                         actionListener="#{MetaCRMControle.abrirWppListener}"
                                         reRender="mdlWhatsapp, tipoOperacao" styleClass="pure-button text-size-12"
                                         oncomplete="Richfaces.showModalPanel('mdlWhatsapp')">

                            <i class="fa-icon-whatsapp"></i> WhatsApp
                        </a4j:commandLink>
                    </c:if>


                </h:column>

                <h:column>
                    <h:outputText style="padding-right: 5px;" value="#{msg_aplic.prt_usar_nono_digito_wapp}" rendered="#{telefone.tipoTelefone == 'CE'}"/>
                    <h:selectBooleanCheckbox value="#{telefone.usarNonoDigitoWApp}" rendered="#{telefone.tipoTelefone == 'CE'}"/>
                </h:column>

            </h:dataTable>

            <h:panelGroup layout="block" style="padding-top: 10px" rendered="#{MetaCRMControle.apresentarTelefoneCliente && MetaCRMControle.metaCorreta ||
                                                                                        MetaCRMControle.exibBotConversa  &&  MetaCRMControle.metaCorreta }">
                <a4j:commandLink rendered="#{not MetaCRMControle.apresentarSMS}"
                                 styleClass="pure-button pure-button-small"
                                 value="Link para cadastrar cartão online"
                                 action="#{MetaCRMControle.selecionarLinkCadastroCartaoOnline}"
                                 reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>

                <a4j:commandLink status="none"
                                 oncomplete="#{MetaCRMControle.msgAlert}"
                                 rendered="#{MetaCRMControle.exibeMqv}"
                                 actionListener="#{MetaCRMControle.abrirMqvListener}"
                                 styleClass="pure-button-mvq  pure-button-small ">
                    Visualizar Relatório MQV
                </a4j:commandLink>

                <a4j:commandLink status="none"
                                 rendered="#{MetaCRMControle.exibBotConversa}"
                                 action="#{MetaCRMControle.dispararFluxo}"
                                 oncomplete="#{MetaCRMControle.msgAlert}"
                                 styleClass="pure-button-botconversa  pure-button-small "
                                 value="#{MetaCRMControle.nomeFluxoBotconversa}"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight">
                </a4j:commandLink>

                <a4j:commandLink id="gymBotPro"
                                 rendered="#{MetaCRMControle.exibGymbotPro}"
                                 action="#{MetaCRMControle.dispararFluxoGymbotPro}"
                                 styleClass="pure-button-gymbotpro pure-button-small "
                                 value="#{MetaCRMControle.nomeFluxoGymbotPro}"
                                 oncomplete="#{MetaCRMControle.mensagemNotificar}; ajustarScrollAlunoSelecionadoDireita()"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight">
                </a4j:commandLink>

            </h:panelGroup>


        </h:panelGrid>


        <%---------------------------------------- BOTCONVERSA GYMBOT----------------------------------------------------------%>
        <h:panelGrid id="panelBotconversa" columns="1" width="100%"
                     rendered="#{!MetaCRMControle.apresentarTelefoneCliente && MetaCRMControle.metaCorreta}">
            <h:panelGroup layout="block" style="padding-top: 10px" rendered="#{MetaCRMControle.exibBotConversa  &&  MetaCRMControle.metaCorreta }">

                <a4j:commandLink id="botconversa"
                                 rendered="#{MetaCRMControle.exibBotConversa}"
                                 action="#{MetaCRMControle.dispararFluxo}"
                                 styleClass="pure-button-botconversa  pure-button-small "
                                 value="#{MetaCRMControle.nomeFluxoBotconversa}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}; ajustarScrollAlunoSelecionadoDireita()"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight, nomeConsultor">

                </a4j:commandLink>

            </h:panelGroup>
        </h:panelGrid>

        <%---------------------------------------- GYMBOT PRO ----------------------------------------------------------%>
        <h:panelGrid id="panelGymbot" columns="2" width="100%"
                     rendered="#{!MetaCRMControle.apresentarTelefoneCliente && MetaCRMControle.metaCorreta}">
            <h:panelGroup layout="block" style="padding-top: 10px" rendered="#{MetaCRMControle.exibGymbotPro  &&  MetaCRMControle.metaCorreta}">

                <a4j:commandLink id="gymBotPro2"
                                 rendered="#{MetaCRMControle.exibGymbotPro}"
                                 action="#{MetaCRMControle.dispararFluxoGymbotPro}"
                                 styleClass="pure-button-gymbotpro pure-button-small "
                                 value="#{MetaCRMControle.nomeFluxoGymbotPro}"
                                 oncomplete="#{MetaCRMControle.mensagemNotificar}; ajustarScrollAlunoSelecionadoDireita()"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight">
                </a4j:commandLink>

            </h:panelGroup>
        </h:panelGrid>

        <%---------------------------------------- EMAILS ----------------------------------------------------------%>
        <h:panelGrid id="tabelaEmails" columns="1" width="100%"
                     rendered="#{MetaCRMControle.apresentarEmail}">

            <h:dataTable id="resultadoConsultaListaEmail" width="100%" headerClass="subordinado"
                         rowClasses="linhaImpar, linhaPar"
                         style="text-align: left; margin-top: 1%;"
                         value="#{MetaCRMControle.listaEmail}"
                         rendered="#{MetaCRMControle.clienteTemEmail}" var="email">

                <h:column>
                    <h:selectBooleanCheckbox id="selecionarEmail" style="margin: 10%;" value="#{email.escolhido}"/>
                </h:column>

                <h:column>
                    <h:outputText
                            style="font-size: 13px; font-weight: bold; font-family: arial, helvetica, sans-serif"
                            value="#{email.email}"/>
                </h:column>

            </h:dataTable>

            <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block"
                          rendered="#{!MetaCRMControle.clienteTemEmail}">
                <h:outputText
                        style="text-align: center; color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                        value="Cliente não possui e-mail cadastrado."/>
            </h:panelGroup>
        </h:panelGrid>


        <%---------------------------------------- APP ----------------------------------------------------------%>
        <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block"
                      rendered="#{MetaCRMControle.apresentarAPP and !MetaCRMControle.temUsuarioMovel}">
            <h:outputText
                    style="text-align: center; color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                    value="Esse cliente não faz uso do aplicativo."/>
        </h:panelGroup>

        <h:panelGrid id="panelMensagemApp" columns="1" width="100%"
                     rendered="#{MetaCRMControle.apresentarAPP and MetaCRMControle.temUsuarioMovel}">

            <h:panelGroup layout="block" style="padding-top: 15px;">
                <h:selectOneMenu id="tipoMensagemApp" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 style="background: #F6F6F6; font-size: 14px; float: left; width: 60%"
                                 value="#{MetaCRMControle.tipoMensagemApp}">
                    <f:selectItems value="#{MetaCRMControle.listaSelectTiposPerguntas}"/>
                    <a4j:support event="onchange" action="#{MetaCRMControle.alterarTipoPerguntaAPP}"
                                 oncomplete="adicionarPlaceHolderCRM()" reRender="panelGridRight"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-top: 10px">
                <h:inputText id="tituloMensagemAPP" maxlength="50"
                             style="background: #F6F6F6; width: 60%"
                             value="#{MetaCRMControle.malaDiretaVO.titulo}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="padding-top: 10px" rendered="#{MetaCRMControle.tipoMensagemApp == 2}">
                <a4j:commandLink rendered="#{!MetaCRMControle.apresentarEmail}"
                                 styleClass="pure-button pure-button-small"
                                 value="Link para cadastrar cartão online"
                                 action="#{MetaCRMControle.selecionarLinkCadastroCartaoOnline}"
                                 reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>

                <a4j:commandLink styleClass="pure-button pure-button-small"
                                 value="Usar Modelo de Mensagem"
                                 style="margin-left: 2%"
                                 action="#{MetaCRMControle.apresentarModeloMensagem}"
                                 oncomplete="adicionarPlaceHolderCRM();"
                                 reRender="panelGridRight"/>
            </h:panelGroup>

            <%--USAR MODELO DE MENSAGEM APP--%>
            <h:panelGrid columns="2" width="100%"
                         rendered="#{MetaCRMControle.mostrarModeloDeMensagem}">
                <h:panelGroup
                        style="padding-top: 5px; padding-bottom: 10px"
                        layout="block">
                    <h:selectOneMenu id="modeloMensagemAPP" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; padding: 3px;"
                                     value="#{MetaCRMControle.malaDiretaVO.modeloMensagem.codigo}">
                        <f:selectItems value="#{MetaCRMControle.listaModeloMensagem}"/>
                        <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModeloMensagem}"
                                     reRender="mensagemAPP" oncomplete="adicionarPlaceHolderCRM();"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" style="padding-top: 10px;">
                <h:inputTextarea id="mensagemAPP" onkeypress="somaApp(this.value);" onkeyup="somaApp(this.value);"
                                 style="width: 100%" rows="3"
                                 value="#{MetaCRMControle.historicoContatoVO.observacao}"/>
            </h:panelGroup>

            <h:panelGroup style="text-align: center;" layout="block">
                <h:inputText disabled="true" size="3" title="Caracteres restantes"
                             style="text-align: center; color: #000000" id="tamanhoMensagemApp"/>
            </h:panelGroup>


            <%--RESPOSTA 01--%>
            <h:panelGroup layout="block" style="padding-top: 10px;" rendered="#{MetaCRMControle.botoesApp}">
                <h:outputText style="padding-right: 10px;" value="Resposta 01"/>
                <h:inputText maxlength="7"
                             style="text-transform: uppercase; background: #F6F6F6; width: 50%"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.campos.campo1}"/>
            </h:panelGroup>

            <%--RESPOSTA 02--%>
            <h:panelGroup layout="block" style="padding-top: 10px;" rendered="#{MetaCRMControle.botoesApp}">
                <h:outputText style="padding-right: 10px; padding-top: 1%; float: left" value="Resposta 02"/>
                <h:inputText maxlength="7"
                             style="text-transform: uppercase; background: #F6F6F6; width: 50%; float: left"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             value="#{MetaCRMControle.campos.campo2}"/>
                                        <span onclick="mostrarProximo(2);"
                                              style="cursor: pointer;display: block; padding-top: 1%;"
                                              class="btnOp2 btnsOptAPP">
                                            <i class="fa-icon-plus" style="color: #555; padding-top: 1%;"></i>
                                        </span>
            </h:panelGroup>

            <%--RESPOSTA 03--%>
            <h:panelGroup layout="block" style="padding-top: 10px;" rendered="#{MetaCRMControle.botoesApp}">
                <h:outputText styleClass="labelOp3" value="Resposta 03"
                              style="display: none; float: left; padding-right: 10px; padding-top: 1%"/>
                <h:inputText maxlength="7"
                             style="display: none; text-transform: uppercase; background: #F6F6F6; width: 50%; float: left"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="labelOp3"
                             value="#{MetaCRMControle.campos.campo3}"/>
                <div class="btnOp3 btnsOptAPP">
                                            <span onclick="sumirItem(3);"
                                                  style="cursor: pointer; padding-top: 1%;">
                                                <i class="fa-icon-remove" style="color: #555; padding-top: 1%;"></i>
                                            </span>
                </div>
            </h:panelGroup>

            <%--BOTAO ENVIAR APP--%>
            <h:panelGroup style="width: 100%; padding-top: 10px; padding-bottom: 10px;  float: left"
                          layout="block">
                <a4j:commandLink styleClass="pure-button pure-button-primary"
                                 value="Enviar"
                                 action="#{MetaCRMControle.gravarEnviandoContatoAPP}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>

                <a4j:commandLink id="usarComoModeloAPP"
                                 rendered="#{MetaCRMControle.tipoMensagemApp == 2 && MetaCRMControle.codigoModeloMensagemCriado == 0}"
                                 styleClass="pure-button pure-button-small"
                                 style="float: right"
                                 value="Utilizar como Modelo de Mensagem"
                                 action="#{MetaCRMControle.confirmarCriarModeloMensagemAPP}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
            </h:panelGroup>

        </h:panelGrid>


        <%---------------------------------------- ENVIAR EMAIL ----------------------------------------------------------%>
        <h:panelGrid id="panelEnviarEmail" columns="1" width="100%"
                     rendered="#{MetaCRMControle.apresentarEmail && MetaCRMControle.clienteTemEmail}">


            <h:panelGroup layout="block" style="padding-bottom: 1%; padding-top: 1%;">

                <a4j:commandLink id="novoEmail"
                                 styleClass="pure-button pure-button-small"
                                 value="Criar Novo E-mail"
                                 action="#{MetaCRMControle.criarNovoEmail}"
                                 oncomplete="adicionarPlaceHolderCRM();"
                                 reRender="panelEnviarEmail"/>

                <a4j:commandLink styleClass="pure-button pure-button-small"
                                 value="Usar Modelo de Mensagem"
                                 style="margin-left: 2%"
                                 action="#{MetaCRMControle.apresentarModeloMensagemEmail}"
                                 oncomplete="adicionarPlaceHolderCRM();"
                                 reRender="panelEnviarEmail"/>
            </h:panelGroup>

            <%--USAR MODELO DE MENSAGEM EMAIL--%>
            <h:panelGroup layout="block" style="padding-top: 5px; padding-bottom: 10px"
                          rendered="#{MetaCRMControle.mostrarModeloDeMensagem}">
                <h:selectOneMenu id="modeloMensagemEmail" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 style="background: #F6F6F6; color: #333333; padding: 3px;"
                                 value="#{MetaCRMControle.malaDiretaVO.modeloMensagem.codigo}">
                    <f:selectItems value="#{MetaCRMControle.listaModeloMensagem}"/>
                    <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModeloMensagemEmail}"
                                 reRender="panelEnviarEmail" oncomplete="adicionarPlaceHolderCRM();"/>
                </h:selectOneMenu>
            </h:panelGroup>


            <h:panelGroup layout="block" rendered="#{MetaCRMControle.apresentarPanelEnviarEmail}">
                <h:panelGroup layout="block" style="padding-bottom: 5px">
                    <h:inputText id="remetenteEmail" maxlength="50"
                                 style="background: #F6F6F6; width: 60%" disabled="true"
                                 value="REMETENTE - #{MetaCRMControle.malaDiretaVO.remetente.nome}"/>
                </h:panelGroup>

                <h:panelGroup layout="block" style="padding-bottom: 5px">
                    <h:inputText id="tituloEmail" maxlength="100"
                                 style="background: #F6F6F6; width: 60%"
                                 value="#{MetaCRMControle.malaDiretaVO.titulo}"
                                 title="#{msg.msg_tip_tituloMail}#{MetaCRMControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}"/>

                </h:panelGroup>

                <h:panelGroup layout="block" style="padding-bottom: 5px">
                    <a4j:commandLink id="tagEmail"
                                     styleClass="pure-button pure-button-small"
                                     value="Adicionar Tag Empresa"
                                     oncomplete="Richfaces.showModalPanel('panelEmail');adicionarPlaceHolderCRM();"
                                     reRender="formMarcadorEmail"/>

                    <a4j:commandLink
                            styleClass="pure-button pure-button-small"
                            value="Link para cadastrar cartão online"
                            style="margin-left: 2%"
                            action="#{MetaCRMControle.selecionarLinkCadastroCartaoOnline}"
                            reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>

                </h:panelGroup>

                <h:panelGroup layout="block">
                    <rich:editor id="mensagemEmail"
                                 configuration="editorpropriedades" viewMode="visual" theme="advanced" height="400"
                                 value="#{MetaCRMControle.malaDiretaVO.mensagem}">
                        <f:param name="width" value="100%"/>
                    </rich:editor>
                </h:panelGroup>

                <h:panelGroup style="padding-top: 10px; padding-bottom: 10px;  float: left; width: 100%"
                              layout="block">
                    <a4j:commandLink styleClass="pure-button pure-button-primary"
                                     id="gravarEnviarEmail"
                                     value="Enviar Email"
                                     action="#{MetaCRMControle.gravarEnviandoEmail}"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>

                    <a4j:commandLink id="usarComoModeloEmail"
                                     rendered="#{MetaCRMControle.codigoModeloMensagemCriado == 0}"
                                     styleClass="pure-button pure-button-small"
                                     style="float: right"
                                     value="Utilizar como Modelo de Mensagem"
                                     action="#{MetaCRMControle.confirmarCriarModeloMensagemEmail}"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                </h:panelGroup>

            </h:panelGroup>

        </h:panelGrid>


        <%---------------------------------------- ENVIAR SMS ----------------------------------------------------------%>
        <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block"
                      rendered="#{MetaCRMControle.apresentarSMS && MetaCRMControle.empresaSemTokem}">
            <h:outputText
                    style="text-align: center; color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                    value="Sua empresa não possui o pacote de SMS."/>
        </h:panelGroup>

        <h:panelGrid id="panelSMS" columns="1" width="100%"
                     rendered="#{MetaCRMControle.apresentarSMS && !MetaCRMControle.empresaSemTokem}">

            <h:panelGroup layout="block" style="padding-top: 10px">
                <a4j:commandLink
                        styleClass="pure-button pure-button-small"
                        value="Link para cadastrar cartão online"
                        action="#{MetaCRMControle.selecionarLinkCadastroCartaoOnline}"
                        reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>


                <a4j:commandLink styleClass="pure-button pure-button-small"
                                 value="Usar Modelo de Mensagem"
                                 action="#{MetaCRMControle.apresentarModeloMensagem}" ajaxSingle="true"
                                 style="margin-left: 2%"
                                 reRender="panelGridRight"
                                 oncomplete="adicionarPlaceHolderCRM();"/>

                <a4j:commandLink styleClass="pure-button pure-button-small"
                                 style="float: right"
                                 value="Tag Primeiro Nome"
                                 action="#{MetaCRMControle.incluirTagPNomeSMS}"
                                 reRender="mensagemSMS"
                                 oncomplete="adicionarPlaceHolderCRM();"/>

                <a4j:commandLink styleClass="pure-button pure-button-small"
                                 style="float: right; margin-right: 10px"
                                 value="Tag Nome"
                                 action="#{MetaCRMControle.incluirTagNomeSMS}"
                                 reRender="mensagemSMS"
                                 oncomplete="adicionarPlaceHolderCRM();"/>


            </h:panelGroup>

            <%--USAR MODELO DE MENSAGEM SMS--%>
            <h:panelGrid columns="2" width="100%"
                         rendered="#{MetaCRMControle.mostrarModeloDeMensagem}">
                <h:panelGroup
                        style="padding-top: 5px; padding-bottom: 10px"
                        layout="block">

                    <h:selectOneMenu id="modeloMensagemSMS" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; padding: 3px;"
                                     value="#{MetaCRMControle.malaDiretaVO.modeloMensagem.codigo}">
                        <f:selectItems value="#{MetaCRMControle.listaModeloMensagem}"/>
                        <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModeloMensagem}"
                                     reRender="mensagemSMS" oncomplete="adicionarPlaceHolderCRM();"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" style="padding-top: 10px">
                <h:inputTextarea id="mensagemSMS" onkeypress="somaCRM(this.value);" onkeyup="somaCRM(this.value);"
                                 style="width: 100%" cols="100" rows="3"
                                 value="#{MetaCRMControle.historicoContatoVO.observacao}"/>

                <rich:toolTip for="mensagemSMS" followMouse="true"
                              rendered="#{MetaCRMControle.toolTipSMS and !MetaCRMControle.apresentarAPP}"
                              direction="top-right" style="width:300px; height:#{MetaCRMControle.tamanhoToolTipSMS}; "
                              showDelay="200">
                    <h:outputText styleClass="tituloCampos" escape="false"
                                  value="#{MetaCRMControle.termosFiscalizados}"/>
                </rich:toolTip>
            </h:panelGroup>

            <h:panelGroup style="text-align: center;" layout="block">
                <c:if test="${MetaCRMControle.apresentarSMS}">
                    <h:inputText disabled="true" size="3" title="Caracteres restantes"
                                 style="text-align: center; color: #000000" id="tamanhoMensagemSMS"/>
                </c:if>
            </h:panelGroup>

            <h:panelGroup style="width: 100%; padding-top: 10px; padding-bottom: 10px;  float: left"
                          layout="block">
                <a4j:commandLink styleClass="pure-button pure-button-primary"
                                 id="gravarEnviandoSMS"
                                 value="Enviar SMS"
                                 action="#{MetaCRMControle.gravarEnviandoSMS}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>

                <a4j:commandLink id="usarComoModeloSMS"
                                 rendered="#{MetaCRMControle.codigoModeloMensagemCriado == 0}"
                                 styleClass="pure-button pure-button-small"
                                 style="float: right"
                                 value="Utilizar como Modelo de Mensagem"
                                 action="#{MetaCRMControle.confirmarCriarModeloMensagemSMS}"
                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                 reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
            </h:panelGroup>

        </h:panelGrid>


        <h:panelGroup id="panelOperacoes" style="padding-top: 10px" layout="block"
                      rendered="#{MetaCRMControle.apresentarContatoPessoalTelefonico}">

            <h:inputTextarea id="observacaoHistorico"
                             rendered="#{!MetaCRMControle.mostrarPanelReagendamento && MetaCRMControle.metaCorreta}"
                             style="width: 100%; margin-bottom: 5px;" rows="5"
                             value="#{MetaCRMControle.historicoContatoVO.observacao}"/>

            <%--BOTÕES--%>
            <h:panelGrid columns="4" width="100%" rendered="#{MetaCRMControle.metaCorreta}">
                <h:panelGroup style="float: left" rendered="#{!MetaCRMControle.mostrarPanelReagendamento}">

                    <%--AGENDAR--%>
                    <a4j:commandLink styleClass="pure-button pure-button-primary"
                                     rendered="#{!MetaCRMControle.apresentarBotaoReagendarAgendamento}"
                                     value="Agendar" action="#{MetaCRMControle.apresentarAgendar}"
                                     reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>

                    <rich:spacer width="10px" rendered="#{!MetaCRMControle.apresentarBotaoReagendarAgendamento}"/>

                    <%--OBJEÇÃO--%>
                    <a4j:commandLink styleClass="pure-button"
                                     id="apresentarObjecao"
                                     value="Objeção"
                                     action="#{MetaCRMControle.apresentarObjecao}"
                                     reRender="panelGridRight"/>

                    <rich:spacer width="10px"/>

                    <%--SIMPLES REGISTRO--%>
                    <a4j:commandLink styleClass="pure-button"
                                     value="Simples Registro"
                                     id="simplesRegistro"
                                     action="#{MetaCRMControle.confirmarSimplesRegistroMeta}"
                                     oncomplete="#{MetaCRMControle.mensagemNotificar}; ajustarScrollAlunoSelecionadoDireita()"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>

                    <rich:spacer width="10px"/>

                </h:panelGroup>

            </h:panelGrid>

            <%--LISTA DE OBJEÇÕES--%>
            <h:panelGroup layout="block" styleClass="triangleCSS" style="float: left; margin-left: 20%;"
                          rendered="#{MetaCRMControle.mostrarPanelObjecao}"/>
            <h:panelGrid id="panelObjecao" columns="1" width="100%" style="text-align: center; background: #e6e6e6"
                         rendered="#{MetaCRMControle.mostrarPanelObjecao}">

                <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                    <h:panelGroup
                            style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                            layout="block">
                        <h:outputText
                                style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                value="Tipo de Objeção"/>
                    </h:panelGroup>
                    <h:panelGroup
                            style="padding-top: 10px; padding-bottom: 10px"
                            layout="block">
                        <h:selectOneMenu id="objecao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         style="background: #F6F6F6; color: #333333; font-size: 14px;"
                                         value="#{MetaCRMControle.historicoContatoVO.objecaoVO.codigo}">
                            <f:selectItems value="#{MetaCRMControle.listaObjecao}"/>
                            <a4j:support event="onchange" action="#{MetaCRMControle.alterarObjecao}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                    <h:panelGroup style="padding: 5px" layout="block">
                        <a4j:commandLink
                                id="concluirObjecao"
                                styleClass="pure-button pure-button-primary"
                                value="Concluir"
                                action="#{MetaCRMControle.gravarObjecaoMeta}"
                                oncomplete="#{MetaCRMControle.modalMensagemGenerica};ajustarScrollAlunoSelecionadoDireita()"
                                reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>

            <%--AGENDAR--%>
            <h:panelGroup id="panelAgendarSeta" layout="block" styleClass="triangleCSS"
                          style="float: left; margin-left: 5%;"
                          rendered="#{MetaCRMControle.mostrarPanelAgendar}"/>
            <h:panelGrid id="panelAgendar" columns="1" width="100%" style="text-align: center; background: #e6e6e6"
                         rendered="#{MetaCRMControle.mostrarPanelAgendar}">

                <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                    <h:panelGroup
                            style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                            layout="block">
                        <h:outputText
                                style="float: left; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                value="Tipo de Agendamento"/>
                        <rich:spacer width="10px" style="float: left"/>
                        <h:selectOneRadio id="agendamento" onblur="blurinput(this);" onfocus="focusinput(this);"
                                          style="float: left; background: #e6e6e6; color: #333333; font-size: 14px;"
                                          value="#{MetaCRMControle.agendaVO.tipoAgendamento}">
                            <f:selectItems value="#{MetaCRMControle.listaSelectItemTipoAgendamentoAgenda}"/>
                            <a4j:support event="onclick" reRender="panelAgendar"
                                         oncomplete="adicionarPlaceHolderCRM();"/>
                        </h:selectOneRadio>
                    </h:panelGroup>
                </h:panelGrid>
                <br/>
                <rich:spacer width="10px" style="float: left"/>

                <h:panelGrid id="selecionarConsultor" columns="2" width="100%" style="background: #e6e6e6;" rendered="#{LoginControle.permissaoAcessoMenuVO.selecionarColaboradorMetas}"
                             styleClass="tabFormSubordinada">
                    <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px" layout="block">
                        <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold" value="Colaboradores"/>

                        <rich:spacer width="10px"/>

                        <h:inputText  id="nomeConsultor"
                                      style="width: 320px; font-size: 12px !important;"
                                      styleClass="campos"
                                      size="50"
                                      maxlength="50"
                                      onfocus="toggleSuggestions ( #{rich:component('suggestionConsultor')} )"
                                      value="#{MetaCRMControle.nomeUsuario}"/>

                        <rich:suggestionbox for="nomeConsultor"
                                            width="320"
                                            fetchValue="#{result.nome}"
                                            status="statusInComponent"
                                            style="background: #F6F6F6; color: #333333; padding: 3px;"
                                            suggestionAction="#{MetaCRMControle.executarAutocompleteCol}"
                                            minChars="1"
                                            rowClasses="linhaImpar, linhaPar"
                                            ajaxSingle="false"
                                            var="result"
                                            id="suggestionConsultor">
                            <a4j:support event="onselect"
                                         focus="telasCRM"
                                         oncomplete="#{MetaCRMControle.mensagemNotificar}"
                                         action="#{MetaCRMControle.selecionarUsuario}"/>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Nome" styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText value="#{result.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                        <script>
                            function toggleSuggestions(suggestionBox) {
                                if (suggestionBox.active)
                                    suggestionBox.hide();
                                else
                                    suggestionBox.callSuggestion(true);
                            }
                        </script>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                             rendered="#{MetaCRMControle.apresentarInputTextAulaExperimental}">
                    <h:panelGroup
                            style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                            layout="block">
                        <h:outputText
                                style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                value="Modalidade"/>
                        <rich:spacer width="10px"/>
                        <h:selectOneMenu id="modalidadeAgenda" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="background: #F6F6F6; color: #333333; padding: 3px;"
                                         value="#{MetaCRMControle.agendaVO.modalidade.codigo}">
                            <f:selectItems value="#{MetaCRMControle.listaModalidade}"/>
                            <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModalidade}"/>
                        </h:selectOneMenu>

                        <%--<h:inputText id="textModalidade" size="50"--%>
                        <%--onblur="blurinput(this);"--%>
                        <%--onfocus="focusinput(this);"--%>
                        <%--styleClass="form"--%>
                        <%--value="#{MetaCRMControle.agendaVO.modalidade.nome}"/>--%>

                        <%--<rich:suggestionbox id="suggestionModalidade"--%>
                        <%--height="200" width="300"--%>
                        <%--for="textModalidade"--%>
                        <%--status="statusInComponent"--%>
                        <%--immediate="true"--%>
                        <%--suggestionAction="#{MetaCRMControle.autocompleteModalidade}"--%>
                        <%--nothingLabel="Nenhuma Modalidade encontrada !" var="modalidade">--%>
                        <%--<a4j:support event="onselect"--%>
                        <%--action="#{MetaCRMControle.selecionarModalidade}"/>--%>
                        <%--<h:column>--%>
                        <%--<h:outputText value="#{modalidade.nome}"/>--%>
                        <%--</h:column>--%>
                        <%--</rich:suggestionbox>--%>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                             rendered="#{MetaCRMControle.apresentarInputTextAulaExperimental}">
                    <h:panelGroup id="selTipoColaborador" layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                        <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                value="Tipo Professor"/>
                        <rich:spacer width="10px"/>
                        <h:selectOneMenu id="selTipoColab" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="background: #F6F6F6; color: #333333; padding: 3px;"
                                         value="#{MetaCRMControle.tipoProfessor}">
                            <f:selectItems value="#{MetaCRMControle.listaTiposProfessores}"/>
                            <a4j:support event="onchange" actionListener="#{MetaCRMControle.alterarTipoColaborador}"
                                         reRender="selColaborador, infoColaborador, selTipoColaborador">
                            </a4j:support>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                             rendered="#{MetaCRMControle.apresentarInputTextAulaExperimental}">
                    <h:panelGroup id="infoColaborador">
                        <h:panelGroup id="selColaborador" rendered="#{MetaCRMControle.mostrarSeletorNome}"
                                      layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                            <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                          value="Professor"/>
                            <rich:spacer width="10px"/>
                            <h:selectOneMenu id="selColab" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             style="background: #F6F6F6; color: #333333; padding: 3px;"
                                             value="#{MetaCRMControle.codigoProfessor}">
                                <f:selectItems value="#{MetaCRMControle.listaColaboradores}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                    <h:panelGroup
                            style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                            layout="block">
                        <h:outputText
                                style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                value="Agendar para"/>
                        <rich:spacer width="10px"/>
                        <rich:calendar id="dataAgendamento"
                                       value="#{MetaCRMControle.agendaVO.dataAgendamento}"
                                       inputSize="10"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <rich:spacer style="width: 10px"/>
                        <h:inputText id="horaMinutoAgendamento"
                                     onkeypress="return dois_pontos(this);"
                                     size="10" maxlength="5" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" value="#{MetaCRMControle.agendaVO.horaMinuto}"/>
                        <rich:spacer style="width: 10px"/>
                        <a4j:commandLink
                                id="buscarAulas"
                                rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                                MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}"
                                style="line-height: 20px"
                                styleClass="botoes nvoBt btSec"
                                value="Buscar aulas"
                                action="#{MetaCRMControle.montarAulasAgenda}"
                                oncomplete="#{MetaCRMControle.mensagemNotificar}"
                                reRender="mensagens, panelAgendar"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                             rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.direcionaragendamentosexperimentaisagenda and
                             MetaCRMControle.apresentarInputTextAulaExperimental and MetaCRMControle.metaLead}">
                    <h:panelGroup id="infoAulas">
                        <h:panelGroup id="selAulas" rendered="#{MetaCRMControle.listaAulasAgenda.size() > 1}"
                                      layout="block" style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px">
                            <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                          value="Aula"/>
                            <rich:spacer width="10px"/>
                            <h:selectOneMenu id="selAula" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             style="background: #F6F6F6; color: #333333; padding: 3px;"
                                             value="#{MetaCRMControle.codigoAula}">
                                <f:selectItems value="#{MetaCRMControle.listaAulasAgenda}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                    <h:panelGroup style="padding: 5px" layout="block">
                        <a4j:commandLink
                                id="concluirAgenda"
                                styleClass="pure-button pure-button-primary"
                                value="Concluir"
                                action="#{MetaCRMControle.gravarAgendaMeta}"
                                oncomplete="#{MetaCRMControle.modalMensagemGenerica}; ajustarScrollAlunoSelecionadoDireita()"
                                reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight, nomeConsultor"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGroup>


        <%-- ABAS HISTÓRICO DE CONTATOS E HISTORICO DE OBJEÇÕES --%>
        <h:panelGroup id="panelGroupRichPanel" style="padding-top: 8px" layout="block">
            <rich:tabPanel id="richPanel" width="100%" tabClass="aba" switchType="ajax" headerAlignment="left"
                           selectedTab="abaHistContato">
                <rich:tab id="abaHistContato" label="Histórico de Contatos">
                    <rich:dataTable id="tableHistContato" width="100%" headerClass="subordinado"
                                    rowClasses="linhaImpar, linhaPar"
                                    rows="#{MetaCRMControle.qtdRegistrosHistoricoContato}"
                                    value="#{MetaCRMControle.listaHistoricoContatoCliente}"
                                    var="contatos">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="#{msg_aplic.prt_Agenda_dataHora}"/>
                            </f:facet>
                            <h:outputText value="#{contatos.dia_Apresentar}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="#{msg_aplic.prt_Agenda_fase}"/>
                            </f:facet>
                            <h:outputText value="#{contatos.fase_Apresentar}"/>
                            <h:outputText rendered="#{contatos.contatoAvulso}" value=" - avulso"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="#{msg_aplic.prt_Agenda_resultado}"/>
                            </f:facet>
                            <h:outputText rendered="#{contatos.objecaoVO.codigo != 0}"
                                          style="color: red" value="#{contatos.resultado}"/>
                            <h:outputText rendered="#{contatos.objecaoVO.codigo == 0}"
                                          value="#{contatos.resultado}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="Usuário Resp. Cadastro"/>
                            </f:facet>
                            <h:outputText
                                    value="#{contatos.responsavelCadastro.nome}"/>
                        </rich:column>

                        <rich:column style="display:flex; justify-content: center" >
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888;"
                                              value="Opções" />
                            </f:facet>
                            <a4j:commandLink rendered="#{contatos.resultado == 'Simples Registro'}"
                                             oncomplete="#{MetaCRMControle.onCompleteEdicaoSimplesRegistro}"
                                             title="Editar Simples Registro"
                                             reRender="form:panelGridRight, form:panelGridCenter, mdlEditarSimplesRegistro"
                                             action="#{MetaCRMControle.prepararEdicaoSimplesRegistro}" style="text-decoration: none">
                                <i class="fa-icon-edit" style="font-size: 18px;"></i>
                            </a4j:commandLink>
                        </rich:column>

                        <rich:column style="padding-top: 5px" colspan="5" width="100%" breakBefore="true">
                            <h:inputTextarea styleClass="textareaResize" rendered="#{contatos.malaDiretaVO.codigo == 0}" value="#{contatos.observacao_Exportar}" readonly="true" rows="5"/>

                            <h:outputText rendered="#{contatos.app}" value="<br/>Resposta: #{contatos.resposta}"
                                          escape="false"/>
                            <a4j:commandLink rendered="#{contatos.malaDiretaVO.codigo != 0}"
                                             value="Abrir Mailing: #{contatos.malaDiretaVO.codigo}"
                                             action="#{MetaCRMControle.consultarMailing}"
                                             oncomplete="#{MetaCRMControle.onComplete}"/>
                        </rich:column>

                    </rich:dataTable>
                    <hr>
                    <a4j:commandLink id="log"
                                     rendered="#{LoginControle.permissaoAcessoMenuVO.crmEditarSimplesRegistro}"
                                     action="#{MetaCRMControle.realizarConsultaLogObjetoSelecionado}"
                                     oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                     title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                     style="display: inline-block; padding: 8px 15px;">
                        <i class="fa-icon-list"></i>
                    </a4j:commandLink>
                </rich:tab>

                <rich:tab id="abaHistObjecao" label="Histórico de Objeções">
                    <rich:dataTable id="tableHistObjecoes" width="100%" headerClass="subordinado"
                                    rowClasses="linhaImpar, linhaPar"
                                    rows="#{MetaCRMControle.qtdRegistrosHistoricoContato}"
                                    value="#{MetaCRMControle.listaHistoricoObjecoesCliente}"
                                    var="objecoes">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="#{msg_aplic.prt_Agenda_dataHora}"/>
                            </f:facet>
                            <h:outputText value="#{objecoes.dia_Apresentar}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="#{msg_aplic.prt_Agenda_fase}"/>
                            </f:facet>
                            <h:outputText value="#{objecoes.fase_Apresentar}"/>
                            <h:outputText rendered="#{objecoes.contatoAvulso}" value=" - avulso"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="#{msg_aplic.prt_Agenda_resultado}"/>
                            </f:facet>
                            <h:outputText rendered="#{objecoes.objecaoVO.codigo != 0}"
                                          style="color: red" value="#{objecoes.resultado}"/>
                            <h:outputText rendered="#{objecoes.objecaoVO.codigo == 0}"
                                          value="#{objecoes.resultado}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                              value="#{msg_aplic.prt_Agenda_usuario}"/>
                            </f:facet>
                            <h:outputText
                                    value="#{objecoes.responsavelCadastro.nome}"/>
                        </rich:column>

                        <rich:column style="padding-top: 8px" colspan="4" width="100%" breakBefore="true">
                            <h:inputTextarea styleClass="textareaResize" value="#{objecoes.observacao_Exportar}" readonly="true" rows="5"/>

                            <h:outputText rendered="#{objecoes.app}" value="<br/>Resposta: #{objecoes.resposta}"
                                          escape="false"/>
                        </rich:column>

                    </rich:dataTable>
                </rich:tab>
            </rich:tabPanel>
            <h:panelGroup id="verMaisHistoricoContato" layout="block">
                <a4j:commandLink
                        id="verTodoHistoricoContato"
                        style="width: 96.5%; text-align: left; font-family: arial, helvetica, sans-serif"
                        styleClass="pure-button pure-button-small "
                        action="#{MetaCRMControle.mostrarTodoHistoricoContato}"
                        reRender="tableHistContato, tableHistObjecoes, verMaisHistoricoContato"
                        value="Ver todo histórico de contatos">
                    <h:panelGroup layout="block"
                                  rendered="#{empty MetaCRMControle.qtdRegistrosHistoricoContato}">
                        <i class="fa-icon-minus" style="float: right"></i>
                    </h:panelGroup>
                    <h:panelGroup layout="block"
                                  rendered="#{not empty MetaCRMControle.qtdRegistrosHistoricoContato}">
                        <i class="fa-icon-plus" style="float: right"></i>
                    </h:panelGroup>
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGroup>

    </h:panelGroup>


    <h:panelGrid id="panelEmailSMSColetivo" columns="1" width="100%"
                 rendered="#{MetaCRMControle.mostrarPanelEmailSMSColetivo}">

        <h:panelGroup style="text-align: center; padding-top: 20px; padding-bottom: 10px; background: #e6e6e6;"
                      layout="block">
            <h:outputText
                    style="font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                    value="#{MetaCRMControle.malaDiretaVO.meioDeEnvioEnum.descricao} Coletivo Mailing"/>
        </h:panelGroup>


        <h:panelGrid columns="2">
            <%--PESSOAS QUE NÃO FORAM ADICIONADAS NA LISTA--%>
            <h:panelGroup id="pessoasNaoAdicionadas">
                <h:outputText styleClass="tituloCampos"
                              style="color: red"
                              escape="false"
                              value="#{MetaCRMControle.pessoasSemEmailTelefone}"/>
            </h:panelGroup>

            <h:panelGrid columns="1">
                <h:panelGroup id="totalLista" style="float: right; margin-right: 10px;">
                    <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                  value="#{msg_aplic.prt_AberturaMeta_totalizadorListaMailing}:"/>
                    <rich:spacer width="10px;"/>
                    <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                  value="#{MetaCRMControle.malaDiretaVO.totalPessoaMalaDireta}"/>
                </h:panelGroup>
                <h:panelGroup style="float: right; margin-right: 10px;">
                    <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                  value="#{msg_aplic.prt_AberturaMeta_totalizadorEnviados}:"/>
                    <rich:spacer width="10px;"/>
                    <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                  value="#{MetaCRMControle.malaDiretaVO.totalPessoaMalaDiretaEnviada}"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

        <rich:tabPanel width="100%" switchType="client">
            <rich:tab id="dadosEmail" label="Dados Envio" switchType="client">
                <h:panelGrid columns="2"
                             width="100%">
                    <h:outputText styleClass="tituloCampos" value="Meio de Envio:"/>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{MetaCRMControle.malaDiretaVO.meioDeEnvioEnum.descricao}"/>
                    <h:outputText styleClass="tituloCampos" value="Data Envio:"/>
                    <h:inputText id="dataEnvio"
                                 onkeypress="return mascara(this.formF, 'formF:dataEnvio', '99/99/9999', event);"
                                 readonly="true" size="10" maxlength="10" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{MetaCRMControle.malaDiretaVO.dataEnvio}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:inputText>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_remetente}"/>
                    <h:panelGroup id="panelResponsavelGrupo">
                        <h:inputText id="textColaboradorResponsavel" size="50" readonly="true"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{MetaCRMControle.malaDiretaVO.remetente.nome}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="modeloMensagem" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         style="background: #F6F6F6; color: #333333; padding: 3px;"
                                         value="#{MetaCRMControle.malaDiretaVO.modeloMensagem.codigo}">
                            <f:selectItems value="#{MetaCRMControle.listaModeloMensagemColetivo}"/>
                            <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModeloMensagemColetivo}"
                                         reRender="textComentario, mensagemEmailColetivo"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 2}"
                            styleClass="tituloCampos" value="Adicionar Tag:"/>
                    <h:panelGroup rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 2}">
                        <a4j:commandLink styleClass="pure-button pure-button-small"
                                         value="Tag Primeiro Nome"
                                         action="#{MetaCRMControle.incluirTagPNomeColetivoSMS}"
                                         reRender="textComentario"
                                         oncomplete="adicionarPlaceHolderCRM();"/>

                        <a4j:commandLink styleClass="pure-button pure-button-small"
                                         style="margin-left: 10px"
                                         value="Tag Nome"
                                         action="#{MetaCRMControle.incluirTagNomeColetivoSMS}"
                                         reRender="textComentario"
                                         oncomplete="adicionarPlaceHolderCRM();"/>

                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_titulo}"/>
                    <h:panelGroup>
                        <h:inputText id="titulo" size="70" maxlength="100" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{MetaCRMControle.malaDiretaVO.titulo}"/>
                        <rich:toolTip for="titulo" followMouse="true" direction="top-right"
                                      style="width:300px; height:#{MetaCRMControle.tamanhoToolTip}; "
                                      showDelay="200">
                            <h:outputText styleClass="tituloCampos" escape="false"
                                          value="#{msg.msg_tip_tituloMail}#{MetaCRMControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}"/>
                        </rich:toolTip>
                    </h:panelGroup>
                </h:panelGrid>

                <%--ENVIO EMAIL--%>
                <h:panelGrid id="mensagemEmailColetivo" rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 1}"
                             columns="1" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_mensagem}"/>
                    <rich:editor configuration="editorpropriedades" viewMode="visual" theme="advanced"
                                 id="imputMensagem" height="500"
                                 value="#{MetaCRMControle.malaDiretaVO.mensagem}">
                        <f:param name="width" value="100%"/>
                    </rich:editor>
                </h:panelGrid>


                <h:panelGrid columns="1" rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 2}"
                             id="textComentario"
                             width="100%">
                    <h:inputTextarea
                            onkeypress="somaCRMColetivo(this.value);" onkeyup="somaCRMColetivo(this.value);"
                            style="width: 100%" id="mensagemSMSColetivo" cols="100" rows="3"
                            value="#{MetaCRMControle.malaDiretaVO.mensagem}"/>

                    <rich:toolTip for="mensagemSMSColetivo" followMouse="true"
                                  rendered="#{MetaCRMControle.toolTipSMS}"
                                  direction="top-right"
                                  style="width:300px; height:#{MetaCRMControle.tamanhoToolTipSMS}; "
                                  showDelay="200">
                        <h:outputText styleClass="tituloCampos" escape="false"
                                      value="#{MetaCRMControle.termosFiscalizados}"/>
                    </rich:toolTip>

                    <h:panelGroup id="panelTamanhoRestante" style="text-align: center;" layout="block"
                                  rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 2}">
                        <h:inputText disabled="true" size="3" title="Caracteres restantes"
                                     style="text-align: center; color: #000000" id="tamanhoRestanteSMSColetivo"/>

                    </h:panelGroup>

                </h:panelGrid>
            </rich:tab>

            <rich:tab id="emailsEnviados" label="Enviar Para" switchType="client">
                <rich:dataTable id="malaDiretaEnviadaVO" width="100%" headerClass="subordinado"
                                styleClass="tabFormSubordinada"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                value="#{MetaCRMControle.malaDiretaVO.malaDiretaEnviadaVOs}"
                                var="malaDiretaEnviada">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{malaDiretaEnviada.clienteVO.pessoa.nome}"
                                      rendered="#{malaDiretaEnviada.apresentarColunaPessoa}"/>
                        <h:outputText value="#{malaDiretaEnviada.passivoVO.nome}"
                                      rendered="#{malaDiretaEnviada.apresentarColunaPassivo}"/>
                        <h:outputText value="#{malaDiretaEnviada.indicadoVO.nomeIndicado}"
                                      rendered="#{malaDiretaEnviada.apresentarColunaIndicado}"/>
                    </rich:column>

                    <rich:column rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 1}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_Email}"/>
                        </f:facet>
                        <%--emails--%>
                        <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPassivo}"
                                      value="#{malaDiretaEnviada.passivoVO.email}"/>
                        <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaIndicado}"
                                      value="#{malaDiretaEnviada.indicadoVO.email}"/>
                        <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPessoa}"
                                      value="#{malaDiretaEnviada.emails}"/>
                    </rich:column>
                    <rich:column rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 2}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_MalaDiretaEnviada_Telefone_Celular}"/>
                        </f:facet>
                        <%--telefones--%>
                        <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPessoa}"
                                      value="#{malaDiretaEnviada.telefonesCelulares}"/>
                        <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaIndicado}"
                                      value="#{malaDiretaEnviada.indicadoVO.telefones}"/>
                        <h:outputText rendered="#{malaDiretaEnviada.apresentarColunaPassivo}"
                                      value="#{malaDiretaEnviada.passivoVO.telefones}"/>
                    </rich:column>
                </rich:dataTable>
            </rich:tab>
        </rich:tabPanel>


        <h:panelGrid columns="1" width="100%">
            <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                <h:outputText styleClass="mensagem" value="#{MetaCRMControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{MetaCRMControle.mensagemDetalhada}"/>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup>
                    <a4j:commandLink id="enviarEmailColetivo"
                                     rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 1}"
                                     styleClass="pure-button pure-button-primary"
                                     action="#{MetaCRMControle.enviarEmailColetivoCRM}"
                                     value="Enviar Email"
                                     title="#{msg.msg_gravar_dados}"
                                     accesskey="2"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>

                    <a4j:commandLink id="enviarSMSColetivo"
                                     rendered="#{MetaCRMControle.malaDiretaVO.meioDeEnvio == 2}"
                                     styleClass="pure-button pure-button-primary"
                                     action="#{MetaCRMControle.enviarSMSColetivoCRM}"
                                     value="Enviar SMS"
                                     title="#{msg.msg_gravar_dados}"
                                     accesskey="2"
                                     oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                     reRender="mdlMensagemGenerica, panelGridLeft, panelGridCenter, panelGridRight"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>


    </h:panelGrid>


    <%--MENSAGEM DETALHADA--%>
    <h:panelGrid columns="1" width="100%" rendered="#{not empty MetaCRMControle.mensagemDetalhada}">
        <h:outputText id="msgRealizarContato" styleClass="mensagem"
                      value="#{MetaCRMControle.mensagem}"/>
        <h:outputText id="msgRealizarContatoDet" styleClass="mensagemDetalhada"
                      value="#{MetaCRMControle.mensagemDetalhada}"/>
    </h:panelGrid>

</h:panelGroup>

<script type="text/javascript">
    adicionarPlaceHolderCRM();

    function somaApp() {
        var limiteAPP = 255;
        var elemento = (document.getElementById('form:mensagemAPP'));
        var tamanhoRestante = document.getElementById('form:tamanhoMensagemApp');

        var mais_um = eval(elemento.value.length - 1);
        mais_um++;

        if (elemento.value.length > limiteAPP) {
            elemento.value = '';
            elemento.value = valor_limite;
        }
        else {
            valor_limite = elemento.value;
            tamanhoRestante.value = '';
            tamanhoRestante.value = (limiteAPP - mais_um);
        }
        elemento.focus();
    }

    function voltarPosicaoScrollDireita() {
        console.log('voltarPosicaoScrollDireita ' + posicaoScrollAtual);
        jQuery('.divScroll').scrollTop(posicaoScrollAtual);
    }

    function ajustarScrollAlunoSelecionadoDireita() {
        try{
            var alunoAtual = jQuery('.colunaEsquerdaCRMSel').offset().top;
            var ondeEstaScroll = jQuery('.divScroll').scrollTop();
            var offSetScroll = jQuery('.divScroll').scroll().offset().top;
            if (alunoAtual > ondeEstaScroll) {
                var moverPara = alunoAtual - offSetScroll;
                jQuery('.divScroll').animate({scrollTop: moverPara}, 0);
            }
        }catch (e) {
            console.log("ERRO ajustarScrollAlunoSelecionado: "+e);
        }
    }

</script>

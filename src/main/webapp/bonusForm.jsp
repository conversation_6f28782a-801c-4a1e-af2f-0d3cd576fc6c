<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
    setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Lançamento de Bônus"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0"
                 columnClasses="colunaEsquerda">
        <c:set var="titulo" scope="session" value="Bônus"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-lancar-bonus-para-um-aluno/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
        <h:form id="form">
            <h:panelGrid columns="1" width="100%" styleClass="font-size-em-max">
                <h:panelGrid columns="1" cellpadding="5" width="100%" styleClass=" col-text-align-left font-size-em-max" >
                    <h:panelGroup>
                        <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                        <h:outputText id="nomeCliBonus" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{BonusContratoControle.bonusContratoVO.contratoVO.pessoa.nome}"/>

                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText value="DETALHES DO CONTRATO" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                        <rich:dataTable id="Bonus"  border="0" style="margin: 0; width: 100%;"
                                        cellspacing="0" cellpadding="0" styleClass="tabelaDados semZebra"
                                        value="#{BonusContratoControle.listaContratoVOs}" var="contrato">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_numeroContrato}"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_plano}"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.plano.descricao}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_dataInicio}"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaDe_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_dataTermino}"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_valorBaseContrato}"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.valorBaseCalculo}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGroup>
                </h:panelGrid>
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

                <h:panelGrid columns="1" cellpadding="5" width="100%" styleClass="col-text-align-left" >
                    <h:panelGrid columns="1" styleClass=" col-text-align-left font-size-em-max">

                        <h:panelGrid id="panelGroupAcrescentar" styleClass="col-text-align-left font-size-em-max">
                            <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="O QUE DESEJA FAZER?"/>
                            <h:selectOneRadio id="acrescentarReduzirDias" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{BonusContratoControle.bonusContratoVO.acrescentarDiaContrato}">
                                <f:selectItems value="#{BonusContratoControle.tipoBonus}"/>
                                <a4j:support event="onclick" action="#{BonusContratoControle.limparNumeroDias}" reRender="panelPeriodoBonusPositivo,panelPeriodoBonusNegativo,panelMensagem"/>
                            </h:selectOneRadio>
                        </h:panelGrid>
                        <h:panelGrid columns="1" id="panelPeriodoBonusPositivo" styleClass="col-text-align-left font-size-em-max">
                            <h:panelGrid rendered="#{BonusContratoControle.bonusContratoVO.apresentarPeriodoBonusPositivo}" styleClass="col-text-align-left font-size-em-max">
                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="QUANTIDADE DE DIAS"/>
                                <h:inputText id="numDiasBonus" size="10" maxlength="3"  value="#{BonusContratoControle.bonusContratoVO.nrDias}"  styleClass="inputTextClean">
                                    <a4j:support event="onchange" action="#{BonusContratoControle.obterDataInicioTermino}" reRender="panelPeriodoBonusPositivo, panelMensagem"/>
                                </h:inputText>
                                <h:panelGrid columns="1" rendered="#{BonusContratoControle.bonusContratoVO.apresentarPeriodoBonus}"  styleClass="col-text-align-left font-size-em-max">
                                    <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="PERÍODO DO BÔNUS"/>
                                    <h:panelGroup>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Início" />
                                        <rich:spacer width="10"/>
                                        <h:outputText id="inicioBonus" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{BonusContratoControle.bonusContratoVO.dataInicio_Apresenta}">
                                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                                        </h:outputText>
                                        <rich:spacer width="10"/>
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" Até " />
                                        <rich:spacer width="10"/>
                                        <h:outputText id="fimBonus"  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{BonusContratoControle.bonusContratoVO.dataTermino_Apresentar}">
                                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                                        </h:outputText>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGrid id="panelPeriodoBonusNegativo" styleClass="col-text-align-left font-size-em-max">
                            <h:panelGrid rendered="#{BonusContratoControle.bonusContratoVO.apresentarPeriodoBonusNegativo}" styleClass="col-text-align-left font-size-em-max">
                                <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="QUANTIDADE DE DIAS" />
                                <h:inputText id="numDiasReducao" size="10" maxlength="3"  value="#{BonusContratoControle.bonusContratoVO.nrDias}" styleClass="inputTextClean">
                                    <a4j:support event="onchange" action="#{BonusContratoControle.obterDataInicioTermino}" reRender="panelPeriodoBonusNegativo, panelMensagem"/>
                                </h:inputText>
                                <h:panelGrid columns="3" rendered="#{BonusContratoControle.bonusContratoVO.apresentarPeriodoBonus}" styleClass="col-text-align-left">
                                    <h:outputText rendered="#{BonusContratoControle.bonusContratoVO.contratoVO.situacao == 'AT'}" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="A data de vencimento do contrato passa a ser dia: " />
                                    <h:outputText rendered="#{BonusContratoControle.bonusContratoVO.contratoVO.situacao == 'IN'}" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="A data final de acesso passa a ser dia: " />


                                    <h:outputText id="fimContratoRed" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{BonusContratoControle.bonusContratoVO.dataTermino_Apresentar}">
                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                    </h:outputText>

                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>


                        <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold " value="JUSTIFICATIVA" />
                        <h:panelGroup>
                            <div class="cb-container margenVertical">
                                <h:selectOneMenu  id="tipoOperacao"   onblur="blurinput(this);"  onfocus="focusinput(this);"  value="#{BonusContratoControle.bonusContratoVO.tipoJustificativa}" >
                                    <f:selectItems  value="#{BonusContratoControle.listaJustificativaOperacaoVOs}" />
                                    <a4j:support event="onchange" action="#{BonusContratoControle.limparMensagem}" reRender="panelMensagem"/>
                                </h:selectOneMenu>
                            </div>
                            <rich:spacer width="7"/>
                            <a4j:commandLink id="atualizar_justificativaDeOperacao" action="#{BonusContratoControle.montarDadosListaJustificativaOperacaoVOs}"  immediate="true" ajaxSingle="true" reRender="form:tipoOperacao">
                                <i class="fa-icon-refresh texto-size-14-real linkAzul " ></i>
                            </a4j:commandLink>
                            <rich:spacer width="7"/>
                            <a4j:commandLink id="adicionarTipoOperacao" action="#{JustificativaOperacaoControle.reset}"  oncomplete="abrirPopup('justificativaOperacaoCons.jsp', 'JustificativaOperacao', 800, 595);">
                                <i class="fa-icon-plus-sign texto-size-14-real linkAzul "/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>


                    <h:panelGrid columns="1">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" value="OBSERVAÇÃO: "/>
                        <h:inputTextarea styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{BonusContratoControle.bonusContratoVO.observacao}"  rows="4" cols="110"/>

                    </h:panelGrid>
                    <h:panelGrid id="panelMensagem" columns="3" width="100%" >
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{BonusContratoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{BonusContratoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgBonus" styleClass="mensagem"  value="#{BonusContratoControle.mensagem}"/>
                            <h:outputText id="msgBonusDet"  styleClass="mensagemDetalhada" value="#{BonusContratoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid id="panelBotoes" width="400px" columns="2" >
                        <h:panelGrid width="350px">
                            <h:panelGroup rendered="#{BonusContratoControle.apresentarBotoes && !BonusContratoControle.processandoOperacao}">
                                <a4j:commandLink id="confirmar"
                                                 reRender="form,panelAutorizacaoFuncionalidade,panel"
                                                 action="#{BonusContratoControle.validarDadosBonus}"
                                                 styleClass="pure-button pure-button-primary">
                                    <i class="fa-icon-ok"></i>&nbsp;Confirmar
                                </a4j:commandLink>
                                <rich:spacer width="7"/>
                                <h:commandLink id="cancelar"  styleClass="pure-button" onclick="fecharJanela();executePostMessage({close: true});">
                                    <i class="fa-icon-remove"></i>&nbsp;Cancelar
                                </h:commandLink>
                            </h:panelGroup>
                            <h:panelGroup rendered="#{!BonusContratoControle.apresentarBotoes && !BonusContratoControle.processandoOperacao}">
                                <a4j:commandLink id="comprovanteOpBo"
                                                 title="Imprimir Comprovante da Operação de Bônus"
                                                 action="#{BonusContratoControle.imprimirComprovanteOperacao}"
                                                 oncomplete="abrirPopupPDFImpressao('relatorio/#{BonusContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                                 styleClass="pure-button">
                                    <i class="fa-icon-print"></i> &nbsp Imprimir Comprovante
                                </a4j:commandLink>
                                <rich:spacer width="7"/>
                                <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});"  styleClass="pure-button pure-button-primary">
                                    <i class="fa-icon-remove"></i>&nbsp;Fechar
                                </h:commandLink>

                            </h:panelGroup>


                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid rendered="#{BonusContratoControle.processandoOperacao}" columns="1">
                        <h:outputText id="msgProcessando" styleClass="mensagem"  value="Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                        <rich:spacer height="7"/>
                        <a4j:commandLink id="atualizar" title="Atulaizar" onclick="window.location.reload();fireElementFromParent('form:btnAtualizaCliente');"
                                         styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                        </a4j:commandLink>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>

    </h:panelGrid>

    <rich:modalPanel id="panel" width="350" height="170"  autosized="true" styleClass="novaModal" shadowOpacity="true"  showWhenRendered="#{BonusContratoControle.bonusContratoVO.mensagemErro}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panel" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup>
                <rich:spacer height="25px"></rich:spacer>
                <h:outputText id="msgBonusPainelDetalhada" styleClass="mensagemDetalhada" value="#{BonusContratoControle.mensagemDetalhada}"/>

            </h:panelGroup>

        </h:panelGrid>
    </rich:modalPanel>

    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
   window.addEventListener("load", function (event) {
       executePostMessage({loaded: true})
   });
    document.getElementById("form:justificativa").focus();
</script>

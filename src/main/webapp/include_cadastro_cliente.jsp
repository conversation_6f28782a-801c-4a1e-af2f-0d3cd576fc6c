<%-- 
    Document   : include_cadastro_cliente
    Created on : 07/05/2012, 16:25:45
    Author     : Waller
--%>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<table width="99%" border="0" align="left"
       cellpadding="0" cellspacing="0" class="text"
       style="margin-left: 6px; margin-top: 6px;">    
    <tr>
        <td width="19" height="50" align="left" valign="top"><img
                src="images/box_centro_top_left.gif" width="19" height="50"></td>
        <td align="left" valign="top"
            background="images/box_centro_top.gif"
            style="padding: 0px 0 0 0;">
            <table width="100%" border="0" align="left" cellpadding="0"
                   cellspacing="0" class="text">
                <tr>
                    <td align="left" valign="top" style="padding: 11px 0 0 0; width: 200px">
                        <span class="tituloboxcentro">
                            Informa&ccedil;&otilde;es do Cliente</span>
                            <h:outputLink styleClass="linkWiki"
                                          value="#{SuperControle.urlWiki}Inicial:Informa��es_do_Cliente"
                                          title="Clique e saiba mais: Informa��es do Cliente"
                                          target="_blank" >
                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                            </h:outputLink>													</td>
                    <td align="left" valign="top" style="padding: 3px 0 0 0;">
                        <h:dataTable id="clienteSituacao" width="70%"
                                     columnClasses="w10"
                                     value="#{ClienteControle.clienteVO.clienteSituacaoVOs}"
                                     var="clienteSituacao">
                            <h:column>
                                <h:graphicImage id="sitFreePass" value="./imagens/botaoFreePass.png"
                                                rendered="#{clienteSituacao.visitanteFreePass}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.visitanteFreePass}"
                                              title="Clique e saiba mais: Visitante FreePass" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitAtivo" value="./imagens/botaoAtivo.png"
                                                rendered="#{clienteSituacao.ativo}" width="30" height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.ativo}" title="Clique e saiba mais: Ativos"
                                              target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                                <h:graphicImage id="sitInativo" value="./imagens/botaoInativo.png"
                                                rendered="#{clienteSituacao.inativo}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.inativo}"
                                              title="Clique e saiba mais: Inativos" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                                <h:graphicImage id="sitVisitante" value="./imagens/botaoVisitante.png"
                                                rendered="#{clienteSituacao.visitante}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.visitante}"
                                              title="Clique e saiba mais: Visitantes" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitTrancado" value="./imagens/botaoTrancamento.png"
                                                rendered="#{clienteSituacao.trancado}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.trancado}"
                                              title="Clique e saiba mais: Trancados" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <rich:spacer width="5px" />
                                <h:graphicImage id="sitAtNormal" value="./imagens/botaoNormal.png"
                                                rendered="#{clienteSituacao.ativoNormal}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.ativoNormal}"
                                              title="Clique e saiba mais: Ativos Normais" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                                <h:graphicImage id="sitTraVencido" value="./imagens/botaoTrancadoVencido.png"
                                                rendered="#{clienteSituacao.trancadoVencido}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.trancadoVencido}"
                                              title="Clique e saiba mais: Trancados Vencidos" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitAulaAvulsa" value="./imagens/botaoAulaAvulsa.png"
                                                rendered="#{clienteSituacao.visitanteAulaAvulsa}"
                                                width="30" height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.visitanteAulaAvulsa}"
                                              title="Clique e saiba mais: Visitante com Aula Avulsa" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitDiaria" value="./imagens/botaoDiaria.png"
                                                rendered="#{clienteSituacao.visitanteDiaria}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.visitanteDiaria}"
                                              title="Clique e saiba mais: Visitante com Di?ria" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <rich:spacer width="5px" />
                                <h:graphicImage id="sitCancelamento" value="./imagens/botaoCancelamento.png"
                                                rendered="#{clienteSituacao.inativoCancelamento}"
                                                width="30" height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.inativoCancelamento}"
                                              title="Clique e saiba mais: Inativos Cancelados" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitDesistente" value="./imagens/botaoDesistente.png"
                                                rendered="#{clienteSituacao.inativoDesistente}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.inativoDesistente}"
                                              title="Clique e saiba mais: Inativos Desistentes" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitAVencer" value="./imagens/botaoAvencer.png"
                                                rendered="#{clienteSituacao.ativoAvencer}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.ativoAvencer}"
                                              title="Clique e saiba mais: Ativos a Vencer" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitVencido" value="./imagens/botaoVencido.png"
                                                rendered="#{clienteSituacao.inativoVencido}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.inativoVencido}"
                                              title="Clique e saiba mais: Inativos Vencidos" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitCarencia" value="./imagens/botaoCarencia.png"
                                                rendered="#{clienteSituacao.ativoCarencia}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.ativoCarencia}"
                                              title="Clique e saiba mais: Inativos" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>

                                <h:graphicImage id="sitAtestado" value="./imagens/botaoAtestado.png"
                                                rendered="#{clienteSituacao.ativoAtestado}" width="30"
                                                height="29" />
                                <h:outputLink styleClass="linkWiki"
                                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                              rendered="#{clienteSituacao.ativoAtestado}"
                                              title="Clique e saiba mais: Ativos com Atestado M�dico" target="_blank" >
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                            </h:column>
                        </h:dataTable>
                    </td>
                    <td>
                        <h:outputLink
                                      value="clienteNav.jsp?page=cliente&matricula=#{ClienteControle.clienteVO.codigoMatricula}"
                                      title="Nova tela de Cliente"
                                      target="_self" >
                            <i id="btnNovaTela" class="fa-icon-star" ></i>
                        </h:outputLink>			
                    </td>
                </tr>
            </table>
        </td>
        <td width="19" align="left" valign="top"><img
                src="images/box_centro_top_right.gif" width="19" height="50"></td>
    </tr>

    <tr>
        <td width="19" align="left" valign="top"
            background="images/box_centro_left.gif"><img
                src="images/shim.gif"></td>

        <td align="left" valign="top" bgcolor="#ffffff"
            style="padding: 0 0 0 0;">
            <%--Inicio Mensagem Superior --%>
            <h:panelGrid id="panelMensagemSuperior" columns="1" width="100%" >
                <c:if test="${ClienteControle.erro}">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">

                        <h:panelGrid columns="1" width="100%" >
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{ClienteControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ClienteControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                            <h:outputText styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </c:if>
            </h:panelGrid>
            <%--Fim Mensagem Superior --%>
            <%--Inicio Include --%>
            <h:panelGroup id="panelIncludeTabPanelCliente" layout="block">
                <jsp:include page="include_tabpanel_cliente.jsp" flush="true"/>
            </h:panelGroup>
            <%--Fim Include --%>
            <br>
            <%--Inicio Mensagem Inferior --%>
            <h:panelGrid id="panelMensagemInferior" columns="1" width="100%" >
                <c:if test="${ClienteControle.erro}">
                    <h:panelGrid  columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{ClienteControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ClienteControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <%--Fim Mensagem Inferior --%>
                </c:if>
            </h:panelGrid>
        </td>
        <td align="left" valign="top"
            background="images/box_centro_right.gif"><img
                src="images/shim.gif"></td>
    </tr>
    <tr>
        <td width="19" height="20" align="left" valign="top"><img
                src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
        <td align="left" valign="top"
            background="images/box_centro_bottom.gif"><img
                src="images/shim.gif"></td>
        <td align="left" valign="top"><img
                src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
    </tr>
</table>

<%@ page contentType="text/html;charset=UTF-8" pageEncoding="ISO-8859-1" language="java" %>
<%@ include file="includes/imports.jsp" %>

<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

<h:form id="formRC">
<h:commandLink action="#{HistoricoContatoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
               onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
               style="display: none"/>
<h:panelGrid id="panelGridForm" columns="1" width="100%" style="margin-top: 20px;">

<h:panelGroup >
    <h:panelGroup layout="block" style="float: left;">
        <h:outputText style="vertical-align:middle;" value="#{msg.msg_consultar_por}"/>
        <h:selectOneMenu style="vertical-align:middle;" id="consulta" onblur="blurinput(this);" onfocus="focusinput(this);"
                         styleClass="form" required="true" value="#{HistoricoContatoControle.controleConsulta.campoConsulta}">
            <f:selectItems value="#{HistoricoContatoControle.tipoConsultaComboCliente}"/>
            <a4j:support event="onchange" action="#{HistoricoContatoControle.decidirTabela}" reRender="formRC:dataTables"/>
        </h:selectOneMenu>
    </h:panelGroup>
    <h:panelGroup layout="block" style="float: left; margin-left: 8px;;width: 30%">
        <h:inputText id="valorConsulta" style="vertical-align:middle; width: 100%" onblur="blurinput(this);"
                     onfocus="focusinput(this);"
                     styleClass="form" value="#{HistoricoContatoControle.controleConsulta.valorConsulta}"/>
        <rich:hotKey selector="#valorConsulta" key="return" handler="#{rich:element('consultar')}.onclick();return false;"/>
    </h:panelGroup>
    <h:panelGroup layout="block" style="float: left; margin-left: 60px">
        <h:outputText style="vertical-align:middle;" value="Situação"/>
        <h:selectOneMenu id="situacao" style="vertical-align:middle;" onblur="blurinput(this);" onfocus="focusinput(this);"
                         styleClass="form" value="#{HistoricoContatoControle.consultaSituacao}">
            <f:selectItems value="#{HistoricoContatoControle.listaSelectItemSituacaoCliente}"/>
        </h:selectOneMenu>
    </h:panelGroup>
    <h:panelGroup style="float: right; margin-right: 20px">
        <a4j:commandLink id="consultar" type="submit"
                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                         reRender="paginaAtual, painelPaginacao, formRC" value="Buscar"
                         actionListener="#{HistoricoContatoControle.consultarPaginadoListener}"
                         styleClass="botaoPrimario texto-size-14-real ">
            <f:attribute name="paginaInicial" value="paginaInicial"/>
        </a4j:commandLink>



    </h:panelGroup>
    <h:commandLink style="display:none;" id="botaoInvisivelTela3"
                   onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" action="#{HistoricoContatoControle.consultarCliente}"/>
</h:panelGroup>

<h:panelGrid id="dataTables" columns="1"  width="100%" style="margin-top: 5px">
    <rich:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                    columnClasses="1, 2, centralizado, centralizado, centralizado, centralizado, centralizado"
                    value="#{HistoricoContatoControle.listaConsulta}"
                    rendered="#{HistoricoContatoControle.apresentarResultadoConsulta}" rows="20" var="cliente">

        <rich:column sortBy="#{cliente.matricula}" filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Cliente_matricula}"/>
            </f:facet>
            <a4j:commandLink action="#{MetaCRMControle.inicializarContatoAvulso}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="matricula" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{cliente.matricula}"/>
            </a4j:commandLink>
        </rich:column>
        <rich:column sortBy="#{cliente.pessoa.nome}" filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Cliente_pessoa}"/>
            </f:facet>
            <a4j:commandLink action="#{MetaCRMControle.inicializarContatoAvulso}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="pessoa" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{cliente.pessoa.nome}"/>
            </a4j:commandLink>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Cliente_categoria}"/>
            </f:facet>
            <h:panelGroup>
                <a4j:commandLink action="#{MetaCRMControle.inicializarContatoAvulso}"
                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                 id="categoria" reRender="form, formAgendamento, panelAgendamento"
                                 oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                    <h:outputText value="#{cliente.categoria.nome}"/>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Inicio Contrato"/>
            </f:facet>
            <h:panelGroup>
                <a4j:commandLink action="#{MetaCRMControle.inicializarContatoAvulso}"
                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                 reRender="form, formAgendamento, panelAgendamento"
                                 oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                    <h:outputText value="#{cliente.situacaoClienteSinteticoVO.dataVigenciaDe}">
                        <f:convertDateTime pattern="dd/MM/yyyy" locale="pt" timeZone="America/Sao_Paulo"/>
                    </h:outputText>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Término Contrato"/>
            </f:facet>
            <h:panelGroup>
                <a4j:commandLink action="#{MetaCRMControle.inicializarContatoAvulso}"
                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                 reRender="form, formAgendamento, panelAgendamento"
                                 oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                    <h:outputText value="#{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustada}">
                        <f:convertDateTime pattern="dd/MM/yyyy" locale="pt" timeZone="America/Sao_Paulo"/>
                    </h:outputText>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Cliente_situacao}"/>
            </f:facet>
            <h:dataTable id="clienteSituacao" width="100%" columnClasses="w10" value="#{cliente.clienteSituacaoVOs}"
                         var="clienteSituacao">
                <h:column>
                    <h:graphicImage value="./images/botaoFreePass.png" rendered="#{clienteSituacao.visitanteFreePass}"/>
                    <h:graphicImage value="./images/botaoAtivo.png" rendered="#{clienteSituacao.ativo}"/>
                    <h:graphicImage value="./images/botaoInativo.png" rendered="#{clienteSituacao.inativo}"/>
                    <h:graphicImage value="./images/botaoVisitante.png" rendered="#{clienteSituacao.visitante}"/>
                    <h:graphicImage value="./images/botaoTrancamento.png" rendered="#{clienteSituacao.trancado}"/>

                    <h:graphicImage value="./images/botaoNormal.png" rendered="#{clienteSituacao.ativoNormal}"/>
                    <h:graphicImage value="./images/botaoTrancadoVencido.png" rendered="#{clienteSituacao.trancadoVencido}"/>
                    <h:graphicImage value="./images/botaoAulaAvulsa.png" rendered="#{clienteSituacao.visitanteAulaAvulsa}"/>
                    <h:graphicImage value="./images/botaoDiaria.png" rendered="#{clienteSituacao.visitanteDiaria}"/>

                    <h:graphicImage value="./images/botaoCancelamento.png" rendered="#{clienteSituacao.inativoCancelamento}"/>
                    <h:graphicImage value="./images/botaoDesistente.png" rendered="#{clienteSituacao.inativoDesistente}"/>
                    <h:graphicImage value="./images/botaoAvencer.png" rendered="#{clienteSituacao.ativoAvencer}"/>
                    <h:graphicImage value="./images/botaoVencido.png" rendered="#{clienteSituacao.inativoVencido}"/>
                    <h:graphicImage value="./images/botaoCarencia.png" rendered="#{clienteSituacao.ativoCarencia}"/>
                    <h:graphicImage value="./images/botaoAtestado.png" rendered="#{clienteSituacao.ativoAtestado}"/>
                </h:column>
            </h:dataTable>
        </rich:column>
    </rich:dataTable>
    <rich:dataTable id="passivo" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                    columnClasses="colunaAlinhamento" rendered="#{HistoricoContatoControle.tabelaPassivo}"
                    value="#{HistoricoContatoControle.listaConsulta}" rows="10" var="passivo">
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Passivo_codigo}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="codigoPassivo" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{passivo.codigo}"/>
            </a4j:commandLink>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Passivo_nome}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="nomePassivo" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{passivo.nome}"/>
            </a4j:commandLink>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Passivo_usuarioResponsavelCadastro}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="responsavelNomePassivo" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{passivo.responsavelCadastro.nome}"/>
            </a4j:commandLink>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Passivo_dia}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="diaApresentarPassivo" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{passivo.dia_Apresentar}"/>
            </a4j:commandLink>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Passivo_colaborador}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="colaboradorPassivo" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{passivo.colaboradorResponsavel.nome}"/>
            </a4j:commandLink>
        </h:column>
    </rich:dataTable>
    <rich:datascroller align="center" for="formRC:passivo" id="scResultadoConsultaPass"
                       rendered="#{HistoricoContatoControle.tabelaPassivo}"/>
    <rich:dataTable id="indicacao" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                    columnClasses="colunaCentralizada" rendered="#{HistoricoContatoControle.tabelaIndicacao}"
                    value="#{HistoricoContatoControle.listaConsulta}" rows="10" var="indicacao">
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Indicacao_codigo}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarIndicacaoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="codigoIndicado" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{indicacao.codigo}"/>
            </a4j:commandLink>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Indicado_nomeIndicado}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarIndicacaoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="quemIndicouIndicado" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{indicacao.nomeIndicado}"/>
            </a4j:commandLink>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Indicado_telefone}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarIndicacaoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="diaApresentarIndicado" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{indicacao.telefone}"/>
            </a4j:commandLink>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Indicado_email}"/>
            </f:facet>
            <a4j:commandLink action="#{HistoricoContatoControle.selecionarIndicacaoParaAgendamento}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             id="eventoIndicado" reRender="form, formAgendamento, panelAgendamento"
                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                <h:outputText value="#{indicacao.email}"/>
            </a4j:commandLink>
        </h:column>
    </rich:dataTable>
    <rich:datascroller align="center" for="formRC:indicacao" id="scResultadoConsultaInd"
                       rendered="#{HistoricoContatoControle.tabelaIndicacao}"/>
</h:panelGrid>
<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
    <h:panelGroup id="painelPaginacao" rendered="#{HistoricoContatoControle.confPaginacao.existePaginacao}">
        <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtual, painelPaginacao"
                         rendered="#{HistoricoContatoControle.confPaginacao.apresentarPrimeiro}"
                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                         actionListener="#{HistoricoContatoControle.consultarPaginadoListener}">
            <f:attribute name="pagNavegacao" value="pagInicial"/>
        </a4j:commandLink>
        <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="items, paginaAtual, painelPaginacao"
                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                         rendered="#{HistoricoContatoControle.confPaginacao.apresentarAnterior}"
                         actionListener="#{HistoricoContatoControle.consultarPaginadoListener}">
            <f:attribute name="pagNavegacao" value="pagAnterior"/>
        </a4j:commandLink>
        <h:outputText id="paginaAtual" styleClass="tituloCampos"
                      value="#{msg_aplic.prt_msg_pagina} #{HistoricoContatoControle.confPaginacao.paginaAtualDeTodas}"
                      rendered="true"/>
        <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="items, paginaAtual, painelPaginacao"
                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                         rendered="#{HistoricoContatoControle.confPaginacao.apresentarPosterior}"
                         actionListener="#{HistoricoContatoControle.consultarPaginadoListener}">
            <f:attribute name="pagNavegacao" value="pagPosterior"/>
        </a4j:commandLink>
        <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="items, paginaAtual, painelPaginacao"
                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                         rendered="#{HistoricoContatoControle.confPaginacao.apresentarUltimo}"
                         actionListener="#{HistoricoContatoControle.consultarPaginadoListener}">
            <f:attribute name="pagNavegacao" value="pagFinal"/>
        </a4j:commandLink>
        <h:outputText id="totalItens" styleClass="tituloCampos"
                      value=" [#{msg_aplic.prt_msg_itens} #{HistoricoContatoControle.confPaginacao.numeroTotalItens}]"
                      rendered="true"/>
    </h:panelGroup>
</h:panelGrid>
</h:panelGrid>
<h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
    <h:panelGrid columns="1" width="100%">
        <f:verbatim>
            <h:outputText value=" "/>
        </f:verbatim>
    </h:panelGrid>
    <h:commandButton rendered="#{HistoricoContatoControle.sucesso}" image="./imagensCRM/sucesso.png"/>
    <h:commandButton rendered="#{HistoricoContatoControle.erro}" image="./imagensCRM/erro.png"/>
    <h:panelGrid columns="1" width="100%">
        <h:outputText styleClass="mensagem" value="#{HistoricoContatoControle.mensagem}"/>
        <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoContatoControle.mensagemDetalhada}"/>
    </h:panelGrid>
</h:panelGrid>
</h:form>
</h:panelGrid>
<script type="text/javascript">
    document.getElementById("formRC:consulta").focus();
</script>

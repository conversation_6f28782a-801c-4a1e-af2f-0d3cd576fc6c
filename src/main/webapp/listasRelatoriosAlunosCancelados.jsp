<%-- 
    Document   : listasRelatoriosAlunosCancelados
    Created on : 26/06/2013, 11:49:30
    Author     : <PERSON><PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<jsp:include page="include_head.jsp" flush="true"/>

<style type="text/css">
    .rich-stglpanel-header {
        color: #0f4c6b;
    }

    .rich-stglpanel-header {
        background-color: #ACBECE;
        border-color: #ACBECE;
        font-size: 12px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemRelatorios" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem  container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value=" Clientes Cancelados" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}relatorio-de-cancelamentos/"
                                                      title="Clique e saiba mais: Relatório Clientes Cancelados"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup  layout="block" styleClass="margin-box">
                                    <!---------------------------------CONSULTA DE ALUNOS CANCELADOS-------------------------------------->

                                        <h:outputText styleClass="text" value="Filtros"
                                                      style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>

                                    <rich:simpleTogglePanel switchType="client" label="Empresas" opened="false" onexpand="true" id="AlunosCanceladosEmpresas">
                                        <h:panelGroup id="emp">
                                            <h:panelGroup rendered="#{LRAlunosCanceladosControle.permissaoConsultaTodasEmpresas}"
                                                          style="padding-left: 10px;padding-top: 15px;padding-bottom: 15px;border: 1px solid #ACBECE;"
                                                          layout="block">
                                                <h:selectOneMenu value="#{LRAlunosCanceladosControle.filtroEmpresa}">
                                                    <f:selectItems  value="#{LRAlunosCanceladosControle.listaEmpresas}" />
                                                    <a4j:support event="onchange"
                                                                 action="#{LRAlunosCanceladosControle.recarregarTela}"
                                                                 reRender="consultores, professores"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>

                                    <rich:simpleTogglePanel switchType="client"
                                                            label="Dados do cancelamento"
                                                            style="margin-top: 8px" opened="true"
                                                            onexpand="false">
                                        <h:panelGrid columns="8">
                                            <h:panelGroup id="dataCancelamento">
                                                <%--intervalo de datas de cancelamento --%>
                                                <h:outputText styleClass="text"
                                                              value="Data cancelamento: "/>
                                                <rich:calendar id="inicioCancelamento"
                                                               value="#{LRAlunosCanceladosControle.inicioCancelamento}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>

                                                <rich:spacer width="5px"/>
                                                <h:outputText styleClass="text" value=" até "/>
                                                <rich:calendar id="fimCancelamento"
                                                               value="#{LRAlunosCanceladosControle.fimCancelamento}"
                                                               inputSize="6"
                                                               inputClass="form"
                                                               oninputblur="blurinput(this);"
                                                               oninputfocus="focusinput(this);"
                                                               oninputchange="return validar_Data(this.id);"
                                                               datePattern="dd/MM/yyyy"
                                                               enableManualInput="true"
                                                               zindex="2"
                                                               showWeeksBar="false"/>
                                            </h:panelGroup>
                                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                            <a4j:commandButton id="limpaData"
                                                               action="#{LRAlunosCanceladosControle.limparData}"
                                                               image="/images/limpar.gif"
                                                               title="Limpar período de cancelamento."
                                                               reRender="inicioCancelamento, fimCancelamento"/>
                                            <rich:spacer width="5px"/>
                                            <h:selectBooleanCheckbox id="filtroCancelamentoComValorDevolver"
                                                                     value="#{LRAlunosCanceladosControle.filtroCancelamentoComValorDevolver}"/>
                                            <h:outputText styleClass="text" value="Filtrar cancelamento com valor a devolver"/>
                                        </h:panelGrid>
                                    </rich:simpleTogglePanel>
                                    <%-- ------------------------------- ABA CONSULTORES   ------------------------------------ --%>
                                    <rich:simpleTogglePanel switchType="client" label="Consultores"
                                                            opened="false"
                                                            onexpand="true">
                                        <h:panelGroup id="consultores">
                                            <rich:dataGrid value="#{LRAlunosCanceladosControle.consultores}"
                                                           var="consultor" width="100%" columns="4">
                                                <h:selectBooleanCheckbox id="checkBoxConsultorRelClientesCancelados"
                                                        value="#{consultor.colaboradorEscolhido}"/>
                                                <h:outputText value="#{consultor.pessoa.nome}"/>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <%-- ------------------------------- ABA PROFESSORES   ------------------------------------ --%>
                                    <rich:simpleTogglePanel  style="margin-bottom: 10px;" switchType="client" label="Professores"
                                                            opened="false"
                                                            onexpand="true">
                                        <h:panelGroup id="professores">
                                            <rich:dataGrid value="#{LRAlunosCanceladosControle.professores}"
                                                           var="professor" width="100%" columns="4">
                                                <h:selectBooleanCheckbox id="checkBoxProfessorRelClientesCancelados"
                                                        value="#{professor.colaboradorEscolhido}" />

                                                <h:outputText value="#{professor.pessoa.nome} "/>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <h:panelGroup layout="block" styleClass="container-botoes">
                                    <a4j:commandLink styleClass="botoes nvoBt" id="btnConsultarClientesCancelados"
                                                     style="margin-left: 0px"
                                                     action="#{LRAlunosCanceladosControle.consultarAlunosCancelados}"
                                                     reRender="groupResultados, imprimir, exportExcel">
                                        Consultar&nbsp<i class="fa-icon-search"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink style="margin-left:5px;margin-top: 8px; padding:5px 7px;" id="btnLimparFiltrosClientesCancelados"
                                                     action="#{LRAlunosCanceladosControle.limparFiltros}"
                                                     styleClass="botoes nvoBt btSec"
                                                     reRender="form">
                                        Limpar filtros&nbsp <i class="fa-icon-eraser"></i>
                                    </a4j:commandLink>
                                    </h:panelGroup>

                                    <h:panelGroup id="exportExcel">
                                            <a4j:commandLink id="exportarExcel" style="margin-left:5px; margin-right: 10px"
                                                             actionListener="#{ExportadorListaControle.exportar}"
                                                             rendered="#{not empty LRAlunosCanceladosControle.resultado}"
                                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                             accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista"
                                                             value="#{LRAlunosCanceladosControle.resultado}"/>
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="itemExportacao" value="relCancelados"/>
                                                <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,telefone=Telefones,email=E-mail,justificativa=Justificativa,observacao=Observação,dataNascimentoApresentar=Nascimento,situacaoClienteApresentar=Situação
                                                                     ,sexo=Sexo Biológico,logradouro=Logradouro,numero=Número,bairro=Bairro,cidade=Cidade,cep=CEP,complemento=Complemento,dataMatriculaApresentar=Data Matrícula
                                                                     ,dataOperacaoApresentar=Lanç. Operação,dataInicioPlanoApresentar=Início Plano,dataVencimentoPlanoApresentar=Venc. Plano,dataUltimoAcessoApresentar=Últ. Acesso,plano=Plano
                                                                     ,dataCadastroApresentar=Data Cadastro,consultor=Consultor/Professor,estadoCivil=Estado Civil,profissao=Profissão
                                                                     ,categoria=Categoria,nomeEmpresa=Empresa,modalidades=Modalidades,vezesSemana=Vezes na semana,cpf=CPF,valorContrato=Valor do Contrato,valorPago=Valor Pago
                                                                     ,valorUtilizado=Valor Utilizado,valorDevolvido=Valor Devolvido,descricaoCancelamento=Descrição do Cancelamento"/>
                                                <f:attribute name="prefixo" value="ClientesCancelados"/>

                                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                            </a4j:commandLink>
                                    </h:panelGroup>
                                    <h:panelGroup id="imprimir" style="display: -webkit-inline-flex;">
                                        <a4j:commandLink style="margin-left:5px;margin-top: 8px;"
                                                         value="Imprimir"
                                                         rendered="#{not empty LRAlunosCanceladosControle.resultado}"
                                                         reRender="relatorioImprimir"
                                                         action="#{LRAlunosCanceladosControle.prepararImpr}"
                                                         styleClass="botoes nvoBt btSec"
                                                         oncomplete="Richfaces.showModalPanel('relatorioImprimir');">
                                            <i class="fa-icon-print"></i>
                                        </a4j:commandLink>

                                    </h:panelGroup>
                                    <%---------------------------------TABELA DO RELATORIO---------------------------------------%>
                                    <h:panelGroup id="groupResultados">
                                        <rich:dataTable width="100%"
                                                        value="#{LRAlunosCanceladosControle.resultado}"
                                                        id="resultados"
                                                        var="item"
                                                        rows="30"
                                                        style="margin-top: 8px"
                                                        rendered="#{not empty LRAlunosCanceladosControle.resultado}">

                                            <rich:column id="matricula" sortBy="#{item.matricula}">
                                                <f:facet name="header">
                                                    <h:outputText value="Mat"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.matricula}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="nome" sortBy="#{item.nome}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nome"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nome}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="telefone" sortBy="#{item.telefone}">
                                                <f:facet name="header">
                                                    <h:outputText value="Telefone"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.telefone}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="email" sortBy="#{item.email}">
                                                <f:facet name="header">
                                                    <h:outputText value="E-mail"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.email}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="justificativa" sortBy="#{item.justificativa}">
                                                <f:facet name="header">
                                                    <h:outputText value="Justificativa"/>
                                                </f:facet>
                                                <a4j:commandLink value="#{item.justificativa}"
                                                                 actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                                 action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                                 oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="observacao" sortBy="#{item.observacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Observação"/>
                                                </f:facet>
                                                <a4j:commandLink value="#{item.observacao}"
                                                                 actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                                 action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                                 oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataCadastro" sortBy="#{item.dataCadastro}">
                                                <f:facet name="header">
                                                    <h:outputText value="Cadastro"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataCadastroApresentar}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataNascimento"
                                                         sortBy="#{item.dataNascimento}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nasc."/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataNascimentoApresentar}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataOperacao" sortBy="#{item.dataOperacao}">
                                                <f:facet name="header">
                                                    <h:outputText value="Lanç. Operação"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataOperacaoApresentar}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="dataVencimentoPlano" sortBy="#{item.vencimentoPlano}">
                                                <f:facet name="header">
                                                    <h:outputText value="Vencimento"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataVencimentoPlanoApresentar}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataUltimoAcesso"
                                                         sortBy="#{item.dataUltimoAcesso}">
                                                <f:facet name="header">
                                                    <h:outputText value="Últ. Acesso"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataUltimoAcessoApresentar}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="empresa"
                                                         sortBy="#{item.nomeEmpresa}">
                                                <f:facet name="header">
                                                    <h:outputText value="Empresa"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nomeEmpresa}"
                                                        actionListener="#{LRAlunosCanceladosControle.prepareEditar}"
                                                        action="#{LRAlunosCanceladosControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                        </rich:dataTable>
                                        <table align="right" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td align="center" valign="middle"><h5>
                                                    <h:outputText
                                                            rendered="#{not empty LRAlunosCanceladosControle.resultado}"
                                                            value=" [Itens:#{LRAlunosCanceladosControle.totalItens}]"> </h:outputText>
                                                </h5></td>
                                            </tr>
                                        </table>
                                        <rich:datascroller for="resultados"
                                                           rendered="#{not empty LRAlunosCanceladosControle.resultado}"
                                                           id="scrollResultados">
                                        </rich:datascroller>
                                    </h:panelGroup>
                                </h:panelGroup>

                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true" />

        </h:panelGroup>
    </h:form>

    <%------------------------- MODAL 'IMPRIMIR' ----------------------------------------%>
    <rich:modalPanel id="relatorioImprimir" autosized="true" shadowOpacity="true" width="350" height="200">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados da Impressão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">

            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="imprimir"/>
                <rich:componentControl for="relatorioImprimir" attachTo="imprimir" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form style="height: 180px">

            <h:outputText styleClass="text" style="font-weight: bold"
                          value="Por Favor, selecione as informações que deseja imprimir:"/><br/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosCanceladosControle.apresentarLinha1}"/>
            <h:outputText styleClass="text" value="Dados Cadastrais"/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosCanceladosControle.apresentarLinha2}"/>
            <h:outputText styleClass="text" value="Endereço"/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosCanceladosControle.apresentarLinha3}"/>
            <h:outputText styleClass="text" value="Informações de Plano"/><br/><br/>
            <center>
                <h:outputText escape="false" value= "<br/>" />
                <a4j:commandLink id="imprimirPDF" ajaxSingle="false"
                                 action="#{LRAlunosCanceladosControle.imprimirListaRelatorio}"
                                 value="Imprimir"
                                 reRender="mensagem"
                                 oncomplete="#{LRAlunosCanceladosControle.mensagemNotificar}#{LRAlunosCanceladosControle.msgAlert}"
                                 accesskey="2"
                                 styleClass="botoes nvoBt btSec">
                    <i class="fa-icon-print"></i>
                </a4j:commandLink>

            </center>
        </a4j:form>
    </rich:modalPanel>

    <%------------------------- MODAL 'CONSULTAR' ----------------------------------------%>
    <rich:modalPanel id="listasRelatorios" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Relátorio de Alunos Cancelados"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="relatorios"/>
                <rich:componentControl for="listasRelatorios" attachTo="relatorios" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <jsp:include page="topoReduzido.jsp"/>
        <br/>
        <a4j:form>
            <h:panelGrid columns="2" columnClasses="colunaDireita, colunaEsquerda">

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.nome}" value="Nome: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.nome}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.nome}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.sexo}" value="Sexo: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.sexo}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.sexo}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.logradouro}"
                              value="Endereço: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.logradouro}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.logradouro}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.numero}" value="Número: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.numero}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.numero}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.bairro}" value="Bairro: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.bairro}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.bairro}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.cidade}" value="Cidade: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.cidade}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.cidade}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.cep}" value="Cep: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.cep}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.cep}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.complemento}"
                              value="Complemento: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.complemento}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.complemento}"/>


                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.dataMatriculaApresentar}"
                              value="Data Matrícula: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.dataMatriculaApresentar}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.dataMatriculaApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.dataInicioPlanoApresentar}"
                              value="Inicio Plano: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.dataInicioPlanoApresentar}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.dataInicioPlanoApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.dataVencimentoPlanoApresentar}"
                              value="Vencimento Plano: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.dataVencimentoPlanoApresentar}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.dataVencimentoPlanoApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.plano}" value="Plano: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.plano}"
                              value="#{LRAlunosCanceladosControle.itemRelatorio.plano}"/>

                <h:outputText styleClass="text" style="font-weight: bold;"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.consultores}"
                              value="Consultor: "/>
                <h:panelGroup>
                    <c:forEach items="${LRAlunosCanceladosControle.itemRelatorio.consultores}" var="consultor">
                        <span rendered="${not empty LRAlunosCanceladosControle.itemRelatorio.consultores}"
                              class="text">${consultor.pessoa.nome}</span><br/>
                    </c:forEach>
                </h:panelGroup>


                <h:outputText styleClass="text" style="font-weight: bold;"
                              rendered="#{not empty LRAlunosCanceladosControle.itemRelatorio.professores}"
                              value="Professor: "/>
                <h:panelGroup>
                    <c:forEach items="${LRAlunosCanceladosControle.itemRelatorio.professores}" var="professor">
                        <span rendered="${not empty LRAlunosCanceladosControle.itemRelatorio.professores}"
                              class="text">${professor.pessoa.nome}</span><br/>
                    </c:forEach>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>

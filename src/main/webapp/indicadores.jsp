<%--
    Document   : indicadores
    Created on : 02/05/2018, 09:23:09
    Author     : arthur
--%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="/includes/imports.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">



<head>
    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
</head>
<link rel="shortcut icon" href="./favicon.ico">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<style type="text/css">
    .menuLateralPontos{
        float: right;
        position: relative;
        border-width: 2px;
        width: 516px;
        height: 100px;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_indicadores}"/>
    </title>
    <h:form id="form">
        <html>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemClubeVantagens" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo zw_ui">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup id="panelConteudo">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial container-conteudo-central">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="#{msg_aplic.prt_clube_vantagens_configuracao_pontos}"
                                                          styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{ItemCampanhaControle.linkWiki}"
                                                          title="Clique e saiba mais: #{msg_aplic.prt_clube_vantagens_configuracao_pontos}"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>

                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box" id="panelGeral" style="min-height: 460px">
                                        <h:panelGrid id="gridBotoes" columns="1">
                                            <h:panelGroup>
                                                <div id="botoesTL" style="display: inline-flex; padding-bottom: 10px; width: 100%;">
                                                    <a4j:commandLink styleClass="botaoModoTimeLine plano btnCampanha}" style="margin-left: 10px;"
                                                                     onclick="trocarBloco('.plano', '.panelCampanhas', '.botaoModoTimeLine');"
                                                                     status="false"
                                                                     id="btnPlano">
                                                            Plano
                                                    </a4j:commandLink>
                                                    <a4j:commandLink styleClass="botaoModoTimeLine acesso btnCampanha}"
                                                                     style="margin-left: 10px;"
                                                                     onclick="trocarBloco('.acesso', '.panelCampanhas', '.botaoModoTimeLine');"
                                                                     status="false"
                                                                     id="btnAcesso">
                                                            Acesso
                                                    </a4j:commandLink>
                                                    <a4j:commandLink styleClass="botaoModoTimeLine aulasColetivas btnCampanha}"
                                                                     style="margin-left: 10px;"
                                                                     onclick="trocarBloco('.aulasColetivas', '.panelCampanhas', '.botaoModoTimeLine');"
                                                                     status="false"
                                                                     id="btnAulasColetivas">
                                                            Aulas Coletivas
                                                    </a4j:commandLink>        
                                                    <a4j:commandLink styleClass="botaoModoTimeLine produtos btnCampanha}"
                                                                     style="margin-left: 10px;"
                                                                     onclick="trocarBloco('.produtos', '.panelCampanhas', '.botaoModoTimeLine');"
                                                                     status="false"
                                                                     id="btnProdutos">
                                                            Produtos
                                                    </a4j:commandLink>        
                                                    <a4j:commandLink styleClass="botaoModoTimeLine indicacoes btnCampanha}"
                                                                     style="margin-left: 10px;"
                                                                     onclick="trocarBloco('.indicacoes', '.panelCampanhas', '.botaoModoTimeLine');"
                                                                     status="false"
                                                                     id="btnIdicacoes">
                                                            Indicações
                                                    </a4j:commandLink>
                                                    <a4j:commandLink styleClass="botaoModoTimeLine geral btnCampanha}"
                                                                     style="margin-left: 10px;"
                                                                     onclick="trocarBloco('.geral', '.panelCampanhas', '.botaoModoTimeLine');"
                                                                     status="false"
                                                                     id="btnGeral">
                                                            Geral
                                                    </a4j:commandLink>

                                                </div>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <rich:spacer height="10px"/>

                                        <h:panelGrid columns="1">
                                            <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_Brinde_empresa}" />
                                            <h:panelGroup styleClass="font-size-em-max">
                                                <h:panelGroup id="groupEmpresa" layout="block" styleClass="cb-container margenVertical" >
                                                    <h:selectOneMenu id="empresaSelecionadaCV" tabindex="8" onblur="blurinput(this);" style="font-size: 14px !important;" onfocus="focusinput(this);"
                                                                     value="#{ItemCampanhaControle.empresaSelecionada.codigo}" >
                                                        <f:selectItems value="#{ItemCampanhaControle.listaEmpresas}"    />
                                                        <a4j:support event="onchange" action="#{ItemCampanhaControle.abrirItemCampanha}"
                                                                     reRender="groupEmpresa,gridBotoes,panelConteudo,panelGeral"/>
                                                    </h:selectOneMenu>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <rich:spacer height="10px"/>
                                        <!-- Planos -->
                                        <jsp:include page="includes/clubedevantagens/include_clube_vantagens_planos.jsp" flush="true"/>
                                        <!-- Acessos -->
                                        <jsp:include page="includes/clubedevantagens/include_clube_vantagens_acessos.jsp" flush="true"/>
                                        <!-- AULAS Coletivas -->
                                        <jsp:include page="includes/clubedevantagens/include_clube_vantagens_aulas_coletivas.jsp" flush="true"/>
                                        <!-- Produtos -->
                                        <jsp:include page="includes/clubedevantagens/include_clube_vantagens_produtos.jsp" flush="true"/>
                                        <!-- Indicações -->
                                        <jsp:include page="includes/clubedevantagens/include_clube_vantagens_indicacoes.jsp" flush="true"/>
                                        <!-- Geral -->
                                        <jsp:include page="includes/clubedevantagens/include_clube_vantagens_geral.jsp" flush="true"/>

                                    </h:panelGroup>

                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true">
                            <jsp:param name="menu" value="ADM-CLUBE_VANTAGENS" />
                        </jsp:include>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>
        <h:panelGroup layout="block" id="containerFuncMask">
            <script>
                function limparNomeProduto(){
                    //document.getElementById('form:nomeProduto').value = ""
                    //document.getElementById("form:btnProdutos").click();
                }
                function limparNomePlano(){
                    //document.getElementById('form:nomePlano').value = ""
                    //document.getElementById("form:btnPlano").click();
                }
                function limparNomeAula(){
                    //document.getElementById('form:nomeAula').value = ""
                    //document.getElementById("form:btnAulasColetivas").click();
                }
                function efeitoClick(){
                    //document.getElementById("form:btnAcesso").click();
                }

                var ultimoBlocoTrocado = {
                    bloco: '.plano',
                    container: '.panelCampanhas',
                    tipoBotao: '.botaoModoTimeLine'
                };

                function trocarUltimoBloco(){
                    trocarBloco(ultimoBlocoTrocado.bloco, ultimoBlocoTrocado.container, ultimoBlocoTrocado.tipoBotao);
                }
                function trocarBloco(bloco, container, tipoBotao) {
                    ultimoBlocoTrocado.bloco = bloco;
                    ultimoBlocoTrocado.container = container;
                    ultimoBlocoTrocado.tipoBotao = tipoBotao;
                    jQuery(container + '.visivel').slideUp();
                    jQuery(container + bloco).slideDown();
                    jQuery(container + bloco).addClass('visivel');
                    jQuery(tipoBotao).removeClass('ativo');
                    jQuery(tipoBotao + bloco).addClass('ativo');
                }
                trocarUltimoBloco();
            </script>
        </h:panelGroup>
    </h:form>
    <rich:modalPanel id="mdlAvisoExcluirCampanha" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Exclusão da Campanha"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:outputText value="Deseja realizar a exclusão dessa Campanha?" style="color: #777;font-size: 16px;font-family: Arial;"/>
                <h:panelGroup layout="block">
                    <a4j:commandLink  action="#{ClubeVantagensControle.excluir}" 
                                       id="confirmacaoExclusaoCampanha"
                                       reRender="form,mdlAvisoExcluirCampanha"
                                       accesskey="2"
                                       styleClass="pure-button pure-button-primary"
                                       style="margin-top: 16px;"
                                       oncomplete="#{ClubeVantagensControle.mensagemNotificar};#{ClubeVantagensControle.oncomplete}">
                        Sim
                    </a4j:commandLink>
                    <a4j:commandLink value="Não" status="false" onclick="Richfaces.hideModalPanel('mdlAvisoExcluirCampanha');" reRender="mdlAvisoExcluirCampanha" id="confirmacaoOpercaoNao" styleClass="pure-button"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <script>
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    </script>
</f:view>

<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Ajuste de saldo da conta corrente do cliente" />
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Ajustes Saldo Conta Corrente"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-lancar-credito-ou-debito-na-conta-corrente-do-aluno/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    </h:panelGrid>
    <h:form id="form">

        <a4j:keepAlive beanName="AjusteSaldoContaCorrenteControle"/>
            <h:panelGrid columns="1" width="100%"  id="dadosAjusteConta" styleClass="font-size-em-max">
                <h:panelGroup>
                    <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                    <h:outputText id="clienteNomeAuto" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AjusteSaldoContaCorrenteControle.cliente.pessoa.nome}"/>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText value="SALDO ATUAL: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                    <h:outputText id="valorFinal" styleClass="texto-size-14 texto-font" style="color:#{AjusteSaldoContaCorrenteControle.debito ? 'red' : 'green'}"
                                  value="#{AjusteSaldoContaCorrenteControle.empresaLogado.moeda} #{AjusteSaldoContaCorrenteControle.ultimoMovimento.saldoAtual_Apresentar}"/>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                </h:panelGroup>
                <rich:spacer height="5px"/>
                <h:outputText value="PAGAMENTOS: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                              rendered="#{!AjusteSaldoContaCorrenteControle.debito && !AjusteSaldoContaCorrenteControle.finalizado}"/>

                <h:panelGrid columns="1" styleClass="font-size-em-max">
                    <h:outputText value="NOVO VALOR DO DÉBITO: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                              rendered="#{AjusteSaldoContaCorrenteControle.debito && !AjusteSaldoContaCorrenteControle.finalizado}"/>
                    <h:inputText value="#{AjusteSaldoContaCorrenteControle.novoValorContaCliente}"
                                 rendered="#{AjusteSaldoContaCorrenteControle.debito}"
                                 size="10"
                                 maxlength="8"
                                 styleClass="inputTextClean">
                        <f:converter converterId="FormatadorNumerico"/>

                    </h:inputText>
                </h:panelGrid>
                <rich:dataTable value="#{AjusteSaldoContaCorrenteControle.ultimoMovimento.movPagamentosVOs}"
                                var="pagamento"
                                width="100%"
                                rendered="#{!AjusteSaldoContaCorrenteControle.debito}">
                    <rich:column>
                        <h:selectBooleanCheckbox value="#{pagamento.movPagamentoEscolhidaFinan}"
                                                 rendered="#{pagamento.diferenteChequeCartao}">
                            <a4j:support event="onclick" action="#{AjusteSaldoContaCorrenteControle.calcularDevolucao}"
                                         reRender="form"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{pagamento.dataLancamentoSemHora_Apresentar}" />
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value=" - #{pagamento.formaPagamento.descricao} - #{AjusteSaldoContaCorrenteControle.empresaLogado.moeda} " />

                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{pagamento.valorApresentar}" rendered="#{!pagamento.movPagamentoEscolhidaFinan}" />
                        <h:inputText value="#{pagamento.valorReceberOuDevolverContaCorrente}"
                                     rendered="#{pagamento.movPagamentoEscolhidaFinan}"
                                     size="10"
                                     maxlength="8">
                            <f:converter converterId="FormatadorNumerico"/>
                            <a4j:support event="onchange" action="#{AjusteSaldoContaCorrenteControle.verificarAlteracaoValor}"
                                         reRender="form"
                                         oncomplete="#{AjusteSaldoContaCorrenteControle.msgAlert}"/>
                        </h:inputText>


                        <rich:dataTable value="#{pagamento.chequeVOs}"
                                var="cheque" rendered="#{fn:length(pagamento.chequeVOs) > 0}"
                                width="100%" styleClass="tabelaDados semZebra">
                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Devolver"/>
                                </f:facet>
                                <h:selectBooleanCheckbox value="#{cheque.chequeEscolhido}"
                                                         rendered="#{!cheque.temLote && !cheque.temComposicao}">
                                    <a4j:support event="onclick" action="#{AjusteSaldoContaCorrenteControle.calcularDevolucao}"
                                                 reRender="panelGroupValorTransferencia"/>
                                </h:selectBooleanCheckbox>
                                <h:graphicImage rendered="#{cheque.temComposicao && !cheque.temLote}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cheque não pode ser devolvido pois está sendo usado para pagar uma parcela."/>

                                <h:graphicImage rendered="#{cheque.temLote}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cheque não pode ser devolvido pois já está no lote #{cheque.loteVO.codigo}. Retire o cheque do lote no financeiro para realizar a devolução."/>

                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Banco"/>
                                </f:facet>
                                <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cheque.banco.nome}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Agência"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cheque.agencia}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Conta"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cheque.conta}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Nr."/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cheque.numero}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Compensação"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cheque.dataCompensacao_Apresentar}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Valor"/>
                                </f:facet>
                                <h:outputText id="valor" value="#{AjusteSaldoContaCorrenteControle.empresaLogado.moeda} #{cheque.valor}" styleClass="texto-size-14-real texto-cor-cinza texto-font"/>
                            </rich:column>
                        </rich:dataTable>

                         <rich:dataTable value="#{pagamento.cartaoCreditoVOs}"
                                var="cartao" rendered="#{fn:length(pagamento.cartaoCreditoVOs) > 0}"
                                width="100%" styleClass="tabelaDados semZebra" >

                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Estornar"/>
                                </f:facet>
                                <h:selectBooleanCheckbox value="#{cartao.cartaoEscolhido}" rendered="#{!cartao.temComposicao && !cartao.temLote}">
                                    <a4j:support event="onclick" action="#{AjusteSaldoContaCorrenteControle.calcularDevolucao}"
                                                 reRender="panelGroupValorTransferencia"/>
                                </h:selectBooleanCheckbox>

                                <h:graphicImage rendered="#{cartao.temComposicao && !cartao.temLote}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cartão não pode ser devolvido pois está sendo usado para pagar uma parcela."/>

                                <h:graphicImage rendered="#{cartao.temLote}" styleClass="linkWiki" url="/imagensCRM/informacao.gif"
                                                    title="Este cartão não pode ser devolvido pois já está no lote #{cartao.lote.codigo}. Retire o cartão do lote no financeiro para realizar a devolução."/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Operadora"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cartao.operadora.descricao}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Compensação"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cartao.dataCompensacao_Apresentar}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                     <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Valor"/>
                                </f:facet>
                                <h:outputText id="valor" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{AjusteSaldoContaCorrenteControle.empresaLogado.moeda} #{cartao.valorFormatado}">
                                </h:outputText>
                            </rich:column>
                        </rich:dataTable>

                    </rich:column>
                </rich:dataTable>

                <h:panelGrid id="panelGroupValorTransferencia" columns="2" cellpadding="3" styleClass="font-size-em-max">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                        value="VALOR DEVOLVER: " rendered="#{!AjusteSaldoContaCorrenteControle.debito && !AjusteSaldoContaCorrenteControle.finalizado}"/>
                    <h:outputText id="valorFinalDevolver" styleClass="texto-size-14 texto-cor-cinza texto-font"
                                  rendered="#{!AjusteSaldoContaCorrenteControle.debito
                                              && !AjusteSaldoContaCorrenteControle.finalizado
                                              && !AjusteSaldoContaCorrenteControle.devolverRestanteEmDinheiro}"
                                  value="#{AjusteSaldoContaCorrenteControle.empresaLogado.moeda} #{AjusteSaldoContaCorrenteControle.valorDevolverClienteFormatado}">
                            </h:outputText>
                    <h:outputText id="valorFinalsaldoDevolverTodos" styleClass="texto-size-14 texto-cor-cinza texto-font"
                                  rendered="#{AjusteSaldoContaCorrenteControle.devolverRestanteEmDinheiro  && !AjusteSaldoContaCorrenteControle.finalizado}"
                                  value="#{AjusteSaldoContaCorrenteControle.empresaLogado.moeda} #{AjusteSaldoContaCorrenteControle.ultimoMovimento.saldoAtualFormatado}">
                    </h:outputText>

                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  value="SALDO RESTANTE:"
                                  rendered="#{!AjusteSaldoContaCorrenteControle.debito && !AjusteSaldoContaCorrenteControle.finalizado}"/>

                    <h:outputText id="valorFinalsaldo" styleClass="texto-size-14 texto-cor-cinza texto-font"
                                  rendered="#{!AjusteSaldoContaCorrenteControle.devolverRestanteEmDinheiro
                                              && !AjusteSaldoContaCorrenteControle.debito  && !AjusteSaldoContaCorrenteControle.finalizado}"
                                  value="#{AjusteSaldoContaCorrenteControle.novoValorContaCliente}"/>
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" rendered="#{AjusteSaldoContaCorrenteControle.devolverRestanteEmDinheiro  && !AjusteSaldoContaCorrenteControle.finalizado}"
                                  value="0,00"/>
                </h:panelGrid>
                <h:panelGroup rendered="#{!AjusteSaldoContaCorrenteControle.debito && !AjusteSaldoContaCorrenteControle.finalizado}">
                <h:panelGroup styleClass="chk-fa-container" style="display:inline-block">
                    <h:selectBooleanCheckbox value="#{AjusteSaldoContaCorrenteControle.devolverRestanteEmDinheiro}" >
                        <a4j:support reRender="panelGroupValorTransferencia" event="onclick"/>
                    </h:selectBooleanCheckbox>
                    <span/>
                </h:panelGroup>

                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" value=" DEVOLVER O RESTANTE EM DINHEIRO"/>
                </h:panelGroup>
                

                <h:panelGrid id="msgTfr" columns="2" width="100%" styleClass="font-size-em-max">
                    <h:panelGrid columns="1" width="100%">
                        <h:commandButton rendered="#{AjusteSaldoContaCorrenteControle.erro}" image="./imagens/erro.png"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgTransfereciaVerde" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="#{AjusteSaldoContaCorrenteControle.mensagem}"/>
                        <h:outputText id="msgTransferenciaDeta" styleClass="tituloCamposNegritoMaiorVermelho" value="#{AjusteSaldoContaCorrenteControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGroup rendered="#{!AjusteSaldoContaCorrenteControle.finalizado}">
                    <div align="center" >
                    <rich:spacer width="7"/>
                        <a4j:commandLink id="confirmar"
                            action="#{AjusteSaldoContaCorrenteControle.confirmarAlteracaoContaCorrente}"
                            reRender="panelAutorizacaoFuncionalidade, form"
                            styleClass="pure-button pure-button-primary texto-font">
                        <i class="fa-icon-ok"></i>&nbsp;Confirmar
                        </a4j:commandLink>
                    <div>
                </h:panelGroup>

                <h:panelGroup rendered="#{AjusteSaldoContaCorrenteControle.finalizado}">
                    <div align="center" >
                        <a4j:commandLink id="imprimir" rendered="#{AjusteSaldoContaCorrenteControle.reciboDevolucao != null}"
                            action="#{AjusteSaldoContaCorrenteControle.imprimirReciboDevolucao}"
                            styleClass="pure-button texto-font" value="Imprimir"
                            oncomplete="abrirPopupPDFImpressao('relatorio/#{AjusteSaldoContaCorrenteControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);"/>
                        <h:commandLink id="fechar"   styleClass="pure-button texto-font"  value="Fechar" onclick="fecharJanela();"/>
                    <div>
                </h:panelGroup>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>

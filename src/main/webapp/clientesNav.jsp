<%@page pageEncoding="ISO-8859-1" %>
<script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
<%@include file="/includes/include_import_minifiles.jsp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<link href="css_pacto.css" rel="stylesheet" type="text/css">
<link href="${root}/beta/css/pacto-icon-font4.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
<body class="paginaFontResponsiva">
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:keepAlive beanName="RedirectClientesNovoFrontControle"/>

    <style>
        .clienteNovoClass {
            padding-top: 25%;
            display: grid;
            align-items: center;
            justify-content: center;
            text-align: center;
            justify-items: center;
        }

        .clienteNovoClassMsg{
            font-size: 20px;
            font-family: Calibri;
            color: #777;
            font-weight: bold;
            padding-bottom: 20px;
        }
    </style>

    <title>Cliente</title>

    <h:form id="form">
        <div class="clienteNovoClass">
            <span class="clienteNovoClassMsg">Carregando...</span>
            <h:graphicImage url="/images/loader-small.gif"/>
        </div>
        <a4j:jsFunction name="verificarAbrirNovaTelaClienteNew" status="false"
                        oncomplete="#{RedirectClientesNovoFrontControle.urlNovoCliente}">
        </a4j:jsFunction>
    </h:form>
</f:view>
</body>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        try {
            verificarAbrirNovaTelaClienteNew();
        } catch (e) {
            console.log(e);
        }
    });
</script>

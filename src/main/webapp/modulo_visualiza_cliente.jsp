    <%--
    Document   : modulo_visualiza_cliente
    Created on : 07/05/2012, 14:45:55
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css_pacto.css" rel="stylesheet" type="text/css">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@include file="includes/imports.jsp" %>

<%@include file="/includes/include_identificadorModuloEstudio.jsp" %>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="Edição Cliente" /></title>

    <h:form id="form">
        <jsp:include page="include_head.jsp" flush="true" />
        <table width="100%" border="0" cellpadding="0"
               cellspacing="0">
            <c:if test="${MenuControle.apresentarTopo}">
                <tr>
                    <td height="77" align="left" valign="top" class="bgtop">
                        <jsp:include page="${contextoModulo}/includes/include_top.jsp" flush="true" />
                    </td>
                </tr>

                <tr>
                    <td height="48" align="left" valign="top" class="bgmenu">
                        <jsp:include page="${contextoModulo}/includes/include_menu.jsp" flush="true" />
                    </td>
                </tr>
            </c:if>
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <a4j:outputPanel id="panelRecarregar">
                        <table width="100%" border="0" cellpadding="0"
                               cellspacing="0">
                            <tr>
                                <td width="150" align="left" valign="top" class="bglateraltop"
                                    style="padding-top:5px;padding-left: 17px; padding-right: 17px">
                                    <jsp:include page="include_box_usuario.jsp" flush="true" />
                                    <jsp:include page="${contextoModulo}/includes/include_box_descricao.jsp" />
                                </td>
                                <td align="left" valign="top" style="padding-left: 5px; padding-right: 0px;">
                                    <jsp:include page="include_cadastro_cliente.jsp" flush="true"/>
                                </td>
                            </tr>
                        </table>
                    </a4j:outputPanel>
                </td>
            <tr>
                <td height="93" align="left" valign="top" class="bgrodape">
                    <jsp:include page="include_rodape.jsp" flush="true" />
                </td>
            </tr>
        </table>
    </h:form>
    <jsp:include page="include_modal/aulasDesmarcadas.jsp" flush="true" />
    <jsp:include page="include_modal/modalControleCreditoTreino.jsp" flush="true"/>
    <jsp:include page="include_modal/modalConvidado.jsp" flush="true"/>
    <jsp:include page="include_modal/modalConvidadoHistorico.jsp" flush="true"/>
    <jsp:include page="include_modal/modalDesfazerCancelamento.jsp" flush="true"/>
    <jsp:include page="include_modal/modalEnviarNotaEmail.jsp" flush="true"/>
    <jsp:include page="include_modal/modalFaltasAluno.jsp" flush="true"/>
    <jsp:include page="include_modal/modalFiltroCredito.jsp" flush="true"/>
    <jsp:include page="include_modal/modalGymPass.jsp" flush="true"/>
    <jsp:include page="include_modal/modalGymPassHistorico.jsp" flush="true"/>
    <jsp:include page="include_modal/modalTotalPass.jsp" flush="true"/>
    <jsp:include page="include_modal/modalTotalPassHistorico.jsp" flush="true"/>
    <jsp:include page="include_modal/modalPanelContratoArmario.jsp" flush="true"/>
    <jsp:include page="include_modal/modalRemoverObjecaoDefinitiva.jsp" flush="true"/>
    <jsp:include page="include_modal/modalTrocarCartaoContratoCliente.jsp" flush="true"/>
    <jsp:include page="include_modal/panelAgendaAluno.jsp" flush="true"/>
    <jsp:include page="include_modal/panelAlterarMatricula.jsp" flush="true"/>
    <jsp:include page="include_modal/panelAulaDesmarcada.jsp" flush="true"/>
    <jsp:include page="include_modal/panelAutorizarEdicaoPagamento.jsp" flush="true"/>
    <jsp:include page="include_modal/panelContratoOperacao.jsp" flush="true"/>
    <jsp:include page="include_modal/panelHistoricoContrato.jsp" flush="true"/>
    <jsp:include page="include_modal/panelIncludeContratoPrestacao.jsp" flush="true"/>
    <jsp:include page="include_modal/panelParcelasRemessas.jsp" flush="true"/>
    <jsp:include page="include_modal/panelStatusTrocaCartao.jsp" flush="true"/>
    <jsp:include page="include_modal/panelUsuarioSenhaObservacaoGeral.jsp" flush="true"/>
    <jsp:include page="includes/cliente/include_modal_editar_vigencia_final_produto_cliente.jsp" flush="true"/>
    <jsp:include page="includes/cliente/include_modal_escolher_produto_renovar_cliente.jsp" flush="true"/>
    <jsp:include page="includes/cliente/include_modal_reposicoes.jsp" flush="true"/>
    <h:panelGroup id="panelIncludeMensagem" layout="block">
        <jsp:include page="include_mensagem_cliente.jsp" flush="true"/>
    </h:panelGroup>
</f:view>



<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>
  Date: 20/12/2019
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-forms.css" type="text/css" rel="stylesheet"/>
<script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>

<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    .panelBotoesImportacao {
        background: #eee;
        padding: 30px;
        text-align: center;
    }

    .textoObjetivo {
        font-size: 16px;
        color: #333;
    }

    .passosImportacao {
        font-size: 15px;
        font-weight: bold;
        font-style: italic;
        color: #333;
    }

    .passosImportacaoDescricao {
        font-size: 15px;
        color: #333;
    }

    .panelObjetivo {
        padding: 20px 20px 0px 20px;
    }

    .panelPassos {
        padding: 10px 10px 10px 30px;
    }

    .panelPassosInterno {
        padding: 10px 10px 10px 10px;
    }

    .colunaEsquerdaImport {
        width: 15%;
        text-align: right;
    }

    .colunaDireitaImport {
        width: 60%;
        text-align: left;
    }

    .observacoesImportantes {
        font-style: italic;
        font-weight: bold;
    }

    .observacoesImportantesItem {
        padding-left: 10px;
    }

    .rich-tab-active {
        background: #29abe2 !important;
        border-color: #29abe2;
        font-weight: bold;
        padding: 5px;
        /* border-bottom-color: white; */
        /* border-bottom-width: 1px; */
        color: #fff;
        border-bottom: none;
    }

    .rich-tab-inactive {
        background: #eee !important;
        border: none !important;
    }

    .rich-tab-header {
        font-size: 14px;
        font-family: arial, helvetica, sans-serif;
    }

    .rich-tabhdr-cell-disabled {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-cell-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-cell {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-border {
        background: none !important;
        border: none !important;
    }

    .rich-tab-bottom-line {
        border-width: 1px;
        border-color: #29abe2;
    }

    .rich-tabpanel-content {
        border-bottom: none;
        border-right: none;
        border-left: none;
        border-top: 1px solid #29abe2;
    }
</style>

<script>
    jQuery.noConflict();
</script>

<%@include file="includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_Importacao_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Importacao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-importar-dados-para-o-sistema-pacto/"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:form id="form" styleClass="pure-form" style="background: #FFF">
        <input type="hidden" value="${modulo}" name="modulo"/>
        <rich:tabPanel width="100%" selectedTab="#{ImportacaoControle.abaAtual}" switchType="ajax">

            <rich:tab id="abaCliente" label="Cliente"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacao}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_cliente.jsp" %>
            </rich:tab>

            <rich:tab id="abaContrato" label="Contrato"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacao}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_contrato.jsp" %>
            </rich:tab>

            <rich:tab id="abaColaborador" label="Colaborador"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacaoColaborador}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_colaborador.jsp" %>
            </rich:tab>

            <rich:tab id="abaFornecedor" label="Fornecedor"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacaoFornecedor}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_fornecedor.jsp" %>
            </rich:tab>

            <rich:tab id="abaProduto" label="Produto"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacaoProduto}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_produto.jsp" %>
            </rich:tab>

            <rich:tab id="abaConta" label="Contas Financeiro"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacaoConta}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_conta.jsp" %>
            </rich:tab>

            <rich:tab id="abaImportacaoMember" label="Importação Members"
                      rendered="#{LoginControle.usuario.usuarioPACTOBR}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_member.jsp" %>
            </rich:tab>

            <rich:tab id="abaImportacaoAlunoTurma" label="Importação de Aluno nas Turmas"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacaoAlunoTurma}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_aluno_turma.jsp" %>
            </rich:tab>

<%--            <rich:tab id="abaImportacaoNextFit" label="Importação NextFit"--%>
<%--                      rendered="#{LoginControle.usuario.usuarioPACTOBR}"--%>
<%--                      style="border-bottom: none; border-right: none; border-left: none"--%>
<%--                      oncomplete="carregarTooltipster();">--%>
<%--                <%@include file="include_importacao_nextFit.jsp" %>--%>
<%--            </rich:tab>--%>

<%--            <rich:tab id="abaImportacaoCloudGyn" label="Importação CloudGyn"--%>
<%--                      rendered="#{LoginControle.usuario.usuarioPACTOBR}"--%>
<%--                      style="border-bottom: none; border-right: none; border-left: none"--%>
<%--                      oncomplete="carregarTooltipster();">--%>
<%--                <%@include file="include_importacao_cloudGyn.jsp" %>--%>
<%--            </rich:tab>--%>

            <rich:tab id="abaImportacaoTurma" label="Importação de Turmas"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacaoTurma}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_turma.jsp" %>
            </rich:tab>

            <rich:tab id="abaParcelasPagamentos" label="Parcelas e Pagamentos"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacao}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_parcelas_pagamentos.jsp" %>
            </rich:tab>

            <rich:tab id="abaHistorico" label="Histórico" reRender="abaHistorico"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();"
                      action="#{ImportacaoControle.consultarImportacoes}">
                <%@include file="include_importacao_historico.jsp" %>
            </rich:tab>

            <rich:tab id="abaTreino" label="Treino"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.importacao}"
                      style="border-bottom: none; border-right: none; border-left: none"
                      oncomplete="carregarTooltipster();">
                <%@include file="include_importacao_treino.jsp" %>
            </rich:tab>

        </rich:tabPanel>
    </h:form>

    <%@include file="/include_importacao_modais.jsp" %>
</f:view>

<script>
    function carregarTooltipster() {
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }

    function bloquearEnter() {
        var tecla = window.event.keyCode;
        if (tecla == 13) {
            event.keyCode = 0;
            event.returnValue = false;
        }
    }

    function adicionarEmail() {
        var tecla = window.event.keyCode;
        if (tecla == 13) {
            document.getElementById("formModImpo:adicionarEmail").click();
            event.keyCode = 0;
            event.returnValue = false;
        }
    }

    function adicionarPlaceHolderImportacao() {
        if (document.getElementById("formModImpo:emailAdicionar") != null) {
            document.getElementById("formModImpo:emailAdicionar").setAttribute("placeholder", "E-mail");
        }
    }

    function atualizarTempoImportacao() {
        try {
            fireElementFromAnyParent('form:btnAtualizaTempo')
        } catch (ex) {
            console.log('Erro [atualizarTempoImportacao] ' + ex)
        }
    }

    carregarTooltipster();
</script>

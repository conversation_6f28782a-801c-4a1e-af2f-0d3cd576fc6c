<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="/includes/include_import_minifiles.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<head>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <style type="text/css">
        .rich-panel {
            background-color: transparent;
            border: none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-panel-body {
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }
    </style>
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Parcelas vencidas em aberto"/>
    </title>
    <html>
    <h:form id="form" prependId="true" styleClass="pure-form no-ext-css">
        <c:set var="titulo" scope="session" value="Parcelas Vencidas em Aberto"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-cobrancas-por-convenio-adm/"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>
        <h:panelGrid width="100%" style="text-align: right">
            <h:panelGroup layout="block">
                <a4j:commandLink id="btnExcel"
                                 style="margin-left: 8px;"
                                 actionListener="#{GestaoRemessasControle.exportarParcelasVencidas}"
                                 rendered="#{fn:length(GestaoRemessasControle.movParcelaTransacaoTO) > 0}"
                                 oncomplete="#{GestaoRemessasControle.mensagemNotificar}#{GestaoRemessasControle.msgAlert}"
                                 accesskey="1" styleClass="linkPadrao">

                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="nome=Nome Cliente,empresaCodigoApresentar=Cod. Empresa,contratoApresentar=Contrato,parcelaCodigoApresentar=Codigo Parcela,descricao=Parcela,valorParcelaApresentar=Vlr. Parcela,dataVencimentoApresentar= Venc. Parcela,nomeConvenioCobranca=Convênio Cobrança,nomeEmpresa=Empresa,nomeConsultor=Consultor,emRemessa=Em Remessa"/>
                    <f:attribute name="prefixo" value="Parcelas Vencidas em Aberto"/>

                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>

                <a4j:commandLink id="btnPdf"
                                 style="margin-left: 8px;"
                                 actionListener="#{GestaoRemessasControle.exportarParcelasVencidas}"
                                 rendered="#{fn:length(GestaoRemessasControle.movParcelaTransacaoTO) > 0}"
                                 oncomplete="#{GestaoRemessasControle.mensagemNotificar}#{GestaoRemessasControle.msgAlert}"
                                 accesskey="2" styleClass="linkPadrao">

                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                 value="nome=Nome Cliente,empresaCodigoApresentar=Cod. Empresa,contratoApresentar=Contrato,parcelaCodigoApresentar=Codigo Parcela,descricao=Parcela,valorParcelaApresentar=Vlr. Parcela,dataVencimentoApresentar= Venc. Parcela,nomeConvenioCobranca=Convênio Cobrança,nomeEmpresa=Empresa,nomeConsultor=Consultor,emRemessa=Em Remessa"/>
                    <f:attribute name="prefixo" value="Parcelas Vencidas em Aberto"/>

                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
        <a4j:outputPanel>
            <h:panelGrid columns="1" columnClasses="w50 alinhar,w50 alinhar" width="100%" rowClasses="margin-v-10"
                         styleClass="forcar-v-margin">
                <rich:panel
                        rendered="#{fn:length(GestaoRemessasControle.movParcelaTransacaoTO) > 0}">
                    <rich:dataTable rowKeyVar="status" id="tblParcelasRemessaTrocar" width="100%"
                                    styleClass="tabelaSimplesCustom"
                                    value="#{GestaoRemessasControle.movParcelaTransacaoTO}"
                                    rows="15" var="parc">
                        <%@include file="/includes/remessas/include_dadosParcelasVencidasEmAberto.jsp" %>
                    </rich:dataTable>
                    <rich:datascroller for="tblParcelasRemessaTrocar" status="false" renderIfSinglePage="false"/>
                </rich:panel>
            </h:panelGrid>
            <h:outputLabel id="mensagem" styleClass="texto-cor-azul texto-cor-cinza texto-font texto-size-14-real"
                           value="Este é apenas um relatório para simples conferência. Para realizar ações com essas parcelas você deverá utilizar o gestão de remessas para convênios (EDI) e gestão de transações para convênios (E-commerce online)."
                           style="display: block; text-align: left; margin-top: 5px; margin-left: 3px; color: red;"/>

            <h:outputLabel id="totalItens" styleClass="texto-cor-azul texto-cor-cinza texto-font texto-size-16-real"
                           style="display: block; text-align: left; margin-top: 10px; margin-left: 3px;"
                           value=" Total: #{fn:length(GestaoRemessasControle.movParcelaTransacaoTO)} #{fn:length(GestaoRemessasControle.movParcelaTransacaoTO) > 1 ? 'Itens' : 'Item'}" rendered="true"/>
            <br/>
        </a4j:outputPanel>
    </h:form>
    </html>
</f:view>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Negociação do Plano"/>
    </title>

    <rich:modalPanel id="panelPlanoModalidadeVezesSemana" autosized="true" shadowOpacity="true" width="450" height="250">        
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consultar Vezes por Semana"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>                
                <rich:componentControl for="panelPlanoModalidadeVezesSemana" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>        
        <a4j:form id="formPlanoModalidadeVezesSemana" ajaxSubmit="true">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">               
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Freüência"/>
                </h:panelGrid>  
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <rich:dataTable id="planoModalidadeVezesSemanaVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                    value="#{ContratoControle.planoModalidadeVO.planoModalidadeVezesSemanaVOs}" var="planoModalidadeVezesSemana">
                        <rich:column>                           
                            <h:selectBooleanCheckbox  value="#{planoModalidadeVezesSemana.vezeSemanaEscolhida}">
                                <a4j:support event="onclick" action="#{ContratoControle.selecionarVezesSemana}" reRender="planoModalidadeVezesSemanaVO"/>
                            </h:selectBooleanCheckbox>
                            <h:outputText  value="#{planoModalidadeVezesSemana.nrVezes}  Vezes por Semana" />
                        </rich:column> 
                        <h:column rendered="#{planoModalidadeVezesSemana.apresentarValorEspecifico || planoModalidadeVezesSemana.apresentarValorDesconto}">
                            <h:outputText value="#{planoModalidadeVezesSemana.tipoOperacao_Apresentar} "/>
                        </h:column>
                        <h:column rendered="#{planoModalidadeVezesSemana.apresentarValorEspecifico}">
                            <h:outputText value="R$ "/>
                            <h:outputText value="#{planoModalidadeVezesSemana.valorEspecifico}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:column>
                        <h:column rendered="#{planoModalidadeVezesSemana.apresentarValorDesconto}">                                                                                    
                            <h:outputText value="#{planoModalidadeVezesSemana.percentualDesconto} ">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value=" %"/>
                        </h:column>

                    </rich:dataTable>
                </h:panelGrid>
                <a4j:commandButton id="salvar" reRender="form, total,total1,subtotal,composicaoMarcada ,modalidadeMarcada" action="#{ContratoControle.calcularContrato}" oncomplete="Richfaces.hideModalPanel('panelPlanoModalidadeVezesSemana')" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
            </h:panelGrid>                       
        </a4j:form>
    </rich:modalPanel>    

    <rich:modalPanel id="panelProdutoSugerido" autosized="true" shadowOpacity="true" width="450" height="250">        
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Escolher Produto Sugerido"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>                
                <rich:componentControl for="panelProdutoSugerido" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>        
        <a4j:form id="formProdutoSugerido" ajaxSubmit="true">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">               
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Produto Sugerido"/>
                </h:panelGrid>  
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <rich:dataTable id="produtoSugeridoVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                    value="#{ContratoControle.contratoModalidadeVO.modalidade.produtoSugeridoVOs}" var="produtoSugerido">
                        <f:facet name="header">
                            <rich:columnGroup>                                
                                <rich:column>
                                    <h:outputText value="Descrição"/>
                                </rich:column>
                                <rich:column>
                                    <h:outputText value="Valor"/>
                                </rich:column>                                
                                <rich:column>
                                    <h:outputText value="Desconto"/>
                                </rich:column>                                
                                <rich:column>
                                    <h:outputText value=""/>
                                </rich:column>                                
                                <rich:column>
                                    <h:outputText value=""/>
                                </rich:column>                                
                            </rich:columnGroup>
                        </f:facet>                        

                        <rich:column>                           
                            <h:selectBooleanCheckbox disabled="#{produtoSugerido.obrigatorio}" value="#{produtoSugerido.produtoSugeridoEscolhida}">
                                <a4j:support event="onclick" action="#{ContratoControle.selecionarModalidadeProdutoSugerido}" reRender="produtoSugeridoVO"/>                              
                            </h:selectBooleanCheckbox>
                            <h:outputText  value="#{produtoSugerido.produto.descricao}" />
                        </rich:column>                                             
                        <rich:column>                                                       
                            <h:outputText  value="#{produtoSugerido.produto.valorFinal}">
                                <f:converter  converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </rich:column>                                             
                        <rich:column>                                                       
                            <h:inputText id="descontoProdutoSugerido" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" size="10" value="#{produtoSugerido.produto.desconto.valor}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>
                        </rich:column>                                             
                        <h:column>
                            <a4j:commandButton image="./images/icon_chave.png"/>
                        </h:column>
                        <h:column>
                            <a4j:commandButton action="#{ContratoControle.consultarDescontoModalidadeProdutoSugerido}" alt="Consutar Desconto" reRender="formDescontoProdutoSugerido" oncomplete="Richfaces.showModalPanel('panelDescontoProdutoSugerido')"  image="./images/icon_calculadora.png"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton action="#{ContratoControle.limparDescontoModalidadeProdutoSugerido}" alt="Limpar Desconto" reRender="produtoSugeridoVO" image="./images/limpar.gif"/>
                        </h:column>                                            
                    </rich:dataTable>                    
                </h:panelGrid>   
                <a4j:commandButton id="salvar" reRender="form,produtoMarcada,total,total1,subtotal" action="#{ContratoControle.calcularContrato}" oncomplete="Richfaces.hideModalPanel('panelProdutoSugerido')" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
            </h:panelGrid>                       
        </a4j:form>                                             
    </rich:modalPanel> 


    <rich:modalPanel id="panelDescontoProdutoSugerido" autosized="true" shadowOpacity="true" width="250" height="150">        
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta de Desconto Para Produto"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink5"/>
                <rich:componentControl for="panelDescontoPlanoProdutoSugerido" attachTo="hidelink5" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formDescontoProdutoSugerido" ajaxSubmit="true">          
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">  
                <rich:dataTable id="resultadoConsultaDescontoProdutoSugerido" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{ContratoControle.listaConsultaDesconto}" rows="5" var="desconto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Desconto_descricao}" />
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}" focus="descontoProdutoSugerido" reRender="formProdutoSugerido,produtoSugeridoVO" oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')" value="#{desconto.descricao}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Desconto_tipoDesconto}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}" focus="descontoProdutoSugerido" reRender="formProdutoSugerido ,produtoSugeridoVO" oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')" value="#{desconto.tipoProduto_Apresentar}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Desconto_valor}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}" focus="descontoProdutoSugerido" reRender="formProdutoSugerido,produtoSugeridoVO" oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')" >
                                <h:outputText value="#{desconto.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{ContratoControle.selecionarDescontoModalidadeProdutoSugerido}" focus="descontoProdutoSugerido" reRender="formProdutoSugerido, produtoSugeridoVO" oncomplete="Richfaces.hideModalPanel('panelDescontoProdutoSugerido')" value="#{msg_bt.btn_selecionar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>                        
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formDescontoProdutoSugerido:resultadoConsultaDescontoProdutoSugerido" maxPages="10"
                                   id="scResultadoDescontoProdutoSugerido" />                
            </h:panelGrid>   
        </a4j:form>
    </rich:modalPanel>  

    <rich:modalPanel id="panelDescontoPlanoProdutoSugerido" autosized="true" shadowOpacity="true" width="450" height="250">        
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta de Desconto Para Produto"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink4"/>
                <rich:componentControl for="panelDescontoPlanoProdutoSugerido" attachTo="hidelink4" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formDescontoPlanoProdutoSugerido" ajaxSubmit="true">          
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">  
                <rich:dataTable id="resultadoConsultaDescontoPlanoProdutoSugerido" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{ContratoControle.listaConsultaDesconto}" rows="5" var="desconto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Desconto_descricao}" />
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}" focus="descontoPlanoProdutoSugerido" reRender="form,planoProdutoVO, planoProdutoMarcada, total, total1, subtotal" oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')" value="#{desconto.descricao}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Desconto_tipoDesconto}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}" focus="descontoPlanoProdutoSugerido" reRender="form ,planoProdutoVO, planoProdutoMarcada, total, total1, subtotal" oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')" value="#{desconto.tipoProduto_Apresentar}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Desconto_valor}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}" focus="descontoPlanoProdutoSugerido" reRender="form,planoProdutoVO, planoProdutoMarcada, total, total1, subtotal" oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')" >
                                <h:outputText value="#{desconto.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{ContratoControle.selecionarDescontoPlanoProdutoSugerido}" focus="descontoPlanoProdutoSugerido" reRender="form,planoProdutoVO, planoProdutoMarcada, total, total1, subtotal" oncomplete="Richfaces.hideModalPanel('panelDescontoPlanoProdutoSugerido')" value="#{msg_bt.btn_selecionar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>                        
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formDescontoPlanoProdutoSugerido:resultadoConsultaDescontoPlanoProdutoSugerido" maxPages="10"
                                   id="scResultadoDescontoPlanoProdutoSugerido" />                
            </h:panelGrid>   
        </a4j:form>
    </rich:modalPanel>  

    <rich:modalPanel id="panelProduto" autosized="true" shadowOpacity="true" width="450" height="250">        
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta do Produto"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panelProduto" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formProduto" ajaxSubmit="true">          
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">                    
                    <h:outputText  value="#{msg.msg_consultar_por}"/> 
                    <h:selectOneMenu onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" id="consultaProduto" value="#{ContratoControle.campoConsultaProduto}">
                        <f:selectItems value="#{ContratoControle.tipoConsultaComboProduto}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultaProduto" size="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ContratoControle.valorConsultaProduto}"/>
                    <a4j:commandButton  id="btnConsultar" reRender="formProduto:mensagemConsultaProduto, formProduto:resultadoConsultaProduto, formProduto:scResultadoProduto" action="#{ContratoControle.consultarProduto}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>                
                <rich:dataTable id="resultadoConsultaProduto" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{ContratoControle.listaConsultaProduto}" rows="5" var="produto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Produto_descricao}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ContratoControle.selecionarProduto}" focus="" reRender="form,planoProdutoVO" oncomplete="Richfaces.hideModalPanel('panelProduto')" value="#{produto.descricao}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Produto_tipoVigencia}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ContratoControle.selecionarProduto}" focus="" reRender="form,planoProdutoVO" oncomplete="Richfaces.hideModalPanel('panelProduto')" value="#{produto.tipoVigencia_Apresentar}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{ContratoControle.selecionarProduto}" focus="" reRender="form,planoProdutoVO" oncomplete="Richfaces.hideModalPanel('panelProduto')" value="#{msg_bt.btn_selecionar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>                        
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formProduto:resultadoConsultaProduto" maxPages="10"
                                   id="scResultadoProduto" />
                <h:panelGrid id="mensagemConsultaProduto" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ContratoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ContratoControle.mensagemDetalhada}"/>
                    </h:panelGrid>                    
                </h:panelGrid>
            </h:panelGrid>      
        </a4j:form>
    </rich:modalPanel> 




    <rich:modalPanel id="panelModalidadeTurma" autosized="true" shadowOpacity="true" width="800" height="200">        
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consultar Turma da Modalidade"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>                
                <rich:componentControl for="panelModalidadeTurma" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>        
        <a4j:form id="formModalidadeTurma" ajaxSubmit="true">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">               
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Turma"/>
                </h:panelGrid>  
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <rich:dataTable id="modalidadeTurma" width="100%" rowClasses="linhaImpar" columnClasses="colunaCentralizada" 
                                    value="#{ContratoControle.contratoModalidadeVO.contratoModalidadeTurmaVOs}" var="contratoTurma">   

                        <f:facet name="header">
                            <rich:columnGroup>
                                <rich:column>

                                </rich:column>
                                <rich:column>
                                    <h:outputText value="Nome da Turma"/>
                                </rich:column>
                                <rich:column>
                                    <h:outputText value="Modalidade"/>
                                </rich:column>
                                <rich:column>
                                    <h:outputText value="Idade Mínima"/>
                                </rich:column>
                                <rich:column>
                                    <h:outputText value="Idade Máxima"/>
                                </rich:column>                                
                            </rich:columnGroup>
                        </f:facet>

                        <rich:column>                            
                            <h:selectBooleanCheckbox  value="#{contratoTurma.turma.turmaEscolhida}" >
                                <a4j:support event="onclick" reRender="formModalidadeTurma"/>
                            </h:selectBooleanCheckbox>                            
                        </rich:column>                                             
                        <rich:column>                                                          
                            <h:outputText  value="#{contratoTurma.turma.identificador}" />
                        </rich:column>                                             
                        <rich:column> 
                            <h:outputText  value="#{contratoTurma.turma.modalidade.nome}" />
                        </rich:column>                                             
                        <rich:column>                                                         
                            <h:outputText  value="#{contratoTurma.turma.idadeMinima}" />
                        </rich:column>                                             
                        <rich:column>                            
                            <h:outputText  value="#{turma.idadeMaxima}" />
                        </rich:column> 

                        <rich:column  breakBefore="true" colspan="5">                                 
                            <rich:simpleTogglePanel rendered="#{contratoTurma.turma.turmaEscolhida}"  switchType="client" label="Opções de Horários" opened="#{contratoTurma.turma.turmaEscolhida}" onexpand="true" width="100%">
                                <rich:dataTable id="turmaHorario" width="97%" rowClasses="linhaPar" columnClasses="colunaCentralizada" 
                                                value="#{contratoTurma.turma.horarioTurmaVOs}" var="horario">
                                    <f:facet name="header">
                                        <rich:columnGroup>                                            
                                            <rich:column >

                                            </rich:column>
                                            <rich:column>
                                                <h:outputText value="Dia da Semana"/>
                                            </rich:column>
                                            <rich:column>
                                                <h:outputText value="Horário"/>
                                            </rich:column>
                                            <rich:column>
                                                <h:outputText value="Numero Máximo de Aluno"/>
                                            </rich:column>
                                            <rich:column>
                                                <h:outputText value="Professor"/>
                                            </rich:column>                                                             
                                        </rich:columnGroup>
                                    </f:facet>

                                    <rich:column>                            
                                        <h:selectBooleanCheckbox  value="#{horario.horarioTurmaEscolhida}" />
                                    </rich:column> 
                                    <rich:column>                                                                    
                                        <h:outputText  value="#{horario.diaSemana_Apresentar}" />
                                    </rich:column> 
                                    <rich:column>                                                                    
                                        <h:outputText  value="#{horario.horaInicial} as #{horario.horaFinal}" />
                                    </rich:column> 
                                    <rich:column>                                                                    
                                        <h:outputText  value="#{horario.nrMaximoAluno}" />
                                    </rich:column> 
                                    <rich:column>                                                                  
                                        <h:outputText  value="#{horario.professor.pessoa.nome}" />
                                    </rich:column> 
                                </rich:dataTable> 
                            </rich:simpleTogglePanel>
                        </rich:column> 

                    </rich:dataTable>                   
                </h:panelGrid>  
                <a4j:commandButton id="salvar" reRender="form" oncomplete="Richfaces.hideModalPanel('panelModalidadeTurma')" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
            </h:panelGrid>                       
        </a4j:form>
    </rich:modalPanel> 

    <rich:modalPanel id="panelDuracaoConvenio" autosized="true" shadowOpacity="true" width="450" height="250">        
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consultar Duração do Convênio"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink6"/>
                <rich:componentControl for="panelDuracaoConvenio" attachTo="hidelink6" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>       
        <a4j:form id="formDuracaoConvenio" ajaxSubmit="true">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">               
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Duração do Convênio"/>
                </h:panelGrid>  
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <rich:dataTable id="configuracaoConvenioVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                    value="#{ContratoControle.contratoVO.convenioDesconto.convenioDescontoConfiguracaoVOs}" var="configuracaoConvenio">
                        <rich:column>      
                            <h:outputText  value="#{configuracaoConvenio.duracao}  Mês" />
                        </rich:column>                        
                        <h:column >
                            <h:outputText value="R$ "/>
                            <h:outputText value="#{configuracaoConvenio.valorDesconto}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:column>
                        <h:column>                                                                                    
                            <h:outputText value="#{configuracaoConvenio.porcentagemDesconto} ">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value=" %"/>
                        </h:column>

                    </rich:dataTable>
                </h:panelGrid>                
            </h:panelGrid>                       
        </a4j:form>
    </rich:modalPanel>    

    <h:form >    
        <html>
            <jsp:include page="include_head.jsp" flush="true" />
            <body>
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                    <c:if test="${MenuControle.apresentarTopo}">
                        <tr>
                            <td height="77" align="left" valign="top" class="bgtop"><jsp:include page="include_top.jsp" flush="true" />	</td>
                        </tr>

                        <tr>
                            <td height="48" align="left" valign="top" class="bgmenu"><jsp:include page="include_menu.jsp" flush="true" /></td>
                        </tr>
                    </c:if>
                    <tr>
                        <td align="left" valign="top" class="bglateral">
                            <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;"><jsp:include page="include_box_menulateral.jsp" flush="true" /><jsp:include page="include_box_descricao.jsp" flush="true" />
                                    </td>
                                    <td align="left" valign="top" style="padding:7px 20px 0 20px;">

                                        <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                            <tr>
                                                <td height="28" colspan="3" align="left" valign="top" style="padding-left:17px;"><a class="hierarquialink" href="tela1.jsp"><img border="0" style="margin-right:4px;vertical-align:middle;" src="images/arrow_back.gif" alt="Inicial">Inicial</a> <span class="hierarquia">> Negocia&ccedil;&atilde;o</span></td>
                                            </tr>
                                            <tr>
                                                <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"></td>
                                                <td align="left" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Negocia&ccedil;&atilde;o</td>
                                                <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"></td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif"></td>
                                                <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;">
                                                    <!-- inicio item -->

                                                    <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0" class="tablepadding2 textsmall" style="margin-bottom:25px;">

                                                        <tr>
                                                            <td width="60%" align="left" valign="top" style="padding:0 10px 10px 0;">
                                                                <!-- inicio botões -->
                                                                <div style="clear:both;margin-bottom:15px;">
                                                                    <a4j:commandButton id="voltar" action="#{ContratoControle.novo}" oncomplete="location='tela4.jsp'"  value="voltar"  accesskey="2" styleClass="botoes"/>
                                                                </div>
                                                                <div style="clear:both;margin-bottom:15px;">  
                                                                </div>
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Escolha sua Empresa</p>
                                                                            </div>
                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top">
                                                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">
                                                                                <tr>
                                                                                    <td width="30%" height="27" align="left" valign="middle">
                                                                                        <h:selectOneMenu  id="empresa"  disabled="#{!ContratoControle.contratoVO.usuarioVO.administrador}" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ContratoControle.contratoVO.empresa.codigo}" >
                                                                                            <a4j:support event="onchange"  action="#{ContratoControle.montarListaSelectItemPlano}"reRender="plano,planoDescricao,planoDuracaoVO,planoCondicaoPagamentoVO,planoModalidadeVO,planoModalidadeVezesSemanaVO,planoHorarioVO,planoComposicaoVO,formaPagamentoVO,composicaoMarcada ,modalidadeMarcada,produtoMarcada,planoProdutoMarcada,planoProdutoVO,botaoAdicionar,total, total1, subtotal,convenioDescontoMarcada "/>
                                                                                            <f:selectItems  value="#{ContratoControle.listaSelectItemEmpresa}" />                                                                                            
                                                                                        </h:selectOneMenu>
                                                                                    </td>
                                                                                </tr>

                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </table>                                                                
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Escolha seu plano</p>
                                                                            </div>
                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top">
                                                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">
                                                                                <tr>
                                                                                    <td width="30%" height="27" align="left" valign="middle">
                                                                                        <h:selectOneMenu  id="plano" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ContratoControle.contratoVO.plano.codigo}" >
                                                                                            <a4j:support event="onchange"  action="#{ContratoControle.selecionarPlano}" reRender="planoDescricao,planoDuracaoVO,convenioDescontoVO,planoCondicaoPagamentoVO,planoModalidadeVO,planoModalidadeVezesSemanaVO,planoHorarioVO,planoComposicaoVO,formaPagamentoVO ,composicaoMarcada ,modalidadeMarcada,produtoMarcada,planoProdutoMarcada,planoProdutoVO,botaoAdicionar, total, total1, subtotal,convenioDescontoMarcada"/>
                                                                                            <f:selectItems  value="#{ContratoControle.listaSelectItemPlano}" />                                                                                            
                                                                                        </h:selectOneMenu>
                                                                                    </td>
                                                                                </tr>

                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </table>                                                                
                                                                <!-- fim botões -->


                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Composi&ccedil;&atildeo</p>
                                                                            </div>
                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top">   
                                                                            <rich:dataTable id="planoComposicaoVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                            value="#{ContratoControle.contratoVO.plano.planoComposicaoVOs}" var="planoComposicao">                               
                                                                                <rich:column width="60%">
                                                                                    <h:selectBooleanCheckbox value="#{planoComposicao.composicao.composicaoEscolhida}"  >
                                                                                        <a4j:support event="onclick" action="#{ContratoControle.selecionarComposicao}" reRender="planoComposicaoVO ,planoModalidadeVO, convenioDescontoVO,composicaoMarcada, modalidadeMarcada, produtoMarcada, total, total1, subtotal "/> 
                                                                                    </h:selectBooleanCheckbox>                                                                                    

                                                                                    <h:outputText value="    "/>

                                                                                    <h:outputText value="#{planoComposicao.composicao.descricao} "/>
                                                                                </rich:column>
                                                                                <rich:column width="40%">
                                                                                    <h:outputText value="#{planoComposicao.composicao.precoComposicao} ">
                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                    </h:outputText>
                                                                                </rich:column>
                                                                            </rich:dataTable>   
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <!-- fim item -->                                                                                                                               
                                                                <!-- inicio item -->
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Modalidade</p>
                                                                            </div>
                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top">
                                                                            <rich:dataTable id="planoModalidadeVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                            value="#{ContratoControle.contratoVO.contratoModalidadeVOs}" var="contratoModalidade">  
                                                                                <rich:column width="150">
                                                                                    <h:selectBooleanCheckbox  disabled="#{contratoModalidade.modalidade.composicao}" value="#{contratoModalidade.modalidade.modalidadeEscolhida}">
                                                                                        <a4j:support event="onclick" action="#{ContratoControle.selecionarModalidade}" reRender="planoModalidadeVO , modalidadeMarcada , produtoMarcada,total,total1,subtotal"/>
                                                                                    </h:selectBooleanCheckbox>                                                    
                                                                                    <h:outputText  value="#{contratoModalidade.modalidade.nome}" />                  
                                                                                </rich:column>
                                                                                <rich:column width="100">
                                                                                    <h:outputText  value="#{contratoModalidade.modalidade.valorMensal}">
                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                    </h:outputText>                                                                                  
                                                                                </rich:column>

                                                                                <rich:column width="150" rendered="#{contratoModalidade.modalidade.modalidadeEscolhida}" >
                                                                                    <a4j:commandLink  value="VEZES POR SEMANA" action="#{ContratoControle.consultarVezesSemana}" reRender="formPlanoModalidadeVezesSemana" oncomplete="Richfaces.showModalPanel('panelPlanoModalidadeVezesSemana')"  />                                                                                  
                                                                                </rich:column>
                                                                                <rich:column  width="150" rendered="#{contratoModalidade.modalidade.utilizarProduto && contratoModalidade.modalidade.modalidadeEscolhida}">
                                                                                    <a4j:commandLink  value="PRODUTO SUGERIDO" action="#{ContratoControle.consultarModalidadeProdutoSugerido}" reRender="formProdutoSugerido" oncomplete="Richfaces.showModalPanel('panelProdutoSugerido')"  />                                                                                  
                                                                                </rich:column>
                                                                                <rich:column width="100" rendered="#{contratoModalidade.modalidade.utilizarTurma && contratoModalidade.modalidade.modalidadeEscolhida}">                                                                                       
                                                                                    <a4j:commandLink value="TURMA" action="#{ContratoControle.selecionarTurmaModalidade}" reRender="formModalidadeTurma" oncomplete="Richfaces.showModalPanel('panelModalidadeTurma')" />
                                                                                </rich:column>
                                                                            </rich:dataTable>
                                                                        </td>
                                                                    </tr>
                                                                </table>


                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Convênio</p>
                                                                            </div>
                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td width="30%" height="27" align="left" valign="middle">   
                                                                            <h:selectOneMenu  id="convenioDescontoVO" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ContratoControle.contratoVO.convenioDesconto.codigo}" >
                                                                                <a4j:support event="onchange" action="#{ContratoControle.selecionarConvenioDesconto}" reRender=" convenioDescontoVO, convenioDescontoMarcada,total,total1,subtotal"/>
                                                                                <f:selectItems  value="#{ContratoControle.listaSelectItemConvenioDesconto}" />
                                                                            </h:selectOneMenu>


                                                                            <%--     <rich:dataTable id="convenioDescontoVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                         value="#{ContratoControle.listaSelectItemConvenioDesconto}" var="convenioDesconto">                               
                                                                                <rich:column>
                                                                                    <h:selectBooleanCheckbox value="#{convenioDesconto.convenioDescontoEscolhida}">
                                                                                        <a4j:support event="onclick" action="#{ContratoControle.selecionarConvenioDesconto}" reRender=" convenioDescontoVO, convenioDescontoMarcada,total,total1,subtotal"/>
                                                                                    </h:selectBooleanCheckbox>

                                                                                        <h:outputText value="    "/>

                                                                                    <h:outputText value="#{convenioDesconto.descricao}"/>                                                                                    
                                                                                </rich:column> 
                                                                                 <rich:column rendered="#{convenioDesconto.pagaMatricula}">
                                                                                    <h:outputText  value="Paga Matrícula"/>
                                                                                </rich:column>
                                                                                <rich:column rendered="#{convenioDesconto.pagaRematricula}">
                                                                                    <h:outputText  value="Paga Rematrícula"/>
                                                                                </rich:column>
                                                                                <rich:column rendered="#{ContratoControle.desenharValorConvenioDesconto && convenioDesconto.convenioDescontoEscolhida }">
                                                                                    <a4j:commandLink value="Duracão" reRender="formDuracaoConvenio" oncomplete="Richfaces.showModalPanel('panelDuracaoConvenio')" />
                                                                                </rich:column>
                                                                            </rich:dataTable>  --%> 
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <!-- inicio item -->


                                                                <!-- inicio item -->
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Produtos</p>
                                                                            </div>
                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top">   
                                                                            <h:panelGrid width="100%" id="botaoAdicionar" columnClasses="colunaCentralizada">
                                                                                <a4j:commandButton   rendered="#{ContratoControle.desenharBotaoAdicionarProduto}" reRender="formProduto" oncomplete="Richfaces.showModalPanel('panelProduto')"alt="Adiconar Produtos" image="./images/icon_add.gif"/>                                                                         
                                                                            </h:panelGrid>
                                                                            <rich:dataTable id="planoProdutoVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda" 
                                                                                            value="#{ContratoControle.contratoVO.plano.planoProdutoSugeridoVOs}" var="planoProduto">                               
                                                                                <rich:column>
                                                                                    <h:selectBooleanCheckbox disabled="#{planoProduto.obrigatorio}" value="#{planoProduto.produtoSugeridoEscolhida}">    
                                                                                        <a4j:support event="onclick"  action="#{ContratoControle.selecionarPlanoProdutoSugerido}" reRender="planoProdutoVO, planoProdutoMarcada, total, total1, subtotal"/>                                                                                    
                                                                                    </h:selectBooleanCheckbox>

                                                                                    <h:outputText value="    "/>

                                                                                    <h:outputText  value="#{planoProduto.produto.descricao}" />
                                                                                </rich:column>
                                                                                <rich:column>
                                                                                    <h:outputText  value="#{planoProduto.produto.valorFinal}" >
                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                    </h:outputText> 
                                                                                </rich:column>
                                                                                <rich:column>
                                                                                    <h:inputText readonly="true"   id="descontoPlanoProdutoSugerido" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  size="10" value="#{planoProduto.produto.desconto.valor}" >
                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                    </h:inputText> 
                                                                                </rich:column>
                                                                                <rich:column>
                                                                                    <a4j:commandButton image="./images/icon_chave.png"/>
                                                                                </rich:column>
                                                                                <rich:column>
                                                                                    <a4j:commandButton action="#{ContratoControle.consultarDescontoPlanoProdutoSugerido}" alt="Consutar Desconto" reRender="formDescontoPlanoProdutoSugerido" oncomplete="Richfaces.showModalPanel('panelDescontoPlanoProdutoSugerido')"  image="./images/icon_calculadora.png"/>

                                                                                    <h:outputText value="    "/>

                                                                                    <a4j:commandButton action="#{ContratoControle.limparDescontoPlanoProdutoSugerido}" alt="Limpar Desconto" reRender="planoProdutoVO, planoProdutoMarcada, total, total1, subtotal" image="./images/limpar.gif"/>
                                                                                </rich:column>
                                                                            </rich:dataTable>   

                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Dura&ccedil;&atildeo</p>
                                                                            </div>
                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top">   
                                                                            <rich:dataTable id="planoDuracaoVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                            value="#{ContratoControle.contratoVO.plano.planoDuracaoVOs}" var="planoDuracao">                               
                                                                                <rich:column width="50%">
                                                                                    <h:selectBooleanCheckbox  value="#{planoDuracao.duracaoEscolhida}">
                                                                                        <a4j:support event="onclick" action="#{ContratoControle.selecionarDuracao}" reRender="planoDuracaoVO , duracao, total,total1,subtotal"/>
                                                                                    </h:selectBooleanCheckbox>

                                                                                    <h:outputText value="    "/>

                                                                                    <h:outputText value="#{planoDuracao.numeroMeses} Mese(s)"/>                                                                                    
                                                                                </rich:column>
                                                                                <rich:column  width="25%" rendered="#{planoDuracao.apresentarValorEspecifico || planoDuracao.apresentarValorDesconto }">
                                                                                    <h:outputText value="#{planoDuracao.tipoOperacao_Apresentar} "/>
                                                                                </rich:column>
                                                                                <rich:column width="25%"rendered="#{planoDuracao.apresentarValorEspecifico}">
                                                                                    <h:outputText value="R$ "/>
                                                                                    <h:outputText value="#{planoDuracao.valorEspecifico}">
                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                    </h:outputText>
                                                                                </rich:column>
                                                                                <rich:column width="25%"rendered="#{planoDuracao.apresentarValorDesconto}">                                                                                    
                                                                                    <h:outputText value="#{planoDuracao.percentualDesconto} ">
                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                    </h:outputText>
                                                                                    <h:outputText value=" %"/>
                                                                                </rich:column>
                                                                            </rich:dataTable>   
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <!-- inicio item -->
                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Horário</p>
                                                                            </div>
                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top"> 
                                                                            <rich:dataTable id="planoHorarioVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                            value="#{ContratoControle.contratoVO.plano.planoHorarioVOs}"  var="planoHorario">
                                                                                <rich:column width="50%">
                                                                                    <h:selectBooleanCheckbox  value="#{planoHorario.horario.horarioEscolhida}">
                                                                                        <a4j:support event="onclick" action="#{ContratoControle.selecionarHorario}" reRender="planoHorarioVO,horario,total,total1,subtotal"/>
                                                                                    </h:selectBooleanCheckbox>                                                
                                                                                    <h:outputText  value="#{planoHorario.horario.descricao} " />                                                                                   
                                                                                </rich:column>
                                                                                <rich:column width="25%"rendered="#{planoHorario.apresentarValorEspecifico || planoHorario.apresentarValorDesconto }">
                                                                                    <h:outputText value="#{planoHorario.tipoOperacao_Apresentar} "/>
                                                                                </rich:column>
                                                                                <rich:column width="25%"rendered="#{planoHorario.apresentarValorEspecifico}">
                                                                                    <h:outputText value="R$ "/>
                                                                                    <h:outputText value="#{planoHorario.valorEspecifico}">
                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                    </h:outputText>
                                                                                </rich:column>
                                                                                <rich:column width="25%"  rendered="#{planoHorario.apresentarValorDesconto}">                                                                                    
                                                                                    <h:outputText value="#{planoHorario.percentualDesconto} ">
                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                    </h:outputText>
                                                                                    <h:outputText value=" %"/>
                                                                                </rich:column>
                                                                            </rich:dataTable> 
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                                <%--<table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Condição de Pagamento</p>
                                                                            </div>
                                                                        <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top"> 
                                                                            <h:dataTable id="planoCondicaoPagamentoVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                         value="#{ContratoControle.contratoVO.plano.planoCondicaoPagamentoVOs}"  var="planoCondicaoPagamento">
                                                                                <h:column>
                                                                                    <h:selectBooleanCheckbox  value="#{planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
                                                                                        <a4j:support event="onclick" action="#{ContratoControle.selecionarCondicaoPagamento}" reRender="planoCondicaoPagamentoVO,condicaoPagamento"/>
                                                                                    </h:selectBooleanCheckbox>                                              
                                                                                    <h:outputText  value="#{planoCondicaoPagamento.condicaoPagamento.descricao} " />                                                                                   
                                                                                </h:column>
                                                                            </h:dataTable> 
                                                                        </td>
                                                                    </tr>
                                                                </table>--%>
                                                                <!-- fim item -->
                                                                <!-- inicio item -->
                                                                <%--<table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                    <tr>
                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Forma de Pagamento</p>
                                                                            </div>
                                                                        <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td align="left" valign="top">
                                                                            <h:dataTable id="formaPagamentoVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                         value="#{NegociacaoControle.listaSelectItemFormaPagamento}" var="formaPagamento">      
                                                                                <h:column>
                                                                                    <h:selectBooleanCheckbox />

                                                                                        <h:outputText value="    "/>

                                                                                    <h:outputText  value="#{formaPagamento.descricao}" />                                                                                    
                                                                                </h:column>
                                                                            </h:dataTable>  
                                                                        </td>
                                                                    </tr>
                                                                </table>--%>
                                                                <!-- fim item -->
                                                                <!-- fim item -->                                                             
                                                                <!-- inicio botões -->
                                                                <div style="clear:both;margin-bottom:5px;">
                                                                    <a4j:commandButton id="voltar1" action="#{ContratoControle.novo}" oncomplete="location='tela4.jsp'"  value="voltar"  accesskey="2" styleClass="botoes"/>
                                                                </div>
                                                                <!-- fim botões -->
                                                            </td>
                                                            <td align="left" valign="top" class="sombrapreview" style="padding:10px;">
                                                                <table width="100%" height="100%" border="0" cellspacing="0" cellpadding="0">
                                                                    <tr>
                                                                        <td align="left" valign="top" style="padding-bottom:20PX;">
                                                                            <div class="tituloboxcentro" style="clear:both;text-align:center;margin-bottom:10px;">Resultado da Negociação</div>
                                                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreviewtotal">

                                                                                <tr>
                                                                                    <td align="right" valign="middle">Total =  <span class="verde"><h:outputText id="total" value="#{ContratoControle.contratoVO.valorFinal}"><f:converter converterId="FormatadorNumerico"/></h:outputText>  </span></td>
                                                                                </tr>
                                                                            </table>
                                                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreview">
                                                                                <tr>
                                                                                    <td align="left" valign="middle"><span style="font-weight: bold">Cliente: </span><h:outputText id="pessoaNome" value="#{ContratoControle.contratoVO.pessoa.nome}"/></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td align="left" valign="middle"><span style="font-weight: bold">Escolha seu plano: </span><h:outputText id="planoDescricao" value="#{ContratoControle.contratoVO.plano.descricao}"/></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td align="left" valign="middle" ><span style="font-weight: bold">Composi&ccedil;&atilde;o: </span>                                                                                        
                                                                                        <h:dataTable id="composicaoMarcada" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                                     value="#{ContratoControle.contratoVO.plano.planoComposicaoVOs}" var="planoComposicao">                               
                                                                                            <h:column rendered="#{planoComposicao.composicao.composicaoEscolhida}">                                                                                               
                                                                                                <h:outputText value="#{planoComposicao.composicao.descricao} "/>
                                                                                            </h:column>
                                                                                            <h:column rendered="#{planoComposicao.composicao.composicaoEscolhida}">
                                                                                                <h:outputText value="#{planoComposicao.composicao.precoComposicao} ">
                                                                                                    <f:converter converterId="FormatadorNumerico"/>
                                                                                                </h:outputText>
                                                                                            </h:column>
                                                                                        </h:dataTable>  
                                                                                    </td>
                                                                                </tr>                                                                                
                                                                                <tr>
                                                                                    <td align="left" valign="middle"><span style="font-weight: bold">Modalidade: </span>
                                                                                        <h:dataTable id="modalidadeMarcada" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                                                                                     value="#{ContratoControle.contratoVO.contratoModalidadeVOs}" var="contratoModalidade">  
                                                                                            <h:column rendered="#{contratoModalidade.modalidade.modalidadeEscolhida}">                                                                                                                                                  
                                                                                                <h:outputText  value="#{contratoModalidade.modalidade.nome}" />                                                                                  
                                                                                            </h:column>
                                                                                            <h:column rendered="#{contratoModalidade.modalidade.modalidadeEscolhida}">
                                                                                                <h:outputText  value="#{contratoModalidade.valorBaseCalculo}">
                                                                                                    <f:converter converterId="FormatadorNumerico"/>
                                                                                                </h:outputText>                                                                                  
                                                                                            </h:column>
                                                                                        </h:dataTable>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td align="left" valign="middle"><span style="font-weight: bold">Convênio: </span><h:outputText id="convenioDescontoMarcada" value="#{ContratoControle.contratoVO.convenioDesconto.descricao}" /></td>
                                                                                </tr>

                                                                                <%--   <tr>
                                                                                    <td align="left" valign="middle"><span style="font-weight: bold">Produtos: </span>

                                                                                        <h:dataTable id="produtoMarcada" rowClasses="linhaImpar" columnClasses="colunaEsquerda"  
                                                                                                     value="#{ContratoControle.contratoVO.contratoModalidadeVOs}" var="contratoModalidade">  
                                                                                            <h:column >
                                                                                                <h:dataTable width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda" style="position:relative; top:-20px; left:-8; "
                                                                                                             value="#{contratoModalidade.modalidade.produtoSugeridoVOs}" var="produtoSugerido">
                                                                                                    <h:column rendered="#{contratoModalidade.modalidade.modalidadeEscolhida && produtoSugerido.produtoSugeridoEscolhida}">
                                                                                                        <h:outputText value="#{produtoSugerido.produto.descricao}"/>
                                                                                                    </h:column>
                                                                                                    <h:column rendered="#{contratoModalidade.modalidade.modalidadeEscolhida && produtoSugerido.produtoSugeridoEscolhida}">
                                                                                                        <h:outputText value="#{produtoSugerido.produto.valorBaseCalculo}">
                                                                                                            <f:converter converterId="FormatadorNumerico"/>
                                                                                                        </h:outputText>
                                                                                                    </h:column>

                                                                                                </h:dataTable>                                                                                         
                                                                                            </h:column>
                                                                                        </h:dataTable>


                                                                                    </td> 
                                                                                </tr>
                                                                                <tr>
                                                                                    <td align="left" valign="middle"><span style="font-weight: bold">
                                                                                        <h:dataTable id="planoProdutoMarcada" rowClasses="linhaImpar" columnClasses="colunaEsquerda" style="position:relative; top:-65px;"
                                                                                                     value="#{ContratoControle.contratoVO.plano.planoProdutoSugeridoVOs}" var="planoProduto">                               
                                                                                            <h:column rendered="#{planoProduto.produtoSugeridoEscolhida}">                                                                                    
                                                                                                <h:outputText  value="#{planoProduto.produto.descricao}" />
                                                                                            </h:column>
                                                                                            <h:column rendered="#{planoProduto.produtoSugeridoEscolhida}">
                                                                                                <h:outputText  value="#{planoProduto.produto.valorBaseCalculo}">
                                                                                                    <f:converter converterId="FormatadorNumerico"/>
                                                                                                </h:outputText> 
                                                                                            </h:column>
                                                                                        </h:dataTable>   
                                                                                    </td>
                                                                                </tr>--%>
                                                                                <tr>
                                                                                    <td align="left" valign="middle"><span style="font-weight: bold">Tipo de Negocia&ccedil;&atilde;o: </span><h:outputText id="duracao" value="#{ContratoControle.contratoVO.planoDuracao.numeroMeses} Mes(es)" /></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td align="left" valign="middle" ><span style="font-weight: bold">Horário: </span><h:outputText id="horario" value="#{ContratoControle.contratoVO.planoHorario.horario.descricao}"/></td>
                                                                                </tr>                                                                             

                                                                                <tr>
                                                                                    <td align="left" valign="middle" style="padding-top:20px;"style="position:relative; top:-10px;"><span style="font-weight: bold">Subtotal:</span> <h:outputText id="subtotal" value="#{ContratoControle.contratoVO.valorFinal}" ><f:converter converterId="FormatadorNumerico"/></h:outputText></td>
                                                                                </tr>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td height="30" align="left" valign="top">
                                                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreviewtotal">                                                                                
                                                                                <tr>
                                                                                    <td align="right" valign="middle">Total =  <span class="verde"><h:outputText id="total1" value="#{ContratoControle.contratoVO.valorFinal}"><f:converter converterId="FormatadorNumerico"/></h:outputText></span></td>
                                                                                </tr>
                                                                            </table>
                                                                            <div style="clear:both;margin-top:10px;text-align:right;">
                                                                                <h:commandLink id="fecharNegociacao" action="#{ContratoControle.fecharNegociacao}">
                                                                                    <img class="imgalpha" style="margin-bottom:10px;" src="images/btn_fecharnegociacao.gif" title="Fechar Negocia&ccedil;&atilde;o" width="191" height="41" border="0"/>
                                                                                </h:commandLink>        
                                                                                <br></br>
                                                                                <h:commandLink id="cancela"  action="#{ContratoControle.cancelarNegociacao}">
                                                                                    <img class="imgalpha" style="margin-bottom:10px;" src="images/btn_cancelar.gif" title="Cancelar" width="111" height="41" border="0"/>
                                                                                </h:commandLink>                                                         

                                                                            </div> 
                                                                        </td>
                                                                    </tr>
                                                                </table>

                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <!-- fim item -->

                                                </td>
                                                <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>
                                            </tr>
                                            <tr>
                                                <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                                <td align="left" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                                <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                            </tr>
                                        </table>	
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="include_rodape.jsp" flush="true" /></td>
                    </tr>
                </table>
            </body>
        </html>
    </h:form>
</f:view>
function focusinput(elemento) {
    elemento.style.border = "1px solid #8eb3c3";
    elemento.style.backgroundColor = "#fff2c2";
}
function blurinput(elemento) {
    elemento.style.border = "1px solid #8eb3c3";
    elemento.style.backgroundColor = "#fff";
}

function mascara(objForm, strField, sMask, evtKeyPress) {
    //alert(objForm[strField].value);
    var i, nCount, sValue, fldLen, mskLen,bolMask, sCod;
    var textoSel = getSelText();
    if (textoSel != '') {
        return true;
    }
    //aqui obtem o nr da tecla digitada
    var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);

    if (evtKeyPress.keyCode != 0 && ((nTecla == 8) || (nTecla == 9))){
        return true;
    }

    TeclasAtalho(evtKeyPress);
    sValue = objForm[strField].value;

    // Limpa todos os caracteres de formata??o que
    // j? estiverem no campo.
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( "-", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( ".", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "/", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( "(", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( ")", "" );
    sValue = sValue.toString().replace( "+", "" );
    sValue = sValue.toString().replace( "+", "" );
    sValue = sValue.toString().replace( " ", "" );
    sValue = sValue.toString().replace( " ", "" );
    fldLen = sValue.length;
    mskLen = sMask.length;


    i = 0;
    nCount = 0;
    sCod = "";
    mskLen = fldLen;

    while (i <= mskLen) {
        bolMask = ((sMask.charAt(i) == "-") || (sMask.charAt(i) == ":") || (sMask.charAt(i) == ".") || (sMask.charAt(i) == "/"))
        bolMask = bolMask || ((sMask.charAt(i) == "(") || (sMask.charAt(i) == ")") || (sMask.charAt(i) == " ") || (sMask.charAt(i) == "+"))

        if (bolMask) {
            sCod += sMask.charAt(i);
            mskLen++;
        } else {
            sCod += sValue.charAt(nCount);
            nCount++;
        }

        i++;
    }
    if (sMask.length == sCod.length) {
        //event.keyCode = 0;
        //Joao Alcides : esta modificacao impedira que a funcao permita que o usuario digite mais
        //numeros do que o definido na mascara.
        return false;
    }

    objForm[strField].value = sCod;
    if (nTecla != 8) { // backspace
        if (sMask.charAt(i-1) == "9") { // apenas n?meros...
            return ((nTecla > 47) && (nTecla < 58));
        } // n?meros de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    } else {
        return true;
    }
}

function submitPagamentoDigital(){
    var myForm = document.createElement("FORM");    
    /*var myFrame = document.createElement("IFRAME");
    myFrame.setAttribute("width", "800");
    myFrame.setAttribute("height", "600");
    myFrame.setAttribute("name", "frame");
    myFrame.setAttribute("id", "frame");*/
    //alert(document.getElementById('form:panelCamposPagamentoDigital'));
    //myForm.target = "_self";
    myForm.setAttribute("enctype","application/x-www-form-urlencoded;charset=iso-8859-1");
    myForm.enctype = "application/x-www-form-urlencoded;charset=iso-8859-1";
    myForm.action = "https://www.pagamentodigital.com.br/checkout/pay/";
    myForm.method = "post";
    
    var formPagDigital = document.getElementById("formPagamentoDigital");    
    var campos = formPagDigital.getElementsByTagName("input");
    for(var j = 0; j < campos.length; j++){
        var obj = campos[j].cloneNode(true);
        obj.id = obj.id.replace("formPagamentoDigital:", "");
        obj.name = obj.name.replace("formPagamentoDigital:", "");
        myForm.appendChild(obj);
    }
    document.body.appendChild(myForm);
    myForm.submit();
}

var MudarCampo = true;

document.onkeypress = HabilitarTABAutom;

function HabilitarTABAutom(evt)
{
    var codTecla;

    var e = evt ? evt : window.event;

    if(e.keyCode)  // IE
    {
        codTecla = e.keyCode;
    }
    else // Netscape/Firefox/Opera
    {
        codTecla = e.which;
    }

    if ( (codTecla < 48) || (codTecla >  255) )
        MudarCampo = false;
    else
        MudarCampo = true;

}

//Quando um input estiver com todos os seu caracteres preenchidos o foco ir� para o 'proximo'
function tabAutom(quem, e)
{
    if ( ( quem.value.length == quem.maxLength ) && ( MudarCampo ) ){
        var i=0,j=0, indice=-1;
        // Localiza em qual form est� o input no documento
        for (i=0; i<document.forms.length; i++) {
            for (j=0; j<document.forms[i].elements.length; j++) {
                if (document.forms[i].elements[j].name == quem.name) {
                    indice=i;
                    break;
                }
            }
            if (indice != -1)
                break;
        }

        // Localiza o input no documento e verifica se existe outro input para receber o foco
        for (i=0; i<document.forms[indice].elements.length; i++)
        {
            if (document.forms[indice].elements[i].name == quem.name)
            {
                while ( ((i+1) < document.forms[indice].elements.length) &&
                    ( (document.forms[indice].elements[(i+1)].type == "hidden") ||
                        (document.forms[indice].elements[(i+1)].name == "Lim") ) )
                        {
                    i++;
                }
                if ( (i+1) < document.forms[indice].elements.length )
                {
                    document.forms[indice].elements[(i+1)].focus();
                    MudarCampo = false;
                }
                break;
            }
        }
    }
}
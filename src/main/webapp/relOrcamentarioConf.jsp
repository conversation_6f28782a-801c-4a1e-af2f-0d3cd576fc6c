<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style>
    .tabelaMetaFinanceira tr td:nth-child(1n+5){
        text-align: right;
    }
</style>
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <h:outputText value="Relatório Orçamentário"/>
    </title>

    <c:set var="titulo" scope="session" value="Relatório Orçamentário"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-configurar-o-relatorio-orcamentario/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>

    <h:form id="form" styleClass="pure-form pure-u-1">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
            <h:commandLink action="#{RelatorioOrcamentarioConfigControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:panelGrid columns="1" width="100%">

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">

                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right" style="margin-top: 6px">
                        <h:panelGroup layout="block" styleClass="controles" style="font-size: 14px;">
                            <a4j:commandLink id="btnNovoRelOrcamentarioConfig"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{RelatorioOrcamentarioConfigControle.novo()}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <table id="tblPrevisaoRelatorioOrcamentario" class="tblPrevisaoRelatorioOrcamentario pure-g-r pure-u-11-12 margin-0-auto">
                <thead>
                    <th>COD</th>
                    <th>EMPRESA</th>
                    <th>ANO/MÊS</th>
                    <th>DESCRIÇÃO</th>
                </thead>
                <tbody></tbody>
            </table>

            <a4j:jsFunction name="jsEditar" action="#{RelatorioOrcamentarioConfigControle.editarMeta}" reRender="mensagem"/>

        </h:panelGrid>
    </h:form>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tblPrevisaoRelatorioOrcamentario", "${contexto}/prest/financeiro/relatorioOrcamentario", 2, "desc");
        });
        // Horizontal, Vertical
        window.resizeTo(1090,595);
    </script>
</f:view>
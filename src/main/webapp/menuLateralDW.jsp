<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>

<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
<!-- inicio box -->
<div class="box">
    <div class="boxtop"><img src="./images/box_top.png"></div>
    <div class="boxmiddle">
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top" style="vertical-align:middle;margin-right:9px;"><a class="titulo2" href="javascript:;">Filtrar Por:</a></td>
            </tr>
        </table>
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">

            <tr>
                <td width="20" align="left" valign="top"><img src="./images/shim.gif"></td>
                <!-- inicio item-->
                <td align="left" valign="top">



                    <h:panelGrid columns="1" cellpadding="0" cellspacing="5">
                        <h:selectBooleanCheckbox value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarPeriodo}">
                            <h:outputText styleClass="titulo3" value="Data"/>
                            <a4j:support event="onclick"  reRender="panelPeriodo"/>
                        </h:selectBooleanCheckbox>

                        <h:panelGroup id="panelPeriodo">
                            <h:panelGrid columns="2" rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarPeriodo}" style="vertical-align: middle">
                                <h:outputText value="De " styleClass="titulo3" />
                                <h:panelGroup>
                                    <rich:calendar id="dataInicial"
                                                   value="#{SituacaoContratoSinteticoDWControle.dataInicial}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <h:message for="dataInicial" styleClass="mensagemDetalhada" />
                                </h:panelGroup>
                                <h:outputText value="At�" styleClass="titulo3" />
                                <h:panelGroup>
                                    <rich:calendar id="dataFinal"
                                                   value="#{SituacaoContratoSinteticoDWControle.dataFinal}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false" />
                                    <h:message for="dataFinal" styleClass="mensagemDetalhada" />
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGroup>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />

                        <h:selectBooleanCheckbox id="situacao" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarSituacao}" >
                            <h:outputText styleClass="titulo3" value="Situa��o" />
                            <a4j:support event="onclick" reRender="panelSituacao"/>
                        </h:selectBooleanCheckbox>

                        <h:panelGrid columns="1" id="panelSituacao" width="50%" >

                            <h:selectOneRadio id="tipoSituacao" styleClass="titulo3" rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarSituacao}"  value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.situacao}">
                                <f:selectItems  value="#{SituacaoContratoSinteticoDWControle.listaSelectItemOrdenacao}"/>
                                <a4j:support event="onclick" action="#{SituacaoContratoSinteticoDWControle.selecionarTipoSituacao}" reRender="panelSituacao"/>
                            </h:selectOneRadio>
                            <h:selectOneRadio id="opcaoAtivo" styleClass="titulo3"  rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarOpcaoAtivos && SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarSituacao}" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.opcao}">
                                <f:selectItems  value="#{SituacaoContratoSinteticoDWControle.listaSelectItemOpcaoAtivo}"/>
                            </h:selectOneRadio>
                            <h:selectOneRadio id="opcaoInativo" styleClass="titulo3" rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarOpcaoInativos && SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarSituacao}" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.opcao}">
                                <f:selectItems value="#{SituacaoContratoSinteticoDWControle.listaSelectItemOpcaoInativo}"/>
                            </h:selectOneRadio>
                            <h:selectOneRadio id="opcaoVisitante" styleClass="titulo3"  rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarOpcaoVisitante && SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarSituacao}" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.opcao}">
                                <f:selectItems  value="#{SituacaoContratoSinteticoDWControle.listaSelectItemOpcaoVisitantes}"/>
                            </h:selectOneRadio>
                        </h:panelGrid>
                        <h:selectBooleanCheckbox id="empresa" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarEmpresa}">
                            <h:outputText styleClass="titulo3" value="Empresa"/>
                            <a4j:support event="onclick" reRender="panelGroupInputTextEmpresa"/>
                        </h:selectBooleanCheckbox>
                        <h:panelGrid columns="2" id="panelGroupInputTextEmpresa" >
                            <h:panelGroup>
                                <h:inputText  id="nomeEmpresa" size="20" maxlength="50" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarEmpresa}" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.nomeEmpresa}" />
                                <rich:spacer width="5" />
                                <a4j:commandButton  oncomplete="Richfaces.showModalPanel('panelEmpresa')" rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarEmpresa}" image="imagens/informacao.gif" alt="#{msg_aplic.prt_SituacaoContratoSinteticoDW_consulta_Empresa_titulo}"/>
                                <a4j:commandButton  id="limparEmpresa" rendered="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarEmpresa}" immediate="true" action="#{SituacaoContratoSinteticoDWControle.limparEmpresa}" image="images/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="nomeEmpresa"/>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:selectBooleanCheckbox id="plano" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarPlano}">
                            <h:outputText styleClass="titulo3" value="Plano"/>
                        </h:selectBooleanCheckbox>

                        <h:selectBooleanCheckbox id="consultor" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarColaborador}">
                            <h:outputText styleClass="titulo3" value="Consultor"/>
                        </h:selectBooleanCheckbox>
                        <h:selectBooleanCheckbox id="orientador" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarColaborador}">
                            <h:outputText styleClass="titulo3" value="Orientador"/>
                        </h:selectBooleanCheckbox>



                        <h:selectBooleanCheckbox id="personal" value="#{SituacaoContratoSinteticoDWControle.situacaoContratoSinteticoDW.apresentarColaborador}">
                            <h:outputText styleClass="titulo3" value="Personal Training"/>
                        </h:selectBooleanCheckbox>
                    </h:panelGrid>



                    <div class="sepmenu"><img src="./images/shim.gif"></div>
                    <div>  <h:outputLink styleClass="titulo3" value="../faces/tela1.jsp">
                            <h:outputText value="Menu Principal"/>
                        </h:outputLink>
                    </div>
                    <div class="sepmenu"><img src="./images/shim.gif"></div>
                    <!-- fim item-->
                </td>
            </tr>
        </table>

        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top"><img style="vertical-align:middle;margin-right:9px;" src="images/icon_cadastros.gif" width="12" height="15"><a class="titulo2" href="javascript:;">Estoque</a></td>
            </tr>
        </table>
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top"><img style="vertical-align:middle;margin-right:7px;" src="images/icon_sair.gif" width="14" height="15"><a class="titulo2" href="login.jsp">Sair</a></td>
            </tr>
        </table>
    </div>

    <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>

<!-- fim box -->




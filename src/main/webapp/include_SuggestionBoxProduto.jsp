<%--
    Author                           : <PERSON>lisses
    Data                             : 26/01/2011
    Objetivo da Tela                 : Pesquisar produtos com AutoComplete.
    Em qual tela pode ser usada      : Nas telas onde � necess�rio pesquisar produtos com AutoComplete.
    Exemplo 1 para importar esta tela: <%@include file="include_SuggestionBoxProduto.jsp" %>
    Exemplo 2 para importar esta tela:  <h:panelGroup rendered="#{(LocalAcessoControle.codigoTipoValidacao == 2)}">
                                             <%@include file="include_SuggestionBoxProduto.jsp" %>
                                        </h:panelGroup>
    Ex. p/ recuperar o produto selecionado: ProdutoVO produto =  (ProdutoVO) context().getExternalContext().getSessionMap().get(ProdutoControle.chaveSessionProduto);
--%>
<%@include file="includes/imports.jsp" %>
<h:inputText  id="nomeProdutoSelecionado"
           size="50"
           maxlength="50"
           onblur="blurinput(this);"
           onfocus="focusinput(this);"
           styleClass="form"
           value="#{ProdutoControle.produtoSelecionado}" />

<rich:suggestionbox   height="200" width="400"
                      for="nomeProdutoSelecionado"
                      status="statusInComponent"
                      immediate="true"
                      suggestionAction="#{ProdutoControle.executarAutocompletePesqProduto}"
                      minChars="1"
                      rowClasses="linhaImpar, linhaPar"
                      var="result"  id="suggestionResponsavel">
    <a4j:support event="onselect"
                 reRender="form"
                 action="#{ProdutoControle.selecionarProduto}">
    </a4j:support>
    <h:column>
        <f:facet name="header">
            <h:outputText value="Nome"  styleClass="textverysmall"/>
        </f:facet>
        <h:outputText styleClass="textverysmall" value="#{result.descricao}" />
    </h:column>
    <h:column >
        <f:facet name="header">
            <h:outputText value="Tipo" styleClass="textverysmall"/>
        </f:facet>
        <h:outputText styleClass="textverysmall" value="#{result.tipoProduto}" />
    </h:column>
    <h:column >
        <f:facet name="header">
            <h:outputText value="Categoria" styleClass="textverysmall"/>
        </f:facet>
        <h:outputText  styleClass="textverysmall" value="#{result.categoriaProduto.descricao}" />
    </h:column>
    <h:column >
        <f:facet name="header">
            <h:outputText value="Valor Final" styleClass="textverysmall"/>
        </f:facet>
        <h:outputText  styleClass="textverysmall" value="#{result.valorFinal}" />
    </h:column>
</rich:suggestionbox>

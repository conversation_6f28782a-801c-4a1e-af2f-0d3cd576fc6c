<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="script/jquery.maskedinput-1.7.6.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<style>
    .dataTables_filter {
        height: auto;
        margin-top: -90px;
    }
    .titulo-topo {
        margin-bottom: 0;
    }
</style>
<script>
    jQuery.noConflict();
</script>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_CupomFiscal_tituloForm}</title>
    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_CupomFiscal_tituloForm}"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlWiki}Cadastros:Config._Financeiras:Consulta_de_Cupons_Fiscais#Consulta_do_Cupom_Fiscal"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{CupomFiscalControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,cupom_Apresentar=Recibo,pessoa_Apresentar=Cliente,dataHoraVendaFormatada=Venda,dataHoraEmissaoFormatada=Emissão,situacao_Apresentar=Situação,valorTotalFormatado=Valor"/>
                                <f:attribute name="prefixo" value="CupomFiscal"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{CupomFiscalControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,cupom_Apresentar=Recibo,pessoa_Apresentar=Cliente,dataHoraVendaFormatada=Venda,dataHoraEmissaoFormatada=Emissão,situacao_Apresentar=Situação,valorTotalFormatado=Valor"/>
                                <f:attribute name="prefixo" value="CupomFiscal"/>
                                <f:attribute name="titulo" value="Cupom Fiscal"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1 margin-v-10" style="margin-left: 46px;margin-right:46px;width: calc(100% - 92px);">
                    <h:panelGroup layout="block" style="width: 100%;">
                        <h:panelGroup layout="block" style="width: 45%; float: left;">
                            <h:panelGroup layout="block">
                                <h:outputText value="Data de Emissão: "/>
                                <rich:calendar id="dtInicioEmissao"
                                               inputSize="10"
                                               showWeekDaysBar="false"
                                               inputClass="form"
                                               showWeeksBar="false"
                                               oninputfocus="focusinput(this);"
                                               oninputblur="blurinput(this);"
                                               enableManualInput="true"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block">
                                <h:outputText value=" até "/>
                                <rich:calendar id="dtFimEmissao"
                                               inputSize="10"
                                               showWeekDaysBar="false"
                                               inputClass="form"
                                               showWeeksBar="false"
                                               oninputfocus="focusinput(this);"
                                               oninputblur="blurinput(this);"
                                               enableManualInput="true"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <a4j:commandButton id="limparPeriodoEmissao"
                                                   onclick="document.getElementById('form:dtInicioEmissaoInputDate').value = '';
                                                       document.getElementById('form:dtFimEmissaoInputDate').value='';"
                                                   image="/images/limpar.gif" title="Limpar data de Emissao."
                                                   status="false"/>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block">
                            <h:panelGroup layout="block">
                                <h:outputText value="Data de Pagamento: "/>
                                <rich:calendar id="dtInicioPagamento"
                                               inputSize="10"
                                               showWeekDaysBar="false"
                                               inputClass="form"
                                               showWeeksBar="false"
                                               oninputfocus="focusinput(this);"
                                               oninputblur="blurinput(this);"
                                               enableManualInput="true"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block">
                                <h:outputText value=" até "/>
                                <rich:calendar id="dtFimPagamento"
                                               inputSize="10"
                                               showWeekDaysBar="false"
                                               inputClass="form"
                                               showWeeksBar="false"
                                               oninputfocus="focusinput(this);"
                                               oninputblur="blurinput(this);"
                                               enableManualInput="true"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <a4j:commandButton id="limparPeriodoPagamento"
                                                   onclick="document.getElementById('form:dtInicioPagamentoInputDate').value='';
                                                       document.getElementById('form:dtFimPagamentoInputDate').value='';"
                                                   image="/images/limpar.gif" title="Limpar data de Pagamento."
                                                   status="false"/>
                            </h:panelGroup>

                            <a4j:commandLink styleClass="pure-button pure-button-primary margin-h-10 pull-right"
                                             value="Filtrar" oncomplete="recarregarTabela()"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblCupomFiscal" class="tabelaCupomFiscal pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_CupomFiscal_label_codigo}</th>
                    <th>${msg_aplic.prt_CupomFiscal_label_recibo}</th>
                    <th>${msg_aplic.prt_CupomFiscal_label_cliente}</th>
                    <th>${msg_aplic.prt_CupomFiscal_label_dataVenda}</th>
                    <th>${msg_aplic.prt_CupomFiscal_label_dataImpressao}</th>
                    <th>${msg_aplic.prt_CupomFiscal_label_situacao}</th>
                    <th>${msg_aplic.prt_CupomFiscal_label_valor}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{CupomFiscalControle.limparDataImpressao}"
                                reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{CupomFiscalControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{CupomFiscalControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty CupomFiscalControle.mensagem}"
                              value=" #{CupomFiscalControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty CupomFiscalControle.mensagemDetalhada}"
                              value=" #{CupomFiscalControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>
    </h:panelGroup>

    <script src="beta/js/dt-server.js" type="text/javascript"></script>

    <script>
        function recarregarTabela() {
            var dtInicioEmissao = document.getElementById("form:dtInicioEmissaoInputDate").value;
            var dtFimEmissao = document.getElementById("form:dtFimEmissaoInputDate").value;
            var dtInicioPagamento = document.getElementById("form:dtInicioPagamentoInputDate").value;
            var dtFimPagamento = document.getElementById("form:dtFimPagamentoInputDate").value;

            var configs = tabelaAtual.dataTableSettings[0];
            var sEcho = configs.iDraw;
            var iDisplayStart = configs._iDisplayStart;
            var iDisplayLength = configs._iDisplayLength;
            var sSearch = jQuery(".filtroDT").val().toLowerCase();
            var iSortCol_0 = configs.aaSorting[0][0];
            var sSortDir_0 = configs.aaSorting[0][1];

            var data = {
                "dtInicioEmissao": dtInicioEmissao, "dtFimEmissao": dtFimEmissao,
                "dtInicioPagamento": dtInicioPagamento, "dtFinalPagamento": dtFimPagamento,
                "sEcho": sEcho, "iDisplayStart": iDisplayStart,
                "iDisplayLength": iDisplayLength, "sSearch": sSearch,
                "iSortCol_0": iSortCol_0, "sSortDir_0": sSortDir_0
            };

            tabelaAtual.dataTable().fnDestroy(0);
            iniTblServer("tabelaCupomFiscal", "${contexto}/prest/financeiro/cupomFiscal", data);
        }

        jQuery(window).on("load", function () {
            iniTblServer("tabelaCupomFiscal", "${contexto}/prest/financeiro/cupomFiscal");
        });
    </script>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>
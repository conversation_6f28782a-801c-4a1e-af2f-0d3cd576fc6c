<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
<script type="text/javascript" language="javascript" src="script/chart_amcharts.js"></script>
<script src="script/chart_pie.js" type="text/javascript"></script>
<script src="script/chart_light.js" type="text/javascript"></script>
<script src="script/chart_serial.js" type="text/javascript"></script>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<script type="text/javascript">
    setDocumentCookie('popupsImportante', 'close',1);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>

<style type="text/css">
    .painelMetadeTelaBoxCanalPacto{
        min-width: 330px;
        width: 47%;
        height: 45vh;
        background-color: white;
        -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
        -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
        display: inline-table;
        margin: 1.5% 0 0 1.5%;
        position: relative;
    }

    .tituloBoxCanalPacto{
        width: 100%;
        border-bottom: #E5E5E5 1px solid;
        display: table;
        line-height: 50px;
        font-family: Arial;
        text-decoration: none;
        font-weight: bold;
        font-size: 1.1em;
        color: #333333;
        margin-left: 10px;
    }

</style>

<%
            pageContext.setAttribute("modulo", "zillyonWeb");
%>

<c:set var="root" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Tickets Fechados"/>
    </title>
    <h:form id="form" >
        <input type="hidden" value="${modulo}" name="modulo"/>
        <html>
            <jsp:include page="include_head.jsp" flush="true" />

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">

                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <tr>

                                <td width="150" align="left" valign="top" class="bglateraltop" style="background-color: white;">
                                      <jsp:include page="include_box_menulateral_minhaconta.jsp" flush="true" />
                                </td>

                                <td align="left" valign="top" style="padding-left: 5px; padding-right: 0px;" class="scrollInfinito">
                                    <%@include file="include_tela_solicitacoesConcluidas.jsp" %>
                                </td>
                            </tr>


                        </table>


                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>
    </h:form>

    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp"/>
</f:view>


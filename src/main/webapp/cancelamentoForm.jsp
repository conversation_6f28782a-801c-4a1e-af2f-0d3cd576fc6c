<%@page contentType="text/html"%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
    .to-uper-case{
        text-transform: uppercase;
    }
</style>
<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        Cancelamento
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"/>
        <c:set var="titulo" scope="session" value="Cancelamento"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="form" styleClass="overflow-visible">
        <h:panelGrid columns="2" styleClass="container">
            <h:panelGrid columns="1" width="100%" cellpadding="5" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:panelGrid  rendered="#{CancelamentoContratoControle.planoVencido}" columns="1"  width="100%">
                    <h:outputText styleClass="mensagemDetalhadaGrande" value="Não será possível cancelar o contrato, pois o mesmo está vencido ou cancelado"/>
                    <h:panelGrid>
                        <h:panelGroup style="padding-left:400px;padding-bottom:200px;">
                            <h:commandButton id="fechar1" alt="Fechar Janela" onclick="fecharJanela();executePostMessage({reloadContractPage: true});" image="./images/btn_fechar.gif" />
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid  rendered="#{!CancelamentoContratoControle.planoVencido}" columns="1"  width="100%">
                    <h:outputText value="NOME DO CLIENTE" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                    
                    <h:outputText styleClass="tituloCampos" 
                                  rendered="#{CancelamentoContratoControle.dataFinalArmario != null}"
                                  value="Cliente tem armário alugado até o dia #{CancelamentoContratoControle.dataFinalArmarioApresentar}."/>
                    
                    <div class="texto-size-14-real texto-cor-cinza-2 texto-font">
                        <br>
                        O <b>Cancelamento</b> <%--é uma operação que é realizada caso o cliente deseja cancelar o
                         contrato.--%>é uma operação que é realizada caso o cliente deseja interromper definitivamente este contrato com a academia.
                        <c:if test="${!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado and !CancelamentoContratoControle.contratoVO.bolsa}">
                        Esta operação poderá gerar um valor para devolução ou transferência do saldo
                        que o cliente tem com a academia.<br>
                        No caso da devolução do saldo do cliente são cobrados os <u>custos administrativos</u> e um <u>percentual
                            de multa pelo cancelamento</u> do contrato. Já no caso da transferência do saldo são cobrados
                        somente os <u>custos administrativos</u> do contrato. A forma de retorno desse saldo fica a critério
                        do cliente.
                        </c:if>
                        <br>
                        <br>
                    </div>
                    <h:panelGrid>
                        <h:outputText value="#{msg_aplic.prt_CancelamentoContrato_dataCancelamento}" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"/>
                        <rich:spacer height="3px;"/>
                        <h:panelGroup styleClass="dateTimeCustom"  >
                            <rich:calendar id="dataCancelamento"
                                           value="#{CancelamentoContratoControle.cancelamentoContratoVO.dataCancelamento}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="false"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           zindex="2"
                                           showWeeksBar="false"
                                           isDayEnabled="#{CancelamentoContratoControle.validarDataRetroativa}"/>
                            <h:message for="dataCancelamento"  styleClass="mensagemDetalhada"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        </h:panelGroup>
                        <br>
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_JustificativaOperacao_consultarJustificativa}" />
                        <h:panelGroup id="pnlCancelamento" styleClass="font-size-em-max">
                            <div class="cb-container margenVertical" style="width: 300px;">
                                <h:selectOneMenu  id="tipoOperacao"  onblur="blurinput(this);"  onfocus="focusinput(this);" style="width: 300px;" value="#{CancelamentoContratoControle.cancelamentoContratoVO.tipoJustificativaOperacao}" >
                                    <f:selectItems value="#{CancelamentoContratoControle.listaSelectItemJustificativaOperacao}"/>
                                    <a4j:support event="onchange" reRender="pnlCancelamento" action="#{CancelamentoContratoControle.validarNecessidadeAnexo}"/>
                                </h:selectOneMenu>
                            </div>
                            <rich:spacer width="7"/>
                            <a4j:commandLink id="atualizar_tipoOperacao" action="#{CancelamentoContratoControle.montarListaSelectItemJustificativaCancelamento}"  ajaxSingle="true" reRender="form:tipoOperacao">
                                <i class="fa-icon-refresh texto-size-14-real texto-cor-cinza " ></i>
                            </a4j:commandLink>
                            <rich:spacer width="7"/>
                            <a4j:commandLink id="adicionarTipoOperacao" action="#{JustificativaOperacaoControle.reset}" oncomplete="abrirPopup('justificativaOperacaoCons.jsp', 'JustificativaOperacao', 800, 595);" >
                                <i class="fa-icon-plus-sign texto-size-14-real texto-cor-cinza "></i>
                            </a4j:commandLink>

                            <h:panelGroup id="pnlUpload" layout="block" rendered="#{CancelamentoContratoControle.apresentarUploadArquivo}" style="margin-top: 8px">
                                <h:outputText value="ANEXAR ATESTADO" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                                <rich:fileUpload id="upload"
                                                 listHeight="60"
                                                 listWidth="625"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{CancelamentoContratoControle.upload}"
                                                 maxFilesQuantity="1"
                                                 allowFlash="false"
                                                 immediateUpload="false"
                                                 acceptedTypes="jpg, jpeg, gif, png, bmp, JPG, JPEG, GIF, PNG, BMP"
                                                 addControlLabel="Adicionar"
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                                 progressLabel="Enviando"
                                                 clearControlLabel="Limpar"
                                                 clearAllControlLabel="Limpar todos"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar">
                                    <a4j:support event="onadd" oncomplete="#{CancelamentoContratoControle.onCompleteArquivoAnexo}"/>
                                    <a4j:support event="onerror" oncomplete="#{CancelamentoContratoControle.onCompleteArquivoAnexo}"/>
                                    <a4j:support event="onupload" oncomplete="#{CancelamentoContratoControle.onCompleteArquivoAnexo}"/>
                                    <a4j:support event="onuploadcomplete" oncomplete="#{CancelamentoContratoControle.onCompleteArquivoAnexo}"/>
                                    <a4j:support event="onclear" reRender="pnlUpload"/>
                                </rich:fileUpload>
                            </h:panelGroup>

                        </h:panelGroup>

                    </h:panelGrid>
                    <h:panelGrid rendered="#{CancelamentoContratoControle.contratoVO.bolsa}">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="OBSERVAÇÃO:"/>
                        <h:inputTextarea value="#{CancelamentoContratoControle.cancelamentoContratoVO.observacao}" id="descricaoCalculo" styleClass=""  rows="7" cols="80"/>
                    </h:panelGrid>
                    <h:panelGroup style="width: 100%;">
                        <a4j:commandLink rendered="#{!CancelamentoContratoControle.contratoVO.bolsa}"
                                         id="proximo" style="float: right; margin-top: 10px;"
                                         action="#{CancelamentoContratoControle.calculoCancelamentoCliente}"
                                         oncomplete="#{CancelamentoContratoControle.mensagemNotificar}"
                                       styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                        <a4j:commandLink rendered="#{CancelamentoContratoControle.contratoVO.bolsa and !CancelamentoContratoControle.finalizouCancelamento}"
                                         id="confirmar"
                                         title="Confirmar"  reRender="form,formConfirmacaoCancelamento,panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade"
                                         action="#{CancelamentoContratoControle.validarAutorizacao}"
                                         styleClass="pure-button pur+e-button-primary"
                                         oncomplete="#{CancelamentoContratoControle.mensagemNotificar};#{CancelamentoContratoControle.msgAlert}">
                            <i class="fa-icon-ok" ></i>&nbsp Confirmar
                        </a4j:commandLink>
                        <a4j:commandLink id="enviarEmailCancelamento"
                                         value="Enviar Email"
                                         rendered="#{CancelamentoContratoControle.finalizouCancelamento}"
                                         title="Enviar email de cancelamento do contrato"
                                         reRender="mdlMensagemGenerica"
                                         action="#{CancelamentoContratoControle.enviarEmailCancelamentoContrato}"
                                         oncomplete="#{CancelamentoContratoControle.modalMensagemGenerica}"
                                         styleClass="pure-button"/>
                        <a4j:commandLink id="comprovanteOpCan"
                                         rendered="#{CancelamentoContratoControle.finalizouCancelamento}"
                                         title="Imprimir Comprovante da Operação de Cancelamento"
                                         action="#{CancelamentoContratoControle.imprimirComprovanteOperacao}"
                                         oncomplete="abrirPopupPDFImpressao('relatorio/#{CancelamentoContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                         styleClass="pure-button">
                            <i class="fa-icon-print"></i>&nbsp Imprimir Comprovante
                        </a4j:commandLink>
                        <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});"
                                       rendered="#{CancelamentoContratoControle.finalizouCancelamento}"
                                       styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-remove" ></i>&nbsp Fechar
                        </h:commandLink>
                    </h:panelGroup>
                </h:panelGrid>

                <%--<h:panelGrid columns="1" width="100%">--%>
                    <%--<rich:modalPanel id="panel" width="350" height="100" showWhenRendered="#{CancelamentoContratoControle.cancelamentoContratoVO.mensagemErro}">--%>
                        <%--<f:facet name="header">--%>
                            <%--<h:panelGroup>--%>
                                <%--<h:outputText value="Atenção!"></h:outputText>--%>
                            <%--</h:panelGroup>--%>
                        <%--</f:facet>--%>
                        <%--<f:facet name="controls">--%>
                            <%--<h:panelGroup>--%>
                                <%--<h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>--%>
                                <%--<rich:componentControl for="panel" attachTo="hidelink" operation="hide" event="onclick"/>--%>
                            <%--</h:panelGroup>--%>
                        <%--</f:facet>--%>
                        <%--<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">--%>
                            <%--<h:panelGroup>--%>
                                <%--<h:graphicImage value="/imagens/erro.png"/>--%>
                                <%--<rich:spacer width="10" />--%>
                                <%--<h:outputText id="msgCancelamentoErro" styleClass="mensagemDetalhada" value="#{CancelamentoContratoControle.mensagemDetalhada}"/>--%>
                            <%--</h:panelGroup>--%>
                            <%--&lt;%&ndash;rich:spacer height="10px"/>--%>
                            <%--<a4j:commandButton id="ok" action="Richfaces.hideModalPanel('panel')" value="   #{msg_bt.btn_ok}   " styleClass="botoes"/&ndash;%&gt;--%>
                        <%--</h:panelGrid>--%>
                    <%--</rich:modalPanel>--%>
                <%--</h:panelGrid>--%>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp"%>
    <%@include file="./includes/imports.jsp" %>
</f:view>

<rich:modalPanel id="modalRenegociadas" autosized="true" shadowOpacity="true"
                 minWidth="680">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Hist�rico de parcela renegociada"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            styleClass="xcloseModal" id="hidelinkRenegociadas"/>
            <rich:componentControl for="modalRenegociadas" attachTo="hidelinkRenegociadas" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formModalRenegociadas">
        <h:panelGrid width="100%" style="text-align: right">
            <h:panelGroup layout="block">
                <a4j:commandLink id="exportarExcel"
                                 style="margin-left: 8px;"
                                 actionListener="#{ExportadorListaControle.exportar}"
                                 rendered="#{not empty ModalRenegociadasControle.listaLogRenegociacaoParcelasExportacao}"
                                 oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                 accesskey="2" styleClass="botoes">
                    <f:attribute name="lista"
                                 value="#{ModalRenegociadasControle.listaLogRenegociacaoParcelasExportacao}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="id=ID Parcela,valor=Valor,dataOperacao_Apresentar=Vencimento,situacao=Situa��o,dataOperacao_Apresentar=Data da Opera��o,origem=Origem"/>
                    <f:attribute name="prefixo" value="ControleOpExcecao"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGroup layout="block" style="padding: 10px 10px 10px 10px;">
            <rich:dataTable id="idRenegociadasOriginais" value="#{ModalRenegociadasControle.listaLogRenegociacaoParcelasOriginais}"
                            var="parcela"
                            rows="20" width="100%">
                <rich:column sortBy="#{parcela.id}">
                    <f:facet name="header">
                        <h:outputText value="ID Parcela"/>
                    </f:facet>
                    <h:outputText value="#{parcela.id}"/>
                </rich:column>
                <rich:column sortBy="#{parcela.valor}">
                    <f:facet name="header">
                        <h:outputText value="Valor"/>
                    </f:facet>
                    <h:outputText value="R$ "/>
                    <h:outputText value="#{parcela.valor}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </rich:column>
                <rich:column sortBy="#{parcela.vencimento_Apresentar}">
                    <f:facet name="header">
                        <h:outputText value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{parcela.vencimento_Apresentar}"/>
                </rich:column>
                <rich:column sortBy="#{parcela.situacao}">
                    <f:facet name="header">
                        <h:outputText value="Situa��o"/>
                    </f:facet>
                    <h:outputText value="#{parcela.situacao}"/>
                </rich:column>
                <rich:column sortBy="#{parcela.dataOperacao_Apresentar}">
                    <f:facet name="header">
                        <h:outputText value="Data da Opera��o"/>
                    </f:facet>
                    <h:outputText value="#{parcela.dataOperacao_Apresentar}"/>
                </rich:column>
                <rich:column sortBy="#{parcela.origem}">
                    <f:facet name="header">
                        <h:outputText value="Origem"/>
                    </f:facet>
                    <h:outputText value="#{parcela.origem}"/>
                </rich:column>
                <rich:column style="text-align: center">
                    <f:facet name="header">
                        <h:outputText value="A��es"/>
                    </f:facet>
                    <a4j:commandLink action="#{ModalRenegociadasControle.atualizarModalRenegociacoesBack}"
                                     reRender="mensagens, formModalRenegociadas"
                                     rendered="#{parcela.origem != 'Original'}"
                                     oncomplete="#{ModalRenegociadasControle.mensagemNotificar}"
                                     styleClass="linkPadrao texto-cor-azul texto-size-14-real">
                        <i class="fa-icon-arrow-left"></i>
                        <f:param name="parcela" value="#{parcela}"/>
                    </a4j:commandLink>
                </rich:column>
                <f:facet name="footer">
                    <h:panelGroup styleClass="pull-right">
                        <h:outputText
                                value="#{fn:length(ModalRenegociadasControle.listaLogRenegociacaoParcelasOriginais)} Itens"/>
                    </h:panelGroup>
                </f:facet>
            </rich:dataTable>
            <rich:datascroller for="idRenegociadasOriginais"
                               maxPages="#{fn:length(ModalRenegociadasControle.listaLogRenegociacaoParcelas)/20}"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="padding: 10px 10px 10px 10px;">
            <rich:dataTable id="idRenegociadas" value="#{ModalRenegociadasControle.listaLogRenegociacaoParcelas}"
                            var="parcela"
                            rows="20" width="100%">
                <rich:column sortBy="#{parcela.id}">
                    <f:facet name="header">
                        <h:outputText value="ID Parcela"/>
                    </f:facet>
                    <h:outputText value="#{parcela.id}"/>
                </rich:column>
                <rich:column sortBy="#{parcela.valor}">
                    <f:facet name="header">
                        <h:outputText value="Valor"/>
                    </f:facet>
                    <h:outputText value="R$ "/>
                    <h:outputText value="#{parcela.valor}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </rich:column>
                <rich:column sortBy="#{parcela.vencimento_Apresentar}">
                    <f:facet name="header">
                        <h:outputText value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{parcela.vencimento_Apresentar}"/>
                </rich:column>
                <rich:column sortBy="#{parcela.situacao}">
                    <f:facet name="header">
                        <h:outputText value="Situa��o"/>
                    </f:facet>
                    <h:outputText value="#{parcela.situacao}"/>
                </rich:column>
                <rich:column sortBy="#{parcela.dataOperacao_Apresentar}">
                    <f:facet name="header">
                        <h:outputText value="Data da Opera��o"/>
                    </f:facet>
                    <h:outputText value="#{parcela.dataOperacao_Apresentar}"/>
                </rich:column>
                <rich:column sortBy="#{parcela.origem}">
                    <f:facet name="header">
                        <h:outputText value="Origem"/>
                    </f:facet>
                    <h:outputText value="#{parcela.origem}"/>
                </rich:column>
                <rich:column style="text-align: center">
                    <f:facet name="header">
                        <h:outputText value="A��es"/>
                    </f:facet>
                    <a4j:commandLink action="#{ModalRenegociadasControle.atualizarModalRenegociacoesNext}"
                                     reRender="mensagens, formModalRenegociadas"
                                     rendered="#{parcela.situacao == 'Renegociado'}"
                                     oncomplete="#{ModalRenegociadasControle.mensagemNotificar}"
                                     styleClass="linkPadrao texto-cor-azul texto-size-14-real">
                        <i class="fa-icon-arrow-right"></i>
                        <f:param name="parcela" value="#{parcela}"/>
                    </a4j:commandLink>
                </rich:column>
                <f:facet name="footer">
                    <h:panelGroup styleClass="pull-right">
                        <h:outputText
                                value="#{fn:length(ModalRenegociadasControle.listaLogRenegociacaoParcelas)} Itens"/>
                    </h:panelGroup>
                </f:facet>
            </rich:dataTable>
            <rich:datascroller for="idRenegociadas"
                               maxPages="#{fn:length(ModalRenegociadasControle.listaLogRenegociacaoParcelas)/20}"/>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>
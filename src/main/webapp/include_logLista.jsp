<%-- 
    Document   : include_log
    Created on : 26/04/2012, 17:31:45
    Author     : carla
--%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<a4j:outputPanel>
    <rich:modalPanel id="panelLog" autosized="true" shadowOpacity="false" width="800" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Detalhes de Log"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink6"/>
                <rich:componentControl for="panelLog" attachTo="hidelink6" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formLog" ajaxSubmit="true">
            <rich:dataTable id="tableLogs" styleClass="tabForm" width="100%" value="#{LogControle.listaConsulta}" var="resumoLogs" rows="3">
                <rich:column width="25%">
                    <f:facet name="header">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_operacao}"/>
                    </f:facet>
                    <h:outputText id="operacao" value="#{resumoLogs.operacao}" />
                </rich:column>
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAnterior}"/>
                    </f:facet>
                    <h:inputTextarea rows="10" styleClass="campos" readonly="true" cols="40" id="valorCampoAnterior" value="#{resumoLogs.valorCampoAnterior}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAlterado}"/>
                    </f:facet>
                    <h:inputTextarea rows="10" styleClass="campos" readonly="true" cols="40" id="valorCampoAlterado" value="#{resumoLogs.valorCampoAlterado}"/>
                </rich:column>
            </rich:dataTable>

            <rich:datascroller for="tableLogs" maxPages="3"  />
        </a4j:form>
    </rich:modalPanel>
</a4j:outputPanel>

<%@include file="/includes/imports.jsp" %>
<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>
  Date: 10/04/2019
  Time: 13:27
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<h:panelGroup layout="block" styleClass="margin-box"
              rendered="#{GestaoTurmaControle.manutencaoTransferencia}">

    <h:panelGrid columns="1" id="panelGeralTransferencia" style="width: 100%">

        <h:panelGroup layout="block" id="panel1" styleClass="panel1" style="display: block; padding-top: 20px;">

            <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font texto-bold"
                          value="PASSO 1: SELECIONE UMA TURMA"/>

            <h:panelGroup layout="block" id="panelFiltrosAlunos"
                          style="display: inline-flex; padding-top: 20px; width: 100%">

                <h:panelGroup layout="block">
                    <h:selectOneMenu id="modalidadeTransferencia" onblur="blurinput(this);"
                                     styleClass="inputTextClean"
                                     onfocus="focusinput(this);"
                                     value="#{GestaoTurmaControle.modalidadeTransferencia}">
                        <f:selectItems value="#{GestaoTurmaControle.selectItensModalidade}"/>
                        <a4j:support event="onchange"
                                     action="#{GestaoTurmaControle.montarTurmas}"
                                     reRender="panelFiltrosAlunos"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:panelGroup layout="block" style="padding-left: 15px"
                              rendered="#{not empty GestaoTurmaControle.listaTurmaOrigem}">
                    <h:selectOneMenu id="turmaOrigem" onblur="blurinput(this);"
                                     styleClass="inputTextClean"
                                     onfocus="focusinput(this);"
                                     value="#{GestaoTurmaControle.turmaOrigem}">
                        <f:selectItems value="#{GestaoTurmaControle.listaTurmaOrigem}"/>
                        <a4j:support event="onchange"
                                     action="#{GestaoTurmaControle.montarHorarioTurmaOrigem}"
                                     reRender="panelFiltrosAlunos"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:panelGroup layout="block" style="padding-left: 15px"
                              rendered="#{not empty GestaoTurmaControle.listaHorarioTurmaOrigem}">
                    <h:selectOneMenu id="horarioOrigem" onblur="blurinput(this);"
                                     styleClass="inputTextClean"
                                     onfocus="focusinput(this);"
                                     value="#{GestaoTurmaControle.horarioOrigem}">
                        <f:selectItems value="#{GestaoTurmaControle.listaHorarioTurmaOrigem}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelBtnAcoesPanel1"
                          style="padding-top: 30px; text-align: center; width: 100%">
                <a4j:commandLink styleClass="botaoPrimario texto-size-16"
                                 id="proximo"
                                 value="Próximo"
                                 action="#{GestaoTurmaControle.consultarAlunos}"
                                 oncomplete="#{GestaoTurmaControle.onComplete};#{GestaoTurmaControle.mensagemNotificar}"
                                 reRender="form:panel2">
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panel2" styleClass="panel2" style="display: none; padding-top: 20px;">

            <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font texto-bold"
                          value="PASSO 2: SELECIONE OS ALUNOS QUE DESEJA TRANSFERIR"/>

            <h:panelGroup layout="block" id="panelListaAlunos"
                          style="padding-top: 20px; width: 100%">

                <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaAlinhamento" id="tabela"
                                rendered="#{not empty GestaoTurmaControle.alunosTurma}"
                                value="#{GestaoTurmaControle.alunosTurma}" rows="50" var="aluno" rowKeyVar="status">
                    <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>

                    <rich:column sortBy="#{aluno.selecionado}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:selectBooleanCheckbox value="#{GestaoTurmaControle.selecionarTodos}">
                                <a4j:support event="onchange"
                                             action="#{GestaoTurmaControle.acaoSelecionarTodos}"
                                             reRender="panelListaAlunos"/>
                            </h:selectBooleanCheckbox>
                        </f:facet>
                        <h:selectBooleanCheckbox value="#{aluno.selecionado}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Foto"/>
                        </f:facet>
                        <h:graphicImage style="width:30px;height:30px; border-radius: 50%;"
                                        styleClass="tooltipsterright"
                                        url="#{aluno.clienteVO.pessoa.urlFoto}">
                        </h:graphicImage>
                    </rich:column>
                    <rich:column sortBy="#{aluno.clienteVO.matricula}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{aluno.clienteVO.matricula}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.clienteVO.pessoa.nome}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Aluno"/>
                        </f:facet>
                        <h:outputText value="#{aluno.clienteVO.pessoa.nome}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.codigo == 0 ? 'Sim' : 'Não'}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Reposição"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.codigo == 0 ? 'Sim' : 'Não'}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.dataInicio}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Data Início Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.dataInicio}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.dataFim}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Data Fim Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.dataFim}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.professor.pessoa_Apresentar}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Professor"/>
                        </f:facet>
                        <h:outputText
                                value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.professor.pessoa_Apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.ambiente.descricao}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Ambiente"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.ambiente.descricao}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.diaSemana_Apresentar}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Dia da Semana"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.diaSemana_Apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.horaInicial}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Hora Início"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.horaInicial}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.horaFinal}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Hora Fim"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.horarioTurma.horaFinal}"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller rendered="#{not empty GestaoTurmaControle.alunosTurma}"
                                   align="center" for="form:tabela" maxPages="20" id="sctabela"/>

            </h:panelGroup>

            <h:panelGroup layout="block" id="panelBtnAcoesPanel2"
                          style="padding-top: 30px; text-align: center; width: 100%">
                <a4j:commandLink styleClass="botaoSecundario texto-size-16"
                                 value="Voltar"
                                 oncomplete="mostrarTela1()"
                                 reRender="form:panel1">
                </a4j:commandLink>

                <a4j:commandLink styleClass="botaoPrimario texto-size-16"
                                 value="Próximo"
                                 style="margin-left: 20px"
                                 rendered="#{not empty GestaoTurmaControle.alunosTurma}"
                                 action="#{GestaoTurmaControle.verificarAlunosParaTransferir}"
                                 oncomplete="#{GestaoTurmaControle.onComplete};#{GestaoTurmaControle.mensagemNotificar}"
                                 reRender="form:panel3,form:panelInforTransferencia,form:panelTurmaDestino,form:panelBtnAcoesPanel3">
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGroup>

        <h:panelGroup layout="block" id="panel3" styleClass="panel3" style="display: none; padding-top: 20px;">

            <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font texto-bold"
                          value="PASSO 3: SELECIONE A TURMA DE DESTINO"/>

            <h:panelGroup layout="block" id="panelTurmaDestino"
                          style="display: inline-flex; padding-top: 20px; width: 100%">

                <h:panelGroup layout="block">
                    <h:selectOneMenu id="turmaDestino" onblur="blurinput(this);"
                                     styleClass="inputTextClean"
                                     onfocus="focusinput(this);"
                                     value="#{GestaoTurmaControle.turmaDestino}">
                        <f:selectItems value="#{GestaoTurmaControle.listaTurmaDestino}"/>
                        <a4j:support event="onchange"
                                     action="#{GestaoTurmaControle.montarHorarioTurmaDestino}"
                                     reRender="form:tela1,form:panel4,form:panelInforTransferencia,form:panelBtnAcoesPanel3,form:panelTurmaDestino"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:panelGroup layout="block" style="padding-left: 15px"
                              rendered="#{not empty GestaoTurmaControle.listaHorarioTurmaDestino}">
                    <h:selectOneMenu id="horarioDestino" onblur="blurinput(this);"
                                     styleClass="inputTextClean"
                                     onfocus="focusinput(this);"
                                     value="#{GestaoTurmaControle.horarioDestino}">
                        <f:selectItems value="#{GestaoTurmaControle.listaHorarioTurmaDestino}"/>
                        <a4j:support event="onchange"
                                     action="#{GestaoTurmaControle.alterouHorarioTurmaDestino}"
                                     reRender="form:tela1,form:panel4,form:panelInforTransferencia,form:panelBtnAcoesPanel3"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelBtnAcoesPanel3"
                          style="padding-top: 30px; text-align: center; width: 100%">
                <a4j:commandLink styleClass="botaoSecundario texto-size-16"
                                 value="Voltar"
                                 oncomplete="mostrarTela2()"
                                 reRender="form:panel2">
                </a4j:commandLink>

                <a4j:commandLink styleClass="botaoPrimario texto-size-16"
                                 value="Verificar"
                                 style="margin-left: 20px"
                                 reRender="form:tela1,form:panel4,form:panelInforTransferencia,form:panelBtnAcoesPanel3"
                                 action="#{GestaoTurmaControle.validarTransferencia}"
                                 oncomplete="#{GestaoTurmaControle.onComplete};#{GestaoTurmaControle.mensagemNotificar}">
                </a4j:commandLink>

                <a4j:commandLink styleClass="#{GestaoTurmaControle.apresentarTransferir ? 'botaoPrimario' : 'botaoPrimarioDesabilitado'} texto-size-16 tooltipster"
                                 value="Transferir"
                                 disabled="#{!GestaoTurmaControle.apresentarTransferir}"
                                 title="#{GestaoTurmaControle.apresentarTransferir ? 'Clique para concluir a operação' : 'Clique em VERIFICAR para habilitar o botão de transferência'}"
                                 style="margin-left: 20px"
                                 reRender="form:tela1,form:panel4,form:panelInforTransferencia,form:panelBtnAcoesPanel3"
                                 action="#{GestaoTurmaControle.transferirAlunos}"
                                 oncomplete="#{GestaoTurmaControle.onComplete};#{GestaoTurmaControle.mensagemNotificar}">
                </a4j:commandLink>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelInforTransferencia"
                          style="padding-top: 20px; width: 100%">
                <h:outputText styleClass="texto-size-16 texto-cor-vermelho texto-font texto-bold"
                              rendered="#{not empty GestaoTurmaControle.retornoTransferencia}"
                              value="ATENÇÃO"/>

                <h:panelGroup layout="block" style="display: inline-flex; padding-top: 20px; width: 100%">

                    <h:outputText value="#{GestaoTurmaControle.retornoTransferencia}"
                                  escape="false"/>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panel4" styleClass="panel4" style="display: none; padding-top: 20px;">

            <h:outputText styleClass="texto-size-16 texto-cor-verde texto-font texto-bold"
                          value="FINALIZADO"/>

            <h:panelGroup layout="block" id="panelTurmaTela4"
                          style="display: inline-flex; padding-top: 20px; width: 100%">

                <h:outputText value="#{GestaoTurmaControle.retornoTransferencia}"
                              escape="false"/>

            </h:panelGroup>

            <h:panelGroup layout="block" id="panelBtnAcoesPanel4"
                          style="padding-top: 30px; text-align: center; width: 100%">
                <a4j:commandLink styleClass="botaoSecundario texto-size-16"
                                 value="Fechar"
                                 action="#{GestaoTurmaControle.alterarTipoManutencao}"
                                 reRender="form:painelTransferirAlunos">
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>

    </h:panelGrid>
</h:panelGroup>


<script type="text/javascript">

    function mostrarTela1() {
        jQuery('.panel1').css('display', 'block');
        jQuery('.panel2').css('display', 'none');
        jQuery('.panel3').css('display', 'none');
        jQuery('.panel4').css('display', 'none');
        carregarTooltipsterGestaoTurma();
    }

    function mostrarTela2() {
        jQuery('.panel1').css('display', 'none');
        jQuery('.panel2').css('display', 'block');
        jQuery('.panel3').css('display', 'none');
        jQuery('.panel4').css('display', 'none');
        carregarTooltipsterGestaoTurma();
    }

    function mostrarTela3() {
        jQuery('.panel1').css('display', 'none');
        jQuery('.panel2').css('display', 'none');
        jQuery('.panel3').css('display', 'block');
        jQuery('.panel4').css('display', 'none');
        carregarTooltipsterGestaoTurma();
    }

    function mostrarTela4() {
        jQuery('.panel1').css('display', 'none');
        jQuery('.panel2').css('display', 'none');
        jQuery('.panel3').css('display', 'none');
        jQuery('.panel4').css('display', 'block');
        carregarTooltipsterGestaoTurma();
    }

    carregarTooltipsterGestaoTurma();
</script>
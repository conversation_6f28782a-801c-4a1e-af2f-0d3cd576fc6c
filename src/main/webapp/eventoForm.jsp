<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title>
        <h:outputText value="#{msg_aplic.prt_Evento_tituloForm}"/>
    </title>


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Evento_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-criar-um-novo-evento/"/>

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topoReduzido_material_crm.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="form">
            <h:commandLink action="#{EventoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                      <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Evento_tituloForm}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-criar-um-novo-evento/"
                                      title="Clique e saiba mais: Evento" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText styleClass="tituloCampos" rendered="#{(EventoControle.eventoVO.codigo > 0) and (EventoControle.eventoVO.dataLancamento != null)}" value="Data cadastro:"/>
                    <h:inputText  onblur="blurinput(this);" size="8" readonly="true"  rendered="#{(EventoControle.eventoVO.codigo > 0) and (EventoControle.eventoVO.dataLancamento != null)}" onfocus="focusinput(this);" styleClass="form" value="#{EventoControle.eventoVO.dataLancamento_Apresentar}" />

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Evento_descricao}" />
                    <h:inputText  id="descricao" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{EventoControle.eventoVO.descricao}" />


                    <h:outputText styleClass="tituloCampos" value="Vigência inicial:"/>
                    <h:panelGroup>
                            <rich:calendar id="vigenciaInicial"
                                       value="#{EventoControle.eventoVO.vigenciaInicial}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <h:message for="vigenciaInicial"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Vigência final:"/>
                    <h:panelGroup>
                        <rich:calendar id="vigenciaFinal"
                                       value="#{EventoControle.eventoVO.vigenciaFinal}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <rich:jQuery id="mskVigenciaFinal" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        <h:message for="vigenciaFinal"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText value="Observação:"/>
                    <h:inputTextarea value="#{EventoControle.eventoVO.observacao}"  cols="80" rows="4"/>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                                <h:panelGrid columns="1" width="100%">
                                    <f:verbatim>
                                        <h:outputText value=" "/>
                                    </f:verbatim>
                                </h:panelGrid>
                                <h:commandButton  rendered="#{EventoControle.sucesso}" image="./imagensCRM/sucesso.png"/>
                                <h:commandButton rendered="#{EventoControle.erro}" image="./imagensCRM/erro.png"/>
                                <h:panelGrid columns="1" width="100%">
                                    <h:outputText id="msgGrupo" styleClass="mensagem"  value="#{EventoControle.mensagem}"/>
                                    <h:outputText  id="msgGrupodet" styleClass="mensagemDetalhada" value="#{EventoControle.mensagemDetalhada}"/>
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{EventoControle.novo}" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="10"/>
                            <a4j:commandButton id="salvar" action="#{EventoControle.gravar}" reRender="form" oncomplete="#{EventoControle.msgAlert}" value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <rich:spacer width="10"/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{EventoControle.msgAlert}" action="#{EventoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <rich:spacer width="10"/>
                            <h:commandButton id="consultar" immediate="true" action="#{EventoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                            <a4j:commandLink rendered="#{EventoControle.eventoVO.codigo > 0}"
                                              action="#{EventoControle.realizarConsultaLogObjetoSelecionado}"
                                              reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                              title="Visualizar LOG"
                                              style="display: inline-block; padding: 8px 15px;"
                                              styleClass="botoes nvoBt btSec">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>

</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
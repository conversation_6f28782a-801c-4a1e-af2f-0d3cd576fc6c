.menuCaptura {
    margin: 0 .5vw 1vh;
    border-bottom: 1px outset
}
.menuHorizontalList {
    list-style-type: none;
    margin: 0;
    padding: 10px 0;
    overflow: hidden
}
.menuHorizontalListItem {
    display: inline
}
.menuHorizontalListItemLink {
    padding: 8px 20px;
    margin: auto 0;
    border-bottom: 0;
    font-size: 14px;
    color: black
}
.menuHorizontalListItemLink:visited {
    color: black
}
.menuHorizontalListItemLink:hover {
    border-bottom: 3px #0078e7 solid;
    background-color: #EEF
}
.menuHorizontalListItemLink.active {
    border-bottom: 3px #0078e7 solid;
    background-color: #EEF
}
.imageUploadBox {
    width: auto;
    height: 100%;
    text-align: center;
    font-size: small
}
.uploadBox {
    width: 100%
}
.uploadBox.videoContainer {
    margin: 0 auto;
    border: 5px #333 solid;
    width: 95%;
    height: 94%;
    display: none
}
.uploadBox.drop_zone {
    border-style: dashed;
    border-color: #bbb;
    border-radius: 15px;
    border-width: 2px;
    margin: auto;
    width: 98%;
    height: 95%
}
.drop_zone.center {
    padding-top: 20%
}
.verticallyCentered {
    vertical-align: middle;
    display: inline-block
}
.background_message {
    font-size: x-large;
    color: #bbb
}
.background_message.or {
    font-size: large;
    display: block;
    padding: 2em 0
}
.background_message.centered{
    position: absolute;
    /*top: calc(50% + 150px);
    left: calc(50% - 126.5px / 2);*/
}
.upload_img_btn {
    overflow: hidden;
    position: relative;
    vertical-align: bottom!important
}
.upload_img_btn [type=file] {
    cursor: pointer;
    display: block;
    min-height: 100%;
    min-width: 100%;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0
}
.imageUploaded {
    display: none;
    max-width: 100%;
    max-height: 100%;
    width: 98%;
    height: 95%;
    margin: auto
}
.capFotoButton {
    margin: 0 5px
}
.capFotoButton.icon {
    margin: 0 5px 0 0
}
.capFotoButton.hiddenBtn,
.jcrop-tracker.noDrag,
.jcrop-handle.disabled,
.jcrop-dragbar.disabled,
.uploadBox.imageDiv {
    display: none
}
.loadingWebcam {
    display: none;
    font-size: 150px;
    position: absolute;
    color: #094771;
    animation: pulse infinite 1500ms;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
}
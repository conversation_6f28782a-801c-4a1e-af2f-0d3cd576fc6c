table.X3 td { padding: 8px; font-weight: bold; color: #365AB5; }
table.X3 td.firstTop { background-color: #FFFFFF; border-bottom: 1px solid #C2C2C2; }
table.X3 td.otherTop { background-color: #FFFFFF; border: 3px solid #C2C2C2; border-right: 0px; border-bottom: 1px solid #C2C2C2; }
table.X3 td.lastTop { background-color: #FFFFFF; border: 3px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
table.X3 td.leftOdd { background-color: #F2F2F2; border-left: 1px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
table.X3 td.leftEven { background-color: #DFDFDF; border-left: 1px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
table.X3 td.innerGreen { cursor: pointer; font-family: <PERSON><PERSON>,Verdana,sans-serif; font-size: 11px; background-color: #E7F3E7; border-left: 3px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
table.X3 td.innerGreenLast { cursor: pointer; font-family: Arial,Verdana,sans-serif; font-size: 11px; background-color: #E7F3E7; border-left: 3px solid #C2C2C2; border-right: 3px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
table.X3 td.innerYellow { cursor: pointer; font-family: Arial,Verdana,sans-serif; font-size: 11px; background-color: #F3E7B6; border-left: 3px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
table.X3 td.innerYellowLast { cursor: pointer; font-family: Arial,Verdana,sans-serif; font-size: 11px; background-color: #F3E7B6; border-left: 3px solid #C2C2C2; border-right: 3px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
table.X3 td.innerRed { cursor: pointer; font-family: Arial,Verdana,sans-serif; font-size: 11px; background-color: #F3CEDB; border-left: 3px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
table.X3 td.innerRedLast { cursor: pointer; font-family: Arial,Verdana,sans-serif; font-size: 11px; background-color: #F3CEDB; border-left: 3px solid #C2C2C2; border-right: 3px solid #C2C2C2; border-bottom: 1px solid #C2C2C2; }
a.obrigatorio:link, a.obrigatorio:visited, a.obrigatorio:hover, a.obrigatorio:active { color: #FF0000; text-decoration: none; cursor: default; }
.mensagemObrigatorio { color: #FF0000; font-family: 'Trebuchet MS',verdana; font-size: 8pt; }
.mensagemAviso { color: #FF0000; font-family: 'Trebuchet MS',verdana; font-size: 8pt; }
.mensagemAvisoPequena { color: #FF0000; font-family: 'Trebuchet MS',verdana; font-size: 6pt !important; }
.historico_texto {font-family: Tahoma, Geneva, sans-serif; font-size: 11px; line-height: 23px; color: #666;}
.areaBotoes { margin-top: 10px; width: 100%; height: 50px; background: #DFDFDF; }
div.legenda { margin-bottom: 10px; }
div.legenda-simbolo { margin-right: 10px; width: 20px; height: 20px; float: left; }
div.legenda-descricao { vertical-align: middle; font-weight: bold; color: #365AB5; font-family: Arial,Verdana,sans-serif; font-size: 15px; }
.tituloLegenda {font: 8pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif; color: #000000; text-align: right;}
td.classForm { width: 50%; vertical-align: top; }
#dhtmltooltip {
    position: absolute;
    left: -300px;
    width: 200px;
    border: 1px solid black;
    padding: 2px;
    background-color: #FCFEE4;
    color: #006684;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size:8pt;
    visibility: hidden;
    z-index: 100;
    /*filter: progid:DXImageTransform.Microsoft.Shadow(color=gray,direction=135);*/
}

#dhtmlpointer {
    position: absolute;
    left: -300px;
    z-index: 101;
    visibility: hidden;
}

.cor_forte {
    color: #CC0000;font-weight:bold;
}
.cor_normal {
    color:#008000;
}
.cor_suave {
    color:#FF6600;
    font-weight:bold;
}
.colunaRight {  
    text-align: right;  
}
.colunaCenter {  
    text-align: center;  
} 
  
.colunaLeft {  
    text-align: left;  
}
td.classEsquerdaCE {
    width: 40%;
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
}
td.classEsquerdaCinzaCE {
    width: 40%;
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    padding:6px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:25%;
    background-color: #CCCCCC;
}
td.classDireitaCE{
    width: 60%;
    text-align: left;
}
.textoAzul {
    font: 9pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #006684;
    font-weight: bold;
    text-align: right;
}
.textoVermelhoGrande {
    font-size: 18px;
    color: #DF0000; 
    font-weight: bold;
}
.textoVermelho {
    font: 9pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #DF0000; 
    font-weight: bold;
}
.tituloCamposLeft {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: left;
}
.tableParcelasNegociacao {
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    text-decoration:none;
    color:#fff;
    font-weight:bold;
    /*border:solid 1px #e6e6e6;*/
}

.tableParcelasNegociacao td {
    /*background-image:url(images/aba_top_middle.png);*/
    background-color:#c4d6de;
    padding:12px;
    border-bottom:none;
}
.tituloAzul{
    font-family:Arial, Helvetica, sans-serif;
    font-size:16px;
    font-weight:bold;
    text-decoration:none;
    color:#0f4c6b;
}
.totalNegrito{
    font-family:Arial, Helvetica, sans-serif;
    font-size:14px;
    font-weight:bold;
    text-decoration:none;
    color:#000000;
}

.sobraNegativa{ font-weight:bold; color: #FF0000; font-family: 'Trebuchet MS',verdana; font-size: 14pt; }

.sobraPositiva{ font-weight:bold; color: #000080; font-family: 'Trebuchet MS',verdana; font-size: 14pt; }

.mensagemOrcamento {
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    text-decoration:none;
    color:#fff;
    font-weight:bold;
    /*border:solid 1px #e6e6e6;*/
}
.mensagemOrcamento td {
    /*background-image:url(images/aba_top_middle.png);*/
    background-color:#c4d6de;
    padding:12px;
   
}
.mensagemOrcamento2 {
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    text-decoration:none;
    color:#fff;
    font-weight:bold;
    /*border:solid 1px #e6e6e6;*/
}
.mensagemOrcamento2 td {
    /*background-image:url(images/aba_top_middle.png);*/
    background-color:#c4d6de;
    padding:0px;  
}

tr.linhaParCE {
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    vertical-align: middle;
}
tr.linhaImparCE {
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    background-color: #EEEEEE;
    vertical-align: middle;
}
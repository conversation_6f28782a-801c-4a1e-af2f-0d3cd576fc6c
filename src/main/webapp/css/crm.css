
.metaSelProximaFila {
    background-color: #004671;
    width: 100%;
}

.colunaEsquerdaCRM {
    text-align: right;
    background-color: white;
    padding: 5px;
    width: 8%;
}

.colunaEsquerdaCRMSel {
    text-align: right;
    background-color: #004671;
    padding: 5px;
    width: 8%;
}

.colunaCentroCRM {
    text-align: center;
    background-color: white;
    padding-top: 5px;
    padding-bottom: 5px;
    width: 14%;
}

.colunaCentroCRMSel {
    text-align: center;
    background-color: #004671;
    padding-top: 5px;
    padding-bottom: 5px;
    width: 14%;
}

.colunaDireitaCRM {
    text-align: left;
    background-color: white;
    height: 32px;
    padding-left: 5px !important;
    padding-top: 5px;
    padding-right: 0px;
    padding-bottom: 5px;
}

.colunaDireitaCRMSel {
    text-align: left;
    background-color: #004671;
    vertical-align: middle;
    height: 32px;
    padding-left: 5px !important;
    padding-top: 5px;
    padding-right: 0px;
    padding-bottom: 5px;
}

.corBranca {
    color: white;
}

.cor<PERSON>erde {
    color: green;
}

.corDarkRed {
    color: darkred !important;
}

.corCinza {
    color: silver !important;
}

.corPretaSelecionado {
    color: #333333;
    font-weight: bold;
}

.corPretaNaoRealizado {
    color: #333333;
    font-weight: bold;
}

.corPretaRealizado {
    color: #333333;
    font-weight: normal;
}

.rich-stglpanel-body {
    padding: 0;
    border-top: 1px solid;
    border-color: #ACBECE;
}

.metaSelAleatoria {
    background: #7FFFA4 none;
    font-weight: normal;
}

.metaAtendida {
    background: #DCDCDC none;
    font-weight: normal;
}

.metaNaoAtendida {
    background: white none;
    font-weight: bold;
}

.tableCRM {
    height: 100%;
    border: none !important;
}

.metaVisitante {
    background-image: url('../imagensCRM/vinteQuatroHoras.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaAgendamentoHoje {
    background-image: url('../imagensCRM/agendamentos_hoje.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaAgendamentoAmanha {
    background-image: url('../imagensCRM/ligacao_amanha.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaRenovacao {
    background-image: url('../imagensCRM/renovacoes.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaDesistentes {
    background-image: url('../imagensCRM/perdas.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaVisitanteAntigo {
    background-image: url('../imagensCRM/visitantes_antigos.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaExAluno {
    background-image: url('../imagensCRM/ex-alunos.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaIndicacoes {
    background-image: url('../imagensCRM/indicacoes.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaGrupoRisco {
    background-image: url('../imagensCRM/grupo_de_risco.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaVencidos {
    background-image: url('../imagensCRM/vencidos.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaPosVenda {
    background-image: url('../imagensCRM/pos_vendas.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaFaltosos {
    background-image: url('../imagensCRM/faltantes.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.metaAniversariante {
    background-image: url('../imagensCRM/aniversariantes.png') !important;
    background-repeat: no-repeat;
    background-size: contain;
    color: #333333;
}

.tblClientesNaFase > tbody > tr > td.rich-table-cell {
    padding: 0;
}

.fotosClientesCRM {
    background: white;
    border-radius: 100%;
    width: 70%;
    height: 80%;
}

.triangleCSS {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 10px 13px 10px;
    border-color: transparent transparent #e6e6e6 transparent;
    background: white;
}

.listaConsultorCRM .rich-menu-group-hover {
    border-color: transparent;
    background-color: #EEEEEE;
    background-image: none;
}

.listaConsultorCRM .rich-menu-list-border {
    border: #89888c !important;
    background-color: transparent;
}

.listaConsultorCRM .rich-menu-list-bg {
    background-image: none !important;
    border-color: #89888c !important;
    border-bottom: 1px solid;
    padding: 0px;
    background-color: white;
}

.listaConsultorCRM .rich-menu-item-hover {
    border-color: transparent;
    background-color: #EEEEEE;
    background-image: none;
}

.listaConsultorCRM .rich-menu-item {
    border-color: transparent;
    font-size: 12px;
    font-weight: bold;
    padding-left: 0px !important;
}

.listaConsultorCRM .rich-menu-item-icon img {
    width: 5px;
}
.listaConsultorCRM .rich-menu-group-icon img {
    width: 5px;
}

.listaConsultorCRM .rich-label-text-decor {
    font-weight: 400 !important;
}

.listaConsultorCRM .rich-ddmenu-label {
    height: 23px;
    line-height: 23px;
    vertical-align: middle;
    font-family: sans-serif;
    font-weight: 400 !important;
    text-transform: none !important;
    font-size: 11px;
    color: rgb(15, 76, 107);
    display: block;

    border: 1px solid #759aab;
    border-radius: 3px;
    padding: 0 8px !important;

    background-image: linear-gradient(bottom, rgb(218, 226, 230) 33%, rgb(254, 254, 254) 90%);
    background-image: -o-linear-gradient(bottom, rgb(218, 226, 230) 33%, rgb(254, 254, 254) 90%);
    background-image: -moz-linear-gradient(bottom, rgb(218, 226, 230) 33%, rgb(254, 254, 254) 90%);
    background-image: -webkit-linear-gradient(bottom, rgb(218, 226, 230) 33%, rgb(254, 254, 254) 90%);
    background-image: -ms-linear-gradient(bottom, rgb(218, 226, 230) 33%, rgb(254, 254, 254) 90%);

    background-image: -webkit-gradient(
            linear,
            left bottom,
            left top,
            color-stop(0.33, rgb(218, 226, 230)),
            color-stop(0.9, rgb(254, 254, 254))
    );

}

.itemSelecionado {
    border-color: transparent;
    background-color: #EEEEEE;
    background-image: none;
}

.itemNaoSelecionado {
    border-color: transparent;
    font-size: 12px;
    font-weight: bold;
}

.tituloBICRM {
    font-family: Arial;
    text-decoration: none;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
}

.camposTotalizadorSuperiorBICRM {
    text-align: right;
    font-family: Arial;
    font-weight: bold;
    font-size: 15px;
    color: #777777;
}

.camposTotalizadorBICRM {
    font-family: Arial;
    color: #777 !important;
    font-weight: bold;
    text-align: right;
    font-size: 16px;
}

.camposTotalizadorBICRM:hover {
    color: #29ABE2 !important;
    text-decoration: none;
}

.camposBICRMMetas {
    font-family: Arial;
    text-align: right;
    color: #777 !important;
    font-size: 14px;
}

.camposBICRMMetas:hover {
    color: #29ABE2 !important;
    text-decoration: none;
}

.indicadoresBICRM {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 40px;
    text-align: center;
    color: #29ABE2;
}

.indicadoresBICRM:hover {
    color: #29ABE2;
    text-decoration: none;
}

.camposTotalizadorBICRMSemHover {
    font-family: Arial;
    font-size: 16px;
    color: #777;
    font-weight: bold;
    text-align: right;
}

.camposBICRMMetasSemHover {
    text-align: right;
    font-family: Arial;
    text-decoration: none;
    font-size: 14px;
    color: #777;
}

.metaSelecionada {
    background: #004671;
    color: white;
    font-size: 14px;
    /*font-weight: bold;*/
    padding: 4px;
}

.metaNaoSelecionada {
    background: #EEEEEE;
    color: #333333;
    font-size: 14px;
    padding: 4px;
}

/*----------------------------------------------------BI'S CRM----------------------------------------------------------------------*/
.bi-crm-box {
    background-color: #c4d6de;
    border-radius: 10px;
    padding-bottom: 10px;
}

.bi-crm-box-header {
    height: 40px;
    line-height: 50px;
    text-align: right;
    margin-left: 15px;
    margin-right: 15px;
}

.bi-crm-box-header .fa-icon-external-link-square{
    color : #32616b;
}

.bi-crm-box-detail {
    width: 100%;
    background-color: #fff;
    padding-bottom: 15px;
    height: 350px;
}

.bi-crm-box-footer {

}

.linhasBICRM {
    border-top: 0.5px solid #dde7e7;
    border-bottom: 0.5px solid #dde7e7;
}

.classLinhasCRM {
    text-align: right;
    border: none;
    width: 100%;
    height: 340px;
}

.marginLeftCRMBI {
    margin-left: 15px;
}

.marginRightCRMBI {
    margin-right: 15px;
}

.footerTableCRMBI {
    padding-right: 15px;
}

.linhasBICRM > td {
    text-align: right;
}

.camposTotalizadorPorcentaemBICRM {
    font-family: Arial;
    font-size: 16px;
    color: #777;
    font-weight: bold;
}
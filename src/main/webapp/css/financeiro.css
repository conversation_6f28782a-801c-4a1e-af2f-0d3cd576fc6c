.special tr[id]{
		display:none; 
	}
	
	.Passo .Texto {
	color:#FFFFFF;
	font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
  	float:left;
  	padding-left:8px;
  	padding-top:19px;
  	text-align:left;
}
.Passo .Imagem {
  float:left;
  height:38px;
  overflow-x:hidden;
  overflow-y:hidden;
}
.Passo .Info {
  color:#C0C0C0;
  padding-right:3px;
  padding-top:25px;
  text-transform:uppercase;
  font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform:uppercase;
    font-weight: bold;

}
.Passo {
  background-position:25px 0;
  background-repeat:no-repeat no-repeat;
  border-bottom-color:#D3D3D3;
  border-bottom-style:solid;
  border-bottom-width:1px;
  color:#FFFFFF;
  cursor:pointer;
  font-weight:bold;
  height:38px;
  text-align:right;
  
}
td.classEsquerdaFinan {
    width: 55%;
    text-align: right;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
}
td.classDireitaFinan{
    width: 45%;
    text-align: left;
}
tr.linhaParFinan {
	background-color: #FFFFFF;
}
tr.linhaImparFinan {
    background-color: #F5F5F5;
    
}
td.colunaFinan {
	border-bottom: none;
    border-right: none;
    border-left:  none;
    border-top: none;
}
.tituloAmarelo {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: bold;
    text-transform: none;
    color: #3CB371;
}
.topoPequeno {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 10px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #474747;
}
td.direitaLancamento {
 	width: 100%; 
 	vertical-align: top;
}
td.centroLancamento {
 	width: 3%;
 	vertical-align: top; 
}
td.esquerdaLancamento {
	width: 35%;
	vertical-align: top;
}
.colunaBIMetas{
	padding: 6px;
	vertical-align: top;
}
.tituloDemonstrativo {
    font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
    font-size: 13px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #000000;
}
.headerDetalhamentoCumprimentoMetas{
	background-color: #EDF1F4;
	border: 1px solid silver;
    color: #474747;
    font-family: Arial,Verdana,sans-serif;
    font-size: 11px;
    padding: 4px;
    text-align: center;
}

.linhaSelecionada {
    font-size: 8pt;
    background-color: #CECECE;
    font-family: 'Trebuchet MS', verdana;
}
.linkLegenda{
    color: rgb(0,0,0) !important;
    font-size: 12px;
}
.tipoRelatorioDF{
    width: 100%;
    -webkit-transition: 1s all;
    -moz-transition: 1s all;
    -ms-transition: 1s all;
    -o-transition: 1s all;
    transition: 1s all;
    cursor: pointer;
    border-top:none;
    border-bottom: 1px solid #9E9E9E;
}
.tipoRelatorioDF:first-child{
    border-top:1px solid #9E9E9E;
}
.tipoRelatorioDF:hover{
    background-color: rgba(0,0,0,.1);
    border-color: #9E9E9E;
    border-radius: 3px;
}

.tipoRelatorioDF .maginTipoRel{
    padding: 10px;
}

.panelLikeSelectMenu{
    width: 100%;
    -webkit-transition: 1s all;
    -moz-transition: 1s all;
    -ms-transition: 1s all;
    -o-transition: 1s all;
    transition: 1s all;
    cursor: pointer;
    border: 1px solid #9E9E9E;
    background-color: white;
}

.panelLikeSelectMenu:hover{
    background-color: rgba(0,0,0,.1);
    border-color: #9E9E9E;
    border-radius: 3px;
}

.panelLikeSelectMenu .maginTipoRel{
    padding: 5px;
}

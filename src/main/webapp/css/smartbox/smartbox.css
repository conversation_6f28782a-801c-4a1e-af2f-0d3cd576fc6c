/* 
    Document   : smartbox
    Created on : 09/01/2012, 15:10:19
    Author     : Waller
    Description:
        Purpose of the stylesheet follows.
*/

/* 
   TODO customize this sample style
   Syntax recommendation http://www.w3.org/TR/REC-CSS2/
*/
.tituloSmart {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #0f4c6b;
}
.cantos_tl, .cantos_t, .cantos_tr, .cantos_l, .cantos_r, .cantos_bl, .cantos_b, .cantos_br, .cantos {
    width: auto;
    margin: 0;
    padding: 0;
    border: 0;
    position: relative;
}
.cantos_t {    
    background-color: #ffffff;
    background-image: url("top.gif");
    background-repeat: repeat-x;
    background-position: top;
}
.cantos_tl {
    height: 100%;
    background-image: url("top_left.gif");
    background-repeat: no-repeat;
    background-position: left top;
}
.cantos_tr {
    height: 100%;
    background-image: url("top_right.gif");
    background-repeat: no-repeat;
    background-position: right top;
}
.cantos_bl {
    height: 100%;
    background-image: url("bottom_left.gif");
    background-repeat: no-repeat;
    background-position: left bottom;
}
.cantos_br {
    height: 100%;
    background-image: url("bottom_right.gif");
    background-repeat: no-repeat;
    background-position: right bottom;
}
.cantos {
    height: 100%;    
    padding: 0px 0px 0px 0px;
}

.cantos_l {
    height: 100%;
    background-image: url("left.gif");
    background-repeat: repeat-y;
    background-position: left;
}
.cantos_r {
    height: 100%;
    background-image: url("right.gif");
    background-repeat: repeat-y;
    background-position: right;
}
.cantos_b {
    height: 100%;
    background-image: url("bottom.gif");
    background-repeat: repeat-x;
    background-position: bottom;
}

.menulateral_retraido{
    cursor: pointer;
    left: 0px;
    top: 110px;
    width: 24px;
    height: 24px;
    position: absolute;
    margin: 2px 2px 2px 2px;
    /*z-index: 1001;*/
    background-image: url("botao_minimizar.png");    
    background-position: top left;
    background-color: transparent;
    background-repeat: no-repeat;
}

.mostra{
    display: block;
}

.esconde{
    display: none;
}

.menulateral_restaurado{
    cursor: pointer;
    left: 0px;
    top: 110px;
    width: 24px;
    height: 24px;
    position: absolute;
    margin: 2px 2px 2px 2px;
    /*z-index: 1001;*/
    background-image: url("botao_maximizar.png");
    background-position: top left;
    background-color: transparent;
    background-repeat: no-repeat;
}

.menulateral_retraido_btn{
    cursor: pointer;
    left: 0px;
    top: 110px;
    width: 24px;
    height: 24px;
    position: absolute;
    margin: 2px 2px 2px 2px;
    background-image: url("botao_minimizar.png");
    background-position: top left;
    background-color: transparent;
    background-repeat: no-repeat;
}

.menulateral_restaurado_btn{
    cursor: pointer;
    left: 0px;
    top: 110px;
    width: 24px;
    height: 24px;
    position: absolute;
    margin: 2px 2px 2px 2px;
    background-image: url("botao_maximizar.png");
    background-position: top left;
    background-color: transparent;
    background-repeat: no-repeat;
}

.dr-table{
	border-left: none;
    border-top: none;
}
.dr-table-cell {
    border-bottom: none;
    border-right: none;
    text-align: center;
}
.dr-table-thead {
    border-bottom: none;
}
.rich-table-subheadercell{
 	padding: 2px;
}
	
.linkListaClientes {
	font-family:Arial, Helvetica, sans-serif;
	font-weight: normal;
	font-size: 11 px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #474747;
    line-height:150%;
}
.listaClientesHeader{
	background-image: url(images/back_header_smartbox.png);
	background-repeat: repeat-x;
	width: 20%;
}
.tituloDetalhamentoCliente {
    font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
    font-size: 13px;
    text-decoration: none;
    font-weight: bold;
    line-height: normal;
    text-transform: none;
    color: #000000;
}
.labelDetalhamentoCliente {
    font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #474747;
}
.valoresDetalhamentoCliente {
    font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
    font-size: 11px;
    font-weight: bold;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #0078D0;
}

.tabelaFichaCliente{
	text-align: center;
	border:1px solid silver;
}
.legenda {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 10px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #474747;
}
.loading {
    position: relative;
    background-color: #d2d2d2;
}
.loading.card-image {
    border-radius: 0;
}
.loading::after {
    display: block;
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    -webkit-animation: loading 1s infinite;
    animation: loading 0.8s infinite;
}

@-webkit-keyframes loading {
    100% {
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
    }
}

@keyframes loading {
    100% {
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
    }
}
.card-title.loading {
    height: 1.3rem;
}
.ghost{
    overflow: hidden;
    margin: 10px;
    margin-top: 30px;
}
.ghost table{
    width: 100%;
}

.ghost table tr td{
    padding: 0px 10px;
}
.ghost table tr td:first-child{
    width: 80%;
}
.bi-unloaded{
    min-height: 200px;
    height: auto !important;
}
.loading.linha{
    width: 100%;
    height: 20px;
    margin-top: 30px;
}

.loading.bloco{
    display: inline-block;
    width: 30%;
    height: 130px;
    margin-bottom: 20px;
    margin-right: 4%;
}
.badgeItem3Icon{
    font-family: Arial !important;
}
.container-bi{
    display: block !important;
}
.painelFeedback{

}
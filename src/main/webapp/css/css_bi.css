@import "css_bi_novo.css";

.bi-link,.bi-link:hover,.bi-link:visited{
     color: #29ABE2;
}
.btn-atualizar-bi{
    display: none;
}
.opacity-1{
    opacity: 1 !important;
}
.bi-panel{
    width: 91%;
    display: inline-block;
    height: 3em;
    margin-left: 4.5%;
    line-height: 3.2em;
}
.container-row{
    width: 91%;
    margin-left: 4.5%;
}
.bi-font-family{
    font-family: Arial;
}
.bi-container-bi-block{
    position: absolute;
    top:0;
    height: calc(100%);
    width: 100%;
    background-color: rgba(119, 119, 119, 0.4);
    z-index: 20;
}
.bi-container-bis{
    position: relative;

}
.container-bis{
    transition: all .4s ease-out;
    -moz-transition: all .4s ease-out;
    -o-transition: all .4s ease-out;
    -webkit-transition: all .4s ease-out;
}
.ui-sortable-placeholder {
    position: relative;
    border: 3px solid #29ABE2;
    z-index: 1 !important;
    display: block !important;
    width: 400px;
    min-height: 300px;
    transition: all .3s ease-out;
    -moz-transition: all .3s ease-out;
    -o-transition: all .3s ease-out;
    -webkit-transition: all .3s ease-out;
    visibility: visible !important;
    background-color: transparent !important;


}
.ui-sortable-helper{
    -webkit-box-shadow: 0px 2px 43px 11px  #29ABE2 !important;
    -moz-box-shadow: 0px 2px 43px 11px  #29ABE2 !important;
    box-shadow:  0px 2px 43px 11px  #29ABE2 !important;
}

.btn-calendar{
    vertical-align: text-bottom !important;
    width: 15.6px;
    height: 16px
}
.btn-data-base{
    cursor: pointer;
}
.bi-separador{
    margin: 10px 0px 10px 0px;
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #E5E5E5;
}

.bi-text-small{
    font-size: .8em;
    font-family: Arial;
    color: #777777;
}
.bi-cor-cinza{
    color: #777777;
}
.bi-cor-azul,.bi-cor-azul:visited,.bi-cor-azul:hover{
    color: #29ABE2;
}
.bi-cor-verde,.bi-cor-verde:hover,.bi-cor-verde:active{
    color : #00C350 !important;
}
.bi-font-bold{
    font-weight: bold;
}
.container-bi-50{
    z-index: 10;
    position: relative;
    transform: scale(0.7);
    -moz-transform: scale(0.7);
    -webkit-transform: scale(0.7);
    -o-transform: scale(0.7);
    -webkit-transform: scale(0.7);
     transition: transform .1s ease-out;
    -moz-transition: -moz-transform .1s ease-out;
    -o-transition: -o-transform .1s ease-out;
    -webkit-transition: -webkit-transform .1s ease-out;
}
.container-bi-corner{
    display: block;
    background-color: transparent;
    border: 1px solid blue;
}
.bi-container-transition{
    transition: all .3s ease-out;
    -moz-transition: all .3s ease-out;
    -o-transition: all .3s ease-out;
    -webkit-transition: all .3s ease-out;
}
.fundo-block-container.open{
    visibility: visible;
    -webkit-transition: all 1s;
    -moz-transition: all 1s;
    -ms-transition: all 1s;
    -o-transition: all 1s;
    transition: all 1s;
}
.fundo-block-container{
    width: 100%;
    visibility: hidden;
    position: absolute;
    z-index: 10;
    left: -100%;
    top: 0;
    -webkit-transition: all 1s;
    -moz-transition: all 1s;
    -ms-transition: all 1s;
    -o-transition: all 1s;
    transition: all 1s;
}
.bi-fundo-block{
    width: 100%;
    height: 3607px;
    position: absolute;
    z-index: 24;
    left: 0;
    top: 0;
    background-image: url('http://wallpapercomputer.com/wp-content/uploads/2016/02/White-Background-Iphone-Pics.png');
}

.bi-icon-sortable-handle{
    transform: scale(1);
    cursor: pointer;
    -moz-transform: scale(1);
    -webkit-transform: scale(1);
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    z-index: 10;
    font-size: 2em;
    color: rgba(41, 171, 226,1);
    display: block;
    padding: 7px;
    background-color: #ffffff;
    float: right;
    border-radius: 10%;
    margin-right: 14px;
}
.bi-icon-sortable-handle:first-child{
    margin-right: 0;
}
.container-bi:last-child{
    margin-bottom: 4%;
}
.container-sortable-bi .col-sortable{
    width: calc(50% - 10px);
    display: inline-block;
    min-height: 600px;
    float: left;
}
.bordaEST{
    border-top: 2px solid #9B2589;
}
.container-sortable-bi .col-sortable:last-child{
    margin-left: 10px;
}
.container-sortable-bi .col-sortable:first-child{
    margin-right: 10px;
}
.container-bi{
    width: 100%;
    margin-bottom: 20px;
    height: auto;
    position: relative;
    display: none;
    -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    background-color: #ffffff;
    transition: transform .1s ease-out;
    -moz-transition: -moz-transform .1s ease-out;
    -o-transition: -o-transform .1s ease-out;
    -webkit-transition: -webkit-transform .1s ease-out;
}
.container-bi.ui-sortable-helper{
    z-index: 55 !important;
}
.bi-titulo{
    font-family: Arial;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.1em;
    color: #333333;
}
.bi-btn-refresh{
    float: right;
    font-size: 1.1em;
    margin-left: 2.6% !important;
}
.bi-right-align{
    float: right;
}
.bi-calendario-align{
    float: right;
    margin-right: 4.4%;
}
.bi-left-align{
    float: left;
}
.bi-btn-header-right{
    font-size: 1.1em;
    margin-right: 2%;
}
.bi-block-sortable{
    left: 0 !important;
    right: 0 !important;
    position: inherit !important;
}
.bi-header{
    height: 3em;
    width: 100%;
    margin-left: 0;
    display: inline-block;
    line-height: 3em;
    border-bottom: 1px solid #E5E5E5;
}
.bi-header i,.bi-header span,.bi-header a{
    font-size: 1.1em;
    margin-top: 0 !important;
}
.bi-header i{
    line-height: 3em;
}
.bi-header .linkWiki{
    line-height: 4em;
    font-size: .7em;
}
.bi-header .bi-right-align{
    margin-top: 4%;
}
.bi-panel-margin-bottom{
    width: 100%;
    display: block;
    height: 8px;
}
.bi-row{
    display: block;
    height: 2.5em;
    line-height: 2.5em;
}
.bi-row-center{
    width: 96%;
    margin-left: 2%;
}
.bi-row-center span:first-child,.bi-row-center a:first-child{
    margin-left: 3.2%;
    margin-right: 0;
    float: left;
}
.bi-row-center span,.bi-row-center a{
   margin-right: 2.6%;
   float: right;
}
/*--------------------------------Aqui come�a a lista de pendencia BI*------------------------------------*/
.bi-table-text,.bi-table-text:visited{
    font-family: Arial;
    color: #777777;
}
.bi-table-row{
    width:100%;
    height: 38px;
    border-bottom: 1px solid #E5E5E5;
    border-left: 0;
    border-right: 0;
    border-top: 0;
}
.bi-table-row > td > span{
    margin-left: 6%;
}

/*------------------------------Aqui Come�a o caixa de vinculo de consultores do B.I.----------------------------*/
.vinculo-table-custom .rich-table{
     empty-cells: hide !important;
     border-collapse: inherit !important;
}

.vinculo-table-custom .rich-table-header{
    background-color: #E5E5E5;
}
.vinculo-table-custom .rich-table-headercell{
     border-right: none;
     border-bottom: none;
     height: 38px;
}
.vinculo-table-custom .rich-table-thead{
    border-bottom: none;
}
.vinculo-header-text{
    font-size: 1em;
    font-weight: normal;
    text-decoration: none;
    font-family: Arial;
    vertical-align: text-bottom;
}
.vinculo-header-text .filter-conteiner{
    margin-right: 4%;
    margin-top: 3%;
}
.filter-text{
    padding-left: 5px;
    margin-right: 4%;
    font-size: 1em;
    font-weight: normal;
    text-decoration: none;
}
 /*-----------------------------------------B.I. Grupo de Risco*-----------------------------------------------------*/

.gr-container-resultado{
    height: 9em;
    width: 100%;
    background-color: #F4C9D1;
    border-top: 1px solid #FF5555;
    border-bottom: 1px solid #FF5555;
    text-align: left;
}
.bi-cor-vermelho{
    color: #FF5555;
    background-color: #F4C9D1;
}
.gr-text-warning,.gr-text-warning:visited{
    color: #FF5555;
    font-size: 1.1em;
    text-decoration: none;
}
.group-header{
    margin-left: 3.6%;
    margin-top : 3.6%;
    margin-bottom : 2.6%;
    text-transform: uppercase;
}
.gr-resultado{
    margin-left: 5%;
    height: 3em;
    line-height: 3em;
    display: block;
}
.gr-container-totalizador{
    width: 100%;
    border-bottom: 1px solid #E5E5E5;
    padding-bottom: 5px;
    padding-top: 15px;
    display: inline-block;
}
.gr-totalizador{
    width: calc(100% / 3 - 1px);
    text-align: center;
    display: inline-block;
    vertical-align: middle;
    height: 90px;
    border-left: 1px solid #E5E5E5;
}
.gr-totalizador-item-size-14{
    height: 2.8em ;
    text-align: center;
    display: block;
    width: 100%;
}
.gr-totalizador-item-size-14-middle{
    height: 2.8em ;
    line-height: 2.8em;
    text-align: center;
    display: block;
    width: 100%;
}
.gr-totalizador-item-size-16{
    height: 2.8em ;
    display: block;
    font-size: 1.3vw;
    width: 100%;
    line-height: 1.8em;
}
.gr-totalizador-item-size-40{
    height: 4em ;
    display: block;
    font-size: 1vw;
    width: 100%;
    line-height: 4em;
}
.gr-totalizador > span {
    padding-top: 7px;
    padding-bottom: 10px;
}
.gr-totalizador:first-child{
    border-left: none;
}
.gr-totalizador-text,.gr-totalizador-text:hover,.gr-totalizador-text:visited{
    font-size: 3em;;
    color: #29ABE2;
    text-decoration: none;
}
/*------------------------------------------------------------------Inicio Calendario BI------------------------------------*/
.bi-calendar .rich-calendar-button{
   height: 1em;
}
.bi-calendar .rich-calendar-exterior{
    left: auto !important;
    right: 0px;
    background-color: #ffffff;
}

.bi-calendar .rich-calendar-days,.rich-calendar-footer{
    background-color: #ffffff;
    border-color : #C3C3C3;
}
.bi-calendar .rich-calendar-select{
    background-color: rgba(41, 171, 226, 0.4);
    color: rgb(255, 255, 2555);
    background-image: none;
}
.bi-calendar .rich-calendar-holly,.bi-calendar .rich-calendar-weekends,.bi-calendar .rich-calendar-editor-btn-selected{
    color: #FF5555  !important;
    background-color: rgb(255, 255, 255);
    background-image: none;
}
.bi-calendar .rich-calendar-editor-btn-over,.bi-calendar .rich-calendar-editor-tool-over{
    border-color: #E5E5E5 !important;
    border-width: 2px !important;
    background-color: #ffffff;
}
.bi-calendar .rich-calendar-header{
    border-bottom-color: #C3C3C3;
}
.bi-calendar .rich-calendar-editor-btn{
    margin: 1px;
}
.bi-calendar .rich-calendar-tool,.bi-calendar .rich-calendar-month,
.bi-calendar .rich-calendar-toolfooter,.bi-calendar .rich-calendar-date-layout-ok ,.bi-calendar .rich-calendar-date-layout-cancel{
    background-image: none;
    background-color: #E5E5E5;
    font-size: .8em;;
    font-family: Arial;
    color: #777777;
    font-weight: normal;
}
.bi-calendar .rich-calendar-time-btn{
    width: 45px;
    border: 1px solid;
    border-radius: 3px;
    padding: 2px;
    border-color: #FFF;
    border-right-color: none;
    border-bottom-color: #none;
    background: #E5E5E5;
}
.bi-calendar .rich-calendar-tool-close{
    background-image: none;
}
.bi-calendar .rich-calendar-today{
    background-image: none;
    background-color: #ffffff;
    border: 1px solid #29ABE2 !important;
    border-radius: 15%;
}
.bi-calendar .rich-calendar-boundary-dates{
    color: rgba(119, 119, 119, 0.2) !important;
}
.bi-calendar .rich-calendar-cell,.bi-calendar .rich-calendar-editor-btn{
    border :1px solid #F5F5F5;
    border-radius: 15%;
    color: #777777;
}
.bi-calendar .rich-calendar-hover{
    background-image: none;
    border-radius: 15%;
    background-color: #ffffff;
    border:1px solid #C3C3C3;
}
.bi-calendar .rich-calendar-tool-btn{

}
.bi-calendar .rich-calendar-tool-btn-hover{
    background-image: none;
    background-color: rgba(0, 0, 0, 0.04);
    border:1px solid #ffffff ;
    border-radius: 10%;
}

/*------------------------------------------------------------------B.I. I.C.V---------------------------------------------*/
.icv-periodo-text{
    font-family: Arial;
    font-weight: bold;
    font-size: 1em;
    color: #777777;
}
.icv-container-numeros{
    display: inline-block;
    border-right: 1px solid #E5E5E5 ;
    height: 129px;
    width: 58%;
    margin-left: 4%;
}
.icv-container-numeros .icv-numeros-col{
    width: 20%;
    display: inline-block;
    height: 25%;
}
.icv-container-numeros .icv-numeros-col:nth-child(3n+3){
    width: 60%;
}
.icv-container-resultado{
    vertical-align: top;
    display: inline-block;
    text-align: center;
    min-width: calc(41% - 2px - 4%);
    margin-top: 4%;
    height: 129px;
    line-height: 64px;
}
/*------------------------------------------------------------------B.I. I.C.V Sess�o------------------------------------------------------------*/
.icvss-container-numeros{
 display: inline-block;
 text-align: center;
 margin-left: 4%;
 border-right: 1px solid #E5E5E5 ;
}
.icvss-container-numeros tbody tr:first-child td:first-child{
    width: auto;
}
.icvss-container-numeros tbody tr td:first-child{
    width: 136px;
}
.icvss-container-numeros tbody tr{
    height: 35px;
}
/*------------------------------------------------------------------BI �ndicie Renova��o----------------------------------------------------------*/
.ir-container{
    width: 100%;
}
.ir-container-numeros{
    padding-top: 10px;
    height: 35px;
}
.ir-container-numeros span{
    margin-top: 11px;
    display: inline-block;
}
.panel-grid-icv tr{
    height: 2.4em;
    width: 330px;
}
.panel-grid-icv tr > td:first-child{
    height: 2.4em;
    width: 330px;
}
/*-------------------------------------------------------------------B.I. Metas Financeiras---------------------------------------------------------*/
.bi-mf-caixa-resultado{
    height: 98px ;
    text-align: center;
    line-height: 98px;
}
.bi-caixa-legenda{
    padding: 10px;
    border: 1px solid #E5E5E5;
}
.bi-mf-caixa-resultado span{
   padding: 6px;
}
/*---------------------------------------------------------------------B.I. D.C.C--------------------------------------------------------------------------------*/
.bi-table-dcc{
    border-bottom: 1px solid #E5E5E5 ;
    height: 2.5em;
    line-height: 2.5em;
}
.bi-table-dcc > span:first-child {
    float: left;
    margin-left: 4.2%;
}
.bi-table-dcc > span {
    float: right;
    margin-right: 5.2%;
}
.bi-table-dcc > span:last-child {
    float: right;
    margin-right: 1%;
}
.bi-table-dcc > a{
    float: right;
    text-align: end;
    margin-right: 4.2%;
    width: 30px;
}
.bi-totalizador-header{
    width: 100%;
    height: 3.3em;
    background-color: #E5E5E5;
    line-height: 3.3em;
}
.bi-header-calendario{
    width: 100%;
    height: 40px;
    background-color: #E5E5E5;
    margin-bottom: 4%;
}
.bi-totalizador-table{
    border-top: 0px;
    border-left: 0px;
    margin-top: 2%;
    margin-bottom: 1%;
}
.bi-totalizador-table .rich-table-subheader{
    background-color: #ffffff;
}
.bi-totalizador-table  .rich-table-cell{
    border-left: 0;
    border-right: 0;
    border-bottom: 0;
    height: 2.5em;
}
.bi-totalizador-table .rich-table-subheadercell{
    border-right: 0;
    text-align: left;
    height: 40px;
}
.bi-totalizador-table .rich-table-subheadercell:nth-child(3){
    text-align: center;
}
.bi-totalizador-table .rich-table-subheadercell:last-child{
    text-align: right;
}
.bi-totalizador-table  .rich-table-thead{
    border-bottom : 0;
}
.bi-totalizador-table  > td > span{
    margin-left: 6.4%;
}
/*-----------------------------------------------------------------------B.I. Controle de opera��es exce��es -------------------------------------------------------*/
.bi-table-2-row{
    width:100%;
    height: 38px;
    border-bottom: 1px solid #E5E5E5;
}
.bi-table-2-row  td:first-child span,.bi-table-2-row  td:first-child a{
    margin-left: 4.4%;
}
.bi-table-2-row  td:last-child span, .bi-table-2-row  td:last-child a {
    margin-right:4.4%;
}
/*----------------------------------------------------------------------B.I. Combo Box -----------------------------------------------------------------------------*/
.cb-container:not(.texto-cor-cinza):after {
    color: #29ABE2 !important;
}
.bi-cb-container .fa-icon-caret-down{
    position: absolute;
    right: 4%;
    top: 35%;
    z-index: 1;
}
.bi-cb-container select option:hover{
    background-color: red;
}
.bi-cb-container option, .bi-cb-container select:focus, .bi-cb-container select, .bi-cb-container select:active, .bi-cb-container select:hover {
    font-family: Arial;
    font-size: 1em;
    color: #29ABE2;
}
/*----------------------------------------------------------------------B.I. SORTABLE-------------------------------------------------------------------*/
.container-sortable-excluded {
    z-index: 11;
    vertical-align: top;
    display: none;
    top: 0px;
    float: left;
    width: 540px;
    height: 500px;
}
.container-sortable-excluded .col-sortable{
    width: 100%;
    display: block;
    min-height: 500px;
}
.colunaSortable{
    margin-top: 50px;
}
@keyframes fadebi {
    from{
        opacity: 0;
        -moz-opacity: 0;
        -webkit-transition: all 5s;
        -moz-transition: all 5s;
        -ms-transition: all 5s;
        -o-transition: all 5s;
        transition: all 5s;
    }
    to{
        opacity: 1;
        -moz-opacity: 1;
        -webkit-transition: all 5s;
        -moz-transition: all 5s;
        -ms-transition: all 5s;
        -o-transition: all 5s;
        transition: all 5s;
    }

}
.container-header-meuBI.meusBIs{
    float: right;
    width: 70%;
}
.bi-block-container-header .container-header-meuBI:first-child{
    width: 22%;
    line-height: 12.5;
    text-align: right;

}
.bi-block-container-body .bi-container-bis{
    margin: 0;
    padding-top: 50px;
    text-align: center;
}
.bi-block-container-body .container-sortable-bi{

}
.bi-block-container-header .container-header-meuBI{
    width: 78%;
    line-height: 7.3;
}
.container-header-meuBI{

    display: none;
    width: 29%;
    float: left;
    height: 100%;
    -webkit-transition: all 5s;
    -moz-transition: all 5s;
    -ms-transition: all 5s;
    -o-transition: all 5s;
    transition: all 5s;
    animation: fadebi 1.2s 1;
}
.colunaSortable  .container-sortable-excluded{
    display: block;
}
.container-sortable-excluded .ui-sortable-placeholder{
    height: 120px !important;
    width: 100% !important;
    margin-left: 0px !important;
    border: 1px solid #BF2A12;
    margin-bottom: 40px;
    -webkit-box-shadow: 0px 0px 20px 2px rgba(191, 5, 11, 0.49) !important;
    -webkit-box-shadow: 0px 0px 20px 2px rgba(191, 5, 11, 0.49) !important;
}
.container-sortable-excluded .ui-sortable-placeholder .fa-icon-eye-open{
    left: 150px;
    margin-left: 0 !important;
    padding: 0;
}
.ui-sortable-placeholder .fa-icon-eye-open{

    position: absolute !important;
    display: inline;
    left: calc(50% - 50px);
    top: calc(50% - 50px) ;
    color: rgba(51, 51, 51, 0.4);
    font-size: 100px;
}
.bi-container-item-lixeira > div{
    width: 100%;
}
.bi-container-item-lixeira{
    width: 100%;
    height: 120px;
    position: relative;
    display: table;
    line-height: center;
    background-color: #ffffff;
    margin-bottom: 40px;
    -webkit-box-shadow:  1px 2px 23px 0px rgba(0, 0, 0, 0.4) !important;
    box-shadow: 1px 2px 23px 0px rgba(0, 0, 0, 0.4) !important;
    -moz-box-shadow: 1px 2px 23px 0px rgba(0, 0, 0, 0.4) !important;
}
.bi-container-item-lixeira  .lixeira-text{
    text-align: left !important;
    font-family: Arial;
    width: 100%;
    font-size: 21px;
    float: left;
    margin-left: 15px;
    color: #333333;
    font-weight: bold;
    text-align: center;
}
.bi-container-item-lixeira > i{
    font-size: 32px;
    color: #9E9E9E;
    float: right;
    margin-right: 20px;
    margin-top: 47px;
    text-align: right;
}

.bi-container-item-lixeira .bi-container-bi-block i {
    left: calc(50% - 50px) !important;
}
.bi-container-bi-block i {
    font-size: 150px;
    position: absolute;
    left: calc(50% - 75px);
    top:calc(50% - 75px);
    color: rgba(255, 255, 255, 0.7);
}
.bi-container-item-lixeira .bi-container-bi-block{
    position: absolute;
    margin-top: 0px;
    top:0 !important;
    left: 0;
    height: 100% !important;
    width: 100% !important;
    background-color: rgba(119, 119, 119, 0.4);
    z-index: 20;
}
.bi-container-item-lixeira .fa-icon-move{
    font-size: 110px !important;
    left: initial !important;
    float: right;
    right: 205px !important;
    top:calc(50% - 56px) !important;;
}
.container-sortable-excluded .ui-sortable-placeholder .fa-icon-eye-open:before{
    content: "\f070";
}
/*-----------------------------------------------------------------------------FILTRO COLABORADORES-----------------------------------------------------------------*/

.container-colaboradores{
    position: absolute;
    right: -440px;
    display: none;
    float: right;
    top: 0px;
    z-index: 25;
    text-align: center;
    width: 426px;
    background-color: #ffffff;
    -webkit-box-shadow:  1px 2px 23px 0px rgba(0, 0, 0, 0.4) !important;
    box-shadow: 1px 2px 23px 0px rgba(0, 0, 0, 0.4) !important;
    -moz-box-shadow: 1px 2px 23px 0px rgba(0, 0, 0, 0.4) !important;
}
.btn-colaboradores-fechar{

    float: right;
    margin-right: 0px;
    font-size: 20px;
    cursor: pointer;

}
.filtro-colaborador-header{
    width: 100%;
    background-color: #094771;
    height: 123px;
}
.filtro-colaborador-header span:first-child{
    float: left;
    display: inline-block;

    width:auto ;
    text-align: left;
    color: #ffffff !important;
    margin-top: 0 !important;
}
.filtro-colaborador-header span:not(.fa-icon-remove-sign){
    float: left;
    display: block;
    width: 100%;
    color: rgba(255, 255, 255, 0.60);
    text-align: left;
    margin-top: 7%;
}
.filtro-colaborador-header .fa-icon-remove-sign{
    float: right;
    display: inline-block;
}
.filtro-colaboradores{
    width: 85%;
    height: 90%;
    margin-top: 5%;
    margin-left: 7.5%;
    float: left;
    text-align: left;;
}
.btn-colaboradores-fechar:hover{
    color: #29ABE2;
}
.filtro-colaborador-grupo-header{
    color: #333333;
    margin-bottom: 2%;
    margin-top: 2%;
    font-family: Arial;
    text-align: left;
    width: 100%;
    font-size: 1.1em;
    font-weight: bold;
    display: block;
    float: left;
}
.filtro-colaborador-grupo-item:nth-child(2n+1){
    float: right;
}
.filtro-colaborador-grupo-item .filtro-colaborador-img img{
    width:32px;
    height:32px;
    border-radius:50%;
    position: relative;
    left: -1px;
    top: -1px;
    border:1px solid #9E9E9E;
}
.filtro-colaborador-grupo-item .filtro-colaborador-img{
    margin-top:7px;
    margin-left: 5px;
    border-radius:50%;
    background-color: #000000;
    width:32px;
    float: left;
    height:32px;
}
.filtro-colaborador-grupo-item:hover img , .filtro-colaborador-grupo-item-click .filtro-colaborador-img img{
    border:1px solid #29ABE2;
}
.filtro-colaborador-grupo-item .filtro-col-icon-check{
    display: none;
}
.filtro-colaborador-grupo-item:hover .filtro-col-icon-check,.filtro-colaborador-grupo-item-click .filtro-col-icon-check{
    position: absolute;
    display: inline;
    font-size: 21px;
    margin-top: 5px;
    color: rgba(255, 255, 255, 0.74);
    margin-left: 6px;
    left: 5px;
}
.filtro-colaborador-grupo-item.filtro-colaborador-grupo-item-click ,.filtro-colaborador-grupo-item-click .filtro-col-icon-check{
    color: #29ABE2;
}
.filtro-colaborador-grupo-item-nome{
    margin-left: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: table-caption;
}
.filtro-colaborador-grupo-item:before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(41, 171, 226, 0.4);
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transition-property: transform;
    transition-property: transform;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
}
.filtro-colaborador-grupo-item-click img{
    opacity: 0.6;
}
.filtro-colaborador-grupo-item-click, .filtro-colaborador-grupo-item-click:focus, .filtro-colaborador-grupo-item-click:active {
    color: white;
}

.filtro-colaborador-grupo-item-click:before, .filtro-colaborador-grupo-item-click:focus:before, .filtro-colaborador-grupo-item-click:active:before {
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
}

.filtro-colaborador-grupo-item:hover {
    color: #29ABE2;
    cursor: pointer;
}
.checkBox-filtro-col{
    display: none;
}
.margin-container{
    display: block;
    margin-top: 30px;
    width: calc(100% - 60px);
    margin-left: 30px;
    margin-right: 30px;
}
.filtro-colaborador-grupo-item{
    float: left;
    margin-bottom: 2%;
    position: relative;
    width: 47%;
    margin-left: 2%;
    line-height: 50px;
    text-align: left;
    color: #777777;
    display: inline-flex;
    font-size: .8em;;
    font-family: Arial;
    height: 50px;
}
.fundo-filtro-col{
    background-image: none;
    background-color: #ffffff;
    opacity: .3;
    -moz-opacity: .3;
}
.bi-combo-empresa{
    height: 40px;
    margin-right: 14px;
    background-color: #ffffff;
    border-radius: 3px;
}
.bi-block-container-header{
    background-color: #094771 ;
    width: 100%;
    height: 123px;
}
.bi-block-container-body{
    height: calc(100% - 123px);
    display: block;
    width: 100%;
    margin: 0;
    background-color: rgba(9,71,113,.8);
}
.bi-block-container-body .bi-container-bis .container-sortable-bi{
    float: right;
    width: 80%;
    margin-right: -9%;
}
.bi-block-container-body .bi-container-bis .container-sortable-excluded{
    float: left;
    margin-left:  -18%;
    width: 30%;
}
.colunaSortable .container-sortable-bi{
    min-width: 760px;
    width: 100%;
}
.bi-block-container-header .bi-icon-sortable-handle{
    color: #ffffff;
    background-color: transparent;
    margin-top: 20px;
}
.btn-toggle-grupo{
    cursor: pointer;
    vertical-align: text-top;
    margin-right: 5px;
    color: #29ABE2;
}
.margin-left-right-7{
    margin-right: 7px;
    margin-left: 7px;
    height: 16px;
    width: 16px;
    margin-top: 5px;
}
.container-filtro-bi{
    -webkit-transition: background-color .6s;
    -moz-transition: background-color .6s;
    -ms-transition: background-color .6s;
    -o-transition: background-color .6s;
    transition: background-color .6s;
}
.container-bi-50 .grafico-icv{
   overflow: hidden;
}
.filtros-bi-fixo{
    z-index: 20;
    top: 50px;
    margin-top: 0px;
    padding-top: 5px;
    padding-right: 30px;
    width: calc(100% - 30px);
    position: fixed;
    background-color: rgba(153, 153, 153, 0.48);
    left: 0;
}
.inFaqTour .filtros-bi-fixo{
    top:0px;
}
.bi-unloaded{
    width: 100%;
    min-height: 200px;
    background-color: #fff;
}
.filtros-bi-fixo div{
    margin: 0px 15px 5px 0 !important;
}
.valorMonetarioTM{
    /*content: "R$ ";*/
    color: #2BAF50 !important;
    font-size: .4em !important;
    margin-left:2px !important
}
.badgeItem:after{
    content: attr(data-bagde);
    position: absolute;
    top: 50%;
    left: calc(50% - 14px);
    top: -7px;
    text-align: center;
    font-size: 1em;
    line-height: normal;
    color: #fff;
    padding: 0px 9px 0px 9px;
    background-color: #29ABE2;
    border-radius: .5em;
}
.badgeItemIcon:after{ /*Utilizado para filtro de colaborador por BI quando n�o tem �cones � direita*/
    content: attr(data-bagde);
    position: absolute;
    top: -7px;
    text-align: center;
    font-size: 12px;
    line-height: normal;
    color: #fff;
    padding: 0px 9px 0px 9px;
    background-color: #29ABE2;
    border-radius: .5em;
}
.badgeItem1Icon:after{ /*Utilizado para filtro de colaborador por BI quando tem apenas um �cone � direita*/
    right: 8%;
    content: attr(data-bagde);
    position: absolute;
    top: -7px;
    text-align: center;
    font-size: 12px;
    line-height: normal;
    color: #fff;
    padding: 0px 9px 0px 9px;
    background-color: #29ABE2;
    border-radius: .5em
}
.badgeItem2Icon:after{ /*Utilizado para filtro de colaborador por BI quando tem 2 �cones � direita*/
    right: 12.4%;
    content: attr(data-bagde);
    position: absolute;
    top: -7px;
    text-align: center;
    font-size: 12px;
    line-height: normal;
    color: #fff;
    padding: 0px 9px 0px 9px;
    background-color: #29ABE2;
    border-radius: .5em
}
.badgeItem3Icon:after{ /*Utilizado para filtro de colaborador por BI quando tem 3 �cones � direita*/
    content: attr(data-bagde);
    position: absolute;
    right: 17%;
    top: -7px;
    text-align: center;
    font-size: 12px;
    line-height: normal;
    color: #fff;
    padding: 1px 9px 1px 9px;
    background-color: #29ABE2;
    border-radius: .5em
}

.tituloLtv{
    font-family: Arial;
    font-style: normal;
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
    text-transform: uppercase;
    color: black;
}

.tituloGymPass {
    font-family: 'Arial';
    font-style: normal;
    font-weight: 700;
    font-size: 40px;
    line-height: 46px;
    text-align: center;
    color: #0380E3;
}

.subtituloLtv{
    font-family: Arial;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 12px;
    color: #9D9D9D;
}
.textoRodapeLtv{
    font-family: Arial;
    font-style: normal;
    font-size: 14px;
    line-height: 16px;
    color: #777777;
}

.tabs-container {
    position: relative;
    margin: 0 auto;
}

.tabs-container:after {
    content: '.';
    display: block;
    clear: both;
    height: 0;
    font-size: 0;
    line-height: 0;
    visibility: none;
}
input.tabs {
    display: none;
}
input.tabs + label {
    line-height: 30px;
    padding: 0 20px;
    float: left;
    border-radius: 50px;
    color: #29ABE2;
    background: #fff;
    cursor: pointer;
    transition: background ease-in-out .3s;
}
input.tabs:checked + label {
    background: #29ABE2;
    color: #fff;
}
input.tabs + label + div {
    width: 95%;
    opacity: 0;
    position: absolute;
    top: 40px;
    left: 0;
    padding: 10px;
    z-index: -1;
    transition: opacity ease-in-out .3s;
}
input.tabs:checked + label + div {
    opacity: 1;
    z-index: 10;
}

.m-signature-pad {
  position: relative;
  font-size: 10px;
  height: 40vh;
  width: calc(100% - 2vh);
  margin-left: 1vh;
}

.m-signature-pad:before, .m-signature-pad:after {
	position: relative;
        z-index: -1;
        content: "";
	height: 10px;
	left: 20px;
	bottom: 10px;
	background: transparent;
	-webkit-transform: skew(-3deg) rotate(-3deg);
	-moz-transform: skew(-3deg) rotate(-3deg);
	-ms-transform: skew(-3deg) rotate(-3deg);
	-o-transform: skew(-3deg) rotate(-3deg);
	transform: skew(-3deg) rotate(-3deg);
	box-shadow: 0 8px 12px rgba(0, 0, 0, 0.4);
}

.m-signature-pad:after {
	left: auto;
	right: 20px;
	-webkit-transform: skew(3deg) rotate(3deg);
	-moz-transform: skew(3deg) rotate(3deg);
	-ms-transform: skew(3deg) rotate(3deg);
	-o-transform: skew(3deg) rotate(3deg);
	transform: skew(3deg) rotate(3deg);
}

.m-signature-pad--body {
  position: relative;
  top: 20px;
}



.m-signature-pad--body
  canvas {
    position: relative;
    left: 0;
    top: 0;
    width: 100%;
    background-color: rgba(237,237,237,.7);
    height: 40vh;
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.02) inset;
  }

.m-signature-pad--footer {
  position: relative;
  bottom: 1vh;
}

.m-signature-pad--footer
  .left, .right {
    position: relative;
    bottom: 0;
  }

.m-signature-pad--footer
  .left {
    left: 0;
  }

.m-signature-pad--footer
  .right {
    right: 0;
  }




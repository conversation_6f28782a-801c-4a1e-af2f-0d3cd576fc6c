.texto-cor-cinza-2 {
    color: #333;
}

.texto-cor-cinza-3 {
    color: #B4B4B4;
}

.abrir {
    display: block;
}

.esconder {
    display: none;
}

.bg-cinza-2 {
    background-color: #333;
}

.bg-cinza-3 {
    background-color: #777;
}

.bg-cinza-4 {
    background-color: #D5D2D2;
}

.autorizacaoCobrancaItem {
    width: 100%;
    -webkit-transition: 1s all;
    -moz-transition: 1s all;
    -ms-transition: 1s all;
    -o-transition: 1s all;
    transition: 1s all;
    cursor: pointer;
    border-top: none;
    border-bottom: 1px solid #9E9E9E;
}

.autorizacaoCobrancaItem:first-child {
    border-top: 1px solid #9E9E9E;
}

.autorizacaoCobrancaItem:hover {
    background-color: rgba(0, 0, 0, .1);
    border-color: #9E9E9E;
    border-radius: 3px;
}

.autorizacaoCobrancaItem .maginAutorizacao {
    padding: 10px;
}

.autorizacaoCobrancaItem .autorizacaoCobrancaBody {
    display: inline-block;
    height: 40px;
    line-height: 20px;
    width: 100%;
    margin-bottom: 10px;
}

.datasContrato {
    display: inline-table;
    height: 64px;
    width: 100%;
    line-height: 1.4;
}

.datasContrato span, .datasContrato a {
    margin-left: 15px;

}

.datasContrato span:first-child, .datasContrato a:first-child {
    margin-left: 0px;
}

.itemLinha {
    width: 100%;
    display: block;
}

.margin-container {
    display: inline-block;
    width: calc(100% - 60px);
    margin-left: 30px;
    margin-right: 30px;
}

.margin-container.faqtoidTourHighlight {
    margin-left: 0px;
    margin-right: 0px;
    padding-left: 30px;
    padding-right: 30px;
    z-index: 9999999999;
    position: relative;
}

.linkPadrao:visited, .linkPadrao:hover, .linkPadrao {
    text-decoration: none;
}

.alinharVerticalBottom {
    vertical-align: bottom;
}

.containerDetalhesNegociacao {
    -webkit-transition: all 1s cubic-bezier(0.78, 0.01, 0.57, 0.98);
    -moz-transition: all 1s cubic-bezier(0.78, 0.01, 0.57, 0.98);
    -ms-transition: all 1s cubic-bezier(0.78, 0.01, 0.57, 0.98);
    -o-transition: all 1s cubic-bezier(0.78, 0.01, 0.57, 0.98);
    transition: all 1s cubic-bezier(0.78, 0.01, 0.57, 0.98);
}

.bg-cinza {
    background-color: #E5E5E5;
}

.caixaInfoNegociacao {
    height: 38px;
    line-height: 38px;
    width: 100%;
}

.caixaInfoFinan {
    height: 100%;
    width: 43%;
    float: right;
    background-color: #F4C9D1;
}

.timeLineSmall {
    position: relative;
    width: 60px;
    vertical-align: middle;
    margin-left: 10px;
    margin-right: 10px;
    display: inline-block;
    height: 2px;
    background-color: #333333;
}

.timeLineSmall:after {
    content: "";
    position: absolute;
    height: 8px;
    width: 8px;
    top: -3px;
    right: 0;
    border-radius: 50%;
    background-color: #333333;
}

.timeLineSmall:before {
    content: "";
    position: absolute;
    height: 8px;
    width: 8px;
    left: 0;
    top: -3px;
    border-radius: 50%;
    background-color: #333333;
}

.separador {
    width: 100%;
    height: 1px;
    background-color: #DDE7E7;
    margin-bottom: 1em;
    margin-top: 1em;
}

.gridValoresContrato {
    width: calc(100% - 40px);
    margin: 20px;
}

.valorItem {
    width: 100%;
    height: 42px;
    line-height: 42px;
    display: inline-block;
}

.gridValoresContrato .valorItem {
    border-top: 1px solid #777
}

.valorItem > span:first-child, .containerModalidadeItem .modalidadeHeader span:first-child {
    float: left;
    margin-left: 2.5%;
}

.valorItem > span:nth-child(2) {
    float: right;
    text-align: right;
    margin-left: 5%;
    margin-right: 5%;
}

.containerModalidadeItem .modalidadeHeader span:last-child {
    float: right;
    text-align: right;
}

.containerModalidadeItem .modalidadeHeader span {
    margin-right: 5%;
}

.valorItem span {
    float: right;
}

.boxTotalPagar {
    line-height: 4.5em;
    height: 4.5em;
    width: 100%;
}

.containerModalidadeItem {
    width: 100%;
    margin-bottom: 10px;
}

.bg-desabilitado {
    background-color: #E5E5E5;
}

.bordaAzul {
    border: 1px solid #29ABE2;
}

.bordaVermelha {
    border: 1px solid #FF5555;
}

/*.containerModalidadeItem.bordaAzul .modalidadeHeader,.containerModalidadeItem.bordaVermelha .modalidadeHeader{*/
/*border-bottom: none;*/
/*}*/
.containerModalidadeItem .modalidadeHeader {
    width: 100%;
    line-height: 3em;
    height: 3em;
}

.containerModalidadeItem .modalidadeHeader {
    border-bottom: 1px solid #DDE7E7;
}

.containerModalidadeItem:not(.bg-desabilitado):not(.bordaAzul):not(.bordaVermelha):not(.bordaCinza) .modalidadeHeader {
    border: 1px solid #DDE7E7;
}

.containerDescontoManual {
    width: 100%;
    height: 70px;
}

.bordaCinza {
    border: 1px solid #DDDDDD;
}

/*-------------------------------------------------Spiner Custom------------------------*/
.spinerFontAwesome {
    display: inline-block;
}

.spinerFontAwesome .rich-spinner-input {
    width: 24px;
    text-align: right;
    font-size: 1.1em;
    color: #29ABE2;
    font-family: Arial;
}

.spinerFontAwesome .rich-spinner-input:focus {
    outline: none;
    border: 1px solid #DDDDDD;
    border-radius: 2px;
}

.spinerFontAwesome .rich-spinner-btn {
    width: calc((100vw / 100) * .8);
    margin: calc((100vw / 100) * .08);
}

.spinerFontAwesome .rich-spinner-buttons, .spinerFontAwesome .rich-spinner-input-container, .spinerFontAwesome .rich-spinner-input-container {
    background-image: none;
    border-style: none;
    border: none;
    background-color: transparent;
}

/*--------------------------------------------------------------Input Text Clean----------------------------------*/
.inputClean {
    width: 60px;
    text-align: center;
    font-size: 16px !important;
    color: #29ABE2 !important;
    font-family: Arial !important;
    border: none !important;
    border-style: none !important;
    border-color: transparent !important;
    border-width: 0 !important;
    background-image: none !important;
    background-color: transparent !important;
}

.detalhesEsconder {
    -webkit-transition: all 1s;
    -moz-transition: all 1s;
    -ms-transition: all 1s;
    -o-transition: all 1s;
    transition: all 1s;
    overflow: hidden;
}

.detalhesEsconder.esconderDetalhes {
    height: 0;
}

.tiposACobrar {
    font-size: 12px;
    width: 100%;
}

.tiposACobrar td {
    width: 25%;
}

.tiposACobrar label {
    font-weight: bold;
}

.tiposACobrar input {
    display: block;
    float: left;
    margin-top: 0;
    margin-bottom: 10px;
}

table.tableAjuda {
    margin-top: -16px;
}

.tableAjuda td {
    width: 25%;
    font-size: 11px;
    vertical-align: super;
    padding-left: 22px;
    padding-right: 2%;
    color: #A1A1A1;
}

.blocoTipos {
    margin-left: 52%;
}

.blocosVitio {
    margin-top: 25px;
    background-color: white !important;
    webkit-box-pack: center !important;
    display: -webkit-inline-box;
    padding: 5px;
    border-color: #80808087 !important;;
    border-width: 1px !important;;
    border-radius: 4px !important;;
    border-style: solid !important;;
    width: 22px;
    height: 22px;
    margin-right: 8px
}

.botaoEnviarEbookPorWpp {
    margin-left: 8px !important;
    border: 0 !important;
    border-color: white !important;
    background: 0 !important;
    color: #878A92 !important;
    font-size: 15px !important;
    cursor: pointer !important;
}

.botaoProsseguirVitio {
    width: 35%;
    background-color: #05E173 !important;
    border: 1px solid #05E173 !important;
    color: #fff !important;
    padding: 0;
    box-shadow: none;
    text-transform: uppercase !important;
    border-radius: 4px;
    line-height: 32px;
    cursor: pointer;
    position: relative;
    outline: 0;
    font-family: 'Nunito Sans',sans-serif !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    background-image: none !important;
}

.imagemVitio {
    background-image: url("imagens/imagem-modal-vitio.png") no-repeat;
}

.inputModalVitus {
    font-family: 'Nunito Sans' !important;
    font-style: normal !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    line-height: 24px !important;
    color: #51555A !important;
    border: 0 !important;
    width: 24.9em !important;
    height: 2.1em !important;
    margin-left: -4px !important;
    margin-top: -5px !important;
}

.textoModalVitusPopUp {
    margin-top: 31px;
    width: 697px;
    height: 100px;
    font-family: 'Nunito Sans';
    font-style: normal;
    font-weight: 700;
    font-size: 48px;
    line-height: 50px;
    color: #51555A;
}
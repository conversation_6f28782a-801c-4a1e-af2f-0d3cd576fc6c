.dataTables_filter{
    display:none;
}
.linkLegenda {
    color: rgb(0,0,0) !important;
    font-size: 12px;
}
a, a:hover, a:visited, a:active{
    text-decoration: none;
    cursor: pointer;
}
.painelGR.cima{
    margin-top: 6em;
}
.painelGR.baixo{
    margin-bottom: 3em;
}
.painelGR{
    border-color: #eee;
    background-color: #E5E5E5;
    height: 3em;
    line-height: 3em;
    text-align: right;
}
.simboloStatus{
    font-size: 15px;
    margin-top: 7px;
}
.btnselecionado{
    color: white !important;
    background-color: #333333 !important;
}
.textoCarregando{
    margin-left: 0px !important;
}
.dataTable tr > * {
    padding: 8px;
}
.info td{
    font-size: 12px;
}
.infoNegrito td{
    font-size: 12px;
    font-weight: bold !important;
}
.legendaFormas{
    display: inline-table;
}
text tspan{
    font-size: 9px !important;
}
.tabelaTotalizador td{
    padding: 5px;
    font-size:12px;
}
svg g path{
    cursor: pointer;
}
.linkPadrao{
    font-size: 16px !important;
}
.novaModal .tituloCampos {
    margin: 6px 0px;
}
.chk-fa-container input[type="checkbox"] + span {
    top: -1px !important
}
.barraFixaClean {
    background-color: rgba(153, 153, 153, 0.8);
}
.tabelaDados td{
    cursor: auto;
}
.formaEspecie{
   background-color: transparent;
}
.formaBoleto {
    background-color: transparent;
}
.formaCC {
   background-color: transparent;
}
.formaSelecionada {
   background-color: #D3D3D3 !important;
} 
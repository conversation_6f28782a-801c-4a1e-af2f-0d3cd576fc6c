
.fundoCinza{
    background-color: #eff2f7 !important;
}
.fechado-zwui .home-page {
    padding-left: 104px;
}
.home-page {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    width: auto;
    height: auto;
    padding: 16px;
    background-color: #eff2f7;
}

.full-width {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 16px;
    width: 100%;
    min-height: 150px;
}

.home-page .carousel{
    margin-bottom: 0 !important;
}
.home-page .home-half-width {
    display: flex;
    align-items: stretch;
    width: 50%;
    flex-wrap: nowrap;
    gap: 16px;
    flex-direction: column;
}
.home-page .home-half-width .home-column{
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    padding: 16px;
    border-radius: 4px;
    background-color: #fff;
    overflow: hidden;
}
.home-page .home-half-width .home-column.home-banner{
    padding: 0;
}
.full-width div{
    justify-content: space-between;
    display: flex;
    font-size: 14px;
    line-height: 125%;
    align-items: center;
}
.troca-empresa div{
    max-width: 800px;
}
.troca-empresa{
    font-family: "Nunito Sans", sans-serif;
    letter-spacing: 0;
    font-weight: 400;
    color: #797D86;
    margin-bottom: 16px;
}
.diviser{
    padding-bottom: 8px;
    border-bottom: 1px solid #c9cbcf;
    margin-bottom: 16px;
}

.ola-empresa{
    text-transform: capitalize;
    font-family: Poppins, sans-serif;
    letter-spacing: .25px;
    font-weight: 600;
    color: #494b50;

}
.troca-empresa a.zw_ui_trocar_unidade div i.pct-pin{
    margin-right: 8px;
}

.home-page a.zw_ui_trocar_unidade:hover{
    text-decoration: none;
}
.troca-empresa a.zw_ui_trocar_unidade div,
.ola-empresa a.zw_ui_trocar_unidade div{
    color: #1e60fa;
    font-family: Poppins, sans-serif;
    font-size: 12px;
    line-height: 100%;
    letter-spacing: .25px;
    font-weight: 600;
    padding: 13px 0px;
}

.full-width div.fixados-itens{
    padding-top: 16px;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
    align-content: center;
    flex-direction: row;
    gap: 6px;
}
.fixados-itens .fixado-item a:hover{
    text-decoration: none;
}
.fixados-itens .fixado-item a i{
    font-size: 14px;
}
.fixados-itens .fixado-item a.link-fixado{
    color: #494b50;
    font-weight: 600;
    font-family: Poppins, sans-serif;
    font-size: 12px;
    line-height: 125%;
    letter-spacing: .25px;
}
.fixados-itens .fixado-item{
    padding: 12px 6px;
    border: 1px solid #c9cbcf;
    border-radius: 4px;
    width: calc(20% - 20px);
    height: 35px;
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
}
.fixados{
    font-size: 12px !important;
}
.ola-empresa .zw_ui_trocar_unidade i{
    padding-right: 5px;
}

.home-page .item-geral-versao-info-data .home-link.item-geral-versao-info-data-saiba:hover {
    background: #B4CAFD;
}
.home-page .item-geral-versao-info-data a:hover {
    text-decoration: none;
    text-transform: none;
}
.home-page .item-geral-versao-info-data .home-link.item-geral-versao-info-data-saiba{
    font-size: 14px;
    line-height: 100%;
    letter-spacing: .25px;
    font-weight: 600;
    padding: 10px 8px;
    font-family: Poppins, sans-serif;
    color: #1E60FA;
    cursor: pointer;
    width: auto;
    border-radius: 5px;
    text-align: center;
}

.avisos-item {
    display: flex;
    gap: 4px;
    margin-top: 16px;
    padding: 8px;
    border: 1px solid #c9cbcf;
    border-radius: 8px;
}
.avisos-footer h3{
    margin-bottom: 0;
    text-transform: capitalize;
    color: hsla(222, 5%, 35%, 1);
}
.avisos-footer h3, .pct-title5 {
    font-family: Poppins, sans-serif;
    font-size: 12px;
    line-height: 125%;
    letter-spacing: .25px;
    font-weight: 700;
}
.avisos-footer p {
    font-family: "Nunito Sans", sans-serif;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0;
    font-weight: 400;
    margin: 0 !important;
    color: #444;
}
.avisos-content p{
    font-family: Arial !important;
    margin: 0 !important;
    font-size: 13px;
    line-height: 20px;
    color: #444;
}
.avisos-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.row.publicar-aviso {
    margin-right: 0;
    margin-left: 0;
    padding: 6px;

    h6 {
        font-family: Poppins, sans-serif;
        font-size: 16px;
        line-height: 18px;
        letter-spacing: .25px;
        font-weight: 600;
        color: hsla(222, 5%, 50%, 1);
        margin: 8px 0px;
    }

    .cb-container::after {
        line-height: 3.4em;
    }

    .cb-container select {
        color: #494b50;
        font-size: 16px;
        line-height: 125%;
        letter-spacing: 0;
        font-weight: 400;
        border-radius: 10px;
    }

    .cb-container {
        width: calc(100% - 16px);
        margin: 16px 0;
    }

    .dateTimeCustom .rich-calendar-input:not(.inputCartao) {
        vertical-align: middle;
        height: 38px;
        width: 345px;
        border-radius: 10px 0 0 10px;
        font-size: 17px !important;
        color: rgb(73, 75, 80) !important;
        padding-left: 20px;
    }
    .dateTimeCustom .rich-calendar-button[src$=".svg"]{
        height: 38px;
    }
    .text-area-aviso, .text-area-aviso:focus-visible{
        width: 100%;
        height: 75px;
        margin-top: 16px;
        font-size: 17px !important;
        color: rgb(73, 75, 80) !important;
        padding: 8px;
        border: 1px solid #c9cbcf !important;
        background-image: none;
        background-color: #FFFFFF;
        outline: none;
        border-radius: 10px;
    }
    .contador {
        text-align: right;
        color: #1E60FA;
        font-size: 17px;
        margin-top: 5px;
        font-weight: 300;
    }
    .botoes-aviso{
        text-align: right;
        margin-top: 16px;
    }
    .ds3-btn:hover{
        text-transform: none;
        text-decoration: none;
    }
    .ds3-btn.outline:hover{
        background: #e7edfe;
    }
    .ds3-btn{
        margin-left: 16px;
        font-family: Poppins, sans-serif;
        font-size: 14px;
        line-height: 100%;
        letter-spacing: .25px;
        font-weight: 600;
        cursor: pointer;
        outline: 0 !important;
        -webkit-tap-highlight-color: transparent;
        padding: 13px 20px;
        margin-bottom: unset;
        border-radius: 4px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        transition: .2s;
        border-color: #1e60fa;
        color: #1E60FA;
        background: 0 0;
        border-width: 1px;
        border-style: solid;
        text-transform: none;
    }
    .ds3-btn.primary{
        background: #1E60FA;
        color: #fff;
    }
}

.lista-avisos{

    .menu-container {
        position: relative;
        display: inline-block;
    }

    .menu-button:hover {
        background: #e7edfe;
    }
    .menu-button {
        background-color: #fff;
        color: #1E60FA;
        border: none;
        padding: 5px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 18px;
        text-decoration: none;
    }

    .menu-button:focus {
        outline: none;
    }

    .dropdown-menu {
        display: none;
        position: absolute;
        right: 0;
        margin-top: 5px;
        background-color: #ffffff;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        z-index: 1;
    }

    .dropdown-item span{
        border-bottom: 1px solid #f0f0f0;
        padding: 8px;
        width: 92px;
        display: block;
        text-align: center;
    }
    .dropdown-item {
        display: block;
        color: #1E60FA;
        padding: 0 5px;
        cursor: pointer;
        text-decoration: none;
        width: auto;
        transition: background-color 0.3s;
        font-family: Poppins,sans-serif;
        font-size: 12px;
        line-height: 100%;
        letter-spacing: .25px;
        font-weight: 600;
        height: auto;
    }

    .dropdown-item:last-child {
        border-bottom: none;
    }

    .dropdown-item.delete {
        color: #ea4335;
    }

    .dropdown-item:hover {
        background-color: #f0f4ff;
    }

    .dropdown-menu.show {
        display: block;
    }

    .avisos-content{
        display: flex;
        justify-content: space-between;
    }

}

.novaModal.modalDs3 .rich-mpnl-header-cell{
    height: 83px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #c9cbcf;
}
.novaModal.modalDs3 .rich-mpnl-text.rich-mpnl-controls img{
    cursor: pointer;
}
.novaModal.modalDs3 .rich-mpnl-header{
    color: rgb(81, 85, 90);
    font-weight: 400;
    font-size: 20px;
    line-height: 22px;
    font-family: "Nunito Sans", sans-serif;
}
.novaModal.modalDs3 input[type='text']:not(.inputAntigo).formDs3{
    height: 38px;
    width: 100%;
    border-radius: 10px;
    font-size: 17px !important;
    color: rgb(73, 75, 80) !important;
    padding-left: 10px;
}
.usuarioAutoSuggestioPanel{
    margin-bottom: 24px;
}
.usuario-aviso{
    line-height: 28px;
    border: 1px solid #51555a;
    border-radius: 16px;
    overflow: hidden;
    margin: 3px;
    padding: 0 8px 0 6px;
    word-break: break-word;
    display: flex;
    align-items: center;
    cursor: pointer;
    .pct-x{
        margin-left: 8px;
    }
    a, a:hover{
        line-height: 24px;
        font-size: 13px;
        color: #444;
    }
    a:hover{
        text-decoration: none;
    }

}
.ds3-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
        margin: 24px 0 16px;
    }
    h2 {
        margin-bottom: 16px;
        color: hsla(222, 5%, 35%, 1);
        font-family: Poppins, sans-serif;
        font-size: 14px;
        line-height: 125%;
        letter-spacing: .25px;
        font-weight: 600;
    }
    p {
        color: hsla(223, 5%, 30%, 1);
        font-family: "Nunito Sans", sans-serif;
        font-size: 16px;
        line-height: 125%;
        letter-spacing: 0;
        font-weight: 400;
    }
}

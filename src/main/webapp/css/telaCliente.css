.menuLateral:hover .btnfotoOverlay{
    visibility: visible;
  opacity: 1;
}
body{
    overflow-x: hidden !important;
}
.btnfotoOverlay{
    visibility: hidden;
  opacity: 0;
  transition: visibility 0s, opacity 0.5s linear;
}

.box-novo{
    background-color: white;
    padding:10px;
    width: 175px;
}
.noPadding{
    padding: 0px !important;
}
.w100{
    width: 100%;
}
.negrito{
    font-weight: bold;
}
.cinzaEscuro{
    color: #333333;
}
.cinza{
    color: #777777;
}
.cinzaClaro{
    color: lightgray;
}
.blocoMeioaMeio{
    width: 48%; 
    display: inline-block;
}
.block{
    display: block;
}
.mtop10{
    margin-top: 10px;
}
.mtop20{
    margin-top: 20px;
}

a.linkAzul, a.linkAzul:visited, a.linkAzul:hover, a.linkAzul:focus{
    color: #29ABE2  !important;
    font-size: .9em !important;
}
.w100.red, .w100.green{
    padding: 5px 0;
}
.w100.red{
    background-color:  #FBC5C5;
}
.w100.green{
    background-color:  #AADFB9;
}
.separator{
    border-bottom: #E5E5E5 1px solid;
    margin-top: 20px;
}
.pl5{
    padding-left: 5px
}
.pl20{
    padding-left: 20px
}
.pl8{
    padding-left: 8px !important;
}
.opcoes{
    color: #094771 !important;
}
.rich-ddmenu-label-select{
    border: none !important;
}
.rich-label-text-decor{
    font-weight: normal !important;
    font-size: 13px;
}
.marginForMenuWiki {
    margin-top: -13px !important;
}
.painelDadosAluno{
    min-width: 330px;
    width: 47%; 
    height: 45vh; 
    background-color: white; 
    -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    display: inline-table;
    margin: 1.5% 0 0 1.5%;
    position: relative;
}
.tituloPainelAluno{
    width: 100%;
    border-bottom: #E5E5E5 1px solid;
    display: table;
    line-height: 50px;
}
.rodapePainelAluno{
    width: 100%;
    border-top: #E5E5E5 1px solid;
    display: table;
    line-height: 40px;
}
.font16{
    font-size: 16px !important;
}
.font14{
    font-size: 14px !important;
}
.font12{
    font-size: 12px !important;
}
.upper{
   text-transform: uppercase; 
}

.dataTable.tableCliente{
    margin: 0 !important;
    width: 100%;
}
.dataTable.tableCliente th a{
    cursor: pointer;
}
.dataTable.tableCliente th, .dataTable.tableCliente th a{
    text-transform: uppercase;
    color: #777777;
    font-size: 14px !important;
}
.dataTable.tableCliente td a, .tituloTotalizador{
    color: #777777;
    text-transform: capitalize;
    font-size: 16px !important;
    display: block;
    padding: 10px 0px;
    width: 100%;
    height: 100%;
}
.tablecontratosaluno.dataTable.tableCliente td a{
    font-size: 14px !important;
}
.tituloPainelPesquisa{
    width: 100%;
    font-weight: bold;
    text-align: left;
    padding: 0px;
    height: 40px;
}
.tituloPainelPesquisa.cinza{
    background-color: #e5e5e5 !important;
}
.tituloPainelPesquisa.notop{
    padding-top: 0px; 
}
.tituloPainelPesquisa.nobottom{
    padding-bottom: 0px; 
}
.linkWiki.cinza{
    color: #9E9E9E;
}
.inputBuscaAlunoSimples, .inputBuscaAlunoSimples:focus{
    border-bottom: solid 1px #29ABE2 !important;
    width: 20vw !important;
    margin: 15px 0px 0 15px;
    font-size: 14px !important;
}

.inputBuscaFiltro{
    border-bottom: solid 1px #29ABE2 !important;
    margin: 15px 0px 0 15px !important;
    font-size: 14px !important;
    background: none !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    height: 20px !important;
    outline: none ;
}

.azulLink{
    color: #29ABE2 !important;
}
.painelToogleAberto .fa-icon-caret-up{
    display: none;
}
.painelToogleFechado .fa-icon-caret-down{
    display: none;
}
.linkFiltros, .linkFiltros:hover{
    float: right; 
    cursor: pointer;
    font-weight: normal;
    font-size: 0.9vw;
    line-height: 40px;
    padding: 0 10px;
}
.paineldropdownfiltros{
    text-align: right; 
    display: none;
}
.painelToogleFiltros:hover, .linkFiltros:hover{
    background-color: #D4D4D4;
}
.painelToogleAberto, .painelToogleFiltros.painelToogleAberto:hover{
    background-color: #7B7B7B;
    color: white !important;
}
.newComboBox.noBorderLeft{
    border-left: none !important;
}
.newComboBox.noBorderRight{
    border-right: none !important;
}

.newComboBox{
    background-color: transparent;
    border: none;
    font-size: 14px;
    padding: 10px;
    border-right: 1px solid #9E9E9E;
    border-left: 1px solid #9E9E9E;
    color: #29ABE2 ;
    vertical-align: middle;
    background-image: none;
}
.font1vw{
    font-size: 1vw !important;
}
.insideCubo{
    width: 100%;
}
.bordaInferiorCinza{
    width: 100%;
    border-bottom: #E5E5E5 1px solid;
}

.bordaSuperiorCinza{
    width: 100%;
    border-top: #E5E5E5 1px solid;
}


.colunaDadosCliente{
    width: 49%; 
    display: inline-table;
}
.colunaDadosCliente .contatos div{
    width: 100%;
    margin-top: 10px;
}
.colunaDadosCliente .contatos div:first-child{
    margin-top: 0px;
}
a{
    outline:none;
}
.nop *{
    padding: 0 !important;
    margin: 0 !important;
}

.containerPequeno{
    width: calc(100% - 2vw); 
    padding: 5px 1vw;
}

.painelMarcarAluno{
    position: fixed; 
    height: 20vh; 
    z-index: 10; 
    width: 100%; 
    top: 0;
    border: none;
    display: none;
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#29abe2+0,7db9e8+100&1+0,0.05+100 */
    background: -moz-linear-gradient(top,  rgba(41,171,226,1) 0%, rgba(125,185,232,0.05) 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top,  rgba(41,171,226,1) 0%,rgba(125,185,232,0.05) 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom,  rgba(41,171,226,1) 0%,rgba(125,185,232,0.05) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#29abe2', endColorstr='#0d7db9e8',GradientType=0 ); /* IE6-9 */
    text-align: center;
}
.textoMarcarAluno{
    color: white;
    font-size: 4vh;
    opacity: 0.5;
    margin-top: 6vh;
}
.font18{
    font-size: 18px;
}
.caixaDados{
    border: solid 1px #E5E5E5;
    margin-left: 20px;
    padding: 10px;
    margin-right: 20px;
    margin-top: 5px;
}
.turmasTl{
    margin-left: 20px;
    width: calc(100% - 40px);
}
.turmasTl td{
    padding: 10px;
    border: solid 1px #E5E5E5;
    border-top: none;
}
.ladolado {
    display: inline-table;
}
.semBorda, .semBorda td{
    border: none !important;
}

.dadosContrato td:not(.semBorda){
   padding: 10px;
   border-bottom: solid 1px #E5E5E5; 
}
td.w4{
    width: 4%;
}
td.w48{
    width: 48%;
}
i.cinzaClaro{
    background-color: transparent !important;
}
.semSpinner{
    display: none !important;
}

.noBorder{
    border: none !important;
}

.noHover, .noHover:hover{
    background-color: #FFFFFF !important;
}

.camposSomenteLeitura{
    background-color: #FFFFFF !important;
    font: 13px verdana, arial, helvetica, sans-serif !important;
    color: #777777 !important;
    border: none !important;
}
.rodapePainelTop{
    border-top: 0 !important;
    border-bottom: #E5E5E5 1px solid !important;
}
@media screen and (max-width: 1024px) {
    .paginaFontResponsiva table,.paginaFontResponsiva{
        font-size:  calc(1024px / 100) ;
    }
}
@media screen and (min-width: 1368px) {
    .paginaFontResponsiva table,.paginaFontResponsiva{
        font-size:  calc(1368px / 100 ) !important;
    }
}
.mLeft10{
    margin-left: 10px;
}

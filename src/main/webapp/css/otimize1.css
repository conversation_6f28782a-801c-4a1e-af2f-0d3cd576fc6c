a:link {text-decoration: none; color: #666666}
a:visited {text-decoration: none; color: #666666}
a:hover {text-decoration: underline overline; color: #000099}
a:active {text-decoration: underline overline; color: #666666}
body {
  margin-left: 0;
  margin-top: 0;
}
.tituloCampos {
    font: 8pt verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
}
.imagens {
    background-color: #FFFFFF;
    text-align: center;
    border: 1px solid #FFFFFF;
}
.campos {
    background-color: #DBDBDB;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#CCCCCC',endColorStr='#ffffff',gradientType='1');
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #000000;
    margin-right: 20px;
    border: 1px solid #666666;
}
.camposObrigatorios {
    background-color: #DBDBDB;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#CCCCCC',endColorStr='#ffffff',gradientType='1');
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #D70000;
    border: 1px solid #D70000;
}
.camposSomenteLeitura {
    background-color: #DBDBDB;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#CCCCCC',endColorStr='#999999',gradientType='0');
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #666666;
    margin-right: 20px;
    border: 1px solid #D70000;
}
.botoes {
    background-color: #000000;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#ffffff',endColorStr='#000000',gradientType='0');
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #FFFFFF;
    margin-right: 20px;
    border: 1px solid #DAFEE0;
}
.botaoExcluir {
    background-color: #000000;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFDDDD',endColorStr='#000000',gradientType='0');
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #FFFFFF;
    margin-right: 20px;
    border: 1px solid #DAFEE0;
}
.mensagemDetalhada {
    font: 7pt verdana, arial, helvetica, sans-serif;
    color: #DF0000;
    text-align: right;
}
textarea { 
    background-color: #FFFFFF;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#ffffff',endColorStr='#999999',gradientType='0');
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #000000;
    margin-right: 20px;
    border: 1px solid #000000;
}
select { 
    background-color: #FFFFFF;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#ffffff',endColorStr='#999999',gradientType='0');
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #000000;
    margin-right: 20px;
    border: 1px solid #000000;
}
table {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#FFFFFF',gradientType='0');
  font-size: 8pt;
  font-family: verdana;  
}
td {
  vertical-align: top;
  text-align: left;
}
td.colunaCentralizada {
  vertical-align: top;
  text-align: center;
}
td.colunaEsquerda {
  vertical-align: top;
  text-align: left;
}
td.colunaDireita {
  vertical-align: top;
  text-align: right;
}
table.tabForm {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#E6E6E6',endColorStr='#FFFFFF',gradientType='0');
}
table.tabConsulta {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#E6E6E6',endColorStr='#FFFFFF',gradientType='0');
}
table.tabTituloFormSubordinada {
    background-color: #E6E6E6;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#E6E6E6',endColorStr='#FFFFFF',gradientType='0');
    font: 08pt verdana, arial, helvetica, sans-serif;
    color: #000000;
}
table.tabBotoes {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#CCCCCC',endColorStr='#FFFFFF',gradientType='0');
}
table.tabMensagens {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#CCCCCC',gradientType='0');
  color: #333333;
  font-size: 8pt;
  font-weight: bold;
  text-align: center;
  font-family: verdana;
}
table.tabMensagenReduzida {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#CCCCCC',gradientType='0');
  color: #333333;
  font-size: 7pt;
  font-style: italic;
  text-align: center;
  font-family: verdana;
}
table.tabTituloForm {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#CCCCCC',gradientType='0');
  color: #333333;
  font-size: 10pt;
  font-weight: bold;
  text-align: center;
  font-family: verdana;
}
table.tabmenu {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#E6E6E6',endColorStr='#FFFFFF',gradientType='0');
}
table.tabitemtitulomenu {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#CCCCCC',gradientType='0');
  color: #333333;

  border: 0;
  border-collapse: collapse;
  padding: 0;

  font-size: 8pt;
  font-weight: bold;
  font-family: verdana;
}
table.tabitemmenu {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#E6E6E6',gradientType='1');

  border: 0;
  border-collapse: collapse;
  padding: 0;

  font-size: 8pt;
  font-family: verdana;  
}
table.tabconteudo {
  background-color: #cedfff;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#FFFFFF',gradientType='0');
  font-size: 8pt;
  color: #FFFFFF;
  font-weight: normal;
  font-family: verdana;  
  text-align: center;
  vertical-align: top;
}
th {
  background-color: #FFFFFF;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#999999',endColorStr='#FFFFFF',gradientType='0');
  text-align: center;
  font-family: verdana;  
  border: 1px solid #EFEFEF;
  font-size: 10pt;
  font-weight: bold;
}
th.consulta {
  background-color: #CCCCCC;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#CCCCCC',gradientType='0');
  font-size: 8pt;
  font-weight: bold;
}
th.subordinado {
  background-color: #CCCCCC;filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FFFFFF',endColorStr='#CCCCCC',gradientType='0');
  font-size: 8pt;
  font-weight: bold;
}
tr.linhaPar:not(.notSize){
  font-size: 8pt;
  font-family: verdana;  
}
tr.linhaImpar:not(.notSize){
  font-size: 8pt;
  font-family: verdana;    
}
tr.linhaCaderno {
	font-size: 8pt;
  	font-family: verdana;  	
}
tr.linhaBotoes {
  font-size: 8pt;
  font-family: verdana;    
}
tr.linhaParSubordinado {
  font-size: 8pt;
  font-family: verdana;  
}
tr.linhaImparSubordinado {
  font-size: 8pt;
  font-family: verdana;    
}
tr.linhaBranco {
  font-size: 8pt;
  font-family: verdana;  
  text-align: center;
  color: #000000;
}
tr.menu {
  background-color: #E6E6E6;
  font-size: 8pt;
  font-family: verdana;  
  border: 1px solid #000000;
}
tr.itemMenu {
  background-color: #FFFFFF;
  font-size: 8pt;
  font-family: verdana;  
  border: 1px solid #000000;
}
td.colunaAlinhamento {
  text-align: center;
}

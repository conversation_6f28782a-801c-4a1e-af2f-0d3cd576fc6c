tituloFormulario
alinhamentoSuperiora:link {text-decoration: none; color: #666666}
a:link, a:visited {text-decoration: none;}
a:hover {text-decoration: underline; color: #000099}
a:active {text-decoration: none; color: #666666}
a.link_red:link {
    text-decoration: none;
    color:#da2128;
    font-weight:bold;
}
.colapseTable{
    border: none;
    background-color: #FFF;
    empty-cells: show;
    border-collapse: collapse;
}
.colapseTable td{
    border-right: none;
    border-top: solid 1px #C0C0C0;
    border-bottom: none;
}
.colapseTable tbody > tr:first-child td{
    border-right: none;
    border-top: none !important;
    border-bottom: none;
}
a.link_red:visited {
    text-decoration: none;
    color:#da2128;
    font-weight:bold;

}
a.link_red:hover {
    text-decoration: underline;
    color:#fc2128;
    font-weight:bold;

}
a.link_red:active {
    text-decoration: underline;
    color:#fc2128;
    font-weight:bold;
}
a.link_preto:link {
    text-decoration: none;
    color:#0f4c6b;
    font-weight:bold;
}
a.link_preto:visited {
    text-decoration: none;
    color:#0f4c6b;
    font-weight:bold;

}
a.link_preto:hover {
    text-decoration: underline;
    color:#0f4c6b;
    font-weight:bold;

}
a.link_preto:active {
    text-decoration: underline;
    color:#0f4c6b;
    font-weight:bold;
}
body {
    margin: 0;
}

.tituloFormulario {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #FFFFFF;
    text-align: right;
}
.tituloModulo {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #FFFFFF;
    text-align: right;
}
.tituloModuloPreto {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
}
.tituloFormularioPreto {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
}
.historico_texto {
    font-family: Tahoma, Geneva, sans-serif;
    font-size: 11px;
    line-height: 23px;
    color: #666;
}
.tituloFormularioPretoTopo {
    font: 20pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
}
.tituloFormularioVerdeTopo {
    font: 14pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #006633;
    text-align: right;
}
camposLeitor {
    background-color: #FEFFDF;
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #496118;
    margin-right: 2px;
    border: 1px solid #618151;
    font-weight: bold;
}
table.tabMensagens {
    background-color: #eee;
}

.mensagem {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #333333;
    text-align: right;
}
.calculo{
    font: 8pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #1E90FF;
    text-align: right;
}
.mensagemVerde {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #006633;
    text-align: right;
}
.mensagemAzul {
    font-family: 'Trebuchet MS',verdana; font-size: 8pt;
    color: #006684;
    text-align: right;
}
.mensagemDetalhada {
    font: 9pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #DF0000;
    text-align: right;
}
.mensagemDetalhadaGrande {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #DF0000;
    text-align: right;

}
.tituloCampos {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
    vertical-align: top;
}
.mensagemGrandeVermelha {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color:#da2128;
    text-align: right;
    vertical-align: top;
    font-weight:bold;
}
.tituloCamposTop {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: left;
    vertical-align: top;
}

.tituloCamposMiddle {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: left;
    vertical-align: middle;
}

.tituloCamposNegrito {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
    font-weight: bold;
}
.tituloCamposDestaque {
    font: 16pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
}
.tituloCamposDestaque12 {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
}
.tituloCamposDestaque12Negrito {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
    font-weight: bold;
}
.tituloCampos14 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 15px;
    color: #000000;
    text-align: right;
    font-weight: bold;
}
.tituloCamposDestaqueNegrito {
    font: 16pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    font-weight: bold;
    text-align: right;
}
.tituloCamposNegrito {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    font-weight: bold;
    text-align: right;
}
.botaoAssinarCotrato .fa-icon-check-empty{
    font-size: 14px;
}

.botaoAssinarCotrato .fa-icon-check{
    font-size: 14px;
}
.tituloCamposVermelho {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #DF0000;
    text-align: right;
}
.tituloCamposVermelhoNegrito {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #DF0000;
    font-weight: bold;
    text-align: right;
}
.tituloCamposVermelhoGrande {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #DF0000;
    text-align: right;
}
.tituloCamposAzul {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color:#333;
    text-align: right;
}
.tituloCamposAzulMedio {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color:#333;
    text-align: right;
}
.tituloCamposAzulComanddLink {  
    font-size:12pt;
}
.tituloCamposAzulDireita {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    text-align: right;
}
.tituloCamposAzulComanddLinkMes {
    font-size:12pt;
}
.tituloCamposAzulComanddLinkMesVerde {
    color:#008800;
    font-size:12pt;
}
.tituloCamposAzulComanddLinkMesVermelho {
    color:#FF0000;
    font-size:12pt;
}
.tituloCamposAzulClaroGrande {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color:#0088FF;
    text-align: right;
}
.tituloCamposAzulGrande {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color:#333;
    text-align: right;
}
.tituloCamposAzulGrandeNegrito {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color:#333;
    font-weight: bold;
    text-align: right;
}
.camposIndicadorVendasCRM {
    font: 13px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #333333;
    text-align: right;
}

.camposIndicadorVendasMetaCRM {
    font: 13px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    text-align: right;

}
.camposIndicadorVendasMetaAtingidaCRM {
    font: 13px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    text-align: right;


}
.camposIndicadorVendasMetaPorcentagemCRM {
    font: 13px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    text-align: right;

}
.camposIndicadorVendas {
    font: 13px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    font-weight: bold;
    color: #333333;
    text-align: right;
}

.camposIndicadorVendasMeta {
    font: 13px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    font-weight: bold;
    color: #0098DA;
    text-align: right;
    background-color: #FFFBD6;
}
.camposIndicadorVendasMetaAtingida {
    font: 13px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    font-weight: bold;
    color: #EF3E41;
    text-align: right;
    background-color: #FFFBD6;

}
.camposIndicadorVendasMetaPorcentagem {
    font: 13px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    font-weight: bold;
    color: #F58634;
    text-align: right;
    background-color: #FFFBD6;
}
.camposAgenda {	
    font: 10px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    font-weight: bold;
    color: #474747;
    text-align: right;
}
.tituloCamposAgenda {	
    font: 11px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    font-weight: bold;
    color: #333333;
    text-align: right;
}
.alinhamentoSuperior {
    text-align: left;
    vertical-align: top;
}
.tamanhoFixo {
    height: 40px;
}
.tituloCamposVerde {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #006633;
    text-align: right;
}
.tituloCamposAmarelo {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #FFD42E;
    text-align: right;
}
.tituloCamposGrande {
    font: 13pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    text-align: right;
}
.tituloCamposVerdeGrande {
    font: 13pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #006633;
    text-align: right;
}
.tituloCamposVermelhoGrande {
    font: 13pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #da2128;
    text-align: right;
}
.tituloCamposReduzidos {
    font: 8pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
}
.tituloCamposReduzidosNegrito {
    font: 8pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: center;
    font-weight: bold;
}
.imagens {
    background-color: #FFFFFF;
    text-align: center;
    border: 1px solid #FFFFFF;
}
.legendaPeriodoAcessoClienteVerde {
    background-color: #006400;
    text-align: center;
    border: 1px solid #006400;
}
.legendaPeriodoAcessoClienteVermelha {
    background-color: #FF0000;
    text-align: center;
    border: 1px solid #FF0000;
}
.campos {
    background-color: #DBDBDB;
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #000000;
    margin-right: 20px;
    border: 1px solid #CCCCCC;
}
.camposColunaEsquerda {
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:250%;
}

.camposObrigatorios {
    background-color: #DBDBDB;
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #C67700;
    border: 1px solid #CCCCCC;
    border-bottom-color: #FF9900;
}
.tituloCamposNegritoMaior {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
    font-weight: bold;
}
.tituloCamposNegritoMaiorVermelho {
    font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #DF0000;
    text-align: right;
    font-weight: bold;
}
.textverysmall {
    font-family:Arial, Helvetica, sans-serif;
    font-size:9px;
    text-decoration:none;
    color:#333;
    line-height:125%;
}

.textsmall {
    font-family:Arial, Helvetica, sans-serif;
    font-size:11px;
    text-decoration:none;
    color:#333;
    line-height:125%;
}

.tabela{
    border-top:solid 10px #ffffff;
    border-bottom-color:solid 10px #ffffff;
}


.camposSomenteLeitura {
    background-color: #EEEEEE;
    font: 11px verdana, arial, helvetica, sans-serif;
    color: #8eb3c3;
    margin-right: 20px;
    border: 1px solid #8eb3c3 ;
}

.centralizado {
    text-align: center;
}
.esquerda {
    text-align: left;
    vertical-align: middle;
}
.direita {
    text-align: right;
    vertical-align: middle;
}
td.colunaCentralizada {
    vertical-align: middle;
    text-align: center;
}

td.colunaTopCentralizada {
    vertical-align: top;
    text-align: center;
}
td.colunaEsquerda {
    vertical-align: middle;
    text-align: left;
}
th.colunaEsquerda {
    vertical-align: middle;
    text-align: left;
}
td.colunaDireita {
    vertical-align: middle;
    text-align: right;
}
th.colunaDireita {
    vertical-align: middle;
    text-align: right;
}

td.classEsquerda {
    width: 35%;
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
}
td.classDireita{
    width: 65%;
    text-align: left;
}

td.classEsquerdaPadding {
    width: 35%;
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
    padding: 7px;
}

td.classEsquerdaMenor {
    width: 25%;
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
}
td.classDireitaMaior{
    width: 75%;
    text-align: left;
}

td.classEsquerdaConfiguracao {
    width: 30%;
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
}
td.classDireitaConfiguracao{
    width: 50%;
    text-align: left;
}
td.classEsquerdaConfiguracaoSistema {
    width: 70%;
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
}
td.classDireitaConfiguracaoSistema{
    width: 30%;
    text-align: left;
}

td.colunaEsquerda {
    vertical-align: middle;
    text-align: left;
}
td.colunaEsquerda2 {
    vertical-align: top;
    text-align: left;
}
td.colunaDireita {
    vertical-align: middle;
    text-align: right;

}
td.colunaDireita2 {
    vertical-align: top;
    text-align: right;
}
table.tabForm {
    background-color: #cedfff;
}
table.tabTituloFormSubordinada {
    background-color: #E6E6E6;
    font: 08pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
}
table.tabmenu {
    background-color: #cedfff;
}
table.tabitemtitulomenu {
    background-color: #cedfff;
    color: #333333;

    border: 0;
    border-collapse: collapse;
    padding: 0;

    font-size: 8pt;
    font-weight: bold;
    font-family: 'Trebuchet MS', verdana;
}
table.tabitemmenu {
    background-color: #cedfff;

    border: 0;
    border-collapse: collapse;
    padding: 0;

    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
}

th.consulta {
    font-family:Arial, Helvetica, sans-serif;
    background-color: #CCCCCC;
    font-size: 8pt;
    font-weight: bold;
}
th.subordinado {
    border-color:#FFF;
    background-color: #CCCCCC;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 8pt;
    font-weight: bold;
}
tr.linhaPar:not(.notSize){
    font-size: 12pt;
    font-family: 'Trebuchet MS', verdana;
}
tr.linhaPar{
    background-color: #FFF;
}
tr.linhaImpar:not(.notSize){
    font-size: pt;
    font-family: 'Trebuchet MS', verdana;
}
tr.linhaImpar{
    background-color: #EEEEEE;
}

tr.linhaImparClara{
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    background-color: #F6F6F6;
}
tr.linhaCadernoPar{
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    background-color: #F5F2F2;
    border-top-width: thin;
    border-top-style: solid;
    border-top-color: blue;

    border-bottom-width: thin;
    border-bottom-style: solid;
    border-bottom-color: blue;

}

tr.linhaParPequeno {
    font-size: 7pt;
    font-family: 'Trebuchet MS', verdana;
}
tr.linhaImparPequeno {
    font-size: 7pt;
    font-family: 'Trebuchet MS', verdana;
    background-color: #EEEEEE;
}
tr.linhaParSubordinado {
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
}
tr.linhaImparSubordinado {
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    background-color: #FFFFFF;
}
tr.linhaBranco {
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    text-align: center;
    color: #000000;
}
tr.menu {
    background-color: #E6E6E6;
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    border: 1px solid #000000;
}
tr.itemMenu {
    background-color: #FFFFFF;
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    border: 1px solid #000000;
}
td.colunaAlinhamento {
    text-align: center;
}
table.tabTituloForm {
    background-color: #CCCCCC;
    color: #333333;
    font-size: 10pt;
    font-weight: bold;
    text-align: center;
    font-family: 'Trebuchet MS', verdana;
}
.tooltip {
    background-color:'#{richSkin.generalBackgroundColor}';
    border-width:3px;
    padding:20px;
}

.titulo3 {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #0f4c6b;
}

.tituloboxcentro{
    font-family:Arial, Helvetica, sans-serif;
    font-size:16px;
    font-weight:bold;
    color:#0f4c6b;
}
.tituloDestaquePreto{
    font-family:Arial, Helvetica, sans-serif;
    font-size:16px;
    font-weight:bold;
    color:black;
}
.w30{
    width: 30%;
    text-align: left;
    vertical-align: top;
}
.w33{
    width: 33%;
    text-align: left;
    vertical-align: top;
}
.w34{
    width: 34%;
    text-align: left;
    vertical-align: top;
}
.w5{
    width: 5%;
    text-align: left;
    vertical-align: top;
}
.w3{
    width: 3%;
    text-align: left;
    vertical-align: top;
}
.w10{
    width: 10%;
    text-align: left;
    vertical-align: top;
}
.w15{
    width: 15%;
    text-align: left;
    vertical-align: top;
}
.w20{
    width: 20%;
    text-align: left;
    vertical-align: top;
}
.w25{
    width: 25%;
    text-align: left;
    vertical-align: top;
}
.w33{
    width: 33%;
    text-align: left;
    vertical-align: top;
}
.w33r{
    width: 33%;
    text-align: right;
    vertical-align: top;
}
.w30{
    width: 30%;
    text-align: left;
    vertical-align: top;
}
.w40{
    width: 40%;
    text-align: left;
    vertical-align: top;
}
.w50{
    width: 50%;
    text-align: left;
    vertical-align: top;
    padding-left: 0px; padding-right: 0px;
}
.w60{
    width: 60%;
    text-align: left;
    vertical-align: top;
}

.w70{
    width: 70%;
    text-align: left;
    vertical-align: top;
}
.w80{
    width: 80%;
    text-align: left;
    vertical-align: top;
}
.w100{
    width: 100%;
    text-align: left;
    vertical-align: top;
}
.w200{
    width: 200px;
    text-align: left;
    vertical-align: top;
}
.w240{
    width: 240px;
    text-align: left;
    vertical-align: top;
}
.semBorda{
    border: none;
}
.botoes {
}
.botoes.nvoBt {
    color: #fff;
    font-size: 12px;
    vertical-align: text-top;
    background-image: none;
    background-repeat: repeat-x;
    border-color: #fbc6b6;
    border-radius: 2px;
    background-color: #004671;
    padding: 6px 15px;
    text-decoration: none;
    border-color: transparent;
    margin: 5px 5px;
    cursor: pointer;
    vertical-align: text-top;
}
.botoes.nvoBt.btSec {
    color: #555;
    background-color: #d5d2d2;
}
.text {
    font-family:Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
}

.mensagemTelefoneNaoEncontrado {
    font: 10px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    font-weight: bold;
    color: #333333;
    text-align: right;
}
td.semBorda{
    border: none;
}

.botaoPesquisar{
    position: relative;
    top: 10px;
}
.botaoPesquisarHistoricoCons{
    position: relative;
    top: 12px;
}
.linkWiki {
    /*-moz-background-clip:border;
    -moz-background-inline-policy:continuous;
    -moz-background-origin:padding;*/
    /*background:transparent url(http://i79.photobucket.com/albums/j152/maujor/site/iconlink.gif) no-repeat scroll right center;*/
    /*background:transparent url(./imagens/wiki_link2.gif) no-repeat scroll right center;*/
    /*background:transparent url(imagens/wiki_link2.gif) no-repeat scroll right center;*/
    padding-bottom:4px;
    padding-right:2px;
    padding-top:3px;
    border:none;
}
.icon-pin-fixed {
    visibility: visible;
}
.fonteTrebuchet{
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
}

td.classColunaEsquerda {
    text-align: left;
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    padding:6px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #333;
    border-collapse: collapse;
    border-spacing: 0px;
    border: 1px solid #A9A9A9;
}
tr.linhaParAcesso {
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    border-collapse: collapse;
    border-spacing: 0px;
    border: 1px solid #A9A9A9;
}
tr.linhaImparAcesso {
    font-size: 8pt;
    font-family: 'Trebuchet MS', verdana;
    background-color: #EEEEEE;
    border-collapse: collapse;
    border-spacing: 0px;
    border: 1px solid #A9A9A9;
}
td.classPadding{
    vertical-align: middle;
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    padding:6px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    line-height:25%;
}
.red{
    color:#da2128;
    font-weight:bold;
}
.green{
    color:#009900;
    font-weight:bold;
}
.forEachStyleLinhaDataTable{
    background-color: #EEEEEE;
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    border-right: solid 1px silver;
}
td.alinhamentoTop{
    vertical-align: top;
    text-align: right;
}
.textoConfiguracoes{
    font-family:"Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
    font-size: 13px;
    text-decoration: none;
    line-height: normal;
    text-transform: none;
    color: #000000;
}
td.colunaEsquerdaConfiguracoes {
    width: 90%;
}

td.colunaDireitaConfiguracoes {
    width: 10%;
}

.numeroDestaque{
    font-family:Arial, Helvetica, sans-serif;
    font-size:12px;
    font-weight:bold;
    color:#0f4c6b;
}

.styleLegendaAcesso{
    width: 120px;
    font-family: Arial,Verdana,sans-serif;
    text-align: center;
    color: white;
    font-size: 10pt;
}
.linhaCentralizadaMeio{
    vertical-align: middle;
}
.tituloCaixaAberto{
    color: #000000;
    font-size: 10pt;
    font-family: Arial,Helvetica,sans-serif;
    font-weight:bold;
}
.tituloCamposNegritoMenor {
    font: 8pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
    font-weight: bold;
}
.tituloCamposMenor {
    font: 8pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
    font-weight: normal;
}
.tituloCampos9 {
    font: 9pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;
}
tr.linhaImparEscura{
    font-size: 8pt;
    background-color: #E6E6E6;
    font-family: 'Trebuchet MS', verdana;
    padding: 2px;
}
tr.linhaTop{
    vertical-align: top !important;
}
th.consultaCEA{
    font-family:Arial, Helvetica, sans-serif;
    background-color: #E6E6E6;
    font-size: 8pt;
    font-weight: bold;
}
td.colunaFina{
    padding: 2px;
}

.headerIndex{
    background: #ffffff; /* Old browsers */
    /* IE9 SVG, needs conditional override of 'filter' to 'none' */
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNkNmYwZmQiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -moz-linear-gradient(top,  #ffffff 0%, #d6f0fd 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#d6f0fd)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top,  #ffffff 0%,#d6f0fd 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top,  #ffffff 0%,#d6f0fd 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top,  #ffffff 0%,#d6f0fd 100%); /* IE10+ */
    background: linear-gradient(top,  #ffffff 0%,#d6f0fd 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#d6f0fd',GradientType=0 ); /* IE6-8 */
}
td.coluna{
    vertical-align: middle;
    text-align: left;
    width: 25%;
}

.paginaAtual {
    color: #ffffff;
    font: 8pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    font-weight: bold;
    text-decoration: underline;
    background-color: orange;
}

.tituloCamposReduzidos {
    font: 8pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #000000;
    text-align: right;

}

.semBordasPaginador .rich-table-cell {
    border: 0 !important;
}
/*..................estilos referentes as dicas de bi - 11/06/2015..............*/

 .container_dicas_bi {     
     background: -webkit-linear-gradient(top, #c5d8df, #ffffff);
     background: -o-linear-gradient(top, #c5d8df, #ffffff);
     background: -moz-linear-gradient(top, #c5d8df, #ffffff);
     background: linear-gradient(to top, #c5d8df, #ffffff);
 }
.container_dicas_bi thead tr {
    height: 240px !important;
}
.container_dicas_bi tbody td {
    height: 235px !important;
}
.container_texto_dicas_bi {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #333;
    display: block;
    padding: 10px 10px;
    /*text-align: justify;*/
}
.container_texto_dicas_bi_titulo {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    font-weight: bold;
    color: #0f4c6b;
    width: 100%;
    display: block;
    text-align: left;
    padding: 0px 10px;
    margin: 20px 0px 5px 0px;
}
.container_texto_dicas_bi_titulo_font {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    font-weight: bold;
    color: #0f4c6b;
}
.container_dicas_bi_dicas {
    float: left;
    border-top: 1px solid #E5E5E5;
    margin: 0px 10px 0px 10px;
}
.container_dicas_bi_botao_topo {
    width: 100%;
    display: block;
    margin: 35px 0px 35px 0px;
}
.container_texto_dicas_bi_titulo_font_topo {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    font-weight: bold;
    color: #0f4c6b;
    width: 300px;
    height: 30px;
    font-size: 14px;
    text-align: left;
    margin-left: 20px;
    margin-top: 10px;
}
.container_texto_dicas_bi_titulo_font_baixo {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    color: #0f4c6b;
    width: 300px;
    text-align: left;
    margin-left: 20px;
}
.painelTicketMedio .textoGrandeVerde{
    font-size: 1vw !important;
}
.painelTicketMedio{
    background-color: #EEEEEE; 
    width: 98%;
    font-size: 1vw;
    text-align: left;
    font-weight:normal;
    padding: 5px;
}
.fundoBranco{
    background-color: #FFFFFF !important; 
}
.azulClaro{
    color: #0078D1;
}

td.classEsquerdaUsuario {
    width: 35%;
    text-align: right;
    font-weight: bold;
    vertical-align: top;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    text-transform: none;
    color: #333;
    line-height: 200%;
}

td.classDireitaUsuario {
    width: 65%;
    text-align: left;
    vertical-align: top;
}

.classInfCadUsuario {
    color: gray;
    font-size: 8pt
}
.panelPrincipalNPS{
    margin-left: 35%;
    width: 45%;
    border: 5px solid #c4d6de;
    border-width: 0 20px 20px 20px;
    display: block;
}
.panelPrincipalNPS span{
    font-size: 18px;
    font-style: oblique;
    background: greenyellow;
 }
.corpo{
    font-size: 16px;
    margin: 5%;
    width: 75%;
    height: 80%;
    padding: 50px;
}

/* Estiliza a barra de ferramentas do upload */
.componente-nfe-upload .rich-fileupload-toolbar-decor {
    background-color: transparent;
    border-radius: 0;
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Estiliza o botão "Adicionar via XML de NFE" */
.componente-nfe-upload .tela-compra-adicionar-via-xml-btn {
    background-color: #004a99;
    color: white;
    font-weight: 600;
    font-size: 14px;
    padding: 12px 24px;
    border-radius: 8px;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

/* Hover para o botão */
.componente-nfe-upload .tela-compra-adicionar-via-xml-btn:hover {
    background-color: #003a7a;
    border-color: #ffffff88;
    transform: translateY(-2px);
    box-shadow: 0px 6px 10px rgba(0, 0, 0, 0.15);
}

/* Efeito de clique */
.componente-nfe-upload .tela-compra-adicionar-via-xml-btn:active {
    background-color: #002a5c;
    transform: translateY(0);
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}
.componente-nfe-upload .display-none {
    display: none !important;
}

.centralizar-titulo-header {
    display: block;
    text-align: center;
    padding: 10px;
    font-size: 16px;
    background-color: #002a5c; /* cor solicitada */
    color: white;
    width: 100%;
    border-radius: 4px;
}
.picklist-turmas .rich-picklist-list-content {
    width: 350px !important;
}

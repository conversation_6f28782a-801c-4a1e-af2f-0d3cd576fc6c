
.topoZW.bgtop {
    height: auto;
}

.bglateraltop {
    width: auto !important;
}

.zw_ui_topo {
    max-height: 100px;
    display: flex;
    position: fixed;
    width: 100%;
    z-index: 3;
}

.zw_ui_modulos_pesquisa.fechado-zwui, .container-imagem.container-conteudo-central.fechado-zwui {
    width: calc(100% - 85px) !important;
}

.zw_ui_modulos_pesquisa {
    justify-content: center;
    align-items: center;
    background-color: #fff;
    width: calc(100% - 267px);
    height: 78px;
    box-shadow: 2px 1px 2px 2px #9e9e9e4d;
}

.zw_ui_modulos_pesquisa.somente-pactobr {
    justify-content: flex-start;
    align-items: center;
    background-color: #fff;
    width: calc(100% - 267px);
    height: 78px;
    box-shadow: 2px 1px 2px 2px #9e9e9e4d;
}

.zw_ui_info_empresa.fechado-zwui {
    width: 18px;
}

.zw_ui_info_empresa.fechado-zwui .zw_ui_info_empresa_content {
    display: none;
}

.zw_ui_info_empresa.mini.fechado-zwui {
    width: 18px;
    display: block;
}

.zw_ui_info_empresa.mini {
    display: none;
}

.zw_ui_info_empresa {
    height: 88px;
    width: 200px;
    background-color: #1b4166;
}

.container-imagem.fechado-zwui .container-box.zw_ui.especial {
    width: calc(100% - 150px) !important;
    margin-left: 115px !important;
}

.container-imagem.fechado-zwui .container-box.zw_ui.especial-2 {
    width: calc(100% - 150px) !important;
    margin-left: 115px !important;
}

.container-imagem .container-box.zw_ui.especial-2 {
    margin-top: 50px !important;
    margin-left: 40px !important;
    width: calc(100% - 120px) !important;
}

.container-imagem .container-box.zw_ui.especial {
    margin-top: 50px !important;
    margin-left: 40px !important;
    width: calc(100% - 80px) !important;
}

.container-box.zw_ui.especial {
    width: calc(100% - 340px) !important;
}

.container-box.zw_ui.fechado-zwui.especial {
    width: calc(100% - 150px) !important;
}

.container-box.fechado-zwui {
    width: calc(100% - 170px) !important;
    margin-left: 110px !important;
}

.container-box {
    width: calc(100% - 360px) !important;
    margin-left: 300px !important;
    margin-top: 150px !important;
}

.container-imagem.fechado-zwui {
    width: calc(100% - 0px) !important;
    margin-left: 0px !important;
}

.container-imagem {
    overflow: auto;
    width: calc(100% - 266px) !important;
    margin-left: 0px !important;
    margin-top: 78px !important;
    height: calc(100vh - 78px);
}

.carousel-inner .item.active a img {
    width: 100% !important;
    max-width: 100% !important;
}

.zw_ui_logo_pacto {
    text-align: center;
    height: 88px;
    flex-shrink: 0;
    flex-direction: column;
    align-items: center;
    width: 64px;
    background-color: #1b4166;
    border-right: 2px solid #163655;
}

.main-logo {
    width: 40px;
    height: 40px;
    margin: 24px 0;
    border-radius: 50%;
}

body {
    /*background-color: #eff2f7 !important;*/
}

.zw_ui_info_empresa_content {
    padding: 24px 16px;
    display: flex;
}

.logo_empresa_zw_ui {
    width: 40px;
    height: 40px;
    background-color: #fff;
    background-position: center;
    background-size: contain;
    flex-shrink: 0;
}

.unidade-aux {
    flex-basis: 104px;
    overflow: hidden;
    flex-grow: 1;
}

.zw_ui_nome_empresa {
    color: #4387b5;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    text-overflow: ellipsis;
    font-family: 'Nunito Sans', sans-serif;
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
}

.zw_ui_trocar_unidade i {
    padding-left: 5px;
}

.zw_ui_trocar_unidade {
    color: #adcce1;
    cursor: pointer;
    display: flex;
    width: 100%;
    align-items: center;
    font-weight: 400;
    font-size: 12px;
    line-height: 1.5;
    font-family: 'Nunito Sans', sans-serif;
}

.zw_ui_opcoes_modulos_laterais {
    height: calc(100vh - 88px);
    flex-shrink: 0;
    text-align: center;
    flex-direction: column;
    align-items: center;
    width: 64px;
    background-color: #1b4166;
    border-right: 2px solid #163655;
    margin-top: 88px;
}

.pacto-primary {
    background-color: #1998fc;
    border: 1px solid #1998fc;
    color: #fff;
}

.pacto-button {
    border: 0;
    padding: 0;
    box-shadow: none;
    text-transform: uppercase;
    border-radius: 4px;
    line-height: 32px;
    cursor: pointer;
    position: relative;
    outline: 0;
}

.pacto-button, .type-btn-bold {
    font-family: 'Nunito Sans', sans-serif;
    font-weight: 600;
    font-size: 12px;
    color: #51555a;
}

.container-zw-ui .button-edit {
    padding-bottom: 12px;
    display: flex;
    justify-content: center;
}

.zw_ui_opcoes_laterais.fechado-zwui {
    display: none;
}

.zw_ui_opcoes_laterais {
    height: 100vh;
    width: 200px;
    background-color: #1b4166;
    margin-top: 88px;
    overflow-x: hidden;
    overflow-y: auto;
}

.zw_ui_modulo_atual {
    justify-content: center;
    cursor: pointer;
}

.zw_ui_modulo_atual, .zw_ui_modulo_atual_descricao {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px;
    line-height: 86%;
    color: #fff;
    font-size: 24px;
    height: 40px;
    width: 48px;
    display: flex;
    align-items: center;
}

.modules-wrapper {
    background-color: #163655;
    width: 26px;
    padding: 18px 7px;
    border-radius: 20px;
    margin-bottom: 20px;
}

.zw_ui_modulo_atual_descricao.CONFIG, .zw_ui_modulo_atual.CONFIG, .zw_ui_menu-toggle.menuOpen.openmenu-zwui.CONFIG {
    background-color: #638BE9 !important;
}

.zw_ui_modulo_atual_descricao.ZW, .zw_ui_modulo_atual.ZW, .zw_ui_menu-toggle.menuOpen.openmenu-zwui.ZW {
    background-color: #1998FC;
}

.zw_ui_modulo_atual_descricao.CANAL, .zw_ui_modulo_atual.CANAL, .zw_ui_menu-toggle.menuOpen.openmenu-zwui.CANAL {
    background-color: #1998FC;
}

.zw_ui_modulo_atual_descricao.CRM, .zw_ui_modulo_atual.CRM, .zw_ui_menu-toggle.menuOpen.openmenu-zwui.CRM {
    background-color: #f6b817;
}

.zw_ui_modulo_atual_descricao.FIN, .zw_ui_modulo_atual.FIN, .zw_ui_menu-toggle.menuOpen.openmenu-zwui.FIN {
    background-color: #00c350;
}

.zw_ui_modulo_atual_descricao.NOTAS, .zw_ui_modulo_atual.NOTAS, .zw_ui_menu-toggle.menuOpen.openmenu-zwui.NOTAS {
    background-color: rgb(95, 99, 105);
}

.zw_ui_modulo_atual_descricao.PES, .zw_ui_modulo_atual.PES, .zw_ui_menu-toggle.menuOpen.openmenu-zwui.PES {
    background-color: #638BE9;
}

.zw_ui_modulo_atual_descricao {
    width: calc(100% - 16px);
}

.upper-section {
    padding-top: 14px;
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    align-items: center;
}

.zw_ui_menu-toggle.menuOpen {
    background-color: #1b4166 !important;
}

.zw_ui_menu-toggle {
    width: 24px;
    height: 32px;
    color: #fff;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    cursor: pointer;
    right: -10px;
    border-radius: 4px;
    top: 100px;
    background-color: #1998FC;
    z-index: 12;
}

.module-name {
    color: #fff;
    margin-right: 24px;
    padding-left: 24px;
    overflow: hidden;
    text-decoration: none;
    font-size: 16px;
    font-weight: 900;
    text-transform: capitalize;
}

.zw_ui_modulo_atual a i {
    cursor: pointer;
    color: #fff;
    font-size: 24px;
}

.icon-height {
    line-height: 1.68;
}

a.icon-module {
    margin-bottom: 14px;
    text-decoration: none;
    display: block;
    color: #fff;
    font-size: 20px;
}

a.icon-module:hover {
    color: #5F9BC4;
}

a.icon-module.selected {
    color: #1998FC;
}

.inner-circle {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0);
    pointer-events: none;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border: 2px solid #fff;
}

.grupoMenuTitulo {
    display: none;
}

.grupoMenuItem.grupoMenuItemContainer:hover {
    background-color: #2e5d7c;
    color: inherit;
    text-decoration: none;
}

.grupoMenuItem.grupoMenuItemContainer a {
    flex-grow: 1;
    font-family: 'Nunito Sans', sans-serif;
    color: #4387b5;
    font-size: 14px;
    font-weight: 600;
    padding-right: 4px;
    text-decoration: none;
    white-space: normal !important;
}

.grupoMenuItem.grupoMenuItemContainer {
    display: block;
    padding: 10px 10px;
    border-bottom: 1px solid #2e5d7c;
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.grupoMenuItemNomeContainer i {
    color: #4387b5;
}

.grupoMenuItemNomeContainer {
    white-space: normal;
!important;
}

.submenu-zwui {
    display: none !important;
}

.opensubmenu .submenu-zwui {
    display: flex !important;
}

.voltar-zwui a, .voltar-zwui a:hover, .voltar-zwui a:visited {
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #fff;
    background-color: #0f253b;
    font-size: 16px;
    padding: 10px 24px;
    width: 100%;
    line-height: 24px;
    font-family: 'Nunito Sans', sans-serif;
    text-decoration: none;
}

.voltar-zwui i {
    font-size: 24px;
    padding-right: 8px;
    display: flex;
    color: #fff;
}

.openmenu-zwui.fechado-zwui {
    display: flex;
}

.zw_ui_opcoes_laterais.mini.fechado-zwui {
    display: block;
    width: 18px;
}

.zw_ui_opcoes_laterais.mini {
    display: none;
}

.rodape {
    border-top: 1px solid #2e5d7c;
    color: #b4b7bb;
    position: absolute;
    padding-bottom: 100px;
    width: 200px;
    height: 120px;
}

.rodape .system-stat {
    line-height: 22px;
    font-size: 12px;
    white-space: nowrap;
}

.rodape .system-stat i {
    padding-right: 4px;
}

.menus {
    flex-grow: 1;
    overflow: auto;
    height: calc(100vh - 337px);
}

.suggestionCampoBusca .rich-sb-ext-decor-3 {
    background-color: #ffffff !important;
}

.zw_ui_menu a:hover, .modal-content-usuario-zw-ui a:hover, .zw_ui_opcoes_modulos_laterais a:hover {
    text-decoration: none !important;
}

.zw_ui_modulos_pesquisa {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}

.zw_ui_modulos_pesquisa.somente-pactobr {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-shrink: 0;
}

.zw_ui_modulos_pesquisa .aux {
    display: flex;
    align-items: center;
    max-width: 1196px;
    width: calc(100vw - 324px);
}

.zw_ui_modulos_pesquisa.somente-pactobr .aux {
    display: flex;
    align-items: center;
    max-width: 1444px;
    width: calc(100vw - 324px);
}

.modulo-opener {
    display: flex;
    align-items: center;
    color: #a1a5aa;
    cursor: pointer;
    font-size: 14px;
    font-family: 'Nunito Sans', sans-serif;
}

.modulo-opener i {
    color: #1b4166;
    margin-right: 8px;
}

.modulo-opener.somente-pactobr i {
    color: #1b4166;
    padding-right: 14px;
    font-size: 24px;
    margin-right: unset;
}

.zw_pesquisa_global input {
    width: 100%;
    color: #51555a;
    outline: unset;
    padding: unset;
    border: 0;
    font-size: 14px;
    font-family: 'Nunito Sans', sans-serif;
    font-weight: 400;
    min-height: 26px;
    background-image: none;
}

.zw_pesquisa_global .pct-search {
    color: #a1a5aa;
    margin-right: 8px;
}

.zw_pesquisa_global {
    width: 100%;
    border: 1px solid #dcdddf;
    max-width: 600px;
    min-width: 135px;
    display: flex;
    position: relative;
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;
    align-items: center;
}

.zw_pesquisa_global.somente-pactobr input {
    width: 100%;
    min-width: 135px;
    border: 0;
    color: #a1a5aa;
    padding: 6px 30px;
    border-bottom: 1px solid #c7c9cc;
    font-family: 'Nunito Sans', sans-serif;
    font-weight: 400;
    font-size: 16px;
    background-image: none;
}

.zw_pesquisa_global.somente-pactobr .pct-search {
    position: absolute;
    left: 0;
    top: 6px;
    color: #a1a5aa;
    font-size: 20px;
}

.zw_pesquisa_global.somente-pactobr {
    width: 100%;
    border: unset;
    max-width: 520px;
    display: block;
    position: relative;
    font-size: unset;
    padding: unset;
    border-radius: unset;
    align-items: unset;
}

.modulos-choose {
    display: block;
    margin-right: 100px;
    font-size: 14px;
    padding: 12px;
    font-weight: 700;
    cursor: pointer;
}

.modulos-choose.somente-pactobr {
    display: block;
    margin-right: 80px;
    font-size: 14px;
    z-index: 10;
    padding: unset;
    font-weight: unset;
    margin-left: 25px;
}

.zw_ui_pacto-user {
    display: block;
    position: relative;
    width: 45%;
    margin-left: 83px;
}

.section-menu .central {
    background-color: #eff2f7;
    width: 40px;
    height: 40px;
    border-radius: 20px;
    display: flex;
    margin-right: 10px;
    cursor: pointer;
}

.section-menu .central i {
    display: flex;
    font-size: 28px;
    margin-left: 5.5px;
    margin-top: 5.5px;
    color: #1b4166;
}

.aberto-usuario-zw-ui.section-menu .background {
    background-color: #25598b;
}

.section-menu .background {
    background-color: #eff2f7;
    width: 72px;
    height: 40px;
    border-radius: 52px;
    display: flex;
}

.section-menu .background .user-section {
    display: inline-flex;
    align-items: center;
    padding-bottom: 20px;
    padding-left: 6px;
    cursor: pointer;
}

.section-menu .background .user-section .user-menu {
    width: 32px;
    height: 32px;
    margin: 32px 0 12px;
    border-radius: 18px;
    border: .1em solid #1b4166;
    background-color: #0380e3;
    overflow: hidden;
}

.section-menu.aberto-usuario-zw-ui .background .user-section i.pct-chevron-up {
    display: block;
    color: #ffffff;
}

.section-menu .background .user-section i.pct-chevron-up,
.section-menu.aberto-usuario-zw-ui .background .user-section i.pct-chevron-down {
    display: none;
}

.section-menu .background .user-section i {
    margin-top: 40%;
    margin-left: 4px;
    font-size: larger;
    color: #25598b;
}

.section-menu {
    display: flex;
    width: 100%;
    justify-content: flex-end;
    align-items: center;
}

.caixa-modulos-zw-ui.aberto {
    height: calc(90vh - 78px);
    transition: height .5s;
}

.caixa-modulos-zw-ui.fechado-zwui {
    left: 110px;
}

.caixa-modulos-zw-ui {
    display: flex;
    justify-content: center;
    background-color: #fff;
    box-shadow: 2px 1px 2px 2px #9e9e9e4d;
    overflow-y: auto;
    position: absolute;
    top: 78px;
    left: 294px;
    right: 0;
    height: 0;
    width: calc(100vw - 318px);
    transition: height .3s;
    z-index: 99
}

.caixa-modulos-zw-ui .aux {
    width: 1196px;
    padding: 24px;
    height: fit-content;
    box-sizing: border-box;
}

.caixa-modulos-zw-ui.somente-pactobr {
    justify-content: flex-start;
    padding: 0px 0px 0px 25px;
    background-color: #fff;
    box-shadow: 2px 1px 2px 2px #9e9e9e4d;
    overflow-y: auto;
    position: absolute;
    top: 88px;
    left: 294px;
    right: 0;
    height: 0;
    width: calc(100vw - 318px);
    transition: height .3s;
}

.caixa-modulos-zw-ui.somente-pactobr.aberto {
    display: flex;
    height: calc(100vh - 160px);
    transition: height .5s;
}

.caixa-modulos-zw-ui.somente-pactobr .aux {
    max-width: 1920px;
    padding: unset;
    width: calc(100vw - 324px);
    grid-template-columns: 0fr 2fr;
    display: grid;
    gap: 30px;
}

.modulo-menu-list {
    margin-right: 30px;
}

.modulo-opener {
    font-weight: 700;
    font-size: 14px;
    align-items: baseline;
    color: #1B4166;
    line-height: 1;
}

.modulo-opener.somente-pactobr {
    font-weight: 700;
    font-size: 14px;
    align-items: center;
    line-height: 100%;
    color: #1B4166;
}

.caixa-modulos-zw-ui .modulos-disponiveis {
    margin-bottom: 5px;
    width: 220px;
}

.caixa-modulos-zw-ui.somente-pactobr .modulos-disponiveis {
    margin-bottom: 5px;
    width: auto;
    margin-right: 30px;
}

.section-title {
    color: #a1a5aa;
    margin-bottom: 5px;
    font-family: 'Nunito Sans', sans-serif;
    font-size: 16px;
    font-weight: 400;
}

.modulos-list .modulo .img-aux {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    margin-right: 16px;
}

.modulos-list .modulo .nome {
    color: #51555a;
    font-family: 'Nunito Sans', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 125%;
}

.modulos-list .modulo {
    display: flex;
    align-items: center;
    padding: 12px;
    cursor: pointer;
    text-decoration: none;
    min-width: 220px;
    background-color: #FAFAFA;
    margin-top: 16px;
    height: 32px;
}

.modulos-list .modulo img {
    width: 32px;
    height: 32px;
}

.modulos-list .modulo.current, .modulos-list .modulo:hover {
    background-color: #C7C9CC;
}

.modulo-opener.aberto .pct-menu {
    display: none;
}

.modulo-opener.aberto .pct-grid {
    display: none;
}

.modulo-opener.aberto .pct-x {
    display: block;
}

.modulo-opener .pct-x {
    display: none;
}

.modal-usuario-zw-ui.modal-open, .modal-ponto-interrogacao.modal-open, .modal-notificacoes-versoes.modal-open, icoMsg.modal-open{
    display: block;
    font-family: 'Nunito Sans', sans-serif;
}

.modal-usuario-zw-ui, .modal-ponto-interrogacao, .modal-notificacoes-versoes {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1100;
    display: none;
    background: rgba(0, 0, 0, 0.5);
    overflow: hidden;
    outline: 0;
}

.balloon-modal .modal-content {
    background-color: transparent;
    border: 0px;
}

.modal-content-usuario-zw-ui {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #ffffff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
}

.pacto-button {
    border: 0;
    padding: 0;
    box-shadow: none;
    text-transform: uppercase;
    border-radius: 4px;
    line-height: 32px;
    cursor: pointer;
    position: relative;
    outline: 0;
}

.pacto-button.pacto-outline {
    background-color: transparent;
    border: 1px solid #1998fc;
    color: #1998fc;
}

.pacto-button, .type-btn-bold {
    font-family: 'Nunito Sans', sans-serif;
    font-weight: 600;
    font-size: 12px;
    color: #51555a;
}

.modal-ponto-interrogacao .blue-container {
    width: calc(100% - 32px);
    height: 40px;
    line-height: 40px;
    color: #fff;
    font-size: 16px;
    background-color: #0380E3;
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.modal-ponto-interrogacao .modal-content-usuario-zw-ui, .modal-notificacoes-versoes {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.modal-ponto-interrogacao .blue-triangle {
    margin-left: 342px;
}

.blue-triangle {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #25598b;
    margin-left: 142px;
    margin-top: -9px;
}

.container-zw-ui .row-padding {
    padding-top: 16px;
}

.blue-container {
    width: 100%;
    height: 8px;
    background-color: #25598b;
}

.modal-conteudo.menu-usuario {
    width: 300px;
}

.menu-usuario-acao {
    width: 268px;
}

.button-sair {
    gap: 8px;
    background: #FFFFFF;
    border: 1px solid #0380E3;
    border-radius: 4px;
    width: 268px;
    height: 32px;
    font-weight: 600;
    font-size: 14px;
    line-height: 100%;
    color: #0380E3;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-family: 'Nunito Sans',sans-serif;
}

.button-sair > i {
    width: 14px;
}

.modal-ponto-interrogacao .modal-conteudo .container-zw-ui {
    max-height: 619px;
    min-height: 300px;
}

.modal-ponto-interrogacao .blue-container i {
    margin-top: 12px;
    cursor: pointer;
}

.modal-conteudo .container-zw-ui {
    border-radius: 0 0 4px 4px;
    background-color: #fff;
}

.row-user-zw-ui {
    display: flex;
    flex-wrap: wrap;
    margin-right: 15px;
    margin-left: 15px;
    align-items: center;
    height: 70px;
}

.row-user-zw-ui.row-action {
    height: 48px;
}

.menu-usuario.menu-usuario-acao {
    display: flex;
    justify-content: space-between;
    background-color: #FFFFFF;
    color: #383B3E;
    font-weight: 700;
    font-size: 14px;
    line-height: 100%;
    text-transform: none;
}

.row-user-zw-ui.menu-usuario {
    flex-wrap: nowrap;
    border-bottom: 1px solid #EFF2F7;
    display: flex;
    align-items: center;
}


.col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
}

.row-qr-code-zw-ui {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
    max-height: 145px;
}

.user-menu {
    display: flex;
    width: 64px;
    height: 64px;
    margin: 16px auto;
    border-radius: 32px;
    border: .1em solid #1b4166;
    background-color: #0380e3;
    overflow: hidden;
}

.menu-usuario-titulo {
    font-style: normal;
    color: #51555A;
    display: flex;
    flex-direction: column;
    padding-left: 8px;
}

.menu-usuario-titulo > .menu-usuario-nome {
    font-weight: 700;
    font-size: 16px;
    line-height: 100%;
    width: 14em;
}

.menu-usuario-titulo > .menu-usuario-perfil {
    font-weight: 400;
    font-size: 14px;
    line-height: 125%;
    width: 15.9em;
}


.menu-usuario-logo {
    display: flex;
    width: 32px;
    height: 32px;
    margin: 16px auto;
    border-radius: 32px;
    border: .1em solid #B4B7BB;
    overflow: hidden;
    background: #B4B7BB;
}

.balloon-modal .modal-dialog {
    margin-right: 100vw;
    z-index: 1100;
    margin-top: 80px;
    width: 170px;
    left: calc(100vw - 212px);
}

.modal-ponto-interrogacao.balloon-modal .modal-dialog {
    margin-right: 0;
    z-index: 1100;
    margin-top: 80px;
    width: 452px;
    left: 1000px;
    position: absolute;
    top: 0;
}

.central a:link,
.central a:hover,
.central a:visited,
.central a:active {
    text-decoration: none;
}

.modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

.row-user-zw-ui .text-user {
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
    text-align: center;
    color: #25598b;
    font-weight: 600;
    line-height: 135%;
}

.text-function {
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    text-align: center;
}

.col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
}

.modal.show .modal-dialog {
    transform: translate(0, 0);
}

.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -25%);
}

.modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

.button-log-out .pacto-button .content {
    color: #25598b;
}

.container-zw-ui .button-log-out {
    padding-bottom: 24px;
    display: flex;
    justify-content: center;
}

.pacto-button .content {
    padding: 0 10px;
    text-decoration: none;
    color: #fff;
}
.container .option {
    margin: 8px 0;
    display: flex;
    cursor: pointer;
    max-height: 30px;
}

.container .option .option-img img {
    width: 100%;
    max-height:30px;
    padding: 3px;
}

.container .option span {
    color: #25598b;
    font-weight: 400;
    font-size: 13px;
    padding-top: 3px;
    padding-left: 14px;
}

.container .option {
    margin: 8px 23px;
    display: flex;
    cursor: pointer;
}

body.zw_ui_not_hidden {
    overflow: auto !important;
}

.open > .dropdown-border-novo {
    background-color: #1B4166;
    z-index: 10;
    -webkit-transition: all .2s;
    -moz-transition: all .2s;
    -ms-transition: all .2s;
    -o-transition: all .2s;
    transition: all .2s;
}

.dropdown-border-novo {
    display: inline-block;
}

.textMenuEmpresas {
    font-family: 'Nunito Sans', sans-serif;
    font-size: 16px;
    color: #4387B5 !important;
    text-decoration: none !important;
}

.dropDownMenuNovo ul {
    position: absolute;
    background-color: #1B4166;
    list-style: none;
    border-top: 1px solid rgb(174, 187, 188);
    border-right: 1px solid rgb(174, 187, 188);
    border-left: 1px solid rgb(174, 187, 188);
    border-bottom: 1px solid rgb(174, 187, 188);
    transition: 1s;
    -webkit-transition: opacity 1s linear;
    -moz-transition: opacity 1s linear;;
    margin-top: 0px;
    padding-left: 0px;
    box-shadow: 0 3px 2px 0 rgba(0, 0, 0, 0.2), 0 3px 9px 0 rgba(0, 0, 0, 0.19);
}

.dropDownMenuNovo ul {
    margin-left: 0px !important;
    top: initial !important;
    right: initial !important;
}

.caixa-pesquisa-troca-empresa {
    width: 100%;
    border-radius: 3px;
    border: 1px solid #bcbfc7;
    padding: 0px 30px 0px 10px;
    line-height: 30px;
    color: #92959b;
    outline: 0px !important;
    font-family: 'Nunito Sans', sans-serif;
    font-weight: 400;
    font-size: 14px;
    overflow: visible;
    background-color: #000a28;
(rgb(255, 255, 255), rgb(59, 59, 59));
    margin-bottom: 12px;
}

.form-search-zw {
    width: 100%;
}

.titulo-troca-empresa {
    color: #51555a;
    line-height: 45px;
    font-family: 'Nunito Sans', sans-serif;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    text-align: left;
    width: auto;
}

.titulo-troca-empresa:hover {
    background-color: #F3F3F4;
}

.botao-fechar-modal-troca-empresa {
    position: absolute;
    text-align: center;
    border-radius: 4px;
    line-height: 32px;
    outline-width: 5px;
    cursor: pointer;
    font-family: 'Nunito Sans';
    border-width: medium;
    border-style: solid;
    padding: 0 10px;
    border-color: #3798FC;
    font-size: 12px;
    border-top-color: rgb(25, 152, 252);
    border-top-style: solid;
    border-top-width: 1px;
    border-right-color: rgb(25, 152, 252);
    border-right-style: solid;
    border-right-width: 1px;
    border-bottom-color: rgb(25, 152, 252);
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-left-color: rgb(25, 152, 252);
    border-left-style: solid;
    border-left-width: 1px;
    border-image-source: initial;
    border-image-slice: initial;
    border-image-width: initial;
    border-image-outset: initial;
    border-image-repeat: initial;
}

.separador-linha-troca-empresa {
    border-top: 1px solid #eff2f7;
}

.modal-app-gestor {
    position: relative;
    display: flex;
    padding: 0;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: 0.3rem;
    outline: 0;
}

.modal-app-gestor .modal-titulo {
    position: relative;
    line-height: 20px;
    color: #51555a;
    border-bottom: 1px solid #dcdddf !important;
    margin-left: -11px;
    margin-right: -11px;
    font-family: 'Nunito Sans', sans-serif;
    font-size: 20px;
    font-weight: 400;
}

.modal-app-gestor .close-modal {
    position: absolute;
    right: 26px;
    top: 26px;
    font-size: 20px;
    color: #51555a;
    cursor: pointer;
}

.titulo-modal-app-gestor {
    padding-left: 21px;
    padding-right: 21px;
    padding-top: 19px;
    padding-bottom: 30px;
    position: relative;
    line-height: 20px;
    color: #51555a;
    font-family: 'Nunito Sans', sans-serif;
    font-size: 20px;
    font-weight: 400;
}

.container-acesso {
    margin: 18px 11px;
}

.text-app-gestor {
    margin-left: -1px;
    text-align: justify;
    margin-bottom: 32px;
    position: relative;
    color: #67757c;
    font-weight: 300;
    display: flex;
    flex-direction: column;
    font-size: 0.9rem;
    line-height: 1.5;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    font-family: "Nunito Sans", sans-serif;
}

.rotulo-campos-app-gestor {
    width: 100%;
    font-weight: 600;
    display: inline-block;
    font-family: "Nunito Sans", sans-serif;
    font-size: 1rem;
    line-height: 1.5;
    text-align: left;
    color: #67757c;
}

.botaoGeraQRCODE {
    width: 35%;
    background-color: #1998fc !important;
    border: 1px solid #1998fc !important;
    color: #fff !important;
    padding: 0;
    box-shadow: none;
    text-transform: uppercase !important;
    border-radius: 4px;
    line-height: 32px;
    cursor: pointer;
    position: relative;
    outline: 0;
    font-family: 'Nunito Sans', sans-serif !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    background-image: none !important;
}

.QR-code-zw-ui {
    align-content: center;
    align-items: center;
    text-align: center;
    top: 50%;
    left: 50%;
    margin-top: -50px;
}

.span-menu-qr-code {
    font-weight: 300;
    font-size: 13px;
}

.caixa-pesquisa-troca-empresa:focus {
    box-shadow: 0 0 0 0.1rem rgb(25 152 252 / 50%);
}

.ponto-interrogacao::-webkit-scrollbar-track {
    background: #ffffff;
}

.ponto-interrogacao::-webkit-scrollbar-thumb {
    background-color: #c7c9cc;
    border-radius: 10px;
    border: 3px solid #ffffff;
}

.ponto-interrogacao::-webkit-scrollbar {
    width: 12px;
}

.ponto-interrogacao {
    scrollbar-width: auto;
    scrollbar-color: #c7c9cc #ffffff;
}

.ponto-interrogacao {
    overflow-y: auto;
    scrollbar-width: none;
    color: #38383e;
    background-color: #fff;
    padding: 16px;
}

.ponto-interrogacao .fake-input {
    width: 0px;
    height: 0px;
    opacity: 0;
    margin: 0;
    padding: 0;
    min-height: 0;
}

.ponto-interrogacao .video.interno{
    background-color: #ffedde;
    padding: 10px 0;
    border-radius: 5px;
}
.ponto-interrogacao .video {
    cursor: pointer;
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin-bottom: 16px;
}

 .ponto-interrogacao .video.interno .texto-interno{
     display: block;
     font-size: 8px;
     text-align: left;
     margin-top: 10px;
     margin-left: 10px;
 }

.ponto-interrogacao .video img {
    width: 180px;
    border-radius: 4px;
}

.ponto-interrogacao .video div {
    margin-left: 12px;
}

.ponto-interrogacao .video div .subtitle {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: #797D86;
}

.ponto-interrogacao .video div .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 8px;
    display: block;
    color: #55585E;
}

.ponto-interrogacao .artigos {
    display: none;
}

.ponto-interrogacao .aprendizado {
    border-bottom: 1px solid #c7c9cc;
    padding: 16px 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;

}

.ponto-interrogacao .aprendizado.util {
    border-bottom: none;
}

.ponto-interrogacao .aprendizado.util .title {
    display: block;
    text-align: center;
}

.ponto-interrogacao .aprendizado.util .title .sim_nao {
    margin-right: 10px;
}

.ponto-interrogacao .aprendizado.util .title .sim_nao.desmarcado {
    opacity: 0.5;
}

.ponto-interrogacao .aprendizado.util .title span, .ponto-interrogacao .aprendizado.util .title i {
    cursor: pointer;
}

.ponto-interrogacao .aprendizado.util .title .pct-thumbs-down {
    color: #9a1b27;
}

.ponto-interrogacao .aprendizado.util .title .pct-thumbs-up {
    color: #026abc;
    margin-right: 16px;
}

.ponto-interrogacao .aprendizado .link-ui {
    line-height: 21px;
    margin-bottom: 16px;
}

.ponto-interrogacao .aprendizado .link-ui.interno {
    background-color: #ffedde;
    padding: 10px;
    border-radius: 5px;
}
.ponto-interrogacao .aprendizado .link-ui.interno .texto-interno{
    display: block;
    font-size: 8px;
    float: right;
}
.ponto-interrogacao .aprendizado .link-ui.icone-pi {
    display: flex;
    font-size: 16px;
    font-weight: 400;
    line-height: 16px;
    color: #204c77;
    margin-right: 8px;
}

#idpontointerrogacao .pct-help-circle {
    font-size: 24px;
}

.hint-pi::before{
    font-family: 'Pacto-Icons-Fonts'!important;
    content: "\e9d7";
    color: #204c77;
}

.hint-pi{
    display: inline !important;
    margin: 0 !important;
    position: relative;
}
.hint-pi .tip-direita{
    top: -8px;
    left: 30px;
}

.hint-pi:hover .tooltip-pi {
    display: block;
}

.tooltip-pi {
    display: none;
    z-index: 999999;
    min-width: 160px;
    position: absolute;
    font-size: 14px;
    background-color: #ffffff;
    border: 1px solid #a1a5aa;
    border-radius: 6px;
    -moz-border-radius: 6px;
    -webkit-border-radius: 6px;
    box-shadow: 0px 0px 8px -1px #a1a5aa;
    -moz-box-shadow: 0px 0px 8px -1px #a1a5aa;
    -webkit-box-shadow: 0px 0px 8px -1px #a1a5aa;
}
.tooltip-pi .verMais i{
    margin-left: 3px;
    font-size: 15px;
    margin-top: 2px;
}
.tooltip-pi span {
    padding: 10px;
    padding-bottom: 0;
    margin: 0 !important;
    display: block;
    min-height: 10px;
    text-transform: none;
}
.tooltip-pi .verMais span{
    text-decoration: underline;
    padding: 0;
}
.tailShadow {
    background-color: transparent;
    width: 4px;
    height: 4px;
    position: absolute;
    top: 16px;
    left: -8px;
    z-index: -10;
    box-shadow: 0px 0px 8px 1px #a1a5aa;
    -moz-box-shadow: 0px 0px 8px 1px #a1a5aa;
    -webkit-box-shadow: 0px 0px 8px 1px #a1a5aa;
}
.tail1 {
    width: 0px;
    height: 0px;
    border: 10px solid;
    border-color: transparent #a1a5aa transparent transparent;
    position: absolute;
    top: 8px;
    left: -20px;
}

.tail2 {
    width: 0px;
    height: 0px;
    border: 10px solid;
    border-color: transparent #ffffff transparent transparent;
    position: absolute;
    left: -18px;
    top: 8px;
}
.tooltip-pi .verMais{
    padding: 10px;
    padding-top: 0;
    display: flex;
    color: #51555a;
    cursor: pointer;
    text-transform: none;
}

.ponto-interrogacao .aprendizado .link-ui.icone i {
    margin-right: 8px;
}

.ponto-interrogacao .aprendizado .link-ui.icone a {
    text-decoration-line: none;
}

.ponto-interrogacao .aprendizado .link-ui .vermais {
    font-weight: 600;
    margin-top: 8px;
    margin-bottom: 0;
}

.ponto-interrogacao .ver-mais {
    font-weight: 600;
    margin-top: 8px;
    margin-bottom: 0;
    cursor: pointer;
    color: #204C77!important;
    font-size: 12px;
    font-style: normal;
    line-height: 100%;
    letter-spacing: 0.25px;
}

.ponto-interrogacao .aprendizado .link-ui a {
    color: #1E60FA;
    text-decoration-line: none;
    cursor: pointer;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 100%;
}

.ponto-interrogacao .link-ajuda {
    margin-bottom: 16px;
}

.ponto-interrogacao .precisa-ajuda i {
    color: #026ABC;
    margin-right: 8px;
}

.ponto-interrogacao .precisa-ajuda .link-ajuda a {
    color: #383B3E;
    text-align: center;
    font-family: Nunito Sans;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%;
    cursor: pointer;
}

.ponto-interrogacao .aprendizado .title {
    margin-bottom: 14px;
    display: flex;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
}

.ponto-interrogacao textarea {
    box-sizing: border-box;
    outline: 0;
    margin: 0;
    font-family: inherit;
    overflow: auto;
    resize: none;
    width: 100%;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem;
    color: #67757c;
    display: initial;
    min-height: 20px;
}

.ponto-interrogacao .aprendizado .title i {
    margin-left: 8px;
}

.ponto-interrogacao .aprendizado .acao {
    margin-top: 16px;
    display: block;
    text-align: right;
}

.pct-telemarketing:before {
    content: "\ea56";
}

#video-selecionado {
    position: absolute;
    top: calc(50vh - 300px);
    left: calc(50vw - 320px);
}

.button-zw-ui {
    pointer-events: auto;
    box-sizing: border-box;
    margin: 0;
    overflow: visible;
    text-transform: none;
    -webkit-appearance: button;
    font-family: 'Nunito Sans', sans-serif;
    font-weight: 600;
    font-size: 12px;
    padding: 0 15px;
    display: inline;
    box-shadow: none;
    border-radius: 4px;
    line-height: 32px;
    cursor: pointer;
    position: relative;
    outline: 0;
    background-color: #0380e3;
    border: 1px solid #1998fc;
    color: #fff;
}

.topbar-menu-item {
    font-size: 20px;
    color: #1b4166;
    cursor: pointer;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.topbar-menu-item:hover, .topbar-menu-item.topbar-item-selected {
    color: #0380e3;
}

.topbar-menu-item a {
    font-size: inherit;
    color: inherit;
    text-decoration: none;
}

.topbar-menu-item a:hover {
    font-size: inherit;
    color: inherit;
    text-decoration: none;
}

.topbar-actions-divider {
    margin-right: 16px;
    border-left: 1px solid #b4b7bb;
    height: 24px;
    align-self: center;
}

.topbar-menu-item .user-menu-novo {
    border-radius: 50%;
}

.topbar-menu-item .user-menu-novo img {
    border-radius: inherit;
}

.menu-explorar-column {
    width: 220px;
    font-family: 'Nunito Sans', sans-serif;
}

.menu-explorar-column-name {
    color: #51555a;
    font-weight: 700;
    margin-bottom: 16px;
    font-size: 16px;
    line-height: 16px;
    font-family: 'Nunito Sans', sans-serif;
}

.menu-explorar-column-submenu-name {
    color: #51555a;
    font-weight: 700;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 14px;
    padding: 8px 12px 8px 0;
}

.menu-explorar-item {
    background: #FAFAFA;
    padding: 8px 12px 8px 16px;
    border-radius: 4px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
    text-decoration: none;
    color: #80858c;
    font-weight: 600;
    font-family: 'Nunito Sans', sans-serif;
}

.menu-explorar-column a.menu-explorar-item {
    font-family: 'Nunito Sans', sans-serif;
    text-decoration: none;
    color: #80858c;
}

.menu-explorar-column .menu-explorar-item .img-aux {
    margin-right: 8px;
    height: 18px;
    width: 18px;
}

.menu-explorar-column .menu-explorar-item .img-aux img {
    height: inherit;
    width: inherit;
}

.menu-explorar-column .menu-explorar-item {
    line-height: 1.25;
    font-size: 14px;
}

.menu-explorar-column .menu-explorar-item.selected {
    color: #1998fc;
    background: #EFF8FF;
}

.grid {
    display: grid;
    gap: 12px;
}

.grid-2c {
    grid-template-columns: 220px calc(100% - 220px);
}

.grid-4c {
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

@media screen and (max-width: 1281px) {
    .caixa-modulos-zw-ui {
        justify-content: unset;
    }

    .caixa-modulos-zw-ui.fechado-zwui {
        overflow: auto;
    }

    .caixa-modulos-zw-ui .grid.grid-4c {
        height: calc(90vh - (48px + 84px)) !important;
    }

    .caixa-modulos-zw-ui .aux {
        min-width: 1280px;
    }

    .caixa-modulos-zw-ui .modulos-disponiveis {
        width: 190px;
    }

    .menu-explorar-column {
        width: 190px;
    }

    .grid-2c {
        grid-template-columns: 190px calc(100% - 190px);
    }

    .grid-4c {
        grid-template-columns: 190px 190px 190px 190px;
    }

}

@media screen and (max-height: 570px) {
    .caixa-modulos-zw-ui.aberto {
        height: 500px;
    }
}

.zw_ui_nome_empresa_unitario {
    color: #4387b5;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    text-overflow: ellipsis;
    font-family: 'Nunito Sans', sans-serif;
    font-size: 16px;
    font-weight: 600;
    line-height: 3;
}
.modal-favorito-zw-ui {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1100;
    display: none;
    /* background: rgba(0, 0, 0, 0.5);*/
    overflow: hidden;
    outline: 0;
}
.modal-content-favorito-zw-ui {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-clip: padding-box;
    border: 0px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.5rem;
    outline: 0;
}

.modal-favorito-zw-ui.modal-open {
    display: block;
    font-family: 'Nunito Sans', sans-serif;
}

.modal-msg-item-favorito {
    margin-right: 100vw;
    z-index: 1100;
    margin-top: 80px;
    width: 170px;
    left: calc(95vw - 212px);
}

.modal-msg-item-favorito {
    margin-right: 0;
    z-index: 1100;
    margin-top: 90px;
    width: 252px;
    position: absolute;
    top: 0;
}
.zw_ui_modulo_atual_descricao.FAVORITOS, .zw_ui_modulo_atual.FAVORITOS {
    background-color: #638BE9;
}
.modules-menu-nav-container {
    width: 40px;
    height: 20px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 13px;
    margin-bottom: 13px;
}

.modules-menu-mov-icon {
    width: 20px;
    height: 20px;
}

.modules-menu-mav-disable {
    opacity: 0.5;
    cursor: initial;
}

.rich-mpnl-content {
    border: 1px solid #67757c;
    border-radius: 0.3rem;
}

.caixa-pesquisa-notificacoes {
    width: 100%;
    border-radius: 3px;
    border: 1px solid #bcbfc7;
    padding: 0px 30px 0px 10px;
    line-height: 30px;
    color: #92959b;
    outline: 0px !important;
    font-family: 'Nunito Sans', sans-serif;
    font-weight: 400;
    font-size: 14px;
    overflow: visible;
    background-color: #000a28;
(rgb(255, 255, 255), rgb(59, 59, 59));
    margin-bottom: 12px;
    margin-left: 14px;
}

.leia-mais-novidade-panel {
    display: none;
}

.div-alunos-favoritos {
    font-family: 'Nunito Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 125%;
    color: #51555A;
    margin-top: 1em;
    margin-bottom: -1.5em;
    margin-left: -1.5em;
}

.input-lembrete-aluno {
    margin-right: 0.4em;
    resize: none;
    height: 7em;
    border: 0;
    font-family: 'Nunito Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 125%;
    display: flex;
    align-items: center;
    color: #90949A;
    background-image: none;
    border: none;
    overflow: auto;
    outline: auto;

    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;

    resize: none;
}


.form-search-zw input {
    width: 100%;
    border: 0;
    color: #a1a5aa;
    border-bottom: 1px solid #c7c9cc;
    font-family: 'Nunito Sans', sans-serif;
    font-weight: 400;
    font-size: 14px;
    background-image: none;
}

.form-search-zw .pct-search {
    position: absolute;
    left: 0;
    top: 6px;
    color: #a1a5aa;
    font-size: 14px;
}

.titulo-notificacoes {
    background: #0380E3;
    padding: 1em;
    font-family: 'Nunito Sans';
    color: white;
    font-size: 16px;
    font-weight: 700;
    margin-left: -16px;
    margin-right: -16px;
    border-radius: 0.8em 0.8em 0 0;
}

.matriculaAlunoFavorito {
    border-radius: 50%;
    width: 2.5em;
    margin-left: 10.5em;
    margin-top: -2.2em;
}

.modules-menu-icon {
    opacity: 0.5;
}

.modules-menu-icon:hover, .modules-menu-icon-selected {
    opacity: 1;
}

.blue-container-assinatura, .blue-container-usuario {
    width: 100%;
    height: 40px;
    background-color: #0380E3;
    color: #FFFFFF;
    margin-top: -4px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-bottom: 1px solid #0380E3;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.class-span {
    font-size: 22px;
    padding-right: 10px;
    font-weight: lighter;
    font-family: Times, Times New Roman, serif;
}

.close {
    font-weight: lighter;
    text-shadow: none;
    padding-top: 8px;
}

button.close {
    color: #FFF;
    opacity: 1;
    padding-top: 10px;
    font-weight: lighter;
}

.modal-title {
    font-size: 16px;
    font-weight: bold;
    padding-top: 0.8rem;
    padding-left: 1.0rem;
}

.modal-conteudo {
    width: 260px;
    height: 183px;
    left: 542px;
}

.class-hr {
    border: 0;
    height: 1px;
    background: #f3f3f3;
    width: 258px;
    margin-left: 16px;
    margin-top: 0px;
    margin-bottom: 12px;
}

.container .option .class-span {
    color: #51555A;
    font-family: 'Nunito Sans';
    font-style: normal;
    font-weight: 300;
    font-size: 16px;
    line-height: 125%;
    padding-left: 0px;
}

.container-alunos-favoritos {
    border-radius: 1em !important;
}


.modules-wrapper{
    height: 244px;
    padding: 10px 7px;
}

.margin-modules{
    margin-top: -4px;
    margin-bottom: 0px;
}

.icon-module i{
    font-size: 20px;
}
.bottom-botton-settings{

    padding-top: 5px;
}

.zw_ui_modulo_atual,.zw_ui_modulo_atual_descricao{
    height: 30px;
}


i.pct.pct-chevron-left.ng-star-inserted{
    margin-right: 5px;
    width: 16px;
    height: 20px;
}

.zw_ui_menu-toggle{
    margin-top: 0px;
    width: 24px;
    height: 25.33px;
}
.menus{
    height: calc(110vh - 337px);
}

.inner-circle{
    width: 32px;
    height: 32px;
}
.green-top-container-favorito {
    width: 100%;
    height: 48px;
    background-color: #037D03;
    color: #FFFFFF;
    margin-top: -4px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-bottom: 0px solid #0380E3;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    font-size: 14px;
    font-family: 'Nunito Sans',sans-serif;
    weight: 600;
}
.red-top-container-favorito {
    width: 100%;
    height: 48px;
    background-color: #7D0303;
    color: #FFFFFF;
    margin-top: -4px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    font-size: 14px;
    font-family: 'Nunito Sans',sans-serif;
    weight: 600;
}
.green-container-favorito {
    width: 100%;

    background-color: #037D03;
    color: #FFFFFF;
    display: -ms-flexbox;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    font-size: 14px;
    font-family: 'Nunito Sans',sans-serif;
    weight: 400;
}
.msg-title-canal-do-cliente {
    padding-left: 32px;
    padding-top: 32px;
    font-family: 'Nunito Sans';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    color: #51555A;
}
.msg-container-canal-do-cliente {
    padding-left: 32px;
    padding-top: 24px;
    font-family: 'Nunito Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 125%;
    color: #80858C;
}
.top-container-canal-do-cliente {
    width: 100%;
    height: 56px;
    background-color: #ffffff;
    color: #51555A;


    border-bottom: 1px solid #DCDDDF;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    font-size: 14px;
    font-family: 'Nunito Sans',sans-serif;
    weight: 600;
}
.container-canal-do-cliente{
    width: 100%;
    height: 156px;
    background: #FFFFFF;
    color: #80858C;
    display: -ms-flexbox;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    font-size: 14px;
    font-family: 'Nunito Sans',sans-serif;
    weight: 400;
}
.red-container-favorito-cheio {
    width: 100%;
    background-color: #7D0303;
    color: #FFFFFF;
    display: -ms-flexbox;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    font-size: 14px;
    font-family: 'Nunito Sans',sans-serif;
    weight: 400;
}
.msg-title-green {
    font-size: 14px;
    font-weight: bold;
    padding-top: 0.8rem;
    margin-right: 50px;
    margin-left: 10px;
}
.msg-container-green {
    font-size: 14px;
    margin-top: 15px;
    margin-left: 35px;
    width: 171px;
    margin-bottom: 15px;
}
.msg-container-red {
    font-size: 14px;
    margin-top: -8px;
    margin-left: 35px;
    width: 171px;
    margin-bottom: 15px;
}
.msg-title-red {
    font-size: 14px;
    font-weight: bold;
    padding-top: 0.8rem;
    margin-right: 50px;
}
.icoMsg{
    font-size: 16px;
    margin-top:13px;
    margin-left:10px;

}
.modal-msg-canal-do-cliente {

    z-index: 1100;

    width: 494px;
    height: 213px;
    position: absolute;
    float: left;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
.modal-notificacao-canal-do-cliente {
    position: fixed;
    display: none;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1100;
    background: rgba(0, 0, 0, 0.5);
    overflow: hidden;
    outline: 0;
}

.container-btn-fechar{
    border: 1px solid #0380E3;
    width: 46px;
    background: #FFFFFF;
    color: #0380E3;
    display: inline-block;
    padding: 9px 20px;
    text-align: center;
    border-radius: 4px;
    font-weight: 700;

}
.container-btn-acesso{
    border: 1px solid #0380E3;
    width: 120px;
    background: #0380E3;
    color: #FFFFFF;
    display: inline-block;
    padding: 9px 20px;
    text-align: center;
    border-radius: 4px;
    font-weight: 700;
    margin-left: 10px;

}
.div-btn{
    margin-right: 32px;
    margin-top: 20px;
    float: right;
}
.btn-classe{
    font-family: 'Nunito Sans';
    font-size: 16px;
}
.modal-notificacao-canal-do-cliente.modal-open {
    display: block;
    font-family: 'Nunito Sans', sans-serif;
}
.container-imagem.close{
    display: none !important;
}
.container-imagem-configuracao.open{
    display: inline-block;
}
.container-imagem-configuracao {
    display: none;
    overflow: auto;
    width: calc(100% - 276px) !important;
    margin-left: 0px !important;
    margin-top: 78px !important;
    height: calc(100vh - 78px);
    float: right;
}

.zw_ui_retornar{
    width: calc(100% - 16px);
    display: none;
    background-color: #e6edfe;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: .25px;
    color: #1e60fa;
    max-width: 174px;
    padding: 8px 16px 8px 16px;
    align-items: center;
    cursor: pointer;
    height: 40px;
}

.btn_zw_ui_retornar {
    background: #e6edfe !important;
    border:none !important;
    width: calc(100% - 16px) !important;
    height: 30px !important;
    color: #5083fb !important;
    font-size: 14px !important;
}

.zw_ui_modulo_configuracao.zw_ui_modulo_atual.CONFIG.color-retornar,
color-retornar,
.color-retornar > span > i,
color-retornar.FAVORITOS > span > i,
.zw_ui_modulo_atual.color-retornar {
    background-color: #e6edfe !important;
    color: #5083fb !important;
}

.zw_ui_modulo_configuracao{
    padding: 8px;
    text-align: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-voltar-configuracao{
    border: none !important;
    width: 45px !important;
    height: 30px !important;
    font-size: 26px !important;
    color: #fff !important;
}
.container-msg-configuracao {
    width: 95%;
    border-radius: 10px;
    background-color: #f7f6f6;
    height: 300px;
    margin-left: 2% !important;
    margin-top: 20px;
}

.center-container-msg-configuracao {
    padding-top: 70px;
    align-items: center;
    text-align: center;
}

.bottom-container-msg-configuracao {
    width: 100%;
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    color: #55585E;
    align-items: center;
    text-align: center;
}
.class-submenu-retornar{
    line-height: 24px;
    font-family: 'Nunito Sans', sans-serif !important;
    text-decoration: none;
    padding-left: 8px;
    color: #fff;
    font-size: 16px;
    display: flex !important;
}

@media (min-height: 721px) {
    .bottom-botton-settings{
        position: absolute;
        bottom: 100px;
    }
    .modules-wrapper{
        height: 298px;
        padding: 5px 7px;
    }

    .icon-module i{
        font-size: 20px;
    }
    .zw_ui_modulo_atual,.zw_ui_modulo_atual_descricao{
        height: 40px;
    }
    .border-botton-settings{
        padding: 8px;
        background: #204C77;
        border-radius: 8px;
        font-family: 'Pacto-Icons-Fonts';
        font-style: normal;
        color: #FFFFFF;
    }
    .modules-menu-nav-container {
        width: 40px;
        height: 20px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 14px;
        margin-bottom: 12px;
    }
    .container-bottom{
        margin-top: 16px;
        margin-bottom: 16px;
    }


    .zw_ui_menu-toggle{
        margin-top: 0px;
        width: 24px;
        height: 32px;
    }
    .menus{
        height: calc(106vh - 337px);
    }
    .margin-modules{
        margin-top: 4px;
        margin-bottom: 0px;
    }
}

.container-mask.menus{
    flex-grow: 1;
    overflow: auto;
    height: calc(100vh - 245px);
}

.container-mask.menus-user-padrao{
    flex-grow: 1;
    overflow: auto;
    height: calc(100vh - 290px);
}
.rodape a:hover {
    text-decoration:none;
    color: #105C9C;
}

.rodape a {
    font-size: 12px;
    color:#9E9E9E;
}

@media (min-height: 600px) {
    .bottom-botton-settings {
        position: absolute;
        bottom: 100px;
    }
}

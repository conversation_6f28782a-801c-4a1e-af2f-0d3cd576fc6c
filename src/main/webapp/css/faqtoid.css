.faqtoidTourHighlight{
    -webkit-filter: none !important;
    -moz-filter: none !important;
    -o-filter: none !important;
    -ms-filter: none !important;
    filter: none !important;
    }
.faqtoidTourHighlight:not(.menuItem){
    -webkit-box-shadow: 0px 7px 13px 3px rgba(0, 0, 0, 0.4) !important;
    -moz-box-shadow: 0px 7px 13px 3px rgba(0, 0, 0, 0.4) !important;
    box-shadow:  0px 7px 13px 3px rgba(0, 0, 0, 0.4) !important;
}
.blur{
    -webkit-filter: blur(6px);
    -moz-filter: blur(6px);
    -o-filter: blur(6px);
    -ms-filter: blur(6px);
    filter: blur(6px);
}
#stopFaqtoidTourTip:hover{
    color: #FFFFFF !important;

}
#prevFaqtoidTourTip.disabled, #nextFaqtoidTourTip.disabled{
    cursor: auto !important;
    opacity: 0.2 !important;
}
#faqtoidTourTip {
    display: none;
    font-size: 30px;
    font-family: Arial;
    max-width: 40vw;
    color : #094771;
    position: absolute;
    z-index: 20001;
}
#faqtoidTourTip.video{
    min-width: 300px;
    width: auto;
}
.textoDicas{
    width: 100%;
    cursor: text;
    font-family: 'Oxygen',sans-serif !important;
    font-weight: normal !important;
}
#faqtoidMore {
    font-size: small;
    color: white;
    background: light-gray;
    display: inline-block;
    margin: 0 0 10px 30px;
    border: none;
    cursor: pointer;
}
#faqtoidShowMailForm {
    width: 100%;
    display: block;
    margin: 1em 0 0 0;
}
.hideFaqtoid {
    cursor: pointer;
}
#faqtoidMatches {
    margin-top: 12px;
    display: block;
    zfont-size: 15px;
}
.faqtoidQuestion {
    font-weight: bold;
}
.faqtoidAnswer {
    margin: 10px 0 0 0;
    display: none;
}
#faqtoid .startFaqtoidTour {
    width: 100%;
    display: block;
}
#faqtoidSidebar {
    clear: both;
    display: none;
    vertical-align: top;
    float: right;
    margin: 5px 0 15px 15px;
}
#faqtoid input[type=email] {
    font-family: "Lucida Console", Monaco, Courier, monospace; /* to prevent email typos */
    width: 100%;
}
#faqtoid .zzp { margin: 12px 0 0 0; }

.faqtoidHighlight {
    background: gold;
}
#fZZZaqtoid .modal-body > *:last {
    margin-bottom: -10px;
}

.faqtoidWindow {
    background: white;
    /*border: 1px solid #bbb;*/
    border-radius: 4px;
    padding: 1em;
    position: absolute;
    min-width: 300px;
    min-height: 300px;
    max-width: 80vw;
    max-height: 80vh;
    overflow: auto;
    overflow-x: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.faqtoidTitle {
    margin-top: 0;
}

.faqtoidBtn {
    /*color: white;*/
    background: white;
    /*border-radius: px;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);*/
    /*background: #888;*/
    padding: 6px 12px;
    border: 1px solid #bbb;
    border-radius: 4px;
    white-space: normal;
}    
.faqtoidBlockBtn {
    width: 100%;
    display: block;
    /*font-weight: bold;*/
    margin: 1em 0;
    /*font-size: 120%;*/
}
.faqtoidBtn:hover {
    background: #eee;
}
.faqtoidPrimaryBtn {
    background: #428bca;
    border: 0;
    border-radius: 4px;
    font-weight: bold;
    color: white;
}
.faqtoidPrimaryBtn:hover {
    background: #357ebd;
}

#faqtoid table {
    display: block;
    width: 100%;
}

#faqtoid input, #faqtoid textarea {
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
}

#faqtoidTour, #faqtoids {
    display: none;
}


#faqtoidTourTip > div:first-child {
    margin-bottom: 5px; 
    font-size: 24px; /* big enough to easily tap buttons */ 
    white-space: nowrap; 
    color: #ccc;
    vertical-align: middle;
}
#faqtoidTourTip > div:first-child div {
    float: right; 
    margin: 0 0 5px 5px; 
    vertical-align: middle;
}
#faqtoidTourTip > div:last-child {
    clear: both;
}


/*#nextFaqtoidTourTip span {
    width: 0;
    height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-left: 8px solid #0f0;    
}*/
/*.iconNext:before {
    width:16px;
    height:16px;
    -webkit-border-radius:16px;
    -moz-border-radius:16px;
    border-radius:16px;
}
.iconNext:after {
    left:7px;
    border:4px solid transparent;
    border-left-color:#00ff00;
    margin-top:-4px;
    background:transparent;
}*/

/* spinner from http://www.designcouch.com/home/<USER>/2013/05/23/dead-simple-pure-css-loading-spinner/ */

.spinner { 
    height:0.8em;
    width:0.8em;
    margin:0px auto;
    display: inline-block;
    position:relative;
    -webkit-animation: rotation .6s infinite linear;
    -moz-animation: rotation .6s infinite linear;
    -o-animation: rotation .6s infinite linear;
    animation: rotation .6s infinite linear;
    border-left:2px solid rgba(0,174,239,.15);
    border-right:2px solid rgba(0,174,239,.15);
    border-bottom:2px solid rgba(0,174,239,.15);
    border-top:2px solid rgba(0,174,239,.8);
    border-radius:100%;
}

@-webkit-keyframes rotation {
    from {-webkit-transform: rotate(0deg);}
to {-webkit-transform: rotate(359deg);}
}

@-moz-keyframes rotation {
    from {-moz-transform: rotate(0deg);}
to {-moz-transform: rotate(359deg);}
}

@-o-keyframes rotation {
    from {-o-transform: rotate(0deg);}
to {-o-transform: rotate(359deg);}
}

@keyframes rotation {
    from {transform: rotate(0deg);}
to {transform: rotate(359deg);}
}
.faqTourTipControls{
    display: none;
    z-index: 999 ;
    line-height: 63px;
    background-color: #094771;
    position: fixed;
    bottom: 0px;
    width: 60vw;
    left: 20vw;
    height: 63px;
    color: #fff;
    font-family: Arial;
    font-size: 16px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}
.faqTourTipControls.faqToidDone{
    background-color: #009962;
}
.faqTourTipControls > div:not(.disabled):not(.notHover):not(.tutorialDone):hover{
    background-color: rgba(0, 0, 0, 0.18);
    color: #ffffff;
}
.faqTourTipControls .controlSair{
    cursor: pointer;
    float: left;
    height: 100%;
}
.btnSairEsc{
    margin-right: 10px;
    border: 2px solid #fff;
    border-radius: 7px;
    height: 29px;
    width: 36px;
    vertical-align: sub;
    line-height: 38px;
    padding: 6px;
    display: inline-block;
}
.faqTourTipControls .controlNav {
    cursor: pointer;
    float: right;
    height: 100%;
    padding: 0px 30px;
}
.faqTourTipControls .controlNav > img {
    margin:0px 15px;
}
.faqTourTipControls .statusStep{
    display: inherit;
    font-size: 16px;
    color: rgba(41, 171, 226, 0.4);
    text-align: center;
    height: 100%;
    padding: 0px 30px;
}
.faqTourTipControls.faqToidDone .statusStep{
    display: none;
}
.blur{
    -webkit-filter: blur(6px);
    -moz-filter: blur(6px);
    -o-filter: blur(6px);
    -ms-filter: blur(6px);
    filter: blur(6px);
}
.faqTourTipControls.faqToidDone .controlNav{
    display: none;
}
.faqTourTipControls .checkContent {
    display: none;
}
.faqTourTipControls .tutorialDone{
    display: none;
}
.faqTourTipControls.faqToidDone .tutorialDone{
    display: inline-block;
    cursor: pointer;
    text-align: center;
    height: 30px;
    margin-top: 13px;
    padding: 0px 80px;
    width: 28%;
    line-height: 19px;
}
.faqTourTipControls.faqToidDone .checkContent {
    display: block;
    cursor: pointer;
    float: right;
    height: 100%;
    padding: 0px 30px;
}
.btnAcaoFaq{
    padding: 8px 10px;
    border: 2px solid #fff;
    border-radius: 7px;
}
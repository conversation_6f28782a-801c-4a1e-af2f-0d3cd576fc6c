/* 
    Document   : toggle_menu
    Created on : 17/01/2012, 16:17:01
    Author     : Waller
    Description:
        Purpose of the stylesheet follows.
*/

/* 
   TODO customize this sample style
   Syntax recommendation http://www.w3.org/TR/REC-CSS2/
*/

#site-body-container {}

#site-body-content {
    padding: 15px 15px 15px 15px ;
}

#site-bottom-bar {
    background-color: #F0F0F0 ;
    border-top: 1px solid #CCCCCC ;
    bottom: 0px ;
    font-family: verdana, arial ;
    font-size: 11px ;
    height: 32px ;
    position: fixed ;
    width: 100% ;
    z-index: 1000 ;
}

#site-bottom-bar-frame {
    height: 30px ;
    margin: 0px 10px 0px 10px ;
    position: relative ;
}

#site-bottom-bar-content {
    padding: 3px 0px 0px 0px ;
}

.menu-root {
    /*background-color: #678491 ;
    border: 1px solid #D0D0D0 ;*/
    padding: 0 0 0 0;
    color: #FFFFFF ;
    display: block ;
    font-weight: bold;
    /*height: 22px ;
    line-height: 22px ;*/
    text-align: center;
    text-decoration: none;
}

.menu-root a:hover {
    background-color: #666666 ;
    border-color: #000000 ;
    color: #FFFFFF ;
}

.menu {
    background-color: #E8E8E8 ;
    border: 1px solid #666666 ;
    bottom: 0px ;
    display: none ;    
    padding: 5px 5px 1px 5px ;    
    top: 0;    
    height: 238px;
    position: absolute;
    width: 120px ;
    z-index: 1000;
}

.menu a {
    background-color: #E8E8E8 ;
    border: 1px solid #FFFFFF ;
    color: #000000 ;
    display: block ;
    margin-bottom: 4px ;
    padding: 5px 0px 5px 5px ;
    text-decoration: none ;
}

.menu a:hover {
    background-color: #666666 ;
    border-color: #000000 ;
    color: #FFFFFF ;
}

/* -------------------------------------------------- */
/* -- IE 6 FIXED POSITION HACK ---------------------- */
/* -------------------------------------------------- */

html,
body,
#site-body-container {
    _height: 100% ;
/*    _overflow: hidden ;*/
    _width: 100% ;
}

#site-body-container {
    _overflow-y: scroll ;
/*    _overflow-x: hidden ;*/
    _position: relative ;
}

/* To make up for scroll-bar. */
#site-bottom-bar {
    _bottom: -1px ;
    _position: absolute ;
    _right: 16px ;
}

/* To make up for overflow left. */
#site-bottom-bar-frame {
    _margin-left: 26px ;
}

/* To fix IE6 display bugs. */
.menu a {
    _display: inline-block ;
    _width: 99% ;
}
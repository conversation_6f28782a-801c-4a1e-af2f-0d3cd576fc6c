-webkit-scrollbar{width:8px;height:6px;margin-right:2px;}
::-webkit-scrollbar-button:start:decrement,::-webkit-scrollbar-button:end:increment{display:block;height:0px;}
::-webkit-scrollbar-button:vertical:end:increment{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-button:vertical:increment{background-color:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-bottom:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-track-piece{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-thumb:vertical{background-color:#ccc;}
::-webkit-scrollbar-thumb:vertical:hover{background-color:#666;}
::-webkit-scrollbar-thumb:vertical:active{background-color:#333;}
form,body{
    margin: 0;
    overflow-x: auto;
}

form.overflow-visible {
    overflow-x: visible;
}

.font-size-Em{
    font-size: 1vw;
}
.lineHeight-3em{
    line-height: calc(1vw * 3) !important;
}
.paginaFontResponsiva table,.paginaFontResponsiva{
    font-size: 1vw;
}
@media screen and (min-width: 1368px) {
    .font-size-Em,.paginaFontResponsiva table,.paginaFontResponsiva,.linkPadrao,form .margin-box, form .container-box-header,.semBorda .rich-table-cell,
    table.tabelaSimplesCustom > tbody .rich-table-cell,table.tabelaSimplesCustom > tbody td:not(:empty), table.tabelaSimplesCustom .rich-table-subheadercell,
    .novaModal.noMargin .rich-mpnl-body, .cb-container:after, .rich-table-cell,.bi-panel, .fonte-responsiva{
        font-size:  calc(1368px / 100) !important;
    }
    .lineHeight-3em{
        line-height:  calc(1368px / 100 * 3) !important;
    }
    table.tabelaSimplesCustom:not(.dataTable) .rich-table-subheadercell,table:not(.dataTable).tabelaSimplesCustom> tbody .rich-table-cell,table:not(.dataTable).tabelaSimplesCustom > tbody td:not(:empty){
        font-size:  calc(1368px / 100 ) !important;
    }
}
@media screen and (min-width: 1196px) {
    .font-size-Em,.paginaFontResponsiva table,.paginaFontResponsiva,.linkPadrao,form .margin-box, form .container-box-header,.semBorda .rich-table-cell,
    table.tabelaSimplesCustom > tbody .rich-table-cell,table.tabelaSimplesCustom > tbody td:not(:empty), table.tabelaSimplesCustom .rich-table-subheadercell,
    .novaModal.noMargin .rich-mpnl-body, .cb-container:after, .rich-table-cell,.bi-panel, .fonte-responsiva{
        font-size:  calc(1196px / 100) !important;
    }
    .lineHeight-3em{
        line-height:  calc(1196px / 100 * 3) !important;
    }
    table.tabelaSimplesCustom:not(.dataTable) .rich-table-subheadercell,table:not(.dataTable).tabelaSimplesCustom> tbody .rich-table-cell,table:not(.dataTable).tabelaSimplesCustom > tbody td:not(:empty){
        font-size:  calc(1196px / 100 ) !important;
    }
}
@media screen and (max-width: 1024px) {
    .font-size-Em,.paginaFontResponsiva table,.paginaFontResponsiva,.linkPadrao,form .margin-box, form .container-box-header,.semBorda .rich-table-cell,
    table.tabelaSimplesCustom > tbody .rich-table-cell,table.tabelaSimplesCustom > tbody td:not(:empty):not([class*="texto-size-"]):not([class^="texto-size-"]), table.tabelaSimplesCustom .rich-table-subheadercell,
    .novaModal.noMargin .rich-mpnl-body, .cb-container:after, .rich-table-cell,.bi-panel, .fonte-responsiva{
        font-size:  calc(1024px / 100) ;
    }
    .lineHeight-3em{
        line-height:  calc(1024px / 100 * 3) !important;
    }
    table.tabelaSimplesCustom:not(.dataTable) .rich-table-subheadercell,table:not(.dataTable).tabelaSimplesCustom> tbody .rich-table-cell,table:not(.dataTable).tabelaSimplesCustom > tbody td:not(:empty){
        font-size:  calc(1024px / 100 ) ;
    }
}

.font-size-Em-max{
    font-size: calc(1368px / 100);
}
.hidden{
    display: none;
}
.semBorda{
    padding-top: 0px;
    padding-bottom: 0px;
    border-bottom: none;
    border-left: none;
    border-right: none;
    border-top: none;
}
.tabelaSemBorda,.tabelaSemBorda tr ,.tabelaSemBorda td{
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
}
form .margin-box, form .container-box-header,.semBorda .rich-table-cell{
    font-size: calc(1024px / 100);
}

@media (min-width: 1024px) {
    form .margin-box, form .container-box-header,.semBorda .rich-table-cell{
        font-size: 1vw;
    }
}
@media (min-width: 1865px) {
    form .margin-box, form .container-box-header,.semBorda .rich-table-cell{
        font-size: calc(1865px / 100);
    }
}
.borderBtnSeparetorLeft{
    border-left: 1px solid rgba(0, 0, 0, 0.30);
}
.bg-cinza{
    background-color: #E6E6E6;
}
.textoImcompleto{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.texto-cor-vermelho,.texto-cor-vermelho:visited,.texto-cor-vermelho:hover{
    color: #FF5555;
}
.texto-cor-amarelo,.texto-cor-amarelo:visited,.texto-cor-amarelo:hover{
    color: #f2d51d;
}
.bg-vermelho{
    background-color: #FF5555;
}
.texto-cor-branco,.texto-cor-branco:visited{
    color: #fff;
}
.texto-cor-azul,.texto-cor-azul:visited{
    color : #29ABE2;
}
.padding-11{
    padding: 11px;
}
.padding-5{
    padding: 5px;
}
.texto-cor-complementar{
    color : #9D9D9D;
}
.texto-cor-disabilitado{
    color : #B4B4B4;
}
.texto-cor-azul.linkPadrao:hover{
    color : #3677e2;
}
.texto-cor-branco.linkPadrao:hover{
    color : #fff;
}
.texto-font{
    font-family: Arial;
}
.linkPadrao{
    cursor: pointer;
    text-decoration: none !important;
}
.linkPadrao:not([class*="texto-size"]){
    font-size: 1em !important;
}
.texto-bold{
    font-weight: bold;
}
.texto-size-20-real{
    font-size: 20px;
}
.texto-size-25-real{
    font-size: 25px;
}
.texto-size-60-real{
    font-size: 60px;
}
.texto-size-20{
    font-size: 1.5em;
}
.texto-size-18{
    font-size: 1.3em;
}
.texto-size-25{
    font-size: 2em;
}
.texto-size-40{
    font-size: 3em;
}
.texto-size-30{
    font-size: 2.5em;
}
.texto-size-60{
    font-size: 4.5em;
}
.texto-size-11{
    font-size: .7em;
}
.texto-size-12{
    font-size: .8em;
}
.texto-size-14{
    font-size: 1em !important;
}
.texto-size-14-real{
    font-size: 14px;
}
.texto-size-12-real{
    font-size: 12px;
}
.texto-formato-nome{
    text-transform: capitalize;
}
.texto-size-16{
    font-size: 1.1em !important;
}
.texto-size-16-real{
    font-size: 16px;
}
.texto-upper{
    text-transform: uppercase;
}
.texto-normal{
    font-weight: normal;
}
.italico{
    font-style: italic;
}
.tituloLabel{
    margin-right: 28px;
}
@font-face {
    font-family: NeoSansItalic;
    src: url('../beta/font/Neo%20Sans%20Std%20Medium%20Italic.otf');
}
.col-text-align-left{
    text-align: left !important;
}
.col-text-align-center{
    text-align: center;
}
.col-text-align-right{
    text-align: right;
}
.topoZW{
    height: 135px;
}
.topoZW.menu-false {
    visibility: hidden;
    height: 0px;
}
.topoZW .topoModulos {
    width: 100%;
}
.barraFixa{
    position: fixed !important;
    top: 0px !important;
    z-index: 22 !important;
    width: 100%;
}
.topoZW table{
    border-collapse: initial !important;
    border-spacing: 0 !important;
}
.topoZW .topoModulos .blocoModulos {
    height: 10px;
    transition: 0.2s;
    -o-transition: 0.2s;
    -webkit-transition: 0.2s;
    -webkit-transition: 0.2s;
}
.transition-all{
    transition: 0.2s;
    -o-transition: 0.2s;
    -webkit-transition: 0.2s;
    -webkit-transition: 0.2s;
}

.topoZW .topoModulos .blocoModulos .moduloAberto {
    float: left;
    width: calc(100% - 291px);
    height: 7px;
    display: inline-block;
}

.topoZW .topoModulos .blocoModulos .ZW,.moduloZW {
    background-color: #2F96D2;
}

.topoZW .topoModulos .blocoModulos .CRM,.moduloCRM {
    background-color: #F6B817;
}

.topoZW .topoModulos .blocoModulos .TR,.moduloTR {
    background-color: #FF0000;
}

.topoZW .topoModulos .blocoModulos .SLC,.moduloSLC {
    background-color: #76529D;
}

.topoZW .topoModulos .blocoModulos .FIN,.moduloFIN {
    background-color: #00C350;
}
.topoZW .topoModulos .blocoModulos .CE,.moduloCE {
    background-color: #a07143;
}
.topoZW .topoModulos .blocoModulos .NFSE,.moduloNFSE {
    background-color: #006350;
}
.topoZW .topoModulos .blocoModulos .NOTAS,.moduloNOTAS {
    background-color: #006350;
}
.topoZW .topoModulos .blocoModulos .CANAL,.moduloCANAL {
    background-color: #82878e;
}
.topoZW .topoModulos .blocoModulos .UCP,.moduloUCP {
    background-color: #FF6E1E;
}
.topoZW .topoModulos .blocoModulos .moduloFechados .moduloFechado {
    height: 100%;
    display: inline-block;
    height: 7px;
    cursor: pointer;
}

.topoZW .topoModulos .blocoModulos .moduloFechados {
    width: 291px;
    display: inline-block;
}

.topoZW .topoOpcoes {
    display: inline-table;
    width: 100%;
    height: 70px;
    padding-top: 5px;
}

.topoZW .topoOpcoes .logoIcones {
    display: inline-block;
    height: 100%;
    width: 60%;
}

.topoZW .topoOpcoes .botoesOpcoes {
    text-align: right;
    height: 100%;
    display: inline-block;
}
.menuOpcoes .configuracoes {
    width: 100%;
    padding-right: 10px;
}

.itemsOpcoes {
    cursor: pointer;
    color: #0F4C6B;
    font-size: 30px;
    border-top:1px solid transparent;
    border-right:1px solid transparent;
    border-left:1px solid transparent;
}
.itemsOpcoes span{
    line-height: 64px;
}
.itemsOpcoes a:hover{
    text-decoration: none;
}
.topoZW .menuOpcoes {
    display: block;
    width: 100%;
    height: 45px;
}

.topoZW .menuOpcoes .menus {
    width: 55%;
    float: left;
    display: inline-block;
}
.topoZW .menuOpcoes .menus .menuItem {
    vertical-align: middle;
    height: 95%;
    min-width: 7%;
    text-align: center;
    display: inline-block;
}
.menuItemAtual{

    border-bottom: 2px solid white;
    border-left: none;
    font-weight: bold;
}
.menuItem > a {
    font-family: Arial;
    font-size: 14px;
    color: white ;
    text-decoration: none !important;
    font-weight: normal;
}
.menuItem > a > i{
    color: white ;
    text-decoration: none !important;
    font-weight: normal;
}
.alinhamentoItemMenus{
    margin: 12px 6px;
}
.topoZW .menuOpcoes .areaBusca {
    width: 45%;
    line-height: 45px;
    height: 100%;
    position: relative;
    float: right;
    display: inline-block;
    text-align: right;
}
.topoZW .menuOpcoes .areaBusca .relogios{
     display: inline-block;
 }
.areaLogout{
    color: #FF5555;
    font-size: 12px;
    padding-left: 4px;
    display: inline-block;
}
.lupaBuscarOpen{
    color: #ffffff;
    font-size: 20px;
    cursor: pointer;
    padding:15px;
    margin: auto !important;
}
.lupaBuscar{
    color: #0F4C6B;
    font-size: 20px;
    display: inline-block;
    padding:15px;
    margin: auto !important;
}
.topoZW .menuOpcoes  .areaBuscaInteligenteAtivo{
    color: #0F4C6B;
}

.inputBusca{
    width: 0px;
    background: none !important;
    height: 20px;
    margin: 25px 0 0 12px;
    border-right: none !important;
    border-left: none !important;
    border-top: none !important;
    border-bottom: none !important;
    -webkit-transition: width 0.37s !important;
    -moz-transition: width 0.37s !important;
    transition: width 0.37s !important;
    font-family: Arial !important;
    font-size: 16px !important;
    color:  #0F4C6B !important;
    text-decoration: none !important;
    font-weight: normal !important;
    padding: 0 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}
.navbar-toggle{
    cursor: pointer;
}
.panelBusca{
    right: 43px;
    left: auto;
    display: block;
    width: 0px;
    height: 50px;
    z-index: 2;
    top: 0px;
    -webkit-transition: width 0.3s !important;
    -moz-transition: width 0.3s !important;
    transition: width 0.3s !important;
    position: absolute !important;
}
.inputBusca:focus {
    outline: none ;
    border-bottom: solid 2px #0F4C6B !important;
}
.conteinerBuscaInteligente{
    display: inline-block;
    position: relative;
    float: right;
    margin-right: 10px;
    height: 100%
}
.conteinerBuscaInteligenteAtivo{
    background-color: white;
    border-left: 1px solid black;
    border-right: 1px solid black;
}
.buscaEspansivel{
    display: inline-block;
    width: auto;
}
.expandirInput{
    width: 310px;
    font-size: 30px;
}

.expandirPanel{
    width: 320px;
    font-size: 30px;
}
.ativo{
    display: inline-block !important;
    visibility: visible;
}
.desativado{
    display: none !important;
    visibility: hidden;
}
.dropDownMenu .item{
    width: 95%;
    height: 100%;
    line-height: 2;
    float: right;
    text-align: left;
}
.dropDownMenu .item a{
    padding: 10px 10px 10px 5%;
}
.dropDownMenu  ul{
    position: absolute;
    background-color: #EEEEEE;
    list-style: none;
    border-top:1px solid rgb(174, 187, 188);
    border-right:1px solid rgb(174, 187, 188);
    border-left:1px solid rgb(174, 187, 188);
    border-bottom:1px solid rgb(174, 187, 188);
    transition:1s;
    -webkit-transition: opacity 1s linear;
    -moz-transition: opacity 1s linear;;
    margin-top: 0px;
    padding-left: 0px;
    box-shadow: 0 3px 2px 0 rgba(0, 0, 0, 0.2), 0 3px 9px 0 rgba(0, 0, 0, 0.19);

}
.dropDownMenu .dropMenuModulo{
    display: none;
    z-index: 2;
    padding-top: 20px;
    top: calc(100% - 1px);
    right: calc(100% - 3 * (100%) - 102px);
}
.dropDownMenu ul{
    margin-left: 0px !important;
    top:  calc(100% - 1px);
    right: calc( 100% - 1 * (100%) );
}
.dropDownMenu .dropMenuEmpresa{
    display: none;
    z-index: 2;
}
.dropDownMenu .dropMenuConfig{
    display: none;
    z-index: 2;
}
.dropDownMenu .dropMenuUsuario{
    display: none;
    z-index: 2;
}

.dropDownMenu .dropMenuSaldoInicial{
    display: none;
    z-index: 2;
}
.dropDownMenu .dropDownFAQ{
    display: none;
    z-index: 2;
}
.dropMenuModulo  li{
    width: 262px;
    height: 52px;
    z-index: 9999;
    line-height: 2;
    text-align: right;
}
.dropDownFAQ   li{
    width: 191px;
    height: 52px;
    z-index: 9999;
    line-height: 1.5;
    text-align: center;
}
.dropMenuEmpresa   li{
    height: 52px;
    z-index: 9999;
    line-height: 3.3;
    text-align: center;
}
.dropMenuConfig   li{
    width: 200px;
    height: 52px;
    z-index: 9999;
    line-height: 2;
    text-align: left;
}
/*.dropMenuUsuario  li:first-child{*/
    /*width: 200px;*/
    /*height: 120px;*/
    /*z-index: 9999;*/
    /*line-height: 2;*/
    /*text-align: left;*/
/*}*/
.dropMenuUsuario  li{
    z-index: 9999;
    min-width: 250px;
    display: flex;
    text-align: left;
}

.dropMenuSaldoInicial  li{
    z-index: 9999;
    min-width: 250px;
    display: flex;
    text-align: left;
}

.dropMenuConfig,.dropMenuUsuario  li div > a{
   padding-left: 5px;
   padding-right: 20px;
   padding-top: 15px;
   padding-bottom: 15px;
   text-decoration: none;
   vertical-align: initial;
}

.dropMenuConfig,.dropMenuSaldoInicial  li div > a{
   padding-left: 5px;
   padding-right: 20px;
   padding-top: 15px;
   padding-bottom: 15px;
   text-decoration: none;
   vertical-align: initial;
}

.dropMenuConfig,.dropMenuUsuario  li div > img{
    vertical-align: text-top;
}

.dropMenuConfig,.dropMenuSaldoInicial  li div > img{
    vertical-align: text-top;
}

.dropDownMenu li:not(.notHover):hover{
    background-color: rgba(0,0,0,0.10);
}
.dropDownMenu li span{
    vertical-align: super;
 }
.menuModulos{
    display: inline-block;
    position: relative;
    width: 52px;
    z-index: 1;
    background-color: white;
    height: 57px;
    border-right:1px solid rgb(222, 222, 222);
}
.open > .dropdown-border{
    background-color: #EEEEEE;
    z-index: 10;
    -webkit-transition: all .2s;
    -moz-transition: all .2s;
    -ms-transition: all .2s;
    -o-transition: all .2s;
    transition: all .2s;
    border-right:1px solid rgb(174, 187, 188);
    border-left:1px solid rgb(174, 187, 188);
    border-top:1px solid rgb(174, 187, 188);
}
.containerFotoCliente{
    width: 100%;
    text-align: center;
    margin-top: 26px;
    margin-bottom: 26px;
}
.containerFotoCliente img{
    border-radius: 7em;
    width: 9em;
    height: 9em;
}
.rotuloCampos{
    font-size: 1em;
    font-weight: bold;
    color: #777777;
    font-family: Arial;
}
.tituloMenu{
    font-family:NeoSansItalic,Arial;
    font-size: 23px;
    color: #BBBDBF !important;
    text-decoration: none !important;
}
.textSmallFlat{
    font-family:NeoSansItalic,Arial;
    font-size: 16px;
    color:#555 !important;
    text-decoration: none !important;
}
.textSmallFlatSubTitle{
    font-family:NeoSansItalic,Arial;
    font-size: 12px;
    opacity: .7;
    color:#555 !important;
    text-decoration: none !important;
}
.dropdown-border{
    display: inline-block;
}
.boxOpcoes div{
    height: 100%;
    z-index: 9;
    min-width: 52px;
    position: relative;
    text-align: center;
    vertical-align: middle;
}
.alinhaMentoItemMenu{
    line-height: 3.5;
}
.botao-hidden{
    cursor: pointer;
    position: relative;
}
.botao-hidden:hover .titulo{
    color: #EFE9E9 !important;
}
.botao-hidden:hover .titulo{
    color: #EFE9E9 !important;
}
.botao-hidden:hover .dropdown-content{
    transition:0.5s;
    -webkit-transition:0.5s;
    -moz-transition:0.5s;
    visibility: visible;
}

.menu-lateral .dropdown-content{
  display: inline-block;
  left: 100%;
}
.menu-flex .container-item{
    display: flex;
}
.menu-flex .container-item a{
    margin-top: 10px;
}
.dropdown-content{
    position: absolute;
    visibility: hidden;
    z-index: 20;
    background-color: #EEEEEE;
    list-style: none;
    border-top:1px solid rgb(174, 187, 188);
    border-right:1px solid rgb(174, 187, 188);
    border-left:1px solid rgb(174, 187, 188);
    border-bottom:1px solid rgb(174, 187, 188);
    transition:0.5s;
    -webkit-transition:0.5s;
    -moz-transition:0.5s;
    margin: 0px;
    padding: 0px !important;
    box-shadow: 0 3px 2px 0 rgba(0, 0, 0, 0.2), 0 3px 9px 0 rgba(0, 0, 0, 0.19);

}
.dropdown-item{
    width: 221px;
    height: 52px;
    z-index: 9999;
    line-height: 3;
    text-align: left;
}
.h3em .dropdown-item{
    height: 2em;
    line-height: 2em;
}
.dropdown-item-consultas{
    width: 240px;
    height: 52px;
    z-index: 9999;
    line-height: 3;
    text-align: left;
}
.dropdown-item-consultas:hover{
    background-color: rgba(0,0,0,0.10);
}
.dropdown-item:hover{
    background-color: rgba(0,0,0,0.10);
}
.container-item{
    width: 90%;
    height: 100%;
    float: right;
}
.textSmallFlat2 {
    font-family: Arial;
    font-size: 12px;
    color: #9E9E9E;
}
.mensagemDetalhada{
    color:#FF5555;
    font-size: 9pt;
    font-family: "Arial";
}
.suggestionCampoBusca .rich-sb-ext-decor-3{
    background-color: #EEEEEE;
}
.suggestionCampoBusca .rich-sb-int-sel{
    background-color: rgba(0,0,0,0.10);
    background-image: none;
    cursor: pointer;
}
.suggestionCampoBusca ._suggestion_size_ {
    width: 337px;
}
.suggestionCampoBusca .rich-sb-int-decor-table   > tbody tr{
    padding: 2px !important;
    height: 40px !important;
}
.suggestionCampoBusca .rich-sb-int-decor-table   > tbody tr td div:hover{
    cursor: pointer;
}
.moduloFechado:hover {
     height: 9px !important;
     transition: 0.2s;
     -o-transition: 0.2s;
     -webkit-transition: 0.2s;
    -webkit-transition: 0.2s;
    vertical-align: top;

}
.textSmallFlat3{
    font-family:NeoSansItalic,Arial;
    font-size: 16px;
    color:#B7B2B2 !important;
    text-decoration: none !important;
}
.imagem-blur-bottom{
    -webkit-mask-image: -webkit-linear-gradient(bottom, rgba(255,255,255,0) 5%, rgba(255,1,1,1) 30%);
}
.imagemApresentacao{
   width: 100%;
}
.container-imagem{
    width: calc(100% - 230px);
    display: inline-block;
    float: right;
}
.container-imagem.menu-false{
    width: 100%;
    display: initial;
    float: none;
}
.menuLateral{
    background-color: #ffffff;
    display: inline-block;
    width: 200px;
    height: 100%;
    min-height: calc( 100vh - 211px);
}
.menuLateral .grupoMenuLateral{
    width: 100%;
    float: right;
    text-align: left;
    padding-top: 14%;
}
.menuLateral .grupoMenuLateral:last-child{
    padding-bottom: 14%;
}
.menuLateral .grupoMenuLateral .grupoMenuTitulo{
    font-size: 14px;
    color: #094771;
    font-family: Arial;
    font-weight: bold;
    padding-left: 1%;
    padding-bottom: 4%;
    margin-left: 13px;
}

a:focus{
  outline: none !important;
}

.menuLateral .grupoMenuLateral .grupoMenuItem {
   width: 100%;
   height: 31px;
    line-height: 31px;
}
.menuLateral .grupoMenuLateral .grupoMenuItem .titulo3{
    line-height: 31px;
}
.menuLateral .grupoMenuLateral .linkFuncionalidade{
    margin:auto 14px;
}
.linkFuncionalidade:hover,.linkFuncionalidade:visited{
    text-decoration: none;
}

.menuLateral .grupoMenuLateral .linkFuncionalidadeNovo{
    margin:auto 14px;
}
.linkFuncionalidadeNovo:hover,.linkFuncionalidadeNovo:visited{
    text-decoration: none;
}
.menuLateral .grupoMenuLateral .grupoMenuItem a{
    font-size: 12px;
    color: #094771;
    font-family: Arial;
    text-decoration: none;
    margin-left: 14px;
}

.menuLateral .grupoMenuLateral .grupoMenuItem .linkWiki{
    float: right;
    visibility: hidden;
    margin-right: 4px;
    margin-top: 6px;
    color: #9E9E9E;
    margin: 3px 0px 0px -17px;
}

.menuLateral .grupoMenuLateral .grupoMenuItem .linkWikiNovo{
    float: right;
    visibility: hidden;
    margin-right: 4px;
    margin-top: 6px;
    color: #9E9E9E;
    margin: 4px 0px 0px -13px;
}

.menuLateral .grupoMenuLateral .grupoMenuItem .linkWikiNovo .fa-icon-external-link-square{
    margin-right: 4px;
    margin-top: 6px;
}

.menuLateral .grupoMenuLateral .grupoMenuItem .linkWiki .fa-icon-external-link-square{
    margin-right: 4px;
}

.menuLateral .grupoMenuLateral .grupoMenuItem:hover{
    background-color: #094771;
    -webkit-animation: fadeIt 0.6s ease-out 1;
    -moz-animation: fadeIt 0.6s ease-out 1;
    -o-animation: fadeIt 0.6s ease-out 1;
    animation: fadeIt 0.6s ease-out 1;
}
.menuLateral .grupoMenuLateral .grupoMenuItem:hover .linkWiki{
    margin: -24px 0 0 0;
    visibility:visible;
}

.menuLateral .grupoMenuLateral .grupoMenuItem:hover .linkWikiNovo{
    visibility:visible;
}

.menuLateral .grupoMenuLateral .grupoMenuItem:hover .linkFuncionalidade{
    display: block;
    color: #ffffff;
}

.menuLateral .grupoMenuLateral .grupoMenuItem:hover .linkFuncionalidadeNovo{
    color: #ffffff;
}
.menuBoxDescricao{
    height: 400px;
    width: 206px;
}


.grupoMenuItemContainer {
    display: flex;
    align-items: center;
}

.grupoMenuItem:hover .menuItemLinkFixedHidden{
    visibility: visible;
}

.grupoMenuItem:hover .menuItemLinkWikiHidden{
    visibility: visible;
    margin-left: 0px !important;
}

.grupoMenuItemNomeContainer {
    flex-grow: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    white-space: nowrap;
}

.grupoMenuItemLinksContainer {
    flex-flow: 0;
}

.icone-grupo-menu {
    margin-right: 10px;
}

.linkFuncionalidadeAcessoRapido {
    margin-left: 2px;
    margin-right: 2px;
}

.menuItemLinkFixedHidden {
    visibility: hidden;
    margin-right: 10px;
}

.menuItemLinkWikiHidden {
    visibility: hidden;
}

.icon-color-grey {
    color: #9e9e9e;
}

.caixaRodape{
    display: block;
    width: 100%;
    border-top: 1px solid #95C3F1;    line-height: 2.5;
    height: 75px;
    line-height: 100%;
    background-color: #094771;
}
.caixaCorpo{
    width: 100%;
    display: inline-flex;
}
.fundoCinza{
    background-color: #e6e6e6;
    width: 100%;
    overflow: hidden;
}
.fundoCinza.menu-false{
    background-color: #eff2f7;
    height: 100%;
    width: 100%;
    overflow: hidden;
}
.fundoBranco{
    background-color: #ffffff;
    width: 100%;
    overflow: hidden;
}
.caixaMenuLatel{
    display: table;
    float: left;
    width: 100%;
    height: 100%;
}
.caixaRodapeInfo{
    float: left;
    width: 70%;
    height: 100%;
}
.tituloVisao{
    font-family: "Oxygen";
    font-size: 21px;
    font-style:italic;
    display: block;
    color: #ffffff;
    line-height: 2.5;
    margin-left: 22px
}
.rotuloSmall{
    font-family: 'Oxygen', sans-serif;
    font-size: 12px;
    color: #9E9E9E;
}
.rotuloSmall:hover{
    color: #9E9E9E;
    text-decoration: none;
}
.tituloDireitos{
   line-height: 1;
   margin-left: 22px;
}
.caixaRodapeMidia{
    float: right;
    width: 30%;
    height: 100%;
}
.infoInstancia{
    float: right;
    margin-right: 5%;
}
.infoInstancia span{
    padding-left: 5px;
    padding-right: 5px;
}
.caixaMidiasPacto{
    float: left;
    width: 50%;
    height: 100%;
    background-color: rgba(0,0,0,0.20);
}
.caixaMidiasPacto .midiasPacto{
    font-size: 28px;
    color: white;
    text-decoration: none;
}
.caixaMidiasPacto a{
    padding: 3px;
}
.titulo:visited,.titulo:hover{
    color: #ffffff;
}
@-webkit-keyframes fadeIt {
    0%   { background-color: #FFFFFF; }
    100% { background-color: #094771; }
}
@-moz-keyframes fadeIt {
    0%   { background-color: #FFFFFF; }
    100% { background-color: #094771; }
}
@-o-keyframes fadeIt {
    0%   { background-color: #FFFFFF; }
    100% { background-color: #094771; }
}
@keyframes fadeIt {
    0%   { background-color: #FFFFFF; }
    100% { background-color: #094771; }
}
.linkWiki:hover{
    text-decoration: none;
    color: lightgrey;
}
.linkWiki:visited,.linkWiki{
    color: lightgrey;
    text-decoration: none;
}
.caixaSitePacto{
    float: right;
    width: 50%;
    height: 100%;
}
/*!
 * Bootstrap v3.3.6 (http://getbootstrap.com)
 * Copyright 2011-2015 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 *//*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
.container-fluid {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto
}
@media (min-width: 100px) {
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand {
        margin-left: -15px
    }
}
.container-fluid > .navbar-collapse, .container-fluid > .navbar-header, .container > .navbar-collapse, .container > .navbar-header {
    margin-right: -15px;
    margin-left: -15px
}

@media (min-width: 820px) {
    .container-fluid > .navbar-collapse, .container-fluid > .navbar-header, .container > .navbar-collapse, .container > .navbar-header {
        margin-right: 0;
        margin-left: 0
    }
}
a:hover{
    text-decoration: none;
}
@media print {
    *, :after, :before {
        color: #000 !important;
        text-shadow: none !important;
        background: 0 0 !important;
        -webkit-box-shadow: none !important;
        box-shadow: none !important
    }

    a, a:visited {
        text-decoration: underline
    }

    a[href]:after {
        content: " (" attr(href) ")"
    }
    .navbar {
        display: none
    }
}
.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-timing-function: ease;
    -o-transition-timing-function: ease;
    transition-timing-function: ease;
    -webkit-transition-duration: .35s;
    -o-transition-duration: .35s;
    transition-duration: .35s;
    -webkit-transition-property: height, visibility;
    -o-transition-property: height, visibility;
    transition-property: height, visibility
}

.dropdown, .dropup {
    position: relative
}

.dropdown-toggle:focus {
    outline: 0
}

/*.dropdown-menu {*/
    /*position: absolute;*/
    /*top: 100%;*/
    /*left: 0;*/
    /*z-index: 1000;*/
    /*display: none;*/
    /*float: left;*/
    /*min-width: 160px;*/
    /*padding: 5px 0;*/
    /*margin: 2px 0 0;*/
    /*font-size: 14px;*/
    /*text-align: left;*/
    /*list-style: none;*/
    /*background-color: #fff;*/
    /*-webkit-background-clip: padding-box;*/
    /*background-clip: padding-box;*/
    /*border: 1px solid #ccc;*/
    /*border: 1px solid rgba(0, 0, 0, .15);*/
    /*border-radius: 4px;*/
    /*-webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);*/
    /*box-shadow: 0 6px 12px rgba(0, 0, 0, .175)*/
/*}*/
.dropdown-menu.pull-right {
    right: 0;
    left: auto
}
.dropdown-menu .divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5
}
.dropdown-menu > li > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap
}
.dropdown-menu > li > a:focus, .dropdown-menu > li > a:hover {
    color: #262626;
    text-decoration: none;
    background-color: #f5f5f5
}
.dropdown-menu > .active > a, .dropdown-menu > .active > a:focus, .dropdown-menu > .active > a:hover {
    color: #fff;
    text-decoration: none;
    background-color: #337ab7;
    outline: 0
}
.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:focus, .dropdown-menu > .disabled > a:hover {
    color: #777
}
.dropdown-menu > .disabled > a:focus, .dropdown-menu > .disabled > a:hover {
    text-decoration: none;
    cursor: not-allowed;
    background-color: transparent;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false)
}

.open > .dropdown-menu {
    display: inline-block;
}

.open > a {
    outline: 0
}

.dropdown-menu-right {
    right: 0;
    left: auto
}

.dropdown-menu-left {
    right: auto;
    left: 0
}

.dropdown-header {
    display: block;
    padding: 3px 20px;
    font-size: 12px;
    line-height: 1.42857143;
    color: #777;
    white-space: nowrap
}
.dropdown-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 990
}
.pull-right > .dropdown-menu {
    right: 0;
    left: auto
}
.dropup .caret, .navbar-fixed-bottom .dropdown .caret {
    content: "";
    border-top: 0;
    border-bottom: 4px dashed;
    border-bottom: 4px solid \9
}
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: 2px
}
@media (min-width: 820px) {
    .navbar-right .dropdown-menu {
        right: 0;
        left: auto
    }

    .navbar-right .dropdown-menu-left {
        right: auto;
        left: 0
    }
}
.nav {
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}
.nav > li {
    position: relative;
    display: block
}
.nav > div > a {
    position: relative;
    display: block;
    padding: 10px 8px;

}
.nav > div > a:focus, .nav > div > a:hover {
    text-decoration: none;
    background-color: #eee
}
.nav > div.disabled > a {
    color: #777
}
.nav > div.disabled > a:focus, .nav > div.disabled > a:hover {
    color: #777;
    text-decoration: none;
    cursor: not-allowed;
    background-color: transparent
}
.nav .open > a, .nav .open > a:focus, .nav .open > a:hover {
    background-color: #eee;
    border-color: #337ab7
}

.nav > div > a > img {
    max-width: none
}
.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}
.container-navbar {
    width: 100%;
    display: block;
    min-height: 50px;
    min-width: 600px;
    height: 50px;
    position: relative;
}
.navbar {
    position: relative;
    z-index: 1;
    min-height: 50px;
}
.bgtop{
    background-color:#fff;
    /*background-image:url(images/bg_top.jpg);*/
    background-repeat:no-repeat;
    background-position:top right;
}
form{
    margin: 0px;
}


@media (min-width: 820px) {
    .navbar-header {
        float: left
    }
}

.navbar-collapse {
    padding-right: 15px;
    padding-left: 15px;
    overflow-x: visible;
    -webkit-overflow-scrolling: touch;
    border-top: 1px solid transparent;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1)
}

.navbar-collapse.in {
    overflow-y: auto
}
@media (min-width: 820px) {
    .navbar-collapse {
        width: auto;
        border-top: 0;
        -webkit-box-shadow: none;
        box-shadow: none
    }

    .navbar-collapse.collapse {
        display: block !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important
    }

    .navbar-collapse.in {
        overflow-y: visible
    }

    .navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse, .navbar-static-top .navbar-collapse {
        padding-right: 0;
        padding-left: 0
    }
}

.navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {
    max-height: 340px
}

@media (max-device-width: 480px) and (orientation: landscape) {
    .navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {
        max-height: 200px
    }
}

.container-fluid > .navbar-collapse, .container-fluid > .navbar-header, .container > .navbar-collapse, .container > .navbar-header {
    margin-right: -15px;
    margin-left: -15px
}

@media (min-width: 820px) {
    .container-fluid > .navbar-collapse, .container-fluid > .navbar-header, .container > .navbar-collapse, .container > .navbar-header {
        margin-right: 0;
        margin-left: 0
    }
}

.navbar-static-top {
    z-index: 1000;
    border-width: 0 0 1px
}

@media (min-width: 820px) {
    .navbar-static-top {
        border-radius: 0
    }
}

.navbar-fixed-bottom, .navbar-fixed-top {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030
}
.boxOpcoes{
    width: 271px;
}
.logo-topo{
    display: none;
}
.logo-nav-bar{
    display: inline-block;
    margin-top: 8px;
    margin-left: 14px;
}
@media (min-width: 820px) {
    .navbar-fixed-bottom, .navbar-fixed-top {
        border-radius: 0
    }
    .logo-topo{
        display: block;
    }
    .logo-nav-bar{
        display: none;
    }
}

.navbar-fixed-top {
    top: 0;
    border-width: 0 0 1px
}

.navbar-fixed-bottom {
    bottom: 0;
    margin-bottom: 0;
    border-width: 1px 0 0
}

.navbar-brand {
    float: left;
    height: 50px;
    padding: 15px 15px;
    font-size: 18px;
    line-height: 20px
}

.navbar-brand:focus, .navbar-brand:hover {
    text-decoration: none
}

.navbar-brand > img {
    display: block
}

@media (min-width: 820px) {
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand {
        margin-left: -15px
    }

}

.navbar-toggle {
    position: relative;
    float: right;
    padding: 9px 10px;
    margin-top: 8px;
    margin-right: 15px;
    margin-bottom: 8px;
    background-color: transparent;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px
}

.navbar-toggle:focus {
    outline: 0
}

.navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px
}

.navbar-toggle .icon-bar + .icon-bar {
    margin-top: 4px
}

@media (min-width: 820px) {
    .navbar-toggle {
        display: none
    }
}

.navbar-nav {
    float: left;
    margin: 0
}
.navbar-nav > div{
    float: left
}
.navbar-nav > div > a {
    padding-top: 15px;
    padding-bottom: 13px;
    line-height: 20px;
}

@media (max-width: 820px) {
    .navbar-nav .open .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        -webkit-box-shadow: none;
        box-shadow: none
    }

    .navbar-nav .open .dropdown-menu .dropdown-header, .navbar-nav .open .dropdown-menu > li > a {
        padding: 5px 15px 5px 25px
    }

    .navbar-nav .open .dropdown-menu > li > a {
        line-height: 20px
    }

    .navbar-nav .open .dropdown-menu > li > a:focus, .navbar-nav .open .dropdown-menu > li > a:hover {
        background-image: none
    }
}
.navbar-nav > div:first-child a {
    padding-top: 19px;
    padding-bottom: 17px
}

.navbar-form {
    padding: 10px 15px;
    margin-top: 8px;
    margin-right: -15px;
    margin-bottom: 8px;
    margin-left: -15px;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1)
}

@media (min-width: 820px) {
    .navbar-form .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }

    .navbar-form .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }

    .navbar-form .form-control-static {
        display: inline-block
    }

    .navbar-form .input-group {
        display: inline-table;
        vertical-align: middle
    }

    .navbar-form .input-group .form-control, .navbar-form .input-group .input-group-addon, .navbar-form .input-group .input-group-btn {
        width: auto
    }

    .navbar-form .input-group > .form-control {
        width: 100%
    }

    .navbar-form .control-label {
        margin-bottom: 0;
        vertical-align: middle
    }

    .navbar-form .checkbox, .navbar-form .radio {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }

    .navbar-form .checkbox label, .navbar-form .radio label {
        padding-left: 0
    }

    .navbar-form .checkbox input[type=checkbox], .navbar-form .radio input[type=radio] {
        position: relative;
        margin-left: 0
    }

    .navbar-form .has-feedback .form-control-feedback {
        top: 0
    }
}

@media (max-width: 820px) {
    .navbar-form .form-group {
        margin-bottom: 5px
    }

    .navbar-form .form-group:last-child {
        margin-bottom: 0
    }
}

@media (min-width: 820px) {
    .navbar-form {
        width: auto;
        padding-top: 0;
        padding-bottom: 0;
        margin-right: 0;
        margin-left: 0;
        border: 0;
        -webkit-box-shadow: none;
        box-shadow: none
    }
}

.navbar-nav > li > .dropdown-menu {
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
    margin-bottom: 0;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.navbar-btn {
    margin-top: 8px;
    margin-bottom: 8px
}

.navbar-btn.btn-sm {
    margin-top: 10px;
    margin-bottom: 10px
}

.navbar-btn.btn-xs {
    margin-top: 14px;
    margin-bottom: 14px
}

.navbar-text {
    margin-top: 19px;
    margin-bottom: 15px;
    margin-right: 3px;
    margin-left: 3px
}


.navbar-left {
        float: left !important
}

.navbar-right {
        float: right !important;
        margin-right: -15px
}

.navbar-right ~ .navbar-right {
        margin-right: 0
}
.navbar-default {
    background-color: #094771;
    box-shadow:0 3px 2px 0 rgba(0, 0, 0, 0.2), 0 3px 9px 0 rgba(0, 0, 0, 0.19);
    border-color: #e7e7e7
}

.navbar-default .navbar-brand {
    color: #777
}

.navbar-default .navbar-brand:focus, .navbar-default .navbar-brand:hover {
    color: #5e5e5e;
    background-color: transparent
}

.navbar-default .navbar-text {
    color: #777
}

.navbar-default .navbar-nav > li > a {
    color: #777
}

.navbar-default .navbar-nav > div > a:focus, .navbar-default .navbar-nav > div > a:hover {
       background-color: rgba(0, 0, 0, 0.18);
       color : #ffffff;
}

.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:focus, .navbar-default .navbar-nav > .active > a:hover {
    color: #555;
    background-color: #e7e7e7
}

.navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:focus, .navbar-default .navbar-nav > .disabled > a:hover {
    color: #ccc;
    background-color: transparent
}

.navbar-default .navbar-toggle {
    border-color: #ddd
}

.navbar-default .navbar-toggle:focus, .navbar-default .navbar-toggle:hover {
    background-color: #ddd
}

.navbar-default .navbar-toggle .icon-bar {
    background-color: #888
}

.navbar-default .navbar-collapse, .navbar-default .navbar-form {
    border-color: #e7e7e7
}

.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:focus, .navbar-default .navbar-nav > .open > a:hover {
    color: #555;
    background-color: #e7e7e7
}

    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
        color: #777
    }

    .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus, .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover {
        color: #333;
        background-color: transparent
    }

    .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover {
        color: #555;
        background-color: #e7e7e7
    }

    .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover {
        color: #ccc;
        background-color: transparent
    }
 .navbar-default .navbar-link {
    color: #777
}

.navbar-default .navbar-link:hover {
    color: #333
}

.navbar-default .btn-link {
    color: #777
}

.navbar-default .btn-link:focus, .navbar-default .btn-link:hover {
    color: #333
}

.navbar-default .btn-link[disabled]:focus, .navbar-default .btn-link[disabled]:hover, fieldset[disabled] .navbar-default .btn-link:focus, fieldset[disabled] .navbar-default .btn-link:hover {
    color: #ccc
}


.list-group-item.active > .badge, .nav-pills > .active > a > .badge {
    color: #337ab7;
    background-color: #fff
}
.nav-pills > li > a > .badge {
    margin-left: 3px
}
.center-block {
    display: block;
    margin-right: auto;
    margin-left: auto
}

.pull-right {
    float: right !important
}

.pull-left {
    float: left !important
}
.row {
    margin-right: -15px;
    margin-left: -15px
}
.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: left;
}
.col-md-12 {
    width: 100%;
}
.col-md-11 {
    width: 91.66666667%;
}
.col-md-10 {
    width: 83.33333333%;
}
.col-md-9 {
    width: 75%;
}
.col-md-8 {
    width: 66.66666667%;
}
.col-md-7 {
    width: 58.33333333%;
}
.col-md-6 {
    width: 50%;
}
.col-md-5 {
    width: 41.66666667%;
}
.col-md-4 {
    width: 33.33333333%;
}
.col-md-3 {
    width: 25%;
}
.col-md-2 {
    width: 16.66666667%;
}
.col-md-1 {
    width: 8.33333333%;
}
.col-md-pull-12 {
    right: 100%;
}
.col-md-pull-11 {
    right: 91.66666667%;
}
.col-md-pull-10 {
    right: 83.33333333%;
}
.col-md-pull-9 {
    right: 75%;
}
.col-md-pull-8 {
    right: 66.66666667%;
}
.col-md-pull-7 {
    right: 58.33333333%;
}
.col-md-pull-6 {
    right: 50%;
}
.col-md-pull-5 {
    right: 41.66666667%;
}
.col-md-pull-4 {
    right: 33.33333333%;
}
.col-md-pull-3 {
    right: 25%;
}
.col-md-pull-2 {
    right: 16.66666667%;
}
.col-md-pull-1 {
    right: 8.33333333%;
}
.col-md-pull-0 {
    right: auto;
}
.col-md-push-12 {
    left: 100%;
}
.col-md-push-11 {
    left: 91.66666667%;
}
.col-md-push-10 {
    left: 83.33333333%;
}
.col-md-push-9 {
    left: 75%;
}
.col-md-push-8 {
    left: 66.66666667%;
}
.col-md-push-7 {
    left: 58.33333333%;
}
.col-md-push-6 {
    left: 50%;
}
.col-md-push-5 {
    left: 41.66666667%;
}
.col-md-push-4 {
    left: 33.33333333%;
}
.col-md-push-3 {
    left: 25%;
}
.col-md-push-2 {
    left: 16.66666667%;
}
.col-md-push-1 {
    left: 8.33333333%;
}
.col-md-push-0 {
    left: auto;
}
.col-md-offset-12 {
    margin-left: 100%;
}
.col-md-offset-11 {
    margin-left: 91.66666667%;
}
.col-md-offset-10 {
    margin-left: 83.33333333%;
}
.col-md-offset-9 {
    margin-left: 75%;
}
.col-md-offset-8 {
    margin-left: 66.66666667%;
}
.col-md-offset-7 {
    margin-left: 58.33333333%;
}
.col-md-offset-6 {
    margin-left: 50%;
}
.col-md-offset-5 {
    margin-left: 41.66666667%;
}
.col-md-offset-4 {
    margin-left: 33.33333333%;
}
.col-md-offset-3 {
    margin-left: 25%;
}
.col-md-offset-2 {
    margin-left: 16.66666667%;
}
.col-md-offset-1 {
    margin-left: 8.33333333%;
}
.col-md-offset-0 {
    margin-left: 0;
}
.col-celula{
    display: table-cell;
}
.col-vert-align{
    position: relative !important;
    text-align: center !important;
    height: auto !important;
    vertical-align: middle !important;
}
 .img-responsive{
    display: block;
    max-width: 100%;
    height: auto
}

.navbar-collapse {
    padding-right: 15px;
    padding-left: 15px;
    overflow-x: visible;
    -webkit-overflow-scrolling: touch;
    border-top: 1px solid transparent;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1)
}

.navbar-collapse.in {
    overflow-y: auto
}

@media (min-width: 820px) {
    .navbar-collapse {
        width: auto;
        border-top: 0;
        -webkit-box-shadow: none;
        box-shadow: none
    }

    .navbar-collapse.collapse {
        display: block !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important
    }

    .navbar-collapse.in {
        overflow-y: visible
    }

    .navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse, .navbar-static-top .navbar-collapse {
        padding-right: 0;
        padding-left: 0
    }
}

.navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {
    max-height: 340px
}

@media (max-device-width: 480px) and (orientation: landscape) {
    .navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {
        max-height: 200px
    }
}

.container-fluid > .navbar-collapse, .container-fluid > .navbar-header, .container > .navbar-collapse, .container > .navbar-header {
    margin-right: -15px;
    margin-left: -15px
}
.fotoUsuario{
    opacity: 1;
    transition: all 1s;
}
.icon-hidden{
    visibility:hidden;
    position: absolute;
    top: 21px;
    left: 22px;
    font-size: 15px;
    color: white;
}
.fotoUsuario:hover .icon-hidden{
    visibility: visible;
}
.fotoUsuario:hover > div > img{
    opacity: 0.4;
    transition: all 0.6s;
}

@media (min-width: 820px) {
    .container-fluid > .navbar-collapse, .container-fluid > .navbar-header, .container > .navbar-collapse, .container > .navbar-header {
        margin-right: 0;
        margin-left: 0
    }
}

/*------------------------------------------------------Inicio Carregando------------------------------------------------------------------*/
.spinnerCarregando {
    margin: 100px auto 0;
    width: 70px;
    text-align: center;
}

.spinnerCarregando > div {
    width: 18px;
    height: 18px;
    background-color: #094771;

    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinnerCarregando .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.spinnerCarregando .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {
    0%, 80%, 100% { -webkit-transform: scale(0) }
    40% { -webkit-transform: scale(1.0) }
}

@keyframes sk-bouncedelay {
    0%, 80%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    } 40% {
          -webkit-transform: scale(1.0);
          transform: scale(1.0);
      }
}
/*------------------------------------------------------*/

@-webkit-keyframes ball-scale-ripple-multiple {
    0% {
        -webkit-transform: scale(.1);
        transform: scale(.1);
        opacity: 1
    }
    70% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: .7
    }
    100% {
        opacity: 0
    }
}

@keyframes ball-scale-ripple-multiple {
    0% {
        -webkit-transform: scale(.1);
        transform: scale(.1);
        opacity: 1
    }
    70% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: .7
    }
    100% {
        opacity: 0
    }
}

.ball-scale-ripple-multiple {
    position: relative;
    margin-left: calc(50% - 25px);
    -webkit-transform: translateY(-25px);
    -ms-transform: translateY(-25px);
    transform: translateY(-25px)
}

.ball-scale-ripple-multiple > div:nth-child(0) {
    -webkit-animation-delay: -.2s;
    animation-delay: -.2s
}

.ball-scale-ripple-multiple > div:nth-child(1) {
    -webkit-animation-delay: 0s;
    animation-delay: 0s
}

.ball-scale-ripple-multiple > div:nth-child(2) {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
}

.ball-scale-ripple-multiple > div:nth-child(3) {
    -webkit-animation-delay: .4s;
    animation-delay: .4s
}

.ball-scale-ripple-multiple > div {
    position: absolute;
    top: 0;
    left: 0;
    width: 50px;
    height: 50px;
    border-radius: 100%;
    border: 2px solid #fff;
    -webkit-animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(.21, .53, .56, .8);
    animation: ball-scale-ripple-multiple 1.25s 0s infinite cubic-bezier(.21, .53, .56, .8)
}
.modal-carregando-ripple .rich-mpnl-mask-div-opaque.rich-mpnl-mask-div{
    background-color: #FFFFFF;
    filter: alpha(opacity=30);
    opacity: .6;
}
.modal-carregando-ripple .ball-scale-ripple-multiple > div{
    border: 2px solid #094771;
}
.bi-color-branco{
    color: #ffffff;
}
.bi-font-family{
    font-family: Arial;
}
.bi-text-verde,.bi-text-verde:visited,.bi-text-verde:hover{
    color: #00C350;
}
.box-filtro{
    width: 100%;
    height: 3em;
    line-height: 3em;
    background-color: #E5E5E5;
}
.box-footer{
    width: 100%;
    height: 4em;
    line-height: 4em;
    border-top: 1px solid #E5E5E5;
}
.box-filtro > div,.box-footer > div {
    padding-bottom: 2em;
    margin: 0px 1.5em 0 1.5em;
}
.box-filtro > div:first-child,.box-footer > div:first-child {
    margin: 0px 0px 0px 1.5em ;
}
.box-filtro > div:last-child,.box-footer > div:last-child {
    margin: 0px 1.5em 0px 0px ;
}
.botao-checkbox{
    text-decoration: none;
}
.texto-cor-azul.botao-checkbox:not(.desabilitado):hover{
    color: #2484B0;
    text-decoration: none;
}
.botao-checkbox.desabilitado{
    color : #777;
}
.botao-checkbox .label{
  font-family: Arial;
  padding: 0px;
  margin-left: 0.5em;
  color: #777;
}
.box-filtro{
    width: 100%;
    height: 3em;
    line-height: 3em;
    background-color: #E5E5E5;
}
.box-footer{
    width: 100%;
    height: 4em;
    line-height: 4em;
    border-top: 1px solid #E5E5E5;
}
.box-filtro > div,.box-footer > div {
    padding-bottom: 2em;
    margin: 0px 1.5em 0 1.5em;
}
.box-filtro > div:first-child,.box-footer > div:first-child {
    margin: 0px 1.5em 0px 0px;
}
.box-filtro > div:last-child,.box-footer > div:last-child {
    margin: 0px 0px 0px 1.5em;
}
.texto-cor-azul.botao-checkbox:hover{
    color: #2484B0;
}
.botao-checkbox .icon{
    padding: 2px;
}
.box-separador{
    width: 100%;
    margin-top: 10px;
    margin-bottom: 10px;
    height: 1px;
    background-color: #E5E5E5;
}
.box-separador-popUp{
    width: 100%;
    height: 1px;
    background-color: #E5E5E5;
}
.bi-bg-verde{
    background-color: #C4F5DC;
}
.modal-carregando-ripple,.modal-carregando-ripple .rich-mpnl-body ,.modal-carregando-ripple .rich-mpnl-content,.modal-carregando-ripple .rich-mpnl-shadow{
    background-color: transparent;
    border: none;
}
/*----------------------------------------------------------INICIOS BOXES------------------------------------------------------------*/
.container-box-blank{
    width:calc(100% - 68px);
    margin: 34px 40px 34px 40px;
    position: relative;
}
.container-box{
    width:calc(100% - 68px);
    margin: 34px 40px 34px 40px;
    min-height: calc(100vh - 144px);
    position: relative;
    -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    background-color: #ffffff;
    transition: transform .1s ease-out;
    -moz-transition: -moz-transform .1s ease-out;
    -o-transition: -o-transform .1s ease-out;
    -webkit-transition: -webkit-transform .1s ease-out;
}
.container-box-bi{
    position: relative;
    -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    background-color: #ffffff;
    transition: transform .1s ease-out;
    -moz-transition: -moz-transform .1s ease-out;
    -o-transition: -o-transform .1s ease-out;
    -webkit-transition: -webkit-transform .1s ease-out;
}
.form-flat .tituloCampos{
    text-transform: uppercase;
    font-weight: bold !important;
}

.form-flat .text{
    font-size: 14px;
    color: #777;
    display: table-cell;
    vertical-align: middle;
}
.form-flat .tituloCampos,.form-flat  .tituloCampos:visited {
    font: 14px Arial;
    color: #29ABE2;
    text-align: right;
}
.form-flat a.tituloCampos:hover{
    color: #29ABE2;
}
.tituloCaixaAlta{
    text-transform: uppercase;
    font-weight: bold;
}
.mensagem{
    font: 14px Arial;
    color: #777777;
    text-align: right;
    vertical-align: middle;
}
.container-box-header{
    width: 100%;
    height: 4em;
    line-height: 4em;
    border-bottom: 1px solid #E5E5E5;
}
.tabelaSimplesCustom tr.linhaImpar{
    font: 14px Arial;
    color: #777777;
    background-color: #FFFFFF;
    font-family:'Arial';
}
.fa-icon-asterisk,.fa-icon-double-asterisk{
    vertical-align: middle !important;
    margin-left:3px;
    margin-right:3px;
    font-size: 10px;
    color: #777777;
}
.quote-text:before{
    content: "\f10d";
    font-family: FontAwesome;
    padding-right: 3px;

}
.quote-text:after{
    content: "\f10e";
    padding-left: 3px;
    font-family: FontAwesome;

}
.container-header-titulo{
    font-family: Arial;
    text-decoration: none;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
    margin-top: 5px;
}
.textoCorAzul{
    color: #29ABE2;
}
.margin-box{
    display: block;
    padding-bottom: 2em;
    width: calc(100% - 3em);
    margin-left: 1.5em;
    margin-right: 1.5em;
    margin-top: 1.5em;
}
/*-------------------------------------------------------------------CONTEUDO BLOG----------------------------------------------------------------*/
.container-cards {
    display: block;
    width: 100%;
    margin-top: 20px;
    margin-bottom: 20px;
}
.card-blog {
    margin-right: 2.3%;
    display: inline-block;
    -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
    -moz-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
    background-color: #ffffff;
    width: 30%;
    height: 130px;
}
.headerBlog{
    font-size: 16px;
    color:#777777;
    font-weight: bold;
    text-align: left;
    display: block;
    margin-left: 45px;
    margin-bottom: 10px;
}
.card-blog:nth-child(2){
    margin-left: 3%;
}
.borderLeftWhite{
    border-left: 1px solid #fff;
}
.card-blog img {
    width: 125px;
    height: 100%;
}

.card-blog .conteudoArtigo {
    width: calc(100% - 145px);
    display: inline-block;
    height: calc(100% - 10px);
    margin: 10px;
    text-align: left;
    vertical-align: top;
}
.card-blog .conteudoArtigo .titulo-artigo,.card-blog .conteudoArtigo .texto-artigo{

    display: block;
}
.card-blog .titulo-artigo {
    font-family: Arial;
    font-size: 1em;
    color: #777777;
}

.card-blog .texto-artigo {
    font-family: Arial;
    font-size: 1.1em;
    color: #333333;
    font-weight: bold;
}
/*----------------------------------------------------------------------B.I. Combo Box -----------------------------------------------------------------------------*/
div.cb-container {
    position: relative;
    display: inline-block;
    background-color: #fff;
    border-radius: 3px;
}
td.cb-container{
    padding:0;
    position: relative;
    display: inline-block;
    background-color: #fff;
    border-radius: 0px;
}
td.cb-container select {
    background-image: none;
    position: relative;
    z-index: 2;
    width: 100%;
    padding-top: 0px;
    color: #29ABE2;
    border-left: 1px solid #DDDDDD !important;
    background-color: transparent !important;
    font-size: 1.1em;
    padding-right: 25px;
    line-height: 45px;
    height: 3.3em;
    padding-left: 8px;
    -webkit-appearance: none;
    outline:none;
    border-right: none !important;
    border-top: none !important;
    border-bottom: none !important;
}
div.cb-container select {
    background-image: none;
    position: relative;
    z-index: 1;
    width: 100%;
    padding-top: 0px;
    color: #777777;
    background-color: transparent !important;
    font-size: 1.1em;
    padding-right: 25px;
    line-height: 2.4em;
    height: 2.4em;
    padding-left: 8px;
    -webkit-appearance: none;
    border-color: #DDDDDD !important;
    border-style: solid;
    outline:none;
    border-radius: 3px;
}
div.cb-container.input-group select{
    -webkit-border-bottom-right-radius: 0px;
    -webkit-border-top-right-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -moz-border-radius-topright: 0px;
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px;
    border-right: none;
}
.cb-container:after {
    font-family: 'FontAwesome';
    font-size: calc( (100vw / 100) * 1.1 );
    position: absolute;
    content: "\f0d7";
    right: 0px;
    top: 0;
    line-height: 2.4em;
    z-index: 0;
    padding-right: 10px;
    color: #777;

}
.font-size-Em-max .cb-container:after {
    font-size: calc((1368px / 100) * 1.1 ) !important;
}
.cb-container.h30px:after {
    line-height: 30px;
}
div.cb-container .fa-icon-caret-down{
    position: absolute;
    right: 4%;
    top: 35%;
    z-index: 1;
}
div.cb-container select option:hover{
    background-color: red;
}
div.cb-container option, div.cb-container select:focus, div.cb-container select, div.cb-container select:active, div.cb-container select:hover {
    font-family: Arial;
    color: #29ABE2;
}

/*-----------------------------------------------------Inicio Scroll Custom-----------------------------------------------*/
.scrollPureCustom{
    margin: 10px 0px 10px 0px;
}
.scrollPureCustom .rich-datascr-button{
    background-color: #cdcdcd;
    border-style: none;
    font-size: 0;
    width: 27px;
    height: 26px;
    border:none;
    background-image: none;
}
.scrollPureCustom .rich-dtascroller-table tbody tr td:not(.rich-datascr-button){
    background-color: #cdcdcd;
    font-size: 14px;
    color: #333;
    min-width: 30px;
    height: 26px;
}
.scrollPureCustom .rich-datascr-inact{
    border-top: none;
}
.scrollPureCustom .rich-dtascroller-table tbody tr td.rich-datascr-act{
    background-color: #cdcdcd;
    border: 1px solid #333;
    font-size: 14px;
    border-radius: 3px;
    color: #333;
}
.scrollPureCustom .rich-dtascroller-table{
    border:none;
    border-style: none;
    background-color: transparent;
    border-spacing: 0;
}
.scrollPureCustom  .rich-dtascroller-table tbody tr td:hover{
    text-decoration: none;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#1a000000', GradientType=0);
    background-image: -webkit-gradient(linear,0 0,0 100%,from(transparent),color-stop(40%,rgba(0,0,0,.05)),to(rgba(0,0,0,.1)));
    background-image: -webkit-linear-gradient(transparent,rgba(0,0,0,.05) 40%,rgba(0,0,0,.1));
    background-image: -moz-linear-gradient(top,rgba(0,0,0,.05) 0,rgba(0,0,0,.1));
    background-image: -ms-linear-gradient(transparent,rgba(0,0,0,.05) 40%,rgba(0,0,0,.1));
    background-image: -o-linear-gradient(transparent,rgba(0,0,0,.05) 40%,rgba(0,0,0,.1));
    background-image: linear-gradient(transparent,rgba(0,0,0,.05) 40%,rgba(0,0,0,.1));
}
.scrollPureCustom .rich-datascr-button:after{
    font-family: FontAwesome;
    font-size: 16px;
    color: #222;
    vertical-align: middle;
}
.scrollPureCustom .rich-datascr-button:first-child:after{
    content: "\f100";
}
.scrollPureCustom .rich-datascr-button:nth-child(2):after{
    content: "\f104";
}
.scrollPureCustom .rich-datascr-button:nth-last-child(3),.scrollPureCustom .rich-datascr-button:nth-child(3){
    display: none;
}
.scrollPureCustom .rich-datascr-button:nth-last-child(2):after{
    content: "\f105";
}
.scrollPureCustom .rich-datascr-button:last-child:after{
    content: "\f101";
}
/*---------------------------------------Inicio Tabela Produtos---------------------------------*/

table.tabelaSimplesCustom{
    border-left: none;
    border-top: none;
    background-color: transparent;
}
table.dataTable.tabelaSimplesCustom{
    margin-top: 25px;
    margin-bottom: 25px;
}
table.tabelaSimplesCustom:not(.dataTable){
    width: 100%;
}
table.tabelaSimplesCustom > tbody .rich-table-subheadercell:first-child{
    border:none;
    text-align: left;
    margin-left: 10px;
}
table.tabelaSimplesCustom > tbody .rich-table-row .rich-table-cell:first-child > span,table.tabelaSimplesCustom > tbody > tr >  td:first-child > span,
table.tabelaSimplesCustom > tbody >  tr > td:first-child >  a,table.tabelaSimplesCustom > tbody tr td:first-child > span,
table.tabelaSimplesCustom > tbody .rich-table-row .rich-table-cell:first-child > a,table.tabelaSimplesCustom  > tbody > tr > td:first-child > div{
    margin-left: 10px;
    text-align: left;
}
table.tabelaSimplesCustom > tbody .rich-table-row .rich-table-cell:last-child > span,table.tabelaSimplesCustom > tbody tr td:last-child >  span,
table.tabelaSimplesCustom > tbody tr td:last-child >  a,table.tabelaSimplesCustom tbody .rich-table-row .rich-table-cell:last-child > a,
table.tabelaSimplesCustom > thead tr .rich-table-subheadercell:last-child > span{
    margin-right: 10px;
}
table.tabelaSimplesCustom > tbody > tr > td  a span:nth-child(2){
    margin-left: 5px;
}
table.tabelaSimplesCustom > thead .rich-table-subheadercell:first-child > div{
    margin-left: 10px;
}
.container-bi table.tabelaSimplesCustom > tbody .rich-table-row .rich-table-cell:first-child span,.container-bi table.tabelaSimplesCustom > tbody tr td:first-child span,
.container-bi table.tabelaSimplesCustom > tbody tr td:first-child a,.container-bi table.tabelaSimplesCustom  tbody .rich-table-row .rich-table-cell:first-child a{
    margin-left: 6.5%;
}
table.tabelaSimplesCustom .rich-table-subheadercell{
    border-right: none;
}
table.tabelaSimplesCustom:not(.dataTable) .rich-table-subheadercell{
    font-size: 1vw;
}
table:not(.dataTable).tabelaSimplesCustom > tbody  > .rich-table-row > td:not(:empty),table:not(.dataTable).tabelaSimplesCustom > tbody  > tr > td:not(:empty){
    height: 2.5em;
}
table.tabelaSimplesCustom.border-color-c2 tbody .rich-table-row:first-child td:not(:empty),
table.tabelaSimplesCustom:not(.semBorda) tbody .rich-table-row:first-child:empty + .rich-table-row,
table.tabelaSimplesCustom:not(.semBorda)  tbody tr:not(:empty):first-child:not(.hidden),
table.tabelaSimplesCustom.border-color-c2 tbody tr:not(:empty):first-child td:not(:empty){
    border-top: 1px solid #DDE7E7;
}
table.semPrimeiraBorda tbody tr td{
    border-top: none;
}
table.tabelaSimplesCustom:not(.showCellEmpty) tr td:empty{
  display: none;
}
table.tabelaSimplesCustom .rich-table-thead{
    border-bottom: none;
}
table.tabelaSimplesCustom:not(.dataTable) .rich-table-subheader{
    background-color: transparent;
    height: 2.5em;
}
.font-size-Em-max table:not(.dataTable).tabelaSimplesCustom > tbody  > .rich-table-row > td:not(:empty),.font-size-Em-max table:not(.dataTable).tabelaSimplesCustom > tbody  > tr > td:not(:empty){
    height: 35px;
}
.font-size-Em-max table.tabelaSimplesCustom:not(.dataTable) .rich-table-subheader{
    background-color: transparent;
    height: 35px;
}
table.tabelaSimplesCustom:not(.semBorda) > tbody .rich-table-cell,table.tabelaSimplesCustom:not(.semBorda) > tbody > tr > td:not(:empty){
    vertical-align: middle;
    font-family: Arial;
    border-bottom: 1px solid #DDE7E7;
    border-right: none;
}
table.tabelaSimplesCustom.semBorda > tbody .rich-table-cell{
    border-bottom: none;
    border-right: none;
}
table:not(.dataTable).tabelaSimplesCustom> tbody .rich-table-cell,table:not(.dataTable).tabelaSimplesCustom > tbody >  td:not(:empty){
    font-size: 1vw ;
}
table.tabelaSimplesCustom.border-color-c2 .rich-table-cell,table.tabelaSimplesCustom.border-color-c2 > tr > td{
    border-bottom: 1px solid #9E9E9E;
    border-right: none;
}
table.tabelaSimplesCustom.tabelaContOp tbody .rich-table-row .rich-table-cell:first-child a,table.tabelaSimplesCustom.tabelaContOp tbody .rich-table-row .rich-table-cell:first-child span{
    margin-left: 4.5%;
}
table.tabelaSimplesCustom.tabelaContOp tbody .rich-table-row .rich-table-cell:last-child a,table.tabelaSimplesCustom.tabelaContOp tbody .rich-table-row .rich-table-cell:last-child span{
    margin-right:0;
}
.dataTable.tabelaSimplesCustom thead .sorting_asc span:after {
    content: "\f0de";
    font-family: FontAwesome;
    font-weight: bold;
    margin-left: 10px;
    vertical-align: top;
    font-size: 14px;
    line-height: 14px;
}
.tabelaSimplesCustom:not(.noHover) > tbody > tr:hover {
    background-color: #e5e5e5 !important;
    cursor: pointer ;
}
.tabelasSemHover .tabelaSimplesCustom > tbody tr:hover {
    background-color: transparent !important;
    cursor: auto;
}
.dataTable.tabelaSimplesCustom thead .sorting_desc span:after {
    content: "\f0dd";
    font-family: FontAwesome;
    font-weight: bold;
    vertical-align: top;
    margin-left: 10px;
    font-size: 14px;
    line-height: 7px;
}
.tabelaSimplesCustom:not(.dataTable) .rich-sort-icon{
 vertical-align: bottom;
}
/*-----------------------------------------Input Clean---------------------------------*/

.novaModal input[type='text']:not(.inputAntigo),.formNovo input[type='text'],.inputTextClean,.dateTimeCustom .rich-calendar-input{
    padding: 0px 5px;
    border: 1px solid #DDDDDD !important;
    background-color: #fff !important;
    border-radius: 3px;
    font-size: 1.1em !important;
    color: #B4B4B4 !important;
    background-image: none;
    -webkit-transition: all .6s;
    -moz-transition: all .6s;
    -ms-transition: all .6s;
    -o-transition: all .6s;
    transition: all .6s;

}
.novaModal input[type='text']:not(.inputCartao):not(.inputAntigo),.formNovo input[type='text']:not(.inputCartao),.inputTextClean:not(.inputCartao),.dateTimeCustom .rich-calendar-input:not(.inputCartao){
    background-image: none !important;
}
.novaModal input[type='text']:not(.inputAntigo),.formNovo input[type='text'],.inputTextClean,.dateTimeCustom .rich-calendar-input{
    height: 2.4em;
}

.novaModal input[type='text']:not(.inputAntigo),.formNovo input[type='text'].noTransitions,.inputTextClean.noTransitions{
    -webkit-transition: border .6s;
    -moz-transition: border .6s;
    -ms-transition: border .6s;
    -o-transition: border .6s;
    transition: border .6s;
}
.novaModal input[type='text'].bordaVermelha:not(.inputAntigo),.formNovo input[type='text'].bordaVermelha,.inputTextClean.bordaVermelha{
    border : 1px solid #FF5555 !important
}
.inputTextClean::-webkit-input-placeholder {
    color: #cfcfcf;
}
.bordaCinza{
    border: 1px solid #777777;
}
.bordaCinzaEsquerda{
    border-left: 1px solid #777777;
}
.novaModal input[type='text']:not(.inputAntigo):hover,.inputTextClean:hover,.formNovo input[type='text']:hover,.dateTimeCustom .rich-calendar-input:hover{
    border: 1px solid #777777 !important;
    outline: none;
}
.novaModal input[type='text']:not(.inputAntigo):focus,.inputTextClean:focus,.formNovo input[type='text']:focus,.dateTimeCustom .rich-calendar-input:focus{
    border: 1px solid #29ABE2 !important;
    outline: none;
    color: #777777 !important;
    -webkit-box-shadow:  0px 0px 8px 0px rgba(41, 171, 226, 0.7);
    -moz-box-shadow:  0px 0px 8px 0px rgba(41, 171, 226, 0.7);
    box-shadow: 0px 0px 8px 0px rgba(41, 171, 226, 0.7);
}
.novaModal input[type='text']:not(.inputAntigo).noBorderLeft,.formNovo input[type='text'].noBorderLeft,.inputTextClean.noBorderLeft{
    -webkit-border-bottom-left-radius: 0px;
    -webkit-border-top-left-radius: 0px;
    -moz-border-radius-bottomleft: 0px;
    -moz-border-radius-topleft: 0px;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
}

/*--------------------------------------------------------------------Date Picket Custom---------------------------------------------------*/
.dateTimeCustom{
    max-height:3.3em;
    position: relative;
}
.dateTimeCustom > span{
   display: inline-flex;
}
.dateTimeCustom .rich-calendar-exterior{
    list-style: none;
    border-radius: 5px;
    z-index: 3 !important;
    background-color: #fff;
    margin: 2px 0;
    padding: 4px;
    width: 19em;
    border-color: rgba(119, 119, 119, 0.4);
}
.dateTimeCustom.alignToRight .rich-calendar-exterior {
        right: 0px !important;
        left: auto !important;
}
.dateTimeCustom.alignToRight .rich-calendar-exterior:before {
    right: 6px;
    left: auto;
}
.dateTimeCustom.alignToRight .rich-calendar-exterior:after {
    right: 7px;
    left: auto;
}
.dateTimeCustom .rich-calendar-input{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.dateTimeCustom .rich-calendar-days{
    font-size: 14px;
    color: #333;
    width: 27px;
    font-weight: bold;
    height: 26px;
    text-align: center;
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.25);
}

.dateTimeCustom .rich-calendar-today:before{
    content: '';
    display: inline-block;
    border: solid transparent;
    border-width: 0 0 7px 7px;
    border-bottom-color: #337ab7;
    border-top-color: rgba(0, 0, 0, 0.2);
    position: absolute;
    bottom: 4px;
    right: 4px;
}
.dateTimeCustom .rich-calendar-editor-btn:not(.rich-calendar-editor-btn-selected):hover{
    border: 1px solid #eeeeee;
    background-color: #fff;
}
.dateTimeCustom .rich-calendar-date-layout-ok > .rich-calendar-time-btn{
    font-size: 16px;
    color: #fff;
    position: relative;
    border-radius: 3px;
    padding: 5px;
    text-align: center;
    border: 1px solid #fff;
    background-color: #337ab7;
}
.dateTimeCustom .rich-calendar-editor-container table > tbody  tr:first-child td:nth-child(1n+3) div{
    font-size: 0;
}
.dateTimeCustom .rich-calendar-editor-container  table > tbody  tr:first-child td:nth-last-child(1) > div:after{
    height: 100%;
    font-family: FontAwesome;
    vertical-align: middle;
    padding: 0px 10px 0px 10px;
    color: #333;
    font-size: 20px;
    content: "\f105";
}
.dateTimeCustom .rich-calendar-editor-container table > tbody  tr:first-child td:nth-last-child(2) > div:after{
    height: 100%;
    font-family: FontAwesome;
    vertical-align: middle;
    padding: 0px 10px 0px 10px;
    color: #333;
    font-size: 20px;
    content: "\f104";
}
.dateTimeCustom .rich-calendar-date-layout-cancel > .rich-calendar-time-btn{
    font-size: 16px;
    color: #777;
    position: relative;
    border-radius: 3px;
    padding: 5px;
    text-align: center;
    border: 1px solid #777;
    background-color: #eeeeee;
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.25);
}
.dateTimeCustom .rich-calendar-date-layout-cancel > .rich-calendar-time-btn > span{
    margin: 0;
}
.dateTimeCustom .rich-calendar-date-layout-ok > .rich-calendar-time-btn > span{
    margin: 0;
    float: none;
}
.dateTimeCustom .rich-calendar-cell, .rich-calendar-editor-btn {
    font-size: 16px;
    color: #777;
    position: relative;
    border-radius: 3px;
    padding: 5px;
    text-align: center;
    border: 1px solid #fff;
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.25);
}
.dateTimeCustom .rich-calendar-editor-btn-selected{
    background-color: #337ab7;
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.25);
    color: #fff;
}
.dateTimeCustom .rich-calendar-toolfooter .rich-calendar-tool-btn{
    font-size: 16px;
    color: #777;
    position: relative;
    border-radius: 3px;
    text-align: center;
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.25);
}
.dateTimeCustom .rich-calendar-cell{
    font-size: 16px;
    color: #777;
    position: relative;
    width: 27px;
    border-radius: 3px;
    height: 26px;
    text-align: center;
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.25);
}
.dateTimeCustom .rich-calendar-boundary-dates{
    color: #999;
    text-shadow: none;
}
.dateTimeCustom .rich-calendar-button[src~='/tronco/imagens_flat/calendar-button.svg']{
    height:100%;
}
.dateTimeCustom .rich-calendar-button[src$=".svg"]{
    height: 2.7em;
}
.dateTimeCustom .rich-calendar-header{
    padding-bottom: 2px;
}
.dateTimeCustom .rich-calendar-editor-layout-shadow{
    display: none;
}
.dateTimeCustom .rich-calendar-tool,.dateTimeCustom .rich-calendar-header,.dateTimeCustom .rich-calendar-month,.dateTimeCustom .rich-calendar-days,
.dateTimeCustom .rich-calendar-cell,.dateTimeCustom .rich-calendar-footer,.dateTimeCustom .rich-calendar-toolfooter,.dateTimeCustom .rich-calendar-date-layout,
.dateTimeCustom .rich-calendar-date-layout-split,.dateTimeCustom .rich-calendar-date-layout-ok,.dateTimeCustom .rich-calendar-date-layout-cancel{
    background-image: none;
    border: none;
    background-color: #fff;
}
.dateTimeCustom .rich-calendar-tool > div{
    height: 100%;
    width: 100%;
    font-size: 0;
}
.dateTimeCustom .rich-calendar-tool-btn{
    padding: 0;
    border-radius: 3px;
    text-shadow: 0px -1px 0px rgba(0, 0, 0, 0.25);
}
.dateTimeCustom .rich-calendar-tool-btn:hover,.dateTimeCustom .rich-calendar-cell:hover{
    background: #eeeeee;
    background-image: none;
    cursor: pointer;
    padding: 0;
    border: none;
}
.dateTimeCustom .rich-calendar-tool > div:after{
    height: 100%;
    font-family: FontAwesome;
    vertical-align: middle;
    padding: 0px 10px 0px 10px;
    color: #333;
    font-size: 20px;
}
.dateTimeCustom .rich-calendar-month{
    margin: 0px 30px 0px 30px;
    font-size: 16px;
    color: #333;

}
.dateTimeCustom .rich-calendar-tool:first-child > div:after{
    content: "\f100";
}
.dateTimeCustom .rich-calendar-tool:nth-child(2) > div:after{
    content: "\f104";
}
.dateTimeCustom .rich-calendar-tool:last-child{
    display: none;
}
.dateTimeCustom .rich-calendar-tool:nth-last-child(2) > div:after{
    content: "\f101";
}
.dateTimeCustom .rich-calendar-tool:nth-last-child(3)  > div:after{
    content: "\f105";
}
.dateTimeCustom .rich-calendar-exterior:before,
.dateTimeCustom .rich-calendar-exterior:after {
    content: '';
    display: inline-block;
    position: absolute;
}
.dateTimeCustom .rich-calendar-exterior:before {
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #cccccc;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    top: -7px;
    left: 7px;
}
.dateTimeCustom .rich-calendar-exterior:after {
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid white;
    top: -6px;
    left: 8px;
}
.dateTimeCustom .rich-calendar-exterior table thead tr:first-child th {
    cursor: pointer;
}
.dateTimeCustom .rich-calendar-exterior table thead tr:first-child th:hover {
    background: #eeeeee;
}
.dateTimeCustom .rich-calendar-exterior table td {
    height: 40px;
    line-height: 40px;
}
.dateTimeCustom .rich-calendar-exterior table td.cw {
    font-size: .8em;
    height: 20px;
    line-height: 20px;
    color: #777777;
}
.dateTimeCustom .rich-calendar-exterior table td.day {
    height: 20px;
    line-height: 20px;
    width: 20px;
}
.dateTimeCustom .rich-calendar-toolfooter{
    display: none;
}
.dateTimeCustom .rich-calendar-toolfooter:nth-last-child(2){
    text-align: center;
    display: block;
}
.dateTimeCustom .rich-calendar-editor-container{
    vertical-align: top;
}
.dateTimeCustom .rich-calendar-date-layout{
    height: 100%;
    width: 100%;
}
.dateTimeCustom .rich-calendar-editor-container > div{
    height: 100%;
    list-style: none;
    border-radius: 5px;
    background-color: #fff;
    padding: 4px;
    border:1px solid rgba(119, 119, 119, 0.4);
}
.dateTimeCustom .rich-calendar-today.rich-calendar-select:before {
    border-bottom-color: #fff;
}
.dateTimeCustom .rich-calendar-select,.dateTimeCustom .rich-calendar-select:hover{
     background-color: #337ab7;
    position: relative;
     color: #ffffff;
     text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.novaModal.grande .rich-mpnl-header,.novaModal.vw90 .rich-mpnl-header{
    padding: 6px 6px 6px 40px;
}
.novaModal .rich-mpnl-header{
    border:none;
    opacity: 1;
    background: transparent none;
    font-family: Arial;
    font-size: 16px;
    color: #fff;
    padding: 6px 48px 6px 28px;
    font-weight: bold;

}
/*------------------------------------------------------------ Modal Dialog ---------------------------------------*/
.novaModal.vw90 .rich-mp-container{
    width: 90vw;
    left: 5vw;
}
.novaModal.vAlignTop .rich-mp-container{
    top: 0 !important;
}
.novaModal.grande .rich-mp-container{
    width: 70vw;
    left: 15vw;
}
.novaModal.noMargin .rich-mpnl-body{
    padding: 0px;
    font-size: 1vw;
}
.novaModal .rich-mpnl-header-cell{
    height: 83px;
    background-color: #094771;
}

.novaModal.bg-vermelho .rich-mpnl-header-cell{
    height: 83px;
    background-color: red !important;
}

.novaModal.bg-amarelo .rich-mpnl-header-cell{
    height: 83px;
    background-color: #F6A700 !important;
}

.novaModal.grande .rich-mp-content-table{
    width: 70vw !important;
}
.novaModal.vw90 .rich-mp-content-table{
    width: 90vw !important;
}
.novaModal form{
    overflow: visible;
}
.novaModal .rich-mp-container{
    -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
    box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
}
.novaModal .rich-mpnl-shadow{
    display: none;
}
.novaModal.grande .rich-mpnl-content{
    width: 70vw;
}
.novaModal.vw90 .rich-mpnl-content{
    width: 90vw;
}
.novaModal .rich-mpnl-content{
    border:none;
    background-color: transparent;
    border-style: none;
    padding: 0;
}
.novaModal.grande .rich-mpnl-header{
    width: calc(70vw - 80px);
}
.novaModal.vw90 .rich-mpnl-header{
    width: calc(90vw - 80px);
}
.novaModal.grande .rich-mpnl-controls,.novaModal.vw90 .rich-mpnl-controls{
    right: 40px;
}
.novaModal .rich-mpnl-controls{
    top: 28px;
    right: 15px;
}
.novaModal.noOverflow .rich-mpnl-ovf-hd{
    overflow: visible;
}
.texto-cor-cinza{
    color:#777777;
}
.texto-cor-cinza-2{
    color:#333;
}
.texto-cor-cinza-3{
    color : #B4B4B4;
}
.texto-cor-zw{
    color : #094771;
}
.texto-cor-verde,.texto-cor-verde:visited{
    color:#2BAF50;
}
.botaoSecundario.texto-cor-verde:hover{
    color:rgb(10, 99, 35);
}
.tooltipster-content .tooltipElemento{
    display: block;
}
.checkbox_wrapper{
    position: relative;
    height: 16px;
    width: 17px;
}
.chk-fa-container{
    position: relative;
}
.chk-fa-container input[type="checkbox"] {
    opacity:0;
    cursor: pointer;
    padding: 4px;
    height: 13px;
    width: 14px;
    position: relative;
    top: 2px;
    left: 2px;
    z-index: 2;
}
.chk-fa-container input[type="checkbox"] + span {
    height: 13px;
    width: 15px;
    position: absolute;
    display:inline-block;
    padding: 0 0 0 0px;
    top: 2px;
    left: 2px;
    z-index: 1;
}
.chk-fa-container input[type="checkbox"] + span{
    background-image: url('../imagens_flat/checkbox-normal.png');

}
.chk-fa-container input[type="checkbox"]:hover + span{
    background-image: url('../imagens_flat/checkbox-hover.png');
}
.chk-fa-container input[type="checkbox"][disabled='disabled'] + span{
    background-image: url('../imagens_flat/checkbox-disabled.png');
}
.chk-fa-container input[type="checkbox"]:checked + span{
    background-image: url('../imagens_flat/checkbox-active.png');
}
.tabelaForm  > tbody > tr > td{
    background-color:transparent;
    border-top:solid 2px #fff;
    border-bottom:solid 2px #fff;
    padding:5px 0px;
}
.tabelaForm .par{
    background-color:transparent;
}
.tabelaForm .prata{
    background-image:none;
}
.tabelaForm{
    width: 75%;
    margin-left: 4.5%;
}
.panelMargin{
    margin-top: 20px;
    margin-bottom: 20px;
}
.container-botoes{
    width: 100%;
    text-align: center;
    height: 50px;
    line-height: 50px;
    display: inline-block;
}
.novaModal .tituloCampos {
    font-family: Arial;
    color: #777777;
    font-size: 14px;
    font-weight: bold;
    vertical-align: top;
}
.checkbox-fa input[type="checkbox"],.grupoRadioButton input[type="checkbox"]{
    display: none;
}
td.celulaCheck{
    position: relative;
}
td.celulaCheck:after{
    position: absolute;
    z-index: 1;
    content: "";
    width: 0;
    top: 0;
    left: 0;
    height: 100%;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -ms-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
}
td.colSelecionada:after{
    width: 100%;
    background-color: #DEF5FF;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -ms-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
}
.rowSelecionada{
    background-color: #DEF5FF;
}
.checkbox-fa span.texto-cor-cinza:before ,.grupoRadioButton span.texto-cor-cinza:before,.grupoRadioButton a.texto-cor-cinza:before {
    color : #29ABE2;
    margin-right: 0.2em;
    width: 10px;
    font-size: 1.1em;
    font-family: FontAwesome;
}
td .checkbox-fa span.texto-cor-cinza:before,td .checkbox-fa span.texto-cor-vermelho:before{
    margin-right: 8px;
}
.checkbox-fa span.texto-cor-cinza{
    font-family: Arial;
}
.checkbox-fa{
    cursor: pointer;
    position: relative;
    z-index: 2;
}
/*-----------------------------------------------------------------INICIO BOTOES----------------------------------------*/
.botaoSecundario,.botaoSecundario:visited{
    padding: 8px 20px 8px 20px;
    border-radius: 4px;
    background-color: #D5D2D2;
    text-decoration: none;
}
.botaoSecundario:not([class*="texto-cor"]):hover,.botaoSecundario:not([class*="texto-cor"]):visited,.botaoSecundario:not([class*="texto-cor"]){
    color: #777;
}
.botaoSecundario:hover{
    background-color: #BCB9B9;
    text-decoration: none;
}

.botaoPrimarioDesabilitado {
    padding: 8px 22px 8px 22px;
    border-radius: 4px;
    background-color: #D5D2D2;
    text-decoration: none;
    color: #B0B0B0; !important;
}

a.botaoPrimarioDesabilitado, a.botaoPrimarioGrandeDesabilitado{
    text-decoration: none; !important;
    color: #B0B0B0; !important;
}

.botaoPrimarioGrandeDesabilitado{
    padding: 12px 39px 12px 39px;
    border-radius: 4px;
    background-color: #D5D2D2;
    text-decoration: none;
    color: #B0B0B0; !important;
}

.botaoPrimarioDesabilitado:hover, .botaoPrimarioGrandeDesabilitado:hover,.botaoPrimarioSmall:hover {
    color: #B0B0B0;
}

.botaoPrimarioSmall,.botaoPrimarioSmall:visited{
    padding: 6px 15px;
    border-radius: 4px;
    background-color: #074871;
    text-decoration: none;
    color: #fff;
}
.botaoPrimario,.botaoPrimario:visited{
    background-image: none !important;
    padding: 8px 22px 8px 22px !important;
    border-radius: 4px !important;
    background-color: #074871  !important;;
    font-weight: normal !important;
    text-decoration: none !important;
    color: #fff  !important;;
}

.botaoPrimarioIgnorar,botaoPrimarioIgnorar:visited {
    background-image: none !important;
    padding: 8px 22px 8px 22px !important;
    text-decoration: none !important;
    border-radius: 5px;
    background-color: #e5e5e5;
    width: 56px;
    height: 17px;
    font-family: Arial;
    font-size: 16px;
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.13;
    letter-spacing: normal;
    text-align: center;
    color: #777777 !important;;
}

.botaoPrimarioMedio,.botaoPrimarioMedio:visited{
    padding: .8em 1.5em .8em 1.5em;
    border-radius: 4px;
    background-color: #004671;
    text-decoration: none;
    color: #fff;
}
.botaoPrimarioGrande,.botaoPrimarioGrande:visited{
    padding: 12px 39px 12px 39px;
    border-radius: 4px;
    background-color: #074871;
    text-decoration: none;
    color: #fff;
}
.botaoPrimario:hover,.botaoPrimarioGrande:hover,.botaoPrimarioMedio:hover{
    background-color: #0A3858;
    text-decoration: none;
    color: #fff;
}

.botaoPrimarioIgnorar:hover{
    text-decoration: none;
}

.botaoBackground{
    border-style: solid;
    border-width: 1px;
    border-color: white;
    padding: 8px 22px 8px 22px;
    border-radius: 2px;
    background-color: #FFFFFF; !important;
    text-decoration: none;
    color: #29abe2;
}

.botaoBackground:hover{
    border-width: 1px;
    border-radius: 2px;
    border-style: solid;
    border-color: #29AbE2;
    text-decoration: none;
}

.dividerFundoClaro{
    border: 0;
    border-top: 1px solid #E5E5E5;
}

.dividerFundoEscuro{
    border: 0;
    border-top: 1px solid #9D9D9D;
}

/*-------------------------------------------------------------INICIO ACCORDION----------------------------------------------------*/
.rotate180:before{
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}
.btnHandler{
    margin: 8px 40px 0px 0px;
    padding: 8px;
    height: 16px;
    width:16px;
    float: right;
    border-radius: 4px;
    line-height: 16px;
}
.btnHandler:before{
    -webkit-transition: all .8s;
    -moz-transition: all .8s;
    -ms-transition: all .8s;
    -o-transition: all .8s;
    transition: all .8s;
}
.accordionBody{
    width: calc(100% - 80px);
    margin-left: 40px;
    height: 0px;
    border-bottom:1px solid #333;
    padding-bottom: 5px;
    padding-top: 30px;
    overflow: hidden;
    -webkit-transition: all 1s;
    -moz-transition: all 1s ;
    -ms-transition: all 1s ;
    -o-transition: all 1s ;
    transition: all 1s ;
}
.accordionBody.semBorda{
    padding-bottom: 0px;
    padding-top: 0px;
}
.accordionHeader{
    cursor: pointer;
    position: relative;
}
.accordionHeader > a ,.accordionHeader > span ,.accordionHeader > div{
    z-index: 2;
    position: relative;
}
.accordionHeader.open > a , .accordionHeader > span:first-child{
    opacity: 1;
    -moz-opacity: 1;
    -webkit-transition: all 1s;
    -moz-transition: all 1s ;
    -ms-transition: all 1s ;
    -o-transition: all 1s ;
    transition: all 1s ;
}
.accordionHeader > a , .accordionHeader.open > span:first-child {
    opacity: 0;
    -moz-opacity: 0;
    -webkit-transition: all 1s;
    -moz-transition: all 1s ;
    -ms-transition: all 1s ;
    -o-transition: all 1s ;
    transition: all 1s ;
}
.accordionHeader:hover:after{
    content: "";
    width: 100%;
    position: absolute;
    left: 0;
    z-index: 1;
    top: 0;
    height: 100%;
    background-color: rgba(0,0,0,0.1);
}

.noBorderLeft{
    -webkit-border-bottom-left-radius: 0px !important;
    -webkit-border-top-left-radius: 0px !important;
    -moz-border-radius-bottomleft: 0px !important;
    -moz-border-radius-topleft: 0px !important;
    border-bottom-left-radius: 0px !important;
    border-top-left-radius: 0px !important;
}
.noBorderRight{
    -webkit-border-bottom-right-radius: 0px !important;
    -webkit-border-top-right-radius: 0px !important;
    -moz-border-radius-bottomright: 0px !important;
    -moz-border-radius-topright: 0px !important;
    border-bottom-right-radius: 0px !important;
    border-top-right-radius: 0px !important;
}
.imagemAluno{
    width: 150px;
    border-radius: 50%;
}
.imagemAluno.pequena{
    width: 40px;
    height: 40px;
}
.painelAlunosMarcados{
    margin-right: 10px;
    margin-top: 15px;
}

.painelAlunosMarcadosNovoMenu{
    display: flex;
    margin-right: 10px;
}

.painelRemoverAluno{
    position: fixed; 
    height: 20vh; 
    z-index: 10; 
    width: 100%; 
    bottom: 0;
    border: none;
    display: none;
    background: -moz-linear-gradient(top,  rgba(255,50,44,0) 0%, rgba(255,50,44,1) 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(top,  rgba(255,50,44,0) 0%,rgba(255,50,44,1) 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to bottom,  rgba(255,50,44,0) 0%,rgba(255,50,44,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00ff322c', endColorstr='#ff322c',GradientType=0 ); /* IE6-9 */
    text-align: center;
}
.textoMarcarAluno{
    color: white;
    font-size: 4vh;
    opacity: 0.5;
    margin-top: 6vh;
}
.imagemAluno.overlay{
    display: none;
    position: absolute;
    background:rgba(255,255,255, 0.5);
    margin-top: -165px;
    margin-left: 8px;
    cursor: move;
}
.btn-print{
    font-family: FontAwesome;
    display: inline-block;
    font-size: 1.5em;
    color: #29ABE2;
    border-radius: 2px;
    padding: 7px;
}
.btn-print-2{
    font-family: FontAwesome;
    font-size: 20px;
    color: #29ABE2;
    border-radius: 2px;
    padding: 2px;
    cursor: pointer;
}
.btn-print:hover{
    color: #fff;
    background-color: rgba(9, 71, 113, 0.9);

}
.btn-print.pdf:before,.btn-print-2.pdf:before{
    content: "\f1c1";
}
.btn-print.excel:before,.btn-print-2.excel:before{
    content: "\f1c3";
}

.btn-print.log:before,.btn-print-2.log:before{
    content: "\f03a";
}

.containerfoto:hover .imagemAluno.overlay{
    display: block;
}
/*---------------------------------------------------------------------------TOGGLE PANEL CUSTOM--------------------------------------*/
.togglePanelCustom{
    border: 1px solid #E5E5E5;
    margin: 10px;
    width: calc(100% - 20px) !important;
}
.togglePanelCustom .rich-stglpanel-header{
  background-image: none;
  background-color: #E5E5E5;
  border-bottom: 1px solid #E5E5E5;
  height: 25px;
  line-height: 25px;
}
.togglePanelCustom .rich-stglpnl-marker{
    font-size: 0;
}
.togglePanelCustom .rich-stglpnl-marker:before{
    font-size: 16px;
    padding: 2px 6px 2px 6px;
    border-radius: 5px;
    background-color: #D5D2D2;
    color: #777777;
    content: "\f107";
    margin-left: 10px;
    font-family: FontAwesome;
}
.togglePanelCustom .rich-stglpnl-marker:first-child:before{
    content: "\f106";
}
.togglePanelCustom .rich-stglpnl-marker:last-child:before{
    content: "\f107";
}
.togglePanelCustom .rich-stglpanel-body{
    border: none;
}
.togglePanelCustom .rich-stglpanel-header span{
    line-height: 25px;
}
.caixaDireita,.containerDetalhesNegociacao{
    margin-top: 20px;
    display: inline-block;
    width: 43%;
}
.caixaEsquerda{
    display: inline-block;
    margin-left: 30px;
    margin-right: 30px;
}
.margenVertical{
    margin: 5px 0px 5px 0px;
}
.containerConfNegociacao,.caixaEsquerda{
    margin-top: 20px;
    vertical-align: top;
    width: calc(57% - 60px);
}
/*---------------------------------------------------------------GROWL ZW-----------------------------------------------*/
@keyframes showMessageAni {
    from{
        -webkit-transform: scale(0);
        -moz-transform: scale(0);
        -ms-transform: scale(0);
        -o-transform: scale(0);
        transform: scale(0);
    }to{
       -webkit-transform: scale(1);
       -moz-transform: scale(1);
       -ms-transform: scale(1);
       -o-transform: scale(1);
       transform: scale(1);
    }
}
@keyframes hideMessageAni {
    from{
        opacity: 1;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1);
    } to {
              opacity: 1;
         -webkit-transform: scale(0);
         -moz-transform:  scale(0);
         -ms-transform:  scale(0);
         -o-transform: scale(0);
         transform:  scale(0);
     }
}
.growl-container{
    position: fixed;
    z-index: 99999;
    right: 12px;
    top: 12px;
}
.growl-container > div{
    cursor: pointer;
    position: relative;
    padding: 12px 18px;
    margin: 0 0 6px 0;
    background-color: transparent;
    color: #fff;
    border-radius: 5px;
    -webkit-box-shadow:  0px 2px 43px 11px rgba(0, 0, 0, 0.4);
    -moz-box-shadow:  0px 2px 43px 11px rgba(0, 0, 0, 0.4);
    box-shadow:  0px 2px 43px 11px rgba(0, 0, 0, 0.4);
    width: 300px;
    min-height: 107px;
    -webkit-transition: all .6s;
    -moz-transition: all .6s;
    -ms-transition: all .6s;
    -o-transition: all .6s;
    transition: all .6s;
    animation: showMessageAni .3s 0s 1 cubic-bezier(.21, .53, .56, .8);
}
.growl-container > div:after{
    content: "\f057";
    font-family: FontAwesome;
    font-size: 20px;
    position: absolute;
    right: 10px;
    top: 10px;
    opacity: 0.6;
}
.growl-container > div .growl-box-icon{
    display: inline-block;
    width: 66px;
    position: relative;
    z-index: 1;
    text-align: center;
    height: 107px;
    line-height: 107px;
    vertical-align: top;
}
.growl-container > div .growl-box-text {
    width: 234px;
    z-index: 1;
    position: relative;
    display: inline-block;
}
.growl-container > div .growl-box-icon span{
    font-size: 30px;
    font-family: FontAwesome;
    position: relative;
    display: inline-block;
}
.growl-container > .mens-erro .growl-box-icon span:before{
    color: #FF5555;
    content: "\f088";
}
.growl-container > .mens-erro {
    border: 3px solid #FF5555;
}
.growl-container > .mens-warn .growl-box-icon span:before{
    color: #F6A700;
    content: "\f256";
}
.growl-container > .mens-warn {
    border: 3px solid #F6A700;
}
.growl-container > .mens-sucess .growl-box-icon span:before{
     color: #2BAF50;
     content: "\f087";
}
.growl-container > .mens-sucess {
    border: 3px solid #2BAF50;
}
.growl-container > .mens-info .growl-box-icon span:before{
    color: #29ABE2;
    content: "\f25b";
}
.growl-container > .mens-info {
    border: 3px solid #29ABE2;
}
.porcentagen:before{
    content: "%";
}
.growl-container > div .growl-notification-fundo{
    background-color: #000;
    opacity: 0.8;
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 0;
    left: 0;
    top:0;
}
.growl-container > div:not(.hideNotificationMessage):hover{
    opacity: 0.9;
    -webkit-box-shadow: 0px 2px 20px 4px rgba(0, 0, 0, 0.8);
    -moz-box-shadow:  0px 2px 20px 4px rgba(0, 0, 0, 0.8);
    box-shadow:  0px 2px 20px 4px rgba(0, 0, 0, 0.8);
}
.growl-container > div .growl-box-text .growl-box-title, .growl-container > div .growl-box-text .growl-box-message{
   font-size: 16px;
   color: #fff;
   margin: 10px;
   font-family: Arial,NeoSansItalic;
}
.growl-container > div .growl-box-text .growl-box-message{
    opacity: 0.8;
}

.hideNotificationMessage {
    opacity: 0;
    animation: hideMessageAni .3s 0s 1 cubic-bezier(.21, .53, .56, .8) !important;
}
.botaoModoTimeLine{
    border-radius: 50px;
    padding: 0 10px;
    height: 32px;
    color: #29ABE2;
    background-color: #FFF;
    margin-right: 10px;
    line-height: 32px;
    font-size: 1em;
}
.botaoModoTimeLine:hover, .botaoModoTimeLine:visited, .botaoModoTimeLine:focus{
    color: #29ABE2;
}
.botaoModoTimeLine:hover{
    background-color: #E5E5E5;
}


.botaoModoTimeLine.ativo{
    color: #FFF !important;
    background-color: #29ABE2 !important;
}
#botoesTL a{
    outline:none;
}
.rodapeBotoes{
    position: absolute;
    bottom: 0;
    padding-top: 10px;
    background-color: white;
    padding-bottom: 10px;
    width: calc(100% - 20px);
}
.flex{
    display: flex;
}
.inlineBlock{
    display: inline-block;
}
.inline{
    display: inline !important;
}
.block{
    display: block;
}
.textoCarregando{
    width: 400px;
    text-align: center;
    font-size: 16px;
    margin-left: -170px;
    margin-top: 34px;
    font-weight: bold;
    color: #094771;
}

.tblHeaderLeft th, .tblHeaderLeft td{
    text-align: left;
    padding: 10px;
}
.tblHeaderLeft tr, .tblHeaderLeft th{
    border-bottom: #E5E5E5 1px solid;
}
.tblHeaderLeft tr:last-child{
    border-bottom: none;
}
.tblHeaderLeft, .tabelaDados {
    width: calc(100% - 40px);
    margin: 20px;
    border: none !important;
}
.tblHeaderLeft:not(.semZebra) tbody tr:nth-child(even), .tabelaDados:not(.semZebra) tbody tr:nth-child(even), .contratoSelecionado tbody tr {
    background:#E5E5E5;
}
.tblHeaderLeft a:not(.icon), .tabelaDados  a:not(.icon) {
    width: 100%;
    display: inline-block;
}

.tabelaDados tbody tr:hover, .tblHeaderLeft:not(.contratoSelecionado) tbody tr:hover{
    background-color: lightgray !important;
}
.tabelaDados td{
    border-bottom: #E5E5E5 1px solid !important;
}
.tabelaDados.tabelaZebrada td{
    border-left: #E5E5E5 1px solid !important;
}
.tabelaDados.tabelaZebrada td:last-child{
    border-right: #E5E5E5 1px solid !important;
}
.tabelaDados:not(.tabelaZebrada) td, .label,.tabelaDados:not(.tabelaZebrada) td > a > span,.tabelaDados:not(.tabelaZebrada) td > a {
    border: none !important;
}
.tabelaDados td, .label,.tabelaDados td > a > span,.tabelaDados td > a {
    border: none;
    font-size: 1em;
    color: #777777;
    padding: 7px 0px;
    
}
.tabelaDados th{
    padding: 7px 0px;
    font-size: .9em;
    color: #777777;
    font-weight: bold;
    border: none;
    text-transform: uppercase;
    background-color: white;
    border-bottom: #e5e5e5 1px solid;
}
.tabelaDados th:not([class*="col-text-align-"]){
    text-align: left;
}
.valorMonetario:before{
    content: "R$ ";
}
/*-----------------------------------------------------BARRA FIXA CAIXA EM ABERTO----------------------------------------------------------*/
.barraFixaClean{
    display: none;
    height: 50px;
    width: 100%;
    position: fixed;
    background-color: rgba(153, 153, 153, 0.48);
    bottom: 0;
    z-index: 99;
    padding: 0;
    margin: 0;
    left: 0;
    right: 0;
    font-family: Arial;
}
.barraFixaClean ul#css3menu1,.barraFixaClean ul#css3menu1 ul{
    border: 1px solid #fff;
}
.barraFixaClean .menubotoesfixo{
    margin: 4px;
    font-size: 80% ;
    font-weight: bold;
    line-height: 1.1;
    text-align: right;
    vertical-align: middle;
    position: fixed;
    right: 4.25em;
}
.barraFixaClean  ul#css3menu1 li:hover>a:not(.botaoPrimario),.barraFixaClean  ul#css3menu1 li:hover>div, ul#css3menu1 li>a.pressed:not(.botaoPrimario){
    background-color:#fff;
    background-image:none;
    color: #394ce2 !important;
    text-decoration: none;
}
.barraFixaClean  ul#css3menu1 li:hover>a,.barraFixaClean  ul#css3menu1 li:hover>div, ul#css3menu1 li>a.pressed{
    background-color:#fff;
    background-image:none;
    color: #394ce2 !important;;
    text-decoration: none;
}
.barraFixaClean .menubotoesfixo  a{
    background-color: #fff;
    background-image:none;
    color: #29ABE2 !important;
    text-shadow: none !important;
    border: none !important;
    font-weight: normal !important;
    font-family: Arial !important;
}
.barraFixaClean  .menubotoesfixo a.botaoPrimario {
    padding: 2px 12px 2px 12px  !important;
    border-radius: 4px !important;
    background-image: none !important;
    background-color: #004671 !important;
    text-decoration: none !important;
    color: #fff !important;
}
.barraFixaClean  ul#css3menu1 li.toplast>a {
    -webkit-border-radius: 0;
     -webkit-border-top-right-radius: 0px;
     -webkit-border-bottom-right-radius: 0px;
}
.barraFixaClean ul#css3menu1 a{
    padding: 2px 8px 2px 2px !important;
}
.centro{
    text-align: center !important;
}
.direita{
    text-align: right !important;
}
.esquerda{
    text-align: left;
}
td.alinhamentoEsquerda{
    text-align: left;
}
.navBarCustom .rich-tab-active ,.navBarCustom .rich-tab-inactive{
    background-image: none;
    background-color: #E5E5E5;
    color: #777777;
    font-size: 14px;
    font-family: Arial;
    border-radius: 6px;
    height: 40px;

}
.navBarCustom .rich-tab-active{
    box-shadow: 1px 2px 11px -2px #333;
}
.navBarCustom .rich-tabhdr-side-cell,.navBarCustom .rich-tabhdr-side-border{
    border: none;
    background-color: transparent;
}
.navBarCustom .rich-tabhdr-cell-active > table {
    top:5px !important;
}
.navBarCustom .rich-tabhdr-cell-inactive > table {
    top:8px !important;
}

/*-----------------------------------------------------------------------NAV BAR CUSTOM------------------------------------------------------------*/
.tabelaDados{
    text-transform: capitalize;
}
.tabelaDados tr td:first-of-type,.tabelaDados tr th:first-of-type{
    padding-left: 10px !important;
}
.tabelaDados .numero{
    text-align: right !important;
    padding-right: 10px;
}


.pecentBoxBody{
    font-size: 20px;

}
.porcentagemBoxBody{
    font-size: 30px;
    color: rgb(71, 71, 71);
}
.resultadoBoxHeader{
    height: 37px;
    line-height: 37px;
    width: 121px;
    background-color: rgb(192, 192, 192);

}
.resultadoBoxBody{
    height: 77px;
    width: 119px;
    background-color: rgba(235, 255, 73, 0);

    border-bottom:solid 1px rgb(192, 192, 192) ;
    border-left:solid 1px rgb(192, 192, 192);
    border-right: solid 1px rgb(192, 192, 192);
}
.tituloModulos{
    font-family: NeoSansItalic,Arial;
    font-size: 23px;
    color: #555 !important;
    text-decoration: none !important;
}
.progressiveBar{
    top: 83px;
    left:0px;
    position: absolute;
    height: 5px;
}
.w120{
    width: 120px;
}
.imagemBanner{
    max-width: 100%;
}
.caixaLembrete{
                       height: 120px;
                       margin-left: -375px;
                       width: 400px;
                       background-color: white;
                       -webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
                       -moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
                       box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);
                       display: none;
                   }
                   .clienteMarcado{
                       position: relative;
                       display: inline-table;
                   }
                    .clienteMarcado .cmarc, .clienteMarcado .mensagemCmarc{
                        visibility: hidden;
                        opacity: 0;
                        transition: visibility 0s, opacity 0.5s linear;
                    }
                   .clienteMarcado .cmarc{
                       background-color: #2F96D2;
                       width: 25px;
                       height: 25px;
                       left: -7px;
                       border-radius: 50%;
                       position: absolute;
                       top: 0;
                   }
                   .cmarc a{
                       color: white;
                       font-size: 15px;
                   }

.clienteMarcado .mensagemCmarc{
    position: absolute;
    background-color: #ffffff;
    border: solid 2px #777777;
    width: 200px;
    padding: 10px;
    text-align: center;
    margin: 15px -75px;
    border-radius: 10px;
}
.mensagemCmarc:after, .mensagemCmarc:before {
    bottom: 100%;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.mensagemCmarc:after {
    border-color: rgba(255, 255, 255, 0);
    border-bottom-color: #ffffff;
    border-width: 10px;
    margin-left: -10px;
}
.mensagemCmarc:before {
    border-color: rgba(119, 119, 119, 0);
    border-bottom-color: #777777;
    border-width: 12px;
    margin-left: -12px;
}


.clienteMarcado:hover .cmarc, .clienteMarcado:hover .mensagemCmarc{
    visibility: visible;
    opacity: 1;
    z-index: 99;
}

.lupaAtiva.mensagemCmarc{
    left:-310%;
    bottom:-20%;
    top:-20%;
    font-size: small;
}

.lupaAtiva.mensagemCmarc:after, .lupaAtiva.mensagemCmarc:before {
    left: 103%;
    bottom: 30%;
    transform: rotate(90deg);
    border-width: 0px 11px 14px 10px;
}

.lupaAtiva.mensagemCmarc:after {
    margin-left: -12px;
}

.lupaAtiva.mensagemCmarc:before {
    margin-left: -9px;
}

/*NOTIFIÇÕES UCP*/
.notificacaoUCP {
    -webkit-background-clip: padding-box;
    -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
    -webkit-font-smoothing: subpixel-antialiased;
    background-color: #F06D29;
    box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
    color: rgb(255, 255, 255) !important;
    cursor: pointer;
    font-family: 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 10px !important;
    line-height: 0;
    text-align: center;
    text-shadow: rgba(0, 0, 0, 0.4) 0px -1px 0px;
    border-radius: 10px;
    padding: 4px 8px 4px 8px;
    position: relative;
    top: -14px;
}

 /*----------------------------------------------------BI'S CRM----------------------------------------------------------------------*/


.crm-toggle-view {
    background-color: #0f4c6b;
    padding: 4px;
    border-radius: 5px;
    color : #fff
}

.super-vertical{
    vertical-align: super;
}

.padding-right-10{
    padding-right: 10px !important;
}

.background-color-tabelaDados-over{
    background-color:'#F1F1F1'
}

.notificacaoSocialMailing {
    -webkit-background-clip: padding-box;
    -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
    -webkit-font-smoothing: subpixel-antialiased;
    background-color: rgb(220, 13, 23) !important;
    background-image: -webkit-linear-gradient(top, rgb(250, 60, 69), rgb(220, 13, 23));
    box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
    color: rgb(255, 255, 255) !important;
    cursor: pointer;
    font-family: 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 10px !important;
    text-align: center;
    text-shadow: rgba(0, 0, 0, 0.4) 0px -1px 0px;
    border-radius: 10px;
    padding: 2px 5px 2px 5px;
    position: relative;
    top: -10px;
    min-width: 10px !important;
    width: 10px;
    height: 10px !important;
    margin-left: 20px;
    line-height: 2 !important;
}
.paginador-container{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 20px 10px 20px;
}
.paginador-itens-pagina{
    display: inline-flex;
    align-items: center
}

.box-totalizador {
    font-weight: bold;
    margin-bottom: 20px;
    background-color: #fff;
    -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.3);
    box-shadow: 0 1px 1px rgba(0,0,0,.3);
}

.box-totalizador > .corpo-totalizador {
    color: #29ABE2;
    font-family: Arial;
    font-size: 26px;
    font-weight: normal;
    text-align: center;
    margin-top: 20px;
    margin-bottom: 20px;
    white-space: nowrap;
    width: 100%;
    display: inline-block;
}

.box-totalizador > .titulo-totalizador {
    text-align: center;
    padding: 10px 15px;
    color: #777;
    background-color: #E5E5E5;
    border-color: #ddd;
    font-family: Arial;
    font-size: 14px;
    text-transform: uppercase;
}
.lista-totalizadores {
    font-family: Arial;
    font-weight: normal;
    font-size: 14px;
    color: #777;
}

.container-totalizador:after {
    content: '';
    display: block;
    clear: both;
}
.container-totalizador {
    overflow: hidden;
}
.container-totalizador .box-totalizador {
    margin-right: 20px;
}
.tabela-zebra tr:nth-child(odd) {
    background-color: #eee;
}
.pulsante {
    display: inline-block;
    font-size: 1.5em;
    animation: pulsante 1s infinite;
    margin: 0.3em;
}

@keyframes pulsante {
    0%, 100% { transform: scale(1);}
    50% {transform: scale(1.3);}
}

.icon-estudio{
    content:url("./../imagens_flat/icon_ZWStudio.svg");
    width: 14px;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.msg-cliente-restricao {
    whith: 100%;
    margin: 12px 0px;
    padding: 6px 12px;
    color: #E10505;
    background: #FEE6E6;
    border: 1px solid #FEE6E6;
    box-sizing: border-box;
    border-radius: 8px;
    font-weight: 400;
    line-height: 17.5px;
    font-family: Nunito Sans;
    text-align: justify;
    display: flex;
    align-items: center;
}

@media screen and (max-width: 1920px) {
    #tabela2 input[id*="residencial"],
    #tabela2 input[id*="comercial"],
    #tabela2 input[id*="celular"] {
        display: block !important;
    }

    #tabela2 input[id*="descricao"] {
        width: 100% !important;
        margin-top: 10px !important;
    }

    #tabela2 span[style*="margin-left: 20px"] {
        margin-left: 0 !important;
        display: block !important;
        margin-top: 10px !important;
    }
}

.borda_cinza {
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-bottom: 1px;
    border-left-width: 1px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: none;
    border-left-style: none;
    border-top-color: #BDBCBD;
    border-right-color: #BDBCBD;
    border-bottom-color: #BDBCBD;
    border-left-color: #BDBCBD;
}
.dia_vazio_cinza {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    color: #8A8A8A;
}
.dia_vazio_branco {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    color: #FFF;
}
.dia_vazio_cinzaclaro {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    color: #DEE7EC;
}


.nao_ha_reservas_azul {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    color: #0066FF;
}
.nao_ha_reservas_amarelo {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    color: #FFD42E;
}

.ha_disponibilidade_azulescuro {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 9px;
    color: #2C528C;
}
.ha_disponibilidade_branco {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 9px;
    color: #FFF;
}


.borda_bege {
    border-bottom: 1px;
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: none;
    border-left-style: solid;
    border-top-color: #BDC1A3;
    border-right-color: #BDC1A3;
    border-bottom-color: #BDC1A3;
    border-left-color: #BDC1A3;
}
.borda_azul {
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-top-style: solid;
    border-right-style: none;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: #59727D;
    border-right-color: #59727D;
    border-bottom-color: #59727D;
    border-left-color: #59727D;
}
.arial_18_branca {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 18px;
    font-weight: bold;
    color: #FFF;
}
.arial_11_preto_sublinhado {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 11px;
    color: #333;
    text-decoration: underline;
}
.arial_15_preto {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 15px;
    color: #000;
}
.dia_vazio {
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: none;
    border-left-style: none;
    border-top-color: #BDBCBD;
    border-right-color: #BDBCBD;
    border-bottom-color: #BDBCBD;
    border-left-color: #BDBCBD;
}

.texto_disponibilidade{
    color: #000000;
    font: 10pt 'Trebuchet MS',verdana,arial,helvetica,sans-serif;
    text-align: right;    
}

.texto_agenda{
    color: #000000;
    font: 10pt 'Trebuchet MS',verdana,arial,helvetica,sans-serif;
    text-align: center;    
}

.texto_agendas{
    color: #000000;
    font: 10pt 'Trebuchet MS',verdana,arial,helvetica,sans-serif;
    text-align: left;    
}

#agenda_ambiente {
    border-color: #ACBECE;
    border-width: 1px;
    border-style: solid;
} 

#agenda_ambiente tr{
    border-color: #ACBECE;
    border-width: 1px;
} 

#agenda_ambiente td{
    border-color: #ACBECE;
    border-width: 1px;
    border-style: solid;
} 

.tamanho_menu{
    width: 220px;
}
.rich-table, .rich-table-header, .rich-table-headercell, .rich-table-cell, .rich-subtable-cell, .rich-table-footercell, .rich-subtable-footercell {
    /*border-width:0px;*/
}
.tooltip tr {
    border-width:0px;
    border-style: none;
}
.tooltip td {
    border-width:0px;
    border-style: none;
}

#ToolbarZillyonWeb{ padding:0; margin:0; left:0; right:0; font-family:Arial;}
#ToolbarZillyonWeb{
    background:transparent url(../images/bg_menubotoes.png) repeat-x;
    /*background:transparent url(../../images/bg_menu.gif) repeat-x;*/
    display:none;
    height:30px;
    width:100%;
    background-position: 0 50%;
    position:fixed;
    bottom: 0;    
    z-index:9999;    
}

div #tableConfirmDisponibilidade{
    overflow-y: auto; 
    height: 180px;
}

.cor_indisponibilidade{

    background-image: linear-gradient(right bottom, #E388B3 8%, #F2D0E0 86%, #F2D0E0 84%);
    background-image: -o-linear-gradient(right bottom, #E388B3 8%, #F2D0E0 86%, #F2D0E0 84%);
    background-image: -moz-linear-gradient(right bottom, #E388B3 8%, #F2D0E0 86%, #F2D0E0 84%);
    background-image: -webkit-linear-gradient(right bottom, #E388B3 8%, #F2D0E0 86%, #F2D0E0 84%);
    background-image: -ms-linear-gradient(right bottom, #E388B3 8%, #F2D0E0 86%, #F2D0E0 84%);

    background-image: -webkit-gradient(
        linear,
        right bottom,
        left top,
        color-stop(0.08, #E388B3),
        color-stop(0.86, #F2D0E0),
        color-stop(0.84, #F2D0E0)
        );

    /* Internet Explorer 5.5 ~ */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#F2D0E0, endColorstr=#E388B3,GradientType=1);
}

.cor_empresa_fechada{

    background-image: linear-gradient(right bottom, #B5DBE6 35%, #D9E7EB 86%, #D9E7EB 84%);
    background-image: -o-linear-gradient(right bottom, #B5DBE6 35%, #D9E7EB 86%, #D9E7EB 84%);
    background-image: -moz-linear-gradient(right bottom, #B5DBE6 35%, #D9E7EB 86%, #D9E7EB 84%);
    background-image: -webkit-linear-gradient(right bottom, #B5DBE6 35%, #D9E7EB 86%, #D9E7EB 84%);
    background-image: -ms-linear-gradient(right bottom, #B5DBE6 35%, #D9E7EB 86%, #D9E7EB 84%);

    background-image: -webkit-gradient(
        linear,
        right bottom,
        left top,
        color-stop(0.35, #B5DBE6),
        color-stop(0.86, #D9E7EB),
        color-stop(0.84, #D9E7EB)
        );

    /* Internet Explorer 5.5 ~ */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#D9E7EB, endColorstr=#B5DBE6,GradientType=1);    

}

.cor_feriado{
    background-image: linear-gradient(right bottom, #EBD775 35%, #F0EBD1 86%, #F0EBD1 84%);
    background-image: -o-linear-gradient(right bottom, #EBD775 35%, #F0EBD1 86%, #F0EBD1 84%);
    background-image: -moz-linear-gradient(right bottom, #EBD775 35%, #F0EBD1 86%, #F0EBD1 84%);
    background-image: -webkit-linear-gradient(right bottom, #EBD775 35%, #F0EBD1 86%, #F0EBD1 84%);
    background-image: -ms-linear-gradient(right bottom, #EBD775 35%, #F0EBD1 86%, #F0EBD1 84%);

    background-image: -webkit-gradient(
        linear,
        right bottom,
        left top,
        color-stop(0.35, #EBD775),
        color-stop(0.86, #F0EBD1),
        color-stop(0.84, #F0EBD1)
        );

    /* Internet Explorer 5.5 ~ */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#F0EBD1, endColorstr=#EBD775,GradientType=1);
}
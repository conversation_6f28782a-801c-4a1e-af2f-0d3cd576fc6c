/* jQuery TreeTable Core 2.0 stylesheet
 *
 * This file contains styles that are used to display the tree table. Each tree
 * table is assigned the +treeTable+ class.
 * ========================================================================= */

/* jquery.treeTable.collapsible
 * ------------------------------------------------------------------------- */
.treeTable tr td .expander {
  background-position: left center;
  background-repeat: no-repeat;
  cursor: pointer;
  padding: 0;
  zoom: 1; /* IE7 Hack */
}

.treeTable tr.collapsed td .expander {
  background-image: url(../images/expandTree.gif);
}

.treeTable tr.expanded td .expander {
  background-image: url(../images/collapTree.gif);
}

/* jquery.treeTable.sortable
 * ------------------------------------------------------------------------- */
.treeTable tr.selected, .treeTable tr.accept {
  background-color: #3875d7;
  color: #fff;
}

.treeTable tr.collapsed.selected td .expander, .treeTable tr.collapsed.accept td .expander {
  background-image: url(../images/expandTree.gif);
}

.treeTable tr.expanded.selected td .expander, .treeTable tr.expanded.accept td .expander {
  background-image: url(../images/collapTree.gif);
}

.treeTable .ui-draggable-dragging {
  color: #000;
  z-index: 1;
}
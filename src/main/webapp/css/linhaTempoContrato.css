.containerLinhaTempoContrato{
    width: 100%;
    height: 120px;
    position: relative;
}
.caixaLinhaTempo{
    width: 98%;
    display: table;
    height: 60px;
    margin: 0 1%;
    position: absolute;
    top: 40px;
}

.linhaCentro{
    height: 2px;
    width: 100%;
    float: left;
    margin-top: 36px;
}
.bola{
    height: 15px !important;
    width: 15px !important;
    border-radius: 50px;
    margin-top: 29px;
    position: absolute;
}
/* Tooltip text */
.bola .tooltiptext, .caixaPeriodo .tooltiptext {
    width: 120px;
    height: 45px;
    background-color: white;
    color: #777777;
    text-align: center;
    position: absolute;
    top: -65px;
    border: solid 1px #E5E5E5;
    left: -3px;
    opacity: 0;
    display: none;


}
.caixaPeriodo .tooltiptext{
    width: 132px !important;
    top: -26px !important;
}
.bola.item:not(.embacado){
    z-index: 7;
}
/* Show the tooltip text when you mouse over the tooltip container */
.painelDadosAluno:hover .especial .tooltiptext.fixo,
.item:not(.embacado):hover .tooltiptext.fixo,
.bola.lancamento:hover .tooltiptext.fixo,
.naoembacar .item:hover .tooltiptext.fixo,
.caixaPeriodo:not(.embacado):hover .tooltiptext.fixo{
    opacity: 1;
    display: block;
    z-index: 9;
}
.especial .tooltiptext.fixo{
    transition: 0.7s opacity linear;
}

.bola.lancamento .tooltiptext.fixo{
    left: 6px !important;
}
.bola.lancamento .icone.linha{
    height: 65px !important;
}
.item:hover .tooltiptext.fixo{
    z-index: 99999999 !important;
}
.icone{
    float: left;
    height: 45px;
    width: 20px;
    color: white;
    padding-top: 0px;
    font-size: 15px;

}
.icone.inicio{
    background-color: #29ABE2;
}
.icone.fim{
    background-color: #FF5555;
}
.invertido{
    left: -105px !important;
}
.invertido .icone{
    float: right !important;
}
.invertido .linha{
    right: 8.5px;
}
.especial .normal .linha{
    left: 8.5px;
}
.item .normal .linha{
    left: 0px;
}
.bola.item .tooltiptext{
    top: -62px !important;
    left: 11px !important;
}

.icone.linha{
    width: 2px !important;
    height: 61px;
    position: absolute;
}
.andamento{
    background-color: #E5E5E5;

}
.painelDadosAluno:hover .hoje{
    opacity: 0;
}
.hoje{
    color: #E5E5E5;
    background-color: #333333;
    width: 50px;
    border-radius: 50px;
    height: 15px;
    position: absolute;
    right: -25px;
    text-align: center;
    padding-top: 3px;
    font-size: 12px;
    top: 3px;
    z-index: 5;
    transition: 0.7s opacity linear;
}
.textoData{
    color: #9E9E9E;
    font-size: 10px;
}
.textoDescricao{
    color: #9E9E9E;
    font-size: 13px;
    display: block;
}
.caixaToolTip{
    padding-left: 25px;
    text-align: left;
    padding-top: 3px;
}
.invertido .caixaToolTip{
    padding-left: 3px !important;
}
.icone i{
    top: 14px;
    position: absolute;
    z-index: 20;
}
.invertido .icone i{
    right: 3.5px;
}
.normal .icone i{
    left: 3.5px;
}
.bola.contorno{
    top: -3px;
    border: 2px solid;
    height: 17px !important;
    width: 17px !important;
    z-index: 1;
}
.naoembacar .bola:hover, .bola:hover:not(.embacado){
    box-shadow: 0 1px 8px 0 rgb(0,0,0);
    z-index: 10;
}
.bola.contorno{
    box-shadow: none !important;
}
.borderAzul{
    border-color: #29ABE2 !important;
}
.borderVermelho{
    border-color: #FF5555 !important;
}
.verde{
    background-color: #2BAF50 !important;
}
.azul{
    background-color: #002C74 !important;
}
.cinzaClaro{
    background-color: #BDBBBB !important;
}
.bola.grande{
    width: 25px !important;
    height: 20px !important;
    margin-top: 23px !important;
    color: white;
    text-align: center;
    font-size: 14px;
    padding-top: 5px;
    border: white solid 2px;
}
.miniTexto{
    position: absolute;
    font-style: italic;
    top: 28px;
    font-size: 11px !important;
    width: 500px;
}
.linhaMes{
    position: absolute;
    top: 0;
    text-transform: uppercase;
    height: 120px;
    border-left: 1px dashed #9E9E9E;
}
.textoData.mes{
    left: -12px;
    position: absolute;
    font-weight: bold;
    text-align: center;
}
.parcela{
    background-color: #ff3700 !important;
}
.parcela.aberta{
    color: #f55 !important;
}
.parcela.paga{
    color: #FFFFFF !important;
}
.pagamento{
    background-color: #65B02A !important;
}
.item .caixaToolTip, .lancamento .caixaToolTip, .caixaPeriodo .caixaToolTip{
    padding-left: 5px !important;
}
.TRANSFERENCIA_SAIDA{
    background-color: #d4285d !important;
}
.ALTERACAO_DURACAO{
    background-color: #ec602a !important;
}
.BONUS_ACRESCIMO{
    background-color: #00b7c0 !important;
}
.BONUS_REDUCAO{
    background-color: #ff64ae !important;
}
.LIBERAR_VAGA{
    background-color: #2CAF4F !important;
}
.CARENCIA{
    background-color: #654fff !important;
}
.TRANSFERENCIA_ENTRADA{
    background-color: #049550 !important;
}
.CANCELAMENTO{
    background-color: #ab0000 !important;
}
.ALTERACAO_HORARIO{
    background-color: #ac3386 !important;
}
.TRANCAMENTO{
    background-color: #ff7300 !important;
}
.TRANCAMENTO_VENCIDO{
    background-color: #b85300 !important;
}
.RETORNO_TRANCAMENTO{
    background-color: #92b500 !important;
}
.INCLUIR_MODALIDADE{
    background-color: #002c74 !important;
}
.ALTERAR_MODALIDADE{
    background-color: #00744f !important;
}
.EXCLUIR_MODALIDADE{
    background-color: #b77c19 !important;
}
.ALTERACAO_CONTRATO{
    background-color: #ffb220 !important;
}
.ATESTADO{
    background-color: #ff3333 !important;
}
.RETORNO_ATESTADO{
    background-color: #4fb05f !important;
}
.CONTATO{
    background-color: #FF6E00 !important;
}
.CONTATO_TELEFONE{
    background-color: #0037a8 !important;
}
.CONTATO_EMAIL{
    background-color: #009ae8 !important;
}
.CONTATO_PESSOAL{
    background-color: #e86900 !important;
}
.LIGACAO_SEM_CONTATO{
    background-color: #7a89a8 !important;
}
.CONTATO_SMS{
    background-color: #00cf5a !important;
}
.CONTATO_APP{
    background-color: #ff003d !important;
}


.naoEmbacar .bola.item.embacado{
    opacity: 1 !important;
    -webkit-filter: none !important;
    -moz-filter: none !important;
    -o-filter: none !important;
    -ms-filter: none !important;
    filter: none !important;
}
.bola.item.embacado{
    opacity: 0.5;
    -webkit-filter: blur(3px);
    -moz-filter: blur(3px);
    -o-filter: blur(3px);
    -ms-filter: blur(3px);
    filter: blur(3px);
}
.bola.item{
    transition: 1s -webkit-filter linear;
}
.caixaPeriodo{
    background-color: transparent !important;
}
.naoembacar .caixaPeriodo:hover, .caixaPeriodo:hover:not(.embacado) .bola, .caixaPeriodo:hover:not(.embacado) .linhaCentro{
    z-index: 999 !important;
    box-shadow: 0 1px 8px 0 rgb(0,0,0);
}
.embacado .linhaCentro{
    display: none;
}
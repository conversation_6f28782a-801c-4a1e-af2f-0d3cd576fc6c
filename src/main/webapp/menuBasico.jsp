
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<!-- inicio box -->
<h:panelGroup layout="block" styleClass="menuLateral">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-briefcase"></i> Cadastros Auxiliares
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.colaborador}">
            <a4j:commandLink value="#{msg_menu.Menu_colaborador}"
                             id="btnCadastroColaborador"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="COLABORADOR" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-novo-colaborador/"
                          title="Clique e saiba mais: Colaborador" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.cliente}">
            <a4j:commandLink value="#{msg_menu.Menu_cliente}"
                             id="btnCadastroCliente"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CLIENTE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-excluir-cadastro-de-pessoas-alunos/"
                          title="Clique e saiba mais: Cliente" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.categoria}">
            <a4j:commandLink  value="#{msg_menu.Menu_categoria}"
                              id="btnCadastroCategoriaClientes"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CATEGORIA_CLIENTES" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Categoria_de_Clientes"
                          title="Clique e saiba mais: Categoria de Clientes" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.profissao}">
            <a4j:commandLink  value="#{msg_menu.Menu_profissao}"
                              id="btnCadastroProfissao"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PROFISSAO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Profissao"
                          title="Clique e saiba mais: Profissão" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" >
            <a4j:commandLink  value="#{msg_menu.Menu_departamento}"
                              id="btnCadastroDepartamento"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="DEPARTAMENTO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Departamento"
                          title="Clique e saiba mais: Departamento" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.grauInstrucao && LoginControle.apresentarLinkZW}">
            <a4j:commandLink  value="#{msg_menu.Menu_grauInstrucao}"
                              id="btnCadastroGrauInstrucao"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="GRAU_DE_INSTRUCAO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Grau_de_Instrucao"
                          title="Clique e saiba mais: Grau de Instrução" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.classificacao}">
            <a4j:commandLink  value="#{msg_menu.Menu_classificacao}"
                              id="btnCadastroClassificacao"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CLASSIFICACAO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Classificacoes"
                          title="Clique e saiba mais: Classificação" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.grupo}">
            <a4j:commandLink  value="#{msg_menu.Menu_grupo}"
                              id="btnCadastroGrupoDesconto"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="GRUPO_DESCONTO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Grupo"
                          title="Clique e saiba mais: Grupo" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.parentesco && LoginControle.apresentarLinkZW}">

            <a4j:commandLink  value="#{msg_menu.Menu_parentesco}"
                              id="btnCadastroParentesco"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PARENTESCO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Parentesco"
                          title="Clique e saiba mais: Parentesco" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.pais}">

            <a4j:commandLink  value="#{msg_menu.Menu_pais}"
                              id="btnCadastroPais"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PAIS" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Pais"
                          title="Clique e saiba mais: País" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.cidade}">

            <a4j:commandLink  value="#{msg_menu.Menu_cidade}"
                              id="btnCadastroCidade"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CIDADE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Cidade"
                          title="Clique e saiba mais: Cidade" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>
                
        <%--
       <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.conviteAulaExperimental}">

           <a4j:commandLink  value="#{msg_menu.Menu_conviteAulaExperimental}"
                             id="btnCadastroConviteAulaExperimental"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
               <f:attribute name="funcionalidade" value="CONVITE_AULA_EXPERIMENTAL" />
           </a4j:commandLink>
           <h:outputLink styleClass="linkWiki"
                         value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Cidade"
                         title="Clique e saiba mais: Cidade" target="_blank">
               <i class="fa-icon-question-sign" style="font-size: 18px"></i>
           </h:outputLink>

       </h:panelGroup>
       --%>

       <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.campanhaCupomDesconto}">
           <a4j:commandLink  value="#{msg_menu.Menu_campanhaCupomDesconto}"
                             id="btnCadastroCampanhaCupomDesconto"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
               <f:attribute name="funcionalidade" value="CAMPANHA_CUPOM_DESCONTO" />
           </a4j:commandLink>
           <h:outputLink styleClass="linkWiki"
                         value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Cidade"
                         title="Clique e saiba mais: Cidade" target="_blank">
               <i class="fa-icon-question-sign" style="font-size: 18px"></i>
           </h:outputLink>
       </h:panelGroup>
               
       <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.brinde}">

            <a4j:commandLink  value="#{msg_menu.Menu_brinde}"
                              id="btnCadastroBrinde"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="BRINDE" />
            </a4j:commandLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.gympass}">
            <a4j:commandLink  value="#{msg_menu.Menu_gympass}"
                              id="btnCadastroGympass"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="GYMPASS" />
            </a4j:commandLink>
        </h:panelGroup>
   </h:panelGroup>

   <h:panelGroup layout="block" styleClass="grupoMenuLateral">
       <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
           <i class="fa-icon-file-alt"></i> Boletim de Visita
       </h:panelGroup>

       <h:panelGroup layout="block" styleClass="grupoMenuItem"
                     rendered="#{LoginControle.permissaoAcessoMenuVO.pergunta && LoginControle.apresentarLinkZW}">

           <a4j:commandLink  value="#{msg_menu.Menu_pergunta}"
                             id="btnCadastroPergunta"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
               <f:attribute name="funcionalidade" value="PERGUNTA" />
           </a4j:commandLink>
           <h:outputLink styleClass="linkWiki"
                         value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Pergunta"
                         title="Clique e saiba mais: Pergunta" target="_blank">
               <i class="fa-icon-question-sign" style="font-size: 18px"></i>
           </h:outputLink>

       </h:panelGroup>

       <h:panelGroup layout="block" styleClass="grupoMenuItem"
                     rendered="#{LoginControle.permissaoAcessoMenuVO.questionario && LoginControle.apresentarLinkZW}">

           <a4j:commandLink  value="#{msg_menu.Menu_questionario}"
                             id="btnCadastroQuestionario"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
               <f:attribute name="funcionalidade" value="QUESTIONARIO" />
           </a4j:commandLink>
           <h:outputLink styleClass="linkWiki"
                         value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Questionario"
                         title="Clique e saiba mais: Questionário" target="_blank">
               <i class="fa-icon-question-sign" style="font-size: 18px"></i>
           </h:outputLink>

       </h:panelGroup>

   </h:panelGroup>
</h:panelGroup>


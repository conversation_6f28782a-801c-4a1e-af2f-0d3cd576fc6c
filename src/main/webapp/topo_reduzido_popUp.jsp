<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 13/07/2016
  Time: 17:47
  To change this template use File | Settings | File Templates.
--%>
<%--
  Created by IntelliJ IDEA.
  User: hellison
  Date: 24/02/2016
  Time: 16:57
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/verificaModulo.jsp" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="include_localize_traducao_linguagem.jsp"/>
<c:set var="from" value="popup" scope="request" />
<c:if test="${SuperControle.ativarGoogleAnalytics}">
    <!-- Google Analytics -->
    <script>
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

        tela = window.location.pathname;
        ga('create', 'UA-113292205-2', 'auto');
        ga('set', '&uid', '${SuperControle.usuarioLogado.username}');
        <%--ga('send', 'pageview');--%>
        ga('send', 'event', 'popup', 'pageView',tela);
    </script>
    <!-- End Google Analytics -->
</c:if>
<style>
    body{
        min-width: inherit !important;
    }
    .container {
        height: 82px;
        text-align: center;
        font: 0/0 a;
        /*width: 170px;*/
    }

    /*centralizar a imagem verticalmente*/
    .container:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        height: 100%;
    }

    .element {
        display: inline-block;
        vertical-align: middle; /* vertical alignment of the inline element */
        font: 16px/1 Arial sans-serif; /* <-- reset the font property */
    }

    .fa-external-link-square:hover {
        color: #29abe2 !important;
    }
    .element{
        width: 100%;
    }
    .image-logo {
        float: left;
        margin-left: 20px;
        text-align: center;
    }
    @media print {
        body {
            display: none;
        }
    }
    /*
    * Removido devido a solicitações de clientes.
    html {
        user-select: none;
    }*/
</style>
<c:choose>
    <c:when test="${modulo eq 'centralEventos'}">
        <div class="moduloCE" style="height: 4px;"></div>
    </c:when>
    <c:when test="${modulo eq 'financeiroWeb'}">
        <div class="moduloFIN" style="height: 4px;"></div>
    </c:when>

    <c:when test="${modulo == 'crm'}">
        <div class="moduloCRM" style="height: 4px;"></div>
    </c:when>
    <c:otherwise>
        <div class="moduloZW" style="height: 4px;"></div>
    </c:otherwise>
</c:choose>

<div style="position: absolute;
    width: 100%;">
    <h2 style="text-align: center; padding-top: 5px; font-family: arial, sans-serif; font-size: 16px; letter-spacing: 0.2px;"><c:out value="${titulo}"/>
         <h:outputLink styleClass="linkWiki"
                      value="#{urlWiki != null and not (urlWiki eq 'semWiki') ? urlWiki : SuperControle.urlWiki}"
                      title="Clique e saiba mais"
                      target="_blank">
             <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h2>
</div>

<h:panelGroup layout="block" styleClass="wrapper" style="height: 82px;">
    <h:panelGroup layout="block" styleClass="container">
        <h:panelGroup layout="block" styleClass="element">
            <c:choose>
                <c:when test="${modulo eq 'centralEventos'}">
                    <h:graphicImage styleClass="image-logo" url="/imagens_flat/shortLogoCE.png"/>
                </c:when>
                <c:when test="${modulo eq 'financeiroWeb'}">
                    <h:graphicImage styleClass="image-logo" url="/imagens_flat/shortLogoFin.png"/>
                </c:when>

                <c:when test="${modulo == 'crm'}">
                    <h:graphicImage styleClass="image-logo" url="/imagens_flat/shortLogoCRM.png"/>
                </c:when>
                <c:otherwise>
                    <h:graphicImage styleClass="image-logo" url="/imagens_flat/shortLogoZW.png"/>
                </c:otherwise>
            </c:choose>


        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
<h:panelGroup layout="block" styleClass="box-separador-popUp"></h:panelGroup>
<script>
    jQuery(document).ready(function(){
        setTimeout(function(){
            console.log('click');
            jQuery('.searchbox-icon').click();
            jQuery('.searchbox-input').focus();
        }, 1000);
    })

</script>

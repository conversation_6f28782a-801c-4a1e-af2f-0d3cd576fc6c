<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Convenio_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Convenio_tituloForm}"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Convenio_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{ConvenioControle.convenioVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao" required="true" size="45" maxlength="45" styleClass="camposObrigatorios" value="#{ConvenioControle.convenioVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_dataAssinatura}" />
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataAssinatura"
                                           value="#{ConvenioControle.convenioVO.dataAssinatura}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false" />

                            <h:message for="dataAssinatura" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_dataInicioVigencia}" />
                    <h:panelGroup>
                        <rich:calendar id="dataInicioVigencia"
                                       value="#{ConvenioControle.convenioVO.dataInicioVigencia}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataInicioVigencia"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_dataFinalVigencia}" />
                    <h:inputText  id="dataFinalVigencia" onchange="return mascara(this.form, 'form:dataFinalVigencia', '99/99/9999', event);" size="10" maxlength="10" styleClass="campos" value="#{ConvenioControle.convenioVO.dataFinalVigencia}" >
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:inputText>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_descontoParcela}" />
                    <h:inputText  id="descontoParcela" size="20" maxlength="20" styleClass="campos" value="#{ConvenioControle.convenioVO.descontoParcela}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_responsavelAutorizacao}" />
                    <h:inputText  id="responsavelAutorizacao" size="10" maxlength="10" styleClass="campos" value="#{ConvenioControle.convenioVO.responsavelAutorizacao}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_dataAutorizacao}" />

                    <h:panelGroup>
                        <rich:calendar id="dataAutorizacao"
                                       value="#{ConvenioControle.convenioVO.dataAutorizacao}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataAutorizacao"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Convenio_situacao}" />
                    <h:inputText  id="situacao" size="45" maxlength="45" styleClass="campos" value="#{ConvenioControle.convenioVO.situacao}" />

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ConvenioControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ConvenioControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{ConvenioControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="salvar" action="#{ConvenioControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="excluir" onclick="return confirm('Confirma exclusão dos dados?');" action="#{ConvenioControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="consultar" immediate="true" action="#{ConvenioControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
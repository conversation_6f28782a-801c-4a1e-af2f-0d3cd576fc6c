<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
		padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_tituloForm}"/>
    </title>


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        
        <h:form id="form">
            <h:commandLink action="#{HistoricoVinculoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_HistoricoVinculo_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoVinculo_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{HistoricoVinculoControle.historicoVinculoVO.codigo}" />
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoVinculo_cliente}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="cliente" styleClass="camposObrigatorios" value="#{HistoricoVinculoControle.historicoVinculoVO.cliente.codigo}" >
                                    <f:selectItems  value="#{HistoricoVinculoControle.listaSelectItemCliente}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_cliente" action="#{HistoricoVinculoControle.montarListaSelectItemCliente}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:cliente"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoVinculo_dataRegistro}" />
                            <h:panelGroup>
                                <h:inputText  id="dataRegistro" onkeypress="return mascara(this.form, 'form:dataRegistro', '99/99/9999', event);" size="10" maxlength="10" styleClass="camposObrigatorios" value="#{HistoricoVinculoControle.historicoVinculoVO.dataRegistro}" >
                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                </h:inputText>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoVinculo_tipoHistoricoVinculo}" />
                            <h:inputText  id="tipoHistoricoVinculo" size="2" maxlength="2" styleClass="campos" value="#{HistoricoVinculoControle.historicoVinculoVO.tipoHistoricoVinculo}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoVinculo_colaborador}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="colaborador" styleClass="camposObrigatorios" value="#{HistoricoVinculoControle.historicoVinculoVO.colaborador.codigo}" >
                                    <f:selectItems  value="#{HistoricoVinculoControle.listaSelectItemColaborador}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_colaborador" action="#{HistoricoVinculoControle.montarListaSelectItemColaborador}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:colaborador"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HistoricoVinculo_tipoColaborador}" />
                            <h:inputText  id="tipoColaborador" size="2" maxlength="2" styleClass="campos" value="#{HistoricoVinculoControle.historicoVinculoVO.tipoColaborador}" />

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{HistoricoVinculoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoVinculoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{HistoricoVinculoControle.novo}" image="./imagens/botaoNovo.png" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="salvar" action="#{HistoricoVinculoControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="excluir" onclick="return confirm('#{msg.msg_ConfirmaExclusao}');" action="#{HistoricoVinculoControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="consultar" immediate="true" action="#{HistoricoVinculoControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:cliente").focus();
</script>
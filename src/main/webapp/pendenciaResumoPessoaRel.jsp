<%--
    Document   : pendenciaResumoPessoaRel
    Author     : <PERSON><PERSON><PERSON>
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo de Cliente(s): \"#{PendenciaControleRel.pendenciaRelVO.tipo.descricao}\""/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- FIM HEADER --%>
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <%-- INICIO HEADER --%>
                <c:set var="titulo" scope="session" value="${PendenciaControleRel.pendenciaRelVO.tipo.descricao} Total: ${PendenciaControleRel.pendenciaRelVO.qtd}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}${PendenciaControleRel.pendenciaRelVO.tipo.urlLinkWiki}"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" >
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid columns="1" width="100%">
                                            <h:panelGrid width="100%" style="text-align: right" >
                                                <h:panelGroup layout="block">
                                                    <a4j:commandLink id="exportarExcel"
                                                                     style="margin-left: 8px;"
                                                                     actionListener="#{PendenciaControleRel.exportarPendencia}"
                                                                     rendered="#{not empty PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                                     oncomplete="#{PendenciaControleRel.mensagemNotificar}#{PendenciaControleRel.msgAlert}"
                                                                     accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="tipo" value="xls"/>
                                                        <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,situacao_Apresentar=Situação,tipoBV=Tipo BV, conteudoMensagem=Mensagem,dataBVApresentar=Data BV"/>
                                                        <f:attribute name="prefixo" value="BoletimVisitasPendentes"/>
                                                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                    </a4j:commandLink>
                                                    <%--BOTÃO PDF--%>
                                                    <a4j:commandLink id="exportarPdf"
                                                                     style="margin-left: 8px;"
                                                                     actionListener="#{PendenciaControleRel.exportarPendencia}"
                                                                     rendered="#{not empty PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                                     oncomplete="#{PendenciaControleRel.mensagemNotificar}#{PendenciaControleRel.msgAlert}"
                                                                     accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="tipo" value="pdf"/>
                                                        <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,situacao_Apresentar=Situação,tipoBV=Tipo BV, conteudoMensagem=Mensagem,dataBVApresentar=Data BV"/>
                                                        <f:attribute name="prefixo" value="BoletimVisitasPendentes"/>
                                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <a4j:region ajaxListener="#{PendenciaControleRel.consultaPaginadoOrdenacao}">
                                                <rich:dataTable width="100%" id="tabela"
                                                                binding="#{PendenciaControleRel.dataTable}"
                                                                styleClass="tabelaSimplesCustom"
                                                                value="#{PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}" rows="50" var="resumoPessoa"  rowKeyVar="status">
                                                    <%--<%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>--%>
                                                    <rich:column sortBy="#{resumoPessoa.clienteVO.matricula}" id="col_matriculacli">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Matrícula" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.clienteVO.matricula}" />
                                                    </rich:column>
                                                    <rich:column sortBy="#{resumoPessoa.clienteVO.pessoa.nome}" id="col_nome">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.pessoa.nome}" />
                                                    </rich:column>
                                                    <rich:column sortBy="#{resumoPessoa.clienteVO.situacao_Apresentar}" id="col_situacaoCliente">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Situação" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.clienteVO.situacao_Apresentar}" />
                                                    </rich:column>
                                                    <rich:column id="col_tipoBV">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Tipo BV" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{resumoPessoa.tipoBV}" />
                                                    </rich:column>
                                                    <rich:column id="col_dataBV">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Data BV" />
                                                        </f:facet>
                                                        <h:outputText
                                                                styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                                                value="#{resumoPessoa.dataBVApresentar}">
                                                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                                                        </h:outputText>
                                                    </rich:column>
                                                    <rich:column id="col_opcao"   styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Opções" />
                                                        </f:facet>
                                                        <a4j:commandLink action="#{PendenciaControleRel.irParaTelaCliente}" styleClass="linkPadrao texto-cor-azul  texto-size-16-real" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                            <i class="fa-icon-search"></i>
                                                            <f:param name="state" value="AC"/>
                                                        </a4j:commandLink>
                                                    </rich:column>
                                                </rich:dataTable>
                                                <%--<rich:datascroller  align="center" for="form:tabela" maxPages="10" id="sctabela" />--%>
                                            </a4j:region>
                                            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="paginacao">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        <td align="center" valign="middle">
                                                            <h:panelGroup id="painelPaginacao" rendered="#{PendenciaControleRel.confPaginacao.existePaginacao}" styleClass="container-botoes">
                                                                <a4j:commandLink id="pagiInicial" styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="tabela, paginaAtual, totalItens"
                                                                                 action="#{PendenciaControleRel.primeiraPagina}">
                                                                    <i class="fa-icon-double-angle-left"></i>
                                                                </a4j:commandLink>

                                                                <a4j:commandLink id="pagiAnterior" styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabela, paginaAtual, totalItens"
                                                                                 action="#{PendenciaControleRel.paginaAnterior}">
                                                                    <i class="fa-icon-angle-left"></i>
                                                                </a4j:commandLink>

                                                                <h:outputText id="paginaAtual" styleClass="linkPadrao texto-font texto-cor-cinza texto-size-16-real"
                                                                              value="#{msg_aplic.prt_msg_pagina} #{PendenciaControleRel.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                                                                <a4j:commandLink id="pagiPosterior" styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="tabela, paginaAtual, totalItens"
                                                                                 action="#{PendenciaControleRel.proximaPagina}">
                                                                    <i class="fa-icon-angle-right"></i>
                                                                </a4j:commandLink>

                                                                <a4j:commandLink id="pagiFinal" styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="tabela, paginaAtual, totalItens"
                                                                                 action="#{PendenciaControleRel.ultimaPagina}">
                                                                    <i class="fa-icon-double-angle-right"></i>
                                                                </a4j:commandLink>

                                                                <h:outputText id="totalItens" styleClass="texto-cor-azul texto-cor-cinza texto-font texto-size-16-real"
                                                                              value=" [#{msg_aplic.prt_msg_itens} #{PendenciaControleRel.confPaginacao.numeroTotalItens}]" rendered="true"/>
                                                            </h:panelGroup>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

            </h:form>
        </body>
    </html>
</h:panelGrid>

</f:view>


<%-- 
    Document   : relatorioBVsForm
    Created on : 18/06/2012, 14:03:20
    Author     : carla
--%>

<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="./script/script.js"></script>
    <link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="./hoverform.js"></script>
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
</head>
<script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<script src="script/chart_amcharts.js" type="text/javascript"></script>
<script src="script/chart_pie.js" type="text/javascript"></script>
<script src="script/chart_light.js" type="text/javascript"></script>
<script src="script/chart_serial.js" type="text/javascript"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    .panelBotoesImportacao {
        background: #eee;
        padding: 30px;
        text-align: center;
    }

    .textoObjetivo {
        font-size: 16px;
        color: #333;
    }

    .passosImportacao {
        font-size: 15px;
        font-weight: bold;
        font-style: italic;
        color: #333;
    }

    .passosImportacaoDescricao {
        font-size: 15px;
        color: #333;
    }

    .panelObjetivo {
        padding: 20px 20px 0px 20px;
    }

    .panelPassos {
        padding: 10px 10px 10px 30px;
    }

    .panelPassosInterno {
        padding: 10px 10px 10px 10px;
    }

    .colunaEsquerdaImport {
        width: 15%;
        text-align: right;
    }

    .colunaDireitaImport {
        width: 60%;
        text-align: left;
    }

    .observacoesImportantes {
        font-style: italic;
        font-weight: bold;
    }

    .observacoesImportantesItem {
        padding-left: 10px;
    }

    .rich-tab-active {
        border-radius: 50px;
        padding: 0 10px;
        height: 32px;
        margin-right: 10px;
        line-height: 32px;
        font-size: 1em;
        background: #29abe2 !important;
        color: #FFF !important;
    }

    .rich-tab-inactive {
        border-radius: 50px;
        padding: 0 10px;
        height: 32px;
        color: #29abe2;
        background-color: #FFF;
        background: #FFF !important;
        margin-right: 10px;
        line-height: 32px;
        font-size: 1em;
    }

    .rich-tab-header {
        font-size: 14px;
        font-family: arial, helvetica, sans-serif;
    }

    .rich-tabhdr-cell-disabled {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-cell-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-cell {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-border {
        background: none !important;
        border: none !important;
    }

    .rich-tab-bottom-line {
        background: none !important;
        border: none !important;
    }

    .rich-tabpanel-content {
        background: none !important;
        border: none !important;
    }

    .fontSize12 {
        font-size: 12px;
    }

</style>

<script type="text/javascript">
    jQuery.noConflict();
</script>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText
                value="#{RelatorioBVsControle.relatorioPesquisa ? 'Relatório de Pesquisas' : 'Relatório de BVs'}"/>
    </title>

    <f:facet name="header">
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <c:set var="titulo" scope="session"
               value="${RelatorioBVsControle.relatorioPesquisa ? 'Relatório de Pesquisas' : 'Relatório de BVs'}"/>

        <c:if test="${RelatorioBVsControle.relatorioPesquisa}">
            <c:set var="urlWiki" scope="session"
                   value="${SuperControle.urlBaseConhecimento}onde-posso-ver-as-respostas-das-pesquisas-enviadas-para-os-clientes/"/>
        </c:if>

        <c:if test="${!RelatorioBVsControle.relatorioPesquisa}">
            <c:set var="urlWiki" scope="session"
                   value="${SuperControle.urlBaseConhecimento}como-tirar-um-relatorio-das-respostas-dos-bvs-boletins-de-visitas-questionarios-respondidos-pelos-clientes/"/>
        </c:if>

        <jsp:include page="topoReduzido_material.jsp"/>

    </f:facet>

    <a4j:form id="form" styleClass="pure-form" style="height: 100%; padding: 10px">

        <%@include file="includes/include_filtros_relatorio_bv.jsp" %>

        <h:panelGroup layout="block" id="panelMsgBv" style="width: 100%; text-align: center">
            <a4j:commandLink id="consultaRelatorio" ajaxSingle="false"
                             styleClass="pure-button pure-button-primary"
                             action="#{RelatorioBVsControle.consultarComFiltros}"
                             reRender="form"
                             oncomplete="#{RelatorioBVsControle.mensagemNotificar}"
                             accesskey="2">
                Consultar&nbsp<i class="fa-icon-search"></i>
            </a4j:commandLink>
            <a4j:commandLink rendered="#{RelatorioBVsControle.mostrarConteudo and false}"
                             style="margin-left: 10px"
                             styleClass="pure-button"
                             oncomplete="abrirPopup('visualizarImpressao.jsp', 'VisualizarImpressao', 1044, 700);">
                <i class="fa-icon-print"></i> &nbsp Visualizar Impressão
            </a4j:commandLink>
        </h:panelGroup>

        <rich:panel style="padding-top: 20px" id="panelFiltros"
                    rendered="#{!empty RelatorioBVsControle.filtrosConsulta.filtrosApresentar}">
            <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                          escape="false" value="Filtros: "/>
            <h:outputText styleClass="tituloCampos" escape="false"
                          value="#{RelatorioBVsControle.filtrosConsulta.filtrosApresentar}"/>
        </rich:panel>

        <%@include file="includes/include_resultado_relatorio_bv.jsp" %>

        <rich:modalPanel id="panelStatus10" autosized="true">
            <h:panelGrid columns="3">
                <h:graphicImage url="./imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>

    </a4j:form>
</f:view>
<script>

    function carregarTooltipster() {
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }

    function atualizarTempoImportacao() {
        try {
            fireElementFromAnyParent('form:btnAtualizaTempo')
        } catch (ex) {
            console.log('Erro [atualizarTempoImportacao] ' + ex)
        }
    }

    atualizarTempoImportacao();
    carregarTooltipster();
</script>

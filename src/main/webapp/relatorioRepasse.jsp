<%--
<%--
    Document   : relatorioRepasse
    Created on : 21/12/2012, 14:03:20
    Author     : joa<PERSON> alcides
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@ taglib prefix="ajax" uri="http://java.sun.com/jsf/core" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="css/jquery.treeTable.css" rel="stylesheet" type="text/css">

<%@include file="/includes/imports.jsp" %>

<script type="text/javascript" src="script/cursor.js"></script>

<script type="text/javascript" src="script/gestaoComissao.js"></script>

<head>
    <jsp:include page="include_head.jsp" flush="true"/>
</head>

<style type="text/css">
    .titulo {
        font-size: 12px;
        padding: 4px 4px 4px 4px;
    }

    .titulo:hover {
        text-decoration: none;
    }

    .rich-table-cell {
        padding: 1px 1px 1px 1px;
    }

    .borda_azul {
        border-bottom: 2px solid #CEDFFF;
    }

    .Passo .Texto {
        color: #FFFFFF;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        line-height: normal;
        font-weight: bold;
        float: left;
        padding-left: 8px;
        padding-top: 19px;
        text-align: left;
    }

    .Passo .Imagem {
        float: left;
        height: 38px;
        overflow-x: hidden;
        overflow-y: hidden;
    }

    .Passo .Info {
        color: #C0C0C0;
        padding-right: 3px;
        padding-top: 25px;
        text-transform: uppercase;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        line-height: normal;
        text-transform: uppercase;
        font-weight: bold;

    }

    .Passo {
        background: no-repeat 25px 0;
        border-bottom: 1px solid #D3D3D3;
        color: #FFFFFF;
        cursor: pointer;
        font-weight: bold;
        height: 38px;
        text-align: right;
    }
    .alinhar{
            vertical-align: top !important;
        }
</style>


<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Relatório de Repasse"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-de-repasse/"/>
    <h:form id="form" style="background-color: #FFFFFF;">
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <body>
            <jsp:include page="include_head.jsp" flush="true" />

            <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                <tr>
                    <jsp:include page="topoReduzido_material.jsp"/>
                    <jsp:include page="include_head.jsp" flush="true"/>
                    <td align="left" valign="top" class="bglateral">
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <tr>

                                <td align="left" width="100%" valign="top"  >
                                    <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" >
                                        <tr>
                                            <td align="left" width="100%"  >
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>


                                                <h:panelGroup id="panelConteudo">
                                                    <h:inputHidden id="CControle"
                                                                   value="#{RelatorioRepasseControle.openConsulta}"/>
                                                    <h:inputHidden id="CDatas"
                                                                   value="#{RelatorioRepasseControle.openParametros}"/>
                                                    <h:inputHidden id="CFiltros"
                                                                   value="#{RelatorioRepasseControle.openPlanos}"/>

                                                    <h:panelGrid columns="1" width="100%">
                                                        <div id="CExpandido" class="Passo"
                                                             onclick="esconderMostrar('consulta', true, 'CExpandido', 'CRetraido', 'form:CControle');"
                                                             style="cursor: pointer; background: url(./imagens/folder/Passo1.jpg) no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso1-exp"
                                                                     src="./imagens/folder/PassoExpandir.jpg"
                                                                     style="border-width: 0;" title="Ocultar"/>
                                                            </div>
                                                            <div class="Texto">Tipo Consulta</div>
                                                            <div class="Info">
                                                                <span id="ctl00_cph_lblPasso1-exp">Ocultar</span>
                                                            </div>
                                                        </div>
                                                        <div id="CRetraido" class="Passo"
                                                             onclick="esconderMostrar('consulta', false, 'CRetraido', 'CExpandido', 'form:CControle');"
                                                             style="display: none; cursor: pointer; background: url(./imagens/folder/Passo1.jpg) no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso1-ret"
                                                                     src="./imagens/folder/PassoOcultar.jpg"
                                                                     style="border-width: 0;"
                                                                     title="Ocultar" alt="passo1"/>
                                                            </div>
                                                            <div class="Texto">Tipo Consulta</div>
                                                            <div class="Info">
                                                                <span id="ctl00_cph_lblPasso1-ret">Expandir</span>
                                                            </div>
                                                        </div>
                                                        <table bgcolor="#F5F5F5" id="consulta" width="100%" style="display: block;">
                                                            <tr>
                                                                <td>
                                                                    <h:selectOneRadio id="TipoRelatorio" styleClass="tituloDemonstrativo"
                                                                                      value="#{RelatorioRepasseControle.tipoRelatorioRep}">
                                                                        <f:selectItem itemLabel="Emitir Relatório de Repasse" itemValue="1"/>
                                                                        <f:selectItem itemLabel="Emitir Listagem de Alunos desconsiderados" itemValue="2"/>
                                                                    </h:selectOneRadio>
                                                                </td>
                                                            </tr>
                                                        </table>


                                                        <div id="RCExpandido" class="Passo"
                                                             onclick="esconderMostrar('rConsulta', true, 'RCExpandido', 'RCRetraido', 'form:CDatas');"
                                                             style="cursor: pointer; background: url(./imagens/folder/Passo2.jpg) no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso3-exp"
                                                                     src="./imagens/folder/PassoExpandir.jpg"
                                                                     style="border-width: 0;"
                                                                     title="Ocultar" alt="passo3-expandir"/>
                                                            </div>
                                                            <div class="Texto">Parâmetros</div>
                                                            <div class="Info">
                                                                <span>Ocultar</span>
                                                            </div>
                                                        </div>
                                                        <div id="RCRetraido"
                                                             class="Passo"
                                                             onclick="esconderMostrar('rConsulta', false, 'RCRetraido', 'RCExpandido', 'form:CDatas');"
                                                             style="display: none; cursor: pointer; background: url(./imagens/folder/Passo2.jpg) no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso3-ret"
                                                                     src="./imagens/folder/PassoOcultar.jpg"
                                                                     style="border-width: 0;" title="Ocultar" alt=""/>
                                                            </div>
                                                            <div class="Texto">Parâmetros</div>
                                                            <div class="Info">
                                                                <span>Expandir</span>
                                                            </div>
                                                        </div>
                                                        <table bgcolor="#F5F5F5" id="rConsulta" width="100%"
                                                               style="display: block;">
                                                            <tr>
                                                                <td width="100%">
                                                                    <h:panelGrid cellpadding="0" cellspacing="0" columns="3"
                                                                                 columnClasses="alinhar, alinhar">
                                                                        <h:panelGroup>
                                                                            <h:panelGroup id="panelDatas" layout="block"
                                                                                          style="vertical-align:top; margin-left: 7px; margin-top: 7px">
                                                                                <h:panelGroup>
                                                                                    <h:outputText styleClass="tituloCampos"
                                                                                                  style="vertical-align:middle;"
                                                                                                  value="De: "/>
                                                                                    <rich:calendar id="dataInicioCompensacao"
                                                                                                   value="#{RelatorioRepasseControle.dataInicio}"
                                                                                                   inputSize="10"
                                                                                                   oninputblur="blurinput(this);"
                                                                                                   oninputfocus="focusinput(this);"
                                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                                   datePattern="#{RelatorioRepasseControle.datePattern}"
                                                                                                   enableManualInput="true"
                                                                                                   zindex="2"
                                                                                                   showWeeksBar="false"/>
                                                                                </h:panelGroup>
                                                                                <rich:spacer width="20px"/>
                                                                                <h:outputText styleClass="tituloCampos"
                                                                                              style="vertical-align:middle;"
                                                                                              value="Até: "/>

                                                                                <h:panelGroup>
                                                                                    <rich:calendar id="dataFimCompensacao"
                                                                                                   value="#{RelatorioRepasseControle.dataFim}"
                                                                                                   inputSize="10"
                                                                                                   oninputblur="blurinput(this);"
                                                                                                   oninputfocus="focusinput(this);"
                                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                                   enableManualInput="true"
                                                                                                   datePattern="#{RelatorioRepasseControle.datePattern}"
                                                                                                   zindex="2"
                                                                                                   showWeeksBar="false"
                                                                                                   mode="client"/>
                                                                                </h:panelGroup>
                                                                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                                                <h:panelGroup>
                                                                                    <rich:spacer width="20px"/>
                                                                                    <h:outputText styleClass="tituloCampos"
                                                                                                  style="vertical-align:middle;"
                                                                                                  value="Taxa de repasse: "/>
                                                                                    <h:inputText maxlength="2" size="3" value="#{RelatorioRepasseControle.taxaRepasse}">
                                                                                        <f:converter converterId="FormatadorNumerico" />
                                                                                    </h:inputText>
                                                                                </h:panelGroup>
                                                                                <rich:spacer width="20px"/>

                                                                            </h:panelGroup>
                                                                        </h:panelGroup>




                                                                        <h:panelGroup layout="block"
                                                                                      style="margin-left: 7px; margin-top: 7px;">
                                                                            <h:outputText styleClass="tituloCampos"
                                                                                                  style="vertical-align:middle;"
                                                                                                  value="Relatório por: "/>
                                                                         </h:panelGroup>

                                                                         <h:selectOneRadio id="tipodatas"
                                                                                                      styleClass="tituloDemonstrativo"
                                                                                                      value="#{RelatorioRepasseControle.tipoEscolhido}">
                                                                                        <f:selectItems value="#{RelatorioRepasseControle.tipo}" />
                                                                                        <a4j:support event="onclick" reRender="panelDatas"/>
                                                                                    </h:selectOneRadio>

                                                                    </h:panelGrid>
                                                                </td>
                                                            </tr>
                                                        </table>


                                                        <div id="CFiltrosExpandido" class="Passo"
                                                             onclick="esconderMostrar('rFiltros', true, 'CFiltrosExpandido', 'CFiltrosRetraido', 'form:CFiltros');"
                                                             style="cursor: pointer; background: url(./imagens/folder/Passo3.jpg) no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso2-exp"
                                                                     src="./imagens/folder/PassoExpandir.jpg"
                                                                     style="border-width: 0;" title="Ocultar"/>
                                                            </div>
                                                            <div class="Texto">Planos a serem desconsiderados</div>
                                                            <div class="Info">
                                                                <span>Ocultar</span>
                                                            </div>
                                                        </div>
                                                        <div id="CFiltrosRetraido"
                                                             class="Passo"
                                                             onclick="esconderMostrar('rFiltros', false, 'CFiltrosRetraido', 'CFiltrosExpandido', 'form:CFiltros');"
                                                             style="display: none; cursor: pointer; background: url(./imagens/folder/Passo3.jpg) no-repeat;">
                                                            <div class="Imagem">
                                                                <img id="ctl00_cph_imgPasso2-ret"
                                                                     src="./imagens/folder/PassoOcultar.jpg"
                                                                     style="border-width: 0;" title="Ocultar" alt=""/>
                                                            </div>
                                                            <div class="Texto">Planos a serem desconsiderados</div>
                                                            <div class="Info">
                                                                <span>Expandir</span>
                                                            </div>
                                                        </div>
                                                        <table bgcolor="#F5F5F5" id="rFiltros" width="100%"
                                                               style="display: block;">
                                                            <tr>
                                                                <td width="100%">
                                                                    <h:panelGrid columns="3">
                                                                        <h:outputText styleClass="tituloCampos"
                                                                                      value="#{msg_aplic.prt_Recibo_empresa}"/>
                                                                        <h:selectOneMenu id="empresa"
                                                                                         onblur="blurinput(this);"
                                                                                         onfocus="focusinput(this);"
                                                                                         styleClass="form"
                                                                                         value="#{RelatorioRepasseControle.empresaVO.codigo}"

                                                                                         >
                                                                            <a4j:support event="onchange" action="#{RelatorioRepasseControle.montarPlanosDesconsiderar}" reRender="planosDesconsiderar,rotuloMarcarTodos"  />
                                                                            <f:selectItems value="#{RelatorioRepasseControle.listaEmpresas}"/>
                                                                        </h:selectOneMenu>


                                                                         </h:panelGrid>
                                                                    <a4j:commandLink id="rotuloMarcarTodos" value="#{RelatorioRepasseControle.rotulo}"  action="#{RelatorioRepasseControle.selecionaTodosPlanos}"
                                                                                     reRender="planosDesconsiderar,rotuloMarcarTodos"/>



                                                                    <rich:panel >
                                                                        <rich:dataGrid id="planosDesconsiderar" style="border: none;"
                                                                                       columnClasses="semBorda"
                                                                                       value="#{RelatorioRepasseControle.planosDesconsiderar}"
                                                                                       var="plano" columns="4">

                                                                            <h:selectBooleanCheckbox id="planoEscolhidoRelRepasse"
                                                                                    styleClass="tituloCampos"
                                                                                    value="#{plano.escolhido}"></h:selectBooleanCheckbox>
                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          value="#{plano.descricao}"/>
                                                                        </rich:dataGrid>
                                                                    </rich:panel>
                                                                    </h:panelGrid>

                                                    <rich:spacer width="10px"></rich:spacer>
                                                                    <h:panelGroup layout="block"
                                                                                  styleClass="container-botoes" style="text-align: left">
                                                                        <a4j:commandLink id="btnConsultarRelRepasse"
                                                                                action="#{RelatorioRepasseControle.consultarInformacoes}"
                                                                                value="#{msg_bt.btn_consultar}"
                                                                                oncomplete="#{RelatorioRepasseControle.msgAlert}"
                                                                                styleClass="botaoPrimario texto-size-16-real"
                                                                                reRender="tabelaRepasse"/>
                                                                    </h:panelGroup>


                                                    <h:panelGrid columns="1" width="100%" id="tabelaRepasse">
                                                        <rich:spacer height="10px"></rich:spacer>
                                                        <h:panelGroup>
                                                            <rich:dataTable value="#{RelatorioRepasseControle.listaRepasse}" var="repasse" width="100%"
                                                                            rendered="#{not empty RelatorioRepasseControle.listaRepasse}"
                                                                            id="listaRepasse"
                                                                            rowClasses="linhaPar,linhaImpar" rows="30"
                                                                            columnClasses="centralizado, esquerda, centralizado, esquerda, direita, centralizado,centralizado, direita, direita, direita">

                                                                <rich:column id="matricula" sortBy="#{repasse.matricula}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_repasse_matricula}"></h:outputText>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.matricula}" ></h:outputText>
                                                                </rich:column>

                                                                <rich:column id="nomeAluno" sortBy="#{repasse.nomeAluno}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_repasse_nomeAluno}"></h:outputText>
                                                                    </f:facet>

                                                                    <a4j:commandLink action="#{RelatorioRepasseControle.irParaTelaCliente}"
                                                                                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                        <h:outputText value="#{repasse.nomeAluno}" ></h:outputText>
                                                                    </a4j:commandLink>
                                                                </rich:column>

                                                                <rich:column >
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{RelatorioRepasseControle.headerColunaCod}"/>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.movpagamento}" rendered="#{!RelatorioRepasseControle.competencia}"/>
                                                                    <h:outputText value="#{repasse.movproduto}" rendered="#{RelatorioRepasseControle.competencia}"/>
                                                                </rich:column>

                                                                <rich:column id="plano" sortBy="#{repasse.plano}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_repasse_plano}"/>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.plano}"/>
                                                                </rich:column>

                                                                <rich:column id="valor" sortBy="#{repasse.valor}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_repasse_valorPago}"/>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.valor_Apresentar}"/>
                                                                </rich:column>

                                                                <rich:column sortBy="#{repasse.dataCompensacao}" id="dataCompensacaoComp"
                                                                             rendered="#{RelatorioRepasseControle.competencia}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="Mês referência"/>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.mesReferencia}" >
                                                                    </h:outputText>
                                                                </rich:column>

                                                                <rich:column sortBy="#{repasse.dataCompensacao}" id="dataCompensacao"
                                                                             rendered="#{!RelatorioRepasseControle.competencia}">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_repasse_dataCompensacao}"></h:outputText>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.dataCompensacao}" >
                                                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                                    </h:outputText>
                                                                </rich:column>

                                                                <rich:column sortBy="#{repasse.totalDescontar}" id="totalDescontar">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_repasse_totalDescontar}"></h:outputText>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.totalDescontar_Apresentar}" ></h:outputText>
                                                                </rich:column>

                                                                <rich:column sortBy="#{repasse.valorCompensado}" id="valorCompensado">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_repasse_valorCompensado}"></h:outputText>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.valorCompensado_Apresentar}" ></h:outputText>
                                                                </rich:column>

                                                                <rich:column sortBy="#{repasse.valorRepasse}" id="valorRepasse">
                                                                    <f:facet name="header">
                                                                        <h:outputText value="#{msg_aplic.prt_repasse_valorRepasse}"></h:outputText>
                                                                    </f:facet>
                                                                    <h:outputText value="#{repasse.valorRepasse_Apresentar}" ></h:outputText>
                                                                </rich:column>
                                                            </rich:dataTable>
                                                            <rich:datascroller rendered="#{not empty RelatorioRepasseControle.listaRepasse}" for="listaRepasse"></rich:datascroller>


                                                        </h:panelGroup>
                                                        <h:panelGroup layout="block" styleClass="container-botoes">
                                                                <a4j:commandLink id="imprimirPDF" ajaxSingle="false"
                                                                                 styleClass="botaoSecundario texto-size-16-real "
                                                                                   oncomplete="#{RelatorioRepasseControle.mensagemNotificar}#{RelatorioRepasseControle.msgAlert}"
                                                                                   action="#{RelatorioRepasseControle.imprimirPDF}"
                                                                                   rendered="#{not empty RelatorioRepasseControle.listaRepasse}">
                                                                    <i class="fa-icon-print"></i>
                                                                </a4j:commandLink>
                                                            </h:panelGroup>
                                                        <h:panelGroup>
                                                            <c:if test="${not empty RelatorioRepasseControle.listaRepasse}">
                                                                <table width="100%">
                                                                    <tr>
                                                                        <td width="20%"><rich:spacer width="30px" ></rich:spacer> <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                                                                                                                    value="#{msg_aplic.prt_repasse_totalValorPago}" ></h:outputText></td>
                                                                            <td width="80%">
                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          value="#{RelatorioRepasseControle.totalValorPago}" ></h:outputText></td>
                                                                        </tr>

                                                                        <tr>
                                                                            <td width="20%"><rich:spacer width="30px" ></rich:spacer> <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                                                                                                                    value="#{msg_aplic.prt_repasse_totalDescontado}" ></h:outputText></td>
                                                                            <td width="80%">
                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          value="#{RelatorioRepasseControle.totalDescontado}" ></h:outputText></td>
                                                                        </tr>

                                                                        <tr>
                                                                            <td width="20%"><rich:spacer width="30px" ></rich:spacer> <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                                                                                                                    value="#{msg_aplic.prt_repasse_totalCompensado}" ></h:outputText></td>
                                                                            <td width="80%">
                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          value="#{RelatorioRepasseControle.totalCompensado}" ></h:outputText></td>
                                                                        </tr>

                                                                        <tr>
                                                                            <td width="20%"><rich:spacer width="30px" ></rich:spacer>
                                                                            <h:outputText styleClass="tituloCampos" style="font-weight: bold;"
                                                                                          value="#{msg_aplic.prt_repasse_totalRepasse}" ></h:outputText></td>
                                                                            <td width="80%">
                                                                            <h:outputText styleClass="tituloCampos"
                                                                                          value="#{RelatorioRepasseControle.totalRepasse}" ></h:outputText></td>
                                                                        </tr>
                                                                    </table>
                                                            </c:if>
                                                        </h:panelGroup>

                                                    </h:panelGrid>
                                                    <h:panelGrid id="mensagem" columns="1" width="100%"
                                                                 styleClass="tabMensagens" style="margin:5px;">
                                                        <h:outputText styleClass="mensagem"
                                                                      value="#{GestaoComissaoControle.mensagem}"/>
                                                        <h:outputText styleClass="mensagemDetalhada"
                                                                      value="#{GestaoComissaoControle.mensagemDetalhada}"/>
                                                    </h:panelGrid>



                                                </h:panelGroup>


                                            </td>
                                        </tr>

                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                        </table>
        </body>

    </h:form>

</f:view>

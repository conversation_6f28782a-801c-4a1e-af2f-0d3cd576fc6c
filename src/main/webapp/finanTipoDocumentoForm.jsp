<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="a4j" uri="http://richfaces.org/a4j" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<%@include file="/includes/verificaModulo.jsp" %>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText
            value="#{msg_aplic.prt_Finan_Tipo_Documento_tituloForm}" /></title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Finan_Tipo_Documento_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-um-tipo-de-documento/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="/topoReduzido_material.jsp" />
        </f:facet>

        <h:form id="form">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <!-- O FORMULARIO -->
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">
                    <!-- Descrição -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Tipo_Documento_descricao}" />

                    <h:inputText id="valorConsulta" styleClass="form" size="40"
                                 maxlength="100" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 value="#{TipoDocumentoControle.tipoDocumentoVO.descricao}" />
                </h:panelGrid>
                <!-- FIM DO FORMULÁRIO -->
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <!-- mensagens -->
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" " />
                        </h:panelGrid>
                        <h:commandButton rendered="#{TipoDocumentoControle.sucesso}"
                                         image="./imagens/sucesso.png" />
                        <h:commandButton rendered="#{TipoDocumentoControle.erro}"
                                         image="./imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{TipoDocumentoControle.mensagem}" />
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{TipoDocumentoControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton immediate="true"
                                             action="#{TipoDocumentoControle.novo}"
                                             value="#{msg_bt.btn_novo}"
                                             styleClass="botoes nvoBt btSec"
                                             title="#{msg.msg_novo_dados}"/>

                            <a4j:commandButton id="salvar" action="#{TipoDocumentoControle.gravar}"
                                               value="Gravar"
                                              oncomplete="#{TipoDocumentoControle.msgAlert}"
                                               alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:commandButton id="excluir"
                                             rendered="#{!empty TipoDocumentoControle.tipoDocumentoVO.codigo}"
                                             onclick="return confirm('Confirma exclusão desta conta?');"
                                             action="#{TipoDocumentoControle.excluir}"
                                             value="#{msg_bt.btn_excluir}"
                                             title="#{msg.msg_excluir_dados}"
                                             accesskey="3"
                                             styleClass="botoes nvoBt btSec btPerigo"/>

                            <a4j:commandLink id="btnLog"
                                             styleClass="botoes nvoBt btSec"
                                             style="display: inline-block; padding: 9px 13px;"
                                             action="#{TipoDocumentoControle.realizarConsultaLogObjetoSelecionado}"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                <h:outputText title="visualizar log da entidade" styleClass="fa-icon-list"/>
                            </a4j:commandLink>

                            <h:commandButton id="consultar" immediate="true"
                                             action="#{TipoDocumentoControle.consultar}"
                                             value="#{msg_bt.btn_voltar_lista}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4"
                                             styleClass="botoes nvoBt btSec"/>

                        </h:panelGroup>
                    </h:panelGrid>
                    <!-- fim botoes -->
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<%-- 
    Document   : include_modais_tela_cadastro_cliente
    Created on : 08/05/2012, 16:51:25
    Author     : Waller
--%>
<%@include file="includes/imports.jsp" %>
<script type="text/javascript" src="script/jquery.maskedinput-1.2.2.js"></script>
<input type="hidden" name="modulo" value="${modulo}"/>
<script type="text/javascript" language="javascript">
    function buildZebraTable(tableId) {
        var table=document.getElementById(tableId);
        if(!table){return};

        // get all <tr> table elements
        var trows=table.getElementsByTagName('tr');
        //table.className = 'tablelistras textsmall';
        for(var j = 0; j < trows.length; j++){
            // assign CSS class to even and odd rows
            trows[j].className = j % 2 == 0 ? 'par' : '';
            if (j % 2 == 0){
                for (var k = 0; k < trows[j].childNodes.length; k ++){
                    trows[j].childNodes[k].className = 'par';
                }

            }
        }
    }

    function validar(){

        if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
                && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
                && document.getElementById('formConsultarCEP:bairroCEP').value == ""
                && document.getElementById('formConsultarCEP:logradouroCEP').value == ""){

            alert("Ao menos um par�metro deve ser informado!");
            return false;
        }
        return true;
    }

    // run 'buildZebraTable()' function when web page is loaded
    window.onload=function zebra() {
        buildZebraTable('tabela1');
        buildZebraTable('tabela2');
        buildZebraTable('tabela3');
    }
</script>
<rich:modalPanel id="panelExistePessoa" autosized="true" shadowOpacity="true"
                 showWhenRendered="#{ClienteControle.pessoaVO.apresentarRichModalErro}" width="300" height="150">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Aten��o!"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formExistePessoa" ajaxSubmit="true">
        <jsp:include page="includes/include_persiste_modulo.jsp"/>
        <h:panelGrid columns="1" width="100%">
            <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloCampos" value="#{msg.msg_existePessoa}" />
            </h:panelGrid>

            <h:panelGrid columns="2" width="20%" headerClass="subordinado" columnClasses="colunaCentralizada">
                <a4j:commandButton action="#{ClienteControle.adicionarPessoa}"
                                   reRender="codigo , empresa, panelFoto,nomeCliente,dataNascCliente,categoria,nomeMae,
                                   nomePai,sexo,profissao,grauIntrucao,estadoCivil,cpf,rg,rgOrgao,rgUf,dataCadastro,
                                   residencial,comercial,celular,email,webPage,containerEndereco"
                                   oncomplete="Richfaces.hideModalPanel('panelExistePessoa')" value="#{msg_bt.btn_sim}"
                                   image="./imagens/botaoSim.png" accesskey="5" styleClass="botaoEspecial" />
                <a4j:commandButton action="#{ClienteControle.setarFalso}"
                                   reRender="codigo, empresa, panelFoto, nomeCliente,dataNascCliente,categoria,
                                   nomeMae,nomePai,sexo,profissao,grauIntrucao,estadoCivil,cpf,rg,rgOrgao,
                                   rgUf,dataCadastro,residencial,comercial,celular,email,webPage,CEP,endereco,
                                   complento,bairro,numero,pais,estado,cidade"
                                   onclick="Richfaces.hideModalPanel('panelExistePessoa')" value="#{msg_bt.btn_nao}"
                                   image="./imagens/botaoNao.png" accesskey="6" styleClass="botaoEspecial" />
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<%@include file="include_modal_transferirCliente.jsp" %>

<rich:modalPanel id="panelCadastroSimplificadoCliente" autosized="true" shadowOpacity="true"
                 styleClass="novaModal"
                 showWhenRendered="#{ClienteControle.clienteVO.apresentarRichModalCadastroRapido && !ClienteControle.telaNegociacaoContrato}" width="550"
                 height="300" onshow="#{rich:element('nomeCadastroSimplificadoCliente')}.focus();">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Cadastrar Cliente"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkCadastroSimplificadoCliente"/>
            <rich:componentControl for="panelCadastroSimplificadoCliente" attachTo="hidelinkCadastroSimplificadoCliente"
                                   operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCadastroSimplificadoCliente" ajaxSubmit="true">
        <jsp:include page="includes/include_persiste_modulo.jsp"/>
        <h:panelGrid columns="1" width="100%">

            <h:panelGrid id="panelCadastroSimples" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">
                <h:outputText value="Cliente de Sess�o" rendered="#{LoginControle.apresentarLinkEstudio}"/>
                <h:panelGroup id="tipoCadastro" rendered="#{LoginControle.apresentarLinkEstudio}">
                    <h:selectBooleanCheckbox value="#{ClienteControle.clienteSessao}"/>
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.nomeOb}"/>
                    <h:outputText value="Nome Completo " />
                </h:panelGroup>
                <h:panelGroup>
                    <h:inputText id="nomeCadastroSimplificadoCliente" size="50" maxlength="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.pessoaVO.nome}" />

                    <rich:suggestionbox   id="suggestionResponsavel"
                                          height="200" width="400"
                                          for="nomeCadastroSimplificadoCliente"
                                          status="statusHora"
                                          immediate="true"
                                          suggestionAction="#{ClienteControle.executarAutocompleteClientePotencialVO}"
                                          minChars="3"
                                          rowClasses="linhaImpar, linhaPar"
                                          var="result">
                        <a4j:support event="onselect" reRender="panelCadastroSimples" focus="nomeCadastroSimplificadoCliente"
                                     action="#{ClienteControle.selecionarClientePotencial}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome"  styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.passivoVO.nome}" rendered="#{result.isApresentarPassivo}" />
                            <h:outputText styleClass="textverysmall" value="#{result.indicadoVO.nomeIndicado}" rendered="#{result.isApresentarIndicado}"/>
                            <h:outputText styleClass="textverysmall" value="#{result.clienteVO.pessoa.nome}"  rendered="#{result.isApresentarCliente}"/>
                        </h:column>
                        <h:column >
                            <f:facet name="header">
                                <h:outputText value="Nascimento" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.dataNasc}" />
                        </h:column>
                        <h:column >
                            <f:facet name="header">
                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.tipoPessoa}" />
                        </h:column>
                        <h:column >
                            <f:facet name="header">
                                <h:outputText value="Telefone" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText  styleClass="textverysmall" value="#{result.telefone}" />
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.dataNascOb}"/>
                    <h:outputText value="Data Nascimento" />
                </h:panelGroup>
                <h:panelGroup>
                    <%--
                    <rich:calendar id="dataNascCadastroSimplificadoCliente" value="#{ClienteControle.pessoaVO.dataNasc}" inputSize="10"
                                   inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                   datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />

                        <h:inputText id="dataNascCadastroSimplificadoCliente" value="#{ClienteControle.pessoaVO.dataNasc}" size="10"
                                     styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     onkeypress="return mascara(this.form, this.id, '99/99/9999', event);">
                            <f:convertDateTime pattern="dd/MM/yyyy" locale="#{SuperControle.localeDefault}" timeZone="#{SuperControle.timeZoneDefault}"/>
                            <a4j:support event="onchange" action="#{ClienteControle.validarIdade}" reRender="form,nomeMaeApresentar" />
                        </h:inputText>
                        <h:message for="dataNascCadastroSimplificadoCliente" styleClass="mensagemDetalhada" />--%>
                    <h:inputText id="dataNascCadastroSimplificadoCliente"
                                 value="#{ClienteControle.pessoaVO.dataNasc}"
                                 size="10"
                                 styleClass="form"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 onchange="return validar_Data(this.id);
                                 return validarIdade(#{rich:component('dataNascCadastroSimplificadoCliente')}.getCurrentDate());"
                                 onkeypress="return mascara(this.form, this.id, '99/99/9999', event);">
                        <f:convertDateTime pattern="dd/MM/yyyy"
                                           locale="#{SuperControle.localeDefault}"
                                           timeZone="#{SuperControle.timeZoneDefault}"/>
                        <a4j:support event="onchange" action="#{ClienteControle.validarIdade}" reRender="formCadastroSimplificadoCliente,panelMensagem"/>
                    </h:inputText>
                    <h:message for="dataNascCadastroSimplificadoCliente" styleClass="mensagemDetalhada" />
                    <a4j:jsFunction name="validarIdade" action="#{ClienteControle.validarIdade}"
                                    focus="cpfCadastroSimplificadoCliente"
                                    reRender="formCadastroSimplificadoCliente,panelMensagem">
                        <a4j:actionparam name="dtnasc" assignTo="#{ClienteControle.pessoaVO.dataNasc}"/>
                    </a4j:jsFunction>

                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.cfpOb}"/>
                    <h:outputText value="CPF" />
                </h:panelGroup>
                <h:panelGroup>
                    <rich:jQuery id="mskCPF" selector="#cpfCadastroSimplificadoCliente" timing="onload" query="mask('999.999.999-99')" />
                    <h:inputText id="cpfCadastroSimplificadoCliente" size="14" maxlength="14" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.pessoaVO.cfp}">
                        <a4j:support event="onchange" action="#{ClienteControle.validarClienteSimples}"
                                     reRender="formCadastroSimplificadoCliente,panelMensagem" />
                    </h:inputText>
                    <h:inputHidden id="inputInvisivel" />
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText id="msgPanelCadas" styleClass="mensagem" value="#{ClienteControle.mensagem}" />
                    <h:outputText id="msgPanelCadasDet"  styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}" />
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="salvarCadastroSimplificadoCliente"
                                           onmousemove="document.getElementById('formCadastroSimplificadoCliente:salvarCadastroSimplificadoCliente').focus();"
                                           action="#{ClienteControle.validarCampos}" focus="nomeCliente"
                                           value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"
                                           reRender="codigo,empresa,iptTipoCadastro,panelFoto,nomeCliente,dataNascCliente,categoria,nomeMae,nomePai,sexo,profissao,
                                           grauIntrucao,estadoCivil,cpfMsk,rg,rgOrgao,rgUf,dataCadastro,residencial,comercial,celular,email,
                                           webPage,CEP,endereco,complento,bairro,numero,pais,estado,cidade,estado,panelExistePessoa,
                                           panelExisteCliente,panelCadastroSimplificadoCliente"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelProfissao" styleClass="novaModal" autosized="true" shadowOpacity="true" width="550" height="300">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Cadastro de Profiss�o"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelink1"/>
            <rich:componentControl for="panelProfissao" attachTo="hidelink1" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formProfissao" ajaxSubmit="true">
        <jsp:include page="includes/include_persiste_modulo.jsp"/>
        <h:panelGrid columns="1" width="100%">

            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold" value="#{msg_aplic.prt_Profissao_maiusculo}" />
                <h:panelGroup>
                    <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{ClienteControle.profissaoVO.codigo}" />
                    <h:message for="codigo" styleClass="mensagemDetalhada" />
                </h:panelGroup>
                <h:outputText  styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold" value="#{msg_aplic.prt_Profissao_descricao_maiusculo}" />
                <h:panelGroup>
                    <h:inputText id="nomeProfissao" required="true" size="45" maxlength="45" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{ClienteControle.profissaoVO.descricao}" />
                    <h:message for="nomeProfissao" styleClass="mensagemDetalhada" />
                </h:panelGroup>

            </h:panelGrid>
            <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{ClienteControle.mensagem}" />
                    <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}" />
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <h:panelGroup>
                        <a4j:commandLink id="salvarProfissao" reRender="profissao" focus="profissao"
                                           action="#{ClienteControle.gravarProfissao}" oncomplete="Richfaces.hideModalPanel('panelProfissao')"
                                           value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}"
                                           accesskey="2" styleClass="botaoPrimario texto-size-16" />
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelCategoria" styleClass="novaModal" autosized="true" shadowOpacity="true" width="550" height="300">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Cadastro de Categoria"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkPanelCategoria"/>
            <rich:componentControl for="panelCategoria" attachTo="hidelinkPanelCategoria" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCategoria" ajaxSubmit="true">
        <jsp:include page="includes/include_persiste_modulo.jsp"/>
        <h:panelGrid columns="1" width="100%">
            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_codigo}" />
                <h:panelGroup>
                    <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{ClienteControle.categoriaVO.codigo}" />
                    <h:message for="codigo" styleClass="mensagemDetalhada" />
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_nome_maiusculo}" />
                <h:panelGroup>
                    <h:inputText id="nome" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{ClienteControle.categoriaVO.nome}" />
                    <h:message for="nome" styleClass="mensagemDetalhada" />
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_tipoCategoria_maiusculo}" />
                <h:panelGroup layout="block" style="height: 40px;" styleClass="cb-container">
                     <h:selectOneMenu id="tipoCategoria" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 value="#{ClienteControle.categoriaVO.tipoCategoria}">
                        <f:selectItems value="#{CategoriaControle.listaSelectItemTipoCategoriaCategoria}" />
                    </h:selectOneMenu>
                </h:panelGroup>
                <%--Foi solicitado pela GQS Bruna que fosse retirado, pois n�o � utilizado em nenhum local do ZW--%>
                <%--<h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_nrConvitePermitido_maiusculo}" />--%>
                <%--<h:inputText id="nrConvitePermitido" size="10" maxlength="10" onblur="blurinput(this);" onfocus="focusinput(this);"--%>
                             <%--styleClass="form" value="#{ClienteControle.categoriaVO.nrConvitePermitido}" />--%>

            </h:panelGrid>

            <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{ClienteControle.mensagem}" />
                    <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}" />
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink id="salvarCategoria" reRender="categoria" focus="categoria"
                                           action="#{ClienteControle.gravarCategoria}" oncomplete="Richfaces.hideModalPanel('panelCategoria')"
                                           value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}"
                                           accesskey="2" styleClass="botaoPrimario texto-size-16" />
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>

    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelCidade" styleClass="novaModal" autosized="true" shadowOpacity="true" width="550" height="300">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Cadastro de Cidade"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkpanelCidade"/>
            <rich:componentControl for="panelCidade" attachTo="hidelinkpanelCidade" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCidade">
        <jsp:include page="includes/include_persiste_modulo.jsp"/>
        <h:panelGrid columns="1" width="100%">

            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_codigo_maiusculo}" />
                <h:panelGroup>
                    <h:inputText id="cidade_codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{ClienteControle.cidadeVO.codigo}" />
                    <h:message for="cidade_codigo" styleClass="mensagemDetalhada" />
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_nome_maiusculo}" />
                <h:panelGroup>
                    <h:inputText id="cidade_nome" size="40" maxlength="40" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{ClienteControle.cidadeVO.nome}" />
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_pais_maiusculo}" />
                <h:panelGroup layout="block" style="height: 40px;" styleClass="cb-container">
                    <h:selectOneMenu id="cidade_pais" disabled="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" value="#{ClienteControle.cidadeVO.pais.codigo}">
                        <%--a4j:support  event="onchange" action="#{ClienteControle.montarListaSelectItemEstadoCadastroCidade}"/--%>
                        <f:selectItems value="#{ClienteControle.listaSelectItemPais}" />
                    </h:selectOneMenu>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_estado_maiusculo}" />
                <h:panelGroup layout="block" style="height: 40px;" styleClass="cb-container">
                    <h:selectOneMenu id="cidade_estado" disabled="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" value="#{ClienteControle.cidadeVO.estado.codigo}">
                        <f:selectItems value="#{ClienteControle.listaSelectItemEstado}" />
                    </h:selectOneMenu>
                </h:panelGroup>

            </h:panelGrid>
            <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{ClienteControle.mensagem}" />
                    <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}" />
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink id="salvarCidade" reRender="pais,estado,cidade" focus="cidade" action="#{ClienteControle.gravarCidade}"
                                           oncomplete="Richfaces.hideModalPanel('panelCidade')" value="#{msg_bt.btn_gravar}"
                                           title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botaoPrimario texto-size-16" />
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>

    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelCEP" styleClass="novaModal vAlignTop" autosized="true" shadowOpacity="true" width="500" height="450">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_CEP_tituloConsulta}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkCEP"/>
            <rich:componentControl for="panelCEP" attachTo="hidelinkCEP" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConsultarCEP" style="overflow: hidden" ajaxSubmit="true">
        <jsp:include page="includes/include_persiste_modulo.jsp"/>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGrid columns="2" columnClasses="classEsquerda" width="100%">
                  <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_estadoC_maiusculo}" />
                            <h:panelGroup layout="block" styleClass="cb-container" style="height: 40px;">
                                <h:selectOneMenu id="estadoCEP" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.ufSigla}">
                                   <f:selectItems value="#{ClienteControle.listaSelectItemRgUfPessoa}" />
                                </h:selectOneMenu>
                            </h:panelGroup>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_cidadeC_maiusculo}" />
                            <h:inputText id="cidadeCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.cidadeDescricao}" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_bairro_maiusculo}" />
                            <h:inputText id="bairroCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.bairroDescricao}" />
                             <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CEP_logradouro_maiusculo}" />
                            <h:inputText id="logradouroCEP" size="20" styleClass="campos" value="#{ClienteControle.cepControle.cepVO.enderecoLogradouro}" />
            </h:panelGrid>
            <h:panelGrid columns="1">
                       
                        <h:panelGroup>
                            <h:outputText styleClass="textsmall" value="Informe o nome ou parte do seu logradouro, rua ou avenida. N�o Inclua o tipo da via nem o n�mero da sua casa." />
                        </h:panelGroup>
                    </h:panelGrid>
             <h:panelGroup>
                    <a4j:commandLink id="btnConsultarCEP"
                                       reRender="formConsultarCEP"
                                       onclick="if(!validar()){return false;};"
                                       action="#{ClienteControle.cepControle.consultarCEPDetalhe}" styleClass="botaoPrimario texto-size-16"
                                       value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}" />
                    <rich:spacer style='display:block' height="35" />
                </h:panelGroup>
            <h:panelGroup layout="block" >
            <rich:dataTable id="resultadoConsultaCEP" width="100%" styleClass="tabelaSimplesCustom"
                            rendered="#{not empty ClienteControle.cepControle.listaConsultaCep}" value="#{ClienteControle.cepControle.listaConsultaCep}" rows="4" var="cep">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14" value="#{msg_aplic.prt_CEP_titulo}" />
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandLink action="#{ClienteControle.selecionarCepSimplificado}" focus="CEP"
                                         styleClass="texto-font texto-cor-cinza texto-size-14"
                                         reRender="form:clienteBairro, form:pais, form:estado, form:cidade, form:clienteEndereco, form:CEP,formDadosPendentes:CEP, formDadosPendentes:clienteEndereco, formDadosPendentes:clienteBairro, formDadosPendentes:pais, formDadosPendentes:estado, formDadosPendentes:cidade, formDadosPendentes:containerEndereco"
                                         oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.enderecoCep}" />
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_cidadeC}" />
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandLink action="#{ClienteControle.selecionarCepSimplificado}" focus="CEP"
                                         styleClass="texto-font texto-cor-cinza texto-size-14"
                                         reRender="form:clienteBairro, form:pais, form:estado, form:cidade, form:clienteEndereco, form:CEP,formDadosPendentes:CEP, formDadosPendentes:clienteEndereco, formDadosPendentes:clienteBairro, formDadosPendentes:pais, formDadosPendentes:estado, formDadosPendentes:cidade, formDadosPendentes:containerEndereco"
                                         oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.cidadeDescricao}" />
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="float: left;" styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_bairroC}" />
                    </f:facet>
                    <a4j:commandLink action="#{ClienteControle.selecionarCepSimplificado}" focus="CEP"
                                     styleClass="texto-font texto-cor-cinza texto-size-14"
                                     reRender="form:clienteBairro, form:pais, form:estado, form:cidade, form:clienteEndereco, form:CEP, formDadosPendentes:CEP, formDadosPendentes:clienteEndereco, formDadosPendentes:clienteBairro, formDadosPendentes:pais, formDadosPendentes:estado, formDadosPendentes:cidade,formDadosPendentes:containerEndereco"
                                     oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.bairroDescricao}" />
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-bold texto-cor-cinza texto-size-14"  value="#{msg_aplic.prt_CEP_logradouroC}" />
                    </f:facet>
                    <a4j:commandLink action="#{ClienteControle.selecionarCepSimplificado}" focus="CEP"
                                     styleClass="texto-font texto-cor-cinza texto-size-14"
                                     reRender="form:clienteBairro, form:pais, form:estado, form:cidade, form:clienteEndereco, form:CEP, formDadosPendentes:CEP, formDadosPendentes:clienteEndereco, formDadosPendentes:clienteBairro, formDadosPendentes:pais, formDadosPendentes:estado, formDadosPendentes:cidade,formDadosPendentes:containerEndereco"
                                     oncomplete="Richfaces.hideModalPanel('panelCEP')" value="#{cep.enderecoLogradouro}" />
                </rich:column>
            </rich:dataTable>
            <rich:datascroller id="scResultadoCEP" align="center" style="margin-top: 10px" styleClass="scrollPureCustom" renderIfSinglePage="false" for="formConsultarCEP:resultadoConsultaCEP" maxPages="10" />
            </h:panelGroup>
            <h:panelGrid id="mensagemConsultaCEP" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{ClienteControle.cepControle.mensagem}" />
                    <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.cepControle.mensagemDetalhada}" />
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelStatusCadTela2" styleClass="novaModal" autosized="true">
    <h:panelGrid columns="3">
        <h:graphicImage url="imagens/carregando.gif" style="border:none"/>
        <h:outputText styleClass="titulo3" value="Carregando..."/>
    </h:panelGrid>
</rich:modalPanel>

<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 02/02/2016
  Time: 09:43
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="H" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="ui" uri="http://richfaces.org/a4j" %>
<h:panelGroup layout="block" styleClass="areaBusca navbar-right">


    <a4j:commandLink  id="voltarUltmoClienteSessao" action="#{ConsultaClienteControle.voltarParaTelaCliente}"
                     rendered="#{ConsultaClienteControle.apresentarVoltarParaCliente && LoginControle.permissaoAcessoMenuVO.cliente}"
                     styleClass="linkPadrao"
                     title="Voltar para Cliente" style="color: #FCF9F9">
        <i class="fa-icon-undo fa-icon-2x" style="padding-top: 14px; padding-right: 5px; font-size: 22px;"></i>
    </a4j:commandLink>

    <%--<div class="navbar-form navbar-left" role="search">--%>
    <%--<div class="form-group">--%>
    <%--<input type="text" class="form-control" placeholder="Search">--%>
    <%--</div>--%>
    <%--<button type="submit" class="btn btn-default">Submit</button>--%>
    <%--</div>--%>
    <h:panelGroup layout="block" styleClass="conteinerBuscaInteligente">
        <h:panelGroup layout="block" styleClass="relogios">
            <h:panelGroup layout="block" styleClass="panelBusca" style="background-color: white">
                <h:inputText id="campoBusca" styleClass="tituloAzul inputBusca inputbuscaInteligente"
                             autocomplete="off"
                             value="#{ConsultaClienteControle.valorConsultaParametrizada}"
                             onfocus="abrirCampoBusca();"
                             onblur="fecharCampoBusca();"
                             onkeydown="trataTeclasCampoBusca(event);"/>
            </h:panelGroup>
        </h:panelGroup>
        <div id="customTooltip_lupaAtiva" class="clienteMarcado">
            <h:outputLabel id="lupaAtiva" styleClass="fa-icon-search lupaBuscarOpen areaBuscaInteligente ativo" title="Buscar clientes e operações"/>
        </div>
        <c:if test="${not LoginControle.moduloAtualFin}">
        <h:outputLabel id="botaoBuscar"
                       styleClass="fa-icon-search lupaBuscar areaBuscaInteligenteAtivo desativado">
            <a4j:support action="#{ConsultaClienteControle.acao}"
                         actionListener="#{ConsultaClienteControle.consultarPaginadoListenerReset}"
                         event="onclick">
                <f:attribute name="paginaInicial" value="paginaInicial"/>
                <f:attribute name="tipoConsulta" value="parametrizado"/>
                <c:if test="${modulo eq '4bf2add2267962ea87f029fef8f75a2f'}">
                    <f:attribute name="modulo" value="4bf2add2267962ea87f029fef8f75a2f"/>
                </c:if>

            </a4j:support>
        </h:outputLabel>
        </c:if>
        <c:if test="${LoginControle.moduloAtualFin}">
            <h:outputLabel id="botaoBuscar"
                           styleClass="fa-icon-search lupaBuscar areaBuscaInteligenteAtivo desativado">

            </h:outputLabel>
        </c:if>
        <jsp:include page="/include_pesquizarFuncionalidade_flat.jsp" flush="true"/>
        <script>
            var placeholderPadrao = "Buscar clientes e operações";
            jQuery(window).scroll(function () {
                topAtual = jQuery(window).scrollTop();
                if (topAtual > 82) {
                    jQuery('.menuOpcoes').addClass('barraFixa');
                } else if (topAtual <= 82) {
                    jQuery('.menuOpcoes').removeClass('barraFixa');
                }

            });
            function desativarLupa() {
                jQuery('.areaBuscaInteligente').removeClass('ativo');
                jQuery('.areaBuscaInteligente').addClass('desativado');
                jQuery('.areaBuscaInteligenteAtivo').removeClass('desativado');
                jQuery('.areaBuscaInteligenteAtivo').addClass('ativo');
                jQuery("#customTooltip_lupaAtiva").addClass("desativado");
            }
            function ativarLupa() {
                jQuery('.areaBuscaInteligenteAtivo').removeClass('ativo');
                jQuery('.areaBuscaInteligenteAtivo').addClass('desativado');
                jQuery('.areaBuscaInteligente').removeClass('desativado');
                jQuery('.areaBuscaInteligente').addClass('ativo');
                jQuery("#customTooltip_lupaAtiva").removeClass("desativado");
            }
            function fecharCampoBusca(force) {
                if(jQuery('.inputbuscaInteligente').val() === '' || force===true){
                    ativarLupa();
                    jQuery('.panelBusca').removeClass('expandirPanel');
                    jQuery('.inputbuscaInteligente').removeClass('expandirInput');
                    jQuery('.conteinerBuscaInteligente').removeClass('conteinerBuscaInteligenteAtivo');
                    limparCampoBusca();
                }
            }
            function abrirCampoBusca(){
                var campoBusca = document.getElementById("form:campoBusca");
                if(campoBusca !== null){
                    var placeholderAtual = campoBusca.getAttribute("placeholder");
                    if(placeholderAtual === null || placeholderAtual === ""){
                        campoBusca.setAttribute("placeholder", placeholderPadrao);
                    }
                }
            }
            function limparCampoBusca(){
                jQuery('.inputbuscaInteligente').val('');

                var campoBusca = document.getElementById("form:campoBusca");
                if(campoBusca !== null && campoBusca.getAttribute("placeholder") !== null){
                    campoBusca.setAttribute("placeholder", "");
                }
            }
            function trataTeclasCampoBusca(event){
                var keyCode = event.keyCode;
                if(keyCode === 27){ //tecla esc
                    fecharCampoBusca(true);
                }
            }
            jQuery('.navbar-toggle').click(function(){
                jQuery(this).parent().parent().children('.navbar-collapse').slideToggle();
            });
            
            carregarTooltipster();
        </script>
        <rich:jQuery selector=".areaBuscaInteligente"
                     query="click(function(){jQuery('.panelBusca').addClass('expandirPanel'); jQuery('.inputbuscaInteligente').addClass('expandirInput');jQuery('.inputbuscaInteligente').focus();desativarLupa();jQuery('.conteinerBuscaInteligente').addClass('conteinerBuscaInteligenteAtivo');})"/>

    </h:panelGroup>
    <jsp:include page="/includes/include_top_datahora_flat.jsp" flush="true"/>
</h:panelGroup>

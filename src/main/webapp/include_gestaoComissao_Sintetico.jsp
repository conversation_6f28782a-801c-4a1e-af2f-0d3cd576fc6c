<%--
    Document   : include_TreeViewDF
    Created on : 17/08/2012, 16:49:37
    Author     : <PERSON><PERSON>
--%>

<table width="100%">
    <tr>
        <td width="100%" style="text-align: center;">
            &nbsp;
            <a id="refExpandir" style="cursor: pointer"
               class="expandir"> Expandir Tudo </a>
            &nbsp;
            <a  class="expandirUm" style="cursor: pointer">
                Expandir </a>
            &nbsp;
            <a  class="retrairUm" style="cursor: pointer">
                Retrair </a>
            &nbsp;
            <a  class="retrair" style="cursor: pointer">
                Retrair Tudo </a>

        </td>
    </tr>
</table>
<br/>
<h:panelGrid columns="2" width="100%" id="panelTree">
    <h:panelGroup>
        <rich:spacer width="10px"/>
    </h:panelGroup>
    <h:panelGroup id="panelMostrarRelatorio">
        <table class="comissao"
               id="dnd-comissao"
               width="100%"
               cellspacing="0">

            <tbody>
            <tr class="tituloBold">
                <td></td>
                <td bgcolor="#DCEFF3" style="padding: 4px; border: 0;" align="center">Valor Pago</td>
                <td bgcolor="#DCEFF3" align="center">Vezes Semana</td>
                <td bgcolor="#DCEFF3" align="center">Valor Comiss�o</td>
                <td bgcolor="#DCEFF3" align="center">Qtd. Alunos</td>
            </tr>

            <c:forEach var="comissao" varStatus="indice" items="${GestaoComissaoControle.listaTree}">
                <c:choose>
                    <c:when test="${fn:indexOf(comissao.codigoAgrupador, '.') > 0}">
                        <c:set var="noPai"
                               value="${fn:substring(comissao.codigoAgrupador,0, fn:length(comissao.codigoAgrupador) -4)}"
                               scope="request"/>

                        <tr bgcolor="#FFFFFF" id="${comissao.codigoNode}"
                            class="child-of-${fn:replace(noPai,'.', '') + 0 }">

                            <!-- Dados da primeira coluna -->
                            <c:choose>
                                <c:when test="${comissao.apresentarLink}">
                                    <!-- Nome do aluno -->
                                    <td class="${comissao.style}">
                                        <a href="#" onclick="preencherValorChamarBotao('form:btClienteVis',
                                                'form:clienteHidden',${comissao.cliente})">
                                            <span class="${comissao.style}">${comissao.nome} </span> </a>
                                        <c:if test="${comissao.percComissao_Apresentar != '0.0%'}">
                                            - <c:out value="${comissao.percComissao_Apresentar}"/>
                                        </c:if>
                                    </td>
                                </c:when>
                                <c:otherwise>
                                    <!-- Turma, Subturma e horario -->
                                    <td class="${comissao.style}"><c:out value="${comissao.nome}"/>
                                        <c:if test="${comissao.percComissao_Apresentar != '0.0%'}">
                                            - <c:out value="${comissao.percComissao_Apresentar}"/>
                                        </c:if>
                                    </td>
                                </c:otherwise>
                            </c:choose>

                            <!-- Dados da segunda coluna (Valor Pago) -->
                            <td class="${comissao.style}" align="center">
                                <c:out value="${comissao.valorPago_Apresentar}"/>
                            </td>

                            <!-- Dados da terceira coluna (Vezes Semana) -->
                            <td class="${comissao.style}" align="center">
                                <c:out value="${comissao.dias}"/>
                            </td>

                            <!-- Dados da quarta coluna (Valor Comissao) -->
                            <td class="${comissao.style}" align="center">
                                <c:out value="${comissao.valorComissao_Apresentar}"/>
                            </td>

                            <!-- Dados da quinta coluna (Quantidade de Alunos) -->
                            <td class="${comissao.style}" align="center">
                                <c:out value="${comissao.qtdAluno}"/>
                            </td>
                        </tr>
                    </c:when>
                    <c:otherwise>
                        <!-- Primeira linha do Professor -->
                        <tr bgcolor="#FFFFFF" id="${comissao.codigoNode}" class="${comissao.style}">
                            <td class="borda_azul">
                                <c:out value="${comissao.nome}"/>
                                <c:if test="${comissao.percComissao_Apresentar != '0.0%'}">
                                    - <c:out value="${comissao.percComissao_Apresentar}"/>
                                </c:if>
                            </td>
                            <td class="borda_azul" align="center">
                                <c:out value="${comissao.valorPago_Apresentar}"/>
                            </td>
                            <td class="borda_azul" align="center">
                                &nbsp;
                            </td>
                            <td class="borda_azul" align="center">
                                <c:out value="${comissao.valorComissao_Apresentar}"/>
                            </td>
                            <td class="borda_azul" align="center">
                                <c:out value="${comissao.qtdAluno}"/>
                            </td>
                        </tr>
                    </c:otherwise>
                </c:choose>
            </c:forEach>
            </tbody>
        </table>
        <br/>
    </h:panelGroup>
</h:panelGrid>                
<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_LogAjusteGeral_tituloForm}</title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_LogAjusteGeral_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Configurações:Log_Ajuste_Geral"/>



    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <jsp:include page="topoReduzido_material.jsp"/>
        </h:panelGroup>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <rich:dataTable id="resultadoConsultaLog" width="100%" headerClass="consulta"
                                    rowClasses="linhaImpar, linhaPar"
                                    columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaEsquerda"
                                    value="#{ConfiguracaoSistemaControle.logAjusteGeralVOList}" rows="15" var="log">

                        <rich:column sortBy="#{log.codigo}">
                            <f:facet name="header">
                                <h:outputText value="Código"/>
                            </f:facet>
                            <h:outputText value="#{log.codigo}"/>
                        </rich:column>

                        <rich:column sortBy="#{log.codigo}">
                            <f:facet name="header">
                                <h:outputText value="Operação"/>
                            </f:facet>
                            <h:outputText value="#{log.processoAjusteGeralEnum.descricao}"/>
                        </rich:column>

                        <rich:column sortBy="#{log.datalancamento}">
                            <f:facet name="header">
                                <h:outputText value="Data Lançamento"/>
                            </f:facet>
                            <h:outputText value="#{log.dataLancamento_Apresentar}"/>
                        </rich:column>

                        <rich:column sortBy="#{log.usuario}">
                            <f:facet name="header">
                                <h:outputText value="Usuário"/>
                            </f:facet>
                            <h:outputText value="#{log.usuario}"/>
                        </rich:column>

                        <rich:column sortBy="#{log.usuarioOAMD}">
                            <f:facet name="header">
                                <h:outputText value="Usuário OAMD"/>
                            </f:facet>
                            <h:outputText value="#{log.usuarioOAMD}"/>
                        </rich:column>

                        <rich:column sortBy="#{log.parametros}">
                            <f:facet name="header">
                                <h:outputText value="Parâmetros"/>
                            </f:facet>
                            <h:outputText escape="false" value="#{log.parametros}"/>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller align="center" for="form:resultadoConsultaLog" maxPages="10"
                                       id="scResultadoLog"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
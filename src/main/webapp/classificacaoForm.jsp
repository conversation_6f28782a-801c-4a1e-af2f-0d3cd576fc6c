<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<script>
    jQuery.noConflict();
</script>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Classificacao_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Classificacao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Classificacoes"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;"/>
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Classificacao_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                      onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{ClassificacaoControle.classificacaoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Classificacao_nome}" />
                    <h:panelGroup>
                        <h:inputText  id="nome"  size="45" maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                      styleClass="form" value="#{ClassificacaoControle.classificacaoVO.nome}" />
                        <h:message for="nome" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Classificacao_enviarSmsAutomatico}" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox  id="enviarSMSAutomatico"  onblur="blurinput(this);"  onfocus="focusinput(this);"
                                      styleClass="form" value="#{ClassificacaoControle.classificacaoVO.enviarSMSAutomatico}" />
                        <h:message for="enviarSMSAutomatico" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <a4j:commandButton  rendered="#{ClassificacaoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <a4j:commandButton rendered="#{ConfiguracaoSiClassificacaoControlestemaControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgClassificacao" styleClass="mensagem"  value="#{ClassificacaoControle.mensagem}"/>
                            <h:outputText id="msgClassificacaoDet" styleClass="mensagemDetalhada" value="#{ClassificacaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>'
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{ClassificacaoControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="salvar" action="#{ClassificacaoControle.gravar}" value="#{msg_bt.btn_gravar}"  alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <h:outputText value="    "/>

                        <h:panelGroup id="grupoMensagem">
                            <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                               oncomplete="#{ClassificacaoControle.msgAlert}" action="#{ClassificacaoControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                               alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec" style="border: 0px" />
                        </h:panelGroup>

                            <h:outputText value="    "/>
                            <a4j:commandButton id="consultar" immediate="true" action="#{ClassificacaoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="15px"/>
                            <a4j:commandLink  action="#{ClassificacaoControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                                oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                style="display: inline-block; padding: 8px 15px; margin-left: -6px;" title="Visualizar Log"
                                                styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>
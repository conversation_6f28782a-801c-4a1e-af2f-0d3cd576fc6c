<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script src="script/jquery.maskedinput-1.7.6.js" type="text/javascript"></script>
<style>
    .dataTables_filter {
        display: none;
    }
</style>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    
    <title>${msg_aplic.prt_Passivo_tituloForm}</title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Passivo_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-receptivos-no-modulo-crm/"/>

    <%-- INICIO HEADER --%>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_crm.jsp"/>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">

                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1 margin-v-10">
                        <h:panelGroup layout="block" style="width: 100%;">
                            <h:panelGroup layout="block" style="width: 100%; float: left;">
                                <h:panelGroup layout="block">
                                    <h:outputText value="Período de: "/>
                                    <rich:calendar id="dtInicio"
                                                   inputSize="7"
                                                   value="#{PassivoControle.dataInicioConsulta}"
                                                   showWeekDaysBar="false"
                                                   inputClass="form"
                                                   showWeeksBar="false"
                                                   oninputfocus="focusinput(this);"
                                                   oninputblur="blurinput(this);"
                                                   enableManualInput="false"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy">
                                        <%--<a4j:support event="onchanged"
                                                     oncomplete="recarregarTabela()"/>--%>
                                    </rich:calendar>
                                </h:panelGroup>

                                <h:panelGroup layout="block">
                                    <h:outputText value=" até "/>
                                    <rich:calendar id="dtFim"
                                                   inputSize="7"
                                                   value="#{PassivoControle.dataFimConsulta}"
                                                   showWeekDaysBar="false"
                                                   inputClass="form"
                                                   showWeeksBar="false"
                                                   oninputfocus="focusinput(this);"
                                                   oninputblur="blurinput(this);"
                                                   enableManualInput="false"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy">
                                        <%--<a4j:support event="onchanged"
                                                     oncomplete="recarregarTabela()"/>--%>
                                    </rich:calendar>
                                </h:panelGroup>
                                <rich:spacer width="10"/>
                                <h:panelGroup>
                                    <a4j:commandButton id="limparPeriodoEmissao"
                                                       onclick="document.getElementById('form:dtInicioInputDate').value = '';
                                                       document.getElementById('form:dtFimInputDate').value='';"

                                                       image="/images/limpar.gif" title="Limpar Data de Emissão"
                                                       status="false"/>
                                </h:panelGroup>
                                <rich:spacer width="8"/>
                                <h:panelGroup style="display: inline-block">
                                    <h:selectBooleanCheckbox id="apresentarConvertidos"
                                                             styleClass="form tooltipster"
                                                             style="padding-left: 5px"
                                                             value="#{PassivoControle.apresentarConvertidos}"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  style="margin-left: 5px; line-height: 1.5vh;"
                                                  value="Apresentar Convertidos"/>

                                </h:panelGroup>
                                <rich:spacer width="8"/>
                                <a4j:commandLink styleClass="botoes nvoBt " style="size: 12px"
                                                 value="Atualizar" oncomplete="recarregarTabela()">

                                    <i class="fa-icon-refresh"></i>
                                </a4j:commandLink>
                                <rich:spacer width="8"/>
                                <h:panelGroup>
                                    <h:inputText id="buscaInput"
                                                 value="#{PassivoControle.filtroConsulta}">

             <%--                           <a4j:queue name="queueFiltro" ignoreDupResponses="true" requestDelay="800"
                                                   timeout="60000"/>

                                        <a4j:support status="statusInComponent" eventsQueue="queueFiltro"
                                                     event="onkeyup"
                                                     oncomplete="recarregarTabela()"/>--%>
                                    </h:inputText>

                                    <a4j:commandLink id="btnLog"
                                                     styleClass="exportadores margin-h-8 linkPadrao pull-right"
                                                     action="#{PassivoControle.realizarConsultaLogObjetoGeral}"
                                                     oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                        <h:outputText title="Visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="btnExcel"
                                                     styleClass="exportadores margin-h-10 linkPadrao pull-right"
                                                     actionListener="#{PassivoControle.exportar}"
                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Receptivo', 800,200);#{ExportadorListaControle.msgAlert}"
                                                     accesskey="3">
                                        <f:attribute name="tipo" value="xls"/>
                                        <f:attribute name="atributos"
                                                     value="codigo=Código,nome=Nome,telefoneResidencial=Telefone Residencial,telefoneCelular=Telefone Celular,telefoneTrabalho=Telefone Comercial,colaboradorResp=Responsável Cadastro,dia_ApresentarRel=Dia,empresa=Empresa,descricaoEvento=Evento,observacao=Comentário,clienteVO.matricula=Matrícula (Convertido)"/>
                                        <f:attribute name="prefixo" value="Receptivo"/>
                                        <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 excel"/>
                                    </a4j:commandLink>

                                    <a4j:commandLink id="btnPDF"
                                                     styleClass="exportadores margin-h-8 linkPadrao pull-right"
                                                     actionListener="#{PassivoControle.exportar}"
                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Receptivo', 800,200);#{ExportadorListaControle.msgAlert}"
                                                     accesskey="4">
                                        <f:attribute name="tipo" value="pdf"/>
                                        <f:attribute name="atributos"
                                                     value="codigo=Código,nome=Nome,telefoneResidencial=Telefone Residencial,telefoneCelular=Telefone Celular,telefoneTrabalho=Telefone Comercial,colaboradorResp=Responsável Cadastro,dia_ApresentarRel=Dia,empresa=Empresa,descricaoEvento=Evento,observacao=Comentário,clienteVO.matricula=Matrícula (Convertido)"/>
                                        <f:attribute name="prefixo" value="Receptivo"/>
                                        <f:attribute name="titulo" value="Receptivo"/>
                                        <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                    </a4j:commandLink>
                                </h:panelGroup>


                                <%--<div  style="float: right;width: 46%;text-align: right">--%>

                                        <%--<rich:spacer width="10"/>--%>
                                        <%--<a4j:commandLink id="btnNovo"--%>
                                        <%--styleClass="pure-button pure-button-primary pure-button-small"--%>
                                        <%--action="#{PassivoControle.novo}"--%>
                                        <%--accesskey="1">--%>
                                        <%--<i class="fa-icon-plus"></i> &nbsp ${msg_bt.btn_novo}--%>
                                        <%--</a4j:commandLink>--%>

                                <%--</div>--%>
                            </h:panelGroup>
                            <%--<h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">--%>
                                <%----%>
                            <%--</h:panelGroup>--%>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblPassivo" class="tabelaPassivo pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${"Código"}</th>
                    <th>${"Nome"}</th>
                    <th>${"Telefone Residencial"}</th>
                    <th>${"Telefone Celular"}</th>
                    <th>${"Telefone Comercial"}</th>
                    <th>${"Responsável Cadastro"}</th>
                    <th>${"Dia"}</th>
                    <th>${"Empresa"}</th>
                    <th>${"Evento"}</th>
                    <th>${"Comentário"}</th>
                    <th>${"Matrícula (Convertido)"}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{PassivoControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{PassivoControle.sucesso}" value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{PassivoControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty PassivoControle.mensagem}"
                              value=" #{PassivoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty PassivoControle.mensagemDetalhada}"
                              value=" #{PassivoControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

    </h:panelGroup>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>
    <script src="beta/js/dt-server.js" type="text/javascript"></script>
    <script>
        function recarregarTabela() {
            var dtInicio= document.getElementById("form:dtInicioInputDate").value;
            var dtFim = document.getElementById("form:dtFimInputDate").value;
            var buscar = document.getElementById("form:buscaInput").value;
            var apresentarConvertidos = document.getElementById("form:apresentarConvertidos").checked;

            var configs = tabelaAtual.dataTableSettings[0];
            var sEcho = configs.iDraw;
            var iDisplayStart = configs._iDisplayStart;
            var iDisplayLength = configs._iDisplayLength;
            var sSearch = jQuery(".filtroDT").val().toLowerCase();
            var iSortCol_0 = configs.aaSorting[0][0];
            var sSortDir_0 = configs.aaSorting[0][1];

            var data = {"dtInicio": dtInicio, "dtFim": dtFim,"sEcho": sEcho, "iDisplayStart": iDisplayStart,
                "iDisplayLength": iDisplayLength, "sSearch": sSearch, "buscar": buscar,
                "iSortCol_0": iSortCol_0, "sSortDir_0": sSortDir_0, "apresentarConvertidos": apresentarConvertidos};

            tabelaAtual.dataTable().fnDestroy(0);
            iniTblServer("tabelaPassivo", "${contexto}/prest/crm/passivo", data);
        }
        jQuery(window).on("load", function () {
            var dtInicio= document.getElementById("form:dtInicioInputDate").value;
            var dtFim = document.getElementById("form:dtFimInputDate").value;
            var buscar = document.getElementById("form:buscaInput").value;
            var apresentarConvertidos = document.getElementById("form:apresentarConvertidos").checked;

            var data = {"dtInicio": dtInicio, "dtFim": dtFim, "buscar": buscar, "apresentarConvertidos": apresentarConvertidos };

            iniTblServer("tabelaPassivo", "${contexto}/prest/crm/passivo", data);
        });
    </script>
</f:view>

<script>
    document.getElementById("form:buscaInput").setAttribute("placeholder", "Pesquisar");
</script>

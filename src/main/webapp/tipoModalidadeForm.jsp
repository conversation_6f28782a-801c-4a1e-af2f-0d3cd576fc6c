<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link rel="stylesheet" href="./css/croppie/croppie.css" type="text/css" />
<script type="text/javascript" src="./script/croppie/croppie.min.js"></script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_TipoModalidade_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_TipoModalidade_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-tipo-de-modalidade/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{TipoModalidadeControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
                           style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel width="100%" >
                    <rich:tab id="dadosTipoModalidade" label="Dados Tipo Modalidade">
                        <h:panelGrid id="panelDadosTipoModalidade" columns="2"  rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText   value="#{msg_aplic.prt_TipoModalidade_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true"
                                              onblur="blurinput(this);"  onfocus="focusinput(this);"
                                              styleClass="camposSomenteLeitura"
                                              value="#{TipoModalidadeControle.tipoModalidadeVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_TipoModalidade_nome}" />
                            <h:panelGroup>
                                <h:inputText  id="nome"  size="50" maxlength="50" onblur="blurinput(this);"
                                              onfocus="focusinput(this);" styleClass="form"
                                              value="#{TipoModalidadeControle.tipoModalidadeVO.nome}" />
                                <h:message for="nome" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText value="#{msg_aplic.prt_TipoModalidade_Identificador}" />
                            <h:inputText  id="identificador"  size="5" maxlength="10"
                                          onkeypress="return mascara(this.form, 'form:identificador', '99999', event);"
                                          onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                          value="#{TipoModalidadeControle.tipoModalidadeVO.identificador}" />

                        </h:panelGrid>
                        <script>
                            document.getElementById("form:nome").focus();
                        </script>
                    </rich:tab>
                </rich:tabPanel>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                                <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{TipoModalidadeControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{TipoModalidadeControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgTipoModalidade" styleClass="mensagem"  value="#{TipoModalidadeControle.mensagem}"/>
                            <h:outputText id="msgTipoModalidadeDet" styleClass="mensagemDetalhada" value="#{TipoModalidadeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{TipoModalidadeControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                            <h:commandButton id="salvar" action="#{TipoModalidadeControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                                <h:outputText value="    "/>

                            <h:commandButton id="excluir" onclick="return confirm('Confirma exclusão dos dados?');" action="#{TipoModalidadeControle.excluir}" value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>

                                <h:outputText value="    "/>

                            <h:commandButton id="consultar" immediate="true" action="#{TipoModalidadeControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                            <a4j:commandLink
                                    action="#{TipoModalidadeControle.realizarConsultaLogObjetoSelecionado}"
                                    reRender="form"
                                    oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                    title="Visualizar Log"
                                    style="display: inline-block; padding: 8px 15px;"
                                    styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>
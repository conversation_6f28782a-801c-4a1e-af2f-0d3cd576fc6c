<table border="0" cellspacing="0" cellpadding="0" class="tudo dicaMenuTopo">
    <tr>
        <td align="left" valign="top">
            <div style="padding:0px 6px; background-image: url(beta/imagens/bg-topo-menu-15.png); border-top: none; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px; overflow: hidden;">
                <h:panelGroup>
                    <ul class="abatop" style="margin-top: -2px;">
                        <li>
                            <%@include file="includes/include_menu_modulos.jsp" %>
                        </li>

                        <li><img src="images/aba_sep.gif"></li>

                        <li>
                            <%@include file="include_top_socialMailing.jsp" %>
                        </li>

                        <%@include file="includes/include_modal_trocarEmpresa.jsp" %>

                        <li>
                            <rich:dropDownMenu>
                                <f:facet name="label">
                                    <h:panelGroup>
                                        <i class="fa-icon-question-sign fa-icon-2x"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <rich:menuItem submitMode="none"                                               
                                               icon="/faces/beta/imagens/logoUCP_57x57.png">
                                    <h:outputLink style="color: #555" target="_blank" value='#{SuperControle.contextPath}/redir?up'>
                                        Plataforma UCP
                                    </h:outputLink>
                                </rich:menuItem>


                                <rich:menuItem submitMode="none"
                                               icon="/faces/beta/imagens/mini-icons-wiki.png">
                                    <h:outputLink id="linkwiki"
                                                  style="color:#555;" target="_blank"
                                                  value='#{SuperControle.urlWikiRaiz}/P%C3%A1gina_principal'>
                                        WikiPacto
                                    </h:outputLink>
                                </rich:menuItem>


                                <rich:menuItem submitMode="none"
                                               icon="/faces/beta/imagens/mini-icons-suport.png">
                                    <h:outputLink id="linksolicitacao"
                                                  style="color:#555;" target="_blank"
                                                  value="#{SuporteControle.urlSolicitacao}">
                                        Suporte
                                        <a4j:support event="onclick" action="#{SuporteControle.prepareUserService}"
                                                     oncomplete="#{SuporteControle.loginService}"/>
                                    </h:outputLink>
                                </rich:menuItem>

                                <c:if test="${LoginControle.permissaoAcessoMenuVO.boletosSistema}">
                                    <rich:menuItem submitMode="none" icon="/imagens/dinheiroContaCorrente.png">
                                        <a4j:commandLink id="linkFinanceiro" style="color:#555;"
                                                         action="financeiroPacto">
                                            Boleto Pacto
                                        </a4j:commandLink>
                                    </rich:menuItem>
                                </c:if>


                            </rich:dropDownMenu>
                        </li>

                        <li>
                            <rich:dropDownMenu itemClass="itemMnuTpo">
                                <f:facet name="label">
                                    <h:panelGroup>
                                        <i class="fa-icon-cog fa-icon-2x"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <rich:menuItem id="linkConfiguracoes"
                                               submitMode="ajax"
                                               style="color: #555;"
                                               icon="/faces/beta/imagens/mini-icons-config.png"
                                               action="#{ConfiguracaoSistemaControle.novo}"
                                               oncomplete="abrirPopup('configuracaoSistemaForm.jsp', 'ConfiguracaoSistema', 1024, 768);"
                                               value="Configura��es">
                                </rich:menuItem>

                                <rich:menuItem id="linkGoogleCalendar"
                                               submitMode="ajax"
                                               rendered="#{!fn:contains(uriPagina, 'pages')}"
                                               style="color: #555"
                                               icon="/faces/beta/imagens/mini-icons-calend.png"
                                               action="#{ConfiguracaoSistemaControle.novo}"
                                               oncomplete="window.open('#{uriPagina}googlecalendar.jsp', 'GoogleCalendar', 820, 620);"
                                               value="Google Calendar">
                                </rich:menuItem>

                                <rich:menuItem id="linkVelocimetro"
                                               submitMode="ajax"
                                               rendered="#{!fn:contains(uriPagina, 'pages')}"
                                               style="color: #555"
                                               icon="/faces/beta/imagens/mini-icons-velo.png"
                                               oncomplete="abrirPopup('#{urlPath}velocimetro.jsp', 'Velocimetro', 500, 300);"
                                               value="Veloc�metro">
                                </rich:menuItem>
                                <rich:menuItem id="linkDocVelocidade"
                                               submitMode="ajax"
                                               rendered="#{!fn:contains(uriPagina, 'pages')}"
                                               style="color: #555"
                                               icon="/faces/beta/imagens/mini-icons-velo.png"
                                               oncomplete="abrirPopup('https://pactosolucoes.com.br/ajuda/conhecimento/doc-velocidade/', 'Doc. Velocimetro', #{SuperControle.widthScreenClient}, #{SuperControle.heightScreenClient});"
                                               value="Doc. Velocidade">
                                </rich:menuItem>

                                <rich:menuItem id="linkAlterarSenha"
                                               submitMode="none"
                                               rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}"
                                               style="color:#555; valign:middle; cursor:pointer;"
                                               styleClass="text2"
                                               icon="/faces/beta/imagens/mini-icons-key.png"
                                               onclick="abrirPopup('#{root}/faces/alterarSenhaClienteForm.jsp', 'AlterarSenha', 410, 350);"
                                               value="Alterar Senha">
                                </rich:menuItem>

                                <rich:menuSeparator id="menuSeparator11"/>
                                <rich:menuItem id="btnLogout" submitMode="none" value="Sair"
                                               icon="/faces/beta/imagens/mini-icons-sair.png"
                                               onclick="document.location.href = '#{LogoutControle.redirectLogout}'"/>
                            </rich:dropDownMenu>
                        </li>

                    </ul>
                </h:panelGroup>
            </div>
        </td>
    </tr>
    <jsp:include page="/includes/include_top_datahora.jsp" flush="true"/>
</table>
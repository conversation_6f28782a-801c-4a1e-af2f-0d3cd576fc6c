<%@ page import="controle.arquitetura.MenuControle" %>
<%@ page import="controle.basico.FuncionalidadeControle" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<%--

Componente de menu lateral

@param String menu      Nomes dos menus definidos no atributo "rupoFuncionalidadeSistemaEnum.menu"

Exemplo de uso:

    1. Adicione a classe css "container-conteudo-central" na div de conte?do central da p?gina.
       O menu ter? a mesma altura da div que cont?m essa classe.

    2. Inclua o jsp do menu na p?gina:

        <jsp:include page="include_box_menulateral.jsp">
            <jsp:param name="menu" value="BI,INICIO" />
        </jsp:include>

--%>

<c:set var="grupoFuncionalidadeMenu" value="${param.menu}" scope="request" />
<c:set var="incluirMenuCaixaAberto" value="#{param.incluirMenuCaixaAberto}" scope="page" />

<c:if test="${SuperControle.menuZwUi}">
    <jsp:include page="include_box_menulateral_zw_ui.jsp" flush="true"/>
</c:if>

<a4j:jsFunction name="reRenderMenuLateral" id="xjsfnction" reRender="menuLateral" ajaxSingle="true" oncomplete="alterarAlturaMenuLateral();registrarEventosPosicaoMenuDropDown();" />
<a4j:commandLink reRender="container-mask-menus" id="atualizaMenuLateral" style="display: none"/>
<c:if test="${!SuperControle.menuZwUi}">
<h:panelGroup id="menuLateral" layout="block" styleClass="menuLateral tudo" style="width: 220px;">

    <jsp:include page="includes/include_menulateral_acessorapido.jsp" flush="true" />

    <a4j:repeat value="#{MenuControle.gruposExibirAux}" var="grupo">

        <h:panelGroup layout="block" styleClass="grupoMenuLateral" rendered="#{grupo.renderizar and grupo.descricao != null}">
            <h:panelGroup layout="block" styleClass="grupoMenuTitulo" >
                <h:outputText styleClass="#{grupo.grupoFuncionalidadeSistemaEnum.iconeClass} icone-grupo-menu" rendered="#{grupo.renderizar}"/>
                <h:outputText value="#{grupo.grupoFuncionalidadeSistemaEnum.descricao}" rendered="#{grupo.renderizar}" />
            </h:panelGroup>

            <a4j:repeat value="#{grupo.funcionalidades}" var="funcionalidade">

                <h:panelGroup rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                              layout="block"
                              styleClass="grupoMenuItem grupoMenuItemContainer">

                    <jsp:include page="include_box_menulateral_menuitem.jsp" />

                </h:panelGroup>

                <h:panelGroup rendered="#{funcionalidade.tipo == 'menudropdown' and funcionalidade.renderizar}"
                              layout="block"
                              styleClass="grupoMenuItem dropDownSimples menu-lateral menu-flex">

                    <h:panelGroup layout="block"
                                  style="display: flex;justify-content: center; align-items: center;">
                        <jsp:include page="include_box_menulateral_menuitem.jsp" />
                        <i class="titulo3 fa-icon-caret-right linkFuncionalidadeNovo" style="font-family: FontAwesome;margin-left: 1px;"></i>
                        <jsp:include page="include_box_menulateral_menuitem_dropdown.jsp" />

                    </h:panelGroup>

                </h:panelGroup>

            </a4j:repeat>

        </h:panelGroup>

    </a4j:repeat>

    <c:if test="${incluirMenuCaixaAberto}" >
        <jsp:include page="pages/finan/includes/include_box_financeiro.jsp" />
    </c:if>

</h:panelGroup>
</c:if>
<script>
    function registrarEventosPosicaoMenuDropDown(){
        jQuery('.dropDownSimples').hover(function(){
            var position = jQuery(this).offset();
            var positionScroll = jQuery(window).scrollTop();
            var menuWidth = jQuery(this).css('width');
            var dropDownMenu = jQuery(this).find('.dropdown-content');

            dropDownMenu.css("top", position.top - positionScroll);
            dropDownMenu.css("left", position.left + menuWidth);
            dropDownMenu.css("position", "fixed");
            dropDownMenu.css("visibility", "visible");

        }, function(){
            var dropDownMenu = jQuery(this).find('.dropdown-content');
            setTimeout(function(){
                dropDownMenu.css("visibility", "hidden");
            }, 100);
        });
    }

    function alterarAlturaMenuLateral() {
        // Bug fix: altura do menu ? calculado com javascript
        // para resolver problemas de overflow de menu dropdown em compatibilidade browsers
        if(document.getElementsByClassName('container-conteudo-central')){
            var elementContainerBox = document.getElementsByClassName('container-conteudo-central');
            elementContainerBox = (elementContainerBox) ? elementContainerBox[0] : null;
            if(elementContainerBox != null){
                var height = obterAlturaTotal('container-conteudo-central');

                if(height){
                    fixarAlturaMenuLateral(height);
                }
            }
        }
    }


    function obterAlturaTotal(classeElemento) {
        var elemento = document.getElementsByClassName(classeElemento);
        elemento = (elemento) ? elemento[0] : null;
        var elmHeight, elmMargin, elm = elemento;

        elmHeight = parseInt(document.defaultView.getComputedStyle(elm, '').getPropertyValue('height').replace("px", ""));
        elmMargin = parseInt(document.defaultView.getComputedStyle(elm, '').getPropertyValue('margin-top')) + parseInt(document.defaultView.getComputedStyle(elm, '').getPropertyValue('margin-bottom'));

        return (elmHeight+elmMargin);
    }

    function fixarAlturaMenuLateral(height){
        var elementoMenuLateral = document.getElementById('form:menuLateral');
        elementoMenuLateral = elementoMenuLateral ? elementoMenuLateral : document.getElementById('menuLateral');
        elementoMenuLateral = elementoMenuLateral ? elementoMenuLateral : document.getElementById('formMenu:menuLateral');
        if(elementoMenuLateral){
            elementoMenuLateral.style.height = height;
            elementoMenuLateral.style.overflowY = "auto";
            elementoMenuLateral.style.overflowX = "hidden";
        }

    }
</script>

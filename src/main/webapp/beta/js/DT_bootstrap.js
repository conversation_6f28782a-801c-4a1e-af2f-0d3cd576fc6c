/* Set the defaults for DataTables initialisation */
!function ($) {
    if($.fn.dataTable == undefined){
        return;
    }
    //Seta Timeout
    $.ajaxSetup({
        timeout: 900000
    });

     $.extend(true, $.fn.dataTable.defaults, {
            "sDom": "<'pure-g-r pure-u-11-12 margin-0-auto'<'pure-u-1-3 text-left'<'dataTables_filter'>><'pure-u-1-3 text-center caixaCarregandoCadastro'r>>t<'pure-g-r pure-u-11-12 margin-0-auto'<'pure-u-1-3 text-left'i><'pure-u-1-3 text-center'p><'pure-u-1-3 text-right'l>>",
            "sPaginationType": "bootstrap",
            "oLanguage": {
                "sLengthMenu": "_MENU_ itens por p\u00E1gina",
                "oPaginate": {
                    "sPrevious": "<",
                    "sNext": ">",
                    "sFirst": "<<",
                    "sLast": ">>"

                },
                "sInfo": "Mostrando _START_ at� _END_",
                "sSearch": "Filtrar",
                "sEmptyTable": "Nenhum resultado para exibir.",
                "sZeroRecords": "Nenhum registro encontrado.",
                "sInfoEmpty": "",
                "sProcessing": "<span class='texto-cor-azul texto-font font-bold'><i class='fa-icon-refresh fa-icon-spin fa-2x' style='vertical-align: middle'></i> Processando...</span>",
                "sLoadingRecords": "Por favor aguarde. (Pode demorar um pouco em caso de muitos dados.)",
                "sInfoFiltered": " - Total _TOTAL_ item(s)"
                
            },
            "fnFormatNumber": function (iIn) {
                if (iIn < 1000) {
                    return iIn;
                } else {
                    var
                        s = (iIn + ""),
                        a = s.split(""), out = "",
                        iLen = s.length;

                    for (var i = 0; i < iLen; i++) {
                        if (i % 3 === 0 && i !== 0) {
                            out = "." + out;
                        }
                        out = a[iLen - i - 1] + out;
                    }
                }
                return out;
            }
        });


    /* Default class modification */
    $.extend($.fn.dataTableExt.oStdClasses, {
        "sWrapper": "dataTables_wrapper form-inline"
    });


    /* API method to get paging information */
    $.fn.dataTableExt.oApi.fnPagingInfo = function ( oSettings )
    {
        return {
            "iStart":         oSettings._iDisplayStart,
            "iEnd":           oSettings.fnDisplayEnd(),
            "iLength":        oSettings._iDisplayLength,
            "iTotal":         oSettings.fnRecordsTotal(),
            "iFilteredTotal": oSettings.fnRecordsDisplay(),
            "iPage":          Math.ceil( oSettings._iDisplayStart / oSettings._iDisplayLength ),
            "iTotalPages":    Math.ceil( oSettings.fnRecordsDisplay() / oSettings._iDisplayLength )
        };
    }


    /* Bootstrap style pagination control */
    $.extend($.fn.dataTableExt.oPagination, {
        "bootstrap": {
            "fnInit": function (oSettings, nPaging, fnDraw) {
                var oLang = oSettings.oLanguage.oPaginate;
                var fnClickHandler = function ( e ) {
                    e.preventDefault();
                    if ( oSettings.oApi._fnPageChange(oSettings, e.data.action) ) {
                        fnDraw( oSettings );
                    }
                };

                $(nPaging).addClass('pagination').append(
                    '<ul class="pure-paginator">' +
                        '<li class="prev disabled"><a class="pure-button" href="#">' + oLang.sFirst + '</a></li>' +
                        '<li class="prev disabled"><a class="pure-button" href="#">'+oLang.sPrevious+'</a></li>'+
                        '<li class="next disabled"><a class="pure-button" href="#">' + oLang.sNext + '</a></li>' +
                        '<li class="next disabled"><a class="pure-button" href="#">' + oLang.sLast + '</a></li>' +
                        '</ul>'
                );
                var els = $('a', nPaging);
                $(els[0]).bind('click.DT', { action: "first" }, fnClickHandler);
                $(els[1]).bind('click.DT', { action: "previous" }, fnClickHandler);
                $(els[2]).bind('click.DT', { action: "next" }, fnClickHandler);
                $(els[3]).bind('click.DT', { action: "last" }, fnClickHandler);
            },
            "fnUpdate": function (oSettings, fnDraw) {
                var iListLength = 5;
                var oPaging = oSettings.oInstance.fnPagingInfo();
                var an = oSettings.aanFeatures.p;
                var i, j, sClass, iStart, iEnd, iHalf=Math.floor(iListLength/2);

                if ( oPaging.iTotalPages < iListLength) {
                    iStart = 1;
                    iEnd = oPaging.iTotalPages;
                }
                else if ( oPaging.iPage <= iHalf ) {
                    iStart = 1;
                    iEnd = iListLength;
                } else if ( oPaging.iPage >= (oPaging.iTotalPages-iHalf) ) {
                    iStart = oPaging.iTotalPages - iListLength + 1;
                    iEnd = oPaging.iTotalPages;
                } else {
                    iStart = oPaging.iPage - iHalf + 1;
                    iEnd = iStart + iListLength - 1;
                }

                for ( i=0, iLen=an.length ; i<iLen ; i++ ) {
                    // Remove the middle elements
                    $('li:gt(1)', an[i]).filter(':not(.next)').remove();

                    // Add the new list items and their event handlers
                    for ( j=iStart ; j<=iEnd ; j++ ) {
                        sClass = (j==oPaging.iPage+1) ? 'class="active"' : '';
                        $('<li '+sClass+'><a class="pure-button" href="#">'+j+'</a></li>')
                            .insertBefore( $('li.next:first', an[i])[0] )
                            .bind('click', function (e) {
                                e.preventDefault();
                                oSettings._iDisplayStart = (parseInt($('a', this).text(),10)-1) * oPaging.iLength;
                                fnDraw( oSettings );
                            } );
                    }

                    // Add / remove disabled classes from the static elements
                    if ( oPaging.iPage === 0 ) {
                        $('li.prev', an[i]).addClass('pure-button-disabled');
                    } else {
                        $('li.prev', an[i]).removeClass('pure-button-disabled');
                    }

                    if ( oPaging.iPage === oPaging.iTotalPages-1 || oPaging.iTotalPages === 0 ) {
                        $('li.next', an[i]).addClass('pure-button-disabled');
                    } else {
                        $('li.next', an[i]).removeClass('pure-button-disabled');
                    }
                }
            }
        }
    });

    /* Melhoria ordena��o localizada */

    jQuery.fn.dataTableExt.oSort['string-asc'] = function (a, b) {
        var x = a.toLowerCase();
        var y = b.toLowerCase();
        return x.localeCompare(y);
    };

    jQuery.fn.dataTableExt.oSort['string-desc'] = function (a, b) {
        var x = a.toLowerCase();
        var y = b.toLowerCase();
        return y.localeCompare(x);
    };

    /*
     * TableTools Bootstrap compatibility
     * Required TableTools 2.1+
     */
    if ($.fn.DataTable.TableTools) {
        // Set the classes that TableTools uses to something suitable for Bootstrap
        $.extend(true, $.fn.DataTable.TableTools.classes, {
            "container": "DTTT btn-group",
            "buttons": {
                "normal": "btn",
                "disabled": "disabled"
            },
            "collection": {
                "container": "DTTT_dropdown dropdown-menu",
                "buttons": {
                    "normal": "",
                    "disabled": "disabled"
                }
            },
            "print": {
                "info": "DTTT_print_info modal"
            },
            "select": {
                "row": "active"
            }
        });

        // Have the collection use a bootstrap compatible dropdown
        $.extend(true, $.fn.DataTable.TableTools.DEFAULTS.oTags, {
            "collection": {
                "container": "ul",
                "button": "li",
                "liner": "a"
            }
        });
    }

    $.fn.dataTableExt.oApi.fnReloadAjax = function (oSettings, sNewSource, fnCallback, bStandingRedraw) {
        if (sNewSource !== undefined && sNewSource !== null) {
            oSettings.sAjaxSource = sNewSource;
        }

        // Server-side processing should just call fnDraw
        if (oSettings.oFeatures.bServerSide) {
            this.fnDraw();
            return;
        }

        this.oApi._fnProcessingDisplay(oSettings, true);
        var that = this;
        var iStart = oSettings._iDisplayStart;
        var aData = [];

        this.oApi._fnServerParams(oSettings, aData);

        oSettings.fnServerData.call(oSettings.oInstance, oSettings.sAjaxSource, aData, function (json) {
            /* Clear the old information from the table */
            that.oApi._fnClearTable(oSettings);

            /* Got the data - add it to the table */
            var aData = (oSettings.sAjaxDataProp !== "") ?
                that.oApi._fnGetObjectDataFn(oSettings.sAjaxDataProp)(json) : json;

            for (var i = 0; i < aData.length; i++) {
                that.oApi._fnAddData(oSettings, aData[i]);
            }

            oSettings.aiDisplay = oSettings.aiDisplayMaster.slice();

            that.fnDraw();

            if (bStandingRedraw === true) {
                oSettings._iDisplayStart = iStart;
                that.oApi._fnCalculateEnd(oSettings);
                that.fnDraw(false);
            }

            that.oApi._fnProcessingDisplay(oSettings, false);

            /* Callback user function - for event handlers etc */
            if (typeof fnCallback == 'function' && fnCallback !== null) {
                fnCallback(oSettings);
            }
        }, oSettings);
    };

    $.fn.dataTableExt.oApi.fnGetHiddenTrNodes = function (oSettings, arg1, arg2) {

        /* Note the use of a DataTables 'private' function thought the 'oApi' object */

        var anNodes = this.oApi._fnGetTrNodes(oSettings);
        var anDisplay = $('tbody tr', oSettings.nTable);

        /* Remove nodes which are being displayed */
        for (var i = 0; i < anDisplay.length; i++) {
            var iIndex = jQuery.inArray(anDisplay[i], anNodes);

            if (iIndex != -1) {
                anNodes.splice(iIndex, 1);
            }
        }

        /* Fire back the array to the caller */
        return anNodes;
    }
}(window.jQuery);

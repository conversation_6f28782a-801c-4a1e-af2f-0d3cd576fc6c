var tabelaAtual, intervalo;
var parou = false;
var rExps = { a: '[a\xE0-\xE6]', e: '[e?\xE8-\xEB]', i: '[i\xEC-\xEF]', o: '[o\xF2-\xF6]', u: '[u\xF9-\xFC]', n: '[n\xF1]'};
function iniTblServer(tabelaSeletor, url, data, eCustomSort, eAscDesc, desabilitarFuncFecharPopup) {
    var objInit = {};
    objInit = {
        "bProcessing": true,
        "bServerSide": true,
        "bDeferRender": true,
        "bAutoWidth": false,
        "bSortClasses": false,
        "sAjaxSource": url,
        "sServerMethod": "POST",
        "aLengthMenu": [
            [5, 10, 15, 25, 50, 100, 200, 500, -1],
            [5, 10, 15, 25, 50, 100, 200, 500, "Todos"]
        ],
        "iDisplayLength": 10,
        "fnCreatedRow": function (nRow, aData, iDataIndex) {
            if (true) {
                var filhosLinha = jQuery(nRow).children("td");
                jQuery.each(filhosLinha, function (key, value) {
                    var celula = value;
                    if (jQuery(celula).html() == "null") {
                        jQuery(celula).html("<span> - </span>");
                    } else if (jQuery(celula).html() == "") {
                        jQuery(celula).html("<span> - </span>");
                    } else if (jQuery(celula).html() == " ") {
                        jQuery(celula).html("<span> - </span>");
                    }
                });
            }
            jQuery(nRow).on("click", function () {
                jQuery("#chavePrimaria").val(aData[0]);
                if (desabilitarFuncFecharPopup != null && desabilitarFuncFecharPopup == 'true') {
                    setDocumentCookie('popupsImportante', '',1);
                }
                jsEditar();
            });
        },
        "fnDrawCallback": function (oSettings) {
            jQuery('.'+tabelaSeletor).find('td').addClass('texto-cor-cinza texto-size-12-real');
        }
    };


    var colSort = 0;
    if (eCustomSort != null) {
        colSort = eCustomSort;
    }

    var colDir = "desc";
    if (eAscDesc != null) {
        colDir = eAscDesc;
    }

    objInit["aaSorting"] = [
                [colSort, colDir]
            ];
    if (data != null) {
        objInit["fnServerParams"] = function (aoData) {
            jQuery.each(data, function (k, v) {
                aoData.push({"name": k, "value": v});
            });
        };
    }
    tabelaAtual = jQuery('.' + tabelaSeletor).dataTable(objInit);
    tabelaAtual.addClass('tabelaSimplesCustom');
    tabelaAtual.find('th').addClass('texto-cor-cinza texto-size-12-real texto-bold texto-upper');

    tabelaAtual.find('th').each(function (i) {
       var celH =  jQuery(this);
       var texto = celH.text();
       celH.text('');
       celH.append('<span>'+texto+'</span>');
     });
// Recreating the removed label and search input
    var label = jQuery('<label />', {text: tabelaAtual.fnSettings().oLanguage.sSearch,'class':'pull-left texto-cor-azul','style':'margin-top:0px'});
    var divSearch =  jQuery('<div style="display: inline-block;margin-left:10px;width: 50%"></div>');
    divSearch.appendTo('.dataTables_filter');
    jQuery('<input />', {
        'type': 'text',
        'class': 'searchbox-input toggle',
    }).bind('keyup.DT',function () {
            filterValue = '';
            window.clearTimeout(intervalo);
            intervalo = window.setTimeout("filtraServerSide()", 1000);

        }).addClass("filtroDT").appendTo(divSearch);
    jQuery('<i />', {
        'class': 'fa-icon-search searchbox-icon'
    }).appendTo(divSearch);
    jQuery("form#form").bind('keypress', function (e) {
        if (e.keyCode == 13) {
            //alert("Enter pressed");
            return false; // prevent the button click from happening
        }
    });
    label.appendTo('.dataTables_filter');
    var submitIcon = jQuery('.searchbox-icon');
    var inputBox = jQuery('.searchbox-input');
    var isOpen = false;
    submitIcon.on('click hover', function(){
        if(isOpen == false){
            inputBox.removeClass('toggle');
            inputBox.focus();
            isOpen = true;
        } else {
            inputBox.addClass('toggle');
            inputBox.focusout();
            isOpen = false;
        }
    });
    inputBox.on('focusout',function () {
        inputBox.addClass('toggle');
        isOpen = false;   
    });
    submitIcon.mouseup(function(){
        return false;
    });
    jQuery(document).mouseup(function(){
        if(isOpen == true){
            jQuery('.searchbox-icon').css('display','inline-block');
            submitIcon.click();
        }
    });

}
function filtraServerSide() {
    var valor = jQuery(".filtroDT").val().toLowerCase();
    var tamValor = valor.length;
    if (tamValor > 1 || valor == "") {
        searchValue = valor;
        parou = false;
// We strip the accents first
        jQuery.each(rExps, function (key, value) {
            searchValue = searchValue.replace(new RegExp(value, 'g'), key);
        });

// Then we recreate a regex to match the accents into the datatable
        for (i = 0; i < searchValue.length; i++) {

            if (rExps[searchValue[i]] != undefined)
                filterValue += rExps[searchValue[i]];
            else
                filterValue += searchValue[i];
        }

        tabelaAtual.fnFilter(filterValue, null, true);
    }
}

function retornaDadosDT() {
    var valorFiltro = "''";
    if (jQuery(".filtroDT").val() != "") {
        valorFiltro = jQuery(".filtroDT").val();
    }
    var dados = tabelaAtual.fnSettings().aaSorting;
    var coluna = "th:nth-child(" + (dados[0][0] + 1) + ")";
    var nomeColuna = jQuery(".dataTable").children("thead").children("tr").children(coluna).html();
    return "[" + nomeColuna + "," + (dados[0][1]) + "," + valorFiltro + "]";
}

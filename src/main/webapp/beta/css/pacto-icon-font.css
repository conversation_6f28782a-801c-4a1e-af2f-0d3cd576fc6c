@font-face {
  font-family: 'Pacto-Icons-Fonts';
  src:  url('../font/Pacto-Icons-Fonts_8_4.eot?etz3kw');
  src:  url('../font/Pacto-Icons-Fonts_8_4.eot?etz3kw#iefix') format('embedded-opentype'),
  url('../font/Pacto-Icons-Fonts_8_4.ttf?etz3kw') format('truetype'),
  url('../font/Pacto-Icons-Fonts_8_4.woff?etz3kw') format('woff'),
  url('../font/Pacto-Icons-Fonts_8_4.svg?etz3kw#Pacto-Icons-Fonts') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.pct {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'Pacto-Icons-Fonts' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.pct-ambulance:before {
  content: "\e900";
}
.pct-bi:before {
  content: "\e901";
}
.pct-cake:before {
  content: "\e902";
}
.pct-cleaner:before {
  content: "\e903";
}
.pct-coins:before {
  content: "\e904";
}
.pct-data-search:before {
  content: "\e905";
}
.pct-date-atention:before {
  content: "\e906";
}
.pct-dollar-ok:before {
  content: "\e907";
}
.pct-drag-and-drop:before {
  content: "\e908";
}
.pct-enotas:before {
  content: "\e909";
}
.pct-facial-bio:before {
  content: "\e90a";
}
.pct-gender:before {
  content: "\e90b";
}
.pct-payment-form:before {
  content: "\e90c";
}
.pct-resend:before {
  content: "\e90d";
}
.pct-smart-check:before {
  content: "\e90e";
}
.pct-tranfer-banks:before {
  content: "\e90f";
}
.pct---xml:before {
  content: "\e910";
}
.pct-add-image:before {
  content: "\e911";
}
.pct-add-shopping-cart:before {
  content: "\e912";
}
.pct-antecipao:before {
  content: "\e913";
}
.pct-classroom:before {
  content: "\e914";
}
.pct-diamond:before {
  content: "\e915";
}
.pct-heartbeat:before {
  content: "\e916";
}
.pct-hourglass:before {
  content: "\e917";
}
.pct-keyboard:before {
  content: "\e918";
}
.pct-knowlodge:before {
  content: "\e919";
}
.pct-megaphone:before {
  content: "\e91a";
}
.pct-muscle:before {
  content: "\e91b";
}
.pct-new-brand-1:before {
  content: "\e91c";
}
.pct-new-brand-2:before {
  content: "\e91d";
}
.pct-reversal:before {
  content: "\e91e";
}
.pct-rocket:before {
  content: "\e91f";
}
.pct-shield-health:before {
  content: "\e920";
}
.pct-star-filled:before {
  content: "\e921";
}
.pct-user-fav:before {
  content: "\e922";
}
.pct-add-notification:before {
  content: "\e923";
}
.pct-calculator:before {
  content: "\e924";
}
.pct-grocery:before {
  content: "\e925";
}
.pct-market:before {
  content: "\e926";
}
.pct-minimize-3:before {
  content: "\e927";
}
.pct-qr-code:before {
  content: "\e928";
}
.pct-recipe:before {
  content: "\e929";
}
.pct-stop-recording:before {
  content: "\e92a";
}
.pct-telemarketing:before {
  content: "\e92b";
}
.pct-time-cap:before {
  content: "\e92c";
}
.pct-walk:before {
  content: "\e92d";
}
.pct-wellness:before {
  content: "\e92e";
}
.pct-afternoon:before {
  content: "\e92f";
}
.pct-biset:before {
  content: "\e930";
}
.pct-circle-filled:before {
  content: "\e931";
}
.pct-circularcontroller:before {
  content: "\e932";
}
.pct-footprint:before {
  content: "\e933";
}
.pct-gdrive:before {
  content: "\e934";
}
.pct-google:before {
  content: "\e935";
}
.pct-gsheets:before {
  content: "\e936";
}
.pct-height:before {
  content: "\e937";
}
.pct-ingredients:before {
  content: "\e938";
}
.pct-insight:before {
  content: "\e939";
}
.pct-logo-pacto:before {
  content: "\e93a";
}
.pct-morning:before {
  content: "\e93b";
}
.pct-move-2:before {
  content: "\e93c";
}
.pct-nutrition:before {
  content: "\e93d";
}
.pct-pin:before {
  content: "\e93e";
}
.pct-play-filled:before {
  content: "\e93f";
}
.pct-stack:before {
  content: "\e940";
}
.pct-tray:before {
  content: "\e941";
}
.pct-triset:before {
  content: "\e942";
}

.pct-trophy:before {
  content: "\e943";
}

.pct-trophy-filled:before {
  content: "\e944";
}

.pct-unpin:before {
  content: "\e945";
}

.pct-viewed:before {
  content: "\e946";
}

.pct-weight:before {
  content: "\e947";
}

.pct-whatsapp:before {
  content: "\e948";
}

.pct-activity:before {
  content: "\e949";
}

.pct-airplay:before {
  content: "\e94a";
}

.pct-alert-circle:before {
  content: "\e94b";
}

.pct-alert-octagon:before {
  content: "\e94c";
}

.pct-alert-triangle:before {
  content: "\e94d";
}

.pct-align-center:before {
  content: "\e94e";
}

.pct-align-justify:before {
  content: "\e94f";
}

.pct-align-left:before {
  content: "\e950";
}

.pct-align-right:before {
  content: "\e951";
}

.pct-anchor:before {
  content: "\e952";
}

.pct-aperture:before {
  content: "\e953";
}

.pct-archive:before {
  content: "\e954";
}

.pct-arrow-down:before {
  content: "\e955";
}

.pct-arrow-down-circle:before {
  content: "\e956";
}

.pct-arrow-down-left:before {
  content: "\e957";
}

.pct-arrow-down-right:before {
  content: "\e958";
}

.pct-arrow-left:before {
  content: "\e959";
}

.pct-arrow-left-circle:before {
  content: "\e95a";
}

.pct-arrow-right:before {
  content: "\e95b";
}

.pct-arrow-right-circle:before {
  content: "\e95c";
}

.pct-arrow-up:before {
  content: "\e95d";
}

.pct-arrow-up-circle:before {
  content: "\e95e";
}

.pct-arrow-up-left:before {
  content: "\e95f";
}

.pct-arrow-up-right:before {
  content: "\e960";
}

.pct-at-sign:before {
  content: "\e961";
}

.pct-award:before {
  content: "\e962";
}

.pct-bar-chart:before {
  content: "\e963";
}

.pct-bar-chart-2:before {
  content: "\e964";
}

.pct-battery:before {
  content: "\e965";
}

.pct-battery-charging:before {
  content: "\e966";
}

.pct-bell:before {
  content: "\e967";
}

.pct-bell-off:before {
  content: "\e968";
}

.pct-bluetooth:before {
  content: "\e969";
}

.pct-bold:before {
  content: "\e96a";
}

.pct-book:before {
  content: "\e96b";
}

.pct-bookmark:before {
  content: "\e96c";
}

.pct-book-open:before {
  content: "\e96d";
}

.pct-box:before {
  content: "\e96e";
}

.pct-briefcase:before {
  content: "\e96f";
}

.pct-calendar:before {
  content: "\e970";
}

.pct-camera:before {
  content: "\e971";
}

.pct-camera-off:before {
  content: "\e972";
}

.pct-caret-down:before {
  content: "\e973";
}

.pct-caret-left:before {
  content: "\e974";
}

.pct-caret-right:before {
  content: "\e975";
}

.pct-caret-up:before {
  content: "\e976";
}

.pct-cast:before {
  content: "\e977";
}

.pct-check:before {
  content: "\e978";
}

.pct-check-circle:before {
  content: "\e979";
}

.pct-check-square:before {
  content: "\e97a";
}

.pct-chevron-down:before {
  content: "\e97b";
}

.pct-chevron-left:before {
  content: "\e97c";
}

.pct-chevron-right:before {
  content: "\e97d";
}

.pct-chevrons-down:before {
  content: "\e97e";
}

.pct-chevrons-left:before {
  content: "\e97f";
}

.pct-chevrons-right:before {
  content: "\e980";
}

.pct-chevrons-up:before {
  content: "\e981";
}

.pct-chevron-up:before {
  content: "\e982";
}

.pct-chrome:before {
  content: "\e983";
}

.pct-circle:before {
  content: "\e984";
}

.pct-clipboard:before {
  content: "\e985";
}

.pct-clock:before {
  content: "\e986";
}

.pct-cloud:before {
  content: "\e987";
}

.pct-cloud-drizzle:before {
  content: "\e988";
}

.pct-cloud-lightning:before {
  content: "\e989";
}

.pct-cloud-off:before {
  content: "\e98a";
}

.pct-cloud-rain:before {
  content: "\e98b";
}

.pct-cloud-snow:before {
  content: "\e98c";
}

.pct-code:before {
  content: "\e98d";
}

.pct-codepen:before {
  content: "\e98e";
}

.pct-codesandbox:before {
  content: "\e98f";
}

.pct-coffee:before {
  content: "\e990";
}

.pct-columns:before {
  content: "\e991";
}

.pct-command:before {
  content: "\e992";
}

.pct-community:before {
  content: "\e993";
}

.pct-compass:before {
  content: "\e994";
}

.pct-copy:before {
  content: "\e995";
}

.pct-corner-down-left:before {
  content: "\e996";
}

.pct-corner-down-right:before {
  content: "\e997";
}

.pct-corner-left-down:before {
  content: "\e998";
}

.pct-corner-left-up:before {
  content: "\e999";
}

.pct-corner-right-down:before {
  content: "\e99a";
}

.pct-corner-right-up:before {
  content: "\e99b";
}

.pct-corner-up-left:before {
  content: "\e99c";
}

.pct-corner-up-right:before {
  content: "\e99d";
}

.pct-cpu:before {
  content: "\e99e";
}

.pct-credit-card:before {
  content: "\e99f";
}

.pct-crop:before {
  content: "\e9a0";
}

.pct-cross:before {
  content: "\e9a1";
}

.pct-crosshair:before {
  content: "\e9a2";
}

.pct-database:before {
  content: "\e9a3";
}

.pct-delete:before {
  content: "\e9a4";
}

.pct-digital:before {
  content: "\e9a5";
}

.pct-disc:before {
  content: "\e9a6";
}

.pct-dollar-sign:before {
  content: "\e9a7";
}

.pct-download:before {
  content: "\e9a8";
}

.pct-download-cloud:before {
  content: "\e9a9";
}

.pct-drag:before {
  content: "\e9aa";
}

.pct-drop-down:before {
  content: "\e9ab";
}

.pct-droplet:before {
  content: "\e9ac";
}

.pct-edit:before {
  content: "\e9ad";
}

.pct-edit-2:before {
  content: "\e9ae";
}

.pct-edit-3:before {
  content: "\e9af";
}

.pct-evaluation:before {
  content: "\e9b0";
}

.pct-external-link:before {
  content: "\e9b1";
}

.pct-eye:before {
  content: "\e9b2";
}

.pct-eye-off:before {
  content: "\e9b3";
}

.pct-facebook:before {
  content: "\e9b4";
}

.pct-fast-forward:before {
  content: "\e9b5";
}

.pct-feather:before {
  content: "\e9b6";
}

.pct-figma:before {
  content: "\e9b7";
}

.pct-file:before {
  content: "\e9b8";
}

.pct-file-minus:before {
  content: "\e9b9";
}

.pct-file-plus:before {
  content: "\e9ba";
}

.pct-file-text:before {
  content: "\e9bb";
}

.pct-film:before {
  content: "\e9bc";
}

.pct-filter:before {
  content: "\e9bd";
}

.pct-flag:before {
  content: "\e9be";
}

.pct-folder:before {
  content: "\e9bf";
}

.pct-folder-minus:before {
  content: "\e9c0";
}

.pct-folder-plus:before {
  content: "\e9c1";
}

.pct-frown:before {
  content: "\e9c2";
}

.pct-gift:before {
  content: "\e9c3";
}

.pct-git-branch:before {
  content: "\e9c4";
}

.pct-git-commit:before {
  content: "\e9c5";
}

.pct-github:before {
  content: "\e9c6";
}

.pct-gitlab:before {
  content: "\e9c7";
}

.pct-git-merge:before {
  content: "\e9c8";
}

.pct-git-pull-request:before {
  content: "\e9c9";
}

.pct-globe:before {
  content: "\e9ca";
}

.pct-grid:before {
  content: "\e9cb";
}

.pct-happy-01:before {
  content: "\e9cc";
}

.pct-happy-02:before {
  content: "\e9cd";
}

.pct-happy-03:before {
  content: "\e9ce";
}

.pct-hard-drive:before {
  content: "\e9cf";
}

.pct-hash:before {
  content: "\e9d0";
}

.pct-headphones:before {
  content: "\e9d1";
}

.pct-heart:before {
  content: "\e9d2";
}

.pct-help-circle:before {
  content: "\e9d3";
}

.pct-hexagon:before {
  content: "\e9d4";
}
.pct-history:before {
  content: "\e9d5";
}
.pct-home:before {
  content: "\e9d6";
}

.pct-image:before {
  content: "\e9d7";
}

.pct-inbox:before {
  content: "\e9d8";
}

.pct-info:before {
  content: "\e9d9";
}

.pct-instagram:before {
  content: "\e9da";
}

.pct-italic:before {
  content: "\e9db";
}

.pct-key:before {
  content: "\e9dc";
}

.pct-keyboard1:before {
  content: "\e9dd";
}

.pct-layers:before {
  content: "\e9de";
}

.pct-layout:before {
  content: "\e9df";
}

.pct-level:before {
  content: "\e9e0";
}

.pct-life-buoy:before {
  content: "\e9e1";
}

.pct-link:before {
  content: "\e9e2";
}

.pct-link-2:before {
  content: "\e9e3";
}

.pct-linkedin:before {
  content: "\e9e4";
}

.pct-list:before {
  content: "\e9e5";
}

.pct-loader:before {
  content: "\e9e6";
}

.pct-lock:before {
  content: "\e9e7";
}

.pct-log-in:before {
  content: "\e9e8";
}

.pct-log-out:before {
  content: "\e9e9";
}

.pct-mail:before {
  content: "\e9ea";
}

.pct-map:before {
  content: "\e9eb";
}

.pct-map-pin:before {
  content: "\e9ec";
}

.pct-maximize:before {
  content: "\e9ed";
}

.pct-maximize-2:before {
  content: "\e9ee";
}

.pct-meh:before {
  content: "\e9ef";
}

.pct-menu:before {
  content: "\e9f0";
}

.pct-message-circle:before {
  content: "\e9f1";
}

.pct-message-square:before {
  content: "\e9f2";
}

.pct-mic:before {
  content: "\e9f3";
}

.pct-mic-off:before {
  content: "\e9f4";
}

.pct-minimize:before {
  content: "\e9f5";
}

.pct-minimize-2:before {
  content: "\e9f6";
}

.pct-minus:before {
  content: "\e9f7";
}

.pct-minus-circle:before {
  content: "\e9f8";
}

.pct-minus-square:before {
  content: "\e9f9";
}

.pct-modality:before {
  content: "\e9fa";
}

.pct-monitor:before {
  content: "\e9fb";
}

.pct-moon:before {
  content: "\e9fc";
}

.pct-more-horizontal:before {
  content: "\e9fd";
}

.pct-more-vertical:before {
  content: "\e9fe";
}

.pct-mouse-pointer:before {
  content: "\e9ff";
}

.pct-move:before {
  content: "\ea00";
}

.pct-music:before {
  content: "\ea01";
}

.pct-navigation:before {
  content: "\ea02";
}

.pct-navigation-2:before {
  content: "\ea03";
}

.pct-octagon:before {
  content: "\ea04";
}

.pct-package:before {
  content: "\ea05";
}

.pct-paperclip:before {
  content: "\ea06";
}

.pct-pause:before {
  content: "\ea07";
}

.pct-pause-circle:before {
  content: "\ea08";
}

.pct-pen-tool:before {
  content: "\ea09";
}

.pct-percent:before {
  content: "\ea0a";
}

.pct-phone:before {
  content: "\ea0b";
}

.pct-phone-call:before {
  content: "\ea0c";
}

.pct-phone-forwarded:before {
  content: "\ea0d";
}

.pct-phone-incoming:before {
  content: "\ea0e";
}

.pct-phone-off:before {
  content: "\ea0f";
}

.pct-phone-outgoing:before {
  content: "\ea10";
}

.pct-pie-chart:before {
  content: "\ea11";
}

.pct-play:before {
  content: "\ea12";
}

.pct-play-circle:before {
  content: "\ea13";
}

.pct-plus:before {
  content: "\ea14";
}

.pct-plus-circle:before {
  content: "\ea15";
}

.pct-plus-square:before {
  content: "\ea16";
}

.pct-pocket:before {
  content: "\ea17";
}

.pct-power:before {
  content: "\ea18";
}

.pct-printer:before {
  content: "\ea19";
}

.pct-radio:before {
  content: "\ea1a";
}

.pct-refresh-ccw:before {
  content: "\ea1b";
}

.pct-refresh-cw:before {
  content: "\ea1c";
}

.pct-repeat:before {
  content: "\ea1d";
}

.pct-rewind:before {
  content: "\ea1e";
}

.pct-rotate-ccw:before {
  content: "\ea1f";
}

.pct-rotate-cw:before {
  content: "\ea20";
}

.pct-rss:before {
  content: "\ea21";
}

.pct-save:before {
  content: "\ea22";
}

.pct-scissors:before {
  content: "\ea23";
}

.pct-search:before {
  content: "\ea24";
}

.pct-send:before {
  content: "\ea25";
}

.pct-server:before {
  content: "\ea26";
}

.pct-settings:before {
  content: "\ea27";
}

.pct-share:before {
  content: "\ea28";
}

.pct-share-2:before {
  content: "\ea29";
}

.pct-shield:before {
  content: "\ea2a";
}

.pct-shield-off:before {
  content: "\ea2b";
}

.pct-shopping-bag:before {
  content: "\ea2c";
}

.pct-shopping-cart:before {
  content: "\ea2d";
}

.pct-shuffle:before {
  content: "\ea2e";
}

.pct-sidebar:before {
  content: "\ea2f";
}

.pct-skill:before {
  content: "\ea30";
}

.pct-skip-back:before {
  content: "\ea31";
}

.pct-skip-forward:before {
  content: "\ea32";
}

.pct-slack:before {
  content: "\ea33";
}

.pct-slash:before {
  content: "\ea34";
}

.pct-sliders:before {
  content: "\ea35";
}

.pct-smartphone:before {
  content: "\ea36";
}

.pct-smile:before {
  content: "\ea37";
}

.pct-speaker:before {
  content: "\ea38";
}

.pct-square:before {
  content: "\ea39";
}

.pct-star:before {
  content: "\ea3a";
}

.pct-stop-circle:before {
  content: "\ea3b";
}

.pct-sun:before {
  content: "\ea3c";
}

.pct-sunrise:before {
  content: "\ea3d";
}

.pct-sunset:before {
  content: "\ea3e";
}

.pct-tablet:before {
  content: "\ea3f";
}

.pct-tag:before {
  content: "\ea40";
}

.pct-target:before {
  content: "\ea41";
}

.pct-terminal:before {
  content: "\ea42";
}

.pct-thermometer:before {
  content: "\ea43";
}

.pct-thumbs-down:before {
  content: "\ea44";
}

.pct-thumbs-up:before {
  content: "\ea45";
}

.pct-toggle-left:before {
  content: "\ea46";
}

.pct-toggle-right:before {
  content: "\ea47";
}

.pct-trash:before {
  content: "\ea48";
}

.pct-trash-2:before {
  content: "\ea49";
}

.pct-treino:before {
  content: "\ea4a";
}

.pct-trello:before {
  content: "\ea4b";
}

.pct-trending-down:before {
  content: "\ea4c";
}

.pct-trending-up:before {
  content: "\ea4d";
}

.pct-triangle:before {
  content: "\ea4e";
}

.pct-truck:before {
  content: "\ea4f";
}

.pct-tv:before {
  content: "\ea50";
}

.pct-twitter:before {
  content: "\ea51";
}

.pct-type:before {
  content: "\ea52";
}

.pct-umbrella:before {
  content: "\ea53";
}

.pct-underline:before {
  content: "\ea54";
}

.pct-unlock:before {
  content: "\ea55";
}

.pct-upload:before {
  content: "\ea56";
}

.pct-upload-cloud:before {
  content: "\ea57";
}

.pct-user:before {
  content: "\ea58";
}

.pct-user-check:before {
  content: "\ea59";
}

.pct-user-minus:before {
  content: "\ea5a";
}

.pct-user-plus:before {
  content: "\ea5b";
}

.pct-users:before {
  content: "\ea5c";
}

.pct-user-x:before {
  content: "\ea5d";
}

.pct-video:before {
  content: "\ea5e";
}

.pct-video-off:before {
  content: "\ea5f";
}

.pct-voicemail:before {
  content: "\ea60";
}

.pct-volume:before {
  content: "\ea61";
}
.pct-volume-1:before {
  content: "\ea62";
}
.pct-volume-2:before {
  content: "\ea63";
}
.pct-volume-x:before {
  content: "\ea64";
}
.pct-watch:before {
  content: "\ea65";
}
.pct-wifi:before {
  content: "\ea66";
}
.pct-wifi-off:before {
  content: "\ea67";
}
.pct-wind:before {
  content: "\ea68";
}
.pct-wod:before {
  content: "\ea69";
}
.pct-x:before {
  content: "\ea6a";
}
.pct-x-circle:before {
  content: "\ea6b";
}
.pct-x-octagon:before {
  content: "\ea6c";
}
.pct-x-square:before {
  content: "\ea6d";
}
.pct-youtube:before {
  content: "\ea6e";
}
.pct-zap:before {
  content: "\ea6f";
}
.pct-zap-off:before {
  content: "\ea70";
}
.pct-zoom-in:before {
  content: "\ea71";
}
.pct-zoom-out:before {
  content: "\ea72";
}

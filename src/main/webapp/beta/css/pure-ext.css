/* Extend Geral */
html {
    font-size: 14px;
}
body {
    /*min-width: 1350px;*/
    overflow: scroll;
}
/* Alinhamento */
/* Margens */
.margin-h-10{margin-left: 10px; margin-right: 10px}
.margin-h-6{margin-left: 6px; margin-right: 6px}
.margin-v-6{margin-left: 6px; margin-right: 6px}
.margin-v-10{margin-top: 10px; margin-bottom: 10px}
.margin-b-10{margin-bottom: 10px}
.margin-0-auto-i{margin-left: auto; margin-right: auto; display: block;}
.margin-0-auto{margin-left: auto; margin-right: auto; display: block;}
/* Texto */
.text-left {text-align: left} .text-center {text-align: center} .text-right {text-align: right}
span[class*='text-'] {display: block; width: 100%}
/* Extend BTNs*/
.pure-button-xsmall {font-size: 70%; padding: 3px 9px;} .pure-button-small {font-size: 85%; padding: 4px 12px;} .pure-button-large {padding: 7px 12px;!important} .pure-button-xlarge {font-size: 125%;}
.pure-button {background-color: #cdcdcd !important;} .pure-button-primary{background-color: #004671 !important;}
/* Pagina��o */
.pure-paginator .pure-button {padding: .4em .7em; text-shadow: none;}
/* Extend container */
.pure-g-r {letter-spacing: normal;}
/* Icones */
a.pure-button,a.pure-button:hover {
    color: black;
    text-decoration: none;
}
a.pure-button-primary,a.pure-button-primary:hover {
    color: white;
    text-decoration: none;
}
a.pure-button:hover {
    text-decoration: none;
}
.pure-button-success {
    background: rgb(28, 184, 65) !important; /* this is a green */
    color: white !important;
}

.pure-button-error {
    background: rgb(202, 60, 60) !important; /* this is a maroon */
    color: white !important;
}
.pure-button-mvq{
    margin-left: 10px; background-color: #1998FC;
    font-family: Arial;
    font-style: normal;
    font-weight: bold;font-size: 12px;
    line-height: 12px;
    color:#ffffff !important;
    text-decoration: none!important;
}
a.pure-button-mvq:hover{
    margin-left: 10px; background-color: #0e90d2;
    font-family: Arial;
    font-style: normal;
    font-weight: bold;font-size: 12px;
    line-height: 12px; color:#ffffff;
    text-decoration: none!important;
}
.pure-button-botconversa, a.pure-button-botconversa:hover{
    margin-left: 10px; background-color: #5aac41;
    font-family: Arial;
    font-style: normal;
    font-weight: bold;font-size: 12px;
    line-height: 12px;
    color:#ffffff !important;
    text-decoration: none!important;
}
.pure-button-botconversa, a.pure-button-botconversa{
    margin-left: 10px; background-color: #5aac41;
    font-family: Arial;
    font-style: normal;
    font-weight: bold;font-size: 12px;
    line-height: 12px;
    color:#ffffff !important;
    text-decoration: none!important;
}

.pure-button-gymbotpro, a.pure-button-gymbotpro:hover{
    margin-left: 10px; background-color: #128c7e;
    font-family: Arial;
    font-style: normal;
    font-weight: bold;font-size: 12px;
    line-height: 12px;
    color:#ffffff !important;
    text-decoration: none!important;
}
.pure-button-gymbotpro, a.pure-button-gymbotpro{
    margin-left: 10px; background-color: #128c7e;
    font-family: Arial;
    font-style: normal;
    font-weight: bold;font-size: 12px;
    line-height: 12px;
    color:#ffffff !important;
    text-decoration: none!important;
}

.pure-button-warning {
    background: rgb(223, 117, 20) !important; /* this is an orange */
}

.pure-button-secondary {
    background: rgb(66, 184, 221) !important; /* this is a light blue */
}
/* pure-table */
.pure-table {
    display: table;
}
.pure-table-links a {
    text-decoration: none !important;
    display: block;
    width: 100%;
    height: 100%;
}
.pure-table.cell-based td:hover {
    background-color: #e8e8e8;
    cursor: pointer;
}
.pure-table.cell-based td a, .pure-table.cell-based td a:hover {
    color: black;
    font-size: 14px;
    text-decoration: none;
}
.pure-table-striped tr:hover td {
    background-color: #E8E8E8;
}
.pure-table-noCellPadding td{
    padding: 0;
}

/* Grade */
.pure-u-1-2 {
    width: 49.7%;
}
.pure-u-2-5 {
    width: 36.9%;
}
/* Forms */
.pure-form:not(.no-ext-css) fieldset {
    padding: 0.35em 0 0;
}
/* tabela */
.dataTables_info {
    margin-top: 10px;
    font-size: 1em;
}

.dataTable{
    display: table;
    margin-bottom: 6px;
}
.dataTable tr {
    font-size: 12px;
}
.dataTable tr > * {
    padding: 10px;
}
.dataTable > thead > tr > th {
    text-align: left;
}
/*.dataTable > thead > tr > th:first-child {*/
    /*text-align: center;*/
/*}*/
.dataTable > tbody tr:nth-child(even) {
    background-color: #fff !important;
}
.dataTable:not(.tabelaSimplesCustom)  > tbody tr:nth-child(odd) {
    background-color: #eee !important;
}
.dataTable > tbody tr:hover {
    background-color: #e5e5e5 !important;
    cursor: pointer !important;
}

.dataTable:not(.tabelaSimplesCustom) thead .sorting { /*background: url('images/sort_both.png') no-repeat center left;*/ margin-left: 5px; font-weight: normal; }
.dataTable:not(.tabelaSimplesCustom) thead .sorting_asc { /*background: url('images/sort_asc.png') no-repeat center left;*/ font-weight: bold; }
.dataTable:not(.tabelaSimplesCustom) thead .sorting_desc { /*background: url('images/sort_desc.png') no-repeat center left;*/ font-weight: bold; }
.dataTable:not(.tabelaSimplesCustom) thead .sorting:before { content: "\f0dc"; font-family: FontAwesome; color: rgba(0,0,0,0.1); font-weight: bold; margin-right: 5px; }
.dataTable:not(.tabelaSimplesCustom) thead .sorting_asc:before { content: "\f0de"; font-family: FontAwesome; color: black; font-weight: bold; margin-right: 5px; }
.dataTable:not(.tabelaSimplesCustom) thead .sorting_desc:before { content: "\f0dd"; font-family: FontAwesome; color: black; font-weight: bold; margin-right: 5px; }

/* INICIO ------------------------------------ novidades do layout material*/
.dataTable tr > * {
    padding: 13px;;
}

.dataTables_filter, .dataTables_length {
    color: #000;
    font-size: 16px;
}

.dataTables_filter {
    position: relative;
    min-width: 125px;
    width: 50%;
    overflow: hidden;
    line-height: 35px;
    font-weight: bold;
    -webkit-transition: width 0.4s;
    -moz-transition: width 0.4s;
    -ms-transition: width 0.4s;
    -o-transition: width 0.4s;
    transition: width 0.4s;
}
.searchbox-input.toggle{
    width: 0px;
    padding: 0px !important;
}
.searchbox-input {
    text-align: left;
    border: 0;
    outline: 0;
    background: #dcddd8;
    width: 70%;
    height: 38px;
    margin: 0;
    padding: 0px 55px 0px 20px;
    font-size: 20px;
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0 !important;
    border-left-width: 0 !important;
    border-radius: 0 !important;
    border-right-width: 0 !important;
    border-top: medium none !important;
    box-shadow: 0 0 0 0 !important;
    -webkit-transition: all .4s ease-in-out !important;
    -moz-transition: all .4s ease-in-out !important;
    -ms-transition: all .4s ease-in-out !important;
    -o-transition: all .4s ease-in-out !important;
    transition: all .4s ease-in-out !important;
}

.searchbox-input::-webkit-input-placeholder {
    color: #d74b4b;
}

.searchbox-input:-moz-placeholder {
    color: #d74b4b;
}

.searchbox-input::-moz-placeholder {
    color: #d74b4b;
}

.searchbox-input:-ms-input-placeholder {
    color: #d74b4b;
}

.searchbox-icon {
    text-align: right;
    line-height: 35px;
    text-align: center;
    cursor: pointer;
    color: #29ABE2;
}

.searchbox-open {
    width: 100%;
}
.titulo-topo {
    color: #333333;
    margin-bottom:-40px;
    background-color: #e6e6e6;
    height: 44px;
}
.controles{
    width: 97%; line-height: 44px;
}
.exportadores{
    text-decoration: none; font-size: 16px; color: #29ABE2;
}
.exportadores:hover{
    text-decoration: none;
}

/* FIM ------------------------------------ novidades do layout material*/

/********************** OVERFLOW CLASSES RICHFACES **********************/
/*** Data Range ***/
.pure-control-group.data-range > * > *:not(:first-child), .pure-control-group.data-range > span.tituloCampos {
    margin: 0 5px;
}
/*** Modal ***/
/*.rich-mpnl-header {
    font-weight: normal;
    font-size: 14px;
}*/
.rich-mpnl-text.rich-mpnl-controls{
    color: black;
    margin-top: 5px;
    margin-right: 5px;
}
.rich-mpnl-text.rich-mpnl-controls a, .rich-mpnl-text.rich-mpnl-controls a:hover {
    color: black;
    text-decoration: none;
    cursor: pointer;
}
.pure-button .rich-menu-item-icon {
    display: none;
}
/****************** Novidades ******************/
.passos {
    height: 0px;
    overflow: hidden;
}
.idOculta {
    opacity: 0;
}
.fa-icon-excel {
    background-image: url("../imagens/file-excel.svg") !important;
    width: 12px !important;
    height: 12px !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    vertical-align: middle !important;
    margin-right: -5px !important;
}
.fa-icon-pdf {
    background-image: url("../imagens/file-pdf.svg") !important;
    width: 12px !important;
    height: 12px !important;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    vertical-align: middle !important;
    margin-right: -5px !important;
}
.caixaCarregandoCadastro{
    position: absolute;
    line-height: 35px;
    left: 33.3%;
}

td:nth-child(3).texto-size-12-real {
    text-transform: uppercase !important;
}

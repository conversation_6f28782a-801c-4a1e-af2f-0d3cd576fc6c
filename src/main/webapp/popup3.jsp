<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
    
        <html>
            <jsp:include page="include_head.jsp" flush="true" />
            <body>
                <script type="text/javascript" language="javascript" src="hoverform.js"></script>
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td align="center" valign="top" bgcolor="#ffffff">
                            <table width="90%" border="0" cellpadding="0" cellspacing="0" style="padding:10px;">
                                <tr>
                                    <td align="left" valign="top">
                                        <!-- inicio botões -->                           
                                         <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                                         
                                        <div style="clear:both;margin-bottom:15px;">
                                            <h:commandButton id="salvar1" action="#{ClienteControle.gravar}"  value="Confirmar" onclick="location='javascript:window.close();'"  alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>                                                                                                                                        
                                        <input onClick="location='javascript:window.close();'" type="button" name="Submit" value="Cancelar"></div>
                                        
                                        <!-- fim botões -->
                                   <!-- inicio item -->
                                        <div style="clear:both;" class="text">
                                            <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Endere&ccedil;o</p>
                                        </div>
                                        <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                        
                                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">                                            
                                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">                               
                                                
                                                <h:outputText    value="#{msg_aplic.prt_Endereco_cep}" />
                                                <h:panelGroup>
                                                    <h:inputText  id="CEP" size="10" maxlength="10" onkeypress="return mascara(this.form, 'form:CEP', '99.999-999', event);"  onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.cep}" />
                                                    <a class="textsmall" href="javascript:;"><img border="0" style="margin-left:4px;margin-right:4px;vertical-align:middle;" src="images/icon_lupa.png" title="Consulte o CEP" width="16" height="16">Consulte o CEP</a>
                                                </h:panelGroup>                                                
                                                <h:outputText    value="#{msg_aplic.prt_Endereco_endereco}" />
                                                <h:inputText  size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.endereco}" />
                                                <h:outputText    value="#{msg_aplic.prt_Endereco_complemento}" />
                                                <h:inputText  size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.complemento}" />
                                                <h:outputText    value="#{msg_aplic.prt_Endereco_bairro}" />                                             
                                                <h:inputText  size="35" maxlength="35" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.bairro}" />                                                
                                                <h:outputText    value="#{msg_aplic.prt_Endereco_numero}" />
                                                <h:inputText  size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.numero}" />                                                   
                                                
                                                <h:outputText   value="#{msg_aplic.prt_Endereco_enderecoCorrespondencia}"/>
                                                <h:selectBooleanCheckbox id="enderecoCorresponencia" value="#{ClienteControle.enderecoVO.enderecoCorrespondencia}"/>
                                                <h:outputText    value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                                <h:selectOneMenu  id="Endereco_tipoEndereco" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.enderecoVO.tipoEndereco}" >
                                                    <f:selectItems  value="#{ClienteControle.listaSelectItemTipoEnderecoEndereco}" />
                                                </h:selectOneMenu>
                                                <h:outputText    value="#{msg_aplic.prt_Pessoa_pais}" />
                                                <h:panelGroup>
                                                    <h:selectOneMenu  id="pais" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.pais.codigo}" >
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemPais}" />
                                                        <a4j:support  event="onchange"   ajaxSingle="true"  reRender="form:cidade" action="#{ClienteControle.montarListaSelectItemCidade}"/>
                                                    </h:selectOneMenu>
                                                    <a4j:commandButton id="atualizar_pais" action="#{ClienteControle.montarListaSelectItemPais}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:pais"/>
                                                    <h:message for="pais" styleClass="mensagemDetalhada"/>
                                                    
                                                </h:panelGroup>
                                                <h:outputText    value="#{msg_aplic.prt_Pessoa_cidade}" />
                                                <h:panelGroup>
                                                    <h:selectOneMenu  id="cidade" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{ClienteControle.pessoaVO.cidade.codigo}" >
                                                        <a4j:support  event="onchange"   reRender="form:estado" action="#{ClienteControle.consultarEstadoPorCidade}"/>
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemCidade}" />
                                                    </h:selectOneMenu>
                                                    <a4j:commandButton id="atualizar_cidade" action="#{ClienteControle.montarListaSelectItemCidade}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:cidade"/>
                                                    <h:message for="cidade" styleClass="mensagemDetalhada"/>
                                                </h:panelGroup>
                                                <h:outputText    value="Estado" />
                                                <h:inputText id="estado"  readonly="true" size="3" maxlength="3"  onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" value="#{ClienteControle.pessoaVO.cidade.estado}" />
                                                
                                            </h:panelGrid>
                                            <h:commandButton action="#{ClienteControle.adicionarEndereco}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                                        </h:panelGrid>
                                        <h:panelGrid columns="1" width="100%" >
                                            <h:dataTable id="enderecoVO" width="100%" headerClass="subordinado"
                                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                                         value="#{ClienteControle.pessoaVO.enderecoVOs}" var="endereco">                               
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText  value="#{msg_aplic.prt_Endereco_endereco}" />
                                                    </f:facet>
                                                    <h:outputText  value="#{endereco.endereco}" />
                                                </h:column>                                                
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText  value="#{msg_aplic.prt_Endereco_bairro}" />
                                                    </f:facet>
                                                    <h:outputText  value="#{endereco.bairro}" />
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText  value="#{msg_aplic.prt_Endereco_cep}" />
                                                    </f:facet>
                                                    <h:outputText  value="#{endereco.cep}" />
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText  value="#{msg_aplic.prt_Endereco_tipoEndereco}" />
                                                    </f:facet>
                                                    <h:outputText  value="#{endereco.tipoEndereco_Apresentar}" />
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText  value="Correspondência" />
                                                    </f:facet>
                                                    <h:outputText  value="#{endereco.enderecoCorrespondencia_Apresentar}" />
                                                </h:column> 
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <h:commandButton id="editarItemVenda"  action="#{ClienteControle.editarEndereco}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>
                                                        <f:verbatim>
                                                            <h:outputText value="    "/>
                                                        </f:verbatim>
                                                        <h:commandButton id="removerItemVenda"  action="#{ClienteControle.removerEndereco}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                                    </h:panelGroup>
                                                </h:column>
                                            </h:dataTable>
                                        </h:panelGrid>
                                        
                                        <!-- fim item -->
                                         <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                                        
                                        <!-- inicio botões -->
                                       
                                        <div style="clear:both;margin-bottom:5px;">
                                            <h:commandButton id="salvar" action="#{ClienteControle.gravar}" onclick="location='javascript:window.close();'"  value="Confirmar"  alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>                                            
                                            <input onClick="location='javascript:window.close();'" type="button" name="Submit2" value="Cancelar">
                                        </div>
                                        
                                        <!-- fim botões -->
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </body>
        </html>
    </h:form>
</f:view>
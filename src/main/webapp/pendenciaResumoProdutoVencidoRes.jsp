<%-- 
    Document   : pendenciaResumoProdutoVencidoRes
    Created on : 21/08/2012, 09:18:13
    Author     : <PERSON>qui<PERSON>
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><script type="text/javascript" language="javascript" src="./script/script.js"></script></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css_pacto.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo de Cliente(s) Com Produto(s) Vencido(s)"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid columns="1" style="height=25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                        <h:outputText id="ttTotalResumo" styleClass="tituloFormulario" value="Resumo de Cliente(s) Com Produto(s) Vencido(s) Total:#{fn:length(PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs)} "/>
                    </h:panelGrid>
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block">
                            <a4j:commandButton id="exportarExcel"
                                               image="/imagens/btn_excel.png"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"
                                               value="Excel"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nomeProd_Apresentar=Nome,produto_Apresentar=Produto,vencimento_Apresentar=Vencimento"/>
                                <f:attribute name="prefixo" value="PendenciasClientes"/>
                            </a4j:commandButton>
                            <%--BOTÃO PDF--%>
                            <a4j:commandButton id="exportarPdf"
                                               style="margin-left: 8px;"
                                               image="/imagens/imprimir.png"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"
                                               value="PDF"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nomeProd_Apresentar=Nome,produto_Apresentar=Produto,vencimento_Apresentar=Vencimento"/>
                                <f:attribute name="prefixo" value="PendenciasClientes"/>
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" id="tabelaRes"
                                    value="#{PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}" rows="15" var="resumoPessoa" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{resumoPessoa.clienteVO.matricula}">
                            <f:facet name="header">
                                <h:outputText value="Matrícula" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.clienteVO.matricula}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.movProdutoVO.pessoa.nome}">
                            <f:facet name="header">
                                <h:outputText value="Nome" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.movProdutoVO.pessoa.nome}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.movProdutoVO.produto.descricao}">
                            <f:facet name="header">
                                <h:outputText value="Produto" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.movProdutoVO.produto.descricao}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.movProdutoVO.dataFinalVigencia}">
                            <f:facet name="header">
                                <h:outputText value="Vencimento" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.movProdutoVO.dataFinalVigencia_Apresentar}">
                            </h:outputText>
                        </rich:column>
                        <rich:column >
                            <f:facet name="header">
                                <h:outputText value="Opção"/>
                            </f:facet>
                            <a4j:commandButton id="visualizarCliente" image="../imagens/botaoVisualizar.png" action="#{PendenciaControleRel.irParaTelaCliente}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <f:param name="state" value="AC"/>
                            </a4j:commandButton>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>


<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="includes/imports.jsp" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
</style>
<h:panelGroup layout="block" id="panelGeralImportacao">

    <h:panelGroup layout="block" styleClass="panelObjetivo">
        <h:outputLabel value="Objetivo:" styleClass="textoObjetivo"
                       style="font-weight: bold; font-style: italic;"/>
        <h:outputLabel styleClass="textoObjetivo"
                       value=" Realizar importação de alunos através de uma planilha modelo disponibilizada para download."/>
    </h:panelGroup>

    <h:panelGroup layout="block" style="padding: 15px;">
        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="1º passo:" styleClass="passosImportacao"/>
            <a4j:commandLink target="_blank"
                             style="padding-left: 5px; font-size: 15px;"
                             value="Baixar planilha modelo"
                             oncomplete="location.href='../DownloadSV?mimeType=application/vnd.ms-excel&diretorio=modelo&relatorio=modelo_importacao_cadastro_cliente.xlsx'"/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="2º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Informe as configurações:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno"
                          style="display: inline-flex; width: 100%"
                          id="panelConfiguracoesImportacao">
                <h:panelGrid columns="2" width="100%"
                             columnClasses="colunaEsquerdaImport, colunaDireitaImport">

                    <h:outputLabel value="Consultor:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu id="consultor" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.configClienteTO.consultor}">
                        <f:selectItems value="#{ImportacaoControle.listaConsultor}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Professor TreinoWeb:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:selectOneMenu id="professorTreinoWeb" onblur="blurinput(this);"
                                     title="Caso não seja adicionado não será criado vincúlo com professor."
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.configClienteTO.professorTreinoWeb}">
                        <f:selectItems value="#{ImportacaoControle.listaProfessorTreinoWeb}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="DDD Padrão:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:inputText id="padraoDDD" styleClass="form tooltipster"
                                 title="DDD Padrão para os números sem o DDD informado. (Somente números)"
                                 maxlength="3" size="3"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.configClienteTO.padraoDDD}"/>

                    <h:outputLabel value="Validar CPF já cadastrado:"
                                   styleClass="passosImportacaoDescricao tooltipster"/>
                    <h:selectBooleanCheckbox id="validarCPFJaCadastrado" styleClass="form tooltipster"
                                             style="padding-left: 5px"
                                             title="Caso exista um aluno com o mesmo CPF o cadastro e contrato não serão importados."
                                             value="#{ImportacaoControle.configClienteTO.validarCpfCnpjJaCadastrado}"/>

                    <h:outputLabel value="Máscara campos de data:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu id="mascaraData" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.configClienteTO.mascaraDataEnum}">
                        <f:selectItems value="#{ImportacaoControle.listaSelectItemMascaraData}"/>
                    </h:selectOneMenu>

                    <!--
                    <h:outputLabel value="Importar Contratos:"
                                   styleClass="passosImportacaoDescricao tooltipster"/>
                    <h:selectBooleanCheckbox id="importarContratos" styleClass="form tooltipster"
                                             style="padding-left: 5px"
                                             value="#{ImportacaoControle.configClienteTO.importarContratos}">
                        <a4j:support event="onchange" reRender="panelConfiguracoesImportacao, panelPasso3Cliente"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectBooleanCheckbox>
                    --->

                    <c:if test="${ImportacaoControle.configClienteTO.importarContratos}">

                        <h:outputLabel value="Plano:"
                                       styleClass="passosImportacaoDescricao"/>
                        <h:selectOneMenu id="plano" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         value="#{ImportacaoControle.configClienteTO.plano}">
                            <f:selectItems value="#{ImportacaoControle.listaPlano}"/>
                            <a4j:support event="onchange" status="false"
                                         oncomplete="atualizarTempoImportacao()"/>
                        </h:selectOneMenu>

                        <h:outputLabel value="Modalidade:"
                                       styleClass="passosImportacaoDescricao"/>

                        <h:selectOneMenu id="modalidade" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         title="São apresentado somente modalidades que não utilizam turma."
                                         value="#{ImportacaoControle.configClienteTO.modalidade}">
                            <f:selectItems value="#{ImportacaoControle.listaModalidade}"/>
                            <a4j:support event="onchange" status="false"
                                         oncomplete="atualizarTempoImportacao()"/>
                        </h:selectOneMenu>

                        <h:outputLabel value="Horário:"
                                       styleClass="passosImportacaoDescricao"/>

                        <h:selectOneMenu id="horario" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         value="#{ImportacaoControle.configClienteTO.horario}">
                            <f:selectItems value="#{ImportacaoControle.listaHorarios}"/>
                            <a4j:support event="onchange" status="false"
                                         oncomplete="atualizarTempoImportacao()"/>
                        </h:selectOneMenu>

                        <h:outputLabel value="Dias Carência:"
                                       styleClass="passosImportacaoDescricao"/>
                        <h:inputText id="diasCarencia" styleClass="form tooltipster"
                                     title="Número de dias de carência"
                                     maxlength="2" size="2"
                                     onkeypress="bloquearEnter()"
                                     style="padding-left: 5px"
                                     value="#{ImportacaoControle.configClienteTO.diasCarencia}"/>

                        <h:outputLabel value="Gerar parcelas de acordo com a duração:"
                                       styleClass="passosImportacaoDescricao tooltipster"/>
                        <h:selectBooleanCheckbox id="gerarParcelasDeAcordoDuracao"
                                                 styleClass="form tooltipster"
                                                 style="padding-left: 5px"
                                                 title="Será gerado uma parcela para cada mês do contrato.<br/>Caso não marcado será gerado somente uma parcela com o valor total do plano."
                                                 value="#{ImportacaoControle.configClienteTO.gerarParcelasDeAcordoDuracao}"/>

                        <h:outputLabel value="Gerar Parcelas Pagas:"
                                       styleClass="passosImportacaoDescricao"/>

                        <h:selectOneMenu id="importacaoParcelasSituacaoEnum" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         value="#{ImportacaoControle.configClienteTO.importacaoParcelasSituacaoEnum}">
                            <f:selectItems value="#{ImportacaoControle.listaImportacaoParcelasSituacao}"/>
                        </h:selectOneMenu>

                    </c:if>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGroup layout="block" styleClass="panelPassos" id="panelPasso3Cliente">
            <h:outputLabel value="3º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Preencher a planilha seguindo as seguintes regras:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno" style="font-size: 15px;">
                       <span>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Campos Obrigatórios para importar o cadastro: <b>ID_EXTERNO e NOME</b></li>
                                <c:if test="${ImportacaoControle.configClienteTO.importarContratos}">
                                    <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Campos Obrigatórios para importar o contrato: <b>DT_INICIO_CONTRATO, DT_FIM_CONTRATO e VALOR_TOTAL_CONTRATO.</b></li>
                                </c:if>
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Coluna Estado Civil informar a letra:
                                  <ol style="padding-top: 5px;"><b>"S"</b> para Solteiro(a)</ol>
                                  <ol><b>"C"</b> para Casado(a)</ol>
                                  <ol><b>"A"</b> para Amasiado(a)</ol>
                                  <ol><b>"V"</b> para Viúvo(a)</ol>
                                  <ol><b>"D"</b> para Divorciado(a)</ol>
                                  <ol><b>"P"</b> para Separado(a)</ol>
                                  <ol><b>"U"</b> para União estável</ol>
                                </li>
                            </ul>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px">
                                    Coluna Sexo biológico informar a letra:
                                    <ol style="padding-top: 5px;"><b>"M"</b> para Masculino</ol>
                                    <ol><b>"F"</b> para Feminino</ol>
                                </li>
                            </ul>
                           <c:if test="${ImportacaoControle.configSesc}">
                               <ul style="padding: 5px;margin: 0;">
                                   <li i="col-necessidade-especial" style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Coluna Necessidades Especiais informar a sigla:
                                       <ol style="padding-top: 5px;"><b>"AH"</b> para Altas Habilidades</ol>
                                       <ol><b>"AU"</b> para Auditiva</ol>
                                       <ol><b>"FI"</b> para Física</ol>
                                       <ol><b>"IN"</b> para Intelectual (Mental)</ol>
                                       <ol><b>"MU"</b> para Múltiplas</ol>
                                       <ol><b>"VI"</b> para Visual</ol>
                                       <ol><b>"ND"</b> para Não Declarada</ol>
                                   </li>
                               </ul>
                           </c:if>
                       </span>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="4º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Fazer o Upload da planilha baixada e realizar a importação:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelUploadFileCliente" style="padding-top: 5px; padding-left: 30px">
            <rich:fileUpload
                    fileUploadListener="#{ImportacaoControle.uploadArquivoCliente}"
                    immediateUpload="true" id="imagemModeloUpload"
                    acceptedTypes="xls,xlsx" allowFlash="false"
                    listHeight="58px"
                    cancelEntryControlLabel="Cancelar"
                    addControlLabel="Adicionar"
                    clearControlLabel="Remover"
                    clearAllControlLabel="Remover Todos"
                    doneLabel="Concluído"
                    sizeErrorLabel="Limite de tamanho atingido"
                    uploadControlLabel="Carregar"
                    transferErrorLabel="Erro na transferência"
                    stopControlLabel="Parar"
                    stopEntryControlLabel="Parar"
                    progressLabel="Carregando"
                    maxFilesQuantity="1">
                <a4j:support event="onerror" reRender="panelBotoesImportacaoCliente"
                             action="#{ImportacaoControle.removerArquivo}"/>
                <a4j:support event="onupload" reRender="panelBotoesImportacaoCliente"/>
                <a4j:support event="onuploadcomplete" reRender="panelBotoesImportacaoCliente"/>
                <a4j:support event="onclear" reRender="panelBotoesImportacaoCliente, panelUploadFileCliente"
                             action="#{ImportacaoControle.removerArquivo}"/>
            </rich:fileUpload>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" id="panelBotoesImportacaoCliente"
                  styleClass="panelBotoesImportacao">
        <a4j:commandLink id="btnImportar" value="Ler Arquivo"
                         style="padding-left: 15px"
                         onclick="atualizarTempoImportacao()"
                         rendered="#{ImportacaoControle.apresentarImportar}"
                         action="#{ImportacaoControle.processarArquivoCliente}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         title="Processar Importação de Dados"
                         reRender="panelGeralModalConfirmarImportacao, formModImpo"
                         styleClass="botoes nvoBt"/>
    </h:panelGroup>
</h:panelGroup>

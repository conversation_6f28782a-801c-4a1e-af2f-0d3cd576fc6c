<%-- 
    Document   : impressaoLancamentos
    Created on : 22/07/2011, 08:27:56
    Author     : carla
--%>
<%@include file="/include_imports.jsp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<c:set var="moduloSession" value="1" scope="session" />
<link href="css/checklist.css" rel="stylesheet" type="text/css">
<link href="${contextoFinan}/css/otimize.css" rel="stylesheet" type="text/css">
<f:view >
    <h:form id="form">
        <title>
            <h:outputText value="#{msg_aplic.prt_Finan_Lancamentos_tituloForm}"/>
        </title>
        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="background-color:#FFFFFF;">
            <h:commandLink
                onclick="window.print();return false;">
                <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0px; width: 65;"/>
            </h:commandLink>
        </h:panelGrid>
        <!-- TABELA GERAL -->
        <table width="100%" height="100%" border="0" cellpadding="4" cellspacing="0">
            <!-- LINHA DE CIMA-->
            <tr>
                <!-- COLUNA ESQUERDA-->
                <td align="left" valign="top" colspan="7">
                    <table width="100%" style="background-color:#FFFFFF;">
                        <tr>
                            <td width="10%">
                                <a4j:mediaOutput element="img" id="fotoRelatorio"  style="width:200px;height:56px"  cacheable="false"
                                                 createContent="#{MovContaControle.paintFotoImpressao}"
                                                 value="#{ImagemData}"
                                                 mimeType="image/jpeg" >
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                </a4j:mediaOutput>

                            </td>
                            <td width="20%" class="colunaEsquerda">
                                <h:outputText  id="nomeEmpresa" styleClass="tituloCampos" value="#{MovContaControle.filtrosLancamentos.empresaRelatorio.nome}"/>
                                <br>
                                <h:outputText  id="enderecoEmpresa" styleClass="tituloCampos" value="#{MovContaControle.filtrosLancamentos.empresaRelatorio.endereco}"/>
                                <h:outputText  id="numeroEmpresa" styleClass="tituloCampos" value="#{MovContaControle.filtrosLancamentos.empresaRelatorio.numero}  "/>
                                <h:outputText  id="setorEmpresa" styleClass="tituloCampos" value="#{MovContaControle.filtrosLancamentos.empresaRelatorio.setor}"/>
                                <br>
                                <h:outputText  id="cidadeEmpresa" styleClass="tituloCampos" value="#{MovContaControle.filtrosLancamentos.empresaRelatorio.cidade.nome} "/>
                                <h:outputText  id="ufEmpresa" styleClass="tituloCampos" value="#{MovContaControle.filtrosLancamentos.empresaRelatorio.cidade.estado.sigla}"/>
                            </td>
                            <td width="40%" colspan="2" class="colunaCentralizada">
                                <h:outputText styleClass="tituloFormularioVerdeTopo"  value="#{msg_aplic.prt_Finan_Lancamentos_tituloForm}"/>
                            </td>
                            <td width="40%" class="colunaDireita" colspan="2">
                                <h:outputText  id="cabecalho1" styleClass="tituloCampos" value="#{msg_aplic.prt_Impressao_Lancamento_cabecalho1}"/>
                                <br>
                                <h:outputText  id="cabecalho2" styleClass="tituloCampos" value="#{msg_aplic.prt_Impressao_Lancamento_cabecalho2}"/>
                                <br>
                                <h:outputText  id="cabecalho3" styleClass="tituloCampos" value="#{msg_aplic.prt_Impressao_Lancamento_cabecalho3}"/>
                            </td>
                        </tr>
                        <tr>
                            <td class="colunaCentralizada" width="100%" colspan="7">
                                <h:outputText  id="tituloFiltros" styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Impressao_Lancamento_filtros}: "/>
                                <h:outputText  id="filtros" styleClass="tituloCamposNegrito" value="#{MovContaControle.filtros}"/>
                            </td>
                        </tr>
                    </table>
                    <table width="100%" id="tabelaBens"  border="1" cellpadding="2" cellspacing="0" class="tabela">
                        <tr>
                            <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamentos_vencimento}"/></th>
                            <c:if test="${MovContaControle.filtrosLancamentos.qtdEmpresasSelecionadas > 1}">
                                <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamentos_empresaOrigem}"/></th>
                            </c:if>
                            <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamentos_favorecido}"/></th>
                            <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="Valor #{MovPagamentoControle.empresaLogado.moeda}"/></th>
                            <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamentos_descricao}"/></th>
                            <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamentos_quitacao}"/></th>
                            <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamento_contaSemPonto}"/></th>
                            <c:if test="${MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto}">
                                <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="Plano de Contas"/></th>
                                <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="Centro de Custos"/></th>
                            </c:if>

                            <th nowrap="nowrap" bgcolor="#CCCCCC" class="campos"><h:outputText styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamentos_tipo}"/></th>
                        </tr>
                        <c:forEach var="movConta" varStatus="index" items="${MovContaControle.listaConsulta}">
                            <tr>
                                <td class="camposTabela" width="10%">${movConta.dataVencimento_Apresentar}</td>
                                <c:if test="${MovContaControle.filtrosLancamentos.qtdEmpresasSelecionadas > 1}">
                                    <td class="camposTabela" width="5%">${movConta.empresaVO.nome}</td>
                                </c:if>
                                <td class="camposTabela" width="20%">${movConta.pessoaVO.nome}</td>
                                <td class="camposTabela" width="5%">${movConta.valor_Apresentar}</td>
								<td class="camposTabela" width="20%">${movConta.descricao}</td>
                                <td class="camposTabela" width="10%">${movConta.dataQuitacao_Apresentar}&nbsp;</td>
                                <td class="camposTabela" width="15%">${movConta.contaVO.descricao}</td>
                                <c:if test="${MovContaControle.filtrosLancamentos.agruparPorPlanoDeContasCentroCusto}">
                                    <td class="camposTabela" width="15%">${movConta.nomePlanoConta}</td>
                                    <td class="camposTabela" width="15%">${movConta.nomeCentroCusto}</td>
                                </c:if>
                                <td class="camposTabela" width="5%">${movConta.tipoOperacaoLancamento_ApresentarResumido}</td>
                            </tr>
                        </c:forEach>
                    </table>
                    <table width="100%">
                        <tr>
                            <td width="80%">
                                <h:outputText  styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamentos_totalRegistros} "/>
                                <h:outputText  id="totalRegistros" styleClass="tituloCamposVerdeGrande" value="#{MovContaControle.confPaginacao.numeroTotalItens}"/>
                            </td>
                            <td width="30%">
                                <h:outputText  styleClass="tituloCamposNegrito" value="#{msg_aplic.prt_Finan_Lancamentos_total} "/>
                                <h:outputText id="totalValor" value="#{MovContaControle.valorTotal_Apresentar}" styleClass="#{MovContaControle.mudarCorTotal}"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </h:form>
</f:view>

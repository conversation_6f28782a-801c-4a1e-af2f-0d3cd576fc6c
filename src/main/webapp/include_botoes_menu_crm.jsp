<div>
    <ul class="btnazul">
        <li>
            <p class="btnleft"></p>

            <p class="btnmiddle">
                <a4j:commandLink id="linkTelaInicialCRM" action="crm" value="Inicial"/>
            </p>

            <p class="btnright"></p>
        </li>

        <%--<li>--%>
        <%--<p class="btnleft"></p>--%>

        <%--<p class="btnmiddle">--%>
        <%--<a4j:commandLink id="linkTelaAntigaCRM" action="telaAntigoCRM" value="Antigo CRM"/>--%>
        <%--</p>--%>

        <%--<p class="btnright"></p>--%>
        <%--</li>--%>

        <li>
            <p class="btnleft"></p>

            <p class="btnmiddle">
                <a4j:commandLink id="linkNovaTelaCRM" action="#{MetaCRMControle.consultarMetas}"
                                 value="Meta Di�ria"
                                 reRender="superiorPrincipalCRM, colunaCRMEsquerda, colunaCRMCentro, colunaCRMDireita"
                                 oncomplete="mostrarTodasTelas();adicionarPlaceHolderCRM();"/>
            </p>

            <p class="btnright"></p>
        </li>


        <li>
            <rich:dropDownMenu>
                <f:facet name="label">
                    <h:panelGroup>
                        Cadastros <i class="fa-icon-caret-down"></i>
                    </h:panelGroup>
                </f:facet>

                <rich:menuItem id="botaoCarteiras" submitMode="ajax" value="Carteiras"
                               action="#{LoginControle.abrirCarteirasCRM}"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.organizadorCarteira}"/>
                <rich:menuItem submitMode="ajax"
                               id="botaoGrupoColaborador" ajaxSingle="true" value="Grupo Colaborador"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.grupoColaborador}"
                               oncomplete="abrirPopup('grupoColaboradorCons.jsp', 'GrupoColaborador', 800, 595);"/>
                <rich:menuItem submitMode="ajax"
                               id="botaoEvento" ajaxSingle="true" value="Evento"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.evento}"
                               oncomplete="abrirPopup('eventoCons.jsp', 'Evento', 800, 595);"/>
                <rich:menuItem submitMode="ajax"
                               id="botaoObjecao" ajaxSingle="true" value="Obje��o"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.objecao}"
                               oncomplete="abrirPopup('objecaoCons.jsp', 'Objecao', 800, 595);"/>
                <rich:menuItem submitMode="ajax"
                               id="botaoModeloMensagem" ajaxSingle="true" value="Modelo de Mensagem"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.modeloMensagem}"
                               oncomplete="abrirPopup('modeloMensagemCons.jsp', 'modeloMensagem', 800, 595);"/>
                <rich:menuItem submitMode="ajax"
                               id="botaoFeriado" ajaxSingle="true" value="Feriado"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.feriado}"
                               oncomplete="abrirPopup('feriadoCons.jsp', 'Feriado', 800, 595);"/>
                <rich:menuItem submitMode="ajax"
                               id="botaoTextoPadrao" ajaxSingle="true" value="Texto Padr�o"
                               oncomplete="abrirPopup('textoPadraoCons.jsp', 'Texto Padr�o', 800, 595);"/>

                <rich:menuItem submitMode="ajax"
                               id="botaoMetaExtra"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.crmExtraCRM}"
                               ajaxSingle="true" value="Meta Extra"
                               oncomplete="abrirPopup('crmExtraCons.jsp', 'Meta Extra', 1000, 650);"/>
            </rich:dropDownMenu>
        </li>

        <li>
            <rich:dropDownMenu>
                <f:facet name="label">
                    <h:panelGroup>
                        Opera��es <i class="fa-icon-caret-down"></i>
                    </h:panelGroup>
                </f:facet>

                <%--<rich:menuItem submitMode="ajax"--%>
                <%--id="botaoFechamentoDia"--%>
                <%--value="Fechar Meta"--%>
                <%--ajaxSingle="true"--%>
                <%--action="#{AberturaMetaControle.inicializarConsultaFechamentoDia}"--%>
                <%--reRender="formFecharAberturaMetaCons"--%>
                <%--oncomplete="Richfaces.showModalPanel('panelFecharAberturaMetaCons')"/>--%>

                <rich:menuItem submitMode="ajax"
                               id="botaoConfirmarAgendados"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.agenda}"
                               ajaxSingle="true" value="Marcar Comparecimento"
                               oncomplete="abrirPopup('confirmarComparecimentoAgendadoForm.jsp', 'ConfirmarAgendados', 780, 595);"
                               action="#{AgendaControle.novo}"/>

                <rich:menuItem submitMode="ajax"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.malaDireta}"
                               id="botaoMalaDireta" ajaxSingle="true" action="#{MalaDiretaControle.abrirMailing}"
                               value="Mailing"
                               oncomplete="abrirPopup('mailing.jsp', 'mailing', 1000, 650);"/>

            </rich:dropDownMenu>
        </li>


        <li>
            <rich:dropDownMenu>
                <f:facet name="label">
                    <h:panelGroup>
                        Consultas <i class="fa-icon-caret-down"></i>
                    </h:panelGroup>
                </f:facet>

                <rich:menuItem submitMode="ajax"
                               id="botaoHistoricoContato"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.historicoContato}"
                               ajaxSingle="true" value="Consulta Hist�rico Contato"
                               action="#{HistoricoContatoControle.inicializarDadosRealizarContato}"
                               oncomplete="abrirPopup('historicoContatoCons.jsp', 'historicoContatoCons', 765, 595);"/>

                <%--<rich:menuItem submitMode="ajax"--%>
                <%--id="botaoConsultarFechamentoDia"--%>
                <%--value="Consulta Metas"--%>
                <%--action="#{AberturaMetaControle.inicializarConsultaAberturaDia}"--%>
                <%--reRender="formAberturaMetaCons"--%>
                <%--oncomplete="Richfaces.showModalPanel('panelAberturaMetaCons')"/>--%>

                <%--<rich:menuItem submitMode="ajax"--%>
                <%--id="botaoConsultarTotalizadorMetas"--%>
                <%--value="Totalizador de Metas"--%>
                <%--rendered="#{LoginControle.permissaoAcessoMenuVO.totalizadorMeta}"--%>
                <%--oncomplete="abrirPopupTopoPagina('totalizadorMeta.jsp', 'TotalizadorMeta', 816, 595);"/>--%>

                <rich:menuItem submitMode="ajax"
                               id="botaoPassivo" ajaxSingle="true"
                               action="#{PassivoControle.realizarlimpezaCamposMensagem}"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.passivo}"
                               value="Receptivo"
                               oncomplete="abrirPopup('passivoCons.jsp', 'Passivo', 850, 595);"/>

                <rich:menuItem submitMode="ajax"
                               id="botaoIndicacao"
                               action="#{IndicacaoControle.realizarlimpezaCamposMensagem}"
                               ajaxSingle="true"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.indicacao}"
                               value="Indica��o"
                               oncomplete="abrirPopup('indicacaoCons.jsp', 'indicacao', 800, 595);"/>

                <rich:menuItem submitMode="ajax"
                               id="botaoAgendamentos"
                               ajaxSingle="true"
                               value="Agendamentos"
                               oncomplete="abrirPopup('agendamentos.jsp', 'indicacao', 1200, 750);"/>

                <rich:menuItem submitMode="ajax"
                               id="botaoContatosApp"
                               ajaxSingle="true"
                               value="Contatos App"
                               oncomplete="abrirPopup('contatosApp.jsp', 'contatosApp', 1000, 650);"/>
            </rich:dropDownMenu>
        </li>

        <h:panelGroup layout="block" rendered="#{LoginControle.permissaoAcessoMenuVO.businessIntelligenceCRM}">
            <li>
                <p class="btnleft"></p>

                <p class="btnmiddle">
                    <a4j:commandLink id="linkBICRM"
                                     action="#{BusinessIntelligenceCRMControle.carregarBusinessIntelligence}"
                                     value="Business Intelligence"/>
                </p>

                <p class="btnright"></p>
            </li>
        </h:panelGroup>
    </ul>
</div>

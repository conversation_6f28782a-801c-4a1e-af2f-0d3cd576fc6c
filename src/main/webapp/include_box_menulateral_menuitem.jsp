<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ page import="controle.basico.FuncionalidadeControle" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<style>
    .divNovidade{
        background: #ff6e1e;
        font-weight: 600;
        border-top-left-radius: 15px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        border-top-right-radius: 15px;
        display: flex;
        height: 20px;
        padding-left: 7px;
        padding-right: 7px;
        position: relative;
        right: 10px;
    }

    .new {
        position: relative;
        top: -4px;
        color: #ffffff;
        font-size: 12px;
    }
</style>

<h:panelGroup layout="block" styleClass="grupoMenuItemNomeContainer">
    <a4j:commandLink value="#{funcionalidade.funcionalidadeSistemaEnum.descricaoMenulateral}"
                     title="#{funcionalidade.funcionalidadeSistemaEnum.descricao}"
                     style="max-width: 201px;"
                     styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido MENU-1#{funcionalidade.name}"
                     actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                     oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                     action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                     reRender="#{funcionalidade.funcionalidadeSistemaEnum.reRenderElement}">
        <f:attribute name="funcionalidade" value="#{funcionalidade.name}" />
        <f:param name="funcionalidadeAberta" value="#{funcionalidade.funcionalidadeSistemaEnum.name}"/>
    </a4j:commandLink>

</h:panelGroup>

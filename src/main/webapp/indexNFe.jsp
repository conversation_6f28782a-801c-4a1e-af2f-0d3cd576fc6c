<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request"/>
<input type="hidden" name="urlPath" value="${urlPath}"/>

<c:set var="moduloSession" value="1" scope="session"/>

<html>
<head>
    <script type="text/javascript" language="javascript" src="./script/basico.js"></script>

    <link href="css_pacto.css" rel="stylesheet" type="text/css">
    <link href="css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
    <link href="css/otimize.css" rel="stylesheet" type="text/css">
    <link href="css/ce.css" rel="stylesheet" type="text/css">
    <link href="css/estudio.css" rel="stylesheet" type="text/css">

    <jsp:include page="include_head.jsp"/>

    <%@taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
</head>
<body>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="gerenciadorNotas" prependId="false">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
            <!-- Topo e menu superior -->
            <c:if test="${MenuControle.apresentarTopo}">
                <tr>
                    <td height="77" align="left" valign="top" class="bgtop">
                        <jsp:include page="include_top.jsp" flush="true"/>
                    </td>
                </tr>

                <tr>
                    <td height="48" align="left" valign="top" class="bgmenu">
                        <jsp:include page="/nfe/include_menuNFe.jsp" flush="true"/>
                    </td>
                </tr>
            </c:if>

            <tr>
                <td align="left" valign="top">
                    <table width="100%" height="100%" align="center" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="206" align="center" valign="top" style="padding-left: 10px; padding-top: 7px;" id="menuLateralNFe">
                                <jsp:include page="nfe/include_filtros_consultaNotas.jsp"/>
                                <jsp:include page="nfe/include_box_descricaoNFe.jsp"/>
                            </td>
                            <td align="top" valign="top" height="100%" style="padding:7px 7px 0px 10px;" id="gridNotasNFe">
                                <jsp:include page="nfe/include_grid_consulta_notas.jsp"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <td height="93" align="left" valign="top" class="bgrodape">
                <jsp:include page="include_rodape.jsp" flush="true"/>
            </td>
        </table>
    </h:form>

    <c:if test="${LoginControle.apresentarHotjar}">
        <script>
            function hotjarParams(empresa, usuario, perfil) {
                hj('tagRecording', ['Empresa', empresa]);
                hj('tagRecording', ['Username', usuario]);
                hj('tagRecording', ['Perfil', perfil]);
            }

            (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                //hotjarParams('${LoginControle.empresa.nome}', '${LoginControle.usuario.username}', '${LoginControle.perfilAcesso.nome}');
                h._hjSettings={hjid:2500298,hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
            })(window,document,'//static.hotjar.com/c/hotjar-','.js?sv=');

        </script>
    </c:if>
</f:view>
</body>
</html>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_HorarioTurma_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_HorarioTurma_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{HorarioTurmaControle.horarioTurmaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_turma}" />
                    <h:panelGroup>
                        <h:inputText  id="turma" required="true" size="10" maxlength="10" styleClass="camposObrigatorios" value="#{HorarioTurmaControle.horarioTurmaVO.turma}" />
                        <h:message for="turma" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_identificador}" />
                    <h:panelGroup>
                        <h:inputText  id="identificador" required="true" size="20" maxlength="20" styleClass="camposObrigatorios" value="#{HorarioTurmaControle.horarioTurmaVO.identificador}" />
                        <h:message for="identificador" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_diaSemana}" />
                    <h:panelGroup>
                        <h:selectOneRadio>
                            <f:selectItems  value="#{HorarioTurmaControle.listaSelectItemNivelTurma}" />
                        </h:selectOneRadio>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_horaInicial}" />
                    <h:inputText  id="horaInicial" size="5" maxlength="5" styleClass="campos" value="#{HorarioTurmaControle.horarioTurmaVO.horaInicial}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_horaFinal}" />
                    <h:inputText  id="horaFinal" size="5" maxlength="5" styleClass="campos" value="#{HorarioTurmaControle.horarioTurmaVO.horaFinal}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_professor}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="professor" styleClass="camposObrigatorios" value="#{HorarioTurmaControle.horarioTurmaVO.professor.codigo}" >
                            <f:selectItems  value="#{HorarioTurmaControle.listaSelectItemProfessor}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_professor" action="#{HorarioTurmaControle.montarListaSelectItemProfessor}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:professor"/>
                        <h:message for="professor" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_ambiente}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="ambiente" styleClass="camposObrigatorios" value="#{HorarioTurmaControle.horarioTurmaVO.ambiente.codigo}" >
                            <f:selectItems  value="#{HorarioTurmaControle.listaSelectItemAmbiente}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_ambiente" action="#{HorarioTurmaControle.montarListaSelectItemAmbiente}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:ambiente"/>
                        <h:message for="ambiente" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_nivelTurma}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="nivelTurma" styleClass="camposObrigatorios" value="#{HorarioTurmaControle.horarioTurmaVO.nivelTurma.codigo}" >
                            <f:selectItems  value="#{HorarioTurmaControle.listaSelectItemNivelTurma}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_nivelTurma" action="#{HorarioTurmaControle.montarListaSelectItemNivelTurma}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:nivelTurma"/>
                        <h:message for="nivelTurma" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_situacao}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="situacao" styleClass="camposObrigatorios" value="#{HorarioTurmaControle.horarioTurmaVO.situacao}" >
                            <f:selectItems  value="#{HorarioTurmaControle.listaSelectItemSituacaoHorarioTurma}" />
                        </h:selectOneMenu>
                        <h:message for="situacao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_nrMaximoAluno}" />
                    <h:inputText  id="nrMaximoAluno"  onkeypress="return Tecla(event);" size="10" maxlength="10" styleClass="campos" value="#{HorarioTurmaControle.horarioTurmaVO.nrMaximoAluno}" />

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_liberadoMarcacaoApp}" />
                    <h:selectBooleanCheckbox id="liberadoMarcacaoApp" styleClass="campos " value="#{TurmaControle.horarioTurmaVO.liberadoMarcacaoApp}"/>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_ativo}" />
                    <h:selectBooleanCheckbox id="liberadoMarcacaoApp" styleClass="campos " value="#{TurmaControle.horarioTurmaVO.ativo}"/>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{HorarioTurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{HorarioTurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{HorarioTurmaControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="salvar" action="#{HorarioTurmaControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="excluir"onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{HorarioTurmaControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="consultar" immediate="true" action="#{HorarioTurmaControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:turma").focus();
</script>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<h:panelGrid id="panelListaHistoricoVinculo" columns="1" width="100%">
    <rich:dataTable id="listaHistoricoVinculo" width="100%" headerClass="consulta"
                    rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                    value="#{HistoricoVinculoControle.listaHistoricoVinculo}"
                    rendered="#{!empty HistoricoVinculoControle.listaHistoricoVinculo}"
                    rows="10" var="historicoVinculo">
        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_dataRegistro}" />
            </f:facet>
            <h:outputText id="dataRegistro" value="#{historicoVinculo.dataRegistro_Apresentar}" />
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_colaborador}" />
            </f:facet>
            <h:outputText id="colaborador" value="#{historicoVinculo.colaborador.pessoa.nome}" />
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_tipoColaborador}" />
            </f:facet>
            <h:outputText id="tipoColaborador" value="#{historicoVinculo.tipoColaborador_Apresentar}" />
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_tipoHistoricoVinculo}" />
            </f:facet>
            <h:outputText id="tipoHistoricoVinculo" value="#{historicoVinculo.tipoHistoricoVinculo_Apresentar}" />
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_origem}"/>
            </f:facet>
            <h:outputText id="origemHistoricoVinculo" value="#{historicoVinculo.origem}"/>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoVinculo_respAlteracao}"/>
            </f:facet>
            <h:outputText id="respAlteracaoHistoricoVinculo" value="#{historicoVinculo.nomeUsuario}"/>
        </rich:column>
    </rich:dataTable>
    <rich:datascroller align="center" for="listaHistoricoVinculo"
                       id="scResultadoConsultalistaHistoricoVinculo"
                       rendered="#{!empty HistoricoVinculoControle.listaHistoricoVinculo}" />
</h:panelGrid>

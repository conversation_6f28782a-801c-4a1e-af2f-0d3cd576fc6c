<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="H" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="ui" uri="http://richfaces.org/a4j" %>


<h:panelGroup rendered="#{GestaoVendasOnlineControle.linksCampanhaSelecionado}" layout="block" styleClass="dadosvendas" id="divLinksCampanha">

    <div class="row">
        <div class="column col-md-12">
            <h3 class="texto-size-14 negrito cinzaEscuro">Link para os
                planos <h:commandLink styleClass="neww divNovidadee"
                                      onclick="abrirPopupMaximizada('https://pactosolucoes.com.br/versoes/agenda-de-aulas-no-vendas-online/','Novidade');"
                                      value="NOVIDADE"/></h3>
        </div>
        <div style="margin-top: 1vh" class="column col-md-8">
            <span class="texto-size-14 cinza">Tipo do Link: </span>
            <h:panelGroup layout="block" styleClass="cb-container"
                          style="margin-left: 9vh;">
                <h:selectOneMenu
                        value="#{GestaoVendasOnlineControle.tipoLinkPlano}">
                    <a4j:support event="onchange" reRender="divLinksCampanha"/>
                    <f:selectItems
                            value="#{GestaoVendasOnlineControle.listaTipoLinkPlano}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </div>


        <c:if test="${GestaoVendasOnlineControle.tipoLinkPlano == 1}">

            <div class="column col-md-12">
                <h4 class="texto-size-14 cinza">Este link leva o usuário para a
                    tela
                    de escolha de planos, com opção de todos os planos marcados
                    como
                    <b>site</b> desta unidade. </h4>
            </div>
        </c:if>


        <c:if test="${GestaoVendasOnlineControle.tipoLinkPlano == 2}">
            <div class="column col-md-12">
                <h4 class="texto-size-14 cinza">Este link leva o usuário direto
                    para
                    a tela de compra, com o plano já escolhido, selecionado por
                    você
                    na lista abaixo.</br>
                    Obs. Caso queira seguir diretamente para tela de compra com
                    o
                    plano preenchido e o cupom tambem já preenchido, basta
                    preenche-lo e atualizar o link.</h4>
            </div>

            <div style="margin-top: 1vh" class="column col-md-8">
                <span class="texto-size-14 cinza">Plano: </span>
                <h:panelGroup layout="block" styleClass="cb-container"
                              style="margin-left: 9vh;">
                    <h:selectOneMenu
                            value="#{GestaoVendasOnlineControle.plano}">
                        <a4j:support event="onchange"
                                     reRender="planoespecificoCampanha"/>
                        <f:selectItems
                                value="#{GestaoVendasOnlineControle.planos}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </div>

            <h:panelGroup id="cupomDescontoCampanha" styleClass="column col-md-8"
                          layout="block" style="margin-top: 1vh;">
                <h:outputText styleClass="texto-size-14 cinza"
                              value="Cupom Desconto: "/>
                <h:inputText styleClass="form"
                             style="height: 3.6vh; font-size: 14px; border-radius: 3px; margin-left: 1.3vh;"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             value="#{GestaoVendasOnlineControle.paramCupom}"/>
                <a4j:commandLink style="margin-left: 5px"
                                 styleClass="botoes nvoBt btSec"
                                 value="Limpar Cupom"
                                 action="#{GestaoVendasOnlineControle.limparAtualizarlinkPlanoCupom}"
                                 reRender="planoespecificoCampanha, cupomDescontoCampanha">
                </a4j:commandLink>
            </h:panelGroup>

        </c:if>

        <c:if test="${GestaoVendasOnlineControle.tipoLinkPlano == 3}">
            <div class="column col-md-12">
                <h4 class="texto-size-14 cinza">Este link leva o usuário direto
                    para
                    a tela de compra, com uma categoria de planos selecionada por
                    você
                    na lista abaixo.</br>
                    Obs. Somente os planos da categoria selecionada
                    irão aparecer.</h4>
            </div>

            <div style="margin-top: 1vh" class="column col-md-8">
                <span class="texto-size-14 cinza">Categoria: </span>
                <h:panelGroup layout="block" styleClass="cb-container"
                              style="margin-left: 9vh;">
                    <h:selectOneMenu
                            value="#{GestaoVendasOnlineControle.categoria}">
                        <a4j:support event="onchange"
                                     reRender="categoriaEspecificaCampanha"/>
                        <f:selectItems
                                value="#{GestaoVendasOnlineControle.categorias}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </div>

        </c:if>

        <div style="margin-top: 1vh" class="column col-md-8">
            <span class="texto-size-14 cinza">Evento: </span>
            <h:panelGroup layout="block" styleClass="cb-container"
                          style="#{GestaoVendasOnlineControle.styleComboEventoPlano}">
                <h:selectOneMenu
                        value="#{GestaoVendasOnlineControle.codigoEventoPlano}">
                    <f:selectItems
                            value="#{GestaoVendasOnlineControle.listaEventos}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </div>



        <h:panelGroup id="pgGerarLinkPlano" styleClass="column col-md-8" layout="block" style="margin-top: 1vh;">
            <a4j:commandLink styleClass="botoes nvoBt tooltipster"
                             value="Gerar Link"
                             title="Gerar novo link"
                             action="#{GestaoVendasOnlineControle.gerarLinkPlanoAbaCampanha}"
                             oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                             reRender="form:tableListaLinksCampanhaPlano, mdlMensagemGenerica">
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup id="tableListaLinksCampanhaPlano" styleClass="column col-md-8" layout="block" style="margin-top: 1vh; margin-bottom: 2vh;">
            <div style="overflow-x: auto;overflow-y: auto; width: 100%; max-height:1000px;">
                <table class="googleAnalytics tblHeaderLeft semZebra">
                <thead><tr>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Data cadastro"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Plano"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Categoria"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Cupom de desconto"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Evento"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Status"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Link"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Link agenda"/></th>
                </tr></thead><tbody>
                 <c:if test="${empty GestaoVendasOnlineControle.listaLinksCampanhaPlano}">
                     <td>
                        <h:outputText  styleClass="texto-size-14 cinza" value="nenhum link criado ainda!"></h:outputText>
                     </td>
                 </c:if>
                    <a4j:repeat
                            value="#{GestaoVendasOnlineControle.listaLinksCampanhaPlano}"
                            rowKeyVar="index"
                            var="objLink">
                        <tr>
                            <td class="texto-size-12 cinza"> <h:outputText value="#{objLink.dataCadastro_apresentar}"></h:outputText> </td>
                            <td class="texto-size-12 cinza"> <h:outputText value="#{objLink.planoVO.descricao}"></h:outputText> </td>
                            <td class="texto-size-12 cinza"> <h:outputText value="#{objLink.categoriaVO.nome}"></h:outputText> </td>
                            <td class="texto-size-12 cinza"> <h:outputText value="#{objLink.cupomDesconto}"></h:outputText> </td>
                            <td class="texto-size-12 cinza"> <h:outputText value="#{objLink.eventoVO.descricao}"></h:outputText> </td>
                            <td class="texto-size-12 cinza"> <h:outputText value="#{objLink.statusEvento}"></h:outputText> </td>
                            <td>
                                <a4j:commandLink
                                        onclick="copiar('#{objLink.link}')">
                                    <i class="fa-icon-link tooltipster"
                                       style="font-size: 16px"
                                       title="Clique para copiar para a área de transferência"></i>
                                </a4j:commandLink>
                            </td>
                            <td>
                                <a4j:commandLink
                                        rendered="#{objLink.linkAgenda != null}"
                                        onclick="copiar('#{objLink.linkAgenda}')">
                                    <i class="fa-icon-link tooltipster"
                                       style="font-size: 16px"
                                       title="Clique para copiar para a área de transferência"></i>
                                </a4j:commandLink>
                            </td>

                        </tr>
                    </a4j:repeat>
            </tbody>
            </table>
            </div>
        </h:panelGroup>

        <div class="column col-md-12">
            <h3 class="texto-size-14 negrito cinzaEscuro">Link para os
                produtos<h:commandLink styleClass="neww divNovidadee"
                                       onclick="abrirPopupMaximizada('https://pactosolucoes.com.br/versoes/aulas-avulsas-no-vendas-online/','Novidade');"
                                       value="NOVIDADE"/></h3>
        </div>
        <div style="margin-top: 1vh" class="column col-md-8">
            <span class="texto-size-14 cinza">Tipo do Link: </span>
            <h:panelGroup layout="block" styleClass="cb-container"
                          style="margin-left: 9vh;">
                <h:selectOneMenu
                        value="#{GestaoVendasOnlineControle.tipoLinkProduto}">
                    <a4j:support event="onchange" reRender="divLinksCampanha"/>
                    <f:selectItems
                            value="#{GestaoVendasOnlineControle.listaTipoLinkProduto}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </div>


        <c:if test="${GestaoVendasOnlineControle.tipoLinkProduto == 1}">
            <div class="column col-md-12">
                <h4 class="texto-size-14 cinza">Este link leva o usuário para a
                    tela
                    de escolha de produtos, com opção de todos os produtos
                    marcados
                    como <b>Apresentar no Vendas Online</b>.</h4>
            </div>

        </c:if>

        <c:if test="${GestaoVendasOnlineControle.tipoLinkProduto == 2}">

            <div class="column col-md-12">
                <h4 class="texto-size-14 cinza">Este link leva o usuário direto
                    para
                    a tela de compra, com o produto já escolhido, selecionado
                    por
                    você na lista abaixo.</h4>
            </div>

            <div style="margin-top: 1vh" class="column col-md-8">
                <span class="texto-size-14 cinza">Produto: </span>
                <h:panelGroup layout="block" styleClass="cb-container"
                              style="margin-left: 4vh;">
                    <h:selectOneMenu
                            value="#{GestaoVendasOnlineControle.produto}">
                        <a4j:support event="onchange"
                                     reRender="produtoespecificoCampanha"/>
                        <f:selectItems
                                value="#{GestaoVendasOnlineControle.produtos}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </div>


            <h:panelGroup styleClass="column col-md-8" layout="block"
                          style="margin-top: 2vh;">
                <h:outputText styleClass="texto-size-14 cinza"
                              value="Quantidade: "/>
                <h:inputText styleClass="form"
                             size="5"
                             style="height: 3.6vh; font-size: 14px; border-radius: 3px; margin-left: 1.4vh;"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             onkeypress="return mascara(this.form, this.id, '99999999', event);"
                             onkeyup="somenteNumeros(this);"
                             value="#{GestaoVendasOnlineControle.qtdProduto}">
                </h:inputText>
            </h:panelGroup>
        </c:if>

        <div style="margin-top: 1vh" class="column col-md-8">
            <span class="texto-size-14 cinza">Evento: </span>
            <h:panelGroup layout="block" styleClass="cb-container"
                          style="#{GestaoVendasOnlineControle.styleComboEventoProduto}">
                <h:selectOneMenu
                        value="#{GestaoVendasOnlineControle.codigoEventoProduto}">
                    <f:selectItems
                            value="#{GestaoVendasOnlineControle.listaEventos}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </div>



        <h:panelGroup id="pgGerarLinkProduto" styleClass="column col-md-8" layout="block" style="margin-top: 1vh;">
            <a4j:commandLink styleClass="botoes nvoBt tooltipster"
                             value="Gerar Link"
                             title="Gerar novo link para produto"
                             action="#{GestaoVendasOnlineControle.gerarLinkProdutoAbaCampanha}"
                             oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                             reRender="form:tableListaLinksCampanhaProduto, mdlMensagemGenerica">
            </a4j:commandLink>
        </h:panelGroup>


        <h:panelGroup id="tableListaLinksCampanhaProduto" styleClass="column col-md-8" layout="block" style="margin-top: 1vh; margin-bottom: 2vh;">
            <div style="overflow-x: auto;overflow-y: auto; width: 100%; max-height:1000px;">
                <table class="googleAnalytics tblHeaderLeft semZebra">
                <thead><tr>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Data cadastro"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Produto"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Evento"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Status"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Link"/></th>
                    <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Link agenda"/></th>
                </tr></thead><tbody>
            <c:if test="${empty GestaoVendasOnlineControle.listaLinksCampanhaProduto}">
                <td>
                    <h:outputText  styleClass="texto-size-14 cinza" value="nenhum link criado ainda!"></h:outputText>
                </td>
            </c:if>
            <a4j:repeat
                    value="#{GestaoVendasOnlineControle.listaLinksCampanhaProduto}"
                    rowKeyVar="index"
                    var="objLinkProduto">
                <tr>
                    <td class="texto-size-12 cinza"> <h:outputText value="#{objLinkProduto.dataCadastro_apresentar}"></h:outputText> </td>
                    <td class="texto-size-12 cinza"> <h:outputText value="#{objLinkProduto.produtoVO.descricao}"></h:outputText> </td>
                    <td class="texto-size-12 cinza"> <h:outputText value="#{objLinkProduto.eventoVO.descricao}"></h:outputText> </td>
                    <td class="texto-size-12 cinza"> <h:outputText value="#{objLinkProduto.statusEvento}"></h:outputText> </td>
                    <td>
                        <a4j:commandLink
                                onclick="copiar('#{objLinkProduto.link}')">
                            <i class="fa-icon-link tooltipster"
                               style="font-size: 16px"
                               title="Clique para copiar para a área de transferência"></i>
                        </a4j:commandLink>
                    </td>
                    <td>
                        <a4j:commandLink
                                rendered="#{objLinkProduto.linkAgenda != null}"
                                onclick="copiar('#{objLinkProduto.linkAgenda}')">
                            <i class="fa-icon-link tooltipster"
                               style="font-size: 16px"
                               title="Clique para copiar para a área de transferência"></i>
                        </a4j:commandLink>
                    </td>

                </tr>
            </a4j:repeat>
            </tbody>
            </table>
            </div>
        </h:panelGroup>

    </div>

    <div class="row">
        <div class="column col-md-12">
            <h3 class="texto-size-14 negrito cinzaEscuro">Link para a Cadastros de Visitantes Site</h3>
            <h4 class="texto-size-14 cinza">Este link leva o usuário para a tela
                de
                cadastros .</h4>
        </div>

        <div style="margin-top: 1vh" class="column col-md-8">
            <span class="texto-size-14 cinza">Evento: </span>
            <h:panelGroup layout="block" styleClass="cb-container"
                          style="margin-left: 1vh;">
                <h:selectOneMenu
                        value="#{GestaoVendasOnlineControle.codigoEventoVisitante}">
                    <f:selectItems
                            value="#{GestaoVendasOnlineControle.listaEventos}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </div>
        <h:panelGroup id="pgGerarLinkVisitante" styleClass="column col-md-8" layout="block" style="margin-top: 2vh;">
            <a4j:commandLink styleClass="botoes nvoBt tooltipster"
                             value="Gerar Link"
                             title="Gerar novo link"
                             action="#{GestaoVendasOnlineControle.gerarLinkVisitanteAbaCampanha}"
                             oncomplete="#{GestaoVendasOnlineControle.mensagemNotificar}"
                             reRender="form:tableListaLinksCampanhaVisitante, mdlMensagemGenerica">
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup id="tableListaLinksCampanhaVisitante" styleClass="column col-md-8" layout="block" style="margin-top: 1vh; margin-bottom: 2vh;">
            <div style="overflow-x: auto;overflow-y: auto; width: 100%; max-height:1000px;">
                <table class="googleAnalytics tblHeaderLeft semZebra">
                    <thead><tr>
                        <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Data cadastro"/></th>
                        <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Evento"/></th>
                        <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Status"/></th>
                        <th><h:outputText styleClass="texto-size-12 cinza negrito" value="Link"/></th>
                    </tr></thead><tbody>
                <c:if test="${empty GestaoVendasOnlineControle.listaLinksCampanhaVisitante}">
                    <td>
                        <h:outputText  styleClass="texto-size-14 cinza" value="nenhum link criado ainda!"></h:outputText>
                    </td>
                </c:if>
                <a4j:repeat
                        value="#{GestaoVendasOnlineControle.listaLinksCampanhaVisitante}"
                        rowKeyVar="index"
                        var="objLinkVi">
                    <tr>
                        <td class="texto-size-12 cinza"> <h:outputText value="#{objLinkVi.dataCadastro_apresentar}"></h:outputText> </td>
                        <td class="texto-size-12 cinza"> <h:outputText value="#{objLinkVi.eventoVO.descricao}"></h:outputText> </td>
                        <td class="texto-size-12 cinza"> <h:outputText value="#{objLinkVi.statusEvento}"></h:outputText> </td>
                        <td>
                            <a4j:commandLink
                                    onclick="copiar('#{objLinkVi.link}')">
                                <i class="fa-icon-link tooltipster"
                                   style="font-size: 16px"
                                   title="Clique para copiar para a área de transferência"></i>
                            </a4j:commandLink>
                        </td>


                    </tr>
                </a4j:repeat>
                </tbody>
                </table>
            </div>
        </h:panelGroup>



    </div>

</h:panelGroup>
<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="${contexto}/css/ce.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript">var contexto = '${contexto}';</script>
<script type="text/javascript" language="javascript" src="${contexto}/script/ajuda.js"></script>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_CategoriaProduto_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_CategoriaProduto_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-um-produto-de-estoque/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">


        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel>
                    <rich:tab id="abaData" styleClass="titulo3SemDecoracao" label="Dados">


                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText value="#{msg_aplic.prt_CategoriaProduto_codigo}"/>
                            <h:panelGroup>
                                <h:inputText id="codigo" size="10" maxlength="10" readonly="true"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="camposSomenteLeitura"
                                             value="#{CategoriaProdutoControle.categoriaProdutoVO.codigo}"/>
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText value="#{msg_aplic.prt_CategoriaProduto_descricao}"/>
                            <h:panelGroup>
                                <h:inputText id="descricao" size="45" maxlength="45" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{CategoriaProdutoControle.categoriaProdutoVO.descricao}"/>
                                <h:message for="descricao" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText value="#{msg_aplic.prt_bloquearProdutosEmAbertoCategoria}"/>
                            <h:selectBooleanCheckbox value="#{CategoriaProdutoControle.categoriaProdutoVO.bloquearAcessoSeProdutoAberto}"/>

                            <h:outputText value="Avaliação física"/>
                            <h:selectBooleanCheckbox value="#{CategoriaProdutoControle.categoriaProdutoVO.avaliacaoFisica}"/>
                            <h:outputText value="Forma de Pagamento"/>
                            <h:panelGroup >
                                <h:selectOneMenu id="teste" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{CategoriaProdutoControle.categoriaProdutoVO.formaPagamento.codigo}">
                                    <f:selectItems value="#{CategoriaProdutoControle.listaSelectItemFormaPagamento}"/>
                                </h:selectOneMenu>

                                <h:message for="teste" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                        </h:panelGrid>

                    </rich:tab>
                    <rich:tab id="abaComissao" rendered="#{CategoriaProdutoControle.apresentarAbaComissao}"
                              style="height:55vh;"
                              styleClass="titulo3SemDecoracao" label="Comissão">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <h:panelGrid id="gridComissao" columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <c:if test="${CategoriaProdutoControle.apresentarEmpresaComissao}">
                                    <h:outputText value="Empresa"/>
                                    <h:selectOneMenu id="empresa" onfocus="focusinput(this);" styleClass="form"
                                                     value="#{CategoriaProdutoControle.comissaoVO.empresa.codigo}">
                                        <f:selectItems value="#{CategoriaProdutoControle.listaEmpresas}"/>
                                    </h:selectOneMenu>
                                </c:if>

                                <h:outputText value="Vigência Inicial"/>
                                <h:panelGroup>
                                    <rich:calendar id="vigenciainicio"
                                                   value="#{CategoriaProdutoControle.comissaoVO.vigenciaInicio}"
                                                   enableManualInput="true"
                                                   popup="true"
                                                   inputSize="7"
                                                   datePattern="MM/yyyy"
                                                   showApplyButton="false"
                                                   style="width:200px;"
                                                   inputClass="MMyyyy"
                                                   showFooter="false"/>
                                    <h:message for="vigenciainicio" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="Vigência Final"/>
                                <h:panelGroup>
                                    <rich:calendar id="vigenciafinal"
                                                   value="#{CategoriaProdutoControle.comissaoVO.vigenciaFinal}"
                                                   enableManualInput="true"
                                                   popup="true"
                                                   inputSize="7"
                                                   datePattern="MM/yyyy"
                                                   showApplyButton="false"
                                                   style="width:200px"
                                                   inputClass="MMyyyy"
                                                   showFooter="false"/>
                                    <h:message for="vigenciafinal" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="Porcentagem"/>
                                <h:panelGroup>
                                    <h:inputText id="percEsp" size="4" maxlength="4" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{CategoriaProdutoControle.comissaoVO.porcentagem}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                    <h:message for="percEsp" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="Valor Fixo"/>
                                <h:panelGroup>
                                    <h:inputText id="fixoAg" size="4" maxlength="4" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{CategoriaProdutoControle.comissaoVO.valorFixo}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                    <h:message for="fixoAg" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                            </h:panelGrid>
                            <a4j:commandButton id="addComissao" value="#{msg_bt.btn_adicionar}"
                                               action="#{CategoriaProdutoControle.adicionarComissao}"
                                               image="./imagens/botaoAdicionar.png" accesskey="7" styleClass="botoes"
                                               reRender="gridComissao,tblComissao,pnlMensagem"/>
                        </h:panelGrid>


                        <h:panelGrid id="tblComissao" columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{CategoriaProdutoControle.categoriaProdutoVO.comissaoCategoriaProdutos}"
                                         var="comissaoProduto">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Código"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.codigo}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Vl. Fixo (R$)"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.valorFixo_Apresentar}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Porc. (%)"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.porcentagemApresentar}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Vig. Início"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.vigenciaInicio_Apresentar}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Vig. Final"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.vigenciaFinal_Apresentar}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Empresa"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.empresa}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Opçoes"/>
                                    </f:facet>
                                    <a4j:commandButton id="editarItemComissao" reRender="gridComissao,tblComissao"
                                                       ajaxSingle="true" immediate="true"
                                                       action="#{CategoriaProdutoControle.editarComissao}"
                                                       style="vertical-align: middle" value="#{msg_bt.btn_editar}"
                                                       image="./imagens/botaoEditar.png" styleClass="botoes"/>

                                    <a4j:commandButton id="removerItemComissao" reRender="gridComissao,tblComissao"
                                                       ajaxSingle="true" immediate="true"
                                                       action="#{CategoriaProdutoControle.removerComissao}"
                                                       style="vertical-align: middle" value="#{msg_bt.btn_excluir}"
                                                       image="./imagens/botaoRemover.png" styleClass="botoes"/>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>
                </rich:tabPanel>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="pnlMensagem" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton id="icCategoriaSuc" rendered="#{CategoriaProdutoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton id="icCategoriaFal" rendered="#{CategoriaProdutoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgCategoria" styleClass="mensagem" value="#{CategoriaProdutoControle.mensagem}"/>
                            <h:outputText id="msgCategoriaDet" styleClass="mensagemDetalhada" value="#{CategoriaProdutoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{CategoriaProdutoControle.novo}"
                                               value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                               styleClass="botoes nvoBt btSec"/>
                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                            <a4j:commandButton id="salvar" action="#{CategoriaProdutoControle.gravar}"
                                               value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2"
                                               styleClass="botoes nvoBt"/>
                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                 oncomplete="#{CategoriaProdutoControle.msgAlert}" action="#{CategoriaProdutoControle.confirmarExcluir}"
                                                 value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>
                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>
                            <a4j:commandButton id="consultar" immediate="true"
                                               action="#{CategoriaProdutoControle.inicializarConsultar}"
                                               value="#{msg_bt.btn_voltar_lista}" alt="#{msg_bt.btn_voltar_lista}"
                                               accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="15px"/>
                            <a4j:commandLink action="#{CategoriaProdutoControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             style="display: inline-block; padding: 8px 15px;"
                                             styleClass="botoes nvoBt btSec" title="Visualizar Log">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>

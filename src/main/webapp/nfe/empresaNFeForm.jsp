<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head><script type="text/javascript" language="javascript" src="../script/script.js"></script></head>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Edição de Empresa"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoNFe.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Edição de Empresa"/>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText styleClass="tituloCampos" value="Código:"/>
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                     value="#{EmpresaNFeControle.empresaNFeVO.id_Empresa}"/>
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Ativa:"/>
                    <h:panelGroup rendered="true">
                        <h:selectBooleanCheckbox id="situacao"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.ativa}"/>
                        <h:message for="situacao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Chave:"/>
                    <h:panelGroup>
                        <h:inputText id="chave" size="45" maxlength="100" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                     value="#{EmpresaNFeControle.empresaNFeVO.chave}"/>
                        <h:message for="chave" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Chave ZW:"/>
                    <h:panelGroup>
                        <h:inputText id="chaveZW" size="45" maxlength="250" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.chaveZW}"/>
                        <h:message for="chaveZW" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Empresa ZW:"/>
                    <h:panelGroup>
                        <h:inputText id="empresaZW" size="10" maxlength="10" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.empresaZW}"/>
                        <h:message for="empresaZW" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="*Razão Social:"/>
                    <h:panelGroup>
                        <h:inputText id="razaoSocial" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.razaoSocial}"/>
                        <h:message for="razaoSocial" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="*Nome Fantasia:"/>
                    <h:panelGroup>
                        <h:inputText id="nomeFantasia" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.nomeFantasia}"/>
                        <h:message for="nomeFantasia" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="*CPF/CNPJ:"/>
                    <h:panelGroup>
                        <h:inputText id="cpfCnpj" size="45" maxlength="14" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.cpf_cnpj}"/>
                        <h:message for="cpfCnpj" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="CNPJ Matriz:"/>
                    <h:panelGroup>
                        <h:inputText id="cnpjMatriz" size="45" maxlength="14" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.cnpjMatriz}"/>
                        <h:message for="cnpjMatriz" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Razão Social da Matriz:"/>
                    <h:panelGroup>
                        <h:inputText id="razaoSocialMatriz" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.razaoSocialMatriz}"/>
                        <h:message for="razaoSocialMatriz" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Inscrição Municipal:"/>
                    <h:panelGroup>
                        <h:inputText id="inscricaoMunicipal" size="20" maxlength="20" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.inscricaoMunicipal}"/>
                        <h:message for="inscricaoMunicipal" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="CCM:"/>
                    <h:panelGroup>
                        <h:inputText id="ccm" size="20" maxlength="20" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.ccm}"/>
                        <h:message for="ccm" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Usuário Inscrição Municipal:"/>
                    <h:panelGroup>
                        <h:inputText id="usuarioInscricaoMunicipal" autocomplete="off" size="4" maxlength="16" onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="form"
                                       value="#{EmpresaNFeControle.empresaNFeVO.usuarioInscricaoMunicipal}"/>
                        <h:message for="usuarioInscricaoMunicipal" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Senha Inscrição Municipal:"/>
                    <h:panelGroup>
                        <h:inputSecret id="senhaInscricaoMunicipal" autocomplete="off" size="4" maxlength="16" onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="form" redisplay="true"
                                       value="#{EmpresaNFeControle.empresaNFeVO.senhaInscricaoMunicipal}"/>
                        <h:message for="senhaInscricaoMunicipal" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Inscrição Estadual:"/>
                    <h:panelGroup>
                        <h:inputText id="inscricaoEstadual" maxlength="20" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.inscricaoEstadual}"/>
                        <h:message for="inscricaoEstadual" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Alíquota Simples:"/>
                    <h:panelGroup>
                        <h:inputText id="aliquotaSimples" maxlength="20" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.aliquotaSimples}"/>
                        <h:message for="aliquotaSimples" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="CFOP:"/>
                    <h:panelGroup>
                        <h:inputText id="cfop" size="30" maxlength="30" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.CFOP}"/>
                        <h:message for="cfop" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="ID_Csc:"/>
                    <h:panelGroup>
                        <h:inputText id="idCsc" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.idCsc}"/>
                        <h:message for="idCsc" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="CSC:"/>
                    <h:panelGroup>
                        <h:inputText id="csc" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.csc}"/>
                        <h:message for="csc" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="*DDD:"/>
                    <h:panelGroup>
                        <h:inputText id="ddd" size="2" maxlength="2" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.ddd_telefone}"/>
                        <h:message for="ddd" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="*Telefone:"/>
                    <h:panelGroup>
                        <h:inputText id="telefone" size="9" maxlength="9" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.telefone}"/>
                        <h:message for="telefone" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Email:"/>
                    <h:panelGroup>
                        <h:inputText id="email" size="30" maxlength="80" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.email}"/>
                        <h:message for="email" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Logradouro:"/>
                    <h:panelGroup>
                        <h:inputText id="logradouro" size="30" maxlength="30" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.logradouro}"/>
                        <h:message for="logradouro" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Complemento:"/>
                    <h:panelGroup>
                        <h:inputText id="complemento" size="30" maxlength="30" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.complemento}"/>
                        <h:message for="complemento" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Bairro:"/>
                    <h:panelGroup>
                        <h:inputText id="bairro" size="20" maxlength="20" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.bairro}"/>
                        <h:message for="bairro" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Número:"/>
                    <h:panelGroup>
                        <h:inputText id="numero" size="9" maxlength="9" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.numEndereco}"/>
                        <h:message for="cep" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="CEP:"/>
                    <h:panelGroup>
                        <h:inputText id="cep" size="9" maxlength="9" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.CEP}"/>
                        <h:message for="cep" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Logotipo (FORMATO jpeg):"/>
                        <h:panelGrid id="panelLogotipo" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_LogoTipo_tituloForm} (FORMATO jpeg)"/>
                            </f:facet>
                            <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada">

                                <a4j:mediaOutput element="img" id="foto"  style="width:120px;height:160px "  cacheable="false"
                                                 createContent="#{EmpresaNFeControle.paintFotoSemPesquisarNoBanco}"  value="#{ImagemData}" mimeType="image/jpeg" >
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="120"/>
                                    <f:param name="altura" value="160"/>
                                </a4j:mediaOutput>
                                <rich:fileUpload listHeight="0"
                                                 listWidth="150"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{EmpresaNFeControle.upload}"
                                                 maxFilesQuantity="1"
                                                 addControlLabel="Adicionar"
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 100 MB"
                                                 progressLabel="Enviando"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar"
                                                 id="upload"
                                                 immediateUpload="true"
                                                 autoclear="true"
                                                 acceptedTypes="jpeg">

                                    <a4j:support event="onuploadcomplete" ajaxSingle="true" reRender="panelLogotipo" />
                                    <a4j:support event="oncomplete" ajaxSingle="true" reRender="panelLogotipo, form:panelErroMensagem" />
                                </rich:fileUpload>
                                <h:outputText value=""/>
                            </h:panelGrid>
                        </h:panelGrid>


                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Certificado Digital:"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <rich:fileUpload
                                id="layoutUpload"
                                fileUploadListener="#{EmpresaNFeControle.uploadLayoutListener}"
                                maxFilesQuantity="1"
                                immediateUpload="false" acceptedTypes="p12, cer, pfx"
                                allowFlash="false" listHeight="58px"
                                onfileuploadcomplete="document.getElementById('form:arquivoLayoutCarregado').value = 'true';"
                                cancelEntryControlLabel="Cancelar"
                                addControlLabel="Adicionar"
                                clearControlLabel="Remover"
                                clearAllControlLabel="Remover Todos"
                                doneLabel="Concluído"
                                sizeErrorLabel="O arquivo solicitado excede o tamanho limite"
                                uploadControlLabel="Carregar"
                                transferErrorLabel="Erro na tranferência do arquivo"
                                stopControlLabel="Parar"
                                stopEntryControlLabel="Parar"
                                progressLabel="Carregando">
                        </rich:fileUpload>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="*Senha do Certificado:"/>
                    <h:panelGroup>
                        <h:inputSecret id="senhaCertificado" size="10" maxlength="25" onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="form" redisplay="true"
                                       value="#{EmpresaNFeControle.empresaNFeVO.senhaCertificado}"/>
                        <h:message for="senhaCertificado" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="*Cidade:"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="cidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{EmpresaNFeControle.empresaNFeVO.municipio.id_Municipio}">
                            <f:selectItems value="#{EmpresaNFeControle.listaSelectMunicipiosHomologados}"/>
                        </h:selectOneMenu>
                        <h:message for="cidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>



                    <h:outputText styleClass="tituloCampos" value="Regime Especial de Tributação:"/>
                    <h:panelGroup rendered="true">
                        <h:selectOneMenu id="regEspTrib" value="#{EmpresaNFeControle.empresaNFeVO.CRT}">
                            <f:selectItems value="#{EmpresaNFeControle.listaCRT}"/>
                        </h:selectOneMenu>
                        <h:message for="regEspTrib" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Optante do Simples Nacional:"/>
                    <h:panelGroup rendered="true">
                        <h:selectBooleanCheckbox id="simplesNac"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.optateSimplesNacional}"/>
                        <h:message for="simplesNac" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Incentivador Cultural:"/>
                    <h:panelGroup rendered="true">
                        <h:selectBooleanCheckbox id="incCultural"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.incentivadorCultural}"/>
                        <h:message for="incCultural" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Imprimir Imposto na Nota:"/>
                    <h:panelGroup rendered="true">
                        <h:selectBooleanCheckbox id="impImposto"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.imprimirImposto}"/>
                        <h:message for="impImposto" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Enviar Email:"/>
                    <h:panelGroup rendered="true">
                        <h:selectBooleanCheckbox id="envEmail"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.enviarEmail}"/>
                        <h:message for="envEmail" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Informações para Fisco:"/>
                    <h:panelGroup rendered="true">
                        <h:inputText id="informacaoFisco" size="50" maxlength="50" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.informacaoFisco}"/>
                        <h:message for="informacaoFisco" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="CST Pis:"/>
                    <h:panelGroup rendered="true">
                        <h:inputText id="CSTPis" size="10" maxlength="50" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.CSTPis}"/>
                        <h:message for="CSTPis" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="CST Cofins:"/>
                    <h:panelGroup rendered="true">
                        <h:inputText id="CSTCofins" size="10" maxlength="50" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.CSTCofins}"/>
                        <h:message for="CSTCofins" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Usar XML da Prefeitura:"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="usarXMLPrefeitura"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.usarXMLPrefeitura}"/>
                        <h:message for="usarXMLPrefeitura" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Usar XML da Prefeitura (Manipulado):"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="usarXMLManipulado"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.usarXMLManipulado}"/>
                        <h:message for="usarXMLManipulado" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Usar XML Manipulado para NFCe:"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="usarXMLManipuladoNFCe" value="#{EmpresaNFeControle.empresaNFeVO.usarXMLManipuladoNFCe}"/>
                        <h:message for="usarXMLManipuladoNFCe" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Série NFCe:"/>
                    <h:panelGroup>
                        <h:inputText id="serieNFCe" size="30" maxlength="30" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaNFeControle.empresaNFeVO.serieNFCe}"/>
                        <h:message for="serieNFCe" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Habilitar NFCe:"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="usaModuloNFCe"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.usaModuloNFCe}"/>
                        <h:message for="usaModuloNFCe" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Enviar apenas um RPS por vez:"/>
                    <h:panelGroup rendered="true">
                        <h:selectBooleanCheckbox id="enviarApenasUmRPSPorVez"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.enviarApenasUmRPSPorVez}"/>
                        <h:message for="enviarApenasUmRPSPorVez" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Processo de homologação:"/>
                    <h:panelGroup rendered="true">
                        <h:selectBooleanCheckbox id="processoHomologacao"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.processoHomologacao}"/>
                        <h:message for="processoHomologacao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Utilizar descrição da nota:"/>
                    <h:panelGroup rendered="true">
                        <h:selectBooleanCheckbox id="naoUsarDescricaoItemRPS"
                                                 value="#{EmpresaNFeControle.empresaNFeVO.naoUsarDescricaoItemRPS}"/>
                        <h:message for="naoUsarDescricaoItemRPS" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Diretório de Execução:"
                                  rendered="#{empty EmpresaNFeControle.empresaNFeVO.identificadorExecutavel}"/>
                    <h:panelGroup rendered="#{empty EmpresaNFeControle.empresaNFeVO.identificadorExecutavel}">
                        <h:selectOneMenu id="dirExec" value="#{EmpresaNFeControle.empresaNFeVO.identificadorExecutavel}">
                            <f:selectItems value="#{EmpresaNFeControle.listaExecutaveisDelphi}"/>
                        </h:selectOneMenu>
                        <h:message for="dirExec" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{EmpresaNFeControle.sucesso}" image="../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{EmpresaNFeControle.erro}" image="../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{EmpresaNFeControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{EmpresaNFeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{EmpresaNFeControle.novo}"
                                             value="#{msg_bt.btn_novo}" image="../imagens/botaoNovo.png"
                                             alt="#{msg.msg_novo_dados}" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="salvar" action="#{EmpresaNFeControle.gravar}"
                                             value="#{msg_bt.btn_gravar}"
                                             image="../imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}"
                                             styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="excluir" onclick="return confirm('Confirma exclusão dos dados?');"
                                             action="#{EmpresaNFeControle.excluir}" value="#{msg_bt.btn_excluir}"
                                             image="../imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}"
                                             styleClass="botaoExcluir"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="consultar" immediate="true"
                                             action="#{EmpresaNFeControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_consultar}" image="../imagens/botaoConsultar.png"
                                             alt="#{msg.msg_consultar_dados}" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script type="text/javascript">
    document.getElementById("form:descricao").focus();
</script>

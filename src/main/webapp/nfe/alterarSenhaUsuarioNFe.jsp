<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<head><script type="text/javascript" language="javascript" src="../script/script.js"></script></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Alterar Senha"/>
    </title>
    <h:form id="formLogin">
        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
            <f:facet name="header">
                <f:verbatim>
                    <div align="center">
                        <table width="100%" height="45" border="0" cellpadding="0" cellspacing="0"
                               background="../imagens/topoDemoReduzidoBack.png">
                            <tr>
                                <td align="left" valign="top" bgcolor="#FFFFFF"
                                    background="../imagens/topoDemoReduzidoBack.png">
                                    <img src="../imagens/nfe/topoReduzidoNFe.png" width="375" height="22">
                                </td>
                            </tr>
                        </table>
                    </div>
                </f:verbatim>
            </f:facet>

            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_AlterarSenhaSenha_titulo_cli}"/>
                    </f:facet>

                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita"
                                 width="100%">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_NomeUsuario_cli}"/>
                        <h:inputText id="nomeusuario" readonly="true"
                                     size="30" maxlength="30" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{UsuarioNFeControle.usuarioLogado.nome}"/>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Username_cli}"/>
                        <h:inputText id="username" size="14" maxlength="20"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" value="#{UsuarioNFeControle.usuarioLogado.usuario}"
                                     disabled="true"/>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_SenhaAtual_cli}"/>
                        <h:inputSecret id="senhaatual" size="14" maxlength="14" onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="form"
                                       value="#{UsuarioNFeControle.senhaAtual}"/>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_NovaSenha_cli}"/>
                        <h:inputSecret id="novasenha" size="14" maxlength="14" onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="form"
                                       value="#{UsuarioNFeControle.senha1}"/>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConfirmarSenha_cli}"/>
                        <h:inputSecret id="confirmarnovasenha" size="14" maxlength="14" onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="form"
                                       value="#{UsuarioNFeControle.senha2}"/>
                    </h:panelGrid>

                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{UsuarioNFeControle.sucesso}" image="../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{UsuarioNFeControle.erro}" image="../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{UsuarioNFeControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{UsuarioNFeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <a4j:commandButton id="confirmarsenha" type="submit" styleClass="botoes"
                                           value="#{msg_bt.btn_alterarsenha_cli}"
                                           image="../imagens/botaoAlterarSenha.png"
                                           action="#{UsuarioNFeControle.alterarSenhaUsuarioNFe}"
                                           oncomplete="#{UsuarioNFeControle.javaScriptFecharPopUpAlteracaoSenha}"
                                           reRender="panelMensagemErro">
                        </a4j:commandButton>
                    </h:panelGrid>

                </h:panelGrid>
            </h:panelGrid>

            <f:facet name="footer">
                <f:subview id="footer">
                    <f:verbatim>
                        <c:import url="./rodape.htm"/>
                    </f:verbatim>
                </f:subview>
            </f:facet>

        </h:panelGrid>
    </h:form>
</f:view>
<script type="text/javascript">
    document.getElementById("formLogin:username").focus();
</script>

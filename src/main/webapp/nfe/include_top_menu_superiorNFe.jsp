<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<table border="0" cellspacing="0" cellpadding="0">
    <tr>
        <td align="left" valign="top">
            <div style="padding:0px 6px; background-image: url(beta/imagens/bg-topo-menu-15.png); border-top: none; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px; overflow: hidden;">
                <h:panelGroup>
                    <ul class="abatop" style="margin-top: -2px;">

                        <%--li><h:outputLink style="color:#fff;text-decoration:none;"
                                          value="#{SuperControle.urlWikiRaiz}/P%C3%A1gina_principal"
                                          title="Wiki do ZillyonWeb" target="_blank">WikiPacto</h:outputLink></li>

                        <li><img src="images/aba_sep.gif"></li>

                        <li>
                            <h:outputLink id="linksolicitacao" style="color:white;"  target="_blank" value="#{SuporteControle.urlSolicitacaoProperties}">
                                Suporte
                            </h:outputLink>
                        </li>

                        <li><img src="images/aba_sep.gif"></li>

                        <li>
                            <%@include file="../includes/include_logout.jsp" %>
                        </li--%>

                        <li></li>

                        <li></li>
                        <li>
                            <rich:dropDownMenu>
                                <f:facet name="label">
                                    <h:panelGroup>
                                        <i class="fa-icon-question-sign fa-icon-2x"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <rich:menuItem submitMode="none"
                                               icon="/faces/beta/imagens/mini-icons-wiki.png">
                                    <h:outputLink id="linkwiki"
                                                  style="color:#555;" target="_blank" value='#{SuperControle.urlWikiRaiz}/ZillyonWeb:Modulo_Nfs-e'>
                                        WikiPacto
                                    </h:outputLink>
                                </rich:menuItem>
                                <rich:menuItem submitMode="none"
                                               icon="/faces/beta/imagens/mini-icons-suport.png">
                                    <h:outputLink id="linksolicitacao"
                                                  style="color:#555;"  target="_blank" value="#{SuporteControle.urlSolicitacaoProperties}">
                                        Suporte
                                        <a4j:support event="onclick" action="#{SuporteControle.prepareUserService}" oncomplete="#{SuporteControle.loginService}"/>
                                    </h:outputLink>
                                </rich:menuItem>

                            </rich:dropDownMenu>
                        </li>
                        <li>
                            <rich:dropDownMenu>
                                <f:facet name="label">
                                    <h:panelGroup>
                                        <i class="fa-icon-cog fa-icon-2x"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <rich:menuItem id="btnLogout" submitMode="none" value="Sair"
                                               icon="/faces/beta/imagens/mini-icons-sair.png"
                                               onclick="document.location.href='#{LogoutControle.redirectLogout}'" />

                            </rich:dropDownMenu>
                        </li>
                    </ul>
                </h:panelGroup>
            </div>
        </td>
    </tr>
</table>
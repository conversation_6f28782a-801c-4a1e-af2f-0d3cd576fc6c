<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="/includes/imports.jsp" %>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request"/>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<div class="box" style="width: 100%">
    <div class="boxmiddle" style="border-left: 1px #000 solid; border-right: 1px #000 solid; border-bottom: 1px #000 solid; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; padding: 10px">
        <div style="text-align:center; padding-left: 10%">
            <h:panelGrid>
                <c:choose>
                    <c:when test="${LoginControle.empresa.codigo > 0}">
                        <a4j:mediaOutput element="img" id="fotoEmpresa" style="width:120px;height:160px;"
                                         cacheable="false"
                                         rendered="#{empty LoginControle.empresa.fotoKey}"
                                         title="#{SuperControle.keyComURLConexao}"
                                         createContent="#{LoginControle.paintFoto}" value="#{ImagemData}"
                                         mimeType="image/jpeg">
                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                            <f:param name="largura" value="120"/>
                            <f:param name="altura" value="160"/>
                        </a4j:mediaOutput>
                        <h:graphicImage rendered="#{not empty LoginControle.empresa.fotoKey}" 
                                        width="120" height="160"                                        
                                        style="width:120px;height:160px"
                                        url="#{LoginControle.paintFotoDaNuvem}">                            
                        </h:graphicImage>
                    </c:when>
                    <c:otherwise>
                        <div>
                            <h:graphicImage width="143" url="/beta/imagens/pacto-nfe-topo.png"/>
                        </div>
                    </c:otherwise>
                </c:choose>
            </h:panelGrid>
        </div>

        <div style="padding-left: 10%; font-family:Arial, Helvetica, sans-serif; font-size: 8pt;font-weight: bold;">
            <h:panelGrid style="font-family:Arial, Helvetica, sans-serif; font-size: 8pt;font-weight: bold;"
                         columns="2">
                <h:outputText value="#{LoginControle.usuario.nome}"/>
                <a4j:commandLink rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}" type="button"
                                 onclick="abrirPopup('#{root}/faces/nfe/alterarSenhaUsuarioNFe.jsp', 'AlterarSenha', 410, 350);"
                                 title="Alterar senha" styleClass="text2"
                                 style="valign:middle;cursor:pointer;">
                    <h:graphicImage style="border:none;" value="/images/icon_chave.png"/>
                </a4j:commandLink>
                <h:outputText value="IP.: #{LoginControle.ipCliente}"/>
            </h:panelGrid>

            <h:panelGroup layout="block" style="font-family:Arial, Helvetica, sans-serif;
                          font-size: 8pt;font-weight: bold;">
                <h:commandLink
                    value="#{SuperControle.versaoSistemaNFSe}"
                    title="#{SuperControle.dataVersaoComoString}"/>
            </h:panelGroup>
        </div>
    </div>
</div>
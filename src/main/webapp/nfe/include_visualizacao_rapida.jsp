<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/imports.jsp" %>

<h:panelGroup id="preVisualizar" style="display: #{NotaFiscalDeServicoControle.preVisualizar};">
    <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0"
           style="padding: 10px; background-color: #e6e6e6;">

        <tr>
            <td align="left" valign="top" style="padding-bottom: 5px;">
                <div style="clear: both;" class="text">
                    <p style="margin-bottom: 6px;">
                        <img src="../images/arrow2.gif" width="16" height="16"
                             style="vertical-align: middle; margin-right: 6px;" alt="arrow2">
                        <h:outputText style="font-weight: bold" value="Pré-Visualização"/>

                        <a4j:commandButton id="btnFecharPreVisualizacao"
                                           style="float: right;"
                                           image="/imagens/close.png"
                                           action="#{NotaFiscalDeServicoControle.fecharPreVisualizacao}"
                                           reRender="preVisualizar"/>
                    </p>

                    <div class="sep" style="margin: 4px 0 5px 0;">
                        <img src="../images/shim.gif" alt="shim">
                    </div>
                </div>
            </td>
        </tr>

        <tr style="background-color: #FFF">
            <td align="left" valign="top">
                <h:panelGrid id="visualizacaoRapida" columns="2" columnClasses="centralizado, centralizado"
                             width="100%" border="0" cellspacing="3" cellpadding="1" styleClass="textsmall">

                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Número da Nota:"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <%--<a href="#">--%>
                            <%--<h:outputText styleClass="blue" value="Ir para visualização completa"/>--%>
                        <%--</a>--%>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.numeroNota}"/>
                    </h:panelGroup>

                    <h:panelGroup/>

                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Data de Emissão:"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Data de Processamento:"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.dataEmissao}">
                            <f:convertDateTime pattern="dd/MM/yyyy" locale="pt" timeZone="America/Sao_Paulo"/>
                        </h:outputText>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.dataProcessamento}">
                            <f:convertDateTime pattern="dd/MM/yyyy" locale="pt" timeZone="America/Sao_Paulo"/>
                        </h:outputText>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="CPF/CNPJ do Consumidor:"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Razão Social do Consumidor:"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText id="cnpjVisualizar"
                                      value="#{NotaFiscalDeServicoControle.notaVO.cpfCnpjFormatado}"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.razaoSocialCons}"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Valor Total:"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value=""/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.valorTotal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText value=""/>
                    </h:panelGroup>
                </h:panelGrid>
            </td>
        </tr>

        <tr style="background-color: #FFF">
            <td>
                <h:panelGrid id="visualizacaoRapida_descricao" columns="1"
                             columnClasses="centralizado" width="100%" border="0"
                             cellspacing="3" cellpadding="1" styleClass="textsmall">
                    <h:panelGroup>
                        <h:outputText style="font-weight: bold" value="Descrição:"/>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.descricao}"/>
                    </h:panelGroup>
                </h:panelGrid>
            </td>
        </tr>

        <tr style="background-color: #FFF">
            <td>
                <rich:dataTable id="itens" width="100%" rowClasses="linhaPar, linhaImpar"
                                reRender="paginaAtual, paginaAtualTop, painelPaginacaoTop, painelPaginacao"
                                columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado"
                                rows="30" var="item" value="#{ItemNFSeControle.listaDeItens}">


                    <rich:column filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Ordem"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.ordem}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Descrição"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.descricao}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Quantidade"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.quantidade}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor Unitário"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="R$ "/>
                            <h:outputText value="#{item.valorUnitario}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Tributável"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.tributavel}"/>
                        </h:panelGroup>
                    </rich:column>

                </rich:dataTable>
            </td>
        </tr>
    </table>
</h:panelGroup>

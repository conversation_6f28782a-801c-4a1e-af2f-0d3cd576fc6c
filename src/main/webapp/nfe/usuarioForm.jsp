<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head><script type="text/javascript" language="javascript" src="../script/script.js"></script></head>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Usuário"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoNFe.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid id="formulario" columns="1" width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Edição de Usuário"/>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText styleClass="tituloCampos" value="Código"/>
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                     value="#{UsuarioNFeControle.usuarioNFeVO.id_Usuario}"/>
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Nome Completo:"/>
                    <h:panelGroup>
                        <h:inputText id="nomeCompleto" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{UsuarioNFeControle.usuarioNFeVO.nome}"/>
                        <h:message for="nomeCompleto" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText rendered="#{UsuarioNFeControle.permitirEditarUsuarioSenha}"
                                  styleClass="tituloCampos" value="Nome de Usuário:"/>
                    <h:panelGroup rendered="#{UsuarioNFeControle.permitirEditarUsuarioSenha}">
                        <h:inputText id="userName" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{UsuarioNFeControle.usuarioNFeVO.usuario}"/>
                        <h:message for="userName" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText rendered="#{UsuarioNFeControle.permitirEditarUsuarioSenha}"
                                  styleClass="tituloCampos" value="Senha de acesso:"/>
                    <h:panelGroup rendered="#{UsuarioNFeControle.permitirEditarUsuarioSenha}">
                        <h:inputSecret id="senha" size="45" maxlength="45" onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="form" redisplay="true"
                                       value="#{UsuarioNFeControle.usuarioNFeVO.senha}"/>
                        <h:message for="senha" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <c:if test="${LoginControle.usuarioLogado.administrador}">
                        <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                        <h:panelGroup>
                            <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{UsuarioNFeControle.empresaVO.id_Empresa}">
                                <f:selectItems value="#{UsuarioNFeControle.listaDeEmpresas}"/>
                                <a4j:support event="onchange" action="#{UsuarioNFeControle.montarListaDePerfis}"
                                             reRender="formulario"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </c:if>

                    <h:outputText styleClass="tituloCampos" value="Perfil:"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="perfilAcesso" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{UsuarioNFeControle.usuarioNFeVO.perfilUsuarioNFe.codigo}">
                            <f:selectItems value="#{UsuarioNFeControle.listaDePerfis}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_perfis"
                                           action="#{UsuarioNFeControle.montarListaDePerfis}"
                                           image="../imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:perfilAcesso"/>
                        <h:message for="perfilAcesso" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Situação:"/>
                    <h:panelGroup rendered="true">
                        <h:selectOneMenu id="situacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{UsuarioNFeControle.usuarioNFeVO.status}">
                            <f:selectItems value="#{UsuarioNFeControle.listaSelectItemStatus}"/>
                        </h:selectOneMenu>
                        <h:message for="situacao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{UsuarioNFeControle.sucesso}" image="../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{UsuarioNFeControle.erro}" image="../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{UsuarioNFeControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{UsuarioNFeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{UsuarioNFeControle.novo}"
                                             value="#{msg_bt.btn_novo}" image="../imagens/botaoNovo.png"
                                             alt="#{msg.msg_novo_dados}" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="salvar" action="#{UsuarioNFeControle.gravar}"
                                             value="#{msg_bt.btn_gravar}"
                                             image="../imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}"
                                             styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="excluir" onclick="return confirm('Confirma exclusão dos dados?');"
                                             action="#{UsuarioNFeControle.excluir}" value="#{msg_bt.btn_excluir}"
                                             image="../imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}"
                                             styleClass="botaoExcluir"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="consultar" immediate="true"
                                             action="#{UsuarioNFeControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_consultar}" image="../imagens/botaoConsultar.png"
                                             alt="#{msg.msg_consultar_dados}" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script type="text/javascript">
    document.getElementById("form:descricao").focus();
</script>
<head><script type="text/javascript" language="javascript" src="../script/script.js"></script></head>
<%@include file="../includes/imports.jsp" %>

<table width="100%" height="48" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td width="100%" align="left" valign="top" class="bgmenuleft" style="padding:9px 0 0 80px;">
            <div style="float:left;">
                <ul class="btnazul">
                    <c:if test="${LoginControle.usuarioLogado.administrador or LoginControle.perfilNFe.permiteCRUD_Usuario}">
                        <li>
                            <h:outputLink onclick="abrirPopup('./nfe/usuarioCons.jsp', 'Usuario', 820, 600);" value="#">
                                <p class="btnleft"></p>

                                <p class="btnmiddle">Usu�rios</p>

                                <p class="btnright"></p>
                            </h:outputLink>
                        </li>
                    </c:if>

                    <c:if test="${LoginControle.usuarioLogado.administrador or LoginControle.perfilNFe.permiteCRUD_Perfil}">
                        <li>
                            <h:outputLink onclick="abrirPopup('./nfe/perfilNFeCons.jsp', 'Perfil', 800, 600);"
                                          value="#">
                                <p class="btnleft"></p>

                                <p class="btnmiddle">Perfil de Usu�rio</p>

                                <p class="btnright"></p>
                            </h:outputLink>
                        </li>
                    </c:if>

                    <c:if test="${LoginControle.usuarioLogado.administrador or LoginControle.perfilNFe.permiteIncluirEmpresa}">
                        <li>
                            <h:outputLink onclick="abrirPopup('./nfe/empresaNFeCons.jsp', 'Empresa', 800, 600);"
                                          value="#">
                                <p class="btnleft"></p>

                                <p class="btnmiddle">Empresas</p>

                                <p class="btnright"></p>
                            </h:outputLink>
                        </li>
                    </c:if>
                </ul>
            </div>
        </td>
    </tr>
</table>

<jsp:include page="../includes/include_carregando.jsp"/>
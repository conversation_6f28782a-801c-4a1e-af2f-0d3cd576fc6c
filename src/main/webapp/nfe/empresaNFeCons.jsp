<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<head><script type="text/javascript" language="javascript" src="../script/script.js"></script></head>

<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gerenciador de Empresas"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoNFe.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Gerenciador de Empresas">
                        <%--<h:outputLink value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Grupo"--%>
                        <%--title="Clique e saiba mais: Grupo com Desconto" target="_blank"--%>
                        <%--rendered="true">--%>
                        <%--<h:graphicImage styleClass="linkWiki" url="../imagens/wiki_bco.gif"/>--%>
                        <%--</h:outputLink>--%>
                    </h:outputText>
                </h:panelGrid>
                <h:panelGrid columns="6" width="100%">
                    <h:outputText styleClass="tituloCampos" value="Consultar por: "/>
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     id="consulta" required="true"
                                     value="#{EmpresaNFeControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{EmpresaNFeControle.tipoConsultaCombo}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{EmpresaNFeControle.controleConsulta.valorConsulta}"/>

                    <h:outputText styleClass="tituloCampos" value="Situação: "/>
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     id="situacaoEmpresa" required="true"
                                     value="#{EmpresaNFeControle.controleConsulta.campoConsulta2}">
                        <f:selectItems value="#{EmpresaNFeControle.situacaoEmpresa}"/>
                    </h:selectOneMenu>


                    <h:commandButton id="consultar" type="submit" styleClass="botoes" value="Consultar"
                                     image="../imagens/botaoConsultar.png"
                                     alt="Consultar Dados"
                                     action="#{EmpresaNFeControle.irPaginaInicial}"/>



                </h:panelGrid>

                <h:panelGroup id="listagemDeEmpresas">
                    <rich:dataTable id="items" width="100%" headerClass="consulta"
                                    rowClasses="linhaPar, linhaImpar "
                                    columnClasses="centralizado, centralizado, centralizado,
                                    centralizado, centralizado, centralizado"
                                    value="#{EmpresaNFeControle.listaConsulta}"
                                    rows="10" var="empresa">

                        <rich:column sortBy="#{empresa.id_Empresa}">
                            <f:facet name="header">
                                <h:outputText value="Código"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText value="#{empresa.id_Empresa}"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column sortBy="#{empresa.razaoSocial}">
                            <f:facet name="header">
                                <h:outputText value="Razão Social"/>
                            </f:facet>
                            <h:outputText value="#{empresa.razaoSocial}"/>
                        </rich:column>

                        <rich:column sortBy="#{empresa.nomeFantasia}">
                            <f:facet name="header">
                                <h:outputText value="Nome Fantasia"/>
                            </f:facet>
                            <h:outputText value="#{empresa.nomeFantasia}"/>
                        </rich:column>

                        <rich:column sortBy="#{empresa.cpf_cnpj}">
                            <f:facet name="header">
                                <h:outputText value="CNPJ/CPF"/>
                            </f:facet>
                            <h:outputText value="#{empresa.cpf_cnpj}"/>
                        </rich:column>

                        <rich:column sortBy="#{empresa.inscricaoMunicipal}">
                            <f:facet name="header">
                                <h:outputText value="Inscrição Municipal"/>
                            </f:facet>
                            <h:outputText value="#{empresa.inscricaoMunicipal}"/>
                        </rich:column>

                        <rich:column sortBy="#{empresa.municipio.nome}">
                            <f:facet name="header">
                                <h:outputText value="Cidade"/>
                            </f:facet>
                            <h:outputText value="#{empresa.municipio.nome}"/>
                        </rich:column>

                        <rich:column sortBy="#{empresa.dataVencimentoCertificado}">
                            <f:facet name="header">
                                <h:outputText value="Venc. Certificado"/>
                            </f:facet>
                            <h:outputText value="#{empresa.dataVencimentoCertificado_Apresentar}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Opções"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:commandButton action="#{EmpresaNFeControle.editar}" value="#{msg_bt.btn_editar}"
                                                 image="../imagens/botaoEditar.png" alt="Editar Dados"
                                                 styleClass="botoes"/>
                            </h:panelGroup>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGroup>

                <h:panelGrid width="100%" footerClass="colunaCentralizada">
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{EmpresaNFeControle.apresentarResultadoConsulta}"
                                      binding="#{EmpresaNFeControle.apresentarLinha}" layout="center">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false"
                                           binding="#{EmpresaNFeControle.apresentarPrimeiro}"
                                           action="#{EmpresaNFeControle.irPaginaInicial}"/>
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false"
                                           binding="#{EmpresaNFeControle.apresentarAnterior}"
                                           action="#{EmpresaNFeControle.irPaginaAnterior}"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_msg_pagina} #{EmpresaNFeControle.paginaAtualDeTodas}"
                                          rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false"
                                           binding="#{EmpresaNFeControle.apresentarPosterior}"
                                           action="#{EmpresaNFeControle.irPaginaPosterior}"/>
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false"
                                           binding="#{EmpresaNFeControle.apresentarUltimo}"
                                           action="#{EmpresaNFeControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{EmpresaNFeControle.sucesso}" image="../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{EmpresaNFeControle.erro}" image="../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{EmpresaNFeControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{EmpresaNFeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{EmpresaNFeControle.novo}" value="#{msg_bt.btn_novo}"
                                         styleClass="botoes" image="../imagens/botaoNovo.png"
                                         alt="Incluir Novos Dados"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script type="text/javascript">
     document.getElementById("form:valorConsulta").focus();
</script>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/imports.jsp" %>

<table style="background-color: #FFF; border-left: 1px #000 solid; border-right: 1px #000 solid; border-top: 1px #000 solid; border-top-left-radius: 10px; border-top-right-radius: 10px; padding: 10px"
       cellpadding="0"
       cellspacing="0">

    <c:if test="${NotaFiscalDeServicoControle.apresentarFiltroNFCe}">
        <tr>
            <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
                <h:selectOneRadio id="tipoNota" onblur="blurinput(this);" onfocus="focusinput(this);"
                                  styleClass="form"
                                  style="border: none;"
                                  value="#{NotaFiscalDeServicoControle.filtro.tipoNota}">
                    <f:selectItems value="#{NotaFiscalDeServicoControle.listaTipoNotaFiscal}"/>
                    <a4j:support event="onclick" reRender="gerenciadorNotas"
                                 action="#{NotaFiscalDeServicoControle.limparFiltro}"/>
                </h:selectOneRadio>
            </td>
        </tr>
    </c:if>

    <c:if test="${NotaFiscalDeServicoControle.filtro.tipoNota == 1}">
        <tr>
            <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
                ID_NFCe<br/>

                <h:inputText id="filtro_idNFCe"
                             size="10"
                             maxlength="20"
                             value="#{NotaFiscalDeServicoControle.filtro.id_NFCe}"
                             onkeypress="if(event.which <= 47 || event.which >= 58) return false;"
                             style="background: url(../images/nav_logo37.png) bottom;
                             border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0;
                             width:200px;
                             vertical-align: middle;"
                             styleClass="form">
                </h:inputText>
            </td>
        </tr>
    </c:if>

    <c:if test="${NotaFiscalDeServicoControle.filtro.tipoNota == 0}">
        <tr>
            <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
                ID Lote<br/>

                <h:inputText id="filtro_idRPS"
                             size="10"
                             value="#{NotaFiscalDeServicoControle.filtro.id_Lote}"
                             style="background: url(../images/nav_logo37.png) bottom;
                             border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0;
                             width:200px;
                             vertical-align: middle;"
                             styleClass="form">
                </h:inputText>
            </td>
        </tr>
    </c:if>

    <c:if test="${NotaFiscalDeServicoControle.filtro.tipoNota == 0}">
        <tr>
            <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
                Número RPS<br/>

                <h:inputText id="filtro_numeroRPS"
                             size="10"
                             maxlength="20"
                             value="#{NotaFiscalDeServicoControle.filtro.numeroRps}"
                             onkeypress="if(event.which <= 47 || event.which >= 58) return false;"
                             style="background: url(../images/nav_logo37.png) bottom;
                             border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0;
                             width:200px;
                             vertical-align: middle;"
                             styleClass="form">
                </h:inputText>
            </td>
        </tr>
    </c:if>

    <tr>
        <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
            <c:if test="${NotaFiscalDeServicoControle.filtro.tipoNota == 0}">
                Número da Nota
            </c:if>
            <c:if test="${NotaFiscalDeServicoControle.filtro.tipoNota == 1}">
                Número Envio
            </c:if>
            <br/>
            <h:inputText id="filtro_numeroNota"
                         size="10"
                         maxlength="50"
                         value="#{NotaFiscalDeServicoControle.filtro.numeroDaNota}"
                         onkeypress="if(event.which <= 47 || event.which >= 58) return false;"
                         style="background: url(../images/nav_logo37.png) bottom;
                             border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0;
                             width:200px;
                             vertical-align: middle;"
                         styleClass="form">
            </h:inputText>
        </td>
    </tr>

    <tr>
        <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
            Chave de Acesso<br/>

            <h:inputText id="filtro_chaveAcesso"
                         size="10"
                         maxlength="100"
                         value="#{NotaFiscalDeServicoControle.filtro.codigoVerificacao}"
                         onkeypress="if(event.which <= 47 || event.which >= 58) return false;"
                         style="background: url(../images/nav_logo37.png) bottom;
                             border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0;
                             width:200px;
                             vertical-align: middle;"
                         styleClass="form">
            </h:inputText>
        </td>
    </tr>

    <tr>
        <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
            CNPJ/CPF do Cliente <br/>
            <h:inputText id="filtro_cnpj"
                         size="14"
                         maxlength="14"
                         value="#{NotaFiscalDeServicoControle.filtro.cpf_cnpj}"
                         onkeypress="if(event.which <= 47 || event.which >= 58) return false;"
                         style="background: url(../images/nav_logo37.png) bottom;
                             border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0;
                             width:200px;
                             vertical-align: middle;"
                         styleClass="form">
            </h:inputText>
        </td>
    </tr>

    <tr>
        <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
            Razão Social do Cliente <br/>
            <h:inputText id="filtro_razaoSocial"
                         size="40"
                         maxlength="100"
                         value="#{NotaFiscalDeServicoControle.filtro.razaoSocial}"
                         style="background: url(../images/nav_logo37.png) bottom;
                             border: 1px solid #CCC;
                             border-bottom-color: #999;
                             border-right-color: #999;
                             height:26px;
                             color: black;
                             margin: 0;
                             width:200px;
                             vertical-align: middle;"
                         styleClass="form">
            </h:inputText>
        </td>
    </tr>

    <c:if test="${LoginControle.usuarioLogado.administrador and NotaFiscalDeServicoControle.filtro.tipoNota == 0}">
        <tr>
            <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
                <div>
                    <h:selectBooleanCheckbox id="filtro_excluidas"
                                             value="#{NotaFiscalDeServicoControle.filtro.somenteExcluidas}"/>
                    <h:outputText value="Notas Excluídas"/>
                </div>
            </td>
        </tr>
    </c:if>

    <tr>
        <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
            Status<br/>

            <div>
                <h:selectOneMenu id="filtro_status"
                                 style="height: 26px;
                                 width: 200px;"
                                 value="#{NotaFiscalDeServicoControle.filtro.status}">
                    <f:selectItems value="#{NotaFiscalDeServicoControle.tipoStatusCombo}"/>
                </h:selectOneMenu>
            </div>
        </td>
    </tr>

    <tr>
        <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
            Data de Emissão <br/>

            <h:panelGroup id="panelDataEmissao" layout="block">
                <h:panelGroup>
                    <rich:calendar id="dataInicio"
                                   value="#{NotaFiscalDeServicoControle.filtro.dataEmissao_inicio}"
                                   inputSize="7"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                    </rich:calendar>
                    <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                    <%-- ajax function para fazer o binding das datas inicio e fim executando os javascripts antes de cada componente--%>
                </h:panelGroup>
                <h:panelGroup>
                    <rich:calendar id="dataTermino"
                                   value="#{NotaFiscalDeServicoControle.filtro.dataEmissao_fim}"
                                   inputSize="7"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                    </rich:calendar>
                </h:panelGroup>
                <a4j:commandButton id="limparPeriodoEmissao"
                                   action="#{NotaFiscalDeServicoControle.limparPeriodoDataEmissao}"
                                   image="/images/limpar.gif" title="Limpar período de emissão."
                                   reRender="dataInicio, dataTermino"/>
            </h:panelGroup>
        </td>
    </tr>

    <c:if test="${NotaFiscalDeServicoControle.filtro.tipoNota == 0}">
        <tr>
            <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
                Data de Processamento <br/>

                <h:panelGroup id="panelDataProcessamento" layout="block">
                    <h:panelGroup>
                        <rich:calendar id="dataInicioProcessamento"
                                       value="#{NotaFiscalDeServicoControle.filtro.dataProcessamento_inicio}"
                                       inputSize="7"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false">
                        </rich:calendar>
                        <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <rich:calendar id="dataTerminoProcessamento"
                                       value="#{NotaFiscalDeServicoControle.filtro.dataProcessamento_fim}"
                                       inputSize="7"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                       datePattern="dd/MM/yyyy"
                                       zindex="2"

                                       showWeeksBar="false">
                        </rich:calendar>
                    </h:panelGroup>
                    <a4j:commandButton id="limparPeriodoProcessamento"
                                       action="#{NotaFiscalDeServicoControle.limparPeriodoDataProcessamento}"
                                       image="/images/limpar.gif" title="Limpar período de processamento."
                                       reRender="dataInicioProcessamento, dataTerminoProcessamento"/>
                </h:panelGroup>
            </td>
        </tr>
    </c:if>


    <tr>
        <td style="padding: 5px;">
            <h:outputText id="mensagemFiltro" styleClass="mensagemDetalhada"
                          value="#{NotaFiscalDeServicoControle.mensagemDetalhada}"/>
        </td>
    </tr>


    <tr>
        <td style="padding-bottom: 10px;padding-top: 10px;" class="blue textsmall">
            <a4j:commandButton id="LimparTela"
                               styleClass="botoes nvoBt btSec"
                               value="Limpar"
                               action="#{NotaFiscalDeServicoControle.limparFiltro}"
                               reRender="listagemDeNotas, listagemDeNotasNFCe, filtro_idNFCe, conteudo, filtro_numeroRPS, filtro_numeroNota, filtro_chaveAcesso, filtro_cnpj, filtro_razaoSocial,
                               dataInicio, dataTermino, preVisualizar, filtro_status, mensagemNFeErro, filtro_idRPS, mensagemFiltro, totalizadores">
            </a4j:commandButton>
            <a4j:commandButton id="Consultar"
                               styleClass="botoes nvoBt"
                               value="Consultar"
                               action="#{NotaFiscalDeServicoControle.filtrar}"
                               reRender="listagemDeNotas, listagemDeNotasNFCe,totalizadores, conteudo, preVisualizar, mensagemNFeErro, mensagemFiltro">
            </a4j:commandButton>

        </td>
    </tr>
</table>

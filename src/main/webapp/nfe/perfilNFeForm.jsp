<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head><script type="text/javascript" language="javascript" src="../script/script.js"></script></head>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Edição de Perfil"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoNFe.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Edição de Perfil"/>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText styleClass="tituloCampos" value="Código"/>
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                     value="#{PerfilNFeControle.perfilVO.codigo}"/>
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Nome do Perfil:"/>
                    <h:panelGroup>
                        <h:inputText id="nomePerfil" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{PerfilNFeControle.perfilVO.nomePerfilUsuario}"/>
                        <h:message for="nomePerfil" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <c:if test="${LoginControle.usuarioLogado.administrador}">
                        <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                        <h:panelGroup>
                            <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{PerfilNFeControle.perfilVO.empresa.id_Empresa}">
                                <f:selectItems value="#{PerfilNFeControle.listaSelectEmpresasCadastradas}"/>
                            </h:selectOneMenu>
                            <h:message for="empresa" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>

                        <%--<h:outputText styleClass="tituloCampos" value="Permite cadastrar Empresas:"/>--%>
                        <%--<h:panelGroup rendered="true">--%>
                            <%--<h:selectBooleanCheckbox id="incluirEmpresa"--%>
                                                     <%--value="#{PerfilNFeControle.perfilVO.permiteIncluirEmpresa}"/>--%>
                            <%--<h:message for="incluirEmpresa" styleClass="mensagemDetalhada"/>--%>
                        <%--</h:panelGroup>--%>
                    </c:if>

                    <%--<h:outputText styleClass="tituloCampos" value="Permite alterar dados da Empresa:"/>--%>
                    <%--<h:panelGroup rendered="true">--%>
                        <%--<h:selectBooleanCheckbox id="alterarEmpresa"--%>
                                                 <%--value="#{PerfilNFeControle.perfilVO.permiteAlterarBasicoEmpresa}"/>--%>
                        <%--<h:message for="alterarEmpresa" styleClass="mensagemDetalhada"/>--%>
                    <%--</h:panelGroup>--%>

                    <%--<h:outputText styleClass="tituloCampos" value="Permite cadastrar Usuários:"/>--%>
                    <%--<h:panelGroup rendered="true">--%>
                        <%--<h:selectBooleanCheckbox id="usuarios"--%>
                                                 <%--value="#{PerfilNFeControle.perfilVO.permiteCRUD_Usuario}"/>--%>
                        <%--<h:message for="usuarios" styleClass="mensagemDetalhada"/>--%>
                    <%--</h:panelGroup>--%>

                    <%--<h:outputText styleClass="tituloCampos" value="Permite cadastrar Perfil:"/>--%>
                    <%--<h:panelGroup rendered="true">--%>
                        <%--<h:selectBooleanCheckbox id="perfis"--%>
                                                 <%--value="#{PerfilNFeControle.perfilVO.permiteCRUD_Perfil}"/>--%>
                        <%--<h:message for="perfis" styleClass="mensagemDetalhada"/>--%>
                    <%--</h:panelGroup>--%>

                    <h:outputText styleClass="tituloCampos" value="Permite cancelar Notas:"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="cancelarNotas"
                                                 value="#{PerfilNFeControle.perfilVO.permiteCancelarNota}"/>
                        <h:message for="cancelarNotas" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Permite alterar número sequencial do RPS:"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="permiteAlterarNumeroRPS"
                                                 value="#{PerfilNFeControle.perfilVO.permiteAlterarNumeroRPS}"/>
                        <h:message for="permiteAlterarNumeroRPS" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{PerfilNFeControle.sucesso}" image="../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{PerfilNFeControle.erro}" image="../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{PerfilNFeControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{PerfilNFeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{PerfilNFeControle.novo}"
                                             value="#{msg_bt.btn_novo}" image="../imagens/botaoNovo.png"
                                             alt="#{msg.msg_novo_dados}" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="salvar" action="#{PerfilNFeControle.gravar}"
                                             value="#{msg_bt.btn_gravar}"
                                             image="../imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}"
                                             styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="excluir" onclick="return confirm('Confirma exclusão dos dados?');"
                                             action="#{PerfilNFeControle.excluir}" value="#{msg_bt.btn_excluir}"
                                             image="../imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}"
                                             styleClass="botaoExcluir"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="consultar" immediate="true"
                                             action="#{PerfilNFeControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_consultar}" image="../imagens/botaoConsultar.png"
                                             alt="#{msg.msg_consultar_dados}" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script type="text/javascript">
    document.getElementById("form:descricao").focus();
</script>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
    <link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
    <link href="./css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
    <link href="./css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
    <script src="bootstrap/jquery.js" type="text/javascript"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript" src="script/jquery.maskedinput-1.7.6.js"></script>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="FreePass "/>
    </title>
    <rich:modalPanel id="panelCliente" autosized="true" styleClass="novaModal" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formCliente:consultarCliente').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_FreePass_consulta_cliente_titulo}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkCliente"/>
                <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCliente" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarCliente" value="#{FreePassControle.campoConsultarCliente}">
                        <f:selectItems value="#{FreePassControle.tipoConsultaCombo}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarCliente" styleClass="campos" value="#{FreePassControle.valorConsultaCliente}"/>
                    <a4j:commandButton id="btnConsultarCliente" reRender="formCliente:mensagemConsultaCliente, formCliente:resultadoConsultaCliente , formCliente:scResultadoCliente" action="#{FreePassControle.consultarCliente}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCliente" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{FreePassControle.listaConsultaDeCliente}" rows="10" var="cliente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_FreePass_consulta_cliente_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_FreePass_consulta_cliente_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_FreePass_consulta_cliente_situacao}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.situacao_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_FreePass_consulta_cliente_matricula}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.matricula}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="selecionarCliente" action="#{FreePassControle.selecionarCliente}" focus="nomeComprador" reRender="form:panelGeral,form:panelBotoesControle, form:mensagem" oncomplete="Richfaces.hideModalPanel('panelCliente')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10" id="scResultadoCliente"/>
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{FreePassControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{FreePassControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form" >
        <html>
        <jsp:include page="include_head.jsp" flush="true" />
        <body>

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central" style="position:relative;">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="FreePass" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-free-pass/"
                                                      title="Clique e saiba mais: FreePass" target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup id="panelCaixaAberto"  layout="block" styleClass="margin-box">

                                    <h:panelGrid id="panelGeral" columns="1" styleClass="font-size-em-max">

                                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="DATA ÍNICIO" >
                                            <i class=" fa-icon-asterisk"></i>
                                        </h:outputText>

                                        <h:panelGroup styleClass="dateTimeCustom" layout="block" style="height: 40px;">
                                            <rich:calendar id="dataIniAbertura"
                                                           value="#{FreePassControle.dataInicio}"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true"
                                                           zindex="2"
                                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                                           showWeeksBar="false" />

                                        </h:panelGroup>
                                        <rich:spacer height="5px"/>
                                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="CLIENTE">
                                            <i class="fa-icon-asterisk"></i>
                                        </h:outputText>

                                        <h:panelGroup id="panelGroupComprador">
                                            <h:panelGroup >
                                                <h:inputText  id="nomeCliente"  size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="inputTextClean"/>
                                                <rich:suggestionbox
                                                        height="200" width="444"
                                                        for="nomeCliente"
                                                        fetchValue="#{result.pessoa.nome}"
                                                        suggestionAction="#{FreePassControle.executarAutocompleteConsultaCliente}"
                                                        minChars="1" rowClasses="20"
                                                        status="statusHora"
                                                        nothingLabel="Nenhum Cliente encontrado !"
                                                        var="result" id="suggestionNomeCliente"
                                                        reRender="panelBotoesControle, mensagem">
                                                    <a4j:support event="onselect"
                                                                 action="#{FreePassControle.selecionarClienteSuggestionBox}"
                                                                 reRender="panelBotoesControle,mensagem,panelDadosProdutosPeriodo"
                                                                 oncomplete="#{FreePassControle.msgAlert}"/>
                                                    <h:column>
                                                        <h:outputText styleClass="texto-font texto-size-14-real" value="#{result.pessoa.nome}"/>
                                                    </h:column>
                                                    <h:column>
                                                        <h:outputText styleClass="texto-font texto-size-14-real" value="#{result.situacao_Apresentar}"/>
                                                    </h:column>
                                                </rich:suggestionbox>
                                                <rich:spacer width="5" />
                                                <a4j:commandLink id="limparCliente"
                                                                 onclick="document.getElementById('form:nomeCliente').value = null;"
                                                                 title="Limpar aluno."
                                                                 status="false"
                                                                 action="#{FreePassControle.limparCliente}"
                                                                 reRender="panelBotoesControle, mensagem,panelDadosProdutosPeriodo">
                                                    <i class="fa-icon-remove texto-size-14-real linkAzul"/>

                                                </a4j:commandLink>

                                            </h:panelGroup>
                                        </h:panelGroup>
                                        <rich:spacer height="5px"/>


                                        <h:panelGroup id="panelDadosProdutosPeriodo">

                                            <h:panelGroup id="panelGroupProduto" rendered="#{FreePassControle.freePassVO.clienteVO.freePass.codigo == 0}">
                                                <h:panelGrid columns="1" styleClass="font-size-em-max" style="margin-left:-3px">
                                                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="PRODUTO"/>
                                                    <h:panelGroup >
                                                        <h:inputText  id="nomeProduto" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="inputTextClean"/>
                                                        <rich:suggestionbox
                                                                height="200" width="444"
                                                                for="nomeProduto"
                                                                fetchValue="#{resultProduto.descricao}"
                                                                suggestionAction="#{FreePassControle.executarAutocompleteConsultaProduto}"
                                                                minChars="1" rowClasses="20"
                                                                status="statusHora"
                                                                nothingLabel="Nenhum produto encontrado !"
                                                                var="resultProduto" id="suggestionNomeProduto"
                                                                reRender="mensagem">
                                                            <a4j:support event="onselect"
                                                                         action="#{FreePassControle.selecionarProdutoSuggestionBox}"/>
                                                            <h:column>
                                                                <h:outputText styleClass="texto-font texto-size-14-real" value="#{resultProduto.descricao}"/>
                                                            </h:column>
                                                            <h:column>
                                                                <h:outputText styleClass="texto-font texto-size-14-real" value="#{resultProduto.nrDiasVigencia} Dias"/>
                                                            </h:column>
                                                        </rich:suggestionbox>
                                                        <rich:spacer width="5" />
                                                        <a4j:commandLink id="limparProduto"
                                                                         onclick="document.getElementById('form:nomeProduto').value = null;"
                                                                         title="Limpar produto."
                                                                         status="false"
                                                                         action="#{FreePassControle.limparProduto}">
                                                            <i class="fa-icon-remove texto-size-14-real linkAzul"/>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </h:panelGrid>

                                            </h:panelGroup>
                                            <h:panelGroup id="panelMsgExistente" rendered="#{FreePassControle.freePassVO.clienteVO.freePass.codigo != 0}">
                                                <h:outputText id="msgExistente" styleClass="mensagemDetalhada" value="#{FreePassControle.msgFreePassExistente}"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGrid id="mensagem" columns="2" width="100%" >
                                            <h:panelGrid columns="3" width="100%" >
                                                <h:panelGrid columns="1" width="100%">
                                                    <f:verbatim>
                                                        <h:outputText value=" "/>
                                                    </f:verbatim>
                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGrid>

                                    <!-- inicio botões -->
                                    <h:panelGrid columns="3"  id="panelBotoesControle" rendered="#{FreePassControle.apresentarBotoes}" styleClass="container-botoes" style="text-align: left; margin-top: 10px; width: 50%">

                                        <a4j:commandLink rendered="#{FreePassControle.freePassVO.clienteVO.freePass.codigo == 0}"
                                                         styleClass="pure-button pure-button-primary"
                                                         id="botaoGravar"
                                                         action="#{FreePassControle.getAbrirRichModalConfirmacaoFreePass}"
                                                         reRender="form:panelGeral,form:panelBotoesControle,panelBotoesControle,formConfirmacaoFreePass,panelResponsavelFreePass,panelAutorizacaoFuncionalidade,panelDadosProdutosPeriodo"
                                                         >
                                            <i class="fa-icon-plus"></i> &nbsp Confirmar
                                        </a4j:commandLink>

                                        <a4j:commandLink  styleClass="pure-button pure-button-primary"
                                                          id="botaoExcluir"
                                                          rendered="#{FreePassControle.freePassVO.clienteVO.freePass.codigo != 0}"
                                                          action="#{FreePassControle.getAbrirRichModalConfirmacaoExclusaoFreePass}"
                                                          reRender="form:panelGeral,form:panelBotoesControle, panelBotoesControle,formConfirmacaoFreePass,panelResponsavelFreePass,panelAutorizacaoFuncionalidade"
                                                          >
                                            <i class="fa-icon-minus"></i> &nbsp Deletar FreePass Existente
                                        </a4j:commandLink>

                                        <h:outputLink  styleClass="pure-button" id="botaoCancelar" value="tela1.jsp">
                                            Cancelar
                                        </h:outputLink>

                                        <a4j:commandLink id="botaoLogFreepass"
                                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                         styleClass="tudo texto-cor-azul linkPadrao tooltipster"
                                                         title="Visualizar logs" target="_blank"
                                                         action="#{FreePassControle.realizarConsultaLogObjetoGeral}"
                                                         style="margin-left: 10px; float: right;">
                                            <i class="fa-icon-list"></i>
                                        </a4j:commandLink>
                                        <script>
                                            carregarTooltipster();
                                        </script>
                                    </h:panelGrid>

                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        </body>
        </html>
        <script>
            jQuery('.rich-calendar-input').mask('99/99/9999');
        </script>
    </h:form>
    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp"/>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:nomeComprador").focus();
</script>

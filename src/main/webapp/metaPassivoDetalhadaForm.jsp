<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
</style>
<script type="text/javascript">
    function trocarTotal(show) {
        var totalHoje = document.getElementById('totalHoje');
        var totalPesquisa = document.getElementById('totalPesquisa');

        totalHoje.style.display = 'none';
        totalPesquisa.style.display = 'none';

        var filtros = document.getElementById('filtros');
        var mostrar = document.getElementById(show);
        mostrar.style.display = 'block';

        if (show == 'totalPesquisa') {
            filtros.style.display = 'block';
        } else {
            filtros.style.display = 'none';
        }
    }
</script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_Passivo_tituloForm}" /></title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp" />
        </f:facet>

    </h:panelGrid>
    <h:form id="form"  >
        <h:commandLink action="#{AberturaMetaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
        <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm"  width="1700">
            <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Passivo_tituloForm}" />
            </h:panelGrid>
        </h:panelGrid>

        <!-- ---------------------------- TOTALIZADORES --------------------------------- -->


        <h:panelGrid columns="2" columnClasses="colunaEsquerda" styleClass="tabForm" width="100%">
            <h:panelGroup id="totalizadores">


                <div id="totalHoje" style="display: block;">
                    <h:panelGrid columns="1" id="totalHojeNrs">
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Passivos na lista" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencial}" />
                            <rich:spacer width="12px" />
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Passivos"  value="#{AberturaMetaControle.fecharMetaPassivoVO.totalizadorHoje}" />
                            <rich:spacer width="30px" />
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Passivos selecionados" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencialEmail}" />
                            <rich:spacer width="12px" />
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Passivos selecionados" value="#{AberturaMetaControle.fecharMetaPassivoVO.totalizadorSelecionadoHoje}" />
                        </h:panelGroup>
                    </h:panelGrid>
                </div>

                <div id="totalPesquisa" style="display: none;">
                    <h:panelGrid columns="1" id="totalPesquisaNrs">
                        <h:panelGroup>
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Passivos na lista" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencial}" />
                            <rich:spacer width="12px" />
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Passivos" value="#{AberturaMetaControle.fecharMetaPassivoVO.totalizadorHistorico}" />
                            <rich:spacer width="30px" />
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Passivos selecionados" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencialEmail}" />
                            <rich:spacer width="12px" />
                            <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Passivos selecionados" value="#{AberturaMetaControle.fecharMetaPassivoVO.totalizadorSelecionadoHistorico}" />
                        </h:panelGroup>
                    </h:panelGrid>
                </div>
            </h:panelGroup>
            <h:panelGroup>
                <rich:spacer width="30px" />

                <a4j:commandButton value="Atualizar" reRender="totalHojeNrs, itemsClientePotencial"
                                   actionListener="#{AberturaMetaControle.atualizarFecharMetaDetalhada}" styleClass="botaoPesquisar" image="./imagensCRM/atualizarCRM.png">
                    <f:attribute name="identificador" value="CP"/>
                </a4j:commandButton>&nbsp;

                <a4j:commandButton styleClass="botaoPesquisar" reRender="form:panelGridMensagens, mensagemNotificacaoColetivoForm, totalLista"
                                   image="./imagensCRM/botaoEnviarEmail.png" title="Enviar E-mail"
                                   oncomplete="#{AberturaMetaControle.executarAberturaPopupMalaDireta}"
                                   action="#{AberturaMetaControle.executarAberturaPopupEmailColetivoPassivo}"  />
                <a4j:commandButton id="btnEnviarSMS" styleClass="botaoPesquisar" reRender="form:panelGridMensagens, mensagemNotificacaoColetivoForm, totalLista"
                                   title="Enviar SMS" value="Enviar SMS"  image="./imagensCRM/enviar-sms.png"
                                   oncomplete="#{AberturaMetaControle.executarAberturaPopupMalaDireta}"
                                   action="#{AberturaMetaControle.executarAberturaPopupSMSColetivoPassivo}" />
                &nbsp;
                <a4j:commandLink  action="#{AberturaMetaControle.executarImpressaoPdfPassivo}" styleClass="botaoPesquisar"
                                  oncomplete="abrirPopupPDFImpressao('relatorio/#{AberturaMetaControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                    <h:graphicImage style="border:none;" url="./imagensCRM/botaoImprimirLista.png" title="Imprimir Lista(PDF)" />
                </a4j:commandLink>
                <a4j:commandLink action="#{AberturaMetaControle.executarImpressaoExcelPassivo}" oncomplete="location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=MetaDetalhadoExcelClienteRel.xlsx'" styleClass="botaoPesquisar">
                    <h:graphicImage url="./imagensCRM/botaoGerarExcel.png" style="border:none;" title="Imprimir Lista(Excel)" />
                </a4j:commandLink>

                <rich:spacer width="50px" />
                <h:outputText value="#{msg_aplic.prt_HistoricoIndicado_consProf}" styleClass="tituloCampos" />
                <rich:spacer width="10px" />
                <h:outputText value="#{AberturaMetaControle.nomeParticipanteSelecionadoVenda}" styleClass="tituloCampos" />
            </h:panelGroup>

        </h:panelGrid>


        <!-- ---------------------------- FILTROS --------------------------------- -->

        <table id="filtros" style="display: none" class="tabForm" width="100%">
            <tr><td width="100%">


                    <h:panelGroup style="width: 100%">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePotencial_nome}" />
                        <rich:spacer width="5px" />
                        <h:inputText value="#{AberturaMetaControle.valorConsultaNomePassivo}" size="50" styleClass="camposAgendado" />

                        <rich:spacer width="10px" />

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_periodo}" />
                        <rich:spacer width="12px" />
                        <rich:calendar id="dataInicio" value="#{AberturaMetaControle.dataInicio}" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                        <h:message for="dataInicio" styleClass="mensagemDetalhada" />
                        <rich:spacer width="12px" />
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_ate}" />

                        <rich:spacer width="12px" />
                        <rich:calendar id="dataTermino" value="#{AberturaMetaControle.dataTermino}" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="true" />
                        <h:message for="dataTermino" styleClass="mensagemDetalhada" />
                        <rich:spacer width="12px" />
                        <a4j:commandButton oncomplete="trocarTotal('totalPesquisa');"
                                           styleClass="botaoPesquisar"
                                           action="#{AberturaMetaControle.consultarFecharMetaDetalhadoVendaPassivoPorHistorico}"
                                           image="./imagensCRM/botaoPesquisar.png"
                                           reRender="totalPesquisaNrs, itemsClientePotencialHistorico" />
                    </h:panelGroup>


                </td></tr>
        </table>


        <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
            <h:outputText styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
            <h:outputText styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
        </h:panelGrid>

        <h:panelGrid columns="1" styleClass="tabForm" width="1700">
            <rich:tabPanel id="panelAbas" width="100%" activeTabClass="true" switchType="client">
                <rich:tab onlabelclick="trocarTotal('totalHoje');" id="hojeClientePotencial" label="Hoje -  #{AberturaMetaControle.aberturaMetaVO.dia_Apresentar}">
                    <h:panelGrid columns="1" styleClass="tabForm" width="100%">

                        <rich:dataTable id="itemsClientePotencial" width="100%" headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno" columnClasses="colunaEsquerda" value="#{AberturaMetaControle.fecharMetaPassivoVO.fecharMetaDetalhadoVOs}" var="fecharMetaDetalhado">

                            <rich:column>
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_opcoes}" />
                                        <h:selectBooleanCheckbox title="Marca todos os Passivos" value="#{AberturaMetaControle.fecharMetaPassivoVO.marcaTodosHoje}">
                                            <a4j:support event="onclick" action="#{AberturaMetaControle.executarSelecaoTodosFecharMetaDetalhadosPassivo}" reRender="panelAbas, totalHojeNrs" />
                                        </h:selectBooleanCheckbox>
                                    </h:panelGroup>
                                </f:facet>
                                <a4j:commandButton image="./imagensCRM/icontelefonista.png" title="Realizar novo Contato" action="#{HistoricoContatoControle.selecionarPassivoRealizacaoContato}" ajaxSingle="true" oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}" />
                                <rich:spacer width="10px" />
                                <a4j:commandButton image="./imagensCRM/icon_Lupa.png" title="Consultar Histórico Contato" action="#{HistoricoContatoControle.consultarHistoricoPassivoVindoTelaMetaPassivoDetalhada}" ajaxSingle="true" oncomplete="abrirPopup('historicoContatoPassivoForm.jsp', 'HistoricoContatoPassivo', 512, 530);" />
                                <rich:spacer width="10px" />
                                <a4j:commandButton  image="./imagensCRM/passivas_menor.png" title="Visualizar Passivo" action="#{PassivoControle.editarPassivoListaFecharMetaDetalhado}" ajaxSingle="true" oncomplete="abrirPopup('passivoForm.jsp', 'configuracaoSistemaCRM', 780, 595);" />
                                <rich:spacer width="10px" />
                                <h:selectBooleanCheckbox title="Enviar E-mail" value="#{fecharMetaDetalhado.enviarEmailSMS}">
                                    <a4j:support event="onclick" action="#{AberturaMetaControle.executarTotalizadorSelecionadoPassivoHoje}" reRender="totalHojeNrs" />
                                </h:selectBooleanCheckbox>
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.nome}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_nomePessoa}" />
                                </f:facet>
                                <h:outputText id="nome" value="#{fecharMetaDetalhado.passivo.nome}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.dia_Apresentar}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.dia_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_dataLancamento}" />
                                </f:facet>
                                <h:outputText id="dia" value="#{fecharMetaDetalhado.passivo.dia_Apresentar}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_dataUltimoContato}" />
                                </f:facet>
                                <h:outputText id="dataUltimoContato" value="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.telefoneResidencial}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.telefoneResidencial}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_telResidencial}" />
                                </f:facet>
                                <h:outputText id="telefoneResidencial" value="#{fecharMetaDetalhado.passivo.telefoneResidencial}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.telefoneCelular}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.telefoneCelular}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_telCelular}" />
                                </f:facet>
                                <h:outputText id="telCelular" value="#{fecharMetaDetalhado.passivo.telefoneCelular}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.telefoneTrabalho}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.telefoneTrabalho}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_telTrabalho}" />
                                </f:facet>
                                <h:outputText id="telTrabalho" value="#{fecharMetaDetalhado.passivo.telefoneTrabalho}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.email}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.email}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_email}" />
                                </f:facet>
                                <h:outputText id="email" value="#{fecharMetaDetalhado.passivo.email}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.evento.descricao}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.evento.descricao}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_evento}" />
                                </f:facet>
                                <h:outputText id="evento" value="#{fecharMetaDetalhado.passivo.evento.descricao}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.colaboradorResponsavel.nome}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.colaboradorResponsavel.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_colaboradorResponsavel}" />
                                </f:facet>
                                <h:outputText id="colabordorResponsavel" value="#{fecharMetaDetalhado.passivo.colaboradorResponsavel.nome}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.historicoContatoVO.resultado}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.historicoContatoVO.resultado}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_resultado}" />
                                </f:facet>
                                <h:outputText id="resultado" value="#{fecharMetaDetalhado.historicoContatoVO.resultado}" />
                            </rich:column>

                        </rich:dataTable>
                    </h:panelGrid>
                </rich:tab>
                <rich:tab onlabelclick="trocarTotal('totalPesquisa');" id="historicoClientePotencial" label="Histórico" >
                    <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                        <rich:dataTable id="itemsClientePotencialHistorico" width="100%" headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno" columnClasses="colunaEsquerda" value="#{AberturaMetaControle.fecharMetaPassivoVO.listaHistoricoContatoHistorico}" var="fecharMetaDetalhado">

                            <rich:column>
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText  value="#{msg_aplic.prt_HistoricoIndicado_opcoes}" />
                                        <h:selectBooleanCheckbox title="Marca todos os Passivos" value="#{AberturaMetaControle.fecharMetaPassivoVO.marcaTodosHistorico}">
                                            <a4j:support event="onclick" action="#{AberturaMetaControle.executarSelecaoTodosFecharMetaDetalhadosPassivo}" reRender="panelAbas, totalPesquisaNrs" />
                                        </h:selectBooleanCheckbox>
                                    </h:panelGroup>
                                </f:facet>
                                <a4j:commandButton image="./imagensCRM/icontelefonista.png" title="Realizar novo Contato" action="#{HistoricoContatoControle.selecionarPassivoRealizacaoContato}" ajaxSingle="true" oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}" />
                                <rich:spacer width="10px" />
                                <a4j:commandButton image="./imagensCRM/icon_Lupa.png" title="Consultar Histórico Contato" action="#{HistoricoContatoControle.consultarHistoricoPassivoVindoTelaMetaPassivoDetalhada}" ajaxSingle="true" oncomplete="abrirPopup('historicoContatoPassivoForm.jsp', 'HistoricoContatoPassivo', 512, 530);" />
                                <rich:spacer width="10px" />
                                <a4j:commandButton image="./imagensCRM/passivas_menor.png" title="Visualizar Passivo" action="#{PassivoControle.editarPassivoListaFecharMetaDetalhado}" ajaxSingle="true" oncomplete="abrirPopup('passivoForm.jsp', 'configuracaoSistemaCRM', 780, 595);" />
                                <rich:spacer width="10px" />
                                <h:selectBooleanCheckbox title="Enviar E-mail" value="#{fecharMetaDetalhado.enviarEmailSMS}">
                                    <a4j:support event="onclick" action="#{AberturaMetaControle.executarTotalizadorSelecionadoPassivoHistorico}" reRender="totalPesquisaNrs" />
                                </h:selectBooleanCheckbox>
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.nome}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_nomePessoa}" />
                                </f:facet>
                                <h:outputText id="nomeHistorico" value="#{fecharMetaDetalhado.passivo.nome}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.dia_Apresentar}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.dia_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_dataLancamento}" />
                                </f:facet>
                                <h:outputText id="horaHistorico" value="#{fecharMetaDetalhado.passivo.dia_Apresentar}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_dataUltimoContato}" />
                                </f:facet>
                                <h:outputText id="dataUltimoContato" value="#{fecharMetaDetalhado.dataUltimoContato_Apresentar}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.telefoneResidencial}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.telefoneResidencial}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_telResidencial}" />
                                </f:facet>
                                <h:outputText id="telefoneResidencialHistorico" value="#{fecharMetaDetalhado.passivo.telefoneResidencial}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.telefoneCelular}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.telefoneCelular}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_telCelular}" />
                                </f:facet>
                                <h:outputText id="telCelularHistorico" value="#{fecharMetaDetalhado.passivo.telefoneCelular}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.telefoneTrabalho}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.telefoneTrabalho}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_telTrabalho}" />
                                </f:facet>
                                <h:outputText id="telTrabalhoHistorico" value="#{fecharMetaDetalhado.passivo.telefoneTrabalho}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.email}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.email}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_email}" />
                                </f:facet>
                                <h:outputText id="emailHistorico" value="#{fecharMetaDetalhado.passivo.email}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.evento.descricao}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.evento.descricao}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_evento}" />
                                </f:facet>
                                <h:outputText id="eventoHistorico" value="#{fecharMetaDetalhado.passivo.evento.descricao}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.passivo.colaboradorResponsavel.nome}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.passivo.colaboradorResponsavel.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_colaboradorResponsavel}" />
                                </f:facet>
                                <h:outputText id="colabordorResponsavelHistorico" value="#{fecharMetaDetalhado.passivo.colaboradorResponsavel.nome}" />
                            </rich:column>
                            <rich:column  filterBy="#{fecharMetaDetalhado.historicoContatoVO.resultado}" filterEvent="onkeyup" sortBy="#{fecharMetaDetalhado.historicoContatoVO.resultado}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ClientePotencial_resultado}" />
                                </f:facet>
                                <h:outputText id="resultadoHistorico" value="#{fecharMetaDetalhado.historicoContatoVO.resultado}" />
                            </rich:column>

                        </rich:dataTable>

                    </h:panelGrid>
                </rich:tab>
            </rich:tabPanel>
        </h:panelGrid>
    </h:form>
</f:view>
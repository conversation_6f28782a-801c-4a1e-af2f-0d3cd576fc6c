<%--
    Document   : cancelamenteTransferencia
    Created on : 26/06/2009, 08:23:21
    Author     : pedro
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
    .to-uper-case{
        text-transform: uppercase;
    }
</style>
<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <c:set var="titulo" scope="session" value="Transferência"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="formCancelamentoAuto">
        <h:panelGrid columns="2" width="100%">
            <h:panelGrid columns="1" width="100%" cellpadding="2" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                <div style="margin-bottom:0px;font-weight: bold;margin-top: 15px;margin-bottom: 15px;" class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">
                    DETALHES DA TRANSFERÊNCIA
                </div>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.mensagemDevolucao}"/>
                <br>
                <h:panelGroup>
                    <h:outputText value="VALOR PAGO PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.empresaLogado.moeda} " />
                    <h:outputText id="vlrPago" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalPagoPeloCliente}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <h:panelGroup rendered="#{CancelamentoContratoControle.devolverTransferenciaSaldoCredito}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR TRANSFERÊNCIA DE SALDO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.empresaLogado.moeda} " />
                    <h:outputText id="vlrPagoTransfSaldo" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.totalTransferenciaSaldo}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>


                <h:panelGroup rendered="#{(CancelamentoContratoControle.cancelamentoContratoVO.transferencia) && (!CancelamentoContratoControle.contratoVO.vendaCreditoTreino)}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR UTILIZADO PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.empresaLogado.moeda} " />
                    <h:outputText id="vlrUsado" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorUtilizadoPeloClienteBase}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <h:panelGroup rendered="#{(CancelamentoContratoControle.cancelamentoContratoVO.transferencia) && (CancelamentoContratoControle.contratoVO.vendaCreditoTreino)}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR UTILIZANDO PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="(Valor unitário crédito x nr.créditos utilizados)"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.empresaLogado.moeda} " />
                    <h:outputText id="vlrUsadoCredito" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUtilizadoMensal}"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  title="(Valor unitário crédito x nr.créditos utilizados)">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <h:panelGroup>
                    <h:outputText value="VALOR DOS PRODUTO(S) NA COMPRA DO CONTRATO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.empresaLogado.moeda} " />
                    <h:outputText id="vlrProdutos" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalSomaProdutoContratos}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <h:panelGroup id="panelCC">
                    <h:panelGroup  rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.transferirEmDias || CancelamentoContratoControle.cancelamentoContratoVO.depositaNaContaTerceiro}">
                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        <h:outputText value="SALDO DA CONTA CONRRENTE DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.empresaLogado.moeda} " />
                        <h:outputText id="vlrCC" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.saldoContaCorrenteCliente}" >
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGroup>
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <h:panelGroup id="panelValor">
                    <h:outputText value="VALOR A SER TRANSFERIDO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.empresaLogado.moeda} " />
                    <h:outputText id="vlrTransferencia" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvido}"
                                  styleClass="#{CancelamentoContratoControle.cancelamentoContratoVO.definirCorCampoValorASerDevolvido} texto-size-14-real texto-font texto-bold">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <h:panelGrid style="vertical-align: middle;" columns="4" id="camposValores"
                			 rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.valorASerDevolvido > 0}" >

                		<h:outputText value="ALTERAR VALOR" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                   		<h:selectBooleanCheckbox id="liberacaoAlterarValor" 
                   								 value="#{CancelamentoContratoControle.alterarValor}">
                            <a4j:support event="onclick" action="#{CancelamentoContratoControle.alterarValor}"  oncomplete="#{CancelamentoContratoControle.msgAlert}"
                            			 reRender="formCancelamentoAuto"/>
                        </h:selectBooleanCheckbox>
                        <h:inputText id="valorNovo" size="7" rendered="#{CancelamentoContratoControle.alterarValor}"
                                     onkeypress="return formatar_moeda(this,'.',',',event);" maxlength="10"
                                     value="#{CancelamentoContratoControle.valorTotalAlteracaoValor}"
                                     onfocus="focusinput(this);" styleClass="inputTextClean" style="margin-left: 8px; margin-right: 8px; height: 36px; width: 150px;">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>
                        <a4j:commandLink id="aplicarvalorNovo" value="Aplicar"
                                           styleClass="botaoPrimarioGrande"
                         			       rendered="#{CancelamentoContratoControle.alterarValor}"
                         			       action="#{CancelamentoContratoControle.aplicarAlterarValor}"
                                                       oncomplete="#{CancelamentoContratoControle.mensagemNotificar}"
                                           reRender="formCancelamentoAuto, panelAutorizacaoFuncionalidade"/>
                </h:panelGrid>
                
                <br/>
                <h:outputText value="O que deseja fazer com o valor a ser transferido?" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                              />
                <h:panelGroup>
                    <h:selectOneRadio id="tipoTransferenciaCancelamento" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{CancelamentoContratoControle.cancelamentoContratoVO.tipoTranferenciaCancelamento}" >
                        <f:selectItems value="#{CancelamentoContratoControle.listaSelectItemTipoTransferenciaCancelamento}"/>
                        <a4j:support event="onclick" action="#{CancelamentoContratoControle.obterTipoTransferencia}" reRender="panelValor,panelCC,camposValores"/>
                    </h:selectOneRadio>
                </h:panelGroup>
                <h:panelGrid width="550px" columns="2" style="float: right;" >
                    <h:panelGrid width="350px"/>
                    <h:panelGrid width="160px">
                        <h:panelGroup >
                            <h:commandLink id="voltar" title="Voltar Passo" action="#{CancelamentoContratoControle.voltarTelaCancelamento}" styleClass="pure-button">
                                <i class="fa-icon-arrow-left"></i>
                            </h:commandLink>
                            <rich:spacer width="7"/>
                            <a4j:commandLink id="proximo" action="#{CancelamentoContratoControle.validarTransferenciaCliente}"
                                             oncomplete="#{CancelamentoContratoControle.mensagemNotificar}"
                                             styleClass="pure-button pure-button-primary" title="Próximo Passo">
                                <i class="fa-icon-arrow-right"></i>
                                <a4j:support event="onclick" reRender="panel"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <%--<h:panelGrid columns="1" width="100%">--%>
                <%--<rich:modalPanel id="panel" width="350" height="100" showWhenRendered="#{CancelamentoContratoControle.cancelamentoContratoVO.mensagemErro}">--%>
                    <%--<f:facet name="header">--%>
                        <%--<h:panelGroup>--%>
                            <%--<h:outputText value="Atenção!"></h:outputText>--%>
                        <%--</h:panelGroup>--%>
                    <%--</f:facet>--%>
                    <%--<f:facet name="controls">--%>
                        <%--<h:panelGroup>--%>
                            <%--<h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>--%>
                            <%--<rich:componentControl for="panel" attachTo="hidelink" operation="hide" event="onclick"/>--%>
                        <%--</h:panelGroup>--%>
                    <%--</f:facet>--%>
                    <%--<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">--%>
                        <%--<h:panelGroup>--%>
                            <%--<h:graphicImage value="/imagens/erro.png"/>--%>
                            <%--<rich:spacer width="10" />--%>
                            <%--<h:outputText id="msgCancelamentoDet" styleClass="mensagemDetalhada" value="#{CancelamentoContratoControle.mensagemDetalhada}"/>--%>
                        <%--</h:panelGroup>--%>
                    <%--</h:panelGrid>--%>
                <%--</rich:modalPanel>--%>
            <%--</h:panelGrid>--%>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>

<h:panelGroup></h:panelGroup>
<link href="${root}/bootstrap/bootplus.min.css" rel="stylesheet">
<c:if test="${MenuControle.exibirMenuExplorar}">
    <jsp:include page="include_topo_pesquisa_usuario_zw_ui_explorar.jsp"/>
</c:if>

<c:if test="${!MenuControle.exibirMenuExplorar}">
        <div class="zw_ui_modulos_pesquisa somente-pactobr" id="zw_ui_modulos_pesquisa">
            <c:if test="${SuporteControle.apresentarMensagemExpiracao}">
                <div style="background-color: #BA202F;position: absolute; width: inherit; height: 24px;top: 0px;color: #ffffff;text-align: center;">
                    <div class="content"><i style="font-size: 20px; padding-right: 5px;" class="pct pct-alert-triangle"></i>
                        <h:outputText style="font-weight: 600; font-size: 14px; line-height: 100%;"
                                      title="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                                      id="dataExpiracaoNovoMenu"
                                      value="#{SuporteControle.mensagemExpiracaoTopo}"/>
                        <a4j:commandLink rendered="#{LoginControle.usuarioLogado.possuiPerfilAcessoAdministrador}"
                                         style="color: #ffffff;font-size: 14px;"
                                         title="Clique para acessar os boletos disponíveis"
                                         value="  Clique aqui para ver suas pendências."
                                         oncomplete="#{CanalPactoControle.mensagemNotificar}"
                                         action="#{CanalPactoControle.abrirTelaMinhaContaFaturas}"/>

                    </div>

                </div>
            </c:if>
            <div class="aux">
                <div class="modulos-choose" id="explorar-trigger-all" onclick="toggleModulos()">
                    <div class="modulo-opener">
                        <i class="pct pct-grid"></i>
                        <i class="pct pct-x"></i>
                        <span> Explorar</span>
                    </div>
                </div>
                <div class="zw_pesquisa_global somente-pactobr">
                    <i class="pct pct-search"></i>
                    <h:inputText id="campoBuscaZWUI" styleClass="ng-pristine ng-valid ng-touched campo-busca-zw-ui" readonly="true" onfocus="this.removeAttribute('readonly');"
                                 value="#{ConsultaClienteControle.valorConsultaParametrizada}" autocomplete="off"/>
                    <script>
                        document.getElementById("form:campoBuscaZWUI").setAttribute("placeholder", "Digite sua busca aqui");
                        document.getElementById("form:campoBuscaZWUI").setAttribute("autocomplete", "new-password");
                        document.getElementById("form:campoBuscaZWUI").setAttribute("spellcheck", "false");
                        document.getElementById("form:campoBuscaZWUI").setAttribute("autocorrect", "off");
                        document.getElementById("form:campoBuscaZWUI").setAttribute("role", "presentation");
                        document.getElementById("form:campoBuscaZWUI").setAttribute("name", "campoBuscaUI");
                    </script>

                    <rich:suggestionbox  width="519"
                                         height="300"
                                         for="campoBuscaZWUI"
                                         frequency="0"
                                         popupStyle="margin-left: 12px;background-color: #000066;z-index: 1200"
                                         fetchValue="#{FuncionalidadeControle.rotulo}"
                                         suggestionAction="#{FuncionalidadeControle.executarAutocompleteFuncionalidade}"
                                         minChars="1" rowClasses="20"
                                         status="true"
                                         nothingLabel="Nenhuma funcionalidade Encontrada!"
                                         styleClass="suggestionCampoBusca"
                                         var="result" id="suggestionCampoBuscaZWUI">
                        <a4j:support  event="onselect" onsubmit="setDocumentCookie('popupsImportante', 'close',1);setTimeout(function() { setDocumentCookie('popupsImportante', '',1); }, 1000);"
                                      action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                      actionListener="#{FuncionalidadeControle.abrirComAcaoListener}"
                                      oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                      focus="buscaFuncionalidade" reRender="textfield2,suggestionFuncionalidade,menuLateral">
                            <f:attribute name="paginaInicial" value="paginaInicial" />
                            <f:attribute name="tipoConsulta" value="parametrizado"/>
                            <f:attribute name="idLocalizacaoMenu" value="PESQUISA_RECURSO"/>
                        </a4j:support>
                        <h:column>
                            <h:graphicImage rendered="#{!result.cliente && result.funcionalidadeSistemaEnumAux.funcionalidadeSistemaEnum !=null}" style="width:28px;height:25px;" url="#{result.funcionalidadeSistemaEnumAux.funcionalidadeSistemaEnum.iconeModulo}"/>

                            <a4j:mediaOutput     element="img"
                                                 rendered="#{!SuperControle.fotosNaNuvem && result.cliente}"
                                                 align="left" style="left:0px;width:25px;height:25px; border:none;border-radius:5px; "
                                                 cacheable="false" session="false"

                                                 title="#{result.clienteVO.pessoa.nome}"
                                                 createContent="#{FuncionalidadeControle.paintFoto}"
                                                 styleClass="shadow"
                                                 value="#{ImagemData}" mimeType="image/jpeg" >
                                <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                <f:param name="largura" value="25"/>
                                <f:param name="altura" value="25"/>
                                <f:param name="pessoa" value="#{result.clienteVO.codigo}"></f:param>
                            </a4j:mediaOutput>
                            <h:graphicImage  rendered="#{SuperControle.fotosNaNuvem && result.cliente}"
                                             width="150" height="180"
                                             style="left:0px;width:25px;height:25px; border:none;border-radius:5px;"
                                             url="#{FuncionalidadeControle.paintFotoDaNuvem}">
                            </h:graphicImage>
                            <h:panelGroup layout="block" style="height: 25px" rendered="#{!result.cliente && result.funcionalidadeClienteEnumAux.funcionalidadeClienteEnum !=null}"/>
                        </h:column>

                        <h:column>

                            <h:panelGroup layout="block" rendered="#{!result.descricao and !result.subTitulo}" style="width: 250px;text-align: left;">
                                <h:outputText rendered="#{!result.cliente && result.funcionalidadeClienteEnumAux.funcionalidadeClienteEnum !=null}"
                                              style="font-size: 15px;text-align: right;" styleClass="fa-icon-arrow-right"/>
                                <h:panelGroup>
                                    <h:outputText
                                            style="text-align: left"
                                            styleClass="textSmallFlat"
                                            value="#{result.rotulo}"/>
                                    <div>
                                        <h:outputText rendered="#{result.cliente}"
                                                      style="text-align: left" value="#{result.clienteVO.empresa.nome}"/>
                                    </div>

                                    <h:panelGroup layout="block" rendered="#{result.cliente}">
                                        <h:outputText style="font-weight: bold; text-align: left; color:#555"
                                                      value="Mat:"/>
                                        <h:outputText style="padding-left: 5px; text-align: left"
                                                      value="#{result.clienteVO.matricula}"/>
                                        <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarCPFBusca && not empty result.clienteVO.pessoa.cfp}"
                                                      style="padding-left: 20px; font-weight: bold; text-align: left; color:#555"
                                                      value="CPF:"/>
                                        <h:outputText rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarCPFBusca && not empty result.clienteVO.pessoa.cfp}"
                                                      style="padding-left: 5px; text-align: left"
                                                      value="#{result.clienteVO.pessoa.cfp}"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:outputText style="text-align: left" rendered="#{result.autorizacaoCobranca}" value="#{result.itemDescricao }"/>

                            <h:panelGroup layout="block" rendered="#{result.descricao and !result.subTitulo}" style="width: 250px;text-align: left;height: 20px;vertical-align: baseline;line-height:19px">
                                <h:outputText style="text-align: left;color: #BBBDBF" styleClass="textSmallFlat" value="#{result.itemDescricao}"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" rendered="#{result.subTitulo}" style="width: 250px;text-align: left;height: 20px;vertical-align: baseline;line-height:19px">
                                <h:outputText style="text-align: left;color: #BBBDBF" styleClass="textSmallFlatSubTitle" value="#{result.itemDescricao}"/>
                            </h:panelGroup>
                        </h:column>


                    </rich:suggestionbox>
                </div>

                <div class="zw_ui_pacto-user">
                    <div class="section-menu">

                <h:panelGroup rendered="#{!LoginControle.usuarioLogado.usuarioPACTOBR}" styleClass="pull-right painelAlunosMarcadosNovoMenu"  id="painelAlunosMarcadosNovoMenu">
                            <rich:dragIndicator id="indicatorNovoMenu" >
                                <f:facet name="single">
                                    <h:panelGroup>
                                        {testDrop}
                                    </h:panelGroup>
                                </f:facet>
                            </rich:dragIndicator>
                            <a4j:repeat value="#{ClientesMarcadosControle.clientesMarcados}" var="clienteM">
                                <a4j:outputPanel>
                                    <rich:dragSupport  dragIndicator=":form:indicatorNovoMenu"
                                                       dragType="alunos" dragValue="#{clienteM}"
                                                       ondragstart="subirPainelRemoverAluno()"
                                                       ondragend="sumirPainelRemoverAluno()">
                                        <rich:dndParam name="testDrop">
                                            <h:graphicImage url="#{(SuperControle.fotosNaNuvem ? clienteM.pessoa.urlFoto : clienteM.pessoa.urlFotoContexto )}"
                                                            styleClass="imagemAluno pequena"/>
                                        </rich:dndParam>
                                    </rich:dragSupport>
                                    <div class="clienteMarcado" style="font-family: Arial; font-size: 14px;">
                                        <a4j:commandLink style="margin-left: 3px;" action="#{ClientesMarcadosControle.abrirCliente}"
                                                         styleClass="tooltipclientesMarcados">
                                            <h:graphicImage styleClass="imagemAluno pequena"
                                                            url="#{(SuperControle.fotosNaNuvem ? ClientesMarcadosControle.fotoNuvem : clienteM.pessoa.urlFotoContexto )}">
                                            </h:graphicImage>
                                        </a4j:commandLink>
                                        <div class="mensagemCmarc">
                                            <h:outputText value="#{clienteM.nomeLembrete}" escape="false"/>
                                        </div>
                                        <div class="cmarc">
                                            <a4j:commandLink title="Adicionar um lembrete"
                                                             styleClass="tooltipsterright"
                                                             onclick="jQuery('.cliente#{clienteM.codigo}').toggle()"
                                                             status="false">
                                                <i class="fa-icon-comments" style="line-height: 24px; margin-left: 5px;"></i>
                                            </a4j:commandLink>
                                            <h:panelGroup styleClass="caixaLembrete cliente#{clienteM.codigo}" layout="block"
                                            >
                                                <h:inputTextarea styleClass="inputTextClean" value="#{clienteM.observacao}"
                                                                 style="width: calc(100% - 20px); margin-top:10px; margin-left:10px; height: 70px;"></h:inputTextarea>
                                                <a4j:commandLink styleClass="botaoPrimarioSmall texto-size-14-real"
                                                                 action="#{ClientesMarcadosControle.gravarObservacao}"
                                                                 reRender="painelAlunosMarcadosNovoMenu"
                                                                 style="float: right; margin-right: 10px;margin-top: 5px">
                                                    Gravar
                                                </a4j:commandLink>
                                            </h:panelGroup>

                                        </div>
                                    </div>

                                </a4j:outputPanel>

                            </a4j:repeat>

                            <script>

                                carregarTooltipster();
                            </script>

                    </h:panelGroup>
                    <div class="topbar-actions-divider" style="margin-left: 6px"></div>

                    <h:panelGroup styleClass="central" layout="block"
                                  rendered="#{ConsultaClienteControle.apresentarVoltarParaCliente && LoginControle.permissaoAcessoMenuVO.cliente && !LoginControle.usuarioLogado.usuarioPACTOBR}">
                        <a4j:commandLink id="voltarUltmoClienteSessaos"
                                         action="#{ConsultaClienteControle.voltarParaTelaCliente}"
                                         styleClass="linkPadrao" style="margin-left: 3px;"
                                         title="Voltar para Cliente">
                            <i class="pct fa-icon-undo" style="color: #1B4166; font-size: 2em"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" id="nrMensagensLidas" styleClass="topbar-menu-item">
                        <a4j:commandLink action="#{LoginControle.abrirSocialMailing}" reRender="nrMensagensLidas"
                                         id="nrMensagensLidasPesquisaLink"
                                         oncomplete="#{LoginControle.msgAlert}" style="display: block;text-decoration: none;">
                            <i class="pct pct-message-circle"></i>
                        </a4j:commandLink>
                        <h:panelGroup layout="block"
                                      rendered="#{UsuarioControle.usuario.nrMensagensNaoLidas > 0}"
                                      style="height: 0;">
                            <a4j:commandLink styleClass="notificacaoSocialMailing" id="nrMensagensNaoLidas"
                                             action="#{LoginControle.abrirSocialMailing}"
                                             oncomplete="#{LoginControle.msgAlert}"
                                             reRender="nrMensagensLidas"
                                             value="#{UsuarioControle.usuario.nrMensagensNaoLidas}"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <div class="topbar-menu-item assinaturaTrigger" id="topbar-menu-item-qrcode" onclick="abrirAssinatura(event);">
                        <a>
                            <i  class="pct pct-qr-code"></i>
                        </a>
                    </div>

                <c:if test="${not SuperControle.pontoInterrogacaoHabilitado}">
                    <div class="topbar-menu-item">
                        <h:outputLink value="https://pactosolucoes.com.br/ajuda/"
                                      title="Central de ajuda" target="_blank" styleClass="tooltipster">
                                <i class="pct pct-help-circle"></i>
                            </h:outputLink>
                        </div>
                    </c:if>
                    <c:if test="${SuperControle.pontoInterrogacaoHabilitado}">
                        <div id="idpontointerrogacao" class="topbar-menu-item" onclick="abrirPontoInterrogacao()" style="color:#1E60FA;">
                            <a style="color:#1E60FA;">
                                <i class="pct pct-help-circle"></i>
                            </a>
                        </div>
                    </c:if>

                <h:panelGroup rendered="#{LoginControle.usuarioLogado.usuarioPACTOBR}">
                <div class="central novidades" style="place-content: center;"
                        onclick="window.open('https://pactosolucoes.com.br/ajuda/kb/novidades/')"
                >
                    <div style="place-self: center;">
                            <h:graphicImage style="color: #264D67" url="/imagens_flat/pct-bellnovidade.svg" width="90%" height="80%"/>
                        </div>
                </div>
                </h:panelGroup>
                <div id="divAbrirUsuario" class="background"  onclick="abrirUsuario(event)">
                    <div class="user-section">
                        <a class="user-menu" title="${SuperControle.usuarioLogado.nome} (${SuperControle.perfilUsuarioLogado.nome})">
                            <h:graphicImage id="fotosNaNuvemTrueZwui"
                                            style="width: 35px; height: 35px"
                                            title="#{SuperControle.titleUsuario}"
                                            url="#{SuperControle.fotoKeyUsuarioLogado}">
                            </h:graphicImage>
                        </a>
                        <i class="pct pct-chevron-up"></i>
                        <i class="pct pct-chevron-down"></i>
                    </div>
                </div>

            </div>
        </div>
    </div>
        </div>

    <style>
        .menu-modulo {
            background: #FAFAFA;
            border-radius: 4px;
            min-width: 220px;
            margin-top: 16px;
            font-weight: 400;
            font-size: 14px;
            line-height: 125%;
            color: #51555A;
        }
    </style>
    <h:panelGroup layout="block" styleClass="caixa-modulos-zw-ui #{LoginControle.usuarioPacto ? 'somente-pactobr' : ''}"
                  id="caixa-modulos-zw-ui">
        <div class="aux">
            <div class="modulos-disponiveis">
                <div class="modulos-list">

                        <script>
                            const moduloAberto = '${LoginControle.moduloAberto.sigla}';
                            document.addEventListener('DOMContentLoaded', function() {
                                const modulos = document.getElementsByClassName("menu-modulo");
                                for (let i = 0; i < modulos.length; i++) {
                                    modulos[i].addEventListener('mouseenter', function(e){
                                        const todosMenusModulo = document.getElementsByClassName('menus-modulo');
                                        for (let i = 0; i < todosMenusModulo.length; i++) {
                                            todosMenusModulo[i].style.visibility = 'hidden';
                                        }

                                        let moduloId = e.target.classList[1];
                                        const menusModuloSelecionado = document.getElementsByClassName('menus-'+moduloId);
                                        for (let i = 0; i < menusModuloSelecionado.length; i++) {
                                            menusModuloSelecionado[i].style.visibility = 'visible';
                                        }
                                    });
                                }
                            }, false);
                            function menuOnMouseEnter(e){
                            }
                        </script>

                        <a4j:repeat value="#{MenuControle.gruposMenuExplorar}" var="menu">
                            <h:panelGroup styleClass="menu-modulo modulo-#{menu.modulo.siglaModulo}" layout="block" >
                                <h:panelGroup styleClass="modulo-aberto" rendered="#{LoginControle.moduloAberto.sigla == menu.modulo.siglaModulo}" />
                                <a4j:commandLink action="#{MenuControle.abrirModulo}"
                                               oncomplete="#{MenuControle.oncompleteModulo}"
                                               style="font-size: 20px;"
                                               styleClass="modulo">
                                    <div class="img-aux">
                                        <h:graphicImage url="#{context}/#{menu.modulo.iconeRelaiveUrl}" />
                                    </div>
                                    <div class="nome"><h:outputText value="#{menu.modulo.descricao}"/></div>
                                    <f:param name="siglaModulo" value="#{menu.modulo.siglaModulo}" />
                                </a4j:commandLink>
                            </h:panelGroup>
                        </a4j:repeat>

                    </div>
                </div>
                <style>
                    .modulo-aberto {
                        background: #1B4166;
                        border-radius: 4px 0px 0px 4px;
                        width: 4px;
                        height: 56px;
                        position: absolute;
                    }
                    .menus-modulo {
                        display: flex;
                        color: #51555A;
                        font-family: 'Nunito Sans';
                        font-style: normal;
                        position: absolute;
                        top: 20px;
                    }

                    .menus-modulo-grupo > ul {
                        padding-inline-start: 12px;
                        min-width: 200px;
                    }

                    .menus-modulo-grupo > ul > lh {
                        font-weight: 700;
                        font-size: 16px;
                        line-height: 100%;
                        margin-top: 12px;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        padding: 12px;
                        max-width: 220px;
                    }

                .menus-modulo-grupo > ul > li {
                    background: #FAFAFA;
                    border-radius: 4px;
                    margin-top: 8px;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    padding: 12px;
                    max-width: 220px;
                    cursor: pointer;
                }
                .menus-modulo-grupo > ul > li > a {
                    text-decoration: none;
                    font-family: 'Nunito Sans';
                    font-style: normal;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 125%;
                    color: #51555A;
                }
                .menus-modulo-menu:hover {
                    background-color: #C7C9CC;
                }
            </style>
            <script>
                function menuItemOnClick(e){
                    for (let i = 0; i < e.children.length; i++) {
                        if(e.children[i].tagName === "A"){
                            e.children[i].click();
                        }
                    }
                }
            </script>
            <c:if test="${MenuControle.usuarioPacto}">
                <div class="modulo-menu-list">
                <a4j:repeat value="#{MenuControle.gruposMenuExplorar}" var="modulo">
                    <h:panelGroup
                            layout="block"
                            style="#{LoginControle.moduloAberto.sigla == modulo.modulo.siglaModulo ? 'visibility: visible;' : 'visibility: hidden;'}"
                            styleClass="menus-modulo menus-modulo-#{modulo.modulo.siglaModulo}">
                        <a4j:repeat value="#{modulo.menuExplorarConfigs}" var="grupo">
                            <h:panelGroup layout="block" styleClass="menus-modulo-grupo">
                                <ul>
                                    <lh><h:outputText value="#{grupo.grupo.descricao}"/></lh>
                                    <a4j:repeat value="#{grupo.funcionalidadesOld}" var="menu">
                                        <li class="menus-modulo-menu" onclick="menuItemOnClick(this)">
                                            <a4j:commandLink value="#{empty menu.descricaoMenulateral ? menu.descricao : menu.descricaoMenulateral}"
                                                             styleClass="menu-#{menu.name}"
                                                             title="#{menu.descricao}"
                                                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                             oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                             reRender="#{menu.reRenderElement}">
                                                <f:attribute name="funcionalidade" value="#{menu.name}" />
                                                <f:param name="funcionalidadeAberta" value="#{menu.name}"/>
                                                <f:attribute name="idLocalizacaoMenu" value="PESQUISA_RECURSO"/>
                                            </a4j:commandLink>
                                        </li>
                                    </a4j:repeat>
                                </ul>
                            </h:panelGroup>
                        </a4j:repeat>
                    </h:panelGroup>
                </a4j:repeat>
            </div>
            </c:if>
        </div>
    </h:panelGroup>
    <script>
        var img = document.getElementById('form:fotosNaNuvemTrueZwui');
        if (img.src && img.src.includes('fotoPadrao.jpg')) {
            img.style.display = 'none';
            var imgPacto = document.getElementById('form:fotoUsuarioPadrao');
            imgPacto.style.display = 'block';
        }
        img.addEventListener('error', function handleError() {
            img.style.display = 'none';
            var imgPacto = document.getElementById('form:fotoUsuarioPadrao');
            imgPacto.style.display = 'block';
        });
    </script>
</c:if>

<style>
    .botao-lista-rapida-acesso i{
        font-size: 16px;
        color: #ffffff;
    }
    .botao-lista-rapida-acesso {
        z-index: 9999;
        position: fixed;
        top: 10vh;
        right: 0;
    }
    .botao-lista-rapida-acesso div{
        background: #1D3263;
        text-align: center;
        width: 30px;
        height: 30px;
        align-items: center;
        display: grid;
        padding: 4px 1px;
        border-radius: 6px 0 0 6px;
    }
</style>

<a4j:commandLink oncomplete="abrirGavetaAcessos()"
                 styleClass="botao-lista-rapida-acesso"
                 reRender="painel-token-nt"
                 action="#{LoginControle.abrirListaRapidaAcessos}">
    <div>
        <i class="pct pct-walk"></i>
    </div>
</a4j:commandLink>

<%--
    Document   : gestaoTransacoes
    Created on : 02/08/2011, 18:38:25
    Author     : Waller
--%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<%@include file="/includes/imports.jsp" %>

<script type="text/javascript" src="script/packJQueryPlugins.min.js" ></script>
<script type="text/javascript" src="script/cursor.js"></script>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<style type="text/css">
    .rich-panel-body {
        padding: 0 0 0 0;
        margin: 0 0 0 0;
    }
</style>

<head>
    <jsp:include page="include_head.jsp" flush="true" />
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Transações"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">

        <html>
            <body>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">


                                <h:panelGroup layout="block" styleClass="container-box container-conteudo-central zw_ui especial">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="Gestão de Transações" styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlWiki}Operacional:Recorrência#Gest.C3.A3o_de_Transa.C3.A7.C3.B5es"
                                                          title="Clique e saiba mais: Gestão de Transações" target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>

                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup  layout="block" styleClass="margin-box novaModal" id="panelGeralGestaoTransacoes">

                                            <h:panelGroup id="showSaldoTransacao"
                                                          rendered="#{GestaoTransacoesControle.empresaCreditoVO.tipoCobrancaPactoPrePago or GestaoTransacoesControle.empresaCreditoVO.tipoCobrancaPactoPrePagoEfetivado or GestaoTransacoesControle.empresaCreditoVO.tipoCobrancaPactoPrePagoRede}">
                                                <fieldset style="height: 100%;vertical-align: top;">
                                                    <legend class="legend">Saldo de Transações</legend>
                                                    <h:panelGroup layout="block" id="creditoPacto">
                                                        <h:panelGrid columns="2" style="vertical-align:top;"
                                                                     rowClasses="remessasOpcoesFix">
                                                            <h:panelGroup>
                                                                <h:outputText styleClass="tituloCampos"
                                                                              style="#{GestaoTransacoesControle.styleSituacaoCreditoPacto}"
                                                                              value="#{GestaoTransacoesControle.situacaoCreditoPacto}"/>
                                                            </h:panelGroup>
                                                        </h:panelGrid>
                                                        <h:panelGrid columns="2" style="vertical-align:top;"
                                                                     rowClasses="remessasOpcoesFix">
                                                            <h:panelGroup>
                                                                <h:outputText styleClass="tituloCampos"
                                                                              style="#{GestaoTransacoesControle.styleSituacaoDataCreditoPacto}"
                                                                              value="#{GestaoTransacoesControle.situacaoDataCreditoPacto}"/>
                                                            </h:panelGroup>
                                                        </h:panelGrid>
                                                    </h:panelGroup>
                                                </fieldset>
                                            </h:panelGroup>

                                        <h:panelGroup id="panelConteudo">
                                            <h:panelGrid cellpadding="0" cellspacing="0" columns="1" style="width: 100%;">
                                                <h:panelGroup>
                                                    <fieldset style="height: 100%;vertical-align: top;">
                                                        <legend>Opções</legend>
                                                        <h:panelGroup layout="block" style="vertical-align:top;"
                                                                      id="panelFiltros">

                                                            <c:if test="${GestaoTransacoesControle.usuarioLogado.administrador || GestaoTransacoesControle.consultarInfoTodasEmpresas}">
                                                                <h:outputText styleClass="tituloCampos"
                                                                              style="vertical-align:middle;"
                                                                              value="Empresa:"/>
                                                                <div class="cb-container"
                                                                     style="margin-left: 5px; font-size: 11px !important; margin-right: 15px;">
                                                                    <h:selectOneMenu id="empresaSelecionada"
                                                                                     styleClass="form"
                                                                                     value="#{GestaoTransacoesControle.empresaFiltro}">
                                                                        <f:selectItems
                                                                                value="#{GestaoTransacoesControle.listaSelectItemEmpresa}"/>
                                                                        <a4j:support
                                                                                event="onchange"
                                                                                action="#{GestaoTransacoesControle.selecionouEmpresa}"
                                                                                reRender="panelConteudo, showSaldoTransacao"/>
                                                                    </h:selectOneMenu>
                                                                </div>
                                                            </c:if>

                                                            <h:panelGroup>
                                                                <h:outputText styleClass="tituloCampos"
                                                                              style="vertical-align:middle;"
                                                                              value="De: "/>
                                                                <h:panelGroup styleClass="dateTimeCustom"
                                                                              style="font-size: 11px !important;">
                                                                    <rich:calendar id="dataInicio"
                                                                                   value="#{GestaoTransacoesControle.dataInicio}"
                                                                                   locale="#{SuperControle.localeDefault}"
                                                                                   inputSize="10"
                                                                                   inputClass="form"
                                                                                   oninputblur="blurinput(this);"
                                                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false">
                                                                        <f:convertDateTime pattern="dd/MM/yyyy"
                                                                                           locale="#{SuperControle.localeDefault}"
                                                                                           timeZone="#{SuperControle.timeZoneDefault}"/>
                                                                    </rich:calendar>
                                                                </h:panelGroup>
                                                            </h:panelGroup>

                                                            <h:panelGroup style="margin-left: 10px;">
                                                                <h:outputText styleClass="tituloCampos"
                                                                              style="vertical-align:middle;"
                                                                              value="até"/>
                                                                <h:panelGroup styleClass="dateTimeCustom"
                                                                              style="font-size: 11px !important; margin-left: 10px;">
                                                                    <rich:calendar id="dataTermino"
                                                                                   value="#{GestaoTransacoesControle.dataFim}"
                                                                                   locale="#{SuperControle.localeDefault}"
                                                                                   inputSize="10"
                                                                                   inputClass="form"
                                                                                   oninputblur="blurinput(this);"
                                                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                                                   oninputfocus="focusinput(this);"
                                                                                   oninputchange="return validar_Data(this.id);"
                                                                                   datePattern="dd/MM/yyyy"
                                                                                   enableManualInput="true"
                                                                                   zindex="2"
                                                                                   showWeeksBar="false">
                                                                        <f:convertDateTime pattern="dd/MM/yyyy"
                                                                                           locale="#{SuperControle.localeDefault}"
                                                                                           timeZone="#{SuperControle.timeZoneDefault}"/>
                                                                    </rich:calendar>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                            <rich:jQuery id="mskData" selector=".rich-calendar-input"
                                                                         timing="onload" query="mask('99/99/9999')"/>
                                                            <div class="cb-container"
                                                                 style="margin-left: 10px; font-size: 11px !important;">
                                                                <h:selectOneMenu id="selectSituacaoTrans"
                                                                                 styleClass="form tooltipster"
                                                                                 title="Situação da transação"
                                                                                 value="#{GestaoTransacoesControle.situacaoSelecionada}">
                                                                    <f:selectItems
                                                                            value="#{GestaoTransacoesControle.listaSituacoes}"/>
                                                                </h:selectOneMenu>
                                                            </div>

                                                            <div class="cb-container"
                                                                 style="margin-left: 10px; font-size: 11px !important;">
                                                                <h:selectOneMenu id="selectPlataformaPagamento"
                                                                                 styleClass="form tooltipster"
                                                                                 title="Plataforma"
                                                                                 value="#{GestaoTransacoesControle.tipoTransacao}">
                                                                    <f:selectItems
                                                                            value="#{GestaoTransacoesControle.listaTipoTransacao}"/>
                                                                </h:selectOneMenu>
                                                            </div>

                                                            <c:if test="${GestaoTransacoesControle.apresentarFiltroRetornoPacto}">
                                                                <div class="cb-container"
                                                                     style="margin-left: 10px; font-size: 11px !important;">
                                                                    <h:selectOneMenu id="selectRetornoPacto"
                                                                                     styleClass="form"
                                                                                     value="#{GestaoTransacoesControle.retornoPacto}">
                                                                        <f:selectItems
                                                                                value="#{GestaoTransacoesControle.listaRetornoPacto}"/>
                                                                    </h:selectOneMenu>
                                                                </div>
                                                            </c:if>

                                                            <a4j:commandLink id="consultarTransacao" value="Consultar"
                                                                             styleClass="botoes nvoBt"
                                                                             style="vertical-align: text-bottom;"
                                                                             reRender="form:panelGeralGestaoTransacoes"
                                                                             action="#{GestaoTransacoesControle.consultarTransacoes}"
                                                                             oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"/>

                                                            <br/>

                                                            <h:panelGroup layout="block" styleClass="tituloCampos"
                                                                          style="padding-top: 10px; text-align: left;">
                                                                <h:outputText style="vertical-align:middle;"
                                                                              value="Filtrar: "/>
                                                                <h:inputText
                                                                        style="vertical-align:middle; height: 28px;"
                                                                        styleClass="form tooltipster"
                                                                        title="Digite algo para filtrar..."
                                                                        onkeypress="enterPesquisa(event);"
                                                                        size="18"
                                                                        id="searchInput"
                                                                        value="#{GestaoTransacoesControle.filtro}">
                                                                </h:inputText>

                                                                <a4j:commandLink id="consultarFiltro"
                                                                                 style="margin: 0 5px; font-size: 20px;"
                                                                                 action="#{GestaoTransacoesControle.consultarTransacoes}"
                                                                                 oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                                                                 reRender="panelConteudo"
                                                                                 styleClass="tooltipster"
                                                                                 title="#{msg_aplic.FILTRAR}...">
                                                                    <i class="fa-icon-search"></i>
                                                                </a4j:commandLink>

                                                                <a4j:commandLink id="limparFiltro"
                                                                                 style="margin: 0 5px; font-size: 20px;"
                                                                                 action="#{GestaoTransacoesControle.limparFiltro}"
                                                                                 reRender="panelConteudo"
                                                                                 styleClass="tooltipster"
                                                                                 title="#{msg_aplic.prt_limparCampo}">
                                                                    <i class="fa-icon-eraser"></i>
                                                                </a4j:commandLink>

                                                                <h:graphicImage id="imageLoading"
                                                                                style="visibility: hidden;vertical-align: middle;align:center"
                                                                                url="images/loading.gif"/>

                                                                <h:panelGroup layout="block" style="display: flex; align-items: center; padding-top: 10px;"
                                                                              rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                                                                    <h:selectBooleanCheckbox
                                                                            value="#{GestaoTransacoesControle.apresentarCobrancaVerificarCartao}"/>
                                                                    <h:outputText style="vertical-align:middle;"
                                                                                  styleClass="tooltipster"
                                                                                  value="Apresentar transações de verificação de cartão"
                                                                                  title="Marque caso queira ver as transações oriundas de verificação de cartão. Toda vez que um cartão é adicionado no sistema será realizado uma tentativa de cobrança de verificação no valor entre R$1,00 e R$2,50. A cobrança é estornada logo em seguida. "/>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                        </h:panelGroup>
                                                    </fieldset>
                                    </h:panelGroup>
                                </h:panelGrid>

                                <h:panelGrid columns="1" width="100%">

                                    <h:panelGroup>

                                        <%@include file="includes/transacoes/include_table_transacoes.jsp" %>

                                        <h:panelGrid rendered="#{not empty GestaoTransacoesControle.totalQuantidadeAgrupadoPorSituacao}"
                                                     columns="4" rowClasses="super-vertical">
                                            <rich:dataTable
                                                    value="#{GestaoTransacoesControle.totalQuantidadeAgrupadoPorSituacao}"
                                                    var="grupo">
                                                <f:facet name="header">
                                                    <h:outputText value="TOTALIZADOR"/>
                                                </f:facet>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value=""/>
                                                    </f:facet>
                                                    <h:panelGroup layout="block"
                                                                  style="border:none; width: 11px; height: 11px; background-color:#{grupo.situacao.imagem}"/>
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Situação"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="tooltipster"
                                                                  title="#{grupo.situacao.hint}"
                                                                  value="#{grupo.situacao.descricao}"/>
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Qtd"/>
                                                    </f:facet>
                                                    <h:outputText value="#{grupo.quantidade}"/>
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Valor R$"/>
                                                    </f:facet>
                                                    <h:outputText value="#{grupo.valor_Apresentar}"/>
                                                </rich:column>

                                                <f:facet name="footer">
                                                    <h:outputText value="Qtd #{GestaoTransacoesControle.quantidadeTotal_TotalQuantidadeAgrupadoPorSituacao} - Total #{GestaoTransacoesControle.valorTotal_TotalQuantidadeAgrupadoPorSituacao}"/>
                                                </f:facet>
                                            </rich:dataTable>

                                            <h:panelGroup layout="block" id="panelTotalizadorTransacoesPorTipo"
                                                          rendered="#{GestaoTransacoesControle.totalizadorTransacoesPorTipo_Size > 1}"
                                                          style="display: flex">
                                                <a4j:repeat
                                                        value="#{GestaoTransacoesControle.totalizadorTransacoesPorTipo}"
                                                        var="tipo">
                                                    <h:panelGroup layout="block"
                                                                  id="panelTipo${tipo.tipo.name}"
                                                                  style="padding-left: 10px">
                                                        <rich:dataTable
                                                                value="#{tipo.erros}"
                                                                var="grupo">

                                                            <f:facet name="header">
                                                                <h:outputText value="#{tipo.tipo_Apresentar}"/>
                                                            </f:facet>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value=""/>
                                                                </f:facet>
                                                                <h:panelGroup layout="block"
                                                                              style="border:none; width: 8px; height: 8px; background-color:#{grupo.situacao.imagem}"/>
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="Situação"/>
                                                                </f:facet>
                                                                <h:outputText styleClass="tooltipster"
                                                                              title="#{grupo.situacao.hint}"
                                                                              value="#{grupo.situacao.descricao}"/>
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="Qtd"/>
                                                                </f:facet>
                                                                <h:outputText value="#{grupo.quantidade}"/>
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="Valor R$"/>
                                                                </f:facet>
                                                                <h:outputText value="#{grupo.valor_Apresentar}"/>
                                                            </rich:column>

                                                            <f:facet name="footer">
                                                                <h:outputText value="Qtd #{tipo.quantidadeTotal_Apresentar} - Total #{tipo.valorTotal_Apresentar}"/>
                                                            </f:facet>
                                                        </rich:dataTable>
                                                    </h:panelGroup>
                                                </a4j:repeat>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block"
                                                          rendered="#{not empty GestaoTransacoesControle.totalParcelasAgrupadoPorSituacao}"
                                                          id="paneltotalParcelasAgrupadoPorSituacao"
                                                          style="padding-left: 10px">
                                                <rich:dataTable value="#{GestaoTransacoesControle.totalParcelasAgrupadoPorSituacao}"
                                                                var="grupo">
                                                    <f:facet name="header">
                                                        <a4j:commandLink id="detalhesParcelas"
                                                                         styleClass="linkPadrao tooltipster"
                                                                         action="#{GestaoTransacoesControle.listarParcelas}"
                                                                         title="Detalhes"
                                                                         oncomplete="#{GestaoTransacoesControle.mensagemDetalhada} abrirPopup('./gestaoTransacoesDetalhesParcelas.jsp', 'ListaParcelas', 1024, 800);"
                                                                         value="PARCELAS"/>
                                                    </f:facet>

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Situação"/>
                                                        </f:facet>
                                                        <h:outputText value="#{grupo.situacao_Apresentar}"/>
                                                    </rich:column>

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Qtd"/>
                                                        </f:facet>
                                                        <h:outputText value="#{grupo.quantidade}"/>
                                                    </rich:column>

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Valor R$"/>
                                                        </f:facet>
                                                        <h:outputText value="#{grupo.valor_Apresentar}"/>
                                                    </rich:column>

                                                    <f:facet name="footer">
                                                        <h:outputText
                                                                value="Total #{GestaoTransacoesControle.somaValoresParcelas}"/>
                                                    </f:facet>
                                                </rich:dataTable>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block"
                                                          rendered="#{not empty GestaoTransacoesControle.totalRepescagemPorSituacao}"
                                                          id="paneltotalRepescagemPorSituacao"
                                                          style="padding-left: 10px">
                                                <rich:dataTable value="#{GestaoTransacoesControle.totalRepescagemPorSituacao}"
                                                                var="grupo">
                                                    <f:facet name="header">
                                                        <h:outputText value="Repescagem"/>
                                                    </f:facet>

                                                    <rich:column>
                                                        <h:panelGroup layout="block"
                                                                      style="border:none; width: 8px; height: 8px; background-color:#{grupo.situacao.imagem}"/>
                                                    </rich:column>
                                                    <rich:column>
                                                        <h:outputText title="#{grupo.situacao.hint}"
                                                                      value="#{grupo.situacao.descricao}"/>
                                                    </rich:column>

                                                    <rich:column>
                                                        <h:outputText value="#{grupo.quantidade}"/>
                                                    </rich:column>
                                                    <rich:column>
                                                        <h:outputText value="#{grupo.valor_Apresentar}"/>
                                                    </rich:column>
                                                    <f:facet name="footer">
                                                        <h:outputText
                                                                value="Total #{GestaoTransacoesControle.somaValoresRepescagem}"/>
                                                    </f:facet>
                                                </rich:dataTable>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block" id="panelListaErrosTipoTransacao"
                                                          rendered="#{not empty GestaoTransacoesControle.listaErrosTipoTransacao}"
                                                          style="display: flex">
                                                <a4j:repeat
                                                        value="#{GestaoTransacoesControle.listaErrosTipoTransacao}"
                                                        var="tipo">
                                                    <h:panelGroup layout="block"
                                                                  id="panelTipo${tipo.tipo.name}"
                                                                  style="padding-left: 10px">
                                                        <rich:dataTable
                                                                value="#{tipo.erros}"
                                                                var="erro">

                                                            <f:facet name="header">
                                                                <h:outputText value="#{tipo.tipo_Apresentar}"/>
                                                            </f:facet>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="Cod. Retorno"/>
                                                                </f:facet>
                                                                <h:outputText
                                                                        styleClass="tooltipster"
                                                                        title="#{erro.msgErro}"
                                                                        value="#{erro.codErro}"/>
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="Qtd"/>
                                                                </f:facet>
                                                                <h:outputText value="#{erro.quantidade}"/>
                                                            </rich:column>

                                                            <rich:column>
                                                                <f:facet name="header">
                                                                    <h:outputText value="Valor R$"/>
                                                                </f:facet>
                                                                <h:outputText value="#{erro.valor_Apresentar}"/>
                                                            </rich:column>

                                                            <f:facet name="footer">
                                                                <h:outputText
                                                                        value="Total #{tipo.valorTotal_Apresentar}"/>
                                                            </f:facet>
                                                        </rich:dataTable>
                                                    </h:panelGroup>
                                                </a4j:repeat>
                                            </h:panelGroup>

                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
                    <c:if test="${SuperControle.menuZwUi}">
                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-INICIO" />
                        </jsp:include>
                    </c:if>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>
    </h:form>
    <%@include file="includes/transacoes/include_paramspanel_transacao.jsp" %>
    <%@include file="include_modal_trocarCartaoRecorrencia.jsp" %>
    <%@include file="includes/parcelas/include_modal_alterarVencimentoParcelas.jsp" %>
</f:view>
<script>
    try {
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    } catch (err) {
    }

    function enterPesquisa(event){
        if(event.keyCode === 13){
            console.log('enterPesquisa...');
            document.getElementById('form:consultarFiltro').click();
            event.keyCode = 0;
            event.returnValue = false;
        }
    }

</script>

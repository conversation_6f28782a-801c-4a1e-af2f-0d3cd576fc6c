<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_QuestionarioCliente_tituloForm}"/>
    </title>
    <h:form id="form">
        <html>
            <jsp:include page="include_head.jsp" flush="true" />
            <body>
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                    <tr>
                        <td align="left" valign="top" >
                            <h:panelGrid width="100%">
                                <f:facet name="header">

                                    <jsp:include page="topoReduzido.jsp"/>

                                </f:facet>
                            </h:panelGrid>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                                <td align="left" valign="top" style="padding:7px 15px 0 20px;">
                                    <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" >
                                        <tr>
                                            <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"></td>
                                            <td align="left" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Question&aacute;rio</td>
                                            <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"></td>
                                        </tr>
                                        <tr>
                                            <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif"></td>
                                            <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;">
                                                <!-- inicio item -->

                                                <table width="100%" align="left" height="100%" border="0" cellpadding="0" cellspacing="0" style="margin-bottom:25px;">
                                                    <tr>
                                                        <td width="60%" align="left" valign="top" style="padding:0 10px 10px 0;">
                                                            <h:outputText id="tituloBV" style="font: 12pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;" value="#{QuestionarioClienteCRMControle.questionarioClienteVO.questionario.nomeInterno}"/>
                                                            <rich:spacer style="display:block" height="10px"/>
                                                            <rich:dataTable id="questionarioCliente"  value="#{QuestionarioClienteCRMControle.questionarioClienteVO.questionarioPerguntaClienteVOs}" var="pergunta"  width="100%">
                                                                <rich:column>
                                                                    <p style="font-weight: bold;line-height:100%;"><br>  <br/></p>
                                                                    <img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                                                    <h:outputText styleClass="tituloCampos" value="#{pergunta.perguntaCliente.descricao}" />
                                                                    <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                                                        <h:dataTable value="#{pergunta.perguntaCliente.respostaPergClienteVOs}" var="repostaPergCliente" rowClasses="linhaImpar" width="100%" >
                                                                            <h:column>
                                                                                <h:selectBooleanCheckbox rendered="#{pergunta.perguntaCliente.multipla}" value="#{repostaPergCliente.respostaOpcao}" style="vertical-align:middle;margin-right:4px;"/>
                                                                                <h:outputText rendered="#{pergunta.perguntaCliente.multipla}" value="#{repostaPergCliente.descricaoRespota}"/>

                                                                            <h:inputTextarea rendered="#{pergunta.perguntaCliente.textual}"  value="#{repostaPergCliente.descricaoRespota}"  rows="3" cols="40" onblur="blurinput(this);"  onfocus="focusinput(this);"  />
                                                                            <h:outputText rendered="#{pergunta.perguntaCliente.textual}" />

                                                                            <h:selectBooleanCheckbox  rendered="#{pergunta.perguntaCliente.simples}" value="#{repostaPergCliente.respostaOpcao}" style="vertical-align:middle;margin-right:4px;" >
                                                                                <a4j:support eventsQueue="onclick" action="#{QuestionarioClienteCRMControle.escolhaSimples}" />
                                                                            </h:selectBooleanCheckbox>
                                                                            <h:outputText rendered="#{pergunta.perguntaCliente.simples}" value="#{repostaPergCliente.descricaoRespota}"/>
                                                                        </h:column>
                                                                    </h:dataTable>
                                                                </rich:column>
                                                                <%--f:facet name="footer">
                                                                        <rich:datascroller for="questionarioCliente" maxPages="10"></rich:datascroller>
                                                                    </f:facet--%>
                                                            </rich:dataTable>

                                                        </td>
                                                        <td align="center" valign="top" width="40%"  class="sombrapreview" style="padding:10px;">
                                                            <!-- inicio box -->
                                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                                <tr>
                                                                    <td width="5" height="9" align="left" valign="top"><img src="images/box_usuario_top_left.png" width="9" height="9"></td>
                                                                    <td height="9" align="left" valign="top" bgcolor="#ffffff"><img src="images/shim.gif" width="1" height="1"></td>
                                                                    <td width="9" align="left" valign="top"><img src="images/box_usuario_top_right.png" width="9" height="9"></td>
                                                                </tr>
                                                                <tr>
                                                                    <td align="left" valign="top" bgcolor="#ffffff">&nbsp;</td>
                                                                    <td align="left" valign="top" bgcolor="#ffffff">
                                                                        <table width="100%" border="0" cellpadding="0" cellspacing="0" bgcolor="#ffffff">
                                                                            <tr>
                                                                                <td align="center" valign="top" style="padding-bottom:15px;">
                                                                                    <a4j:outputPanel id="panelFoto">
                                                                                        <a4j:mediaOutput element="img" id="imagem1"  style="width:150px;height:180px "  cacheable="false" session="true"
                                                                                                         rendered="#{!SuperControle.fotosNaNuvem}" 
                                                                                                         createContent="#{ClienteControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                                                                                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                                                                            <f:param name="largura" value="150"/>
                                                                                            <f:param name="altura" value="180"/>
                                                                                        </a4j:mediaOutput>
                                                                                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                                                                                        width="150" height="180"                                        
                                                                                                        style="width:150px;height:180px"
                                                                                                        url="#{ClienteControle.paintFotoDaNuvem}">                            
                                                                                        </h:graphicImage>
                                                                                    </a4j:outputPanel>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td align="left" valign="top">
                                                                                    <table width="100%" border="0" cellspacing="0" cellpadding="2" class="textsmall">
                                                                                        <tr>
                                                                                            <td align="right" valign="middle"><span style="font-weight: bold">Nome do Aluno:</span></td>
                                                                                            <td align="left" valign="middle"><h:outputText  value="#{ClienteControle.pessoaVO.nome}" /></td>
                                                                                        </tr>
                                                                                        <%--  <tr>
                                                                                                <td align="right" valign="middle"><span style="font-weight: bold">Data do Boletim de Visita:</span></td>
                                                                                                <td align="left" valign="middle"><input onBlur="blurinput(this);" onFocus="focusinput(this);" name="textfield5" type="text" class="form" style="width:80px;vertical-align:middle;" value="13/08/2008"><a href="javascript:;"><img border="0" style="margin-left:4px;vertical-align:middle;" src="images/icon_calendar.gif" title="Data de hoje" width="16" height="16"></a></td>
                                                                                        </tr>--%>
                                                                                        <tr>
                                                                                            <td align="right" valign="middle"><span style="font-weight: bold"> <h:outputText  value="#{msg_aplic.prt_QuestionarioCliente_consultor}" /></span></td>
                                                                                            <td align="left" valign="middle">
                                                                                                <h:panelGroup id="consultor" layout="block">
                                                                                                    <c:choose>
                                                                                                        <c:when test="${QuestionarioClienteCRMControle.usuarioLogado.administrador}">
                                                                                                            <h:selectOneMenu  onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form"  value="#{QuestionarioClienteControle.questionarioClienteVO.consultor.codigo}" >
                                                                                                                <f:selectItems  value="#{QuestionarioClienteCRMControle.listaSelectConsultor}" />
                                                                                                            </h:selectOneMenu>
                                                                                                            <a4j:commandButton id="atualizar_consultor" rendered="#{QuestionarioClienteCRMControle.apresentarBotaoAtualizar}" action="#{QuestionarioClienteControle.montarListaSelectItemConsultor}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:consultor"/></td>
                                                                                                        </c:when>
                                                                                                        <c:otherwise>
                                                                                                            <h:outputText value="#{QuestionarioClienteCRMControle.questionarioClienteVO.consultor.pessoa.nome}"/>
                                                                                                        </c:otherwise>
                                                                                                    </c:choose>
                                                                                                </h:panelGroup></td>
                                                                                        </tr>
                                                                                        <%--<tr>
                                                                                                <td align="right" valign="middle"><span style="font-weight: bold"> <h:outputText value="#{msg_aplic.prt_QuestionarioCliente_questionario}" /></span></td>
                                                                                                <td align="left" valign="middle">
                                                                                                    <h:selectOneMenu  id="questionario" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{QuestionarioClienteCRMControle.questionarioClienteVO.questionario.codigo}" >
                                                                                                        <a4j:support event="onchange" action="#{QuestionarioClienteCRMControle.selecionarQuestionarioSerRespondidoPeloCliente}"   reRender="questionarioCliente,questionario,consultor,data,textarea,panelMesangem"/>
                                                                                                        <f:selectItems  value="#{QuestionarioClienteCRMControle.listaSelectQuestionario}" />
                                                                                                    </h:selectOneMenu>
                                                                                                    <a4j:commandButton id="atualizar_questionario" action="#{QuestionarioClienteCRMControle.montarListaSelectItemQuestionario}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:questionario"/>
                                                                                                </td>

                                                                                    </tr>--%>
                                                                                        <tr>
                                                                                            <td align="right" valign="middle"><span style="font-weight: bold"> <h:outputText   value="#{msg_aplic.prt_QuestionarioCliente_data}" /></span></td>
                                                                                            <td align="left" valign="middle">
                                                                                                <h:outputText   value="#{QuestionarioClienteCRMControle.questionarioClienteVO.data}" >
                                                                                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                                                                </h:outputText>
                                                                                                <%--<rich:calendar id="data"
                                                                                                               value="#{QuestionarioClienteCRMControle.questionarioClienteVO.data}"
                                                                                                               inputSize="10"
                                                                                                               inputClass="form"
                                                                                                               oninputblur="blurinput(this);"
                                                                                                               oninputfocus="focusinput(this);"
                                                                                                               oninputchange="return validar_Data(this.id);"
                                                                                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                                                                               datePattern="dd/MM/yyyy"
                                                                                                               enableManualInput="true"
                                                                                                               zindex="2"
                                                                                                               showWeeksBar="false" />
                                                                                                <h:message for="data" styleClass="mensagemDetalhada"/>--%>
                                                                                                <%--h:inputText  id="data" onchange="return mascara(this.form, 'form:data', '99/99/9999', event);" readonly="true" size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:data');"  onfocus="focusinput(this);"   styleClass="form" value="#{QuestionarioClienteCRMControle.questionarioClienteVO.data}" >

                                                                                        </tr>--%>                                                                                        
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td align="right" valign="middle"><span style="font-weight: bold">Evento:</span></td>
                                                                                            <td align="left" valign="middle">
                                                                                                <h:outputText   value="#{QuestionarioClienteCRMControle.questionarioClienteVO.eventoVO.descricao}" >
                                                                                                </h:outputText>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td align="right" valign="middle"><span style="font-weight: bold">Observa&ccedil;&otilde;es:</span></td>
                                                                                            <td align="center" valign="middle">&nbsp;</td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td colspan="2" align="center" valign="middle">
                                                                                                <h:inputTextarea styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);"   id="textarea" cols="3" rows="50" style="width:90%;height:90px;margin-bottom:15px;" value="#{QuestionarioClienteCRMControle.questionarioClienteVO.observacao}"/>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td colspan="2" align="center" valign="middle">
                                                                                                <h:dataTable id="listaQuestionarioRespondidoPeloCliente" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                                                                                             rows="5" value="#{QuestionarioClienteCRMControle.listaSelectQuestionario}" var="questionario"  width="100%">
                                                                                                    <h:column>
                                                                                                        <f:facet name="header">
                                                                                                            <h:outputText value="Nome Questionário"/>
                                                                                                        </f:facet>
                                                                                                        <a4j:commandLink reRender="questionarioCliente, consultor, data, textarea, listaQuestionarioRespondidoPeloCliente, tituloBV" action="#{QuestionarioClienteCRMControle.selecionarQuestionarioSerRespondidoPeloCliente}" id="descricaoQuestionario" value="#{questionario.questionario.nomeInterno}"/>
                                                                                                    </h:column>
                                                                                                    <h:column>
                                                                                                        <f:facet name="header">
                                                                                                            <h:outputText value="Data de Respota"/>
                                                                                                        </f:facet>
                                                                                                        <a4j:commandLink reRender="questionarioCliente, consultor, data, textarea, listaQuestionarioRespondidoPeloCliente, tituloBV" action="#{QuestionarioClienteCRMControle.selecionarQuestionarioSerRespondidoPeloCliente}" id="dataQuestionario" value="#{questionario.data_Apresentar}"/>
                                                                                                    </h:column>
                                                                                                </h:dataTable>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                    <td align="left" valign="top" bgcolor="#ffffff">&nbsp;</td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="9" height="9" align="left" valign="top"><img src="images/box_usuario_bottom_left.png" width="9" height="9"></td>
                                                                    <td align="left" valign="top" bgcolor="#ffffff"><img src="images/shim.gif" width="1" height="1"></td>
                                                                    <td width="9" height="9" align="left" valign="top"><img src="images/box_usuario_bottom_right.png" width="9" height="9"></td>
                                                                </tr>
                                                            </table>
                                                            <!-- fim box -->
                                                        </td>
                                                    </tr>
                                                </table>
                                                <!-- fim item -->

                                            </td>
                                            <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>
                                        <tr>
                                            <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                            <td align="left" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                            <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                        </tr>
                                    </table>


                                    <!-- fim botões -->	</td>




                            </table>
                        </td>
                    </tr>

                </table>

            </h:form>
        </f:view>
    </body>
</html>

<%--
    Document   : listasRelatoriosAlunosBonus
    Created on : 27/06/2013, 16:02:31
    Author     : <PERSON><PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<jsp:include page="include_head.jsp" flush="true"/>

<style type="text/css">
    .rich-stglpanel-header {
        color: #0f4c6b;
    }

    .rich-stglpanel-header {
        background-color: #ACBECE;
        border-color: #ACBECE;
        font-size: 12px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemRelatorios" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Clientes com Bônus" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-emitir-um-relatorio-de-todos-os-clientes-com-bonus/"
                                                      title="Clique e saiba mais: Relatório Clientes Bônus"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup  layout="block" styleClass="margin-box">
                                    <%-- ------------------------------- ABA CONSULTORES   ------------------------------------ --%>

                                    <h:outputText styleClass="text" value="Filtros"
                                                  style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
                                    <h:panelGroup rendered="#{LRAlunosBonusControle.permissaoConsultaTodasEmpresas}"
                                                  style="padding-left: 10px;padding-top: 15px;padding-bottom: 15px;border: 1px solid #ACBECE;"
                                                  layout="block">
                                        <h:outputText style="margin-left: 7px" styleClass="text" value="Empresas: "/>
                                        <h:selectOneMenu value="#{LRAlunosBonusControle.filtroEmpresa}">
                                            <f:selectItems value="#{LRAlunosBonusControle.listaEmpresas}" id="consultoresClientesComBonus"/>
                                            <a4j:support event="onchange"
                                                         action="#{LRAlunosBonusControle.recarregarTela}"
                                                         reRender="consultores, professores"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <rich:simpleTogglePanel switchType="client" label="Dados do Bônus"
                                                            style="margin-top: 8px" opened="true"
                                                            onexpand="false">
                                        <h:panelGroup id="bonusAcrescimoReducao">
                                            <h:outputText styleClass="text" value="Tipo do Bônus: "/>
                                            <h:selectOneMenu id="bonus"
                                                             value="#{LRAlunosBonusControle.bonus}">
                                                <f:selectItem itemValue=""
                                                              itemLabel="#{CElabels['operacoes.selecione']}"/>
                                                <f:selectItems value="#{LRAlunosBonusControle.listaBonus}"/>
                                            </h:selectOneMenu>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>

                                    <rich:simpleTogglePanel switchType="client" label="Consultores"
                                                            opened="false" onexpand="false">
                                        <h:panelGroup id="consultores">
                                            <rich:dataGrid value="#{LRAlunosBonusControle.consultores}" id="consultorRelatorioGroup"
                                                           var="consultor" width="100%" columns="4">

                                                <h:selectBooleanCheckbox id="selecConsultorRel"
                                                        value="#{consultor.colaboradorEscolhido}">
                                                </h:selectBooleanCheckbox>
                                                <h:outputText value="#{consultor.pessoa.nome}"/>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <%-- ------------------------------- ABA PROFESSORES   ------------------------------------ --%>
                                    <rich:simpleTogglePanel style="margin-bottom: 10px;" switchType="client" label="Professores"
                                                            opened="false" onexpand="true">
                                        <h:panelGroup id="professores">
                                            <rich:dataGrid value="#{LRAlunosBonusControle.professores}" id="GroupProfessorSelecionar"
                                                           var="professor" width="100%" columns="4">
                                                <h:selectBooleanCheckbox id="checkBoxProfessorSelecionar"
                                                        value="#{professor.colaboradorEscolhido}"/>
                                                <h:outputText value="#{professor.pessoa.nome} "/>
                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>


                                    <a4j:commandLink styleClass="botoes nvoBt" id="btnConsultarClienteBonus"
                                                     style="margin-left: 0px"
                                                     action="#{LRAlunosBonusControle.consultarAlunosBonus}"
                                                     reRender="groupResultados, imprimir">
                                        Consultar&nbsp<i class="fa-icon-search"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink
                                            style="margin-left:5px;margin-top: 8px; padding:5px 7px;"
                                            action="#{LRAlunosBonusControle.limparFiltros}"
                                            styleClass="botoes nvoBt btSec"
                                            reRender="form">
                                        Limpar filtros&nbsp <i class="fa-icon-eraser"></i>
                                    </a4j:commandLink>

                                    <h:panelGroup id="imprimir">

                                        <a4j:commandLink id="exportarExcel" style="margin-left:5px;"
                                                           actionListener="#{ExportadorListaControle.exportar}"
                                                           rendered="#{not empty LRAlunosBonusControle.resultado}"
                                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                           accesskey="2" styleClass="botoes linkPadrao">
                                            <f:attribute name="lista"
                                                         value="#{LRAlunosBonusControle.resultado}"/>
                                            <f:attribute name="tipo" value="xls"/>
                                            <f:attribute name="itemExportacao" value="relBonus"/>
                                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,telefone=Telefones,email=E-mail,dataNascimentoApresentar=Nascimento
                                                                     ,situacaoClienteApresentar=Situação,sexo=Sexo Biológico,dataMatriculaApresentar=Data Matrícula,dataInicioPlanoApresentar=Início Plano
                                                                     ,dataVencimentoPlanoApresentar=Venc. Plano,dataUltimoAcessoApresentar=Últ.Acesso,plano=Plano,dataCadastroApresentar=Data Cadastro
                                                                     ,consultor=Consultor/Professor,estadoCivil=Estado Civil,profissao=Profissão,nomeEmpresa=Empresa"/>
                                            <f:attribute name="prefixo" value="ClientesBonus"/>
                                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                        </a4j:commandLink>

                                        <a4j:commandLink style="margin-left: 5px;"
                                                           rendered="#{not empty LRAlunosBonusControle.resultado}"
                                                           reRender="relatorioImprimir"
                                                           action="#{LRAlunosBonusControle.prepararImpr}"
                                                           oncomplete="Richfaces.showModalPanel('relatorioImprimir');"
                                                           styleClass="linkPadrao">
                                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>

                                    <%---------------------------------TABELA DO RELATORIO---------------------------------------%>
                                    <h:panelGroup id="groupResultados">
                                        <rich:dataTable width="100%"
                                                        value="#{LRAlunosBonusControle.resultado}"
                                                        id="resultados"
                                                        var="item"
                                                        rows="30"
                                                        style="margin-top: 8px"
                                                        rendered="#{not empty LRAlunosBonusControle.resultado}">

                                            <rich:column id="matricula" sortBy="#{item.matricula}">
                                                <f:facet name="header">
                                                    <h:outputText value="Matricula"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.matricula}"
                                                        actionListener="#{LRAlunosBonusControle.prepareEditar}"
                                                        action="#{LRAlunosBonusControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="nome" sortBy="#{item.nome}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nome"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nome}"
                                                        actionListener="#{LRAlunosBonusControle.prepareEditar}"
                                                        action="#{LRAlunosBonusControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="telefone" sortBy="#{item.telefone}">
                                                <f:facet name="header">
                                                    <h:outputText value="Telefone"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.telefone}"
                                                        actionListener="#{LRAlunosBonusControle.prepareEditar}"
                                                        action="#{LRAlunosBonusControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="email" sortBy="#{item.email}">
                                                <f:facet name="header">
                                                    <h:outputText value="E-mail"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.email}"
                                                        actionListener="#{LRAlunosBonusControle.prepareEditar}"
                                                        action="#{LRAlunosBonusControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataCadastro" sortBy="#{item.dataCadastro}">
                                                <f:facet name="header">
                                                    <h:outputText value="Cadastro"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataCadastroApresentar}"
                                                        actionListener="#{LRAlunosBonusControle.prepareEditar}"
                                                        action="#{LRAlunosBonusControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataNascimento"
                                                         sortBy="#{item.dataNascimento}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nascimento"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataNascimentoApresentar}"
                                                        actionListener="#{LRAlunosBonusControle.prepareEditar}"
                                                        action="#{LRAlunosBonusControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                            <rich:column id="dataUltimoAcesso"
                                                         sortBy="#{item.dataUltimoAcesso}">
                                                <f:facet name="header">
                                                    <h:outputText value="Último Acesso"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataUltimoAcessoApresentar}"
                                                        actionListener="#{LRAlunosBonusControle.prepareEditar}"
                                                        action="#{LRAlunosBonusControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column sortBy="#{item.nomeEmpresa}" rendered="#{LRAlunosBonusControle.permissaoConsultaTodasEmpresas}">
                                                <f:facet name="header">
                                                    <h:outputText value="Empresa"></h:outputText>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nomeEmpresa}"
                                                        actionListener="#{LRAlunosBonusControle.prepareEditar}"
                                                        action="#{LRAlunosBonusControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                        </rich:dataTable>


                                        <table align="right" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td align="center" valign="middle"><h5>
                                                    <h:outputText
                                                            rendered="#{not empty LRAlunosBonusControle.resultado}"
                                                            value=" [Itens:#{LRAlunosBonusControle.totalItens}]"> </h:outputText>
                                                </h5></td>
                                            </tr>
                                        </table>
                                        <rich:datascroller for="resultados"
                                                           rendered="#{not empty LRAlunosBonusControle.resultado}"
                                                           id="scrollResultados"></rich:datascroller>

                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true" />

        </h:panelGroup>
    </h:form>

    <%------------------------- MODAL 'IMPRIMIR' ----------------------------------------%>
    <rich:modalPanel id="relatorioImprimir" autosized="true" shadowOpacity="true" width="350">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados da impressão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="imprimir"/>
                <rich:componentControl for="relatorioImprimir" attachTo="imprimir" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form>

            <h:outputText styleClass="text" style="font-weight: bold"
                          value="Por Favor, selecione as informações que deseja imprimir:"/><br/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosBonusControle.apresentarLinha1}"/>
            <h:outputText styleClass="text" value="Dados Cadastrais"/><br/>
            <h:selectBooleanCheckbox value="#{LRAlunosBonusControle.apresentarLinha3}"/>
            <h:outputText styleClass="text" value="Informações de Plano"/><br/><br/>
            <center>
                <a4j:commandButton id="imprimirPDF" ajaxSingle="false"
                                   action="#{LRAlunosBonusControle.imprimirListaRelatorio}"
                                   value="Imprimir"
                                   reRender="mensagem"
                                   image="../imagens/imprimirContrato.png"
                                   oncomplete="#{LRAlunosBonusControle.mensagemNotificar}#{LRAlunosBonusControle.msgAlert}"
                                   accesskey="2" styleClass="botoes"/>
            </center>
        </a4j:form>
    </rich:modalPanel>

    <%------------------------- MODAL 'CONSULTAR' ----------------------------------------%>
    <rich:modalPanel id="listasRelatorios" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Relátorio de Alunos com Bônus"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="relatorios"/>
                <rich:componentControl for="listasRelatorios" attachTo="relatorios" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <jsp:include page="topoReduzido.jsp"/>
        <br/>
        <a4j:form>
            <h:panelGrid columns="2" columnClasses="colunaDireita, colunaEsquerda">

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.nome}" value="Nome: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosBonusControle.itemRelatorio.nome}"
                              value="#{LRAlunosBonusControle.itemRelatorio.nome}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.sexo}" value="Sexo: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosBonusControle.itemRelatorio.sexo}"
                              value="#{LRAlunosBonusControle.itemRelatorio.sexo}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.dataMatriculaApresentar}"
                              value="Data Matrícula: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.dataMatriculaApresentar}"
                              value="#{LRAlunosBonusControle.itemRelatorio.dataMatriculaApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.dataInicioPlanoApresentar}"
                              value="Inicio Plano: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.dataInicioPlanoApresentar}"
                              value="#{LRAlunosBonusControle.itemRelatorio.dataInicioPlanoApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.dataVencimentoPlanoApresentar}"
                              value="Vencimento Plano: "/>
                <h:outputText styleClass="text"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.dataVencimentoPlanoApresentar}"
                              value="#{LRAlunosBonusControle.itemRelatorio.dataVencimentoPlanoApresentar}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.plano}" value="Plano: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRAlunosBonusControle.itemRelatorio.plano}"
                              value="#{LRAlunosBonusControle.itemRelatorio.plano}"/>

                <h:outputText styleClass="text" style="font-weight: bold;"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.consultores}"
                              value="Consultor: "/>
                <h:panelGroup>
                    <c:forEach items="${LRAlunosBonusControle.itemRelatorio.consultores}" var="consultor">
                        <span rendered="${not empty LRAlunosBonusControle.itemRelatorio.consultores}"
                              class="text">${consultor.pessoa.nome}</span><br/>
                    </c:forEach>
                </h:panelGroup>

                <h:outputText styleClass="text" style="font-weight: bold;"
                              rendered="#{not empty LRAlunosBonusControle.itemRelatorio.professores}"
                              value="Professor: "/>
                <h:panelGroup>
                    <c:forEach items="${LRAlunosBonusControle.itemRelatorio.professores}" var="professor">
                        <span rendered="${not empty LRAlunosBonusControle.itemRelatorio.professores}"
                              class="text">${professor.pessoa.nome}</span><br/>
                    </c:forEach>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>

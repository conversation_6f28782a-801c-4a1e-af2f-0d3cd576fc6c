<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="include_head.jsp" flush="true"/>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<script src="script/chart_amcharts.js" type="text/javascript"></script>
<script src="script/chart_pie.js" type="text/javascript"></script>
<script src="script/chart_light.js" type="text/javascript"></script>
<script src="script/chart_serial.js" type="text/javascript"></script>
<script type="text/javascript" src="script/script.js"></script>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css"/>
<link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css"/>
<link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css"/>
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css"/>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css"/>
<link href="./css/css_bi_1.4.css" rel="stylesheet" type="text/css"/>
<link href="./css/pacto_css_1.3.min.css" rel="stylesheet" type="text/css"/>
<link href="./css/crm_1.0.min.css" rel="stylesheet" type="text/css"/>

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<style type="text/css">

    .bi-panel-bi-crm {
        cursor: pointer;
        font-size: 2em;
        color: rgba(41, 171, 226, 1);
        display: block;
        padding: 7px;
        background-color: #ffffff;
        float: right;
        border-radius: 10%;
        margin-right: 14px;
    }

    .rich-calendar-button.semIcone {
        display: none;
    }

    .panel-esquerda {
        background-color: #e6e6e6;
    }

    .rich-stglpanel-header {
        /**/
        background: none !important;
        padding: inherit !important;
    }

    .rich-stglpanel-body {
        /**/
        border: none !important;
    }

    .rich-stglpanel-marker {
        display: none !important;
    }

    .rich-calendar-button.semIcone {
        display: none;
    }

    .listaConsultorCRM .rich-menu-item{
        padding-right: 0px;
    }
    
    .rich-menu-item-icon, .rich-menu-group-icon{
        margin-right: 0px;
    }
    
    .listaConsultorCRM .grupoPai{
        width: 200px;
    }
    
    .listaConsultorCRM .grupoFilho{
        width: 450px;
    }
    
    .rich-ddmenu-label-select.listaConsultorCRM {
        border: none !important;
    }

    .rich-calendar-input.listaConsultorCRM {
        text-align: center;
    }

    .semBorda {
        position: relative !important;
    }

    .rich-table-cell {
        padding: 5px 7px 5px 7px;
    }

    .consultaSuperior {
        font-family: Arial, Helvetica, sans-serif;
        background-color: #CCCCCC;
        font-size: 8pt;
        font-weight: bold;
        text-align: center;
    }

    .porcentagemBoxBody {
        font-size: 30px;
        color: rgb(71, 71, 71);
    }

    .pecentBoxBody {
        font-size: 20px;
    }

    .tituloTotalMetas {
        font-family: "Trebuchet MS", Verdana, Arial, Helvetica, sans-serif;
        color: #333;
        font-weight: 100;
        font-size: 16px;
    }

    .rich-table-subfooter {
        background-color: white;
        border-top: 1px #333 solid
    }

    .rich-table-subfootercell {
        border: none;
        text-align: center;
        font-size: 18px !important;
        font-weight: bold;
        padding-top: 10px;
    }

    .resultadoBoxHeader {
        height: 37px;
        width: 121px;
        background-color: rgb(192, 192, 192);
    }

    .resultadoBoxBody {
        height: 77px;
        width: 119px;
        background-color: rgba(235, 255, 73, 0);
        border-bottom: solid 1px rgb(192, 192, 192);
        border-left: solid 1px rgb(192, 192, 192);
        border-right: solid 1px rgb(192, 192, 192);
    }

    .rich-table-subheadercell {
        border: none !important;
        padding: 4px 4px 10px 4px !important;
        height: 25px;
        text-align: left;
        font-size: 11px;
        color: #474747;
        font-family: Arial, Verdana, sans-serif;
        white-space: nowrap;
    }

    .alinharDireita {
        text-align: right;
    }

    .rich-table-subheader {
        border: none !important;
        background-color: white;
        text-align: center;
    }

    .rich-table-thead {
        border: none;
    }

    body {
        margin: 0;
        padding: 0;
    }

    .rich-table-subfooter.classeTabela {
        height: 33px;
    }

    .rich-table-cell.colunaEsquerda.tabelaSumario {
        padding: 0 !important;
    }

    .container-bi-crm {
        margin: 10px;
        height: auto;
        position: relative;
        -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        -moz-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        background-color: #ffffff;
        transition: transform .1s ease-out;
        -moz-transition: -moz-transform .1s ease-out;
        -o-transition: -o-transform .1s ease-out;
        -webkit-transition: -webkit-transform .1s ease-out;
    }

    .rich-menu-list-strut {
        display: none;
    }

</style>
<%
    String m = request.getParameter("m");
    if (m == null) {
        m = "web";
    }
    pageContext.setAttribute("modoView", m);
%>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <html>
        <jsp:include page="include_headCRM.jsp" flush="true"/>
        <body class="crm">
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_crm_flat.jsp" flush="true"/>
                <rich:jQuery query="addClass('menuItemAtual')" selector=".item7"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo" style="min-height: calc(100vh - 240px);">
                <h:panelGroup layout="block" style="height: auto;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <div style="display: inline-block;height: auto;width: ${modoView != 'mobile' ? 'calc(100% - 230px);' : 'calc(100%);'}"
                             class="container-imagem container-conteudo-central">

                            <h:panelGroup layout="block" id="superiorBICRM"
                                          style="margin: 20px 15px 8px 15px; display: inline-flex;">

                                <h:panelGroup id="containerDataInicialBI" layout="block"
                                              style="border-radius: 2px;height:auto;min-height: 21px;position: relative;margin:0px 15px 1% 0;font-size: 14px !important;"
                                              styleClass="bi-panel-bi-crm dateTimeCustom">
                                    <rich:calendar
                                            id="dataInicialBI"
                                            buttonClass="margin-left-right-7"
                                            buttonIcon="/imagens_flat/icon-calendar-check.png"
                                            inputClass="forcarSemBorda"
                                            inputStyle="height: auto;border: none !important;" inputSize="6"
                                            datePattern="dd/MM/yyyy"
                                            value="#{BusinessIntelligenceCRMControle.dataInicio}"
                                            showWeeksBar="false">
                                    </rich:calendar>
                                    <rich:jQuery
                                            query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                            selector=".btn-toggle-calendar"/>
                                </h:panelGroup>

                                <h:panelGroup id="containerDataFinalBI" layout="block"
                                              style="border-radius: 2px;height:auto;min-height: 21px;position: relative;margin:0px 15px 1% 0;font-size: 14px !important;"
                                              styleClass="bi-panel-bi-crm pull-right dateTimeCustom bi-btn-filtroCol bi-filtro-dataBase tudo step1">
                                    <rich:calendar
                                            id="dataFinalBI"
                                            buttonClass="margin-left-right-7"
                                            buttonIcon="/imagens_flat/icon-calendar-check.png"
                                            inputClass="forcarSemBorda"
                                            inputStyle="height: auto;border: none !important;" inputSize="6"
                                            datePattern="dd/MM/yyyy"
                                            value="#{BusinessIntelligenceCRMControle.dataFinal}"
                                            showWeeksBar="false">
                                    </rich:calendar>
                                    <rich:jQuery
                                            query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                            selector=".btn-toggle-calendar"/>
                                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                                 query="mask('99/99/9999')"/>
                                </h:panelGroup>

                                <h:inputHidden id="totalUsuariosSelecionados"
                                               value="#{BusinessIntelligenceCRMControle.totalUsuariosSelecionados}"/>

                                <h:panelGroup layout="block" id="panellistaGrupoColaboradorHabili" rendered="#{BusinessIntelligenceCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresPorTipoColaborador}">

                                    <rich:dropDownMenu id="listaGrupoColaboradorHabi"
                                                       styleClass="listaConsultorCRM"
                                                       selectItemClass="itemSelecionado">

                                        <f:facet name="label">
                                            <h:panelGroup layout="block" styleClass="pure-button">
                                                <h:outputText id="totalUsuarioSelecionadoHabi"
                                                              value="Total selecionado: #{BusinessIntelligenceCRMControle.totalUsuariosSelecionados}"/>
                                                <rich:spacer width="2%"/>
                                                <i class="fa-icon-caret-down"></i>
                                            </h:panelGroup>
                                        </f:facet>

                                        <c:forEach var="grupos"
                                                   items="#{BusinessIntelligenceCRMControle.listaGrupoTipoColaborador}">

                                            <rich:menuGroup value="#{grupos.tipoColaborador_Apresentar}" styleClass="grupoPai">

                                                <%--SELECIONAR TODOS DO GRUPO PARTICIPANTE--%>
                                                <rich:menuItem style="border-bottom: 1px #000 solid"
                                                               styleClass="grupos grupoPai#{grupos.codigoTipoColaboradorOrdinal}"
                                                               submitMode="ajax"
                                                               status="false"
                                                               reRender="totalUsuarioSelecionado"
                                                               onclick="selecionarTodos(#{grupos.codigoTipoColaboradorOrdinal});consultarTotalSelecionado();consultarTotalGrupoHabi(#{grupos.codigoTipoColaboradorOrdinal})">

                                                    <h:selectBooleanCheckbox
                                                            styleClass="selTodos#{grupos.codigoTipoColaboradorOrdinal}"
                                                            id="selTodosBIHabi${grupos.codigoTipoColaboradorOrdinal}"
                                                            onclick="selecionarTodosCheck(#{grupos.codigoTipoColaboradorOrdinal});consultarTotalSelecionado();consultarTotalGrupoHabi(#{grupos.codigoTipoColaboradorOrdinal})"
                                                            value="#{grupos.todosUsuariosSelecionados}">
                                                        <a4j:support status="false" event="onchange"
                                                                     reRender="totalUsuarioSelecionado, totalUsuarioSelecionadoHabi"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText style="padding-left: 2%"
                                                                  id="selecionarTodosBIHabi${grupos.codigoTipoColaboradorOrdinal}"
                                                                  value=""/>
                                                </rich:menuItem>

                                                <c:forEach var="usuario"
                                                           items="#{grupos.usuarios}">

                                                    <rich:menuItem submitMode="ajax"
                                                                   styleClass="grupoFilho"
                                                                   status="false"
                                                                   reRender="totalUsuarioSelecionado"
                                                                   onclick="selecionarColaborador(#{grupos.codigoTipoColaboradorOrdinal}#{usuario.codigo});consultarTotalSelecionado();consultarTotalGrupoHabi(#{grupos.codigoTipoColaboradorOrdinal})">

                                                        <h:selectBooleanCheckbox
                                                                styleClass="todosItens grupo#{grupos.codigoTipoColaboradorOrdinal} colabo#{grupos.codigoTipoColaboradorOrdinal}#{usuario.codigo}"
                                                                onclick="consultarTotalSelecionado();consultarTotalGrupoHabi(#{grupos.codigoTipoColaboradorOrdinal})"
                                                                value="#{usuario.selecionado}">
                                                            <a4j:support status="false" event="onchange"
                                                                         reRender="totalUsuarioSelecionado, totalUsuarioSelecionadoHabi"/>
                                                        </h:selectBooleanCheckbox>

                                                        <h:outputText style="padding-left: 2%"
                                                                      value="#{usuario.nome}"/>
                                                    </rich:menuItem>
                                                </c:forEach>
                                            </rich:menuGroup>
                                        </c:forEach>
                                    </rich:dropDownMenu>


                                    <%--<h:selectBooleanCheckbox value="#{BusinessIntelligenceCRMControle.somenteAtivos}">--%>
                                        <%--<a4j:support status="false" event="onchange"--%>
                                                     <%--reRender="superiorBICRM"--%>
                                                     <%--action="#{BusinessIntelligenceCRMControle.montarListaUsuariosCRM}"/>--%>
                                    <%--</h:selectBooleanCheckbox>--%>
                                    <%--<h:outputText value="Somente usuários ativos"/>--%>
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panellistaGrupoColaboradorDesabi" rendered="#{!BusinessIntelligenceCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresPorTipoColaborador}">
                                    <rich:dropDownMenu id="listaGrupoColaborador"
                                                       styleClass="listaConsultorCRM"
                                                       selectItemClass="itemSelecionado">

                                        <f:facet name="label">
                                            <h:panelGroup layout="block" styleClass="pure-button">
                                                <h:outputText id="totalUsuarioSelecionado"
                                                              value="Total selecionado: #{BusinessIntelligenceCRMControle.totalUsuariosSelecionados}"/>
                                                <rich:spacer width="2%"/>
                                                <i class="fa-icon-caret-down"></i>
                                            </h:panelGroup>
                                        </f:facet>

                                        <c:forEach var="grupos"
                                                   items="#{BusinessIntelligenceCRMControle.listaGrupoColaborador}">
                                            <rich:menuGroup id="grupoParticipanteTipoColabBI" value="#{grupos.descricao}" style="text-align: left;" styleClass="grupoPai">

                                                <%--SELECIONAR TODOS DO GRUPO PARTICIPANTE--%>
                                                <rich:menuItem style="border-bottom: 1px #000 solid; text-align: left; display: block;"
                                                               styleClass="grupos grupoPai#{grupos.codigo}"
                                                               submitMode="ajax"
                                                               status="false"
                                                               reRender="totalUsuarioSelecionado"
                                                               onclick="selecionarTodos(#{grupos.codigo});consultarTotalSelecionado();consultarTotalGrupo(#{grupos.codigo})">

                                                    <h:selectBooleanCheckbox styleClass="selTodos#{grupos.codigo}"
                                                                             id="selTodosBIDesa${grupos.codigo}"
                                                                             onclick="selecionarTodosCheck(#{grupos.codigo});consultarTotalSelecionado();consultarTotalGrupo(#{grupos.codigo})"
                                                                             value="#{grupos.todosParticipantesSelecionados}">
                                                        <a4j:support status="false" event="onchange"
                                                                     reRender="totalUsuarioSelecionado"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText style="padding-left: 2%"
                                                                  id="selecionarTodosBI${grupos.codigo}"
                                                                  value=""/>
                                                </rich:menuItem>


                                                <c:forEach var="grupoColaboradorParticipante"
                                                           items="#{grupos.grupoColaboradorParticipanteVOs}">

                                                    <rich:menuItem submitMode="ajax"
                                                                   styleClass="grupoFilho"
                                                                   status="false"
                                                                   reRender="totalUsuarioSelecionado"
                                                                   onclick="selecionarColaborador(#{grupoColaboradorParticipante.codigo});consultarTotalSelecionado();consultarTotalGrupo(#{grupos.codigo})">

                                                        <h:selectBooleanCheckbox id="selParticipante"
                                                                                 styleClass="todosItens grupo#{grupos.codigo} colabo#{grupoColaboradorParticipante.codigo}"
                                                                                 onclick="consultarTotalSelecionado();consultarTotalGrupo(#{grupos.codigo})"
                                                                                 value="#{grupoColaboradorParticipante.grupoColaboradorParticipanteEscolhido}">
                                                            <a4j:support status="false" event="onchange"
                                                                         reRender="totalUsuarioSelecionado"/>
                                                        </h:selectBooleanCheckbox>

                                                        <h:outputText style="padding-left: 2%"
                                                                      value="#{grupoColaboradorParticipante.colaboradorParticipante.pessoa_Apresentar}"/>
                                                    </rich:menuItem>
                                                </c:forEach>
                                            </rich:menuGroup>
                                        </c:forEach>
                                    </rich:dropDownMenu>
                                </h:panelGroup>


                                <h:panelGroup layout="block" id="panelConsultarBICRM" style="margin-left: 15px;">
                                    <a4j:commandLink id="consultarBICRM"
                                                     styleClass="pure-button pure-button-primary"
                                                     action="#{BusinessIntelligenceCRMControle.processarDados}"
                                                     oncomplete="#{BusinessIntelligenceCRMControle.modalMensagemGenerica}"
                                                     reRender="bicrm, indicadoresBICRM, mdlMensagemGenerica, panelGeralBIOBjecoes,biIndicadores"
                                                     title="Buscar" accesskey="2">
                                        <i class="fa-icon-search"></i> Buscar
                                    </a4j:commandLink>
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panelimprimirBICRM" style="margin-left: 15px;">
                                    <%--BOTÃO DE IMPRESSÃO DO BI--%>
                                    <a4j:commandLink id="imprimirBICRM"
                                                     title="Imprimir BI CRM"
                                                     styleClass="pure-button"
                                                     action="#{BusinessIntelligenceCRMControle.imprimirRelatorio}"
                                                     oncomplete="abrirPopupPDFImpressao('relatorio/#{BusinessIntelligenceCRMControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);"
                                                     accesskey="2">
                                        <i class="fa-icon-print"></i>&nbsp Imprimir
                                    </a4j:commandLink>

                                </h:panelGroup>
                            </h:panelGroup>


                            <h:panelGrid id="bicrm" columns="2" width="100%" columnClasses="w50,w50">

                                <h:panelGroup layout="block"
                                              styleClass="itemBI1">
                                    <jsp:include flush="true"
                                                 page="includes/crm/include_bi_crm_metaVendas.jsp"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              styleClass="itemBI2">
                                    <jsp:include flush="true"
                                                 page="includes/crm/include_bi_crm_metaFidelizacao.jsp"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              styleClass="itemBI3">
                                    <jsp:include flush="true" page="includes/crm/include_bi_crm_resultado.jsp"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              rendered="#{BusinessIntelligenceCRMControle.existeMetaCRMExtra}"
                                              styleClass="itemBI6">
                                    <jsp:include flush="true"
                                                 page="includes/crm/include_bi_crm_metaCRMExtra.jsp"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              styleClass="itemBI5"
                                              rendered="#{LoginControle.apresentarLinkEstudio}">
                                    <jsp:include flush="true"
                                                 page="includes/crm/include_bi_crm_metaStudio.jsp"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              styleClass="itemBI7">
                                    <jsp:include flush="true"
                                                 page="includes/crm/include_bi_crm_objecoes.jsp"/>
                                </h:panelGroup>

                            </h:panelGrid>

                            <h:panelGroup layout="block"
                                          styleClass="itemBI4">
                                <jsp:include flush="true"
                                             page="includes/crm/include_bi_crm_desempenhoConsultores.jsp"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block"
                                          styleClass="itemBI8">
                                <jsp:include flush="true"
                                             page="includes/crm/include_bi_crm_indicadores_novo.jsp"/>
                            </h:panelGroup>
                        </div>
                        <c:if test="${modoView != 'mobile'}">
                            <jsp:include page="include_box_menulateral.jsp">
                                <jsp:param name="menu" value="CRM_INICIO" />
                            </jsp:include>
                        </c:if>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

        <c:if test="${SuperControle.menuZwUi}">
            <script>
                fecharMenu();
            </script>
        </c:if>

        </body>
        </html>
    </h:form>

    <jsp:include page="include_menu_modalQuarentenaCRM.jsp" flush="true"/>
    <jsp:include page="include_modais_tela_principalCRM.jsp" flush="true"/>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>

</f:view>


<script type="text/javascript">
    function mostrarTodosBIs() {
        jQuery('.itemBI1').fadeIn(400);
        jQuery('.itemBI2').fadeIn(600);
        jQuery('.itemBI3').fadeIn(800);
        jQuery('.itemBI4').fadeIn(1000);
        jQuery('.itemBI5').fadeIn(1200);
        jQuery('.itemBI6').fadeIn(1400);
        jQuery('.itemBI7').fadeIn(1600);
        jQuery('.itemBI8').fadeIn(1800);
    }

    function selecionarTodos(codigo) {
        var itens = jQuery('.grupo' + codigo);
        var elemento = jQuery('.selTodos' + codigo);
        var marcado = elemento.attr("checked");

        if (marcado) {
            elemento.attr("checked", false);
            itens.attr("checked", false);
        } else {
            elemento.attr("checked", true);
            itens.attr("checked", true);
        }
    }

    function selecionarTodosCheck(codigo) {
        var itens = jQuery('.grupo' + codigo);
        var elemento = jQuery('.selTodos' + codigo);
        var marcado = elemento.attr("checked");

        if (marcado) {
            itens.attr("checked", true);
        } else {
            itens.attr("checked", false);
        }
    }

    function selecionarColaborador(codigo) {
        var elemento = jQuery('.colabo' + codigo);
        var marcado = elemento.attr("checked");

        if (marcado) {
            elemento.attr("checked", false);
        } else {
            elemento.attr("checked", true);
        }
    }

    function consultarTotalSelecionado() {
        try {
            var itens = jQuery('.todosItens');
            var i = 0;
            var totalSelecionado = 0;
            for (i = 0; i < itens.length; i++) {
                var marcado = itens[i].checked;
                if (marcado) {
                    totalSelecionado++;
                }
            }
            var inputTotal = document.getElementById('form:totalUsuariosSelecionados');
            inputTotal.value = totalSelecionado;
        }catch (e) {
            console.log("ERRO consultarTotalSelecionado BI: "+e);
        }
    }

    function consultarTotalGrupo(codigo) {
        var itens = jQuery('.grupo' + codigo);

        var i = 0;
        var totalSelecionado = 0;
        for (i = 0; i < itens.length; i++) {
            var marcado = itens[i].checked;
            if (marcado) {
                totalSelecionado++;
            }
        }
        var idVariavel= "form:selecionarTodosBI"+codigo+"";
        var mensagemSelecionado = "Selecionar todos <span style='float: right;'>Total: "+totalSelecionado+"</span>"
        var textHtml = jQuery(document.getElementById(idVariavel)).text(); //document.getElementById(idVariavel).innerText;
        var quantidade = parseInt(textHtml.substring(24));
        if(totalSelecionado !== quantidade){
            jQuery(document.getElementById(idVariavel)).html(mensagemSelecionado);
            
        }
        
    }
    
    function consultarTotalGrupoHabi(codigo) {
        var itens = jQuery('.grupo' + codigo);

        var i = 0;
        var totalSelecionado = 0;
        for (i = 0; i < itens.length; i++) {
            var marcado = itens[i].checked;
            if (marcado) {
                totalSelecionado++;
            }
        }
        
        var idVariavel= "form:selecionarTodosBIHabi"+codigo+"";
        var mensagemSelecionado = "Selecionar todos <span style='float: right;'>Total: "+totalSelecionado+"</span>"
        var textHtml = jQuery(document.getElementById(idVariavel)).text(); //document.getElementById(idVariavel).innerText;
        var quantidade = parseInt(textHtml.substring(24));
        if(totalSelecionado !== quantidade){
            jQuery(document.getElementById(idVariavel)).html(mensagemSelecionado);
            
        }
    }
    
    function consultarTotalSelecionadoPorGrupo() {
        var itens = jQuery('.grupos');

        var i = 0;
        for (i = 0; i < itens.length; i++) {
            var valor = itens[i].classList[3];
            var grupo = valor.substring(8);
            consultarTotalGrupo(grupo);
        }
    }
    
    function consultarTotalSelecionadoPorGrupoHabi() {
        var itens = jQuery('.grupos');

        var i = 0;
        for (i = 0; i < itens.length; i++) {
            var valor = itens[i].classList[3];
            var grupo = valor.substring(8);
            consultarTotalGrupoHabi(grupo);
        }
    }
    
    mostrarTodosBIs();
    var eVisivel = jQuery(document.getElementById('form:totalUsuarioSelecionado')).is(':visible');
    if(eVisivel){
        consultarTotalSelecionadoPorGrupo();
    }
    
    var eVisivelHabi = jQuery(document.getElementById('form:totalUsuarioSelecionadoHabi')).is(':visible');
    if(eVisivelHabi){
        consultarTotalSelecionadoPorGrupoHabi();
    }
</script>

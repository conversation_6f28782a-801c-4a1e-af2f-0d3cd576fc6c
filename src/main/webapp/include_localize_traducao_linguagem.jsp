<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<!-- localize -->
<h:panelGroup rendered="#{SuperControle.usuarioLogado ne null and not empty SuperControle.usuarioLogado.linguagem and SuperControle.usuarioLogado.linguagem ne 'pt'}">
    <script src="https://global.localizecdn.com/localize.js"></script>

    <script>!function(a){if(!a.Localize){a.Localize={};for(var e=["translate","untranslate","phrase","initialize","translatePage","setLanguage","getLanguage","detectLanguage","getAvailableLanguages","untranslatePage","bootstrap","prefetch","on","off"],t=0;t<e.length;t++)a.Localize[e[t]]=function(){}}}(window);</script>

    <script>
        Localize.initialize({
            key: 'drNwovo4VY29C',
            rememberLanguage: true
        });

        Localize.setLanguage('${SuperControle.usuarioLogado.linguagem}');
    </script>
</h:panelGroup>
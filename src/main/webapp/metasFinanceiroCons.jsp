<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style>
    .tabelaMetaFinanceira tr td:nth-child(1n+5){
        text-align: right;
    }
</style>
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Finan_MetaFinanceiro_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-metas-financeiras-de-venda-adm/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>

    <h:form id="form" styleClass="pure-form pure-u-1">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
            <h:commandLink action="#{MetaFinanceiroControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:panelGrid columns="1" width="100%">

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles" style="font-size: 14px;">
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{MetaFinanceiroControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,empresa_Apresentar=Empresa,anoMes=Ano/Mês,descricao=Descrição,valor0=Meta 1,valor1=Meta 2,valor2=Meta 3,valor3=Meta 4,valor4=Meta 5"/>
                                <f:attribute name="prefixo" value="MetasFinanceiro"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{MetaFinanceiroControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,empresa_Apresentar=Empresa,anoMes=Ano/Mês,descricao=Descrição,valor0=Meta 1,valor1=Meta 2,valor2=Meta 3,valor3=Meta 4,valor4=Meta 5"/>
                                <f:attribute name="prefixo" value="MetasFinanceiro"/>
                                <f:attribute name="titulo" value="Metas do Financeiro"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 action="#{MetaFinanceiroControle.realizarConsultaLogObjetoGeral}"
                                                        oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{MetaFinanceiroControle.novo}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <table id="tblMetaFinanceira" class="tabelaMetaFinanceira pure-g-r pure-u-11-12 margin-0-auto">
                <thead>
                    <th>COD</th>
                    <th>EMPRESA</th>
                    <th>ANO/MÊS</th>
                    <th>DESCRIÇÃO</th>
                    <th style="text-align: right">META 1</th>
                    <th style="text-align: right">META 2</th>
                    <th style="text-align: right">META 3</th>
                    <th style="text-align: right">META 4</th>
                    <th style="text-align: right">META 5</th>
                </thead>
                <tbody></tbody>
            </table>

            <a4j:jsFunction name="jsEditar" action="#{MetaFinanceiroControle.editarMeta}" reRender="mensagem"/>

        </h:panelGrid>
    </h:form>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaMetaFinanceira", "${contexto}/prest/financeiro/metaFinanceira", 1, "asc");
        });
    </script>
</f:view>
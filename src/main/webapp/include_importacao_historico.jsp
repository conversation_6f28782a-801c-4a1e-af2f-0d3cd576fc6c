<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
</style>
<h:panelGroup layout="block" id="panelGeralHistorico">
    <rich:dataTable width="100%" headerClass="consulta" id="tblHistorico"
                    rowClasses="linhaImpar, linhaPar"
                    columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                    value="#{ImportacaoControle.historico}" rows="20"
                    var="item">
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Código"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{item.codigo}"/>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Usuário"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{item.usuarioVO.nome}"/>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Tipo"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{item.tipoImportacaoEnum.descricao}"/>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Status"/>
            </f:facet>
            <h:panelGroup rendered="#{!item.parado}">
                <h:panelGroup>
                    <h:outputText value="#{item.percentual}%"/>
                </h:panelGroup>
                <h:panelGroup id="percentual" layout="block"
                              style="height: 10px; background: #C0C0C0;  margin-left: 7px; margin-right: 7px;">
                    <h:panelGroup layout="block"
                                  style="height: 10px; background: green; width: #{item.percentual}%; max-width: 100%"/>
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText value="#{item.atual} / #{item.total}"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup rendered="#{item.parado}">
                <h:outputText value="Parado manualmente"/>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Sucesso"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{item.sucesso}"/>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Falha"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{item.falha}"/>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Data Início"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{item.dataInicioApresentar}"/>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Data Fim"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{item.dataFimApresentar}"/>
            </h:panelGroup>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Opções"/>
            </f:facet>
            <h:panelGroup>
                <a4j:commandLink id="visualizarLogImportacao"
                                 title="Visualizar log do processo de importação"
                                 styleClass="linkPadrao texto-cor-azul texto-size-16-real tooltipster"
                                 onclick="atualizarTempoImportacao()"
                                 action="#{ImportacaoControle.visualizarLogImportacao}"
                                 oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                                 reRender="panelGeralModalImportacaoLog">
                    <i class="fa-icon-list"></i>
                </a4j:commandLink>

                <a4j:commandLink id="visualizarDetalhesImportacao"
                                 title="Visualizar itens importados"
                                 rendered="#{item.dataFim != null}"
                                 style="padding-left: 10px"
                                 styleClass="linkPadrao texto-cor-azul texto-size-16-real tooltipster"
                                 onclick="atualizarTempoImportacao()"
                                 action="#{ImportacaoControle.visualizarDetalhesImportacao}"
                                 oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                                 reRender="panelGeralModalImportacaoDetalhe">
                    <i class="fa-icon-search"></i>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>
    </rich:dataTable>
    <rich:datascroller for="tblHistorico" maxPages="20" page="#{ImportacaoControle.scrollerPage}"
                       rendered="#{ImportacaoControle.historicoSize > 20}"
                       id="sc2"/>

    <h:panelGroup layout="block" style="padding: 15px; text-align: center;">
        <a4j:commandLink value="Atualizar"
                         id="atualizarImport"
                         onclick="atualizarTempoImportacao()"
                         styleClass="botoes nvoBt btSec"
                         action="#{ImportacaoControle.consultarImportacoes}"
                         reRender="panelGeralHistorico"/>
        <a4j:commandLink value="Parar"
                         id="stopImport"
                         styleClass="botoes nvoBt btSec"
                         rendered="#{ImportacaoControle.existeImportacaoEmAndamento}"
                         action="#{ImportacaoControle.parar}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         reRender="panelGeralHistorico"/>
    </h:panelGroup>
</h:panelGroup>

<a4j:poll id="pollAtualizarImport" interval="1000" status="false"
          enabled="#{ImportacaoControle.historicoSize > 0}"
          process="@this"
          oncomplete="carregarTooltipster()"
          action="#{ImportacaoControle.consultarImportacoes}"
          reRender="panelGeralHistorico" limitToList="true"/>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/> 
                    <h:selectOneMenu styleClass="campos" id="consulta" required="true" value="#{PlanoModalidadeVezesSemanaControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{PlanoModalidadeVezesSemanaControle.tipoConsultaCombo}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" styleClass="campos" value="#{PlanoModalidadeVezesSemanaControle.controleConsulta.valorConsulta}"/>
                    <h:commandButton id="consultar" type="submit" styleClass="botoes" value="#{msg_bt.btn_consultar}" action="#{PlanoModalidadeVezesSemanaControle.irPaginaInicial}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="2"/>
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{PlanoModalidadeVezesSemanaControle.apresentarResultadoConsulta}" binding="#{PlanoModalidadeVezesSemanaControle.apresentarLinha}">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false" binding="#{PlanoModalidadeVezesSemanaControle.apresentarPrimeiro}" action="#{PlanoModalidadeVezesSemanaControle.irPaginaInicial}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false" binding="#{PlanoModalidadeVezesSemanaControle.apresentarAnterior}" action="#{PlanoModalidadeVezesSemanaControle.irPaginaAnterior}"/> 
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{PlanoModalidadeVezesSemanaControle.paginaAtualDeTodas}" rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false" binding="#{PlanoModalidadeVezesSemanaControle.apresentarPosterior}" action="#{PlanoModalidadeVezesSemanaControle.irPaginaPosterior}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false" binding="#{PlanoModalidadeVezesSemanaControle.apresentarUltimo}" action="#{PlanoModalidadeVezesSemanaControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{PlanoModalidadeVezesSemanaControle.listaConsulta}" rendered="#{PlanoModalidadeVezesSemanaControle.apresentarResultadoConsulta}" rows="10" var="planoModalidadeVezesSemana">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_codigo}"/>
                        </f:facet>
                        <h:commandLink action="#{PlanoModalidadeVezesSemanaControle.editar}" id="codigo" value="#{planoModalidadeVezesSemana.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemana}"/>
                        </f:facet>
                        <h:commandLink action="#{PlanoModalidadeVezesSemanaControle.editar}" id="vezesSemana" value="#{planoModalidadeVezesSemana.vezesSemana.nrVezes}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_planoModalidade}"/>
                        </f:facet>
                        <h:commandLink action="#{PlanoModalidadeVezesSemanaControle.editar}" id="planoModalidade" value="#{planoModalidadeVezesSemana.planoModalidade}"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:commandButton action="#{PlanoModalidadeVezesSemanaControle.editar}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PlanoModalidadeVezesSemanaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PlanoModalidadeVezesSemanaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{PlanoModalidadeVezesSemanaControle.novo}" value="#{msg_bt.btn_novo}" styleClass="botoes" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>
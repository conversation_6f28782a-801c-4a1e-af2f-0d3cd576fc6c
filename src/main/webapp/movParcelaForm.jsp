<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_MovParcela_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{MovParcelaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_MovParcela_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{MovParcelaControle.movParcelaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao" size="30" readonly="true" maxlength="30" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovParcelaControle.movParcelaVO.descricao}"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_pessoa}" />
                    <h:panelGroup>
                        <h:inputText  id="nomeCliente" size="70" readonly="true" maxlength="30" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovParcelaControle.movParcelaVO.contrato.pessoa.nome}"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_valorParcela}" />
                    <h:panelGroup>
                        <h:inputText  id="valorParcela" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovParcelaControle.movParcelaVO.valorParcela}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>                        
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_dataRegistro}" />
                    <h:panelGroup>
                        <rich:calendar id="dataRegistro" 
                                       readonly="true"
                                       value="#{MovParcelaControle.movParcelaVO.dataRegistro}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <%--h:inputText  id="dataRegistro" onchange="return mascara(this.form, 'form:dataRegistro', '99/99/9999', event);"  size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:dataRegistro');"  onfocus="focusinput(this);" styleClass="form" value="#{MovParcelaControle.movParcelaVO.valorParcela}" >
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:inputText--%>
                        <h:message for="dataRegistro" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_dataVencimento}" />
                    <h:panelGroup>
                        <rich:calendar id="dataVencimento"
                                       readonly="true"
                                       value="#{MovParcelaControle.movParcelaVO.dataVencimento}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <%--h:inputText  id="dataVencimento" onchange="return mascara(this.form, 'form:dataVencimento', '99/99/9999', event);" size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:dataVencimento');"  onfocus="focusinput(this);" styleClass="form" value="#{MovParcelaControle.movParcelaVO.dataVencimento}" >
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:inputText--%>
                        <h:message for="dataVencimento"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_situacao}" />
                    <h:panelGroup>
                        <h:inputText  id="situacao" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovParcelaControle.movParcelaVO.situacao_Apresentar}" />
                        <h:message for="situacao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>                    
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_responsavel}" />
                    <h:panelGroup>
                        <h:inputText  id="responsavel" readonly="true" size="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovParcelaControle.movParcelaVO.responsavel.codigo}" />
                        <h:outputText value="#{MovParcelaControle.movParcelaVO.responsavel.nome}"/>                        
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_percentualMulta}" />
                    <h:inputText  id="percentualMulta"  size="20" readonly="true" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{MovParcelaControle.movParcelaVO.percentualMulta}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_percentualJuro}" />
                    <h:inputText  id="percentualJuro" size="20" readonly="true" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{MovParcelaControle.movParcelaVO.percentualJuro}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>
                    <%--
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_utilizaConvenio}" />
                    <h:selectBooleanCheckbox id="utilizaConvenio"onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"value="#{MovParcelaControle.movParcelaVO.utilizaConvenio}">
                        <a4j:support event="onclick" reRender="form" action="#{MovParcelaControle.limparConvenio}"/>
                    </h:selectBooleanCheckbox>

                    <h:outputText  rendered="#{MovParcelaControle.movParcelaVO.utilizaConvenio}" styleClass="tituloCampos" value="#{msg_aplic.prt_MovParcela_convenioCobranca}" />
                    <h:panelGroup rendered="#{MovParcelaControle.movParcelaVO.utilizaConvenio}">
                        <h:selectOneMenu  id="convenioCobranca"  readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovParcelaControle.movParcelaVO.convenioCobranca.codigo}" >
                            <f:selectItems  value="#{MovParcelaControle.listaSelectItemConvenioCobranca}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_convenioCobranca" action="#{MovParcelaControle.montarListaSelectItemConvenioCobranca}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:formaPagamento"/>
                    </h:panelGroup>
                    --%>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{MovParcelaControle.sucesso}"image="./imagens/sucesso.png"/>                        
                        <h:commandButton rendered="#{MovParcelaControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{MovParcelaControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{MovParcelaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>                           
                            <h:commandButton id="consultar" immediate="true" action="#{MovParcelaControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>
<script>
    document.getElementById("form:dataRegistro").focus();
</script>
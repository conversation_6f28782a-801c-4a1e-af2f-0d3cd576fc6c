<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ConfiguracaoSistema_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/> 
                    <h:selectOneMenu onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" id="consulta" required="true" value="#{ConfiguracaoSistemaControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{ConfiguracaoSistemaControle.tipoConsultaCombo}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ConfiguracaoSistemaControle.controleConsulta.valorConsulta}"/>
                    <h:commandButton id="consultar" type="submit" styleClass="botoes" value="#{msg_bt.btn_consultar}" action="#{ConfiguracaoSistemaControle.irPaginaInicial}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="2"/>
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{ConfiguracaoSistemaControle.apresentarResultadoConsulta}" binding="#{ConfiguracaoSistemaControle.apresentarLinha}">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false" binding="#{ConfiguracaoSistemaControle.apresentarPrimeiro}" action="#{ConfiguracaoSistemaControle.irPaginaInicial}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false" binding="#{ConfiguracaoSistemaControle.apresentarAnterior}" action="#{ConfiguracaoSistemaControle.irPaginaAnterior}"/> 
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{ConfiguracaoSistemaControle.paginaAtualDeTodas}" rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false" binding="#{ConfiguracaoSistemaControle.apresentarPosterior}" action="#{ConfiguracaoSistemaControle.irPaginaPosterior}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false" binding="#{ConfiguracaoSistemaControle.apresentarUltimo}" action="#{ConfiguracaoSistemaControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{ConfiguracaoSistemaControle.listaConsulta}" rendered="#{ConfiguracaoSistemaControle.apresentarResultadoConsulta}" rows="10" var="configuracaoSistema">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_codigo}"/>
                        </f:facet>
                        <h:commandLink action="#{ConfiguracaoSistemaControle.editar}" id="codigo" value="#{configuracaoSistema.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_questionarioPrimeiraVisita}"/>
                        </f:facet>
                        <h:commandLink action="#{ConfiguracaoSistemaControle.editar}" id="questionarioPrimeiraVisita" value="#{configuracaoSistema.questionarioPrimeiraVisita.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_questionarioRetorno}"/>
                        </f:facet>
                        <h:commandLink action="#{ConfiguracaoSistemaControle.editar}" id="questionarioRetorno" value="#{configuracaoSistema.questionarioRetorno.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_questionarioReMatricula}"/>
                        </f:facet>
                        <h:commandLink action="#{ConfiguracaoSistemaControle.editar}" id="questionarioReMatricula" value="#{configuracaoSistema.questionarioReMatricula.descricao}"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:commandButton action="#{ConfiguracaoSistemaControle.editar}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ConfiguracaoSistemaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ConfiguracaoSistemaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{ConfiguracaoSistemaControle.novo}" value="#{msg_bt.btn_novo}" styleClass="botoes" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>
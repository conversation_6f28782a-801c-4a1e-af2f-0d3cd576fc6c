<%--
    Document   : relatorioFechamentoDiario
    Created on : Aug 15, 2012, 5:37:59 PM
    Author     : <PERSON> - GeoInova Soluções
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
    <%@page contentType="text/html" pageEncoding="UTF-8"%>
    <%@include file="pages/estudio/includes/include_imports.jsp" %>
    <%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

    <head>
        <script type="text/javascript" src="${contexto}/script/relatorioFechamentoDiario.js"></script>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <script type="text/javascript" src="${contexto}/script/script.js"></script>
        <link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">
        <!--<link href="${contexto}/css/otimizeCRM.css" rel="stylesheet" type="text/css">-->
        <script type="text/javascript" src="${contexto}/script/demonstrativoFinan.js"></script>
        <jsp:include page="pages/estudio/includes/include_head.jsp" />
        <c:set var="moduloSession" value="1" scope="session" />
        <link href="css/jquery.treeTable.css" rel="stylesheet" type="text/css">
        <script type="text/javascript" src="script/jquery.treeTable.js"></script>
    </head>

    <f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form" style="margin: 0;">
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <body onload="atualizarTreeViewFD();">

        <title>Relatório Fechamento Diário</title>

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

            <h:form id="formTopoFD" prependId="false">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            </h:form>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem  container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Fechamento Diário" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlWikiGST}Relatórios:Diário"
                                                      title="Clique e saiba mais: Fechamento Diário"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:form id="relatorioFechamento" prependId="false">
                                        <h:panelGrid columns="2" id="filtro">
                                            <h:outputLabel
                                                    value="Filtros: " style="font-weight: bold; font-size: 10pt; font-family: 'Trebuchet MS',verdana,arial,helvetica,sans-serif;"  />
                                            <h:outputLabel
                                                    value="#{RelatorioFechamentoDiarioControle.filtros} " styleClass="texto_disponibilidade" />
                                        </h:panelGrid>

                                        <h:panelGrid columns="3" id="botoes">
                                            <a4j:commandButton
                                                    id="tete"
                                                    title="Refazer Consulta"
                                                    image="imagens/estudio/refazer_consulta.png"
                                                    value="Refazer Consulta"
                                                    status="none"
                                                    reRender="formPanelFiltro"
                                                    onclick="#{rich:component('panelFiltro')}.show();"/>
                                            <rich:spacer width="10px"/>
                                            <a4j:commandButton
                                                    oncomplete="abrirPopup('pages/estudio/relatorioFechamentoDiarioImpressao.jsp', 'VisualizarImpressao', 1044, 700);"
                                                    image="imagens/estudio/visualizar_impressao.png"
                                                    value="Visualizar Impressão"/>
                                        </h:panelGrid>

                                        <c:if test="${RelatorioFechamentoDiarioControle.mostrarRelatorio}">

                                            <%@include file="pages/estudio/relatorioFechamentoDiarioTree.jsp" %>
                                        </c:if>

                                        <c:if test="${RelatorioFechamentoDiarioControle.mostrarRelatorio}">
                                            <a4j:commandButton
                                                    oncomplete="abrirPopup('pages/estudio/relatorioFechamentoDiarioImpressao.jsp', 'VisualizarImpressao', 1044, 700);"
                                                    image="imagens/estudio/visualizar_impressao.png"
                                                    value="Visualizar Impressão"/>
                                        </c:if>
                                    </h:form>
                                </h:panelGroup>
                                <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                                   id="btnAtualizaPagina">
                                </a4j:commandButton>
                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>

    </h:form>



        <!-- MODAL FILTRO -->
        <rich:modalPanel id="panelFiltro" autosized="true" shadowOpacity="true"
                         onshow="focusAt('relatorioFechamentoDiarioControle-periodoInicialInputDate');#{rich:component('mykeyfiltro')}.enable();"
                         showWhenRendered="false" width="700" height="200">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Filtros" styleClass="texto_disponibilidade"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltro" />
                    <rich:componentControl for="panelFiltro" attachTo="hidelinkPanelFiltro"
                                           operation="hide" event="onclick" />
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltro" prependId="false">
                <h:panelGrid columns="5" id="periodos">
                    <h:outputLabel
                            value="Período:" styleClass="texto_disponibilidade" />
                    <rich:spacer width="01px"/>
                    <rich:calendar
                            locale="pt_BR"
                            inputSize="10"
                            inputClass="form"
                            oninputblur="blurinput(this);"
                            oninputfocus="focusinput(this);"
                            oninputchange="return validar_Data(this.id);"
                            datePattern="dd/MM/yyyy"
                            enableManualInput="true"
                            zindex="99"
                            showWeeksBar="false"
                            value="#{RelatorioFechamentoDiarioControle.periodoInicial}"
                            id="RelatorioFechamentoDiarioControle-periodoInicial"
                            popup="true"  styleClass="texto_disponibilidade">
                    </rich:calendar>
                    <h:outputLabel
                            value=" a " styleClass="texto_disponibilidade"/>
                    <rich:calendar
                            locale="pt_BR"
                            inputSize="10"
                            inputClass="form"
                            oninputblur="blurinput(this);"
                            oninputfocus="focusinput(this);"
                            oninputchange="return validar_Data(this.id);"
                            datePattern="dd/MM/yyyy"
                            enableManualInput="true"
                            zindex="99"
                            showWeeksBar="false"
                            value="#{RelatorioFechamentoDiarioControle.periodoFinal}"
                            id="relatorioFechamentoDiarioControle-periodoFinal"
                            popup="true" styleClass="texto_disponibilidade" >
                    </rich:calendar>
                </h:panelGrid>
                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                <h:panelGrid columns="3" id="horarios">
                    <h:outputLabel
                            value="Horários:" styleClass="texto_disponibilidade"/>
                    <h:selectOneMenu
                            id="relatorioFechamentoDiarioControle-listaHorarioInicial"
                            value="#{RelatorioFechamentoDiarioControle.horaInicial}"
                            onblur="blurinput(this);"
                            onfocus="focusinput(this);"
                            converter="timeConverter2"
                            styleClass="form">
                        <f:selectItem itemLabel="" itemValue=""/>
                        <f:selectItems value="#{RelatorioFechamentoDiarioControle.selectHorario}" />
                    </h:selectOneMenu>
                    <h:selectOneMenu
                            id="relatorioFechamentoDiarioControle-listaHorarioFinal"
                            value="#{RelatorioFechamentoDiarioControle.horaFinal}"
                            onblur="blurinput(this);"
                            onfocus="focusinput(this);"
                            converter="timeConverter2"
                            styleClass="form">
                        <f:selectItem itemLabel="" itemValue=""/>
                        <f:selectItems value="#{RelatorioFechamentoDiarioControle.selectHorario}" />
                    </h:selectOneMenu>
                </h:panelGrid>

                <h:panelGrid columns="2" id="dadosUsuario">
                    <rich:spacer width="54px"/>
                    <h:outputLabel
                            value="Nome" styleClass="texto_disponibilidade"/>

                    <h:outputLabel
                            value="Usuário:" styleClass="texto_disponibilidade"/>

                    <h:inputText
                            maxlength="50"
                            autocomplete="off"
                            style="width: 250px;"
                            onkeydown="return tabOnEnter(event, 'btnConfirmarConfirmacao');"
                            onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                            value="#{RelatorioFechamentoDiarioControle.usuarioVO.nome}"
                            id="relatorioFechamentoDiarioControle-usuario-descricao">
                    </h:inputText>

                    <rich:suggestionbox
                            id="usuarioSuggestion"
                            for="relatorioFechamentoDiarioControle-usuario-descricao"
                            status="none"
                            suggestionAction="#{RelatorioFechamentoDiarioControle.listarUsuarios}"
                            minChars="1" rowClasses="30"
                            width="400"
                            fetchValue="#{item.nome}"
                            var="item"
                            nothingLabel="Nenhum dado encontrado">
                        <h:column>
                            <h:outputText value="#{item.nome}"/>
                        </h:column>
                        <a4j:support
                                event="onselect" oncomplete="focusAt('btnConfirmarConfirmacao');">
                            <f:setPropertyActionListener
                                    target="#{RelatorioFechamentoDiarioControle.usuarioVO}"
                                    value="#{item}" />
                        </a4j:support>
                    </rich:suggestionbox>
                </h:panelGrid>

                <h:panelGrid columns="2" id="dadosProfessor">
                    <h:outputLabel
                            value="Profissional:" styleClass="texto_disponibilidade"/>

                    <h:inputText
                            maxlength="50"
                            autocomplete="off"
                            style="width: 250px;"
                            onkeydown="return tabOnEnter(event, 'btnConfirmarConfirmacao');"
                            onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                            value="#{RelatorioFechamentoDiarioControle.profissionalVO.pessoa.nome}"
                            id="relatorioFechamentoDiarioControle-professor-nome">
                    </h:inputText>

                    <rich:suggestionbox
                            id="professorSuggestion"
                            for="relatorioFechamentoDiarioControle-professor-nome"
                            status="none"
                            suggestionAction="#{RelatorioFechamentoDiarioControle.listarProfissionais}"
                            minChars="1" rowClasses="30"
                            width="400"
                            fetchValue="#{item.pessoa.nome}"
                            var="item"
                            nothingLabel="Nenhum dado encontrado">
                        <h:column>
                            <h:outputText value="#{item.pessoa.nome}"/>
                        </h:column>
                        <a4j:support
                                event="onselect" oncomplete="focusAt('btnConfirmarConfirmacao');">
                            <f:setPropertyActionListener
                                    target="#{RelatorioFechamentoDiarioControle.profissionalVO}"
                                    value="#{item}" />
                        </a4j:support>
                    </rich:suggestionbox>
                </h:panelGrid>
                <h:panelGrid columns="3" id="botoesPanel">
                    <a4j:commandButton
                            image="imagens/estudio/limpar_filtros.png"
                            reRender="relatorioFechamento,formPanelFiltro"
                            title="Limpar Filtros"
                            status="none"
                            action="#{RelatorioFechamentoDiarioControle.limparDados}"/>
                    <rich:spacer width="05px"/>
                    <a4j:commandButton
                            image="imagens/estudio/visualizar_relatorio.png"
                            title="Visualizar Relatório"
                            oncomplete="atualizarTreeViewFD();"
                            reRender="form"
                            onclick="#{rich:component('panelFiltro')}.hide();#{rich:component('mykeyfiltro')}.disable();"
                            action="#{RelatorioFechamentoDiarioControle.pesquisar}"/>
                    <rich:hotKey
                            id="mykeyfiltro"
                            key="esc"
                            handler="#{rich:component('panelFiltro')}.hide();"/>
                </h:panelGrid>
            </a4j:form>

        </rich:modalPanel>

        <rich:modalPanel id="modalPanelErroFec" autosized="true" shadowOpacity="true"
                         onshow="focusAt('btnFecharErro');"
                         zindex="999"
                         showWhenRendered="#{RelatorioFechamentoDiarioControle.apresentarRichModalErro}"
                         width="450" height="150">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Atenção!"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formModalPanelErroFec" prependId="false">
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada" value="#{RelatorioFechamentoDiarioControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:spacer height="05px"/>
                <h:panelGrid style="position: relative; float:right; ">
                    <a4j:commandButton
                            image="imagens/estudio/fechar.png"
                            value="Fechar"
                            id="btnFecharErro"
                            onclick="#{rich:component('modalPanelErro')}.hide();"
                            action="#{RelatorioFechamentoDiarioControle.acaoFecharModalErro}" reRender="modalPanelErro"/>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>
        <%@include  file="pages/estudio/includes/include_modal_dados_aluno.jsp" %>
        <%@include  file="pages/estudio/includes/include_modal_agenda_aluno.jsp" %>
        <%@include file="pages/estudio/includes/include_modal_sucesso.jsp" %>
        <%@include file="pages/estudio/includes/include_modal_erro.jsp" %>

</f:view>

</body>
</html>

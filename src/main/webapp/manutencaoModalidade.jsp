<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
     window.addEventListener("load", function (event) {
         executePostMessage({loaded: true})
     });
</script>
<link href="${root}/beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
</style>
<script>
    jQuery.noConflict();
</script>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <%@include file="includes/include_consultarTurma.jsp" %>

    <title>
        <h:outputText value="Manutenção Modalidade" />
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Manutenção de Modalidades"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-mudar-as-modalidades-de-um-aluno-sem-precisar-vender-um-novo-plano-manutencao-de-modalidade/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>

    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

    <a4j:form id="form">
        <style>
            td.obstrucao{
                background-color: #f1cdd4 !important;
            }
            td.advertencia{
                background-color: #ffffd6 !important;
            }

        </style>

        <h:panelGrid id="panelGeral" columns="1" width="100%" >

            <h:panelGrid columns="1" width="100%" styleClass="font-size-em-max">

                <h:outputText value="NOME DO CLIENTE:" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" style="font-weight: bold;"/>
                <h:outputText id="nomeCliente" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.contratoNovo.pessoa.nome}"/>
                <rich:spacer height="10px"/>
                <h:panelGroup>
                    <h:outputText value="CONTRATOS" styleClass="texto-size-14 texto-font texto-bold" style="font-weight: bold;"/>
                </h:panelGroup>

                <rich:dataTable styleClass="tabelaDados semZebra" style="margin: 0; width: 100%;" id="contrato" width="100%" rowClasses="tablelistras textsmall"
                                value="#{ManutencaoModalidadeControle.contratos}" var="contrato">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_numeroContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{contrato.codigo}"/>
                    </rich:column>
                    <rich:column width="30%">
                        <f:facet name="header">
                            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_plano}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{contrato.plano.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_dataInicio}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{contrato.vigenciaDe_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_dataTermino}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{contrato.vigenciaAteAjustada_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="Duração em Meses"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{contrato.contratoDuracao.numeroMeses}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_valorBaseContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{contrato.valorBaseCalculo}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_ManutencaoModalidade_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="desfazer" title="Desfazer alterações" action="#{ManutencaoModalidadeControle.desfazerAlteracoes}"
                                           image="./images/change.png" reRender="panelGeral"/>
                    </rich:column>
                </rich:dataTable>
                <rich:spacer height="10px"/>
                <h:panelGroup>
                    <h:outputText value="MODALIDADES DO CONTRATO" styleClass="texto-size-14 texto-font texto-bold" style="font-weight: bold;"/>
                </h:panelGroup>

                <rich:dataTable id="modalidades" width="100%"
                                styleClass="tabelaDados semZebra" style="margin: 0; width: 100%;"
                                value="#{ManutencaoModalidadeControle.contratoNovo.contratoModalidadeVOs}" var="cm">
                    <rich:column width="50%" styleClass="#{cm.obstrucaoConclusaoManutencao ? 'obstrucao' : cm.advertenciaAcimaLimite ? 'advertencia' : ''}">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_ManutencaoModalidade_modalidade}"/>
                        </f:facet>

                        <h:outputText styleClass="fa-icon-ban-circle tooltipster"  rendered="#{cm.obstrucaoConclusaoManutencao}"
                                      style="margin-right: 10px; color: #e9322d;"
                                      title="Esta turma está inativa ou não permite matrículas acima do limite, que está excedido."/>

                        <h:outputText styleClass="fa-icon-warning-sign tooltipster"  rendered="#{cm.advertenciaAcimaLimite}"
                                      style="margin-right: 10px; color: burlywood;"
                                      title="Esta turma está com alunos acima do limite, mas não bloqueia novas matrículas."/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{cm.modalidade.nome}"/>
                    </rich:column>
                    <rich:column styleClass="#{cm.obstrucaoConclusaoManutencao ? 'obstrucao' : cm.advertenciaAcimaLimite ? 'advertencia' : ''}">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_ManutencaoModalidade_vezesSemana}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{cm.nrVezesSemana}X POR SEMANA"/>
                    </rich:column>
                    <rich:column rendered="#{ManutencaoModalidadeControle.permiteAlterarModalidade}"
                                 styleClass="#{cm.obstrucaoConclusaoManutencao ? 'obstrucao' : cm.advertenciaAcimaLimite ? 'advertencia' : ''}">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_ManutencaoModalidade_opcoes}"/>
                        </f:facet>
                        <a4j:commandLink id="btnAdvertencia"
                                         styleClass="icon linkPadrao" 
                                         rendered="#{cm.advertenciaMatriculasFuturas}" title="Turma com matrícula(s) futura(s) que pode(m) exceder o limite do horário"> 
                                 <h:outputText style="color: #f2d51d !important;" styleClass="fa-icon-exclamation-triangle-sign texto-cor-amarelo"/>
                        </a4j:commandLink> 
                        <a4j:commandLink id="editarMod" styleClass="icon linkPadrao texto-cor-azul " action="#{ManutencaoModalidadeControle.editarModalidade}"
                                           title="editar modalidade" reRender="panelGeral">
                        <i class="fa-icon-ok-sign"></i>
                        </a4j:commandLink>
                        <a4j:commandLink id="excluirMod" styleClass="icon linkPadrao texto-cor-azul" action="#{ManutencaoModalidadeControle.excluirModalidade}"
                                           title="excluir modalidade" reRender="panelGeral">
                            <i class="fa-icon-minus-sign"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>

                <div style="margin-top:10px;"></div>

                <h:panelGroup rendered="#{! ManutencaoModalidadeControle.mostrarEditorModalidade}">
                    <div style="margin-bottom: 10px;">
                        <a4j:commandLink id="adicionarMod"
                                         styleClass="pure-button" value="Adicionar Modalidade"
                                         rendered="#{ManutencaoModalidadeControle.permiteAlterarModalidade}"
                                         action="#{ManutencaoModalidadeControle.adicionarModalidade}"
                                         title="adicionar modalidade" reRender="panelGeral"/>
                    </div>
                </h:panelGroup>

                <rich:panel id="panelEdicaoModalidade" rendered="#{ManutencaoModalidadeControle.mostrarEditorModalidade}">
                    <f:facet name="header">
                        <h:outputText value="Edição de Modalidade"/>
                    </f:facet>
                    <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ManutencaoModalidade_modalidade}" />
                        <h:panelGroup>
                            <h:inputText id="nomeMod" maxlength="30" size="50" value="#{ManutencaoModalidadeControle.contratoModalidade.modalidade.nome}" readonly="true"/>
                            <a4j:commandButton id="consultarMod" title="consultar modalidade" style="vertical-align:middle; margin-left:5px;" 
                                               image="./imagens/bt_detalhes.png" reRender="panelEdicaoModalidade"
                                               oncomplete="Richfaces.showModalPanel('panelConsultaModalidades')"/>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGroup rendered="#{ManutencaoModalidadeControle.contratoModalidade.modalidade.utilizarTurma}">
                        <h:panelGroup styleClass="fonteTrebuchet" style="font-weight: bold;">
                            <div style="margin-top:10px; margin-bottom:10px;">

                                <h:outputText styleClass="texto-size-14 texto-font texto-bold tituloCaixaAlta" style="font-weight: bold;" value="#{msg_aplic.prt_ManutencaoModalidade_turmaModalidade}" />

                        </h:panelGroup>
                        <rich:dataTable id="modalidadeTurma" columnClasses="centralizado textsmall" var="turma" width="100%"
                                        value="#{ManutencaoModalidadeControle.contratoModalidade.contratoModalidadeTurmaVOs}">
                            <rich:column breakBefore="true">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ManutencaoModalidade_turma}"/>
                                </f:facet>
                                <h:outputText value="#{turma.turma.identificador}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ManutencaoModalidade_horariosTurma}"/>
                                </f:facet>
                                <rich:dataTable id="turmaHorario" rowClasses="textsmall linhaImpar" width="100%" style="border-color:white;"
                                                columnClasses="colunaEsquerda, colunaEsquerda, colunaEsquerda, colunaEsquerda, centralizado"
                                                value="#{turma.contratoModalidadeHorarioTurmaVOs}" var="horario">
                                    <rich:column style="border-color:white;"
                                                 styleClass="#{horario.horarioTurma.obstrucaoConclusaoManutencao ? 'obstrucao' : horario.horarioTurma.advertenciaAcimaLimite ? 'advertencia' : ''}">
                                        <h:outputText value="#{horario.horarioTurma.diaSemana_Apresentar}    "/>
                                    </rich:column>
                                    <rich:column style="border-color:white;"
                                                 styleClass="#{horario.horarioTurma.obstrucaoConclusaoManutencao ? 'obstrucao' : horario.horarioTurma.advertenciaAcimaLimite ? 'advertencia' : ''}">
                                        <h:outputText value="#{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal}" />
                                    </rich:column>
                                    <rich:column style="border-color:white;"
                                                 styleClass="#{horario.horarioTurma.obstrucaoConclusaoManutencao ? 'obstrucao' : horario.horarioTurma.advertenciaAcimaLimite ? 'advertencia' : ''}">
                                        <h:outputText value="#{horario.horarioTurma.professor.pessoa.nome}" />
                                    </rich:column>
                                    <rich:column style="border-color:white;"
                                                 styleClass="#{horario.horarioTurma.obstrucaoConclusaoManutencao ? 'obstrucao' : horario.horarioTurma.advertenciaAcimaLimite ? 'advertencia' : ''}">
                                        <h:outputText value="#{horario.horarioTurma.ambiente.descricao}" />
                                    </rich:column>
                                    <rich:column style="border-color:white;"
                                                 styleClass="#{horario.horarioTurma.obstrucaoConclusaoManutencao ? 'obstrucao' : ''}">
                                        <a4j:commandLink  rendered="#{horario.horarioTurma.msgMatriculasFuturas != ''}"
                                                         styleClass="linkPadrao tooltipster"
                                                         title="#{horario.horarioTurma.msgMatriculasFuturas}">
                                                  <h:outputText
                                                    styleClass="fa-icon-exclamation-triangle-sign texto-cor-amarelo texto-size-20"/>
                                        </a4j:commandLink>
                                        <rich:spacer rendered="#{horario.horarioTurma.msgMatriculasFuturas != ''}" width="5"/>
                                        <a4j:commandButton id="btnRemoverHor" image="./imagens/time-delete-icon.png" action="#{ManutencaoModalidadeControle.removerHorario}"
                                                           title="remover horário" reRender="modalidadeTurma"/>
                                    </rich:column>
                                </rich:dataTable>
                                <div style="margin-top:5px;"></div>
                                <a4j:commandButton id="btnConsultaHor" image="./imagens/time-add-icon.png" oncomplete="#{ManutencaoModalidadeControle.abrirPanelConsulta}"
                                                   title="abrir consulta de horários" action="#{ManutencaoModalidadeControle.abrirConsultaHorario}" reRender="panelConsultarTurma"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ManutencaoModalidade_opcoes}"/>
                                </f:facet>
                                <a4j:commandButton id="btnRemoverTur" image="./imagens/group-delete-icon.png" action="#{ManutencaoModalidadeControle.removerTurma}"
                                                   title="remover turma" reRender="panelEdicaoModalidade"/>
                            </rich:column>
                        </rich:dataTable>
                        <div style="margin-top:10px; text-align: center;">
                        <a4j:commandButton id="btnConsultarTur" image="./imagens/group-add-icon.png" oncomplete="#{ManutencaoModalidadeControle.abrirPanelConsulta}"
                                           action="#{ManutencaoModalidadeControle.abrirConsultaTurma}" title="abrir consulta de turmas" reRender="panelConsultarTurma"/>
                    </div>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{!ManutencaoModalidadeControle.contratoModalidade.modalidade.utilizarTurma}">
                        <h:panelGrid columns="1" width="30%" rowClasses="linhaImpar" cellpadding="5">
                            <a4j:commandLink id="btnConsultarVezes" action="#{ManutencaoModalidadeControle.inicializarVezesPorSemana}" 
                                             oncomplete="Richfaces.showModalPanel('panelConsultaVezesSemana')" reRender="panelConsultaVezesSemana"
                                             styleClass="link_preto" value="#{ManutencaoModalidadeControle.contratoModalidade.nrVezesSemana} vezes por semana"/>
                        </h:panelGrid>
                    </h:panelGroup>
                    <div style="margin-top:10px; text-align: center;">
                        <a4j:commandLink id="btnGravar" action="#{ManutencaoModalidadeControle.gravarModalidade}" value="#{msg_bt.btn_gravar}" styleClass="pure-button pure-button-primary"
                                           title="gravar alterações na modalidade" reRender="mensagem, panelGeral, panelEdicaoModalidade"/>
                        <rich:spacer width="10"/>
                        <a4j:commandLink id="btnCancelar" styleClass="pure-button"  value="#{msg_bt.btn_cancelar}"  action="#{ManutencaoModalidadeControle.cancelarEdicaoModalidade}"
                                           title="cancelar edição de modalidade" reRender="mensagem, panelGeral, panelEdicaoModalidade"/>
                    </div>
                </rich:panel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens" style="padding:15;">
                    <h:outputText id="mensagemDetalhada" styleClass="mensagemDetalhada" value="#{ManutencaoModalidadeControle.mensagemDetalhada}"/>
                </h:panelGrid>

                <h:panelGrid id="detalhes" width="100%" columns="1" cellspacing="0" cellpadding="0">
                    <h:panelGrid rendered="#{ManutencaoModalidadeControle.apresentarDadosNovoModalidade && (!ManutencaoModalidadeControle.contratoNovo.vendaCreditoTreino)}"  width="100%" columns="1" cellspacing="0" cellpadding="0">
                        <h:panelGroup>
                            <h:outputText value="Valor do Contrato:" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <rich:spacer width="5px"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="R$ "/>
                            <h:outputText id="valorContrato" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.contratoNovo.valorBaseCalculo}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="Valor da Diferença: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <rich:spacer width="px"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="R$ "/>
                            <h:outputText id="valorDiferenca" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.valorAlteracaoModalidadeNovo}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="Valor diario da diferença (Dias do contrato): " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <rich:spacer width="5px"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="R$ "/>
                            <h:outputText id="valorDia" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.valorDiaModalidadeNovo}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="Quantidade de dias restante: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <rich:spacer width="5px"/>
                            <h:outputText id="qtdeDias" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.restanteDiasContrato}"/>
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="Valor final da manutenção: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="R$ "/>
                            <rich:spacer width="5px"/>
                            <h:outputText id="valorFinal" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.valorModalidadeNovo}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{ManutencaoModalidadeControle.periodoBonus}">
                            <h:outputText styleClass="tituloCamposVermelho" value="Este contrato está em período de Bônus. Portanto, nenhum valor será cobrado."/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="detalhesCreditoTreino" width="100%" columns="1" cellspacing="0" cellpadding="0">
                    <h:panelGrid rendered="#{ManutencaoModalidadeControle.apresentarDadosNovoModalidade && ManutencaoModalidadeControle.contratoNovo.vendaCreditoTreino}"  width="100%" columns="1" cellspacing="0" cellpadding="0">
                        <h:panelGroup rendered="#{ManutencaoModalidadeControle.alterouVezesSemanaContratoCreditoTreino}">
                            <h:outputText value="Saldo #{ClienteControle.configNomenclaturaVendaCredito}:"  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <rich:spacer width="px"/>
                            <h:outputText id="saldoCreditoTreino" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.saldoCreditoTreino}">
                            </h:outputText>
                        </h:panelGroup>

                        <h:panelGroup rendered="#{ManutencaoModalidadeControle.alterouVezesSemanaContratoCreditoTreino}">
                            <h:outputText value="#{ManutencaoModalidadeControle.labelQuantidadeCreditoTreinoNecessario}"  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <rich:spacer width="px"/>
                            <h:outputText id="creditoTreinoNecessario" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.totalCreditoPossivelUtilizar}">
                            </h:outputText>
                        </h:panelGroup>


                        <h:panelGroup rendered="#{ManutencaoModalidadeControle.alterouVezesSemanaContratoCreditoTreino}">
                            <h:outputText value="#{ManutencaoModalidadeControle.labelValorDiferencaCreditoTreino}"  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <rich:spacer width="px"/>
                            <h:outputText id="totalDiferencaCreditoTreino" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.valorDiferencaQuantidadeCreditoTreino}">
                            </h:outputText>
                        </h:panelGroup>

                        <h:panelGroup rendered="#{ManutencaoModalidadeControle.alterouVezesSemanaContratoCreditoTreino}">
                            <h:outputText rendered="#{ManutencaoModalidadeControle.contratoNovo.diferencaQtdeCreditoTreinoManutencaoModalidade != 0}"  value="#{ManutencaoModalidadeControle.labelValorUnitarioCreditoTreino}"  styleClass="tituloCampos"/>
                            <rich:spacer width="5px"/>
                            <h:outputText rendered="#{ManutencaoModalidadeControle.contratoNovo.diferencaQtdeCreditoTreinoManutencaoModalidade != 0}" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="R$ "/>
                            <h:outputText rendered="#{ManutencaoModalidadeControle.contratoNovo.diferencaQtdeCreditoTreinoManutencaoModalidade != 0}" id="valorContratoCreditoTreino" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.valorUnitarioCreditoTreino}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:outputText value="Valor final da manutenção: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="R$ "/>
                            <rich:spacer width="5px"/>
                            <h:outputText id="valorFinalCreditoTreino" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{ManutencaoModalidadeControle.valorModalidadeNovo}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{ManutencaoModalidadeControle.periodoBonus}">
                            <h:outputText styleClass="tituloCamposVermelho" value="Este contrato está em período de Bônus. Portanto, nenhum valor será cobrado."/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid width="150px">
                    <h:panelGroup>
                        <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});" styleClass="pure-button">
                            <i class="fa-icon-arrow-left"></i>
                        </h:commandLink>
                        <rich:spacer width="10"/>
                        <a4j:commandLink id="proximo" title="Finalizar Manutenção de Modalidade"
                                         styleClass="pure-button pure-button-primary"
                                         reRender="mdlMensagemGenerica"
                                         oncomplete="#{ManutencaoModalidadeControle.msgAlert};#{ManutencaoModalidadeControle.mensagemNotificar}"
                                         rendered="#{ManutencaoModalidadeControle.permiteAlterarModalidade}"
                                         action="#{ManutencaoModalidadeControle.proximo}">
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>

    <rich:modalPanel id="panelConsultaModalidades" styleClass="novaModal" shadowOpacity="true" autosized="true"
                     width="500" height="250">
        <f:facet name="header">

            <h:panelGroup>
                <h:outputText value="Seleção de Modalidades"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelink1"/>
                <rich:componentControl for="panelConsultaModalidades" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConsultaModalidades">
            <rich:dataTable id="listaModalidades" width="100%" styleClass="tabelaSimplesCustom" rowClasses="textsmall linhaImpar"
                            style="border-color:white;"
                            rows="10"
                            columnClasses="colunaEsquerda, centralizado"
                            value="#{ManutencaoModalidadeControle.modalidadesPlano}" var="modalidade">
                <rich:column styleClass="col-text-align-left" headerClass="covl-text-align-left">


                    <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul" id="selecionarModalidade"
                                     action="#{ManutencaoModalidadeControle.selecionarModalidade}"
                                     oncomplete="Richfaces.hideModalPanel('panelConsultaModalidades')"
                                     title="selecionar modalidade" reRender="panelGeral, panelEdicaoModalidade"
                                     value="#{modalidade.modalidade.nome}"/>

                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" for="formConsultaModalidades:listaModalidades" maxPages="10"
                               styleClass="scrollPureCustom" renderIfSinglePage="false" reRender="listaModalidades"
                               id="scTableAtualizacao"/>
            <div style="margin:10px 0 0 0; text-align: center;">
                <a4j:commandLink id="btnCancelarMod" oncomplete="Richfaces.hideModalPanel('panelConsultaModalidades')"
                                 style="margin-top: 50px"
                                 styleClass="botaoPrimario texto-size-14-real " value="#{msg_bt.btn_cancelar}"
                                 title="cancelar"/>
            </div>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelConsultaVezesSemana" styleClass="novaModal" shadowOpacity="true" autosized="true"
                     width="500" height="250">
        <f:facet name="header">

            <h:panelGroup>
                <h:outputText value="Vezes Por Semana da Modalidade"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink2"/>
                <rich:componentControl for="panelConsultaVezesSemana" attachTo="hidelink2" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConsultaVezesSemana" ajaxSubmit="true" styleClass="font-size-Em-max">
            <rich:dataTable id="listaVezesSemana" rowClasses="textsmall linhaImpar" width="100%"
                            styleClass="tabelaSimplesCustom"
                            columnClasses="colunaEsquerda, centralizado"
                            value="#{ManutencaoModalidadeControle.listaVezesSemana}" var="vezesSemana">
                <rich:column styleClass="col-text-align-left" headerClass="covl-text-align-left">

                    <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul" id="btnSelecVezes"
                                     oncomplete="Richfaces.hideModalPanel('panelConsultaVezesSemana')"
                                     action="#{ManutencaoModalidadeControle.selecionarVezesSemana}"
                                     title="selecionar vezes por semana" reRender="panelGeral, panelEdicaoModalidade"
                                     value="#{vezesSemana.nrVezes} vezes por semana"/>

                </rich:column>
                <rich:column>
                    <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                     action="#{ManutencaoModalidadeControle.selecionarVezesSemana}"
                                     oncomplete="Richfaces.hideModalPanel('panelConsultaVezesSemana')"
                                     reRender="panelGeral, panelEdicaoModalidade">
                        <span>Selecionar </span>
                        <i class="fa-icon-arrow-right"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <div style="margin:10px 0 0 0; text-align: center;">

                <a4j:commandLink id ="btnCancelaVezes"
                                   oncomplete="Richfaces.hideModalPanel('panelConsultaVezesSemana')"
                                   styleClass="botaoPrimario texto-size-14-real " value="#{msg_bt.btn_cancelar}"
                                   title="cancelar"/>

            </div>
        </a4j:form>
    </rich:modalPanel>

    <script>
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    </script>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp"/>
</f:view>

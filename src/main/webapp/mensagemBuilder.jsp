<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="./script/telaInicial.js"></script>
<script type="text/javascript" src="./bootstrap/jquery.js"></script>
<link href="./css/packcss1.0.min.css" rel="stylesheet" type="text/css">
<link href="./bootstrap/bootplus.css" rel="stylesheet">

<link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>

<script src="https://editor.unlayer.com/embed.js"></script>

<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>
<script type="text/javascript" src="script/scriptSMSBuilder.js"></script>
<script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>

<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:form id="form">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <html>
        <jsp:include page="include_headCRM.jsp" flush="true"/>
        <link href="./css/msg-builder-v0.3.css" rel="stylesheet" type="text/css">
        <head>
            <style type="text/css">
                .w25 {
                    padding: 0 !important;
                }

                .w50 {
                    padding: 0 !important;
                }

                .container-meta-diaria div.cb-container select {
                    z-index: 0;
                }

                .container-meta-diaria {
                    /* width: 100%; */
                    margin: 15px;
                    padding: 10px;
                    height: auto;
                    position: relative;
                    -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
                    -moz-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
                    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
                    background-color: #ffffff;
                    transition: transform .1s ease-out;
                    -moz-transition: -moz-transform .1s ease-out;
                    -o-transition: -o-transform .1s ease-out;
                    -webkit-transition: -webkit-transform .1s ease-out;
                }

                .rich-menu-list-bg {
                    overflow-y: auto;
                    max-height: 500px;
                }

                table.tabelaSimplesCustom:not(.dataTable) .rich-table-subheader {
                    height: auto !important;
                }

                table,
                tr,
                td {
                    vertical-align: middle !important;
                    border-collapse: collapse;
                    color: #000000 !important;
                }

                td.rich-mpnl-body {
                    vertical-align: top !important;
                }

                .rich-table-sortable-header {
                    color: #474747 !important;
                }

                .tags b {
                    cursor: copy;
                }
            </style>

            <script type="text/javascript">
                jQuery.noConflict();

                function resizeIframe(obj) {
                    obj.style.height = obj.contentWindow.document.documentElement.scrollHeight + 'px';
                }

                function copiar(elcopy) {
                    var el = document.createElement('textarea');
                    el.value = elcopy.innerHTML;
                    document.body.appendChild(el);
                    el.select();
                    document.execCommand('copy');
                    document.body.removeChild(el);
                    Notifier.info(el.value + ' copiada para a área de transferência.');
                }
            </script>
        </head>


        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_crm_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>


            <a4j:jsFunction reRender="mensagembuilder" name="novoModelo"
                            oncomplete="init()"
                            action="#{MensagemBuilderControle.novo}"></a4j:jsFunction>

            <c:if test="${SuperControle.menuZwUi}">
                <jsp:include page="include_box_menulateral.jsp">
                    <jsp:param name="menu" value="CRM-INICIO" />
                </jsp:include>
            </c:if>
            <c:if test="${not SuperControle.menuZwUi}">
                <style>
                    .container-imagem{
                        float: none !important;
                        width: 100% !important;
                    }


                    button.close {
                        color: #FFF;
                        opacity: 1;
                        padding-top: 10px;
                        font-weight: lighter;
                    }
                </style>
            </c:if>
            <h:panelGroup  layout="block" styleClass="container-imagem" style="height: 100% !important">
                <h:panelGroup layout="block" styleClass="container-meta-diaria container-box zw_ui especial" id="mensagembuilder"
                              style="width: 96%; height: 100%; margin-left: auto; margin-right: auto; display: block; min-height: 75vh; padding-bottom: 150px;">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned"
                                  style="padding: 10px; position: relative">
                        <div class="tituloPainelPesquisa notop" style="
        font-weight: bold;font-size: 16px; z-index: 10; width: 100%; float: left;">

                            <h:panelGroup layout="block" styleClass="text" style="display: inline;">
                                <a4j:commandLink rendered="#{not MensagemBuilderControle.building and not MensagemBuilderControle.replicarEmpresa}"
                                                 id="btnCriarContato" action="#{MensagemBuilderControle.sairVisaoAgendados}"
                                                 oncomplete="location.reload();"
                                                 styleClass="top-aba-builder"
                                                 style="#{MensagemBuilderControle.visaoAgendados ? 'opacity: 0.3;' : 'border-bottom: solid #094771;' }">
                                    <h:outputText styleClass="container-header-titulo"
                                                  value="Criar contato"/>
                                </a4j:commandLink>

                                <a4j:commandLink action="#{MensagemBuilderControle.entrarVisaoAgendados}"
                                                 id="btnVerAgendamentos" rendered="#{not MensagemBuilderControle.building and not MensagemBuilderControle.replicarEmpresa}"
                                                 reRender="mensagembuilder"
                                                 styleClass="top-aba-builder"
                                                 style="padding: 0 15px; display: inline-block; padding-bottom: 10px; #{MensagemBuilderControle.visaoAgendados ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}">
                                    <h:outputText styleClass="container-header-titulo" value="Ver agendamentos"/>
                                </a4j:commandLink>

                                <a4j:commandLink rendered="#{MensagemBuilderControle.building or MensagemBuilderControle.replicarEmpresa}"
                                              styleClass="top-aba-builder"
                                              style="padding: 0 15px; display: inline-block; padding-bottom: 10px; #{MensagemBuilderControle.building ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}"
                                              reRender="mensagembuilder"
                                              action="#{MensagemBuilderControle.acionarAbaConfigEnvio}">
                                    <h:outputText styleClass="container-header-titulo" value="Configurando envio"/>
                                </a4j:commandLink>

                                <a4j:commandLink rendered="#{MalaDiretaControle.exibirReplicarRedeEmpresa and (MensagemBuilderControle.building or MensagemBuilderControle.replicarEmpresa)}"
                                         styleClass="top-aba-builder"
                                         style="padding: 0 15px; display: inline-block; padding-bottom: 10px; #{!MensagemBuilderControle.building ? 'border-bottom: solid #094771;' : 'opacity: 0.3;'}"
                                         action="#{MensagemBuilderControle.acionarAbaReplicarEmpresa}"
                                         reRender="mensagembuilder">
                                    <h:outputText styleClass="container-header-titulo" value="Replicar Empresa"/>
                                </a4j:commandLink>
                            </h:panelGroup>


                            <h:panelGroup layout="block" styleClass="botoes-builder"
                                          rendered="#{not MensagemBuilderControle.building}">


                                <h:panelGroup
                                        rendered="#{MalaDiretaControle.apresentarOpcoesSMS and MensagemBuilderControle.meioSms and LoginControle.apresentarPactoStore}">
                                    <a4j:commandLink action="#{MensagemBuilderControle.abrirPactoStore}"
                                                     style="color: #0090FF; font-size: 14px;">
                                        Você está sem saldo. Clique aqui para adquirir.
                                    </a4j:commandLink>
                                </h:panelGroup>


                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="botoes-builder"
                                          rendered="#{MensagemBuilderControle.building and not MensagemBuilderControle.configEnvio}">

                                <a onclick="Richfaces.showModalPanel('mdlDescartar')"
                                   style="margin-right: 5px;"
                                   id="btnDescartarModelo"class="botaoSecundario texto-size-14-real ">
                                    <i class="fa-icon-remove"></i>
                                    <span>
                                        <c:if test="${MensagemBuilderControle.editandoModelo}">Descartar alterações</c:if>
                                        <c:if test="${not MensagemBuilderControle.editandoModelo}">Descartar</c:if>
                                    </span>
                                </a>

                                <a style="margin-right: 5px;cursor: pointer" onclick="setdados();"
                                   id="btnSalvarModelo"class="botaoSecundario texto-size-14-real">
                                    <i class="fa-icon-save"></i>

                                    <span><c:if test="${MensagemBuilderControle.editandoModelo}">Salvar alterações</c:if>
                                    <c:if test="${not MensagemBuilderControle.editandoModelo}">Salvar como modelo</c:if></span>
                                </a>

                                <a style="margin-right: 5px;cursor: pointer" onclick="prosseguir();"
                                   id="btnProsseguirEnvio"class="botaoPrimario texto-size-14-real" id="ProsseguirEnvio">
                                    <span>Prosseguir com o envio</span>
                                    <i class="fa-icon-chevron-right"></i>
                                </a>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="botoes-builder"
                                          rendered="#{MensagemBuilderControle.building and MensagemBuilderControle.configEnvio}">

                                <a4j:commandLink action="#{MensagemBuilderControle.voltarLista}"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.codigo > 0}"
                                                 style="margin-right: 5px;"
                                                 reRender="mensagembuilder"
                                                 styleClass="botaoSecundario texto-size-14-real ">
                                    <i class="fa-icon-chevron-left"></i>
                                    <h:outputText value="Voltar"/>
                                </a4j:commandLink>

                            <a4j:commandLink action="#{MensagemBuilderControle.voltarBuilder}"
                                             rendered="#{!MensagemBuilderControle.modeloAntigoSelecionado && !MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo && (MalaDiretaControle.malaDiretaVO.codigo == 0 || not empty MalaDiretaControle.malaDiretaVO.configs)}"
                                             style="margin-right: 5px;"
                                             oncomplete="init(body)"
                                             reRender="mensagembuilder"
                                             styleClass="botaoSecundario texto-size-14-real ">
                                <h:outputText style="margin-right: 5px" styleClass="fa-icon-chevron-left"
                                              rendered="#{MalaDiretaControle.malaDiretaVO.codigo == 0}"></h:outputText>
                                <h:outputText style="margin-right: 5px" styleClass="fa-icon-pencil"
                                              rendered="#{MalaDiretaControle.malaDiretaVO.codigo > 0}"></h:outputText>
                                <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.codigo == 0}" value="Voltar"/>
                                <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.codigo > 0}"
                                              value="Editar mensagem"/>
                            </a4j:commandLink>
                            <a4j:commandLink
                                    rendered="#{MensagemBuilderControle.modeloAntigoSelecionado && !MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo && (MalaDiretaControle.malaDiretaVO.codigo == 0 || not empty MalaDiretaControle.malaDiretaVO.configs)}"
                                    style="margin-right: 5px;"
                                    oncomplete="Richfaces.showModalPanel('mdlDescartar')"
                                    reRender="mensagembuilder"
                                    styleClass="botaoSecundario texto-size-14-real ">
                                <i class="fa-icon-remove" style="margin-right: 5px"></i><h:outputText
                                    value="Descartar"/>
                            </a4j:commandLink>
                            <a4j:commandLink action="#{MensagemBuilderControle.gravarMaladireta}"
                                             reRender="mensagembuilder"
                                             rendered="#{MalaDiretaControle.malaDiretaVO.codigo == 0 or (!MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo)}"
                                             oncomplete="#{MensagemBuilderControle.msgAlert}"
                                             styleClass="gravarenviar botaoPrimario texto-size-14-real ">
                                <h:outputText style="margin-right: 5px" styleClass="fa-icon-paper-plane"
                                              rendered="#{MalaDiretaControle.malaDiretaVO.codigo == 0}"/>
                                <h:outputText style="margin-right: 5px" styleClass="fa-icon-save"
                                              rendered="#{MalaDiretaControle.malaDiretaVO.codigo > 0}"/>
                                <h:outputText value="Gravar e enviar"
                                              rendered="#{MalaDiretaControle.malaDiretaVO.codigo == 0}"/>
                                <h:outputText value="Gravar alterações"
                                              rendered="#{MalaDiretaControle.malaDiretaVO.codigo > 0}"/>
                            </a4j:commandLink>
                            <a4j:commandLink action="#{MensagemBuilderControle.habilitarEnvio}"
                                             reRender="mensagembuilder"
                                             rendered="#{!MalaDiretaControle.malaDiretaVO.envioHabilitado and MalaDiretaControle.malaDiretaVO.codigo > 0}"
                                             oncomplete="#{MensagemBuilderControle.msgAlert}"
                                             styleClass="botaoPrimario texto-size-14-real ">
                                <h:outputText style="margin-right: 5px" styleClass="fa-icon-paper-plane"></h:outputText>
                                <h:outputText value="Habilitar envio"></h:outputText>
                            </a4j:commandLink>

                                <a4j:commandLink id="reenviaEmail" action="#{MensagemBuilderControle.forcarEnvio}"
                                                 styleClass="botaoPrimario texto-size-14-real"
                                                 reRender="mensagembuilder"
                                                 rendered="#{MalaDiretaControle.contatoInstantaneo}"
                                                 oncomplete="#{MensagemBuilderControle.msgAlert}">
                                    <h:outputText style="margin-right: 5px" styleClass="fa-icon-paper-plane"></h:outputText>
                                    <h:outputText value="Reenviar e-mail" rendered="#{MensagemBuilderControle.meioEmail}"></h:outputText>
                                    <h:outputText value="Reenviar SMS" rendered="#{MensagemBuilderControle.meioSms}"></h:outputText>
                                    <h:outputText value="Reenviar APP" rendered="#{MensagemBuilderControle.meioApp}"></h:outputText>
                                </a4j:commandLink>
                                </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="botoes-builder"
                                      rendered="#{not MensagemBuilderControle.building and MensagemBuilderControle.replicarEmpresa}">
                        </h:panelGroup>

                        </div>
                        <c:if test="${!MensagemBuilderControle.replicarEmpresa}">
                            <jsp:include page="mensagemBuilder_meio_envio.jsp" flush="true"/>
                            <jsp:include page="mensagemBuilder_modelos.jsp" flush="true"/>
                            <jsp:include page="mensagemBuilder_modelos_antigos.jsp" flush="true"/>
                            <jsp:include page="mensagemBuilder_templatesPacto.jsp" flush="true"/>
                            <jsp:include page="mensagemBuilder_listaMailings.jsp" flush="true"/>
                            <jsp:include page="mensagemBuilder_builder.jsp" flush="true"/>
                            <jsp:include page="mensagemBuilder_config_envio.jsp" flush="true"/>
                        </c:if>

                        <c:if test="${MensagemBuilderControle.replicarEmpresa}">
                            <jsp:include page="mensagemBuilder_replicarEmpresa.jsp" flush="true"/>
                        </c:if>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

        <c:if test="${SuperControle.menuZwUi}">
            <script>
                fecharMenu();
            </script>
        </c:if>

        </html>
    </h:form>



    <jsp:include page="mensagemBuilder_modalConfig.jsp" flush="true"/>
    <jsp:include page="mensagemBuilder_modalSalvarModelo.jsp" flush="true"/>
    <jsp:include page="include_modais_tela_messageBuilder.jsp" flush="true"/>

    <rich:modalPanel id="modalComprarSMS" autosized="true" styleClass="novaModal" shadowOpacity="true"
                     width="650">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Comprar SMS."/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkComprarSMS"/>
                <rich:componentControl for="modalComprarSMS" attachTo="hidelinkComprarSMS"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formSMS" ajaxSubmit="true" styleClass="novaModal">

            <a4j:commandLink id="calcularValorSMS" reRender="formSMS"
                             action="#{AdquiraSMSControle.calcularValorSMS}">
            </a4j:commandLink>

            <h:inputHidden id="valorCalcular" value="#{AdquiraSMSControle.limiteUnico}"/>

            <h:panelGroup layout="block" styleClass="margin-box">
                <h:panelGrid width="100%" columns="2" cellpadding="5"
                             styleClass="font-size-Em-max">
                    <h:selectOneRadio value="#{AdquiraSMSControle.tipoServico}"
                                      onclick="#{AdquiraSMSControle.calcularValorSMS}"
                                      style="font-family: Arial; !important; font-size: 1.1em !important;  color:#777777; !important;"
                                      layout="pageDirection">
                        <f:selectItem itemValue="1"
                                      itemLabel="Comprar serviço anual de envio de 1000 SMS/DIA: R$ 3.574,00"/>
                        <f:selectItem itemValue="2"
                                      itemLabel="Comprar serviço anual de envio de 500 SMS/DIA: R$ 1.787,00"/>
                        <f:selectItem itemValue="3" itemLabel="Comprar SMS Avulso: R$ 0,12"/>
                        <a4j:support event="onclick" reRender="formSMS"></a4j:support>
                    </h:selectOneRadio>

                </h:panelGrid>

                <h:panelGroup layout="block" styleClass="margin-box" rendered="#{AdquiraSMSControle.tipoServico eq 3}"
                              id="panelLimiteUnico" style="margin-top: -10px; margin-bottom: -25px;">
                    <h:panelGrid width="100%" columns="2" cellpadding="5"
                                 styleClass="font-size-Em-max">
                        <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                      value="Digite quantos SMS deseja (mínimo de 5000):"/>

                        <h:panelGroup>
                            <input type="text" class="inputTextClean"
                                   id="limiteUnico"
                                   onchange="preencherValorChamarBotao('formSMS:calcularValorSMS', 'formSMS:valorCalcular', this.value)"
                                   style="margin-left:1px; margin-top: 3px;text-align: right;width: 130px;"
                                   value="${AdquiraSMSControle.limiteUnico}"/>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGroup>


                <h:panelGroup style="margin-top: 10px !important;" styleClass="margin-box">
                    <h:panelGrid width="100%" columns="2" cellpadding="5">
                        <h:outputText styleClass="rotuloCampos" style="font-size: 18px; !important;" value="TOTAL:"/>

                        <h:panelGroup>
                            <h:outputText styleClass="rotuloCampos" id="valorSMS"
                                          style="margin-left: 320px; font-size: 18px; !important;"
                                          value=" #{AdquiraSMSControle.valorSMSApresentar}"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGroup styleClass="container-botoes">

                    <h:panelGroup layout="block">
                        <a4j:commandLink action="#{AdquiraSMSControle.realizarConsultaLogObjetoSelecionado}"
                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                         title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                         style="display: inline-block; margin-top: -11px; padding: 10px 16px; border-radius: 6px;">
                            <i class="fa-icon-list"></i>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <a4j:commandLink id="comprar" value="Comprar"
                                     styleClass="botaoPrimario texto-size-18"
                                     reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                                     oncomplete="#{AdquiraSMSControle.onCompleteGravar};#{AdquiraSMSControle.mensagemNotificar}"
                                     action="#{AdquiraSMSControle.comprarSMS}">
                    </a4j:commandLink>

                </h:panelGroup>
            </h:panelGroup>
            <script>
                function preencherValorChamarBotao(idBotao, idHidden, valorHidden) {
                    var hidden = document.getElementById(idHidden);
                    var botao = document.getElementById(idBotao);
                    hidden.value = valorHidden;
                    botao.click();
                    event.stopPropagation();
                }
            </script>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalSMSVendido" autosized="true" styleClass="novaModal" shadowOpacity="true"
                     width="650" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Conclusão negociação compra de SMS."/>
            </h:panelGroup>
        </f:facet>
        <a4j:form ajaxSubmit="true">
            <h:panelGrid>
                <h:panelGroup layout="block" style="margin: 10 20 10 20;">
                    <h:panelGrid id="ativacao" style="margin-top: 2vh; font-size: 14px !important;" columns="1"
                                 columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza"
                                      value="Em breve nosso financeiro irá entrar em contato para concretizar a compra. Enquanto isso você já pode desfrutar do envio de SMS."/>
                    </h:panelGrid>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes"
                              style="position: absolute; width: 100%; bottom: 6%; text-align: center">
                    <a4j:commandLink id="btnAtualizar" styleClass="botaoPrimario texto-size-16"
                                     value="Ok"
                                     reRender="formM,panelGridMensagens"
                                     action="#{MalaDiretaControle.consultarSaldo}"
                                     oncomplete="Richfaces.hideModalPanel('modalSMSVendido')" style="color: #0090FF;">
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlDescartar" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Descartar"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkDescartar"/>
                <rich:componentControl for="mdlDescartar" attachTo="hidelinkDescartar" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlDescartar">
            <h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        Deseja descartar a mensagem que está criando?
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <h:panelGroup styleClass="margin-box">
                        <a4j:commandLink action="#{MensagemBuilderControle.cancelar}" value="Sim"
                                         id="sim"
                                         styleClass="botaoPrimario texto-size-16-real"
                                         oncomplete="Richfaces.hideModalPanel('mdlDescartar');location.reload();"/>
                        <rich:spacer width="30px;"/>
                        <a onclick="Richfaces.hideModalPanel('mdlDescartar');"
                           class="botaoSecundario texto-size-16-real">Não</a>
                    </h:panelGroup>
                </h:panelGroup>
                <br/>
                <br/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="comprarCreditoMarketing" styleClass="novaModal" width="560" autosized="true"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Saldo Insuficiente"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkcomprarCreditoMarketing"/>
                <rich:componentControl for="comprarCreditoMarketing" attachTo="hidelinkcomprarCreditoMarketing"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlcomprarCreditoMarketing">
            <h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup rendered="#{not LoginControle.apresentarPactoStore}" layout="block"
                                  styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        Seu contato em grupo foi salvo, mas você não possui saldo suficiente de SMS para enviá-lo. Entre em contato com o comercial para adquirir um novo pacote. Após adquirir, volte no contato e clique em Habilitar envio.
                    </h:panelGroup>

                    <h:panelGroup rendered="#{LoginControle.apresentarPactoStore}" layout="block"
                                  styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        Seu contato em grupo foi salvo, mas você não possui saldo suficiente de SMS para enviá-lo. Clicando em Ir para o Market Place, você será direcionado à página onde poderá adquirir um pacote de SMS para o envio das suas mensagens. Após adquirir, volte no contato e clique em
                        <b>Habilitar envio</b>
                    </h:panelGroup>

                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes"
                              rendered="#{LoginControle.apresentarPactoStore}">
                    <h:panelGroup>
                        <a4j:commandLink action="#{MensagemBuilderControle.forcarEnvio}"
                                         value="Enviar saldo restante"
                                         oncomplete="Richfaces.hideModalPanel('comprarCreditoMarketing');#{MensagemBuilderControle.msgAlert}"
                                         reRender="mensagembuilder"
                                         id="forcar-envio"
                                         styleClass="botaoSecundario texto-size-16-real"/>
                    </h:panelGroup>
                    <h:panelGroup style="margin-left: 25px">
                        <a4j:commandLink action="#{MensagemBuilderControle.abrirPactoStore}"
                                         value="Ir para o Market Place"
                                         id="market-place"
                                         styleClass="botaoPrimario texto-size-16-real"/>
                    </h:panelGroup>
                </h:panelGroup>
                <br/>
                <br/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="limiteDiarioAtingido" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Limite de Envio Diario Atingido"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hideLimiteDiarioAtingido"/>
                <rich:componentControl for="limiteDiarioAtingido" attachTo="hideLimiteDiarioAtingido" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlLimiteDiarioAtingido">
            <h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        <h:outputText
                                value="A quantidade de destinatários é maior que o limite diário disponível, você possui #{MalaDiretaControle.qtdEmailsRestandoEnviarDia} envios restantes hoje"/>
                        <h:outputText value="#{MalaDiretaControle.qtdEmailsRestandoEnviarDia > 0 ? ',' : '.'}"/>
                        <h:outputText rendered="#{MalaDiretaControle.qtdEmailsRestandoEnviarDia > 0}"
                                      value=" o e-mail será enviado para #{MalaDiretaControle.qtdEmailsRestandoEnviarDia} destinatários, deseja prosseguir?"/>
                    </h:panelGroup>

                </h:panelGroup>
                <h:panelGroup rendered="#{MalaDiretaControle.qtdEmailsRestandoEnviarDia > 0}" layout="block"
                              styleClass="container-botoes">
                    <h:panelGroup>
                        <a4j:commandLink action="#{MensagemBuilderControle.simEnviarSomenteSaldoRestanteDia}"
                                         value="Sim"
                                         reRender="limiteMensalAtingido,mensagembuilder"
                                         oncomplete="Richfaces.hideModalPanel('limiteDiarioAtingido');#{MensagemBuilderControle.msgAlert}"
                                         styleClass="botaoPrimario texto-size-16-real"/>
                    </h:panelGroup>
                    <h:panelGroup style="margin-left: 15px">
                        <a4j:commandLink oncomplete="Richfaces.hideModalPanel('limiteDiarioAtingido');"
                                         value="Não"
                                         styleClass="botaoSecundario texto-size-16-real"/>
                    </h:panelGroup>
                </h:panelGroup>
                <br/>
                <br/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="limiteMensalAtingido" styleClass="novaModal" width="520" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Limite de Envio Mensal Atingido"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hideLimiteMensalAtingido"/>
                <rich:componentControl for="limiteMensalAtingido" attachTo="hideLimiteMensalAtingido" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form prependId="true" id="formMdlLimiteMensalAtingido">
            <h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                        <h:outputText
                                value="A quantidade de destinatários é maior que o limite mensal disponível, você possui #{MalaDiretaControle.qtdEmailsRestandoEnviarMensal} envios restantes no mês"/>
                        <h:outputText value="#{MalaDiretaControle.qtdEmailsRestandoEnviarMensal > 0 ? ',' : '.'}"/>
                        <h:outputText rendered="#{MalaDiretaControle.qtdEmailsRestandoEnviarMensal > 0}"
                                      value=" o e-mail será enviado para #{MalaDiretaControle.qtdEmailsRestandoEnviarMensal} destinatários, deseja prosseguir?"/>
                        <br/>
                        <br/>
                        <h:panelGroup>
                            <a4j:commandLink action="#{MensagemBuilderControle.voltarLista}"
                                             value="Descartar alterações e sair"
                                             reRender="mensagembuilder"
                                             oncomplete="Richfaces.hideModalPanel('limiteMensalAtingido');#{MensagemBuilderControle.msgAlert}"
                                             styleClass="botaoSecundario texto-size-16-real"/>
                        </h:panelGroup>
                        <h:panelGroup style="margin-left: 10px">
                            <a4j:commandLink action="#{MensagemBuilderControle.simEnviarSomenteSaldoRestanteMes}"
                                             value="Salvar alterações e sair"
                                             reRender="mensagembuilder"
                                             oncomplete="Richfaces.hideModalPanel('limiteMensalAtingido');#{MensagemBuilderControle.msgAlert}"
                                             styleClass="botaoPrimario texto-size-16-real"/>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGroup>
                <h:panelGroup rendered="#{MalaDiretaControle.qtdEmailsRestandoEnviarMensal > 0}" layout="block"
                              styleClass="container-botoes">
                    <h:panelGroup>
                        <a4j:commandLink action="#{MensagemBuilderControle.simEnviarSomenteSaldoRestanteMes}"
                                         value="Sim"
                                         reRender="mensagembuilder"
                                         oncomplete="Richfaces.hideModalPanel('limiteMensalAtingido');#{MensagemBuilderControle.msgAlert}"
                                         styleClass="botaoPrimario texto-size-16-real"/>
                    </h:panelGroup>
                    <h:panelGroup style="margin-left: 15px">
                        <a4j:commandLink oncomplete="Richfaces.hideModalPanel('limiteMensalAtingido');"
                                         value="Não"
                                         styleClass="botaoSecundario texto-size-16-real"/>
                    </h:panelGroup>
                </h:panelGroup>
                <br/>
                <br/>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
    <%@include file="includes/include_modal_mensagem_entregabilidade.jsp"%>
    <jsp:include page="mensagemBuilder_modais.jsp" flush="true"/>
    <jsp:include page="mensagemBuilder_modais_tags.jsp" flush="true"/>

</f:view>



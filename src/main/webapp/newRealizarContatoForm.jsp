<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/crm.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>

<script type="text/javascript" src="script/scriptSMS.js"></script>
<script type="text/javascript" src="script/script.js"></script>
<style type="text/css">
    .rich-table-thead {
        background: none !important;
        border: none !important;
    }

    .rich-table-subheader {
        background: none !important;
        border: none !important;
    }

    .rich-table-subheadercell {
        background: none !important;
        border: none !important;
        text-align: left !important;
        padding-left: 7px !important;
    }

    .rich-tabpanel-content {
        background: none !important;
        border: none !important;
        padding-top: 7px !important;
        padding-left: 0px !important;
        padding-right: 0px !important;
        text-align: left !important;
    }

    .rich-table {
        background: none !important;
        border: none !important;
        text-align: left !important;
    }

    .rich-table-cell {
        border: none !important;
        text-align: left !important;
    }

    .rich-table-row {
        padding-top: 10px !important;
    }

    .rich-tab-active {
        background: none !important;
        background-color: #ffffff !important;
        border-color: #C0C0C0;
        font-weight: bold;

        border-bottom-color: white;
        border-bottom-width: 1px;
    }

    .rich-tab-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tab-header {
        font-size: 14px;
        padding: 10px;

        font-family: arial, helvetica, sans-serif;
    }

    .rich-tabhdr-cell-disabled {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-cell-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-cell {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-border {
        background: none !important;
        border: none !important;
    }

    .rich-tab-bottom-line {
        border-width: 1px;
        border-color: #C0C0C0;
        background-color: #EEEEEE !important;
    }

    .btnsOptAPP {
        margin-left: 10px;
        float: left;
        display: none;
    }

    .textareaResize {
        width: 100%;
        resize: none;
        box-shadow: none !important;
        border: none !important;
        background: transparent !important;
        margin: 0px !important;
        padding: 0px !important;
        font-size: 10pt !important;
    }

    .rich-tab-bottom-line table:first-child{
        width: 100%;
    }
    th.subordinado {
        padding: 12px;
    }

    .sumir{
        display: none;
    }

    .sumir.aparecer{
        display: block;
    }

    .sumiricon.aparecer .fa-icon-chevron-down, .sumiricon .fa-icon-chevron-up{
        display: none;
    }

    .sumiricon .fa-icon-chevron-down, .sumiricon.aparecer .fa-icon-chevron-up{
        display: block;
    }
</style>

<script type="text/javascript">
    function mostrarProximo(item) {
        jQuery('.labelOp' + (item + 1)).show();
        jQuery('.btnOp' + item).hide();
        jQuery('.btnOp' + (item + 1)).show();
    }

    function sumirItem(item) {
        jQuery('.labelOp' + item).hide();
        jQuery('.form.labelOp' + item).val('');
        jQuery('.btnOp' + (item - 1)).show();
        jQuery('.btnOp' + item).hide();
    }

    function show_hide(classe){
        jQuery(classe).toggleClass('aparecer');
    }
</script>

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>


<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Agenda_realizarContatoAvulso}"/>
    </title>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:form id="form" styleClass="pure-form" style="background: white; height: 100%;">

        <h:panelGroup id="panelGridRight" layout="block">


            <h:panelGroup style="padding: 20px; " layout="block"
                          rendered="#{not empty MetaCRMControle.mensagemRealizarContato}">
                <h:outputText
                        style="color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                        value="#{MetaCRMControle.mensagemRealizarContato}"/>
            </h:panelGroup>

            <%--MENSAGEM DETALHADA--%>
            <h:panelGrid columns="1" width="100%" rendered="#{not empty MetaCRMControle.mensagemDetalhada}">
                <h:outputText id="msgRealizarContatoSuperior" styleClass="mensagem"
                              value="#{MetaCRMControle.mensagem}"/>
                <h:outputText id="msgRealizarContatoDetSuperior" styleClass="mensagemDetalhada"
                              value="#{MetaCRMControle.mensagemDetalhada}"/>
            </h:panelGrid>


            <h:panelGroup id="operacoesHistorico" layout="block"
                          rendered="#{MetaCRMControle.mostrarPanelRealizarContato}">

                <jsp:include page="include_dados_cliente_realizar_contato.jsp" flush="true"/>


                <%-- MOTIVO POS-VENDA --%>
                <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                             rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'PV'}">
                    <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                                  layout="block">
                        <h:outputText
                                style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                value="Motivo"/>
                        <h:outputText
                                style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                                value="#{MetaCRMControle.metaDetalhadoVOSelecionado.configuracaoDiasPosVendaVO.descricao}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <%-- OBS TURMA LISTA ESPERA CRM --%>
                <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                             rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'FT'}">
                    <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                                  layout="block">
                        <h:outputText
                                style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                value="Observação"/>
                        <h:outputText
                                style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                                value="#{MetaCRMControle.metaDetalhadoVOSelecionado.observacaoFilaEsperaTurmaCrm}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <%-- EX-ALUNO --%>
                <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                             rendered="#{MetaCRMControle.tipoMetaSelecionada.fasesCRMEnum.sigla == 'EX'}">
                    <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                                  layout="block">
                        <h:outputText
                                style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: #333333; font-weight: bold;"
                                value="Ex-Aluno "/>
                        <h:outputText
                                style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: #333333;"
                                value="#{MetaCRMControle.metaDetalhadoVOSelecionado.diasSemAgendamento} Dias"/>
                    </h:panelGroup>
                </h:panelGrid>

                <%-- GRUPO DE RISCO --%>
                <h:panelGrid columns="1" width="100%" style="background: #e6e6e6;"
                             rendered="#{MetaCRMControle.historicoContatoVO.clienteVO.situacaoClienteSinteticoVO.pesoRisco >= 6}">
                    <h:panelGroup style="padding-left: 20px; padding-bottom: 10px"
                                  layout="block">
                        <img style="vertical-align: middle; margin-right: 4px;" src="imagens/ico_ani_telefone.gif">
                        <h:outputText
                                style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: red; font-weight: bold;"
                                value="Cliente com peso #{MetaCRMControle.historicoContatoVO.clienteVO.situacaoClienteSinteticoVO.pesoRisco} em RISCO de sair da academia. Faça algo urgente."/>
                    </h:panelGroup>
                </h:panelGrid>

                <%-- FASES DO ALUNO --%>
                <h:panelGrid id="fasesDoContato" rendered="#{MetaCRMControle.apresentarFasesDoContato}" columns="1"
                             width="100%"
                             style="background: #e6e6e6;">
                    <h:panelGroup style="padding-left: 20px;" layout="block">
                        <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: red;"
                                      value="O Aluno está nas seguintes Fases: "/>
                        <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; color: #333333;"
                                      value="#{MetaCRMControle.fasesDoContato}"/>
                    </h:panelGroup>
                    <h:panelGroup style="padding-left: 20px; padding-bottom: 10px" layout="block">
                        <h:outputText style="font-size: 12px;font-family: arial, helvetica, sans-serif; color: #333333;"
                                      value="Lembrando que: O contato não necessariamente irá bater meta para as duas fases."/>
                    </h:panelGroup>
                </h:panelGrid>


                <h:panelGroup style="border: 1px solid #ccc; margin: 10px; padding: 10px; " layout="block">
                    <h:panelGroup layout="block" style="margin-bottom: 10px">
                        <%--TIPO CONTATO--%>
                        <h:selectOneMenu id="tipoOperacao" style="margin-right: 10px; font-size: 12px"
                                         value="#{MetaCRMControle.historicoContatoVO.tipoContato}">
                            <f:selectItems value="#{MetaCRMControle.listaTipoContato}"/>
                            <a4j:support event="onchange" action="#{MetaCRMControle.alterarMeioEnvio}"
                                         oncomplete="adicionarPlaceHolderCRM()" reRender="panelGridRight"/>
                        </h:selectOneMenu>

                        <%--FASE ATUAL--%>
                        <h:selectOneMenu id="faseAtual" style="margin-right: 10px; font-size: 12px"
                                         value="#{MetaCRMControle.historicoContatoVO.fase}">
                            <f:selectItem itemLabel="Fase Atual" itemValue=" "/>
                            <f:selectItems value="#{MetaCRMControle.listaSelectItemFaseAtual}"/>
                        </h:selectOneMenu>

                        <%--PESQUISA--%>
                        <h:selectOneMenu id="pesquisa" style="margin-right: 10px; font-size: 12px"
                                         rendered="#{MetaCRMControle.existePesquisa}"
                                         value="#{MetaCRMControle.pesquisa}">
                            <f:selectItems value="#{MetaCRMControle.listaSelectItemPesquisa}"/>
                            <a4j:support event="onchange" action="#{MetaCRMControle.adicionarLinkPesquisa}"
                                         status="false"
                                         oncomplete="adicionarPlaceHolderCRM();#{MetaCRMControle.mensagemNotificar}"
                                         reRender="panelGridRight"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <%---------------------------------------- FAZER INDICAÇÃO ----------------------------------------------------------%>
                    <h:panelGroup layout="block" styleClass="triangleCSS" style="float: right; margin-right: 7%;"
                                  rendered="#{MetaCRMControle.mostrarPanelIndicacao}"/>
                    <h:panelGrid id="panelFazerIndicao" columns="1" width="100%" style="background: #e6e6e6"
                                 rendered="#{MetaCRMControle.mostrarPanelIndicacao}">

                        <h:panelGroup layout="block"
                                      style="padding-left: 20px; padding-bottom: 15px; padding-top: 15px">
                            <h:inputText id="nomeIndicado" maxlength="50"
                                         onkeypress="tabenter('form:telIndicado01')"
                                         style="background: #F6F6F6; width: 58%"
                                         value="#{MetaCRMControle.indicadoVO.nomeIndicado}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 10px" >
                            <h:inputText id="cpfindicado" maxlength="16"
                                         style="background: #F6F6F6; width: 28%"
                                         onblur="blurinput(this);"
                                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                         onfocus="focusinput(this);"
                                         value="#{MetaCRMControle.indicadoVO.cpf}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                            <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                <h:inputText id="telIndicado01" maxlength="13"
                                             style="background: #F6F6F6; width: 28%"
                                             onchange="return validar_Telefone(this.id);"
                                             onblur="blurinput(this);"
                                             onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                             onfocus="focusinput(this);"
                                             value="#{MetaCRMControle.indicadoVO.telefoneIndicado}"/>
                                <rich:spacer width="13px"/>
                                <h:inputText id="telIndicado02" maxlength="13"
                                             style="background: #F6F6F6; width: 28%"
                                             onchange="return validar_Telefone(this.id);"
                                             onblur="blurinput(this);"
                                             onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                             onfocus="focusinput(this);"
                                             value="#{MetaCRMControle.indicadoVO.telefone}"/>
                            </c:if>

                            <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                <h:inputText id="telIndicado01" maxlength="20"
                                             style="background: #F6F6F6; width: 28%"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{MetaCRMControle.indicadoVO.telefoneIndicado}"/>
                                <rich:spacer width="13px"/>
                                <h:inputText id="telIndicado02" maxlength="20"
                                             style="background: #F6F6F6; width: 28%"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{MetaCRMControle.indicadoVO.telefone}"/>
                            </c:if>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                            <h:inputText id="emailIndicado" maxlength="50"
                                         onkeypress="tabenter('form:eventoIndicado')"
                                         style="background: #F6F6F6; width: 58%"
                                         value="#{MetaCRMControle.indicadoVO.email}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="padding-left: 20px; padding-bottom: 5px">
                            <h:inputText id="eventoIndicado"
                                         onkeypress="tabenter('form:obsIndicado')"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         style="background: #F6F6F6; width: 58%"
                                         value="#{MetaCRMControle.indicacaoVO.evento.descricao}"/>
                            <rich:suggestionbox
                                    id="suggestionEventoIndicacao"
                                    style="height: 5%; width: 26%"
                                    for="eventoIndicado"
                                    fetchValue="#{eventos.descricao}"
                                    suggestionAction="#{MetaCRMControle.executarAutocompleteConsultaEvento}"
                                    minChars="1" rowClasses="5"
                                    status="statusHora"
                                    nothingLabel="Nenhum Evento encontrado !"
                                    var="eventos"
                                    reRender="mensagem">
                                <a4j:support event="onselect"
                                             action="#{MetaCRMControle.selecionarEventoIndicacao}"/>
                                <h:column>
                                    <h:outputText value="#{eventos.descricao}"/>
                                </h:column>
                            </rich:suggestionbox>
                        </h:panelGroup>

                        <h:panelGroup layout="block"
                                      style="padding-left: 20px; padding-right: 20px; padding-bottom: 20px">
                            <h:inputTextarea id="obsIndicado" rows="3"
                                             style="width: 100%; background: white;"
                                             value="#{MetaCRMControle.indicacaoVO.observacao}"/>
                        </h:panelGroup>

                        <h:panelGrid columns="3" width="100%">
                            <h:panelGroup
                                    style="padding-top: 0px; padding-right: 20px; padding-bottom: 20px;  float: right"
                                    layout="block">
                                <a4j:commandLink id="cancelarIndicacao" styleClass="pure-button"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 value="Cancelar"
                                                 action="#{MetaCRMControle.apresentarIndicacao}"
                                                 reRender="panelGridRight"/>
                                <rich:spacer width="13px"/>
                                <a4j:commandLink id="gravarFazerNovaIndicacao"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 styleClass="pure-button"
                                                 value="Salvar e fazer nova indicação"
                                                 action="#{MetaCRMControle.gravarIndicacaoTela}"
                                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                                 reRender="mdlMensagemGenerica, panelGridRight"/>
                                <rich:spacer width="13px"/>
                                <a4j:commandLink id="gravarFecharPanelIndicacao"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 styleClass="pure-button pure-button-primary"
                                                 value="Salvar"
                                                 action="#{MetaCRMControle.gravarIndicacaoFecharPanel}"
                                                 oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                                 reRender="mdlMensagemGenerica, panelGridRight"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>


                    <%---------------------------------------- REAGENDAMENTO ----------------------------------------------------------%>
                    <h:panelGroup id="panelReagendamentoSeta" layout="block" styleClass="triangleCSS"
                                  style="float: left; margin-left: 7%;"
                                  rendered="#{MetaCRMControle.mostrarPanelReagendamento}"/>
                    <h:panelGrid id="panelReagendamento" columns="1" width="100%"
                                 style="text-align: center; background: #e6e6e6"
                                 rendered="#{MetaCRMControle.mostrarPanelReagendamento}">

                        <h:panelGroup layout="block"
                                      style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px; padding-right: 20px">
                            <h:inputTextarea id="observacaoReagendamento"
                                             style="width: 100%" rows="3"
                                             value="#{MetaCRMControle.historicoContatoVO.observacao}"/>
                        </h:panelGroup>

                        <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                            <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                                          layout="block">
                                <h:outputText
                                        style="float: left; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                        value="Tipo de Reagendamento"/>
                                <rich:spacer width="10px" style="float: left"/>
                                <h:selectOneRadio id="tipoReagendamento" onblur="blurinput(this);"
                                                  onfocus="focusinput(this);"
                                                  style="float: left; background: #e6e6e6; color: #333333; font-size: 14px;"
                                                  value="#{MetaCRMControle.agendaVO.tipoAgendamento}">
                                    <f:selectItems
                                            value="#{MetaCRMControle.listaSelectItemTipoAgendamentoReagendamento}"/>
                                    <a4j:support event="onclick" reRender="panelReagendamento"
                                                 oncomplete="adicionarPlaceHolderCRM();"/>
                                </h:selectOneRadio>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                                     rendered="#{MetaCRMControle.apresentarInputTextAulaExperimental}">
                            <h:panelGroup
                                    style="float: left; padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                                    layout="block">
                                <h:outputText
                                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                        value="Modalidade"/>
                                <rich:spacer width="10px"/>
                                <h:selectOneMenu id="modalidadeAgendaReagendamento" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 style="background: #F6F6F6; color: #333333; padding: 3px;"
                                                 value="#{MetaCRMControle.agendaVO.modalidade.codigo}">
                                    <f:selectItems value="#{MetaCRMControle.listaModalidade}"/>
                                    <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModalidade}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>


                        <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                            <h:panelGroup
                                    style="float: left; padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                                    layout="block">
                                <h:outputText
                                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                        value="Reagendar para"/>
                                <rich:spacer width="10px"/>

                                <rich:calendar id="dataReagendamento"
                                               value="#{MetaCRMControle.agendaVO.dataAgendamento}"
                                               inputSize="10"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <rich:spacer style="width: 10px"/>
                                <h:inputText id="horaMinutoReagendamento"
                                             onkeypress="return dois_pontos(this);"
                                             size="10" maxlength="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{MetaCRMControle.agendaVO.horaMinuto}"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                            <h:panelGroup style="padding: 5px" layout="block">
                                <a4j:commandLink
                                        onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                        id="gravarReagendamento"
                                        styleClass="pure-button pure-button-primary"
                                        value="Reagendar"
                                        action="#{MetaCRMControle.reagendarAgendamento}"
                                        oncomplete="#{MetaCRMControle.modalMensagemGenerica};adicionarPlaceHolderCRM();"
                                        reRender="mdlMensagemGenerica, panelGridRight"/>
                                <rich:spacer width="10px"/>
                                <a4j:commandLink
                                        onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                        id="cancelarReagendamento"
                                        styleClass="pure-button pure-button-secundary"
                                        value="Cancelar"
                                        action="#{MetaCRMControle.apresentarPanelReagendamento}"
                                        reRender="panelGridRight"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                 query="mask('99/99/9999')"/>

                    <%---------------------------------------- TELEFONES ----------------------------------------------------------%>
                    <h:panelGrid id="tabelaTelefones" columns="1" width="100%"
                                 rendered="#{MetaCRMControle.apresentarTelefoneCliente}">

                        <h:dataTable id="resTelefone" width="100%" headerClass="subordinado"
                                     rowClasses="linhaImpar, linhaPar"
                                     style="text-align: left; align-content: center;"
                                     value="#{MetaCRMControle.historicoContatoVO.listaTelefoneClientePorTipoContato}"
                                     rendered="#{MetaCRMControle.apresentarTelefoneCliente}" var="telefone">

                            <h:column rendered="#{MetaCRMControle.historicoContatoVO.tipoContato == 'CS'}">
                                <h:selectBooleanCheckbox id="telSelecionado" style="margin: 10%;"
                                                         value="#{telefone.selecionado}">
                                    <a4j:support event="onchange"
                                                 action="#{MetaCRMControle.selecionarTelefoneSMS}"
                                                 reRender="tabelaTelefones"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <h:outputText
                                        style="font-size: 13px; font-weight: bold; font-family: arial, helvetica, sans-serif"
                                        value="#{telefone.ddi} #{telefone.numero}"/>
                            </h:column>

                            <h:column>
                                <h:outputText style="font-size: 13px; font-family: arial, helvetica, sans-serif;"
                                              value="#{telefone.tipoTelefone_Apresentar}"/>
                            </h:column>

                            <h:column>
                                <h:outputText
                                        style="font-size: 13px; font-family: arial, helvetica, sans-serif; color: #999"
                                        value="#{telefone.descricao}"/>
                            </h:column>
                            <h:column>
                                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <a4j:commandLink status="none" reRender="resTelefone, tipoOperacao"
                                                     oncomplete="#{MetaCRMControle.msgAlert}"
                                                     rendered="#{telefone.numeroCelular}"
                                                     title="Clique aqui para abrir o Whatsapp Web já no contato selecionado"
                                                     actionListener="#{MetaCRMControle.abrirWppListener}"
                                                     style="float: inherit; margin-left: 20px; color: #00C350">

                                        <f:attribute name="ddiWpp" value="#{telefone.ddi}"/>
                                        <f:attribute name="foneWpp" value="#{telefone.numeroSemMascara}"/>
                                        <f:attribute name="usarNonoDigitoWApp" value="#{telefone.usarNonoDigitoWApp}"/>
                                        <h:graphicImage style="width: 20px" url="./imagens/whatsapp-logo.png"/>
                                    </a4j:commandLink>
                                </c:if>

                                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                                    <a4j:commandLink status="none" reRender="resTelefone, tipoOperacao"
                                                     oncomplete="#{MetaCRMControle.msgAlert}"
                                                     rendered="#{telefone.tipoTelefone == 'CE'}"
                                                     title="Clique aqui para abrir o Whatsapp Web já no contato selecionado"
                                                     actionListener="#{MetaCRMControle.abrirWppListener}">

                                        <f:attribute name="ddiWpp" value="#{telefone.ddi}"/>
                                        <f:attribute name="foneWpp" value="#{telefone.numeroSemMascara}"/>
                                        <f:attribute name="usarNonoDigitoWApp" value="#{telefone.usarNonoDigitoWApp}"/>
                                        <h:graphicImage style="width: 20px" url="./imagens/whatsapp-logo.png"/>
                                    </a4j:commandLink>
                                </c:if>



                            </h:column>

                            <h:column>
                                <h:outputText style="padding-right: 5px;" value="#{msg_aplic.prt_usar_nono_digito_wapp}"
                                              rendered="#{telefone.tipoTelefone == 'CE'}"/>
                                <h:selectBooleanCheckbox value="#{telefone.usarNonoDigitoWApp}" rendered="#{telefone.tipoTelefone == 'CE'}"/>
                            </h:column>

                        </h:dataTable>

                        <a4j:commandLink rendered="#{not MetaCRMControle.apresentarSMS}"
                                         id="linkCadastroCartaoOnline"
                                         styleClass="pure-button pure-button-small"
                                         value="Link para cadastrar cartão online"
                                         action="#{MetaCRMControle.selecionarLinkCadastroCartaoOnline}"
                                         reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>
                    </h:panelGrid>

                    <%---------------------------------------- EMAILS ----------------------------------------------------------%>
                    <h:panelGrid id="tabelaEmails" columns="1" width="100%"
                                 rendered="#{MetaCRMControle.apresentarEmail}">

                        <h:dataTable id="resultadoConsultaListaEmail" width="100%" headerClass="subordinado"
                                     rowClasses="linhaImpar, linhaPar"
                                     style="text-align: left;"
                                     value="#{MetaCRMControle.listaEmail}"
                                     rendered="#{MetaCRMControle.clienteTemEmail}" var="email">

                            <h:column>
                                <h:selectBooleanCheckbox id="selecionarEmail" style="margin: 10%;"
                                                         value="#{email.escolhido}"/>
                            </h:column>

                            <h:column>
                                <h:outputText
                                        style="font-size: 13px; font-weight: bold; font-family: arial, helvetica, sans-serif"
                                        value="#{email.email}"/>
                            </h:column>

                        </h:dataTable>

                        <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block"
                                      rendered="#{!MetaCRMControle.clienteTemEmail}">
                            <h:outputText
                                    style="text-align: center; color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                                    value="Cliente não possui e-mail cadastrado."/>
                        </h:panelGroup>
                    </h:panelGrid>


                    <%---------------------------------------- APP ----------------------------------------------------------%>
                    <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block"
                                  rendered="#{MetaCRMControle.apresentarAPP and !MetaCRMControle.temUsuarioMovel}">
                        <h:outputText
                                style="text-align: center; color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                                value="Esse cliente não faz uso do aplicativo."/>
                    </h:panelGroup>

                    <h:panelGrid id="panelMensagemApp" columns="1" width="100%"
                                 rendered="#{MetaCRMControle.apresentarAPP and MetaCRMControle.temUsuarioMovel}">

                        <h:panelGroup layout="block" style="padding-top: 15px;">
                            <h:selectOneMenu id="tipoMensagemApp" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             style="background: #F6F6F6; font-size: 14px; float: left; width: 60%"
                                             value="#{MetaCRMControle.tipoMensagemApp}">
                                <f:selectItems value="#{MetaCRMControle.listaSelectTiposPerguntas}"/>
                                <a4j:support event="onchange" action="#{MetaCRMControle.alterarTipoPerguntaAPP}"
                                             oncomplete="adicionarPlaceHolderCRM()" reRender="panelGridRight"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="padding-top: 10px">
                            <h:inputText id="tituloMensagemAPP" maxlength="50"
                                         style="background: #F6F6F6; width: 60%"
                                         value="#{MetaCRMControle.malaDiretaVO.titulo}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="padding-top: 10px"
                                      rendered="#{MetaCRMControle.tipoMensagemApp == 2}">
                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             value="Link para cadastrar cartão online"
                                             action="#{MetaCRMControle.selecionarLinkCadastroCartaoOnline}"
                                             reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>
                            <rich:spacer width="13px"/>

                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="Usar Modelo de Mensagem"
                                             action="#{MetaCRMControle.apresentarModeloMensagem}"
                                             oncomplete="adicionarPlaceHolderCRM();"
                                             reRender="panelGridRight"/>
                        </h:panelGroup>

                        <%--USAR MODELO DE MENSAGEM APP--%>
                        <h:panelGrid columns="2" width="100%"
                                     rendered="#{MetaCRMControle.mostrarModeloDeMensagem}">
                            <h:panelGroup
                                    style="padding-top: 5px; padding-bottom: 10px"
                                    layout="block">
                                <h:selectOneMenu id="modeloMensagemAPP" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 style="background: #F6F6F6; color: #333333; padding: 3px;"
                                                 value="#{MetaCRMControle.malaDiretaVO.modeloMensagem.codigo}">
                                    <f:selectItems value="#{MetaCRMControle.listaModeloMensagem}"/>
                                    <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModeloMensagem}"
                                                 reRender="mensagemAPP" oncomplete="adicionarPlaceHolderCRM();"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGroup layout="block" style="padding-top: 10px;">
                            <h:inputTextarea id="mensagemAPP" onkeypress="quantidadeCaracteres()"
                                             onkeyup="quantidadeCaracteres();"
                                             onchange="quantidadeCaracteres();"
                                             style="width: 100%" rows="3"
                                             value="#{MetaCRMControle.historicoContatoVO.observacao}"/>
                        </h:panelGroup>

                        <h:panelGroup style="text-align: center;" layout="block">
                            <h:inputText disabled="true" size="3" title="Caracteres restantes"
                                         style="text-align: center; color: #000000" id="tamanhoMensagemApp"/>
                        </h:panelGroup>


                        <%--RESPOSTA 01--%>
                        <h:panelGroup layout="block" style="padding-top: 10px;" rendered="#{MetaCRMControle.botoesApp}">
                            <h:outputText style="padding-right: 10px;" value="Resposta 01"/>
                            <h:inputText maxlength="7"
                                         style="text-transform: uppercase; background: #F6F6F6; width: 50%"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         value="#{MetaCRMControle.campos.campo1}"/>
                        </h:panelGroup>

                        <%--RESPOSTA 02--%>
                        <h:panelGroup layout="block" style="padding-top: 10px;" rendered="#{MetaCRMControle.botoesApp}">
                            <h:outputText style="padding-right: 10px; padding-top: 1%; float: left"
                                          value="Resposta 02"/>
                            <h:inputText maxlength="7"
                                         style="text-transform: uppercase; background: #F6F6F6; width: 50%; float: left"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         value="#{MetaCRMControle.campos.campo2}"/>
                            <span onclick="mostrarProximo(2);"
                                  style="cursor: pointer;display: block; padding-top: 1%;"
                                  class="btnOp2 btnsOptAPP">
                                            <i class="fa-icon-plus" style="color: #555; padding-top: 1%;"></i>
                                        </span>
                        </h:panelGroup>

                        <%--RESPOSTA 03--%>
                        <h:panelGroup layout="block" style="padding-top: 10px;" rendered="#{MetaCRMControle.botoesApp}">
                            <h:outputText styleClass="labelOp3" value="Resposta 03"
                                          style="display: none; float: left; padding-right: 10px; padding-top: 1%"/>
                            <h:inputText maxlength="7"
                                         style="display: none; text-transform: uppercase; background: #F6F6F6; width: 50%; float: left"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="labelOp3"
                                         value="#{MetaCRMControle.campos.campo3}"/>
                            <div class="btnOp3 btnsOptAPP">
                                            <span onclick="sumirItem(3);"
                                                  style="cursor: pointer; padding-top: 1%;">
                                                <i class="fa-icon-remove" style="color: #555; padding-top: 1%;"></i>
                                            </span>
                            </div>
                        </h:panelGroup>

                        <%--BOTAO ENVIAR APP--%>
                        <h:panelGroup style="width: 100%; padding-top: 10px; padding-bottom: 10px;  float: left"
                                      layout="block">
                            <a4j:commandLink styleClass="pure-button pure-button-primary"
                                             value="Enviar"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             action="#{MetaCRMControle.gravarEnviandoContatoAPP}"
                                             oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                             reRender="mdlMensagemGenerica, panelGridRight"/>

                            <a4j:commandLink id="usarComoModeloAPP"
                                             rendered="#{MetaCRMControle.tipoMensagemApp == 2 && MetaCRMControle.codigoModeloMensagemCriado == 0}"
                                             styleClass="pure-button pure-button-small"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             style="float: right"
                                             value="Utilizar como Modelo de Mensagem"
                                             action="#{MetaCRMControle.confirmarCriarModeloMensagemAPP}"
                                             oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                             reRender="mdlMensagemGenerica, panelGridRight"/>
                        </h:panelGroup>

                    </h:panelGrid>


                    <%---------------------------------------- ENVIAR EMAIL ----------------------------------------------------------%>
                    <h:panelGrid id="panelEnviarEmail" columns="1" width="100%"
                                 rendered="#{MetaCRMControle.apresentarEmail && MetaCRMControle.clienteTemEmail}">

                        <h:panelGroup layout="block" style="padding-top: 10px; padding-bottom: 5px">
                            <h:inputText id="remetenteEmail" maxlength="50"
                                         style="background: #F6F6F6; width: 60%" disabled="true"
                                         value="REMETENTE - #{MetaCRMControle.malaDiretaVO.remetente.nome}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" style="padding-bottom: 5px">
                            <h:inputText id="tituloEmail" maxlength="100"
                                         style="background: #F6F6F6; width: 60%"
                                         value="#{MetaCRMControle.malaDiretaVO.titulo}"
                                         title="#{msg.msg_tip_tituloMail}#{MetaCRMControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}"/>

                        </h:panelGroup>

                        <h:panelGroup layout="block" style="padding-bottom: 5px">
                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             value="Link para cadastrar cartão online"
                                             action="#{MetaCRMControle.selecionarLinkCadastroCartaoOnline}"
                                             reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>
                            <rich:spacer width="13px"/>

                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="Usar Modelo de Mensagem"
                                             action="#{MetaCRMControle.apresentarModeloMensagem}"
                                             oncomplete="adicionarPlaceHolderCRM();"
                                             reRender="panelGridRight"/>
                            <rich:spacer width="13px"/>

                            <a4j:commandLink id="tagEmail"
                                             styleClass="pure-button pure-button-small"
                                             value="Adicionar Tag Empresa"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             oncomplete="Richfaces.showModalPanel('panelEmail');adicionarPlaceHolderCRM();"
                                             reRender="formMarcadorEmail"/>

                        </h:panelGroup>

                        <%--USAR MODELO DE MENSAGEM EMAIL--%>
                        <h:panelGrid columns="2" width="100%"
                                     rendered="#{MetaCRMControle.mostrarModeloDeMensagem}">
                            <h:panelGroup
                                    style="padding-top: 5px; padding-bottom: 10px"
                                    layout="block">

                                <h:selectOneMenu id="modeloMensagemEmail" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 style="background: #F6F6F6; color: #333333; padding: 3px;"
                                                 value="#{MetaCRMControle.malaDiretaVO.modeloMensagem.codigo}">
                                    <f:selectItems value="#{MetaCRMControle.listaModeloMensagem}"/>
                                    <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModeloMensagem}"
                                                 reRender="mensagemEmail" oncomplete="adicionarPlaceHolderCRM();"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>


                        <h:panelGroup layout="block">
                            <rich:editor id="mensagemEmail"
                                         configuration="editorpropriedades" viewMode="visual" theme="advanced"
                                         height="400"
                                         value="#{MetaCRMControle.malaDiretaVO.mensagem}">
                                <f:param name="width" value="100%"/>
                            </rich:editor>
                        </h:panelGroup>

                        <h:panelGroup style="padding-top: 10px; padding-bottom: 10px;  float: left; width: 100%"
                                      layout="block">
                            <a4j:commandLink styleClass="pure-button pure-button-primary"
                                             id="gravarEnviarEmail"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="Enviar Email"
                                             action="#{MetaCRMControle.gravarEnviandoEmail}"
                                             oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                             reRender="mdlMensagemGenerica, panelGridRight"/>
                            <a4j:commandLink id="usarComoModeloEmail"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             rendered="#{MetaCRMControle.codigoModeloMensagemCriado == 0}"
                                             styleClass="pure-button pure-button-small"
                                             style="float: right"
                                             value="Utilizar como Modelo de Mensagem"
                                             action="#{MetaCRMControle.confirmarCriarModeloMensagemEmail}"
                                             oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                             reRender="mdlMensagemGenerica, panelGridRight"/>
                        </h:panelGroup>

                    </h:panelGrid>


                    <%---------------------------------------- ENVIAR SMS ----------------------------------------------------------%>
                    <h:panelGroup style="text-align: center; padding: 20px; background: #e6e6e6;" layout="block"
                                  rendered="#{MetaCRMControle.apresentarSMS && MetaCRMControle.empresaSemTokem}">
                        <h:outputText
                                style="text-align: center; color: red; font-size: 16px;font-family: arial, helvetica, sans-serif; font-weight: bold;"
                                value="Sua empresa não possui o pacote de SMS."/>
                    </h:panelGroup>
                    <h:panelGrid id="panelSMS" columns="1" width="100%"
                                 rendered="#{MetaCRMControle.apresentarSMS && !MetaCRMControle.empresaSemTokem}">

                        <h:panelGroup layout="block" style="padding-top: 10px">
                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             value="Link para cadastrar cartão online"
                                             action="#{MetaCRMControle.selecionarLinkCadastroCartaoOnline}"
                                             reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>
                            <rich:spacer width="13px"/>

                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             value="Usar Modelo de Mensagem"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             action="#{MetaCRMControle.apresentarModeloMensagem}" ajaxSingle="true"
                                             reRender="panelGridRight"
                                             oncomplete="adicionarPlaceHolderCRM();"/>

                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             style="float: right"
                                             value="Tag Primeiro Nome"
                                             action="#{MetaCRMControle.incluirTagPNomeSMS}"
                                             reRender="mensagemSMS"
                                             oncomplete="adicionarPlaceHolderCRM();"/>

                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             style="float: right; margin-right: 10px"
                                             value="Tag Nome"
                                             action="#{MetaCRMControle.incluirTagNomeSMS}"
                                             reRender="mensagemSMS"
                                             oncomplete="adicionarPlaceHolderCRM();"/>


                        </h:panelGroup>

                        <%--USAR MODELO DE MENSAGEM SMS--%>
                        <h:panelGrid columns="2" width="100%"
                                     rendered="#{MetaCRMControle.mostrarModeloDeMensagem}">
                            <h:panelGroup
                                    style="padding-top: 5px; padding-bottom: 10px"
                                    layout="block">

                                <h:selectOneMenu id="modeloMensagemSMS" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 style="background: #F6F6F6; color: #333333; padding: 3px;"
                                                 value="#{MetaCRMControle.malaDiretaVO.modeloMensagem.codigo}">
                                    <f:selectItems value="#{MetaCRMControle.listaModeloMensagem}"/>
                                    <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModeloMensagem}"
                                                 reRender="mensagemSMS" oncomplete="adicionarPlaceHolderCRM();"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGroup layout="block" style="padding-top: 10px">
                            <h:inputTextarea id="mensagemSMS" onkeypress="somaCRM(this.value);"
                                             onkeyup="somaCRM(this.value);"
                                             style="width: 100%" cols="100" rows="3"
                                             value="#{MetaCRMControle.historicoContatoVO.observacao}"/>

                            <rich:toolTip for="mensagemSMS" followMouse="true"
                                          rendered="#{MetaCRMControle.toolTipSMS and !MetaCRMControle.apresentarAPP}"
                                          direction="top-right"
                                          style="width:300px; height:#{MetaCRMControle.tamanhoToolTipSMS}; "
                                          showDelay="200">
                                <h:outputText styleClass="tituloCampos" escape="false"
                                              value="#{MetaCRMControle.termosFiscalizados}"/>
                            </rich:toolTip>
                        </h:panelGroup>

                        <h:panelGroup style="text-align: center;" layout="block">
                            <c:if test="${MetaCRMControle.apresentarSMS}">
                                <h:inputText disabled="true" size="3" title="Caracteres restantes"
                                             style="text-align: center; color: #000000" id="tamanhoMensagemSMS"/>
                            </c:if>
                        </h:panelGroup>

                        <h:panelGroup style="width: 100%; padding-top: 10px; padding-bottom: 10px;  float: left"
                                      layout="block">
                            <a4j:commandLink styleClass="pure-button pure-button-primary"
                                             id="gravarEnviandoSMS"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="Enviar SMS"
                                             action="#{MetaCRMControle.gravarEnviandoSMS}"
                                             oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                             reRender="mdlMensagemGenerica, panelGridRight"/>

                            <a4j:commandLink id="usarComoModeloSMS"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             rendered="#{MetaCRMControle.codigoModeloMensagemCriado == 0}"
                                             styleClass="pure-button pure-button-small"
                                             style="float: right"
                                             value="Utilizar como Modelo de Mensagem"
                                             action="#{MetaCRMControle.confirmarCriarModeloMensagemSMS}"
                                             oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                             reRender="mdlMensagemGenerica, panelGridRight"/>
                        </h:panelGroup>

                    </h:panelGrid>

                </h:panelGroup>

                <h:panelGroup id="panelOperacoes" style="margin: 10px" layout="block"
                              rendered="#{MetaCRMControle.apresentarContatoPessoalTelefonico}">

                    <h:inputTextarea id="observacaoHistorico" onkeypress="somaApp(this.value);"
                                     onkeyup="somaApp(this.value);"
                                     rendered="#{!MetaCRMControle.mostrarPanelReagendamento}"
                                     style="width: 100%" rows="6"
                                     value="#{MetaCRMControle.historicoContatoVO.observacao}"/>

                    <%--BOTÕES--%>
                    <h:panelGroup layout="block" style="text-align: right; margin-top: 10px"
                                  rendered="#{!MetaCRMControle.mostrarPanelReagendamento}">


                        <%--SIMPLES REGISTRO--%>
                        <a4j:commandLink styleClass="pure-button" style="margin-left: 10px"
                                         id="btnSimplesRegistro"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         value="Simples Registro"
                                         action="#{MetaCRMControle.confirmarSimplesRegistro}"
                                         oncomplete="#{MetaCRMControle.mensagemNotificar}"
                                         reRender="mdlMensagemGenerica, panelGridRight"/>
                        <%--OBJEÇÃO--%>
                        <a4j:commandLink styleClass="pure-button" style="margin-left: 10px"
                                         id="btnObjecao"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         value="Objeção"
                                         action="#{MetaCRMControle.apresentarObjecao}"
                                         reRender="panelGridRight"/>

                        <%--AGENDAR--%>
                        <a4j:commandLink styleClass="pure-button pure-button-primary" style="margin-left: 10px"
                                         id="btnAgendar"
                                         onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                         value="Agendar" action="#{MetaCRMControle.apresentarAgendar}"
                                         reRender="panelGridRight" oncomplete="adicionarPlaceHolderCRM();"/>

                    </h:panelGroup>

                    <%--LISTA DE OBJEÇÕES--%>
                    <h:panelGroup layout="block" styleClass="triangleCSS" style="float: left; margin-left: 20%;"
                                  rendered="#{MetaCRMControle.mostrarPanelObjecao}"/>
                    <h:panelGrid id="panelObjecao" columns="1" width="100%"
                                 style="text-align: center; background: #e6e6e6"
                                 rendered="#{MetaCRMControle.mostrarPanelObjecao}">

                        <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                            <h:panelGroup
                                    style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                                    layout="block">
                                <h:outputText
                                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                        value="Tipo de Objeção"/>
                            </h:panelGroup>
                            <h:panelGroup
                                    style="padding-top: 10px; padding-bottom: 10px"
                                    layout="block">
                                <h:selectOneMenu id="objecao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 style="background: #F6F6F6; color: #333333; font-size: 14px; width: 90%;"
                                                 value="#{MetaCRMControle.historicoContatoVO.objecaoVO.codigo}">
                                    <f:selectItems value="#{MetaCRMControle.listaObjecao}"/>
                                    <a4j:support event="onchange" action="#{MetaCRMControle.alterarObjecao}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                            <h:panelGroup style="padding: 5px" layout="block">
                                <a4j:commandLink
                                        id="concluirObjecao"
                                        onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                        styleClass="pure-button pure-button-primary"
                                        value="Concluir"
                                        action="#{MetaCRMControle.gravarObjecao}"
                                        oncomplete="#{MetaCRMControle.modalMensagemGenerica}"
                                        reRender="mdlMensagemGenerica, panelGridRight"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>

                    <%--AGENDAR--%>
                    <h:panelGroup id="panelAgendarSeta" layout="block" styleClass="triangleCSS"
                                  style="float: left; margin-left: 5%;"
                                  rendered="#{MetaCRMControle.mostrarPanelAgendar}"/>
                    <h:panelGrid id="panelAgendar" columns="1" width="100%"
                                 style="text-align: center; background: #e6e6e6"
                                 rendered="#{MetaCRMControle.mostrarPanelAgendar}">

                        <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                            <h:panelGroup
                                    style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                                    layout="block">
                                <h:outputText
                                        style="float: left; font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                        value="Tipo de Agendamento"/>
                                <rich:spacer width="10px" style="float: left"/>
                                <h:selectOneRadio id="agendamento" onblur="blurinput(this);"
                                                  onfocus="focusinput(this);"
                                                  style="float: left; background: #e6e6e6; color: #333333; font-size: 14px;"
                                                  value="#{MetaCRMControle.agendaVO.tipoAgendamento}">
                                    <f:selectItems value="#{MetaCRMControle.listaSelectItemTipoAgendamentoAgenda}"/>
                                    <a4j:support event="onclick" reRender="panelAgendar"
                                                 oncomplete="adicionarPlaceHolderCRM();"/>
                                </h:selectOneRadio>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid id="selecionarConsultor" columns="2" width="100%" style="background: #e6e6e6;" rendered="#{LoginControle.permissaoAcessoMenuVO.selecionarColaboradorMetas}"
                                     styleClass="tabFormSubordinada">
                            <h:panelGroup style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px" layout="block">
                                <h:outputText style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold" value="Colaboradores"/>

                                <rich:spacer width="10px"/>

                                <h:inputText  id="nomeConsultor"
                                              style="width: 320px; font-size: 12px !important;"
                                              styleClass="campos"
                                              size="50"
                                              maxlength="50"
                                              onfocus="toggleSuggestions ( #{rich:component('suggestionConsultor')} )"
                                              value="#{MetaCRMControle.nomeUsuario}"/>

                                <rich:suggestionbox for="nomeConsultor"
                                                    width="320"
                                                    fetchValue="#{result.nome}"
                                                    status="statusInComponent"
                                                    style="background: #F6F6F6; color: #333333; padding: 3px;"
                                                    suggestionAction="#{MetaCRMControle.executarAutocompleteCol}"
                                                    minChars="1"
                                                    rowClasses="linhaImpar, linhaPar"
                                                    ajaxSingle="false"
                                                    var="result"
                                                    id="suggestionConsultor">
                                    <a4j:support event="onselect"
                                                 reRender="nomeConsultor, selecionarConsultor"
                                                 focus="telasCRM"
                                                 action="#{MetaCRMControle.selecionarUsuario}"/>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Nome" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText value="#{result.nome}"/>
                                    </h:column>
                                </rich:suggestionbox>
                                <script>
                                    function toggleSuggestions(suggestionBox) {
                                        if (suggestionBox.active)
                                            suggestionBox.hide();
                                        else
                                            suggestionBox.callSuggestion(true);
                                    }
                                </script>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;"
                                     rendered="#{MetaCRMControle.apresentarInputTextAulaExperimental}">
                            <h:panelGroup
                                    style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                                    layout="block">
                                <h:outputText
                                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                        value="Modalidade"/>
                                <rich:spacer width="10px"/>

                                <h:selectOneMenu onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 style="background: #F6F6F6; color: #333333; padding: 3px;"
                                                 value="#{MetaCRMControle.agendaVO.modalidade.codigo}">
                                    <f:selectItems value="#{MetaCRMControle.listaModalidade}"/>
                                    <a4j:support event="onchange" action="#{MetaCRMControle.selecionarModalidade}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>


                        <h:panelGrid columns="2" width="100%" style="background: #e6e6e6;">
                            <h:panelGroup
                                    style="padding-left: 20px; padding-top: 10px; padding-bottom: 10px"
                                    layout="block">
                                <h:outputText
                                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold"
                                        value="Agendar para"/>
                                <rich:spacer width="10px"/>
                                <rich:calendar id="dataAgendamento"
                                               value="#{MetaCRMControle.agendaVO.dataAgendamento}"
                                               inputSize="10"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <rich:spacer style="width: 10px"/>
                                <h:inputText id="horaMinutoAgendamento"
                                             onkeypress="return dois_pontos(this);"
                                             size="10" maxlength="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{MetaCRMControle.agendaVO.horaMinuto}"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" style="text-align: center; background: #e6e6e6">
                            <h:panelGroup style="padding: 5px" layout="block">
                                <a4j:commandLink
                                        onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                        id="concluirAgenda"
                                        styleClass="pure-button pure-button-primary"
                                        value="Concluir"
                                        action="#{MetaCRMControle.gravarAgenda}"
                                        oncomplete="#{MetaCRMControle.modalMensagemGenerica};adicionarPlaceHolderCRM();"
                                        reRender="mdlMensagemGenerica, panelGridRight"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>

                <%-- ABAS HISTÓRICO DE CONTATOS E HISTORICO DE OBJEÇÕES --%>

                <h:panelGroup style="border: 1px solid #ccc; margin: 10px; " layout="block">
                    <h:panelGroup id="panelGroupRichPanel" layout="block">
                        <rich:tabPanel id="richPanel" width="100%" tabClass="aba" switchType="ajax"
                                       headerAlignment="left"
                                       selectedTab="abaHistContato">
                            <rich:tab id="abaHistContato" label="Histórico de Contatos">
                                <rich:dataTable id="tableHistContato" width="100%" headerClass="subordinado"
                                                rowClasses="linhaImpar, linhaPar"
                                                value="#{MetaCRMControle.listaHistoricoContatoCliente}"
                                                var="contatos">

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-size: 13px; font-weight: bold; color: #888888"
                                                          value="#{msg_aplic.prt_Agenda_dataHora}"/>
                                        </f:facet>
                                        <h:outputText value="#{contatos.dia_Apresentar}"/>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-size: 13px; font-weight: bold; color: #888888"
                                                          value="#{msg_aplic.prt_Agenda_fase}"/>
                                        </f:facet>
                                        <h:outputText value="#{contatos.fase_Apresentar}"/>
                                        <h:outputText rendered="#{contatos.contatoAvulso}" value=" - avulso"/>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-size: 13px; font-weight: bold; color: #888888"
                                                          value="#{msg_aplic.prt_Agenda_resultado}"/>
                                        </f:facet>
                                        <h:outputText rendered="#{contatos.objecaoVO.codigo != 0}"
                                                      style="color: red" value="#{contatos.resultado}"/>
                                        <h:outputText rendered="#{contatos.objecaoVO.codigo == 0}"
                                                      value="#{contatos.resultado}"/>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-size: 13px; font-weight: bold; color: #888888"
                                                          value="#{msg_aplic.prt_Agenda_usuario}"/>
                                        </f:facet>
                                        <h:outputText
                                                value="#{contatos.responsavelCadastro.nome}"/>
                                    </rich:column>

                                    <rich:column>
                                        <a4j:commandLink oncomplete="show_hide('.item#{contatos.codigo}')" styleClass="sumiricon item#{contatos.codigo}" status="false" style="color: #888888">
                                            <i class="fa-icon-chevron-down"></i>
                                            <i class="fa-icon-chevron-up"></i>
                                        </a4j:commandLink>
                                    </rich:column>

                                    <rich:column style="padding: 0px;" colspan="5" width="100%" breakBefore="true">
                                        <h:panelGroup styleClass="sumir item#{contatos.codigo}" layout="block"  style="padding: 10px">
                                            <h:outputText styleClass="textareaResize"
                                                          value="#{contatos.observacao}"
                                                          escape="false"/>

                                            <h:outputText rendered="#{contatos.app}"
                                                          value="<br/>Resposta: #{contatos.resposta}"
                                                          escape="false"/>
                                            <a4j:commandLink rendered="#{contatos.malaDiretaVO.codigo != 0}"
                                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                             value="Abrir Mailing: #{contatos.malaDiretaVO.codigo}"
                                                             action="#{MetaCRMControle.consultarMailing}"
                                                             oncomplete="#{MetaCRMControle.onComplete}"/>
                                        </h:panelGroup>
                                    </rich:column>

                                </rich:dataTable>

                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" style="margin-top: 10px"
                                             rendered="#{MetaCRMControle.listaHistoricoContatoClientePaginada.count > 0}">
                                    <%-- rendered="#{MetaCRMControle.mostrarTodoHistoricoContato.count > 0}">--%>
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="center" valign="middle">
                                                <h:panelGroup layout="block"
                                                              styleClass="paginador-container">
                                                    <h:panelGroup styleClass="pull-left"
                                                                  layout="block">
                                                        <h:outputText
                                                                styleClass="texto-size-12 texto-cor-cinza"
                                                                value=" Total de #{MetaCRMControle.listaHistoricoContatoClientePaginada.count} itens"></h:outputText>
                                                    </h:panelGroup>


                                                    <h:panelGroup layout="block"
                                                                  style="align-items: center">
                                                        <a4j:commandLink
                                                                styleClass="linkPadrao texto-cor-azul texto-size-14-real" oncomplete="atualiza()"
                                                                reRender="panelGroupRichPanel"
                                                                actionListener="#{MetaCRMControle.primeiraPagina}">
                                                            <i class="fa-icon-double-angle-left"
                                                               id="primPaginaHistorico"></i>
                                                            <f:attribute name="tipo"
                                                                         value="LISTA_CONTATO"/>
                                                        </a4j:commandLink>

                                                        <a4j:commandLink
                                                                styleClass="linkPadrao texto-cor-azul texto-size-14-real" oncomplete="atualiza()"
                                                                reRender="panelGroupRichPanel"
                                                                actionListener="#{MetaCRMControle.paginaAnterior}">
                                                            <i class="fa-icon-angle-left" id="pagAntHistorico"></i>
                                                            <f:attribute name="tipo"
                                                                         value="LISTA_CONTATO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText
                                                                styleClass="texto-font texto-cor-cinza texto-size-12-real"
                                                                value="#{msg_aplic.prt_msg_pagina} #{MetaCRMControle.listaHistoricoContatoClientePaginada.paginaAtualApresentar}"
                                                                rendered="true"/>
                                                        <a4j:commandLink
                                                                styleClass="linkPadrao texto-font texto-cor-azul texto-size-14-real" oncomplete="atualiza()"
                                                                reRender="panelGroupRichPanel"
                                                                actionListener="#{MetaCRMControle.proximaPagina}">
                                                            <i class="fa-icon-angle-right" id="proxPagHistorico"></i>
                                                            <f:attribute name="tipo"
                                                                         value="LISTA_CONTATO"/>
                                                        </a4j:commandLink>

                                                        <a4j:commandLink
                                                                styleClass="linkPadrao texto-cor-azul texto-size-14-real" oncomplete="atualiza()"
                                                                reRender="panelGroupRichPanel"
                                                                actionListener="#{MetaCRMControle.ultimaPagina}">
                                                            <i class="fa-icon-double-angle-right"
                                                               id="ultimaPaginaHistorico"></i>
                                                            <f:attribute name="tipo"
                                                                         value="LISTA_CONTATO"/>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>


                                                    <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                                        <h:panelGroup styleClass="pull-right" layout="block">
                                                            <h:outputText styleClass="texto-size-12-real texto-cor-cinza" style="margin-right: 10px"
                                                                          value="Itens por página "/>
                                                        </h:panelGroup>

                                                        <h:panelGroup styleClass="cb-container pl20" layout="block">
                                                            <h:selectOneMenu style="font-size: 12px"
                                                                             value="#{MetaCRMControle.listaHistoricoContatoClientePaginada.limit}"
                                                                             id="qtdeItensPaginaHistorico">
                                                                <f:selectItem itemValue="#{5}"></f:selectItem>
                                                                <f:selectItem itemValue="#{10}"></f:selectItem>
                                                                <f:selectItem itemValue="#{15}"></f:selectItem>
                                                                <f:selectItem itemValue="#{30}"></f:selectItem>
                                                                <f:selectItem itemValue="#{50}"></f:selectItem>
                                                                <f:selectItem itemValue="#{100}"></f:selectItem>
                                                                <a4j:support event="onchange"
                                                                             actionListener="#{MetaCRMControle.atualizarNumeroItensPagina}"
                                                                             reRender="panelGroupRichPanel">
                                                                    <f:attribute name="tipo" value="LISTA_CONTATO"/>
                                                                </a4j:support>
                                                            </h:selectOneMenu>

                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                </h:panelGroup>

                                            </td>
                                        </tr>
                                    </table>
                                </h:panelGrid>

                            </rich:tab>


                            <rich:tab id="abaHistObjecao" label="Histórico de Objeções">
                                <rich:dataTable id="tableHistObjecoes" width="100%" headerClass="subordinado"
                                                rowClasses="linhaImpar, linhaPar"

                                                value="#{MetaCRMControle.listaHistoricoObjecoesCliente}"
                                                var="objecoes">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                                          value="#{msg_aplic.prt_Agenda_dataHora}"/>
                                        </f:facet>
                                        <h:outputText value="#{objecoes.dia_Apresentar}"/>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                                          value="#{msg_aplic.prt_Agenda_fase}"/>
                                        </f:facet>
                                        <h:outputText value="#{objecoes.fase_Apresentar}"/>
                                        <h:outputText rendered="#{objecoes.contatoAvulso}" value=" - avulso"/>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                                          value="#{msg_aplic.prt_Agenda_resultado}"/>
                                        </f:facet>
                                        <h:outputText rendered="#{objecoes.objecaoVO.codigo != 0}"
                                                      style="color: red" value="#{objecoes.resultado}"/>
                                        <h:outputText rendered="#{objecoes.objecaoVO.codigo == 0}"
                                                      value="#{objecoes.resultado}"/>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                                          value="#{msg_aplic.prt_Agenda_usuario}"/>
                                        </f:facet>
                                        <h:outputText
                                                value="#{objecoes.responsavelCadastro.nome}"/>
                                    </rich:column>

                                    <rich:column>
                                        <a4j:commandLink oncomplete="show_hide('.itemobj#{objecoes.codigo}')" styleClass="sumiricon itemobj#{objecoes.codigo}" status="false" style="color: #888888">
                                            <i class="fa-icon-chevron-down"></i>
                                            <i class="fa-icon-chevron-up"></i>
                                        </a4j:commandLink>
                                    </rich:column>

                                    <rich:column style="padding: 0px" colspan="5" width="100%" breakBefore="true">
                                        <h:panelGroup styleClass="sumir itemobj#{objecoes.codigo}" layout="block" style="padding: 10px">
                                            <h:inputTextarea styleClass="textareaResize"
                                                             value="#{objecoes.observacao_Exportar}"
                                                             readonly="true" rows="5"/>

                                            <h:outputText rendered="#{objecoes.app}"
                                                          value="<br/>Resposta: #{objecoes.resposta}"
                                                          escape="false"/>
                                        </h:panelGroup>
                                    </rich:column>

                                </rich:dataTable>
                            </rich:tab>
                        </rich:tabPanel>


                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <%--MENSAGEM DETALHADA--%>
            <h:panelGrid columns="1" width="100%" rendered="#{not empty MetaCRMControle.mensagemDetalhada}">
                <h:outputText id="msgRealizarContato" styleClass="mensagem"
                              value="#{MetaCRMControle.mensagem}"/>
                <h:outputText id="msgRealizarContatoDet" styleClass="mensagemDetalhada"
                              value="#{MetaCRMControle.mensagemDetalhada}"/>
            </h:panelGrid>

        </h:panelGroup>


        <script type="text/javascript">
            try {
                window.onload = adicionarPlaceHolderCRM();
            } catch (e) {
                console.log(e);
            }

            function atualiza(){
                location.reload();
            }

            function setupEditor(ed) {
                ed.onKeyUp.add(
                    function (ed, evt) {
                        var htmlText = tinyMCE.activeEditor.getContent();
                        var value = htmlText.replace(/<.*?>/g, '');
                        document.getElementById('form:tamanhoMensagemSMS').value = 140 - value.length;
                        if (value.length > 140) {
                            var caracteresRemover = value.length - 140;
                            while (caracteresRemover > 0) {
                                var indexRemove = -1;
                                if (htmlText.endsWith('>')) {
                                    for (var i = htmlText.length - 1; i > 0; i--) {
                                        if (htmlText.charAt(i) === '<' && htmlText.charAt(i - 1) !== '>') {
                                            indexRemove = i - 1;
                                            break;
                                        }
                                    }
                                } else {
                                    indexRemove = htmlText.length - 1;
                                }
                                htmlText = htmlText.substring(0, indexRemove - 1) + htmlText.substring(indexRemove);
                                caracteresRemover--;
                            }
                            tinyMCE.activeEditor.setContent(htmlText);
                            tinyMCE.activeEditor.selection.select(tinyMCE.activeEditor.getBody(), true);
                            tinyMCE.activeEditor.selection.collapse(false);
                            document.getElementById('form:tamanhoMensagemSMS').value = 0;
                        }
                    }
                );
            }

            function somaApp() {
                var limiteAPP = 255;
                var elemento = (document.getElementById('form:mensagemAPP'));
                var tamanhoRestante = document.getElementById('form:tamanhoMensagemApp');

                var mais_um = eval(elemento.value.length - 1);
                mais_um++;

                if (elemento.value.length > limiteAPP) {
                    elemento.value = '';
                    elemento.value = valor_limite;
                } else {
                    valor_limite = elemento.value;
                    tamanhoRestante.value = '';
                    tamanhoRestante.value = (limiteAPP - mais_um);
                }
                elemento.focus();
            }

            function quantidadeCaracteres() {
                var elemento = (document.getElementById('form:mensagemAPP'));
                var tamanhoRestante = document.getElementById('form:tamanhoMensagemApp');
                tamanhoRestante.value = elemento.value.length;
                elemento.focus();
            }

        </script>
    </h:form>
    <%--MODAL TEXTO PADRÃO--%>
    <rich:modalPanel id="panelTextoPadraoNewRealizarContato" domElementAttachment="parent" autosized="true" width="700"
                     height="500">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Texto Padrão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTextoNewRealizarContato"/>
                <rich:componentControl for="panelTextoPadraoNewRealizarContato"
                                       attachTo="hidelinkTextoNewRealizarContato" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formTextoPadraoNewRealizarContato">
            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda">
                <h:outputText value="Texto Padrão:" style="font-weight: bold; font-size: 12px"/>
                <h:selectOneMenu id="listaTextoPadraoNewRealizarContato"
                                 style="background: #F6F6F6; color: #333333; font-size: 14px; float: left"
                                 value="#{MetaCRMControle.textoPadraoVO.codigo}">
                    <f:selectItems value="#{MetaCRMControle.listaTextoPadraoVO}"/>
                    <a4j:support event="onchange" action="#{MetaCRMControle.setarMensagemTextoPadrao}"
                                 reRender="formTextoPadraoNewRealizarContato"/>
                </h:selectOneMenu>

                <h:outputText value="Link(Google Docs):" style="font-weight: bold; font-size: 12px"/>
                <h:outputLink value="#{MetaCRMControle.textoPadraoVO.linkDocs}" target="_blank">
                    <h:outputText value="#{MetaCRMControle.textoPadraoVO.linkDocs}"/>
                </h:outputLink>
            </h:panelGrid>

            <h:panelGrid id="mensagemTextoPadraoNewRealizarContato" columns="1" width="100%"
                         style="border: 1px #000 solid; height: 100%">
                <h:outputText styleClass="mensagemTextoPadraoNewRealizarContato"
                              value="#{MetaCRMControle.textoPadraoVO.mensagemPadrao}" escape="false"/>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <a4j:commandLink id="inserirTextoPadraoNewRealizarContato"
                                 value="Usar Script"
                                 styleClass="pure-button pure-button-small pure-button-primary"
                                 action="#{MetaCRMControle.inserirTextoPadraoNewRealizarContato}"
                                 oncomplete="#{MetaCRMControle.onComplete};"
                                 reRender="panelTextoPadraoNewRealizarContato, form">
                </a4j:commandLink>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <jsp:include page="include_modais_tela_principalCRM.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
</f:view>
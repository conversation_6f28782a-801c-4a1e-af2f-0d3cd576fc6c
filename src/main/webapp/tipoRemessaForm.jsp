<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_TipoRemessa_tituloForm}"/>
    </title>
    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_TipoRemessa_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-tipo-de-remessa/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{TipoRemessaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_TipoRemessa_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{TipoRemessaControle.tipoRemessaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_TipoRemessa_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao" size="50" maxlength="50" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{TipoRemessaControle.tipoRemessaVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_TipoRemessa_tipoRetorno}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="tipoRetorno" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{TipoRemessaControle.tipoRemessaVO.tipoRetorno.codigo}" >
                            <f:selectItems  value="#{TipoRemessaControle.listaSelectItemTipoRetorno}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_tipoRetorno" action="#{TipoRemessaControle.montarListaSelectItemTipoRetorno}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:tipoRetorno"/>
                        <h:message for="tipoRetorno" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_TipoRemessa_arquivoLayoutRemessa}" />
                    <h:panelGroup>
                        <h:selectOneMenu id="arquivoLayoutRemessa" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" value="#{TipoRemessaControle.tipoRemessaVO.arquivoLayoutRemessa}">
                            <f:selectItems value="#{TipoRemessaControle.listaSelectItemArquivoLayoutRemessa}"/>
                        </h:selectOneMenu>
                        <h:message for="arquivoLayoutRemessa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_TipoRemessa_tipoRemessa}" />
                    <h:panelGroup>
                        <h:inputText  id="tipoRemessa" size="50" maxlength="50" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{TipoRemessaControle.tipoRemessaVO.tipoRemessa}" />
                        <h:message for="tipoRemessa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{TipoRemessaControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{TipoRemessaControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{TipoRemessaControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{TipoRemessaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{TipoRemessaControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <h:commandButton id="salvar" action="#{TipoRemessaControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{TipoRemessaControle.msgAlert}" action="#{TipoRemessaControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <h:commandButton id="consultar" immediate="true" action="#{TipoRemessaControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink action="#{TipoRemessaControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
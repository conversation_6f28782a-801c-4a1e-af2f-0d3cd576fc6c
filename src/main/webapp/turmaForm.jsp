<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>
<link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<!--<script src="bootstrap/jquery.js" type="text/javascript"></script>-->
<!--<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>-->
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Turma_tituloForm}"/>
    </title>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <rich:modalPanel id="panelModalidade"  autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_semUCP.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta de Modalidade"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panelModalidade" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalidade" oncomplete="setFocus(this.form,'formModalidade:valorConsultaModalidade');" ajaxSubmit="true">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" id="consultaModalidade" value="#{TurmaControle.campoConsultaModalidade}">
                        <f:selectItems value="#{TurmaControle.tipoConsultaComboModalidade}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultaModalidade"  size="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.valorConsultaModalidade}"/>
                    <a4j:commandButton  id="btnConsultar"
                                        reRender="formModalidade:mensagemConsultaModalidade, formModalidade:resultadoConsultaModalidade, formModalidade:scResultadoModalidade"
                                        action="#{TurmaControle.consultarModalidade}" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                        image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaModalidade" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{TurmaControle.listaConsultaModalidade}" rows="5" var="modalidade">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Modalidade_nome}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{TurmaControle.selecionarModalidade}" focus="nomeModalidade"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelModalidade')" value="#{modalidade.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Modalidade_valorMensal}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{TurmaControle.selecionarModalidade}" focus="nomeModalidade"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelModalidade')" >
                                <h:outputText value="#{modalidade.valorMensal}" >
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{TurmaControle.selecionarModalidade}" focus="nomeModalidade"
                                           reRender="form" oncomplete="Richfaces.hideModalPanel('panelModalidade')"
                                           value="#{msg_bt.btn_selecionar}" image="./imagens/botaoEditar.png"
                                           alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModalidade:resultadoConsultaModalidade" maxPages="10"
                                   id="scResultadoModalidade" />
                <h:panelGrid id="mensagemConsultaModalidade" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{TurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{TurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelColaborador" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_semUCP.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta de Professor"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink1"/>
                <rich:componentControl for="panelColaborador" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formColaborador" ajaxSubmit="true">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" id="consultaColaborador" value="#{TurmaControle.campoConsultaColaborador}">
                        <f:selectItems value="#{TurmaControle.tipoConsultaComboColaborador}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultaColaborador" size="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.valorConsultaColaborador}"/>
                    <a4j:commandButton  id="btnConsultar" reRender="formColaborador:mensagemConsultaColaborador, formColaborador:resultadoConsultaColaborador, formColaborador:scResultadoColaborador"
                                        action="#{TurmaControle.consultarColaborador}" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                        image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaColaborador" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{TurmaControle.listaConsultaColaborador}" rows="5" var="colaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText  value="#{msg_aplic.prt_Pessoa_nome}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{TurmaControle.selecionarColaborador}" focus="nomeColaborador"
                                             reRender="form" oncomplete="Richfaces.hideModalPanel('panelColaborador')" value="#{colaborador.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{TurmaControle.selecionarColaborador}" focus="nomeColaborador"
                                           reRender="form" oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                           value="#{msg_bt.btn_selecionar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formColaborador:resultadoConsultaColaborador" maxPages="10"
                                   id="scResultadoColaborador" />
                <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{TurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{TurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelAmbiente" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_semUCP.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Ambiente"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink3"/>
                <rich:componentControl for="panelAmbiente" attachTo="hidelink3" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formAmbiente" ajaxSubmit="true">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">
                    <h:outputText value="#{msg_aplic.prt_Ambiente_codigo}"/>
                    <h:panelGroup>
                        <h:inputText id="codigoAmbiente" size="10" maxlength="10" readonly="true"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="camposSomenteLeitura" value="#{TurmaControle.ambiente.codigo}"/>
                        <h:message for="codigoAmbiente" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_Ambiente_descricao}"/>
                    <h:panelGroup>
                        <h:inputText id="nomeAmbiente" size="45" maxlength="45" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{TurmaControle.ambiente.descricao}"/>
                        <h:message for="nomeAmbiente" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_Ambiente_capacidade}"/>
                    <h:panelGroup>
                        <h:inputText id="capacidade" size="2" maxlength="3" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{TurmaControle.ambiente.capacidade}"/>
                        <h:message for="capacidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_Ambiente_tipoAmbiente}"/>
                    <h:panelGroup>
                        <h:selectOneMenu
                                onfocus="focusinput(this);" styleClass="form" id="tiposAmbiente"
                                value="#{TurmaControle.ambiente.tipoModulo}">
                            <f:selectItem itemValue="" itemLabel="--Selecione--"/>
                            <f:selectItems value="#{AmbienteControle.listaAmbienteModulo}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText value="*Situação"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="situacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{TurmaControle.ambiente.situacaoAmbiente}">
                            <f:selectItems value="#{AmbienteControle.listaSituacaoAmbiente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="salvarAmbiente" reRender="formHorarioTurma"
                                           action="#{TurmaControle.gravarAmbiente}"
                                           oncomplete="Richfaces.hideModalPanel('panelAmbiente')"
                                           value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png"
                                           alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelNivelTurma" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_semUCP.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Nivel da Turma"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink4"/>
                <rich:componentControl for="panelNivelTurma" attachTo="hidelink4" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formNivelTurma" ajaxSubmit="true">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText   value="#{msg_aplic.prt_NivelTurma_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigoNivelTurma"  size="10" maxlength="10" readonly="true"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura"  value="#{TurmaControle.nivelTurma.codigo}" />
                        <h:message for="codigoNivelTurma" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_NivelTurma_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="nomeNivelTurma"  size="45" maxlength="45"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{TurmaControle.nivelTurma.descricao}" />
                        <h:message for="nomeNivelTurma" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="salvarNivelTurma" reRender="formHorarioTurma" action="#{TurmaControle.gravarNivelTurma}"
                                           oncomplete="Richfaces.hideModalPanel('panelNivelTurma')" value="#{msg_bt.btn_gravar}"
                                           image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelHorarioTurma" autosized="true" shadowOpacity="true" width="700" height="350">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Horário da Turma"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink2"/>
                <rich:componentControl for="panelHorarioTurma" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formHorarioTurma" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_diaSemana}" />
                    <h:panelGroup>
                        <h:selectOneRadio id="diaSemana" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                          rendered="#{TurmaControle.horarioTurmaVOTemporaria.botaoEditar}"
                                          value="#{TurmaControle.horarioTurmaVOTemporaria.diaSemana}">
                            <f:selectItems value="#{TurmaControle.listaSelectItemDiaSemana}"/>
                        </h:selectOneRadio>
                        <h:selectManyCheckbox id="radioGroupDiaSemana" onblur="blurinput(this);" onfocus="focusinput(this);"
                                              rendered="#{!TurmaControle.horarioTurmaVOTemporaria.botaoEditar}"
                                              styleClass="form" value="#{TurmaControle.diasSelecionados}">
                            <f:selectItems value="#{TurmaControle.listaSelectItemDiaSemana}"/>
                        </h:selectManyCheckbox>
                    </h:panelGroup>
                    <h:outputText rendered="#{TurmaControle.horarioTurmaVOTemporaria.botaoEditar}" styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_horaInicial}" />
                    <h:panelGroup rendered="#{TurmaControle.horarioTurmaVOTemporaria.botaoEditar}">
                        <h:inputText id="horarioTurma_horaInicial"
                                      onkeypress="return mascaraTodos(this.form, 'formHorarioTurma:horarioTurma_horaInicial', '99:99', event);"
                                      size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                      styleClass="form" value="#{TurmaControle.horarioTurmaVOTemporaria.horaInicial}" />
                        <h:message for="horarioTurma_horaInicial" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText rendered="#{TurmaControle.horarioTurmaVOTemporaria.botaoEditar}" styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_horaFinal}" />
                    <h:panelGroup rendered="#{TurmaControle.horarioTurmaVOTemporaria.botaoEditar}">
                        <h:inputText  id="horarioTurma_horaFinal"
                                      onkeypress="return mascaraTodos(this.form, 'formHorarioTurma:horarioTurma_horaFinal', '99:99', event);"
                                      size="5" maxlength="5" onblur="blurinput(this);"
                                      onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.horarioTurmaVOTemporaria.horaFinal}" />
                        <h:message for="horarioTurma_horaFinal" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_toleranciaEntradaMinutos}" />
                    <h:panelGroup>
                        <h:inputText  id="toleranciaEntradaMinutos" size="4" maxlength="4"
                                      title="Informe a quantidade de minutos que o aluno poderá entrar na academia antes do início da aula."
                                      onkeypress="return mascaraTodos(this.form, 'formHorarioTurma:toleranciaEntradaMinutos', '99', event);"
                                      styleClass="campos" value="#{TurmaControle.horarioTurmaVOTemporaria.toleranciaEntradaMinutos}" />

                        <h:outputText  styleClass="tituloCampos" value="(minutos)" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_professor}" />
                    <h:panelGroup>
                        <%--   <h:inputText   readonly="true"  id="nomeColaborador" size="40"  maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form"  value="#{TurmaControle.horarioTurmaVO.professor.pessoa.nome}" />
                                    <a4j:commandButton id="consultaDadosColaborador" focus="valorConsultaColaborador" alt="Consulta de Professores" reRender="formColaborador" oncomplete="Richfaces.showModalPanel('panelColaborador'),  setFocus(formColaborador,'formColaborador:valorConsultaColaborador');" image="./imagens/informacao.gif" />   --%>
                        <h:selectOneMenu  id="HorarioTurma_professor" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.horarioTurmaVOTemporaria.professor.codigo}" >
                            <f:selectItems  value="#{TurmaControle.listaSelectItemProfessor}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_HorarioTurma_professor" action="#{TurmaControle.montarListaSelectItemProfessor}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="formHorarioTurma:HorarioTurma_professor"/>
                        <a4j:commandButton id="cadastroProfessor" action="#{ColaboradorControle.inicializarTipoColaborador}"
                                           oncomplete="abrirPopup('professor.jsp', 'Colaborador', 680, 595);" image="./images/icon_add.gif" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_ambiente}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="HorarioTurma_ambiente" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.horarioTurmaVOTemporaria.ambiente.codigo}" >
                            <f:selectItems  value="#{TurmaControle.listaSelectItemAmbiente}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_HorarioTurma_ambiente" action="#{TurmaControle.montarListaSelectItemAmbiente}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="formHorarioTurma:HorarioTurma_ambiente"/>
                        <a4j:commandButton id="cadastroAmbiente" focus="nomeAmbiente" alt="Cadastrar Ambiente"
                                           reRender="formAmbiente" oncomplete="Richfaces.showModalPanel('panelAmbiente')" image="./images/icon_add.gif" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_nivelTurma}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="HorarioTurma_nivelTurma" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.horarioTurmaVOTemporaria.nivelTurma.codigo}" >
                            <f:selectItems  value="#{TurmaControle.listaSelectItemNivelTurma}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_HorarioTurma_nivelTurma" action="#{TurmaControle.montarListaSelectItemNivelTurma}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="formHorarioTurma:HorarioTurma_nivelTurma"/>
                        <a4j:commandButton id="cadastroNivelTurma" focus="nomeNivelTurma" alt="Cadastrar Nivel da Turma"
                                           reRender="formNivelTurma" oncomplete="Richfaces.showModalPanel('panelNivelTurma')" image="./images/icon_add.gif" />
                    </h:panelGroup>
                    <%-- <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_situacao}" />
                     <h:selectOneMenu  id="HorarioTurma_situacao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.horarioTurmaVO.situacao}" >
                         <f:selectItems  value="#{TurmaControle.listaSelectItemSituacaoHorarioTurma}" />
                     </h:selectOneMenu>--%>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_nrMaximoAluno}" />
                    <h:inputText  id="horarioTurma_nrMaximoAluno" onkeypress="return Tecla(event);" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.horarioTurmaVOTemporaria.nrMaximoAluno}" />

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_liberadoMarcacaoApp}" />
                    <h:selectBooleanCheckbox id="liberadoMarcacaoApp" styleClass="campos " value="#{TurmaControle.horarioTurmaVOTemporaria.liberadoMarcacaoApp}"/>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_ativo}" title="#{msg_aplic.prt_HorarioTurma_ativo_title}"/>
                    <h:selectBooleanCheckbox id="turmaAtiva" styleClass="campos " value="#{TurmaControle.horarioTurmaVOTemporaria.ativo}"/>

                </h:panelGrid>

                <h:panelGrid rendered="#{!TurmaControle.horarioTurmaVOTemporaria.botaoEditar}" columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_horaInicial}"/>
                    <h:panelGroup>
                        <h:inputText id="hT_horaInicial"
                                     onkeypress="return mascaraTodos(this.form, 'formHorarioTurma:hT_horaInicial', '99:99', event);"
                                     size="5" maxlength="5" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.ht_horaInicio}"/>
                        <h:message for="hT_horaInicial" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_HorarioTurma_horaFinal}"/>

                    <h:panelGroup>
                        <h:inputText id="hT_horaFinal"
                                     onkeypress="return mascaraTodos(this.form, 'formHorarioTurma:hT_horaFinal', '99:99', event);"
                                     size="5" maxlength="5" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.ht_horaFim}"/>
                        <h:message for="hT_horaFinal" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>

                <a4j:commandButton id="addHorario" action="#{TurmaControle.incluirHorarioNaListaParaAdicionar}"
                                   rendered="#{!TurmaControle.horarioTurmaVOTemporaria.botaoEditar}"
                                   reRender="formHorarioTurma, panelDataTableHorarios, dataTableHorarios, panelMensagem"
                                   focus="enderecoColaborador" value="#{msg_bt.btn_adicionar}"
                                   image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                <h:panelGrid id="panelDataTableHorarios" rendered="#{!TurmaControle.horarioTurmaVOTemporaria.botaoEditar}" columns="1" width="100%"
                             styleClass="tabFormSubordinada">
                    <h:dataTable id="dataTableHorarios" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                 value="#{TurmaControle.horariosIncluidos}" var="horario">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Hora Inicial"/>
                            </f:facet>
                            <h:outputText id="horaInicial_table" value="#{horario.horaInicial}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Hora Final"/>
                            </f:facet>
                            <h:outputText id="horaFinal_table" value="#{horario.horaFinal}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Opções"/>
                            </f:facet>
                            <a4j:commandButton id="removerHorario" reRender="dataTableHorarios, panelMensagemErro"
                                               value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                               action="#{TurmaControle.removerHorarioDaListaParaAdicionar}" styleClass="botoes"/>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>

                <a4j:commandButton id="btnGravarHorarioTurma" action="#{TurmaControle.gravarHorario}"
                                   oncomplete="#{TurmaControle.fecharModalHorarioTurma}"
                                   reRender="formHorarioTurma, panelHorarioTurma, panelMensagem ,msgTurmaPanel,msgTurmaPanelDet, formConflitos, horarioTurmaVO"
                                   focus="adicionar" value="Gravar" image="./imagens/botaoGravar.png" accesskey="5" styleClass="botoes"/>
                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgTurmaPanel" styleClass="mensagem"  value="#{TurmaControle.mensagem}"/>
                        <h:outputText id="msgTurmaPanelDet" styleClass="mensagemDetalhada" value="#{TurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="conflitos" autosized="true" shadowOpacity="true" width="800" height="350">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Conflitos de Turmas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkConflitos"/>
                <rich:componentControl for="panelHorarioTurma" attachTo="hidelinkConflitos" operation="show" event="onclick"/>
                <rich:componentControl for="conflitos" attachTo="hidelinkConflitos" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConflitos" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:outputText  styleClass="tituloCampos" value="Os horários que deseja cadastrar estão conflitando com:" />
                <h:dataTable id="dataTableConflitos" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                             value="#{TurmaControle.horariosConflitantes}" var="turmaConfli">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Código Turma"/>
                        </f:facet>
                        <h:outputText id="coluna1" value="#{turmaConfli.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Identificador Turma"/>
                        </f:facet>
                        <h:outputText value="#{turmaConfli.identificador}"/><br/>
                        <h:outputText id="infoAulaCheia" styleClass="tituloCamposNegritoMenor tooltipster" rendered="#{turmaConfli.aulaColetiva}" value="(AULA CHEIA)"/>
                        <rich:toolTip for="infoAulaCheia" followMouse="true">
                            <h:outputText styleClass="tituloCampos" escape="false" value="Esta aula é referente ao Aula Cheia" />
                        </rich:toolTip>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição Turma"/>
                        </f:facet>
                        <h:outputText value="#{turmaConfli.descricao}"/>
                    </h:column>
                    <h:column>
                        <h:dataTable id="tblHorarioConfl" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                     value="#{turmaConfli.horarioTurmaVOs}" var="htConfl">
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Código"/>
                                </f:facet>
                                <h:outputText value="#{htConfl.codigo}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Dia da Semana"/>
                                </f:facet>
                                <h:outputText value="#{htConfl.diaSemana_Apresentar}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Professor"/>
                                </f:facet>
                                <h:outputText value="#{htConfl.professor.pessoa.nome}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Hora Inicial"/>
                                </f:facet>
                                <h:outputText value="#{htConfl.horaInicial}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Hora Final"/>
                                </f:facet>
                                <h:outputText value="#{htConfl.horaFinal}"/>
                            </h:column>
                        </h:dataTable>
                    </h:column>
                </h:dataTable>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Turma_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-turma/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{TurmaControle.liberarBackingBeanMemoria}"
                           id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1"  width="100%">
                <rich:tabPanel width="100%" switchType="ajax" selectedTab="#{TurmaControle.abaSelecionada}">
                    <rich:tab id="dadosTurma" name="abaDadosTurma" label="Dados Turma" action="#{TurmaControle.alterarAba}">
                        <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                            <h:outputText rendered="#{TurmaControle.turmaVO.aulaColetiva}"  styleClass="tituloCampos" value="" />
                            <h:outputText rendered="#{TurmaControle.turmaVO.aulaColetiva}"  styleClass="tituloCampos" value="AULA COLETIVA" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{TurmaControle.turmaVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_descricao}" />
                            <h:panelGroup>
                                <h:inputText  id="descricao"  size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.turmaVO.descricao}" />
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_identificador}" />
                            <h:panelGroup>
                                <h:inputText  id="identificador"  size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.turmaVO.identificador}" />
                                <h:message for="identificador" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText rendered="#{TurmaControle.turmaVO.usuarioVO.administrador}" styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_empresa}" />
                            <h:panelGroup rendered="#{TurmaControle.turmaVO.usuarioVO.administrador}">
                                <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{TurmaControle.turmaVO.empresa.codigo}" >
                                    <a4j:support event="onchange" reRender="form" focus="empresa" action="#{TurmaControle.montarListaSelectItemModalidade}"/>
                                    <f:selectItems  value="#{TurmaControle.listaSelectItemEmpresa}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_empresa" action="#{TurmaControle.montarListaSelectItemEmpresa}"
                                                   image="imagens/atualizar.png" ajaxSingle="true" reRender="form:empresa"/>
                                <h:message for="empresa" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText rendered="#{TurmaControle.turmaVO.desenhaModalidade}" styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_modalidade}" />
                            <h:panelGroup rendered="#{TurmaControle.turmaVO.desenhaModalidade}">
                                <h:outputText rendered="#{!TurmaControle.permiteAlterarModalidade}" styleClass="tituloCampos" value="#{TurmaControle.turmaVO.modalidade.nome}" />
                                <%--  <h:inputText   readonly="true"  id="nomeModalidade" size="40"  maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form"  value="#{TurmaControle.turmaVO.modalidade.nome}" />
                              <a4j:commandButton id="consultaDadosModalidade" alt="Consulta Modalidade" reRender="formModalidade" oncomplete="Richfaces.showModalPanel('panelModalidade'), setFocus(formModalidade,'formModalidade:valorConsultaModalidade');" image="./imagens/informacao.gif" /> --%>
                                <h:selectOneMenu rendered="#{TurmaControle.permiteAlterarModalidade}" id="modalidade" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.turmaVO.modalidade.codigo}" >
                                    <f:selectItems  value="#{TurmaControle.listaSelectItemModalidade}" />
                                </h:selectOneMenu>
                                <a4j:commandLink rendered="#{TurmaControle.permiteAlterarModalidade}" id="atualizar_modalidade"
                                                   action="#{TurmaControle.montarListaSelectItemModalidade}"
                                                   styleClass="fa-icon-refresh" style="padding:5px; font-size:14px;color:#29AAE2"
                                                   ajaxSingle="true" reRender="form:modalidade"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_dataInicialVigencia}" />
                            <h:panelGroup>
                                <rich:calendar id="dataInicialVigencia"
                                               value="#{TurmaControle.turmaVO.dataInicialVigencia}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" />
                                <h:message for="dataInicialVigencia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_dataFinalVigencia}" />
                            <h:panelGroup>
                                <rich:calendar id="dataFinalVigencia"
                                               value="#{TurmaControle.turmaVO.dataFinalVigencia}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" />
                                <h:message for="dataFinalVigencia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_idadeMinima}" />
                            <h:panelGroup>
                                <h:inputText  id="idadeMinima" onkeypress="return Tecla(event);"  size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.turmaVO.idadeMinima}" />
                                <h:message for="idadeMinima" styleClass="mensagemDetalhada"/>
                                <rich:spacer width="5px"/>
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_idadeMeses}" />
                                <h:inputText  id="idadeMinimaMeses" onkeypress="return Tecla(event);"  size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.turmaVO.idadeMinimaMeses}" />
                                <h:message for="idadeMinimaMeses" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_idadeMaxima}" />

                            <h:panelGroup>
                                <h:inputText  id="idadeMaxima"  onkeypress="return Tecla(event);" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.turmaVO.idadeMaxima}" />
                                <h:message for="idadeMaxima" styleClass="mensagemDetalhada"/>
                                <rich:spacer width="5px"/>
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_idadeMeses}" />
                                <h:inputText  id="idadeMaximaMeses"  onkeypress="return Tecla(event);" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.turmaVO.idadeMaximaMeses}" />
                                <h:message for="idadeMaximaMeses" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_bloquearMatriculasAcimaLimite}" />
                            <h:selectBooleanCheckbox id="bloquearMatriculasAcimaLimite" styleClass="campos " value="#{TurmaControle.turmaVO.bloquearMatriculasAcimaLimite}"/>

                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_bloquearReposicoesAcimaLimite}" />
                            <h:selectBooleanCheckbox id="bloquearReposicoesAcimaLimite" styleClass="campos " value="#{TurmaControle.turmaVO.bloquearReposicaoAcimaLimite}"/>
                            
                            <!--Comentado para retirar o tooltipster que estava causando conflito com a máscara das datas-->
                            <%--<h:outputText  styleClass="tituloCampos tooltipster"
                                title="<p>Com esta opção <b><u>desabilitada</u></b>, <u>não será possível desmarcar aulas
                                que são reposições</u> nos seguintes cenários:<br>
                                &emsp;&emsp;1 - No aplicativo utilizado pelo aluno<br>
                                &emsp;&emsp;2 - No ZW, na tela do cliente operada pelo consultor<br>
                                &emsp;&emsp;3 - Na agenda do Aula Cheia.<br></p>
                                <p>Nesses casos se o consultor ou o aluno tentar desmarcar, 
                                será exibido uma mensagem informando que não é possível desmarcar aulas de reposição.</p>"
                                value="#{msg_aplic.prt_Turma_permitirDesmarcarReposicoes}" />--%>
                            <h:outputText  styleClass="tituloCampos"
                                title=
"Com esta opção desabilitada, não será possível desmarcar aulas que são 
reposições nos seguintes cenários:
        1 - No aplicativo utilizado pelo aluno
        2 - No ZW, na tela do cliente operada pelo consultor
        3 - Na agenda do Aula Cheia.
Nesses casos se o consultor ou o aluno tentar desmarcar, será exibido uma 
mensagem informando que não é possível desmarcar aulas de reposição."
                                value="#{msg_aplic.prt_Turma_permitirDesmarcarReposicoes}" />
                            <h:selectBooleanCheckbox id="permitirDesmarcarReposicoes" styleClass="campos " value="#{TurmaControle.turmaVO.permitirDesmarcarReposicoes}"/>
                            
                            <h:panelGroup>
                                <h:outputText id="permitirAulaExperimental_msg" styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Turma_permitirAulaExperimental}:"/>
                                <rich:toolTip for="permitirAulaExperimental_msg" followMouse="true" style="text-align: left; font-weight: normal; color: #000; line-height: normal; width: 300px">
                                    <h:outputText value="#{msg_aplic.msg_turma_aluno_exp}"/>
                                </rich:toolTip>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:selectBooleanCheckbox id="permitirAulaExperimental" styleClass="campos" value="#{TurmaControle.turmaVO.permitirAulaExperimental}"/>
                                <rich:toolTip for="permitirAulaExperimental" followMouse="true" style="width: 300px">
                                    <h:outputText value="#{msg_aplic.msg_turma_aluno_exp}"/>
                                </rich:toolTip>
                            </h:panelGroup>

                            <h:outputText id="permiteAlunoOutraEmpresa_label" styleClass="tituloCampos" value="Permite marcar aula experimental para alunos de outras unidades:"/>
                            <h:selectBooleanCheckbox id="permiteAlunoOutraEmpresa" styleClass="campos" value="#{TurmaControle.turmaVO.permiteAlunoOutraEmpresa}"/>

                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Turma_monitorar}" />
                            <h:selectBooleanCheckbox id="turmaMonitorada" styleClass="campos " value="#{TurmaControle.turmaVO.monitorada}"/>


                            <h:outputText rendered="#{TurmaControle.integracaoSpivi}" styleClass="tooltipster tituloCampos"
                                          title="Os horários de início e fim e também o dia da semana em que a aula acontece, cadastrados para cada aula desta turma, deverão ser idênticos aos cadastrados na Spivi. Desta forma, quando clicar em 'Escolher bike SPIVI' na aula desejada pelo Aula Cheia, o sistema irá encontrar a aula e trará as bikes para a escolha. Caso não seja encontrado a aula, o sistema irá sugerir outras aulas Spivis que estão acontecendo no mesmo dia."
                                          value="Integração SPIVI:"/>
                            <h:selectBooleanCheckbox rendered="#{TurmaControle.integracaoSpivi}"
                                                     title="Os horários de início e fim e também o dia da semana em que a aula acontece, cadastrados para cada aula desta turma, deverão ser idênticos aos cadastrados na Spivi. Desta forma, quando clicar em 'Escolher bike SPIVI' na aula desejada pelo Aula Cheia, o sistema irá encontrar a aula e trará as bikes para a escolha. Caso não seja encontrado a aula, o sistema irá sugerir outras aulas Spivis que estão acontecendo no mesmo dia."
                                                     styleClass="campos tooltipster " value="#{TurmaControle.turmaVO.integracaoSpivi}"/>

                            <h:outputText  styleClass="tituloCampos" value="Mínimo minutos antecedência desmarcar aula:" />
                            <h:inputText  id="minutAntecedDesmarcarAula"
                                          onkeypress="return Tecla(event);"  size="5" maxlength="5" onblur="blurinput(this);"
                                          title="Informe zero para o sistema não validar esta configuração."
                                          onfocus="focusinput(this);" styleClass="form"
                                          value="#{TurmaControle.turmaVO.minutosAntecedenciaDesmarcarAula}" />

                            <h:outputText  styleClass="tituloCampos" value="Antecedência marcar aula:" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="comboTipoAntecMarcarAula"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TurmaControle.turmaVO.tipoAntecedenciaMarcarAula}" >
                                    <a4j:support event="onchange" reRender="pgAnteced, idDescLonga"></a4j:support>
                                    <f:selectItems  value="#{TurmaControle.listaSelectItemTipoAntecedenciaMarcarAula}"/>
                                </h:selectOneMenu>
                                <h:outputText  styleClass="tituloCampos"
                                               style="padding-left: 5px; padding-right: 5px"
                                               id="idDescLonga"
                                               value="#{TurmaControle.turmaVO.tipoAntecedenciaMarcarAulaEnum.descricaoLonga}" />
                                <h:panelGroup id="pgAnteced">
                                    <h:inputText  id="minutAntecedMarcarAula" onkeypress="return Tecla(event);"  size="2" maxlength="5"
                                                  rendered="#{(!(TurmaControle.turmaVO.tipoAntecedenciaMarcarAulaEnum eq 'NAO_VALIDAR'))}"
                                                  title="#{TurmaControle.turmaVO.tipoAntecedenciaMarcarAulaEnum.exemplo}"
                                                  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                                  value="#{TurmaControle.turmaVO.minutosAntecedenciaMarcarAula}" />
                                    <h:outputText  style="padding-left: 5px"
                                                   styleClass="tituloCampos" value="#{TurmaControle.turmaVO.tipoAntecedenciaMarcarAulaEnum.descFinal}" />
                                </h:panelGroup>

                            </h:panelGroup>

                            <h:outputText  styleClass="tituloCampos" value="Níveis de desconto por ocupação:" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="descontoNivelSelect"  onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                  styleClass="form" value="#{TurmaControle.turmaVO.qtdeNivelOcupacao}" >
                                    <a4j:support event="onchange" ajaxSingle="false" reRender="descontoNivel"></a4j:support>
                                    <f:selectItem itemValue="0" itemLabel="SEM DESCONTO" />
                                    <f:selectItem itemValue="1" itemLabel="1 (UM) NÍVEL DE DESCONTO" />
                                    <f:selectItem itemValue="2" itemLabel="2 (DOIS) NÍVEIS DE DESCONTO" />
                                    <f:selectItem itemValue="3" itemLabel="3 (TRÊS) NÍVEIS DE DESCONTO" />
                                    <f:selectItem itemValue="4" itemLabel="4 (QUATRO) NÍVEIS DE DESCONTO" />
                                    <f:selectItem itemValue="5" itemLabel="5 (CINCO) NÍVEIS DE DESCONTO" />
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" id="labelMinutosAposInicioApp"
                                          value="Tolerância em minutos após início para apresentar aula APP:" />
                            <h:inputText id="minutosAposInicioApp"
                                         onkeypress="return Tecla(event);"  size="5" maxlength="5" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{TurmaControle.turmaVO.minutosAposInicioApp}"/>

                            <rich:toolTip for="labelMinutosAposInicioApp"
                                          style="text-align: left"
                                          value="Este campo se refere ao tempo máximo em que a aula continuará sendo apresentada no aplicativo mesmo após ter sido iniciado,
                                                <br/>permitindo que o aluno realize uma marcação/reposição após o início da mesma.
                                                <br/>Ex: início da aula às 10:00; tolerância definida de 10 minutos; aluno consegue marcar/repor até as 10:10.
                                                <br/>Após esse período a aula não será mais exibida no aplicativo."/>

                            <rich:toolTip for="minutosAposInicioApp"
                                          style="text-align: left"
                                          value="Este campo se refere ao tempo máximo em que a aula continuará sendo apresentada no aplicativo mesmo após ter sido iniciado,
                                                <br/>permitindo que o aluno realize uma marcação/reposição após o início da mesma.
                                                <br/>Ex: início da aula às 10:00; tolerância definida de 10 minutos; aluno consegue marcar/repor até as 10:10.
                                                <br/>Após esse período a aula não será mais exibida no aplicativo."/>

                        </h:panelGrid>
                        <h:panelGrid columns="1" id="descontoNivel"
                                     rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                            <a4j:repeat value="#{TurmaControle.turmaVO.nivelDescontos}" var="nivel"   >
                                <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                    <h:outputText style="background-color: #{nivel.color}; width:50%; display: inline-block; " value="De #{nivel.percentualIncial}% à #{nivel.percentualFinal}%"/>
                                    <h:inputText onkeypress="return formatar_percentuallimite(this,'.',',',event,5);"  size="5" maxlength="5" onblur="blurinput(this);"
                                                  title="Informe o percentual de desconto que será aplicado"
                                                  onfocus="focusinput(this);"
                                                  value="#{nivel.percentualDesconto}">
                                        <f:converter converterId="FormatarPercentual" />
                                    </h:inputText>
                                </h:panelGrid>
                            </a4j:repeat>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="dadoshorarioTurma" name="abaDadosHorarioTurma" label="#{msg_aplic.prt_HorarioTurma_tituloForm}" action="#{TurmaControle.alterarAba}">
                        <h:panelGroup layout="block" styleClass="container-botoes">

                            <a4j:commandLink id="adicionar" title="Cadastrar Horário da Turma"
                                               reRender="formHorarioTurma" action="#{TurmaControle.adicionarNovoHorarioTurma}"
                                               oncomplete="Richfaces.showModalPanel('panelHorarioTurma'),  setFocus(formHorarioTurma,'formHorarioTurma:diaSemana');"
                                               value="#{msg_bt.btn_adicionar}"  accesskey="5" styleClass="botoes nvoBt btSec"/>
                        </h:panelGroup>
                        <h:panelGrid columns="1" width="100%" >
                            <%--<a4j:region  ajaxListener="#{TurmaControle.campoOrdenacao}">--%>

                            <rich:dataTable id="horarioTurmaVO" width="100%"  headerClass="subordinado" styleClass="tabFormSubordinada" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaAlinhamento"
                                            value="#{TurmaControle.turmaVO.horarioTurmaVOs}"  var="horarioTurma">
                                <rich:column sortBy="#{horarioTurma.codigo}"  filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_ModeloMensagem_codigo}" />
                                    </f:facet>
                                    <h:outputText  value=" #{horarioTurma.codigo}" />
                                </rich:column>
                                <rich:column id="cl_dsemana"  sortBy="#{horarioTurma.ordenaDiaSemana}" filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_HorarioTurma_diaSemana}" />
                                    </f:facet>
                                    <h:outputText  value=" #{horarioTurma.diaSemana_Apresentar}" />
                                </rich:column>
                                <rich:column  id="cl_hora" sortBy="#{horarioTurma.ordenaHorario}"  filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_HorarioTurma_tituloForm}" />
                                    </f:facet>
                                    <h:outputText  value="#{horarioTurma.horaInicial} às #{horarioTurma.horaFinal}" />
                                </rich:column>

                                <rich:column sortBy="#{horarioTurma.toleranciaEntradaMinutos}"  filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="Tol." />
                                    </f:facet>
                                    <h:outputText  value="#{horarioTurma.toleranciaEntradaMinutos}" />
                                </rich:column>

                                <rich:column id="cl_professor" sortBy="#{horarioTurma.ordenaProfessor}"  filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_HorarioTurma_professor}" />
                                    </f:facet>
                                    <h:outputText  value="#{horarioTurma.professor.pessoa.nome}" />
                                </rich:column>
                                <rich:column id="cl_ambiente" sortBy="#{horarioTurma.ordenaAmbiente}"  filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_HorarioTurma_ambiente}" />
                                    </f:facet>
                                    <h:outputText  value="#{horarioTurma.ambiente.descricao}" />
                                </rich:column>
                                <rich:column sortBy="#{horarioTurma.nivelTurma.descricao}"  filterEvent="onkeyup">
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_HorarioTurma_nivelTurma}" />
                                    </f:facet>
                                    <h:outputText  value="#{horarioTurma.nivelTurma.descricao}" />
                                </rich:column>
<%--                                <rich:column sortBy="#{horarioTurma.situacao_Apresentar}"  filterEvent="onkeyup">--%>
<%--                                    <f:facet name="header">--%>
<%--                                        <h:outputText  value="#{msg_aplic.prt_HorarioTurma_situacao}" />--%>
<%--                                    </f:facet>--%>
<%--                                    <h:outputText  value="#{horarioTurma.situacao_Apresentar}" />--%>
<%--                                </rich:column>--%>
                                <rich:column sortBy="#{horarioTurma.nrMaximoAluno}"  filterEvent="onkeyup" >
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_HorarioTurma_nrMaximoAluno}" />
                                    </f:facet>
                                    <h:outputText  value="#{horarioTurma.nrMaximoAluno}" />
                                </rich:column>
                                <rich:column sortBy="#{horarioTurma.ativo}"  filterEvent="onkeyup" >
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_HorarioTurma_ativo}" />
                                    </f:facet>
                                    <h:outputText  value="#{horarioTurma.ativo? 'Sim' : 'Não'}" />
                                </rich:column>
                                <rich:column >
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                    <h:panelGroup>
                                        <a4j:commandButton id="editarItemVenda" action="#{TurmaControle.editarHorarioTurma}"
                                                           reRender="formHorarioTurma" oncomplete="Richfaces.showModalPanel('panelHorarioTurma'); setFocus(formHorarioTurma,'formHorarioTurma:diaSemana');"
                                                           value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                        <h:outputText value="    "/>
                                        <h:commandButton id="removerItemVenda" immediate="true"
                                                         action="#{TurmaControle.removerHorarioTurma}" value="#{msg_bt.btn_excluir}"
                                                         image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>

                                        <%--<h:outputText value="    "/>--%>

                                        <%--<a4j:commandButton id="clonarHorarioTurma" action="#{TurmaControle.clonarHorarioTurma}"  alt="#{msg.msg_clonar_dados}" reRender="formHorarioTurma" oncomplete="Richfaces.showModalPanel('panelHorarioTurma'),  setFocus(formHorarioTurma,'formHorarioTurma:diaSemana');" value="#{msg_bt.btn_consultar}" image="./imagens/botaoClonar.png" accesskey="7" styleClass="botoes"/>--%>
                                    </h:panelGroup>
                                </rich:column>
                            </rich:dataTable>
                            <%--</a4j:region>--%>
                        </h:panelGrid>
                    </rich:tab>
                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <jsp:include page="includes/include_mensagem_exception.jsp" flush="true" />
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" action="#{TurmaControle.novo}" value="#{msg_bt.btn_novo}"
                                             alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"
                                             reRender="dadosModalidade, mdlAviso, panelMensagem"/>
                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{TurmaControle.gravar}" value="#{msg_bt.btn_gravar}"
                                               alt="#{msg.msg_gravar_dados}"
                                               accesskey="2" styleClass="botoes nvoBt" reRender="dadosModalidade, mdlAviso, panelMensagem"/>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{TurmaControle.msgAlert}" action="#{TurmaControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <%--<a4j:commandButton id="clonar"  action="#{TurmaControle.clonar}" reRender="form" value="#{msg_bt.btn_consultar}" image="./imagens/botaoClonar.png" alt="#{msg.msg_clonar_dados}" accesskey="5" styleClass="botoes"/>--%>

                            <%--<h:outputText value="    "/>--%>

                            <h:commandButton id="consultar" immediate="true" action="#{TurmaControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>
                            <a4j:commandLink id="visualizarLog"
                                             immediate="true"
                                             action="#{TurmaControle.realizarConsultaLogObjetoSelecionado}"
                                             accesskey="5"
                                             style="display: inline-block; padding: 8px 15px;"
                                             styleClass="botoes nvoBt btSec"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script type="text/javascript">
    document.getElementById("form:descricao").focus();
    //carregarTooltipster();
</script>


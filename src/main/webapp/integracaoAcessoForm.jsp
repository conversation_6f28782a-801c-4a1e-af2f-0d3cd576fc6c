<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_LocalAcesso_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_acesso_integracao_grupo}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Acesso_ao_sistema:Integra%C3%A7%C3%A3o_Acesso"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaPar, linhaImpar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText rendered="#{IntegracaoAcessoGrupoEmpresarialControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_empresa}" />
                    <h:panelGroup rendered="#{IntegracaoAcessoGrupoEmpresarialControle.usuarioLogado.administrador}">
                        <h:selectOneMenu id="empresa"  onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="color:black;"
                                         styleClass="form"
                                         value="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.empresaLocal.codigo}" >
                            <f:selectItems  value="#{IntegracaoAcessoGrupoEmpresarialControle.listaEmpresas}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_label_descricao}" />
                    <h:inputText size="40" onfocus="focusinput(this);" styleClass="form" 
                                 value="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.descricao}">
                    </h:inputText> 
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_integracao_url} da Unidade" />
                    <h:inputText size="80" onfocus="focusinput(this);" styleClass="form" 
                                 value="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.urlZillyonWeb}">
                    	<a4j:support event="onblur" reRender="form"
                    	             action="#{IntegracaoAcessoGrupoEmpresarialControle.consultarEmpresasRemotas}"/>
                    </h:inputText> 
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_integracao_chave} da Unidade" />
                    <h:inputText size="80" onfocus="focusinput(this);" styleClass="form" 
                                 value="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.chave}">
                    	<a4j:support event="onblur" reRender="form"
                    	             action="#{IntegracaoAcessoGrupoEmpresarialControle.consultarEmpresasRemotas}"/>
                    </h:inputText>             
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_empresa_remota}" />
                    <h:panelGroup id="comboEmpresasRemotas">
                        <h:selectOneMenu id="empresaRemota"  onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="color:black;"
                                         styleClass="form"
                                         value="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.empresaRemota.codigo}" >
                            <f:selectItems  value="#{IntegracaoAcessoGrupoEmpresarialControle.listaEmpresasRemotas}" />
                            <a4j:support reRender="form" event="onchange"
                            			 action="#{IntegracaoAcessoGrupoEmpresarialControle.montarLocaisAcesso}"/>
                        </h:selectOneMenu>

                        <h:outputText styleClass="tituloCampos" style="margin-left: 12px"
                                      rendered="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.codigoChaveIntegracaoDigitais > 0}"
                                      value="Código da Chave: #{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.codigoChaveIntegracaoDigitais}"/>
                    </h:panelGroup>
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_integracao_localAcesso}" />
                    <h:panelGroup id="comboLocalAcesso">
                        <h:selectOneMenu id="localAcesso"  onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="color:black;"
                                         styleClass="form"
                                         value="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.localAcesso}" >
                            <f:selectItems  value="#{IntegracaoAcessoGrupoEmpresarialControle.listaLocaisAcesso}" />
                            <a4j:support reRender="form" event="onchange"
                            			 action="#{IntegracaoAcessoGrupoEmpresarialControle.montarColetores}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_integracao_coletor}" />
                    <h:panelGroup id="comboColetor">
                        <h:selectOneMenu id="coletor"  onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="color:black;"
                                         styleClass="form"
                                         value="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.coletor}" >
                            <f:selectItems  value="#{IntegracaoAcessoGrupoEmpresarialControle.listaColetores}" />
                            <a4j:support reRender="form" event="onchange"
                            			 action="#{IntegracaoAcessoGrupoEmpresarialControle.selecionarColetor}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_integracao_Terminal}" />
                    <h:panelGroup id="terminalCampo">
                         <h:outputText styleClass="tituloCampos" value="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.terminalApresentar}" />
                    </h:panelGroup>
                   
                                 
                </h:panelGrid>
                
                <h:panelGrid id="painelMensagem" columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">

                        <h:outputText value=" "/>

                    </h:panelGrid>
                    <h:commandButton  rendered="#{IntegracaoAcessoGrupoEmpresarialControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{IntegracaoAcessoGrupoEmpresarialControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" id="mensagem2"  value="#{IntegracaoAcessoGrupoEmpresarialControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada2" value="#{IntegracaoAcessoGrupoEmpresarialControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                
                   <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{IntegracaoAcessoGrupoEmpresarialControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg_bt.btn_novoLocal}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{IntegracaoAcessoGrupoEmpresarialControle.gravar}" reRender="painelMensagem, comboEmpresasRemotas" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{IntegracaoAcessoGrupoEmpresarialControle.msgAlert}" action="#{IntegracaoAcessoGrupoEmpresarialControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{IntegracaoAcessoGrupoEmpresarialControle.integracaoAcesso.podeExcluir}" value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{IntegracaoAcessoGrupoEmpresarialControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}"  alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <a4j:commandLink action="#{IntegracaoAcessoGrupoEmpresarialControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
   
</f:view>

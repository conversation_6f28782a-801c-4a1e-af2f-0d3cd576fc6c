<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c"  uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<jsp:include page="include_head.jsp"  flush="true"/>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<head>

    <link href="./css/otimize.css" rel="stylesheet" type="text/css"/>
    <link href="./css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
    <link href="./css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
    <link href="./css/css_bi_1.5.css" rel="stylesheet" type="text/css"/>
    <link href="./css/ghost_loading.css" rel="stylesheet" type="text/css"/>
    <link href="./css/faqtoid.css" rel="stylesheet" type="text/css"/>

    <script src="script/amcharts_v2.js" type="text/javascript"></script>
    <script src="script/amcharts_serial_v2.js" type="text/javascript"></script>
    <script src="bootstrap/jquery.js" type="text/javascript"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript" src="script/faqtoid_1.1.min.js"></script>
    <script type="text/javascript" src="script/jquery-sortable.min.js"></script>
    <script type="text/javascript" src="script/jquery-ui.min.js"></script>
    <script src="script/jquery.ui.touch-punch.min.js" type="text/javascript"></script>

</head>


<script>
    jQuery.noConflict();
    jQuery( document ).ready(function() {
        organizarSortable();
    });
</script>

<style>
    body{
        overflow-x: hidden;
    }
    .modaldemanda .rich-mpnl-mask-div-opaque.rich-mpnl-mask-div{
        background: transparent !important;
    }

    .modaldemanda .mensagemBICarregando{

    }
    .modaldemanda .spinnerCarregando{
        display: inline-block;
        margin: 0;
    }
    .badgeItem2Icon, .badgeItem3Icon, .badgeItem1Icon{
        font-family: Arial !important;
    }
</style>
<body class="paginaFontResponsiva">
<%
    String m = request.getParameter("m");
    if(m == null){
        m = "web";
    }
    pageContext.setAttribute("modoView",m);
%>
<f:view locale="#{SuperControle.idioma}">
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>

    <h:form id="form" styleClass="tabelasSemHover">
        <h:panelGroup layout="block" styleClass="fundo-block-container" style="overflow: hidden">
            <h:panelGroup layout="block" styleClass="bi-block-container-header tudo ">

                <h:panelGroup layout="block" style="margin-left: 0px" styleClass="container-header-meuBI">

                    <h:outputText styleClass="fa-icon-eye-close" style="font-size:28px;color:#ffffff;"/>
                    <h:outputText style="font-size:16px;color:#ffffff;" value=" BIs que não estarão visíveis" styleClass="bi-font-family"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="container-header-meuBI font-size-Em">

                    <h:outputText styleClass="fa-icon-eye-open"
                                  style="font-size:28px;color:#ffffff;margin-left: 17%;"/>
                    <h:outputText style="font-size:28px;color:#ffffff;" value=" Meus BIs"
                                  styleClass="bi-font-family"/>
                    <h:outputText style="font-size:16px;color:rgba(255, 255, 255, 0.63);padding-left: 16px;"
                                  value="  Vizualize como seus BIs ficarão na sua tela"
                                  styleClass="bi-font-family"/>
                    <a4j:jsFunction name="reorganizarBIs" action="#{BIControle.montarOrdemBI}" reRender="bi-container-bis" oncomplete="organizarSortable();montarTips();"/>

                    <a4j:commandLink title="Cancelar" styleClass="fa-icon-cubes btn-icon-toggle-sortable bi-icon-sortable-handle tooltipster"
                                     oncomplete="cancelarAlteracoesArrayOrdem();reorganizarBIs();history.go(0);"/>

                    <a4j:commandLink action="#{BIControle.gravarOrdemBI}" styleClass="font-size-Em" oncomplete="toggleSortable();reorganizarBIs();montarTips();history.go(0);">
                        <h:outputText title="Gravar Ordem B.I." style="display: none;margin-right: 12px;" styleClass="tooltipster fa-icon-save bi-icon-sortable-handle"/>
                    </a4j:commandLink>

                    <a4j:commandLink action="#{BIControle.voltarParaPadrao}" styleClass="font-size-Em"  onclick="alterarInputHiddenOrdem('0-0,1-0,2-0,3-0,4-0,5-1,6-1,7-1,8-1,9-1');" oncomplete="toggleSortable();reorganizarBIs();carregarBITodos();montarTips();history.go(0);">
                        <h:outputText title="Voltar para padrão" style="margin-right: 12px;"  styleClass="tooltipster fa-icon-undo bi-icon-sortable-handle"/>
                    </a4j:commandLink>

                </h:panelGroup>

            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-block-container-body" style="position: relative;">
                <h:panelGroup layout="block" style="height: 100%;width: 6px;left: 25%;position: absolute;display: inline-block;background-color:#9E9E9E" styleClass="tudo"/>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" style="display: inline-table;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW tudo" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item3" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo" style="display: inline-table">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">

                        <c:if test="${!BIControle.requiscaoMobile and BIControle.carregarMenuWeb and SuperControle.menuZwUi}">
                            <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                        </c:if>
                        <c:if test="${!BIControle.requiscaoMobile and BIControle.carregarMenuWeb and !SuperControle.menuZwUi}">
                            <jsp:include page="menuAcessoRapido.jsp" flush="true"/>
                        </c:if>
                        <!--Não usar div para separar o menu, desalinha no safari-->
                        <h:panelGroup layout="block" styleClass="container-imagem zw_ui container-conteudo-central" style="position:relative; overflow: overlay;">
                            <h:panelGroup layout="block" id="container-filtro-colaborador" >
                                <h:panelGroup layout="block" id="textoCarregando"
                                              styleClass="modaldemanda" style="float: left; margin-top: 25px; margin-left: 15px;">
                                    <h:panelGroup layout="block" rendered="#{!BIControle.biCarregado}"
                                                  styleClass="spinnerCarregando">
                                        <div class="bounce1"></div>
                                        <div class="bounce2"></div>
                                        <div class="bounce3"></div>
                                    </h:panelGroup>
                                    <h:outputText id="mensagemBICarregando"
                                                  rendered="#{!BIControle.biCarregado}"
                                                  style="color:#094771;font-style: italic;padding: 10px;" styleClass="texto-font texto-size-20 texto-cor-cinza mensagemBICarregando"
                                                  value="#{BIControle.rotuloBICarregando}"/>
                                </h:panelGroup>
                                <jsp:include page="includes/include_filtro_colaborador.jsp"  flush="true"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <h:panelGroup layout="block" styleClass="container-filtro-bi">
                                    <span title="Organizar B.I."
                                          class="tooltipster fa-icon-cubes btn-icon-toggle-sortable bi-icon-sortable-handle tudo fqtBtnOrganizarBI step1 step4"
                                          onclick="toggleSortable();preencherArrayOrdemOriginal()"></span>

                                    <a4j:commandLink id="btnAbrirFiltroCol" status="none"
                                                     action="#{BIControle.abrirPanelTodosColab}"
                                                     onclick="abrirFiltroCol();"
                                                     style="color: #29abe2; text-decoration: none; border-radius: 2px;padding-left:17px;padding-right: 17px;min-height: 21px;line-height: 1.8;font-family: Arial"
                                                     value="Todos os Colaboradores"
                                                     styleClass="badgeItem tooltipster bi-btn-filtroCol btn-open-filtro-col texto-size-16 bi-icon-sortable-handle fqtBtnFiltro tudo step1 step2">
                                    </a4j:commandLink>

                                    <h:panelGroup layout="block"
                                                  styleClass="pull-right bi-combo-empresa cb-container tudo"
                                                  rendered="#{BIControle.usuarioFiltro.administrador or BIControle.permiteConsultarTodasEmpresas}">
                                        <h:selectOneMenu value="#{BIControle.empresaFiltro.codigo}" style="height: 40px;">
                                            <f:selectItems value="#{BIControle.listaSelectItemEmpresa}"/>
                                            <a4j:support event="onchange"
                                                         action="#{BIControle.inicializarCarregarDados}"
                                                         oncomplete="carregarBITodos();verificarGrupoColaboradorCheck();montarTips();"
                                                         reRender="containerFiltroColaborador,bi-movimentacao-contrato"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup id="containerDataBaseBI" layout="block"
                                                  style="border-radius: 2px;height:auto;z-index: 1;min-height: 21px;position: relative;margin:0px 15px 1% 0;font-size: 14px !important;"
                                                  styleClass="bi-icon-sortable-handle pull-right dateTimeCustom bi-btn-filtroCol bi-filtro-dataBase tudo step1">
                                        <rich:calendar buttonClass="margin-left-right-7"
                                                       id="dataGeralBI"
                                                       buttonIcon="/imagens_flat/icon-calendar-check.png"
                                                       inputClass="forcarSemBorda"
                                                       inputStyle="height: 1.7em;border: none !important;width: 90px;" inputSize="6"
                                                       datePattern="dd/MM/yyyy" value="#{BIControle.dataBaseFiltro}"
                                                       showWeeksBar="false">
                                            <a4j:support event="onchanged"
                                                         action="#{BIControle.inicializarCarregarDados}"
                                                         reRender="containerDataBaseBI,containerFiltroColaborador,bi-container-bis"
                                                         oncomplete="carregarBITodosDataBase();organizarSortable();verificarGrupoColaboradorCheck();montarTips();"/>
                                        </rich:calendar>
                                        <rich:jQuery
                                                query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                                selector=".btn-toggle-calendar"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup style="width: 100%;" layout="block" id="bi-container-bis" styleClass="bi-container-bis bi-calendar bi-container-transition tudo">

                                <h:panelGroup layout="block" styleClass="container-sortable-bi margin-box">
                                    <h:panelGroup layout="block" style="clear:both;" styleClass="col-sortable col-sortable-1">
                                        <jsp:include page="/includes/include_bi_filtro_movimentacao_contratos.jsp" flush="true" /> <!-- filtro movimentacao contratos --->
                                        <jsp:include page="includes/bi/include_bi_pendencias_clientes.jsp" flush="true"/><!-- Pendências Clientes -->
                                        <jsp:include page="includes/bi/include_bi_indiceconversao.jsp" flush="true"/><!-- Conversão de vendas -->
                                        <jsp:include page="includes/bi/include_bi_indiceconversaosessao.jsp" flush="true"/><!-- Conversão de vendas por Sessão -->
                                        <jsp:include page="includes/bi/include_bi_metasFinanceiras.jsp" flush="true"/><!-- Metas Financeiras de Venda -->
                                        <jsp:include page="includes/bi/include_bi_ticketmedio.jsp" flush="true"/><!-- Ticket Médio de Planos -->
                                        <jsp:include page="includes/bi/include_bi_conviteAulaExperimental.jsp" flush="true"/><!-- ??? -->
                                        <jsp:include page="includes/bi/include_bi_clientesverificados.jsp" flush="true"/><!-- Verificação de Clientes -->
                                        <jsp:include page="includes/bi/include_bi_aulaExperimental.jsp" flush="true"/><!-- Aulas experimentais -->
                                        <jsp:include page="includes/bi/include_bi_ltv.jsp" flush="true"/><!-- LTV -->
                                        <jsp:include page="includes/bi/include_bi_acesso_gympass.jsp" flush="true"/><!-- Acessos Gympass -->
                                    </h:panelGroup>

                                    <h:panelGroup style="vertical-align: top;" styleClass="col-sortable col-sortable-2" layout="block">
                                        <%--<jsp:include page="includes/bi/include_bi_probabilidadeevasao.jsp" flush="true"/><!-- Pacto IA -->--%>
                                        <jsp:include page="includes/bi/include_bi_risco.jsp" flush="true"/><!-- Grupo de Risco -->
                                        <jsp:include page="includes/bi/include_bi_indicerenovacao.jsp" flush="true"/><!-- Índice Renovação -->
                                        <jsp:include page="includes/bi/include_bi_movimentacao_contratos.jsp" flush="true"/><!-- Movimentação de contratos -->
                                        <jsp:include page="includes/bi/include_bi_contratosRecorrencia.jsp" flush="true"/><!-- Cobranças por Convênio -->
                                        <jsp:include page="includes/bi/include_bi_controleoperacoes.jsp" flush="true"/><!-- Controle de Operações de Exceções -->
                                        <jsp:include page="includes/bi/include_bi_gestao_acessos.jsp" flush="true"/><!-- Gestão de Acessos -->
                                        <jsp:include page="includes/bi/include_bi_inadimplencia.jsp" flush="true"/><!-- Inadimplência -->

                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:panelGroup style="vertical-align: top;" styleClass="container-sortable-excluded" layout="block">
                                    <h:panelGroup style="vertical-align: top;" styleClass="col-sortable col-sortable-3" layout="block"/>
                                    <h:panelGroup style="display: none !important;" styleClass="col-sortable-hidden" layout="block"/>
                                </h:panelGroup>
                                <h:inputHidden value="#{BIControle.configuracaoBI.ordenacao}" id="arrayOrdemBI" />
                            </h:panelGroup>
                    </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        <h:panelGroup layout="block" id="containerCarregandoActions">
            <a4j:jsFunction name="carregarBITodos"  action="#{BIControle.carregarBITodos}"  status="naoUtilizaStatusRichFaces" reRender="formModalCarregando:mensagemBICarregando, textoCarregando,bi-container-bis"  oncomplete="abrirCarregando();"/>
            <a4j:jsFunction name="carregarBITodosDataBase"  action="#{BIControle.carregarBITodosDataBase}"  status="naoUtilizaStatusRichFaces"  reRender="formModalCarregando:mensagemBICarregando, textoCarregando,dataInicioPendencia,dataInicioDCC,dataInicioCO,dataInicioICV,dataInicioICVSS,dataInicioRenovacao,dataInicioRisco,dataInicioRotatividade,dataInicioTicketMedio,dataInicioBIIna,dataInicioAExperimental,bi-container-bis,dataInicioAcessoGymPass,panelConfigBiLtv"  oncomplete="abrirCarregando();"/>
            <a4j:jsFunction name="carregarBIPorDemanda"  action="#{BIControle.carregarAsync}"
                            status="naoUtilizaStatusRichFaces"  reRender="formModalCarregando:mensagemBICarregando, textoCarregando,containerFaqToid,panelConfigBiLtv, bi-container-bis #{BIControle.carregandoBIs ? BIControle.containersRenderiza : ''}"  oncomplete="#{BIControle.carregandoBIs ? 'carregarBISelecionado();' :   'organizarSortable();fecharCarregando();montarTips();requisicao = false;marcarExibirFaqToid();' }"/>
            <a4j:jsFunction name="carregarBISelecionado" action="#{BIControle.carregarBISelecionado}" status="naoUtilizaStatusRichFaces" reRender="containerFaqToid,formModalCarregando:mensagemBICarregando, textoCarregando" oncomplete="#{BIControle.carregandoBIs ? 'carregarBIPorDemanda();' : 'fecharCarregando();montarTips();'}"/>
            <a4j:jsFunction name="carregarBIScroll" status="naoUtilizaStatusRichFaces"  action="#{BIControle.selecionarBIVisiveis}" oncomplete="abrirCarregando();">
                <a4j:actionparam name="param1" assignTo="#{BIControle.bisCarregarScroll}"  />
            </a4j:jsFunction>
        </h:panelGroup>
            <h:commandButton id="carregarScrit" onclick="carregarBITodos()" style="display: none"/>
            <jsp:include page="include_rodape_flat.jsp"  flush="true"/>
            <script type="text/javascript" src="script/zw_bi_v1.13.js"></script>


        </h:panelGroup>
            <h:panelGroup layout="block" rendered="#{!BIControle.biCarregado}">
              <script>
                jQuery(document).ready(function(){
                    carregarBIsVisiveis(0);
                });
              </script>
            </h:panelGroup>
        <h:panelGroup layout="block" rendered="#{!(!BIControle.biCarregado)}">
            <script>
                jQuery(document).ready(function() {
                    startFaqtoidTour();
                });
            </script>
        </h:panelGroup>
        <a4j:jsFunction action="#{BIControle.marcarExibirFaqToid}" name="marcarExibirFaqToid" oncomplete="#{BIControle.msgAlert}" status="nouses"/>
        <a4j:jsFunction action="#{DicasControle.marcarEsconder}" name="marcarExibirTutorial" reRender="containerFaqToid">
            <f:setPropertyActionListener value="#{BIControle.exibirTutorial}" target="#{DicasControle.naoMostrarMais}"/>
        </a4j:jsFunction>
        <h:panelGroup layout="block" rendered="#{BIControle.exibirTutorial && !BIControle.biCarregado}" id="containerFaqToid">
            <%--Retirado apenas o Passo a passo do FAQ para caso necessite colocar novamente, estï¿½ o espaï¿½o reservado--%>
        </h:panelGroup>
    </h:form>

    <h:form id="formAtualizarChurnRate">
        <a id="btnatualizarchurn" onclick="atualizaChurn()"></a>
        <a4j:jsFunction process="none" reRender="grupoSlider, valorSliderPerc" name="atualizaChurn"/>
    </h:form>

    <jsp:include page="/includes/include_carregando_demanda.jsp" flush="true" />
    <jsp:include page="/pages/finan/includes/include_modal_consultaMetaFinanceiro.jsp" flush="true" />
    <jsp:include page="/includes/include_bi_contratosRecorrenciafiltrotipo.jsp" flush="true" />
    <jsp:include page="/includes/include_bi_indiceconversaofiltrotipo.jsp" flush="true"  />
    <jsp:include page="/includes/include_bi_filtroInadimplencia.jsp" flush="true"  />
    <jsp:include page="/includes/include_bi_conveniocobrancafiltro.jsp"  flush="true" />
    <jsp:include page="/includes/include_bi_filtro_movimentacao_contratos.jsp" flush="true" />
    <jsp:include page="/includes/include_bi_ticketMedio_filtros.jsp" flush="true" />
    <jsp:include page="/includes/modal_grafico_comparativo_bi.jsp" flush="true" />
    <jsp:include page="/includes/include_bi_indiceconversaosessaofiltrotipo.jsp" flush="true"  />
    <jsp:include page="/includes/autorizacao/include_autorizacao_funcionalidade_nova.jsp" flush="true" />
    <!-- Filtro por colaborador -->
    <jsp:include page="/includes/include_bi_indiceconversaofiltroColaborador.jsp" flush="true" />
    <jsp:include page="/includes/include_bi_config_ltv.jsp" flush="true" />
    <jsp:include page="/includes/include_bi_grafico_ltv.jsp" flush="true" />
    <jsp:include page="/includes/include_modal_bi_inadimplencia.jsp" flush="true" />
    <c:if test="${SuperControle.menuZwUi}">
        <style>
            .container-imagem.zw_ui.fechado-zwui {
                width: calc(100% - 85px) !important;
                margin-left: 0px !important;
                overflow: auto;
            }
        </style>
        <script>
            fecharMenu();
        </script>
    </c:if>
</f:view>


</body>

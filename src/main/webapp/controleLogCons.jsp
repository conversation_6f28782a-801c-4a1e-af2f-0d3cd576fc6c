<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <script>
        <%@include file="script/script.js" %>
    </script>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ControleLog_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ControleLog_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-utilizar-o-controle-de-log/"/>
    <jsp:include page="include_log.jsp"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="13" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="Consulta por:"/>
                    <h:selectOneMenu styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     id="consulta" value="#{LogControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{LogControle.tipoConsultaCombo}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" size="10" maxlength="30" styleClass="form" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" value="#{LogControle.controleConsulta.valorConsulta}"/>
                    E&nbsp;
                    <h:selectOneMenu styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     id="consulta2" value="#{LogControle.controleConsulta.campoConsulta2}">
                        <f:selectItems value="#{LogControle.tipoConsultaCombo}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta2" size="10" maxlength="30" styleClass="form"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 value="#{LogControle.controleConsulta.valorConsulta2}"/>

                    <h:outputText styleClass="tituloCampos" value="De"/>
                    <h:panelGroup>
                        <rich:calendar id="dtDe"
                                       value="#{LogControle.controleConsulta.inicio}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Até"/>
                    <h:panelGroup>
                        <rich:calendar id="dtAte"
                                       value="#{LogControle.controleConsulta.fim}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    <h:panelGroup id="botoes">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <a4j:commandButton id="consultar" type="submit" styleClass="botoes nvoBt btSec"
                                               value="#{msg_bt.btn_consultar}"
                                               reRender="paginaAtual, painelPaginacao, form, botoes, items"
                                               actionListener="#{LogControle.consultarPaginadoListener}"
                                               alt="#{msg.msg_consultar_dados}" accesskey="2">
                                <f:attribute name="paginaInicial" value="paginaInicial"/>
                            </a4j:commandButton>
                        </c:if>
                        <c:if test="${modulo eq 'centralEventos'}">
                            <a4j:commandButton type="submit" styleClass="botoes nvoBt btSec"
                                               value="#{msg_bt.btn_consultar}"
                                               reRender="paginaAtual, painelPaginacao, form, botoes, items"
                                               actionListener="#{LogControle.consultarPaginadoListener}"
                                               alt="#{msg.msg_consultar_dados}" accesskey="2">
                                <f:attribute name="paginaInicial" value="paginaInicial"/>
                            </a4j:commandButton>
                        </c:if>
                        <%--BOTÃO EXCEL--%>
                        <a4j:commandButton id="exportarExcel"
                                           image="imagens/btn_excel.png"
                                           style="margin-left: 8px;"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty LogControle.listaConsulta}"
                                           value="Excel"
                                           oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                           accesskey="2" styleClass="botoes">
                            <f:attribute name="lista" value="#{LogControle.listaConsulta}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="itemExportacao" value="controleLog"/>
                            <f:attribute name="atributos"
                                         value="nomeEntidade=Entidade,operacao=Operação,dataHoraAlteracao_Apresentar=Data e Hora,responsavelAlteracao=Responsável"/>
                            <f:attribute name="prefixo" value="ControleLog"/>
                        </a4j:commandButton>
                        <%--BOTÃO PDF--%>
                        <a4j:commandButton id="exportarPdf"
                                           style="margin-left: 8px;"
                                           image="/imagens/imprimir.png"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty LogControle.listaConsulta}"
                                           value="PDF"
                                           oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                           accesskey="2" styleClass="botoes">
                            <f:attribute name="lista" value="#{LogControle.listaConsulta}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="itemExportacao" value="controleLog"/>
                            <f:attribute name="atributos"
                                         value="nomeEntidade=Entidade,operacao=Operação,dataHoraAlteracao_Apresentar=Data e Hora,responsavelAlteracao=Responsável"/>
                            <f:attribute name="prefixo" value="ControleLog"/>
                        </a4j:commandButton>
                    </h:panelGroup>
                </h:panelGrid>

                <%--
                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{LogControle.listaConsulta}" rendered="#{LogControle.apresentarResultadoConsulta}" rows="10" var="controleLog">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_entidade}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="entidade"
                                         value="#{controleLog.nomeEntidade}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_operacao}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="operacao"
                                         value="#{controleLog.operacao} #{controleLog.nomeEntidade_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_nomeCampo}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="nomeCampo"
                                         value="#{controleLog.nomeCampo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_dataHora}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="dataAlteracao"
                                         value="#{controleLog.dataHoraAlteracao_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_responsavel}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="responsavel"
                                         value="#{controleLog.responsavelAlteracao}"/>
                    </h:column>
                </h:dataTable>

                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup id="painelPaginacao" rendered="#{LogControle.confPaginacao.existePaginacao}">
                        <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarPrimeiro}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagInicial" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarAnterior}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagAnterior" />
                        </a4j:commandLink>
                        <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{LogControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                        <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarPosterior}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagPosterior" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarUltimo}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagFinal" />
                        </a4j:commandLink>
                        <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{LogControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
                    </h:panelGroup>
                </h:panelGrid>



--%>
                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                             columnClasses="colunaAlinhamento"
                             value="#{LogControle.listaConsulta}" rendered="#{LogControle.apresentarResultadoConsulta}"
                             rows="20" var="controleLog">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_entidade}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="entidade"
                                         value="#{controleLog.nomeEntidade}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_operacao}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="operacao"
                                         value="#{controleLog.operacao} #{controleLog.nomeEntidade_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_dataHora}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="dataAlteracao"
                                         value="#{controleLog.dataHoraAlteracao_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_responsavel}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="form, formLog"
                                         oncomplete="Richfaces.showModalPanel('panelLog')" id="responsavel"
                                         value="#{controleLog.responsavelAlteracao}"/>
                    </h:column>
                </h:dataTable>
                <rich:datascroller align="center" for="form:items" id="scResultadoLog"/>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{LogControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{LogControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="./imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>

    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>

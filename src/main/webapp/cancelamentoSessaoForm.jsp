<%-- 
    Document   : cancelamentoSessaoForm
    Created on : 13/12/2012, 09:17:03
    Author     : carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_CancelamentoSessao_tituloForm}" /></title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_CancelamentoSessao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}"/>
    <h:form id="form">
        <%@include file="includes/include_identificadorModuloEstudio.jsp" %>
        <h:panelGrid
            columns="1"
            width="100%"
            style="background-color:#FFFFFF;"
            cellpadding="0"
            cellspacing="0"
            columnClasses="colunaCentralizada">
            <f:facet name="header">
                <h:graphicImage url="/imagens/estudio/topoDemoReduzidoEstudio.png" width="100%" height="45"/>
            </f:facet>

            <h:panelGrid
                columns="1"
                width="100%"
                columnClasses="colunaCentralizada">
                <h:panelGrid
                    columns="1"
                    style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                    columnClasses="colunaCentralizada"
                    width="100%">
                    <h:outputText
                        styleClass="tituloFormulario"
                        value="#{msg_aplic.prt_CancelamentoSessao_tituloForm}" />
                </h:panelGrid>
                <h:panelGrid
                    columns="1"
                    style="background-color:#FFFFFF;" columnClasses="colunaCentralizada"
                    width="100%">
                    <h:panelGrid
                        columns="2"
                        headerClass="subordinado"
                        width="100%"
                        columnClasses="colunaEsquerda" >
                        <h:panelGroup>
                            <h:outputText
                                styleClass="text"
                                style="font-weight: bold"
                                value="Nome do Cliente: " />
                            <h:outputText
                                styleClass="text"
                                value="#{CancelamentoSessaoControle.clienteVO.pessoa.nome}" />
                        </h:panelGroup>
                        <h:panelGroup >
                            <h:outputText
                                styleClass="text"
                                style="font-weight: bold"
                                value="Matrícula: " />
                            <h:outputText
                                styleClass="text"
                                value="#{CancelamentoSessaoControle.clienteVO.matricula}" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid
                        columns="1"
                        headerClass="subordinado"
                        width="100%"
                        columnClasses="colunaCentralizada">
                        <rich:dataTable
                            id="movProdutoEstornado"
                            width="100%"
                            border="0"
                            cellspacing="0"
                            cellpadding="0"
                            styleClass="textsmall"
                            columnClasses="centralizado, centralizado, centralizado"
                            value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.listaMovProduto}"
                            var="movProduto">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText
                                        style="font-weight: bold"
                                        value="#{msg_aplic.prt_HistoricoComprasCliente_contrato}" />
                                </f:facet>
                                <h:outputText
                                    style="font-weight: bold"
                                    value="#{movProduto.contrato.codigo}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText
                                        style="font-weight: bold"
                                        value="#{msg_aplic.prt_HistoricoComprasCliente_descricao}" />
                                </f:facet>
                                <h:outputText
                                    style="font-weight: bold"
                                    value="#{movProduto.descricao}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText
                                        style="font-weight: bold"
                                        value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                                </f:facet>
                                <h:outputText
                                    style="font-weight: bold"
                                    value="#{movProduto.dataLancamento_Apresentar}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText
                                        style="font-weight: bold"
                                        value="#{msg_aplic.prt_HistoricoComprasCliente_quantidade}" />
                                </f:facet>
                                <h:outputText
                                    style="font-weight: bold"
                                    value="#{movProduto.quantidade}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText
                                        style="font-weight: bold"
                                        value="#{msg_aplic.prt_HistoricoComprasCliente_unitario}" />
                                </f:facet>
                                <h:outputText
                                    style="font-weight: bold"
                                    value="#{movProduto.precoUnitario}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText
                                        style="font-weight: bold"
                                        value="#{msg_aplic.prt_HistoricoComprasCliente_desconto}" />
                                </f:facet>
                                <h:outputText
                                    style="font-weight: bold"
                                    value="#{movProduto.valorDesconto}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText
                                        style="font-weight: bold"
                                        value="#{msg_aplic.prt_HistoricoComprasCliente_totalFinal}" />
                                </f:facet>
                                <h:outputText
                                    style="font-weight: bold"
                                    value="#{movProduto.totalFinal}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText
                                        style="font-weight: bold"
                                        value="#{msg_aplic.prt_HistoricoComprasCliente_situacao}" />
                                </f:facet>
                                <h:outputText
                                    style="font-weight: bold"
                                    value="#{movProduto.situacao_Apresentar}" />
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>
                    <rich:spacer height="20px"/>
                    <h:outputText  value="Agendamentos Lançados"
                                   styleClass="tituloFormularioPreto"/>
                    <rich:dataTable
                        id="listaAgendamodal"
                        width="100%"
                        rowClasses="textsmall" rows="5"
                        styleClass="textsmall"
                        columnClasses="centralizado, centralizado, centralizado"
                        value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.listaAgendamentos}"
                        var="item">
                        <rich:column
                            id="item-dataAula"
                            sortable="true"
                            width="10%"
                            selfSorted="true"
                            sortBy="#{item.dataAula}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Data Aula"/>
                            </f:facet>
                            <h:outputText value="#{item.dataAula}" converter="dataConverter"/>
                        </rich:column>
                        <rich:column
                            id="item-diaSemana"
                            width="8%"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.diaSemana_Apresentar}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Dia Sem"/>
                            </f:facet>
                            <h:outputText value="#{item.diaSemana_Apresentar}" />
                        </rich:column>
                        <rich:column
                            id="item-horaInicio"
                            width="10%"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.horaInicio}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Horário"/>
                            </f:facet>
                            <h:outputText value="#{item.horaInicio}" converter="timeConverter"/>
                        </rich:column>
                        <rich:column
                            id="item-produto-venda"
                            width="20%"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.produtoVO.descricao}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Produto"/>
                            </f:facet>
                            <h:outputText value="#{item.produtoVO.descricao}"/>
                        </rich:column>
                        <rich:column
                            id="item-professor"
                            width="15%"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.colaboradorVO.pessoa.nome}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Colaborador"/>
                            </f:facet>
                            <h:outputText value="#{item.colaboradorVO.pessoa.nome}"/>
                        </rich:column>
                        <rich:column
                            id="item-ambiente"
                            width="12%"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.ambienteVO.descricao}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Ambiente"/>
                            </f:facet>
                            <h:outputText value="#{item.ambienteVO.descricao}"/>
                        </rich:column>
                        <rich:column
                            id="item-status"
                            width="15%"
                            sortable="true"
                            selfSorted="true"
                            sortBy="#{item.status.descricao}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Status"/>
                            </f:facet>
                            <h:outputText value="#{item.status.descricao}"/>
                        </rich:column>
                        <rich:column
                            id="item-tipoHorarioVO"
                            sortable="true"
                            width="13%"
                            selfSorted="true"
                            sortBy="#{item.tipoHorarioVO.descricao}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Tipo Horário"/>
                            </f:facet>
                            <h:outputText value="#{item.tipoHorarioVO.descricao}"/>
                        </rich:column>
                        <rich:column
                            id="item-responsavel"
                            sortable="true"
                            width="13%"
                            selfSorted="true"
                            sortBy="#{item.usuarioLancamento.nome}"
                            filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Resp. Agend."/>
                            </f:facet>
                            <h:outputText value="#{item.usuarioLancamento.nome}"/>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller rendered="true" maxPages="500" for="form:listaAgendamodal" id="scitems"/>

                    <h:panelGrid columns="2" width="100%" columnClasses="colunaCentralizada,colunaCentralizada">
                        <h:panelGroup>
                            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                <h:panelGroup>
                                    <h:outputText value="Valor Pago Pelo Cliente: " styleClass="tituloCamposNegrito" />
                                    <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                    <h:outputText id="vlrPago" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorPagoPeloCliente}" style="font-weight: bold; color:red">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText value="Saldo da Conta Corrente do Cliente: " styleClass="tituloCamposNegrito" />
                                    <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                    <h:outputText id="vlrCC" value="#{CancelamentoSessaoControle.clienteVO.saldoContaCorrente}" style="font-weight: bold; color:red">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGroup>
                        <h:panelGroup>
                            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                <h:panelGroup>
                                    <h:outputText value="Valor Utilizado Pelo Cliente: " styleClass="tituloCamposNegrito" />
                                    <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                    <h:outputText id="vlrUsado" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorUtilizadoCliente}" style="font-weight: bold; color:red">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:panelGroup>
                                <h:panelGroup id="panelValor">
                                    <h:outputText value="Crédito Restante: " styleClass="tituloCamposNegrito" />
                                    <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                    <h:outputText id="vlrTransferencia" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorTransferidoCliente}"
                                                  styleClass="green"
                                                  style="font-weight: bold;">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGroup>






                    </h:panelGrid>
                    <p>

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" rendered="#{CancelamentoSessaoControle.apresentarBotaoCancelar}">
                            <h:panelGroup>
                                <h:outputText value="O que deseja fazer com o cancelamento?" styleClass="tituloCampos"
                                              style="position:relative; left:140px;font-weight: bold"/>
                                <h:selectOneRadio id="tipoDevolucaoCancelamento" styleClass="tituloCampos" style="position:relative; left:140px;" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.tipoCancelamento}" >
                                    <f:selectItems value="#{CancelamentoSessaoControle.listaSelectItemTipoDevolucaoCancelamento}"/>
                                </h:selectOneRadio>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid
                            id="panelMensagem"
                            columns="3"
                            width="100%"
                            columnClasses="colunaEsquerda" >
                            <h:panelGrid
                                columns="1"
                                width="100%">

                                <h:outputText value=" " />

                            </h:panelGrid>
                            <h:commandButton
                                rendered="#{CancelamentoSessaoControle.sucesso}"
                                image="./imagens/sucesso.png" />
                            <h:commandButton
                                rendered="#{CancelamentoSessaoControle.erro}"
                                image="./imagens/erro.png" />
                            <h:panelGrid
                                columns="1"
                                width="100%" >
                                <h:outputText
                                    styleClass="mensagem"
                                    value="#{CancelamentoSessaoControle.mensagem}" />
                                <h:outputText
                                    styleClass="mensagemDetalhada"
                                    value="#{CancelamentoSessaoControle.mensagemDetalhada}" />
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGroup>
                            <h:commandButton
                                id="fechar"
                                alt="Fechar Janela"
                                onclick="fecharJanela();"
                                image="./imagens/estudio/fechar.png" />
                            <rich:spacer width="20px" />
                            <a4j:commandButton rendered="#{CancelamentoSessaoControle.apresentarBotaoCancelar}"
                                               id="avancar"
                                               value="Avançar"
                                               action="#{CancelamentoSessaoControle.validarOpcaoCancelamento}"
                                               image="/imagens/estudio/avancar.png" reRender="panelMensagem"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

            </h:panelGrid>
        </h:form>
    </f:view>
    <script>
        document.getElementById("form:valorConsulta").focus();
    </script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<h:panelGroup layout="block" styleClass="menuLateral">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-key"></i> Acesso ao Sistema
        </h:panelGroup>

        <!-- Empresa -->    
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.empresa}">
            <a4j:commandLink value="#{msg_menu.Menu_empresa}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="EMPRESA" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}configurando-os-dados-da-empresa-para-emissao-de-notas-fiscais/"
                          title="Clique e saiba mais: Empresa" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Empresa -->

        <!-- Perfil Acesso -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.perfilAcesso}">
            <a4j:commandLink value="#{msg_menu.Menu_perfilAcesso}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PERFIL_ACESSO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}quais-sao-as-permissoes-do-perfil-de-acesso-do-modulo-adm/"
                          title="Clique e saiba mais: Perfil de Acesso" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>        
        <!-- //Perfil Acesso -->

        <!-- Usu�rio -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.usuario}">
            <a4j:commandLink value="#{msg_menu.Menu_usuario}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="USUARIO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastro-um-novo-usuario-no-sistema/"
                          title="Clique e saiba mais: Usu�rio" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>           
        <!-- //Usu�rio -->

        <!-- Local de Acesso -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.localAcesso}">
            <a4j:commandLink value="#{msg_menu.Menu_localAcesso}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="LOCAL_ACESSO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-alterar-o-tempo-de-entrada-da-catraca/"
                          title="Clique e saiba mais: Local de Acesso" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Local de Acesso -->

        <!-- Servidor Facial -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.servidorFacial}">
            <a4j:commandLink value="#{msg_menu.Menu_servidorFacial}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="SERVIDOR_FACIAL" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Acesso_ao_sistema:Servidor_Facial"
                          title="#{msg_menu.Menu_wikiServidorFacial}" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- Servidor Facial -->


        <!-- Atualiza��es do bd -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.usuarioLogado.administrador}">
            <a4j:commandLink value="#{msg_menu.Menu_atualizacoesBD}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="ATUALIZACOES_BD" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Acesso_ao_sistema:Atualiza��es_de_Banco_de_Dados_executadas"
                          title="Clique e saiba mais: Local de Acesso" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Atualiza��o do bd -->        

        <!-- Controle de Log -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.localAcesso}">
            <a4j:commandLink value="#{msg_menu.Menu_log}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONTROLE_LOG" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-utilizar-o-controle-de-log/"
                          title="Clique e saiba mais: Controle de Log" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>        
        <!-- //Controle de Log -->

        <!-- Integra��o de Acesso -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.localAcesso}">
            <a4j:commandLink value="#{msg_menu.Menu_integracao}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="INTEGRACAO_ACESSO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Acesso_ao_sistema:Integra%C3%A7%C3%A3o_Acesso"
                          title="Clique e saiba mais: Integra��o de Acesso" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Integra��o de Acesso -->

        <!-- Autoriza��o de Acesso -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.localAcesso}">
            <a4j:commandLink value="#{msg_menu.Menu_autorizacao}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="AUTORIZACAO_ACESSO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-realizar-a-autorizacao-acesso-permitindo-que-os-alunos-de-uma-unidade-acesse-outra/"
                          title="Clique e saiba mais: Autoriza��o de Acesso" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>        
        <!-- //Autoriza��o de Acesso -->

        <!-- Gerador de Consultas -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.geradorConsultas}">
            <a4j:commandLink value="#{msg_menu.Menu_geradorCons}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="GERADOR_CONSULTAS" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}"
                          title="Clique e saiba mais: Gerador de Consultas" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>         
        <!-- //Gerador de Consultas -->

    </h:panelGroup>            
</h:panelGroup>



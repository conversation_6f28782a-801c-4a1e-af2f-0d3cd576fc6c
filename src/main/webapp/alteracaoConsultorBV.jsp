<%-- 
    Document   : historicoPontosResumo
    Created on : 27/09/2017, 11:26:38
    Author     : arthur
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>

<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Consultores alterados do BV"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Consultores alterados do BV"/>
        
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>
        
        <h:form id="form">
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" >
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <h:panelGrid columns="1" width="100%" >
                                    <h:panelGrid style="width:100%">
                                        <h:panelGroup id="botoes" style="text-align:right;" layout="block">
                                            <a4j:commandLink id="exportarExcel"
                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                               rendered="#{not empty IndiceConversaoVendaRelControle.listaQuestionarioContsultoBV}"
                                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                               accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista" value="#{IndiceConversaoVendaRelControle.listaQuestionarioContsultoBV}"/>
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="atributos" value="matriculaCliente=Matricula,nomeCliente=Nome,valorCampoAnterior=Consulor Antigo,valorCampoAlterado=Consultor Novo,responsavelAlteracao=Responsavel Operação,tipoBV_Apresentar=Tipo BV,dataAlteracao_Apresentar=Data Operação"/>
                                                <f:attribute name="prefixo" value="ConsultoresAlteradosDoBV"/>
                                                <h:outputText title="#{msg_aplic.prt_exportar_form_excel}" styleClass="btn-print-2 excel tooltipster"/>
                                            </a4j:commandLink>
                                            <%--BOTÃO PDF--%>
                                            <a4j:commandLink id="exportarPdf"
                                                               style="margin-left: 8px;"
                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                               rendered="#{not empty IndiceConversaoVendaRelControle.listaQuestionarioContsultoBV}"
                                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                               accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista" value="#{IndiceConversaoVendaRelControle.listaQuestionarioContsultoBV}"/>
                                                <f:attribute name="tipo" value="pdf"/>
                                                <f:attribute name="atributos" value="matriculaCliente=Matricula,nomeCliente=Nome,valorCampoAnterior=Consulor Antigo,valorCampoAlterado=Consultor Novo,responsavelAlteracao=Responsavel Operação,tipoBV_Apresentar=Tipo BV,dataAlteracao_Apresentar=Data Operação"/>
                                                <f:attribute name="prefixo" value="ConsultoresAlteradosDoBV"/>
                                                <h:outputText title="#{msg_aplic.prt_exportar_form_pdf}" styleClass="btn-print-2 PDF tooltipster"/>
                                            </a4j:commandLink>
                                        </h:panelGroup> 
                                    </h:panelGrid>
                                    <rich:dataTable id="listaPontosAlunos"  width="100%" styleClass="tabelaSimplesCustom"
                                                    value="#{IndiceConversaoVendaRelControle.listaQuestionarioContsultoBV}" var="lista">
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.questionarioClienteVO.cliente.matricula}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Matricula" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.questionarioClienteVO.cliente.matricula}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.nomeCliente}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.nomeCliente}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.log.valorCampoAnterior}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Consulor Antigo" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.log.valorCampoAnterior}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.log.valorCampoAlterado}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Consulor Novo" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.log.valorCampoAlterado}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.log.responsavelAlteracao}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Responsavel Operação" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.log.responsavelAlteracao}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.tipoBV_Apresentar}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Tipo BV" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.tipoBV_Apresentar}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.dataAlteracao_Apresentar}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Data Operação" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.dataAlteracao_Apresentar}" />
                                        </rich:column>
                                                                                
                                        <rich:column>
                                            <a4j:commandLink id="visualizarCliente" action="#{IndiceConversaoVendaRelControle.irParaTelaClienteConsulorBV}" title="#{msg_aplic.prt_tela_cliente}"
                                                             oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                                                    styleClass="linkPadrao texto-cor-azul texto-size-14-real tooltipster">
                                                <i class="fa-icon-search"></i>
                                            </a4j:commandLink>
                                        </rich:column>
                                        
                                    </rich:dataTable>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{HistoricoPontosControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoPontosControle.mensagemDetalhada}"/>
                    </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    
    <script>
        function carregarTooltipster(){
            jQuery('.tooltipster').tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        };
        carregarTooltipster();
    </script>
</f:view>


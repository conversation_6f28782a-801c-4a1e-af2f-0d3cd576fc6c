<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_Turma_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Turma_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-turma/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r" >
        <div layout="block" style="text-align: center;margin-bottom: 20px">
        <span style="display: inline-block;background-color: #FEE6E6;font-family: 'Nunito Sans';font-size: 14px;font-weight: bold;padding: 10px 10px 10px 10px;border-radius: 5px" class="fa-icon-exclamation-triangle-sign texto-cor-vermelho">
            Orientamos que utilize a nova tela de cadastro de turmas porque essa tela será descontinuada a partir do dia 01/12/2023 e não poderá mais ser acessada.
        </span>
        </div>
        <h:form id="form" styleClass="pure-form pure-u-1" >
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo" style="width: 101.33333%;">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-3-12"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-9-12 text-right" >
                        <h:panelGroup layout="block" styleClass="controles">
                            
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" value="Tipo Turma"/>
                            <h:panelGroup layout="block" styleClass="cb-container" >
                                <h:selectOneMenu id="tipoturma" styleClass="exportadores"
                                                 value="#{TurmaControle.tipoTurma}">
                                    <f:selectItems value="#{TurmaControle.listaTipoTurma}"/>
                                    <a4j:support event="onchange" oncomplete="recarregarTabelaTurma()"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                            
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{TurmaControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="codigo=Código,identificador=Identificador,descricao=Descrição,modalidade_Apresentar=Modalidade
                                         ,empresa_Apresentar=Empresa,idadeMinima=Idade Mínima,idadeMaxima=Idade Máxima,dataInicialVigencia_Apresentar=Início da Vigência,dataFinalVigencia_Apresentar=Final da Vigência"/>
                                <f:attribute name="prefixo" value="Turma"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{TurmaControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="codigo=Código,identificador=Identificador,descricao=Descrição,modalidade_Apresentar=Modalidade
                                         ,empresa_Apresentar=Empresa,idadeMinima=Idade Mínima,idadeMaxima=Idade Máxima,dataInicialVigencia_Apresentar=Início da Vigência,dataFinalVigencia_Apresentar=Final da Vigência"/>
                                <f:attribute name="prefixo" value="Turma"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnLog"
                                             styleClass="exportadores margin-h-10"
                                             action="#{TurmaControle.realizarConsultaLogObjetoGeral}"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>
                            <a4j:commandLink
                                    id="btnInativarTurma" rendered="#{LoginControle.usuarioLogado.administrador}"
                                    oncomplete="recarregarTabelaTurma()"
                                    styleClass="pure-button pure-button-primary" style="margin-right: 5px; width: 88px; margin-left: 0px;  padding-left: 5px"
                                    action="#{TurmaControle.inativarTurmas}" reRender="tblTurma, mensagem">
                                &nbsp${msg_bt.btn_inativar_turmas}
                            </a4j:commandLink>

                            <a4j:commandLink id="btnNovo" reRender="mensagem"
                                             styleClass="pure-button pure-button-primary" style="margin-right: 5px; width: 95px; margin-left: 5px ; padding-left: 5px"
                                             action="#{TurmaControle.novo}"
                                             accesskey="1">
                               &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblTurma" class="tabelaTurma pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_identificador_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_descricao_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_modalidade_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_empresa_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_idadeMinima_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_idadeMaxima_maiusculo}</th>
                    <c:if test="${TurmaControle.integracaoSpivi}">
                        <th>INTEGRAÇÃO SPIVI</th>
                    </c:if>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{TurmaControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{TurmaControle.sucesso}" value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{TurmaControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty TurmaControle.mensagem}"
                              value=" #{TurmaControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty TurmaControle.mensagemDetalhada}"
                              value=" #{TurmaControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>


    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        function recarregarTabelaTurma() {
            var tipoTurma = document.getElementById("form:tipoturma").value;
            tabelaAtual.dataTable().fnDestroy(0);
            iniciarTabela("tabelaTurma", "${contexto}/prest/plano/turma?tipoTurma="+tipoTurma, 2, "asc", "", true);
        }
        
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaTurma", "${contexto}/prest/plano/turma?tipoTurma=${TurmaControle.tipoTurma}", 2, "asc", "", true);
        });
    </script>

</f:view>

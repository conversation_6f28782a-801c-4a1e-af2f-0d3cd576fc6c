<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${contexto}/css/ce.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">var contexto = '${contexto}';</script>
<script type="text/javascript" language="javascript" src="${contexto}/script/ajuda.js"></script>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>

<style>
    .subordinado {
        padding: 5px !important;
    }
    .imagemacademia img{
        width: 100%;
    }
    .imagemacademia{
        vertical-align: top;
        display: inline-block;
        width: 150px;
        margin: 10px;
    }

    .caixaResolucao{
        margin-bottom: 30px;
        margin-top: 10px;
        display: grid;
        justify-content: center;
        font-size: 12px;
        color: #888;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Produto_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Produto_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-um-produto-de-estoque/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel>
                    <rich:tab id="abaData" styleClass="titulo3SemDecoracao" label="Dados">
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText value="#{msg_aplic.prt_Produto_codigo}"/>
                            <h:panelGroup>
                                <h:inputText id="codigo" size="10" readonly="true" styleClass="camposSomenteLeitura"
                                             value="#{ProdutoControle.produtoVO.codigo}"/>
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText value="Importação de produtos:"/>
                            <a4j:commandLink value="Clique aqui"
                                             styleClass="tooltipster"
                                             title="Clique aqui para abrir o processo de importação de produtos."
                                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                             oncomplete="#{FuncionalidadeControle.abrirPopUp};reRenderMenuLateral()"
                                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}">
                                <f:attribute name="funcionalidade" value="IMPORTACAO" />
                            </a4j:commandLink>

                            <c:if test="${empty contexto}">
                                <h:outputText value="#{msg_aplic.prt_Produto_descricao}"/>
                            </c:if>
                            <h:panelGroup>
                                <h:inputText id="descricao"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form" size="100" maxlength="100"
                                             disabled="#{ProdutoControle.creditoPersonal}"
                                             value="#{ProdutoControle.produtoVO.descricao}"/>
                            </h:panelGroup>
                            <c:if test="${not empty contexto}">
                                <h:panelGroup>
                                    <%@include file="pages/ce/includes/include_obrigatorio.jsp" %>
                                    <h:outputText value="#{CElabels['menu.cadastros.produto.tipo']}:"/>
                                </h:panelGroup>
                            </c:if>

                            <h:outputText value="#{msg_aplic.prt_Produto_tipoProduto}"/>
                            <h:outputText value="Crédito Personal" rendered="#{ProdutoControle.creditoPersonal}"/>
                            <h:panelGrid columns="4" rendered="#{!ProdutoControle.creditoPersonal}">

                                <h:selectOneMenu id="tipoProduto" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 disabled="#{ProdutoControle.naoPermiteEditar || ProdutoControle.editarSomenteDescricao}"
                                                 styleClass="form" value="#{ProdutoControle.produtoVO.tipoProduto}">
                                    <a4j:support event="onchange" action="#{ProdutoControle.selecionarTipoProduto}"
                                                 focus="tipoProduto" reRender="form"/>
                                    <f:selectItems value="#{ProdutoControle.listaSelectItemTipoProdutoProduto}"/>
                                </h:selectOneMenu>
                                <rich:spacer width="10px"/>
                                <c:if test="${empty contexto}">
                                    <h:outputText
                                            rendered="#{ProdutoControle.produtoVO.tipoProduto == 'SS'}"
                                            style="font-weight: bold;
                                font-family: Arial, Helvetica, sans-serif;
                                font-size: 12px;
                                text-decoration: none;
                                text-transform: none;
                                line-height: 150%;
                                color: #333;"
                                            value="#{msg_aplic.prt_Produto_capacidade}"/>
                                </c:if>
                                <h:panelGroup rendered="#{ProdutoControle.produtoVO.tipoProduto == 'SS'}">
                                    <h:inputText rendered="#{ProdutoControle.produtoVO.tipoProduto == 'SS'}"
                                                 id="capacidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" size="2" maxlength="3"
                                                 value="#{ProdutoControle.produtoVO.capacidade}"/>
                                </h:panelGroup>
                            </h:panelGrid>

                            <h:outputText value="Unidade de Medida:"/>
                            <h:selectOneMenu id="unidadeMedida" styleClass="form"
                                             value="#{ProdutoControle.produtoVO.unidadeMedida}"
                                             disabled="#{ProdutoControle.produtoVO.desativarUnidadeMedida}">
                                <a4j:support event="onchange" focus="unidadeMedida" reRender="form"/>
                                <f:selectItems value="#{ProdutoControle.listaSelectItemUnidadeMedida}"/>
                            </h:selectOneMenu>

                            <h:outputText value="Código de Barras:" styleClass="tooltipster"
                                          title="#{ProdutoControle.produtoVO.codigoBarrasTitle}"
                                          rendered="#{ProdutoControle.produtoVO.tipoProduto == 'PE'}"/>
                            <h:panelGroup rendered="#{ProdutoControle.produtoVO.tipoProduto == 'PE'}">
                                <h:inputText rendered="#{ProdutoControle.produtoVO.tipoProduto == 'PE'}"
                                             id="codigoBarras" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form tooltipster" size="12" maxlength="50"
                                             title="#{ProdutoControle.produtoVO.codigoBarrasTitle}"
                                             value="#{ProdutoControle.produtoVO.codigoBarras}"/>
                            </h:panelGroup>

                            <h:outputText value="#{msg_aplic.prt_Produto_desativado}"/>
                            <h:selectBooleanCheckbox disabled="#{ProdutoControle.editarSomenteDescricao}"
                                                     id="situacaoProduto" styleClass="campos"
                                                     value="#{ProdutoControle.produtoVO.desativado}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_aparecerAulaCheia}"
                                          title="#{msg_aplic.prt_Produto_aparecerAulaCheiatip}"
                                          rendered="#{ProdutoControle.usaAulaCheia}"
                                          styleClass="tooltipster"/>
                            <h:selectBooleanCheckbox disabled="#{ProdutoControle.editarSomenteDescricao}"
                                                     title="#{msg_aplic.prt_Produto_aparecerAulaCheiatip}"
                                                     styleClass="campos tooltipster"
                                                     rendered="#{ProdutoControle.usaAulaCheia}"
                                                     value="#{ProdutoControle.produtoVO.aparecerAulaCheia}"/>

                            <h:outputText rendered="#{ProdutoControle.produtoVO.desenhartela}"
                                          value="#{msg_aplic.prt_Produto_categoriaProduto}"/>

                            <h:panelGroup rendered="#{ProdutoControle.produtoVO.desenhartela}">
                                <h:selectOneMenu id="categoriaProduto" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ProdutoControle.produtoVO.categoriaProduto.codigo}"
                                                 disabled="#{ProdutoControle.creditoPersonal}">
                                    <f:selectItems value="#{ProdutoControle.listaSelectItemCategoriaProduto}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_categoriaProduto"
                                                   rendered="#{ProdutoControle.produtoVO.desenhartela}"
                                                   action="#{ProdutoControle.montarListaSelectItemCategoriaProduto}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                   reRender="form:categoriaProduto"/>
                                <h:message for="categoriaProduto" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{ProdutoControle.produtoVO.armario}"
                                          value="#{msg_aplic.prt_TamanhoArmario_tituloForm}:"/>

                            <h:panelGroup rendered="#{ProdutoControle.produtoVO.armario}">
                                <h:selectOneMenu id="TamanhoArmario" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ProdutoControle.produtoVO.tamanhoArmario.codigo}">
                                    <f:selectItems value="#{ProdutoControle.tiposArmario}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_tiposArmario"
                                                   rendered="#{ProdutoControle.produtoVO.armario}"
                                                   action="#{ProdutoControle.resetarComboTiposArmario}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                   reRender="form:TamanhoArmario"/>
                            </h:panelGroup>

                            <c:if test="${ProdutoControle.apresentarMargemLucroPrecoCusto}">
                                <h:outputText value="Preço de custo:"/>
                                <h:panelGroup>
                                    <h:inputText id="precoCusto"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 size="10" maxlength="10"
                                                 value="#{ProdutoControle.produtoVO.precoCusto}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                        <a4j:support status="false"
                                                     action="#{ProdutoControle.acaoAlterarPrecoCusto}"
                                                     event="onkeyup" reRender="form:valor"/>
                                    </h:inputText>
                                </h:panelGroup>

                                <h:outputText value="Margem de lucro:"/>
                                <h:panelGroup>
                                    <h:inputText id="margemLucro"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 size="10" maxlength="10"
                                                 value="#{ProdutoControle.produtoVO.margemLucro}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                        <a4j:support status="false"
                                                     action="#{ProdutoControle.acaoAlterarMargemLucro}"
                                                     event="onkeyup" reRender="form:valor"/>
                                    </h:inputText>
                                    <h:outputText style="padding-left: 5px" value="% (Porcentagem)"/>
                                </h:panelGroup>
                            </c:if>

                            <c:if test="${ProdutoControle.produtoVO.desenharValorProduto}">
                                <h:outputText value="#{msg_aplic.prt_Produto_valor}"/>
                                <h:panelGroup>
                                    <h:inputText id="valor"
                                                 onkeypress="return formatar_moeda(this,'.',',',event);"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 size="10" maxlength="10"
                                                 value="#{ProdutoControle.produtoVO.valorFinal}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                        <a4j:support status="false"
                                                     rendered="#{ProdutoControle.apresentarMargemLucroPrecoCusto}"
                                                     action="#{ProdutoControle.acaoAlterarValorProduto}"
                                                     event="onkeyup" reRender="form:margemLucro"/>
                                    </h:inputText>
                                    <h:outputText style="padding-left: 5px"
                                                  styleClass="tooltipster"
                                                  title="#{ProdutoControle.titleUnidadeMedidaProduto}"
                                                  value="#{ProdutoControle.produtoVO.valorExplicacaoUnidadeMedida}"/>
                                </h:panelGroup>
                            </c:if>

                            <h:outputText rendered="#{ProdutoControle.produtoVO.desenharTipoVigencia}"
                                          value="#{msg_aplic.prt_Produto_tipoVigencia}"/>
                            <h:panelGroup rendered="#{ProdutoControle.produtoVO.desenharTipoVigencia}">
                                <h:selectOneMenu rendered="#{ProdutoControle.produtoVO.desenharTipoVigencia}"
                                                 disabled="#{ProdutoControle.produtoVO.desativarComboBox || ProdutoControle.produtoVO.armario}"
                                                 id="tipoVigencia" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ProdutoControle.produtoVO.tipoVigencia}">
                                    <a4j:support event="onchange" focus="tipoVigencia" reRender="form"/>
                                    <f:selectItems value="#{ProdutoControle.listaSelectItemTipoVigenciaProduto}"/>
                                </h:selectOneMenu>
                                <h:message for="tipoVigencia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{ProdutoControle.produtoVO.desenharDataVigencia}"
                                          value="#{msg_aplic.prt_Produto_dataInicioVigencia}"/>
                            <h:panelGroup>
                                <rich:calendar id="dataInicioVigencia"
                                               rendered="#{ProdutoControle.produtoVO.desenharDataVigencia}"
                                               value="#{ProdutoControle.produtoVO.dataInicioVigencia}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="dataInicioVigencia" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{ProdutoControle.produtoVO.desenharDataVigencia}"
                                          value="#{msg_aplic.prt_Produto_dataFinalVigenciaFixa}"/>
                            <h:panelGroup>
                                <rich:calendar id="dataFinalVigenciaFixa"
                                               rendered="#{ProdutoControle.produtoVO.desenharDataVigencia}"
                                               value="#{ProdutoControle.produtoVO.dataFinalVigencia}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="dataFinalVigenciaFixa" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText rendered="#{ProdutoControle.produtoVO.desenharPeriodoVigencia}"
                                          value="#{msg_aplic.prt_Produto_nrDiasVigencia}"/>
                            <h:inputText rendered="#{ProdutoControle.produtoVO.desenharPeriodoVigencia}"
                                         id="nrDiasVigencia" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="4" maxlength="5"
                                         value="#{ProdutoControle.produtoVO.nrDiasVigencia}"/>
                            <h:outputText rendered="#{ProdutoControle.produtoVO.podeSerRenovadoAutomatica}"
                                          value="#{msg_aplic.prt_Produto_renovavelAutomaticamente}"/>
                            <h:selectBooleanCheckbox rendered="#{ProdutoControle.produtoVO.podeSerRenovadoAutomatica}" styleClass="campos"
                                                     title="Se marcado todos produtos lançados serão renovados automaticamente."
                                                     value="#{ProdutoControle.produtoVO.renovavelAutomaticamente}">

                                <a4j:support event="onchange"
                                             action="#{ProdutoControle.validarRenovacaoAutomatica}"
                                             reRender="form"/>
                            </h:selectBooleanCheckbox>

                            <h:outputText value="Prefixo:"/>
                            <h:inputText onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="4" maxlength="4" style="text-transform: uppercase"
                                         value="#{ProdutoControle.produtoVO.prefixo}"/>

                            <h:outputText
                                    rendered="#{(ProdutoControle.produtoVO.desenharPeriodoVigencia or ProdutoControle.produtoVO.desenharVigenciaVariavel) and (ProdutoControle.produtoVO.tipoProduto == 'SE' or ProdutoControle.produtoVO.tipoProduto == 'AT')}"
                                    value="#{msg_aplic.prt_Produto_bloqueiaPelaVigencia}"/>
                            <h:selectBooleanCheckbox
                                    rendered="#{(ProdutoControle.produtoVO.desenharPeriodoVigencia or ProdutoControle.produtoVO.desenharVigenciaVariavel) and (ProdutoControle.produtoVO.tipoProduto == 'SE' or ProdutoControle.produtoVO.tipoProduto == 'AT')}"
                                    id="bloqueiaPelaVigencia"
                                    styleClass="campos" value="#{ProdutoControle.produtoVO.bloqueiaPelaVigencia}"/>


                            <h:outputText rendered="#{(ProdutoControle.produtoVO.desenharPeriodoVigencia
                                              || ProdutoControle.produtoVO.desenharDataVigencia) && !ProdutoControle.produtoVO.renovavelAutomaticamente }"
                                          value="#{msg_aplic.prt_Produto_prevalecerVigenciaContrato}"
                                          title="#{msg_aplic.prt_Produto_prevalecerVigenciaContratoExplain}"/>

                            <h:selectBooleanCheckbox rendered="#{(ProdutoControle.produtoVO.desenharPeriodoVigencia
                                                         || ProdutoControle.produtoVO.desenharDataVigencia) && !ProdutoControle.produtoVO.renovavelAutomaticamente}"
                                                     id="prevalecerVigenciaContrato"
                                                     styleClass="campos"
                                                     value="#{ProdutoControle.produtoVO.prevalecerVigenciaContrato}"
                                                     title="#{msg_aplic.prt_Produto_prevalecerVigenciaContratoExplain}"/>

                            <%--                            <h:outputText style="font-weight: bold" value="#{msg_aplic.PONTOS}" rendered="#{ProdutoControle.empresaLogado.trabalharComPontuacao}"/>--%>
                            <%--                            <h:inputText id="pontos" title="#{msg_aplic.title_insira_valor_inteiro}"--%>
                            <%--                                         value="#{ProdutoControle.produtoVO.pontos}" rendered="#{ProdutoControle.empresaLogado.trabalharComPontuacao}" styleClass="tooltipster"/>--%>
                            <%--                            --%>

                            <h:outputText value="#{msg_aplic.prt_Produto_qtdePontos}"/>
                            <h:inputText id="qtdePontos" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="10"
                                         value="#{ProdutoControle.produtoVO.qtdePontos}"/>
                            <h:outputText styleClass="tituloCampos" rendered="#{!(ProdutoControle.produtoVO.tipoProduto == null) and
                             !(ProdutoControle.produtoVO.tipoProduto == '') && ProdutoControle.produtoVO.tipoProduto != 'FR'}"
                                          value="Texto Contrato do Produto"/>
                            <h:panelGroup rendered="#{!(ProdutoControle.produtoVO.tipoProduto == null) and
                             !(ProdutoControle.produtoVO.tipoProduto == '') && ProdutoControle.produtoVO.tipoProduto != 'FR'}">
                                <h:selectOneMenu id="produtoTextoPadrao" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ProdutoControle.produtoVO.contratoTextoPadrao}">
                                    <f:selectItems value="#{ProdutoControle.listaSelectItemContratoTextoPadrao}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_planoTextoPadrao"
                                                   action="#{ProdutoControle.montarListaSelectItemContratoTextoPadrao}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                   reRender="form:produtoTextoPadrao"/>

                            </h:panelGroup>
                            <h:outputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" value="Código Produto Sesi:"/>
                            <h:inputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" id="codSesi" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="10"
                                         value="#{ProdutoControle.produtoVO.codigoProdutoSesi}"/>
                            <h:outputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" value="CR Sesi:"/>
                            <h:inputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" id="crSesi" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="10"
                                         value="#{ProdutoControle.produtoVO.cRSesi}"/>
                            <h:outputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" value="Projeto Sesi:"/>
                            <h:inputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" id="projetoSesi" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="10"
                                         value="#{ProdutoControle.produtoVO.projetoSesi}"/>
                            <h:outputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" value="Conta Financeira Sesi:"/>
                            <h:inputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" id="contaFinanceiraSesi" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="10"
                                         value="#{ProdutoControle.produtoVO.contaFinanceiraSesi}"/>
                            <h:outputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" value="Negócio Sesi:"/>
                            <h:selectOneMenu rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" id="negocioSesi" styleClass="form"
                                             value="#{ProdutoControle.produtoVO.negocioSesi}">
                                <f:selectItems value="#{ProdutoControle.listaSelectItemNegocioSesi}"/>
                            </h:selectOneMenu>

                            <h:outputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" value="ID do Produto no SMD:"/>
                            <h:inputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" id="idProdutoSMD" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="10"
                                         value="#{ProdutoControle.produtoVO.idProdutoSMD}"/>

                            <h:outputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" value="Código do Formulário:"/>
                            <h:inputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}" id="codigoFormulario" onkeypress="return Tecla(event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="10"
                                         value="#{ProdutoControle.produtoVO.codigoFormulario}"/>

                            <h:outputText rendered="#{ProdutoControle.apresentarConfiguracoesSesi}"
                                          value="#{msg_aplic.prt_Produto_exibirRelatorioSMD}"
                                          title="#{msg_aplic.prt_Produto_exibirRelatorioSMDExplain}"/>
                            <h:selectBooleanCheckbox rendered="#{ProdutoControle.apresentarConfiguracoesSesi}"
                                                     id="exibirRelatorioSMD"
                                                     styleClass="campos"
                                                     value="#{ProdutoControle.produtoVO.exibirRelatorioSMD}"
                                                     title="#{msg_aplic.prt_Produto_exibirRelatorioSMDExplain}"/>

                            <h:outputText style="font-weight: bold" value="#{msg_aplic.prt_Produto_observacao}"/>
                            <h:panelGroup>
                                <h:inputTextarea id="observacao" value="#{ProdutoControle.produtoVO.observacao}"
                                                 cols="50" rows="3"/>
                            </h:panelGroup>

                            <c:if test="${ProdutoControle.produtoVO.tipoProduto eq 'DI'}">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_Produto_quantidade_convites}"/>
                                <h:inputText id="qtdConvites" onkeypress="return Tecla(event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             size="2" maxlength="10"
                                             value="#{ProdutoControle.produtoVO.qtdConvites}"/>
                            </c:if>

                        </h:panelGrid>

                        <!-----------CONFIGURAR VALOR POR UNIDADE----------------------->
                        <h:panelGrid id="cfgEmpresas" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada"
                                     rendered="#{ProdutoControle.exibirConfiguracoesParaEmpresas}">
                            <f:facet name="header">
                                <h:outputText value="Configurar valor por empresa"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         rendered="#{ProdutoControle.podeAdicionarCfgEmpresa}"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">

                                <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                                <h:selectOneMenu id="empresaCfg" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ProdutoControle.cfgEmpresa.empresa.codigo}">
                                    <f:selectItems value="#{ProdutoControle.listaEmpresas}"/>
                                </h:selectOneMenu>

                                <h:outputText styleClass="tituloCampos" value="Valor padrão:"/>
                                <h:inputText id="valorempresaCfg" value="#{ProdutoControle.cfgEmpresa.valor}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" size="10" maxlength="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText styleClass="tituloCampos" value="Valor para alunos Gogood:"/>
                                <h:inputText id="valorempresaCfg_goGood" value="#{ProdutoControle.cfgEmpresa.valorAlunoGogood}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" size="10" maxlength="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText styleClass="tituloCampos" value="Valor para alunos Gympass:"/>
                                <h:inputText id="valorempresaCfg_gymPass" value="#{ProdutoControle.cfgEmpresa.valorAlunoGympass}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" size="10" maxlength="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText styleClass="tituloCampos" value="Valor para alunos Totalpass:"/>
                                <h:inputText id="valorempresaCfg_totalPass" value="#{ProdutoControle.cfgEmpresa.valorAlunoTotalpass}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" size="10" maxlength="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </h:panelGrid>
                            <a4j:commandButton id="addempresaCfg" action="#{ProdutoControle.adicionarCfg}"
                                               reRender="form"
                                               rendered="#{ProdutoControle.podeAdicionarCfgEmpresa}"
                                               value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="7" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="tabelaCfgs" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada w20,w20 direita, w20 direita,colunaCentralizada w20"
                                             value="#{ProdutoControle.produtoVO.configuracoesEmpresa}"
                                             rendered="#{not empty ProdutoControle.produtoVO.configuracoesEmpresa}"
                                             var="cfg">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Empresa"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.empresa.nome}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor padrão"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.valor}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column rendered="#{cfg.valorAlunoGympassPreenchido}">
                                        <f:facet name="header">
                                            <h:outputText value="Valor para alunos Gympass"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.valorAlunoGympass}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column rendered="#{cfg.valorAlunoTotalpassPreenchido}">
                                        <f:facet name="header">
                                            <h:outputText value="Valor para alunos Totalpass"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.valorAlunoTotalpass}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column  rendered="#{cfg.valorAlunoGogoodPreenchido}">
                                        <f:facet name="header">
                                            <h:outputText value="Valor para alunos Gogood"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.valorAlunoGogood}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="editarempresaCfg" reRender="form"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{ProdutoControle.editarCfg}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png"
                                                               styleClass="botoes"/>

                                            <a4j:commandButton id="removerempresaCfg" reRender="form"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{ProdutoControle.removerCfg}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>

                        <!-----------CONFIGURAR VALOR POR PLANO----------------------->
                        <h:panelGrid id="cfgEmpresas_porPlano" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada"
                                     rendered="#{ProdutoControle.exibirConfiguracoesParaEmpresas}">
                            <f:facet name="header">
                                <h:outputText value="Configurar valor por plano"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         rendered="#{ProdutoControle.podeAdicionarCfgEmpresa}"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">

                                <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                                <h:selectOneMenu id="empresaCfg_valorPlano" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ProdutoControle.cfgValorProdutoPlano.empresa.codigo}">
                                    <f:selectItems value="#{ProdutoControle.listaEmpresas}"/>
                                    <a4j:support event="onchange"
                                                 action="#{ProdutoControle.atualizarListaPlano}"
                                                 reRender="form"/>
                                </h:selectOneMenu>

                                <h:outputText styleClass="tituloCampos" value="Plano:"/>
                                <h:selectOneMenu id="selectPlano" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" value="#{ProdutoControle.cfgValorProdutoPlano.plano.codigo}">
                                    <f:selectItems value="#{ProdutoControle.listaPlanos}"/>
                                </h:selectOneMenu>

                                <h:outputText styleClass="tituloCampos" value="Valor:"/>
                                <h:inputText id="valorempresaCfg_porPlano" value="#{ProdutoControle.cfgValorProdutoPlano.valor}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" size="10" maxlength="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </h:panelGrid>
                            <a4j:commandButton id="addempresaCfg_porPlano" action="#{ProdutoControle.adicionarCfgValorPorPlano}"
                                               reRender="form"
                                               rendered="#{ProdutoControle.podeAdicionarCfgEmpresa}"
                                               value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="7" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="tabelaCfgs_porPlano" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada w20,w20 direita, w20 direita,colunaCentralizada w20"
                                             value="#{ProdutoControle.produtoVO.configuracoesValorPorPlano}"
                                             rendered="#{not empty ProdutoControle.produtoVO.configuracoesValorPorPlano}"
                                             var="cfg">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Empresa"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.empresa.nome}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Plano"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.plano.descricao}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor"/>
                                        </f:facet>
                                        <h:outputText value="#{cfg.valor}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="editarempresaCfg_porPlano" reRender="form"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{ProdutoControle.editarCfgValorPorPlano}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png"
                                                               styleClass="botoes"/>

                                            <a4j:commandButton id="removerempresaCfg_porPlano" reRender="form"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{ProdutoControle.removerCfgValorPorPlano}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                        <!---------------------------------->
                        <h:panelGrid id="pacotes" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada"
                                     rendered="#{ProdutoControle.creditoPersonal}">
                            <f:facet name="header">
                                <h:outputText value="Pacotes de créditos para personal"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">
                                <h:outputText styleClass="tituloCampos" value="Quantidade:"/>
                                <rich:inputNumberSpinner value="#{ProdutoControle.pacote.quantidade}"/>
                                <h:outputText styleClass="tituloCampos" value="Valor p/ pré-pago:"/>
                                <h:inputText id="valorPacote" value="#{ProdutoControle.pacote.valorPrePago}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" size="10" maxlength="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText styleClass="tituloCampos" value="Valor p/ pós-pago:"/>
                                <h:inputText id="valorPacotePos" value="#{ProdutoControle.pacote.valorPosPago}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" size="10" maxlength="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </h:panelGrid>
                            <a4j:commandButton id="addPacote" action="#{ProdutoControle.adicionarPacote}"
                                               reRender="form"
                                               value="#{msg_bt.btn_adicionar}"
                                               image="./imagens/botaoAdicionar.png" accesskey="7" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="tabelaPacotes" width="100%" headerClass="subordinado"
                                             styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada w20,w20 direita, w20 direita,colunaCentralizada w20"
                                             value="#{ProdutoControle.produtoVO.pacotesPersonal}"
                                             rendered="#{not empty ProdutoControle.produtoVO.pacotesPersonal}"
                                             var="pacote">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Quantidade"/>
                                        </f:facet>
                                        <h:outputText value="#{pacote.quantidade}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor pré-pago"/>
                                        </f:facet>
                                        <h:outputText value="#{pacote.valorPrePago}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor pós-pago"/>
                                        </f:facet>
                                        <h:outputText value="#{pacote.valorPosPago}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                        </f:facet>
                                        <h:panelGroup>
                                            <h:outputText value="    "/>

                                            <a4j:commandButton id="editarPacote" reRender="form"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{ProdutoControle.editarPacote}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png"
                                                               styleClass="botoes"/>

                                            <a4j:commandButton id="removerPacote" reRender="form"
                                                               ajaxSingle="true" immediate="true"
                                                               action="#{ProdutoControle.removerPacote}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>

                        <!---------------------------------->
                    </rich:tab>

                    <rich:tab id="abaComissao" rendered="#{ProdutoControle.apresentarAbaComissao}" style="height:55vh;"
                              styleClass="titulo3SemDecoracao" label="Comissão">
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">
                            <h:panelGrid id="gridComissao" columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">
                                <c:if test="${ProdutoControle.apresentarEmpresaComissao}">
                                    <h:outputText value="Empresa"/>
                                    <h:selectOneMenu id="empresa" onfocus="focusinput(this);" styleClass="form"
                                                     value="#{ProdutoControle.comissaoVO.empresa.codigo}">
                                        <f:selectItems value="#{ProdutoControle.listaEmpresas}"/>
                                    </h:selectOneMenu>
                                </c:if>

                                <h:outputText value="Vigência Inicial"/>
                                <h:panelGroup>
                                    <rich:calendar id="vigenciainicio"
                                                   value="#{ProdutoControle.comissaoVO.vigenciaInicio}"
                                                   enableManualInput="true"
                                                   popup="true"
                                                   inputSize="7"
                                                   datePattern="MM/yyyy"
                                                   showApplyButton="false"
                                                   style="width:200px;"
                                                   inputClass="MMyyyy"
                                                   showFooter="false"/>
                                    <h:message for="vigenciainicio" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="Vigência Final"/>
                                <h:panelGroup>
                                    <rich:calendar id="vigenciafinal"
                                                   value="#{ProdutoControle.comissaoVO.vigenciaFinal}"
                                                   enableManualInput="true"
                                                   popup="true"
                                                   inputSize="7"
                                                   datePattern="MM/yyyy"
                                                   showApplyButton="false"
                                                   style="width:200px"
                                                   inputClass="MMyyyy"
                                                   showFooter="false"/>
                                    <h:message for="vigenciafinal" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="Porcentagem"/>
                                <h:panelGroup>
                                    <h:inputText id="percEsp" size="4" maxlength="4" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ProdutoControle.comissaoVO.porcentagem}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                    <h:message for="percEsp" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText value="Valor Fixo"/>
                                <h:panelGroup>
                                    <h:inputText id="fixoAg" size="4" maxlength="4" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ProdutoControle.comissaoVO.valorFixo}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:inputText>
                                    <h:message for="fixoAg" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                            </h:panelGrid>
                            <a4j:commandButton id="addComissao" value="#{msg_bt.btn_adicionar}"
                                               action="#{ProdutoControle.adicionarComissao}"
                                               image="./imagens/botaoAdicionar.png" accesskey="7" styleClass="botoes"
                                               reRender="gridComissao,tblComissao,pnlMensagem"/>
                        </h:panelGrid>


                        <h:panelGrid id="tblComissao" columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                         value="#{ProdutoControle.produtoVO.comissaoProdutos}" var="comissaoProduto">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Código"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.codigo}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Vl. Fixo (R$)"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.valorFixo_Apresentar}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Porc. (%)"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.porcentagemApresentar}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Vig. Início"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.vigenciaInicio_Apresentar}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Vig. Final"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.vigenciaFinal_Apresentar}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Empresa"/>
                                    </f:facet>
                                    <h:outputText value="#{comissaoProduto.empresa}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Opções"/>
                                    </f:facet>

                                    <a4j:commandButton id="editarItemComissao" reRender="gridComissao,tblComissao"
                                                       ajaxSingle="true" immediate="true"
                                                       action="#{ProdutoControle.editarComissao}"
                                                       style="vertical-align: middle" value="#{msg_bt.btn_editar}"
                                                       image="./imagens/botaoEditar.png" styleClass="botoes"/>

                                    <a4j:commandButton id="removerItemComissao" reRender="gridComissao,tblComissao"
                                                       ajaxSingle="true" immediate="true"
                                                       action="#{ProdutoControle.removerComissao}"
                                                       style="vertical-align: middle" value="#{msg_bt.btn_excluir}"
                                                       image="./imagens/botaoRemover.png" styleClass="botoes"/>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="abaNota" styleClass="titulo3SemDecoracao" label="Nota Fiscal"
                              rendered="#{ProdutoControle.exibirConfiguracoesNFSe || ProdutoControle.exibirConfiguracoesNFCe}">

                        <h:panelGrid id="panelNFSe" columns="2" rowClasses="linhaImpar, linhaPar"
                                     rendered="#{ProdutoControle.exibirConfiguracoesNFSe}"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="Configuração NFS-e"/>
                            </f:facet>

                            <h:outputText value="Configuração Emissão NFS-e:"/>
                            <h:panelGroup layout="block">
                                <h:selectOneMenu id="configEmissaoNFSe" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ProdutoControle.produtoVO.configuracaoNotaFiscalNFSe.codigo}">
                                    <f:selectItems value="#{ProdutoControle.listaConfigEmissaoNFSe}"/>
                                </h:selectOneMenu>

                                <a4j:commandButton id="montarComboConfigEmissaoNFSe"
                                                   action="#{ProdutoControle.montarComboConfigEmissao}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                   reRender="form:panelNFSe"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid id="panelNFCe"
                                     rendered="#{ProdutoControle.exibirConfiguracoesNFCe}"
                                     columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="Configuração NFC-e"/>
                            </f:facet>

                            <h:outputText value="Configuração Emissão NFC-e:"/>
                            <h:panelGroup layout="block">
                                <h:selectOneMenu id="configEmissaoNFCe" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ProdutoControle.produtoVO.configuracaoNotaFiscalNFCe.codigo}">
                                    <f:selectItems value="#{ProdutoControle.listaConfigEmissaoNFCe}"/>
                                </h:selectOneMenu>

                                <a4j:commandButton id="montarComboConfigEmissaoNFCe"
                                                   action="#{ProdutoControle.montarComboConfigEmissao}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                   reRender="form:abaNota"/>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid id="panelConfgNotaFiscalProd" columns="2" rowClasses="linhaImpar, linhaPar"
                                     rendered="#{ProdutoControle.exibirConfiguracoesNFSe || ProdutoControle.exibirConfiguracoesNFCe}"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="Nota Fiscal"/>
                            </f:facet>

                            <h:outputText value="#{msg_aplic.prt_Produto_cfop}"/>
                            <h:inputText id="cfop" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="50" value="#{ProdutoControle.produtoVO.cfop}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_ncm}"
                                          rendered="#{ProdutoControle.exibirConfiguracoesNFSe}"/>
                            <h:inputText id="ncm" onblur="blurinput(this);"
                                         rendered="#{ProdutoControle.exibirConfiguracoesNFSe}"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="10" value="#{ProdutoControle.produtoVO.ncm}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_ncmNFCe}"
                                          rendered="#{ProdutoControle.exibirConfiguracoesNFCe}"/>
                            <h:inputText id="ncmNFCe" onblur="blurinput(this);"
                                         rendered="#{ProdutoControle.exibirConfiguracoesNFCe}"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="50" value="#{ProdutoControle.produtoVO.ncmNFCe}"/>

                            <h:outputText value="CEST:"/>
                            <h:inputText id="cest" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="50" value="#{ProdutoControle.produtoVO.cest}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_codigoListaServico}"/>
                            <h:inputText id="codigoListaServico" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="50"
                                         value="#{ProdutoControle.produtoVO.codigoListaServico}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_codigoTributacaoMunicipio}"/>
                            <h:inputText id="codigoTributacaoMunicipio" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="50"
                                         value="#{ProdutoControle.produtoVO.codigoTributacaoMunicipio}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_descricaoServicoMunicipio}"/>
                            <h:inputText id="descricaoServicoMunicipio" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="40" maxlength="200"
                                         value="#{ProdutoControle.produtoVO.descricaoServicoMunicipio}"/>

                            <h:outputText value="Enviar Percentual Impostos (Federal, Estadual, Municipal):"/>
                            <h:selectBooleanCheckbox id="enviarPercentualImposto"
                                                     value="#{ProdutoControle.produtoVO.enviarPercentualImposto}">
                                <a4j:support event="onclick" reRender="panelConfgNotaFiscalProd"/>
                            </h:selectBooleanCheckbox>

                            <c:if test="${ProdutoControle.produtoVO.enviarPercentualImposto}">
                                <h:outputText value="Percentual Federal:"/>
                                <h:inputText id="percentualFederal"
                                             value="#{ProdutoControle.produtoVO.percentualFederal}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form" size="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText value="Percentual Estadual:"/>
                                <h:inputText id="percentualEstadual"
                                             value="#{ProdutoControle.produtoVO.percentualEstadual}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form" size="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>

                                <h:outputText value="Percentual Municipal:"/>
                                <h:inputText id="percentualMunicipal"
                                             value="#{ProdutoControle.produtoVO.percentualMunicipal}"
                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form" size="10">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                            </c:if>
                        </h:panelGrid>

                        <h:panelGrid id="panelConfgNotaFiscalProdICMS" columns="2" rowClasses="linhaImpar, linhaPar"
                                     rendered="#{ProdutoControle.produtoVO.produtoApresentarIcms}"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="ICMS"/>
                            </f:facet>

                            <h:outputText value="Situação Tributária ICMS:"/>
                            <h:inputText id="situacaoTributariaICMS" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="5"
                                         value="#{ProdutoControle.produtoVO.situacaoTributariaICMS}"/>

                            <h:outputText value="Isento ICMS:"/>
                            <h:selectBooleanCheckbox id="isentoICMS"
                                                     value="#{ProdutoControle.produtoVO.isentoICMS}"/>

                            <h:outputText styleClass="tooltipster" value="Enviar alíquota ICMS no JSON (NFe):"
                                          title="Envia a alíquota do ICMS junto com o JSON do NFe para o enotas" />
                            <h:selectBooleanCheckbox id="enviaAliquotaNFeICMS"
                                                     value="#{ProdutoControle.produtoVO.enviaAliquotaNFeICMS}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_aliquotaICMS}"/>
                            <h:inputText id="aliquotaICMS" value="#{ProdutoControle.produtoVO.aliquotaICMS}"
                                         onkeypress="return formatar_moeda(this,'.',',',event);"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form" size="10">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>

                        </h:panelGrid>

                        <h:panelGrid id="panelConfgNotaFiscalProdCbnef" columns="2" rowClasses="linhaImpar, linhaPar"
                                     rendered="#{ProdutoControle.produtoVO.produtoEstoque}"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="CBNEF"/>
                            </f:facet>

                            <h:outputText value="Código de Benefício Fiscal:" styleClass="tooltipster"
                                          title="O CBNEF será enviado na NFC-e, quando a situação tributária do ICMS for 40(isenta) ou 41(não tributada)"/>
                            <h:inputText id="codigoBeneficioFiscal" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="20"
                                         value="#{ProdutoControle.produtoVO.codigoBeneficioFiscal}"/>

                        </h:panelGrid>

                        <h:panelGrid id="panelConfgNotaFiscalProdISSQN" columns="2" rowClasses="linhaImpar, linhaPar"
                                     rendered="#{!ProdutoControle.produtoVO.produtoEstoque}"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="ISSQN"/>
                            </f:facet>

                            <h:outputText value="Situação Tributária ISSQN:"/>
                            <h:inputText id="situacaoTributariaISSQN" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="5"
                                         value="#{ProdutoControle.produtoVO.situacaoTributariaISSQN}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_aliquotaISSQN}"/>
                            <h:inputText id="aliquotaISSQN" value="#{ProdutoControle.produtoVO.aliquotaISSQN}"
                                         onkeypress="return formatar_moeda(this,'.',',',event);"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form" size="10">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>

                        </h:panelGrid>

                        <h:panelGrid id="panelConfgNotaFiscalProdPIS" columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="PIS"/>
                            </f:facet>


                            <h:outputText value="Situação Tributária PIS:"/>
                            <h:inputText id="situacaoTributariaPIS" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="5"
                                         value="#{ProdutoControle.produtoVO.situacaoTributariaPIS}"/>

                            <h:outputText value="Isento PIS:"/>
                            <h:selectBooleanCheckbox id="isentoPIS"
                                                     value="#{ProdutoControle.produtoVO.isentoPIS}"/>

                            <h:outputText styleClass="tooltipster" value="Enviar alíquota PIS no JSON (NFe):"
                                          title="Envia a alíquota do PIS junto com o JSON do NFe para o enotas" />
                            <h:selectBooleanCheckbox id="enviaAliquotaNFePIS"
                                                     value="#{ProdutoControle.produtoVO.enviaAliquotaNFePIS}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_aliquotaPIS}"/>
                            <h:inputText id="aliquotaPIS" value="#{ProdutoControle.produtoVO.aliquotaPIS}"
                                         onkeypress="return formatar_moeda(this,'.',',',event);"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form" size="10">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>

                        </h:panelGrid>

                        <h:panelGrid id="panelConfgNotaFiscalProdCOFINS" columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="COFINS"/>
                            </f:facet>

                            <h:outputText value="Situação Tributária COFINS:"/>
                            <h:inputText id="situacaoTributariaCOFINS" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         size="10" maxlength="5"
                                         value="#{ProdutoControle.produtoVO.situacaoTributariaCOFINS}"/>

                            <h:outputText value="Isento COFINS:"/>
                            <h:selectBooleanCheckbox id="isentoCOFINS"
                                                     value="#{ProdutoControle.produtoVO.isentoCOFINS}"/>

                            <h:outputText styleClass="tooltipster" value="Enviar alíquota COFINS no JSON (NFe):"
                                          title="Envia a alíquota do COFINS junto com o JSON do NFe para o enotas" />
                            <h:selectBooleanCheckbox id="enviaAliquotaNFeCOFINS"
                                                     value="#{ProdutoControle.produtoVO.enviaAliquotaNFeCOFINS}"/>

                            <h:outputText value="#{msg_aplic.prt_Produto_aliquotaCOFINS}"/>
                            <h:inputText id="aliquotaCOFINS" value="#{ProdutoControle.produtoVO.aliquotaCOFINS}"
                                         onkeypress="return formatar_moeda(this,'.',',',event);"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form" size="10">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>

                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="abaVendasOnline" styleClass="titulo3SemDecoracao" label="Vendas Online"
                              rendered="#{ProdutoControle.exibirAbaVendasOnline && LoginControle.apresentarVendas && LoginControle.permissaoAcessoMenuVO.vendasOnline}">

                        <h:panelGrid id="panelVendasOnline" columns="2" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="Configuração Vendas Online"/>
                            </f:facet>

                            <h:outputText styleClass="tituloCampos" value="Apresentar no Pacto App:"/>
                            <h:selectBooleanCheckbox id="apresentarPactoApp" styleClass="tituloCampos"
                                                     value="#{ProdutoControle.produtoVO.apresentarPactoApp}">
                            </h:selectBooleanCheckbox>

                            <h:outputText styleClass="tituloCampos" value="Apresentar no Vendas Online:"/>
                            <h:selectBooleanCheckbox id="apresentarVendasOnline" styleClass="tituloCampos"
                                                     value="#{ProdutoControle.produtoVO.apresentarVendasOnline}">
                                <a4j:support event="onchange" reRender="form"
                                             oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                            </h:selectBooleanCheckbox>

                            <h:outputText styleClass="tituloCampos" value="Apresentar no Pacto Flow:"/>
                            <h:selectBooleanCheckbox id="apresentarPactoFlow" styleClass="tituloCampos"
                                                     value="#{ProdutoControle.produtoVO.apresentarPactoFlow}">
                            </h:selectBooleanCheckbox>

                            <c:if test="${ProdutoControle.produtoVO.apresentarVendasOnline}">
                                <h:outputText styleClass="tituloCampos" value="Quantidade máxima de parcelas:"/>
                                <h:panelGroup layout="block">
                                    <h:inputText id="maxDivisao" size="4" maxlength="2"
                                                 styleClass="form tooltipster"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 onkeypress="return mascara(this.form, this.id, '99999999', event);"
                                                 onkeyup="somenteNumeros(this);"
                                                 title="Quantidade máxima de parcelas"
                                                 value="#{ProdutoControle.produtoVO.maxDivisao}"/>
                                    <h:outputText styleClass="tituloCampos"
                                                  style="padding-left: 5px"
                                                  value="(\"À vista\" - Informe 1)"/>
                                </h:panelGroup>
                            </c:if>


                            <h:outputText styleClass="tituloCampos" value="Modalidade:"
                                          rendered="#{ProdutoControle.produtoVO.apresentarVendasOnline and ProdutoControle.diaria}"/>
                            <h:panelGroup layout="block" rendered="#{ProdutoControle.produtoVO.apresentarVendasOnline and ProdutoControle.diaria}">
                                <h:selectOneMenu value="#{ProdutoControle.produtoVO.modalidadeVendasOnline}" id="modalidadeVenda">
                                    <f:selectItems value="#{ProdutoControle.listaSelectItemModalidades}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%"
                                     rendered="#{ProdutoControle.produtoVO.apresentarVendasOnline}"
                                     headerClass="subordinado">

                            <f:facet name="header">
                                <h:outputText value="Imagens"/>
                            </f:facet>

                            <h:panelGroup id="painelfotos" layout="block" style="margin: 20px;">

                                <div style="display: grid; justify-content: center;">
                                    <rich:fileUpload listHeight="0" listWidth="100" noDuplicate="false"
                                                     fileUploadListener="#{ProdutoControle.uploadImagem}"
                                                     maxFilesQuantity="1"
                                                     addControlLabel="Adicionar" cancelEntryControlLabel="Cancelar"
                                                     doneLabel="Pronto"
                                                     sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 10 MB"
                                                     progressLabel="Enviando" stopControlLabel="Parar"
                                                     uploadControlLabel="Enviar"
                                                     transferErrorLabel="Falha de Transmissão"
                                                     stopEntryControlLabel="Parar"
                                                     id="uploadAssinaturaDigital" immediateUpload="true"
                                                     autoclear="true"
                                                     acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                                        <a4j:support event="onuploadcomplete" reRender="painelfotos"
                                                     oncomplete="sleep(2000);"/>
                                        <a4j:support event="oncomplete" reRender="painelfotos"
                                                     oncomplete="sleep(2000);"/>
                                    </rich:fileUpload>
                                </div>

                                <div class="caixaResolucao">
                                    <h:outputText escape="false" value="Melhor resolução: 250x200"/>
                                </div>

                                <a4j:repeat var="imagem" value="#{ProdutoControle.listaImagens}">
                                    <h:panelGroup layout="block" styleClass="imagemacademia">
                                        <h:graphicImage url="#{imagem.urlFoto}"/>
                                        <div style="text-align: center;margin-top: 15px">
                                            <a4j:commandLink style="font-size: 20px;"
                                                             action="#{ProdutoControle.removerImagem}"
                                                             reRender="painelfotos">
                                                <i class="fa-icon-trash"></i>
                                            </a4j:commandLink>
                                        </div>
                                    </h:panelGroup>
                                </a4j:repeat>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="abaMesclado" styleClass="titulo3SemDecoracao" label="Mesclagem" rendered="#{ProdutoControle.getProdutoVO().getCodigo()!=null && ProdutoControle.getProdutoVO().getCodigo()>0}">

                        <h:panelGrid id="panelMesclado" columns="1" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="classEsquerda, classDireita" width="100%"
                                     headerClass="subordinado">

                            <h:panelGrid id="gridProdutoMescladoItens" columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">
                                <f:facet name="header">
                                    <h:outputText styleClass="tituloCampos" value="Mesclagem de produtos"/>
                                </f:facet>

                                <h:outputText   value="Produto" />
                                <h:panelGroup>
                                    <h:inputText  id="nomeProdutoMescladoSelecionado"
                                                  size="50"
                                                  maxlength="50"
                                                  title="Para pesqusar, informe o código ou nome do produto."
                                                  onblur="blurinput(this);"
                                                  onfocus="focusinput(this);"
                                                  styleClass="form"
                                                  value="#{ProdutoControle.produtoMescladoSelecionado}" />

                                    <rich:suggestionbox   height="200" width="400"
                                                          for="nomeProdutoMescladoSelecionado"
                                                          status="statusInComponent"
                                                          immediate="true"
                                                          suggestionAction="#{ProdutoControle.executarAutocompletePesqProdutoMesclado}"
                                                          minChars="1"
                                                          reRender="panelMensagemErro"
                                                          rowClasses="linhaImpar, linhaPar"
                                                          var="result"  id="suggestionResponsavel">
                                        <a4j:support event="onselect"
                                                     reRender="form, panelMensagemErro"
                                                     action="#{ProdutoControle.selecionarProdutoMesclado}">
                                        </a4j:support>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Nome"  styleClass="textverysmall"/>
                                            </f:facet>
                                            <h:outputText styleClass="textverysmall" value="#{result.descricao}" />
                                        </h:column>
                                        <h:column >
                                            <f:facet name="header">
                                                <h:outputText value="Categoria" styleClass="textverysmall"/>
                                            </f:facet>
                                            <h:outputText  styleClass="textverysmall" value="#{result.categoriaProduto.descricao}" />
                                        </h:column>
                                    </rich:suggestionbox>
                                </h:panelGroup>


                            </h:panelGrid>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="tbProdutoMesclado" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="esquerda, centralizado, direita,direita,direita,centralizado"
                                             value="#{ProdutoControle.listaProdutoMescladoVO}" var="produtoMescladoItem">

                                    <h:column >
                                        <f:facet name="header">
                                            <h:outputText  value="Produto" />
                                        </f:facet>
                                        <h:outputText  value="#{produtoMescladoItem.descricao}" />
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText   value="Valor " />
                                        </f:facet>
                                        <h:outputText  value="#{produtoMescladoItem.getValorFinal_Apresentar()}" />
                                    </h:column>

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="Estoque" />
                                        </f:facet>
                                        <h:outputText   value="#{ProdutoControle.quantidadeEstoquePorProduto(produtoMescladoItem.codigo)}" />
                                    </h:column>


                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>

                                        <h:panelGroup  >
                                            <h:outputText value="    "/>
                                            <a4j:commandButton  reRender="form, gridProdutoMescladoItens, panelMensagemErro"  id="removerProdutoMescladoItem" immediate="true" action="#{ProdutoControle.removerProdutoMesclado}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                                   </h:panelGroup>
                                    </h:column>

                                </h:dataTable>
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                <h:outputText value="#{msg.msg_atencao_mesclagem}" styleClass="mensagemAvisoPequena"/>

                                <a4j:commandButton id="botaoSalvarProdutosMesclados"
                                                   action="#{ProdutoControle.salvarProdutosMesclados}"
                                                   value="Salvar produtos mesclados"
                                                   title="Salvar produtos mesclados"
                                                   reRender="form"
                                                   styleClass="botoes nvoBt"/>
                            </h:panelGrid>

                        </h:panelGrid>

<!-- fim mesclado -->



                    </rich:tab>
                    <c:if test="${ProdutoControle.exibirReplicarRedeEmpresa}">
                        <rich:tab id="tabReplicarEmpresasProduto" label="Replicar Empresas" rendered="#{ProdutoControle.exibirReplicarRedeEmpresa}">
                            <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_PlanoReplicarEmpresa_tituloForm}"/>
                                </f:facet>
                                <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaProduto"
                                             columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                             width="100%">
                                    <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="Não Replicadas" styleClass="botoes nvoBt"/>
                                    <h:outputText value="#{ProdutoControle.listaProdutoRedeEmpresaSize}"
                                                  style="font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText value="#{ProdutoControle.listaProdutoRedeEmpresaSincronizado}"
                                                  style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
                                    <h:outputText
                                            value="#{ProdutoControle.listaProdutoRedeEmpresaSize - ProdutoControle.listaProdutoRedeEmpresaSincronizado}"
                                            style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
                                </h:panelGrid>
                                <h:panelGrid columns="1" id="contadorReplicaProduto2"
                                             columnClasses="colunaDireita"
                                             width="100%"
                                             style="margin-top: 20px; margin-bottom: 1px">
                                    <h:panelGroup layout="block">
                                        <a4j:commandButton value="Replicar Todas" styleClass="botoes nvoBt"
                                                           action="#{ProdutoControle.replicarTodas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaProduto"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Replicar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{ProdutoControle.replicarSelecionadas}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaProduto"
                                                           ajaxSingle="true" immediate="true"/>
                                        <a4j:commandButton value="Limpar Selecionadas" styleClass="botoes nvoBt btSec"
                                                           action="#{ProdutoControle.limparReplicar}"
                                                           reRender="listaEmpresasReplicar, contadorReplicaProduto"
                                                           ajaxSingle="true" immediate="true"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="colunaCentralizada" width="100%">

                                    <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                                                 styleClass="tabFormSubordinada"
                                                 rowClasses="linhaImpar, linhaPar"
                                                 columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                                                 style="text-align: center;"
                                                 value="#{ProdutoControle.listaProdutoRedeEmpresa}"
                                                 var="produtoRedeEmpresaReplicacao">
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:selectBooleanCheckbox id="check" styleClass="form"
                                                                     rendered="#{!produtoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                     value="#{produtoRedeEmpresaReplicacao.selecionado}">
                                                <a4j:support event="onchange" reRender="listaEmpresasReplicar"/>
                                            </h:selectBooleanCheckbox>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                                            </f:facet>
                                            <h:outputText value="#{produtoRedeEmpresaReplicacao.nomeUnidade}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                                            </f:facet>
                                            <h:outputText value="#{produtoRedeEmpresaReplicacao.chave}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value=""/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton id="replicarProduto"
                                                                   reRender="listaEmpresasReplicar, contadorReplicaProduto"
                                                                   ajaxSingle="true" immediate="true"
                                                                   rendered="#{!produtoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                                   action="#{ProdutoControle.replicarProdutoRedeEmpresaGeral}"
                                                                   value="Replicar"/>
                                                <h:graphicImage url="./images/check.png"
                                                                rendered="#{produtoRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                                            </h:panelGroup>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                                            </f:facet>
                                            <h:outputText value="#{produtoRedeEmpresaReplicacao.mensagemSituacao}"/>
                                        </h:column>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText
                                                        value="Vínculo"/>
                                            </f:facet>
                                            <h:panelGroup>
                                                <a4j:commandButton
                                                        rendered="#{produtoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                        reRender="listaEmpresasReplicar, contadorReplicaProduto"
                                                        ajaxSingle="true" immediate="true"
                                                        action="#{ProdutoControle.retirarVinculoReplicacao}"
                                                        value="Retirar"/>
                                            </h:panelGroup>
                                        </h:column>

                                    </h:dataTable>
                                </h:panelGrid>
                            </h:panelGrid>
                        </rich:tab>
                    </c:if>


                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="pnlMensagem" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton id="icProdutoSuc" rendered="#{ProdutoControle.sucesso}"
                                         image="./imagens/sucesso.png"/>
                        <h:commandButton id="icProdutoFal" rendered="#{ProdutoControle.erro}"
                                         image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgProduto" styleClass="mensagem" value="#{ProdutoControle.mensagem}"/>
                            <h:outputText id="msgProdutoDet" styleClass="mensagemDetalhada"
                                          value="#{ProdutoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{ProdutoControle.novo}"
                                             value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                             styleClass="botoes nvoBt btSec"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                            <h:outputText value="    "/>
                            <h:commandButton id="salvar" action="#{ProdutoControle.gravar}" value="#{msg_bt.btn_gravar}"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica" onclick="fireElementFromAnyParent('form:btnAtualizaTempo');"
                                                   oncomplete="#{ProdutoControle.msgAlert}" action="#{ProdutoControle.confirmarExcluir}"   value="#{msg_bt.btn_excluir}"
                                                   alt="#{msg.msg_excluir_dados}" accesskey="3"
                                                   styleClass="botoes nvoBt btSec" style="margin-right: 5px;" >
                                    <f:param name="metodochamar" value="excluir"/>
                                </a4j:commandButton>
                            </h:panelGroup>

                            <h:outputText value="    "/>
                            <h:commandButton id="consultar" immediate="true"
                                             action="#{ProdutoControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                             accesskey="4" styleClass="botoes nvoBt btSec"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink
                                    action="#{ProdutoControle.realizarConsultaLogObjetoSelecionado}"
                                    reRender="form"
                                    oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo');abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                    title="Visualizar Log"
                                    style="display: inline-block; padding: 8px 15px;"
                                    styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')"/>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
    jQuery('.tooltipster').tooltipster({
        theme: 'tooltipster-light',
        position: 'bottom',
        animation: 'grow',
        contentAsHTML: true
    });

    function copiar(copyText) {
        var el = document.createElement('textarea');
        el.value = copyText;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        Notifier.info('Link copiado para a área de transferância.');
    }

    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }
</script>

<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<script type="text/javascript" language="javascript" src="./script/ce_script.js"></script>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
<title>
    <h:outputText value="Manutenção Modalidade"/>
</title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Manutenção de Modalidades"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-mudar-as-modalidades-de-um-aluno-sem-precisar-vender-um-novo-plano-manutencao-de-modalidade/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>

    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
<h:form id="form">
    <h:panelGrid id="panelGeral" columns="1" columnClasses="w30,w70" width="100%">

        <h:panelGrid columns="1" width="100%" styleClass="font-size-em-max">
            <h:panelGrid id="panelModalidade" columns="1" width="100%" styleClass="font-size-em-max">
                <h:panelGrid rendered="#{!empty ManutencaoModalidadeControle.listaModalidadeAdicionada}" columns="1" width="100%">
                    <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                        <f:facet name="header">
                            <h:outputText id="ttModAdicionadas" styleClass="tituloCamposAzul" style="font-weight: bold"
                                          value="Modalidade(s) Adicionada(s)"/>
                        </f:facet>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada font-size-em-max">
                        <h:dataTable id="modalidadeAdicionada" width="100%" rowClasses="linhaImpar, linhaPar"
                                     headerClass="subordinado" columnClasses="textsmall"
                                     value="#{ManutencaoModalidadeControle.listaModalidadeAdicionada}"
                                     styleClass="tabFormSubordinada" var="contratoModalidade">
                            <h:column>
                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta" style="font-weight: bold" value="Modalidade: "/>
                                <h:outputText id="modalidadeAdicionada" styleClass="tituloCamposAzulMedio"
                                              value="#{contratoModalidade.modalidade.nome}"/>
                            </h:column>
                            <h:column>
                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                                              value="Número de Vezes por Semana: "/>
                                <h:outputText id="numeroVezesMod" styleClass="tituloCamposAzulMedio"
                                              value="#{contratoModalidade.nrVezesSemana} "/>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid rendered="#{!empty ManutencaoModalidadeControle.listaModalidadeAlterada}" columns="1" width="100%" styleClass="font-size-em-max">
                    <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                        <f:facet name="header">
                            <h:outputText id="ttModAlteradas" styleClass="texto-size-14-real texto-font texto-bold tituloCaixaAlta"
                                          value="Modalidade(s) Alterada(s)"/>
                        </f:facet>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada font-size-em-max" >
                        <h:dataTable id="modalidadeAlterada" width="100%" rowClasses="linhaImpar, linhaPar"
                                     headerClass="subordinado" columnClasses="textsmall"
                                     value="#{ManutencaoModalidadeControle.listaModalidadeAlterada}"
                                     styleClass="tabFormSubordinada" var="contratoModalidade">
                            <h:column>
                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"  value="Modalidade: "/>
                                <h:outputText id="modAlterada" styleClass="texto-size-14-real texto-font texto-bold tituloCaixaAlta"
                                              value="#{contratoModalidade.modalidade.nome}"/>
                            </h:column>
                            <h:column>
                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                                              value="Número de Vezes por Semana: "/>
                                <h:outputText id="vezesModAlterada" styleClass="tituloCamposAzulMedio"
                                              value="#{contratoModalidade.nrVezesSemana} "/>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid rendered="#{!empty ManutencaoModalidadeControle.listaModalidadeExcluida}" columns="1" width="100%" styleClass="font-size-em-max">
                    <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                        <f:facet name="header">
                            <h:outputText id="ttModExcluidas" styleClass="texto-size-14 texto-font texto-bold tituloCaixaAlta"
                                          value="Modalidade(s) Excluída(s)"/>
                        </f:facet>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada font-size-em-max">
                        <h:dataTable id="modalidadeExcluida" width="100%" rowClasses="linhaImpar, linhaPar"
                                     headerClass="subordinado" columnClasses="textsmall"
                                     value="#{ManutencaoModalidadeControle.listaModalidadeExcluida}"
                                     styleClass="tabFormSubordinada" var="contratoModalidade">
                            <h:column>
                                <h:outputText styleClass="tituloCamposAzulMedio" style="font-weight: bold" value="Modalidade: "/>
                                <h:outputText id="modExcluida" styleClass="tituloCamposAzulMedio"
                                              value="#{contratoModalidade.modalidade.nome}"/>
                            </h:column>
                            <h:column>
                                <h:outputText styleClass="tituloCamposAzulMedio" style="font-weight: bold"
                                              value="Número de Vezes por Semana: "/>
                                <h:outputText id="vezesModExcluida" styleClass="tituloCamposAzulMedio"
                                              value="#{contratoModalidade.nrVezesSemana} "/>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid rendered="#{ManutencaoModalidadeControle.apresentarDadosNovoModalidade}" styleClass="font-size-em-max">
                    <h:panelGroup rendered="#{ManutencaoModalidadeControle.apresentarDepositoEmConta}">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta" rendered="#{ManutencaoModalidadeControle.mostrarAlterarValor}"
                                      value=" Valor original da manutenção a creditar ao aluno:"/>
                        
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta" rendered="#{!ManutencaoModalidadeControle.mostrarAlterarValor}"
                                      value=" Valor a depositar na conta corrente ou abater nas parcelas em aberto do aluno:"/>
                        
                        <h:outputText styleClass="tituloCamposVerdeGrande" value=" R$ "/>
                        <h:outputText id="valorDevolucao" styleClass="tituloCamposVerdeGrande"
                                      value="#{ManutencaoModalidadeControle.valorTotalAlteracaoModalidade}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{ManutencaoModalidadeControle.apresentarParcela}">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                        			  rendered="#{ManutencaoModalidadeControle.mostrarAlterarValor}" 
                        			  value="Valor original da manutenção de modalidade: "/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                                      rendered="#{!ManutencaoModalidadeControle.mostrarAlterarValor && ManutencaoModalidadeControle.apresentarMsgLiberacao}" 
                        			  value="Valor liberado para o cliente: "/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                        			  rendered="#{!ManutencaoModalidadeControle.mostrarAlterarValor && !ManutencaoModalidadeControle.apresentarMsgLiberacao}" 
                        			  value="Valor da parcela a ser gerada: "/>
                        			  
                        <h:outputText styleClass="tituloCamposVerdeGrande" value=" R$ "/>
                        <h:outputText id="valorQuitacao" styleClass="tituloCamposVerdeGrande"
                                      value="#{ManutencaoModalidadeControle.valorTotalAlteracaoModalidade}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>

                    </h:panelGroup>

                </h:panelGrid>
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

                <h:selectOneRadio id="opcoesAlterarValor" styleClass="texto-size-14 texto-cor-cinza texto-font  tituloCaixaAlta"
                				  rendered="#{ManutencaoModalidadeControle.mostrarTiposDesconto && !ManutencaoModalidadeControle.processandoOperacao}"
                                  value="#{ManutencaoModalidadeControle.opcaoAlterarValor}">
                    <f:selectItems value="#{ManutencaoModalidadeControle.opcoesAlterarValor}"/>
                    <a4j:support event="onclick" action="#{ManutencaoModalidadeControle.alterarOpcaoAlterarValor}"
                                 reRender="panelGeral,panelAutorizacaoFuncionalidade,panelModalidade"
                                 oncomplete="#{ManutencaoModalidadeControle.msgAlert}"/>
                </h:selectOneRadio>
                <rich:spacer height="10px"/>
                <h:panelGroup id="tipoDesconto" rendered="#{ManutencaoModalidadeControle.apresentarTiposDesconto}">
                    <h:selectOneRadio id="opcoesDesconto" styleClass="texto-size-14 texto-cor-cinza texto-font  tituloCaixaAlta" value="#{ManutencaoModalidadeControle.tipoDesconto}">
                        <f:selectItems value="#{ManutencaoModalidadeControle.opcoesDesconto}"/>
                        <a4j:support event="onclick" action="#{ManutencaoModalidadeControle.alterarOpcaoDesconto}" reRender="form:panelGeral"/>
                    </h:selectOneRadio>


                    <h:panelGroup id="desconto" rendered="#{ManutencaoModalidadeControle.apresentarCampoValorDesconto}">

                        <h:inputText rendered="#{ManutencaoModalidadeControle.tipoDesconto eq ('Valor' or 'Porcentagem') }"
                                     id="valorDesconto" size="15" value="#{ManutencaoModalidadeControle.desconto}"
                                     onkeypress="return formatar_moeda(this,'.',',',event);" maxlength="10"
                                     onfocus="focusinput(this);" styleClass="inputTextClean" style="margin-left: 8px">
                            <f:converter converterId="FormatadorNumerico"/>
                            <a4j:support event="onblur" action="#{ManutencaoModalidadeControle.calcularValorFinal}" ajaxSingle="true" reRender="form:panelGeral"/>
                        </h:inputText>
                        <rich:spacer width="10px"/>
                        <a4j:commandLink id="aplicarDesconto" value="Aplicar Desconto"
                                           action="#{ManutencaoModalidadeControle.calcularValorFinal}" ajaxSingle="true"
                                           reRender="form:panelGeral" styleClass="pure-button"/>

                    </h:panelGroup>
                </h:panelGroup>
                <rich:spacer height="10px"/>
                 <h:panelGroup id="panelvalorNovo" rendered="#{ManutencaoModalidadeControle.mostrarCampoAlterarValor}">
						<h:inputText id="valorNovo" size="8" value="#{ManutencaoModalidadeControle.valorAlterado}"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="inputTextClean" style="margin-left: 8px">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>
                     <rich:spacer width="10px"/>
                     <a4j:commandLink id="aplicarvalorNovo" value="Aplicar Novo Valor"
                                           action="#{ManutencaoModalidadeControle.aplicarValorNovo}"
                                           reRender="form:panelGeral"
                                        styleClass="pure-button"/>
                </h:panelGroup>
                <rich:spacer height="10px"/>
                <h:panelGrid rendered="#{ManutencaoModalidadeControle.apresentarValorFinalModificado}" styleClass="font-size-em-max">
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="Valor da parcela a ser gerada com descontos: "/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value=" R$ "/>
                        <h:outputText id="valorQuitacaoComDesconto" styleClass="tituloCamposVerdeGrande"
                                      value="#{ManutencaoModalidadeControle.valorFinal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGrid>
                
                <h:panelGrid rendered="#{ManutencaoModalidadeControle.mostrarAlterarValor}" styleClass="font-size-em-max">
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                        			  rendered="#{!ManutencaoModalidadeControle.mostrarCampoAlterarValor && !ManutencaoModalidadeControle.apresentarDepositoEmConta}" 
                        			  value="Valor da parcela gerada: "/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                        			  rendered="#{ManutencaoModalidadeControle.mostrarCampoAlterarValor && !ManutencaoModalidadeControle.apresentarDepositoEmConta}" 
                        			  value="Valor com alteração da parcela a ser gerada: "/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                        			  rendered="#{ManutencaoModalidadeControle.mostrarCampoAlterarValor && ManutencaoModalidadeControle.apresentarDepositoEmConta}" 
                        			  value="Valor com alteração a creditar ao aluno: "/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                        			  rendered="#{!ManutencaoModalidadeControle.mostrarCampoAlterarValor && ManutencaoModalidadeControle.apresentarDepositoEmConta}" 
                        			  value="Valor creditado ao aluno: "/>			  
                        <h:outputText styleClass="tituloCamposVerdeGrande" value=" R$ "/>
                        <h:outputText id="valorAlteradoFinal" styleClass="tituloCamposVerdeGrande"
                                      value="#{ManutencaoModalidadeControle.valorFinal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="3" width="100%" styleClass="font-size-em-max">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText value=" "/>
                    </h:panelGrid>
                    <h:commandButton rendered="#{ManutencaoModalidadeControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{ManutencaoModalidadeControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgManutencaoMod" styleClass="mensagem"
                                      value="#{ManutencaoModalidadeControle.mensagem}"/>
                        <h:outputText id="msgManutencaoModDet" styleClass="mensagemDetalhada"
                                      value="#{ManutencaoModalidadeControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid rendered="#{ManutencaoModalidadeControle.apresentarBotao && !ManutencaoModalidadeControle.processandoOperacao}">
                    <h:outputText styleClass="mensagemDetalhada texto-size-14-real texto-font"
                                  value="Atenção !!! A alteração de modalidades pode impactar no pagamento de Comissão de Professores!">
                        <h:outputLink
                                value="#{SuperControle.urlBaseConhecimento}como-mudar-as-modalidades-de-um-aluno-sem-precisar-vender-um-novo-plano-manutencao-de-modalidade/"
                                title="Clique e saiba mais: Manutenção de Modalidade" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <h:panelGrid id="panelBotoes" columns="2" columnClasses="colunaCentralizada">
                    <h:panelGroup rendered="#{ManutencaoModalidadeControle.apresentarBotao && !ManutencaoModalidadeControle.processandoOperacao }">
                        <a4j:commandLink id="voltar" action="#{ManutencaoModalidadeControle.voltar}"
                                         styleClass="pure-button">
                            <i class="fa-icon-chevron-left"></i>&nbsp;Voltar
                        </a4j:commandLink>
                        <rich:spacer width="7"/>
						<a4j:commandLink  id="confirmar" action="#{ManutencaoModalidadeControle.gravar}"
                            reRender="form,panelAutorizacaoFuncionalidade"
                            oncomplete="#{ManutencaoModalidadeControle.msgAlert}"
                            styleClass="pure-button pure-button-primary">
                        <i class="fa-icon-ok"></i>&nbsp;Confirmar
                        </a4j:commandLink>

                        <rich:spacer width="7"/>
                        <a4j:commandLink styleClass="pure-button" id="cancelar" onclick="fecharJanela();executePostMessage({close: true});">
                            <i class="fa-icon-remove"></i>&nbsp;Cancelar
                        </a4j:commandLink>

                    </h:panelGroup>
                    <h:panelGroup rendered="#{!ManutencaoModalidadeControle.apresentarBotao && !ManutencaoModalidadeControle.processandoOperacao}">
                        <rich:spacer width="7"/>
                        <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});"
                                         styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-remove"></i>&nbsp;Fechar
                        </h:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid rendered="#{ManutencaoModalidadeControle.processandoOperacao}" columns="1">
                    <rich:spacer height="7"/>
                    <a4j:commandLink id="atualizar" title="fechar" onclick="fecharJanela();executePostMessage({close: true});"  styleClass="pure-button pure-button-primary">
                        <i class="fa-icon-remove"></i>&nbsp;Fechar
                    </a4j:commandLink>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:panelGrid>
</h:form>
<%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>

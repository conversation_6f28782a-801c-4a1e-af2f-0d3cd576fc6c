<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style>
    .tabelaProduto tbody tr td:nth-child(6){
        text-align: right;
    }
</style>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title style="text-transform: capitalize;">Log Total Pass de ${ClienteControle.clienteVO.pessoa.nomeMinusculo}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="Log Total Pass - ${ClienteControle.clienteVO.pessoa.nomeMinusculo}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblLogTotalPassAcessos" class="tabelaLogTotalPassAcessos pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                        <th>Dt.Registro</th>
                        <th>Origem</th>
                        <th>Tempo de resposta</th>
                        <th>Resposta</th>
                        <th>Status</th>
                        <th>Uri</th>
                        <th>ApiKey</th>
                        <th>Json</th>
                        <th>Tipo</th>
                        <th>Ip.</th>
                        <th>Resposta API</th>
                    </thead>
                    <tbody></tbody>
                </table>

                

            </h:panelGroup>
            <%-- FIM CONTENT --%>

        </h:form>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaLogTotalPassAcessos", "${contexto}/prest/logTotalPasscliente?cliente=${ClienteControle.pessoaVO.codigo}", 0, "desc", "", true, null, null, null, true, [], null, [[5, 4], [7, 6], [10, 9]]);
        });
    </script>


</f:view>

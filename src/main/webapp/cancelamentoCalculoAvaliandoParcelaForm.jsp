<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
    setTimeout(setInterval(function () {
        getDocumentCookie('popupsImportante') == 'close' ? this.close() : '';
    }, 500), 500);
</script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .tabelaCancelamento > tbody > tr > td:nth-child(2) {
        width: 100%;
    }

    .pure-button {
        font-family: sans-serif;
        font-size: 100%;
        margin: 0;
        vertical-align: baseline;
        *vertical-align: middle
    }

    .to-uper-case {
        text-transform: uppercase;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        Cancelamento
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"/>
        <c:set var="titulo" scope="session" value="Cancelamento"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>


    <div styleClass="col-md-12">
        <hr class="dividerFundoClaro"/>
    </div>
    <h:form id="form">
        <h:panelGrid columns="2" id="painelCancelamento" styleClass="tabelaCancelamento" width="100%">
            <h:panelGrid columns="1" width="100%"
                         style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <%--<h:outputText value="NOME DO CLIENTE: "--%>
                <%--styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>--%>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                              value="#{ClienteControle.clienteVO.matricula} - #{ClienteControle.clienteVO.pessoa.nome} - #{ClienteControle.clienteVO.empresa_Apresentar}"/>

                <br>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                              value="Solicitação de cancelamento"/>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              value="- Data da solicitação de cancelamento: #{CancelamentoContratoControle.dataAtual}"/>

                <%--                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"--%>
                <%--                              value="- Acesso até o dia #{CancelamentoContratoControle.cancelamentoContratoVO.dataCancelamentoApresentar}."/>--%>

                <br>

                <c:if test="${not empty CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.listaParcelasVencidas}">

                    <h:outputText
                            styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                            style="color: red"
                            value="Parcelas Vencidas:"/>

                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                                  value="Todas as parcelas vencidas devem ser cobradas, conforme previsto no termo de matrícula."/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.valorParcelaValoresEmAtraso_Apresentar} referente ao valor das parcelas em atraso: "/>


                    <rich:dataTable id="listaParcelasVencidas" width="100%" border="0" cellspacing="0"
                                    columnClasses="col-text-align-left" headerClass="col-text-align-left"
                                    styleClass="tabelaSimplesCustom"
                                    value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.listaParcelasVencidas}"
                                    var="parcela">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              value="CÓDIGO"/>
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{parcela.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              value="DESCRIÇÃO"/>
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{parcela.descricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              value="DATA VENCIMENTO"/>
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{parcela.dataVencimento}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              value="VALOR"/>
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{parcela.valorParcela}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </rich:column>

                        <c:if test="${CancelamentoContratoControle.contratoVO.empresa.aplicarMultaeJurosNoCancelamentoAutomatico}">
                            <rich:column style="text-align: right !important;">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                  value="MULTA E JUROS"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{parcela.valorMultaJuros}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>

                            <rich:column style="text-align: right !important;">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                  value="VALOR TOTAL"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{parcela.valorParcela + parcela.valorMultaJuros}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>
                        </c:if>
                    </rich:dataTable>
                    <br>
                </c:if>

                <%--                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"--%>
                <%--                              value="Próximas cobranças:"/>--%>
                <%--                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"--%>
                <%--                              value="Serão efetuadas as cobranças futuras do pró-rata da taxa de manutenção anual,--%>
                <%--                              última mensalidade e eventuais débitos pendentes até a data do efetivo cancelamento,--%>
                <%--                              conforme previsto no termo de matrícula."/>--%>

                <br>

                <%--                <c:if test="${!CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.naoCobrarMulta}">--%>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                              value="Multa Cancelamento:"/>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.contratoVO.empresa.aplicarMultaSobreValorTotalContrato}"
                              value="Valor restante das parcelas em aberto: #{CancelamentoContratoControle.cancelamentoContratoVO.valorBaseCalculoMultaCancelamento_Apresentar} - #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.quantidadeParcelasRestanteEmAberto}."/>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.contratoVO.empresa.aplicarMultaSobreValorTotalContrato}"
                              value="Será cobrado uma multa de #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.valorMulta_Apresentar}, referente à #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.porcentagemMulta_Apresentar}% sobre o valor restante do contrato."/>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.contratoVO.empresa.aplicarMultaSobreValorTotalContrato}"
                              value="Valor total do contrato: #{CancelamentoContratoControle.cancelamentoContratoVO.valorBaseCalculoMultaCancelamento_Apresentar}"/>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.contratoVO.empresa.aplicarMultaSobreValorTotalContrato}"
                              value="Será cobrado uma multa de #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.valorMulta_Apresentar}, referente à #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.porcentagemMulta_Apresentar}% sobre o valor total do contrato."/>

                <br>

                <%--                </c:if>--%>

                <%--                <c:if test="${CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.cobrarAnuidade == 'S' && CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.cobrarAnuidadeTotal}">--%>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                              value="Anuidade:"/>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.valorAnuidade_Apresentar} - Referente a anuidade, a ser cobrada no dia #{CancelamentoContratoControle.cancelamentoContratoVO.dataCancelamentoApresentar}."/>
                <br>
                <%--                </c:if>--%>


                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                              rendered="#{not empty CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.transacoes && CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.contratoVO.empresa.cancelamentoApresentarTransacoes}"
                              value="Transações:"/>
                <rich:dataTable id="listaTransacoes" width="100%" border="0" cellspacing="0"
                                columnClasses="col-text-align-left" headerClass="col-text-align-left"
                                styleClass="tabelaSimplesCustom"
                                rendered="#{not empty CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.transacoes && CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.contratoVO.empresa.cancelamentoApresentarTransacoes}"
                                value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoAvaliandoParcelaTO.transacoes}"
                                var="transacao">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          value="CÓDIGO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{transacao.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          value="DATA"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{transacao.dataProcessamento_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          value="VALOR"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{transacao.valor_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>
                <br>


                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                              rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                              value="#{msg_aplic.prt_JustificativaOperacao_consultarJustificativa}"/>
                <h:panelGroup styleClass="font-size-em-max"
                              rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}">
                    <div class="cb-container margenVertical" style="width: 300px;">
                        <h:selectOneMenu id="tipoOperacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         style="width: 300px;"
                                         value="#{CancelamentoContratoControle.cancelamentoContratoVO.tipoJustificativaOperacao}">
                            <f:selectItems value="#{CancelamentoContratoControle.listaSelectItemJustificativaOperacao}"/>
                            <a4j:support event="onchange" reRender="painelCancelamento" action="#{CancelamentoContratoControle.validarNecessidadeAnexoAvaliandoParcelas}"/>
                        </h:selectOneMenu>
                    </div>
                    <rich:spacer width="7"/>
                    <a4j:commandLink id="atualizar_tipoOperacao"
                                     action="#{CancelamentoContratoControle.montarListaSelectItemJustificativaCancelamento}"
                                     ajaxSingle="true" reRender="form:tipoOperacao">
                        <i class="fa-icon-refresh texto-size-14-real texto-cor-cinza "></i>
                    </a4j:commandLink>
                    <rich:spacer width="7"/>
                    <a4j:commandLink id="adicionarTipoOperacao" action="#{JustificativaOperacaoControle.reset}"
                                     oncomplete="abrirPopup('justificativaOperacaoCons.jsp', 'JustificativaOperacao', 800, 595);">
                        <i class="fa-icon-plus-sign texto-size-14-real texto-cor-cinza "/>
                    </a4j:commandLink>
                </h:panelGroup>

                <h:panelGroup id="pnlUpload" layout="block" rendered="#{CancelamentoContratoControle.apresentarUploadArquivo}" style="margin-top: 8px">
                    <h:outputText value="ANEXAR ATESTADO" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                    <rich:fileUpload id="upload"
                                     listHeight="60"
                                     listWidth="625"
                                     noDuplicate="false"
                                     fileUploadListener="#{CancelamentoContratoControle.upload}"
                                     maxFilesQuantity="1"
                                     allowFlash="false"
                                     immediateUpload="false"
                                     acceptedTypes="jpg, jpeg, gif, png, bmp, JPG, JPEG, GIF, PNG, BMP"
                                     addControlLabel="Adicionar"
                                     cancelEntryControlLabel="Cancelar"
                                     doneLabel="Pronto"
                                     sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                     progressLabel="Enviando"
                                     clearControlLabel="Limpar"
                                     clearAllControlLabel="Limpar todos"
                                     stopControlLabel="Parar"
                                     uploadControlLabel="Enviar"
                                     transferErrorLabel="Falha de Transmissão"
                                     stopEntryControlLabel="Parar">
                        <a4j:support event="onadd" reRender="panelMensagem"/>
                        <a4j:support event="onerror" reRender="pnlUpload, panelMensagem"/>
                        <a4j:support event="onupload" reRender="panelMensagem"/>
                        <a4j:support event="onuploadcomplete" reRender="panelMensagem"/>
                        <a4j:support event="onclear" reRender="pnlUpload"/>
                    </rich:fileUpload>
                </h:panelGroup>

                <h:panelGrid>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  value="OBSERVAÇÃO:"/>
                    <h:inputTextarea value="#{CancelamentoContratoControle.cancelamentoContratoVO.observacao}"
                                     id="descricaoCalculo" styleClass="" rows="7" cols="80"/>
                </h:panelGrid>

                <h:panelGrid style="top:10px;" width="100%">
                    <h:panelGrid width="100%" columns="2">
                        <h:panelGroup>
                            <a4j:commandLink id="confirmar"
                                             rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                                             title="Finalizar"
                                             reRender="form,formConfirmacaoCancelamento,panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade"
                                             style="float: right;"
                                             action="#{CancelamentoContratoControle.validarAutorizacaoCancelamentoAvaliandoParcelas}"
                                             oncomplete="#{CancelamentoContratoControle.mensagemNotificar};#{CancelamentoContratoControle.msgAlert}"
                                             styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-ok"></i>&nbsp Finalizar
                            </a4j:commandLink>

                            <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});"
                                           style="float: right;"
                                           rendered="#{!CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                                           styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-remove"></i>&nbsp Fechar
                            </h:commandLink>

                            <rich:spacer width="7" style="float: right"/>

                            <%--                            <h:commandLink id="voltar" style="float: right;" title="Voltar Passo"--%>
                            <%--                                           rendered="#{CancelamentoContratoControle.apresentarBoteos && !CancelamentoContratoControle.processandoOperacao && !CancelamentoContratoControle.contratoVO.contratoRecorrenciaVO.cancelamentoProporcional}"--%>
                            <%--                                           action="#{CancelamentoContratoControle.voltarTelaCancelamento}"--%>
                            <%--                                           styleClass="pure-button">--%>
                            <%--                                <i class="fa-icon-arrow-left"></i>--%>
                            <%--                            </h:commandLink>--%>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>

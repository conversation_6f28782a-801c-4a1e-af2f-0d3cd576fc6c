<%@include file="includes/imports.jsp" %>
<rich:modalPanel id="panelCheque" autosized="true" shadowOpacity="true"
                 width="1000" height="200" styleClass="novaModal modalcheques noMargin"
                 onshow="document.getElementById('formCheque:codigoBanco').focus()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Informa��es do Cheque"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkPanelCheque"/>
            <rich:componentControl for="panelCheque" attachTo="hidelinkPanelCheque"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCheque">
        <input type="hidden" value="${modulo}" name="modulo"/>
        <h:panelGroup id="panelChequePreenchido" styleClass="detalhesPagamento">
            <h:panelGroup layout="block" styleClass="bloco">
                <span class="cinza negrito upper flex"><h:outputText value="N� do Banco:"></h:outputText></span>
                <div>
                    <h:inputText id="codigoBanco"
                                 value="#{ChequeControle.banco}" onblur="blurinput(this);"
                                 size="10" maxlength="10" onfocus="focusinput(this);"
                                 onkeypress="if (event.keyCode == 13) { document.getElementById('formCheque:listaCheque:0:linkcodigoBancohidden').click();return false;}; "
                                 styleClass="form">
                        <a4j:support event="onchange" focus="bancoPreenchido"
                                     action="#{ChequeControle.consultarBancoPorCodigoBanco}"
                                     reRender="panelChequePreenchido"/>
                    </h:inputText>
                    <a4j:commandLink style="visibility: hidden;" id="linkcodigoBancohidden"
                                     focus="bancoPreenchido"
                                     action="#{ChequeControle.consultarBancoPorCodigoBanco}"
                                     reRender="panelChequePreenchido"/>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="bloco">
                <span class="cinza negrito upper flex"><h:outputText value="Banco:"></h:outputText></span>
                <h:panelGroup layout="block" styleClass="block cb-container">
                    <h:selectOneMenu id="bancoPreenchido"
                                     converter="simpleIndexConverter"
                                     value="#{ChequeControle.bancoSelected}" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form">
                        <f:selectItems
                                value="#{ChequeControle.listaSelectItemBanco}"/>
                        <a4j:support event="onchange" focus="bancoPreenchido"
                                     action="#{ChequeControle.consultarBancoPorCodigo}"
                                     reRender="panelChequePreenchido"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bloco">
                <span class="cinza negrito upper flex"><h:outputText value="Ag�ncia:"></h:outputText></span>
                <h:inputText id="agencia" value="#{ChequeControle.cheque.agencia}"
                             onblur="blurinput(this);" size="7" maxlength="5"
                             onfocus="focusinput(this);" styleClass="form"
                             onkeypress="if (event.keyCode == 13) {return false}; "/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bloco">
                <span class="cinza negrito upper flex"><h:outputText value="Conta:"></h:outputText></span>
                <h:inputText id="conta" value="#{ChequeControle.cheque.conta}"
                             onblur="blurinput(this);" size="15" maxlength="20"
                             onfocus="focusinput(this);" styleClass="form"
                             onkeypress="if (event.keyCode == 13) {return false}; "/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bloco">
                <span class="cinza negrito upper flex"><h:outputText value="N� do Cheque:"></h:outputText></span>
                <h:inputText id="nDoc" value="#{ChequeControle.cheque.numero}"
                             onblur="blurinput(this);" size="15" maxlength="15"
                             onfocus="focusinput(this);" styleClass="form"
                             onkeypress="if (event.keyCode == 13) {return false}; "/>
            </h:panelGroup>

        </h:panelGroup>
        <h:panelGroup id="identificacao"  style="margin-top: 20px" styleClass="detalhesPagamento" layout="block">

            <h:panelGroup layout="block"  styleClass="bloco" rendered="#{!ChequeControle.financeiro}">
                <span class="cinza negrito upper flex"><h:outputText value="Tipo de identifica��o:"></h:outputText></span>
                <h:panelGroup layout="block" styleClass="block cb-container">
                    <h:selectOneMenu id="cpfCnpjCheque"
                                     value="#{ChequeControle.variavelCpfCnpj}"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form">
                        <f:selectItem itemValue="" itemLabel="#{msg_aplic.prt_Agenda_Selecione}"/>
                        <f:selectItems
                                value="#{ChequeControle.listaSelectItemCpfCnpj}"/>
                        <a4j:support event="onchange" focus="cpfCnpjCheque"
                                     action="#{ChequeControle.mostrarCampoCPF}"
                                     reRender="formCheque:identificacao"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="bloco" style="margin-top: 20px" rendered="#{!ChequeControle.financeiro}">
                <span class="cinza negrito upper flex">
                    <h:outputText rendered="#{ChequeControle.apresentarCampoCPF}" value="#{ChequeControle.displayIdentificadorFront[0]}:"></h:outputText>
                    <h:outputText rendered="#{ChequeControle.apresentarCampoCNPJ}" value="#{ChequeControle.displayIdentificadorFront[2]}:"></h:outputText>
                </span>
                <c:if test="${!ChequeControle.configuracaoSistemaVO.usarSistemaInternacional}">
                    <h:inputText id="cpfCheque2"
                                 rendered="#{ChequeControle.apresentarCampoCPF}"
                                 value="#{ChequeControle.cheque.cpf}"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" size="14"
                                 maxlength="14"
                                 onkeypress="return mascara(this.form, 'formCheque:cpfCheque2', '999.999.999-99', event);"
                                 styleClass="form"/>
                </c:if>
                <c:if test="${ChequeControle.configuracaoSistemaVO.usarSistemaInternacional}">
                    <h:inputText id="cpfCheque2Internacional"
                                 rendered="#{ChequeControle.apresentarCampoCPF}"
                                 value="#{ChequeControle.cheque.cpf}"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" size="14"
                                 maxlength="14"
                                 styleClass="form"/>
                </c:if>
                <c:if test="${!ChequeControle.configuracaoSistemaVO.usarSistemaInternacional}">
                    <h:inputText id="CnpjCheque2"
                                 rendered="#{ChequeControle.apresentarCampoCNPJ}"
                                 value="#{ChequeControle.cheque.cnpj}"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" size="18"
                                 maxlength="18"
                                 onkeypress="return mascara(this.form, 'formCheque:CnpjCheque2', '99.999.999/9999-99', event);"
                                 styleClass="form"/>
                </c:if>
                <c:if test="${ChequeControle.configuracaoSistemaVO.usarSistemaInternacional}">
                    <h:inputText id="CnpjCheque2Internacional"
                                 rendered="#{ChequeControle.apresentarCampoCNPJ}"
                                 value="#{ChequeControle.cheque.cnpj}"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" size="18"
                                 maxlength="20"
                                 styleClass="form"/>
                </c:if>
            </h:panelGroup>


        </h:panelGroup>

        <h:panelGroup id="identificacao2" styleClass="detalhesPagamento" layout="block">
            <h:panelGroup layout="block" styleClass="bloco" style="margin-top: 20px">
                <span class="cinza negrito upper flex">
                    <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nomeTerceiro}"></h:outputText>
                </span>
                <h:inputText id="nomeTerceiro"
                             value="#{ChequeControle.cheque.nomeNoCheque}"
                             onblur="blurinput(this);" onfocus="focusinput(this);" size="18"
                             maxlength="120"
                             styleClass="form"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bloco" style="margin-top: 20px">
                <span class="cinza negrito upper flex">
                    <h:outputText value="Qtd. de Cheques:" ></h:outputText>
                </span>
                <h:inputText id="qtdeCheques" readonly="#{ChequeControle.financeiro}"
                             value="#{ChequeControle.qtdeCheques}"
                             onblur="blurinput(this);"
                             size="5" maxlength="5" onfocus="focusinput(this);"
                             styleClass="form">
                </h:inputText>
                <rich:toolTip for="qtdeCheques" rendered="#{ChequeControle.financeiro}"
                              followMouse="true" direction="top-right" style="width:200px;height: 90px;">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_Finan_msg_qtdChequesFinanceiro}"></h:outputText>
                </rich:toolTip>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGrid id="mensagem1">
            <h:outputText styleClass="mensagem"
                          value="#{MovPagamentoControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada"
                          value="#{MovPagamentoControle.mensagemDetalhada}"/>
        </h:panelGrid>

        <h:panelGrid columns="1" width="100%"
                     columnClasses="colunaCentralizada">
            <a4j:commandButton id="linkGravarInformacoesCheque"
                               value="Adicionar cheque"
                               rendered="#{!ChequeControle.financeiro}"
                               action="#{MovPagamentoControle.adicionarListaCheque}"
                               reRender="escolhaFormaPagamento,MovPagamentoCheque,
                                       escolhaFormaPagamentoCC,totalLancado,
                                       residuo,mensagem1, form:panelCheque"
                               oncomplete="#{MovPagamentoControle.msgAlert}"
                               styleClass="botoes nvoBt botaoPrimarioGrande "/>


            <a4j:commandButton id="linkGravarInformacoesChequeFinanceiro"
                               value="Gerar" rendered="#{ChequeControle.financeiro}"
                               action="#{MovContaControle.gerarCheques}"
                               reRender="formLancarPagamento"
                               oncomplete="Richfaces.hideModalPanel('panelCheque')"/>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
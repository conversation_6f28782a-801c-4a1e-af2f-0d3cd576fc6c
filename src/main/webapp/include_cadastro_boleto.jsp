<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">

<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<h:panelGroup id="panelBoletos" layout="block" styleClass="margin-box">
    <h:panelGrid width="100%" columns="2" rowClasses="linhaTop">

        <h:panelGroup>
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td align="left" valign="top">
                        <div style="clear:both;" class="text">
                            <p style="margin-bottom:4px;">
                                <img src="${root}/images/arrow2.gif" width="16" height="16"
                                     style="vertical-align:middle;margin-right:6px;">
                                Confirmação do Boleto
                            </p>
                        </div>
                        <rich:separator height="2px" lineType="dotted"/>
                    </td>
                </tr>
            </table>

            <h:panelGrid id="panelCampos" columnClasses="classEsquerda, classDireita" width="100%" columns="2"
                         rowClasses="linhaPar, linhaImpar">

                <h:outputText styleClass="tituloCampos" value="Convênio de Cobrança:"/>
                <h:panelGroup>
                    <h:selectOneMenu id="comboConvenioBoletoPadrao" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{BoletoBancarioControle.convenioCobrancaSelecionado}">
                        <a4j:support event="onchange" reRender="panelBoletos"
                                     action="#{BoletoBancarioControle.selecionouConvenioCobranca}"/>
                        <f:selectItems value="#{BoletoBancarioControle.listaSelectItemConvenioCobrancaBoleto}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <c:if test="${BoletoBancarioControle.convenioCobrancaSelecionado > 0}">

                    <h:outputText rendered="#{BoletoBancarioControle.empresaUtilizaMultaJuros && BoletoBancarioControle.apresentarOpcaoCalcularMultaJuros}"
                                  styleClass="tituloCampos tooltipster"
                                  title="Calcular a Multa e Juros conforme configuração realizada no cadastro da Empresa."
                                  value="Calcular Multa e Juros (Boleto Único):"/>
                    <h:panelGroup id="valorMultaJuros"
                                  rendered="#{BoletoBancarioControle.empresaUtilizaMultaJuros && BoletoBancarioControle.apresentarOpcaoCalcularMultaJuros}">
                        <h:selectBooleanCheckbox id="culculojuro"
                                                 styleClass="tooltipster"
                                                 title="Calcular a Multa e Juros conforme configuração realizada no cadastro da Empresa."
                                                 value="#{BoletoBancarioControle.calcularMultaJuros}">
                            <a4j:support event="onchange"
                                         actionListener="#{BoletoBancarioControle.selecionouCalculoMultaJuros}"
                                         reRender="panelCampos, panelBotoes"/>
                        </h:selectBooleanCheckbox>
                    </h:panelGroup>

                    <h:outputText rendered="#{BoletoBancarioControle.apresentarAgruparPorVencimento}"
                                  styleClass="tituloCampos tooltipster"
                                  title="Gerar um boleto separado para cada parcela de acordo com o vencimento de cada uma."
                                  value="Emitir boletos por data de vencimento das parcelas:"/>
                    <h:selectBooleanCheckbox id="btnEmitirVencimentoParcela"
                                             styleClass="tooltipster"
                                             title="Gerar um boleto separado para cada parcela de acordo com o vencimento de cada uma."
                                             rendered="#{BoletoBancarioControle.apresentarAgruparPorVencimento}"
                                             value="#{BoletoBancarioControle.agruparBoletosPorVencimento}">
                        <a4j:support event="onchange"
                                     actionListener="#{BoletoBancarioControle.processarAgrupamento}"
                                     reRender="pnlParcelas, panelCampos, panelBotoes"/>
                    </h:selectBooleanCheckbox>

                    <h:outputText
                            rendered="#{BoletoBancarioControle.apresentarOpcaoParcelasMesmoVencimento && BoletoBancarioControle.possuiParcelasMesmoVencimento && BoletoBancarioControle.convenioSelecionadoPJBank}"
                            styleClass="tituloCampos tooltipster"
                            value="Emitir Boleto único para parcelas com mesmo vencimento:"
                            title="#{BoletoBancarioControle.titleBoletoUnicoParcelasMesmoVencimento}"/>
                    <h:selectBooleanCheckbox id="btnEmitirBoletoUnicoMesmoVencimentoParcela"
                                             title="#{BoletoBancarioControle.titleBoletoUnicoParcelasMesmoVencimento}"
                                             rendered="#{BoletoBancarioControle.apresentarOpcaoParcelasMesmoVencimento && BoletoBancarioControle.possuiParcelasMesmoVencimento && BoletoBancarioControle.convenioSelecionadoPJBank}"
                                             value="#{BoletoBancarioControle.boletoUnicoParcelasMesmoVencimento}">
                        <a4j:support event="onchange"
                                     reRender="pnlParcelas"/>
                    </h:selectBooleanCheckbox>

                    <h:outputText rendered="#{!BoletoBancarioControle.agruparBoletosPorVencimento}"
                                  title="#{BoletoBancarioControle.hintDataVencimentoBoleto}"
                                  styleClass="tituloCampos tooltipster" value="Vencimento do boleto:"/>
                    <h:panelGrid columns="2" rendered="#{!BoletoBancarioControle.agruparBoletosPorVencimento}">
                    <h:panelGroup id="dataVencimento" style="display: block">
                        <rich:calendar value="#{BoletoBancarioControle.dataVencimento}"
                                       disabled="#{BoletoBancarioControle.apresentarBotaoImprimir or BoletoBancarioControle.bloquearAlterarDataVencimento}"
                                       showInput="true" datePattern="dd/MM/yyyy" zindex="2" showWeeksBar="false">
                            <a4j:support event="onchanged"
                                         actionListener="#{BoletoBancarioControle.processarCalculoMultaJuros}"
                                         reRender="panelCampos, panelBotoes"/>
                        </rich:calendar>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                     query="mask('99/99/9999')"/>
                    </h:panelGroup>
                    <h:outputLink styleClass="tooltipster" style="display: block; text-decoration: none"
                                  title="#{BoletoBancarioControle.hintDataVencimentoBoleto}">
                        <i class="fa-icon-info-circle" style="color:#2d2c2c; font-size: 14px"></i>
                    </h:outputLink>
                    </h:panelGrid>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{!BoletoBancarioControle.usarLabelResponsavelAluno}" value="Nome Sacado: "/>
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  rendered="#{BoletoBancarioControle.usarLabelResponsavelAluno}"
                                  value="Nome Sacado (Responsável pelo aluno): "
                                  title="Aluno possui responsável. O boleto será gerado em nome e CPF do responsável."/>
                    <h:outputText value="#{BoletoBancarioControle.nomeSacadoBoleto}"/>

                    <h:outputText styleClass="tituloCampos" value="Valor (#{EmpresaControle.empresaLogado.moeda}):"/>
                    <h:outputText value="#{BoletoBancarioControle.valorTitulo}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>

                    <h:outputText rendered="#{BoletoBancarioControle.calcularMultaJuros}"
                                  styleClass="tituloCampos" value="Multa e Juros (R$):"/>
                    <h:outputText rendered="#{BoletoBancarioControle.calcularMultaJuros}"
                                  value="#{BoletoBancarioControle.valorMultaJuros}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>

                    <h:outputText rendered="#{BoletoBancarioControle.calcularMultaJuros}"
                                  styleClass="tituloCampos" value="Valor do Título (R$):"/>
                    <h:outputText rendered="#{BoletoBancarioControle.calcularMultaJuros}"
                                  value="#{BoletoBancarioControle.valorTitulo + BoletoBancarioControle.valorMultaJuros}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </c:if>
            </h:panelGrid>

            <h:panelGroup id="pnlParcelas" layout="block" style="padding: 15px 0;">
                <h:panelGroup rendered="#{BoletoBancarioControle.agruparBoletosPorVencimento}" layout="block">

                    <rich:dataTable id="dataTableParcelas" value="#{BoletoBancarioControle.boletosTO}" var="boleto" style="margin: 0 auto">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Vencimento Boleto"/>
                            </f:facet>
                            <h:outputText title="Data de vencimento do boleto a ser gerado"
                                          styleClass="tooltipster"
                                          value="#{boleto.dataVencimento_Apresentar}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Parcelas do Boleto"/>
                            </f:facet>
                            <rich:dataTable width="100%" value="#{boleto.parcelas}" var="parcela">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Descrição"/>
                                    </f:facet>
                                    <h:outputText value="#{parcela.descricao}"/>
                                </h:column>

                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Valor"/>
                                    </f:facet>
                                    <h:outputText value="#{parcela.valorParcela_Apresentar_Moeda}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="Venc. Parcela"/>
                                    </f:facet>
                                    <h:outputText styleClass="tooltipster"
                                                  title="#{BoletoBancarioControle.titleVencParcelaBoleto}"
                                                  value="#{parcela.dataVencimento_Apresentar}"/>
                                </h:column>
                            </rich:dataTable>
                        </h:column>
                        <h:column rendered="#{BoletoBancarioControle.boletoUnicoParcelasMesmoVencimento}">
                            <f:facet name="header">
                                <h:outputText value="Valor do Boleto"/>
                            </f:facet>
                            <h:outputText value="#{boleto.valorParcelas_Apresentar}"/>
                        </h:column>
                    </rich:dataTable>

                    <div style="margin: 0 auto; text-align: center; margin-top: 10px; font-size: 12px; color: red;">
                        <h:outputText rendered="#{BoletoBancarioControle.apresentarBotaoGravar && BoletoBancarioControle.convenioCobrancaSelecionado > 0}"
                                      value="Obs: Boletos e Parcelas com vencimento em finais de semana ou feriados do CRM, terão suas datas ajustadas para o próximo dia útil.">
                        </h:outputText>
                    </div>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGrid id="panelMensagem" columns="1" width="100%">
                <c:if test="${BoletoBancarioControle.erro || BoletoBancarioControle.sucesso}">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">

                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{BoletoBancarioControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{BoletoBancarioControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                            <h:outputText styleClass="mensagem" value="#{BoletoBancarioControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{BoletoBancarioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </c:if>
            </h:panelGrid>

            <h:panelGrid id="panelBotoes" columnClasses="centralizado" width="100%" columns="1">
                <h:panelGroup>
                    <h:panelGroup styleClass="pure-form" style="font-size:80%">
                        <a4j:commandLink id="btnVoltar" styleClass="pure-button"
                                         action="#{BoletoBancarioControle.voltar}"
                                         accesskey="1" reRender="panelMensagem, panelBotoes">
                            <i class="fa-icon-reply"></i> Voltar
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGroup styleClass="pure-form" style="margin-left: 15px; font-size: 80%">
                        <a4j:commandLink id="btnGravar" styleClass="pure-button pure-button-primary"
                                         rendered="#{BoletoBancarioControle.apresentarBotaoGravar && BoletoBancarioControle.convenioCobrancaSelecionado > 0}"
                                         action="#{BoletoBancarioControle.gravar}"
                                         oncomplete="#{BoletoBancarioControle.mensagemNotificar}"
                                         reRender="panelMensagem, panelBotoes, panelCampos, dataTableParcelas">
                            <i class="fa-icon-check"></i> Gerar Boleto
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGroup styleClass="pure-form" style="font-size: 80%">
                        <a4j:commandLink id="btnImprimir" styleClass="pure-button pure-button-primary"
                                         rendered="#{empty BoletoBancarioControle.linkBoleto && BoletoBancarioControle.apresentarBotaoImprimir}"
                                         action="#{BoletoBancarioControle.imprimirBoleto}"
                                         oncomplete="#{BoletoBancarioControle.msgAlert} #{BoletoBancarioControle.mensagemNotificar} #{BoletoBancarioControle.mensagemNotificar}"
                                         reRender="panelMensagem, panelBotoes">
                            <i class="fa-icon-print"></i> Imprimir
                        </a4j:commandLink>
                        <h:outputLink id="btnImprimirBoletoOnline"
                                      value="#{BoletoBancarioControle.linkBoleto}" target="_blank"
                                      rendered="#{not empty BoletoBancarioControle.linkBoleto && BoletoBancarioControle.apresentarBotaoImprimir}"
                                      style="padding: 6.4px 12.8px;background-color: #094771;color: #FFF;border-radius: 2px;font-size: 13px">
                            <i class="fa-icon-print" style="margin-right: 7px;"></i> Imprimir
                        </h:outputLink>
                    </h:panelGroup>

                    <h:panelGroup styleClass="pure-form" style="font-size: 80%; margin-left: 12px">
                        <a4j:commandLink id="btnEnviarEmailGeracaoBoleto" styleClass="pure-button pure-button"
                                         rendered="#{BoletoBancarioControle.apresentarBotaoImprimir}"
                                         oncomplete="#{BoletoBancarioControle.mensagemNotificar}"
                                         action="#{BoletoBancarioControle.enviarEmail}"
                                         reRender="panelMensagem, panelBotoes, panelConfirmacao">
                            <i class="fa-icon-share-alt"></i> Enviar E-mail
                        </a4j:commandLink>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGrid>

        </h:panelGroup>
    </h:panelGrid>
</h:panelGroup>

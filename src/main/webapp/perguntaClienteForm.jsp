<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_PerguntaCliente_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{PerguntaClienteControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_PerguntaCliente_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PerguntaCliente_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{PerguntaClienteControle.perguntaClienteVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PerguntaCliente_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao" required="true" size="70" maxlength="100" styleClass="camposObrigatorios" value="#{PerguntaClienteControle.perguntaClienteVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PerguntaCliente_tipoPergunta}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="tipoPergunta" styleClass="camposObrigatorios" value="#{PerguntaClienteControle.perguntaClienteVO.tipoPergunta}" >
                            <f:selectItems  value="#{PerguntaClienteControle.listaSelectItemTipoPerguntaPerguntaCliente}" />
                        </h:selectOneMenu>
                        <h:message for="tipoPergunta" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_RespostaPergCliente_tituloForm}"/>
                    </f:facet>
                    <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_RespostaPergCliente_descricaoRespota}" />
                        <h:inputText  id="respostaPergCliente_descricaoRespota" size="50" maxlength="50" styleClass="camposObrigatorios" value="#{PerguntaClienteControle.respostaPergClienteVO.descricaoRespota}" />
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_RespostaPergCliente_respostaOpcao}" />
                        <h:selectBooleanCheckbox id="respostaPergCliente_respostaOpcao" styleClass="campos" value="#{PerguntaClienteControle.respostaPergClienteVO.respostaOpcao}"/>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_RespostaPergCliente_respostaTextual}" />
                        <h:inputText  id="respostaPergCliente_respostaTextual" size="70" maxlength="100" styleClass="campos" value="#{PerguntaClienteControle.respostaPergClienteVO.respostaTextual}" />
                    </h:panelGrid>
                    <h:commandButton action="#{PerguntaClienteControle.adicionarRespostaPergCliente}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <h:dataTable id="respostaPergClienteVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                 rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                 value="#{PerguntaClienteControle.perguntaClienteVO.respostaPergClienteVOs}" var="respostaPergCliente">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_RespostaPergCliente_descricaoRespota}" />
                            </f:facet>
                            <h:outputText  value="#{respostaPergCliente.descricaoRespota}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_RespostaPergCliente_respostaOpcao}" />
                            </f:facet>
                            <h:outputText  value="#{respostaPergCliente.respostaOpcao}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_RespostaPergCliente_respostaTextual}" />
                            </f:facet>
                            <h:outputText  value="#{respostaPergCliente.respostaTextual}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                            </f:facet>
                            <h:panelGroup>
                                <h:commandButton id="editarItemVenda" immediate="true" action="#{PerguntaClienteControle.editarRespostaPergCliente}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                <h:outputText value="    "/>

                                <h:commandButton id="removerItemVenda" immediate="true" action="#{PerguntaClienteControle.removerRespostaPergCliente}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                            </h:panelGroup>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{PerguntaClienteControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{PerguntaClienteControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{PerguntaClienteControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{PerguntaClienteControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{PerguntaClienteControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{PerguntaClienteControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
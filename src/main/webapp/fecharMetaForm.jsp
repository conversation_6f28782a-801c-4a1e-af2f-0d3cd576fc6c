<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
		padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_FecharMeta_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_FecharMetaDetalhado_consultarCliente}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}CRMWeb:Operações:Fechamento_da_Meta_do_Dia"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <rich:modalPanel id="panelCliente" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formCliente:consultarCliente').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_FecharMetaDetalhado_consultarCliente}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkCliente"/>
                <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCliente" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarCliente" value="#{FecharMetaControle.campoConsultarCliente}">
                        <f:selectItems value="#{FecharMetaControle.tipoConsultarComboCliente}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarCliente" styleClass="campos" value="#{FecharMetaControle.valorConsultarCliente}"/>
                    <a4j:commandButton id="btnConsultarCliente" reRender="formCliente:mensagemConsultarCliente, formCliente:resultadoConsultaCliente , formClientescResultadoCliente , formCliente" action="#{FecharMetaControle.consultarCliente}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCliente" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{FecharMetaControle.listaConsultarCliente}" rows="10" var="cliente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.codigo}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.pessoa.nome}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_situacao}"/>
                        </f:facet>
                        <h:outputText value="#{fecharMeta.situacao_Apresentar}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_matricula}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.matricula}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_categoria}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.categoria.nome}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_codAcesso}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.codAcesso}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_banco}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.banco}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_agencia}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.agencia}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_conta}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.conta}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{FecharMetaControle.selecionarCliente}" focus="cliente" reRender="form, formCliente" oncomplete="Richfaces.hideModalPanel('panelCliente')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoSelecionar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10" id="scResultadoCliente"/>
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{FecharMetaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{FecharMetaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        
        <h:form id="form">
            <h:commandLink action="#{FecharMetaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_FecharMeta_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMeta_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo" size="10" maxlength="10" styleClass="camposObrigatorios" value="#{FecharMetaControle.fecharMetaVO.codigo}" />
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMeta_dataRegistro}" />
                            <h:panelGroup>
                                <a4j:outputPanel layout="block">
                                    <rich:calendar id="dataRegistro" oninputchange="return validar_Data(this.id);"  value="#{FecharMetaControle.fecharMetaVO.dataRegistro}" enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy" showApplyButton="false" cellWidth="24px" cellHeight="24px" style="width:200px" inputClass="campos" showFooter="false"/>
                                </a4j:outputPanel>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMeta_colaborador}" />
                            <h:panelGroup>
                                <h:selectOneMenu  id="colaborador" styleClass="camposObrigatorios" value="#{FecharMetaControle.fecharMetaVO.colaborador.codigo}" >
                                    <f:selectItems  value="#{FecharMetaControle.listaSelectItemColaborador}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_colaborador" action="#{FecharMetaControle.montarListaSelectItemColaborador}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:colaborador"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMeta_meta}" />
                            <h:inputText  id="meta" size="10" maxlength="10" styleClass="campos" value="#{FecharMetaControle.fecharMetaVO.meta}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMeta_metaAtingida}" />
                            <h:inputText  id="metaAtingida" size="10" maxlength="10" styleClass="campos" value="#{FecharMetaControle.fecharMetaVO.metaAtingida}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMeta_porcentagem}" />
                            <h:inputText  id="porcentagem"onkeypress="return Tecla('form:id');" onkeyup="FormataValor(this, 13, event);" size="20" maxlength="20" styleClass="campos" value="#{FecharMetaControle.fecharMetaVO.porcentagem}" >
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMeta_justificativa}" />
                            <h:inputTextarea id="justificativa" cols="70" rows="3" styleClass="campos" value="#{FecharMetaControle.fecharMetaVO.justificativa}"/>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMeta_codOrigem}" />
                            <h:panelGroup>
                                <h:inputText  id="codOrigem" size="50" maxlength="50" styleClass="camposObrigatorios" value="#{FecharMetaControle.fecharMetaVO.codOrigem}" />
                            </h:panelGroup>

                </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_FecharMetaDetalhado_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMetaDetalhado_cliente}" />
                                    <h:panelGroup>
                                        <h:inputText  id="fecharMetaDetalhado_cliente" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{FecharMetaControle.fecharMetaDetalhadoVO.cliente.situacao}" />
                                        <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelCliente')"image="imagens/informacao.gif" alt="#{msg_aplic.prt_FecharMetaDetalhado_consultarCliente}"/>
                                        <rich:spacer width="5" />
                                        <a4j:commandButton id="limparClienteCliente" immediate="true" action="#{FecharMetaControle.limparCampoCliente}" image="imagens/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="fecharMetaDetalhado_cliente"/>
                                    </h:panelGroup>
                                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMetaDetalhado_codigo}" />
                                    <h:inputText  id="fecharMetaDetalhado_codigo" size="10" maxlength="10" styleClass="camposObrigatorios" value="#{FecharMetaControle.fecharMetaDetalhadoVO.codigo}" />
                                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_FecharMetaDetalhado_obteveSucesso}" />
                                    <h:selectBooleanCheckbox id="fecharMetaDetalhado_obteveSucesso" styleClass="campos" value="#{FecharMetaControle.fecharMetaDetalhadoVO.obteveSucesso}"/>
                            </h:panelGrid>
                            <h:commandButton action="#{FecharMetaControle.adicionarFecharMetaDetalhado}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                            <h:dataTable id="fecharMetaDetalhadoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                             rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                             value="#{FecharMetaControle.fecharMetaVO.fecharMetaDetalhadoVOs}" var="fecharMetaDetalhado">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_FecharMetaDetalhado_cliente}" />
                                    </f:facet>
                                    <h:outputText  value="#{fecharMetaDetalhado.cliente.situacao}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_FecharMetaDetalhado_codigo}" />
                                    </f:facet>
                                    <h:outputText  value="#{fecharMetaDetalhado.codigo}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_aplic.prt_FecharMetaDetalhado_obteveSucesso}" />
                                    </f:facet>
                                    <h:outputText  value="#{fecharMetaDetalhado.obteveSucesso}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                    </f:facet>
                                <h:panelGroup>
                                    <h:commandButton id="editarItemVenda" immediate="true" action="#{FecharMetaControle.editarFecharMetaDetalhado}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>
                                    <f:verbatim>
                                        <h:outputText value="    "/>
                                    </f:verbatim>
                                    <h:commandButton id="removerItemVenda" immediate="true" action="#{FecharMetaControle.removerFecharMetaDetalhado}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{FecharMetaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{FecharMetaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{FecharMetaControle.novo}" image="./imagens/botaoNovo.png" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="salvar" action="#{FecharMetaControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="excluir" onclick="return confirm('#{msg.msg_ConfirmaExclusao}');" action="#{FecharMetaControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="consultar" immediate="true" action="#{FecharMetaControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>
<script>
    document.getElementById("form:codigo").focus();
</script>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <script type="text/javascript" language="javascript" src="script/script.js"></script>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">


<script type="text/javascript" src="bootstrap/jquery.js"></script>
<jsp:include page="include_head.jsp" flush="true"/>
<head>
    <script type="text/javascript">
        $.noConflict();
    </script>
</head>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">

        <html>

        <body class="zw">
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">

            <tr>
                <td height="77" align="left" valign="top" class="bgtop">
                    <a4j:loadStyle src="/css/toggle_menu.css"/>
                    <a4j:loadScript src="/script/toggle_menu.js"/>

                    <table width="100%" height="77" align="left" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="15%" align="left" valign="top">

                                <c:set property="tmp" var="tmp" scope="request" value="<%=request.getRequestURI()%>"/>
                                <c:set property="name" var="name" scope="request" value="smartbox"/>
                                <h:panelGroup style="padding:-1000px 0 0 0;" styleClass="#{SuporteControle.classTopo}"
                                              layout="block"/>

                            </td>

                            <td align="right" valign="top" style="padding-right:13px;">

                                <%@include file="nfe/include_top_menu_superiorNFe.jsp" %>

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <tr>
                <td height="48" align="left" valign="top" class="bgmenu">
                </td>
            </tr>
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" height="100%" align="center" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">
                            </td>
                            <td align="center" valign="top" style="padding-left: 5px; padding-right: 0;">
                                <jsp:include page="/includes/cliente/include_box_financeiroPacto.jsp" flush="true"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td height="93" align="left" valign="top" class="bgrodape">
                    <jsp:include page="include_rodape.jsp" flush="true"/>
                </td>
            </tr>
        </table>
        </body>
        </html>
    </h:form>
</f:view>
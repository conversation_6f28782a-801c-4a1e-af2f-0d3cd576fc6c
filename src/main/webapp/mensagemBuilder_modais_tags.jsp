<%@include file="includes/imports.jsp" %>

<rich:modalPanel id="maisTags" styleClass="novaModal" width="600" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Tags"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkmaisTags"/>
            <rich:componentControl for="maisTags" attachTo="hidelinkmaisTags" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formMdlhidelinkmaisTags">
        <h:panelGroup>
            <h:panelGroup styleClass="margin-box">
                <a4j:repeat value="#{ModeloMensagemControle.listaSelectItemMarcadoEmail}" var="tag" rowKeyVar="rkv">
                    <h:panelGroup layout="block" style="display: inline-block; width: 49%; vertical-align: top">
                        <span class="tooltipster" title="Clique para copiar" onclick="copiar(this)" style="cursor: copy; display: block; font-weight: bold"><h:outputText value="#{tag.tag} "></h:outputText></span>
                        <h:outputText value="(#{tag.nome})"></h:outputText>
                    </h:panelGroup>
                </a4j:repeat>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup >
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('maisTags')" value="Ok"
                                     styleClass="botaoPrimario texto-size-16-real"/>
                </h:panelGroup>
            </h:panelGroup>
            <br/>
            <br/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="maisTagsRemessa" styleClass="novaModal" width="600" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Tags"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkmaisTagsRemessa"/>
            <rich:componentControl for="maisTagsRemessa" attachTo="hidelinkmaisTagsRemessa" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formMdlhidelinkmaisTagsRemessa">
        <h:panelGroup>
            <h:panelGroup styleClass="margin-box">
                <a4j:repeat value="#{ModeloMensagemControle.listaTagsEmailRemessa}" var="tag" rowKeyVar="rkv">
                    <h:panelGroup layout="block" style="display: inline-block; width: 49%; vertical-align: top">
                        <span class="tooltipster" title="Clique para copiar" onclick="copiar(this)" style="cursor: copy; display: block; font-weight: bold"><h:outputText value="#{tag.tag} "></h:outputText></span>
                        <h:outputText value="(#{tag.nome})"></h:outputText>
                    </h:panelGroup>
                </a4j:repeat>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup >
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('maisTagsRemessa')" value="Ok"
                                     styleClass="botaoPrimario texto-size-16-real"/>
                </h:panelGroup>
            </h:panelGroup>
            <br/>
            <br/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>


<rich:modalPanel id="maisTagsBoasVindas" styleClass="novaModal" width="600" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Tags de contrato mais recente do aluno"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkmaisTagsBoasVindas"/>
            <rich:componentControl for="maisTagsBoasVindas" attachTo="hidelinkmaisTagsBoasVindas" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formMdlhidelinkmaisTagsBoasVindas">

        <h:outputText value="*Tags exclusivas para o contrato mais recente do aluno."></h:outputText>

        <h:panelGroup>
            <h:panelGroup styleClass="margin-box">
                <a4j:repeat value="#{ModeloMensagemControle.listaTagsBoasVindas}" var="tag" rowKeyVar="rkv">
                    <h:panelGroup layout="block" style="display: inline-block; width: 49%; vertical-align: top">
                        <span class="tooltipster" title="Clique para copiar" onclick="copiar(this)" style="cursor: copy; display: block; font-weight: bold"><h:outputText value="#{tag.tag} "></h:outputText></span>
                        <h:outputText value="(#{tag.nome})"></h:outputText>
                    </h:panelGroup>
                </a4j:repeat>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup >
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('maisTagsBoasVindas')" value="Ok"
                                     styleClass="botaoPrimario texto-size-16-real"/>
                </h:panelGroup>
            </h:panelGroup>
            <br/>
            <br/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

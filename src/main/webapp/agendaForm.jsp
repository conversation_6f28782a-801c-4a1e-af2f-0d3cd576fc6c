<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Agenda_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Agenda_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-aulas-experimentais-adm/"/>

    <rich:modalPanel id="panelPassivo" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formPassivo:consultarPassivo').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Agenda_consultarPassivo}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkPassivo"/>
                <rich:componentControl for="panelPassivo" attachTo="hiperlinkPassivo" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formPassivo" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarPassivo" value="#{AgendaControle.campoConsultarPassivo}">
                        <f:selectItems value="#{AgendaControle.tipoConsultarComboPassivo}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarPassivo"styleClass="campos" value="#{AgendaControle.valorConsultarPassivo}"/>
                    <a4j:commandButton id="btnConsultarPassivo" reRender="formPassivo:mensagemConsultarPassivo, formPassivo:resultadoConsultaPassivo , formPassivoscResultadoPassivo , formPassivo" action="#{AgendaControle.consultarPassivo}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaPassivo" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AgendaControle.listaConsultarPassivo}" rows="10" var="passivo">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Passivo_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{passivo.codigo}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Passivo_nome}"/>
                        </f:facet>
                        <h:outputText value="#{passivo.nome}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Passivo_usuarioResponsavelCadastro}"/>
                        </f:facet>
                        <h:outputText value="#{passivo.responsavelCadastro.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Passivo_dia}"/>
                        </f:facet>
                        <h:outputText value="#{passivo.dia}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Passivo_colaborador}"/>
                        </f:facet>
                        <h:outputText value="#{passivo.colaboradorResponsavel.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AgendaControle.selecionarPassivo}" focus="passivo" reRender="form, formPassivo" oncomplete="Richfaces.hideModalPanel('panelPassivo')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagensCRM/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formPassivo:resultadoConsultaPassivo" maxPages="10" id="scResultadoPassivo"/>
                <h:panelGrid id="mensagemConsultaPassivo" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AgendaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AgendaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelIndicado" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formIndicado:consultarIndicado').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Agenda_consultarIndicacao}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkIndicado"/>
                <rich:componentControl for="panelIndicado" attachTo="hiperlinkIndicado" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formIndicado" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarIndicado" value="#{AgendaControle.campoConsultarIndicado}">
                        <f:selectItems value="#{AgendaControle.tipoConsultarComboIndicado}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarIndicado"styleClass="campos" value="#{AgendaControle.valorConsultarIndicado}"/>
                    <a4j:commandButton id="btnConsultarIndicado" reRender="formIndicado:mensagemConsultarIndicado, formIndicado:resultadoConsultaIndicado , formIndicadoscResultadoIndicado , formIndicado" action="#{AgendaControle.consultarIndicado}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaIndicado" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AgendaControle.listaConsultarIndicado}" rows="10" var="indicado">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Indicacao_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{indicado.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Indicacao_nomeIndicado}"/>
                        </f:facet>
                        <h:outputText value="#{indicado.nomeIndicado}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AgendaControle.selecionarIndicado}" focus="indicado" reRender="form, formIndicado" oncomplete="Richfaces.hideModalPanel('panelIndicado')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagensCRM/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formIndicado:resultadoConsultaIndicado" maxPages="10" id="scResultadoIndicado"/>
                <h:panelGrid id="mensagemConsultaIndicado" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AgendaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AgendaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelColaboradorResponsavel" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formColaboradorResponsavel:consultarColaboradorResponsavel').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Agenda_consultarColaborador}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkColaboradorResponsavel"/>
                <rich:componentControl for="panelColaboradorResponsavel" attachTo="hiperlinkColaboradorResponsavel" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formColaboradorResponsavel" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarColaboradorResponsavel" value="#{AgendaControle.campoConsultarColaboradorResponsavel}">
                        <f:selectItems value="#{AgendaControle.tipoConsultarComboColaboradorResponsavel}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarColaboradorResponsavel"styleClass="campos" value="#{AgendaControle.valorConsultarColaboradorResponsavel}"/>
                    <a4j:commandButton id="btnConsultarColaboradorResponsavel" reRender="formColaboradorResponsavel:mensagemConsultarColaboradorResponsavel, formColaboradorResponsavel:resultadoConsultaColaboradorResponsavel , formColaboradoResponsavelrscResultadoColaboradorResponsavel , formColaboradorResponsavel" action="#{AgendaControle.consultarColaboradorResponsavel}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaColaboradorResponsavel" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AgendaControle.listaConsultarColaboradorResponsavel}" rows="10" var="colaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{colaborador.codigo}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{colaborador.pessoa.nome}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_situacao}"/>
                        </f:facet>
                        <h:outputText value="#{agenda.situacao_Apresentar}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_codAcesso}"/>
                        </f:facet>
                        <h:outputText value="#{colaborador.codAcesso}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AgendaControle.selecionarColaboradorResponsavel}" focus="colaborador" reRender="form, formColaboradorResponsavel" oncomplete="Richfaces.hideModalPanel('panelColaboradorResponsavel')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagensCRM/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formColaboradorResponsavel:resultadoConsultaColaboradorResponsavel" maxPages="10" id="scResultadoColaboradorResponsavel"/>
                <h:panelGrid id="mensagemConsultaColaboradorResponsavel" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AgendaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AgendaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelCliente" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formCliente:consultarCliente').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Agenda_consultarCliente}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkCliente"/>
                <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCliente" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarCliente" value="#{AgendaControle.campoConsultarCliente}">
                        <f:selectItems value="#{AgendaControle.tipoConsultarComboCliente}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarCliente"styleClass="campos" value="#{AgendaControle.valorConsultarCliente}"/>
                    <a4j:commandButton id="btnConsultarCliente" reRender="formCliente:mensagemConsultarCliente, formCliente:resultadoConsultaCliente , formClientescResultadoCliente , formCliente" action="#{AgendaControle.consultarCliente}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCliente" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AgendaControle.listaConsultarCliente}" rows="10" var="cliente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.codigo}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.pessoa.nome}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_situacao}"/>
                        </f:facet>
                        <h:outputText value="#{agenda.situacao_Apresentar}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_matricula}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.matricula}"/> 
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AgendaControle.selecionarCliente}" focus="cliente" reRender="form, formCliente" oncomplete="Richfaces.hideModalPanel('panelCliente')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagensCRM/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10" id="scResultadoCliente"/>
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AgendaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AgendaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>



    <h:form id="form">
        <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">


            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_passivo}" />
            <h:panelGroup>
                <h:inputText  id="passivo" size="50" maxlength="50" readonly="true" styleClass="camposSomenteLeitura" value="#{AgendaControle.agendaVO.passivo.nome}" />
                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelPassivo')"image="imagensCRM/informacao.gif" alt="#{msg_aplic.prt_Agenda_consultarPassivo}"/>
                <rich:spacer width="5" />
                <a4j:commandButton id="limparPassivo" immediate="true" action="#{AgendaControle.limparCampoPassivo}" image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="passivo"/>
            </h:panelGroup>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_indicacao}" />
            <h:panelGroup>
                <h:inputText  id="indicado" size="50" maxlength="50" readonly="true" styleClass="camposSomenteLeitura" value="#{AgendaControle.agendaVO.indicado.nomeIndicado}" />
                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelIndicado')" image="imagensCRM/informacao.gif" alt="#{msg_aplic.prt_Agenda_consultarIndicacao}"/>
                <rich:spacer width="5" />
                <a4j:commandButton id="limparIndicado" immediate="true" action="#{AgendaControle.limparCampoIndicado}" image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="indicado"/>
            </h:panelGroup>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_cliente}" />
            <h:panelGroup>
                <h:inputText  id="cliente" size="50" maxlength="50" readonly="true" styleClass="camposSomenteLeitura" value="#{AgendaControle.agendaVO.cliente.pessoa.nome}" />
                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelCliente')"image="imagensCRM/informacao.gif" alt="#{msg_aplic.prt_Agenda_consultarCliente}"/>
                <rich:spacer width="5" />
                <a4j:commandButton id="limparCliente" immediate="true" action="#{AgendaControle.limparCampoCliente}" image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="cliente"/>
            </h:panelGroup>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_dia}" />
            <h:panelGroup>
                <a4j:outputPanel layout="block">
                    <rich:calendar id="dia" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" value="#{AgendaControle.agendaVO.dia}" enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy" showApplyButton="false" cellWidth="24px" cellHeight="24px" style="width:200px" inputClass="campos" showFooter="false"/>
                </a4j:outputPanel>
            </h:panelGroup>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_hora}" />
            <h:inputText  id="hora" size="5" maxlength="5" styleClass="campos" value="#{AgendaControle.agendaVO.hora}" />
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_tipoAgendamento}" />
            <h:panelGroup>
                <h:selectOneMenu  id="tipoAgendamento" styleClass="camposObrigatorios" value="#{AgendaControle.agendaVO.tipoAgendamento}" >
                    <f:selectItems  value="#{AgendaControle.listaSelectItemTipoAgendamentoAgenda}" />
                </h:selectOneMenu>
            </h:panelGroup>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_modalidade}" />
            <h:panelGroup id="panelModalidade">
                <h:inputText id="textModalidade" size="50" styleClass="camposSomenteLeitura" value="#{AgendaControle.agendaVO.modalidade.nome}"/>
                <rich:suggestionbox   height="200" width="200"
                                      for="textModalidade"
                                      fetchValue="#{result.nome}"
                                      suggestionAction="#{AgendaControle.autocompleteModalidade}"
                                      minChars="3" rowClasses="20"
                                      nothingLabel="Nenhuma Modalidade encontrada !"
                                      var="result"  id="suggestionModalidade">

                    <h:column>
                        <h:outputText value="#{result.nome}" />
                    </h:column>
                </rich:suggestionbox>
                <a4j:commandButton id="limparClienteQueIndicou" ajaxSingle="true" action="#{IndicacaoControle.limparCampoClienteQueIndicou}" image="imagens/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="form:informacoesIndicador, form:panelClienteQueIndicou"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText  id="modalidade" size="50" maxlength="50" styleClass="camposObrigatorios" value="#{AgendaControle.agendaVO.modalidade}" />
            </h:panelGroup>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_colaborador}" />
            <h:panelGroup>
                <h:inputText  id="colaboradorResponsavel" size="40" maxlength="40" readonly="true" styleClass="camposSomenteLeitura" value="#{AgendaControle.agendaVO.colaboradorResponsavel.pessoa.nome}" />
                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelColaborador')"image="imagensCRM/informacao.gif" alt="#{msg_aplic.prt_Agenda_consultarColaborador}"/>
                <rich:spacer width="5" />
                <a4j:commandButton id="limparColaborador" immediate="true" action="#{AgendaControle.limparCampoColaborador}" image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="colaborador"/>
            </h:panelGroup>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_usuarioResponsavelCadastro}" />
            <h:panelGroup>
                <h:inputText  id="responsavelCadastro" size="40" maxlength="40" readonly="true" styleClass="camposSomenteLeitura" value="#{AgendaControle.agendaVO.responsavelCadastro.pessoa.nome}" />
                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelResponsavelCadastro')"image="imagensCRM/informacao.gif" alt="#{msg_aplic.prt_Agenda_consultarUsuarioResponsavelCadastro}"/>
                <rich:spacer width="5" />
                <a4j:commandButton id="limparResponsavelCadastro" immediate="true" action="#{AgendaControle.limparCampoResponsavelCadastro}" image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="usuarioResponsavelCadastro"/>
            </h:panelGroup>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_totalContatos}" />
            <h:inputText  id="totalContatos" size="10" maxlength="10" styleClass="campos" value="#{AgendaControle.agendaVO.totalContatos}" />
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_realizouVenda}" />
            <h:selectBooleanCheckbox id="realizouVenda"styleClass="campos"value="#{AgendaControle.agendaVO.realizouVenda}"/>
            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_observacaoAgenda}" />
            <h:inputTextarea id="observacaoAgenda" cols="70" rows="3" styleClass="campos" value="#{AgendaControle.agendaVO.observacaoAgenda}"/>

        </h:panelGrid>

        <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
            <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                <h:outputText styleClass="mensagem"  value="#{AgendaControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{AgendaControle.mensagemDetalhada}"/>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup>
                    <h:commandButton id="novo" immediate="true" action="#{AgendaControle.novo}" image="./imagensCRM/botaoNovo.png" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                    <rich:spacer width="10"/>
                    <a4j:commandButton id="salvar" action="#{AgendaControle.gravar}" reRender="form" oncomplete="#{AgendaControle.msgAlert}" value="#{msg_bt.btn_gravar}" image="./imagensCRM/botaoGravar.png" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                    <rich:spacer width="10"/>
                    <h:commandButton id="excluir" onclick="return confirm('#{msg.msg_ConfirmaExclusao}');" action="#{AgendaControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagensCRM/botaoExcluir.png" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>
                    <rich:spacer width="10"/>
                    <h:commandButton id="consultar" immediate="true" action="#{AgendaControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
</f:view>
<script>
    document.getElementById("form:consulta").focus();
</script>
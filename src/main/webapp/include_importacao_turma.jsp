<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="includes/imports.jsp" %>
<style>
    .subordinado {
        padding: 5px !important;
    }

    .item-li {
        margin-left: 2%;
        line-height: 15px
    }
</style>

<h:panelGroup layout="block" id="panelGeralTurma">

    <h:panelGroup layout="block" styleClass="panelObjetivoImportacao">
        <h:outputLabel value="Objetivo:" styleClass="textoObjetivo"
                       style="font-weight: bold; font-style: italic;"/>
        <h:outputLabel styleClass="textoObjetivo" style="padding-left: 5px"
                       value="Realizar importação de turmas através de uma planilha modelo disponibilizada para download."/>
    </h:panelGroup>

    <h:panelGroup layout="block" style="padding: 15px;">


        <h:panelGroup layout="block" styleClass="panelPassosImport">
            <h:outputLabel value="1º passo:" styleClass="passosImportacao"/>
            <a4j:commandLink target="_blank"
                             style="padding-left: 5px; font-size: 15px;"
                             value="Baixar planilha modelo"
                             oncomplete="location.href='../DownloadSV?mimeType=application/vnd.ms-excel&diretorio=modelo&relatorio=modelo_importacao_cadastro_turma.xlsx'"/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassosImport">
            <h:outputLabel value="2º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Preencher a planilha seguindo as seguintes regras:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno" style="font-size: 15px;">
                       <span>
                           <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Campos obrigátorios para importar as Turmas:
                                    <ul>
                                       <li class="item-li"><b>ID_EMPRESA</b> - Informar o código da da empresa na qual será importada, caso esteja vazio irá considerar a empresa logada.</li>
                                       <li class="item-li"><b>ID_EXTERNO_TURMA</b> - Informar o código da turma no sistema antigo</li>
                                       <li class="item-li"><b>TURMA</b> - Informar o nome da turma</li>
                                       <li class="item-li"><b>MODALIDADE</b> - Informar o nome da modalidade</li>
                                       <li class="item-li"><b>VIGENCIA_DE</b> - Informar a vigência de padrão dd/MM/yyyy</li>
                                       <li class="item-li"><b>VIGENCIA_ATE</b> - Informar a vigência ate padrão dd/MM/yyyy</li>
                                       <li class="item-li"><b>DIAS_SEMANA</b> - Informar o dias da semana separados por virgula(SEG,TER,QUA,QUI,SEX,SAB,DOM) ou por espaço(SEG TER QUA QUI SEX SAB DOM)</li>
                                       <li class="item-li"><b>HORA_INICIO</b> - Informar a hora inicio padrão 00:00</li>
                                       <li class="item-li"><b>HORA_FIM</b> - Informar a hora fim padrão 00:00</li>
                                       <li class="item-li"><b>CAPACIDADE</b> - Informar a capacidade</li>
                                       <li class="item-li"><b>ID_PROFESSOR</b> - Informar o codigo do professor cadastrado no sistema</li>
                                       <li class="item-li"><b>AMBIENTE</b> - Informar o nome do ambiente</li>
                                       <li class="item-li"><b>NIVEL</b> - Informar o nivel (iniciante, avançado etc.)</li>
                                    </ul>
                               </li>
                           </ul>
                       </span>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassosImport">
            <h:outputLabel value="3º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Fazer o Upload da planilha baixada e realizar a importação:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelUploadFileTurma" style="padding-top: 5px; padding-left: 30px">
            <rich:fileUpload
                    fileUploadListener="#{ImportacaoControle.uploadArquivoTurma}"
                    immediateUpload="true" id="imagemModeloUploadTurma"
                    acceptedTypes="xls,xlsx" allowFlash="false"
                    listHeight="58px"
                    cancelEntryControlLabel="Cancelar"
                    addControlLabel="Adicionar"
                    clearControlLabel="Remover"
                    clearAllControlLabel="Remover Todos"
                    doneLabel="Concluído"
                    sizeErrorLabel="Limite de tamanho atingido"
                    uploadControlLabel="Carregar"
                    transferErrorLabel="Erro na transferência"
                    stopControlLabel="Parar"
                    stopEntryControlLabel="Parar"
                    progressLabel="Carregando"
                    maxFilesQuantity="1">
                <a4j:support event="onerror" reRender="panelBotoesImportacaoTurma"
                             action="#{ImportacaoControle.removerArquivo}"/>
                <a4j:support event="onupload" reRender="panelBotoesImportacaoTurma"/>
                <a4j:support event="onuploadcomplete" reRender="panelBotoesImportacaoTurma"/>
                <a4j:support event="onclear" reRender="panelBotoesImportacaoTurma, panelUploadFileTurma"
                             action="#{ImportacaoControle.removerArquivo}"/>
            </rich:fileUpload>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" id="panelBotoesImportacaoTurma"
                  styleClass="panelBotoesImportacao">
        <a4j:commandLink id="btnImportarTurma" value="Ler Arquivo"
                         style="padding-left: 15px"
                         onclick="atualizarTempoImportacao()"
                         rendered="#{ImportacaoControle.apresentarImportar}"
                         action="#{ImportacaoControle.processarArquivoTurma}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         title="Processar Importação de Dados"
                         reRender="panelGeralModalConfirmarImportacao, formModImpo"
                         styleClass="botoes nvoBt"/>
    </h:panelGroup>

</h:panelGroup>


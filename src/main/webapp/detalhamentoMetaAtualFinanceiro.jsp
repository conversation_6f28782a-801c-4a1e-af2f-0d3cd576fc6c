
<%@include file="pages/finan/includes/include_imports.jsp" %>
<link href="css/financeiro.css" rel="stylesheet" type="text/css">
<link href="css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script src="${contextoFinan}/script/packJQueryPlugins.min.js" type="text/javascript" ></script>
<style>
	body, html {
		background-color: #FFF !important;
	}

</style>

<script type="text/javascript" language="javascript" src="${contextoFinan}/script/script.js"></script>
<script type="text/javascript" language="javascript" src="${contextoFinan}/script/ce_script.js"></script>
<script type="text/javascript" language="javascript">var contextoFinan = '${contextoFinan}';</script>
<script type="text/javascript" language="javascript" src="${contextoFinan}/script/ajuda.js"></script>
<script type="text/javascript">
	function preencherHiddenChamarBotaoFat(idBotao, idHidden, valorHidden) {
		var hidden = document.getElementById(idHidden);
		var botao = document.getElementById(idBotao);
		hidden.value = valorHidden;
		botao.click();
	}

	function preencherHiddenChamarBotao(idBotao, idHidden, idHidden2, valorHidden, valorHidden2) {
		var hidden = document.getElementById(idHidden);
		var hidden2 = document.getElementById(idHidden2);
		var botao = document.getElementById(idBotao);
		hidden.value = valorHidden;
		hidden2.value = valorHidden2;
		botao.click();
	}

	function atualizarPaginacao() {
		var componente = jQuery('div[id*=paginacaoDataScroller]');
		Event.fire(componente[0], 'rich:datascroller:onscroll', {'page': 'first'});

	}

</script>

<f:view>
	<jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Detalhes do Cumprimento da Meta"/>
    </title>

    <h:form id="form">

		<c:set var="titulo" scope="session" value="Detalhes do Cumprimento da Meta"/>
		<c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-metas-financeiras-de-venda-adm/"/>
        <c:set var="iconeWikiEquivalentes" scope="request" value="true"/>
		<h:panelGroup layout="block" styleClass="pure-g-r">
			<f:facet name="header">
				<jsp:include page="topoReduzido_material.jsp"/>
			</f:facet>
		</h:panelGroup>
        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                 		<rich:spacer height="10px"/>
		                <rich:panel>
		                	<table width="100%">
		                	<tr>
		                		<td width="50%" valign="top">
		                			<h:outputText value="Meta: " styleClass="tituloDemonstrativo"/>
		                			<h:outputText value="#{MetaFinanceiroBIControle.meta.descricao}" styleClass="tituloDemonstrativo"/>
		                			<br/>
									<h:outputText value="#{MetaFinanceiroBIControle.mesAtual}" styleClass="tituloDemonstrativo"/>
									<br/>
									<rich:spacer height="20px"/>
									<h:outputText styleClass="tituloDemonstrativo"
												  rendered="#{MetaFinanceiroBIControle.apresentarApenasMetaConsultor}"
											      value="#{msg_aplic.prt_Finan_colaborador}: #{MetaFinanceiroBIControle.nomeUsuarioLogado}"/>
									<a4j:commandLink styleClass="linkPadrao texto-size-16-real" rendered="#{!MetaFinanceiroBIControle.apresentarApenasMetaConsultor}"
													   oncomplete="Richfaces.showModalPanel('modalPanelFiltrosDetalhamentoMeta');">
                                        <i class="fa-icon-filter"></i>
                                        <span class="texto-font texto-size-16">Filtrar</span>
									</a4j:commandLink>

                                    <br/>
                                    <br/>

                                </td>
		                		<td width="50%" valign="top">
		                				<h:outputText value="#{msg_aplic.prt_Finan_metaAtingidaAte}: " 
			                						  styleClass="tituloDemonstrativo"/>
			                			<h:outputText value="#{MetaFinanceiroBIControle.dataMetaAtualizada}" styleClass="tituloDemonstrativo">
			                				<f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>  
			                			</h:outputText>
		                				<br/>
										<rich:spacer height="20px"/>
				                		<h:panelGroup id="metaAtingida" style="background-color: #{MetaFinanceiroBIControle.meta.cor};padding: 2px; border-width: 2px; border-style: solid; border-color: #000;" >
				                			<h:outputText styleClass="tituloDemonstrativo" style="color: #{MetaFinanceiroBIControle.meta.corTexto};" 
														  value="#{MetaFinanceiroBIControle.meta.metaAtingida_Apresentar}">
											</h:outputText>
										</h:panelGroup>
									<a4j:commandLink id="atualizarDetalhamento" reRender="form"
													 oncomplete="montarTips();"
													 title="Obter dados em tempo real"
													 style="font-size: 20px; margin-left: 5px;"
													 styleClass="fa-icon-refresh"
													 action="#{MetaFinanceiroBIControle.atualizarDetalhamentoMetaFinan}">
									</a4j:commandLink>
										<br/>
								</td>
		                	</tr>
		                	</table>
		                	<rich:spacer height="10px"/>
									<rich:panel rendered="#{!MetaFinanceiroBIControle.apresentarApenasMetaConsultor}">
										<h:outputLink styleClass="linkWiki"
													  value="#{SuperControle.urlBaseConhecimento}bi-resumo-financeiro-financeiro/"
													  title="Clique e saiba mais: Resumo Financeiro"
													  target="_blank">
											<i class="fa-icon-question-sign" style="font-size: 18px"></i>
										</h:outputLink>
										<h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_filtros}: "/>
										<h:outputText escape="false" styleClass="tituloDemonstrativo" value="#{MetaFinanceiroBIControle.filtros}"/>
									</rich:panel>
		                </rich:panel>
		<%@include file="pages/finan/includes/include_detalhamentoMetaAtual.jsp" %>
						
		</h:panelGrid>
					<h:panelGroup layout="block" styleClass="container-botoes">
                       <a4j:commandLink oncomplete="#{MetaFinanceiroBIControle.mensagemNotificar}#{MetaFinanceiroBIControle.msgAlert}" action="#{MetaFinanceiroBIControle.validarPermissaoImpressao}"
                       					 styleClass="botaoPrimario texto-size-16"	value="Visualizar Impress�o">
                       </a4j:commandLink>
                    </h:panelGroup>

					<h:inputHidden id="idDataFatRealizado"
						value="#{MetaFinanceiroBIControle.dataListaFaturamentoRealizado}" />
					<h:inputHidden id="idDataFatAcumulado"
						value="#{MetaFinanceiroBIControle.dataListaFaturamentoAcumulado}" />
					<a4j:commandButton style="visibility: hidden;"
						oncomplete="Richfaces.showModalPanel('modalLancamentos'); atualizarPaginacao();"
						id="botaoVisualizarListaRealizados" reRender="modalLancamentos"
						action="#{MetaFinanceiroBIControle.consultarListaFaturamentoRealizado}">
					</a4j:commandButton>
					<a4j:commandButton style="visibility: hidden;"
						oncomplete="Richfaces.showModalPanel('modalLancamentos'); atualizarPaginacao();"
						id="botaoVisualizarListaAcumulados" reRender="modalLancamentos"
						action="#{MetaFinanceiroBIControle.consultarListaFaturamentoAcumulado}">
					</a4j:commandButton>
           
            </h:form>
    
    <%@include file="pages/finan/includes/include_modal_FiltrosMetaAtual.jsp" %>
    <%@include file="pages/finan/includes/include_modal_LancamentosDia.jsp" %>
</f:view>

<%--
    Document   : afastamentoContrato
    Created on : 12/04/2011, 16:31:09
    Author     : carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>


<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
    setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<script>
    jQuery.noConflict();
    window.addEventListener("load", function (event) {
        executePostMessage({loaded: true})
    });
  </script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <style>
        .circulo {
            width: 100px !important;
            height: 100px !important;
            margin-top: 20px !important;
            color: white; !important;
            text-align: center;
            font-size: 50px;
            border-radius: 50%;
            position: relative;
            top: 6%;
            left: 14%;
            box-shadow: 1px 1px 8px 0 #000000;
        }

        .descricao{
            margin-top: 25px !important;
            text-align: center;
            width: 140px;
        }

        .card{
            width: 140px;
            height: 180px;
            border-width: 1px;
            border-color: white;
            border-radius: 2px;
            margin-bottom: 10px;
            transition: box-shadow .8s;

        }
        .card:hover {
            border-width: 1px;
            border-radius: 2px;
            border-color: #29AbE2;
            box-shadow: 0 1px 8px 0 #29AbE2;
        }

        .show-help {
            display: none !important;
        }

        .card:hover .show-help{
            display: inline !important;
        }

        .help{
            margin-top: 5px !important;
            text-align: center;
            width: 140px;
        }

        a.fill-div {
            text-align: center;
            display: block;
            height: 100%;
            width: 100%;
        }
    </style>
    <title>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"/>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_afastamentoContrato_tituloForm}"/>
    </title>
    <jsp:include page="topoReduzido_material.jsp"/>
    <h:form id="form">
        <div styleClass="row">
            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

            <div class="col-md-6 col-md-offset-3" >
                <div class="col-md-4" >
                    <div class="card">
                        <a4j:commandLink disabled="#{!ClienteControle.apresentarCarencia}" styleClass="fill-div" action="#{CarenciaContratoControle.novo}" id="linkAfasCarencia">
                            <h:panelGrid rendered="#{!ClienteControle.apresentarCarencia}" styleClass="circulo" style="background-color:#DCDCDC;">
                                <i class="fa-icon-ship " />
                            </h:panelGrid>
                            <h:panelGrid rendered="#{ClienteControle.apresentarCarencia}" styleClass="circulo" style="background-color:#654FFF;">
                                <i class="fa-icon-ship " />
                            </h:panelGrid>
                            <h:panelGrid styleClass="descricao">
                                <h:panelGroup>
                                    <span class="texto-size-12 texto-cor-cinza texto-font texto-bold" >FÉRIAS</span>
                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-lancar-ferias-para-aluno/"
                                                  title="Clique e saiba mais: Férias" target="_blank" styleClass="tooltipster">
                                        <i class="fa-icon-question-sign texto-cor-cinza" style="font-size: 18px"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid styleClass="help texto-size-25 texto-cor-cinza texto-font texto-bold">
                                <i class="fa-icon-question-sign show-help tooltipster" title="Deixar de freqüentar por alguns</br> dias, fazendo uso de um período</br> autorizado pelo contrato."/>
                            </h:panelGrid>
                        </a4j:commandLink>
                    </div>
                </div>
                <div class="col-md-4 col-md-offset-4">
                    <div class="card">
                        <a4j:commandLink id="linkAfasAtestado" disabled="#{!ClienteControle.apresentarAtestado}" styleClass="fill-div" action="#{AtestadoContratoControle.novo}">
                            <h:panelGrid rendered="#{!ClienteControle.apresentarAtestado}" styleClass="circulo" style="background-color:#DCDCDC;" title="Já existe um atestado lançado ou a configuração do plano não permite situação atestado">
                                <i class="fa-icon-ambulance" />
                            </h:panelGrid>
                            <h:panelGrid rendered="#{ClienteControle.apresentarAtestado}" styleClass="circulo" style="background-color:#FF3333;">
                                <i class="fa-icon-ambulance" />
                            </h:panelGrid>    
                            <h:panelGrid styleClass="descricao">
                                <h:panelGroup>
                                    <span class="texto-size-12 texto-cor-cinza texto-font texto-bold" >ATESTADO</span>
                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-lancar-um-atestado-medico/"
                                                  title="Clique e saiba mais: Atestado" target="_blank" styleClass="tooltipster">
                                        <i class="fa-icon-question-sign texto-cor-cinza" style="font-size: 18px"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid styleClass="help texto-size-25 texto-cor-cinza texto-font texto-bold">
                                <i class="fa-icon-question-sign show-help tooltipster" title="Afastamento por motivos médicos"/>
                            </h:panelGrid>
                        </a4j:commandLink>
                    </div>
                </div>
            </div>
            <div class="col-md-4"></div>
            <div class="col-md-6 col-md-offset-3" >
                <div class="col-md-4" >
                    <div class="card">
                        <a4j:commandLink  id="linkAfasTrancamento" disabled="#{!ClienteControle.apresentarTrancamento}" styleClass="fill-div" action="#{TrancamentoContratoControle.novo}">
                            <h:panelGrid rendered="#{!ClienteControle.apresentarTrancamento}" styleClass="circulo" style="background-color:#DCDCDC;">
                                <i class="fa-icon-lock"/>
                            </h:panelGrid>
                             <h:panelGrid rendered="#{ClienteControle.apresentarTrancamento}" styleClass="circulo" style="background-color:#FF7300;">
                                <i class="fa-icon-lock"/>
                            </h:panelGrid>    
                            <h:panelGrid styleClass="descricao">
                                <h:panelGroup>
                                    <span class="texto-size-12 texto-cor-cinza texto-font texto-bold" >TRANCAMENTO</span>
                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-lancar-um-trancamento-para-um-aluno/"
                                                  title="Clique e saiba mais: Trancamento" target="_blank" styleClass="tooltipster">
                                        <i class="fa-icon-question-sign texto-cor-cinza" style="font-size: 18px"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid styleClass="help texto-size-25 texto-cor-cinza texto-font texto-bold">
                                <i class="fa-icon-question-sign show-help tooltipster" title="Afastamento por mais dias que o</br> período de férias permite,</br> e por um período indeterminado</br> a escolha do cliente."/>
                            </h:panelGrid>
                        </a4j:commandLink>
                    </div>
                </div>
                <div class="col-md-4 col-md-offset-4">
                    <div class="card">
                        <a4j:commandLink id="linkCancelamento" disabled="#{!ClienteControle.apresentarCancelamento}" styleClass="fill-div" action="#{CancelamentoContratoControle.novo}" oncomplete="#{ClienteControle.urlPopup}" reRender="panelParcelasRemessas">
                            <h:panelGrid rendered="#{!ClienteControle.apresentarCancelamento}" styleClass="circulo" style="background-color:#DCDCDC;" title="#{ClienteControle.motivoCancelamentoDesabilitado}">
                                <i class="fa-icon-ban-circle"></i>
                            </h:panelGrid>
                            <h:panelGrid rendered="#{ClienteControle.apresentarCancelamento}" styleClass="circulo" style="background-color:#AB0000;">
                                <i class="fa-icon-ban-circle"></i>
                            </h:panelGrid>
                            <h:panelGrid styleClass="descricao">
                                <h:panelGroup>
                                    <span class="texto-size-12 texto-cor-cinza texto-font texto-bold" >CANCELAMENTO</span>
                                    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"
                                                  title="Clique e saiba mais: Cancelamento do Contrato" target="_blank" styleClass="tooltipster">
                                        <i class="fa-icon-question-sign texto-cor-cinza" style="font-size: 18px"></i>
                                    </h:outputLink>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid styleClass="help texto-size-25 texto-cor-cinza texto-font texto-bold "
                                         rendered="#{!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                                <i class="fa-icon-question-sign show-help tooltipster" title="Afastamento definitivo pegando</br> o dinheiro de volta ou para </br>transferir os dias para outro cliente </br>com contrato ativo."></i>
                            </h:panelGrid>
                            <h:panelGrid styleClass="help texto-size-25 texto-cor-cinza texto-font texto-bold "
                                         rendered="#{CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                                <i class="fa-icon-question-sign show-help tooltipster" title="Cancelamento definitivo do contrato do aluno."></i>
                            </h:panelGrid>
                            <f:setPropertyActionListener value="Cancelamento só poderá ser realizado após o retorno das remessas listadas acima" target="#{ClienteControle.tituloModalParcelaRemessa}"/>
                        </a4j:commandLink>
                    </div>
                </div>
            </div>
            <div class="col-md-4"></div>
        </div>
    </h:form>


    <jsp:include page="include_modal/aulasDesmarcadas.jsp"/>
    <jsp:include page="include_modal/modalControleCreditoTreino.jsp"/>
    <jsp:include page="include_modal/modalConvidado.jsp"/>
    <jsp:include page="include_modal/modalConvidadoHistorico.jsp"/>
    <jsp:include page="include_modal/modalDesfazerCancelamento.jsp"/>
    <jsp:include page="include_modal/modalEnviarNotaEmail.jsp"/>
    <jsp:include page="include_modal/modalFaltasAluno.jsp"/>
    <jsp:include page="include_modal/modalFiltroCredito.jsp"/>
    <jsp:include page="include_modal/modalGymPass.jsp"/>
    <jsp:include page="include_modal/modalGymPassHistorico.jsp"/>
    <jsp:include page="include_modal/modalTotalPass.jsp"/>
    <jsp:include page="include_modal/modalTotalPassHistorico.jsp"/>
    <jsp:include page="include_modal/modalPanelContratoArmario.jsp"/>
    <jsp:include page="include_modal/modalRemoverObjecaoDefinitiva.jsp"/>
    <jsp:include page="include_modal/modalTrocarCartaoContratoCliente.jsp"/>
    <jsp:include page="include_modal/panelAgendaAluno.jsp"/>
    <jsp:include page="include_modal/panelAlterarMatricula.jsp"/>
    <jsp:include page="include_modal/panelAulaDesmarcada.jsp"/>
    <jsp:include page="include_modal/panelAutorizarEdicaoPagamento.jsp"/>
    <jsp:include page="include_modal/panelContratoOperacao.jsp"/>
    <jsp:include page="include_modal/panelHistoricoContrato.jsp"/>
    <jsp:include page="include_modal/panelIncludeContratoPrestacao.jsp"/>
    <jsp:include page="include_modal/panelParcelasRemessas.jsp"/>
    <jsp:include page="include_modal/panelStatusTrocaCartao.jsp"/>
    <jsp:include page="include_modal/panelUsuarioSenhaObservacaoGeral.jsp"/>
    <jsp:include page="includes/cliente/include_modal_editar_vigencia_final_produto_cliente.jsp"/>
    <jsp:include page="includes/cliente/include_modal_escolher_produto_renovar_cliente.jsp"/>
    <jsp:include page="includes/cliente/include_modal_reposicoes.jsp"/>

    <script>
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    </script>
</f:view>

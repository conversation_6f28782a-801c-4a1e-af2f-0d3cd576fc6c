<%-- 
    Document   : 
--%>
<%@include file="includes/imports.jsp" %>

<rich:modalPanel id="panelExisteCliente" autosized="true" shadowOpacity="true" styleClass="novaModal"
                 top="2"
                 showWhenRendered="#{ClienteControle.clienteVO.apresentarRichModalErro}" width="600">
    <f:facet name="header">
        <h:outputText value="Aten��o!"/>
    </f:facet>
    <a4j:form id="formExisteCliente">
        <jsp:include page="includes/include_persiste_modulo.jsp"/>
        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza" value="#{ClienteControle.clienteVO.msgErroExisteCliente}" />
            <h:panelGroup rendered="#{!ClienteControle.permiteTransferirContratoAtivo}"
                          layout="block"
                          style="padding-top: 10px;">
                <div style="padding-top: 10px;">
                    <span style="color: orangered; padding-top: 10px;">O aluno n�o pode ser transferido porque ele tem contrato ativo e seu perfil de acesso n�o tem a permiss�o: 2.85 - Permiss�o para transferir clientes com contrato ativo</span>
                </div>
            </h:panelGroup>
            <h:panelGroup rendered="#{ClienteControle.permiteTransferirContrato}"
                    layout="block"
                    style="padding-top: 10px;">
                <div>
                    <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza"
                                  value="Ao transferir este aluno, o contrato ser� cancelado na empresa de origem e um novo contrato com a mesma vig�ncia final
                                  ser� gerado na empresa de destino. Sendo assim, todas as parcelas em aberto do cliente ser�o transferidas para empresa de destino.">
                    </h:outputText>
                </div>
                <div style="padding-top: 10px;">
                    <h:outputText rendered="#{!ClienteControle.permiteTransferirContrato}"
                                  style="color: orangered; padding-top: 10px;"
                                  styleClass="texto-size-14 texto-font texto-cor-cinza"
                                  value="O aluno n�o pode ser transferido porque o seu plano
                                  (#{ClienteControle.contratoBloqueioTransferenciaPorNaoPermitirVendaEmpresa.plano.descricao}) n�o pode ser vendido nesta unidade.
                              Para realizar esta transfer�ncia voc� precisa fazer o cancelamento manual do contrato atual do aluno e lan�ar um novo contrato nesta empresa.">
                    </h:outputText>
                </div>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelMsgAdicional" style="padding-top: 10px;">
                <div style="padding-top: 10px;">
                    <h:outputText styleClass="texto-size-14 texto-font texto-cor-vermelho"
                                  value="#{ClienteControle.msgAdicionalTrocaEmpresa}">
                    </h:outputText>
                </div>
            </h:panelGroup>

            <h:panelGroup>
                <div style="padding-top: 10px;">
                    <h:outputText styleClass="texto-size-14 texto-font texto-cor-vermelho"
                                  value="#{ClienteControle.msgTrocarConvenioCobrancaTrocaEmpresa}">
                    </h:outputText>
                </div>
            </h:panelGroup>

            <h:panelGroup rendered="#{ClienteControle.apresentarTrocaConvenioCobrancaTrocaEmpresa}"
                          layout="block" id="panelTrocarConvenioCobranca" style="padding-top: 10px;">
                <h:panelGroup layout="block"
                              rendered="#{fn:length(ClienteControle.listaConvenioCobrancaTrocaEmpresa) > 0}"
                              style="padding-top: 10px; padding-bottom: 10px">
                    <table class="tblHeaderLeft semZebra" style="padding: 10px; width: 100%; margin: 0;">
                        <thead>
                        </thead>
                        <tbody>
                        <a4j:repeat var="conve" value="#{ClienteControle.listaConvenioCobrancaTrocaEmpresa}">
                            <tr>
                                <td>
                                    <h:selectBooleanCheckbox value="#{conve.selecionado}">
                                        <a4j:support event="onclick" reRender="panelTrocarConvenioCobranca" status="false"
                                                     action="#{ClienteControle.selecionouConvenioCobrancaTrocaEmpresa}"/>
                                        <f:attribute name="prefixo" value="#{conve}"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText style="margin-left: 3px" value="#{conve.descricao}"/>
                                </td>
                            </tr>
                        </a4j:repeat>
                        <tr>
                            <td>
                                <h:selectBooleanCheckbox value="#{ClienteControle.naoAlterarConvenioCobrancaTrocaEmpresa}">
                                    <a4j:support event="onclick" reRender="panelTrocarConvenioCobranca" status="false"
                                                 action="#{ClienteControle.selecionouNaoAlterarConvenioCobrancaTrocaEmpresa}"/>
                                </h:selectBooleanCheckbox>
                                <h:outputText style="margin-left: 3px" value="N�o alterar o conv�nio de cobran�a"/>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="btnTransferirClienteEmpresa"
                                       rendered="#{ClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa && LoginControle.permissaoAcessoMenuVO.permissaoTransferirClienteEmpresa}"
                                       accesskey="9"
                                     oncomplete="#{ClienteControle.mensagemNotificar}"
                                       styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                       action="#{ClienteControle.trocarClienteEmpresa}">
                        Transferir Cliente de Empresa <i class="fa-icon-exchange"></i>
                    </a4j:commandLink>

                    <a4j:commandButton id="btnIrParaTelaCliente"
                                       rendered="#{!ClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa}"
                                       styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                       style="margin-right: 30%; height: 36px;"
                                       value="Ver informa��es do cliente"
                                       action="#{ClienteControle.editarCliente}"/>
                    <rich:spacer width="12px"/>

                    <a4j:commandLink id="btnFechar"
                                       reRender="codigo,empresa,panelFoto,nomeCliente,dataNascCliente,categoria,nomeMae,nomePai,sexo,
                                   profissao,grauIntrucao,estadoCivil,cpf,rg,rgOrgao,rgUf,dataCadastro,residencial,comercial,
                                   celular,email,webPage,CEP,endereco,complento,bairro,numero,pais,estado,cidade,estado"
                                       accesskey="5"
                                       styleClass="botaoSecundario texto-size-16-real"
                                       oncomplete="Richfaces.hideModalPanel('panelExisteCliente')"
                                       value="Fechar"/>
                </h:panelGroup>
                <h:outputText rendered="#{ClienteControle.clienteVO.apresentarBotaoTransferirClienteEmpresa && !LoginControle.permissaoAcessoMenuVO.permissaoTransferirClienteEmpresa}"
                              styleClass="mensagemDetalhada"
                              value="Usu�rio sem permiss�o para transferir aluno: '2.52 - Permiss�o para transferir cliente de empresa'"/>

            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

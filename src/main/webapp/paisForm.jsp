<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Pais_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Pais_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Entidade:Pais"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">

            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{PaisControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pais_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{PaisControle.paisVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Pais_nome}" />
                    <h:panelGroup>
                        <h:inputText  id="nome"  size="40" maxlength="40" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{PaisControle.paisVO.nome}" />
                        <h:message for="nome" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pais_nacionalidade}"/>
                    <h:panelGroup>
                        <h:inputText id="nacionalidade" size="40" maxlength="40" styleClass="form" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" value="#{PaisControle.paisVO.nacionalidade}"/>
                        <h:message for="nacionalidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>
                <h:panelGrid id="panelEstado" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Estado_tituloForm}"/>
                    </f:facet>
                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Estado_sigla}" />
                        <h:inputText  id="estado_sigla" size="2" maxlength="2" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{PaisControle.estadoVO.sigla}" />
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Estado_descricao}" />
                        <h:inputText  id="estado_descricao" size="30" maxlength="30" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{PaisControle.estadoVO.descricao}" />
                    </h:panelGrid>
                    <a4j:commandButton action="#{PaisControle.adicionarEstado}" reRender="form" focus="form:estado_sigla" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                        <h:dataTable id="estadoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                     value="#{PaisControle.paisVO.estadoVOs}" var="estado">
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Estado_sigla}" />
                                </f:facet>
                                <h:outputText  value="#{estado.sigla}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Estado_descricao}" />
                                </f:facet>
                                <h:outputText  value="#{estado.descricao}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                </f:facet>
                                <h:panelGroup>
                                    <h:commandButton id="editarItemVenda" immediate="true" action="#{PaisControle.editarEstado}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                    <h:outputText value="    "/>

                                    <h:commandButton id="removerItemVenda" immediate="true" action="#{PaisControle.removerEstado}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{PaisControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{PaisControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{PaisControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{PaisControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{PaisControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="salvar" action="#{PaisControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                                <h:outputText value="    "/>


                                <h:panelGroup id="grupoBtnExcluir">
                                    <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                       oncomplete="#{PaisControle.msgAlert}" action="#{PaisControle.confirmarExcluir}"
                                                       value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                                </h:panelGroup>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="consultar" immediate="true" action="#{PaisControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                                <rich:spacer width="15px"/>
                                <a4j:commandLink action="#{PaisControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                                    oncomplete="abrirPopup('../faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                    title="Visualizar Log"
                                                 style="display: inline-block; padding: 8px 15px; margin-left: -6px;"
                                                    styleClass="botoes nvoBt btSec">
                                    <i style="text-decoration: none" class="fa-icon-list"/>
                                </a4j:commandLink>
                            </h:panelGroup>

                        </c:if>

                        <c:if test="${modulo eq 'centralEventos'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{PaisControle.novo}"
                                                 value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}"
                                                 accesskey="1" styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/>

                                <a4j:commandButton id="salvar" action="#{PaisControle.gravarCE}"
                                                 value="#{msg_bt.btn_gravar}"
                                                 alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                                 actionListener="#{PaisControle.autorizacao}">
                                    <!-- Entidade.pais -->
                                    <f:attribute name="entidade" value="115" />
                                    <!-- Operacao.GRAVAR -->
                                    <f:attribute name="operacao" value="G" />
                                </a4j:commandButton>



                                <h:outputText value="    "/>

                                <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                 action="#{PaisControle.excluir}"
                                                 value="#{msg_bt.btn_excluir}"
                                                 alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                                                 actionListener="#{PaisControle.autorizacao}">
                                    <!-- Entidade.pais -->
                                    <f:attribute name="entidade" value="115" />
                                    <!-- Operacao.excluir -->
                                    <f:attribute name="operacao" value="E" />
                                </a4j:commandButton>



                                <h:outputText value="    "/>

                                <a4j:commandButton id="consultar" immediate="true" action="#{PaisControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_consultar}"
                                                 alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"
                                                 actionListener="#{PaisControle.autorizacao}">
                                    <!-- Entidade.pais -->
                                    <f:attribute name="entidade" value="115" />
                                    <!-- Operacao.excluir -->
                                    <f:attribute name="operacao" value="C" />
                                </a4j:commandButton>
                            </h:panelGroup>

                        </c:if>

                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>

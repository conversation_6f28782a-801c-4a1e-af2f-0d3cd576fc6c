<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_LocalAcesso_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_LocalAcesso_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}local-de-acesso/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{LocalAcessoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
                           style="display: none"/>
            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens" id="gridMensagens">
                <h:panelGrid columns="1" width="100%">

                    <h:outputText value=" "/>

                </h:panelGrid>
                <h:commandButton rendered="#{LocalAcessoControle.sucesso}" image="./imagens/sucesso.png"/>
                <h:commandButton rendered="#{LocalAcessoControle.erro}" image="./imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" id="mensagem2" value="#{LocalAcessoControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada2"
                                  value="#{LocalAcessoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="localacesso" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_empresa}"/>
                <h:panelGroup>
                    <h:selectOneMenu disabled="#{!LocalAcessoControle.usuarioLogado.administrador}"
                                     id="empresa" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="color:black;"
                                     styleClass="form"
                                     value="#{LocalAcessoControle.localAcesso.empresa.codigo}">
                        <f:selectItems value="#{LocalAcessoControle.listaSelectItemEmpresa}"/>
                    </h:selectOneMenu>
                    <a4j:commandButton rendered="#{LocalAcessoControle.usuarioLogado.administrador}"
                                       id="atualizar_empresa"
                                       action="#{LocalAcessoControle.montarListaSelectItemEmpresa}"
                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                       reRender="form:empresa"/>

                    <h:panelGroup layout="block" style="float: right;padding-right: 30px;"  rendered="#{LocalAcessoControle.localAcesso.versaoAcesso != ''}">
                        <h:outputText styleClass="tituloCampos" style="font-style: italic" value="#{msg_aplic.prt_LocalAcesso_versaoAcesso} : #{LocalAcessoControle.localAcesso.versaoAcesso}"/>
                    </h:panelGroup>

                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_codigo}"/>
                <h:panelGroup>
                    <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                 value="#{LocalAcessoControle.localAcesso.codigo}"/>
                    <h:message for="codigo" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_descricao}"/>
                <h:panelGroup>
                    <h:inputText id="nome" size="40" maxlength="40" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.descricao}"/>
                    <h:message for="nome" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_nomeComputador}"/>
                <h:panelGroup>
                    <h:inputText id="comp" size="40" maxlength="40" onblur="blurinput(this);"
                                 title="#{msg.localAcesso_nomeComputador}" onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.nomeComputador}"/>
                    <h:message for="comp" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_tempoEntreAcessos}"/>
                <h:panelGroup>
                    <h:inputText id="tempo" size="5" maxlength="5" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{LocalAcessoControle.localAcesso.tempoEntreAcessos}"/>
                    <h:outputText styleClass="tituloCampos" value="(min)"/>
                    <h:message for="tempo" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos"
                              value="#{msg_aplic.prt_LocalAcesso_tempoEntreAcessosColaborador}"/>
                <h:panelGroup>
                    <h:inputText id="tempocolaborador" size="5" maxlength="5" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.tempoEntreAcessosColaborador}"/>
                    <h:outputText styleClass="tituloCampos" value="(min)"/>
                    <h:message for="tempocolaborador" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_tempoToleranciaSaida}"/>
                <h:panelGroup>
                    <h:inputText id="tempotolerancia" size="5" maxlength="5" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.tempoToleranciaSaida}"/>
                    <h:outputText styleClass="tituloCampos" value="(min)"/>
                    <h:message for="tempotolerancia" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

                <!-- CAMPO CAPACIDADE LIMITE --->
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_capacidadeLimite}"/>
                <h:panelGroup>
                    <h:inputText id="capacidadeLimiteInputText" size="5" maxlength="3" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.capacidadeLimite}"/>
                    <h:outputText styleClass="tituloCampos" value="(0 = sem limite)"/>
                    <h:message for="capacidadeLimiteInputText" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

                <!-- --->

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_servidorImpressoes}"/>
                <h:panelGroup>
                    <h:inputText id="servidor" size="40" maxlength="40" onblur="blurinput(this);"
                                 title="#{msg.localAcesso_nomeComputador}" onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.servidorImpressoes}"/>
                    <h:message for="servidor" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_portaServidorImp}"/>
                <h:panelGroup>
                    <h:inputText id="porta" size="5" maxlength="5" title="#{msg.localAcesso_portaServidorImp}"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.portaServidorImp}"/>
                    <h:message for="porta" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <c:if test="${LoginControle.permissaoAcessoMenuVO.servidorFacial}">
                    <%--<h:outputText styleClass="tituloCampos" value="Usa Reconhecimento Facial: "/>
                    <h:selectBooleanCheckbox  id="usarReconhecimento"  value="#{LocalAcessoControle.localAcesso.usarReconhecimento}"/>--%>
                    <%--<h:outputText styleClass="tituloCampos" value="Porta Servidor de Imagens: "/>
                    <h:inputText id="portaImagens" size="5" maxlength="5" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.portaImagens}"/>--%>
                    <h:outputText styleClass="tituloCampos" value="Facial Pacto: "/>
                    <h:inputText size="40" maxlength="200" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.urlServidorCamera}"/>

                    <%--<h:outputText styleClass="tituloCampos" value="Porta Servidor de C&lt;%&ndash;âmera: "/>
                    <h:inputText id="portaServidorCamera" size="5" maxlength="5" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.portaServidorCamera}"/>&ndash;%&gt;--%>
                </c:if>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_servidorFacialInner}"/>
                <h:panelGroup>
                    <h:inputText id="servidorFacialInner" size="40" maxlength="40" onblur="blurinput(this);"
                                 title="#{msg_aplic.prt_LocalAcesso_servidorFacialInner}" onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.servidorFacialInner}"/>
                    <h:message for="servidor" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_portaServidorFacialInner}"/>
                <h:panelGroup>
                    <h:inputText id="portaServidorFacialInner" size="5" maxlength="5" title="#{msg_aplic.prt_LocalAcesso_portaServidorFacialInner}"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 value="#{LocalAcessoControle.localAcesso.portaServidorFacialInner}"/>
                    <h:message for="portaServidorFacialInner" styleClass="mensagemDetalhada"/>
                </h:panelGroup>


                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_categoriaLocalAcesso}"/>
                <h:panelGroup>
                    <h:selectOneMenu id="categoriaLocalAcesso" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     style="color:black;"
                                     styleClass="form"
                                     title="#{msg.prt_LocalAcesso_categoriaLocalAcesso}"
                                     value="#{LocalAcessoControle.localAcesso.categoriaLocalAcesso}">
                        <f:selectItems value="#{LocalAcessoControle.listaSelectItemCategoriaLocalAcesso}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_restringirAcessoOutrasUnidades}"/>
                <h:panelGroup>
                    <h:selectBooleanCheckbox id="checkbox_restringirAcessoOutrasUnidades" title="Marque para impedir que alunos de outras unidades tenham o acesso liberado"
                                             value="#{LocalAcessoControle.localAcesso.restringirAcessoOutrasUnidades}"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_pedirSenhaLib}"/>
                <h:panelGroup>
                    <h:selectBooleanCheckbox id="pedirSenhaLib" title="#{msg.localAcesso_pedirSenhaLibParaCadaAcesso}"
                                             value="#{LocalAcessoControle.localAcesso.pedirSenhaLibParaCadaAcesso}"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" title="#{msg_aplic.prt_pedirSenhaCadastrarMaisBiometrias_hint}" value="#{msg_aplic.prt_pedirSenhaCadastrarMaisBiometrias}:"/>
                <h:panelGroup>
                    <h:selectBooleanCheckbox id="pedirSenhaCadastrarMaisBiometrias" title="#{msg_aplic.prt_pedirSenhaCadastrarMaisBiometrias_hint}" value="#{LocalAcessoControle.localAcesso.pedirSenhaCadastrarMaisBiometrias}"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" title="#{msg_aplic.prt_solicitarJustificativaLiberacaoManual_hint}" value="#{msg_aplic.prt_solicitarJustificativaLiberacaoManual}:"/>
                <h:panelGroup>
                    <h:selectBooleanCheckbox id="solicitarJustificativaLiberacaoManual" title="#{msg_aplic.prt_solicitarJustificativaLiberacaoManual_hint}" value="#{LocalAcessoControle.localAcesso.solicitarJustificativaLiberacaoManual}"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" title="#{msg_aplic.prt_ignorarconsumocredito_hint}" value="#{msg_aplic.prt_ignorarconsumocredito}:"/>
                <h:panelGroup>
                    <h:selectBooleanCheckbox id="ignorarconsumocredito" title="#{msg_aplic.prt_ignorarconsumocredito_hint}" value="#{LocalAcessoControle.localAcesso.ignorarConsumoCredito}"/>
                </h:panelGroup>


                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_utilizarModoOffline}"/>
                <h:panelGroup>
                    <h:selectBooleanCheckbox  id="utilizarModoOffline" title="#{msg.localAcesso_utilizarModoOffline}"
                                              value="#{LocalAcessoControle.localAcesso.utilizarModoOffline}">
                        <a4j:support event="onclick" reRender="form"/>
                    </h:selectBooleanCheckbox>
                        
                    <h:outputText value=" "/>
                    <a4j:commandButton reRender="gridMensagens,gridMensagens1,localacesso"
                                       value="Gerar Dados"
                                       onclick="if(!confirm('Deseja gerar os dados offline para esse local de acesso? Isso pode levar alguns minutos.')){return false;}"
                                       rendered="#{LocalAcessoControle.mostrarBotaoGerarDadosOff && LocalAcessoControle.localAcesso.utilizarModoOffline}"
                                       action="#{LocalAcessoControle.atualizarDadosOffLocalAcesso}"/>
                     <h:outputText value=" "/>
                    <h:outputText rendered="#{LocalAcessoControle.localAcesso.utilizarModoOffline}"
                                  styleClass="classInfCadUsuario" value="*Se essa opção estiver marcada, esse local de acesso não poderá ser utilizado na integração de acesso."/>
                </h:panelGroup>

                <h:outputText rendered="#{LocalAcessoControle.localAcesso.utilizarModoOffline}"
                              styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_dataBaseOffline}"/>
                <h:panelGroup rendered="#{LocalAcessoControle.localAcesso.utilizarModoOffline}">
                    <h:outputText rendered="#{LocalAcessoControle.localAcesso.dataBaseOffline == null}"
                                  styleClass="tituloCampos" value="Não houve geração da Base."/>
                    <h:outputText rendered="#{LocalAcessoControle.localAcesso.dataBaseOffline != null}"
                                  styleClass="tituloCampos" value="#{LocalAcessoControle.localAcesso.dataBaseOffline}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </h:panelGroup>

                <h:outputText rendered="#{LocalAcessoControle.localAcesso.utilizarModoOffline}"
                              styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_dataDownloadBase}"/>
                <h:panelGroup rendered="#{LocalAcessoControle.localAcesso.utilizarModoOffline}">
                    <h:outputText rendered="#{LocalAcessoControle.localAcesso.dataDownloadBase == null}"
                                  styleClass="tituloCampos" value="Ainda não houve download da Base."/>
                    <h:outputText rendered="#{LocalAcessoControle.localAcesso.dataDownloadBase != null}"
                                  styleClass="tituloCampos" value="#{LocalAcessoControle.localAcesso.dataDownloadBase}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </h:panelGroup>



            </h:panelGrid>

            <%-- Incluir Formulário de cadastro de Coletores. --%>
            <%@include file="include_CadColetor.jsp" %>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens" id="gridMensagens1">
                    <h:panelGrid columns="1" width="100%">

                        <h:outputText value=" "/>

                    </h:panelGrid>
                    <h:commandButton rendered="#{LocalAcessoControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{LocalAcessoControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" id="mensagem" value="#{LocalAcessoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada"
                                      value="#{LocalAcessoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="novo" immediate="true" action="#{LocalAcessoControle.novo}"
                                         value="#{msg_bt.btn_novo}" alt="#{msg_bt.btn_novoLocal}"
                                         title="#{msg_bt.btn_novoLocal}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                        <h:outputText value="    "/>

                        <a4j:commandButton id="salvar" action="#{LocalAcessoControle.gravar}" value="#{msg_bt.btn_gravar}"
                                         alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                        <h:outputText value="    "/>

                        <h:panelGroup id="grupoBtnExcluir">
                            <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                               oncomplete="#{LocalAcessoControle.msgAlert}" action="#{LocalAcessoControle.confirmarExcluir}"
                                               value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                        </h:panelGroup>


                        <h:outputText value="    "/>

                        <a4j:commandButton id="clonar" rendered="#{LocalAcessoControle.localAcesso.codigo > 0}" action="#{LocalAcessoControle.clonar}" reRender="form"
                                           value="#{msg_bt.btn_Clonar}" alt="#{msg.msg_clonar_dados}" accesskey="4"
                                           styleClass="botoes nvoBt btSec"/>


                        <h:outputText value="    "/>

                        <a4j:commandButton id="consultar" immediate="true"
                                         action="#{LocalAcessoControle.inicializarConsultar}"
                                         value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4"
                                         styleClass="botoes nvoBt btSec"/>

                        <h:outputText value="    "/>
                        <a4j:commandLink action="#{LocalAcessoControle.realizarConsultaLogObjetoSelecionado}"
                                         reRender="form"
                                         oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                         title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                         style="display: inline-block; padding: 8px 15px;">
                            <i style="text-decoration: none" class="fa-icon-list"/>
                        </a4j:commandLink>
                        <%--

                            <h:outputText value="    "/>

                        <a4j:commandButton action="#{LocalAcessoControle.realizarConsultaLogObjetoSelecionado}" reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);" image="./imagens/botalVisualizarLog.png" alt="Visualizar LOG" title="Visualizar Log" styleClass="botoes"/>
                        --%>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%-- RichModal para pesquisa de Coletores  --%>
    <rich:modalPanel id="richModalColetor"
                     showWhenRendered="#{LocalAcessoControle.mostrarRichModalPesqColetor}" width="750" autosized="true" shadowOpacity="true"
                     height="250" onshow="document.getElementById('formPesqColetor:nomePesqColetor').focus();">
        <f:facet name="header">

            <jsp:include page="topoReduzido_material_semUCP.jsp" />

        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Pesquisa Rápida de Coletores com suggestionbox"></h:outputText>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formPesqColetor" ajaxSubmit="true">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" id="panelPesqColetores" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">
                    <h:panelGroup>
                        <h:outputText value="#{msg_aplic.prt_Asterisco}" rendered="#{ClienteControle.configuracaoSistema.nomeOb}"/>
                        <h:outputText value="Nome: " />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:inputText id="nomePesqColetor" size="50" maxlength="50" onblur="blurinput(this);"
                                     title="Digite o nome do coletor, a partir da 1ª letra digitada, a pesquisa é realizada automaticamente."
                                     onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.nomeColetorEscolhido}" />

                        <rich:suggestionbox   height="200" width="400"
                                              for="nomePesqColetor"
                                              status="statusInComponent"
                                              immediate="true"
                                              suggestionAction="#{LocalAcessoControle.executarAutocompletePesqColetor}"
                                              minChars="1"
                                              rowClasses="linhaImpar, linhaPar"
                                              var="resultColetor"  id="suggestionResponsavel">
                            <a4j:support event="onselect"
                                         reRender="form, richModalColetor"
                                         focus="nomePesqColetor"
                                         action="#{LocalAcessoControle.selecionarColetor}">
                            </a4j:support>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Descrição"  styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText styleClass="textverysmall" value="#{resultColetor.descricao}" />
                            </h:column>
                            <h:column >
                                <f:facet name="header">
                                    <h:outputText value="Nr. Terminal" styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText styleClass="textverysmall" value="#{resultColetor.numeroTerminal}" />
                            </h:column>
                            <h:column >
                                <f:facet name="header">
                                    <h:outputText value="Modelo" styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText  styleClass="textverysmall" value="#{resultColetor.modelo}" />
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="panelMensagemColetor" columns="1" width="100%" >
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagemDetalhada" value="#{LocalAcessoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="panelCancelarColetor" columnClasses="colunaCentralizada" columns="1" width="100%" >
                    <a4j:commandButton
                        image="/images/btn_cancelar.gif"
                        style="cursor:pointer"
                        id="hidelinkPesqColetor"
                        ajaxSingle="true"
                        reRender="richModalColetor"
                        action="#{LocalAcessoControle.fecharRichModalPesqColetor}">

                    </a4j:commandButton>
                </h:panelGrid>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

<script>
    document.getElementById("form:nome").focus();
</script>


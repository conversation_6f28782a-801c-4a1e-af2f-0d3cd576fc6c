<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<link href="./css/acesso.css" rel=stylesheet type="text/css">

<%@page import="negocio.comuns.utilitarias.Uteis" %>
<%@page import="acesso.webservice.AcessoControle" %>
<%@page import="java.util.HashMap" %>
<%@page import="java.util.Map" %>

<jsp:useBean id="auxiliar" scope="page" class="acesso.webservice.DaoAuxiliar"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="Pacto Zillyon Acesso Web" /></title>
    <script language="Javascript" type="text/javascript">
        function RelogioDigital() {
            var RELOGIO = new Date();
            var HORA = RELOGIO.getHours();
            var MINUTO = RELOGIO.getMinutes();
            var SEGUNDO = RELOGIO.getSeconds();
            if (HORA <= 9) {
                HORA = '0' + HORA;
            }
            if (MINUTO <= 9) {
                MINUTO = '0' + MINUTO;
            }
            if (SEGUNDO <= 9) {
                SEGUNDO = '0' + SEGUNDO;
            }
            document.formHora.Saida.value = HORA + ':' + MINUTO + ':' + SEGUNDO;
        }
        window.setInterval('RelogioDigital()', 1000);
    </script>

    <%
            String chave = request.getParameter("chave");

            AcessoControle obj = auxiliar.getMapaControladores().get(chave);
            if (obj != null){
                Map<Integer, byte[]> mapImagem = new HashMap<Integer, byte[]>();
                mapImagem.put(obj.getRxTx().getCliente().getPessoa().getCodigo(), obj.getRxTx().getCliente().getPessoa().getFoto());

                //request.setAttribute("IMAGES_MAP", mapImagem);
                request.getSession().setAttribute("IMAGES_MAP", mapImagem);
            }
    %>


    <body>
        <h:form id="formHora">
            <!-- start header -->
            <div id="tela" style="width: 100%;height: 100%;overflow: hidden;">
                <div id="header">
                    <div id="logo">
                        <ul>
                            <img src="./images/logo_zw.png" alt="logoPacto" width="130" height="52">
                        </ul>
                    </div>
                    <div id="menu">
                        <ul>
                            <li class="current_page_item"><input size="7" TYPE="text" name="Saida"></li>
                        </ul>
                    </div>
                    <div id="conteudo">
                        <div id="foto">
                            <input type="image" class="fotoAluno" border="0" src="acesso?image=<%=obj.getRxTx().getCliente().getPessoa().getCodigo()%>" width="180" height="215"/>
                        </div>
                        <div class="dadosAluno">
                            <table class="tabelaAluno" cellpadding="0" cellspacing="3" width="100%">
                                <tr style="background-color: #ADD8E6">
                                    <td width="28%"><span style="font-size:28px; margin-left:5px">Vencimento:</span></td>
                                    <td><span style="font-size:28px; color:#00008B; margin-left:5px"><%=obj.getRxTx().getDataLimiteAcesso()%></span></td>
                                </tr>
                                <tr style="background-color: #D3D3D3">
                                    <td><span style="font-size:20px; font-weight:normal; margin-left:5px;">Matrícula</span></td>
                                    <td><span style="font-size:20px; font-weight:normal; margin-left:5px">Nome</span></td>
                                </tr>
                                <tr style="background-color: #ADD8E6">
                                    <td><span style="margin-left:10px;"><%=obj.getRxTx().getCliente().getMatricula()%></span></td>
                                    <td><span style="margin-left:10px;"><%= obj.getRxTx().getCliente().getPessoa().getNome()%></span></td>
                                </tr>
                                <tr style="background-color: #D3D3D3">
                                    <td><span style="font-size:20px; font-weight:normal; margin-left:5px">Categoria</span></td>
                                    <td></td>
                                </tr>
                                <tr style="background-color: #ADD8E6">
                                    <td colspan="2"><span style="margin-left:10px;"><%=obj.getRxTx().getCliente().getCategoria()%></span></td>
                                </tr>
                                <%if (obj.getRxTx().getCliente().getMensagem() != "") {%>
                                <tr>
                                    <td colspan="2" align="center"><br><span style="font-size:24px; color:#00008B; margin-left:5px"><%=obj.getRxTx().getCliente().getMensagem()%></span></td>
                                </tr>
                                <%}%>
                                <%if (obj.getRxTx().getCliente().getAniversarioHj() == true) {%>

                                <tr>
                                    <td colspan="2">
                                        <br>
                                        <span style="font-size:32px; font-weight:bold; color:#FF1493; margin-left:5px;">Feliz Aniversário</span>
                                    </td>
                                </tr>
                                <%}%>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- end header -->

                <div id="wrapper">
                    <!-- start page -->
                    <%if (obj.getRxTx().getResultado().getBloqueadoLiberado().equals("B")) {%>
                    <div id="pageBloq">
                        <% } else {%>
                        <div id="pageLib">
                            <%}%>
                            <!-- start content -->
                            <div class="content">
                                <div class="post">
                                    <h2 class="title"><a><%=obj.getRxTx().getResultado().getDescricao()%></a></h2>

                                </div>
                            </div>
                            <!-- end content -->
                            <div style="clear: both;">&nbsp;</div>
                        </div>
                        <!-- end page -->
                    </div>
                    <div id="footer">
                        <p>&copy;2009 All Rights Reserved. &nbsp;&nbsp; Design by <a href="http://www.pactosolucoes.com.br/">Pacto Soluções Tecnológicas</a></p>
                    </div>
                </div>
            </div>
        </h:form>
    </body>
</f:view>
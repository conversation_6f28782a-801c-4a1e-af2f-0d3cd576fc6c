<%@ taglib prefix="rich" uri="http://richfaces.org/rich" %>
<%@ taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="a4j" uri="http://richfaces.org/a4j" %>

<style>
    .table-td-top-centered {
        text-align: center;
        vertical-align: top;
        padding: 10px;
    }
    .table-no-border {
        border: 0px;
    }
    .no-padding {
        padding: 0px;
    }
    .table-full-with {
        width: 100%;
    }
    td tr {
        padding: 0px !important;
    }
</style>

<rich:tab label="#{msg_aplic.ANALITICO_PROFESSOR}">

    <a4j:repeat value="#{GestaoComissaoControle.listaSintenticoProfessor}"
                rowKeyVar="status"
                rows="#{DataScrollerControle.nrRows}"
                var="professor">
        <div style="text-align: left; padding: 10px; background-color: #ACBECE;">
            <h:outputText style="font-size: 18px; font-weight: bold;" value="Professor: " />
            <h:outputText style="font-size: 18px;"
                          value="#{professor.nome},
                              #{msg_aplic.VALOR_PAGO}: R$ #{professor.valorPago_Apresentar},
                              #{msg_aplic.VALOR_COMISSAO}: #{professor.valorComissao_Apresentar}" />
        </div>
        <rich:dataTable
                style="margin-bottom: 20px;"
                value="#{professor.modalidades}"
                columnClasses="centralizado, centralizado"
                var="modalidade" styleClass="table-full-with"
                width="100%">
            <f:facet name="header">
                <rich:columnGroup>
                    <rich:column>
                        <h:outputText value="#{msg_aplic.MODALIDADES}" />
                    </rich:column>
                    <rich:column>
                        <h:outputText value="" />
                    </rich:column>
                </rich:columnGroup>
            </f:facet>
            <rich:column styleClass="table-td-top-centered">
                <h:outputText value="#{modalidade.nome}" />
                <br />
                <h:outputText value="#{msg_aplic.VALOR_PAGO}: #{modalidade.valorPago_Apresentar}" />
                <br />
                <h:outputText value="#{msg_aplic.VALOR_COMISSAO}: #{modalidade.valorComissao_Apresentar}" />
                <br />
                <h:outputText value="#{modalidade.qtdAluno} #{msg_aplic.ALUNOS}" />
            </rich:column>
            <rich:column styleClass="no-padding">
                <rich:dataTable styleClass="table-no-border table-full-with" value="#{modalidade.turmas}" var="turma">
                    <f:facet name="header">
                        <rich:columnGroup>
                            <rich:column>
                                <h:outputText value="#{msg_aplic.TURMAS}" />
                            </rich:column>
                            <rich:column>
                                <h:outputText value="" />
                            </rich:column>
                        </rich:columnGroup>
                    </f:facet>
                    <rich:column styleClass="table-td-top-centered">
                        <h:outputText value="#{turma.nome}" />
                        <br />
                        <h:outputText value="#{msg_aplic.VALOR_PAGO}: #{turma.valorPago_Apresentar}" />
                        <br />
                        <h:outputText value="#{msg_aplic.VALOR_COMISSAO}: #{turma.valorComissao_Apresentar}" />
                        <br />
                        <h:outputText value="#{turma.qtdAluno} #{msg_aplic.ALUNOS}" />
                        <br />
                    </rich:column>
                    <rich:column styleClass="table-no-border">
                        <rich:dataTable styleClass="table-no-border table-full-with" value="#{turma.horarios}" var="horario">
                            <f:facet name="header">
                                <rich:columnGroup>
                                    <rich:column>
                                        <h:outputText value="#{msg_aplic.HORARIOS}" />
                                    </rich:column>
                                    <rich:column style="text-align: left; padding-left: 10px;" >
                                        <h:outputText value="#{msg_aplic.ALUNOS}" />
                                    </rich:column>
                                </rich:columnGroup>
                            </f:facet>
                            <rich:column styleClass="table-td-top-centered">
                                <h:outputText value="#{horario.nome}" />
                                <br />
                                <h:outputText value="#{horario.dias}" />
                            </rich:column>
                            <rich:column styleClass="table-no-border">
                                <rich:dataTable styleClass="table-no-border table-full-with" value="#{horario.alunos}" var="aluno">
                                    <f:facet name="header">
                                        <rich:columnGroup styleClass="no-padding">
                                            <rich:column style="width: 40%;">
                                                <h:outputText value="" />
                                            </rich:column>
                                            <rich:column style="width: 10%;">
                                                <h:outputText value="#{msg_aplic.VALOR_PAGO}" />
                                            </rich:column>
                                            <rich:column style="width: 10%;">
                                                <h:outputText value="#{msg_aplic.VALOR_COMISSAO}" />
                                            </rich:column>
                                            <rich:column style="width: 10%;">
                                                <h:outputText value="#{msg_aplic.prt_id_pagamento}" />
                                            </rich:column>
                                            <rich:column style="width: 10%;">
                                                <h:outputText value="#{msg_aplic.prt_inc_matricula}"/>
                                            </rich:column>
                                            <rich:column style="width: 10%;">
                                                <h:outputText value="#{msg_aplic.prt_fim_matricula}"/>
                                            </rich:column>
                                        </rich:columnGroup>
                                    </f:facet>
                                    <rich:column style="border: 0px;width: 40%; padding-left: 10px;" styleClass="esquerda">
                                        <h:outputText value="#{aluno.cliente} - #{aluno.nome}"/>
                                    </rich:column>
                                    <rich:column style="border: 0px;width: 10%;">
                                        <h:outputText value="#{aluno.valorPago_Apresentar}" />
                                    </rich:column>
                                    <rich:column style="border: 0px;width: 10%;">
                                        <h:outputText value="#{aluno.valorComissao_Apresentar}" />
                                    </rich:column>
                                    <rich:column style="border: 0px;width: 10%;">
                                        <h:outputText value="#{aluno.movPagamento}"/>
                                    </rich:column>
                                    <rich:column style="border: 0px;width: 10%;">
                                        <h:outputText  style="#{(aluno.matriculaAluno.vigente ? 'color:blue;' : 'color:red;')}"
                                                       value="#{aluno.matriculaAluno.dataInicio_Apresentar}"/>
                                    </rich:column>
                                    <rich:column style="border: 0px;width: 10%;">
                                        <h:outputText  style="#{(aluno.matriculaAluno.vigente ? 'color:blue;' : 'color:red;')}"
                                                       value="#{aluno.matriculaAluno.dataFim_Apresentar}"/>
                                    </rich:column>
                                </rich:dataTable>
                            </rich:column>
                        </rich:dataTable>
                    </rich:column>
                </rich:dataTable>
            </rich:column>
        </rich:dataTable>
    </a4j:repeat>
</rich:tab>
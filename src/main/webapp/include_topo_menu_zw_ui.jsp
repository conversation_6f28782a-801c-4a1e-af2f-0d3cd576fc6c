<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<link href="${root}/bootstrap/bootplus.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600&display=swap" rel="stylesheet">
<link href="${root}/beta/css/pacto-icon-font4.0.min.css" type="text/css" rel="stylesheet"/>

<c:if test="${!SuperControle.menuZwUi}">
<h:panelGroup>
    <style>
        .modal-usuario-zw-ui.balloon-modal {
            display: none;
        }
    </style>
</h:panelGroup>
</c:if>
<c:if test="${SuperControle.menuZwUi}">
<h:panelGroup rendered="#{SuperControle.menuZwUi}">
    <a>
        <div class="zw_ui_topo">
            <div class="zw_ui_logo_pacto">
                    <h:graphicImage id="foto_zw_ui_empresaPacto" styleClass="main-logo" style="border-radius: 50%"
                                    value="#{SuperControle.urlFotoEmpresa}">
                    </h:graphicImage>
            </div>
            <div class="zw_ui_info_empresa">
                <div class="zw_ui_info_empresa_content">
                    <div _ngcontent-mhy-c6="" class="unidade-aux">
                        <c:if test="${InicioControle.apresentarTrocaDeUnidades}">
                            <div class="zw_ui_nome_empresa tooltipster"
                                 title="${SuperControle.empresaLogado.nome}">${SuperControle.empresaLogado.nome}
                            </div>
                        </c:if>
                        <c:if test="${!InicioControle.apresentarTrocaDeUnidades}">
                            <div class="zw_ui_nome_empresa tooltipster" style="line-height: 3;"
                                 title="${SuperControle.empresaLogado.nome}">${SuperControle.empresaLogado.nome}
                            </div>
                        </c:if>
                        <c:if test="${InicioControle.apresentarTrocaDeUnidades && !LoginControle.usuarioLogado.usuarioPACTOBR}">
                            <a4j:commandLink
                                    styleClass="zw_ui_trocar_unidade"
                                    reRender="form:panelModalTrocaUnidade,formTopo:panelModalTrocaUnidade"
                                    id="trocarUnidadeUsuarioLink"
                                    oncomplete="#{InicioControle.onComplete}"
                                    action="#{InicioControle.abrirTrocaEmpresa}">
                                <div> Trocar Unidade <i _ngcontent-mhy-c6="" class="pct pct-repeat"></i></div>
                            </a4j:commandLink>
                        </c:if>

                        <c:if test="${InicioControle.apresentarTrocaDeUnidades && LoginControle.usuarioLogado.usuarioPACTOBR}">
                            <a4j:commandLink
                                    styleClass="zw_ui_trocar_unidade"
                                    style="color: #adcce1 !important;"
                                    reRender="form:panelModalTrocaUnidade,formTopo:panelModalTrocaUnidade"
                                    id="trocarUnidadeUsuarioPactoLink"
                                    oncomplete="#{InicioControle.onComplete}"
                                    action="#{InicioControle.abrirTrocaEmpresa}">
                                <div style="font-family: 'Nunito Sans';font-style: normal;font-weight: 400;">
                                    Trocar Unidade <i _ngcontent-mhy-c6="" class="pct pct-repeat"></i></div>
                            </a4j:commandLink>
                        </c:if>

                        <c:if test="${!InicioControle.apresentarTrocaDeUnidades}">
                            <a href="">
                                <div class="zw_ui_trocar_unidade"><i _ngcontent-mhy-c6=""
                                                                                     class=""></i></div>
                            </a>
                        </c:if>
                    </div>
                </div>
            </div>
            <jsp:include page="include_topo_pesquisa_usuario_zw_ui.jsp"/>
            <jsp:include page="include_troca_empresa_zw_ui.jsp"/>
        </div>
    </a>
</h:panelGroup>
</c:if>

<script>
    var img = document.getElementById('form:foto_zw_ui_empresa');
    if (img) {
        img.addEventListener('error', function handleError() {
            console.log(img.src);
            img.style.display = 'none';
            var imgPacto = document.getElementById('form:foto_zw_ui_pacto');
            imgPacto.style.display = '';
        });
    }
</script>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script language="javascript" src="script/telaCliente1.3.js" type="text/javascript"></script>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/css_bi_1.4.css" rel="stylesheet" type="text/css"/>

<jsp:include page="include_head.jsp" flush="true"/>


<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:keepAlive beanName="LogProtheusControle"/>
    <title>
        <h:outputText value="Log integração ZW - Protheus"/>
    </title>
    <h:form id="form">
        <html>
        <body class="maximizado">
        <h:panelGroup layout="block" styleClass="bgtop topoZW">
            <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            <link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>
        </h:panelGroup>

        <style>
            .isa_info, .isa_success, .isa_warning, .isa_error {
                margin: 10px 0px;
                padding: 12px;
                text-align: left;
            }

            .agu {
                color: #00529B;
                background-color: #BDE5F8;
            }

            .isa_success {
                color: #4F8A10;
                background-color: #DFF2BF;
            }

            .isa_warning {
                color: #9F6000;
                background-color: #FEEFB3;
            }

            .isa_error {
                color: #D8000C;
                background-color: #FFD2D2;
            }

            .isa_info i, .isa_success i, .isa_warning i, .isa_error i {
                margin: 10px 5px;
                font-size: 16px;
                vertical-align: middle;
            }

            .caixatotal {
                display: inline-block;
                min-height: 120px;
                background: #fff;
                width: calc(25% - 30px);
                margin: 0 13px;
                box-shadow: 2px 0px 10px 1px rgba(0, 0, 0, 0.1);
                border-radius: 2px;
                margin-bottom: 15px;
            }

            .totais {
                margin-top: 30px;
            }

            .gr-totalizador {
                height: 200px;
                font-size: 23px;
                width: calc(100% / 4 - 1px);
            }

            .caixaLista .cabc.erro {
                background-color: #FFD2D2;
                color: #D8000C;
            }

            .caixaLista .cabc.suc {
                color: #4F8A10;
                background-color: #DFF2BF;
            }

            .caixaLista .cabc {
                width: 100%;
                height: 40px;
                line-height: 40px;
                font-weight: bold;
                text-align: left;
            }

            .caixaLista {
                display: block;
                width: calc(100% - 42px);
                padding: 15px;
                margin: 5px;
                border: 1px solid #E5E5E5;
                vertical-align: top;
                min-height: 300px;
            }

            .maximizado .container-navbar,.maximizado .topoZW, .maximizado .bglateraltop{
                display: none !important;
            }
        </style>

        <table width="100%" border="0" cellpadding="0" cellspacing="0">


            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="180" align="left" valign="top" class="bglateraltop"
                                style="padding: 0 !important;">
                            </td>
                            <td align="center" valign="top">
                                <div style="margin: 1.5vw; background-color: white;padding: 20px; position: relative; width: calc(100% - 6vw); display: inline-block; font-size: 14px"
                                     class="container-box">


                                    <div class="tituloPainelPesquisa notop" style="font-size: 16px;">
                                        <h:outputText value="Log da integração ZW - Protheus"/>

                                    </div>

                                    <h:panelGroup rendered="#{LogProtheusControle.ativo}" layout="block"
                                                  styleClass="isa_success">
                                        <i class="fa fa-icon-check"></i>
                                        Serviço ativo
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{!LogProtheusControle.ativo}" layout="block"
                                                  styleClass="isa_error">
                                        <i class="fa fa-icon-remove-circle"></i>
                                        Não consegui comunicar com o serviço da integração.
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="gr-container-totalizador">

                                        <h:panelGroup layout="block" styleClass="gr-totalizador">
                                            <h:outputText style="display: block;"
                                                          styleClass="bi-font-family bi-table-text"
                                                          value="Erros"/>
                                            <h:outputText styleClass="gr-totalizador-text bi-font-family" style="color: #777777"
                                                             value="#{LogProtheusControle.erros}"/>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" styleClass="gr-totalizador">
                                            <h:outputText style="display: block;"
                                                          styleClass="bi-font-family bi-table-text"
                                                          value="Aguardando integração"/>
                                            <h:outputText styleClass="gr-totalizador-text bi-font-family" style="color: #777777"
                                                             value="#{LogProtheusControle.aguardando}"/>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" styleClass="gr-totalizador">
                                            <h:outputText style="display: block;"
                                                          styleClass="bi-font-family bi-table-text"
                                                          value="Aguardando borderô no Protheus"/>
                                            <h:outputText styleClass="gr-totalizador-text bi-font-family" style="color: #777777"
                                                             value="#{LogProtheusControle.bordero}"/>

                                            <div style="text-align: center" >
                                                <a4j:commandLink style="font-size: 24px;" action="#{LogProtheusControle.downloadArquivoBordero}"
                                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=embordero.txt&mimetype=text/plain','GeradorConsultas', 800,200);">
                                                    <i class="fa-icon-download-alt"></i>
                                                </a4j:commandLink>
                                            </div>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" styleClass="gr-totalizador">
                                            <h:outputText style="display: block;"
                                                          styleClass="bi-font-family bi-table-text"
                                                          value="Alunos importados com pendências"/>
                                            <h:outputText styleClass="gr-totalizador-text bi-font-family" style="color: #777777"
                                                             value="#{LogProtheusControle.pendentesImportados}"/>

                                            <div style="text-align: center" >
                                                <a4j:commandLink style="font-size: 18px;" action="#{LogProtheusControle.downloadArquivoPendencias}"
                                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=pendentesImportados.txt&mimetype=text/plain','GeradorConsultas', 800,200);">
                                                    <i class="fa-icon-download-alt"></i>
                                                </a4j:commandLink>
                                            </div>

                                            <div style="text-align: center; margin-top: 20px; display: inline-block; width: 49%" >
                                                <h:outputText styleClass="bi-font-family bi-table-text"
                                                              style="display: inline-block; margin-right: 10px; font-size: 16px;" value="#{LogProtheusControle.pendentesPagos} já pagos"/>

                                                <a4j:commandLink style="font-size: 16px;display: inline-block" action="#{LogProtheusControle.downloadArquivoPendenciasPagas}"
                                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=pendentesImportadosPagos.txt&mimetype=text/plain','GeradorConsultas', 800,200);">
                                                    <i class="fa-icon-download-alt"></i>
                                                </a4j:commandLink>
                                            </div>

                                            <div style="text-align: center; margin-top: 20px; display: inline-block; width: 49%;border-left: 1px solid #E5E5E5;" >
                                                <h:outputText styleClass="bi-font-family bi-table-text" style="display: inline-block; margin-right: 10px;font-size: 16px;"
                                                              value="#{LogProtheusControle.pendentesAguardando} em borderô"/>

                                                <a4j:commandLink style="font-size: 16px;display: inline-block" action="#{LogProtheusControle.downloadArquivoPendenciasAguardando}"
                                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=pendentesImportadosAguardando.txt&mimetype=text/plain','GeradorConsultas', 800,200);">
                                                    <i class="fa-icon-download-alt"></i>
                                                </a4j:commandLink>
                                            </div>
                                        </h:panelGroup>


                                    </h:panelGroup>


                                    <h:panelGroup layout="block"
                                                  style="margin-top: 30px">
                                        <div style="width: 50%; display: inline-block; vertical-align: top">

                                            <h:panelGroup layout="block" styleClass="caixaLista">

                                                <div class="cabc erro"><h:outputText styleClass="fa-icon-ban-circle"
                                                                                     style="color: #db394f; margin: 0px 10px;"/>Erros
                                                </div>

                                                <h:outputText style="display: block; margin-top: 50px"
                                                              rendered="#{empty LogProtheusControle.logsErros and LogProtheusControle.ativo}"
                                                              value="NENHUM ERRO ENCONTRADO NOS LOGS"/>

                                                <h:dataTable var="log" value="#{LogProtheusControle.logsErros}"
                                                             width="100%"
                                                             rendered="#{not empty LogProtheusControle.logsErros}"
                                                             styleClass="pure-g-r pure-u-11-12 margin-0-auto dataTable tableCliente">

                                                    <h:column> <h:outputText styleClass="fa-icon-ban-circle"
                                                                             style="color: #db394f;"/></h:column>
                                                    <h:column><f:facet name="header"><h:outputText
                                                            value="Cliente"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{log.nome}"/>
                                                    </h:column>
                                                    <h:column>
                                                        <f:facet name="header"><h:outputText
                                                                value="Última tentativa"/></f:facet>
                                                                      target="_self">
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{log.hora}"/>
                                                    </h:column>
                                                    <h:column>
                                                        <f:facet name="header"><h:outputText value="Recibo"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{log.recibo}"/>
                                                    </h:column>
                                                    <h:column>
                                                        <f:facet name="header"><h:outputText
                                                                value="LOG DO ERRO"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{log.logerro}"/>

                                                    </h:column>


                                                </h:dataTable>

                                            </h:panelGroup>
                                            <h:panelGroup layout="block" styleClass="caixaLista">
                                                <div class="cabc agu"><h:outputText styleClass="fa-icon-ok-sign"
                                                                                    style="margin: 0px 10px;"/>Aguardando
                                                    integração
                                                </div>


                                                <h:dataTable var="log" value="#{LogProtheusControle.logsAguardando}"
                                                             width="100%"
                                                             rendered="#{not empty LogProtheusControle.logsAguardando}"
                                                             styleClass="pure-g-r pure-u-11-12 margin-0-auto dataTable tableCliente">

                                                    <h:column><f:facet name="header"><h:outputText
                                                            value="Cliente"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{log.nome}"/>
                                                    </h:column>
                                                    <h:column>
                                                        <f:facet name="header"><h:outputText
                                                                value="Hora recibo"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{log.hora}"/>
                                                    </h:column>


                                                </h:dataTable>

                                            </h:panelGroup>
                                        </div>
                                        <div style="width: 49%; display: inline-block">
                                            <h:panelGroup layout="block" styleClass="caixaLista">
                                                <div class="cabc suc"><h:outputText styleClass="fa-icon-ok-sign"
                                                                                    style="margin: 0px 10px;"/>Últimos
                                                    sucessos
                                                    <h:outputText style="margin-right: 10px" value="- #{LogProtheusControle.enviosHoje} envios hoje"/>
                                                </div>

                                                <h:dataTable var="logsuc" value="#{LogProtheusControle.logsSucesso}"
                                                             width="100%"
                                                             columnClasses="colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaDireita,colunaDireita,colunaDireita "
                                                             rendered="#{not empty LogProtheusControle.logsSucesso}"
                                                             styleClass="pure-g-r pure-u-11-12 margin-0-auto dataTable tableCliente">

                                                    <h:column> <h:outputText styleClass="fa-icon-ok-sign"
                                                                             style="color: #3cdb5c;"/></h:column>

                                                    <h:column>
                                                        <f:facet name="header"><h:outputText value="Recibo"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{logsuc.recibo}"/>
                                                    </h:column>

                                                    <h:column>
                                                        <f:facet name="header"><h:outputText
                                                                value="Envio"/></f:facet>
                                                        <h:outputText
                                                                style="color: #777777; font-size: 13px !important;"
                                                                value="#{logsuc.hora}"/>
                                                    </h:column>

                                                    <h:column><f:facet name="header"><h:outputText
                                                            value="Histórico"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{logsuc.descricao}"/>
                                                    </h:column>

                                                    <h:column>
                                                        <f:facet name="header"><h:outputText value="Valor"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{logsuc.valor}"/>

                                                    </h:column>

                                                    <h:column>
                                                        <f:facet name="header"><h:outputText value="Taxa"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{logsuc.taxaValor}"/>
                                                    </h:column>

                                                    <h:column>
                                                        <f:facet name="header"><h:outputText
                                                                value="Taxa base"/></f:facet>
                                                            <h:outputText
                                                                    style="color: #777777; font-size: 13px !important;"
                                                                    value="#{logsuc.taxaBase}"/>
                                                    </h:column>


                                                </h:dataTable>


                                            </h:panelGroup>
                                        </div>



                                    </h:panelGroup>


                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                </td>
            </tr>
        </table>

        </body>
        </html>

        <script>
            carregarTooltipster();
        </script>
    </h:form>

</f:view>
<script type="text/javascript">
    document.getElementById("form:valorConsulta").focus();
</script>

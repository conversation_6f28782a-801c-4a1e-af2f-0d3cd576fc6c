<%-- 
    Document   : lancarBrindeClienteForm
    Created on : 19/10/2017, 11:14:06
    Author     : arthur
--%>

<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style type="text/css">
    .radio-buton-espaco input{
        margin-left: 10px;
    }
    
    .radio-buton-espaco td:first-child input{
        margin-left: 0px;
    }
</style>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Ajuste Pontuação Cliente"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-ajustar-os-pontos-do-aluno-de-forma-manual/"/>
    </title>
        
    <c:set var="titulo" scope="session" value="Ajuste Pontuação Cliente"/>
    
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        
        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
            </h:panelGrid>
            <h:panelGroup id="panelCaixaAberto"  layout="block" style="margin-left: 15px;">
                <h:panelGrid id="panelGeral" columns="1" >
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.CLIENTE_MAISC}" />
                    <rich:spacer height="5px"/>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{LancarBrindeClienteControle.clienteSelecionadoAjuste.pessoa.nome}" />
                        <rich:spacer height="20px"/>
                        <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="TIPO DE OPERAÇÃO" />
                        <rich:spacer height="5px"/>
                        <h:panelGroup>
                            <h:selectOneRadio id="tipoPontoAjuste"
                                    onblur="blurinput(this);" onfocus="focusinput(this);"
                                    styleClass="texto-size-14 texto-cor-cinza texto-font radio-buton-espaco" style="border: none;"
                                    value="#{LancarBrindeClienteControle.tipoPontoAjuste}">
                                    <f:selectItems
                                        value="#{LancarBrindeClienteControle.listaAjusteTipoPonto}" />
                            </h:selectOneRadio>
                        </h:panelGroup>
                    <rich:spacer height="20px"/>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_Cadastro_label_quantidade_maiusculo}" />
                    <h:inputText  id="pontosAjuste"  size="50" maxlength="50" onkeyup="somenteNumeros(this);"  styleClass="inputTextClean" value="#{LancarBrindeClienteControle.historicoPontosAjuste.pontos}"/>
                    <rich:spacer height="20px"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.btn_descricao_brinde}" />
                    <h:panelGroup>
                        <h:inputTextarea  cols="70" rows="50" id="descricao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="inputTextClean" style="height:5.4em;" value="#{LancarBrindeClienteControle.historicoPontosAjuste.descricao}" />
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
            <rich:spacer height="25px"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" width="100%" style="text-align: center;font-family: Arial;font-size: 14px;">
                    <h:panelGroup>
                        <a4j:commandLink id="gerarRelatorioPontos" reRender="form"
                                         styleClass="pure-button pure-button-primary"
                                         action="#{LancarBrindeClienteControle.lancarAjustePontuacao}"
                                         oncomplete="#{LancarBrindeClienteControle.mensagemNotificar};fireElementFromParent('form:btnAtualizaCliente');"
                                         accesskey="2">
                            Gravar
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGroup layout="block" id="containerFuncMask">
                <script>
                    function somenteNumeros(num) {
                        var er = /[^0-9.]/;
                        er.lastIndex = 0;
                        var campo = num;
                        if (er.test(campo.value)) {
                          campo.value = "";
                        }
                    }
                </script>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>
</f:view>

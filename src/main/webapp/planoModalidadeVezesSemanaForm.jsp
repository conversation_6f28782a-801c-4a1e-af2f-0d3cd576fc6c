<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm"  width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" columnClasses="colunaCentralizada"  width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoModalidade_modalidade}" />
                    <h:inputText  id="modalidade" size="30" maxlength="30" readonly="true" styleClass="camposSomenteLeitura" value="#{PlanoControle.planoModalidadeVOSelecionado.modalidade.nome}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{PlanoControle.planoModalidadeVezesSemanaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemana}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="vezesSemana" styleClass="camposObrigatorios" value="#{PlanoControle.planoModalidadeVezesSemanaVO.vezesSemana.codigo}" >
                            <f:selectItems  value="#{PlanoControle.listaSelectItemVezesSemana}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_vezesSemana" action="#{PlanoControle.montarListaSelectItemVezesSemana}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:vezesSemana"/>
                        <h:message for="vezesSemana" styleClass="mensagemDetalhada"/>
                    </h:panelGroup> 
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemanaDefault}" />
                    <h:selectBooleanCheckbox id="vezesSemanaDefault" styleClass="campos "value="#{PlanoControle.planoModalidadeVezesSemanaVO.vezesSemanaDefault}"/>
                </h:panelGrid>
                <h:panelGrid columns="1" columnClasses="colunaCentralizada"  width="100%">
                    <h:commandButton action="#{PlanoControle.adicionarPlanoModalidadeVezesSemana}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="8" styleClass="botoes"/>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <h:dataTable id="planoModalidadeVezesSemanaVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                 rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                 value="#{PlanoControle.planoModalidadeVOSelecionado.planoModalidadeVezesSemanaVOs}" var="planoModalidadeVezesSemana">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemana}" />
                            </f:facet>
                            <h:outputText  value="#{planoModalidadeVezesSemana.vezesSemana.nrVezes}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_PlanoModalidadeVezesSemana_vezesSemanaDefault}" />
                            </f:facet>
                            <h:outputText  value="#{planoModalidadeVezesSemana.vezesSemanaDefault_Apresentar}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                            </f:facet>
                            <h:panelGroup>
                                <h:commandButton id="editarItemVenda" immediate="true" action="#{PlanoControle.editarPlanoModalidadeVezesSemana}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                <h:outputText value="    "/>

                                <h:commandButton id="removerItemVenda" immediate="true" action="#{PlanoControle.removerPlanoModalidadeVezesSemana}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                            </h:panelGroup>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>  
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:vezesSemana").focus();
</script>
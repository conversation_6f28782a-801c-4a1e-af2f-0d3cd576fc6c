<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>

<script type="text/javascript" language="javascript" src="../bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Alunos na academia com aula agendada"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
        <body onload="fireElement('form:botaoAtualizarPagina')"/>
        <h:form id="form">
            <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
            <c:set var="titulo" scope="session"
                   value="Aluno(s) na academia com aula agendada Total:${fn:length(GestaoAcessoRelControle.listaAcessoQuantidadeClienteAulasAgendadas)} "/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-gestao-de-acessos-adm/"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
                <f:facet name="header">
                    <jsp:include page="topo_reduzido_popUp.jsp"/>
                </f:facet>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid width="100%" style="text-align: right">
                    <h:panelGroup layout="block">
                        <a4j:commandLink id="exportarExcel"
                                         style="margin-left: 8px;"
                                         actionListener="#{ExportadorListaControle.exportar}"
                                         rendered="#{not empty GestaoAcessoRelControle.listaAcessoQuantidadeClienteAulasAgendadas}"
                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{GestaoAcessoRelControle.listaAcessoQuantidadeClienteAulasAgendadas}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="itemExportacao" value="biAcessoAlunosAulasAgendadas"/>
                            <f:attribute name="atributos" value="codigoCliente=Código,matricula=Matrícula,nome=Nome,inicioAula=Início da Aula,finalAula=Fim da Aula,descricaoAula=Aula"/>
                            <f:attribute name="prefixo" value="ClienteNaAcademiaComTreinoRealizado"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>

                        <%--BOTÃO PDF--%>
                        <a4j:commandLink id="exportarPdf"
                                         style="margin-left: 8px;"
                                         actionListener="#{ExportadorListaControle.exportar}"
                                         rendered="#{not empty GestaoAcessoRelControle.listaAcessoQuantidadeClienteAulasAgendadas}"
                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{GestaoAcessoRelControle.listaAcessoQuantidadeClienteAulasAgendadas}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="itemExportacao" value="biAcessoAlunosAulasAgendadas"/>
                            <f:attribute name="atributos" value="codigoCliente=Código,matricula=Matrícula,nome=Nome,inicioAula=Início da Aula,finalAula=Fim da Aula,descricaoAula=Aula"/>
                            <f:attribute name="prefixo" value="ClienteNaAcademiaComTreinoRealizado"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
                <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                value="#{GestaoAcessoRelControle.listaAcessoQuantidadeClienteAulasAgendadas}"
                                rows="15" var="aluno" rowKeyVar="status">

                    <rich:column sortBy="#{aluno.matricula}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Matrícula"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{aluno.matricula}"/>
                    </rich:column>

                    <rich:column sortBy="#{aluno.nome}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Nome"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{aluno.nome}"/>
                    </rich:column>

                    <rich:column sortBy="#{aluno.descricaoAula}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Aula"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{aluno.descricaoAula}"/>
                    </rich:column>


                    <rich:column sortBy="#{aluno.inicioAula}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Início da Aula"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{aluno.inicioAula}"/>
                    </rich:column>

                    <rich:column sortBy="#{aluno.finalAula}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Final da Aula"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{aluno.finalAula}"/>
                    </rich:column>

                    <rich:column>
                        <a4j:commandLink id="visualizarCliente" action="#{GestaoAcessoRelControle.irParaTelaCliente}"
                                         title="#{msg_aplic.prt_tela_cliente}"
                                         oncomplete="#{GestaoAcessoRelControle.onComplete}"
                                         styleClass="linkPadrao texto-cor-azul texto-size-14-real tooltipster">
                            <i class="fa-icon-search"></i>
                            <f:param name="cliente" value="#{aluno.codigoCliente}"/>
                        </a4j:commandLink>
                    </rich:column>

                </rich:dataTable>
                <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center"
                                   for="form:tabelaRes" maxPages="10" id="sctabelaRes"/>
            </h:panelGrid>
        </h:form>
        </body>
        </html>
    </h:panelGrid>
    <script>
        function carregarTooltipster() {
            jQuery('.tooltipster').tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        };
        carregarTooltipster();
    </script>
</f:view>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<h:panelGroup id="menuLateralInicialCRM" layout="block" styleClass="menuLateral tudo">

    <jsp:include page="includes/include_menulateral_acessorapido.jsp" flush="true" />

    <%--Opera��es--%>
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-align-justify"></i> Opera��es
    </h:panelGroup>

        <%--Gest�o de Carteiras--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.organizadorCarteira}">
            <a4j:commandLink id="botaoOrganizadorCarteira" ajaxSingle="true"
                             action="#{LoginControle.abrirCarteirasCRM}"
                             value="Gest�o de Carteiras"
                             styleClass="titulo3 linkFuncionalidade"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}o-que-e-a-gestao-de-carteiras/"
                          title="Clique e saiba mais: Gest�o de Carteira"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <%--Mailing--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.malaDireta}">
        <a4j:commandLink id="botaoLateralMalaDireta"
                         ajaxSingle="true"
                             styleClass="titulo3 linkFuncionalidade"
                         value="Mailing"
                         action="#{MalaDiretaControle.abrirMailing}"
                         oncomplete="abrirPopup('mailing.jsp', 'mailing', 1000, 650);"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-realizar-um-contato-avulso-contato-pessoal/"
                          title="Clique e saiba mais: Mailing" target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
    </h:panelGroup>

        <%--Marcar Comparecimento--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.agenda}">
            <a4j:commandLink id="botaoLateralMarcarComparecimento"
                             ajaxSingle="true"
                             action="#{AgendaControle.novo}"
                             oncomplete="abrirPopup('confirmarComparecimentoAgendadoForm.jsp', 'ConfirmarAgendados', 780, 595);"
                             value="Marcar Comparecimento"
                             styleClass="titulo3 linkFuncionalidade"/>
        <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCRM}Recursos_Extras:Marcar_Comparecimento"
                          title="Clique e saiba mais: Marcar Comparecimento" target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h:panelGroup>
    </h:panelGroup>

    <%--Cadastros--%>
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-briefcase"></i> Cadastros
        </h:panelGroup>

        <%--Meta Extra--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.crmExtraCRM}">
            <a4j:commandLink id="botaoLateralCRMExtra" ajaxSingle="true"
                             oncomplete="abrirPopup('crmExtraCons.jsp', 'Meta Extra', 1000, 650);"
                             value="Meta Extra"
                             styleClass="titulo3 linkFuncionalidade"/>
        <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}bi-meta-extra-crm/"
                          title="Clique e saiba mais: Meta Extra"
                          target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h:panelGroup>

    </h:panelGroup>

    <%--Consultas--%>
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-search"></i> Consultas
    </h:panelGroup>

        <%--Agendamentos--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.relatorioAgendamentos}">
            <a4j:commandLink id="btnLateralAgendamentos"
                             ajaxSingle="true"
                             value="Agendamentos"
                             oncomplete="abrirPopup('agendamentos.jsp', 'indicacao', 1200, 650);"
                             styleClass="titulo3 linkFuncionalidade"/>
        <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}bi-aulas-experimentais-adm/"
                          title="Clique e saiba mais: Agendamentos" target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h:panelGroup>

        <%--Contatos App--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.relatorioContatosAPP}">
            <a4j:commandLink id="btnLateralContatoAPP"
                             ajaxSingle="true"
                             oncomplete="abrirPopup('contatosApp.jsp', 'contatosApp', 1000, 650);"
                             value="Contatos App"
                             styleClass="titulo3 linkFuncionalidade"/>
        <h:outputLink styleClass="linkWiki"
                      value="#{SuperControle.urlBaseConhecimento}como-enviar-notificacao-para-o-app-de-um-unico-aluno/"
                      title="Clique e saiba mais: Contatos App" target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h:panelGroup>

        <%--Hist�rico de Contatos--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.historicoContato}">
            <a4j:commandLink id="btnLateralHistoricoContato"
                             ajaxSingle="true"
                             action="#{HistoricoContatoControle.inicializarDadosRealizarContato}"
                             oncomplete="abrirPopup('historicoContatoCons.jsp', 'historicoContatoCons', 765, 595);"
                             value="Hist�rico de Contatos"
                             styleClass="titulo3 linkFuncionalidade"/>
        <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCRM}Consultas:Consulta_Histrico_Contato"
                          title="Clique e saiba mais: Consulta Hist�rico Contato" target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h:panelGroup>

    </h:panelGroup>

</h:panelGroup>

<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="a3j" uri="http://java.sun.com/jsf/html" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Departamento_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Departamento_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Departamento"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Departamento_codigo}"/>
                    <h:panelGroup>
                        <f:verbatim>
                            <h:outputText value="                      "/>
                        </f:verbatim>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                     value="#{DepartamentoControle.departamentoVO.codigo}"/>
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <c:if test="${DepartamentoControle.usuarioLogado.administrador}">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Departamento_empresa}"/>
                        <h:selectOneMenu id="empresa" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         value="#{DepartamentoControle.departamentoVO.empresaVO.codigo}">
                            <f:selectItems value="#{DepartamentoControle.listaEmpresas}"/>
                        </h:selectOneMenu>

                    </c:if>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Departamento_nome}"/>
                    <h:panelGroup>
                        <h:inputText id="nome" size="50" maxlength="70" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{DepartamentoControle.departamentoVO.nome}"/>
                        <h:message for="nome" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Departamento_concessionario}"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="concessionario" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{DepartamentoControle.departamentoVO.concessionario}"/>
                        <h:message for="concessionario" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{DepartamentoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{DepartamentoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{DepartamentoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{DepartamentoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                                <a4j:commandLink id="novo" immediate="true" action="#{DepartamentoControle.novo}"
                                                 value="#{msg_bt.btn_novo}"
                                                 title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandLink id="salvar" action="#{DepartamentoControle.gravar}"
                                                 value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2"
                                                 styleClass="botoes nvoBt"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>

                                <h:panelGroup id="grupoBtnExcluir">
                                    <a4j:commandLink id="excluir"  reRender="mdlMensagemGenerica"
                                                     oncomplete="#{DepartamentoControle.msgAlert}" action="#{DepartamentoControle.confirmarExcluir}"
                                                     value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                                </h:panelGroup>

                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandLink id="consultar" immediate="true"
                                                 action="#{DepartamentoControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4"
                                                 styleClass="botoes nvoBt btSec"/>
                                <rich:spacer width="15px"/>
                                <a4j:commandLink  action="#{DepartamentoControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                                    oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                    styleClass="botoes nvoBt btSec"  title="Visualizar Log">
                                    <i style="text-decoration: none" class="fa-icon-list"/>
                                </a4j:commandLink>


                            </h:panelGroup>
                        </c:if>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>
<%@include file="includes/imports.jsp" %>

<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

    <h:panelGrid id="panelTopColetor" columns="3" width="100%" columnClasses="colunaEsquerda" styleClass="tabMensagens">
        <a4j:commandButton id="btnAdicColetor"
                           rendered="true"
                           action="#{ServidorFacialControle.adicionarCamera}"
                           title="#{msg_bt.btn_novoColetor}"
                           value="Novo"
                           ajaxSingle="true"
                           process="panelEdicaoColetor"
                           reRender="panelEdicaoColetor, panelListaColetor, form:mensagem, form:mensagemDetalhada, panelTopColetor"
                           styleClass="botoes nvoBt btSec"/>


        <h:panelGroup rendered="#{ServidorFacialControle.mostrarFormCamera}">
            <h:outputText value="Webcam Selecionada: "/>
            <h:outputText value="#{ServidorFacialControle.camera.descricao}"/>
        </h:panelGroup>
    </h:panelGrid>

    <h:panelGrid id="panelListaColetor" columns="1" width="100%">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Webcams"/>
        </h:panelGrid>

        <h:dataTable id="cameras" width="100%" headerClass="subordinado"
                     rowClasses="linhaImpar, linhaPar"
                     columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                     value="#{ServidorFacialControle.servidorFacial.cameras}" var="camera">
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Coletor_descricao}"/>
                </f:facet>
                <h:outputText value="#{camera.descricao}"/>
            </h:column>

            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_bt.btn_opcoes}"/>
                </f:facet>
                <h:panelGroup>
                    <a4j:commandButton id="editarItemColetor"
                                       action="#{ServidorFacialControle.editarCamera}"
                                       value="#{msg_bt.btn_editar}"
                                       title="#{msg_bt.btn_editar}"
                                       image="./imagens/botaoEditar.png"
                                       accesskey="6"
                                       styleClass="botoes"
                                       ajaxSingle="true"
                                       process="panelEdicaoColetor"
                                       reRender="panelEdicaoColetor, panelListaColetor, panelTopColetor"/>
                    <f:verbatim>
                        <h:outputText value="    "/>
                    </f:verbatim>

                    <a4j:commandButton id="excluirItemColetor"
                                       action="#{ServidorFacialControle.removerCamera}"
                                       value="#{msg_bt.btn_excluir}"
                                       title="#{msg_bt.btn_excluir}"
                                       image="./imagens/botaoRemover.png"
                                       styleClass="botoes"
                                       ajaxSingle="true"
                                       reRender="panelEdicaoColetor, panelListaColetor, panelTopColetor"/>

                </h:panelGroup>
            </h:column>
        </h:dataTable>
    </h:panelGrid>

    <h:panelGroup id="panelEdicaoColetor" layout="block">
        <h:panelGrid rendered="#{ServidorFacialControle.mostrarFormCamera}" id="panelColetorLocal" columns="1"
                     width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
            <rich:tabPanel>
                <rich:tab label="Dados b�sicos">
                    <h:panelGrid id="pnlCamera" columns="2" rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita" width="100%">

                        <h:outputText value="#{msg_aplic.prt_Coletor_descricao}" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:inputText id="descColetor" size="40" maxlength="40" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{ServidorFacialControle.camera.descricao}"/>
                        </h:panelGroup>

                        <h:outputText value="Concentrador"
                                      styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:inputText id="endereco" size="40" maxlength="200" onblur="blurinput(this);"
                                         readonly="true"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{ServidorFacialControle.camera.endereco}"/>
                        </h:panelGroup>

                        <h:outputText value="Acionar Terminal" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:inputText id="terminalCamera" size="5" maxlength="5" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         title="Use % para ver todos terminais dispon�veis"
                                         value="#{ServidorFacialControle.camera.terminal}">
                                <rich:suggestionbox height="80" width="400" for="terminalCamera"
                                                    status="statusInComponent"
                                                    suggestionAction="#{ServidorFacialControle.executarAutocompleteTerminal}"
                                                    minChars="1"
                                                    reRender="panelMensagem, gridMensagens, gridMensagens1"
                                                    rowClasses="linhaImpar, linhaPar"
                                                    ajaxSingle="false"
                                                    var="result"
                                                    id="suggestionTerminal">
                                    <a4j:support event="onselect"
                                                 reRender="panelEdicaoColetor, panelListaColetor, form:mensagem, form:mensagemDetalhada, panelTopColetor"
                                                 action="#{ServidorFacialControle.selecionarTerminal}"/>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Terminal" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText styleClass="textverysmall" value="#{result.codigo}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Descri��o" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText styleClass="textverysmall"
                                                      value="#{result.label}"/>
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText value="Nome do Computador" styleClass="textverysmall"/>
                                        </f:facet>
                                        <h:outputText styleClass="textverysmall"
                                                      value="#{result.codigoString}"/>
                                    </h:column>
                                </rich:suggestionbox>
                            </h:inputText>
                            <h:message for="terminalCamera" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>

                        <h:outputText value="Url Rtsp"
                                      styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:inputText id="urlRtsp" size="40" maxlength="80" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{ServidorFacialControle.camera.urlRtsp}"/>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos" value="Webcam"/>
                        <h:panelGroup>
                            <h:inputText id="indice" size="5" maxlength="3" title="�ndice da Webcam"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ServidorFacialControle.camera.indice}"/>
                        </h:panelGroup>

                    </h:panelGrid>
                </rich:tab>


            </rich:tabPanel>
        </h:panelGrid>
    </h:panelGroup>


</h:panelGrid>


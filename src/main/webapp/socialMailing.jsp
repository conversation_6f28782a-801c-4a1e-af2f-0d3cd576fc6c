<%-- 
    Document   : newjsp
    Created on : 24/08/2012, 15:51:47
    Author     : Waller
--%>


<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="socialmailing.css" rel="stylesheet" type="text/css">

<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">

        <html>
            <head>
            <jsp:include page="include_head.jsp" flush="true"/>
                <script src="socialmailing/jquery.min.js"></script>
                <script type="text/javascript" src="socialmailing/socialmailing.js"></script>

            </head>

            <body class="fundo">


        <div class="caixaCentralizada">
                    <div class="caixaTop">
                        <table width="95%" height="100%">
                            <tr valign="top">
                        <td style="width: 60%; max-width: 200px;">
                            <h:panelGroup id="nomeGrupoPainel" layout="block" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align: left !important;">
                                        <rich:inplaceInput value="#{SocialMailingControle.grupoSelected.nome}"
                                                   minInputWidth="200px"
                                                           defaultLabel="Clique e d� um nome ao grupo"
                                                           rendered="#{SocialMailingControle.grupoSelected.coletivo}"
                                                           styleClass="tituloGeral">
                                            <a4j:support action="#{SocialMailingControle.gravarNomeGrupo}"
                                                         reRender="form"
                                                         event="onviewactivated"/>

                                        </rich:inplaceInput>
                                <h:outputText value="#{SocialMailingControle.grupoSelected.nomeResumido}"
                                              title="#{SocialMailingControle.grupoSelected.nomeResumido}"
                                                      rendered="#{!SocialMailingControle.grupoSelected.coletivo}"
                                              style="background-color: transparent"
                                                      styleClass="tituloGeral"
                                                      escape="false"/>
                                <c:if test="${SocialMailingControle.tipoSocialMail.codigo == 0}">
                                        <br/>
                                    <h:outputText
                                            value="<b>Admin:</b> #{SocialMailingControle.grupoSelected.dono.primeiroNomeConcatenado}"
                                                      rendered="#{SocialMailingControle.grupoSelected.coletivo}"
                                                      style="font-size: 8pt; padding-top: 5px;"
                                                      escape="false"/>
                                </c:if>
                                    </h:panelGroup>
                        </td>

                        <c:if test="${SocialMailingControle.tipoSocialMail.codigo == 0}">
                            <td style="width: 5%; text-align: center;">
                                <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-enviar-mensagens-pelo-chat-interno-do-sistema-social-mailing/"
                                              title="Clique e saiba mais: Social Mailing" target="_blank">
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                                </td>
                        </c:if>

                        <td style="width: 5%; text-align: center;">
                                <h:commandLink action="#{SocialMailingControle.atualizarConversas}">
                                    <h:graphicImage alt="Atualizar"
                                                    style="width: 35px;"
                                                    url="images/socialmail/refresh-icon.png"
                                                    title="Atualizar"/>
                                </h:commandLink>
                        </td>

                        <td align="right" style="width: 1%;">
                            <img src="imagens_flat/genteZW.svg" width="35">
                        </td>
                            </tr>
                        </table>
                    </div>


                    <!-- -------------- MENSAGENS ---------------------------- -->
                    <div class="caixaMensagens">

                        <div id="divCaixaMensagens" class="caixaConversas">

                            <h:panelGrid width="100%" id="conversa">

                                <h:panelGroup>
                                    <center>
                                        <a4j:commandButton value="Visualizar Mais Mensagens" styleClass="botaoMore"
                                                           rendered="#{!SocialMailingControle.grupoSelected.disabledMais}"
                                                           action="#{SocialMailingControle.mais}"
                                                   reRender="botaoMais, conversa" id="botaoMais">
                                        </a4j:commandButton>
                                    </center>
                                </h:panelGroup>
                        <h:dataTable width="100%" cellpadding="4" var="mensagem"
                                     value="#{SocialMailingControle.grupoSelected.mensagens}"
                                             columnClasses="colunaFoto, colunaTexto">

                                    <h:column>

                                <h:graphicImage
                                        rendered="#{mensagem.usuarioPactoBR}"
                                        url="images/socialmail/profile-pacto-solicitacao.png"
                                        styleClass="styleFotoUsuarioSocial"/>


                                <a4j:mediaOutput element="img"
                                                 rendered="#{(SocialMailingControle.codPessoaLogada != mensagem.codPessoa && !SuperControle.fotosNaNuvem) && !mensagem.usuarioPactoBR}"
                                                 align="left"
                                                             cacheable="false" session="true"
                                                             title="#{mensagem.pessoa}"
                                                             createContent="#{SocialMailingControle.paintFotoMensagens}"
                                                 styleClass="styleFotoUsuarioSocial"
                                                 value="#{ImagemData}" mimeType="image/jpeg">
                                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="45"/>
                                    <f:param name="altura" value="45"/>
                                    <f:param name="pessoa" value="#{mensagem.codPessoa}"/>
                                        </a4j:mediaOutput>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem && !mensagem.usuarioPactoBR}"
                                                title="#{mensagem.pessoa}"
                                                styleClass="styleFotoUsuarioSocial"
                                                url="#{mensagem.fotoKey}">
                                        </h:graphicImage>

                                <a4j:mediaOutput element="img"
                                                 rendered="#{(SocialMailingControle.codPessoaLogada == mensagem.codPessoa && !SuperControle.fotosNaNuvem) && !mensagem.usuarioPactoBR}"
                                                 align="left"
                                                             cacheable="false" session="true"
                                                             createContent="#{SocialMailingControle.paintFotoUsuarioLogado}"
                                                 styleClass="styleFotoUsuarioSocial"
                                                 value="#{ImagemData}" mimeType="image/jpeg">
                                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="45"/>
                                    <f:param name="altura" value="45"/>
                                        </a4j:mediaOutput>


                                    </h:column>
                                    <h:column>
                                        <table width="100%">
                                            <tr>
                                                <td align="left">
                                                <%--<h:outputText escape="false" styleClass="eu"--%>
                                                <%--rendered="#{SocialMailingControle.codPessoaLogada == mensagem.codPessoa}"--%>
                                                <%--value="Eu"/>--%>
                                            <h:outputText escape="false" styleClass="participante"
                                                          value="#{mensagem.pessoa}"/>
                                                    </td>
                                                    <td align="right">
                                            <h:outputText escape="false" styleClass="dataEnviada"
                                                          value="(Enviada tamb�m por SMS) "
                                                          rendered="#{mensagem.enviadoSMS}"/>
                                            <h:outputText escape="false" styleClass="dataEnviada"
                                                          value="#{mensagem.dataEnviada}"/>
                                                    </td>
                                                </tr>
                                            </table>

                                <h:outputText escape="false"
                                              style="font-size: 10pt; font-family: 'Trebuchet MS', verdana;"
                                              value="#{mensagem.mensagem}"/>

                                <h:panelGroup layout="block"
                                              style="padding-top: 4px"
                                              rendered="#{SocialMailingControle.grupoSolicitacao && (!LoginControle.usuarioLogado.usuarioPACTOBR and !LoginControle.usuarioLogado.usuarioAdminPACTO) && SocialMailingControle.codPessoaLogada != mensagem.codPessoa}">

                                    <h:selectBooleanCheckbox
                                            disabled="#{mensagem.respostaCompreendida}"
                                            value="#{mensagem.respostaCompreendida}">
                                        <a4j:support event="onchange"
                                                     reRender="conversa"
                                                     action="#{SocialMailingControle.marcarRespostaCompreendida}"/>
                                    </h:selectBooleanCheckbox>

                                    <h:outputText
                                            style="font-family: Helvetica, Arial,sans-serif; font-size: 11px; font-style: normal; color: #2a496d; font-weight: bold; position: absolute;"
                                            value="Entendi a resposta"/>
                                </h:panelGroup>

                                <rich:separator rendered="#{mensagem.separator}" width="100%"
                                                height="1"/>
                                    </h:column>

                                </h:dataTable>

                            </h:panelGrid>


                        </div>


                <h:panelGroup layout="block" id="panelSuperiorSocial" styleClass="panelSuperiorSocial"
                              style="width: 100%">

                    <h:panelGroup rendered="#{SocialMailingControle.grupoSelected.coletivo}" layout="block"
                                  style="width: 100%; display: inline-flex;">

                        <h:panelGroup layout="block" id="caixaUsuariosGrupo" style="width: 80%"
                                      styleClass="caixaConfiguracoesSocial">

                                        <a4j:repeat value="#{SocialMailingControle.grupoSelected.participantes}" var="partner"
                                                    rowKeyVar="status">
                                <h:panelGroup
                                        rendered="#{SocialMailingControle.usuarioLogado.colaboradorVO.pessoa.codigo != partner.participante.codigo && status < 9}">
                                    <a4j:mediaOutput element="img"
                                                     title="#{partner.participante.nome} (Para remover contatos ou ver todos, clique no bot�o de configura��o do grupo, aqui do lado)"
                                                     style="margin-left: 5px; margin-right: 5px"
                                                     styleClass="styleFotoUsuarioSocialSuperior"
                                                                 cacheable="false" session="true"
                                                     rendered="#{!SuperControle.fotosNaNuvem && !partner.usuarioPactoBR}"
                                                                 createContent="#{SocialMailingControle.paintFotoMensagens}"
                                                     value="#{ImagemData}" mimeType="image/jpeg">
                                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                        <f:param name="largura" value="35"/>
                                        <f:param name="altura" value="35"/>
                                        <f:param name="pessoa" value="#{partner.participante.codigo}"/>
                                                </a4j:mediaOutput>

                                    <h:graphicImage
                                            rendered="#{SuperControle.fotosNaNuvem && !partner.usuarioPactoBR}"
                                            style="margin-left: 5px; margin-right: 5px"
                                            title="#{partner.participante.nome} (Para remover contatos ou ver todos, clique no bot�o de configura��o do grupo, aqui do lado)"
                                            styleClass="styleFotoUsuarioSocialSuperior"
                                            url="#{partner.participante.urlFoto}">
                                                </h:graphicImage>

                                    <h:graphicImage
                                            rendered="#{partner.usuarioPactoBR}"
                                            url="images/socialmail/profile-pacto-solicitacao.png"
                                            style="margin-left: 5px; margin-right: 5px"
                                            styleClass="styleFotoUsuarioSocialSuperior"/>

                                            </h:panelGroup>
                                        </a4j:repeat>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="caixaConfiguracoes" style="width: 20%; text-align: right"
                                      styleClass="caixaConfiguracoesSocial">
                            <a4j:commandButton id="botaoAdicionar"
                                               image="images/socialmail/add-chat.png"
                                               style="background: #eee;"
                                               styleClass="styleFotoUsuarioSocialSuperior"
                                               title="Adicionar Pessoa � Conversa, assim todos ser�o notificados."
                                               onclick="showAddContato(); return false;"
                                               rendered="#{SocialMailingControle.grupoSelected.coletivo && (SocialMailingControle.grupoSelected.souDono || SocialMailingControle.grupoSolicitacao)}">
                            </a4j:commandButton>

                            <a4j:commandLink reRender="formCfgGrupo"
                                             status="false"
                                             title="Clique para visualizar todos os contatos desse grupo, deixar o grupo ou mesmo desativ�-lo, se voc� for o administrador"
                                             oncomplete="Richfaces.showModalPanel('cfgGrupo');">
                                <h:graphicImage url="images/socialmail/config.png"
                                                style="background: #eee;"
                                                styleClass="styleFotoUsuarioSocialSuperior"/>
                            </a4j:commandLink>

                        </h:panelGroup>


                                    <script>
                                        var $j = jQuery.noConflict();
                            $j(document).ready(function () {
                                            $j('.infiniteCarousel').infiniteCarousel();
                                        });

                                        var div = document.getElementById('divCaixaMensagens');
                                        div.scrollTop = '9999';
                                    </script>
                                </h:panelGroup>

                                <h:panelGroup rendered="#{!SocialMailingControle.grupoSelected.coletivo}">
                        <div class="caixaConfiguracoesSocial">
                            <a4j:mediaOutput element="img"
                                                         title="#{SocialMailingControle.grupoSelected.nome}"
                                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                             style="margin-left: 5px; margin-right: 5px"
                                             styleClass="styleFotoUsuarioSocialSuperior"
                                                         cacheable="false" session="true"
                                                         createContent="#{SocialMailingControle.paintFoto}"
                                             value="#{ImagemData}" mimeType="image/jpeg">
                                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                <f:param name="largura" value="35"/>
                                <f:param name="altura" value="35"/>
                                <f:param name="grupo"
                                         value="#{SocialMailingControle.grupoSelected.codigo}"/>
                                        </a4j:mediaOutput>
                                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                            style="margin-left: 5px; margin-right: 5px"
                                            styleClass="styleFotoUsuarioSocialSuperior"
                                                        title="#{SocialMailingControle.grupoSelected.nome}"
                                            url="#{SocialMailingControle.grupoSelected.urlFoto}">
                                        </h:graphicImage>
                                    </div>
                                </h:panelGroup>
                            </h:panelGroup>

                <div class="addContato shadow" id="modalAddContato" style="z-index: 40;">
                            <table width="100%" cellpadding="5" cellspacing="5">
                                <tr>
                                    <td align="left">

                                <h:inputText style="width: 200px; height: 23px;" styleClass="campoBuscar"
                                             id="campoBuscarAddUser"
                                                      value="#{SocialMailingControle.consultaAdicionarUsuario}">
                                    <a4j:queue name="queueFiltroAdicionar" ignoreDupResponses="true" requestDelay="1000"
                                               timeout="60000"/>
                                    <a4j:support status="statusInComponent" eventsQueue="queueFiltroAdicionar"
                                                 event="onkeyup"
                                                         action="#{SocialMailingControle.consultarUsuarioAdicionarConversa}"
                                                         reRender="usersAdd"/>

                                        </h:inputText></td>


                            <td align="right"><a4j:commandLink
                                    onclick="hideAddContato(); return false; position: absolute;">
                                            <div id="botaoFechar">
                                            </div>
                                        </a4j:commandLink></td>
                                </tr>
                            </table>
                            <div>
                                <center>
                            <rich:separator width="273px" height="1"/>
                                    </center>
                        <rich:spacer height="6px"/>
                        <h:dataTable id="usersAdd" cellpadding="0" cellspacing="0"
                                     value="#{SocialMailingControle.listaUsuariosAdicionarConversa}" var="result"
                                     width="100%">
                                    <h:column>

                                        <h:panelGrid columnClasses="coluna1, coluna2, direita" columns="3"
                                                     cellpadding="5" cellspacing="5"
                                             style="width:100%; height: 100%;">

                                    <a4j:commandLink
                                            reRender="painelContatos, buscar, conversa, repeat, campoBuscarAddUser, usersAdd"
                                                             action="#{SocialMailingControle.selecionarResultadoAdicionar}"
                                                             oncomplete="hideAddContato();">

                                        <a4j:mediaOutput element="img"
                                                         rendered="#{result.colaboradorVO.pessoa.possuiFoto && !SuperControle.fotosNaNuvem}"
                                                         align="left"
                                                                 cacheable="false" session="true"
                                                                 createContent="#{SocialMailingControle.paintFotoConsulta}"
                                                         styleClass="styleFotoUsuarioSocial"
                                                         value="#{ImagemData}" mimeType="image/jpeg">
                                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                            <f:param name="largura" value="45"/>
                                            <f:param name="altura" value="45"/>
                                            <f:param name="usuario" value="#{result.codigo}"/>
                                                </a4j:mediaOutput>

                                        <h:graphicImage
                                                rendered="#{result.colaboradorVO.pessoa.possuiFotoNuvem && SuperControle.fotosNaNuvem}"
                                                styleClass="styleFotoUsuarioSocial"
                                                url="#{result.colaboradorVO.pessoa.urlFoto}">
                                                </h:graphicImage>


                                        <h:graphicImage
                                                rendered="#{(!SuperControle.fotosNaNuvem && !result.colaboradorVO.pessoa.possuiFoto) || (SuperControle.fotosNaNuvem && !result.colaboradorVO.pessoa.possuiFotoNuvem)}"
                                                                url="images/socialmail/padrao-user.png"
                                                styleClass="styleFotoUsuarioSocial"/>


                                            </a4j:commandLink>

                                            <a4j:commandLink styleClass="notselected"
                                                     reRender="painelContatos, buscar, conversa, repeat,campoBuscarAddUser, usersAdd, botaoAddPainel"
                                                     action="#{SocialMailingControle.selecionarResultadoAdicionar}"
                                                             oncomplete="hideAddContato();">
                                                <h:outputText  value="#{result.nomeAbreviado}"  escape="false">
                                                </h:outputText>
                                            </a4j:commandLink>
                                    <a4j:commandButton styleClass="botaoAddC"
                                                       action="#{SocialMailingControle.selecionarResultadoAdicionar}"
                                                               reRender="painelContatos, buscar, conversa, repeat, campoBuscarAddUser, usersAdd, botaoAddPainel"
                                                               oncomplete="hideAddContato();">


                                            </a4j:commandButton>

                                        </h:panelGrid>

                                    </h:column>
                                </h:dataTable>

                            </div>
                        </div>

                    </div>

                    <a4j:commandButton style="display: none;"
                                       reRender="painelContatos"
                                       id="atualizarPainelContatos"
                                       action="#{SocialMailing.verificarConsulta}">

                    </a4j:commandButton>


                    <!-- -------------- CAIXA DE PESQUISA ---------------------------- -->
            <c:if test="${SocialMailingControle.tipoSocialMail.codigo == 0}">
                <h:inputText id="buscar" style="width: 30%; height: 3.6%;" styleClass="campoBuscar caixaPesquisa"
                                 value="#{SocialMailingControle.consultaUsuario}"
                                 onfocus="if(this.value=='Buscar contato')this.value='';"
                                 onblur="if(this.value=='')this.value='Buscar contato';">
                    <a4j:queue name="queueFiltro" ignoreDupResponses="true" requestDelay="1000" timeout="60000"/>
                        <a4j:support status="statusInComponent" eventsQueue="queueFiltro" event="onkeyup"
                                     action="#{SocialMailingControle.consultarUsuario}" reRender="painelContatos"/>
                    </h:inputText>
            </c:if>

                    <!-- -------------- LISTA DE CONTATOS ---------------------------- -->

            <div class="${SocialMailingControle.tipoSocialMail.codigo == 1 ? 'caixaContatosView' : 'caixaContatos'}">

                        <h:panelGroup id="painelContatos">
                            <h:panelGroup rendered="#{SocialMailingControle.exibirResultados}">
                                <div class="caixaTituloContatosPesquisa">

                                    <table width="100%" height="100%" class="tituloContatosGrupos">
                                        <tr>
                                            <td valign="middle">
                                        <h:outputText
                                                value="#{msg_aplic.socialMailing_resultadoBusca}"/>
                                                </td>
                                                <td>
                                        <a4j:commandButton styleClass="botaoFecharCons"
                                                           action="#{SocialMailingControle.fecharConsulta}"
                                                           reRender="painelContatos, buscar"/>
                                                </td>
                                            </tr>
                                        </table>

                                    </div>

                        <rich:spacer height="6px"/>

                        <h:dataTable cellpadding="0" cellspacing="0" value="#{SocialMailingControle.listaUsuarios}"
                                     var="result" width="100%">
                                    <h:column>
                                <h:panelGrid columnClasses="centralizado, coluna1, coluna2" columns="3"
                                                     style="width:100%; height: 100%;">
                                    <rich:spacer width="3"/>
                                    <a4j:commandLink
                                            reRender="painelContatos, buscar, conversa, repeat, botaoAddPainel, nomeGrupoPainel, painelEnviarMensagem"
                                                             action="#{SocialMailingControle.selecionarContato}"
                                                             oncomplete="abaixar();">

                                        <a4j:mediaOutput element="img"
                                                                 rendered="#{result.colaboradorVO.pessoa.possuiFoto && !SuperControle.fotosNaNuvem}"
                                                         align="left"
                                                                 cacheable="false" session="true"
                                                                 createContent="#{SocialMailingControle.paintFotoConsulta}"
                                                         styleClass="styleFotoUsuarioSocial"
                                                         value="#{ImagemData}" mimeType="image/jpeg">
                                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                            <f:param name="largura" value="45"/>
                                            <f:param name="altura" value="45"/>
                                            <f:param name="usuario" value="#{result.codigo}"/>
                                                </a4j:mediaOutput>
                                        <h:graphicImage
                                                rendered="#{(!SuperControle.fotosNaNuvem && !result.colaboradorVO.pessoa.possuiFoto) || (SuperControle.fotosNaNuvem && !result.colaboradorVO.pessoa.possuiFotoNuvem)}"
                                                                url="images/socialmail/padrao-user.png"
                                                styleClass="styleFotoUsuarioSocial"/>

                                        <h:graphicImage
                                                rendered="#{result.colaboradorVO.pessoa.possuiFotoNuvem && SuperControle.fotosNaNuvem}"
                                                styleClass="styleFotoUsuarioSocial"
                                                url="#{result.colaboradorVO.pessoa.urlFoto}">
                                                </h:graphicImage>


                                            </a4j:commandLink>

                                    <a4j:commandLink styleClass="notselected"
                                                     reRender="painelContatos, buscar, conversa, repeat, botaoAddPainel, nomeGrupoPainel, painelEnviarMensagem"
                                                             action="#{SocialMailingControle.selecionarContato}"
                                                             oncomplete="abaixar();">
                                                <h:outputText  value="#{result.nomeAbreviado}"  escape="false">
                                                </h:outputText>
                                            </a4j:commandLink>

                                        </h:panelGrid>
                                <rich:spacer height="6px"/>
                                    </h:column>
                                </h:dataTable>

                            </h:panelGroup>

                            <h:panelGroup rendered="#{!SocialMailingControle.exibirResultados}">
                                <div class="caixaTituloContatos">
                                    <table width="100%" height="100%" class="tituloContatosGrupos">
                                        <tr>
                                            <td valign="middle">
                                            <h:outputText
                                                value="#{SocialMailingControle.tipoSocialMail.titulo}"/>
                                                </td>
                                    <c:if test="${SocialMailingControle.tipoSocialMail.codigo == 0}">
                                                <td>
                                            <a4j:commandLink
                                                    reRender="painelContatos, buscar, conversa, repeat, botaoAddPainel,nomeGrupoPainel, painelEnviarMensagem"
                                                                 style="color: #FFFFFF !important;"
                                                                 title="Clique aqui para criar um novo grupo"
                                                                 action="#{SocialMailingControle.novoGrupo}">
                                                    <i class="fa-icon-plus"></i> NOVO
                                                </a4j:commandLink>
                                            </td>
                                    </c:if>
                                        </tr>
                                    </table>
                                </div>
                        <rich:spacer height="6px"/>
                        <h:dataTable cellpadding="0" cellspacing="0" id="grupos" style="border: none !important;"
                                     width="100%"
                                             value="#{SocialMailingControle.gruposColetivos}" var="grupo">

                                    <h:column>
                                <h:panelGrid columnClasses="coluna1, coluna2" columns="3"
                                             styleClass="#{grupo.selected}" style="width:100%; height: 100%;">
                                            <a4j:commandLink action="#{SocialMailingControle.escolherGrupo}"
                                                     reRender="form, panelSuperiorSocial, painelEnviarMensagem, painelContatos, buscar, conversa, repeat, botaoAddPainel,nomeGrupoPainel, painelEnviarMensagem, caixaCentralizada"
                                                     status="false"
                                                             oncomplete="abaixar();">
                                        <h:graphicImage
                                                rendered="#{grupo.solicitacao_id > 0}"
                                                url="images/socialmail/profile-pacto-solicitacao.png"
                                                style="box-shadow: none"
                                                styleClass="styleFotoUsuarioSocial"/>

                                        <h:graphicImage
                                                rendered="#{grupo.solicitacao_id == 0}"
                                                url="images/socialmail/padrao-grupo.png" id="pic"
                                                style="box-shadow: none;"
                                                styleClass="styleFotoUsuarioSocial"/>
                                            </a4j:commandLink>

                                    <a4j:commandLink styleClass="#{grupo.selected}"
                                                     title="#{grupo.nomeTodos}"
                                                     reRender="form, panelSuperiorSocial, painelEnviarMensagem, painelContatos, buscar, conversa, repeat, botaoAddPainel,nomeGrupoPainel, painelEnviarMensagem, caixaCentralizada"
                                                     status="false"
                                                             action="#{SocialMailingControle.escolherGrupo}"
                                                             oncomplete="abaixar();">
                                        <h:outputText value="#{grupo.nomeResumido}" escape="false"/>
                                        <br/>
                                        <h:outputText rendered="#{!grupo.ativo && grupo.solicitacao_id > 0}"
                                                      value="Solicita��o Finalizada" style="color: red"/>
                                            </a4j:commandLink>

                                    <h:graphicImage rendered="#{grupo.nrMensagensNaoLidas > 0}"
                                                    width="20"
                                                    url="images/socialmail/icon-notificacao-24px.png"/>


                                        </h:panelGrid>

                                <rich:spacer height="6px"/>
                                    </h:column>

                                </h:dataTable>

                        <c:if test="${SocialMailingControle.tipoSocialMail.codigo == 0}">
                                <div class="caixaTituloContatos">
                                    <table width="100%" height="100%" class="tituloContatosGrupos">
                                        <tr>
                                            <td valign="middle">
                                            <h:outputText value="#{msg_aplic.socialMailing_contatos}"/>

                                                </td>
                                            <td>
                                            <h:panelGroup
                                                    rendered="#{SocialMailingControle.verificarPermissaoSomenteAtivos}"
                                                    layout="block" style="float: right;margin-right: 6%;">
                                                <h:selectBooleanCheckbox
                                                        value="#{SocialMailingControle.apresentarSomenteAtivos}">
                                                    <a4j:support action="#{SocialMailingControle.atualizarConversas}"
                                                                 event="onchange" reRender="form"/>
                                                </h:selectBooleanCheckbox>
                                                <h:outputText styleClass="text"
                                                              style="vertical-align: text-bottom;color: white;"
                                                              value="Somente Ativos"/>
                                                </h:panelGroup>
                                            </td>
                                            </tr>
                                        </table>

                                    </div>
                            <rich:spacer height="6px"/>

                            <h:dataTable cellpadding="0" cellspacing="0" id="contatos" style="border: none !important;"
                                         width="100%" value="#{SocialMailingControle.gruposSimples}"
                                         var="grupo">

                                    <h:column>

                                    <h:panelGrid columnClasses="centralizado,coluna1, coluna2" columns="4"
                                                 styleClass="#{grupo.selected}" style="width:100%; height: 100%;">
                                        <rich:spacer width="3"/>

                                            <a4j:commandLink style="border:none;"
                                                         reRender="form, painelContatos, panelSuperiorSocial, painelEnviarMensagem, buscar, conversa, repeat, botaoAddPainel, nomeGrupoPainel, painelEnviarMensagem, caixaCentralizada"
                                                         status="false"
                                                             action="#{SocialMailingControle.escolherGrupo}"
                                                             oncomplete="abaixar();">
                                                <a4j:outputPanel>
                                                <a4j:mediaOutput
                                                        rendered="#{grupo.temFoto && !SuperControle.fotosNaNuvem}"
                                                        element="img"
                                                        align="left"
                                                                     cacheable="false" session="true"
                                                                     createContent="#{SocialMailingControle.paintFoto}"
                                                        styleClass="styleFotoUsuarioSocial"
                                                        value="#{ImagemData}" mimeType="image/jpeg">
                                                        <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                                    <f:param name="largura" value="45"/>
                                                    <f:param name="altura" value="45"/>
                                                        <f:param name="grupo" value="#{grupo.codigo}"/>

                                                    </a4j:mediaOutput>
                                                </a4j:outputPanel>
                                            <h:graphicImage
                                                    rendered="#{(!grupo.temFoto && !SuperControle.fotosNaNuvem) || (!grupo.temFotoNuvem && SuperControle.fotosNaNuvem)}"
                                                                url="images/socialmail/padrao-user.png"
                                                    styleClass="styleFotoUsuarioSocial"/>
                                            <h:graphicImage
                                                    rendered="#{grupo.temFotoNuvem && SuperControle.fotosNaNuvem}"
                                                    styleClass="styleFotoUsuarioSocial"
                                                    url="#{grupo.urlFoto}">
                                                </h:graphicImage>

                                            </a4j:commandLink>
                                            <a4j:commandLink styleClass="#{grupo.selected}"
                                                         title="#{grupo.nomeTodos}"
                                                         reRender="form, painelContatos, panelSuperiorSocial, painelEnviarMensagem, buscar, conversa, repeat, botaoAddPainel,nomeGrupoPainel, painelEnviarMensagem, caixaCentralizada"
                                                             status="false" oncomplete="abaixar();"
                                                             action="#{SocialMailingControle.escolherGrupo}">
                                                <h:outputText  value="#{grupo.nomeResumido}" escape="false">
                                                </h:outputText>
                                            </a4j:commandLink>
                                        <h:graphicImage rendered="#{grupo.nrMensagensNaoLidas > 0}"
                                                        url="images/socialmail/icon-notificacao-24px.png"/>
                                        </h:panelGrid>
                                    <rich:spacer height="6px"/>
                                    </h:column>
                                </h:dataTable>
                        </c:if>
                            </h:panelGroup>
                        </h:panelGroup>
                    </div>

                        <!-- -------------- CAIXA DE ENVIO ---------------------------- -->
            <div id="painelEnviarMensagem" class="caixaEscrever">
                            <table width="100%" cellpadding="0" cellspacing="0">
                    <h:panelGroup layout="block" id="panelEscrever" style="width: 100%; display: inline-flex">
                        <h:panelGroup layout="block" id="panelInputEscrever" style="width: 80%">
                            <h:inputTextarea id="escrever"
                                             styleClass="campoEscrever"
                                             rendered="#{SocialMailingControle.permiteEnviarMensagem}"
                                                     onkeydown="if(event.shiftKey && event.keyCode == 13) { return;}else{ if(event.keyCode == 13) {document.getElementById('form:enviarMensagem').click();}else return;}"
                                                     value="#{SocialMailingControle.socialMail.texto}"> </h:inputTextarea>
                        </h:panelGroup>
                        <h:panelGroup layout="block" id="panelInputEnviar" style="width: 20%; margin-left: 10px">
                                    <a4j:commandButton styleClass="botaoEnviar" id="enviarMensagem"
                                               rendered="#{SocialMailingControle.permiteEnviarMensagem}"
                                                       action="#{SocialMailingControle.enviarMensagem}"
                                                       reRender="conversa, escrever, checkenviarsms" focus="escrever"
                                                       oncomplete="#{SocialMailingControle.oncompleteCarrossel}">
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGroup>
                            <tr style="vertical-align: middle;">
                                <td>
                                    <table>
                                        <tr style="vertical-align: middle;">
                                    <td>
                                        <h:selectBooleanCheckbox value="#{SocialMailingControle.socialMail.enviarSMS}"
                                                                     id="checkenviarsms"
                                                                 rendered="#{LoginControle.permissaoAcessoMenuVO.enviarSMSSocialMailing and !SocialMailingControle.grupoSolicitacao and !SocialMailingControle.grupoSelected.coletivo and SocialMailingControle.permiteEnviarMensagem}">
                                        </h:selectBooleanCheckbox>
                                    </td>
                                            <td>
                                        <h:outputText
                                                value="Voc� n�o pode enviar mensagem com o usu�rio #{LoginControle.usuarioLogado.usuarioPACTOBR ? 'PACTOBR' : 'ADMIN'}!"
                                                styleClass="notselected"
                                                style="font-size: 14px !important; color: red !important;"
                                                rendered="#{LoginControle.usuarioLogado.usuarioPACTOBR || LoginControle.usuarioLogado.usuarioAdminPACTO}">
                                        </h:outputText>
                                        <h:outputText
                                                value="Solicita��o Finalizada: Entendemos o caso por resolvido!"
                                                styleClass="notselected"
                                                style="font-size: 14px !important; color: red !important;"
                                                rendered="#{SocialMailingControle.grupoSolicitacao && !SocialMailingControle.grupoSelected.ativo and !LoginControle.usuarioLogado.usuarioPACTOBR and !LoginControle.usuarioLogado.usuarioAdminPACTO}">
                                        </h:outputText>
                                                <h:outputText  value="Enviar via SMS" styleClass="notselected"
                                                      rendered="#{LoginControle.permissaoAcessoMenuVO.enviarSMSSocialMailing and !SocialMailingControle.grupoSolicitacao and !SocialMailingControle.grupoSelected.coletivo and SocialMailingControle.permiteEnviarMensagem}">
                                                </h:outputText>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>


            <h:inputHidden value="#{SocialMailingControle.matriculaSelecionada}"
                           id="matriculaSelecionada"/>
            <h:inputHidden value="#{SocialMailingControle.grupoSelecionado}" id="grupoSelecionado"/>
            <h:inputHidden value="#{SocialMailingControle.idSelecionado}" id="idSelecionado"/>

                <a4j:commandButton action="#{SocialMailingControle.mostrarAluno}"
                                   style="display: none;" oncomplete="#{SocialMailingControle.onCompleteTag}"
                               id="mostrarAluno"/>
                <a4j:commandButton action="#{SocialMailingControle.selecionarGrupo}"
                                   style="display: none;"
                                   reRender="painelContatos, buscar, conversa, repeat, botaoAddPainel"
                               id="selecionarGrupo"/>


            </html>


    </h:form>
    <script>
        abaixar();

    </script>


    <%@include file="includes/imports.jsp" %>

    <!-- ------------------------------------------------------------------------------ -->
    <rich:modalPanel id="cfgGrupo" autosized="true"
                     shadowOpacity="true" width="450" minHeight="180"
                     top="100">

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidecfgGrupo"/>
                <rich:componentControl for="cfgGrupo"
                                       attachTo="hidecfgGrupo" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCfgGrupo" ajaxSubmit="true">
            <br/>
            <h:panelGrid width="100%"  id="panelCfgGrupo">
                <rich:dataGrid value="#{SocialMailingControle.grupoSelected.participantes}" var="partner"
                               width="100%" columns="2"
                               style="border: none !important;"
                               columnClasses="semBorda">
                    <h:column>
                        <h:panelGrid columns="2" width="100%">
                            <a4j:commandLink onclick="#{SocialMailingControle.onClickTirar}"
                                             reRender="painelContatos, buscar, conversa, repeat, panelCfgGrupo"
                                             action="#{SocialMailingControle.tirarParticipantedoGrupo}"
                                             oncomplete="#{SocialMailingControle.msgAlert}">

                                <h:graphicImage
                                        rendered="#{partner.usuarioPactoBR}"
                                        url="images/socialmail/profile-pacto-solicitacao.png"
                                        styleClass="styleFotoUsuarioSocial"/>

                                <a4j:mediaOutput element="img"
                                                 title="#{partner.participante.nome} (Clique para remover)"
                                                 align="left"
                                                 styleClass="styleFotoUsuarioSocial"
                                                 cacheable="false" session="true"
                                                 createContent="#{SocialMailingControle.paintFotoMensagens}"
                                                 value="#{ImagemData}" mimeType="image/jpeg"
                                                 rendered="#{!SuperControle.fotosNaNuvem && !partner.usuarioPactoBR}">
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="45"/>
                                    <f:param name="altura" value="45"/>
                                    <f:param name="pessoa" value="#{partner.participante.codigo}"></f:param>
                                </a4j:mediaOutput>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem && !partner.usuarioPactoBR}"
                                                styleClass="styleFotoUsuarioSocial"
                                                url="#{partner.participante.urlFoto}"
                                                title="#{partner.participante.nome} (Clique para remover)">
                                </h:graphicImage>
                            </a4j:commandLink>
                            <a4j:commandLink onclick="#{SocialMailingControle.onClickTirar}"
                                             reRender="painelContatos, buscar, conversa, repeat, panelCfgGrupo"
                                             action="#{SocialMailingControle.tirarParticipantedoGrupo}"
                                             oncomplete="#{SocialMailingControle.msgAlert}"
                                             title="#{partner.participante.nome} (Clique para remover)">
                                <h:outputText value="#{partner.participante.nomeAbreviado}"
                                              style="font-size: 8pt; padding-top: 5px; color:black; font-weight: bold;"/>
                            </a4j:commandLink>

                        </h:panelGrid>
                    </h:column>
                </rich:dataGrid>
                <h:panelGrid columns="2">
                    <a4j:commandButton id="salvar"
                                       value="Sair do grupo"
                                       reRender="form, panelAutorizacaoFuncionalidade"
                                       action="#{SocialMailingControle.sairGrupo}"
                                       onclick="if(!confirm('Tem certeza que deseja sair do grupo?')){return false;};"
                                       oncomplete="Richfaces.hideModalPanel('cfgGrupo');"
                                       title="Sair do grupo" accesskey="2" styleClass="botoes nvoBt btSec"/>

                    <a4j:commandButton id="estornar"
                                       oncomplete="Richfaces.hideModalPanel('cfgGrupo');"
                                       value="Desativar o grupo"
                                       onclick="if(!confirm('Tem certeza que deseja desativar o grupo?')){return false;};"
                                       rendered="#{SocialMailingControle.grupoSelected.souDono}"
                                       reRender="form, panelAutorizacaoFuncionalidade"
                                       action="#{SocialMailingControle.desativarGrupo}"
                                       title="Desativar o grupo" accesskey="2" styleClass="botoes nvoBt btSec"/>

                </h:panelGrid>

            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>
</f:view>


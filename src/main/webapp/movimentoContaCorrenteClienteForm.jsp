<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />



<f:view locale="#{SuperControle.idioma}">

    <rich:modalPanel id="panelInformacao"  styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="200">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informação"/>
            </h:panelGroup>
        </f:facet>
        
        <f:facet name="controls">
            
        </f:facet>
        
        <a4j:form id="formInformacao">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" width="100%">
                    <rich:spacer style="display:block" width="10"/>
                    <h:outputText styleClass="mensagem"  value="#{MovimentoContaCorrenteClienteControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{MovimentoContaCorrenteClienteControle.mensagemDetalhada}"/>
                    <h:panelGrid width="100%" columns="1" style="text-align:center; padding:20px 0 0 0;">
                        <h:panelGroup>
                            <rich:spacer width="10"/>
                            <a4j:commandButton reRender="panelInformacao, tabMensagens" id="fechar" image="./imagens/botaoFechar.png"
                                               oncomplete="#{MovimentoContaCorrenteClienteControle.oncomplete}"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <title>
        <h:outputText value="#{msg_aplic.prt_MovimentoContaCorrenteCliente_tituloForm}"/>
    </title>
    <rich:modalPanel id="panelPessoa"   styleClass="novaModal" autosized="true" shadowOpacity="true" width="550" height="250">
       <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Pessoa"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink1"/>
                <rich:componentControl for="panelPessoa" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formPessoa" ajaxSubmit="true" styleClass="paginaFontResponsiva">
            <h:panelGrid columns="1" columnClasses="classEsquerda, classDireita" width="100%" >
                <h:panelGrid columns="4" footerClass="colunaCentralizada" styleClass="paginaFontResponsiva" width="100%">
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-bold" value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultaPessoa"
                                         value="#{MovimentoContaCorrenteClienteControle.campoConsultaPessoa}">
                            <f:selectItems value="#{MovimentoContaCorrenteClienteControle.tipoConsultaComboPessoa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaPessoa" size="10"  value="#{MovimentoContaCorrenteClienteControle.valorConsultaPessoa}"/>
                    <a4j:commandLink  id="btnConsultar"
                                        reRender="formPessoa:mensagemConsultaPessoa, formPessoa:resultadoConsultaPessoa, formPessoa:scResultadoPessoa"
                                        action="#{MovimentoContaCorrenteClienteControle.consultarPessoa}" styleClass="botaoPrimario texto-size-14" value="#{msg_bt.btn_consultar}"
                                        title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaPessoa" width="100%"  styleClass="tabelaSimplesCustom"
                                value="#{MovimentoContaCorrenteClienteControle.listaConsultaPessoa}" rows="5" var="cliente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Categoria_codigo_maiusculo}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{cliente.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Cliente_label_nome_maiusculo}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{cliente.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Cadastro_label_situacao_maiusculo}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{cliente.situacao_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Cliente_label_matricula_maiusculo}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{cliente.matricula}"/>
                    </rich:column>
                    <rich:column>
                        <a4j:commandLink action="#{MovimentoContaCorrenteClienteControle.selecionarPessoa}"
                                           focus="nomePessoa" reRender="form" oncomplete="Richfaces.hideModalPanel('panelPessoa')"
                                           title="#{msg.msg_selecionar_dados}" styleClass="linkPadrao texto-cor-azul">
                            <h:outputText styleClass="texto-font texto-size-16">Selecionar </h:outputText>
                            <h:outputText styleClass="fa-icon-arrow-right"></h:outputText>

                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formPessoa:resultadoConsultaPessoa" maxPages="10"
                                   styleClass="scrollPureCustom"
                                   renderIfSinglePage="false"
                                   id="scResultadoPessoa" />
                <h:panelGrid id="mensagemConsultaPessoa" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{MovimentoContaCorrenteClienteControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{MovimentoContaCorrenteClienteControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{MovimentoContaCorrenteClienteControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{MovimentoContaCorrenteClienteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>             
                </h:panelGrid>
            </h:panelGrid>        
        </a4j:form>
    </rich:modalPanel>



    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <%-- INICIO HEADER --%>
                <c:set var="titulo" scope="session" value="${msg_aplic.prt_MovimentoContaCorrenteCliente_tituloForm}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-lancar-credito-ou-debito-na-conta-corrente-do-aluno/"/>

                <jsp:include page="topoReduzido_material.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="pages/ce/includes/topoReduzido.jsp"/>
            </c:if>

        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">


                    <h:outputText   value="#{msg_aplic.prt_MovimentoContaCorrenteCliente_pessoaNome}" />                    
                    <h:panelGroup >
                        <h:inputText id="nomePessoaa" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" readonly="#{!MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.novoObj}" value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.pessoa.nome}" />
                        <a4j:commandButton disabled="#{!MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.novoObj}" id="consultaDadosPessoa" focus="nomePessoa" alt="Localizar Pessoa" reRender="formPessoa" oncomplete="Richfaces.showModalPanel('panelPessoa')" image="imagens/informacao.gif" />
                    </h:panelGroup>

                    <h:outputText   value="#{msg_aplic.prt_MovimentoContaCorrenteCliente_descricao}" />
                    <h:inputText  id="descricao" size="45" maxlength="45"  onblur="blurinput(this);"  readonly="#{!MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.novoObj}" onfocus="focusinput(this);" styleClass="form" value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.descricao}" />
                    <h:outputText   value=" " />
                    <h:selectOneRadio  id="tipoMovimentacao" onblur="blurinput(this);" disabled="#{!MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.novoObj}" onfocus="focusinput(this);" styleClass="form"  value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.tipoMovimentacao}" >
                        <f:selectItems  value="#{MovimentoContaCorrenteClienteControle.listaSelectItemTipoMovimentacao}" />
                    </h:selectOneRadio>

                    <h:outputText   value="#{msg_aplic.prt_MovimentoContaCorrenteCliente_valor}" />
                    <h:inputText  id="valor" size="8" maxlength="10"  onblur="blurinput(this);"  readonly="#{!MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.novoObj}"
                                  onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                  styleClass="form" value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.valor}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>
                   
                    <%--h:inputText  id="dataRegistro" readonly="true"  onchange="return mascara(this.form, 'form:dataRegistro', '99/99/9999', event);" size="10" maxlength="10"  onblur="blurinput(this);return validar_Data('form:dataRegistro');"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura"value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.dataRegistro}" >
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:inputText--%>

                    <h:outputText   value="#{msg_aplic.prt_MovimentoContaCorrenteCliente_saldoAtual}" />
                    <h:inputText  id="saldoAtual" size="20" maxlength="20" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.saldoAtual}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>

                    <h:outputText   value="#{msg_aplic.prt_MovimentoContaCorrenteCliente_responsavelAutorizacao}" />
                    <h:panelGroup>
                        <h:inputText  id="responsavelAutorizacao" size="5" maxlength="20" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.responsavelAutorizacao.codigo}" />

                        <h:outputText value="          "/>

                        <h:outputText   value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.responsavelAutorizacao.nome}" />
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{MovimentoContaCorrenteClienteControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{MovimentoContaCorrenteClienteControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{MovimentoContaCorrenteClienteControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{MovimentoContaCorrenteClienteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{MovimentoContaCorrenteClienteControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/> 

                                <a4j:commandButton id="salvar" focus="descricao" reRender="formInformacao,form"
                                                   action="#{MovimentoContaCorrenteClienteControle.gravar}"
                                                   oncomplete="#{MovimentoContaCorrenteClienteControle.msgAlert}"
                                                   value="#{msg_bt.btn_gravar}"
                                                   alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                                <h:outputText value="    "/>

                                <h:panelGroup id="grupoBtnExcluir">
                                    <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                       oncomplete="#{MovimentoContaCorrenteClienteControle.msgAlert}" action="#{MovimentoContaCorrenteClienteControle.confirmarExcluir}"
                                                       value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                                </h:panelGroup>

                                <%--<a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{MovimentoContaCorrenteClienteControle.excluir}" value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>--%>

                                <h:outputText value="    "/> 

                                <a4j:commandButton id="consultar" action="#{MovimentoContaCorrenteClienteControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec consultar"/>
                            </h:panelGroup>
                        </c:if>

                        <c:if test="${modulo eq 'centralEventos'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{MovimentoContaCorrenteClienteControle.novo}"
                                                   value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                                <h:outputText value="    "/> 

                                <a4j:commandButton id="salvar" action="#{MovimentoContaCorrenteClienteControle.gravarCE}"
                                                   focus="descricao" reRender="formInformacao,form"
                                                   oncomplete="Richfaces.showModalPanel('panelInformacao')"
                                                   value="#{msg_bt.btn_gravar}"
                                                   alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                                   actionListener="#{MovimentoContaCorrenteClienteControle.autorizacao}">
                                    <!-- entidade.MCCC  -->
                                    <f:attribute name="entidade" value="122"/>
                                    <!-- operacao.gravar  -->
                                    <f:attribute name="operacao" value="G"/>
                                </a4j:commandButton>

                                <h:outputText value="    "/> 

                                <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                   action="#{MovimentoContaCorrenteClienteControle.excluirCE}"
                                                   value="#{msg_bt.btn_excluir}"
                                                   alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                                                   actionListener="#{MovimentoContaCorrenteClienteControle.autorizacao}">
                                    <!-- entidade.MCCC  -->
                                    <f:attribute name="entidade" value="122"/>
                                    <!-- operacao.excluir  -->
                                    <f:attribute name="operacao" value="E"/>
                                </a4j:commandButton>

                                <h:outputText value="    "/> 

                                <a4j:commandButton id="consultar"
                                                   action="#{MovimentoContaCorrenteClienteControle.inicializarConsultar}"
                                                   value="#{msg_bt.btn_consultar}"
                                                   alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec consultar"
                                                   actionListener="#{MovimentoContaCorrenteClienteControle.autorizacao}">
                                    <!-- entidade.MCCC  -->
                                    <f:attribute name="entidade" value="122"/>
                                    <!-- operacao.consulta -->
                                    <f:attribute name="operacao" value="C"/>
                                </a4j:commandButton>
                            </h:panelGroup>
                        </c:if>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
</f:view>

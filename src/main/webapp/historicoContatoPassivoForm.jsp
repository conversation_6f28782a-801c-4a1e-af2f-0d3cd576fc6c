<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:panelGrid columns="1" styleClass="tabForm" width="100%">
        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                     style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;">
            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_historicoContato}" />
        </h:panelGrid>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAgendamento" />
                <rich:componentControl for="panelAgendamento" attachTo="hiperlinkAgendamento" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" width="100%" style="border:1px solid black">
                    <h:panelGrid id="dadosPassivo" columns="1" width="100%" 
                                 style="border:1px solid black" columnClasses="colunaEsquerda"
                                 rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoPassivo}">
                        <h:panelGrid columns="1" columnClasses="colunaEsquerda"
                                     cellpadding="0" cellspacing="0" style="text-align: top;"
                                     width="100%">
                            <h:panelGrid columns="2" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
                                    <h:panelGroup>
                                        <h:outputText styleClass="titulosAgenda" value="#{msg_aplic.prt_Agenda_aluno}" />
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.passivoVO.nome}" />
                                    </h:panelGroup>
                                </h:panelGrid>

                                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
                                    <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.passivoVO.dia_Apresentar}" />
                                </h:panelGrid>
                            </h:panelGrid>

                            <rich:spacer width="30px;" />

                            <h:panelGroup>
                                <h:panelGrid columns="5" width="100%">
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_diasUltAcesso}" />
                                        <h:outputText styleClass="camposAgenda"
                                                      rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}"
                                                      value="#{HistoricoContatoControle.historicoContatoVO.nrDiasUltimoAcesso}" />
                                        <rich:spacer height="15px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_ligacoes}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.totalLigacao}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdEmail}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdEmail}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdPessoal}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdPessoal}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdSMS}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdSMS}" />
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                                     style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;">
                            <h:outputText styleClass="tituloFormulario" value="Telefones" />
                        </h:panelGrid>

                        <h:panelGrid id="listaTelefonesPassivo" columns="1" columnClasses="colunaCentralizada" width="100%">
                            <rich:dataTable id="tabelaTelefones" width="100%" headerClass="subordinado"
                                            value="#{HistoricoContatoControle.historicoContatoVO}"
                                            rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                            var="historicoPassivo">
                                <rich:column width="33%">
                                    <f:facet name="header">
                                        <h:outputText value="Residencial" />
                                    </f:facet>
                                    <h:outputText value="#{historicoPassivo.passivoVO.telefoneResidencial}" />
                                </rich:column>

                                <rich:column width="33%">
                                    <f:facet name="header">
                                        <h:outputText value="Celular" />
                                    </f:facet>
                                    <h:outputText value="#{historicoPassivo.passivoVO.telefoneCelular}" />
                                </rich:column>

                                <rich:column width="33%">
                                    <f:facet name="header">
                                        <h:outputText value="Trabalho" />
                                    </f:facet>
                                    <h:outputText value="#{historicoPassivo.passivoVO.telefoneTrabalho}" />
                                </rich:column>
                            </rich:dataTable>
                        </h:panelGrid>

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                                     style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_historicoContato}" />
                        </h:panelGrid>

                        <h:panelGrid id="listaHistoricoPassivo" columns="1" columnClasses="colunaCentralizada" width="100%">
                            <rich:dataTable id="resTelefone" width="100%" headerClass="subordinado"
                                            value="#{HistoricoContatoControle.listaHistoricoContatoPassivo}"
                                            rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                            var="historicoPassivo">
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_dataHora}" />
                                    </f:facet>
                                    <h:outputText value="#{historicoPassivo.dia_Apresentar}" />
                                </rich:column>

                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_resultado}" />
                                    </f:facet>
                                    <h:outputText value="#{historicoPassivo.resultado}" />
                                </rich:column>

                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_fase}" />
                                    </f:facet>
                                    <h:outputText value="#{historicoPassivo.fase_Apresentar}" />
                                </rich:column>

                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_usuario}" />
                                    </f:facet>
                                    <h:outputText
                                        value="#{historicoPassivo.responsavelCadastro.colaboradorVO.pessoa.primeiroNomeConcatenado}" />
                                </rich:column>

                                <rich:column colspan="4" breakBefore="true" style="vertical-align:top; text-align: left;">
                                    <h:outputText value="#{historicoPassivo.observacao}" escape="false" />
                                </rich:column>
                            </rich:dataTable>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic['operacoes.atualizar.bd.executado']}"/>
    </title>
    <c:set var="titulo" scope="session" value="Atualizações de banco de dados executadas"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Acesso_ao_sistema:Atualizações_de_Banco_de_Dados_executadas"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGroup id="parametrosPaginacaoConsulta">
                <h:inputHidden value="#{AtualizadorBDControle.controleConsulta.paginaAtual}" />
                <h:inputHidden value="#{AtualizadorBDControle.controleConsulta.tamanhoConsulta}" />
            </h:panelGroup>

            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="3" width="100%">
                    <h:column>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic['entidade.atualizacao.resultado']}:"/>&nbsp;
                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{AtualizadorBDControle.atualizacao.resultado}">
                            <f:selectItem itemValue="" itemLabel="#{msg_aplic['operacoes.todos']}"/>
                            <f:selectItems value="#{AtualizadorBDControle.itensResultado}" />
                        </h:selectOneMenu>
                    </h:column>

                    <h:column>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic['entidade.dataDe']}"/>&nbsp;
                        <rich:calendar id="dataInicial" styleClass="form" value="#{AtualizadorBDControle.atualizacao.dataInicial}"
                                       datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                       oninputchange="if(!validar_Data(this.id)) {this.value = ''};" oninputkeypress="return mascara(this, '99/99/9999', event);"
                                       enableManualInput="true" zindex="2" showWeeksBar="false" />&nbsp;
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic['entidade.dataAte']}"/>&nbsp;
                        <rich:calendar id="dataFinal" styleClass="form" value="#{AtualizadorBDControle.atualizacao.dataFinal}"
                                       datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                       oninputchange="if(!validar_Data(this.id)) {this.value = ''};" oninputkeypress="return mascara(this, '99/99/9999', event);"
                                       enableManualInput="true" zindex="2" showWeeksBar="false" />
                    </h:column>
                        <h:panelGroup id="botoes">
                        <h:column>
                            <a4j:commandLink id="consultar" reRender="controlePaginas, itens, mensagens, parametrosPaginacaoConsulta,botoes"
                                               style="font-size: 14px; padding: 6px;"
                                               styleClass="pure-button pure-button-primary" value="#{msg_aplic['operacoes.consultar']}" action="#{AtualizadorBDControle.irPaginaInicial}"
                                               title="#{msg_aplic['operacoes.consultar']}"/>
                      
                        <%--BOTÃO EXCEL--%>
                        <a4j:commandLink id="exportarExcel"
                                           style="margin-left: 8px;"
                                           styleClass="exportadores"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty AtualizadorBDControle.atualizacoes}"
                                           oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                           accesskey="2">
                            <f:attribute name="lista" value="#{AtualizadorBDControle.atualizacoes}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos" value="versao=Versão,dataFormatada=Data,resultado_Apresentar=Resultado,nomeUsuario=Usuário"/>
                            <f:attribute name="prefixo" value="AtualizacaoBancoDeDados"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                        <%--BOTÃO PDF--%>
                        <a4j:commandLink id="exportarPdf"
                                           style="margin-left: 8px;"
                                           styleClass="exportadores"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty AtualizadorBDControle.atualizacoes}"
                                           oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                           accesskey="2">
                            <f:attribute name="lista" value="#{AtualizadorBDControle.atualizacoes}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos" value="versao=Versão,dataFormatada=Data,resultado_Apresentar=Resultado,nomeUsuario=Usuário"/>
                            <f:attribute name="prefixo" value="AtualizacaoBancoDeDados"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>
                          </h:column>
                    </h:panelGroup>
               	</h:panelGrid>

               	<h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                    <f:facet name="footer">
                        <h:panelGroup id="controlePaginas" rendered="#{AtualizadorBDControle.apresentarResultadoConsulta}" binding="#{AtualizadorBDControle.apresentarLinha}">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false" binding="#{AtualizadorBDControle.apresentarPrimeiro}" action="#{AtualizadorBDControle.irPaginaInicial}"/>
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false" binding="#{AtualizadorBDControle.apresentarAnterior}" action="#{AtualizadorBDControle.irPaginaAnterior}"/>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{AtualizadorBDControle.paginaAtualDeTodas}" rendered="true" />
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false" binding="#{AtualizadorBDControle.apresentarPosterior}" action="#{AtualizadorBDControle.irPaginaPosterior}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false" binding="#{AtualizadorBDControle.apresentarUltimo}" action="#{AtualizadorBDControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
               	</h:panelGrid>


                <rich:dataTable id="itens" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{AtualizadorBDControle.atualizacoes}" rows="10" var="atualizacao">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic['entidade.atualizacao.versao']}"/>
                        </f:facet>
                        <h:outputText value="#{atualizacao.versao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic['entidade.atualizacao.data']}"/>
                        </f:facet>
                        <h:outputText value="#{atualizacao.dataFormatada}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic['entidade.atualizacao.resultado']}"/>
                        </f:facet>
                        <h:outputText value="#{atualizacao.resultado_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic['entidade.atualizacao.usuario']}"/>
                        </f:facet>
                        <h:outputText value="#{atualizacao.nomeUsuario}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic['entidade.atualizacao.descricao']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AtualizadorBDControle.exibirDescricaoAtualizacao}" value="Exibir" styleClass="botoes"
                                           reRender="panelDetalhesAtualizacoes" type="submit" oncomplete="Richfaces.showModalPanel('panelDetalhesAtualizacoes');" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic['entidade.atualizacao.script']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AtualizadorBDControle.exibirScriptAtualizacao}" value="Exibir" styleClass="botoes"
                                           reRender="panelDetalhesAtualizacoes" type="submit" oncomplete="Richfaces.showModalPanel('panelDetalhesAtualizacoes');" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic['entidade.atualizacao.mensagem']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AtualizadorBDControle.exibirMensagemAtualizacao}" value="Exibir" styleClass="botoes"
                                           reRender="panelDetalhesAtualizacoes" type="submit" oncomplete="Richfaces.showModalPanel('panelDetalhesAtualizacoes');" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic['entidade.atualizacao.stackTrace']}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AtualizadorBDControle.exibirStackTraceAtualizacao}" value="Exibir" styleClass="botoes"
                                           reRender="panelDetalhesAtualizacoes" type="submit" oncomplete="Richfaces.showModalPanel('panelDetalhesAtualizacoes');" />
                    </rich:column>
                </rich:dataTable>

                <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{AtualizadorBDControle.sucesso}" image="/imagens/bt_sucesso.png"/>
                        <h:commandButton rendered="#{AtualizadorBDControle.erro}" image="/imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{AtualizadorBDControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{AtualizadorBDControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

            </h:panelGrid>            

        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="panelDetalhesAtualizacoes" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic['operacoes.atualizar.bd.executado']}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkDetalhesAtualizacoes"/>
                <rich:componentControl for="panelDetalhesAtualizacoes" attachTo="hidelinkDetalhesAtualizacoes" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup layout="block"  style="height: 450px; overflow: auto;" >
            <h:outputText value="#{AtualizadorBDControle.detalheAtualizacao}" escape="false" />
        </h:panelGroup>
    </rich:modalPanel>
</f:view>
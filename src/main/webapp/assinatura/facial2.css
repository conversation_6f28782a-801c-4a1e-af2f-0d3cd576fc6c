.cabecalhofacial{
    width: 100%;
    font-weight: normal;
    font-style: normal;
    padding: 40px 30px;
    font-size: 38px;
    text-align: left;
}
video{
    width: 96%;
    max-height: 75vh;
}
.containervideo{
    text-align: center;
    position: relative
}
.containerbtsvideo{
    display: block;
    width: 100%;
    text-align: center;
    position: relative
}
.btnfacial.virar{
    left: 5vw;
}
.btnfacial.avancar{
    right: 5vw;
}
.btnfacial{
    position: absolute;
    font-size: 15vw;
    top: 8vw;
    color: lightgray;
}
.frt-take{
    background-color: white;
    border-radius: 50%;
    height: 20vw;
    width: 20vw;
    margin-top: 5vw;
    display: inline-block;
}
.bck-take{
    background-color: lightgrey;
    width: 30vw;
    height: 30vw;
    border-radius: 50%;
    margin-top: 30px;
    text-align: center;
    display: inline-block;
}
.amostra{
    position: absolute;
    text-align: right;
    right: 5vw;
    top: 5vw;
}
.amostra img{
    width: 15vw;
}
@media (orientation: landscape) {
    video{
        width: auto;
        height: 30vh;
    }
    .containerbtsvideo{
        width: 50%;
        margin: auto;
    }
    .btnfacial{
        font-size: 5vw;
    }
    .bck-take {
        width: 10vw;
        height: 10vw;
        margin-top: 6vw;
    }
    .frt-take {
        height: 6vw;
        width: 6vw;
        margin-top: 2vw;
    }
    .amostra{
        position: absolute;
        text-align: right;
        right: 45%;
        top: 33vh;
    }
    .amostra img{
        width: 4vw;
    }
}

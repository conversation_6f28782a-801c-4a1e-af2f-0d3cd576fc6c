/*.uploadBox {*/
/*    width: 100%;*/
/*    text-align: center;*/
/*}*/


.uploadBox {
    display: flex;
    flex-direction: column; /* Organiza os itens verticalmente */
    justify-content: center; /* Centraliza os itens verticalmente dentro do container */
    align-items: center; /* Alinha os itens ao centro horizontalmente */
}

.uploadBox.videoContainer {
    margin: 0 auto;
    border: 5px #333 solid;
    width: 95%;
    height: 94%;
    display: none
}

.upload_img_btn {
    overflow: hidden;
    position: relative;
    vertical-align: bottom!important
}
.upload_img_btn [type=file] {
    cursor: pointer;
    display: block;
    min-height: 100%;
    min-width: 100%;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0
}
.imageUploaded {
    display: none;
    max-width: 100%;
    max-height: 100%;
    width: 98%;
    height: 95%;
    margin: auto
}
/*.capFotoButton {*/
/*    display: block;*/
/*    margin: 0 auto;*/
/*    text-align: center;*/
/*}*/
.capFotoButton {
    display: inline-block; /* Permite que o botão ocupe apenas o tamanho necessário */
    margin: 20px 0; /* Adiciona um espaço entre os botões e outros elementos */
    text-align: center;
}
.capFotoButton.hiddenBtn,
.jcrop-tracker.noDrag,
.jcrop-handle.disabled,
.jcrop-dragbar.disabled,
.uploadBox.imageDiv {
    display: none
}
.loadingWebcam {
    display: none;
    font-size: 150px;
    position: absolute;
    color: #094771;
    animation: pulse infinite 1500ms;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.5);
    }
}

.closeBtn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: #000;
    font-size: 24px;
    cursor: pointer;
    z-index: 1100;
    display: block;
}

.closeBtn i {
    font-size: 24px;
}

.modal {
    padding-top: 10px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background-color: white;
    display: none;
    overflow: hidden;
    border-radius: 10px;
    z-index: 1000;
    text-align: center;
}

.modal-content {
    margin: 10% auto;
    padding: 20px;
    width: 400px;
    text-align: center;
    border-radius: 10px;
}
.hidden {
    display: none;
}

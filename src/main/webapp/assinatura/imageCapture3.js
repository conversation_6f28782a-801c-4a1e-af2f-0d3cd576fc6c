/*
Copyright 2017 Google Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

'use strict';

/* globals ImageCapture */

// This code is adapted from
// https://cdn.rawgit.com/Miguelao/demos/master/imagecapture.html

// window.isSecureContext could be used for Chrome
var isSecureOrigin = location.protocol === 'https:' ||
    location.host === 'localhost';

var constraints;
var imageCptur;
var mediaStream;
var img = document.querySelector('#fotofacial1');
var img2 = document.querySelector('#fotofacial2');
var video = document.querySelector('video');



// Get a list of available media input (and output) devices
// then get a MediaStream for the currently selected input device


// From the list of media devices available, set up the camera source <select>,
// then get a video stream from the default camera source.
var deviceSelecionado;
var device1;
var device2;

function gotDevices(deviceInfos) {
    console.log('gotDevices');
    device1 = null;
    device2 = null;
    for (var i = 0; i !== deviceInfos.length; ++i) {
        var deviceInfo = deviceInfos[i];
        if (deviceInfo.kind === 'videoinput') {
            if(!device1){
                device1 = deviceInfo;
                if(!deviceSelecionado){
                    deviceSelecionado = deviceInfo.deviceId;
                }
                continue;
            }
            if(!device2){
                device2 = deviceInfo;
                break;
            }
        }
    }
}

// Get a video stream from the currently selected camera source.
function getStream() {
    if (mediaStream) {
        mediaStream.getTracks().forEach(track => {
            track.stop();
    });
    }
    var videoSource = deviceSelecionado;
    constraints = {
        video: {deviceId: videoSource ? {exact: videoSource} : undefined}
    };
    console.log(constraints);
    navigator.mediaDevices.getUserMedia(constraints)
        .then(gotStream)
        .catch(error => {
        console.log('getUserMedia error: ', error);
        });
}

// Display the stream from the currently selected camera source, and then
// create an ImageCapture object, using the video from the stream.
function gotStream(stream) {
    console.log('getUserMedia() got stream: ', stream);
    mediaStream = stream;
    video.srcObject = stream;
    video.classList.remove('hidden');
    imageCptur = new ImageCapture(stream.getVideoTracks()[0]);
}

function limparFotos(){
    $('#fotofacial1').attr('src', null);
    $('#fotofacial2').attr('src', null);
    $('.limparfotos').hide();
    control = 1;

}
var control = 3;

// Get a Blob from the currently selected camera source and
// display this with an img element.
function takePhoto() {
    imageCptur.takePhoto().then(function(blob) {
        $('.limparfotos').show();
        if(control === 1){
            img.src = URL.createObjectURL(blob);
            control = 2;
        }else{
            img2.src = URL.createObjectURL(blob);
            control = 3;
            setTimeout(function () {
                enviarFotoFacial();
            }, 1000);
        }


    }).catch(function(error) {
        console.log('takePhoto() error: ', error);
    });
}

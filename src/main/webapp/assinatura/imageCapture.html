<!DOCTYPE html>

<!--
Copyright 2017 Google Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<html lang="en">
<head>
    <script async src="https://www.google-analytics.com/analytics.js"></script>

    <meta charset="utf-8">
    <meta name="description" content="Simplest possible examples of HTML, CSS and JavaScript.">
    <meta name="author" content="//samdutton.com">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta itemprop="name" content="simpl.info: simplest possible examples of HTML, CSS and JavaScript">
    <meta itemprop="image" content="/images/icons/icon192.png">
    <meta id="theme-color" name="theme-color" content="#fff">

    <link rel="icon" href="/images/icons/icon192.png">

    <base target="_blank">


    <title>Image Capture</title>

    <link rel="canonical" href="https://simpl.info/mediarecorder" />

    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="css/main.css">

</head>

<body>

<div id="container">

    <h1><a href="../index.html" title="simpl.info home page">simpl.info</a> Image&nbsp;Capture</h1>

    <p>This demo requires Chrome 52 or later with <strong>Experimental Web Platform features</strong> enabled: chrome://flags/#enable-experimental-web-platform-features. This is also enabled by default in Chrome 59 or later.</p>

    <p>For more information see the Image Capture API <a href="https://w3c.github.io/mediacapture-image/index.html" title="W3C Image Capture API Editor's Draft">Editor's&nbsp;Draft</a>.</p>

    <div>
        <button id="grabFrame">Grab Frame</button>
        <button id="takePhoto">Take Photo</button>
        <div class="select">
            <label for="videoSource">Video source: </label><select id="videoSource"></select>
        </div>
        <input class="hidden" id="zoom" type="range" step="20">
    </div>

    <video autoplay class="hidden"></video>
    <img>
    <canvas class="hidden"></canvas>

    <a href="https://github.com/samdutton/simpl/blob/gh-pages/imagecapture" title="View source for this page on GitHub" id="viewSource">View source on GitHub</a>

</div>

<script src="imageCapture3.js"></script>

</body>
</html>

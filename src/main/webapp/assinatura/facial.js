function iniciarCaptura(){
    navigator.mediaDevices.enumerateDevices()
        .then(gotDevices)
        .catch(error => {
            console.log('enumerateDevices() error: ', error);
        }).then(getStream);
}
function sairCapturaFacial(){
    adicionarUmaFotoRecFacial();
}

function trocarCamera() {
    if(deviceSelecionado === device1.deviceId && device2){
        deviceSelecionado = device2.deviceId;
    }else{
        deviceSelecionado = device1.deviceId;
    }
    getStream();
}
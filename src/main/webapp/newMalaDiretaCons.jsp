<%@ page contentType="text/html;charset=UTF-8" pageEncoding="ISO-8859-1" language="java" %>
<%@ include file="includes/imports.jsp" %>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
<style>
</style>


<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
    <a4j:form id="formM" style="border: 0; margin: 0">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <h:commandLink style="display: none" id="idLiberarBackingBeanMemoria"
                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                       action="#{MalaDiretaControle.liberarBackingBeanMemoria}"/>

            <h:panelGroup layout="block" rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}">
                    <h:panelGrid style="width: 100%" columnClasses="classEsquerda, classDireita" columns="2">
                        <h:outputText styleClass="tituloCampos" value="Nome da Meta:"/>
                        <h:panelGroup id="panelTituloCRMExtra">
                            <h:inputText id="textTituloCRMExtra" size="50" maxlength="50"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{MalaDiretaControle.malaDiretaVO.titulo}"/>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGrid columnClasses="classEsquerda, classDireita" columns="2"
                                 style="width: 100%">
                        <h:outputText styleClass="text" value="Período para realizar a Meta:"/>
                        <h:panelGroup id="periodoExecucaoCRMExtra">
                            <rich:calendar value="#{MalaDiretaControle.malaDiretaVO.dataEnvio}"
                                           datePattern="dd/MM/yyyy"
                                           inputSize="8"
                                           oninputchange="return validar_Data(this.id);"
                                           enableManualInput="true" zindex="2" showWeeksBar="false"/>

                            <h:outputText styleClass="text"
                                          value="até"/>

                            <rich:calendar value="#{MalaDiretaControle.malaDiretaVO.vigenteAte}"
                                           datePattern="dd/MM/yyyy"
                                           inputSize="8"
                                           oninputchange="return validar_Data(this.id);"
                                           enableManualInput="true" zindex="2" showWeeksBar="false"/>
                            <a4j:commandButton id="limparPeriodoCRMExtra"
                                               onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               action="#{MalaDiretaControle.limparPeriodoExecucaoForm}"
                                               image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                               reRender="periodoExecucaoCRMExtra"/>
                        </h:panelGroup>
                    </h:panelGrid>
            </h:panelGroup>

        <h:panelGrid id="panelGridMensagens" columns="1" width="100%" styleClass="tabMensagens"
                     rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}">
            <h:panelGroup styleClass="centralizado" layout="block" style="width: 100%; margin-top: 20px;">
                <h:outputText styleClass="textoConfiguracoes" style="font-size: 14px"
                              value="#{MalaDiretaControle.saldoApresentar}"/>
                <a4j:commandLink id="btnAtualizar" reRender="panelGridMensagens"
                                 action="#{MalaDiretaControle.consultarSaldo}" style="color: #0090FF;">
                    <i class="fa-icon-refresh"></i>
                </a4j:commandLink>

                <a4j:commandLink oncomplete="Richfaces.showModalPanel('modalComprarSMS')" action="#{AdquiraSMSControle.notificarRecurso}" rendered="#{MalaDiretaControle.apresentarOpcoesSMS}" style="color: #0090FF;">

                    Clique aqui para adquirir.
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" style="margin-top: 6px">
            <a4j:commandButton id="novo"
                               action="#{MalaDiretaControle.novo}"
                               onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                               value="Criar Novo"
                               styleClass="botoes nvoBt"
                               title="#{msg.msg_novo_dados}" accesskey="1"
                               reRender="mailingPanel"/>
        </h:panelGrid>

        <h:panelGrid columns="1" style="margin-left: 60px; margin-top: 15px" rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}">

            <h:panelGrid columns="2" columnClasses="colunaEsquerda" border="0" id="filtrosMailing">

                <h:outputText rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}" styleClass="tituloCampos" style="margin-top: 6px" value="Empresa:"/>
                <h:selectOneMenu rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}"
                                 styleClass="form" id="empresa" value="#{MalaDiretaControle.filtroEmpresa}">
                    <f:selectItems value="#{MalaDiretaControle.listaEmpresas}"/>
                </h:selectOneMenu>

                <h:outputText styleClass="tituloCampos" value="Meio de Envio:"/>
                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" id="meioDeEnvio"
                                 value="#{MalaDiretaControle.consultarMeioEnvio}">
                    <f:selectItems value="#{MalaDiretaControle.listaSelectItemMeioDeEnvio}"/>
                </h:selectOneMenu>
                
                <h:outputText  styleClass="tituloCampos"  value="Código:"/>
                <h:inputText  id="campoCodigo" styleClass="form" size="10" value="#{MalaDiretaControle.codigoMailing}"/>

                <h:outputText styleClass="tituloCampos" style="margin-top: 6px" value="Título:"/>
                <h:inputText id="campoTitulo" styleClass="form" size="40"  value="#{MalaDiretaControle.consultarDescricao}"/>

                <h:outputText styleClass="tituloCampos"  value="Remetente:"/>
                <h:inputText styleClass="form" size="40" value="#{MalaDiretaControle.consultarRemetente}"/>

                <h:outputText styleClass="tituloCampos" value="Tipo:" />
                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 value="#{MalaDiretaControle.codigoTipoAgendamento}"
                                 style="width: 140px;margin-left: 2px;">
                    <f:selectItems value="#{MalaDiretaControle.tiposAgendamento}" />
                    <a4j:support event="onchange" reRender="filtrosMailing"/>
                </h:selectOneMenu>

                <h:outputText styleClass="tituloCampos" value="Vigência:"  rendered="#{MalaDiretaControle.agendamentoOuTodos}"/>
                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 style="width: 140px; margin-left: 2px;"
                                 rendered="#{MalaDiretaControle.agendamentoOuTodos}"
                                 value="#{MalaDiretaControle.tipoVigencia}">
                    <f:selectItems value="#{MalaDiretaControle.tiposVigencia}" />
                </h:selectOneMenu>

                <h:outputText styleClass="tituloCampos" value="Período de Criação:" />
                <h:panelGroup id="periodoCriacao">
                    <rich:calendar value="#{MalaDiretaControle.dataInicialCriacao}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:outputText styleClass="tituloCampos" value="até"/>
                    <rich:calendar value="#{MalaDiretaControle.dataFinalCriacao}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <a4j:commandButton id="limparPeriodoCriacao" action="#{MalaDiretaControle.limparPeriodoCriacao}"
                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                       image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                       reRender="periodoCriacao"/>
                </h:panelGroup>
                
                <h:outputText styleClass="tituloCampos" value="Período de Execução:" rendered="#{MalaDiretaControle.consultaAgendamentos}"/>
                <h:outputText styleClass="tituloCampos" value="Período de Envio:" rendered="#{MalaDiretaControle.consultaAvulsos}"/>
                <h:panelGroup id="periodoExecucao" rendered="#{MalaDiretaControle.consultaAgendamentos || MalaDiretaControle.consultaAvulsos}">
                    <rich:calendar value="#{MalaDiretaControle.dataInicial}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:outputText styleClass="tituloCampos" value="até"/>
                    <rich:calendar value="#{MalaDiretaControle.dataFinal}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <a4j:commandButton id="limparPeriodoExecucao" action="#{MalaDiretaControle.limparPeriodoExecucao}"
                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                       image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                       reRender="periodoExecucao"/>
                </h:panelGroup>
                
            </h:panelGrid>

        </h:panelGrid>

        <div style="margin-top: 20px; text-align: center;">
            <a4j:commandButton id="consultarAtivos" styleClass="botoes nvoBt btSec" value="Buscar agendados"
                               action="#{MalaDiretaControle.consultarInicial}"
                               onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                               title="#{msg.msg_consultar_dados}" accesskey="2"
                               reRender="itens, painelPaginacaoOUT, panelGridMensagens, paginacaoTela"/>
        </div>

        <h:panelGrid columns="3" style="margin-top: 20px; width: 100%;">
            <h:outputText styleClass="tituloCamposNegrito" id="nomeTabela" value="Lista de e-mails ativos"/>

            <h:panelGroup layout="block">
                <%--BOTÃO DE LOG--%>
                <a4j:commandButton action="#{MalaDiretaControle.realizarConsultaLog}" reRender="form"
                                   oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                   style="margin-left: 8px; float: right;"
                                   onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                   image="/imagens/botalVisualizarLog.png" alt="Visualizar LOG" title="Visualizar Log"
                                   styleClass="botoes"/>
                <%--BOTÃO EXCEL--%>
                <a4j:commandButton id="exportarExcelMailing"
                                   image="../imagens/btn_excel.png"
                                   style="margin-left: 8px; float: right;"
                                   actionListener="#{MalaDiretaControle.exportar}"
                                   onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                   value="Excel"
                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Mailing', 800,200);#{ExportadorListaControle.msgAlert}"
                                   accesskey="2" styleClass="botoes">
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="titulo=Título,remetenteApresentar=Remetente,modeloMensagemApresentar=Modelo de Mensagem,dataCriacao=Data de Criação,dataEnvio=Última Execução,nomeEmpresa=Empresa"/>
                    <f:attribute name="prefixo" value="Mailing"/>
                </a4j:commandButton>
                <%--BOTÃO PDF--%>
                <a4j:commandButton id="exportarPdfMailing"
                                   style="margin-left: 8px; float: right;"
                                   image="../imagens/imprimir.png"
                                   onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                   actionListener="#{MalaDiretaControle.exportar}"
                                   value="PDF"
                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Mailing', 800,200);#{ExportadorListaControle.msgAlert}"
                                   accesskey="2" styleClass="botoes">
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                 value="titulo=Título,remetenteApresentar=Remetente,modeloMensagemApresentar=Modelo de Mensagem,dataCriacao=Data de Criação,dataEnvio=Última Execução,nomeEmpresa=Empresa"/>
                    <f:attribute name="prefixo" value="Mailing"/>
                </a4j:commandButton>
            </h:panelGroup>
        </h:panelGrid>

        <a4j:region  ajaxListener="#{MalaDiretaControle.campoOrdenacao}" >
            <rich:dataTable reRender="paginador" id="itens" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                            columnClasses="colunaCentralizada" style="margin-top: 10px"
                            value="#{MalaDiretaControle.listaConsulta}" binding="#{MalaDiretaControle.htmlDataTable}"
                            rows="10" var="malaDireta">

                <rich:column  id="ml_codigo" sortBy="#{malaDireta.codigo}">
                    <f:facet name="header">
                        <h:outputText value="Código"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.editar}" id="codigo" value="#{malaDireta.codigo}"
                                     reRender="mailingPanel"/>
                </rich:column>

                <rich:column id="ml_titulo" sortBy="#{malaDireta.titulo}">
                    <f:facet name="header">
                        <h:outputText value="Título"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.editar}" id="titulo" value="#{malaDireta.titulo}"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                     reRender="mailingPanel"/>
                </rich:column>

                <rich:column id="u_nome" sortBy="#{malaDireta.remetenteApresentar}">
                    <f:facet name="header">
                        <h:outputText value="Remetente"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.editar}" id="remetente"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                     value="#{malaDireta.remetenteApresentar}" reRender="mailingPanel"/>
                </rich:column>

                <rich:column id="mm_titulo" sortBy="#{malaDireta.modeloMensagemApresentar}">
                    <f:facet name="header">
                        <h:outputText value="Modelo Mensagem"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.editar}" id="modeloMensagem"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                     value="#{malaDireta.modeloMensagemApresentar}" reRender="mailingPanel"/>
                </rich:column>

                <rich:column id="ml_datacriacao" sortBy="#{malaDireta.dataCriacao}">
                    <f:facet name="header">
                        <h:outputText value="Data de Criação"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.editar}" id="dataCriacao"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                     reRender="mailingPanel">
                        <h:outputText value="#{malaDireta.dataCriacao}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                        </h:outputText>
                    </a4j:commandLink>
                </rich:column>

                <rich:column id="ml_dataenvio" sortBy="#{malaDireta.dataEnvio}">
                    <f:facet name="header">
                        <h:outputText value="#{MalaDiretaControle.labelData}"/>

                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.editar}" id="envioData"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" reRender="mailingPanel">
                        <h:outputText id="dataEnvio" value="#{malaDireta.dataApresentar}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column id="ml_empresa" sortBy="#{malaDireta.nomeEmpresa}"
                            rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}">
                    <f:facet name="header">
                        <h:outputText value="Empresa"/>

                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.editar}" id="malaEmpresa"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')" reRender="mailingPanel">
                        <h:outputText id="nomeEmpresa" value="#{malaDireta.nomeEmpresa}"/>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
        </a4j:region>

        <h:panelGrid id="paginador" rendered="#{!empty MalaDiretaControle.listaConsulta}" columns="1" columnClasses="colunaCentralizada" width="100%">
            <h:panelGrid columns="3" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto">
                <h:panelGroup>
                    <a4j:commandLink rendered="#{MalaDiretaControle.controleConsulta.isTemAnterior}"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                     action="#{MalaDiretaControle.consultarMalaDireta}" value="<< " reRender="itens,paginador" style="position:relative;">
                        <f:setPropertyActionListener value="1" target="#{MalaDiretaControle.controleConsulta.paginaAtual}" />
                    </a4j:commandLink>
                    <a4j:commandLink rendered="#{MalaDiretaControle.controleConsulta.isTemAnterior}"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                     action="#{MalaDiretaControle.consultarMalaDireta}" value="< " reRender="itens,paginador" style="position:relative;">
                        <f:setPropertyActionListener value="#{MalaDiretaControle.controleConsulta.paginaAnterior}" target="#{MalaDiretaControle.controleConsulta.paginaAtual}" />
                    </a4j:commandLink>
                </h:panelGroup>
                <rich:dataGrid id="gridPaginas" style="border: 0 none;" cellpadding="0" cellspacing="0" border="0" elements="#{MalaDiretaControle.controleConsulta.numeroMaximoPagina}" columns="#{MalaDiretaControle.controleConsulta.numeroMaximoPagina}" value="#{MalaDiretaControle.controleConsulta.listaPaginas}" var="pagina" rowClasses="semBordasPaginador">
                    <h:panelGrid cellpadding="0" cellspacing="0" border="0" styleClass="#{MalaDiretaControle.controleConsulta.cssPaginaAtual}"  columns="1" columnClasses="colunaCentralizada">
                        <a4j:commandLink  action="#{MalaDiretaControle.consultarMalaDireta}" value="#{pagina}"
                                          onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                          reRender="itens,paginador">
                            <f:setPropertyActionListener value="#{pagina}" target="#{MalaDiretaControle.controleConsulta.paginaAtual}" />
                        </a4j:commandLink>
                    </h:panelGrid>
                </rich:dataGrid>
                <h:panelGroup>
                    <a4j:commandLink  rendered="#{MalaDiretaControle.controleConsulta.isTemProximo}"
                                      onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                      action="#{MalaDiretaControle.consultarMalaDireta}" value=" >" reRender="itens,paginador" style="position:relative;">
                        <f:setPropertyActionListener value="#{MalaDiretaControle.controleConsulta.proximaPagina}" target="#{MalaDiretaControle.controleConsulta.paginaAtual}" />
                    </a4j:commandLink>
                    <a4j:commandLink  rendered="#{MalaDiretaControle.controleConsulta.isTemProximo}"
                                      onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                      action="#{MalaDiretaControle.consultarMalaDireta}" value=" >>" reRender="itens,paginador" style="position:relative;">
                        <f:setPropertyActionListener value="#{MalaDiretaControle.controleConsulta.totalPaginas}" target="#{MalaDiretaControle.controleConsulta.paginaAtual}" />
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </a4j:form>
</h:panelGrid>

<script>
    function setarPermissaoFalso() {
        document.getElementById('formSenhaAutorizacao:idBtnpedirpermissaofalso').click();
    }
</script>

<rich:modalPanel id="panelAutorizacaoFuncionalidade" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450"
                 showWhenRendered="#{AutorizacaoFuncionalidadeControle.pedirPermissao}"
                 onshow="document.getElementById('formSenhaAutorizacao:senha').focus(); setarPermissaoFalso();"
                 onhide="document.getElementById('formSenhaAutorizacao:botaoFecharHide').click()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{AutorizacaoFuncionalidadeControle.titulo}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkAutorizacao"/>
            <rich:componentControl for="panelAutorizacaoFuncionalidade" attachTo="hidelinkAutorizacao"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form prependId="true" id="formSenhaAutorizacao">
        <a4j:commandLink action="#{AutorizacaoFuncionalidadeControle.pedirPermissaoFalso}"
                         status="false"
                         id="idBtnpedirpermissaofalso"
                         style="display: none;">
        </a4j:commandLink>
        <a4j:commandButton style="display : none;" status="false" id="botaoFecharHide"
                           reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                           action="#{AutorizacaoFuncionalidadeControle.fechar}"/>
        <h:panelGroup layout="block" styleClass="margin-box">
            <h:panelGrid id="panelConfimSenhaAutorizacao" width="75%" columns="2" cellpadding="5" styleClass="font-size-Em-max">
                <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                <h:outputText styleClass="rotuloCampos" value="CÓDIGO"/>
                <h:panelGroup>
                    <h:inputText id="idUsuarioAutorizacao" styleClass="inputTextClean" size="5" maxlength="7"
                                 style="margin-left:5px"
                                 value="#{AutorizacaoFuncionalidadeControle.usuario.codigo}">
                        <a4j:support event="onchange" focus="formSenhaAutorizacao:senha"
                                     action="#{AutorizacaoFuncionalidadeControle.consultarUsuario}"
                                     reRender="panelConfimSenhaAutorizacao, mensagemAutorizacaoFuncionalidade"/>
                    </h:inputText>
                    <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:5px;opacity:0;"
                                 value="#{AutorizacaoFuncionalidadeControle.usuario.username}"/>
                </h:panelGroup>
                <h:outputText styleClass="rotuloCampos" value="USÚARIO"/>
                <h:panelGroup>
                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" style="margin-left:5px"
                                  value="#{AutorizacaoFuncionalidadeControle.usuario.username}"/>
                </h:panelGroup>
                <h:outputText styleClass="rotuloCampos" value="SENHA"/>
                <h:panelGroup>
                    <h:inputSecret  autocomplete="off" id="senha" styleClass="inputTextClean" size="14" maxlength="64" style="margin-left:5px"
                                    value="#{AutorizacaoFuncionalidadeControle.usuario.senha}" onkeypress="validarEnter(event,'formSenhaAutorizacao:btnAutorizar');"/>
                </h:panelGroup>

            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="margin-box">
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza" value="#{AutorizacaoFuncionalidadeControle.mensagemUsuario}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink id="btnAutorizar" value="#{msg_bt.btn_gravar}"
                                 styleClass="botaoPrimario texto-size-16"
                                 reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                                 oncomplete="#{AutorizacaoFuncionalidadeControle.onComplete};#{AutorizacaoFuncionalidadeControle.mensagemNotificar}"
                                 title="#{msg.msg_gravar_dados}"
                                 action="#{AutorizacaoFuncionalidadeControle.invoke}"/>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup id="panelFinalizarAutorizacao"
                      rendered="#{AutorizacaoFuncionalidadeControle.finalizarAutorizacaoSemExibirModal}">
            <a4j:jsFunction name="finalizarAutorizacao"
                            action="#{AutorizacaoFuncionalidadeControle.finalizarAutorizacao}"
                            reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                            oncomplete="#{AutorizacaoFuncionalidadeControle.onComplete};#{AutorizacaoFuncionalidadeControle.mensagemNotificar};#{AdquiraSMSControle.msgAlert}"/>
            <script type="text/javascript" language="javascript">
                (function(){
                    finalizarAutorizacao();
                })()
            </script>
        </h:panelGroup>
        <h:panelGroup id="panelNotificacoes" rendered="#{not empty AutorizacaoFuncionalidadeControle.mensagemNotificar and AutorizacaoFuncionalidadeControle.pedirPermissao}">
            <a4j:jsFunction name="exibirNotificacao"
                            oncomplete="#{AutorizacaoFuncionalidadeControle.mensagemNotificar};"/>
            <script type="text/javascript" language="javascript">
                (function(){
                    exibirNotificacao();
                })()
            </script>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalComprarSMS" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="650">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Comprar SMS."/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkComprarSMS"/>
            <rich:componentControl for="modalComprarSMS" attachTo="hidelinkComprarSMS"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formSMS" ajaxSubmit="true" styleClass="novaModal">

        <a4j:commandLink id="calcularValorSMS" reRender="formSMS"
                         action="#{AdquiraSMSControle.calcularValorSMS}">
        </a4j:commandLink>

        <h:inputHidden id="valorCalcular" value="#{AdquiraSMSControle.limiteUnico}"/>

        <h:panelGroup layout="block" styleClass="margin-box">
            <h:panelGrid width="100%" columns="2" cellpadding="5"
                         styleClass="font-size-Em-max">
                    <h:selectOneRadio value="#{AdquiraSMSControle.tipoServico}"
                                      onclick="#{AdquiraSMSControle.calcularValorSMS}"
                                      style="font-family: Arial; !important; font-size: 1.1em !important;  color:#777777; !important;" layout="pageDirection">
                        <f:selectItem itemValue="1" itemLabel="Comprar serviço anual de envio de 1000 SMS/DIA: R$ 3.574,00"/>
                        <f:selectItem itemValue="2" itemLabel="Comprar serviço anual de envio de 500 SMS/DIA: R$ 1.787,00"/>
                        <f:selectItem itemValue="3" itemLabel="Comprar SMS Avulso: R$ 0,12"/>
                        <a4j:support event="onclick" reRender="formSMS"></a4j:support>
                    </h:selectOneRadio>

            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="margin-box" rendered="#{AdquiraSMSControle.tipoServico eq 3}"
                          id="panelLimiteUnico" style="margin-top: -10px; margin-bottom: -25px;">
                <h:panelGrid width="100%" columns="2" cellpadding="5"
                             styleClass="font-size-Em-max">
                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                  value="Digite quantos SMS deseja (mínimo de 5000):"/>

                    <h:panelGroup>
                        <input type="text" class="inputTextClean"
                               id="limiteUnico"
                               onchange="preencherValorChamarBotao('formSMS:calcularValorSMS', 'formSMS:valorCalcular', this.value)"
                               style="margin-left:1px; margin-top: 3px;text-align: right;width: 130px;"
                               value="${AdquiraSMSControle.limiteUnico}"/>

                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>


            <h:panelGroup style="margin-top: 10px !important;" styleClass="margin-box">
                <h:panelGrid width="100%" columns="2" cellpadding="5">
                    <h:outputText styleClass="rotuloCampos" style="font-size: 18px; !important;" value="TOTAL:"/>

                    <h:panelGroup>
                        <h:outputText styleClass="rotuloCampos" id="valorSMS"
                                      style="margin-left: 320px; font-size: 18px; !important;"
                                      value=" #{AdquiraSMSControle.valorSMSApresentar}"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>

            <h:panelGroup styleClass="container-botoes">

                <h:panelGroup layout="block" >
                    <a4j:commandLink action="#{AdquiraSMSControle.realizarConsultaLogObjetoSelecionado}"
                                     oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                     title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                     style="display: inline-block; margin-top: -11px; padding: 10px 16px; border-radius: 6px;">
                        <i class="fa-icon-list"></i>
                    </a4j:commandLink>
                </h:panelGroup>

                <a4j:commandLink id="comprar" value="Comprar"
                                 styleClass="botaoPrimario texto-size-18"
                                 reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                                 oncomplete="#{AdquiraSMSControle.onCompleteGravar};#{AdquiraSMSControle.mensagemNotificar}"
                                 action="#{AdquiraSMSControle.comprarSMS}">
                </a4j:commandLink>

            </h:panelGroup>
        </h:panelGroup>
        <script>
            function preencherValorChamarBotao(idBotao, idHidden, valorHidden) {
                var hidden = document.getElementById(idHidden);
                var botao = document.getElementById(idBotao);
                hidden.value = valorHidden;
                botao.click();
                event.stopPropagation();
            }
        </script>

    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalSMSVendido" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="650" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Conclusão negociação compra de SMS."/>
        </h:panelGroup>
    </f:facet>
    <a4j:form ajaxSubmit="true">
        <h:panelGrid>
            <h:panelGroup layout="block" style="margin: 10 20 10 20;">
                <h:panelGrid id="ativacao" style="margin-top: 2vh; font-size: 14px !important;" columns="1"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza"
                                  value="Em breve nosso financeiro irá entrar em contato para concretizar a compra. Enquanto isso você já pode desfrutar do envio de SMS."/>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="container-botoes"
                          style="position: absolute; width: 100%; bottom: 6%; text-align: center">
                    <a4j:commandLink id="btnAtualizar" styleClass="botaoPrimario texto-size-16"
                                     value="Ok"
                                     reRender="formM,panelGridMensagens"
                                     action="#{MalaDiretaControle.consultarSaldo}"
                                     oncomplete="Richfaces.hideModalPanel('modalSMSVendido')" style="color: #0090FF;">
                    </a4j:commandLink>
                </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<script type="text/javascript">
    document.getElementById("formM:campoTitulo").focus();
</script>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title><h:outputText
        value="#{msg_aplic.prt_Agendados_tituloForm}" /></title>


    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Agendados_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-confirmar-o-comparecimento-de-um-cliente-agendado-pelo-crm/"/>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_crm.jsp"/>
        </f:facet>
    </h:panelGroup>

    <h:form id="form">
        <h:commandLink action="#{AberturaMetaControle.liberarBackingBeanMemoria}"
                       id="idLiberarBackingBeanMemoria" style="display: none" />

          <h:panelGrid columns="2" columnClasses="colunaEsquerda" styleClass="tabForm" width="100%">
          <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_HistoricoIndicado_consProf}" styleClass="tituloCampos" />
                <rich:spacer width="10px"/>
                <h:outputText value="#{AgendaControle.inicializarNomeConsultorProfessor}" styleClass="tituloCampos" />
            </h:panelGroup>
            <h:panelGroup id="totalizadores" style="float: right; margin-right: 10px;" >
	                <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Agendados na lista" value="#{msg_aplic.prt_AberturaMeta_totalizadorClientePotencial}"/>
	                   <rich:spacer width="10px"/>
	                <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total de Agendados na lista" value="#{AgendaControle.agendaVO.totalizadorAgendadosComparecimento}"/>
	                   <rich:spacer width="20px"/>
	                <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total Comparecidos" value="#{msg_aplic.prt_AberturaMeta_totalComparecidos}"/>
	                   <rich:spacer width="10px"/>
	                <h:outputText styleClass="tituloCamposDestaqueNegrito" title="Total Comparecidos" value="#{AgendaControle.agendaVO.totalComparecidos}"/>
			</h:panelGroup>
        </h:panelGrid>



        <h:panelGrid id="filtroPesquisa" columns="2" styleClass="tabForm" width="100%">
            <h:panelGrid rendered="#{AgendaControle.apresentarFiltroPesquisaHistorico}" columns="9" footerClass="colunaCentralizada" width="100%">
                <h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agendados_nome}"/>
                </h:panelGroup>
                <h:panelGroup>
                    <h:inputText value="#{AgendaControle.valorConsultarNomeComparecimento}" size="50" styleClass="camposAgendado" />
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agendados_periodo}"/>
                </h:panelGroup>
                <h:panelGroup>

                    <rich:calendar id="dataInicio"
                                   value="#{AgendaControle.dataInicio}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:panelGroup>

                    <h:outputText styleClass="tituloCampos"   value="#{msg_aplic.prt_AberturaMeta_ate}" />
                </h:panelGroup>

                <h:panelGroup>

                    <rich:calendar id="dataTermino"
                                   value="#{AgendaControle.dataTermino}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="true" />
                    <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:panelGroup>

                    <h:commandButton  action="#{AgendaControle.consultarAgendadosHistoricoConfirmacaoComparecimentoHistorico}" image="./imagensCRM/botaoPesquisar.png"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

		<h:panelGrid id="panelGridMensagens" columns="1" width="100%">
             <h:outputText styleClass="mensagem"  value="#{AberturaMetaControle.mensagem}"/>
             <h:outputText styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}"/>
        </h:panelGrid>

        <h:panelGrid columns="1" styleClass="tabForm" width="100%">
            <rich:tabPanel id="panelAbas" width="100%" activeTabClass="true"  switchType="Client">
                <rich:tab id="confirmacaoAgendadosHoje" label="Hoje" reRender="form:filtroPesquisa" action="#{AgendaControle.inicializarAgendadosComparecidosHoje}">
                    <h:panelGrid columns="1" styleClass="tabForm" width="100%">

                        <rich:dataTable id="itemsAgendados" width="100%" headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno" columnClasses="colunaEsquerda"
                                     value="#{AgendaControle.agendaVO.listaConfirmacaoComparecimentoAgendamentoHoje}" var="agendados">

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_matricula}"/>
                                </f:facet>
                                <h:outputText id="matricula"  value="#{agendados.cliente.matricula}" />
                            </rich:column>
							<rich:column sortBy="#{agendados.cliente.pessoa.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_nomePessoa}"/>
                                </f:facet>
                                <h:outputText  id="nomePassivo" value="#{agendados.passivo.nome}" rendered="#{agendados.validarPassivoAgendadoApresentar}"/>
                                <h:outputText  id="nomeIndicado" value="#{agendados.indicado.nomeIndicado}" rendered="#{agendados.validarIndicadoAgendadoApresentar}"/>
                                <h:outputText  id="nomeCliente" value="#{agendados.cliente.pessoa.nome}" rendered="#{agendados.validarClienteAgendadoApresentar}"/>
                            </rich:column>
                            <rich:column sortBy="#{agendados.tipoAgendamento_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_tipoAgendamento}"/>
                                </f:facet>
                                <h:outputText  id="tipoAgendamento" value="#{agendados.tipoAgendamento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_modalidade}"/>
                                </f:facet>
                                <h:outputText  id="modalidadeAgendamento" value="#{agendados.modalidade.nome}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataLancamento}"/>
                                </f:facet>
                                <h:outputText id="dataLancamento"  value="#{agendados.dataLancamento_Apresentar}"/>
                            </rich:column>
                            <rich:column sortBy="#{agendados.dataAgendamentoComparecimento_Apresentar}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataAgendamento}"/>
                                </f:facet>
                                <h:outputText id="dataAgendamento"  value="#{agendados.dataAgendamentoComparecimento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataComparecimento}"/>
                                </f:facet>
                                <h:outputText id="dataComparecimento"  value="#{agendados.dataComparecimento_Apresentar}"/>
                            </rich:column>
                            <rich:column sortBy="#{agendados.responsavelCadastro.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_responsavelcadastro}"/>
                                </f:facet>
                                <h:outputText  id="responsavelCadastro" value="#{agendados.responsavelCadastro.nomeAbreviado}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_responsavelComparecimento}"/>
                                </f:facet>
                                <h:outputText id="responsavelComparecimento"  value="#{agendados.responsavelComparecimento.nome}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_opcoes}"/>
                                </f:facet>
                                <a4j:commandButton id="btnMarcarOpcao" image="./imagensCRM/diskMenor.png" rendered="#{agendados.apresentarBotaoGravarAgendadosComparecimento}" reRender="form:itemsAgendados, form:totalizadores" action="#{AgendaControle.gravarAgendadosComparecidos}" oncomplete="#{AgendaControle.msgAlert}" title="Confirmar Comparecimento" ajaxSingle="true"  />
                                <a4j:commandButton id="btnDesmarcarOpcao" image="./imagensCRM/cancelarAgendados.png" rendered="#{!agendados.apresentarBotaoGravarAgendadosComparecimento}" reRender="form:itemsAgendados, form:totalizadores" action="#{AgendaControle.cancelarAgendadosComparecidos}" title="Cancelar Comparecimento" ajaxSingle="true" />
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>
                </rich:tab>
                <rich:tab id="confirmacaoAgendadosMes" label="Mês" reRender="form:filtroPesquisa" action="#{AgendaControle.consultarAgendadosMesConfirmacaoComparecimentoMes}" >
                    <h:panelGrid columns="1" styleClass="tabForm" width="100%">

                        <rich:dataTable id="itemsAgendadosMes" width="100%" headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno" columnClasses="colunaEsquerda"
                                     value="#{AgendaControle.agendaVO.listaConfirmacaoComparecimentoAgendamentoMes}" var="agendados">

							<rich:column>
                                <f:facet name="header">
           	                        <h:outputText  value="#{msg_aplic.prt_Agendados_opcoes}"/>
                                </f:facet>
                                <a4j:commandButton  image="./imagensCRM/diskMenor.png" rendered="#{agendados.apresentarBotaoGravarAgendadosComparecimento}" reRender="form:itemsAgendadosMes, form:totalizadores" action="#{AgendaControle.gravarAgendadosComparecidos}" title="Confirmar Comparecimento" ajaxSingle="true"  />
                                <a4j:commandButton  image="./imagensCRM/cancelarAgendados.png" rendered="#{!agendados.apresentarBotaoGravarAgendadosComparecimento}" reRender="form:itemsAgendadosMes, form:totalizadores" action="#{AgendaControle.cancelarAgendadosComparecidos}" title="Cancelar Comparecimento" ajaxSingle="true" />
                            </rich:column>
							<rich:column sortBy="#{agendados.passivo.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_nomePessoa}"/>
                                </f:facet>
                                <h:outputText  id="nomePassivoMes" value="#{agendados.passivo.nome}" rendered="#{agendados.validarPassivoAgendadoApresentar}"/>
                                <h:outputText  id="nomeIndicadoMes" value="#{agendados.indicado.nomeIndicado}" rendered="#{agendados.validarIndicadoAgendadoApresentar}"/>
                                <h:outputText  id="nomeClienteMes" value="#{agendados.cliente.pessoa.nome}" rendered="#{agendados.validarClienteAgendadoApresentar}"/>
                            </rich:column>
							<rich:column sortBy="#{agendados.passivo.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_tipoAgendamento}"/>
                                </f:facet>
                                <h:outputText  id="tipoAgendamentoMes" value="#{agendados.tipoAgendamento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataLancamento}"/>
                                </f:facet>
                                <h:outputText id="dataLancamentoMes"  value="#{agendados.dataLancamento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataAgendamento}"/>
                                </f:facet>
                                <h:outputText id="dataAgendamentoMes"  value="#{agendados.dataAgendamentoComparecimento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataComparecimento}"/>
                                </f:facet>
                                <h:outputText id="dataComparecimentoMes"  value="#{agendados.dataComparecimento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_responsavelComparecimento}"/>
                                </f:facet>
                                <h:outputText id="responsavelComparecimentoMes"  value="#{agendados.responsavelComparecimento.nome}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_matricula}"/>
                                </f:facet>
                                <h:outputText id="matriculaMes"  value="#{agendados.cliente.matricula}" />
                            </rich:column>

                        </rich:dataTable>
                    </h:panelGrid>
                </rich:tab>
                <rich:tab id="confirmacaoAgendadosHistorico" label="Histórico" reRender="form:filtroPesquisa" action="#{AgendaControle.inicializarDadosFiltroPesquisa}" >
                    <h:panelGrid columns="1" styleClass="tabForm" width="100%">

                        <rich:dataTable id="itemsAgendadosHistorico" width="100%" headerClass="consulta" rowClasses="linhaImparPequeno, linhaParPequeno" columnClasses="colunaEsquerda"
                                     value="#{AgendaControle.agendaVO.listaConfirmacaoComparecimentoAgendamentoHistorico}" var="agendados">

							<rich:column>
                                <f:facet name="header">
           	                        <h:outputText  value="#{msg_aplic.prt_Agendados_opcoes}"/>
                                </f:facet>
                                <a4j:commandButton  image="./imagensCRM/diskMenor.png" rendered="#{agendados.apresentarBotaoGravarAgendadosComparecimento}" reRender="form:itemsAgendadosHistorico, form:totalizadores" action="#{AgendaControle.gravarAgendadosComparecidos}" title="Confirmar Comparecimento" ajaxSingle="true"  />
                                <a4j:commandButton  image="./imagensCRM/cancelarAgendados.png" rendered="#{!agendados.apresentarBotaoGravarAgendadosComparecimento}" reRender="form:itemsAgendadosHistorico, form:totalizadores" action="#{AgendaControle.cancelarAgendadosComparecidos}" title="Cancelar Comparecimento" ajaxSingle="true" />
                            </rich:column>
							<rich:column sortBy="#{agendados.passivo.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_nomePessoa}"/>
                                </f:facet>
                                <h:outputText  id="nomePassivoHistorico" value="#{agendados.passivo.nome}" rendered="#{agendados.validarPassivoAgendadoApresentar}"/>
                                <h:outputText  id="nomeIndicadoHistorico" value="#{agendados.indicado.nomeIndicado}" rendered="#{agendados.validarIndicadoAgendadoApresentar}"/>
                                <h:outputText  id="nomeClienteHistorico" value="#{agendados.cliente.pessoa.nome}" rendered="#{agendados.validarClienteAgendadoApresentar}"/>
                            </rich:column>
							<rich:column sortBy="#{agendados.passivo.nome}">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_tipoAgendamento}"/>
                                </f:facet>
                                <h:outputText  id="tipoAgendamentoHistorico" value="#{agendados.tipoAgendamento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataLancamento}"/>
                                </f:facet>
                                <h:outputText id="dataLancamentoHistorico"  value="#{agendados.dataLancamento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataAgendamento}"/>
                                </f:facet>
                                <h:outputText id="dataAgendamentoHistorico"  value="#{agendados.dataAgendamentoComparecimento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_dataComparecimento}"/>
                                </f:facet>
                                <h:outputText id="dataComparecimentoHistorico"  value="#{agendados.dataComparecimento_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_responsavelComparecimento}"/>
                                </f:facet>
                                <h:outputText id="responsavelComparecimentoHistorico"  value="#{agendados.responsavelComparecimento.nome}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_Agendados_matricula}"/>
                                </f:facet>
                                <h:outputText id="matriculaHistorico"  value="#{agendados.cliente.matricula}" />
                            </rich:column>

                        </rich:dataTable>
                    </h:panelGrid>
                </rich:tab>
            </rich:tabPanel>
        </h:panelGrid>
        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />

    </h:form>
</f:view>
<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
<script type="text/javascript" language="javascript" src="script/script.js"></script>
<%@include file="/includes/include_import_minifiles.jsp" %>
<script type="text/javascript">
    setDocumentCookie('popupsImportante', 'close',1);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Venda Avulsa"/>
    </title>
    <h:form id="form" >

        <html>
            <head>
                <link rel="shortcut icon" href="./favicon.ico">
                <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
                <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
                <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
                <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
                <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
            </head>
            <jsp:include page="include_head.jsp" flush="true" />
            <body>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <jsp:include page="include_venda_avulsa_form.jsp" flush="true"/>
                                <a4j:commandButton style="visibility: hidden; height: 0px;" reRender="panelExpiracaoSenha"
                                                   id="btnAtualizaPagina">
                                </a4j:commandButton>
                            </h:panelGroup>
                            <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="include_rodape_flat.jsp" flush="true"/>
            </h:panelGroup>
            </body>
        </html>
    </h:form>
    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp" flush="true"/>
    <jsp:include page="include_modais_venda_avulsa.jsp" flush="true"/>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    window.onload = function() {
        // document.getElementById("form:nomeComprador").focus();
        document.getElementById("form:itemVendaAvulsa_produto").focus();
    }

    function fecharSuggestion() {
        try {
            document.getElementById('form:suggestionNomeComprador').style.display = 'none';
        } catch (e) {
            console.log(e);
        }
    }

    function bloquearTeclaEnter(event) {
        if (event.keyCode === 13) {
            event.keyCode = 0;
            event.returnValue = false;
        }
    }

    function enterAdicionar(event) {
        if (event.keyCode === 13) {
            adicionarProdutoAutomatico();
            event.keyCode = 0;
            event.returnValue = false;
        }
    }

    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }

    function focusQuantidade() {
        console.log('focusQuantidade');
        document.getElementById('form:itemVendaAvulsa_quantidade').select();
    }

    function focusPesquisarProduto() {
        console.log('focusPesquisarProduto');
        document.getElementById('form:itemVendaAvulsa_produto').focus();
        document.getElementById('form:itemVendaAvulsa_produto').select();
    }

    function adicionarProdutoAutomatico() {
        document.getElementById('form:botaoAdicionar').click();
    }

    function cadastrarClienteVendaAvulsa() {
        document.getElementById('form:cadastrarClienteVendaAvulsa').click();
    }

    function atualizarProdutoTela() {
        document.getElementById('form:atualizarProdutoTela').click();
    }
</script>

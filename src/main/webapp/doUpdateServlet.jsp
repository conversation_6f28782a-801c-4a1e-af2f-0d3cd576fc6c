<%-- 
    Document   : doUpdateServlet
    Created on : 14/08/2012, 16:30:57
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="negocio.comuns.utilitarias.Uteis" %>
<%@ page import="org.json.*" %>
<%@ page import="servicos.integracao.OamdWSConsumer" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <%
        if (request.getParameter("lgn") != null) {
            JSONObject o = new JSONObject(Uteis.desencriptar(request.getParameter("lgn"), 
                "chave_login_unificado"));
            if (o.getString("userName") != null && !o.getString("userName").isEmpty()) {
                final String retorno = OamdWSConsumer.validarUsuarioOamd(o.getString("userName"), 
                    o.getString("pwd"));
                if (retorno.equals("sucesso")) {
                    request.setAttribute("valido", true);
                    request.setAttribute("lgn", request.getParameter("lgn"));
                }
            }
        }
    %>

    <html>
        <head>
            <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
            <title>SQL Service</title>
            <style type="text/css">
                .text{
                    margin-left: 5px;
                    font-size:11px;
                    font-family:Arial;
                }
            </style>
        </head>
        <body style="">
            <c:if test="${valido}">
                <form action="UpdateServlet" method="POST">
                    <h:panelGrid columns="2" columnClasses="text,text">
                        <h:outputText value="Operation"/>
                        <h:selectOneMenu id="op">
                            <f:selectItem itemValue="(SELECIONE)"/>
                            <f:selectItem itemValue="selectFULL_XML"/>
                            <f:selectItem itemValue="createNOTF"/>
                            <f:selectItem itemValue="listNOTF"/>
                            <f:selectItem itemValue="clearNOTF"/>
                            <f:selectItem itemValue="processONE"/>
                            <f:selectItem itemValue="processALL"/>
                            <f:selectItem itemValue="exportData"/>
                        </h:selectOneMenu>

                        <h:outputText value="DatabaseHost"/>
                        <h:inputText id="hostPG"  value="localhost"/>

                        <h:outputText value="DatabasePort"/>
                        <h:inputText id="portaPG" value="5432"/>

                        <h:outputText value="DatabaseUser"/>
                        <h:inputText id="userPG" value="postgres"/>

                        <h:outputText value="DatabasePassword"/>
                        <h:inputText id="pwdPG" value="pactodb"/>

                        <h:outputText value="Format(html/json/excel)"/>
                        <h:inputText id="format" value="html"/>

                        <h:outputText value="Email receber Export Data"/>
                        <h:inputText id="email" value="<EMAIL>"/>

                        <h:outputText value="ShowTitle(s/n)"/>
                        <h:inputText id="title" value="s"/>

                        <h:outputText value="Except"/>
                        <h:inputText id="except" value="bd1|bd4|bd15"/>

                        <h:outputText value="Prefixo Banco"/>
                        <h:inputText id="prefixoBanco" value=""/>

                        <h:outputText value="Only Keys"/>
                        <h:inputText id="chavesOnly" value=""/>

                        <h:outputText value="Database"/>
                        <h:inputText id="bd" value=""/>

                        <h:outputText value="Intervalo Cache"/>
                        <h:inputText id="intervalo" value=""/>

                        <h:outputText value="Tables"/>
                        <h:inputText id="tables" value="*"/>

                        <h:outputText value="New Owner"/>
                        <h:inputText id="newOwner" value="zillyonweb"/>

                        <h:outputText value="Notification Description"/>
                        <h:inputTextarea id="desc" cols="80" rows="4" value="Uma manutenção preventiva programada irá acontecer no dia 31/12/2012 de 22:00. Com previsão de Retorno até as 05:00 do dia 01/01/2013. "/>

                        <h:outputText value="Notification Type"/>
                        <h:selectOneMenu id="tipoNOTF">
                            <f:selectItem itemValue="ZW"/>
                            <f:selectItem itemValue="WS"/>
                        </h:selectOneMenu>

                        <h:outputText value="NotifStart(yyyymmddHHMMSS)"/>
                        <h:inputText id="dti" value=""/>

                        <h:outputText value="NotifEnd(yyyymmddHHMMSS)"/>
                        <h:inputText id="dtf" value=""/>

                        <h:outputText value="Propagable"/>
                        <h:inputText id="propagable" value="s"/>

                        <h:outputText value="Key"/>
                        <h:inputText id="chave" value=""/>

                        <h:outputText value="LocalAcesso"/>
                        <h:inputText id="localAcesso" value=""/>

                        <h:outputText value="Método"/>
                        <h:inputText size="120" id="method" value="br.com.pactosolucoes.atualizadb.processo.SetarPagamentoProduto.metodoExemplo" />

                        <h:outputText value="SQL"/>
                        <h:inputTextarea id="sql" cols="150" rows="10" value="select * from pg_stat_activity"/>

                    </h:panelGrid>

                    <h:inputHidden id="lgn" value="#{lgn}"></h:inputHidden>

                    <input value="Process!" type="submit"/>

                </form>
            </c:if>
        </body>
    </html>
</f:view>

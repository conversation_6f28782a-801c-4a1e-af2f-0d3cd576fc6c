<%-- 
    Document   : lancarBrindeClienteForm
    Created on : 19/10/2017, 11:14:06
    Author     : arthur
--%>

<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="script/vanilla-masker.min.js"></script>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript">
        jQuery.noConflict();
    </script>
</head>
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<head>
    <script>
        function toggleBtnProcess(disable, component, label) {
            var botao = document.getElementById(component);
            botao.setAttribute("disabled", disable);
            if (disable) {
                botao.setAttribute("value", "Aguarde...");
                botao.setAttribute("disabled", disable);
            } else {
                botao.setAttribute("value", label);
                botao.removeAttribute("disabled");
            }
        }
        function carregarTooltipster(){
            carregarTooltip(jQuery('.tooltipster'));
        }
        function carregarTooltip(el) {
            el.tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        }

    </script>
</head>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <h:outputText value="#{msg_aplic.prt_LancaBrinde_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_LancaBrinde_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-trocar-os-pontos-por-brindes-no-clube-de-vantagens/"/>
    
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
            </h:panelGrid>
            <h:panelGroup id="panelCadastroBrinde"  layout="block" style="margin-left: 15px;">
                <h:panelGrid id="panelGeral" columns="1" >
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Saldo Atual:" />
                    <rich:spacer height="3px"/>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{LancarBrindeClienteControle.pontosClienteSelecioando} pontos" />
                    <rich:spacer height="13px"/>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Brinde:" />
                    <rich:spacer height="3px"/>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{LancarBrindeClienteControle.brindeSelecionado.nome}" />
                    <rich:spacer height="13px"/>
                    <h:outputText  rendered="#{LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Será debitado:" />
                    <rich:spacer height="3px"/>
                    <h:outputText rendered="#{LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}" styleClass="texto-size-14 texto-cor-cinza texto-font" style="color: red;" value="#{LancarBrindeClienteControle.brindeSelecionado.pontosNegativo} pontos" />
                    <rich:spacer height="13px"/>
                    <h:outputText  rendered="#{LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Saldo após lançamento:" />
                    <rich:spacer height="3px"/>
                    <h:outputText rendered="#{LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}" styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{LancarBrindeClienteControle.pontosDepoisDeGravar} pontos" />
                    <rich:spacer height="13px"/>
                    <h:outputText  rendered="#{LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}"
                                   styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Observação:" />
                    <rich:spacer height="3px"/>
                    <h:panelGroup rendered="#{LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}">
                        <h:inputTextarea  cols="70" rows="50" id="descricao" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                          styleClass="inputTextClean" style="height:5.4em;" value="#{LancarBrindeClienteControle.dadosBrindeLancar.observacao}" />
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid  columns="1" width="100%"  style="margin-top: 13px;" >
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>

                            <a4j:commandLink id="voltar" immediate="true" style="margin-top: 13px;"
                                             action="#{LancarBrindeClienteControle.lancarNovoBrinde}"
                                             rendered="#{LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}"
                                             value="#{msg_aplic.prt_Cheque_bancoprt_voltar}" onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             accesskey="5" styleClass="pure-button"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink id="consultar" immediate="true"
                                             action="#{LancarBrindeClienteControle.lancarNovoBrinde}"
                                             rendered="#{!LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}"
                                             value="#{msg_aplic.btn_lancarnovo_brinde}" onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             accesskey="5" styleClass="pure-button pure-button-primary"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink id="salvar" action="#{LancarBrindeClienteControle.validarDadosParaGravar}" value="#{msg_bt.btn_gravar}"
                                               rendered="#{LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}"
                                               oncomplete="#{LancarBrindeClienteControle.onCompleteGravar};"
                                               accesskey="2" styleClass="pure-button pure-button-primary" reRender="form"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink id="imprimirRecibo" styleClass="pure-button"
                                             title="Imprimir comprovante do brinde" immediate="true"
                                             action="#{LancarBrindeClienteControle.imprimirComprovanteOperacao}"
                                             rendered="#{!LancarBrindeClienteControle.mostrarPontosDepoisDeGravar}"
                                             oncomplete="abrirPopupPDFImpressao('relatorio/#{LancarBrindeClienteControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')">
                                <i id="imprimirIcon" class="fa-icon-print"></i>&nbsp Imprimir recibo de Brinde
                            </a4j:commandLink>
                            <h:outputText value="    "/>

<%--                            <a4j:commandLink id="comprovanteOpCan"--%>
<%--                                             rendered="#{!CancelamentoContratoControle.apresentarBoteos && !CancelamentoContratoControle.processandoOperacao}"--%>
<%--                                             title="Imprimir Comprovante da Operação de Cancelamento"--%>
<%--                                             action="#{CancelamentoContratoControle.imprimirComprovanteOperacao}"--%>
<%--                                             oncomplete="abrirPopupPDFImpressao('relatorio/#{CancelamentoContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"--%>
<%--                                             styleClass="pure-button">--%>
<%--                                <i class="fa-icon-print"></i>&nbsp Imprimir Comprovante--%>
<%--                            </a4j:commandLink>--%>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <SCRIPT>
              carregarTooltipster();
            </SCRIPT>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="mdlAvisoLancamentoBrinde" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmar Lançamento de Brinde"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                
                <h:panelGroup layout="block">
                    <a4j:commandLink value="Sim" action="#{LancarBrindeClienteControle.gravar}" reRender="form"
                                       id="confirmacaoOpercaoSim"
                                       oncomplete="#{LancarBrindeClienteControle.mensagemNotificar};#{LancarBrindeClienteControle.onCompleteGravar}"
                                       styleClass="pure-button pure-button-primary"/>
                    <h:outputText value="    "/>
                    <a4j:commandLink value="Não" onclick="Richfaces.hideModalPanel('mdlAvisoLancamentoBrinde');" reRender="mdlAvisoLancamentoBrinde" id="confirmacaoOpercaoNao" styleClass="pure-button"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />

</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>


<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:view>
<jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
<jsp:include page="include_head.jsp" flush="true" />

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>


    <head>
        <script type="text/javascript" src="bootstrap/jquery.js"></script>
        <script src="script/amcharts.js" type="text/javascript"></script>
        <script src="script/serial.js" type="text/javascript"></script>
        <script src="script/gauge.js" type="text/javascript"></script>
        <script type="text/javascript" type="text/javascript" src="script/script.js"></script>
        <script type="text/javascript" language="javascript">
            setDocumentCookie('popupsImportante', 'close',1);
        </script>
        <link href="./css/otimize.css" rel="stylesheet" type="text/css"/>
        <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css"/>
        <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css"/>
        <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css"/>
        <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css"/>

        <link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

        <link href="./css/otimize.css" rel="stylesheet" type="text/css"/>
        <link href="css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
        <link href="bootstrap/bootplus.min.css" rel="stylesheet">
        <style>
            .botaoGame, .botaoGame:hover, .botaoGame:visited, .botaoGame:active{
                background: #250061; /* Old browsers */
                background: -moz-linear-gradient(left, #250061 0%, #004671 100%); /* FF3.6-15 */
                background: -webkit-linear-gradient(left, #250061 0%,#004671 100%); /* Chrome10-25,Safari5.1-6 */
                background: linear-gradient(to right, #250061 0%,#004671 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
                filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6419e5', endColorstr='#004671',GradientType=1 ); /* IE6-9 */
            }

            .botaoGame .hexagono{
                transition: transform 2s;
                transform-origin: center;
            }
            .botaoGame:hover .hexagono{
                transform: rotate(90deg);

            }
                /* TOPO MENU */

            .rich-ddmenu-label, .rich-ddmenu-label-disabled {
                padding: 0px !important;
            }

            /* CUSTOMIZE THE CAROUSEL
            -------------------------------------------------- */

            /* Carousel base class */
            .carousel {
                margin-bottom: 0px;
                height: 100%;
            }

            .carousel .container {
                position: relative;
                z-index: 9;
                height: 100%;
            }

            .carousel-control {
                height: 80px;
                margin-top: 0;
                font-size: 36px;
                text-shadow: 0 1px 1px rgba(0, 0, 0, .4);
                background-color: transparent;
                border: 0;
                z-index: 10;
            }

            .carousel .item {
                height: 100%
            }

            .carousel img {
                margin-left: auto;
                margin-right: auto;
            }

            .carousel-caption {
                background-color: transparent;
                position: static;
                max-width: 550px;
                padding: 0 20px;
                margin-top: 200px;
            }

            .carousel-caption h1,
            .carousel-caption .lead {
                margin: 0;
                line-height: 1.25;
                color: #fff;
                text-shadow: 0 1px 1px rgba(0, 0, 0, .4);
            }

            .carousel-caption .btn {
                margin-top: 10px;
            }

        </style>
        <!-- Le javascript -->
        <!-- Placed at the end of the document so the pages load faster -->
        <script src="bootstrap/bootstrap-transition.js"></script>
        <script src="bootstrap/bootstrap-carousel.js"></script>
    </head>



    <%-- Retirando a condição para a tela de relatórios não redirecionar para a tela de BI
    <c:if test="${BIControle.exibirBIs}">
        <c:redirect url="detalheBI.jsp"/>
    </c:if>
    --%>

    <h:form id="form" style="margin: 0;">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemRelatorios" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" style="height: 100%" styleClass="container-imagem container-conteudo-central">
                            <%-- Retirando botão para ir para tela de BI
                            <h:panelGroup layout="block" styleClass="container-box-bi form-flat">
                                <h:panelGroup layout="block" style="padding-bottom:0px;margin-top: auto;" styleClass="margin-box">
                                    <h:panelGrid columns="3" width="100%" columnClasses="w33,w34,w33">
                                        <f:facet name="header">
                                                <h:panelGroup layout="block" styleClass="container_dicas_bi_botao_topo" style="width: 100%;">
                                                    <a4j:commandLink id="linkBI" styleClass="pure-button button-xlarge pure-button-primary"
                                                                     action="#{BIControle.irTelaBI}"
                                                                     style="font-size: 22px; max-width: 20vw; width: 20vw; height: 2.5vw; line-height:2.5vw; border-radius: 5px; padding: 10px !important;">
                                                        Confira aqui seus BI's
                                                        <span>
                                                        <i class="fa-icon-chevron-right"></i>
                                                    </span>
                                                    </a4j:commandLink>
                                            </h:panelGroup>
                                        </f:facet>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                            --%>
                            <a href="#" class="bannerPadrao">
                                <img border="none" class="img-responsive imagem-blur-bottom imagemApresentacao"
                                     src="images/banner-respiracao.png"/>
                            </a>
                            <div id="myCarousel" class="carousel slide container-conteudo-central">
                                <div class="carousel-inner">
                                    <c:forEach items="${SuporteControle.bannersBI}" var="ban" varStatus="ind">
                                        <div class="item ${ind.count == 1 ? 'active' : ''}">

                                            <c:if test="${not empty ban.funcionalidade}">
                                                <a onclick="abrirFuncionalidade('${ban.funcionalidade}')"
                                                   style="cursor: pointer">
                                                    <img border="none" style="max-width:100%"
                                                         src="${ban.urlImagem}">
                                                </a>
                                            </c:if>

                                            <c:if test="${empty ban.funcionalidade}">
                                                <c:if test="${not empty ban.urlLink}">
                                                    <a onclick="clickNotificar()"
                                                       href="${ban.urlLink}"
                                                       target="_blank">
                                                        <img border="none" style="max-width:100%"
                                                             src="${ban.urlImagem}">
                                                    </a>
                                                </c:if>
                                                <c:if test="${empty ban.urlLink}">
                                                    <img border="none" style="max-width:100%"
                                                         src="${ban.urlImagem}">
                                                </c:if>
                                            </c:if>
                                        </div>
                                    </c:forEach>
                                </div>

                                <ol class="carousel-indicators">
                                    <c:forEach items="${SuporteControle.bannersBI}" var="ban"
                                               varStatus="ind">
                                        <li style="cursor: pointer;" data-target="#myCarousel"
                                            data-slide-to="${ind.count -1}"
                                            class="${ind.count == 1 ? 'active' : ''}"></li>
                                    </c:forEach>
                                </ol>
                            </div>
                            <script>
                                validarTamanhoBanner(false);
                            </script>

                            <a4j:commandLink
                                    status="false"
                                    id="notificarClickBI"
                                    style="display: none"
                                    action="#{SuporteControle.notificarClickBI}">
                            </a4j:commandLink>

                            <a4j:commandLink id="funcionalidadeAbrirClick"
                                             style="display: none"
                                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                                <f:attribute name="funcionalidade" value="#{SuporteControle.funcionalidadeAbrir}"/>
                            </a4j:commandLink>

                            <h:inputHidden id="funcionalidadeAbrir"
                                           value="#{SuporteControle.funcionalidadeAbrir}"/>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp">
                            <jsp:param name="menu" value="ADM-BI" />
                        </jsp:include>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
          <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">

    </h:form>
    <%@include file="/include_load_configs.jsp"%>
    <script>
        function clickNotificar() {
            document.getElementById('form:notificarClickBI').click();
        }

        function abrirFuncionalidade(funcionalidade) {
            clickNotificar();
            var funcionalidadeAbrir = document.getElementById('form:funcionalidadeAbrir');
            funcionalidadeAbrir.value = funcionalidade;
            document.getElementById('form:funcionalidadeAbrirClick').click();
        }
    </script>
</f:view>



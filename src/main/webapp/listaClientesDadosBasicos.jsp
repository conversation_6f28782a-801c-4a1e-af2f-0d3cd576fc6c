<%-- 
    Document   : listaClientesDadosBasicos
    Created on : 31/08/2011, 15:27:09
    Author     : Waller
--%>

<%@page import="controle.basico.ClienteControle"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@include file="/includes/include_import_minifiles.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@include file="/includes/imports.jsp" %>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Lista de Clientes - Dados Básicos"/>
    </title>
    <h:form id="form">

        <html>
            <jsp:include page="include_head.jsp" flush="true" />
            <body>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".itemRelatorios" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="Lista de Clientes Simplificada" styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}como-gerar-um-relatorio-de-todos-os-alunos-cadastrados-no-sistema/"
                                                          title="Clique e saiba mais: Clientes Simplificado"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGrid width="100%" style="text-align: right;margin-bottom:8px;">
                                            <h:panelGroup layout="block">
                                                <a4j:commandLink id="exportarExcel"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{ClienteControle.listaClientesSimplificado}"/>
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="itemExportacao" value="relClientesSimplificado"/>
                                                    <f:attribute name="prefixo" value="ListaClientesSimplificada"/>
                                                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                </a4j:commandLink>
                                                <%--BOTÃO PDF--%>
                                                <a4j:commandLink id="exportarPdf"
                                                                   style="margin-left: 8px;"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty ClienteControle.listaClientesSimplificado}"
                                                                   oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{ClienteControle.listaClientesSimplificado}"/>
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <f:attribute name="itemExportacao" value="relClientesSimplificado"/>
                                                    <f:attribute name="prefixo" value="ListaClientesSimplificada"/>
                                                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <rich:dataTable id="tabela" width="100%"
                                                                  rows="15"
                                                                  styleClass="tabelaSimplesCustom"
                                                                  rowKeyVar="status"
                                                                  value="#{ClienteControle.listaClientesSimplificado}" var="cliente">
                                            <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="Matrícula"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.matricula}"/>
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="Nome"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.nome}"/>
                                            </rich:column>

                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="Situação"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.situacao}"/>
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="#{ClienteControle.displayIdentificadorFront[0]}"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.cpf}"/>
                                            </rich:column>

                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="Email"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.email}"/>
                                            </rich:column>

                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="Endereço"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.endereco}"/>
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="Cidade"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.cidade}"/>
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="Estado"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.estado}"/>
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-12-real texto-cor-cinza texto-bold"  value="Telefone"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza "  value="#{cliente.telefone}"/>
                                            </rich:column>
                                        </rich:dataTable>
                                        <rich:datascroller align="center" styleClass="scrollPureCustom" renderIfSinglePage="false" for="form:tabela" maxPages="20" id="scitems" />
                                        <h:panelGrid id="mensagem" columns="1" width="100%" styleClass="tabMensagens" style="margin:5 5 5 5;">
                                            <h:outputText styleClass="mensagem" value="#{GestaoTransacoesControle.mensagem}"/>
                                            <h:outputText styleClass="mensagemDetalhada" value="#{GestaoTransacoesControle.mensagemDetalhada}"/>

                                        </h:panelGrid>
                                    </h:panelGroup>

                                </h:panelGroup>

                            </h:panelGroup>
                            <jsp:include page="menuRelatorio.jsp" flush="true"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="include_rodape_flat.jsp" flush="true" />

            </h:panelGroup>
            </body>
        </html>
    </h:form>
</f:view>

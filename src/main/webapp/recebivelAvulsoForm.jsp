<%@page contentType="text/html;charset=UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<script type="text/javascript" src="script/demonstrativoFinan.js"></script>
<f:view>
	<jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
	<html>

	<title><h:outputText
		value="Lançamento de Cheque/Cartão de Crédito Avulso" /></title>
	<c:set var="titulo" scope="session" value="Lançamento de Cheque/Cartão de Crédito Avulso"/>
	<f:facet name="header">
		<jsp:include page="topoReduzido_material.jsp" />
	</f:facet>
	<a4j:loadScript src="script/jquery.maskedinput-1.2.2.js" />
	<h:form id="form">
            <a4j:keepAlive beanName="ExportadorListaControle"/>

		<head>
            <%@include file="pages/finan/includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript"
                    src="${contextoFinan}/script/telaInicial.js"></script>
        </head>

		<body style="background-color: #FFFFFF;">
		<hr style="border-color: #e6e6e6;"/>
		<h:panelGrid columns="1" width="100%">
			<h:panelGrid id="painelImportacao" columns="2"
						  rowClasses="linhaPar,linhaImpar"
				          columnClasses="classEsquerda, classDireita" width="100%">
				          <h:outputText  styleClass="tituloCampos" value="Importar via arquivo:"></h:outputText>
				<h:selectBooleanCheckbox value="#{RecebivelAvulsoControle.importarViaArquivo}"> 
				                         <a4j:support event="onclick" reRender="form" action="#{RecebivelAvulsoControle.selecionarImportarViaArquivo}"></a4j:support>
				                         </h:selectBooleanCheckbox>
				
				<h:outputText value="Arquivo:" styleClass="tituloCampos"
				              rendered="#{RecebivelAvulsoControle.importarViaArquivo}"></h:outputText>
				<h:panelGroup rendered="#{RecebivelAvulsoControle.importarViaArquivo}">
					<rich:fileUpload 
						fileUploadListener="#{RecebivelAvulsoControle.uploadArquivoListener}"
						immediateUpload="true" id="imagemModeloUpload"
						acceptedTypes="xls" allowFlash="false"
						listHeight="58px"
						onfileuploadcomplete="document.getElementById('form:arquivoCarregado').value = 'true';"
						cancelEntryControlLabel="Cancelar"
						addControlLabel="Adicionar"
						clearControlLabel="Remover"
						clearAllControlLabel="Remover Todos"
						doneLabel="Concluído"
						sizeErrorLabel="Limite de tamanho atingido"
						uploadControlLabel="Carregar"
						transferErrorLabel="Erro na transferência"
						stopControlLabel="Parar"
						stopEntryControlLabel="Parar"
						progressLabel="Carregando"
                                                maxFilesQuantity="1">
					</rich:fileUpload>
					<a4j:support event="onclear" action="#{RecebivelAvulsoControle.removerArquivo}" reRender="painelImportacao, mensagens"></a4j:support>
					<a4j:support event="onuploadcanceled" action="#{RecebivelAvulsoControle.removerArquivo}" reRender="painelImportacao, mensagens"></a4j:support>
                                        <a href="modelo/modelo_importacao_cheques.xls">Baixar modelo de arquivo</a>

				</h:panelGroup>

			</h:panelGrid>
			<h:panelGrid id="painelCC" columns="2"
				rowClasses="linhaPar,linhaImpar"
				columnClasses="classEsquerda, classDireita" width="100%">


				<h:outputText></h:outputText>
				<h:selectOneRadio id="mudarChequeCartao" tabindex="1" disabled="#{RecebivelAvulsoControle.importarViaArquivo}"
					styleClass="tituloCampos" value="#{RecebivelAvulsoControle.tipo}">
					<f:selectItem itemLabel="Cheque" itemValue="1" />
					<f:selectItem itemLabel="Cartão de Crédito" itemValue="2" />
					<a4j:support event="onchange" reRender="form, mensagens" />
				</h:selectOneRadio>
				
				
				<h:outputText  styleClass="tituloCampos" value="Empresa :"
					rendered="#{RecebivelAvulsoControle.usuarioLogado.administrador}"> </h:outputText>
				<h:selectOneMenu
					value="#{RecebivelAvulsoControle.recebivelAvulso.empresa.codigo}"
					rendered="#{RecebivelAvulsoControle.usuarioLogado.administrador}">
					<f:selectItems value="#{RecebivelAvulsoControle.empresas}" />
				</h:selectOneMenu>

				<h:outputText styleClass="tituloCampos" value="Responsável:" />
				<h:panelGroup>
					<h:inputText id="responsavel" size="50" style="width: 300px"
						maxlength="50" onblur="blurinput(this);"
						onfocus="focusinput(this);" styleClass="painelCC" tabindex="2"
						value="#{RecebivelAvulsoControle.nomeResponsavel}">

					</h:inputText>
					<rich:suggestionbox height="200" width="200" for="responsavel"
						fetchValue="#{result.nome}"
						suggestionAction="#{RecebivelAvulsoControle.executarAutocompleteUsuario}"
						minChars="1" rowClasses="20" status="statusHora"
						nothingLabel="Nenhum usuário encontrado !" var="result"
						id="suggestionNomeColaborador">
						<a4j:support event="onselect"
							reRender="formDestino, form:nomesDest"
							action="#{RecebivelAvulsoControle.selecionarUsuario}"
							oncomplete="#{rich:element('nomePlanoSelecionadoRateio')}.focus();" />
						<h:column>
							<h:outputText value="#{result.nome}" />
						</h:column>
					</rich:suggestionbox>
				</h:panelGroup>

				<h:outputText styleClass="tituloCampos" value="Plano de Contas:" />

				<h:panelGroup>
					<h:inputText id="nomePlanoSelecionadoRateio" size="50"
						maxlength="50" style="width: 300px" onfocus="focusinput(this);"
						styleClass="form"
						value="#{RecebivelAvulsoControle.descricaoPlano}">
					</h:inputText>


					<rich:suggestionbox height="200" width="400"
						for="nomePlanoSelecionadoRateio" status="statusInComponent"
						immediate="true"
						suggestionAction="#{RecebivelAvulsoControle.executarAutocompletePlanoContas}"
						minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
						id="suggestionResponsavelRateio">
						<a4j:support event="onselect" reRender="painelCC, mensagens"
							focus="nomeCentroSelecionadoRateio"
							action="#{RecebivelAvulsoControle.selecionarPlanoContas}">
						</a4j:support>
						<h:column>
							<f:facet name="header">
								<h:outputText value="Nome" styleClass="textverysmall" />
							</f:facet>
							<h:outputText styleClass="textverysmall"
								value="#{result.descricaoCurta}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText value="Tipo" styleClass="textverysmall" />
							</f:facet>
							<h:outputText styleClass="textverysmall"
								value="#{result.tipoPadrao.descricao}" />
						</h:column>
					</rich:suggestionbox>
					&nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaCorretaRateio}"
                                           reRender="modalPlanos, mensagens"
                                           id="btAddPlanoRateio" value="Consultar"
                                           oncomplete="Richfaces.showModalPanel('modalPlanos')"/>
				</h:panelGroup>
				<!-- Plano de Contas -->

				<h:outputText styleClass="tituloCampos" value="Centro de Custos:" />

				<h:panelGroup>
					<h:inputText id="nomeCentroSelecionadoRateio" size="50"
						maxlength="50" style="width: 300px" onblur="blurinput(this);"
						onfocus="focusinput(this);" styleClass="form"
						value="#{RecebivelAvulsoControle.descricaoCentro}">
					</h:inputText>

					<rich:suggestionbox height="200" width="400"
						for="nomeCentroSelecionadoRateio" status="statusInComponent"
						immediate="true"
						suggestionAction="#{RecebivelAvulsoControle.executarAutocompleteCentroCusto}"
						minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
						id="suggestionCentroCustoRateio">
						<a4j:support event="onselect" reRender="painelCC, mensagens" focus="pessoa"
							action="#{RecebivelAvulsoControle.selecionarCentroCusto}">
						</a4j:support>
						<h:column>
							<f:facet name="header">
								<h:outputText value="Nome" styleClass="textverysmall" />
							</f:facet>
							<h:outputText styleClass="textverysmall"
								value="#{result.descricaoCurta}" />
						</h:column>
					</rich:suggestionbox>
					
					&nbsp;<a4j:commandLink reRender="modalCentros, mensagens"
                                        id="btAddCentro" value="Consultar"
                                        oncomplete="Richfaces.showModalPanel('modalCentros')"/>
				</h:panelGroup>



				<h:outputText styleClass="tituloCampos" value="Cliente:" rendered="#{!RecebivelAvulsoControle.importarViaArquivo || RecebivelAvulsoControle.edicao}" />
				<h:panelGroup rendered="#{!RecebivelAvulsoControle.importarViaArquivo || RecebivelAvulsoControle.edicao}">

					<h:inputText id="pessoa" size="50" maxlength="50" onkeypress="if (event.keyCode == 13) { document.getElementById('form:faturamentoInputDate').focus();return false;};"
								 onfocus="focusinput(this);" style="width: 300px" styleClass="form" value="#{RecebivelAvulsoControle.nomePessoa}">
						<a4j:support event="onchange" reRender="modalPanelCadastrarPessoaSimplificada"
									 oncomplete="#{RecebivelAvulsoControle.msgAlertAuxiliar} document.getElementById('form:faturamentoInputDate').focus();"
									 action="#{RecebivelAvulsoControle.cadastrarNovaPessoa}"/>
					</h:inputText>

					<rich:suggestionbox height="200" width="500" for="pessoa"
						fetchValue="#{result}"
						suggestionAction="#{RecebivelAvulsoControle.executarAutocompleteConsultaPessoa}"
						minChars="1" rowClasses="20" status="statusInComponent"
						nothingLabel="Nenhuma pessoa encontrada!" var="result"
						id="suggestionPessoa">
						<a4j:support event="onselect"
							action="#{RecebivelAvulsoControle.selecionarPessoaSuggestionBox}"
							reRender="painelCC,mensagens, suggestionPessoa"
							oncomplete="document.getElementById('form:faturamentoInputDate').focus();" />

						<h:column>
							<f:facet name="header">
								<h:outputText styleClass="textverysmall" value="Nome :" />
							</f:facet>
							<h:outputText value="#{result.nome}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText styleClass="textverysmall" value="Tipo :" />
							</f:facet>
							<h:outputText value="#{result.tipoPessoa}" />
						</h:column>
						<h:column>
							<f:facet name="header">
								<h:outputText styleClass="textverysmall" value="CPF/CNPJ :" />
							</f:facet>
							<h:outputText value="#{result.cfp}" />
						</h:column>
					</rich:suggestionbox>
					<rich:spacer width="15px" />
				</h:panelGroup>

				<h:outputText styleClass="tituloCampos" value="Faturamento:" rendered="#{!RecebivelAvulsoControle.importarViaArquivo || RecebivelAvulsoControle.edicao}"/>
				<h:outputText styleClass="tituloCampos" value="Faturamento padrão:" rendered="#{RecebivelAvulsoControle.importarViaArquivo && !RecebivelAvulsoControle.edicao}"/>
				<rich:calendar id="faturamentopadrao" rendered="#{RecebivelAvulsoControle.importarViaArquivo && !RecebivelAvulsoControle.edicao}"
						oninputchange="return validar_Data(this.id);"
						inputStyle="margin: 0 0 0 0; padding: 0 0 0 0;"
						value="#{RecebivelAvulsoControle.dataPadrao}"
						enableManualInput="true" popup="true" inputSize="10"
						datePattern="dd/MM/yyyy" showApplyButton="false"
						inputClass="campos" showWeeksBar="false" firstWeekDay="0"/>
				
				<h:outputText styleClass="tituloCampos" value="Mostrar apenas sem cliente:" rendered="#{RecebivelAvulsoControle.importarViaArquivo && !RecebivelAvulsoControle.edicao}"/>
				<h:selectBooleanCheckbox value="#{RecebivelAvulsoControle.semCliente}" rendered="#{RecebivelAvulsoControle.importarViaArquivo  && !RecebivelAvulsoControle.edicao}">
					<a4j:support action="#{RecebivelAvulsoControle.verificarImportacaoRegistrosSemCliente}"
					             event="onclick"
					             reRender="painelCC, listaCheques,listaCartoes, scrollerlistacartoes, scrollerlistacheques, mensagens"></a4j:support>
				</h:selectBooleanCheckbox>
						
				<rich:calendar id="faturamento"
                                                rendered="#{!RecebivelAvulsoControle.importarViaArquivo || RecebivelAvulsoControle.edicao}"
						oninputchange="return validar_Data(this.id);"
						inputStyle="margin: 0 0 0 0; padding: 0 0 0 0;"
						value="#{RecebivelAvulsoControle.recebivelAvulso.faturamento}"
						enableManualInput="true" popup="true" inputSize="10"
						datePattern="dd/MM/yyyy" showApplyButton="false"
						inputClass="campos" showWeeksBar="false" firstWeekDay="0" />

					<h:outputText styleClass="tituloCampos" value="Compensação:" 
                                                      rendered="#{!RecebivelAvulsoControle.importarViaArquivo || RecebivelAvulsoControle.edicao}"/>
					<rich:calendar rendered="#{!RecebivelAvulsoControle.importarViaArquivo || RecebivelAvulsoControle.edicao}"
						oninputchange="return validar_Data(this.id);"
						inputStyle="margin: 0 0 0 0; padding: 0 0 0 0;"
						value="#{RecebivelAvulsoControle.recebivelAvulso.compensacao}"
						enableManualInput="true" popup="true" inputSize="10"
						datePattern="dd/MM/yyyy" showApplyButton="false"
						inputClass="campos" showWeeksBar="false" firstWeekDay="0" />

				

				<h:outputText styleClass="tituloCampos" value="Valor:" rendered="#{!RecebivelAvulsoControle.importarViaArquivo  || RecebivelAvulsoControle.edicao}"/>
				<h:inputText size="10" maxlength="10" onblur="blurinput(this);" rendered="#{!RecebivelAvulsoControle.importarViaArquivo || RecebivelAvulsoControle.edicao}"
					onfocus="focusinput(this);" styleClass="form" style="width: 300px"
					onkeypress="return(currencyFormat(this,'.',',',event));"
					value="#{RecebivelAvulsoControle.recebivelAvulso.valor}">
					<f:converter converterId="FormatadorNumerico" />
				</h:inputText>
                                <h:outputText styleClass="tituloCampos" value="Forma de pagamento:"/>
                                <h:selectOneMenu value="#{RecebivelAvulsoControle.formaPagamento}">
                                    <f:selectItem itemLabel="Selecione" itemValue=""/>
                                    <f:selectItems value="#{RecebivelAvulsoControle.formasPagamentoSelectItem}"/>
                                </h:selectOneMenu>
                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
			</h:panelGrid>


			<h:panelGrid columns="2" width="100%" headerClass="subordinado" rendered="#{!RecebivelAvulsoControle.importarViaArquivo
			                                                                || RecebivelAvulsoControle.edicao}"
				columnClasses="colunaCentralizada"
				style="margin-right:15px;background-color:#B5B5B5;">
				<h:panelGroup>
					<h:outputText value="Dados Cheque"
						rendered="#{RecebivelAvulsoControle.apresentarCheque}"
						styleClass="tituloCampos" />
					<h:outputText value="Dados Cartão de Crédito"
						rendered="#{!RecebivelAvulsoControle.apresentarCheque}"
						styleClass="tituloCampos" />
				</h:panelGroup>
			</h:panelGrid>


			<h:panelGrid columns="2" rowClasses="linhaPar,linhaImpar" rendered="#{!RecebivelAvulsoControle.importarViaArquivo
			                                                                        || RecebivelAvulsoControle.edicao}"
				columnClasses="classEsquerda, classDireita" width="100%">

				<h:outputText styleClass="tituloCampos"
					rendered="#{RecebivelAvulsoControle.apresentarCheque}"
					value="Banco" />

				<h:panelGrid id="panelChequePreenchido"
					rendered="#{RecebivelAvulsoControle.apresentarCheque}"
					cellpadding="0" cellspacing="0" width="300px" columns="3"
					columnClasses="esquerda, direita">
					<h:inputText id="codigoBanco"
						value="#{RecebivelAvulsoControle.banco}" size="8" maxlength="6"
						onfocus="focusinput(this);" styleClass="form"
						onkeypress="return mascara(this.form, this.id, '999999', event);">
						<a4j:support event="onblur" focus="bancoPreenchido"
							action="#{RecebivelAvulsoControle.consultarBanco}"
							reRender="panelChequePreenchido, mensagens" />
					</h:inputText>

					<h:selectOneMenu id="bancoPreenchido"
					 	converter="simpleIndexConverter"
						value="#{RecebivelAvulsoControle.bancoSelected}"
						onblur="blurinput(this);" onfocus="focusinput(this);"
						styleClass="form">
						<f:selectItems value="#{ChequeControle.listaSelectItemBanco}" />
						<a4j:support event="onchange" focus="agencia"
							action="#{RecebivelAvulsoControle.consultarBancoCombo}"
							reRender="panelChequePreenchido, mensagens" />
					</h:selectOneMenu>
				</h:panelGrid>

				<h:outputText rendered="#{RecebivelAvulsoControle.apresentarCheque}"
					styleClass="tituloCampos" value="Agência: " />
				<h:panelGrid rendered="#{RecebivelAvulsoControle.apresentarCheque}"
					cellpadding="0" cellspacing="0" width="300px" columns="3"
					columnClasses="esquerda, centralizado, direita">
					<h:inputText size="20" maxlength="20"
						rendered="#{RecebivelAvulsoControle.apresentarCheque}"
						onblur="blurinput(this);" onfocus="focusinput(this);"
						styleClass="form" id="agencia"
						value="#{RecebivelAvulsoControle.recebivelAvulso.cheque.agencia}" />

					<h:outputText styleClass="tituloCampos" value="Conta: "
						rendered="#{RecebivelAvulsoControle.apresentarCheque}" />
					<h:inputText size="20" maxlength="20" onblur="blurinput(this);"
						rendered="#{RecebivelAvulsoControle.apresentarCheque}"
						onfocus="focusinput(this);" styleClass="form"
						value="#{RecebivelAvulsoControle.recebivelAvulso.cheque.conta}" />
				</h:panelGrid>
				<h:outputText styleClass="tituloCampos" value="Nr. do Cheque: "
					rendered="#{RecebivelAvulsoControle.apresentarCheque}" />
					
					<h:panelGrid rendered="#{RecebivelAvulsoControle.apresentarCheque}">
						<h:inputText size="20" maxlength="20" onblur="blurinput(this);"
							onfocus="focusinput(this);" styleClass="form" style="width: 300px"
							value="#{RecebivelAvulsoControle.recebivelAvulso.cheque.numero}" />
				</h:panelGrid>
				<h:panelGroup rendered="#{RecebivelAvulsoControle.apresentarCheque}">
					<table width="100%" cellpadding="0" cellspacing="0">
						<tr>
							<td align="right"><h:selectOneRadio id="radioCPFCNPJ"
								styleClass="tituloCampos"
								value="#{RecebivelAvulsoControle.recebivelAvulso.cheque.cpfOuCnpj}">
								<f:selectItem itemLabel="CPF" itemValue="CPF" />
								<f:selectItem itemLabel="CNPJ" itemValue="CNPJ" />
								<a4j:support event="onchange" reRender="panelCPFCNPJ, mensagens" />
							</h:selectOneRadio></td>
						</tr>
					</table>

				</h:panelGroup>
				<h:panelGrid id="panelCPFCNPJ"
					rendered="#{RecebivelAvulsoControle.apresentarCheque}">
					<h:inputText id="CPF"
						value="#{RecebivelAvulsoControle.recebivelAvulso.cheque.cpf}"
						style="width: 300px"
						rendered="#{RecebivelAvulsoControle.recebivelAvulso.cheque.cpfOuCnpj == 'CPF'}"
						onblur="blurinput(this);"
						onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
						onfocus="focusinput(this);" size="14" maxlength="14"
						styleClass="form">
					</h:inputText>
					<h:inputText id="CNPJ"
						value="#{RecebivelAvulsoControle.recebivelAvulso.cheque.cnpj}"
						rendered="#{RecebivelAvulsoControle.recebivelAvulso.cheque.cpfOuCnpj == 'CNPJ'}"
						onblur="blurinput(this);" style="width: 300px"
						onkeypress="return mascara(this.form,this.id,'99.999.999/9999-99', event);"
						onfocus="focusinput(this);" size="18" maxlength="18"
						styleClass="form">
					</h:inputText>
				</h:panelGrid>
				<h:outputText styleClass="tituloCampos" value="Nome no cheque: "
					rendered="#{RecebivelAvulsoControle.apresentarCheque}" />
				<h:panelGrid rendered="#{RecebivelAvulsoControle.apresentarCheque}">
				<h:inputText size="50" maxlength="50" onblur="blurinput(this);"
					rendered="#{RecebivelAvulsoControle.apresentarCheque}"
					onfocus="focusinput(this);"
					value="#{RecebivelAvulsoControle.recebivelAvulso.cheque.nomeNoCheque}"
					styleClass="form" style="width: 300px" />
				</h:panelGrid>
				<h:outputText styleClass="tituloCampos" value="Autorização"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}" />
				<h:inputText size="20" maxlength="10" onblur="blurinput(this);"
					style="width: 300px"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}"
					onfocus="focusinput(this);" styleClass="form"
					value="#{RecebivelAvulsoControle.recebivelAvulso.codAutorizacao}" />

				<h:outputText styleClass="tituloCampos" value="Nr. Parcela"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}" />
				<h:inputText size="20" maxlength="2" onblur="blurinput(this);"
					style="width: 300px" title="O número da parcela numa operação parcelada"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}"
					onfocus="focusinput(this);" styleClass="form"
					value="#{RecebivelAvulsoControle.recebivelAvulso.cartaoCredito.nrParcela}" />
                                <h:outputText styleClass="tituloCampos" value="Nr. Documento"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}" />
                                <h:inputText size="20" maxlength="20" onblur="blurinput(this);"
					style="width: 300px"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}"
					onfocus="focusinput(this);" styleClass="form"
					value="#{RecebivelAvulsoControle.recebivelAvulso.cartaoCredito.nrDocumento}" />

				<h:outputText styleClass="tituloCampos" value="Operadora"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}" />
				<h:selectOneMenu id="operadoraCartao" style="width: 300px"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}"
					value="#{RecebivelAvulsoControle.recebivelAvulso.cartaoCredito.operadora.codigo}"
					onblur="blurinput(this);" onfocus="focusinput(this);"
					styleClass="form">
					<f:selectItems value="#{RecebivelAvulsoControle.operadoraCartaoSelectItem}" />
				</h:selectOneMenu>

				<h:outputText styleClass="tituloCampos" value="Nome no Cartão"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}" />
				<h:inputText size="50" maxlength="50" onblur="blurinput(this);"
					style="width: 300px"
					rendered="#{!RecebivelAvulsoControle.apresentarCheque}"
					onfocus="focusinput(this);" styleClass="form"
					value="#{RecebivelAvulsoControle.recebivelAvulso.cartaoCredito.nomeNoCartao}" />


			</h:panelGrid>


			<h:panelGrid columns="3" width="100%" cellpadding="0" cellspacing="0" id="mensagens">
				<h:panelGrid columns="1" width="100%">

					<h:outputText value=" " />

				</h:panelGrid>

				<h:panelGrid columns="1" width="100%">
					<h:outputText styleClass="mensagem"
						value="#{RecebivelAvulsoControle.mensagem}" />
					<h:outputText styleClass="mensagemDetalhada"
						value="#{RecebivelAvulsoControle.mensagemDetalhada}" />
				</h:panelGrid>
			</h:panelGrid>

                        <h:panelGrid columns="3" width="100%">
                        <rich:spacer width="40px"/>
                        <h:panelGroup>
                        	<h:outputText  styleClass="mensagemDetalhada"  rendered="#{RecebivelAvulsoControle.recebivelEmLote}" value="#{msg.msg_recebido_nao_pode_excluir}">
                            </h:outputText>
                                                      
                        </h:panelGroup>    
                        </h:panelGrid>                
			<h:panelGrid id="gridBotoes" columns="1" width="100%"
				columnClasses="colunaCentralizada">
				<h:panelGroup>
					<rich:hotKey selector="#form" key="return" handler="return false;" />
					<rich:hotKey selector="#novo" key="return"
						handler="#{rich:element('novo')}.onclick();return false;" />
					<a4j:commandButton id="novo"  rendered="#{!RecebivelAvulsoControle.importarViaArquivo}"
						action="#{RecebivelAvulsoControle.novo}"
						value="#{msg_bt.btn_novo}"
						alt="#{msg.msg_novo_dados}" styleClass="botoes nvoBt btSec" reRender="painelCC, mensagens" />
					
					
					<a4j:commandButton id="prepararImp" rendered="#{RecebivelAvulsoControle.importarViaArquivo &&  empty RecebivelAvulsoControle.recebiveis}"
						action="#{RecebivelAvulsoControle.prepararDados}" 
						value="Carregar Dados" styleClass="botoes nvoBt" reRender="gridTabelasCheque,mensagens,gridBotoes" />


					<a4j:commandButton id="salvarImp" rendered="#{RecebivelAvulsoControle.importarViaArquivo && not empty RecebivelAvulsoControle.recebiveis}"
						action="#{RecebivelAvulsoControle.confirmarAlteracoes}"
						value="Limpar"
						oncomplete="#{RecebivelAvulsoControle.msgAlert}"
						alt="#{msg.msg_gravar_dados}" styleClass="botoes nvoBt btSec" reRender="form" />
						
                                        <a4j:commandButton id="salvarAlt" rendered="#{RecebivelAvulsoControle.importarViaArquivo && not empty RecebivelAvulsoControle.recebiveis}"
						action="#{RecebivelAvulsoControle.gravarImportacao}"
						value="Gravar todos" oncomplete="#{RecebivelAvulsoControle.msgAlert}"
						alt="#{msg.msg_gravar_dados}" styleClass="botoes nvoBt" reRender="gridTabelasCheque,mensagens,gridBotoes" />
                                        <a4j:commandButton id="btnExcel" rendered="#{RecebivelAvulsoControle.importarViaArquivo && not empty RecebivelAvulsoControle.listaNaoImportados}"
                                             styleClass="botoes nvoBt"
                                             value="Exportar Erros"
                                             actionListener="#{RecebivelAvulsoControle.exportar}"
                                             oncomplete="#{RecebivelAvulsoControle.msgAlert}"
                                             style="padding: -11px 15px"
                                             accesskey="3"
                                             reRender="mensagens">
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="atributos"
                                                             value="faturamentoApresentar=Dt Faturamento,lancamentoApresentar=DT Compensação,valor=Valor,bancoCodigo=Banco,agenciaApresentar=Agencia,contaApresentar=Conta,numeroApresentar=Nr Cheque,CPFCNPJApresentar=CPF,nomeNoChequeApresentar=Nome Cheque,matriculaApresentar=Matrícula,msgErro=Motivo"/>
                                                <f:attribute name="prefixo" value="ChequeNaoImportados"/>
                                          </a4j:commandButton>    

					<a4j:commandButton id="salvar" rendered="#{!RecebivelAvulsoControle.importarViaArquivo}"
						action="#{RecebivelAvulsoControle.gravar}"
						value="#{msg_bt.btn_gravar}"
						alt="#{msg.msg_gravar_dados}" styleClass="botoes nvoBt" reRender="form"
						oncomplete="document.getElementById('form:pessoa').focus();fireElementFromParent('form:btnAtualizarRecAvulso')" />
					<rich:hotKey selector="#salvar" key="return"
						handler="#{rich:element('salvar')}.onclick();return false;" />
					<h:outputText value="    " />

					<h:panelGroup id="grupoBtnExcluir" rendered="#{RecebivelAvulsoControle.recebivelAvulso.movConta > 0 && !RecebivelAvulsoControle.recebivelEmLote}" >
						<a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
										   oncomplete="#{RecebivelAvulsoControle.msgAlert}" action="#{RecebivelAvulsoControle.confirmarExcluir}"
										   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
					</h:panelGroup>

					<h:outputText value="    " />

					<h:panelGroup id="grupoBtnExcluirImp" rendered="#{RecebivelAvulsoControle.importarViaArquivo && RecebivelAvulsoControle.edicao}" >
						<a4j:commandButton id="excluirimp"  reRender="mdlMensagemGenerica"
										   oncomplete="#{RecebivelAvulsoControle.msgAlert}" action="#{RecebivelAvulsoControle.confirmarExcluirImp}"
										   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
					</h:panelGroup>

					<a4j:commandButton id="buscar"
									   action="#{RecebivelAvulsoControle.buscar}"
									   value="#{msg_bt.btn_voltar_lista}"
									   alt="Buscar" styleClass="botoes nvoBt btSec"/>
				</h:panelGroup>
			</h:panelGrid>
		</h:panelGrid>

                <h:panelGrid id="gridTabelasCheque" columns="1" width="100%">
                    <h:panelGroup rendered="#{RecebivelAvulsoControle.importarViaArquivo && not empty RecebivelAvulsoControle.recebiveis}">
			<rich:dataTable id="listaCheques" rows="20" rowClasses="linhaPar, linhaImpar"
				value="#{RecebivelAvulsoControle.cheques}" var="recebivel"
                                width="100%" rendered="#{RecebivelAvulsoControle.importarViaArquivo}">
				<f:facet name="header">
                                        <h:outputText styleClass="titulocampos"
                                                        value="Cheques Para Importar Presentes no Arquivo" />
                                 </f:facet>
				<rich:column sortBy="#{recebivel.cliente.pessoa.nome}">
					<f:facet name="header">
						<h:outputText value="Cliente"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.cliente.pessoa.nome}"></h:outputText>
					</a4j:commandLink>
				</rich:column>

				<rich:column sortable="true" sortBy="#{recebivel.lancamento}">
					<f:facet name="header">
						<h:outputText value="Lançamento"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.lancamentoApresentar}"></h:outputText>
					</a4j:commandLink>
				</rich:column>
				
				<rich:column sortBy="#{recebivel.faturamento}">
					<f:facet name="header">
						<h:outputText value="Faturamento"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.faturamentoApresentar}"></h:outputText>
					</a4j:commandLink>
				</rich:column>

				<rich:column sortBy="#{recebivel.compensacao}">
					<f:facet name="header">
						<h:outputText value="Compensação"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.compensacaoApresentar}"></h:outputText>
					</a4j:commandLink>
				</rich:column>

				<rich:column>
					<f:facet name="header" >
						<h:outputText value="Banco"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.cheque.banco.nome}"></h:outputText>
					</a4j:commandLink>
				</rich:column>

				<rich:column sortBy="#{recebivel.cheque.banco.nome}">
					<f:facet name="header">
						<h:outputText value="Agência"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.cheque.agencia}"></h:outputText>
					</a4j:commandLink>
				</rich:column>

				<rich:column sortBy="#{recebivel.cheque.conta}">
					<f:facet name="header">
						<h:outputText value="Conta"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.cheque.conta}"></h:outputText>
					</a4j:commandLink>
				</rich:column>

				<rich:column sortBy="#{recebivel.cheque.numero}">
					<f:facet name="header">
						<h:outputText value="Número"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.cheque.numero}"></h:outputText>
					</a4j:commandLink>
				</rich:column>

				

				<rich:column sortBy="#{recebivel.valor}" styleClass="direita">
					<f:facet name="header">
						<h:outputText value="Valor"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.valor}">
						<f:converter converterId="FormatadorNumerico" />
					</h:outputText>
					</a4j:commandLink>
				</rich:column>
				<rich:column sortBy="#{recebivel.cheque.nomeNoCheque}">
					<f:facet name="header">
						<h:outputText value="Nome no cheque"></h:outputText>
					</f:facet>
					<a4j:commandLink reRender="form" action="#{RecebivelAvulsoControle.selecionar}">
					<h:outputText value="#{recebivel.cheque.nomeNoCheque}"></h:outputText>
					</a4j:commandLink>
				</rich:column>

				

			</rich:dataTable>
			<rich:datascroller for="listaCheques" id="scrollerlistacheques"
                                           rendered="#{RecebivelAvulsoControle.importarViaArquivo}">

			</rich:datascroller>

                    </h:panelGroup>
                    <h:panelGroup rendered="#{not empty RecebivelAvulsoControle.listaNaoImportados}">
                        
			<rich:dataTable id="listaChequesNaoImportados" rows="20" rowClasses="linhaPar, linhaImpar"
                                        value="#{RecebivelAvulsoControle.listaNaoImportados}" var="recebivel"  width="100%" >
				<f:facet name="header">
                                        <h:outputText style="font-weight: bold" styleClass="red"
                                                        value="Cheques que não foram importados" />
                                 </f:facet>
				<rich:column sortBy="#{recebivel.cliente.pessoa.nome}">
					<f:facet name="header">
						<h:outputText value="Cliente"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.cliente.pessoa.nome}"></h:outputText>
				</rich:column>

				<rich:column sortable="true" sortBy="#{recebivel.lancamento}">
					<f:facet name="header">
						<h:outputText value="Lançamento"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.lancamentoApresentar}"></h:outputText>
				</rich:column>
				
				<rich:column sortBy="#{recebivel.faturamento}">
					<f:facet name="header">
						<h:outputText value="Faturamento"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.faturamentoApresentar}"></h:outputText>
				</rich:column>

				<rich:column sortBy="#{recebivel.compensacao}">
					<f:facet name="header">
						<h:outputText value="Compensação"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.compensacaoApresentar}"></h:outputText>
				</rich:column>

				<rich:column>
					<f:facet name="header" >
						<h:outputText value="Banco"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.cheque.banco.nome}"></h:outputText>
				</rich:column>

				<rich:column sortBy="#{recebivel.cheque.banco.nome}">
					<f:facet name="header">
						<h:outputText value="Agência"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.cheque.agencia}"></h:outputText>
				</rich:column>

				<rich:column sortBy="#{recebivel.cheque.conta}">
					<f:facet name="header">
						<h:outputText value="Conta"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.cheque.conta}"></h:outputText>
				</rich:column>

				<rich:column sortBy="#{recebivel.cheque.numero}">
					<f:facet name="header">
						<h:outputText value="Número"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.cheque.numero}"></h:outputText>
				</rich:column>

				

				<rich:column sortBy="#{recebivel.valor}" styleClass="direita">
					<f:facet name="header">
						<h:outputText value="Valor"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.valor}">
						<f:converter converterId="FormatadorNumerico" />
					</h:outputText>
				</rich:column>
				<rich:column sortBy="#{recebivel.cheque.nomeNoCheque}">
					<f:facet name="header">
						<h:outputText value="Nome no cheque"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.cheque.nomeNoCheque}"></h:outputText>
				</rich:column>
                                <rich:column sortBy="#{recebivel.msgErro}">
					<f:facet name="header">
						<h:outputText value="Motivo"></h:outputText>
					</f:facet>
					<h:outputText style="font-weight: bold" styleClass="red" value="#{recebivel.msgErro}"></h:outputText>
				</rich:column>

				

			</rich:dataTable>
			<rich:datascroller for="listaChequesNaoImportados" id="scrollerlistachequesNaoImportados">
			</rich:datascroller>

			</h:panelGroup>
		</h:panelGrid>                        
		</body>
	</h:form>

	</html>
	
	
	<rich:modalPanel id="modalPlanos" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Selecionar Plano de Contas"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hidelinkSelecionarPlano" />
            <rich:componentControl for="modalPlanos"
                                   attachTo="hidelinkSelecionarPlano" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <!-- TREE VIEW -->
    <div id="content">
        <rich:panel id="dados">
            <h:panelGrid columns="2" columnClasses="top,top" width="100%">                
                <rich:tree style="height: 350px; width: 420px; overflow: scroll;" id="treeview"
                           iconCollapsed="/images/expandTree.gif"
                           iconExpanded="/images/collapTree.gif"           nodeSelectListener="#{PlanoContasControle.processSelection}"
                           nodeFace="#{treeNode.parent.parent == null ? 'node' : 'leaf'}"
                           reRender="selectedNode, dados" ajaxSubmitSelection="true"
                           switchType="client" data="descricao"
                           value="#{PlanoContasControle.treeNode}" var="item">
                    <rich:treeNode id="tNodeP" type="node" >
                        <a4j:commandLink onclick="preencherHiddenChamarBotao('formPlano:botaoSelecao','formPlano:codigoSelecao', #{item.codigo})"
                                         id="descricaoDetalhada">
                            <h:outputText styleClass="tituloCampos" value="#{item.descricaoDetalhada}">
                            </h:outputText>
                        </a4j:commandLink>
                    </rich:treeNode>
                    <rich:treeNode id="tLeafP"  type="leaf" dragType="pic">
                        <a4j:commandLink onclick="preencherHiddenChamarBotao('formPlano:botaoSelecao','formPlano:codigoSelecao', #{item.codigo})"
                                         id="codigoDescricao">
                            <h:outputText styleClass="tituloCampos" value="#{item.codigoPlano} - #{item.descricao}">
                            </h:outputText>
                        </a4j:commandLink>
                    </rich:treeNode>
                </rich:tree>
            </h:panelGrid>            
        </rich:panel>
        <a4j:form>
            <a4j:commandButton value="Atualizar" action="#{PlanoContasControle.loadTree}" reRender="treeview"
            					style="width:100px; height:30px;"
            					image="imagens/botaoAtualizarGrande.png"/>
        </a4j:form>
    </div>
    <!-- fim: TREE VIEW -->
    <!-- Controles para a tree view -->
    <h:form id="formPlano">
        <a4j:commandButton style="visibility: hidden;" reRender="painelCC, mensagens"
                           id="botaoSelecao" action="#{PlanoContasControle.processSelectionRecebiveisAvulso}" 
                           oncomplete="Richfaces.hideModalPanel('modalPlanos');"></a4j:commandButton>
        <h:inputHidden id="codigoSelecao"
                       value="#{PlanoContasControle.codigoBancoPlanoContas}" /></h:form>
    <!-- fim: Controles para a tree view -->
</rich:modalPanel>

<rich:modalPanel id="modalCentros" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Selecionar Centro de Custos"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hidelinkSelecionarCentro" />
            <rich:componentControl for="modalCentros"
                                   attachTo="hidelinkSelecionarCentro" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <!-- TREE VIEW -->
    <div id="content">
        <rich:panel id="centro">
            <h:panelGrid columns="2" columnClasses="top,top" width="100%">
                <rich:tree style="height: 350px; width: 420px; overflow: scroll;" id="treeViewCentro"
                           iconCollapsed="/images/expandTree.gif"
                           iconExpanded="/images/collapTree.gif"
                           nodeSelectListener="#{CentroCustosControle.processSelection}"
                           nodeFace="#{treeNode.parent.parent == null ? 'node' : 'leaf'}"
                           reRender="selectedNode, dados" ajaxSubmitSelection="true"
                           switchType="client" data="descricao"
                           value="#{CentroCustosControle.treeNode}" var="item">
                    <rich:treeNode id="tNodeC"   type="node" >
                        <a4j:commandLink onclick="preencherHiddenChamarBotao('formCentro:botaoSelecao','formCentro:codigoSelecao', #{item.codigo})"
                                         id="descricaoDetalhada">
                            <h:outputText styleClass="tituloCampos" value="#{item.descricaoCurta}">
                            </h:outputText>
                        </a4j:commandLink>
                    </rich:treeNode>
                    <rich:treeNode id="tLeafC"  type="leaf" dragType="pic">
                        <a4j:commandLink onclick="preencherHiddenChamarBotao('formCentro:botaoSelecao','formCentro:codigoSelecao', #{item.codigo})"
                                         id="codigoDescricao">
                            <h:outputText styleClass="tituloCampos" value="#{item.codigoCentro} - #{item.descricao}">
                            </h:outputText>
                        </a4j:commandLink>
                    </rich:treeNode>
                </rich:tree>
            </h:panelGrid>            
        </rich:panel>
        <a4j:form>
            <a4j:commandButton value="Atualizar" action="#{CentroCustosControle.loadTree}" reRender="treeViewCentro"
								style="width:100px; height:30px;"
            					image="imagens/botaoAtualizarGrande.png"/>
        </a4j:form>
    </div>
    <!-- fim: TREE VIEW -->
    <!-- Controles para a tree view -->
    <h:form id="formCentro">
        <a4j:commandButton style="visibility: hidden;" reRender="painelCC , mensagens"
                           id="botaoSelecao" action="#{CentroCustosControle.processSelectionRecebiveisAvulsos}" 
                           oncomplete="Richfaces.hideModalPanel('modalCentros');"></a4j:commandButton>
        <h:inputHidden id="codigoSelecao"
                       value="#{CentroCustosControle.codigoBancoCentroCustos}" /></h:form>
    <!-- fim: Controles para a tree view -->
</rich:modalPanel>

	<%@include file="pages/finan/pessoaSimplificado.jsp" %>
	<%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

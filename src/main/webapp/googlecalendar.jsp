<%-- 
    Document   : googlecalendar
    Created on : 20/01/2011, 10:09:59
    Author     : Waller
--%>

<%@page import="controle.basico.ConfiguracaoSistemaControle"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<%
            ConfiguracaoSistemaControle control = (ConfiguracaoSistemaControle) pageContext.getSession().getAttribute("ConfiguracaoSistemaControle");
            if (control != null) {
                pageContext.getOut().write(control.getConfiguracaoSistemaVO().getUrlGoogleAgenda());
            }
%>


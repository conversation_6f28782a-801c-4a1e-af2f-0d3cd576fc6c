<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>

<script type="text/javascript" src="../../script/jquery.js"></script>
<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>


<script type="text/javascript">
    $.noConflict();
    atualizarTreeViewRateio();
</script>

<%-- Começo da Página  --%>
<h:form id="form">

    <rich:tabPanel width="100%" switchType="client" selectedTab="#{RateioIntegracaoControle.abaSelecionada}">
        <rich:tab label="Categorias e Produtos" id="abaCategoria"
                  onlabelclick="trocarAba('abaCategoria');"
                  onclick="trocarAba('abaCategoria');"
                  onlabeldblclick="trocarAba('abaCategoria');">
            <br/>
            <h:panelGroup>
                <img src="../../images/arrow2.gif" width="16" height="16" style="vertical-align: middle; margin-right: 6px;">
                <h:outputText styleClass="tituloBold" value="Cadastro dos Rateios de Categorias de Produtos e Produtos do ADM"/>
                <div class="sep" style="margin:4px 0 5px 0;"><img src="../../images/shim.gif"></div>
            </h:panelGroup>
            <br/>
            <a href="#"
               class="expandirCategoria">
               Expandir Tudo
            </a>
            &nbsp;
            <a href="#"
               class="expandirUmCategoria">
               Expandir Um Nível
            </a>
            &nbsp;
            <a href="#"
               class="retrairUmCategoria">
               Retrair Um Nível
            </a>
            &nbsp;
            <a href="#"
               class="retrairCategoria">
               Retrair Tudo
            </a>
            <br/>
            <rich:panel>
                <h:panelGrid columns="2" width="100%"  id="panelDfTreeView">
                    <h:panelGroup>
                        <rich:spacer width="10px"></rich:spacer>
                    </h:panelGroup>
                    <h:panelGroup id ="panelMostrarRelatorio">

                        <table  border="0"
                                class="categoria"
                                id="dnd-categoria"
                                width="100%">

                            <tbody>
                                <c:forEach var="rateioP" varStatus="indice"
                                           items="${RateioIntegracaoControle.listaRateiosProdutos}">
                                    <c:if test="${not rateioP.detalhamento}">
                                        <c:choose>
                                            <c:when test="${fn:indexOf(rateioP.codigoAgrupador, '.') > 0}">
                                                <c:set var="noPai" value="${fn:substring(rateioP.codigoAgrupador,0, fn:length(rateioP.codigoAgrupador) -4)}" scope="request" />

                                                <tr bgcolor="#FFFFFF" id="${rateioP.codigoNode}"
                                                    class="child-of-${fn:replace(noPai,'.', '') + 0 }">

                                                    <c:if test="${rateioP.entidadeRateio == 0}">
                                                        <td bgcolor="#FFFFFF" class="tituloBold">
                                                            <c:out value="${rateioP.nome}"></c:out>
                                                            <c:if test="${rateioP.exibirPrevia}">
                                                                <span class="previa">[${rateioP.previa} produtos]</span>
                                                            </c:if>
                                                        </td>
                                                    </c:if>
                                                    <c:if test="${rateioP.entidadeRateio > 0}">
                                                        <td bgcolor="#E9F0F7" class="textsmall">
                                                            <c:out value="${rateioP.nome}"></c:out>
                                                            <a href="#"
                                                               onclick="preencher3HiddenChamarBotao('form:chamaModalRateio','form:idEntidade','<c:out value="${rateioP.codigoEntidade}"></c:out>', 'form:idTipo','<c:out value="${rateioP.entidadeRateio}"></c:out>','form:idNome','<c:out value="${rateioP.nome}"></c:out>')">
                                                                <img title="Adicionar Rateio" alt="Adicionar Rateio" src="../../imagens/btn_Adicionar_verde_pequeno.png" border="0px;"/>
                                                            </a>
                                                        </td>
                                                    </c:if>
                                                </tr>
                                            </c:when>
                                            <c:otherwise>
                                                <tr bgcolor="#FFFFFF" id="${rateioP.codigoNode}">
                                                    <td class="tituloBold">
                                                        <c:out value="${rateioP.nome}"></c:out>
                                                        <a href="#"
                                                           onclick="preencher3HiddenChamarBotao('form:chamaModalRateio','form:idEntidade','<c:out value="${rateioP.codigoEntidade}"></c:out>', 'form:idTipo','<c:out value="${rateioP.entidadeRateio}"></c:out>','form:idNome','<c:out value="${rateioP.nome}"></c:out>')">
                                                            <img title="Adicionar Rateio"  alt="Adicionar Rateio" src="../../imagens/btn_Adicionar_verde_pequeno.png" border="0px;"/>
                                                        </a>
                                                        <span class="previa">[${rateioP.previa} produtos associados]</span>
                                                        <c:if test="${not rateioP.possuiRateio}">
                                                            <span class="previavermelha"> Categoria ainda sem rateio configurado.</span>

                                                        </c:if>
                                                    </td>
                                                </tr>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:if>
                                    <!-- ------------------- detalhamento do rateio ------------------------- -->
                                    <c:if test="${rateioP.detalhamento}">
                                        <c:set var="noPai" value="${fn:substring(rateioP.codigoAgrupador,0, fn:length(rateioP.codigoAgrupador) -4)}" scope="request" />

                                        <tr bgcolor="#FFFFFF" id="${rateioP.codigoNode}"
                                            class="child-of-${fn:replace(noPai,'.', '') + 0 }">
                                            <td>
                                                <table><tr> <td valign="top">
                                                            <!--  PLANO DE CONTAS -->
                                                            <table>
                                                                <tr>
                                                                    <td class="titulotexto">Plano Conta</td>
                                                                    <td class="titulotexto">%</td>
                                                                    <td class="titulotexto">Empresa</td>
                                                                </tr>
                                                                <c:forEach items="${rateioP.rateiosPlano}" var="previaRateio">
                                                                    <tr>
                                                                        <td class="texto">${previaRateio.nomePlano}</td>
                                                                        <td class="texto">${previaRateio.percentagem}</td>
                                                                        <td class="texto">${previaRateio.nomeEmpresa}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                            </table>
                                                        </td><td  valign="top">
                                                            <!--  CENTRO DE CUSTOS -->
                                                            <table>
                                                                <tr>
                                                                    <td class="titulotexto">Centro Custo</td>
                                                                    <td class="titulotexto">%</td>
                                                                    <td class="titulotexto">Empresa</td>
                                                                </tr>
                                                                <c:forEach items="${rateioP.rateiosCentro}" var="previaRateio">
                                                                    <tr>
                                                                        <td class="texto">${previaRateio.nomeCentro}</td>
                                                                        <td class="texto">${previaRateio.percentagem}</td>
                                                                        <td class="texto">${previaRateio.nomeEmpresa}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </c:if>
                                </c:forEach>
                            </tbody>
                        </table>
                    </h:panelGroup>
                </h:panelGrid>
            </rich:panel>
        </rich:tab>

        <rich:tab label="Modalidades" id="abaModalidade"
                  onlabelclick="trocarAba('abaModalidade');"
                  onclick="trocarAba('abaModalidade');"
                  onlabeldblclick="trocarAba('abaModalidade');">

            <br/>
            <h:panelGroup>
                <img src="../../images/arrow2.gif" width="16" height="16" style="vertical-align: middle; margin-right: 6px;">
                <h:outputText styleClass="tituloBold" value="Cadastro dos Rateios de Modalidades do ADM"/>

                <div class="sep" style="margin:4px 0 5px 0;"><img src="../../images/shim.gif"></div>
            </h:panelGroup>
            <br/>
            <a href="#"
               class="expandirModalidade">
               Expandir Tudo
            </a>
            &nbsp;
            <a href="#"
               class="expandirUmModalidade">
               Expandir Um Nível
            </a>
            &nbsp;
            <a href="#"
               class="retrairUmModalidade">
               Retrair Um Nível
            </a>
            &nbsp;
            <a href="#"
               class="retrairModalidade">
               Retrair Tudo
            </a>
            <br/>
            <rich:panel>
                <h:panelGrid width="100%" columns="3"  id="panelDfTreeViewModalidades">
                    <h:panelGroup>
                        <rich:spacer width="10px"></rich:spacer>
                    </h:panelGroup>
                    <h:panelGroup id ="panelMostrarRelatorioModalidades">

                        <table  border="0"
                                class="modalidades"
                                id="dnd-modalidades"
                                width="100%">

                            <tbody>
                                <c:forEach var="rateioM" varStatus="indice"
                                           items="${RateioIntegracaoControle.listaRateiosModalidades}">
                                    <c:if test="${not rateioM.detalhamento}">
                                        <c:choose>
                                            <c:when test="${fn:indexOf(rateioM.codigoAgrupador, '.') > 0}">
                                                <c:set var="noPai" value="${fn:substring(rateioM.codigoAgrupador,0, fn:length(rateioM.codigoAgrupador) -4)}" scope="request" />

                                                <tr bgcolor="#FFFFFF" id="${rateioM.codigoNode}"
                                                    class="child-of-${fn:replace(noPai,'.', '') + 0 }">
                                                    <td bgcolor="#FFFFFF" class="tituloBold">
                                                        <c:out value="${rateioM.nome}"></c:out>
                                                        <span class="texto" style="color: #929292;">
                                                            <c:out value="${rateioM.empresas}"></c:out>
                                                        </span>
                                                        
                                                        <a href="#"
                                                           onclick="preencher3HiddenChamarBotao('form:chamaModalRateio','form:idEntidade','<c:out value="${rateioM.codigoEntidade}"></c:out>', 'form:idTipo','<c:out value="${rateioM.entidadeRateio}"></c:out>','form:idNome','<c:out value="${rateioM.nome}"></c:out>')">
                                                            <img title="Adicionar Rateio" alt="Adicionar Rateio" src="../../imagens/btn_Adicionar_verde_pequeno.png" border="0px;"/>
                                                        </a>
                                                    </td>
                                                </tr>
                                            </c:when>
                                            <c:when test="${rateioM.codigoEntidade < 0}">
                                                <tr bgcolor="#FFFFFF" id="${rateioM.codigoNode}">
                                                    <td bgcolor="#FFFFFF" class="tituloBold">
                                                        <c:out value="${rateioM.nome}"></c:out>
                                                        <a href="#"
                                                           onclick="preencher3HiddenChamarBotao('form:chamaModalRateio','form:idEntidade','0', 'form:idTipo','<c:out value="${rateioM.entidadeRateio}"></c:out>','form:idNome','')">
                                                            <img title="Adicionar Rateio" alt="Adicionar Rateio" src="../../imagens/btn_Adicionar_verde_pequeno.png" border="0px;"/>
                                                        </a>
                                                    </td>
                                                </tr>
                                            </c:when>
                                            <c:otherwise>
                                                <tr bgcolor="#FFFFFF" id="${rateioM.codigoNode}">
                                                    <td bgcolor="#FFFFFF" class="tituloBold">
                                                        <c:out value="${rateioM.nome}"></c:out>
                                                        <span class="previa">[${rateioM.previa} modalidades]</span>
                                                    </td>
                                                </tr>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:if>
                                    <!-- ------------------- detalhamento do rateio ------------------------- -->
                                    <c:if test="${rateioM.detalhamento}">
                                        <c:set var="noPai" value="${fn:substring(rateioM.codigoAgrupador,0, fn:length(rateioM.codigoAgrupador) -4)}" scope="request" />

                                        <tr bgcolor="#FFFFFF" id="${rateioM.codigoNode}"
                                            class="child-of-${fn:replace(noPai,'.', '') + 0 }">
                                            <td>
                                                <table><tr><td valign="top">
                                                            <!--  PLANO DE CONTAS -->
                                                            <table>
                                                                <tr>
                                                                    <td class="titulotexto">Plano Conta</td>
                                                                    <td class="titulotexto">%</td>
                                                                    <td class="titulotexto">Empresa</td>
                                                                </tr>
                                                                <c:forEach items="${rateioM.rateiosPlano}" var="previaRateio">
                                                                    <tr>
                                                                        <td class="texto">${previaRateio.nomePlano}</td>
                                                                        <td class="texto">${previaRateio.percentagem}</td>
                                                                        <td class="texto">${previaRateio.nomeEmpresa}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                            </table>
                                                        </td><td valign="top">
                                                            <!--  CENTRO DE CUSTOS -->
                                                            <table>
                                                                <tr>
                                                                    <td class="titulotexto">Centro Custo</td>
                                                                    <td class="titulotexto">%</td>
                                                                    <td class="titulotexto">Empresa</td>
                                                                </tr>
                                                                <c:forEach items="${rateioM.rateiosCentro}" var="previaRateio">
                                                                    <tr>
                                                                        <td class="texto">${previaRateio.nomeCentro}</td>
                                                                        <td class="texto">${previaRateio.percentagem}</td>
                                                                        <td class="texto">${previaRateio.nomeEmpresa}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </c:if>
                                </c:forEach>
                            </tbody>
                        </table>
                    </h:panelGroup>
                </h:panelGrid>
            </rich:panel>
        </rich:tab>

        <rich:tab label="Central de Eventos" id="abaCentral"
                  onlabelclick="trocarAba('abaCentral');"
                  onclick="trocarAba('abaCentral');"
                  onlabeldblclick="trocarAba('abaCentral');"
                  rendered="#{RateioIntegracaoControle.confFinanceiro.usarCentralEventos}">
            <br/>
            <h:panelGroup>
                <img src="../../images/arrow2.gif" width="16" height="16" style="vertical-align: middle; margin-right: 6px;"/>
                <h:outputText styleClass="tituloBold" value="Cadastro dos Rateios de Produtos e Serviços do Central de Eventos"/>
                <div class="sep" style="margin:4px 0 5px 0;"><img src="../../images/shim.gif"/></div>
            </h:panelGroup>
            <br/>
            <a href="#"
               class="expandirCentralEventos">
               Expandir Tudo
            </a>
            &nbsp;
            <a href="#"
               class="expandirCentralEventos">
               Expandir Um Nível
            </a>
            &nbsp;
            <a href="#"
               class="retrairCentralEventos">
               Retrair Um Nível
            </a>
            &nbsp;
            <a href="#"
               class="retrairCentralEventos">
               Retrair Tudo
            </a>
            <br/>
            <rich:panel>
                <h:panelGrid columns="2" width="100%"  id="panelCETreeView">
                    <h:panelGroup>
                        <rich:spacer width="10px"></rich:spacer>
                    </h:panelGroup>
                    <h:panelGroup id ="panelMostrarRelatorioCE">

                        <table  border="0"
                                class="centraleventos"
                                id="dnd-centraleventos"
                                width="100%">

                            <tbody>
                                <c:forEach var="rateioP" varStatus="indice"
                                           items="${RateioIntegracaoControle.listaRateiosCentralEventos}">
                                    <c:if test="${not rateioP.detalhamento}">
                                        <c:choose>
                                            <c:when test="${fn:indexOf(rateioP.codigoAgrupador, '.') > 0}">
                                                <c:set var="noPai" value="${fn:substring(rateioP.codigoAgrupador,0, fn:length(rateioP.codigoAgrupador) -4)}" scope="request" />

                                                <tr bgcolor="#FFFFFF" id="${rateioP.codigoNode}"
                                                    class="child-of-${fn:replace(noPai,'.', '') + 0 }">

                                                    <c:if test="${rateioP.entidadeRateio == 0}">
                                                        <td bgcolor="#FFFFFF" class="tituloBold">
                                                            <c:out  value="${rateioP.nome}"></c:out>
                                                            <c:if test="${rateioP.exibirPrevia}">
                                                                <span class="previa">[${rateioP.previa} produtos]</span>
                                                            </c:if>
                                                        </td>
                                                    </c:if>
                                                    <c:if test="${rateioP.entidadeRateio > 0}">
                                                        <td bgcolor="#E9F0F7" class="textsmall">
                                                            <c:out value="${rateioP.nome}"></c:out>
                                                            <a href="#"
                                                               onclick="preencher3HiddenChamarBotao('form:chamaModalRateio','form:idEntidade','<c:out value="${rateioP.codigoEntidade}"></c:out>', 'form:idTipo','<c:out value="${rateioP.entidadeRateio}"></c:out>','form:idNome','<c:out value="${rateioP.nome}"></c:out>')">
                                                                <img title="Adicionar Rateio" alt="Adicionar Rateio" src="../../imagens/btn_Adicionar_verde_pequeno.png" border="0px;"/>
                                                            </a>
                                                        </td>
                                                    </c:if>
                                                </tr>
                                            </c:when>
                                            <c:otherwise>
                                                <tr bgcolor="#FFFFFF" id="${rateioP.codigoNode}">
                                                    <td class="tituloBold">
                                                        <c:out value="${rateioP.nome}"></c:out>
                                                        <a href="#"
                                                           onclick="preencher3HiddenChamarBotao('form:chamaModalRateio','form:idEntidade','<c:out value="${rateioP.codigoEntidade}"></c:out>', 'form:idTipo','<c:out value="${rateioP.entidadeRateio}"></c:out>','form:idNome','<c:out value="${rateioP.nome}"></c:out>')">
                                                            <img title="Adicionar Rateio"  alt="Adicionar Rateio" src="../../imagens/btn_Adicionar_verde_pequeno.png" border="0px;"/>
                                                        </a>
                                                        <span class="previa">[${rateioP.previa} itens associados]</span>
                                                    </td>
                                                </tr>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:if>
                                    <!-- ------------------- detalhamento do rateio ------------------------- -->
                                    <c:if test="${rateioP.detalhamento}">
                                        <c:set var="noPai" value="${fn:substring(rateioP.codigoAgrupador,0, fn:length(rateioP.codigoAgrupador) -4)}" scope="request" />

                                        <tr bgcolor="#FFFFFF" id="${rateioP.codigoNode}"
                                            class="child-of-${fn:replace(noPai,'.', '') + 0 }">
                                            <td>
                                                <table><tr> <td valign="top">
                                                            <!--  PLANO DE CONTAS -->
                                                            <table>
                                                                <tr>
                                                                    <td class="titulotexto">Plano Conta</td>
                                                                    <td class="titulotexto">%</td>
                                                                </tr>
                                                                <c:forEach items="${rateioP.rateiosPlano}" var="previaRateio">
                                                                    <tr>
                                                                        <td class="texto">${previaRateio.nomePlano}</td>
                                                                        <td class="texto">${previaRateio.percentagem}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                            </table>
                                                        </td><td  valign="top">
                                                            <!--  CENTRO DE CUSTOS -->
                                                            <table>
                                                                <tr>
                                                                    <td class="titulotexto">Centro Custo</td>
                                                                    <td class="titulotexto">%</td>
                                                                </tr>
                                                                <c:forEach items="${rateioP.rateiosCentro}" var="previaRateio">
                                                                    <tr>
                                                                        <td class="texto">${previaRateio.nomeCentro}</td>
                                                                        <td class="texto">${previaRateio.percentagem}</td>
                                                                    </tr>
                                                                </c:forEach>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </c:if>
                                </c:forEach>
                            </tbody>
                        </table>
                    </h:panelGroup>
                </h:panelGrid>
            </rich:panel>
        </rich:tab>
    </rich:tabPanel>

    <a4j:commandButton action="#{RateioIntegracaoControle.selecionarRateio}"
                       id="chamaModalRateio"
                       reRender="modalIncluirRateio"
                      style="visibility: hidden;">
    </a4j:commandButton>

    <a4j:commandButton action="#{RateioIntegracaoControle.selecionarRateio}"
                       id="atualizaTree"
                       reRender="panelDfTreeView,panelDfTreeViewModalidades"
                       style="visibility: hidden;"
                       oncomplete="atualizarTreeViewRateio();">
    </a4j:commandButton>

    <h:inputHidden id="idEntidade" value="#{RateioIntegracaoControle.idEntidadeSelecionada}" />
    <h:inputHidden id="idTipo" value="#{RateioIntegracaoControle.idTipoSelecionado}" />
    <h:inputHidden id="idNome" value="#{RateioIntegracaoControle.nomeSelecionado}" />
    <h:inputHidden id="abaSelecionada" value="#{RateioIntegracaoControle.abaSelecionada}" />

</h:form>

<%-- Fim da Página  --%>

<%@include file="/pages/finan/includes/include_modalRateios.jsp" %>
<%@include file="/pages/finan/includes/include_modalSelecaoPlanoConta.jsp" %>
<%@include file="/pages/finan/includes/include_modalSelecaoCentroCusto.jsp" %>


<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_NivelTurma_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_NivelTurma_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-niveis-para-serem-utilizados-nas-turmas/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText   value="#{msg_aplic.prt_NivelTurma_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" styleClass="camposSomenteLeitura" onblur="blurinput(this);"  onfocus="focusinput(this);" size="10" maxlength="10" readonly="true"  value="#{NivelTurmaControle.nivelTurmaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_NivelTurma_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" size="45" maxlength="45"  value="#{NivelTurmaControle.nivelTurmaVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                        <h:outputText  rendered="#{NivelTurmaControle.mgb}"  value="#{msg_aplic.prt_Cadastro_label_codigomgb}" />
                        <h:panelGroup id="cidade" styleClass="cb-container margenVertical tamanhoInputMedio required cidadeid"
                                      layout="block"
                                      rendered="#{NivelTurmaControle.mgb}">
                            <h:selectOneMenu  id="codigoMgb" onblur="blurinput(this);"

                                              onfocus="focusinput(this);"
                                              styleClass="form"
                                              value="#{NivelTurmaControle.nivelTurmaVO.codigoMgb}" >
                                <f:selectItems  value="#{NivelTurmaControle.listaSelectItemCodigoMgb}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton id="icNivelSuc" rendered="#{NivelTurmaControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton id="icNivelFal" rendered="#{NivelTurmaControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgNivel" styleClass="mensagem"  value="#{NivelTurmaControle.mensagem}"/>
                            <h:outputText id="msgNivelDet" styleClass="mensagemDetalhada" value="#{NivelTurmaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{NivelTurmaControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="salvar" action="#{NivelTurmaControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{NivelTurmaControle.msgAlert}" action="#{NivelTurmaControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>
                            <a4j:commandButton id="consultar" immediate="true" action="#{NivelTurmaControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}"  alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink action="#{NivelTurmaControle.realizarConsultaLogObjetoSelecionado}"
                                           reRender="form"
                                           oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                           title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                        <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>

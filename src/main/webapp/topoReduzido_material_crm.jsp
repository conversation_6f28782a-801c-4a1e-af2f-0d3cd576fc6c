<%--
  Created by IntelliJ IDEA.
  User: hellison
  Date: 24/02/2016
  Time: 16:57
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="from" value="popup" scope="request" />
<link href="${root}/css/menu_zw_ui_v1.20.css" rel="stylesheet" type="text/css">
<link href="${root}/beta/css/pacto-icon-font4.0.min.css" type="text/css" rel="stylesheet"/>
<link rel="icon" type="image/png" href=".${LoginControle.favIconModule}"/>
<style>
    .container {
        height: 82px;
        text-align: center;
        font: 0/0 a;
        /*width: 170px;*/
    }

    /*centralizar a imagem verticalmente*/
    .container:before {
        content: ' ';
        display: inline-block;
        vertical-align: middle;
        height: 100%;
    }

    .element {
        display: inline-block;
        vertical-align: middle; /* vertical alignment of the inline element */
        font: 16px/1 Arial sans-serif; /* <-- reset the font property */
    }

    .fa-external-link-square:hover {
        color: #29abe2 !important;
    }

    .element {
        width: 100%;
    }

    .image-logo {
        float: left;
        margin-left: 20px;
        text-align: center;
    }

    .btnUCP {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        background-color: #F06D29;
        box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        color: rgb(255, 255, 255) !important;
        cursor: pointer;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        line-height: 0;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0px -1px 0px;
        border-radius: 10px;
        padding: 10px 10px 10px 10px;
        position: relative;
        float: right;
        margin-right: 15px;
        margin-top: 15px;
        right: 35px;
    }

    #gpt-iframe-button {
        padding-right: 16px;
    }
    #gpt-iframe-button:hover {
        color: #387edb;
    }
    .iframe-container {
        position: fixed;
        bottom: 40px;
        right: 65px;
        width: 397px;
        height: 562px;
        z-index: 1000;
        border-radius: 25px;
        box-shadow: 0 0 16px rgba(0, 0, 0, 0.5);
    }
    .iframe-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 25px;
    }
    #id-baloon-ponto-interrogacao {
        left: calc(100vw - 495px)!important;
        top: -60px!important;
    }
    .ponto-interrogacao {
        max-height: 485px!important;
    }

    .videos-conhecimento-title {
        color: #55585E;
    }

    .videos-conhecimento-title,
    .ponto-interrogacao .video div .title,
    .ponto-interrogacao .video div .subtitle,
    .chat-max-gpt-content .title,
    .chat-max-gpt-content .text span,
    .ponto-interrogacao .aprendizado .title,
    .ponto-interrogacao .aprendizado .link-ui a,
    .ponto-interrogacao .precisa-ajuda .link-ajuda a {
        font-family: system-ui !important;
    }

</style>

<jsp:include page="/include_ponto_interrogacao.jsp"/>

<div class="moduloCRM" style="height: 4px;"></div>
<div style="position: absolute;
    width: 100%;">
    <h2 style="text-align: center; padding-top: 20px; font-family: arial, sans-serif; font-size: 21px;">
        <c:out value="${titulo}"/>
        <c:choose>
            <c:when test="${empty titleWiki}">
                <a title="Clique e saiba mais:" target="_blank" href="${urlWiki}">
                    <i class="fa-icon-question-sign" style="color: #9e9e9e; font-size: 18px"></i>
                </a>
            </c:when>
            <c:otherwise>
                <a title="${titleWiki}" target="_blank" href="${urlWiki}">
                    <i class="fa-icon-question-sign" style="color: #9e9e9e; font-size: 18px"></i>
                </a>
            </c:otherwise>
        </c:choose>
    </h2>

    <div id="idpontointerrogacao" onclick="abrirPontoInterrogacao()" style="color:#1E60FA; cursor: pointer; position: absolute; top: 37px; right: 0px; margin-right: 4vw;">
        <a style="color:#1E60FA;">
            <i class="pct pct-help-circle"></i>
        </a>
    </div>

    <c:if test="${SuperControle.maxGpt}">
        <div id="iframe-container" class="iframe-container" style="display: none;">
            <iframe src="https://pgpt.pactosolucoes.com.br/?chat_open=True"></iframe>
        </div>
    </c:if>

</div>
<h:panelGroup layout="block" styleClass="row wrapper" style="height: 82px;">

    <c:if test="${!param.semTopoUCP}">
        <jsp:include page="include_topo_conhecimento_UCP_reduzido.jsp"/>
    </c:if>

    <h:panelGroup layout="block" styleClass="container">
        <h:panelGroup layout="block" styleClass="element">
            <h:graphicImage styleClass="image-logo" url="/imagens_flat/shortLogoCRM.png"/>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
<script>

    function abrirPontoInterrogacao() {
        try {
            let left = getOffset(document.getElementById('idpontointerrogacao')).left;
            document.getElementById("id-baloon-ponto-interrogacao").style.left = (left - 338) + 'px';
            document.getElementById('idpontointerrogacao').classList.add('topbar-item-selected');
        } catch (e) {
            console.log(e);
        }

        clearConhecimento();
        initConhecimento();
        jQuery('.modal-ponto-interrogacao').addClass('modal-open');
    }

    function fecharPonto() {
        jQuery('.modal-ponto-interrogacao').removeClass('modal-open');
        jQuery('.topbar-menu-item').removeClass('topbar-item-selected');
        clearConhecimento();
        jQuery('#conteudo-modal-mkt').html('');
    }

    jQuery(document).ready(function () {
        setTimeout(function () {
            jQuery('.searchbox-icon').click();
            jQuery('.searchbox-input').focus();
        }, 1000);
    })

</script>

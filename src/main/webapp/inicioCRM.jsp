<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td align="center" valign="top" style="padding: 7px 15px 0 20px;">
            <h:panelGrid columns="2" id="panelGridPrincipal" width="100%"
                         columnClasses="w50,w50">

                <h:panelGrid columns="2" width="100%" columnClasses="w50,w50">
                    <h:panelGrid columns="1" width="100%" style="clear:both;"
                                 cellpadding="0" cellspacing="0">
                        <a4j:include id="indicador1" viewId="#{DefinirLayoutControle.layout1}"/>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="clear:both;"
                                 cellpadding="0" cellspacing="0">
                        <a4j:include id="indicador2" viewId="#{DefinirLayoutControle.layout2}"/>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="clear:both;" rendered="#{LoginControle.apresentarLinkEstudio}"
                                 cellpadding="0" cellspacing="0">
                        <jsp:include flush="true" page="include_indicadorEstudio.jsp"/>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="clear:both;" cellpadding="0" cellspacing="0">
                        <a4j:include id="indicador4" viewId="#{DefinirLayoutControle.layout4}"/>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="clear:both;" cellpadding="0" cellspacing="0">
                        <a4j:include id="indicador5" viewId="#{DefinirLayoutControle.layout5}"/>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="clear:both;" cellpadding="0" cellspacing="0">
                        <a4j:include id="indicador6" viewId="#{DefinirLayoutControle.layout6}"/>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" style="clear:both;" cellpadding="0" cellspacing="0">
                        <a4j:include id="indicador7" viewId="#{DefinirLayoutControle.layout7}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </td>
    </tr>
</table>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script>
    function formataCampo(form, event){
        var formatacao = document.getElementById('form:tipoFormatacao');
        if(formatacao.value == 'CPF'){
            return mascara(form, 'form:cpfPortador', '999.999.999-99', event);
        }else
            return mascara(form, 'form:cpfPortador', '99.999.999/9999-99', event);
    }

    function trocaCampo(mostrar){
        var formatacao = document.getElementById('form:tipoFormatacao');
        var campoMostrar = document.getElementById('form:cpfPortador');
        campoMostrar.value= '';
        formatacao.value = mostrar;
    }
    

</script>
<style>
    .comboBandeirasCartao label{
        margin-left: 14px;
    }
    .comboBandeirasCartao input{
        position: absolute;
        margin-top: 10px;
    }
    .comboBandeirasCartao img{
        width: 50px;
    }
    .inputCartao input{
        margin-right: 10px;
    }
</style>

<a4j:outputPanel layout="block" id="panelPagamentoCartaoDebito" styleClass="formnovo">

    <h:panelGroup id="panelConteudoCartaoDebito">
        <!-- BANDEIRAS -->
            <h:panelGroup>
                <table align="center">
                    <tr>
                        <td>
                            <h:panelGrid rowClasses="linhaImpar, linhaPar" width="520px" columns="2">

                                <h:outputText style="font-weight:normal" value="Tipo D�bito Online"
                                              rendered="#{fn:length(PagamentoCartaoDebitoOnlineControle.listaTipoDebitoDisponiveis) > 1}"/>
                                <h:selectOneMenu id="comboTipoDebitoOnline"
                                                 rendered="#{fn:length(PagamentoCartaoDebitoOnlineControle.listaTipoDebitoDisponiveis) > 1}"
                                                 value="#{PagamentoCartaoDebitoOnlineControle.tipoDebitoOnline}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" style="padding-top: 0;">
                                    <f:selectItems value="#{PagamentoCartaoDebitoOnlineControle.listaTipoDebitoDisponiveis}" />
                                    <a4j:support event="onchange" reRender="form" action="#{PagamentoCartaoDebitoOnlineControle.acaoMudarTipoDebitoOnline}"/>
                                </h:selectOneMenu>

                                <h:outputText style="font-weight:normal" value="Bandeira" />
                                <h:selectOneRadio styleClass="comboBandeirasCartao" value="#{PagamentoCartaoDebitoOnlineControle.operadoraCartao}">
                                    <f:selectItems  value="#{PagamentoCartaoDebitoOnlineControle.operadorasCartaoDebito}"/>
                                    <a4j:support event="onchange" reRender="panelConteudoCartaoDebito" action="#{PagamentoCartaoDebitoOnlineControle.selecionaOperadora}"/>
                                </h:selectOneRadio>

                                <h:outputText style="font-weight:normal" value="N�mero do Cart�o" />
                                <h:panelGroup styleClass="inputCartao">
                                    <h:inputText  id="nrCartaoDebito1" value="#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.numero}"
                                                  size="4" tabindex="2"
                                                  onkeyup="tabAutom(this)"
                                                  onkeypress="return mascara(this.form, this.id, '9999999999999999999', event);"
                                                  styleClass="form" onfocus="focusinput(this);"
                                                  onblur="blurinput(this);" maxlength="4"/>

                                </h:panelGroup>



                                <!-- NOME DO TITULAR -->
                                <h:outputText style="font-weight:normal" value="Nome do Titular" />
                                <h:inputText id="nomeTitularCartaoDebito" value="#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.nomeTitular}"
                                             size="50" styleClass="form"
                                             onkeyup="tabAutom(this)"
                                             tabindex="3" onfocus="focusinput(this);" onblur="blurinput(this);"
                                             maxlength="50">
                                    <rich:toolTip followMouse="true" direction="top-right" style="width:150px; height:70px; " showDelay="200">
                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{msg_aplic.prt_pagamentoCartaoCredito_nomeTitular}" />
                                    </rich:toolTip>
                                </h:inputText>

                                <!-- CODIGO SEGURANCA -->
                                <h:outputText style="font-weight:normal" value="C�digo de seguran�a" />
                                <h:panelGroup>
                                    <h:inputText id="codSegurancaCartaoDebito" value="#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.codigoSeguranca}"
                                                 onkeypress="return mascara(this.form, this.id, '9999', event);"
                                                 tabindex="4"
                                                 onkeyup="tabAutom(this)"
                                                 size="4" styleClass="form"
                                                 onfocus="focusinput(this);" onblur="blurinput(this);"
                                                 maxlength="4">

                                    </h:inputText>

                                    <h:panelGroup rendered="#{!empty PagamentoCartaoDebitoOnlineControle.dadosPagamento.band}">
                                        <h:graphicImage id="botaoHelpCartaoDebito" style="padding-left: 5px; vertical-align: middle;border:none;cursor:pointer" value="images/help.png">
                                            <rich:toolTip for="botaoHelp">
                                                <h:panelGrid width="189" columns="2">
                                                    <rich:panel rendered="#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.band.id != 4}"
                                                                header="#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.band.descricao}">
                                                        <h:graphicImage width="189" value="images/cartao_visa_cvc2.gif"/>
                                                        <h:outputText value="O c�digo de seguran�a est� localizado no verso do cart�o e corresponde aos tr�s �ltimos d�gitos da faixa num�rica."/>
                                                    </rich:panel>
                                                    <rich:panel rendered="#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.band.id == 4}" header="American Express">
                                                        <h:graphicImage width="189" value="images/cartao_amex_cvc2.gif"/>
                                                        <h:outputText value="O c�digo de seguran�a est� localizado na parte frontal do cart�o American Express e corresponde aos quatro d�gitos localizados do lado direito acima da faixa num�rica do cart�o."/>
                                                    </rich:panel>
                                                </h:panelGrid>
                                            </rich:toolTip>
                                        </h:graphicImage>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <!-- VALIDADE -->
                                <h:outputText style="font-weight:normal" value="Validade" />
                                <h:inputText id="anoValidadeCartaoDebito" value="#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.validade}"
                                             tabindex="6"
                                             onkeyup="tabAutom(this)"
                                             onkeypress="return mascara(this.form, this.id, '99/9999', event);"
                                             size="7" styleClass="form" onfocus="focusinput(this);" onblur="blurinput(this);"
                                             maxlength="7">
                                    <rich:toolTip followMouse="true" direction="top-right" style="width:150px; height:70px; " showDelay="200">
                                        <h:outputText styleClass="tituloCampos"
                                                      value="#{msg_aplic.prt_pagamentoCartaoCredito_validade}" />
                                    </rich:toolTip>
                                </h:inputText>


                            </h:panelGrid>

                        </td>

                        <td>

                            <a4j:commandLink action="#{PagamentoCartaoDebitoOnlineControle.alterarBandeira}"
                                             reRender="panelConteudoCartaoDebito">
                                <h:graphicImage id="imgBandeiraDebitoSelecionada" style="border:none;" title="Selecionado \"#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.band.descricao}\" [Clique para alterar]"
                                                url="/imagens/bandeiras/#{PagamentoCartaoDebitoOnlineControle.dadosPagamento.band.imagem}.png"
                                                rendered="#{!empty PagamentoCartaoDebitoOnlineControle.dadosPagamento.band}"/>
                            </a4j:commandLink>


                        </td>
                    </tr>
                </table>


                <div class="sep" style="margin:4px 0 5px 0;"><img src="../images/shim.gif"></div>
                    <a4j:commandLink rendered="#{MovPagamentoControle.movPagamentoVO.usarPagamentoDebitoOnline}"
                                     tabindex="13"
                                     action="#{MovPagamentoControle.escolherOutraFormaPagamento}"
                                     reRender="form"
                                     oncomplete="montaModulomenu();"
                                     value="Alterar forma de pagamento"/>

                <!-- ------------------------ PAINEL DE MENSAGENS -------------------------------- -->

                <table width="100%">
                    <tr>
                        <td>
                            <h:panelGrid  id="mensagensCartaoDebito"  >
                                <h:outputText id="msgFormasPagCartaoDebito" styleClass="mensagem" value="#{PagamentoCartaoDebitoOnlineControle.mensagem}"/>
                                <h:outputText id="msgFormasPagDetCartaoDebito" styleClass="mensagemDetalhada" value="#{PagamentoCartaoDebitoOnlineControle.mensagemDetalhada}" />
                            </h:panelGrid>
                        </td>
                    </tr>
                </table>
            </h:panelGroup>
    </h:panelGroup>

    <h:commandLink id="confirmarTransacaoDebitoOnline"
                   style="display: none;"
                   action="#{PagamentoCartaoDebitoOnlineControle.consultarSituacaoPagamentoDebito}"/>
</a4j:outputPanel>


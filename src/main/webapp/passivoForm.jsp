<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />


<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title><h:outputText value="#{msg_aplic.prt_Passivo_tituloForm}" /></title>

    <rich:modalPanel id="panelAgenda" autosized="true" shadowOpacity="true" width="500" height="250" onshow="document.getElementById('formAgenda:opcoesTipoAgendamento').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Agenda_escolhaAgendamento}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAgenda" />
                <rich:componentControl for="panelAgenda" attachTo="hiperlinkAgenda" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formAgenda" ajaxSubmit="true">
            <h:panelGrid columns="1" rowClasses="linhaImpar" columnClasses="colunaAlinhamento" width="100%" style="border:1px solid black;">
                <h:panelGrid width="100%" columns="2">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_tipoAgendamento}" />
                    <h:selectOneRadio id="opcoesTipoAgendamento" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{PassivoControle.historicoContatoVO.agendaVO.tipoAgendamento}">
                        <f:selectItems value="#{HistoricoContatoControle.listaSelectItemTipoAgendamentoAgenda}" />
                        <a4j:support event="onclick" reRender="formAgenda:panelTipoAgendamento" />
                    </h:selectOneRadio>
                </h:panelGrid>
                <h:panelGroup id="panelTipoAgendamento">
                    <h:panelGroup id="panelModalidade" rendered="#{PassivoControle.apresentarInputTextAulaExperimental}">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_modalidade}" />
                        <rich:spacer width="5px" />
                        <h:inputText id="textModalidade" size="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{PassivoControle.historicoContatoVO.agendaVO.modalidade.nome}"  />
                        <rich:suggestionbox height="200" width="200" for="textModalidade" fetchValue="#{result.nome}" suggestionAction="#{PassivoControle.autocompleteModalidade}" minChars="3" rowClasses="20" nothingLabel="Nenhuma Modalidade encontrada !" var="result" id="suggestionResponsavel" status="1">

                            <h:column>
                                <h:outputText value="#{result.nome}" />
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>
                    <h:panelGroup>
                        <a4j:outputPanel layout="block">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_data}" />
                            <rich:spacer width="5" />
                            <rich:calendar id="modaldataAgendamento" oninputchange="return validar_Data(this.id);" value="#{PassivoControle.historicoContatoVO.agendaVO.dataAgendamento}" enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy" showApplyButton="false" cellWidth="24px" cellHeight="24px" style="width:200px" inputClass="campos" showFooter="false" />

                            <rich:spacer width="20" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_hora}" />
                            <rich:spacer width="5" />
                            <h:selectOneMenu id="selectHoras" value="#{PassivoControle.historicoContatoVO.agendaVO.hora}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{HistoricoContatoControle.listaSelectItemHoras}" />
                            </h:selectOneMenu>
                            <rich:spacer width="20" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_minuto}" />
                            <rich:spacer width="5" />
                            <h:selectOneMenu id="selectMinutos"  value="#{PassivoControle.historicoContatoVO.agendaVO.minuto}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{HistoricoContatoControle.listaSelectItemMinutos}" />
                            </h:selectOneMenu>
                        </a4j:outputPanel>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="1" columnClasses="colunaDireita" width="100%">
                <h:panelGroup>
                    <a4j:commandButton id="btnOK" reRender="form, formAgenda" action="#{PassivoControle.gravarPassivoAgendamento}" oncomplete="#{PassivoControle.manterAbertoRichModalPanelAgenda}" styleClass="botoes" image="./imagensCRM/botaoGravar.png" />
                    <rich:spacer width="10" />
                    <a4j:commandButton id="btnCancelar" reRender="form, formAgenda" action="#{PassivoControle.cancelarAgendamentoPanelAgenda}" oncomplete="Richfaces.hideModalPanel('panelAgenda')" styleClass="botoes" image="./imagensCRM/botaoCancelar.png" />
                </h:panelGroup>
            </h:panelGrid>


            <h:panelGrid id="panelGridMensagensAgendamento" columns="1" width="100%">
                <h:outputText styleClass="mensagem" value="#{PassivoControle.mensagem}" />
                <h:outputText styleClass="mensagemDetalhada" value="#{PassivoControle.mensagemDetalhada}" />
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelObjecao" autosized="true" shadowOpacity="true" width="400" height="300" onshow="document.getElementById('formModeloMensagem:consultarObjecao').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Objecao_consultarObjecao}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkObjecao" />
                <rich:componentControl for="panelObjecao" attachTo="hiperlinkObjecao" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formObjecao" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <rich:dataTable id="resultadoConsultaObjecao" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" value="#{PassivoControle.listaConsultaObjecao}" rows="10" var="objecao">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Objecao_codigo}" />
                        </f:facet>
                        <h:outputText value="#{objecao.codigo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Objecao_descricao}" />
                        </f:facet>
                        <h:outputText value="#{objecao.descricao}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Objecao_grupo}" />
                        </f:facet>
                        <h:outputText value="#{objecao.grupo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <a4j:commandButton id="btnSelecionarObjecao" action="#{PassivoControle.gravarPassivoObjecao}" focus="objecao" reRender="form" title="Gravar Passivo com Objeção" oncomplete="#{PassivoControle.fecharRichModalPanelObjecao}" styleClass="botoes" image="./imagensCRM/botaoEditar.png" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formObjecao:resultadoConsultaObjecao" maxPages="10" id="scResultadoObjecao" />
                <h:panelGrid id="mensagemConsultaObjecao" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgPassivo" styleClass="mensagem" value="#{PassivoControle.mensagem}" />
                        <h:outputText id="msgPassivoDet" styleClass="mensagemDetalhada" value="#{PassivoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp" />
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{PassivoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:panelGroup>
                        <h:outputText styleClass="tituloFormulario" value=" #{msg_aplic.prt_Passivo_tituloForm}" />
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-cadastrar-receptivos-no-modulo-crm/"
                                      title="Clique e saiba mais: Receptivo" target="_blank">
                            <i class="fa-icon-question-sign linkWiki" style="font-size: 18px; margin-left: 0.2em"></i>
                        </h:outputLink>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  rendered="#{PassivoControle.passivoVO.responsavelCadastro.administrador}"  styleClass="tituloCampos" value="#{msg_aplic.prt_Colaborador_empresa}" />
                    <h:panelGroup rendered="#{PassivoControle.passivoVO.responsavelCadastro.administrador}">
                        <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{PassivoControle.passivoVO.empresaVO.codigo}" >
                            <f:selectItem itemValue="" itemLabel="-- Selecione --"/>
                            <f:selectItems  value="#{PassivoControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Passivo_nome}" />
                    <h:panelGroup>
                        <h:inputText id="nome" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{PassivoControle.passivoVO.nome}" />
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Passivo_telefoneResidencial}" />
                    <h:inputText id="telefoneResidencial" size="13"
                                 maxlength="13"
                                 onchange="return validar_Telefone(this.id);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form" value="#{PassivoControle.passivoVO.telefoneResidencial}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Passivo_telefoneCelular}" />
                    <h:inputText id="telefoneCelular" size="13"
                                 maxlength="13"
                                 onchange="return validar_Telefone(this.id);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form" value="#{PassivoControle.passivoVO.telefoneCelular}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Passivo_telefoneTrabalho}" />
                    <h:inputText id="telefoneTrabalho" size="13"
                                 maxlength="13"
                                 onchange="return validar_Telefone(this.id);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form" value="#{PassivoControle.passivoVO.telefoneTrabalho}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Passivo_email}" />
                    <h:inputText id="email" value="#{PassivoControle.passivoVO.email}" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" />
                    <h:outputText rendered="#{PassivoControle.passivoVO.lead}" styleClass="tituloCampos" value="Url RD" />
                    <h:outputLink  rendered="#{PassivoControle.passivoVO.lead}" value="#{PassivoControle.passivoVO.leadVO.urlRD}" target="_blank"
                              ><h:outputText style="padding-left: 5px; font-size: 14px;font-family: arial, helvetica, sans-serif;"
                                  value="#{PassivoControle.passivoVO.leadVO.urlRD}"/>
                    </h:outputLink>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_evento}" />
                    <h:panelGroup id="panelEvento">
                        <h:inputText id="textEvento" size="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{PassivoControle.passivoVO.evento.descricao}" />
                        <rich:suggestionbox height="200" width="200" for="textEvento" fetchValue="#{result.descricao}" suggestionAction="#{PassivoControle.autocompleteEvento}" minChars="1" rowClasses="20" nothingLabel="Nenhum Evento encontrado !" var="result" id="suggestionEvento" status="1">
                            <h:column>
                                <h:outputText value="#{result.descricao}" />
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_comentario}" />
                    <h:inputTextarea style="width: 530px; margin-bottom: 5px;" rows="5"
                                     id="comentario"
                                     value="#{PassivoControle.passivoVO.observacao}"/>
                </h:panelGrid>

                <h:panelGrid id="panelMensagensBotoes" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" " />
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{PassivoControle.sucesso}" image="./imagensCRM/sucesso.png" />
                        <h:commandButton rendered="#{PassivoControle.erro}" image="./imagensCRM/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgPassivoRo" styleClass="mensagem" value="#{PassivoControle.mensagem}" />
                            <h:outputText id="msgPassivoRoDet" styleClass="mensagemDetalhada" value="#{PassivoControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="gravar" rendered="#{PassivoControle.somenteEditarPassivo}" value="#{msg_bt.btn_gravar}"
                                               styleClass="botoes nvoBt" action="#{PassivoControle.gravar}"
                                               oncomplete="#{PassivoControle.msgAlert}" reRender="form" />

                            <rich:spacer width="10" />

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{PassivoControle.msgAlert}" action="#{PassivoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <rich:spacer width="10" />
                            <h:commandButton id="consultar" immediate="true" action="#{PassivoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec" />

                            <rich:spacer width="10" />
                            <a4j:commandLink id="logReceptivoCRM"
                                             action="#{PassivoControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             title="Visualizar Log" style="padding: 6px 12px;position: relative;top: 10px;left: 9px;"
                                             styleClass="pure-button">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>

<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
    .btnsOptAPP{
        margin-left: 10px;
        float: left;
        display: none;
    }

</style>
<script type="text/javascript" src="script/scriptSMS.js"></script>

<script type="text/javascript" >
    function marcarTelefone(selecionado) {
        //esta funcao tem por objetivo permitir que apenas uma check box seja marcada,
        //ou seja, só permitir que um telefone seja escolhido para o envio do sms
        var controle = true;
        var cont = 0;
        var marcado = selecionado.checked;

        while (controle) {
            var id = 'formF:resTelefone:' + cont + ':telSelecionado';
            var check = document.getElementById(id);
            if (check != null) {
                check.checked = 0;
            } else {
                controle = false;
            }
            cont = cont + 1;
        }
        selecionado.checked = marcado;
    }
    function validarComentario(mensagem) {

        var textoComentario = document.getElementById('formF:comentarioRichEditorTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
        var validade = true;
        if (textoComentario == null || textoComentario == '<br mce_bogus="1">') {
            validade = false;
            alert(mensagem);
        }
        return validade;
    }
    function mostrarProximo(item) {
        jQuery('.labelOp' + (item + 1)).show();
        jQuery('.btnOp' + item).hide();
        jQuery('.btnOp' + (item + 1)).show();
    }
    function sumirItem(item) {
        jQuery('.labelOp' + item).hide();
        jQuery('.form.labelOp' + item).val('');
        jQuery('.btnOp' + (item - 1)).show();
        jQuery('.btnOp' + item).hide();
    }
</script>
<f:view>
    <title><h:outputText value="#{msg_aplic.prt_Agenda_tituloForm}" /></title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <rich:modalPanel id="panelContrato" autosized="true" shadowOpacity="true" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados do contrato Vigente"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink5" />
                <rich:componentControl for="panelContrato" attachTo="hidelink5" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formPanelContrato" ajaxSubmit="true">
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <rich:dataTable width="440px" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" var=""  value="">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição" />
                        </f:facet>
                        <h:outputText styleClass="campos" value="#{HistoricoContatoControle.contratoVigente.descricaoResumida}" />
                    </rich:column>
                    <rich:column >
                        <f:facet name="header">
                            <h:outputText value="Opções" />
                        </f:facet>
                        <a4j:commandButton action="#{HistoricoContatoControle.addURLParaRenovacaoRematricula}"
                                           reRender="formF:panelEmailIndicado,formF:panelEmailPassivo,formF:panelEmailCliente,formF:panelGridMensagens"
                                           oncomplete="Richfaces.hideModalPanel('panelContrato');"
                                           image="./imagens/botaoAlteracaoIncluir.gif"
                                           value="Adicionar" styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>



    <rich:modalPanel id="panelEmail" autosized="true" shadowOpacity="true" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores de Email"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink4" />
                <rich:componentControl for="panelEmail" attachTo="hidelink4" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formMarcadorEmail" ajaxSubmit="true">
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <rich:dataTable id="MarcadoEmail" width="440px" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" var="marcadorEmail" rows="40" value="#{HistoricoContatoControle.listaSelectItemMarcadoEmail}">
                    <f:facet name="header">
                        <h:outputText value="Email" />
                    </f:facet>
                    <rich:column width="170px">
                        <f:facet name="header">
                            <h:outputText value="Tags" />
                        </f:facet>
                        <h:outputText styleClass="campos" value="#{marcadorEmail.nome}" />
                    </rich:column>
                    <rich:column width="240px">
                        <f:facet name="header">
                            <h:outputText value="Opções" />
                        </f:facet>
                        <a4j:commandButton action="#{HistoricoContatoControle.executarInsercaoTag}"
                                           reRender="formF:panelEmailIndicado,formF:panelEmailPassivo,formF:panelEmailCliente,formF:panelGridMensagens"
                                           oncomplete="Richfaces.hideModalPanel('panelEmail');" image="./imagens/botaoAlteracaoIncluir.gif"
                                           value="Adicionar" styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelAgenda" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Agenda_escolhaAgendamento}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <%--<h:panelGroup>
                                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAgenda" />
                                <rich:componentControl for="panelAgenda" attachTo="hiperlinkAgenda"	operation="hide" event="onclick" >
                                        <a4j:support reRender="form:panelGridMensagens" action="#{HistoricoContatoControle.adicionarMensagemErro}" />
                                </rich:componentControl>
                        </h:panelGroup>--%>
        </f:facet>

        <a4j:form id="formAgenda">

            <h:panelGrid columns="1" rowClasses="linhaImpar" columnClasses="colunaAlinhamento" width="100%" style="border:1px solid black;">
                <h:panelGrid width="100%" columns="2">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_tipoAgendamento}" />
                    <rich:spacer width="5px" />
                    <h:selectOneRadio id="opcoesTipoAgendamento" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" style="border: none;"
                                      value="#{HistoricoContatoControle.agendaVO.tipoAgendamento}">
                        <f:selectItems value="#{HistoricoContatoControle.listaSelectItemTipoAgendamentoAgenda}" />
                        <a4j:support event="onclick" reRender="formAgenda:panelTipoAgendamento" />
                    </h:selectOneRadio>
                </h:panelGrid>
                <h:panelGroup id="panelTipoAgendamento">
                    <h:panelGroup id="panelModalidade" rendered="#{HistoricoContatoControle.apresentarInputTextAulaExperimental}">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_modalidade}" />
                        <rich:spacer width="5px" />
                        <h:inputText id="textModalidade" size="50"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{HistoricoContatoControle.agendaVO.modalidade.nome}" />

                        <rich:suggestionbox   height="200" width="200"
                                              for="textModalidade"
                                              status="statusInComponent"
                                              immediate="true"
                                              suggestionAction="#{HistoricoContatoControle.autocompleteModalidade}"
                                              nothingLabel="Nenhuma Modalidade encontrada !" var="result" id="suggestionResponsavel">
                            <h:column>
                                <h:outputText value="#{result.nome}" />
                            </h:column>
                        </rich:suggestionbox>

                    </h:panelGroup>

                    <h:panelGroup>
                        <a4j:outputPanel layout="block">
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_data}" />
                            <rich:spacer width="5" />

                            <rich:calendar id="modaldataAgendamento"
                                           value="#{HistoricoContatoControle.agendaVO.dataAgendamento}"
                                           inputSize="8"
                                           inputClass="form"
                                           oninputblur="blurinput(this);document.getElementById('formF:atualizaCompetencia').click();"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           showWeeksBar="false" />
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                            <rich:spacer width="20" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_hora}" />
                            <rich:spacer width="5" />
                            <h:selectOneMenu id="selectHora" value="#{HistoricoContatoControle.agendaVO.hora}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{HistoricoContatoControle.listaSelectItemHoras}" />
                            </h:selectOneMenu>
                            <rich:spacer width="20" />
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_minuto}" />
                            <rich:spacer width="5" />
                            <h:selectOneMenu id="selectMinutos" value="#{HistoricoContatoControle.agendaVO.minuto}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems value="#{HistoricoContatoControle.listaSelectItemMinutos}" />
                            </h:selectOneMenu>
                        </a4j:outputPanel>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columns="1" columnClasses="colunaDireita" width="100%">
                <h:panelGroup>
                    <a4j:commandButton id="btnGrvar"
                                       reRender="formF, formAgenda"
                                       action="#{HistoricoContatoControle.gravarAgendaHistoricoContato}"
                                       oncomplete="#{HistoricoContatoControle.manterAbertoRichModalPanelAgenda};recarregarMetas();"
                                       styleClass="botoes" image="./imagensCRM/botaoGravar.png" />
                    <rich:spacer width="10" />
                    <a4j:commandButton id="btnCancelar" reRender="formF" action="#{HistoricoContatoControle.cancelarAgendamentoPanelAgenda}"
                                       oncomplete="Richfaces.hideModalPanel('panelAgenda');" styleClass="botoes" image="./imagensCRM/botaoCancelar.png" />
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="panelGridMensagensAgendamento" columns="1" width="100%">
                <h:outputText id="msgPanelAgendamento"	 styleClass="mensagem" value="#{HistoricoContatoControle.mensagem}" />
                <h:outputText id="msgPanelAgendamentoDet" styleClass="mensagemDetalhada" value="#{HistoricoContatoControle.mensagemDetalhada}" />
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>



    <%--Email--%>
    <%--Panel Modelo Mensagem--%>

    <rich:modalPanel id="panelModeloMensagem" autosized="true" shadowOpacity="true" width="450" height="300" onshow="document.getElementById('formModeloMensagem:consultarModeloMensagem').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkModeloMensagem" />
                <rich:componentControl for="panelModeloMensagem" attachTo="hiperlinkModeloMensagem" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formModeloMensagem" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}" />
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" id="consultarModeloMensagem" value="#{HistoricoContatoControle.campoConsultarModeloMensagem}">
                        <f:selectItems value="#{HistoricoContatoControle.tipoConsultarComboModeloMensagem}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarModeloMensagem" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.valorConsultarModeloMensagem}" />
                    <a4j:commandButton id="btnConsultarModeloMensagem"
                                       reRender="formModeloMensagem:consultarModeloMensagem, formModeloMensagem:resultadoConsultaModeloMensagem , formModeloMensagem:scResultadoModeloMensagem , formModeloMensagem, panelGridMensagens"
                                       action="#{HistoricoContatoControle.consultarModeloMensagem}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaModeloMensagem" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" value="#{HistoricoContatoControle.listaConsultarModeloMensagem}" rows="10" var="modeloMensagem">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_codigo}" />
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.codigo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_titulo}" />
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.titulo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <a4j:commandButton action="#{HistoricoContatoControle.selecionarModeloMensagem}"
                                           focus="modeloMensagem"
                                           reRender="formF, panelEmail, resultadoConsultaModeloMensagem, comentarioTextArea, comentarioTextAreaapp"
                                           oncomplete="Richfaces.hideModalPanel('panelModeloMensagem')"
                                           value="#{msg_bt.btn_selecionar}" styleClass="botoes"
                                           image="./imagensCRM/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModeloMensagem:resultadoConsultaModeloMensagem" maxPages="10" id="scResultadoModeloMensagem" />
                <h:panelGrid id="mensagemConsultaModeloMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{HistoricoContatoControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoContatoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>





    <%-- OBJECAO --%>

    <rich:modalPanel id="panelObjecao" autosized="true" shadowOpacity="true" width="400" height="250" onshow="document.getElementById('formModeloMensagem:consultarObjecao').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Objecao_consultarObjecao}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <%--<h:panelGroup>
                                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkObjecao" />
                                <rich:componentControl for="panelObjecao" attachTo="hiperlinkObjecao" operation="hide" event="onclick" >
                                        <a4j:support event="onclick" reRender="form:panelGridMensagens" action="#{HistoricoContatoControle.adicionarMensagemErro}" />
                                </rich:componentControl>
                        </h:panelGroup>--%>
        </f:facet>
        <a4j:form id="formObjecao" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <rich:dataTable id="resultadoConsultaObjecao" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" value="#{HistoricoContatoControle.listaConsultaObjecao}" rows="10" var="objecao">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Objecao_codigo}" />
                        </f:facet>
                        <h:outputText value="#{objecao.codigo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Objecao_descricao}" />
                        </f:facet>
                        <h:outputText value="#{objecao.descricao}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Objecao_grupo}" />
                        </f:facet>
                        <h:outputText value="#{objecao.grupo}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" />
                        </f:facet>
                        <a4j:commandButton id="btnGravar" reRender="form, formAgenda" title="Após clicar a Objeção será gravada !"
                                           action="#{HistoricoContatoControle.gravarObjecaoHistoricoContato}"
                                           oncomplete="#{HistoricoContatoControle.fecharRichModalPanelObjecao};recarregarMetas()"
                                           styleClass="botoes" image="./imagensCRM/disk.png" value="#{msg_bt.btn_gravar}" />

                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formObjecao:resultadoConsultaObjecao" maxPages="10" id="scResultadoObjecao" />
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaDireita">
                <a4j:commandButton id="btnCancelarObjecao" reRender="form:panelGridMensagens"
                                   action="#{HistoricoContatoControle.cancelarObjecaoPanelObjecao}"
                                   oncomplete="#{HistoricoContatoControle.fecharRichModalPanelObjecao}"
                                   styleClass="botoes" image="./imagensCRM/botaoCancelar.png" />
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelSimplesRegistro" autosized="true" shadowOpacity="true" width="400" height="150" onshow="document.getElementById('formSimplesRegistro:').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Agenda_SimplesRegistro}" />
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkSimplesRegistro" />
                <rich:componentControl for="panelSimplesRegistro" attachTo="hiperlinkSimplesRegistro" operation="hide" event="onclick">
                    <a4j:support event="onclick" reRender="form:panelGridMensagens" action="#{HistoricoContatoControle.adicionarMensagemErro}" />
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formSimplesRegistro" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Agenda_gravarSimplesRegistro}" />
            </h:panelGrid>

            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:panelGroup>
                    <a4j:commandButton id="btnGravar" reRender="formF, formAgenda" action="#{HistoricoContatoControle.gravarSimplesRegistroHistoricoContato}"
                                       oncomplete="#{HistoricoContatoControle.fecharRichModalPanelSimplesRegistro};recarregarMetas();"
                                       styleClass="botoes" image="./imagensCRM/botaoGravar.png" value="#{msg_bt.btn_gravar}" />
                    <rich:spacer width="10" />
                    <a4j:commandButton id="btnCancelarSimplesRegistro" reRender="formF:panelGridMensagens"
                                       action="#{HistoricoContatoControle.cancelarSimplesRegistroPanelSimplesRegistro}"
                                       oncomplete="#{HistoricoContatoControle.fecharRichModalPanelSimplesRegistro}" styleClass="botoes"
                                       image="./imagensCRM/botaoCancelar.png" />
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" styleClass="tabForm" width="100%">

        <h:panelGrid columns="1" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_realizarContato}" />
        </h:panelGrid>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAgendamento" />
                <rich:componentControl for="panelAgendamento" attachTo="hiperlinkAgendamento" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <h:form id="formF">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">

                <h:panelGrid columns="1" width="100%" style="border:1px solid black">

                    <%-- DADOS PASSIVO (HISTORICO) --%>

                    <h:panelGrid id="dadosPassivo" columns="1" width="100%" style="border:1px solid black" columnClasses="colunaEsquerda" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoPassivo}">
                        <h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0" style="text-align: top;" width="100%">
                            <h:panelGrid columns="2" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_aluno}" />
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.passivoVO.nome}" />
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
                                    <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.passivoVO.dia_Apresentar}" />
                                </h:panelGrid>
                            </h:panelGrid>

                            <rich:spacer width="30px;" />

                            <h:panelGroup>
                                <h:panelGrid columns="5" width="100%">

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_diasUltAcesso}" />
                                        <h:outputText styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" value="#{HistoricoContatoControle.historicoContatoVO.diasUltAcesso_Apresentar}" />
                                        <rich:spacer height="15px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_ligacoes}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.totalLigacao}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdEmail}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdEmail}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdPessoal}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdPessoal}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdSMS}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdSMS}" />
                                    </h:panelGrid>

                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>


                    <%-- DADOS INDICADO (HISTORICO) --%>

                    <h:panelGrid id="dadosIndicado" columns="1" width="100%" style="border:1px solid black" columnClasses="colunaEsquerda" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoIndicado}">
                        <h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0" style="text-align: top;" width="100%">
                            <h:panelGrid columns="2" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_aluno}" />
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.indicadoVO.nomeIndicado}" />
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
                                    <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.indicadoVO.indicacaoVO.dia_Apresentar}" />
                                </h:panelGrid>
                            </h:panelGrid>

                            <rich:spacer width="30px;" />

                            <h:panelGroup>
                                <h:panelGrid columns="4" width="100%">

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_diasUltAcesso}" />
                                        <h:outputText styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" value="#{HistoricoContatoControle.historicoContatoVO.diasUltAcesso_Apresentar}" />
                                        <rich:spacer height="15px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_ligacoes}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.totalLigacao}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdEmail}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdEmail}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdPessoal}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdPessoal}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdSMS}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdSMS}" />
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>


                    <%-- DADOS CLIENTE (HISTORICO) --%>

                    <h:panelGrid id="dadosCliente" columns="2" width="100%" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoCliente}" style="border:1px solid black" columnClasses="colunaEsquerda">
                        <h:panelGrid columns="1" width="50px" cellpadding="0" cellspacing="0" columnClasses="colunaEsquerda">
                            <a4j:outputPanel id="panelFoto">
                                <a4j:mediaOutput element="img" id="imagem1" 
                                                 style="width:60px;height:80px" 
                                                 rendered="#{!SuperControle.fotosNaNuvem}"
                                                 cacheable="false" session="true" 
                                                 createContent="#{HistoricoContatoControle.paintFoto}" 
                                                 value="#{ImagemData}" mimeType="image/jpeg">
                                    <f:param value="#{SuperControle.timeStamp}" name="time" />
                                    <f:param name="largura" value="60"/>
                                    <f:param name="altura" value="80"/>
                                </a4j:mediaOutput>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                                width="60" height="80"
                                                style="width:60px;height:80px"
                                                url="#{HistoricoContatoControle.paintFotoDaNuvem}">                            
                                </h:graphicImage>
                                <h:panelGrid columns="1" rendered="#{HistoricoContatoControle.alunoMenorIdade}" columnClasses="colunaEsquerda" width="100%">

                                    <h:panelGroup>
                                        <h:outputText id="cmpNomeCliente" style="text-align: center" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.nome}" />
                                    </h:panelGroup>

                                </h:panelGrid>
                            </a4j:outputPanel>
                        </h:panelGrid>
                        <h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0"  width="100%">
                            <h:panelGrid columns="4" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGrid columns="1" rendered="#{HistoricoContatoControle.alunoMenorIdade == false}" columnClasses="colunaEsquerda" width="100%">
                                    <h:panelGroup>
                                        <h:outputText id="cmpRotuloNomeCliente" style="text-align: center" styleClass="camposAgenda" value="Nome" />
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText id="cmpNomeCliente2" style="text-align: center" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.nome}" />
                                    </h:panelGroup>
                                </h:panelGrid>

                                <h:panelGrid  rendered="#{HistoricoContatoControle.alunoMenorIdade}" style="margin-top: 10px;" columns="1" columnClasses="colunaCentralizada" >
                                    <h:outputText  styleClass="tituloCamposAgenda" value="#{HistoricoContatoControle.rotuloResponsavel}"/>
                                    <c:forEach items="#{HistoricoContatoControle.listaResponsaveis}" var="responsaveis">
                                        <h:outputText styleClass="camposAgenda"  value="#{responsaveis}"/>
                                    </c:forEach>
                                </h:panelGrid>

                                <h:panelGrid columns="1" width="100%"  style="margin-top: 0px; " columnClasses="colunaCentralizada">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_idade}" />
                                    <h:outputText id="cpmIdade" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.idade}" />
                                </h:panelGrid>
                                <h:panelGrid columns="1" width="100%" style="margin-top: 0px;" columnClasses="colunaCentralizada">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_estadoCivil}" />
                                    <h:outputText id="cpmEstadoCivil" styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.estadoCivil_Apresentar}" />
                                    <rich:spacer height="22px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}" />
                                </h:panelGrid>
                                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
                                    <h:outputText id="cpmDataCadastro" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.dataCadastro_Apresentar}" />
                                </h:panelGrid>
                            </h:panelGrid>

                            <rich:spacer width="30px;" />

                            <h:panelGroup>
                                <h:panelGrid columns="7" width="100%">

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_diasUltAcesso}" />
                                        <h:outputText id="cpmDiasUltimoAcesso" styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" value="#{HistoricoContatoControle.historicoContatoVO.nrDiasUltimoAcesso}" />
                                        <rich:spacer height="15px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" />
                                    </h:panelGrid>

                                    <h:panelGrid rendered="#{HistoricoContatoControle.historicoContatoVO.vencimentoContrato != null}" columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_vencimentoContrato}" />
                                        <h:outputText id="cpmVencimentoContrato" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.vencimentoContrato_Apresentar}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_ligacoes}" />
                                        <h:outputText id="cpmtotalLigacao" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.totalLigacao}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdEmail}" />
                                        <h:outputText id="cpmTotalEmail" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdEmail}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdPessoal}" />
                                        <h:outputText id="cpmTotalPessoal" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdPessoal}" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdSMS}" />
                                        <h:outputText id="cpmTotalSms" styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdSMS}" />
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGrid>

                    </h:panelGrid>

                    <h:panelGrid columns="5" width="100%" style="border:1px dotted black">
                        <h:panelGrid columns="1" width="100%" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoCliente}" columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_faseAtual}" />
                                <h:selectOneMenu value="#{HistoricoContatoControle.historicoContatoVO.fase}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" id="consulta" required="true">
                                    <f:selectItems value="#{HistoricoContatoControle.listaSelectItemFaseAtual}" />
                                    <a4j:support event="onchange" actionListener="#{HistoricoContatoControle.atualizarObjetivo}" reRender="panelGridForm, objetivoFase, panelTextoPadrao" />
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>



                        <h:panelGrid columns="1" width="100%" rendered="#{HistoricoContatoControle.apresentarDadosCabecalhoCliente}" columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_grauSatisfacao}" />
                                <h:selectOneMenu value="#{HistoricoContatoControle.historicoContatoVO.grauSatisfacao}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" id="grauSatisfacao" required="true">
                                    <f:selectItem itemLabel="#{msg_aplic.prt_Agenda_Selecione}" itemValue=" " />
                                    <f:selectItems value="#{HistoricoContatoControle.listaGrauSatisfacao}" />
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" rendered="#{!HistoricoContatoControle.apresentarDadosCabecalhoCliente}" columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_faseAtual}" />
                                <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.fase_Apresentar}" />
                            </h:panelGroup>
                        </h:panelGrid>


                        <h:panelGrid rendered="#{HistoricoContatoControle.historicoContatoVO.apresentarTipoOperacao}" columns="1" width="100%" columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_tipoOperacao}" />
                                <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.tipoOperacao_Apresentar}" />
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                            <h:panelGroup>
                                <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_tipoContato}" />
                                <rich:spacer width="5px" />
                                <h:selectOneMenu id="tipoOperacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form" style="border: none;"
                                                 value="#{HistoricoContatoControle.historicoContatoVO.tipoContato}">
                                    <f:selectItems value="#{HistoricoContatoControle.listaSelectItemTipoContato}" />
                                    <a4j:support event="onchange" action="#{HistoricoContatoControle.alterarMeioEnvio}"
                                                 reRender="formF, dadosTelefonar, dadosTelefonarPassivo, comentarioPassivo, panelEmail, panelEmailPassivo" />
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGrid>


                        <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                            <h:panelGroup>
                                <a4j:commandButton id="btnIndicacao" oncomplete="abrirPopup('indicacaoForm.jsp', 'configuracaoSistemaCRM', 780, 595);"
                                                   action="#{IndicacaoControle.validarTelaRealizarContato}" image="./imagensCRM/botaoIndicacao.png" />
                                <rich:spacer width="10" />
                                <a4j:commandButton id="btnHistorico" action="#{HistoricoContatoControle.consultarHistoricoPassivoIndicadoCliente}"
                                                   oncomplete="#{HistoricoContatoControle.validarQualPopUpAbrirHistoricoContato}"
                                                   image="./imagensCRM/botaoHistorico.png" />
                                <rich:spacer width="10" />
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid id="objetivoFase" rendered="#{!HistoricoContatoControle.historicoContatoVO.contatoAvulso}" columns="5" width="100%" style="border:1px dotted black; height: 30px">
                        <h:outputText styleClass="titulo3" style="margin-left: 8px" value="Objetivo: "/>
                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.fase.objetivo}"/>
                    </h:panelGrid>

                    <h:panelGrid id="fasesDoContato" rendered="#{HistoricoContatoControle.apresentarFasesDoContato}" columns="1" width="100%" style="border:1px dotted black; height: 30px">
                        <h:panelGroup>
                            <h:outputText styleClass="titulo3" style="margin-left: 8px;color: red" value="O Aluno está nas seguintes Fases: "/>
                            <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.fasesDoContato}"/>
                        </h:panelGroup>
                        <h:outputText styleClass="camposAgenda" style="margin-left: 8px" value="Lembrando que: O contato não necessariamente irá bater meta para as duas fases."/>
                    </h:panelGrid>

                    <h:panelGrid id="observacaoContatoAvulso" rendered="#{HistoricoContatoControle.historicoContatoVO.contatoAvulso}" columns="5" width="100%" style="border:1px dotted black; height: 30px">
                        <h:outputText styleClass="titulo3" style="margin-left: 8px;color: red" value="*Esse é um contato avulso, portanto não valerá como resultado de nenhuma meta do CRM Web."/>
                    </h:panelGrid>


                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                        <%--DADOS DO TELEFONE PASSIVO --%>

                        <h:panelGrid id="dadosTelefonarPassivo" columns="1" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarTelefonePassivo}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_telefones}" />

                            <rich:spacer height="10px" />
                            <h:dataTable id="resTelefonePassivo" width="90%" headerClass="subordinado"
                                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                         value="#{HistoricoContatoControle.telefonesPassivo}"
                                         rendered="#{!empty HistoricoContatoControle.telefonesPassivo}" var="telefonePassivo">
                                <h:column rendered="#{HistoricoContatoControle.historicoContatoVO.tipoContato == 'CS'}">
                                    <h:selectBooleanCheckbox onclick="marcarTelefone(this);" id="telSelecionado" value="#{telefonePassivo.selecionado}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_numero}" />
                                    </f:facet>
                                    <h:outputText value="#{telefonePassivo.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_tipoTelefone}" />
                                    </f:facet>
                                    <h:outputText value="#{telefonePassivo.tipoTelefone_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_descricao}" />
                                    </f:facet>
                                    <h:outputText value="#{telefonePassivo.descricao}" />
                                </h:column>
                            </h:dataTable>

                            <rich:spacer width="20px" />

                            <h:panelGrid id="comentarioPassivo" columns="1" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarComentarioTelefonePassivo}">
                                <h:outputText value="#{msg_aplic.prt_Agenda_telefoneNaoEncontrado}" styleClass="mensagemTelefoneNaoEncontrado" />
                            </h:panelGrid>
                        </h:panelGrid>

                        <%--DADOS DO TELEFONE INDICADO --%>

                        <h:panelGrid id="dadosTelefonarIndicado" columns="1" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarTelefoneIndicado}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_telefones}" />

                            <rich:spacer height="10px" />
                            <h:dataTable id="resTelefoneIndicado" width="90%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" value="#{HistoricoContatoControle.listaTelefoneIndicado}" rendered="#{!empty HistoricoContatoControle.listaTelefoneIndicado}" var="telefoneIndicado">
                                <h:column rendered="#{HistoricoContatoControle.historicoContatoVO.tipoContato == 'CS'}">
                                    <h:selectBooleanCheckbox onclick="marcarTelefone(this);" id="telSelecionado" value="#{telefoneIndicado.selecionado}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_numero}" />
                                    </f:facet>
                                    <h:outputText value="#{telefoneIndicado.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_tipoTelefone}" />
                                    </f:facet>
                                    <h:outputText value="#{telefoneIndicado.tipoTelefone_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_descricao}" />
                                    </f:facet>
                                    <h:outputText value="#{telefoneIndicado.descricao}" />
                                </h:column>
                            </h:dataTable>

                            <rich:spacer width="20px" />

                            <h:panelGrid id="comentarioIndicado" columns="1" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarComentarioTelefoneIndicado}">
                                <h:outputText value="#{msg_aplic.prt_Agenda_telefoneNaoEncontrado}" styleClass="mensagemTelefoneNaoEncontrado" />
                            </h:panelGrid>
                        </h:panelGrid>

                        <%--DADOS DO TELEFONE CLIENTE --%>

                        <h:panelGrid columns="1" id="tabelaCliente" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarTelefoneCliente}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_telefones}" />

                            <rich:spacer height="10px" />
                            <h:dataTable id="resTelefone" width="90%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                         value="#{HistoricoContatoControle.historicoContatoVO.listaTelefoneClientePorTipoContato}"
                                         rendered="#{HistoricoContatoControle.apresentarTelefoneCliente}" var="telefone">
                                <h:column rendered="#{HistoricoContatoControle.historicoContatoVO.tipoContato == 'CS'}">
                                    <h:selectBooleanCheckbox onclick="marcarTelefone(this);" id="telSelecionado" value="#{telefone.selecionado}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_numero}" />
                                    </f:facet>
                                    <h:outputText value="#{telefone.numero}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_tipoTelefone}" />
                                    </f:facet>
                                    <h:outputText value="#{telefone.tipoTelefone_Apresentar}" />
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_Agenda_descricao}" />
                                    </f:facet>
                                    <h:outputText value="#{telefone.descricao}" />
                                </h:column>
                            </h:dataTable>

                            <rich:spacer width="20px" />

                            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarComentarioTelefoneCliente}">
                                <h:outputText value="#{msg_aplic.prt_Agenda_telefoneNaoEncontrado}" styleClass="mensagemTelefoneNaoEncontrado" />
                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid id="mensagemEmail" columns="1" width="100%">
                            <h:panelGrid columns="1" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarComentarioNaoPossueEmail}">
                                <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_emails}" />

                                <rich:spacer width="20px" />

                                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.apresentarComentarioNaoPossueEmail}">
                                    <h:outputText value="#{msg_aplic.prt_Agenda_naoPossueEmail}" styleClass="mensagemTelefoneNaoEncontrado" />
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>

                        <h:panelGrid columns="1" id="textComentario" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%" rendered="#{HistoricoContatoControle.historicoContatoVO.apresentarComentario}">
                            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_comentario}" />
                            <rich:spacer height="20"/>
                                
                            
                            <h:outputText rendered="#{HistoricoContatoControle.apresentarSMS}"
                                          styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}"/>
                            <h:panelGroup rendered="#{HistoricoContatoControle.apresentarSMS}">
                                <h:inputText id="modeloSms" size="70" maxlength="100"
                                             readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{HistoricoContatoControle.malaDiretaVO.modeloMensagem.titulo}"/>
                                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"
                                                   image="imagensCRM/informacao.gif"
                                                   alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}"/>
                                <%--rendered="#{HistoricoContatoControle.mensagemNotificacaoVO.novoObj}" --%>
                                <rich:spacer width="5"/>
                                <a4j:commandButton id="limparModeloSms"
                                                   action="#{HistoricoContatoControle.limparCampoModeloMensagem}"
                                                   image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                                   reRender="modeloSms"/>
                                <%--rendered="#{HistoricoContatoControle.mensagemNotificacaoVO.novoObj}" --%>
                            </h:panelGroup>

                            <h:panelGroup rendered="#{!HistoricoContatoControle.apresentarAPP}">
                                <a4j:commandLink action="#{HistoricoContatoControle.inserirTextoPadrao}" value="Texto Padrão"
                                                 rendered="#{!HistoricoContatoControle.listaComMaisDeUm}" reRender="comentarioRichEditor"/>
                                <h:outputText rendered="#{HistoricoContatoControle.listaComMaisDeUm}"  
                                              styleClass="camposAgenda" value="Texto"/>
                                <h:selectOneMenu  id="idTextoPadrao" styleClass="form" rendered="#{HistoricoContatoControle.listaComMaisDeUm}" value="#{HistoricoContatoControle.codigoTextoPadrao}" >
                                    <f:selectItems  value="#{HistoricoContatoControle.listaTextoPadrao}" />
                                    <a4j:support event="onchange" action="#{HistoricoContatoControle.setarMensagemTextoPadrao}" reRender="comentarioRichEditor"/>
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:panelGroup id="panelTextoPadrao" 
                                          rendered="#{!HistoricoContatoControle.apresentarAPP}">
                                <h:outputLink rendered="#{HistoricoContatoControle.apresentarLinkExternoTextoPadrao}"
                                              value="#{HistoricoContatoControle.textoPadrao.linkDocs}" target="_blank">
                                    <h:outputText value="#{HistoricoContatoControle.textoPadrao.descricao}"/>
                                </h:outputLink>
                                <br/>
                                <h:outputText rendered="#{HistoricoContatoControle.apresentarLinkExternoTextoPadrao}"
                                              styleClass="camposAgenda" value="O uso do formulário, não bate meta."/>
                            </h:panelGroup>
                                <h:outputText value="Esse cliente não possui usuário móvel, portanto ainda não faz uso do aplicativo."
                                              styleClass="titulo3" style="margin-left: 8px;color: red"
                                              rendered="#{HistoricoContatoControle.apresentarAPP and !HistoricoContatoControle.temUsuarioMovel}"/>
                                <h:panelGrid columns="2" width="100%" columnClasses="direita, esquerda w50"
                                             rendered="#{HistoricoContatoControle.apresentarAPP and HistoricoContatoControle.temUsuarioMovel}">

                                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}"/>
                                    <h:panelGroup>
                                        <h:inputText id="modeloApp" size="40" maxlength="70"
                                                     readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                     value="#{HistoricoContatoControle.malaDiretaVO.modeloMensagem.titulo}"/>
                                        <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"
                                                           image="imagensCRM/informacao.gif"
                                                           alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}"/>
                                        <rich:spacer width="5"/>
                                        <a4j:commandButton id="limparModeloApp"
                                                           action="#{HistoricoContatoControle.limparCampoModeloMensagem}"
                                                           image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                                           reRender="modeloSms"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCampos" value="Título:"/>
                                    <h:inputText id="tituloApp" size="40" maxlength="70"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{HistoricoContatoControle.malaDiretaVO.titulo}"/>
                                    <h:outputText styleClass="tituloCampos" value="Tipo da mensagem:"/>
                                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{HistoricoContatoControle.tipoMensagemApp}">
                                        <f:selectItems value="#{HistoricoContatoControle.listaSelectTiposPerguntas}"/>
                                        <a4j:support event="onchange" reRender="formF"/>
                                    </h:selectOneMenu>



                                    <h:outputText styleClass="tituloCampos" value="Opção 1:"
                                                  rendered="#{HistoricoContatoControle.botoesApp}"/>
                                    <h:inputText size="18" maxlength="7"
                                                 style="text-transform: uppercase;"
                                                 onblur="blurinput(this);" 
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{HistoricoContatoControle.campos.campo1}"
                                                 rendered="#{HistoricoContatoControle.botoesApp}"/>

                                    <h:outputText styleClass="tituloCampos" value="Opção 2:"
                                                  rendered="#{HistoricoContatoControle.botoesApp}"/>
                                    <h:panelGroup rendered="#{HistoricoContatoControle.botoesApp}">
                                        <h:inputText size="18" maxlength="7"
                                                     style="text-transform: uppercase;float: left;"
                                                     onblur="blurinput(this);" 
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{HistoricoContatoControle.campos.campo2}"/>
                                        <span onclick="mostrarProximo(2);"
                                              style="cursor: pointer;display: block;"
                                              class="btnOp2 btnsOptAPP">
                                            <i class="fa-icon-plus" style="color: #555"></i>
                                        </span>
                                    </h:panelGroup>


                                    <h:outputText styleClass="tituloCampos labelOp3" value="Opção 3:"
                                                  style="display: none;"
                                                  rendered="#{HistoricoContatoControle.botoesApp}"/>
                                    <h:panelGroup rendered="#{HistoricoContatoControle.botoesApp}">
                                        <h:inputText size="18" maxlength="7"
                                                     style="display: none; text-transform: uppercase;float: left;"
                                                     onblur="blurinput(this);" 
                                                     onfocus="focusinput(this);" styleClass="form labelOp3"
                                                     value="#{HistoricoContatoControle.campos.campo3}"/>
                                        <div class="btnOp3 btnsOptAPP">
                                            <span onclick="sumirItem(3);"
                                                  style="cursor: pointer;">
                                                <i class="fa-icon-remove" style="color: #555"></i>
                                            </span>
                                        </div>

                                    </h:panelGroup>

                                    
                                </h:panelGrid> 
                                <h:panelGroup rendered="#{HistoricoContatoControle.apresentarAPP and HistoricoContatoControle.temUsuarioMovel}">
                                    <a4j:commandLink action="#{HistoricoContatoControle.inserirTextoPadrao}" value="Texto Padrão"
                                                     rendered="#{!HistoricoContatoControle.listaComMaisDeUm}" 
                                                     reRender="comentarioRichEditor, comentarioTextAreaapp"/>
                                    <h:outputText rendered="#{HistoricoContatoControle.listaComMaisDeUm}"  
                                                  styleClass="camposAgenda" value="Texto"/>
                                    <h:selectOneMenu  id="idTextoPadraoapp" styleClass="form" rendered="#{HistoricoContatoControle.listaComMaisDeUm}" value="#{HistoricoContatoControle.codigoTextoPadrao}" >
                                        <f:selectItems  value="#{HistoricoContatoControle.listaTextoPadrao}" />
                                        <a4j:support event="onchange" action="#{HistoricoContatoControle.setarMensagemTextoPadrao}" reRender="comentarioRichEditor, comentarioTextAreaapp"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:panelGroup id="panelTextoPadraoAPP" 
                                              rendered="#{HistoricoContatoControle.apresentarAPP and HistoricoContatoControle.temUsuarioMovel}">
                                    <h:outputLink rendered="#{HistoricoContatoControle.apresentarLinkExternoTextoPadrao}"
                                                  value="#{HistoricoContatoControle.textoPadrao.linkDocs}" target="_blank">
                                        <h:outputText value="#{HistoricoContatoControle.textoPadrao.descricao}"/>
                                    </h:outputLink>
                                    <br/>
                                    <h:outputText rendered="#{HistoricoContatoControle.apresentarLinkExternoTextoPadrao}"
                                                  styleClass="camposAgenda" value="O uso do formulário, não bate meta."/>
                                </h:panelGroup>

                            <rich:editor rendered="#{!HistoricoContatoControle.apresentarSMS and !HistoricoContatoControle.apresentarAPP}" style="align:center;"
                                         id="comentarioRichEditor" width="530"
                                         value="#{HistoricoContatoControle.historicoContatoVO.observacao}"/>


                            <h:inputTextarea rendered="#{HistoricoContatoControle.apresentarSMS}"
                                             onkeypress="soma(this.value);" onkeyup="soma(this.value);"
                                             style="align:center;" id="comentarioTextArea" cols="100" rows="2"
                                             value="#{HistoricoContatoControle.historicoContatoVO.observacao}"/>
                            
                            <h:inputTextarea rendered="#{HistoricoContatoControle.apresentarAPP and HistoricoContatoControle.temUsuarioMovel}"
                                             style="align:center;" id="comentarioTextAreaapp" cols="100" rows="2"
                                             value="#{HistoricoContatoControle.historicoContatoVO.observacao}"/>

                            <rich:toolTip for="comentarioTextArea" followMouse="true"
                                          rendered="#{HistoricoContatoControle.toolTipSMS and !HistoricoContatoControle.apresentarAPP}"
                                          direction="top-right"  style="width:300px; height:#{HistoricoContatoControle.tamanhoToolTipSMS}; " showDelay="200">
                                <h:outputText styleClass="tituloCampos" escape="false"
                                              value="#{HistoricoContatoControle.termosFiscalizados}" />
                            </rich:toolTip>

                        </h:panelGrid>
                        <h:panelGroup style="position:relative;" layout="block">
                            <c:if test="${HistoricoContatoControle.apresentarSMS}">
                                <h:inputText style="align:left;" disabled="true" size="3" id="tamanhoRestante"/>

                            </c:if>
                        </h:panelGroup>

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                                     id="painelBotoes"
                                     width="100%" rendered="#{HistoricoContatoControle.historicoContatoVO.apresentarComentario}">

                            <h:panelGroup>
                                <a4j:commandButton id="btnAgendar"
                                                   onclick="if(!validarComentario('#{msg.campoObrigatorio_comentario}')){return false;};"
                                                   rendered="#{!HistoricoContatoControle.apresentarSMS and !HistoricoContatoControle.apresentarAPP}"
                                                   image="./imagensCRM/botaoAgendar.png"
                                                   action="#{HistoricoContatoControle.inicializarDadosAgendamento}"
                                                   oncomplete="#{HistoricoContatoControle.onCompleteBotoes}"
                                                   reRender="painelBotoes,formAgenda" />

                                <rich:spacer width="10" />
                                <a4j:commandButton id="btnObjecao"
                                                   action="#{HistoricoContatoControle.consultarObjecao }"
                                                   oncomplete="#{HistoricoContatoControle.onCompleteBotoes}"
                                                   image="./imagensCRM/botaoObjecao.png" reRender="painelBotoes,panelObjecao"
                                                   rendered="#{HistoricoContatoControle.apresentarBotaoObjecao}" />

                                <rich:spacer width="10" />
                                <a4j:commandButton id="btnSimplesRegistro"
                                                   rendered="#{HistoricoContatoControle.apresentarBotaoSimplesRegistro }"
                                                   action="#{HistoricoContatoControle.inicializarDadosSimplesRegistro}"
                                                   reRender="painelBotoes"
                                                   oncomplete="#{HistoricoContatoControle.onCompleteBotoes}"
                                                   image="./imagensCRM/botaoSimplesRegistro.png" />

                                <rich:spacer width="10" />
                                <a4j:commandButton
                                    id="btnEnviarSMS"
                                    rendered="#{HistoricoContatoControle.apresentarSMS}"
                                    action="#{HistoricoContatoControle.gravarSimplesRegistroHistoricoContato}"
                                    oncomplete="#{HistoricoContatoControle.msgAlert};recarregarMetas();"
                                    image="./imagensCRM/botaoEnviarSMS.png" />

                                <a4j:commandButton
                                    id="btnEnviarAPP"
                                    rendered="#{HistoricoContatoControle.apresentarAPP and HistoricoContatoControle.temUsuarioMovel}"
                                    action="#{HistoricoContatoControle.gravarEnviandoContatoAPP}"
                                    oncomplete="#{HistoricoContatoControle.msgAlert};recarregarMetas();"
                                    image="./imagensCRM/botaoEnviar.png" />
                            </h:panelGroup>
                        </h:panelGrid>


                        <%--------------------E-MAIL CLIENTE-------------------%>

                        <h:panelGrid id="panelEmailCliente" columnClasses="colunaDireita" columns="1" width="100%" cellpadding="0" cellspacing="0">
                            <h:commandLink action="#{HistoricoContatoControle.liberarBackingBeanMemoria}"
                                           id="idLiberarBackingBeanMemoria" style="display: none" />
                            <h:panelGrid columnClasses="colunaDireita" columns="1" styleClass="tabForm" width="100%" rendered="#{HistoricoContatoControle.apresentarEmail}">
                                <h:panelGrid columns="1" style="height:10px; background-image:url('./imagensCRM/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_enviarEmail}" />
                                </h:panelGrid>

                                <rich:tabPanel width="100%" activeTabClass="true">
                                    <rich:tab id="dadosEmail" label="Dados E-mail">
                                        <h:panelGrid columns="2" styleClass="tabForm" width="100%" style="vertical-align: top;">

                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_remetente}" />
                                            <h:panelGroup id="panelRemetente">
                                                <h:inputText id="textColaboradorResponsavel" size="50" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.remetente.nome}" />
                                                <rich:suggestionbox height="200" width="200" for="textColaboradorResponsavel" fetchValue="#{result.nome}" suggestionAction="#{HistoricoContatoControle.autocompleteRemetente}" minChars="3" rowClasses="20" nothingLabel="Nenhum Responsável encontrado !" var="result" id="suggestionResponsavel">
                                                    <h:column>
                                                        <h:outputText value="#{result.nome}" />
                                                    </h:column>
                                                </rich:suggestionbox>
                                            </h:panelGroup>

                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}" />
                                            <h:panelGroup>
                                                <h:inputText id="modeloMensagem" size="70" maxlength="100" readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.modeloMensagem.titulo}" />
                                                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"
                                                                   image="imagensCRM/informacao.gif" alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
                                                <%--rendered="#{HistoricoContatoControle.mensagemNotificacaoVO.novoObj}" --%>
                                                <rich:spacer width="5" />
                                                <a4j:commandButton id="limparModeloMensagem" action="#{HistoricoContatoControle.limparCampoModeloMensagem}"
                                                                   image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="modeloMensagem" />
                                                <%--rendered="#{HistoricoContatoControle.mensagemNotificacaoVO.novoObj}" --%>
                                            </h:panelGroup>

                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_titulo}" />
                                            <h:panelGroup>
                                                <h:inputText id="titulo" size="70" maxlength="100" 
                                                             readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" 
                                                             onblur="blurinput(this);" onfocus="focusinput(this);" 
                                                             styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.titulo}" />
                                                <rich:toolTip for="titulo" followMouse="true" direction="top-right"  style="width:300px; height:#{HistoricoContatoControle.tamanhoToolTip}; " showDelay="200">
                                                    <h:outputText styleClass="tituloCampos" escape="false"
                                                                  value="#{msg.msg_tip_tituloMail}#{HistoricoContatoControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}" />
                                                </rich:toolTip>
                                            </h:panelGroup>

                                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_adicionarTag}" />
                                            <h:panelGroup>
                                                <!--				                    		<a4j:commandButton id="tagNomeCliente" oncomplete="Richfaces.showModalPanel('panelEmail');" reRender="formMarcadorEmail" image="./imagensCRM/botaoTagNome.png" title="Tag Nome" />-->
                                                <a4j:commandButton id="tagEmail" oncomplete="Richfaces.showModalPanel('panelEmail');"
                                                                   reRender="formMarcadorEmail" image="./imagensCRM/botaoTagEmail2.png"
                                                                   value="Tag E-mail" title="Tag E-mail"/>
                                                <a4j:commandButton id="tagURLRenovacao" rendered="#{HistoricoContatoControle.contratoVigente.codigo != 0}"
                                                                   oncomplete="Richfaces.showModalPanel('panelContrato');" reRender="formPanelFormContrato"
                                                                   image="./imagensCRM/botaoTagContrato.png" title="Tag Contrato" />
                                            </h:panelGroup>

                                        </h:panelGrid>

                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_mensagem}" />

                                            <rich:editor configuration="editorpropriedades" viewMode="visual" styleClass="colunaCentralizada" readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" theme="advanced" id="imputMensagem" height="400" width="760" value="#{HistoricoContatoControle.malaDiretaVO.mensagem}" />
                                        </h:panelGrid>
                                    </rich:tab>

                                    <rich:tab id="emailsEnviados" label="Enviar Para:" switchType="Client">
                                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Agenda_listaEmail}" />
                                            </f:facet>
                                            <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                                                <h:panelGrid columns="1" width="100%">

                                                    <rich:dataTable id="resultadoConsultaListaEmail" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                                                    columnClasses="colunaCentralizada" value="#{HistoricoContatoControle.listaEmail}" rows="10" var="malaDireta">
                                                        <rich:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="#{msg_aplic.prt_Agenda_email}" />
                                                            </f:facet>
                                                            <h:outputText value="#{malaDireta.email}" styleClass="campos" />
                                                        </rich:column>
                                                        <h:column>
                                                            <f:facet name="header">
                                                                <h:outputText value="#{msg_bt.btn_opcoes}" />
                                                            </f:facet>
                                                            <h:panelGroup>
                                                                <h:selectBooleanCheckbox id="selecionarEmail" styleClass="campos" value="#{malaDireta.escolhido}">
                                                                </h:selectBooleanCheckbox>
                                                            </h:panelGroup>
                                                        </h:column>
                                                    </rich:dataTable>
                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </rich:tab>
                                </rich:tabPanel>
                            </h:panelGrid>
                        </h:panelGrid>

                        <%-------------------- EMAIL PASSIVO -------------------%>

                        <h:panelGrid id="panelEmailPassivo" columnClasses="colunaDireita" columns="1" width="100%" cellpadding="0" cellspacing="0">
                            <h:commandLink action="#{HistoricoContatoControle.liberarBackingBeanMemoria}"
                                           id="idLiberarBackingBeanMemoriaPassivo" style="display: none" />
                            <h:panelGrid columnClasses="colunaDireita" columns="1" styleClass="tabForm" width="100%" rendered="#{HistoricoContatoControle.apresentarEmailPassivo}">
                                <h:panelGrid columns="1" style="height:10px; background-image:url('./imagensCRM/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_enviarEmail}" />
                                </h:panelGrid>

                                <rich:tabPanel width="100%" activeTabClass="true">
                                    <rich:tab id="dadosEmailPassivo" label="Dados E-mail">
                                        <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_remetente}" />
                                            <h:panelGroup id="panelRemetentePassivo">
                                                <h:inputText id="textColaboradorResponsavelPassivo" size="50" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.remetente.nome}" />
                                                <rich:suggestionbox height="200" width="200" for="textColaboradorResponsavelPassivo" fetchValue="#{result.nome}" suggestionAction="#{HistoricoContatoControle.autocompleteRemetente}" minChars="3" rowClasses="20" nothingLabel="Nenhum Responsável encontrado !" var="result" id="suggestionResponsavelPassivo">

                                                    <h:column>
                                                        <h:outputText value="#{result.nome}" />
                                                    </h:column>
                                                </rich:suggestionbox>
                                            </h:panelGroup>
                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}" />
                                            <h:panelGroup>
                                                <h:inputText id="modeloMensagemPassivo" size="70" maxlength="100" readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.modeloMensagem.titulo}" />
                                                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"
                                                                   image="imagensCRM/informacao.gif" alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
                                                <%--rendered="#{HistoricoContatoControle.mensagemNotificacaoVO.novoObj}" --%>
                                                <rich:spacer width="5" />
                                                <a4j:commandButton id="limparModeloMensagemPassivo" action="#{HistoricoContatoControle.limparCampoModeloMensagem}"
                                                                   image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="modeloMensagem" />
                                                <%--rendered="#{HistoricoContatoControle.mensagemNotificacaoVO.novoObj}" --%>
                                            </h:panelGroup>

                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_titulo}" />
                                            <h:panelGroup>
                                                <h:inputText id="tituloPassivo" size="70" maxlength="100" readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.titulo}" />
                                            </h:panelGroup>
                                            <a4j:commandButton id="tagNomePassivo" oncomplete="Richfaces.showModalPanel('panelEmail');"
                                                               reRender="formMarcadorEmail" image="./imagensCRM/botaoTagNome.png" title="Tag Nome" />
                                        </h:panelGrid>

                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_mensagem}" />

                                            <rich:editor  configuration="editorpropriedades" viewMode="visual" styleClass="colunaCentralizada" readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" theme="advanced" id="inputMensagemPassivo" height="400" width="760" value="#{HistoricoContatoControle.malaDiretaVO.mensagem}" />
                                        </h:panelGrid>
                                    </rich:tab>

                                    <rich:tab id="emailsEnviadosPassivo" label="Enviar Para:" switchType="Client">
                                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Agenda_listaEmail}" />
                                            </f:facet>
                                            <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                                    <h:outputText value="#{HistoricoContatoControle.historicoContatoVO.passivoVO.email}" styleClass="campos" />
                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </rich:tab>
                                </rich:tabPanel>
                            </h:panelGrid>
                        </h:panelGrid>

                        <%-------------------- EMAIL INDICADO -------------------%>

                        <h:panelGrid id="panelEmailIndicado" columnClasses="colunaDireita" columns="1" width="100%" cellpadding="0" cellspacing="0">
                            <h:commandLink action="#{HistoricoContatoControle.liberarBackingBeanMemoria}"
                                           id="idLiberarBackingBeanMemoriaIndicado" style="display: none" />
                            <h:panelGrid columnClasses="colunaDireita" columns="1" styleClass="tabForm" width="100%" rendered="#{HistoricoContatoControle.apresentarEmailIndicado}">
                                <h:panelGrid columns="1" style="height:10px; background-image:url('./imagensCRM/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_enviarEmail}" />
                                </h:panelGrid>

                                <rich:tabPanel width="100%" activeTabClass="true">
                                    <rich:tab id="dadosEmailIndicado" label="Dados E-mail">
                                        <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_remetente}" />
                                            <h:panelGroup id="panelRemetenteIndicado">
                                                <h:inputText id="textColaboradorResponsavelIndicado" size="50" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.remetente.nome}" />
                                                <rich:suggestionbox height="200" width="200" for="textColaboradorResponsavelIndicado" fetchValue="#{result.nome}" suggestionAction="#{HistoricoContatoControle.autocompleteRemetente}" minChars="3" rowClasses="20" nothingLabel="Nenhum Responsável encontrado !" var="result" id="suggestionResponsavelIndicado">

                                                    <h:column>
                                                        <h:outputText value="#{result.nome}" />
                                                    </h:column>
                                                </rich:suggestionbox>
                                            </h:panelGroup>
                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_modeloMensagem}" />
                                            <h:panelGroup>
                                                <h:inputText id="modeloMensagemIndicado" size="70" maxlength="100" readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.modeloMensagem.titulo}" />
                                                <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"
                                                                   image="imagensCRM/informacao.gif" alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
                                                <%--rendered="#{HistoricoContatoControle.mensagemNotificacaoVO.novoObj}" --%>
                                                <rich:spacer width="5" />
                                                <a4j:commandButton id="limparModeloMensagemIndicado" action="#{HistoricoContatoControle.limparCampoModeloMensagem}"
                                                                   image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="modeloMensagemIndicado" />
                                                <%--rendered="#{HistoricoContatoControle.mensagemNotificacaoVO.novoObj}" --%>
                                            </h:panelGroup>

                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_titulo}" />
                                            <h:panelGroup>
                                                <h:inputText id="tituloIndicado" size="70" maxlength="100" readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.malaDiretaVO.titulo}" />
                                            </h:panelGroup>
                                            <a4j:commandButton id="tagNomeIndicado" oncomplete="Richfaces.showModalPanel('panelEmail');"
                                                               reRender="formMarcadorEmail" image="./imagensCRM/botaoTagNome.png" title="Tag Nome" />
                                        </h:panelGrid>

                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_mensagem}" />

                                            <rich:editor configuration="editorpropriedades" viewMode="visual" styleClass="colunaCentralizada" readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}" theme="advanced" id="inputMensagemIndicado" height="400" width="760" value="#{HistoricoContatoControle.malaDiretaVO.mensagem}" />
                                        </h:panelGrid>
                                    </rich:tab>

                                    <rich:tab id="emailsEnviadosIndicado" label="Enviar Para:" switchType="Client">
                                        <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                            <f:facet name="header">
                                                <h:outputText value="#{msg_aplic.prt_Agenda_listaEmail}" />
                                            </f:facet>
                                            <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                                    <h:outputText value="#{HistoricoContatoControle.historicoContatoVO.indicadoVO.email}" styleClass="campos" />
                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </rich:tab>
                                </rich:tabPanel>
                            </h:panelGrid>
                        </h:panelGrid>


                        <%-- BOTAO DE EVIAR E-MAIL PASSIVO --%>

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" styleClass="tabMensagens" rendered="#{HistoricoContatoControle.apresentarEmailPassivo}">
                            <a4j:commandButton id="btnConcluirEmailPassivo"
                                               reRender="formF, panelGridMensagens"
                                               oncomplete="#{HistoricoContatoControle.msgAlert}recarregarMetas();"
                                               action="#{HistoricoContatoControle.gravarEnviandoEmail}"
                                               image="./imagensCRM/botaoEnviarEmail.png" />
                        </h:panelGrid>

                        <%-- BOTAO DE EVIAR E-MAIL INDICADO --%>

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" styleClass="tabMensagens" rendered="#{HistoricoContatoControle.apresentarEmailIndicado}">
                            <a4j:commandButton id="btnConcluirEmailIndicado"
                                               reRender="formF, panelGridMensagens"
                                               oncomplete="#{HistoricoContatoControle.msgAlert}recarregarMetas();"
                                               action="#{HistoricoContatoControle.gravarEnviandoEmail}"
                                               image="./imagensCRM/botaoEnviarEmail.png" />
                        </h:panelGrid>

                        <%-- BOTAO DE EVIAR E-MAIL CLIENTE --%>

                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" styleClass="tabMensagens" rendered="#{HistoricoContatoControle.apresentarEmail}">
                            <a4j:commandButton id="btnConcluirEmail"
                                               reRender="formF, panelGridMensagens"
                                               oncomplete="#{HistoricoContatoControle.msgAlert}recarregarMetas();"
                                               action="#{HistoricoContatoControle.gravarEnviandoEmail}"
                                               image="./imagensCRM/botaoEnviarEmail.png" />
                        </h:panelGrid>

                        <h:panelGrid id="panelGridMensagens" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                                <h:panelGrid columns="1" width="100%">
                                    <f:verbatim>
                                        <h:outputText value=" " />
                                    </f:verbatim>
                                </h:panelGrid>
                                <a4j:commandButton rendered="#{HistoricoContatoControle.sucesso}"
                                                   image="./imagens/sucesso.png" />
                                <a4j:commandButton rendered="#{HistoricoContatoControle.erro}"
                                                   image="./imagens/erro.png" />
                                <h:panelGrid columns="1" width="100%">
                                    <h:outputText id="msgRealizarContato" styleClass="mensagem"
                                                  value="#{HistoricoContatoControle.mensagem}" />
                                    <h:outputText id="msgRealizarContatoDet" styleClass="mensagemDetalhada"
                                                  value="#{HistoricoContatoControle.mensagemDetalhada}" />
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="panelStatus1" autosized="true">
        <h:panelGrid columns="3">
            <h:graphicImage url="/imagens/carregando.gif" style="border:none"/>
            <h:outputText styleClass="titulo3" value="Carregando..."/>
        </h:panelGrid>
    </rich:modalPanel>
    <a4j:status id="status" onstart="Richfaces.showModalPanel('panelStatus1');"
                onstop="#{rich:component('panelStatus1')}.hide();">
    </a4j:status>
</f:view>

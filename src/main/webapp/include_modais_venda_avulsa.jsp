<%-- 
    Document   : include_modais_venda_avulsa
    Created on : 03/06/2012, 22:05:01
    Author     : Waller
--%>
<%@include file="includes/imports.jsp" %>


<rich:modalPanel id="panelConfirmacaoVendaAvulsa" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formConfirmacaoVendaAvulsa:senha').focus();">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Confirma��o da Venda"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkVendaAvulsa"/>
            <rich:componentControl for="panelConfirmacaoVendaAvulsa" attachTo="hidelinkVendaAvulsa" operation="hide"  event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConfirmacaoVendaAvulsa" styleClass="font-size-Em-max" >
        <%@include file="/includes/include_identificadorModuloEstudio.jsp" %>
        <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
            <h:panelGrid  id="panelResponsavelVendaAvulsa" columns="1" width="100%" columnClasses="colunaEsquerda" cellpadding="5" styleClass="font-size-Em-max">
                <h:panelGroup>
                    <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                    <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                </h:panelGroup>
                <h:panelGroup >
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"   value="C�digo:" />
                    <h:inputText id="codigoUsuario" size="5" maxlength="7" style="margin-left:6px" value="#{VendaAvulsaControle.vendaAvulsaVO.responsavel.codigo}">
                        <a4j:support event="onchange" focus="formConfirmacaoVendaAvulsa:senha" action="#{VendaAvulsaControle.consultarResponsavel}" reRender="panelResponsavelVendaAvulsa, msg"/>
                    </h:inputText>
                    <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{VendaAvulsaControle.vendaAvulsaVO.responsavel.username}"/>
                </h:panelGroup>
                <h:panelGroup >
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"   value="Usu�rio:" />
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  style="margin-left:5px"  value="#{VendaAvulsaControle.vendaAvulsaVO.responsavel.username}" />
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"   value="Senha:" />
                    <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                   styleClass="inputTextClean"
                                   value="#{VendaAvulsaControle.vendaAvulsaVO.responsavel.senha}"/>
                    <rich:hotKey selector="#senha" key="return"
                                 handler="#{rich:element('loginPagar1')}.onclick();return false;"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid id="msg" columns="2" width="100%" >
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem"  value="#{VendaAvulsaControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{VendaAvulsaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink id="loginPagar1" value="#{msg_bt.btn_confirmar}"
                                   reRender="form:mensagem" oncomplete="#{VendaAvulsaControle.abrirRichConfimacao}"
                                   title="#{msg.msg_gravar_dados}"
                                   styleClass="botaoPrimario texto-size-14-real"
                                   action="#{VendaAvulsaControle.verificarUsuarioSenhaabrirRichPagarResponsavel}" />
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelProduto" autosized="true" styleClass="novaModal" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formProduto:consultarProduto').focus();">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_ItemVendaAvulsa_consultarProduto}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hiperlinkProduto"/>
            <rich:componentControl for="panelProduto" attachTo="hiperlinkProduto" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formProduto" ajaxSubmit="true" >
        <%@include file="/includes/include_identificadorModuloEstudio.jsp" %>
        <h:panelGrid columns="1" width="100%">
            <h:panelGrid columns="4" width="100%" styleClass="font-size-Em-max">
                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{msg.msg_consultar_por}"/>
                <h:panelGroup layout="block" styleClass="cb-container">
                    <h:selectOneMenu styleClass="campos" id="consultarProduto" value="#{VendaAvulsaControle.campoConsultarProduto}">
                        <f:selectItems value="#{VendaAvulsaControle.tipoConsultarComboProduto}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <h:inputText id="valorConsultarProduto" styleClass="campos" value="#{VendaAvulsaControle.valorConsultarProduto}"/>
                <a4j:commandLink id="btnConsultarProduto"
                                 reRender="formProduto:mensagemConsultarProduto, formProduto:resultadoConsultaProduto , formProdutoscResultadoProduto , formProduto"
                                 action="#{VendaAvulsaControle.consultarProduto}" value="#{msg_bt.btn_consultar}"
                                 styleClass="botaoPrimario texto-size-14-real"/>
            </h:panelGrid>
            <rich:dataTable id="resultadoConsultaProduto" width="100%" styleClass="tabelaSimplesCustom" value="#{VendaAvulsaControle.listaConsultarProduto}" rows="10" var="produto">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"   value="#{msg_aplic.prt_Produto_codigo}"/>
                    </f:facet>
                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{produto.codigo}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{msg_aplic.prt_Produto_descricao}"/>
                    </f:facet>
                    <h:outputText value="#{produto.descricao}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{msg_aplic.prt_Produto_valor}"/>
                    </f:facet>
                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{produto.valorFinal}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{msg_bt.btn_opcoes}"/>
                    </f:facet>
                    <a4j:commandLink action="#{VendaAvulsaControle.selecionarProduto}" focus="produto" reRender="form:panelGeral, formProduto,mensagem"
                                     oncomplete="Richfaces.hideModalPanel('panelProduto')" value="#{msg_bt.btn_selecionar}" styleClass="linkPadrao texto-size-16-real" >
                        Selecionar <i class="fa-icon-arrow-right"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller  styleClass="scrollPureCustom" align="center" for="formProduto:resultadoConsultaProduto" maxPages="10" id="scResultadoProduto"/>
            <h:panelGrid id="mensagemConsultaProduto" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{VendaAvulsaControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{VendaAvulsaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>


<rich:modalPanel id="panelCliente" autosized="true" styleClass="novaModal" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formCliente:consultarCliente').focus();">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_VendaAvulsa_consultarCliente}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hiperlinkCliente"/>
            <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formCliente" ajaxSubmit="true" >
        <%@include file="/includes/include_identificadorModuloEstudio.jsp" %>
        <h:panelGrid columns="1" width="100%">
            <h:panelGrid columns="4" width="100%" styleClass="font-size-Em-max">
                <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                <h:panelGroup layout="block" styleClass="cb-container">
                    <h:selectOneMenu styleClass="campos" id="consultarCliente"
                                     value="#{VendaAvulsaControle.campoConsultarCliente}">
                        <f:selectItems value="#{VendaAvulsaControle.tipoConsultarComboCliente}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <h:inputText id="valorConsultarCliente" styleClass="campos" value="#{VendaAvulsaControle.valorConsultarCliente}"/>
                <a4j:commandLink id="btnConsultarCliente" reRender="formCliente:mensagemConsultarCliente, formCliente:resultadoConsultaCliente , formClientescResultadoCliente , formCliente"
                                 action="#{VendaAvulsaControle.consultarCliente}"
                                 value="#{msg_bt.btn_consultar}"
                                 styleClass="botaoPrimario texto-size-14-real"/>
            </h:panelGrid>
            <rich:dataTable id="resultadoConsultaCliente" width="100%" styleClass="tabelaSimplesCustom" value="#{VendaAvulsaControle.listaConsultarCliente}" rows="10" var="cliente">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Cliente_codigo}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{cliente.codigo}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="#{msg_aplic.prt_Cliente_pessoa}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{cliente.pessoa.nome}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="#{msg_aplic.prt_Cliente_situacao}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{cliente.situacao_Apresentar}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="#{msg_aplic.prt_Cliente_matricula}"/>
                    </f:facet>
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{cliente.matricula}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="#{msg_bt.btn_opcoes}"/>
                    </f:facet>
                    <a4j:commandLink action="#{VendaAvulsaControle.selecionarCliente}" focus="cliente" reRender="form:panelGeral, formCliente, mensagem"
                                     oncomplete="Richfaces.hideModalPanel('panelCliente')">
                        Selecionar <i class="fa-icon-arrow-right"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" styleClass="scrollPureCustom" renderIfSinglePage="false" for="formCliente:resultadoConsultaCliente" maxPages="10" id="scResultadoCliente"/>
            <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{VendaAvulsaControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{VendaAvulsaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelColaborador" autosized="true" styleClass="novaModal" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formColaborador:consultarColaborador').focus();">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_VendaAvulsa_consultarColaborador}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hiperlinkColaborador"/>
            <rich:componentControl for="panelColaborador" attachTo="hiperlinkColaborador" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formColaborador" ajaxSubmit="true" >
        <%@include file="/includes/include_identificadorModuloEstudio.jsp" %>
        <h:panelGrid columns="1" width="100%">
            <h:panelGrid columns="4" width="100%">
                <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                <h:selectOneMenu styleClass="campos" id="consultarColaborador" value="#{VendaAvulsaControle.campoConsultarColaborador}">
                    <f:selectItems value="#{VendaAvulsaControle.tipoConsultarComboColaborador}"/>
                </h:selectOneMenu>
                <h:inputText id="valorConsultarColaborador" styleClass="campos" value="#{VendaAvulsaControle.valorConsultarColaborador}"/>
                <a4j:commandButton id="btnConsultarColaborador" reRender="formColaborador:mensagemConsultarColaborador, formColaborador:resultadoConsultaColaborador , formColaboradorscResultadoColaborador , formColaborador" action="#{VendaAvulsaControle.consultarColaborador}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
            </h:panelGrid>
            <rich:dataTable id="resultadoConsultaColaborador" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{VendaAvulsaControle.listaConsultarColaborador}" rows="10" var="colaborador">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Colaborador_codigo}"/>
                    </f:facet>
                    <h:outputText value="#{colaborador.codigo}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Colaborador_pessoa}"/>
                    </f:facet>
                    <h:outputText value="#{colaborador.pessoa.nome}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_bt.btn_opcoes}"/>
                    </f:facet>
                    <a4j:commandButton action="#{VendaAvulsaControle.selecionarColaborador}" focus="colaborador" reRender="form:panelGeral, formColaborador, mensagem" oncomplete="Richfaces.hideModalPanel('panelColaborador')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" for="formColaborador:resultadoConsultaColaborador" maxPages="10" id="scResultadoColaborador"/>
            <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{VendaAvulsaControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{VendaAvulsaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelDesconto" autosized="true" styleClass="novaModal" shadowOpacity="true" width="250" height="150">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Consulta de Desconto Para Produto"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink5"/>
            <rich:componentControl for="panelDesconto" attachTo="hidelink5" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formDesconto" ajaxSubmit="true">
        <%@include file="/includes/include_identificadorModuloEstudio.jsp" %>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <rich:dataTable id="resultadoConsultaDesconto" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                            value="#{VendaAvulsaControle.listaConsultarTabelaDesconto}" rows="5" var="desconto">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Desconto_descricao}" />
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandLink action="#{VendaAvulsaControle.selecionarTabelaDesconto}" focus="itemVendaAvulsa_tabelaDesconto" reRender="form:panelGeral, mensagem" oncomplete="Richfaces.hideModalPanel('panelDesconto')" value="#{desconto.descricao}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Desconto_tipoDesconto}"/>
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandLink id="btnSelecionarDesconto" action="#{VendaAvulsaControle.selecionarTabelaDesconto}" 
                                         focus="itemVendaAvulsa_tabelaDesconto" reRender="form:panelGeral, mensagem"
                                         oncomplete="Richfaces.hideModalPanel('panelDesconto')" value="#{desconto.tipoProduto_Apresentar}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Desconto_valor}"/>
                    </f:facet>
                    <h:panelGroup rendered="#{desconto.apresentarDescontoValor}">
                        <a4j:commandLink action="#{VendaAvulsaControle.selecionarTabelaDesconto}" focus="itemVendaAvulsa_tabelaDesconto"
                                         reRender="form:panelGeral, mensagem" oncomplete="Richfaces.hideModalPanel('panelDesconto')" id="valor">
                            <h:panelGroup>
                                <h:outputText value="#{VendaAvulsaControle.empresaLogado.moeda} "/>
                                <h:outputText value="#{desconto.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                        </a4j:commandLink>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{desconto.apresentarDescontoPorcentagem}">
                        <a4j:commandLink action="#{VendaAvulsaControle.selecionarTabelaDesconto}" focus="itemVendaAvulsa_tabelaDesconto"
                                         reRender="form:panelGeral, mensagem" oncomplete="Richfaces.hideModalPanel('panelDesconto')" id="percentual">
                            <h:panelGroup>
                                <h:outputText value="#{desconto.valor}">
                                    <f:converter converterId="FormatadorNumerico7Casa"/>
                                </h:outputText>
                                <h:outputText value=" %"/>
                            </h:panelGroup>
                        </a4j:commandLink>
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_bt.btn_opcoes}"/>
                    </f:facet>
                    <a4j:commandButton action="#{VendaAvulsaControle.selecionarTabelaDesconto}" focus="itemVendaAvulsa_tabelaDesconto" 
                                       reRender="form:panelGeral,mensagem" oncomplete="Richfaces.hideModalPanel('panelDesconto')"
                                       value="#{msg_bt.btn_selecionar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_selecionar_dados}" styleClass="botoes"/>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" for="formDesconto:resultadoConsultaDesconto" maxPages="10"
                               id="scResultadoDesconto" />
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalBVEstudio" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="350" height="120">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Responder Boletim de Visita"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkVendaAvulsaEstudio"/>
            <rich:componentControl for="modalBVEstudio" attachTo="hidelinkVendaAvulsaEstudio" operation="hide"  event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formBVEstudio" >
        <h:outputText value="#{msg.msg_responder_bv_sessao}" styleClass="tituloCampos"></h:outputText>
            <br/>
            <br/>
            <center>
            <h:commandButton id="responderBVEstudio"
                             value="Responder BV" 
                             action="#{VendaAvulsaControle.responderBV}" />
        </center>
    </h:form>
</rich:modalPanel>


<rich:modalPanel id="panelPacotesCreditoPersonal" styleClass="novaModal" autosized="true"
                 shadowOpacity="true" width="350" height="200">


    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Pacotes de cr�dito para personal"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkPacotePersonal"/>
            <rich:componentControl for="panelPacotesCreditoPersonal" attachTo="hidelinkPacotePersonal" operation="hide"  event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formPacotesPersonal" >

        <h:outputText value="Produto:" style="font-weight: bold;" styleClass="tituloCampos"></h:outputText>
        <h:outputText styleClass="tituloCampos" value="#{VendaAvulsaControle.itemVendaAvulsaVO.produto.descricao}"/>
        <br/><br/>
        <rich:dataTable id="tableCreditos" width="100%" var="pacote" rows="10"
                        styleClass="pure-table pure-table-horizontal pure-table-striped pure-table-links"
                        value="#{VendaAvulsaControle.itemVendaAvulsaVO.produto.pacotesPersonal}"
                        columnClasses="centralizado, direita, direita">
            <rich:column>
                <f:facet name="header">
                    <h:outputText value="Quantidade" style="font-weight: bold;" styleClass="tituloCampos"/>
                </f:facet>
                <a4j:commandLink action="#{VendaAvulsaControle.selecionarPacotePersonal}"
                                 oncomplete="#{VendaAvulsaControle.msgAlert}"
                                 reRender="panelItemVenda">
                    <h:outputText value="#{pacote.quantidade}" styleClass="text"/>
                </a4j:commandLink>

            </rich:column>

            <rich:column>
                <f:facet name="header">
                    <h:outputText value="Valor Pr�-Pago #{VendaAvulsaControle.empresaLogado.moeda}"  style="font-weight: bold;" styleClass="tituloCampos"/>
                </f:facet>
                <a4j:commandLink action="#{VendaAvulsaControle.selecionarPacotePersonal}"
                                 oncomplete="#{VendaAvulsaControle.msgAlert}"
                                 reRender="panelItemVenda">
                    <h:outputText styleClass="text" value="#{pacote.valorPrePagoApresentar}"/>
                </a4j:commandLink>
            </rich:column>
            <rich:column>
                <f:facet name="header">
                    <h:outputText value="Valor P�s-Pago #{VendaAvulsaControle.empresaLogado.moeda}" style="font-weight: bold;" styleClass="tituloCampos"/>
                </f:facet>
                <a4j:commandLink action="#{VendaAvulsaControle.selecionarPacotePersonal}"
                                 oncomplete="#{VendaAvulsaControle.msgAlert}"
                                 reRender="panelItemVenda">
                    <h:outputText styleClass="text" value="#{pacote.valorPosPagoApresentar}"/>
                </a4j:commandLink>
            </rich:column>

        </rich:dataTable>
        <rich:datascroller align="center" for="formPacotesPersonal:tableCreditos" maxPages="10"
                               id="sctableCreditos" />
        <br/>
        <center>
            <a4j:commandLink 
                styleClass="pure-button"
                oncomplete="Richfaces.hideModalPanel('panelPacotesCreditoPersonal');">
                Cancelar
            </a4j:commandLink>
        </center>

    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalCadastrarClienteVendaAvulsa" domElementAttachment="parent"
                 onshow="document.getElementById('formModalCadastrarClienteVendaAvulsa:inptclienteCadastrarCelular').focus();fecharSuggestion();"
                 autosized="true" shadowOpacity="false" width="400"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Cadastro r�pido de cliente"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkModalCadastrarClienteVendaAvulsa"/>
            <rich:componentControl for="modalCadastrarClienteVendaAvulsa"
                                   attachTo="hidelinkModalCadastrarClienteVendaAvulsa"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalCadastrarClienteVendaAvulsa" ajaxSubmit="true">
        <h:panelGroup layout="block" style="padding: 10px"
                      id="panelModalCadastrarClienteVendaAvulsa">

            <h:panelGroup layout="block"
                          style="padding-top: 10px;padding-bottom: 15px;">
                <h:outputText styleClass="texto-size-20"
                              style="display: block; color: #9E9E9E;"
                              value="Nome:"/>
                <h:inputText styleClass="form"
                             id="inpuclienteCadastrar"
                             style="width: 100%"
                             value="#{VendaAvulsaControle.clienteCadastrar}"/>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          style="display: grid; grid-template-columns: 1fr 1fr;">
                <h:panelGroup layout="block"
                              style="padding-top: 10px;padding-bottom: 15px;">
                    <h:outputText styleClass="texto-size-20"
                                  style="display: block; color: #9E9E9E;"
                                  value="Celular:"/>
                    <h:inputText size="13"
                                 id="inptclienteCadastrarCelular"
                                 maxlength="13"
                                 onchange="return validar_Telefone(this.id);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{VendaAvulsaControle.clienteCadastrarCelular}"/>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              style="padding-top: 10px;padding-bottom: 15px;">
                    <h:outputText styleClass="texto-size-20"
                                  style="display: block; color: #9E9E9E;"
                                  value="Residencial:"/>
                    <h:inputText size="13"
                                 id="inptclienteCadastrarResidencial"
                                 maxlength="13"
                                 onchange="return validar_Telefone(this.id);"
                                 onblur="blurinput(this);"
                                 onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{VendaAvulsaControle.clienteCadastrarResidencial}"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          style="padding-top: 10px;padding-bottom: 15px;">
                <h:outputText styleClass="texto-size-20"
                              style="display: block; color: #9E9E9E;"
                              value="E-mail:"/>
                <h:inputText styleClass="form"
                             id="inputEmailCadastrar"
                             style="width: 100%"
                             value="#{VendaAvulsaControle.clienteCadastrarEmail}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelBtnModalCadastrarClienteVendaAvulsa"
                          style="text-align: center; padding: 20px 0 20px 0px;">
                <a4j:commandLink id="btnCadastrarClienteNovoVendaAvulsa"
                                 value="Confirmar"
                                 reRender="form:panelGroupComprador"
                                 action="#{VendaAvulsaControle.cadastrarClienteNovo}"
                                 oncomplete="#{VendaAvulsaControle.mensagemNotificar};#{VendaAvulsaControle.onCompleteCadastrarCliente}"
                                 styleClass="botaoPrimario texto-size-16"/>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Text field dialog window.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title></title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta content="noindex, nofollow" name="robots" />
	<script src="common/fck_dialog_common.js" type="text/javascript"></script>
	<script type="text/javascript">

var dialog	= window.parent ;
var oEditor = dialog.InnerDialogLoaded() ;

// Gets the document DOM
var oDOM = oEditor.FCK.EditorDocument ;

var oActiveEl = dialog.Selection.GetSelectedElement() ;

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	if ( oActiveEl && oActiveEl.tagName == 'INPUT' && ( oActiveEl.type == 'text' || oActiveEl.type == 'password' ) )
	{
		GetE('txtName').value	= oActiveEl.name ;
		GetE('txtValue').value	= oActiveEl.value ;
		GetE('txtSize').value	= GetAttribute( oActiveEl, 'size' ) ;
		GetE('txtMax').value	= GetAttribute( oActiveEl, 'maxLength' ) ;
		GetE('txtType').value	= oActiveEl.type ;
	}
	else
		oActiveEl = null ;

	dialog.SetOkButton( true ) ;
	dialog.SetAutoSize( true ) ;
	SelectField( 'txtName' ) ;
}

function Ok()
{
	if ( isNaN( GetE('txtMax').value ) || GetE('txtMax').value < 0 )
	{
		alert( "Maximum characters must be a positive number." ) ;
		GetE('txtMax').focus() ;
		return false ;
	}
	else if( isNaN( GetE('txtSize').value ) || GetE('txtSize').value < 0 )
	{
		alert( "Width must be a positive number." ) ;
		GetE('txtSize').focus() ;
		return false ;
	}

	oEditor.FCKUndo.SaveUndoStep() ;

	oActiveEl = CreateNamedElement( oEditor, oActiveEl, 'INPUT', {name: GetE('txtName').value, type: GetE('txtType').value } ) ;

	SetAttribute( oActiveEl, 'value'	, GetE('txtValue').value ) ;
	SetAttribute( oActiveEl, 'size'		, GetE('txtSize').value ) ;
	SetAttribute( oActiveEl, 'maxlength', GetE('txtMax').value ) ;

	return true ;
}

	</script>
</head>
<body style="overflow: hidden">
	<table width="100%" style="height: 100%">
		<tr>
			<td align="center">
				<table cellspacing="0" cellpadding="0" border="0">
					<tr>
						<td>
							<span fcklang="DlgTextName">Name</span><br />
							<input id="txtName" type="text" size="20" />
						</td>
						<td>
						</td>
						<td>
							<span fcklang="DlgTextValue">Value</span><br />
							<input id="txtValue" type="text" size="25" />
						</td>
					</tr>
					<tr>
						<td>
							<span fcklang="DlgTextCharWidth">Character Width</span><br />
							<input id="txtSize" type="text" size="5" />
						</td>
						<td>
						</td>
						<td>
							<span fcklang="DlgTextMaxChars">Maximum Characters</span><br />
							<input id="txtMax" type="text" size="5" />
						</td>
					</tr>
					<tr>
						<td>
							<span fcklang="DlgTextType">Type</span><br />
							<select id="txtType">
								<option value="text" selected="selected" fcklang="DlgTextTypeText">Text</option>
								<option value="password" fcklang="DlgTextTypePass">Password</option>
							</select>
						</td>
						<td>
							&nbsp;</td>
						<td>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
</body>
</html>

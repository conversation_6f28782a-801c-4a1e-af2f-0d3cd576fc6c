<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Text Area dialog window.
-->
<html>
	<head>
		<title>Text Area Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<script type="text/javascript">

var dialog	= window.parent ;
var oEditor = dialog.InnerDialogLoaded() ;

// Gets the document DOM
var oDOM = oEditor.FCK.EditorDocument ;

var oActiveEl = dialog.Selection.GetSelectedElement() ;

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	if ( oActiveEl && oActiveEl.tagName == 'TEXTAREA' )
	{
		GetE('txtName').value		= oActiveEl.name ;
		GetE('txtCols').value		= GetAttribute( oActiveEl, 'cols' ) ;
		GetE('txtRows').value		= GetAttribute( oActiveEl, 'rows' ) ;
	}
	else
		oActiveEl = null ;

	dialog.SetOkButton( true ) ;
	dialog.SetAutoSize( true ) ;
	SelectField( 'txtName' ) ;
}

function Ok()
{
	oEditor.FCKUndo.SaveUndoStep() ;

	oActiveEl = CreateNamedElement( oEditor, oActiveEl, 'TEXTAREA', {name: GetE('txtName').value} ) ;

	SetAttribute( oActiveEl, 'cols', GetE('txtCols').value ) ;
	SetAttribute( oActiveEl, 'rows', GetE('txtRows').value ) ;

	return true ;
}

		</script>
	</head>
	<body style="overflow: hidden">
		<table height="100%" width="100%">
			<tr>
				<td align="center">
					<table border="0" cellpadding="0" cellspacing="0" width="80%">
						<tr>
							<td>
								<span fckLang="DlgTextareaName">Name</span><br>
								<input type="text" id="txtName" style="WIDTH: 100%">
								<span fckLang="DlgTextareaCols">Collumns</span><br>
								<input id="txtCols" type="text" size="5">
								<br>
								<span fckLang="DlgTextareaRows">Rows</span><br>
								<input id="txtRows" type="text" size="5">
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>

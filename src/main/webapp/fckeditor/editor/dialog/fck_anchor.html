<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Anchor dialog window.
-->
<html>
	<head>
		<title>Anchor Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<script src="common/fck_dialog_common.js" type="text/javascript"></script>
		<script type="text/javascript">

var dialog			= window.parent ;
var oEditor			= dialog.InnerDialogLoaded() ;

var FCK				= oEditor.FCK ;
var FCKBrowserInfo	= oEditor.FCKBrowserInfo ;
var FCKTools		= oEditor.FCKTools ;
var FCKRegexLib		= oEditor.FCKRegexLib ;

var oDOM			= FCK.EditorDocument ;

var oFakeImage = dialog.Selection.GetSelectedElement() ;

var oAnchor ;

if ( oFakeImage )
{
	if ( oFakeImage.tagName == 'IMG' && oFakeImage.getAttribute('_fckanchor') )
		oAnchor = FCK.GetRealElement( oFakeImage ) ;
	else
		oFakeImage = null ;
}

//Search for a real anchor
if ( !oFakeImage )
{
	oAnchor = FCK.Selection.MoveToAncestorNode( 'A' ) ;
	if ( oAnchor )
		FCK.Selection.SelectNode( oAnchor ) ;
}

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	if ( oAnchor )
		GetE('txtName').value = oAnchor.name ;
	else
		oAnchor = null ;

	window.parent.SetOkButton( true ) ;
	window.parent.SetAutoSize( true ) ;

	SelectField( 'txtName' ) ;
}

function Ok()
{
	var sNewName = GetE('txtName').value ;

	// Remove any illegal character in a name attribute:
	// A name should start with a letter, but the validator passes anyway.
	sNewName = sNewName.replace( /[^\w-_\.:]/g, '_' ) ;

	if ( sNewName.length == 0 )
	{
		// Remove the anchor if the user leaves the name blank
		if ( oAnchor )
		{
			// Removes the current anchor from the document using the new command
			FCK.Commands.GetCommand( 'AnchorDelete' ).Execute() ;
			return true ;
		}

		alert( oEditor.FCKLang.DlgAnchorErrorName ) ;
		return false ;
	}

	oEditor.FCKUndo.SaveUndoStep() ;

	if ( oAnchor )	// Modifying an existent anchor.
	{
		ReadjustLinksToAnchor( oAnchor.name, sNewName );

		// Buggy explorer, bad bad browser. http://alt-tag.com/blog/archives/2006/02/ie-dom-bugs/
		// Instead of just replacing the .name for the existing anchor (in order to preserve the content), we must remove the .name
		// and assign .name, although it won't appear until it's specially processed in fckxhtml.js

		// We remove the previous name
		oAnchor.removeAttribute( 'name' ) ;
		// Now we set it, but later we must process it specially
		oAnchor.name = sNewName ;

		return true ;
	}

	// Create a new anchor preserving the current selection
	var aNewAnchors = oEditor.FCK.CreateLink( '#' ) ;

	if ( aNewAnchors.length == 0 )
	{
		// Nothing was selected, so now just create a normal A
		aNewAnchors.push( oEditor.FCK.InsertElement( 'a' ) ) ;
	}
	else
	{
		// Remove the fake href
		for ( var i = 0 ; i < aNewAnchors.length ; i++ )
			aNewAnchors[i].removeAttribute( 'href' ) ;
	}

	// More than one anchors may have been created, so interact through all of them (see #220).
	for ( var i = 0 ; i < aNewAnchors.length ; i++ )
	{
		oAnchor = aNewAnchors[i] ;

		// Set the name
		oAnchor.name = sNewName ;

		// IE does require special processing to show the Anchor's image
		// Opera doesn't allow to select empty anchors
		if ( FCKBrowserInfo.IsIE || FCKBrowserInfo.IsOpera )
		{
			if ( oAnchor.innerHTML != '' )
			{
				if ( FCKBrowserInfo.IsIE )
					oAnchor.className += ' FCK__AnchorC' ;
			}
			else
			{
				// Create a fake image for both IE and Opera
				var oImg = oEditor.FCKDocumentProcessor_CreateFakeImage( 'FCK__Anchor', oAnchor.cloneNode(true) ) ;
				oImg.setAttribute( '_fckanchor', 'true', 0 ) ;

				oAnchor.parentNode.insertBefore( oImg, oAnchor ) ;
				oAnchor.parentNode.removeChild( oAnchor ) ;
			}

		}
	}

	return true ;
}

// Checks all the links in the current page pointing to the current name and changes them to the new name
function ReadjustLinksToAnchor( sCurrent, sNew )
{
	var oDoc = FCK.EditorDocument ;

	var aLinks = oDoc.getElementsByTagName( 'A' ) ;

	var sReference = '#' + sCurrent ;
	// The url of the document, so we check absolute and partial references.
	var sFullReference = oDoc.location.href.replace( /(#.*$)/, '') ;
	sFullReference += sReference ;

	var oLink ;
	var i = aLinks.length - 1 ;
	while ( i >= 0 && ( oLink = aLinks[i--] ) )
	{
		var sHRef = oLink.getAttribute( '_fcksavedurl' ) ;
		if ( sHRef == null )
			sHRef = oLink.getAttribute( 'href' , 2 ) || '' ;

		if ( sHRef == sReference || sHRef == sFullReference )
		{
			oLink.href = '#' + sNew ;
			SetAttribute( oLink, '_fcksavedurl', '#' + sNew ) ;
		}
	}
}

		</script>
	</head>
	<body style="overflow: hidden">
		<table height="100%" width="100%">
			<tr>
				<td align="center">
					<table border="0" cellpadding="0" cellspacing="0" width="80%">
						<tr>
							<td>
								<span fckLang="DlgAnchorName">Anchor Name</span><BR>
								<input id="txtName" style="WIDTH: 100%" type="text">
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>

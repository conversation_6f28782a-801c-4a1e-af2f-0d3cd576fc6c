<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * "About" dialog window.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title></title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
	<script src="common/fck_dialog_common.js" type="text/javascript"></script>
	<script type="text/javascript">

var oEditor = window.parent.InnerDialogLoaded() ;
var FCKLang	= oEditor.FCKLang ;

window.parent.AddTab( 'About', FCKLang.DlgAboutAboutTab ) ;
window.parent.AddTab( 'License', FCKLang.DlgAboutLicenseTab ) ;
window.parent.AddTab( 'BrowserInfo', FCKLang.DlgAboutBrowserInfoTab ) ;

// Function called when a dialog tag is selected.
function OnDialogTabChange( tabCode )
{
	ShowE('divAbout', ( tabCode == 'About' ) ) ;
	ShowE('divLicense', ( tabCode == 'License' ) ) ;
	ShowE('divInfo'	, ( tabCode == 'BrowserInfo' ) ) ;
}

function SendEMail()
{
	var eMail = 'mailto:' ;
	eMail += 'fredck' ;
	eMail += '@' ;
	eMail += 'fckeditor' ;
	eMail += '.' ;
	eMail += 'net' ;

	window.location = eMail ;
}

window.onload = function()
{
	// Translate the dialog box texts.
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	window.parent.SetAutoSize( true ) ;
}

	</script>
</head>
<body style="overflow: hidden">
	<div id="divAbout">
		<table cellpadding="0" cellspacing="0" border="0" width="100%" style="height: 100%">
			<tr>
				<td colspan="2">
					<img alt="" src="fck_about/logo_fckeditor.gif" width="236" height="41" align="left" />
					<table width="80" border="0" cellspacing="0" cellpadding="5" bgcolor="#ffffff" align="right">
						<tr>
							<td align="center" nowrap="nowrap" style="border-right: #000000 1px solid; border-top: #000000 1px solid;
								border-left: #000000 1px solid; border-bottom: #000000 1px solid">
								<span fcklang="DlgAboutVersion">version</span>
								<br />
								<b>2.6.2</b><br />
								Build 19417</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr style="height: 100%">
				<td align="center" valign="middle">
					<span style="font-size: 14px" dir="ltr">
						<b><a href="http://www.fckeditor.net/?about" target="_blank" title="Visit the FCKeditor web site">
							Support <b>Open Source</b> Software</a></b> </span>
					<div style="padding-top:15px">
						<img alt="" src="fck_about/logo_fredck.gif" width="87" height="36" />
					</div>
				</td>
				<td align="center" nowrap="nowrap" valign="middle">
					<div>
						<div style="margin-bottom:5px" dir="ltr">Selected Sponsor</div>
						<a href="http://www.spellchecker.net/fckeditor/" target="_blank"><img alt="Selected Sponsor" border="0" src="fck_about/sponsors/spellchecker_net.gif" width="75" height="75" /></a>
					</div>
				</td>
			</tr>
			<tr>
				<td width="100%" nowrap="nowrap">
					<span fcklang="DlgAboutInfo">For further information go to</span> <a href="http://www.fckeditor.net/?About"
						target="_blank">http://www.fckeditor.net/</a>.
					<br />
					Copyright &copy; 2003-2008 <a href="#" onclick="SendEMail();">Frederico Caldeira Knabben</a>
				</td>
				<td align="center">
					<a href="http://www.fckeditor.net/sponsors/apply" target="_blank">Become a Sponsor</a>
				</td>
			</tr>
		</table>
	</div>
	<div id="divLicense" style="display: none">
			<p>
				Licensed under the terms of any of the following licenses at your
				choice:
			</p>
			<ul>
				<li style="margin-bottom:15px">
					<b>GNU General Public License</b> Version 2 or later (the "GPL")<br />
					<a href="http://www.gnu.org/licenses/gpl.html" target="_blank">http://www.gnu.org/licenses/gpl.html</a>
				</li>
				<li style="margin-bottom:15px">
					<b>GNU Lesser General Public License</b> Version 2.1 or later (the "LGPL")<br />
					<a href="http://www.gnu.org/licenses/lgpl.html" target="_blank">http://www.gnu.org/licenses/lgpl.html</a>
				</li>
				<li>
					<b>Mozilla Public License</b> Version 1.1 or later (the "MPL")<br />
					<a href="http://www.mozilla.org/MPL/MPL-1.1.html" target="_blank">http://www.mozilla.org/MPL/MPL-1.1.html</a>
			   </li>
			</ul>
	</div>
	<div id="divInfo" style="display: none" dir="ltr">
		<table align="center" width="80%" border="0">
			<tr>
				<td>
					<script type="text/javascript">
<!--
document.write( '<b>User Agent<\/b><br />' + window.navigator.userAgent + '<br /><br />' ) ;
document.write( '<b>Browser<\/b><br />' + window.navigator.appName + ' ' + window.navigator.appVersion + '<br /><br />' ) ;
document.write( '<b>Platform<\/b><br />' + window.navigator.platform + '<br /><br />' ) ;

var sUserLang = '?' ;

if ( window.navigator.language )
	sUserLang = window.navigator.language.toLowerCase() ;
else if ( window.navigator.userLanguage )
	sUserLang = window.navigator.userLanguage.toLowerCase() ;

document.write( '<b>User Language<\/b><br />' + sUserLang ) ;
//-->
					</script>
				</td>
			</tr>
		</table>
	</div>
</body>
</html>

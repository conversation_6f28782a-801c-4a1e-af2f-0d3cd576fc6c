<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Smileys (emoticons) dialog window.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title></title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
	<style type="text/css">
		.Hand
		{
			cursor: pointer;
			cursor: hand;
		}
	</style>
	<script src="common/fck_dialog_common.js" type="text/javascript"></script>
	<script type="text/javascript">

var dialog	= window.parent ;
var oEditor = dialog.InnerDialogLoaded() ;

window.onload = function ()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	dialog.SetAutoSize( true ) ;
}

function InsertSmiley( url )
{
	oEditor.FCKUndo.SaveUndoStep() ;

	var oImg = oEditor.FCK.InsertElement( 'img' ) ;
	oImg.src = url ;
	oImg.setAttribute( '_fcksavedurl', url ) ;

	// For long smileys list, it seams that IE continues loading the images in
	// the background when you quickly select one image. so, let's clear
	// everything before closing.
	document.body.innerHTML = '' ;

	dialog.Cancel() ;
}

function over(td)
{
	td.className = 'LightBackground Hand' ;
}

function out(td)
{
	td.className = 'DarkBackground Hand' ;
}
	</script>
</head>
<body style="overflow: hidden">
	<table cellpadding="2" cellspacing="2" align="center" border="0" width="100%" height="100%">
		<script type="text/javascript">

var FCKConfig = oEditor.FCKConfig ;

var sBasePath	= FCKConfig.SmileyPath ;
var aImages		= FCKConfig.SmileyImages ;
var iCols		= FCKConfig.SmileyColumns ;
var iColWidth	= parseInt( 100 / iCols, 10 ) ;

var i = 0 ;
while (i < aImages.length)
{
	document.write( '<tr>' ) ;
	for(var j = 0 ; j < iCols ; j++)
	{
		if (aImages[i])
		{
			var sUrl = sBasePath + aImages[i] ;
			document.write( '<td width="' + iColWidth + '%" align="center" class="DarkBackground Hand" onclick="InsertSmiley(\'' + sUrl.replace(/'/g, "\\'" ) + '\')" onmouseover="over(this)" onmouseout="out(this)">' ) ;
			document.write( '<img src="' + sUrl + '" border="0" />' ) ;
		}
		else
			document.write( '<td width="' + iColWidth + '%" class="DarkBackground">&nbsp;' ) ;
		document.write( '<\/td>' ) ;
		i++ ;
	}
	document.write('<\/tr>') ;
}

		</script>
	</table>
</body>
</html>

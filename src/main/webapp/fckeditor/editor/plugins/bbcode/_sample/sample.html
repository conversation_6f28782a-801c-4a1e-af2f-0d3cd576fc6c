<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Sample page.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>FCKeditor - BBCode Sample</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="robots" content="noindex, nofollow" />
	<link href="../../../../_samples/sample.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="../../../../fckeditor.js"></script>
</head>
<body>
	<h1>
		FCKeditor - BBCode Sample</h1>
	<p>
		This is a sample of custom Data Processor implementation for (very) basic BBCode
		syntax. Only <strong>[b]</strong>, <strong>[i]</strong>, <strong>[u]</strong> and
		<strong>[url]</strong> may be used. It may be extended, but this is out of this
		sample purpose.
	</p>
	<p>
		Note that the input and output of the editor is not HTML, but BBCode
	</p>
	<hr />
	<form action="../../../../_samples/html/sampleposteddata.asp" method="post" target="_blank">
		<script type="text/javascript">
<!--
// Automatically calculates the editor base path based on the _samples directory.
// This is usefull only for these samples. A real application should use something like this:
// oFCKeditor.BasePath = '/fckeditor/' ;	// '/fckeditor/' is the default value.
var sBasePath = document.location.pathname.substring(0,document.location.pathname.lastIndexOf('editor')) ;

var oFCKeditor = new FCKeditor( 'FCKeditor1' ) ;
oFCKeditor.BasePath	= sBasePath ;

// Set the custom configurations file path (in this way the original file is mantained).
oFCKeditor.Config['CustomConfigurationsPath'] = sBasePath + 'editor/plugins/bbcode/_sample/sample.config.js' ;

oFCKeditor.Value = 'This is some [b]sample text[/b]. You are using [url=http://www.fckeditor.net/]FCKeditor[/url].' ;
oFCKeditor.Create() ;
//-->
		</script>
		<br />
		<input type="submit" value="Submit" />
	</form>
</body>
</html>

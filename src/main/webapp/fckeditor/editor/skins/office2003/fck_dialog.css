/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Styles used by the dialog boxes.
 */

html, body
{
	background-color: transparent;
	margin: 0px;
	padding: 0px;
}

body
{
	padding: 10px;
}

body, td, input, select, textarea
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Arial, Helvetica, Verdana;
}

body, .BackColor
{
	background-color: #f7f8fd;
}

.PopupBody
{
	height: 100%;
	width: 100%;
	overflow: hidden;
	background-color: transparent;
	padding: 0px;
}

#header
{
	cursor: move;
}

.PopupTitle
{
	font-weight: bold;
	font-size: 14pt;
	color: #0e3460;
	background-color: #8cb2fd;
	padding: 3px 10px 3px 10px;
}

.PopupButtons
{
	position: absolute;
	right: 0px;
	left: 0px;
	bottom: 0px;
	border-top: #466ca6 1px solid;
	background-color: #8cb2fd;
	padding: 7px 10px 7px 10px;
}

.Button
{
	border: #1c3460 1px solid;
	color: #000a28;
	background-color: #7096d3;
}

#btnOk
{
	width: 100px;
}

.DarkBackground
{
	background-color: #eaf2f8;
}

.LightBackground
{
	background-color: #ffffbe;
}

.PopupTitleBorder
{
	border-bottom: #d5d59d 1px solid;
}

.PopupTabArea
{
	color: #0e3460;
	background-color: #8cb2fd;
}

.PopupTabEmptyArea
{
	padding-left: 10px ;
	border-bottom: #466ca6 1px solid;
}

.PopupTab, .PopupTabSelected
{
	border-right: #466ca6 1px solid;
	border-top: #466ca6 1px solid;
	border-left: #466ca6 1px solid;
	padding: 3px 5px 3px 5px;
	color: #0e3460;
}

.PopupTab
{
	margin-top: 1px;
	border-bottom: #466ca6 1px solid;
	cursor: pointer;
	cursor: hand;
}

.PopupTabSelected
{
	font-weight: bold;
	cursor: default;
	padding-top: 4px;
	border-bottom: #f7f8fd 1px solid;
	background-color: #f7f8fd;
}

.PopupSelectionBox
{
	border: #1e90ff 1px solid !important;
	background-color: #add8e6 !important;
	cursor: pointer;
	cursor: hand;
}

#tdBrowse
{
	vertical-align: bottom;
}

/**
 * Dialog frame related styles.
 */

.contents
{
	position: absolute;
	top: 2px;
	left: 16px;
	right: 16px;
	bottom: 20px;
	background-color: #f7f8fD;
	overflow: hidden;
	z-index: 1;
}

.tl, .tr, .tc, .bl, .br, .bc
{
	position: absolute;
	background-image: url(images/sprites.png);
	background-repeat: no-repeat;
}

* html .tl, * html .tr, * html .tc, * html .bl, * html .br, * html .bc
{
	background-image: url(images/sprites.gif);
}

.ml, .mr
{
	position: absolute;
	background-image: url(images/dialog.sides.png);
	background-repeat: repeat-y;
}

* html .ml, * html .mr
{
	background-image: url(images/dialog.sides.gif);
}

.rtl .ml, .rtl .mr
{
	position: absolute;
	background-image: url(images/dialog.sides.rtl.png);
	background-repeat: repeat-y;
}

* html .rtl .ml, * html .rtl .mr
{
	background-image: url(images/dialog.sides.gif);
}

.tl
{
	top: 0px;
	left: 0px;
	width: 16px;
	height: 16px;
	background-position: -16px -16px;
}

.rtl .tl
{
	background-position: -16px -397px;
}

.tr
{
	top: 0px;
	right: 0px;
	width: 16px;
	height: 16px;
	background-position: -16px -76px;
}

.rtl .tr
{
	background-position: -16px -457px;
}

.tc
{
	top: 0px;
	right: 16px;
	left: 16px;
	height: 16px;
	background-position: 0px -136px;
	background-repeat: repeat-x;
}

.ml
{
	top: 16px;
	left: 0px;
	width: 16px;
	bottom: 51px;
	background-position: 0px 0px;
}

.mr
{
	top: 16px;
	right: 0px;
	width: 16px;
	bottom: 51px;
	background-position: -16px 0px;
}

.bl
{
	bottom: 0px;
	left: 0px;
	width: 30px;
	height: 51px;
	background-position: -16px -196px;
}

.rtl .bl
{
	background-position: -16px -517px;
}

.br
{
	bottom: 0px;
	right: 0px;
	width: 30px;
	height: 51px;
	background-position: -16px -263px;
}

.rtl .br
{
	background-position: -16px -584px;
}

.bc
{
	bottom: 0px;
	right: 30px;
	left: 30px;
	height: 51px;
	background-position: 0px -330px;
	background-repeat: repeat-x;
}

/* For IE6. Do not change it. */
* html .blocker
{
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 12;
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=0);
}

/* The layer used to cover the dialog when opening a child dialog. */
.cover
{
	position: absolute;
	top: 0px;
	left: 14px;
	right: 14px;
	bottom: 18px;
	z-index: 11;
}

#closeButton
{
	position: absolute;
	right: 0px;
	top: 0px;
	margin-top: 5px;
	margin-right: 10px;
	width: 20px;
	height: 20px;
	cursor: pointer;
	background-image: url(images/sprites.png);
	background-repeat: no-repeat;
	background-position: -16px -651px;
}

* html #closeButton
{
	cursor: hand;
	background-image: url(images/sprites.gif);
}

.rtl #closeButton
{
	right: auto;
	left: 10px;
	margin-right: 0px;
}

#closeButton:hover
{
	background-position: -16px -687px;
}

#throbberBlock
{
	z-index: 10;
}

#throbberBlock div
{
	float: left;
	width: 8px;
	height: 9px;
	margin-left: 2px;
	margin-right: 2px;
	font-size: 1px;	/* IE6 */
}

/*
	Color Gradient Generator:
	http://www.herethere.net/~samson/php/color_gradient/?cbegin=0E3460&cend=8cb2fd&steps=4
*/

.throbber_1
{
	background-color: #0E3460;
}

.throbber_2
{
	background-color: #2D5387;
}

.throbber_3
{
	background-color: #4D73AE;
}

.throbber_4
{
	background-color: #6C92D5;
}

.throbber_5
{
	background-color: #8CB2FD;
}

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Useful page that enumerates all icons in the skins strips.
-->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>FCKeditor - View Icons Strips</title>
	<style type="text/css">
		.TB_Button_Image
		{
			overflow: hidden;
			width: 16px;
			height: 16px;
			margin: 3px;
			background-repeat: no-repeat;
		}

		.TB_Button_Image img
		{
			position: relative;
		}
	</style>
	<script type="text/javascript">

window.onload = function()
{
	var eImg1 = document.createElement( 'img' ) ;
	eImg1.onload = Img_OnLoad ;
	eImg1.src = 'default/fck_strip.gif' ;

	var eImg2 = document.createElement( 'img' ) ;
	eImg2.onload = Img_OnLoad ;
	eImg2.src = 'office2003/fck_strip.gif' ;

	var eImg3 = document.createElement( 'img' ) ;
	eImg3.onload = Img_OnLoad ;
	eImg3.src = 'silver/fck_strip.gif' ;
}

var iTotalStrips = 3 ;
var iMaxHeight = 0 ;

function Img_OnLoad()
{
	if ( iMaxHeight < this.height )
		iMaxHeight = this.height ;

	iTotalStrips-- ;

	if ( iTotalStrips == 0 )
		LoadIcons( iMaxHeight / 16 ) ;
}

function LoadIcons( total )
{
	var xIconsTable = document.getElementById( 'xIconsTable' ) ;

	for ( var i = 0 ; i < total ; i++ )
	{
		var eRow = xIconsTable.insertRow(-1) ;

		var eCell = eRow.insertCell(-1) ;
		eCell.innerHTML = i + 1 ;

		eCell = eRow.insertCell(-1) ;
		eCell.align = 'center' ;
		eCell.style.border = '#dcdcdc 1px solid' ;
		eCell.innerHTML = '<div class="TB_Button_Image"><img src="default/fck_strip.gif" style="top:-' + ( i * 16 ) + 'px;"><\/div>' ;

		eCell = eRow.insertCell(-1) ;
		eCell.align = 'center' ;
		eCell.style.border = '#dcdcdc 1px solid' ;
		eCell.innerHTML = '<div class="TB_Button_Image"><img src="office2003/fck_strip.gif" style="top:-' + ( i * 16 ) + 'px;"><\/div>' ;

		eCell = eRow.insertCell(-1) ;
		eCell.align = 'center' ;
		eCell.style.border = '#dcdcdc 1px solid' ;
		eCell.innerHTML = '<div class="TB_Button_Image"><img src="silver/fck_strip.gif" style="top:-' + ( i * 16 ) + 'px;"><\/div>' ;
	}
}

	</script>
</head>
<body>
	<table id="xIconsTable">
		<tr>
			<td rowspan="2">
				Index</td>
			<td align="center" colspan="3">
				Skins</td>
		</tr>
		<tr>
			<td width="80" align="center">
				default</td>
			<td width="80" align="center">
				office2003</td>
			<td width="80" align="center">
				silver</td>
		</tr>
	</table>
</body>
</html>

/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Styles used by the dialog boxes.
 */

html, body
{
	background-color: transparent;
	margin: 0px;
	padding: 0px;
}

body
{
	padding: 10px;
}

body, td, input, select, textarea
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Arial, Helvetica, Verdana;
}

body, .BackColor
{
	background-color: #f1f1e3;
}

.PopupBody
{
	height: 100%;
	width: 100%;
	overflow: hidden;
	background-color: transparent;
	padding: 0px;
}

#header
{
	cursor: move;
}

.PopupTitle
{
	font-weight: bold;
	font-size: 14pt;
	color: #737357;
	background-color: #e3e3c7;
	padding: 3px 10px 3px 10px;
}

.PopupButtons
{
	position: absolute;
	right: 0px;
	left: 0px;
	bottom: 0px;
	border-top: #d5d59d 1px solid;
	background-color: #e3e3c7;
	padding: 7px 10px 7px 10px;
}

.Button
{
	border: #737357 1px solid;
	color: #3b3b1f;
	background-color: #c7c78f;
}

#btnOk
{
	width: 100px;
}

.DarkBackground
{
	background-color: #eaead1;
}

.LightBackground
{
	background-color: #ffffbe;
}

.PopupTitleBorder
{
	border-bottom: #d5d59d 1px solid;
}

.PopupTabArea
{
	color: #737357;
	background-color: #e3e3c7;
}

.PopupTabEmptyArea
{
	padding-left: 10px;
	border-bottom: #d5d59d 1px solid;
}

.PopupTab, .PopupTabSelected
{
	border-right: #d5d59d 1px solid;
	border-top: #d5d59d 1px solid;
	border-left: #d5d59d 1px solid;
	padding: 3px 5px 3px 5px;
	color: #737357;
}

.PopupTab
{
	margin-top: 1px;
	border-bottom: #d5d59d 1px solid;
	cursor: pointer;
	cursor: hand;
}

.PopupTabSelected
{
	font-weight: bold;
	cursor: default;
	padding-top: 4px;
	border-bottom: #f1f1e3 1px solid;
	background-color: #f1f1e3;
}

.PopupSelectionBox
{
	border: #ff9933 1px solid !important;
	background-color: #fffacd !important;
	cursor: pointer;
	cursor: hand;
}

#tdBrowse
{
	vertical-align: bottom;
}

/**
 * Dialog frame related styles.
 */

.contents
{
	position: absolute;
	top: 2px;
	left: 16px;
	right: 16px;
	bottom: 20px;
	background-color: #f1f1e3;
	overflow: hidden;
	z-index: 1;
}

.tl, .tr, .tc, .bl, .br, .bc
{
	position: absolute;
	background-image: url(images/sprites.png);
	background-repeat: no-repeat;
}

* html .tl, * html .tr, * html .tc, * html .bl, * html .br, * html .bc
{
	background-image: url(images/sprites.gif);
}

.ml, .mr
{
	position: absolute;
	background-image: url(images/dialog.sides.png);
	background-repeat: repeat-y;
}

* html .ml, * html .mr
{
	background-image: url(images/dialog.sides.gif);
}

.rtl .ml, .rtl .mr
{
	position: absolute;
	background-image: url(images/dialog.sides.rtl.png);
	background-repeat: repeat-y;
}

* html .rtl .ml, * html .rtl .mr
{
	background-image: url(images/dialog.sides.gif);
}

.tl
{
	top: 0px;
	left: 0px;
	width: 16px;
	height: 16px;
	background-position: -16px -16px;
}

.rtl .tl
{
	background-position: -16px -397px;
}

.tr
{
	top: 0px;
	right: 0px;
	width: 16px;
	height: 16px;
	background-position: -16px -76px;
}

.rtl .tr
{
	background-position: -16px -457px;
}

.tc
{
	top: 0px;
	right: 16px;
	left: 16px;
	height: 16px;
	background-position: 0px -136px;
	background-repeat: repeat-x;
}

.ml
{
	top: 16px;
	left: 0px;
	width: 16px;
	bottom: 51px;
	background-position: 0px 0px;
}

.mr
{
	top: 16px;
	right: 0px;
	width: 16px;
	bottom: 51px;
	background-position: -16px 0px;
}

.bl
{
	bottom: 0px;
	left: 0px;
	width: 30px;
	height: 51px;
	background-position: -16px -196px;
}

.rtl .bl
{
	background-position: -16px -517px;
}

.br
{
	bottom: 0px;
	right: 0px;
	width: 30px;
	height: 51px;
	background-position: -16px -263px;
}

.rtl .br
{
	background-position: -16px -584px;
}

.bc
{
	bottom: 0px;
	right: 30px;
	left: 30px;
	height: 51px;
	background-position: 0px -330px;
	background-repeat: repeat-x;
}

/* For IE6. Do not change it. */
* html .blocker
{
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 12;
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=0);
}

/* The layer used to cover the dialog when opening a child dialog. */
.cover
{
	position: absolute;
	top: 0px;
	left: 14px;
	right: 14px;
	bottom: 18px;
	z-index: 11;
}

#closeButton
{
	position: absolute;
	right: 0px;
	top: 0px;
	margin-top: 5px;
	margin-right: 10px;
	width: 20px;
	height: 20px;
	cursor: pointer;
	background-image: url(images/sprites.png);
	background-repeat: no-repeat;
	background-position: -16px -651px;
}

* html #closeButton
{
	cursor: hand;
	background-image: url(images/sprites.gif);
}

.rtl #closeButton
{
	right: auto;
	left: 10px;
	margin-right: 0px;
}

#closeButton:hover
{
	background-position: -16px -687px;
}

#throbberBlock
{
	z-index: 10;
}

#throbberBlock div
{
	float: left;
	width: 8px;
	height: 9px;
	margin-left: 2px;
	margin-right: 2px;
	font-size: 1px;	/* IE6 */
}

/*
	Color Gradient Generator:
	http://www.herethere.net/~samson/php/color_gradient/?cbegin=737357&cend=E3E3C7&steps=4
*/

.throbber_1
{
	background-color: #737357;
}

.throbber_2
{
	background-color: #8f8f73;
}

.throbber_3
{
	background-color: #abab8f;
}

.throbber_4
{
	background-color: #c7c7ab;
}

.throbber_5
{
	background-color: #e3e3c7;
}

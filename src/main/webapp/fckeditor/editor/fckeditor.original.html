ï»¿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Main page that holds the editor.
-->
<html>
<head>
	<title>FCKeditor</title>
	<meta name="robots" content="noindex, nofollow">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<!-- @Packager.RemoveLine
	<meta http-equiv="Cache-Control" content="public">
	@Packager.RemoveLine -->
	<script type="text/javascript">

// Save a reference to the default domain.
var FCK_ORIGINAL_DOMAIN ;

// Automatically detect the correct document.domain (#123).
(function()
{
	var d = FCK_ORIGINAL_DOMAIN = document.domain ;

	while ( true )
	{
		// Test if we can access a parent property.
		try
		{
			var test = window.parent.document.domain ;
			break ;
		}
		catch( e ) {}

		// Remove a domain part: www.mytest.example.com => mytest.example.com => example.com ...
		d = d.replace( /.*?(?:\.|$)/, '' ) ;

		if ( d.length == 0 )
			break ;		// It was not able to detect the domain.

		try
		{
			document.domain = d ;
		}
		catch (e)
		{
			break ;
		}
	}
})() ;

// Save a reference to the detected runtime domain.
var FCK_RUNTIME_DOMAIN = document.domain ;

var FCK_IS_CUSTOM_DOMAIN = ( FCK_ORIGINAL_DOMAIN != FCK_RUNTIME_DOMAIN ) ;

// Instead of loading scripts and CSSs using inline tags, all scripts are
// loaded by code. In this way we can guarantee the correct processing order,
// otherwise external scripts and inline scripts could be executed in an
// unwanted order (IE).

function LoadScript( url )
{
	document.write( '<scr' + 'ipt type="text/javascript" src="' + url + '"><\/scr' + 'ipt>' ) ;
}

// Main editor scripts.
var sSuffix = ( /*@cc_on!@*/false ) ? 'ie' : 'gecko' ;

/* @Packager.RemoveLine
LoadScript( 'js/fckeditorcode_' + sSuffix + '.js' ) ;
@Packager.RemoveLine */
// @Packager.Remove.Start

LoadScript( '_source/fckconstants.js' ) ;
LoadScript( '_source/fckjscoreextensions.js' ) ;

if ( sSuffix == 'ie' )
	LoadScript( '_source/classes/fckiecleanup.js' ) ;

LoadScript( '_source/internals/fckbrowserinfo.js' ) ;
LoadScript( '_source/internals/fckurlparams.js' ) ;
LoadScript( '_source/classes/fckevents.js' ) ;
LoadScript( '_source/classes/fckdataprocessor.js' ) ;
LoadScript( '_source/internals/fck.js' ) ;
LoadScript( '_source/internals/fck_' + sSuffix + '.js' ) ;
LoadScript( '_source/internals/fckconfig.js' ) ;

LoadScript( '_source/internals/fckdebug_empty.js' ) ;
LoadScript( '_source/internals/fckdomtools.js' ) ;
LoadScript( '_source/internals/fcktools.js' ) ;
LoadScript( '_source/internals/fcktools_' + sSuffix + '.js' ) ;
LoadScript( '_source/fckeditorapi.js' ) ;
LoadScript( '_source/classes/fckimagepreloader.js' ) ;
LoadScript( '_source/internals/fckregexlib.js' ) ;
LoadScript( '_source/internals/fcklistslib.js' ) ;
LoadScript( '_source/internals/fcklanguagemanager.js' ) ;
LoadScript( '_source/internals/fckxhtmlentities.js' ) ;
LoadScript( '_source/internals/fckxhtml.js' ) ;
LoadScript( '_source/internals/fckxhtml_' + sSuffix + '.js' ) ;
LoadScript( '_source/internals/fckcodeformatter.js' ) ;
LoadScript( '_source/internals/fckundo.js' ) ;
LoadScript( '_source/classes/fckeditingarea.js' ) ;
LoadScript( '_source/classes/fckkeystrokehandler.js' ) ;

LoadScript( 'dtd/fck_xhtml10transitional.js' ) ;
LoadScript( '_source/classes/fckstyle.js' ) ;
LoadScript( '_source/internals/fckstyles.js' ) ;

LoadScript( '_source/internals/fcklisthandler.js' ) ;
LoadScript( '_source/classes/fckelementpath.js' ) ;
LoadScript( '_source/classes/fckdomrange.js' ) ;
LoadScript( '_source/classes/fckdocumentfragment_' + sSuffix + '.js' ) ;
LoadScript( '_source/classes/fckw3crange.js' ) ;
LoadScript( '_source/classes/fckdomrange_' + sSuffix + '.js' ) ;
LoadScript( '_source/classes/fckdomrangeiterator.js' ) ;
LoadScript( '_source/classes/fckenterkey.js' ) ;

LoadScript( '_source/internals/fckdocumentprocessor.js' ) ;
LoadScript( '_source/internals/fckselection.js' ) ;
LoadScript( '_source/internals/fckselection_' + sSuffix + '.js' ) ;

LoadScript( '_source/internals/fcktablehandler.js' ) ;
LoadScript( '_source/internals/fcktablehandler_' + sSuffix + '.js' ) ;
LoadScript( '_source/classes/fckxml.js' ) ;
LoadScript( '_source/classes/fckxml_' + sSuffix + '.js' ) ;

LoadScript( '_source/commandclasses/fcknamedcommand.js' ) ;
LoadScript( '_source/commandclasses/fckstylecommand.js' ) ;
LoadScript( '_source/commandclasses/fck_othercommands.js' ) ;
LoadScript( '_source/commandclasses/fckshowblocks.js' ) ;
LoadScript( '_source/commandclasses/fckspellcheckcommand_' + sSuffix + '.js' ) ;
LoadScript( '_source/commandclasses/fcktextcolorcommand.js' ) ;
LoadScript( '_source/commandclasses/fckpasteplaintextcommand.js' ) ;
LoadScript( '_source/commandclasses/fckpastewordcommand.js' ) ;
LoadScript( '_source/commandclasses/fcktablecommand.js' ) ;
LoadScript( '_source/commandclasses/fckfitwindow.js' ) ;
LoadScript( '_source/commandclasses/fcklistcommands.js' ) ;
LoadScript( '_source/commandclasses/fckjustifycommands.js' ) ;
LoadScript( '_source/commandclasses/fckindentcommands.js' ) ;
LoadScript( '_source/commandclasses/fckblockquotecommand.js' ) ;
LoadScript( '_source/commandclasses/fckcorestylecommand.js' ) ;
LoadScript( '_source/commandclasses/fckremoveformatcommand.js' ) ;
LoadScript( '_source/internals/fckcommands.js' ) ;

LoadScript( '_source/classes/fckpanel.js' ) ;
LoadScript( '_source/classes/fckicon.js' ) ;
LoadScript( '_source/classes/fcktoolbarbuttonui.js' ) ;
LoadScript( '_source/classes/fcktoolbarbutton.js' ) ;
LoadScript( '_source/classes/fckspecialcombo.js' ) ;
LoadScript( '_source/classes/fcktoolbarspecialcombo.js' ) ;
LoadScript( '_source/classes/fcktoolbarstylecombo.js' ) ;
LoadScript( '_source/classes/fcktoolbarfontformatcombo.js' ) ;
LoadScript( '_source/classes/fcktoolbarfontscombo.js' ) ;
LoadScript( '_source/classes/fcktoolbarfontsizecombo.js' ) ;
LoadScript( '_source/classes/fcktoolbarpanelbutton.js' ) ;
LoadScript( '_source/internals/fcktoolbaritems.js' ) ;
LoadScript( '_source/classes/fcktoolbar.js' ) ;
LoadScript( '_source/classes/fcktoolbarbreak_' + sSuffix + '.js' ) ;
LoadScript( '_source/internals/fcktoolbarset.js' ) ;
LoadScript( '_source/internals/fckdialog.js' ) ;
LoadScript( '_source/classes/fckmenuitem.js' ) ;
LoadScript( '_source/classes/fckmenublock.js' ) ;
LoadScript( '_source/classes/fckmenublockpanel.js' ) ;
LoadScript( '_source/classes/fckcontextmenu.js' ) ;
LoadScript( '_source/internals/fck_contextmenu.js' ) ;
LoadScript( '_source/classes/fckhtmliterator.js' ) ;
LoadScript( '_source/classes/fckplugin.js' ) ;
LoadScript( '_source/internals/fckplugins.js' ) ;

// @Packager.Remove.End

// Base configuration file.
LoadScript( '../fckconfig.js' ) ;

	</script>
	<script type="text/javascript">

// Adobe AIR compatibility file.
if ( FCKBrowserInfo.IsAIR )
	LoadScript( 'js/fckadobeair.js' ) ;

if ( FCKBrowserInfo.IsIE )
{
	// Remove IE mouse flickering.
	try
	{
		document.execCommand( 'BackgroundImageCache', false, true ) ;
	}
	catch (e)
	{
		// We have been reported about loading problems caused by the above
		// line. For safety, let's just ignore errors.
	}

	// Create the default cleanup object used by the editor.
	FCK.IECleanup = new FCKIECleanup( window ) ;
	FCK.IECleanup.AddItem( FCKTempBin, FCKTempBin.Reset ) ;
	FCK.IECleanup.AddItem( FCK, FCK_Cleanup ) ;
}

// The first function to be called on selection change must the the styles
// change checker, because the result of its processing may be used by another
// functions listening to the same event.
FCK.Events.AttachEvent( 'OnSelectionChange', function() { FCKStyles.CheckSelectionChanges() ; } ) ;

// The config hidden field is processed immediately, because
// CustomConfigurationsPath may be set in the page.
FCKConfig.ProcessHiddenField() ;

// Load the custom configurations file (if defined).
if ( FCKConfig.CustomConfigurationsPath.length > 0 )
	LoadScript( FCKConfig.CustomConfigurationsPath ) ;

	</script>
	<script type="text/javascript">

// Load configurations defined at page level.
FCKConfig_LoadPageConfig() ;

FCKConfig_PreProcess() ;

// Load the full debug script.
if ( FCKConfig.Debug )
	LoadScript( '_source/internals/fckdebug.js' ) ;

	</script>
	<script type="text/javascript">

var FCK_InternalCSS			= FCKConfig.BasePath + 'css/fck_internal.css' ;					// @Packager.RemoveLine
var FCK_ShowTableBordersCSS	= FCKConfig.BasePath + 'css/fck_showtableborders_gecko.css' ;	// @Packager.RemoveLine
/* @Packager.RemoveLine
// CSS minified by http://iceyboard.no-ip.org/projects/css_compressor (see _dev/css_compression.txt).
var FCK_InternalCSS			= FCKTools.FixCssUrls( FCKConfig.BasePath + 'css/', 'html{min-height:100%}table.FCK__ShowTableBorders,table.FCK__ShowTableBorders td,table.FCK__ShowTableBorders th{border:#d3d3d3 1px solid}form{border:1px dotted #F00;padding:2px}.FCK__Flash{border:#a9a9a9 1px solid;background-position:center center;background-image:url(images/fck_flashlogo.gif);background-repeat:no-repeat;width:80px;height:80px}.FCK__UnknownObject{border:#a9a9a9 1px solid;background-position:center center;background-image:url(images/fck_plugin.gif);background-repeat:no-repeat;width:80px;height:80px}.FCK__Anchor{border:1px dotted #00F;background-position:center center;background-image:url(images/fck_anchor.gif);background-repeat:no-repeat;width:16px;height:15px;vertical-align:middle}.FCK__AnchorC{border:1px dotted #00F;background-position:1px center;background-image:url(images/fck_anchor.gif);background-repeat:no-repeat;padding-left:18px}a[name]{border:1px dotted #00F;background-position:0 center;background-image:url(images/fck_anchor.gif);background-repeat:no-repeat;padding-left:18px}.FCK__PageBreak{background-position:center center;background-image:url(images/fck_pagebreak.gif);background-repeat:no-repeat;clear:both;display:block;float:none;width:100%;border-top:#999 1px dotted;border-bottom:#999 1px dotted;border-right:0;border-left:0;height:5px}.FCK__InputHidden{width:19px;height:18px;background-image:url(images/fck_hiddenfield.gif);background-repeat:no-repeat;vertical-align:text-bottom;background-position:center center}.FCK__ShowBlocks p,.FCK__ShowBlocks div,.FCK__ShowBlocks pre,.FCK__ShowBlocks address,.FCK__ShowBlocks blockquote,.FCK__ShowBlocks h1,.FCK__ShowBlocks h2,.FCK__ShowBlocks h3,.FCK__ShowBlocks h4,.FCK__ShowBlocks h5,.FCK__ShowBlocks h6{background-repeat:no-repeat;border:1px dotted gray;padding-top:8px;padding-left:8px}.FCK__ShowBlocks p{background-image:url(images/block_p.png)}.FCK__ShowBlocks div{background-image:url(images/block_div.png)}.FCK__ShowBlocks pre{background-image:url(images/block_pre.png)}.FCK__ShowBlocks address{background-image:url(images/block_address.png)}.FCK__ShowBlocks blockquote{background-image:url(images/block_blockquote.png)}.FCK__ShowBlocks h1{background-image:url(images/block_h1.png)}.FCK__ShowBlocks h2{background-image:url(images/block_h2.png)}.FCK__ShowBlocks h3{background-image:url(images/block_h3.png)}.FCK__ShowBlocks h4{background-image:url(images/block_h4.png)}.FCK__ShowBlocks h5{background-image:url(images/block_h5.png)}.FCK__ShowBlocks h6{background-image:url(images/block_h6.png)}' ) ;
var FCK_ShowTableBordersCSS	= FCKTools.FixCssUrls( FCKConfig.BasePath + 'css/', 'table:not([border]),table:not([border]) > tr > td,table:not([border]) > tr > th,table:not([border]) > tbody > tr > td,table:not([border]) > tbody > tr > th,table:not([border]) > thead > tr > td,table:not([border]) > thead > tr > th,table:not([border]) > tfoot > tr > td,table:not([border]) > tfoot > tr > th,table[border=\"0\"],table[border=\"0\"] > tr > td,table[border=\"0\"] > tr > th,table[border=\"0\"] > tbody > tr > td,table[border=\"0\"] > tbody > tr > th,table[border=\"0\"] > thead > tr > td,table[border=\"0\"] > thead > tr > th,table[border=\"0\"] > tfoot > tr > td,table[border=\"0\"] > tfoot > tr > th{border:#d3d3d3 1px dotted}' ) ;
@Packager.RemoveLine */

// Popup the debug window if debug mode is set to true. It guarantees that the
// first debug message will not be lost.
if ( FCKConfig.Debug )
	FCKDebug._GetWindow() ;

// Load the active skin CSS.
document.write( FCKTools.GetStyleHtml( FCKConfig.SkinEditorCSS ) ) ;

// Load the language file.
FCKLanguageManager.Initialize() ;
LoadScript( 'lang/' + FCKLanguageManager.ActiveLanguage.Code + '.js' ) ;

	</script>
	<script type="text/javascript">

// Initialize the editing area context menu.
FCK_ContextMenu_Init() ;

FCKPlugins.Load() ;

	</script>
	<script type="text/javascript">

// Set the editor interface direction.
window.document.dir = FCKLang.Dir ;

	</script>
	<script type="text/javascript">

window.onload = function()
{
	InitializeAPI() ;

	if ( FCKBrowserInfo.IsIE )
		FCK_PreloadImages() ;
	else
		LoadToolbarSetup() ;
}

function LoadToolbarSetup()
{
	FCKeditorAPI._FunctionQueue.Add( LoadToolbar ) ;
}

function LoadToolbar()
{
	var oToolbarSet = FCK.ToolbarSet = FCKToolbarSet_Create() ;

	if ( oToolbarSet.IsLoaded )
		StartEditor() ;
	else
	{
		oToolbarSet.OnLoad = StartEditor ;
		oToolbarSet.Load( FCKURLParams['Toolbar'] || 'Default' ) ;
	}
}

function StartEditor()
{
	// Remove the onload listener.
	FCK.ToolbarSet.OnLoad = null ;

	FCKeditorAPI._FunctionQueue.Remove( LoadToolbar ) ;

	FCK.Events.AttachEvent( 'OnStatusChange', WaitForActive ) ;

	// Start the editor.
	FCK.StartEditor() ;
}

function WaitForActive( editorInstance, newStatus )
{
	if ( newStatus == FCK_STATUS_ACTIVE )
	{
		if ( FCKBrowserInfo.IsGecko )
			FCKTools.RunFunction( window.onresize ) ;

		_AttachFormSubmitToAPI() ;

		FCK.SetStatus( FCK_STATUS_COMPLETE ) ;

		// Call the special "FCKeditor_OnComplete" function that should be present in
		// the HTML page where the editor is located.
		if ( typeof( window.parent.FCKeditor_OnComplete ) == 'function' )
			window.parent.FCKeditor_OnComplete( FCK ) ;
	}
}

// Gecko browsers doesn't calculate well the IFRAME size so we must
// recalculate it every time the window size changes.
if ( FCKBrowserInfo.IsGecko && !FCKBrowserInfo.IsOpera )
{
	window.onresize = function( e )
	{
		// Running in Chrome makes the window receive the event including subframes.
		// we care only about this window. Ticket #1642.
		// #2002: The originalTarget from the event can be the current document, the window, or the editing area.
		if ( e && e.originalTarget !== document && e.originalTarget !== window && (!e.originalTarget.ownerDocument || e.originalTarget.ownerDocument != document ))
			return ;

		var oCell = document.getElementById( 'xEditingArea' ) ;

		var eInnerElement = oCell.firstChild ;
		if ( eInnerElement )
		{
			eInnerElement.style.height = '0px' ;
			eInnerElement.style.height = ( oCell.scrollHeight - 2 ) + 'px' ;
		}
	}
}

	</script>
</head>
<body>
	<table width="100%" cellpadding="0" cellspacing="0" style="height: 100%; table-layout: fixed">
		<tr id="xToolbarRow" style="display: none">
			<td id="xToolbarSpace" style="overflow: hidden">
				<table width="100%" cellpadding="0" cellspacing="0">
					<tr id="xCollapsed" style="display: none">
						<td id="xExpandHandle" class="TB_Expand" colspan="3">
							<img class="TB_ExpandImg" alt="" src="images/spacer.gif" width="8" height="4" /></td>
					</tr>
					<tr id="xExpanded" style="display: none">
						<td id="xTBLeftBorder" class="TB_SideBorder" style="width: 1px; display: none;"></td>
						<td id="xCollapseHandle" style="display: none" class="TB_Collapse" valign="bottom">
							<img class="TB_CollapseImg" alt="" src="images/spacer.gif" width="8" height="4" /></td>
						<td id="xToolbar" class="TB_ToolbarSet"></td>
						<td class="TB_SideBorder" style="width: 1px"></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td id="xEditingArea" valign="top" style="height: 100%"></td>
		</tr>
	</table>
</body>
</html>

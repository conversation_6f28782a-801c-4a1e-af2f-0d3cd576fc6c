/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * Hungarian language file.
 */

var FCKLang =
{
// Language direction : "ltr" (left to right) or "rtl" (right to left).
Dir					: "ltr",

ToolbarCollapse		: "Eszköztár elrejtése",
ToolbarExpand		: "Eszköztár megjelenítése",

// Toolbar Items and Context Menu
Save				: "Mentés",
NewPage				: "<PERSON>j oldal",
Preview				: "Előnézet",
Cut					: "Kivágás",
Copy				: "Másolás",
Paste				: "Beillesztés",
PasteText			: "Beillesztés formázás nélkül",
PasteWord			: "Beillesztés Word-ből",
Print				: "Nyomtatás",
SelectAll			: "Mindent kijelöl",
RemoveFormat		: "Formázás eltávolítása",
InsertLinkLbl		: "Hivatkozás",
InsertLink			: "Hivatkozás beillesztése/módosítása",
RemoveLink			: "Hivatkozás törlése",
Anchor				: "Horgony beillesztése/szerkesztése",
AnchorDelete		: "Horgony eltávolítása",
InsertImageLbl		: "Kép",
InsertImage			: "Kép beillesztése/módosítása",
InsertFlashLbl		: "Flash",
InsertFlash			: "Flash beillesztése, módosítása",
InsertTableLbl		: "Táblázat",
InsertTable			: "Táblázat beillesztése/módosítása",
InsertLineLbl		: "Vonal",
InsertLine			: "Elválasztóvonal beillesztése",
InsertSpecialCharLbl: "Speciális karakter",
InsertSpecialChar	: "Speciális karakter beillesztése",
InsertSmileyLbl		: "Hangulatjelek",
InsertSmiley		: "Hangulatjelek beillesztése",
About				: "FCKeditor névjegy",
Bold				: "Félkövér",
Italic				: "Dőlt",
Underline			: "Aláhúzott",
StrikeThrough		: "Áthúzott",
Subscript			: "Alsó index",
Superscript			: "Felső index",
LeftJustify			: "Balra",
CenterJustify		: "Középre",
RightJustify		: "Jobbra",
BlockJustify		: "Sorkizárt",
DecreaseIndent		: "Behúzás csökkentése",
IncreaseIndent		: "Behúzás növelése",
Blockquote			: "Idézet blokk",
Undo				: "Visszavonás",
Redo				: "Ismétlés",
NumberedListLbl		: "Számozás",
NumberedList		: "Számozás beillesztése/törlése",
BulletedListLbl		: "Felsorolás",
BulletedList		: "Felsorolás beillesztése/törlése",
ShowTableBorders	: "Táblázat szegély mutatása",
ShowDetails			: "Részletek mutatása",
Style				: "Stílus",
FontFormat			: "Formátum",
Font				: "Betűtípus",
FontSize			: "Méret",
TextColor			: "Betűszín",
BGColor				: "Háttérszín",
Source				: "Forráskód",
Find				: "Keresés",
Replace				: "Csere",
SpellCheck			: "Helyesírás-ellenőrzés",
UniversalKeyboard	: "Univerzális billentyűzet",
PageBreakLbl		: "Oldaltörés",
PageBreak			: "Oldaltörés beillesztése",

Form			: "Űrlap",
Checkbox		: "Jelölőnégyzet",
RadioButton		: "Választógomb",
TextField		: "Szövegmező",
Textarea		: "Szövegterület",
HiddenField		: "Rejtettmező",
Button			: "Gomb",
SelectionField	: "Legördülő lista",
ImageButton		: "Képgomb",

FitWindow		: "Maximalizálás",
ShowBlocks		: "Blokkok megjelenítése",

// Context Menu
EditLink			: "Hivatkozás módosítása",
CellCM				: "Cella",
RowCM				: "Sor",
ColumnCM			: "Oszlop",
InsertRowAfter		: "Sor beillesztése az aktuális sor mögé",
InsertRowBefore		: "Sor beillesztése az aktuális sor elé",
DeleteRows			: "Sorok törlése",
InsertColumnAfter	: "Oszlop beillesztése az aktuális oszlop mögé",
InsertColumnBefore	: "Oszlop beillesztése az aktuális oszlop elé",
DeleteColumns		: "Oszlopok törlése",
InsertCellAfter		: "Cella beillesztése az aktuális cella mögé",
InsertCellBefore	: "Cella beillesztése az aktuális cella elé",
DeleteCells			: "Cellák törlése",
MergeCells			: "Cellák egyesítése",
MergeRight			: "Cellák egyesítése jobbra",
MergeDown			: "Cellák egyesítése lefelé",
HorizontalSplitCell	: "Cellák szétválasztása vízszintesen",
VerticalSplitCell	: "Cellák szétválasztása függőlegesen",
TableDelete			: "Táblázat törlése",
CellProperties		: "Cella tulajdonságai",
TableProperties		: "Táblázat tulajdonságai",
ImageProperties		: "Kép tulajdonságai",
FlashProperties		: "Flash tulajdonságai",

AnchorProp			: "Horgony tulajdonságai",
ButtonProp			: "Gomb tulajdonságai",
CheckboxProp		: "Jelölőnégyzet tulajdonságai",
HiddenFieldProp		: "Rejtett mező tulajdonságai",
RadioButtonProp		: "Választógomb tulajdonságai",
ImageButtonProp		: "Képgomb tulajdonságai",
TextFieldProp		: "Szövegmező tulajdonságai",
SelectionFieldProp	: "Legördülő lista tulajdonságai",
TextareaProp		: "Szövegterület tulajdonságai",
FormProp			: "Űrlap tulajdonságai",

FontFormats			: "Normál;Formázott;Címsor;Fejléc 1;Fejléc 2;Fejléc 3;Fejléc 4;Fejléc 5;Fejléc 6;Bekezdés (DIV)",

// Alerts and Messages
ProcessingXHTML		: "XHTML feldolgozása. Kérem várjon...",
Done				: "Kész",
PasteWordConfirm	: "A beilleszteni kívánt szöveg Word-ből van másolva. El kívánja távolítani a formázást a beillesztés előtt?",
NotCompatiblePaste	: "Ez a parancs csak Internet Explorer 5.5 verziótól használható. Megpróbálja beilleszteni a szöveget az eredeti formázással?",
UnknownToolbarItem	: "Ismeretlen eszköztár elem \"%1\"",
UnknownCommand		: "Ismeretlen parancs \"%1\"",
NotImplemented		: "A parancs nem hajtható végre",
UnknownToolbarSet	: "Az eszközkészlet \"%1\" nem létezik",
NoActiveX			: "A böngésző biztonsági beállításai korlátozzák a szerkesztő lehetőségeit. Engedélyezni kell ezt az opciót: \"Run ActiveX controls and plug-ins\". Ettől függetlenül előfordulhatnak hibaüzenetek ill. bizonyos funkciók hiányozhatnak.",
BrowseServerBlocked : "Nem lehet megnyitni a fájlböngészőt. Bizonyosodjon meg róla, hogy a felbukkanó ablakok engedélyezve vannak.",
DialogBlocked		: "Nem lehet megnyitni a párbeszédablakot. Bizonyosodjon meg róla, hogy a felbukkanó ablakok engedélyezve vannak.",

// Dialogs
DlgBtnOK			: "Rendben",
DlgBtnCancel		: "Mégsem",
DlgBtnClose			: "Bezárás",
DlgBtnBrowseServer	: "Böngészés a szerveren",
DlgAdvancedTag		: "További opciók",
DlgOpOther			: "Egyéb",
DlgInfoTab			: "Alaptulajdonságok",
DlgAlertUrl			: "Illessze be a webcímet",

// General Dialogs Labels
DlgGenNotSet		: "<nincs beállítva>",
DlgGenId			: "Azonosító",
DlgGenLangDir		: "Írás iránya",
DlgGenLangDirLtr	: "Balról jobbra",
DlgGenLangDirRtl	: "Jobbról balra",
DlgGenLangCode		: "Nyelv kódja",
DlgGenAccessKey		: "Billentyűkombináció",
DlgGenName			: "Név",
DlgGenTabIndex		: "Tabulátor index",
DlgGenLongDescr		: "Részletes leírás webcíme",
DlgGenClass			: "Stíluskészlet",
DlgGenTitle			: "Súgócimke",
DlgGenContType		: "Súgó tartalomtípusa",
DlgGenLinkCharset	: "Hivatkozott tartalom kódlapja",
DlgGenStyle			: "Stílus",

// Image Dialog
DlgImgTitle			: "Kép tulajdonságai",
DlgImgInfoTab		: "Alaptulajdonságok",
DlgImgBtnUpload		: "Küldés a szerverre",
DlgImgURL			: "Hivatkozás",
DlgImgUpload		: "Feltöltés",
DlgImgAlt			: "Buborék szöveg",
DlgImgWidth			: "Szélesség",
DlgImgHeight		: "Magasság",
DlgImgLockRatio		: "Arány megtartása",
DlgBtnResetSize		: "Eredeti méret",
DlgImgBorder		: "Keret",
DlgImgHSpace		: "Vízsz. táv",
DlgImgVSpace		: "Függ. táv",
DlgImgAlign			: "Igazítás",
DlgImgAlignLeft		: "Bal",
DlgImgAlignAbsBottom: "Legaljára",
DlgImgAlignAbsMiddle: "Közepére",
DlgImgAlignBaseline	: "Alapvonalhoz",
DlgImgAlignBottom	: "Aljára",
DlgImgAlignMiddle	: "Középre",
DlgImgAlignRight	: "Jobbra",
DlgImgAlignTextTop	: "Szöveg tetejére",
DlgImgAlignTop		: "Tetejére",
DlgImgPreview		: "Előnézet",
DlgImgAlertUrl		: "Töltse ki a kép webcímét",
DlgImgLinkTab		: "Hivatkozás",

// Flash Dialog
DlgFlashTitle		: "Flash tulajdonságai",
DlgFlashChkPlay		: "Automata lejátszás",
DlgFlashChkLoop		: "Folyamatosan",
DlgFlashChkMenu		: "Flash menü engedélyezése",
DlgFlashScale		: "Méretezés",
DlgFlashScaleAll	: "Mindent mutat",
DlgFlashScaleNoBorder	: "Keret nélkül",
DlgFlashScaleFit	: "Teljes kitöltés",

// Link Dialog
DlgLnkWindowTitle	: "Hivatkozás tulajdonságai",
DlgLnkInfoTab		: "Alaptulajdonságok",
DlgLnkTargetTab		: "Megjelenítés",

DlgLnkType			: "Hivatkozás típusa",
DlgLnkTypeURL		: "Webcím",
DlgLnkTypeAnchor	: "Horgony az oldalon",
DlgLnkTypeEMail		: "E-Mail",
DlgLnkProto			: "Protokoll",
DlgLnkProtoOther	: "<más>",
DlgLnkURL			: "Webcím",
DlgLnkAnchorSel		: "Horgony választása",
DlgLnkAnchorByName	: "Horgony név szerint",
DlgLnkAnchorById	: "Azonosító szerint",
DlgLnkNoAnchors		: "(Nincs horgony a dokumentumban)",
DlgLnkEMail			: "E-Mail cím",
DlgLnkEMailSubject	: "Üzenet tárgya",
DlgLnkEMailBody		: "Üzenet",
DlgLnkUpload		: "Feltöltés",
DlgLnkBtnUpload		: "Küldés a szerverre",

DlgLnkTarget		: "Tartalom megjelenítése",
DlgLnkTargetFrame	: "<keretben>",
DlgLnkTargetPopup	: "<felugró ablakban>",
DlgLnkTargetBlank	: "Új ablakban (_blank)",
DlgLnkTargetParent	: "Szülő ablakban (_parent)",
DlgLnkTargetSelf	: "Azonos ablakban (_self)",
DlgLnkTargetTop		: "Legfelső ablakban (_top)",
DlgLnkTargetFrameName	: "Keret neve",
DlgLnkPopWinName	: "Felugró ablak neve",
DlgLnkPopWinFeat	: "Felugró ablak jellemzői",
DlgLnkPopResize		: "Méretezhető",
DlgLnkPopLocation	: "Címsor",
DlgLnkPopMenu		: "Menü sor",
DlgLnkPopScroll		: "Gördítősáv",
DlgLnkPopStatus		: "Állapotsor",
DlgLnkPopToolbar	: "Eszköztár",
DlgLnkPopFullScrn	: "Teljes képernyő (csak IE)",
DlgLnkPopDependent	: "Szülőhöz kapcsolt (csak Netscape)",
DlgLnkPopWidth		: "Szélesség",
DlgLnkPopHeight		: "Magasság",
DlgLnkPopLeft		: "Bal pozíció",
DlgLnkPopTop		: "Felső pozíció",

DlnLnkMsgNoUrl		: "Adja meg a hivatkozás webcímét",
DlnLnkMsgNoEMail	: "Adja meg az E-Mail címet",
DlnLnkMsgNoAnchor	: "Válasszon egy horgonyt",
DlnLnkMsgInvPopName	: "A felbukkanó ablak neve alfanumerikus karakterrel kezdôdjön, valamint ne tartalmazzon szóközt",

// Color Dialog
DlgColorTitle		: "Színválasztás",
DlgColorBtnClear	: "Törlés",
DlgColorHighlight	: "Előnézet",
DlgColorSelected	: "Kiválasztott",

// Smiley Dialog
DlgSmileyTitle		: "Hangulatjel beszúrása",

// Special Character Dialog
DlgSpecialCharTitle	: "Speciális karakter választása",

// Table Dialog
DlgTableTitle		: "Táblázat tulajdonságai",
DlgTableRows		: "Sorok",
DlgTableColumns		: "Oszlopok",
DlgTableBorder		: "Szegélyméret",
DlgTableAlign		: "Igazítás",
DlgTableAlignNotSet	: "<Nincs beállítva>",
DlgTableAlignLeft	: "Balra",
DlgTableAlignCenter	: "Középre",
DlgTableAlignRight	: "Jobbra",
DlgTableWidth		: "Szélesség",
DlgTableWidthPx		: "képpont",
DlgTableWidthPc		: "százalék",
DlgTableHeight		: "Magasság",
DlgTableCellSpace	: "Cella térköz",
DlgTableCellPad		: "Cella belső margó",
DlgTableCaption		: "Felirat",
DlgTableSummary		: "Leírás",

// Table Cell Dialog
DlgCellTitle		: "Cella tulajdonságai",
DlgCellWidth		: "Szélesség",
DlgCellWidthPx		: "képpont",
DlgCellWidthPc		: "százalék",
DlgCellHeight		: "Magasság",
DlgCellWordWrap		: "Sortörés",
DlgCellWordWrapNotSet	: "<Nincs beállítva>",
DlgCellWordWrapYes	: "Igen",
DlgCellWordWrapNo	: "Nem",
DlgCellHorAlign		: "Vízsz. igazítás",
DlgCellHorAlignNotSet	: "<Nincs beállítva>",
DlgCellHorAlignLeft	: "Balra",
DlgCellHorAlignCenter	: "Középre",
DlgCellHorAlignRight: "Jobbra",
DlgCellVerAlign		: "Függ. igazítás",
DlgCellVerAlignNotSet	: "<Nincs beállítva>",
DlgCellVerAlignTop	: "Tetejére",
DlgCellVerAlignMiddle	: "Középre",
DlgCellVerAlignBottom	: "Aljára",
DlgCellVerAlignBaseline	: "Egyvonalba",
DlgCellRowSpan		: "Sorok egyesítése",
DlgCellCollSpan		: "Oszlopok egyesítése",
DlgCellBackColor	: "Háttérszín",
DlgCellBorderColor	: "Szegélyszín",
DlgCellBtnSelect	: "Kiválasztás...",

// Find and Replace Dialog
DlgFindAndReplaceTitle	: "Keresés és csere",

// Find Dialog
DlgFindTitle		: "Keresés",
DlgFindFindBtn		: "Keresés",
DlgFindNotFoundMsg	: "A keresett szöveg nem található.",

// Replace Dialog
DlgReplaceTitle			: "Csere",
DlgReplaceFindLbl		: "Keresett szöveg:",
DlgReplaceReplaceLbl	: "Csere erre:",
DlgReplaceCaseChk		: "kis- és nagybetű megkülönböztetése",
DlgReplaceReplaceBtn	: "Csere",
DlgReplaceReplAllBtn	: "Az összes cseréje",
DlgReplaceWordChk		: "csak ha ez a teljes szó",

// Paste Operations / Dialog
PasteErrorCut	: "A böngésző biztonsági beállításai nem engedélyezik a szerkesztőnek, hogy végrehajtsa a kivágás műveletet. Használja az alábbi billentyűkombinációt (Ctrl+X).",
PasteErrorCopy	: "A böngésző biztonsági beállításai nem engedélyezik a szerkesztőnek, hogy végrehajtsa a másolás műveletet. Használja az alábbi billentyűkombinációt (Ctrl+X).",

PasteAsText		: "Beillesztés formázatlan szövegként",
PasteFromWord	: "Beillesztés Word-ből",

DlgPasteMsg2	: "Másolja be az alábbi mezőbe a <STRONG>Ctrl+V</STRONG> billentyűk lenyomásával, majd nyomjon <STRONG>Rendben</STRONG>-t.",
DlgPasteSec		: "A böngésző biztonsági beállításai miatt a szerkesztő nem képes hozzáférni a vágólap adataihoz. Illeszd be újra ebben az ablakban.",
DlgPasteIgnoreFont		: "Betű formázások megszüntetése",
DlgPasteRemoveStyles	: "Stílusok eltávolítása",

// Color Picker
ColorAutomatic	: "Automatikus",
ColorMoreColors	: "További színek...",

// Document Properties
DocProps		: "Dokumentum tulajdonságai",

// Anchor Dialog
DlgAnchorTitle		: "Horgony tulajdonságai",
DlgAnchorName		: "Horgony neve",
DlgAnchorErrorName	: "Kérem adja meg a horgony nevét",

// Speller Pages Dialog
DlgSpellNotInDic		: "Nincs a szótárban",
DlgSpellChangeTo		: "Módosítás",
DlgSpellBtnIgnore		: "Kihagyja",
DlgSpellBtnIgnoreAll	: "Mindet kihagyja",
DlgSpellBtnReplace		: "Csere",
DlgSpellBtnReplaceAll	: "Összes cseréje",
DlgSpellBtnUndo			: "Visszavonás",
DlgSpellNoSuggestions	: "Nincs javaslat",
DlgSpellProgress		: "Helyesírás-ellenőrzés folyamatban...",
DlgSpellNoMispell		: "Helyesírás-ellenőrzés kész: Nem találtam hibát",
DlgSpellNoChanges		: "Helyesírás-ellenőrzés kész: Nincs változtatott szó",
DlgSpellOneChange		: "Helyesírás-ellenőrzés kész: Egy szó cserélve",
DlgSpellManyChanges		: "Helyesírás-ellenőrzés kész: %1 szó cserélve",

IeSpellDownload			: "A helyesírás-ellenőrző nincs telepítve. Szeretné letölteni most?",

// Button Dialog
DlgButtonText		: "Szöveg (Érték)",
DlgButtonType		: "Típus",
DlgButtonTypeBtn	: "Gomb",
DlgButtonTypeSbm	: "Küldés",
DlgButtonTypeRst	: "Alaphelyzet",

// Checkbox and Radio Button Dialogs
DlgCheckboxName		: "Név",
DlgCheckboxValue	: "Érték",
DlgCheckboxSelected	: "Kiválasztott",

// Form Dialog
DlgFormName		: "Név",
DlgFormAction	: "Adatfeldolgozást végző hivatkozás",
DlgFormMethod	: "Adatküldés módja",

// Select Field Dialog
DlgSelectName		: "Név",
DlgSelectValue		: "Érték",
DlgSelectSize		: "Méret",
DlgSelectLines		: "sor",
DlgSelectChkMulti	: "több sor is kiválasztható",
DlgSelectOpAvail	: "Elérhető opciók",
DlgSelectOpText		: "Szöveg",
DlgSelectOpValue	: "Érték",
DlgSelectBtnAdd		: "Hozzáad",
DlgSelectBtnModify	: "Módosít",
DlgSelectBtnUp		: "Fel",
DlgSelectBtnDown	: "Le",
DlgSelectBtnSetValue : "Legyen az alapértelmezett érték",
DlgSelectBtnDelete	: "Töröl",

// Textarea Dialog
DlgTextareaName	: "Név",
DlgTextareaCols	: "Karakterek száma egy sorban",
DlgTextareaRows	: "Sorok száma",

// Text Field Dialog
DlgTextName			: "Név",
DlgTextValue		: "Érték",
DlgTextCharWidth	: "Megjelenített karakterek száma",
DlgTextMaxChars		: "Maximális karakterszám",
DlgTextType			: "Típus",
DlgTextTypeText		: "Szöveg",
DlgTextTypePass		: "Jelszó",

// Hidden Field Dialog
DlgHiddenName	: "Név",
DlgHiddenValue	: "Érték",

// Bulleted List Dialog
BulletedListProp	: "Felsorolás tulajdonságai",
NumberedListProp	: "Számozás tulajdonságai",
DlgLstStart			: "Start",
DlgLstType			: "Formátum",
DlgLstTypeCircle	: "Kör",
DlgLstTypeDisc		: "Lemez",
DlgLstTypeSquare	: "Négyzet",
DlgLstTypeNumbers	: "Számok (1, 2, 3)",
DlgLstTypeLCase		: "Kisbetűk (a, b, c)",
DlgLstTypeUCase		: "Nagybetűk (A, B, C)",
DlgLstTypeSRoman	: "Kis római számok (i, ii, iii)",
DlgLstTypeLRoman	: "Nagy római számok (I, II, III)",

// Document Properties Dialog
DlgDocGeneralTab	: "Általános",
DlgDocBackTab		: "Háttér",
DlgDocColorsTab		: "Színek és margók",
DlgDocMetaTab		: "Meta adatok",

DlgDocPageTitle		: "Oldalcím",
DlgDocLangDir		: "Írás iránya",
DlgDocLangDirLTR	: "Balról jobbra",
DlgDocLangDirRTL	: "Jobbról balra",
DlgDocLangCode		: "Nyelv kód",
DlgDocCharSet		: "Karakterkódolás",
DlgDocCharSetCE		: "Közép-Európai",
DlgDocCharSetCT		: "Kínai Tradicionális (Big5)",
DlgDocCharSetCR		: "Cyrill",
DlgDocCharSetGR		: "Görög",
DlgDocCharSetJP		: "Japán",
DlgDocCharSetKR		: "Koreai",
DlgDocCharSetTR		: "Török",
DlgDocCharSetUN		: "Unicode (UTF-8)",
DlgDocCharSetWE		: "Nyugat-Európai",
DlgDocCharSetOther	: "Más karakterkódolás",

DlgDocDocType		: "Dokumentum típus fejléc",
DlgDocDocTypeOther	: "Más dokumentum típus fejléc",
DlgDocIncXHTML		: "XHTML deklarációk beillesztése",
DlgDocBgColor		: "Háttérszín",
DlgDocBgImage		: "Háttérkép cím",
DlgDocBgNoScroll	: "Nem gördíthető háttér",
DlgDocCText			: "Szöveg",
DlgDocCLink			: "Cím",
DlgDocCVisited		: "Látogatott cím",
DlgDocCActive		: "Aktív cím",
DlgDocMargins		: "Oldal margók",
DlgDocMaTop			: "Felső",
DlgDocMaLeft		: "Bal",
DlgDocMaRight		: "Jobb",
DlgDocMaBottom		: "Alsó",
DlgDocMeIndex		: "Dokumentum keresőszavak (vesszővel elválasztva)",
DlgDocMeDescr		: "Dokumentum leírás",
DlgDocMeAuthor		: "Szerző",
DlgDocMeCopy		: "Szerzői jog",
DlgDocPreview		: "Előnézet",

// Templates Dialog
Templates			: "Sablonok",
DlgTemplatesTitle	: "Elérhető sablonok",
DlgTemplatesSelMsg	: "Válassza ki melyik sablon nyíljon meg a szerkesztőben<br>(a jelenlegi tartalom elveszik):",
DlgTemplatesLoading	: "Sablon lista betöltése. Kis türelmet...",
DlgTemplatesNoTpl	: "(Nincs sablon megadva)",
DlgTemplatesReplace	: "Kicseréli a jelenlegi tartalmat",

// About Dialog
DlgAboutAboutTab	: "Névjegy",
DlgAboutBrowserInfoTab	: "Böngésző információ",
DlgAboutLicenseTab	: "Licensz",
DlgAboutVersion		: "verzió",
DlgAboutInfo		: "További információkért látogasson el ide:"
};

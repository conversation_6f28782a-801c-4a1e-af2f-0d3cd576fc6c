/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * CSS styles used by all pages that compose the File Browser.
 */

body
{
	background-color: #f1f1e3;
	margin-top:0;
	margin-bottom:0;
}

form
{
	margin: 0;
	padding: 0;
}

.Frame
{
	background-color: #f1f1e3;
	border: thin inset #f1f1e3;
}

body.FileArea
{
	background-color: #ffffff;
	margin: 10px;
}

body, td, input, select
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Arial, Helvetica, Verdana;
}

.ActualFolder
{
	font-weight: bold;
	font-size: 14px;
}

.PopupButtons
{
	border-top: #d5d59d 1px solid;
	background-color: #e3e3c7;
	padding: 7px 10px 7px 10px;
}

.Button, button
{
	color: #3b3b1f;
	border: #737357 1px solid;
	background-color: #c7c78f;
}

.FolderListCurrentFolder img
{
	background-image: url(images/FolderOpened.gif);
}

.FolderListFolder img
{
	background-image: url(images/Folder.gif);
}

.fullHeight {
	height: 100%;
}

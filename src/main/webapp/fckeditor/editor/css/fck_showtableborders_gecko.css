/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * This CSS Style Sheet defines the rules to show table borders on Gecko.
 */

/* #########
 *  WARNING
 * #########
 * When changing this file, the minified version of it must be updated in the
 * fckeditor.html file (see FCK_ShowTableBordersCSS).
 */

/* For tables with the "border" attribute set to "0" */
table[border="0"],
table[border="0"] > tr > td, table[border="0"] > tr > th,
table[border="0"] > tbody > tr > td, table[border="0"] > tbody > tr > th,
table[border="0"] > thead > tr > td, table[border="0"] > thead > tr > th,
table[border="0"] > tfoot > tr > td, table[border="0"] > tfoot > tr > th
{
	border: #d3d3d3 1px dotted ;
}

/* For tables with no "border" attribute set */
table:not([border]),
table:not([border]) > tr > td, table:not([border]) > tr > th,
table:not([border]) > tbody > tr > td, table:not([border]) > tbody > tr > th,
table:not([border]) > thead > tr > td, table:not([border]) > thead > tr > th,
table:not([border]) > tfoot > tr > td, table:not([border]) > tfoot > tr > th
{
	border: #d3d3d3 1px dotted ;
}

/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * This is the default CSS file used by the editor area. It defines the
 * initial font of the editor and background color.
 *
 * A user can configure the editor to use another CSS file. Just change
 * the value of the FCKConfig.EditorAreaCSS key in the configuration
 * file.
 */

/*
    The "body" styles should match your editor web site, mainly regarding
    background color and font family and size.
*/

body
{
	background-color: #ffffff;
	padding: 5px 5px 5px 5px;
	margin: 0px;
}

body, td
{
	font-family: <PERSON><PERSON>, <PERSON>erdana, sans-serif;
	font-size: 12px;
}

a[href]
{
	color: -moz-hyperlinktext !important;		/* For Firefox... mark as important, otherwise it becomes black */
	text-decoration: -moz-anchor-decoration;	/* For Firefox 3, otherwise no underline will be used */
}

/*
	Just uncomment the following block if you want to avoid spaces between
	paragraphs. Remember to apply the same style in your output front end page.
*/

/*
p, ul, li
{
	margin-top: 0px;
	margin-bottom: 0px;
}
*/

/*
    The following are some sample styles used in the "Styles" toolbar command.
    You should instead remove them, and include the styles used by the site
    you are using the editor in.
*/

.Bold
{
	font-weight: bold;
}

.Title
{
	font-weight: bold;
	font-size: 18px;
	color: #cc3300;
}

.Code
{
	border: #8b4513 1px solid;
	padding-right: 5px;
	padding-left: 5px;
	color: #000066;
	font-family: 'Courier New' , Monospace;
	background-color: #ff9933;
}

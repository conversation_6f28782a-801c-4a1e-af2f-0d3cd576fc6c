<?xml version="1.0" encoding="utf-8" ?>
<!--
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * This is the configuration file to be used with FCKpackager to generate the
 * compressed code files in the "js" folder.
 *
 * Please check http://www.fckeditor.net for more info.
-->
<Package>
	<Header><![CDATA[/*
 * FCKeditor - The text editor for Internet - http://www.fckeditor.net
 * Copyright (C) 2003-2008 <PERSON><PERSON>
 *
 * == BEGIN LICENSE ==
 *
 * Licensed under the terms of any of the following licenses at your
 * choice:
 *
 *  - GNU General Public License Version 2 or later (the "GPL")
 *    http://www.gnu.org/licenses/gpl.html
 *
 *  - GNU Lesser General Public License Version 2.1 or later (the "LGPL")
 *    http://www.gnu.org/licenses/lgpl.html
 *
 *  - Mozilla Public License Version 1.1 or later (the "MPL")
 *    http://www.mozilla.org/MPL/MPL-1.1.html
 *
 * == END LICENSE ==
 *
 * This file has been compressed for better performance. The original source
 * can be found at "editor/_source".
 */
]]></Header>
	<Constants removeDeclaration="false">
		<Constant name="FCK_STATUS_NOTLOADED" value="0" />
		<Constant name="FCK_STATUS_ACTIVE" value="1" />
		<Constant name="FCK_STATUS_COMPLETE" value="2" />
		<Constant name="FCK_TRISTATE_OFF" value="0" />
		<Constant name="FCK_TRISTATE_ON" value="1" />
		<Constant name="FCK_TRISTATE_DISABLED" value="-1" />
		<Constant name="FCK_UNKNOWN" value="-9" />
		<Constant name="FCK_TOOLBARITEM_ONLYICON" value="0" />
		<Constant name="FCK_TOOLBARITEM_ONLYTEXT" value="1" />
		<Constant name="FCK_TOOLBARITEM_ICONTEXT" value="2" />
		<Constant name="FCK_EDITMODE_WYSIWYG" value="0" />
		<Constant name="FCK_EDITMODE_SOURCE" value="1" />
		<Constant name="FCK_STYLE_BLOCK" value="0" />
		<Constant name="FCK_STYLE_INLINE" value="1" />
		<Constant name="FCK_STYLE_OBJECT" value="2" />
	</Constants>
	<PackageFile path="editor/js/fckeditorcode_ie.js">
		<File path="editor/_source/fckconstants.js" />
		<File path="editor/_source/fckjscoreextensions.js" />
		<File path="editor/_source/classes/fckiecleanup.js" />
		<File path="editor/_source/internals/fckbrowserinfo.js" />
		<File path="editor/_source/internals/fckurlparams.js" />
		<File path="editor/_source/classes/fckevents.js" />
		<File path="editor/_source/classes/fckdataprocessor.js" />
		<File path="editor/_source/internals/fck.js" />
		<File path="editor/_source/internals/fck_ie.js" />
		<File path="editor/_source/internals/fckconfig.js" />
		<File path="editor/_source/internals/fckdebug_empty.js" />
		<File path="editor/_source/internals/fckdomtools.js" />
		<File path="editor/_source/internals/fcktools.js" />
		<File path="editor/_source/internals/fcktools_ie.js" />
		<File path="editor/_source/fckeditorapi.js" />
		<File path="editor/_source/classes/fckimagepreloader.js" />

		<File path="editor/_source/internals/fckregexlib.js" />
		<File path="editor/_source/internals/fcklistslib.js" />
		<File path="editor/_source/internals/fcklanguagemanager.js" />
		<File path="editor/_source/internals/fckxhtmlentities.js" />
		<File path="editor/_source/internals/fckxhtml.js" />
		<File path="editor/_source/internals/fckxhtml_ie.js" />
		<File path="editor/_source/internals/fckcodeformatter.js" />
		<File path="editor/_source/internals/fckundo.js" />
		<File path="editor/_source/classes/fckeditingarea.js" />
		<File path="editor/_source/classes/fckkeystrokehandler.js" />

		<File path="editor/dtd/fck_xhtml10transitional.js" />
		<File path="editor/_source/classes/fckstyle.js" />
		<File path="editor/_source/internals/fckstyles.js" />

		<File path="editor/_source/internals/fcklisthandler.js" />
		<File path="editor/_source/classes/fckelementpath.js" />
		<File path="editor/_source/classes/fckdomrange.js" />
		<File path="editor/_source/classes/fckdomrange_ie.js" />
		<File path="editor/_source/classes/fckdomrangeiterator.js" />
		<File path="editor/_source/classes/fckdocumentfragment_ie.js" />
		<File path="editor/_source/classes/fckw3crange.js" />
		<File path="editor/_source/classes/fckenterkey.js" />

		<File path="editor/_source/internals/fckdocumentprocessor.js" />
		<File path="editor/_source/internals/fckselection.js" />
		<File path="editor/_source/internals/fckselection_ie.js" />

		<File path="editor/_source/internals/fcktablehandler.js" />
		<File path="editor/_source/internals/fcktablehandler_ie.js" />
		<File path="editor/_source/classes/fckxml.js" />
		<File path="editor/_source/classes/fckxml_ie.js" />

		<File path="editor/_source/commandclasses/fcknamedcommand.js" />
		<File path="editor/_source/commandclasses/fckstylecommand.js" />
		<File path="editor/_source/commandclasses/fck_othercommands.js" />
		<File path="editor/_source/commandclasses/fckshowblocks.js" />
		<File path="editor/_source/commandclasses/fckspellcheckcommand_ie.js" />
		<File path="editor/_source/commandclasses/fcktextcolorcommand.js" />
		<File path="editor/_source/commandclasses/fckpasteplaintextcommand.js" />
		<File path="editor/_source/commandclasses/fckpastewordcommand.js" />
		<File path="editor/_source/commandclasses/fcktablecommand.js" />
		<File path="editor/_source/commandclasses/fckfitwindow.js" />
		<File path="editor/_source/commandclasses/fcklistcommands.js" />
		<File path="editor/_source/commandclasses/fckjustifycommands.js" />
		<File path="editor/_source/commandclasses/fckindentcommands.js" />
		<File path="editor/_source/commandclasses/fckblockquotecommand.js" />
		<File path="editor/_source/commandclasses/fckcorestylecommand.js" />
		<File path="editor/_source/commandclasses/fckremoveformatcommand.js" />
		<File path="editor/_source/internals/fckcommands.js" />

		<File path="editor/_source/classes/fckpanel.js" />
		<File path="editor/_source/classes/fckicon.js" />
		<File path="editor/_source/classes/fcktoolbarbuttonui.js" />
		<File path="editor/_source/classes/fcktoolbarbutton.js" />
		<File path="editor/_source/classes/fckspecialcombo.js" />
		<File path="editor/_source/classes/fcktoolbarspecialcombo.js" />
		<File path="editor/_source/classes/fcktoolbarstylecombo.js" />
		<File path="editor/_source/classes/fcktoolbarfontformatcombo.js" />
		<File path="editor/_source/classes/fcktoolbarfontscombo.js" />
		<File path="editor/_source/classes/fcktoolbarfontsizecombo.js" />
		<File path="editor/_source/classes/fcktoolbarpanelbutton.js" />
		<File path="editor/_source/internals/fcktoolbaritems.js" />
		<File path="editor/_source/classes/fcktoolbar.js" />
		<File path="editor/_source/classes/fcktoolbarbreak_ie.js" />
		<File path="editor/_source/internals/fcktoolbarset.js" />
		<File path="editor/_source/internals/fckdialog.js" />

		<File path="editor/_source/classes/fckmenuitem.js" />
		<File path="editor/_source/classes/fckmenublock.js" />
		<File path="editor/_source/classes/fckmenublockpanel.js" />
		<File path="editor/_source/classes/fckcontextmenu.js" />
		<File path="editor/_source/internals/fck_contextmenu.js" />
		<File path="editor/_source/classes/fckhtmliterator.js" />

		<File path="editor/_source/classes/fckplugin.js" />
		<File path="editor/_source/internals/fckplugins.js" />
	</PackageFile>

	<PackageFile path="editor/js/fckeditorcode_gecko.js">
		<File path="editor/_source/fckconstants.js" />
		<File path="editor/_source/fckjscoreextensions.js" />
		<File path="editor/_source/internals/fckbrowserinfo.js" />
		<File path="editor/_source/internals/fckurlparams.js" />
		<File path="editor/_source/classes/fckevents.js" />
		<File path="editor/_source/classes/fckdataprocessor.js" />
		<File path="editor/_source/internals/fck.js" />
		<File path="editor/_source/internals/fck_gecko.js" />
		<File path="editor/_source/internals/fckconfig.js" />
		<File path="editor/_source/internals/fckdebug_empty.js" />
		<File path="editor/_source/internals/fckdomtools.js" />
		<File path="editor/_source/internals/fcktools.js" />
		<File path="editor/_source/internals/fcktools_gecko.js" />
		<File path="editor/_source/fckeditorapi.js" />
		<File path="editor/_source/classes/fckimagepreloader.js" />

		<File path="editor/_source/internals/fckregexlib.js" />
		<File path="editor/_source/internals/fcklistslib.js" />
		<File path="editor/_source/internals/fcklanguagemanager.js" />
		<File path="editor/_source/internals/fckxhtmlentities.js" />
		<File path="editor/_source/internals/fckxhtml.js" />
		<File path="editor/_source/internals/fckxhtml_gecko.js" />
		<File path="editor/_source/internals/fckcodeformatter.js" />
		<File path="editor/_source/internals/fckundo.js" />
		<File path="editor/_source/classes/fckeditingarea.js" />
		<File path="editor/_source/classes/fckkeystrokehandler.js" />

		<File path="editor/dtd/fck_xhtml10transitional.js" />
		<File path="editor/_source/classes/fckstyle.js" />
		<File path="editor/_source/internals/fckstyles.js" />

		<File path="editor/_source/internals/fcklisthandler.js" />
		<File path="editor/_source/classes/fckelementpath.js" />
		<File path="editor/_source/classes/fckdomrange.js" />
		<File path="editor/_source/classes/fckdomrange_gecko.js" />
		<File path="editor/_source/classes/fckdomrangeiterator.js" />
		<File path="editor/_source/classes/fckdocumentfragment_gecko.js" />
		<File path="editor/_source/classes/fckw3crange.js" />
		<File path="editor/_source/classes/fckenterkey.js" />

		<File path="editor/_source/internals/fckdocumentprocessor.js" />
		<File path="editor/_source/internals/fckselection.js" />
		<File path="editor/_source/internals/fckselection_gecko.js" />

		<File path="editor/_source/internals/fcktablehandler.js" />
		<File path="editor/_source/internals/fcktablehandler_gecko.js" />
		<File path="editor/_source/classes/fckxml.js" />
		<File path="editor/_source/classes/fckxml_gecko.js" />

		<File path="editor/_source/commandclasses/fcknamedcommand.js" />
		<File path="editor/_source/commandclasses/fckstylecommand.js" />
		<File path="editor/_source/commandclasses/fck_othercommands.js" />
		<File path="editor/_source/commandclasses/fckshowblocks.js" />
		<File path="editor/_source/commandclasses/fckspellcheckcommand_gecko.js" />
		<File path="editor/_source/commandclasses/fcktextcolorcommand.js" />
		<File path="editor/_source/commandclasses/fckpasteplaintextcommand.js" />
		<File path="editor/_source/commandclasses/fckpastewordcommand.js" />
		<File path="editor/_source/commandclasses/fcktablecommand.js" />
		<File path="editor/_source/commandclasses/fckfitwindow.js" />
		<File path="editor/_source/commandclasses/fcklistcommands.js" />
		<File path="editor/_source/commandclasses/fckjustifycommands.js" />
		<File path="editor/_source/commandclasses/fckindentcommands.js" />
		<File path="editor/_source/commandclasses/fckblockquotecommand.js" />
		<File path="editor/_source/commandclasses/fckcorestylecommand.js" />
		<File path="editor/_source/commandclasses/fckremoveformatcommand.js" />
		<File path="editor/_source/internals/fckcommands.js" />

		<File path="editor/_source/classes/fckpanel.js" />
		<File path="editor/_source/classes/fckicon.js" />
		<File path="editor/_source/classes/fcktoolbarbuttonui.js" />
		<File path="editor/_source/classes/fcktoolbarbutton.js" />
		<File path="editor/_source/classes/fckspecialcombo.js" />
		<File path="editor/_source/classes/fcktoolbarspecialcombo.js" />
		<File path="editor/_source/classes/fcktoolbarstylecombo.js" />
		<File path="editor/_source/classes/fcktoolbarfontformatcombo.js" />
		<File path="editor/_source/classes/fcktoolbarfontscombo.js" />
		<File path="editor/_source/classes/fcktoolbarfontsizecombo.js" />
		<File path="editor/_source/classes/fcktoolbarpanelbutton.js" />
		<File path="editor/_source/internals/fcktoolbaritems.js" />
		<File path="editor/_source/classes/fcktoolbar.js" />
		<File path="editor/_source/classes/fcktoolbarbreak_gecko.js" />
		<File path="editor/_source/internals/fcktoolbarset.js" />
		<File path="editor/_source/internals/fckdialog.js" />

		<File path="editor/_source/classes/fckmenuitem.js" />
		<File path="editor/_source/classes/fckmenublock.js" />
		<File path="editor/_source/classes/fckmenublockpanel.js" />
		<File path="editor/_source/classes/fckcontextmenu.js" />
		<File path="editor/_source/internals/fck_contextmenu.js" />
		<File path="editor/_source/classes/fckhtmliterator.js" />

		<File path="editor/_source/classes/fckplugin.js" />
		<File path="editor/_source/internals/fckplugins.js" />
	</PackageFile>

</Package>

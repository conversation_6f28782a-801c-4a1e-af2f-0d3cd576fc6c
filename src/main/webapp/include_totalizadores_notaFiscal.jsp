<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@include file="./includes/imports.jsp" %>

<style>
    .panelGeralTotalizadorNotaFiscal {
        width: 40%;
    }
</style>

<h:panelGroup layout="block"
              id="panelGeralTotalizadorNotaFiscal"
              styleClass="panelGeralTotalizadorNotaFiscal">

    <rich:dataTable value="#{NotaFiscalControle.listaTotalizador}"
                    styleClass="tabelaDados semZebra"
                    style="margin-top: 0px;"
                    var="total">

        <f:facet name="header">
            <h:outputText styleClass="textoBold" value="totalizador"/>
        </f:facet>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Status"/>
            </f:facet>
            <h:outputText value="#{total.descricao}"
                          style="font-weight: bold;"
                          styleClass="tooltipster fontListaNotaFiscal #{total.css}"
                          title="#{total.title}"/>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Qtd."/>
            </f:facet>
            <h:outputText value="#{total.quantidade}"/>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Valor Total"/>
            </f:facet>
            <h:outputText value="#{total.valorApresentar}"/>
        </rich:column>

    </rich:dataTable>
</h:panelGroup>
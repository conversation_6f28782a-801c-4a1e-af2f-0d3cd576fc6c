<%@page import="negocio.comuns.arquitetura.UsuarioPerfilAcessoVO"%>
<%@page import="javax.faces.context.FacesContext"%>
<%@page import="controle.arquitetura.SuperControle"%>
<%@page import="controle.arquitetura.Criptografia"%>
<%@page import="negocio.comuns.contrato.ContratoVO"%>
<%@page import="negocio.comuns.utilitarias.Uteis"%>
<%@page import="java.util.regex.*"%>
<%@page import="java.util.Date"%>
<%@page import="java.util.List"%>
<%@page import="negocio.comuns.basico.ClienteVO"%>
<%@page import="java.util.ArrayList"%>
<%@page import="negocio.comuns.arquitetura.PerfilAcessoVO"%>
<%@page import="controle.arquitetura.LoginControle"%>
<%@page import="negocio.comuns.arquitetura.UsuarioVO"%>
<%@page import="controle.basico.ClienteControle"%>
<%@page import="negocio.facade.jdbc.arquitetura.FacadeManager"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="iso-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <%

                String url = request.getQueryString();
                String params = url.substring(url.indexOf("?") + 1);

                params = (Criptografia.decrypt(params,
                        SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM));
                Pattern imagem = Pattern.compile("validade=?\\s*\\w*\\s*");
                Matcher matcher = imagem.matcher(params);
                boolean dataValida = false;
                while (matcher.find()) {
                    String validade = (params.substring(matcher.start(),
                            matcher.end()));
                    validade = validade.substring(validade.indexOf("=") + 1);
                    Date dataURL = new Date();
                    dataURL.setTime(new Long(validade));
                    dataValida = true;//dataURL.after(new Date());

                }
                imagem = Pattern.compile("contrato=?\\s*\\w*\\s*");
                matcher = imagem.matcher(params);
                while (matcher.find()) {
                    String contrato = (params.substring(matcher.start(),
                            matcher.end()));
                    contrato = contrato.substring(contrato.indexOf("=") + 1);
                    ContratoVO contratoVO = FacadeManager.getFacade().getContrato().
                            consultarPorChavePrimaria(new Integer(contrato),
                            Uteis.NIVELMONTARDADOS_TODOS);
                    if ((contratoVO != null) && (contratoVO.getCodigo().intValue() != 0) &&
                            (contratoVO.getPessoa().verificaCPF(
                                contratoVO.getPessoa().getCfp()))  &&
                            (dataValida)) {

                        LoginControle loginControl = new LoginControle();
                        
                        ClienteVO cliente = FacadeManager.getFacade().getCliente().
                                consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), 
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                        String user = cliente.getPessoa().getCfp().replaceAll(
                                "\\.", "").substring(0, 9);
                        String pwd = user;
                        UsuarioVO pseudoUsuario = new UsuarioVO();
                        pseudoUsuario.setTipoUsuario("CI");
                        pseudoUsuario.setClienteVO(cliente);
                        pseudoUsuario.setUsername(user);
                        pseudoUsuario.setSenha(pwd);
                        pseudoUsuario.setPseudo(true);
                        
                        loginControl.setUsername(user);
                        loginControl.setSenha(pwd);
                        loginControl.setUsuario(pseudoUsuario);
                        loginControl.setPerfilAcesso(new PerfilAcessoVO());

                        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("LoginControle",
                                loginControl);                        

                        PerfilAcessoVO perfil = new PerfilAcessoVO();
                        perfil.setNome("CLIENTES");
                        perfil = FacadeManager.getFacade().getPerfilAcesso().
                                criarOuConsultarSeExistePorNome(perfil);
                        List listaPerfil = new ArrayList();
                        UsuarioPerfilAcessoVO usuarioPerfil = new UsuarioPerfilAcessoVO();
                        usuarioPerfil.setUsuarioVO(pseudoUsuario);
                        usuarioPerfil.setPerfilAcesso(perfil);
                        usuarioPerfil.setEmpresa(cliente.getEmpresa());
                        listaPerfil.add(usuarioPerfil);                        
                        pseudoUsuario.setUsuarioPerfilAcessoVOs(listaPerfil);

                        pseudoUsuario = FacadeManager.getFacade().getUsuario().criarOuConsultarSeExistePorNome(
                                pseudoUsuario);

                        ClienteControle cliControl = new ClienteControle();

                        cliControl.setContratoVO(contratoVO);
                        cliControl.setClienteVO(FacadeManager.getFacade().getCliente().
                                consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        cliControl.setOverSession(true);
                        
                        String retorno = cliControl.gerarOutroContratoApartirDoContratoMatricula();
                        if (retorno.equals("questionario")) {
                            response.sendRedirect("faces/tela4.jsp");
                        } else if (retorno.equals("contrato")) {
                            response.sendRedirect("faces/tela5.jsp");
                        }

                    }

                }
    %>   


</f:view>

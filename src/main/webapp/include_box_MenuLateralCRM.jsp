<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<h:panelGroup id="menuLateralCRM" layout="block"
              styleClass="menuLateral tudo">

    <jsp:include page="includes/include_menulateral_acessorapido.jsp" flush="true" />

    <%--Relat�rios--%>
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <h:graphicImage value="/imagensCRM/relatorio_bi-crm.png"/> Relat�rios
    </h:panelGroup>

        <%--Agendamentos--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.relatorioAgendamentos}">
            <a4j:commandLink id="btnLateralAgendamentos"
                             ajaxSingle="true"
                             value="Agendamentos"
                             oncomplete="abrirPopup('agendamentos.jsp', 'indicacao', 1200, 650);"
                             styleClass="titulo3 linkFuncionalidade"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}bi-aulas-experimentais-adm/"
                          title="Clique e saiba mais: Agendamentos" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <%--Contatos App--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.relatorioContatosAPP}">
            <a4j:commandLink id="btnLateralContatoAPP"
                             ajaxSingle="true"
                             oncomplete="abrirPopup('contatosApp.jsp', 'contatosApp', 1000, 650);"
                             value="Contatos App"
                             styleClass="titulo3 linkFuncionalidade"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-enviar-notificacao-para-o-app-de-um-unico-aluno/"
                          title="Clique e saiba mais: Contatos App" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <%--Gest�o de Carteiras--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.organizadorCarteira}">
        <a4j:commandLink id="botaoOrganizadorCarteira" ajaxSingle="true"
                         action="#{LoginControle.abrirCarteirasCRM}"
                             value="Gest�o de Carteiras"
                             styleClass="titulo3 linkFuncionalidade"/>
        <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}o-que-e-a-gestao-de-carteiras/"
                          title="Clique e saiba mais: Gest�o de Carteira"
                          target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h:panelGroup>

        <%--Hist�rico de Contatos--%>
        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.historicoContato}">
            <a4j:commandLink id="btnLateralHistoricoContato"
                             ajaxSingle="true"
                             action="#{HistoricoContatoControle.inicializarDadosRealizarContato}"
                             oncomplete="abrirPopup('historicoContatoCons.jsp', 'historicoContatoCons', 765, 595);"
                             value="Hist�rico de Contatos"
                             styleClass="titulo3 linkFuncionalidade"/>
        <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCRM}Consultas:Consulta_Hist�rico_Contato"
                          title="Clique e saiba mais: Consulta Hist�rico Contato" target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h:panelGroup>

        </h:panelGroup>
</h:panelGroup>

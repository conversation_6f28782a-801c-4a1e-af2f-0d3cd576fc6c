<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<a4j:loadStyle src="/css/toggle_menu.css"/>
<a4j:loadScript src="/script/toggle_menu.js"/>
<link href="beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<link href="bootstrap/bootplus.css" rel="stylesheet">
<table width="100%" align="left" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td width="20%" align="left" valign="top">
            <h:panelGroup layout="block" styleClass="#{SuporteControle.classTopoCRM}"/>
        </td>
        <%@include file="includes/include_feedGestao.jsp" %>
        <%@include file="includes/include_expiracao.jsp" %>
        <%--<td style=" vertical-align: top;">--%>
                <%--<c:if test="${InicioControle.qntEmpresasPermitidas > 1}">--%>
            <%--<%@include file="includes/include_modal_trocarEmpresa.jsp" %>--%>
                    <%--</c:if>--%>
        <%--</td>--%>
        <td align="right" valign="top" style="padding-right:13px;">
            <table border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td align="left" valign="top">
                        <div style="padding:0px 6px; background-image: url(beta/imagens/bg-topo-menu-15.png); border-top: none; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px; overflow: hidden;">
                            <ul class="abatop">
                                <li>
                                    <%@include file="/includes/include_menu_modulos.jsp" %>
                                </li>

                                <li><img src="images/aba_sep.gif"></li>

                                <%@include file="include_top_socialMailing.jsp" %>

                                <%@include file="/includes/include_modal_trocarEmpresa.jsp" %>
                                <li>
                                    <rich:dropDownMenu>
                                        <f:facet name="label">
                                            <h:panelGroup>
                                                <i class="fa-icon-question-sign fa-icon-2x"></i>
                                            </h:panelGroup>
                                        </f:facet>
                                        <rich:menuItem submitMode="none"
                                                       icon="/faces/beta/imagens/mini-icons-wiki.png">
                                            <h:outputLink id="linkwiki"
                                                          style="color:#555;" target="_blank" value='#{SuperControle.urlWikiRaiz}/ZillyonWeb:Módulo_CRM'>
                                            WikiPacto
                                            </h:outputLink>
                                        </rich:menuItem>
                                        <rich:menuItem submitMode="none"
                                                icon="/faces/beta/imagens/mini-icons-suport.png">
                                            <h:outputLink id="linksolicitacao"
                                                          style="color:#555;"  target="_blank" value="#{SuporteControle.urlSolicitacao}">
                                                Suporte
                                                <a4j:support event="onclick" action="#{SuporteControle.prepareUserService}" oncomplete="#{SuporteControle.loginService}"/>
                                            </h:outputLink>
                                        </rich:menuItem>

                                    </rich:dropDownMenu>
                                </li>

                                <li>
                                    <rich:dropDownMenu itemClass="itemMnuTpo">
                                        <f:facet name="label">
                                            <h:panelGroup>
                                                <i class="fa-icon-cog fa-icon-2x"></i>
                                            </h:panelGroup>
                                        </f:facet>
                                        <rich:menuItem submitMode="ajax"
                                                icon="/faces/beta/imagens/mini-icons-config.png">
                                            <a4j:commandLink id="linkConfiguracoes"
                                                             action="#{ConfiguracaoSistemaCRMControle.iniciar}"
                                                             oncomplete="abrirPopup('configuracaoSistemaCRMForm.jsp', 'configuracaoSistemaCRM', 900, 595);"
                                                             value="Configurações"
                                                             style="color: #555;"/>
                                        </rich:menuItem>
                                        <rich:menuItem submitMode="ajax"
                                                icon="/faces/beta/imagens/mini-icons-calend.png">
                                            <a4j:commandLink style="color: #555" id="linkGoogleCalendar" rendered="#{!fn:contains(uriPagina, 'pages')}" action="#{ConfiguracaoSistemaControle.novo}"
                                                             oncomplete="window.open('#{uriPagina}googlecalendar.jsp', 'GoogleCalendar', 820, 620);" value="Google Calendar"/>
                                        </rich:menuItem>
                                        <rich:menuItem submitMode="ajax"
                                                icon="/faces/beta/imagens/mini-icons-velo.png">
                                            <a4j:commandLink style="color: #555" id="linkVelocimetro" rendered="#{!fn:contains(uriPagina, 'pages')}" oncomplete="abrirPopup('#{urlPath}velocimetro.jsp', 'Velocimetro', 500, 300);"  value="Velocímetro"/>
                                        </rich:menuItem>
                                        <rich:menuItem submitMode="ajax"
                                                icon="/faces/beta/imagens/mini-icons-key.png">
                                            <a4j:commandLink rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}" value="Alterar Senha"
                                                             onclick="abrirPopup('#{root}/faces/alterarSenhaClienteForm.jsp', 'AlterarSenha', 410, 350);"
                                                             title="Alterar senha" styleClass="text2"
                                                             style="valign:middle;cursor:pointer;color:#555;"/>
                                        </rich:menuItem>
                                        <rich:menuSeparator id="menuSeparator11" />

                                        <rich:menuItem submitMode="none" value="Sair"
                                                       icon="/faces/beta/imagens/mini-icons-sair.png"
                                                       onclick="document.location.href='#{LogoutControle.redirectLogout}'" />
                                    </rich:dropDownMenu>
                                </li>
                                <%--li><img src="images/aba_sep.gif"></li>
										
									<%@include file="include_top_socialMailing.jsp" %>
                                <li><img src="imagensCRM/aba_sep.gif"></li>
                                <li style="width: 80px;text-align: center"><a4j:commandLink action="#{ConfiguracaoSistemaCRMControle.iniciar}"  value="Configurações"
                                                 oncomplete="abrirPopup('configuracaoSistemaCRMForm.jsp', 'configuracaoSistemaCRM', 870, 595);" /></li>
                                <li><img src="imagensCRM/aba_sep.gif"></li>
                                <li style="width: 60px; text-align: center"><%@include file="includes/include_logout.jsp" %></li--%>
                            </ul>
                        </div>
                    </td>
                </tr>
                <jsp:include page="/includes/include_top_datahora.jsp" flush="true"/>
            </table>
        </td>
        <%--
        <td align="right" valign="top" style="padding-right:0px;">
            <table border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td style="margin: 0 0 0 0;padding: 0 0 0 0;" align="left" valign="top"><img src="imagensCRM/aba_top_left.png" width="11" height="32"></td>
                    <td style="margin: 0 0 0 0;padding: 0 0 0 0;" align="left" valign="top" background="imagensCRM/aba_top_middle.png">
                        <div>
                            <ul class="abatop">
                                <li ><a4j:commandLink action="#{ConfiguracaoSistemaCRMControle.iniciar}"  value="Configurações"
                                				     onclick="abrirPopup('configuracaoSistemaCRMForm.jsp', 'configuracaoSistemaCRM', 780, 595);" /></li>
                                <li><img src="imagensCRM/aba_sep.gif"></li>                                
                                <li><a4j:commandLink action="#{LoginControle.abrirZillyonWeb}"  value="Zillyon Web" actionListener="#{SkinControle.definirSkinZillyon}" />
                                </li>
                                <li><img src="imagensCRM/aba_sep.gif"></li>
                                <li><%@include file="includes/include_logout.jsp" %></li>
                            </ul>
                        </div>
                    </td>
                    <td width="11" align="left" valign="top"><img src="imagensCRM/aba_top_right.png" width="11" height="32"></td>
                </tr>
                <jsp:include page="/includes/include_top_datahora.jsp" flush="true"/>
            </table>
        </td>
        --%>
    </tr>
</table>


<rich:modalPanel id="panelMedidor" autosized="true"
                 shadowOpacity="true">



    <a4j:outputPanel>

        <a4j:commandButton id="fechar" value="#{msg_bt.btn_fechar}"
                           image="./imagens/botaoFechar.png" alt="Fechar Janela"
                           action="#{SuporteControle.fecharMedidor}"
                           oncomplete="Richfaces.hideModalPanel('panelMedidor')" />

    </a4j:outputPanel>




</rich:modalPanel>






<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
<script type="text/javascript">
    setDocumentCookie('popupsImportante', 'close',1);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>


<%
    pageContext.setAttribute("modulo", "zillyonWeb");
%>

<c:set var="root" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Caixa em Aberto"/>
    </title>
    <h:form id="form" >
        <input type="hidden" value="${modulo}" name="modulo"/>
        <html>
        <jsp:include page="include_head.jsp" />

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza menu-#{MenuControle.apresentarMenu}">
            <h:panelGroup layout="block" styleClass="bgtop topoZW menu-#{MenuControle.apresentarMenu}" rendered="#{MenuControle.apresentarTopo}">
                <c:if test="${MenuControle.apresentarMenu}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                </c:if>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central menu-#{MenuControle.apresentarMenu}">
                            <%@include file="include_tela_caixaAberto.jsp" %>
                        </h:panelGroup>
                        <c:choose>
                            <c:when test="${(not LoginControle.apresentarLinkZW) && LoginControle.apresentarLinkEstudio}">
                                <jsp:include page="pages/estudio/includes/include_box_menulateral_sc.jsp" flush="true" />
                            </c:when>
                            <c:when test="${MenuControle.apresentarMenu}">
                                <jsp:include page="include_box_menulateral.jsp" flush="true" />
                            </c:when>
                        </c:choose>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <c:if test="${MenuControle.apresentarMenu}">
                <jsp:include  page="include_rodape_flat.jsp" flush="true"/>
            </c:if>
        </h:panelGroup>
        </body>
        </html>
    </h:form>

    <rich:modalPanel id="mdlJustificativa" autosized="true" shadowOpacity="true" width="450" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Justificativa para o Cancelamento"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideLinkMdlJustificativa" />
                <rich:componentControl for="mdlJustificativa" attachTo="hideLinkMdlJustificativa" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="frmJustificativa">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background: url('./imagens/fundoBarraTopo.png') repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Justificativa" />
                </h:panelGrid>
                <h:panelGrid id="pnlJustificativa" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:outputText styleClass="text" value="Justificativa:" />
                        <h:inputTextarea styleClass="form" id="txtJustificativa" style="margin-left:5px" rows="4" cols="80" value="#{MovParcelaControle.justificativaCancelamento}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <a4j:commandButton id="confirmarCancelamentoParc"
                                   value="#{msg_bt.btn_confirmar}" image="./imagens/btn_Confirmar.png"
                                   action="#{MovParcelaControle.fecharRichModalPanelConfirmacao}"
                                   oncomplete="Richfaces.hideModalPanel('mdlJustificativa');"
                                   reRender="panelAutorizacaoFuncionalidade"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="include_modal_renegociacao.jsp" %>
    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp"/>

    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>

</f:view>

<script type="text/javascript">
    document.getElementById("form:valorConsulta").focus();
</script>

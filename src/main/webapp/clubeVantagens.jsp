<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@include file="/includes/imports.jsp" %>

<head>
    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
</head>
<link rel="shortcut icon" href="./favicon.ico">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<style type="text/css">

    .class30 {
        width: 30%;
    }
    .class70 {
        width: 70%;
    }
    .iconCalendar {
        width: 0.8vw;
        height: 0.8vw;
        padding-top: 7px;
        padding-left: 5px;
    }

    .novaModal .rich-mpnl-controls {
        top: 1.5vw !important;
        right: 1.5vw !important;
    }

    .novaModal .rich-mpnl-header {
        font-size: 1vw !important;
        padding-left: 0.7vw !important;
        margin-top: -4vw;
        padding-bottom: 1vw !important;
        padding-top: 5vw !important;
    }

    .rotuloCampos {
        color: black !important;
        font-size: 0.75vw !important;
    }

    .rich-panel-body {
        padding: 1vw;
    }

    .buttonStyle {
        font-size: 0.6vw !important;
        font-weight: normal !important;
        border: none;
        background-image: none !important;
        color: white !important;
        padding: 0.5vw 0.5vw 0.5vw 0.5vw !important;
        width: 7.5vw;
        border-radius: 0.3vw;;
    }

    .novaModal .rich-mpnl-header-cell {
        background-color: white !important;
        height: auto !important;
        padding-bottom: 1vw;
    }

    .novaModal .rich-mpnl-content {
        padding: 1vw !important;
        background-color: white !important;
    }

    .rich-table {
        background: none;
        border: none;
    }

    .rich-panel {
        padding: 0px;
        margin-bottom: 0.25vw;
        border-color: #FFF;
        border-radius: 0.5vw;
        -moz-border-radius: 0.5vw;
        -webkit-border-radius: 0.5vw;
        background-color: #ffffff;
        width: 100%;
    }

    .rich-paneltp {
        float: left;
        border-radius: 0.4vw;
        border: 0.05vw solid lightgray;
        width: 17.5vw;
    }

    .rich-panel label {
        font-size: 0.5vw;
        color: #666;
        margin-left: 0.25vw;
    }

    .rich-panel input[type=checkbox] {
        margin-left: 1.25vw;
    }

    .rich-table-cell {
        margin-bottom: 0.25vw;
        border-color: #FFF;
        padding: 0.25vw;
        background: none;
        border: none;
        width: 25%;
    }

    .rich-table-cell span {
        font-family: Arial;
        font-size: 0.9vw;
    }

    .linha-separadora {
        margin: 0.5vw 0px 0.5vw 0px;
        width: 100%;
        border-bottom: 1px solid #DCDDDF;
    }

    .rich-panel-header {
        margin-bottom: 0.25vw;
        border-color: #FFF;
        border-top-left-radius: 0.5vw;
        border-right-right-radius: 0.5vw;
        padding: 0.25vw;
        background-color: #ffffff;
        background-image: none;
    }

    .header-top-zw {
        padding-top: 3px;
        border-top-color: #34b65b;
        border-top-style: solid;
        border-top-width: 1px;
        display: block;
        width: auto;
    }

    .campanha {
        padding: 10px 0 5px 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left !important;
        color: #29AAE2;
    }

    .header-top-zw-ativo {
        font-family: Arial;
        font-weight: bold;
        font-size: 0.8vw !important;
        color: #64AF45;
    }

    .header-top-zw-inativo {
        font-family: Arial;
        font-weight: bold;
        font-size: 0.8vw !important;
        color: #BE2E26;
    }

    .tpGruposss td {
        font-size: 11px;
        color: #666;
        margin-left: 5px;
    }

    .tpGruposss td:nth-child(2) {
        padding-left: 5px;
        font-size: 11px;
        color: #666;
        margin-left: 5px;
    }

    .rich-modalpanel:not(.novaModal) .rich-mpnl-body {
        background: none !important;
    }

    .rich-mpnl-body img.xcloseModal {
        padding: 0 0 0 0;
        float: right !important;
    }

    .rich-mpnl-shadow {
        opacity: 0 !important;
    }

    .tituloCampos {
        line-height: 1.9 !important;
        padding: 5px;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_campanha}"/>
    </title>
    <h:form id="form">
        <a4j:commandButton id="btnAtualizaCampanhaDuracao"
                           title="Recarregar dados do cliente"
                           value="Atualizar"
                           style="display: none !important;"
                           reRender="gridCampanha, panelGeral"
                           action="#{ClubeVantagensControle.montarListaCampanha}">
        </a4j:commandButton>
        <a4j:keepAlive beanName="ClubeVantagensControle"/>
        <html>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemClubeVantagens" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup id="panelConteudo">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box"
                                                      style="border-bottom: 1px solid lightgray; display: inline-block !important;">
                                            <h:panelGrid columns="2" style="width: 100%;">
                                                <h:panelGroup style="float: left !important;" layout="block">
                                                    <h:outputText value="#{msg_aplic.prt_campanha}"
                                                                  styleClass="container-header-titulo"
                                                                  style="font-size: 1vw !important;"/>
                                                    <h:outputLink styleClass="linkWiki"
                                                                  value="#{ClubeVantagensControle.linkWiki}"
                                                                  title="Clique e saiba mais: Clube de Vantagens"
                                                                  target="_blank">
                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                    </h:outputLink>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <a4j:commandLink id="btnCadastrarCampanha"
                                                                     styleClass="pure-button pure-button-primary"
                                                                     accesskey="2"
                                                                     style="float: right; padding: 0.7vw; font-size: 1vw; border-radius: 0.3vw;"
                                                                     status="false"
                                                                     action="#{ClubeVantagensControle.limparDadosCampanha}"
                                                                     reRender="panelGeralModal"
                                                                     oncomplete="Richfaces.showModalPanel('mdlEditarCampanha');">
                                                        Cadastrar Nova
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box" id="panelGeral">
                                        <h:panelGroup layout="block">
                                            <rich:dataGrid columns="4" style="width: 100%;"
                                                           id="listaCampanha"
                                                           value="#{ClubeVantagensControle.listaCampanhas}"
                                                           var="campanha">
                                                <rich:panel styleClass="rich-paneltp">

                                                    <h:panelGrid columns="1" style="line-height: 1.2vw; width: 100%">
                                                        <h:outputText value="#{campanha.nome}"
                                                                      style="font-size: 0.9vw; font-weight: bold;"/>
                                                        <h:outputText
                                                                styleClass="#{campanha.campanhaVigente ? 'header-top-zw-ativo' : 'header-top-zw-inativo'}"
                                                                value="#{campanha.campanhaVigente ? 'VIGENTE' : 'NÃO VIGENTE'}"/>
                                                        <h:panelGroup styleClass="center" layout="block">
                                                            <h:outputText value="#{campanha.descricao}"
                                                                          style="font-size: 0.7vw !important; color: #9D9D9D;"/>
                                                        </h:panelGroup>
                                                        <h:panelGroup styleClass="linha-separadora" layout="block"/>
                                                    </h:panelGrid>

                                                    <h:panelGrid id="gripIntermediario" columns="2" columnClasses="class30, class70"
                                                                 style="float: left;line-height: 1.2vw !important; width: 100%">
                                                        <h:outputText value="Empresa"
                                                                      style="font-size: 0.8vw;font-weight: bold; padding-right: 1vw;"/>
                                                        <h:outputText value="#{campanha.empresa.nomeApresentar}"
                                                                      title="#{campanha.empresa.nome}"
                                                                      styleClass="tooltipster"
                                                                      style="font-size: 0.7vw; color: #9D9D9D;"/>

                                                        <h:outputText value="Inicio"
                                                                      style="font-size: 0.8vw;font-weight: bold; padding-right: 1vw;"/>
                                                        <h:outputText
                                                                value="#{campanha.dataInicial__Apresentar}"
                                                                style="font-size: 0.7vw;  color: #9D9D9D;"/>
                                                        <h:outputText value="Final"
                                                                      style="font-size: 0.8vw;font-weight: bold; padding-right: 1vw;"/>
                                                        <h:outputText
                                                                value="#{campanha.dataFinal__Apresentar}"
                                                                style="font-size: 0.7vw;  color: #9D9D9D;"/>

                                                    </h:panelGrid>

                                                    <h:panelGrid id="gripInferior" columns="2"
                                                                 style="float: left; margin-top: 1vw; margin-bottom: 1vw; width: 100%">
                                                        <a4j:commandButton
                                                                id="btnEditar"
                                                                style="background-color: #074871;"
                                                                styleClass="buttonStyle"
                                                                actionListener="#{ClubeVantagensControle.abrirCampanha}"
                                                                oncomplete="#{ClubeVantagensControle.oncomplete}"
                                                                value="Editar" reRender="mdlEditarCampanha">
                                                            <f:attribute name="objCampanhaGrid"
                                                                         value="#{campanha}"/>
                                                        </a4j:commandButton>
                                                        <a4j:commandButton
                                                                id="btnExcluir1"
                                                                style="background-color: white; color: #074871 !important; border: 1px solid #074871;"
                                                                styleClass="buttonStyle"
                                                                actionListener="#{ClubeVantagensControle.validarDadosExclussao}"
                                                                reRender="mdlAvisoExcluirCampanha"
                                                                oncomplete="#{ClubeVantagensControle.oncomplete}"
                                                                value="Excluir">
                                                            <f:attribute name="objCampanhaGrid"
                                                                         value="#{campanha}"/>
                                                        </a4j:commandButton>
                                                    </h:panelGrid>

                                                </rich:panel>

                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-CLUBE_VANTAGENS"/>
                        </jsp:include>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>
        <h:panelGroup layout="block" id="containerFuncMask">
            <script>
                var ultimoBlocoTrocado = {
                    bloco: '.filtroCampanha',
                    container: '.panelCampanhas',
                    tipoBotao: '.botaoModoTimeLine'
                };

                function trocarUltimoBloco() {
                    trocarBloco(ultimoBlocoTrocado.bloco, ultimoBlocoTrocado.container, ultimoBlocoTrocado.tipoBotao);
                }

                function trocarBloco(bloco, container, tipoBotao) {
                    ultimoBlocoTrocado.bloco = bloco;
                    ultimoBlocoTrocado.container = container;
                    ultimoBlocoTrocado.tipoBotao = tipoBotao;
                    jQuery(container + '.visivel').slideUp();
                    jQuery(container + bloco).slideDown();
                    jQuery(container + bloco).addClass('visivel');
                    jQuery(tipoBotao).removeClass('ativo');
                    jQuery(tipoBotao + bloco).addClass('ativo');
                }

                trocarUltimoBloco();
            </script>
        </h:panelGroup>
    </h:form>
    <rich:modalPanel id="mdlAvisoExcluirCampanha" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     width="450" height="200">
        <f:facet name="header">
            <h:panelGroup style="color: black!important;">
                <h:outputText value="Exclusão da Campanha"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:outputText value="Deseja realizar a exclusão dessa Campanha?"
                              style="color: #777;font-size: 16px;font-family: Arial;"/>
                <h:panelGroup layout="block">
                    <a4j:commandLink action="#{ClubeVantagensControle.excluir}"
                                     id="confirmacaoExclusaoCampanha"
                                     reRender="form, formEditar, mdlAvisoExcluirCampanha"
                                     accesskey="2"
                                     styleClass="pure-button pure-button-primary"
                                     style="margin-top: 16px;"
                                     oncomplete="#{ClubeVantagensControle.mensagemNotificar};#{ClubeVantagensControle.oncomplete}">
                        Sim
                    </a4j:commandLink>
                    <a4j:commandLink value="Não" status="false"
                                     onclick="Richfaces.hideModalPanel('mdlAvisoExcluirCampanha');"
                                     reRender="mdlAvisoExcluirCampanha" id="confirmacaoOpercaoNao"
                                     styleClass="pure-button"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="mdlEditarCampanha" styleClass="novaModal" autosized="true" shadowOpacity="true"
                     style="overflow: auto; height: 90vh;">

        <f:facet name="header">
            <h:panelGroup style="color: black!important;">
                <h:outputText value="Editar Campanha"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText styleClass="linkPadrao fa-icon-remove floatright" style="font-size: 1.5vw !important;"
                              id="hidelink"/>
                <rich:componentControl for="mdlEditarCampanha" attachTo="hidelink"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formEditar">
            <h:panelGrid id="panelGeralModal" columns="1" width="100%">
                <h:panelGrid columns="1">
                    <h:outputText style="display: block" styleClass="rotuloCampos margenVertical"
                                  value="Selecionar Empresa"/>
                    <h:panelGroup id="groupEmpresa" layout="block" styleClass="font-size-em-max">
                        <h:panelGroup styleClass="cb-container margenVertical" layout="block">
                            <h:selectOneMenu id="empresaSelecionadaCD" tabindex="8"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             disabled="#{ClubeVantagensControle.campanha.codigo>0}"
                                             style="font-size: 14px !important;"
                                             value="#{ClubeVantagensControle.campanha.empresa.codigo}">
                                <f:selectItems value="#{ItemCampanhaControle.listaEmpresas}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>

                <h:outputText styleClass="rotuloCampos" value="Nome"/>
                <rich:spacer height="3px"/>
                <h:inputText id="nomeBrinde" size="#{ClubeVantagensControle.QTD_MAX_NOME + 5}"
                             maxlength="#{ClubeVantagensControle.QTD_MAX_NOME}"
                             style="font-size: 14px !important;" styleClass="inputTextClean"
                             value="#{ClubeVantagensControle.campanha.nome}"/>
                <rich:spacer height="10px"/>
                <h:outputText id="labelPeriodo" styleClass="rotuloCampos" value="Data de Vigência"/>
                <h:panelGroup id="panelPeriodo">
                    <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                        <rich:calendar value="#{ClubeVantagensControle.campanha.dataInicial}"
                                       id="dataInicial"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       buttonIcon="/imagens_flat/icon-calendar-check.png"
                                       buttonClass="iconCalendar"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                    <h:outputText styleClass="rotuloCampos" value="até" style="margin-left: 15px;"/>
                    <h:panelGroup styleClass="dateTimeCustom"
                                  style="font-size: 11px !important;margin-left: 15px;">
                        <rich:calendar value="#{ClubeVantagensControle.campanha.dataFinal}"
                                       id="dataFinal"
                                       inputSize="10"
                                       buttonIcon="/imagens_flat/icon-calendar-check.png"
                                       buttonClass="iconCalendar"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                </h:panelGroup>
                <rich:spacer height="10px"/>
                <h:outputText styleClass="rotuloCampos" value="Breve Descrição"/>
                <rich:spacer height="3px"/>
                <h:inputText id="descricao" style="font-size: 14px !important;"
                             size="#{ClubeVantagensControle.QTD_MAX_DESCRICAO + 5}"
                             maxlength="#{ClubeVantagensControle.QTD_MAX_DESCRICAO}"
                             styleClass="inputTextClean"
                             value="#{ClubeVantagensControle.campanha.descricao}"/>
            </h:panelGrid>
            <h:panelGroup layout="block">
                <jsp:include page="includes/clubedevantagens/tabela_categorias.jsp"/>
            </h:panelGroup>
            <h:panelGrid columns="4" width="100%" columnClasses="colunaCentralizada" style="margin-top: 2vw;">
                <a4j:commandButton reRender="@form,panelGeralModal,listaCampanha,tblComPontos"
                                   styleClass="buttonStyle"
                                   style="background-color: green;"
                                   accesskey="2"
                                   id="novo"
                                   action="#{ClubeVantagensControle.montarNovaCampanha}"
                                   value="Novo">
                </a4j:commandButton>
                <a4j:commandButton reRender="@form,panelGeralModal,listaCampanha,tblComPontos"
                                   id="salvar"
                                   style="background-color: #1B4166;"
                                   styleClass="buttonStyle"
                                   accesskey="2"
                                   action="#{ClubeVantagensControle.gravar}"
                                   oncomplete="#{ClubeVantagensControle.mensagemNotificar};fireElementFromAnyParent('form:btnAtualizaCampanhaDuracao');"
                                   value="Salvar">
                </a4j:commandButton>
                <a4j:commandButton
                        action="#{ClubeVantagensControle.realizarConsultaLogObjetoSelecionado}"
                        reRender="form"
                        style="background-color: white; color: #1B4166 !important; border: 1px solid;"
                        oncomplete="fireElementFromAnyParent('formEditar:btnAtualizaTempo');abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                        title="Visualizar Log"
                        styleClass="buttonStyle"
                        value="Logs">
                </a4j:commandButton>
                <a4j:commandButton reRender="@form,listaCampanha,tblComPontos"
                                   styleClass="buttonStyle"
                                   style="background-color: #F15858;"
                                   accesskey="2"
                                   id="excluir"
                                   action="#{ClubeVantagensControle.validarDadosExclusao}"
                                   oncomplete="#{ClubeVantagensControle.oncomplete}"
                                   value="Excluir">
                </a4j:commandButton>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
    <script>
        jQuery('.tooltipster').tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    </script>
</f:view>
<%-- 
    Document   : include_modal_expiracaoSenha
    Created on : 03/07/2012, 11:41:07
    Author     : carla
--%>
<%@include file="includes/imports.jsp" %>
<rich:modalPanel id="panelExpiracaoSenha" domElementAttachment="parent" styleClass="novaModal"
                     showWhenRendered="#{LoginControle.mostrarModalExpiracaoSenha}"
                     autosized="false" shadowOpacity="false" width="420"  >

        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_AvisoExpiracaoSenha_tituloForm}"/>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                            id="hidelinkSenhaExpirando"/>
                    <rich:componentControl for="panelExpiracaoSenha" attachTo="hidelinkSenhaExpirando" operation="hide"
                                           event="onclick"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formExpiracaoSenha"  ajaxSubmit="false">
            <h:panelGrid columns="1">
                <h:panelGrid columns="2" >
                    <h:outputText styleClass="fa-icon-warning-sign texto-cor-vermelho texto-size-20-real texto-bold "/>
                    <h:outputText value="Expira��o de Senha" styleClass="texto-cor-cinza texto-size-16-real texto-bold" style="font-weight:bold;"/>
                </h:panelGrid>
                <h:panelGrid columns="2">
                    <h:outputText id="msgAvisoExpiracao"  styleClass="texto-cor-cinza texto-size-16-real " value="Sua senha est� pr�xima de expirar.#{LoginControle.diasFaltaExpirarFormatado} " />
                </h:panelGrid>
                <h:panelGrid columns="1">
                    <a4j:commandLink rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}" id="linkExpiracaoSenha" reRender="panelExpiracaoSenha"
                                     onclick="abrirPopup('#{root}/faces/alterarSenhaClienteForm.jsp', 'AlterarSenha', 410, 350);"
                                     styleClass="linkPadrao texto-cor-azul texto-size-14-real" value="Clique aqui para alterar sua senha"/>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
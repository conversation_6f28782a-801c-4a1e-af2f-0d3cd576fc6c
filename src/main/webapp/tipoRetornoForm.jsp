<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_TipoRetorno_tituloForm}"/>
    </title>
    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_TipoRetorno_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-tipo-de-retorno/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>

    </f:facet>

    <h:form id="form">
        <hr style="border-color: #e6e6e6;"/>
        <h:commandLink action="#{TipoRetornoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
                       style="display: none"/>
        <h:panelGrid columns="1" width="100%">
            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                         width="100%">

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_TipoRetorno_codigo}"/>
                <h:panelGroup>
                    <h:inputText id="codigo" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 value="#{TipoRetornoControle.tipoRetornoVO.codigo}"/>
                    <h:message for="codigo" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_TipoRetorno_descricao}"/>
                <h:panelGroup>
                    <h:inputText id="descricao" size="50" maxlength="50" styleClass="form" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" value="#{TipoRetornoControle.tipoRetornoVO.descricao}"/>
                    <h:message for="descricao" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_TipoRetorno_arquivoLayoutRetorno}"/>
                <h:panelGroup>
                    <h:inputText id="arquivoLayoutRetorno" size="50" maxlength="50" styleClass="form"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 value="#{TipoRetornoControle.tipoRetornoVO.arquivoLayoutRetorno}"/>
                    <h:message for="arquivoLayoutRetorno" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">

                        <h:outputText value=" "/>

                    </h:panelGrid>
                    <h:commandButton rendered="#{TipoRetornoControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{TipoRetornoControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{TipoRetornoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{TipoRetornoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="novo" immediate="true" action="#{TipoRetornoControle.novo}"
                                         value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                         styleClass="botoes nvoBt btSec"/>

                        <h:outputText value="    "/>

                        <a4j:commandButton id="salvar" action="#{TipoRetornoControle.gravar}" value="#{msg_bt.btn_gravar}"
                                         alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                        <h:outputText value="    "/>

                        <h:panelGroup id="grupoBtnExcluir">
                            <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                               oncomplete="#{TipoRetornoControle.msgAlert}" action="#{TipoRetornoControle.confirmarExcluir}"
                                               value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                        </h:panelGroup>

                        <h:outputText value="    "/>

                        <a4j:commandButton id="consultar" immediate="true"
                                         action="#{TipoRetornoControle.inicializarConsultar}"
                                         value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4"
                                         styleClass="botoes nvoBt btSec btPerigo"/>
                        <h:outputText value="    "/>
                        <a4j:commandLink action="#{TipoRetornoControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                       </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
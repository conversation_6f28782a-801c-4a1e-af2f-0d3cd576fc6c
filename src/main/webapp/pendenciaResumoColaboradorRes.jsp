<%-- 
    Document   : pendenciaResumoColaboradorRes
    Created on : 04/07/2011, 16:37:54
    Author     : carla
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo de Colaborador(es) \"#{PendenciaControleRel.pendenciaRelVO.tipo.descricao}\""/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
        <body onload="fireElement('form:botaoAtualizarPagina')"/>
        <h:form id="form">
            <c:set var="titulo" scope="session"
                   value="Resumo de Colaborador(es) ${PendenciaControleRel.pendenciaRelVO.tipo.descricao}"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
                <f:facet name="header">
                    <jsp:include page="topo_reduzido_popUp.jsp"/>
                </f:facet>
            </h:panelGroup>
            <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh"
                          styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%">
                                        <rich:dataTable width="100%" id="tabelaRes"
                                                        styleClass="tabelaSimplesCustom"
                                                        value="#{PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs}"
                                                        rows="50" var="resumoPessoa" rowKeyVar="status">
                                            <rich:column styleClass="col-text-align-left"
                                                         headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText
                                                            styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                            value="Código"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                                              value="#{resumoPessoa.colaboradorVO.codigo}"/>
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left"
                                                         headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText
                                                            styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                            value="Nome"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                                              value="#{resumoPessoa.colaboradorVO.pessoa.nome}"/>
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left"
                                                         headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText
                                                            styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                            value="CPF"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                                              value="#{resumoPessoa.colaboradorVO.pessoa.cfp}"/>
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left"
                                                         headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText
                                                            styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                                            value="Situação"/>
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                                              value="#{resumoPessoa.colaboradorVO.situacao_Apresentar}"/>
                                            </rich:column>
                                            <rich:column width="5%" styleClass="col-text-align-center"
                                                         headerClass="col-text-align-center">
                                                <a4j:commandLink action="#{PendenciaControleRel.irParaTelaColaborador}"
                                                                 styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                                                 oncomplete="abrirPopup('colaboradorForm.jsp', 'Colaborador', 1024, 700);">
                                                    <f:param name="state" value="AC"/>
                                                    <i class="fa-icon-search"></i>
                                                </a4j:commandLink>
                                            </rich:column>
                                        </rich:dataTable>
                                        <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false"
                                                           align="center" for="form:tabelaRes" maxPages="10"
                                                           id="sctabelaRes"/>
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                                                 id="paginacao">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td align="center" valign="middle">
                                                    <h:panelGroup id="painelPaginacao"
                                                                  rendered="#{PendenciaControleRel.confPaginacao.existePaginacao}"
                                                                  styleClass="container-botoes">
                                                        <a4j:commandLink id="pagiInicial"
                                                                         styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                         reRender="tabelaRes, paginaAtual, totalItens"
                                                                         action="#{PendenciaControleRel.primeiraPagina}">
                                                            <i class="fa-icon-double-angle-left"></i>
                                                        </a4j:commandLink>

                                                        <a4j:commandLink id="pagiAnterior"
                                                                         styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                         reRender="tabelaRes, paginaAtual, totalItens"
                                                                         action="#{PendenciaControleRel.paginaAnterior}">
                                                            <i class="fa-icon-angle-left"></i>
                                                        </a4j:commandLink>

                                                        <h:outputText id="paginaAtual"
                                                                      styleClass="linkPadrao texto-font texto-cor-cinza texto-size-16-real"
                                                                      value="#{msg_aplic.prt_msg_pagina} #{PendenciaControleRel.confPaginacao.paginaAtualDeTodas}"
                                                                      rendered="true"/>
                                                        <a4j:commandLink id="pagiPosterior"
                                                                         styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                                                         reRender="tabelaRes, paginaAtual, totalItens"
                                                                         action="#{PendenciaControleRel.proximaPagina}">
                                                            <i class="fa-icon-angle-right"></i>
                                                        </a4j:commandLink>

                                                        <a4j:commandLink id="pagiFinal"
                                                                         styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                                         reRender="tabelaRes, paginaAtual, totalItens"
                                                                         action="#{PendenciaControleRel.ultimaPagina}">
                                                            <i class="fa-icon-double-angle-right"></i>
                                                        </a4j:commandLink>

                                                        <h:outputText id="totalItens"
                                                                      styleClass="texto-cor-azul texto-cor-cinza texto-font texto-size-16-real"
                                                                      style="display: block; text-align: left; margin-top: -53px;"
                                                                      value=" Total: #{PendenciaControleRel.confPaginacao.numeroTotalItens} itens"
                                                                      rendered="true"/>
                                                    </h:panelGroup>
                                                </td>
                                            </tr>
                                        </table>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
        </body>
        </html>
    </h:panelGrid>
</f:view>


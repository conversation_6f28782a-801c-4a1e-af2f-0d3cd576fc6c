<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="java.net.URL"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<c:set var="root" value="${pageContext.request.contextPath}" scope="request" />
<input type="hidden" name="context" value="${contexto}"/>
<input type="hidden" name="urlPath" value="${urlPath}"/>
<%
	String context = (String) request.getAttribute("context");
	pageContext.setAttribute("context",context);

        URL oURL = new URL(request.getRequestURL().toString());

        String urlPath = String.format("%s://%s:%s%s/", new Object[]{
            oURL.getProtocol(),
            oURL.getHost(),
            oURL.getPort(),
            request.getContextPath()
        });
        request.setAttribute("urlPath", urlPath);
        pageContext.setAttribute("urlPath",urlPath);
%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${contexto}/css/ce.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">var contexto = '${contexto}';</script>
<script type="text/javascript" language="javascript" src="${contexto}/script/ajuda.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Brinde_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Pontos:Brinde"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Brinde_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Pontos:Brinde"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
            </h:panelGrid>
            <h:panelGroup id="panelCadastroBrinde" layout="block" style="margin-left: 15px;">
                <h:panelGrid id="panelGeral" columns="1">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  value="#{msg_aplic.prt_Brinde_nome}"/>
                    <rich:spacer height="3px"/>
                    <h:inputText id="nomeBrinde" tabindex="1" size="35" styleClass="inputTextClean" style="font-size: 14px !important;"
                                 value="#{BrindeControle.brindeVO.nome}"/>
                    <rich:spacer height="13px"/>

                    <h:panelGrid columns="2">
                        <h:panelGroup>
                            <h:panelGroup styleClass="chk-fa-container">
                                <h:selectBooleanCheckbox value="#{BrindeControle.brindeVO.ativo}" tabindex="2"/>
                                <span/>
                            </h:panelGroup>
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          style="margin-left: 1px;" value="#{msg_aplic.ATIVO}"/>
                        </h:panelGroup>
                    </h:panelGrid>


                    <h:panelGrid columns="1">
                        <rich:spacer height="13px"/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="#{msg_aplic.prt_Brinde_pontos}"/>
                        <rich:spacer height="3px"/>
                        <h:inputText id="pontos" size="8" styleClass="inputTextClean" onkeyup="somenteNumeros(this);" style="font-size: 14px !important;"
                                     value="#{BrindeControle.brindeVO.pontos}" tabindex="3"/>
                        <rich:spacer height="13px"/>

                        <h:panelGrid columns="1">
                            <h:outputText   style="display: block" styleClass="rotuloCampos margenVertical" value="#{msg_aplic.prt_Brinde_empresa}" />
                            <h:panelGroup id="groupEmpresaBrinde" layout="block"  styleClass="font-size-em-max">
                                <h:panelGroup  styleClass="cb-container margenVertical" layout="block">
                                    <h:selectOneMenu id="listaEmpresa" tabindex="8" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     disabled="#{BrindeControle.brindeVO.codigo > 0}"  style="font-size: 14px !important;"
                                                     value="#{BrindeControle.empresaSelecionada}"
                                                     title="#{msg_aplic.prt_Brinde_alterar_empresa}">
                                        <f:selectItems value="#{BrindeControle.listaEmpresas}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <rich:spacer height="13px"/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="#{msg_aplic.prt_Brinde_descricao}"/>
                        <h:panelGroup>
                            <h:inputTextarea cols="70" rows="50" id="descricao" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="inputTextClean"
                                             style="height:5.4em; font-size: 14px !important;" value="#{BrindeControle.brindeVO.descricao}"
                                             tabindex="5"/>
                        </h:panelGroup>
                    </h:panelGrid>


                </h:panelGrid>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" width="100%"  style="margin-top: 13px;">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandLink reRender="form"
                                             styleClass="botoes nvoBt btSec"
                                             style="margin-top: 13px;"
                                             accesskey="2"
                                             id="novo"
                                             action="#{BrindeControle.novo}"
                                             tabindex="7">
                                Novo
                            </a4j:commandLink>
                            <h:outputText value="    "/>
                            <a4j:commandLink reRender="form"
                                             id="salvar"
                                             styleClass="botoes nvoBt"
                                             accesskey="2"
                                             action="#{BrindeControle.gravar}"
                                             oncomplete="#{BrindeControle.mensagemNotificar}"
                                             tabindex="8">
                                Gravar
                            </a4j:commandLink>
                            <h:outputText value="    "/>
                            <a4j:commandLink reRender="form,mdlAvisoExcluirBrinde"
                                             styleClass="botoes nvoBt btSec"
                                             accesskey="2"
                                             id="excluir"
                                             action="#{BrindeControle.validarDadosExclusao}"
                                             oncomplete="#{BrindeControle.oncomplete};"
                                             tabindex="9">
                                Excluir
                            </a4j:commandLink>
                            <h:outputText value="    "/>
                            <a4j:commandLink reRender="form"
                                             id="consultar"
                                             styleClass="botoes nvoBt btSec"
                                             accesskey="2"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             action="#{BrindeControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}"
                                             tabindex="10">

                            </a4j:commandLink>
                            <h:outputText value="    "/>
                            <a4j:commandLink
                                    action="#{BrindeControle.realizarConsultaLogObjetoSelecionado}"
                                    reRender="form"
                                    oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo');abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                    title="Visualizar Log"
                                    styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')"/>
        </h:form>
    </h:panelGrid>
    <rich:modalPanel id="mdlAvisoExcluirBrinde" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Exclus�o de Brinde"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:outputText value="Deseja realizar a exclus�o desse Brinde?" style="color: #777;font-size: 16px;font-family: Arial;"/>
                <h:panelGroup layout="block">
                    <a4j:commandLink  action="#{BrindeControle.excluir}"
                                      id="confirmacaoExclusaoBrinde"
                                      reRender="form,mdlAvisoExcluirBrinde"
                                      accesskey="2"
                                      styleClass="pure-button pure-button-primary"
                                      style="margin-top: 16px;"
                                      oncomplete="#{BrindeControle.mensagemNotificar};#{BrindeControle.oncomplete}">
                        Sim
                    </a4j:commandLink>
                    <a4j:commandLink value="N�o" status="false" onclick="Richfaces.hideModalPanel('mdlAvisoExcluirBrinde');" reRender="mdlAvisoExcluirBrinde" id="confirmacaoOpercaoNao" styleClass="pure-button"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>
<script>
    document.getElementById("form:nomeBrinde").focus();

    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        if (er.test(num.value)) {
            num.value = "";
        }
    }
</script>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Totalizador de Acessos</title>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .alinharMeio tbody tr td label {
        vertical-align: bottom;
        padding-right: 10px;
    }

</style>
<f:view>
    <title>
        <h:outputText value="#{msg_aplic.prt_TotalizadorTickets_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_TotalizadorTickets_tituloForm}"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}relatorio-totalizador-de-tickets/"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid id="dadosConsulta" columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText styleClass="tituloCampos" rendered="#{TotalizadorTicketsControleRel.permissaoConsultaTodasEmpresas}"
                                  value="#{msg_aplic.prt_CompetenciaSintetico_empresa}"/>
                    <h:panelGroup rendered="#{TotalizadorTicketsControleRel.permissaoConsultaTodasEmpresas}">
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{TotalizadorTicketsControleRel.filtroEmpresa}">
                            <f:selectItems value="#{TotalizadorTicketsControleRel.listaEmpresas}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_TotalizadorTickets_periodoPesquisa}"/>
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{TotalizadorTicketsControleRel.totalizadorFrequenciaRel.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="10px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ate}"/>
                        <rich:spacer width="10px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{TotalizadorTicketsControleRel.totalizadorFrequenciaRel.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>

                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                         query="mask('99/99/9999')"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_TotalizadorFrequencia_frequencia}"/>
                    <h:panelGroup layout="block" style="height: 20px">
                        <h:selectOneRadio id="ordenacao" styleClass="tituloCampos alinharMeio"
                                          value="#{TotalizadorTicketsControleRel.totalizadorFrequenciaRel.frequencia}">
                            <f:selectItems value="#{TotalizadorTicketsControleRel.listaSelectItemFrequencia}"/>
                            <a4j:support event="onchange" reRender="dadosConsulta"/>
                        </h:selectOneRadio>
                    </h:panelGroup>
                    <h:outputText id="textoSomenteAcessoDia" styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_TotalizadorFrequencia_contabilizarAcessoDia}"/>
                    <h:panelGroup layout="block" id="containeSomenteAcessoDia">
                        <h:selectBooleanCheckbox id="somenteAcessoDia"
                                                 styleClass="tituloCampos alinharMeio"
                                                 value="#{TotalizadorTicketsControleRel.totalizadorFrequenciaRel.somenteAcessoDia}">
                        </h:selectBooleanCheckbox>
                        <rich:toolTip for="textoSomenteAcessoDia"
                                      value="Irá considerar somente o primeiro acesso do cliente à academia no dia."/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem" value="#{TotalizadorTicketsControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{TotalizadorTicketsControleRel.mensagemDetalhada}"/>
                        <h:panelGroup layout="block" style="height:60px;margin: 0 auto;width: 20%;">
                            <a4j:commandLink id="imprimirPDF" action="#{TotalizadorTicketsControleRel.imprimir}"
                                             styleClass="pure-button pure-button-primary"
                                             oncomplete="#{TotalizadorTicketsControleRel.abrirPopUp}"
                                             accesskey="2"
                                             reRender="mensagem" value="Gerar Relatório"/>
                            <a4j:status>
                                <f:facet name="start">
                                    <h:panelGroup>
                                        <h:graphicImage width="30px" style=" padding: 5px 65px;"
                                                        value="../imagens/ajax.gif"/>
                                    </h:panelGroup>
                                </f:facet>
                            </a4j:status>

                        </h:panelGroup>
                    </h:panelGrid>

                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>


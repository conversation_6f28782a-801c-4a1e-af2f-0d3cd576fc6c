<%-- 
    Document   : faturamentoSinteticoResumoPessoa
    Created on : 26/10/2009, 18:48:13
    Author     : pedro
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_FaturamentoSintetico_tituloFormPessoas}"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="../topoReduzido.jsp"/>

        </f:facet>
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid columns="1" style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">

                        <h:outputText styleClass="tituloFormulario"
                                      value=" #{msg_aplic.prt_FaturamentoSintetico_tituloForm} - Total: #{fn:length(FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO.listaResumoPessoa)}"
                                      rendered="#{!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                            <h:outputLink value="#{SuperControle.urlBaseConhecimento}onde-vejo-minhas-vendas-do-mes-faturamento-por-periodo/"
                                          title="Clique e saiba mais: Faturamento por Período" target="_blank">
                                <h:graphicImage styleClass="linkWiki" url="../imagens/wiki_bco.gif"/>
                            </h:outputLink>
                        </h:outputText>

                        <h:outputText styleClass="tituloFormulario"
                                      value=" #{msg_aplic.prt_FaturamentoRecebidoSintetico_tituloForm} - Total: #{fn:length(FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO.listaResumoPessoa)}"
                                      rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                            <h:outputLink value="#{SuperControle.urlWiki}Relatorios:Faturamento_Recebido_por_Período"
                                          title="Clique e saiba mais: Faturamento Recebido por Período" target="_blank">
                                <h:graphicImage styleClass="linkWiki" url="../imagens/wiki_bco.gif"/>
                            </h:outputLink>
                        </h:outputText>

                    </h:panelGrid>
                    <h:panelGrid width="100%" style="margin-bottom: 8px;text-align: right;">
                        <h:panelGroup layout="block">
                            <%--BOTÃO EXCEL--%>
                            <a4j:commandButton id="exportarExcel"
                                               image="../imagens/btn_excel.png"
                                               style="margin-left: 8px;"
                                               actionListener="#{FaturamentoSinteticoControleRel.exportarResumoPessoa}"
                                               rendered="#{not empty FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO.listaResumoPessoa}"
                                               value="Excel"
                                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO.listaResumoPessoa}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="descricaoProduto=Produto,matricula_Apresentar=Matrícula,nome_Apresentar=Nome,nomeResponsavelLancamento=Resp. Venda,nomeResponsavelRecebimento=Resp. Recebimento,dataCadastro_Apresentar=Data de Cadastro,contrato_Apresentar=N° Contrato,dataInicio_Apresentar=Data Início,dataAte_Apresentar=Data Término,duracao_Apresentar=Duração,modalidade_Apresentar=Modalidades,plano_Apresentar=Plano,situacaoContrato_Apresentar=Situação do Contrato,dataLancamentoMovProduto_Apresentar=Data Lançamento,formaPagApresentar=Forma Pagamento,nomeProduto=Produto,quitado_Apresentar=Quitado,quantidade=Qtd,valorApresentar=Valor,nomeEmpresaPlanoOuVendaApresentar=Empresa Plano/Venda"/>
                                <c:if test="${!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                                    <f:attribute name="prefixo" value="FaturamentoPorPeriodoPessoa"/>
                                </c:if>
                                <c:if test="${FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                                    <f:attribute name="prefixo" value="FaturamentoRecebidoPorPeriodoPessoa"/>
                                </c:if>
                            </a4j:commandButton>
                            <%--BOTÃO PDF--%>
                            <a4j:commandButton id="exportarPdf"
                                               style="margin-left: 8px;"
                                               image="../imagens/imprimir.png"
                                               actionListener="#{FaturamentoSinteticoControleRel.exportarResumoPessoa}"
                                               rendered="#{not empty FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO.listaResumoPessoa}"
                                               value="PDF"
                                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO.listaResumoPessoa}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="descricaoProduto=Produto,matricula_Apresentar=Matrícula,nome_Apresentar=Nome,nomeResponsavelLancamento=Responsável Venda,nomeResponsavelRecebimento=Responsável Recebimento,dataCadastro_Apresentar=Data de Cadastro,contrato_Apresentar=N° Contrato,dataInicio_Apresentar=Data Início,dataAte_Apresentar=Data Término,duracao_Apresentar=Duração,modalidade_Apresentar=Modalidades,plano_Apresentar=Plano,situacaoContrato_Apresentar=Situação do Contrato,dataLancamentoMovProduto_Apresentar=Data Lançamento,formaPagApresentar=Forma Pagamento,nomeProduto=Produto,quitado_Apresentar=Quitado,quantidade=Qtd,valorApresentar=Valor"/>
                                <c:if test="${!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                                    <f:attribute name="prefixo" value="FaturamentoPorPeriodoPessoa"/>
                                </c:if>
                                <c:if test="${FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                                    <f:attribute name="prefixo" value="FaturamentoRecebidoPorPeriodoPessoa"/>
                                </c:if>
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                    binding="#{FaturamentoSinteticoControleRel.dadosTable}"
                                    value="#{FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO.listaResumoPessoa}" rows = "10" var="resumoPessoa" id="listaPessoa" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{resumoPessoa.cliente.matricula}" id="matriculaApresentar">
                            <f:facet name="header">
                                <h:outputText value="Matrícula" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.cliente.matricula}"
                                          rendered="#{resumoPessoa.cliente.codigo!=null && resumoPessoa.cliente.codigo!=0}"/>
                            <h:outputText rendered="#{resumoPessoa.colaboradorVO.codigo!=null && resumoPessoa.colaboradorVO.codigo!=0}"
                                          value="#{resumoPessoa.colaboradorVO.codAcesso}" />
                            <h:outputText rendered="#{(resumoPessoa.cliente.codigo==null || resumoPessoa.cliente.codigo == 0)
                                          && (resumoPessoa.colaboradorVO == null || resumoPessoa.colaboradorVO.codigo==null || resumoPessoa.colaboradorVO.codigo==0)}"
                                          value="(CONSUMIDOR)" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.cliente.pessoa.nome}" id="nomePessoaApresentar">
                            <f:facet name="header">
                                <h:outputText value="Nome" />
                            </f:facet>
                            <h:outputText rendered="#{resumoPessoa.cliente.codigo!=null && resumoPessoa.cliente.codigo!=0}"
                                          value="#{resumoPessoa.cliente.pessoa.nome}" />
                            <h:outputText value="#{resumoPessoa.colaboradorVO.pessoa.nome}"
                                          rendered="#{resumoPessoa.colaboradorVO.codigo!=null && resumoPessoa.colaboradorVO.codigo!=0}"/>
                            
                            <h:outputText rendered="#{(resumoPessoa.cliente.codigo==null || resumoPessoa.cliente.codigo == 0)
                                          && (resumoPessoa.colaboradorVO == null || resumoPessoa.colaboradorVO.codigo==null || resumoPessoa.colaboradorVO.codigo==0)}"
                                          value="#{resumoPessoa.consumidor}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.cliente.pessoa.dataCadastro_Apresentar}" id="dataCadastroApresentar">
                            <f:facet name="header">
                                <h:outputText value="Data Cadastro" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.cliente.pessoa.dataCadastro_Apresentar}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.nomeResponsavelLancamento}" id="nomeResponsavelLancamento">
                            <f:facet name="header">
                                <h:outputText value="Responsável Venda" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.nomeResponsavelLancamento}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.nomeResponsavelRecebimento}" id="nomeResponsavelRecebimento">
                            <f:facet name="header">
                                <h:outputText value="Responsável Recebimento" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.nomeResponsavelRecebimento}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contrato.codigo}" id="numeroContratoApresentar">
                            <f:facet name="header">
                                <h:outputText value="N° Contrato" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.codigo}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contrato.vigenciaDe_Ordenar}" id="vigenciaDeApresentar">
                            <f:facet name="header">
                                <h:outputText value="Data Início" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.vigenciaDe_Apresentar}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contrato.vigenciaAteAjustada_Ordenar}" id="vigenciaAteApresentar">
                            <f:facet name="header">
                                <h:outputText value="Data Término" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.vigenciaAteAjustada_Apresentar}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contrato.contratoDuracao.numeroMeses}" id="duracaoMesesApresentar">
                            <f:facet name="header">
                                <h:outputText value="Duração" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.contratoDuracao.numeroMeses}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.numeroModalidades}" style="width: 10px" id="numeroModalidades">
                            <f:facet name="header">
                                <h:outputText value="Modalidades" />
                            </f:facet>
                            <a4j:commandLink   rendered="#{!resumoPessoa.produtoDiaria}" action="#{CompetenciaSinteticoControleRel.visualizarModalidades}"
                                              oncomplete="abrirPopup('../relatorio/include_contratoModalidade.jsp', 'ContratoModalidade',850,650);"  >
                                <f:setPropertyActionListener target="#{CompetenciaSinteticoControleRel.contratoSelecionado}"
                                                             value="#{resumoPessoa.contrato.codigo}"/>
                                <h:outputText value="#{resumoPessoa.numeroModalidades}" />
                            </a4j:commandLink>
                            <h:outputText rendered="#{resumoPessoa.produtoDiaria}" value="#{resumoPessoa.numeroModalidades}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contrato.plano.descricao}" id="planoApresentar">
                            <f:facet name="header">
                                <h:outputText value="Plano" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.plano.descricao}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contrato.plano.empresa}" id="empresaPlanoApresentarDetalhe">
                            <f:facet name="header">
                                <h:outputText value="Empresa Plano/Venda" id="teste"/>
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.nomeEmpresaPlanoOuVendaApresentar}" id="nomeEmpresaPlanoDetalhe" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.situacaoContrato_Apresentar}" id="situacaoApresentar">
                            <f:facet name="header">
                                <h:outputText value="Situação Contrato" />
                            </f:facet>
                            <h:outputText rendered="#{resumoPessoa.contrato.codigo!=null && resumoPessoa.contrato.codigo != 0}"
                                          value="#{resumoPessoa.contrato.situacaoContrato_Apresentar}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.dataLancamentoMovProduto_Apresentar}" id="dataLancamentoMovProduto_Apresentar">
                            <f:facet name="header">
                                <h:outputText value="Lançamento" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.dataLancamentoMovProduto_Apresentar}"/>
                        </rich:column>

                        <rich:column sortBy="#{resumoPessoa.formaPagApresentar}" rendered="#{!resumoPessoa.formaPagApresentar == ''}" id="formaPagApresentar">
                            <f:facet name="header">
                                <h:outputText value="Forma Pagamento" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.formaPagApresentar}"/>


                        </rich:column>

                        <rich:column sortBy="#{resumoPessoa.nomeProduto}" rendered="#{!resumoPessoa.nomeProduto == '' && resumoPessoa.nomeProduto != '-'}" id="nomeProduto">
                            <f:facet name="header">
                                <h:outputText value="Produto" />
                            </f:facet>
                            <h:outputText   />



                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.quantidade}" id="quantidade">
                            <f:facet name="header">
                                <h:outputText value="Qtd" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.quantidade}"/>


                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.valor}" id="valor">
                            <f:facet name="header">
                                <h:outputText value="Valor" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </rich:column>
                        <rich:column >
                            <f:facet name="header">
                                <h:outputText value="Opção"/>
                            </f:facet>
                            <a4j:commandButton id="visualizarCliente" rendered="#{resumoPessoa.cliente.codigo!=null && resumoPessoa.cliente.codigo!=0}"
                                               action="#{FaturamentoSinteticoControleRel.irParaTelaCliente}"
                                               alt="Ir para tela de Edição do Cliente" image="../imagens/botaoVisualizar.png"
                                               oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <f:param name="state" value="AC"/>
                            </a4j:commandButton>
                            <a4j:commandButton id="visualizarColaborador"
                                               rendered="#{resumoPessoa.colaboradorVO.codigo!=null && resumoPessoa.colaboradorVO.codigo!=0}"
                                               action="#{FaturamentoSinteticoControleRel.irParaTelaColaborador}"
                                               alt="Ir para tela de Edição do Colaborador" image="../imagens/botaoVisualizar.png"
                                               oncomplete="abrirPopup('../colaboradorForm.jsp', 'Colaborador', 1024, 700);">
                                <f:param name="state" value="AC"/>
                            </a4j:commandButton>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller align="center" for="listaPessoa" maxPages="10" id="scTablePessoas"  reRender="listaPessoa"/>
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>


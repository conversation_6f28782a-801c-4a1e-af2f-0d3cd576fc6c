<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="../hoverform.js"></script>
    <link href="../css/otimize.css" rel="stylesheet" type="text/css">
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
</head>
<title>Relatório de Convidados</title>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .alinharMeio tbody tr td label {
        vertical-align: bottom;
        padding-right: 10px;
    }

</style>
<f:view>

    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Relatório de convidados"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Convidado"/>


    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%">
        <f:facet name="header">

            <jsp:include page="../topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" columnClasses="colunaTopCentralizada" width="100%">
                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita" width="100%" style="padding-left:0px;">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      rendered="#{ListaConvidadosRel.usuarioLogado.administrador}" value="EMPRESA: "/>
                        <h:panelGroup styleClass="font-size-em-max"
                                      rendered="#{ListaConvidadosRel.usuarioLogado.administrador}">
                            <div class="cb-container margenVertical">
                                <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form"
                                                 value="#{ListaConvidadosRel.empresaSelecionada.codigo}">
                                    <f:selectItems value="#{ListaConvidadosRel.listaEmpresas}"/>
                                </h:selectOneMenu>
                            </div>
                        </h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      value="#{msg_aplic.prt_GymPass_periodoPesquisa}"/>
                        <h:panelGroup styleClass="flex" layout="block">
                            <div class="margenVertical">
                                <h:panelGroup styleClass="dateTimeCustom">
                                    <rich:calendar id="dataInicio"
                                                   value="#{ListaConvidadosRel.dataInicial}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"
                                                   buttonIcon="/imagens_flat/calendar-button.svg">
                                    </rich:calendar>
                                    <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <rich:spacer width="5px"/>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              style="margin: 7px 9px 0 0px;margin-left: 15px;"
                                              value="#{msg_aplic.prt_ate_Maisculo}"/>
                                <rich:spacer width="10px"/>
                                <h:panelGroup styleClass="dateTimeCustom">
                                    <rich:calendar id="dataTermino"
                                                   value="#{ListaConvidadosRel.dataFinal}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"
                                                   buttonIcon="/imagens_flat/calendar-button.svg">
                                    </rich:calendar>
                                    <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                                 query="mask('99/99/9999')"/>
                                </h:panelGroup>
                                <rich:spacer width="12px"/>
                            </div>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                    <h:outputText styleClass="mensagem" value="#{ListaConvidadosRel.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{ListaConvidadosRel.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <rich:spacer width="20px"/>
                        <a4j:commandLink id="gerarRelatorio" ajaxSingle="false"
                                         styleClass="pure-button pure-button-primary"
                                         action="#{ListaConvidadosRel.gerarRelatorio}"
                                         accesskey="2" reRender="resultado">
                            Consultar&nbsp<i class="fa-icon-search"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>


            <h:panelGroup layout="block" styleClass="paginaFontResponsiva" id="resultado"
                          style="width: 100%; text-align: center; height: 500px; overflow-y: auto">

                <h:panelGrid width="100%" style="text-align: right">
                    <h:panelGroup layout="block">
                        <a4j:commandLink id="exportarExcel"
                                         style="margin-left: 8px;"
                                         actionListener="#{ExportadorListaControle.exportar}"
                                         rendered="#{not empty ListaConvidadosRel.listaConvidados}"
                                         oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Convidados', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{ListaConvidadosRel.listaConvidados}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="codigo=Codigo,diaApresentar=Dia,convidouApresentar=Convidou,convidadoApresentar=Convidado,situacao=Situação"/>
                            <f:attribute name="prefixo" value="ListaConvidados"/>
                            <f:attribute name="filtro" value=""/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                        <%--BOTÃO PDF--%>
                        <a4j:commandLink id="exportarPdf"
                                         style="margin-left: 8px;"
                                         actionListener="#{ExportadorListaControle.exportar}"
                                         rendered="#{not empty ListaConvidadosRel.listaConvidados}"
                                         oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Convites', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{ListaConvidadosRel.listaConvidados}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="codigo=Codigo,diaApresentar=Dia,convidouApresentar=Convidou,convidadoApresentar=Convidado,situacao=Situação"/>
                            <f:attribute name="prefixo" value="ListaConvidados"/>
                            <f:attribute name="filtro" value=""/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGroup rendered="#{empty ListaConvidadosRel.listaConvidados}">
                    <h:outputText style="margin-top: 50px; font-size: 16px; font-weight: bold; display: block;" value="Não foram encontrados convites no período."/>
                </h:panelGroup>

                <h:panelGroup rendered="#{not empty ListaConvidadosRel.listaConvidados}">
                    <table class="tblHeaderLeft semZebra" style="margin-bottom: 5px;">
                        <thead>
                        <tr>
                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="CÓDIGO"/>
                            </th>
                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="ALUNO QUE CONVIDOU"/>
                            </th>
                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="CONVIDADO"/>
                            </th>

                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="DIA DO CONVITE"/>
                            </th>

                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="SITUAÇÃO"/>
                            </th>
                        </tr>
                        </thead>

                        <tbody>
                        <a4j:repeat var="conv" value="#{ListaConvidadosRel.listaConvidados}">

                            <tr>

                                <td>
                                    <h:outputText
                                            value="#{conv.codigo}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; "/>
                                </td>
                                <td>
                                    <h:outputText
                                            value="#{conv.convidou.pessoa.nome}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; "/>
                                </td>
                                <td>
                                    <a4j:commandLink action="#{ListaConvidadosRel.irParaTelaCliente}"
                                                     style="text-decoration: none; text-align: left; font-size: 14px;"
                                                     value="#{conv.convidado.pessoa.nome}"
                                                     oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                            <f:param name="state" value="AC"/>
                                    </a4j:commandLink>
                                </td>
                                <td>
                                    <h:outputText
                                            value="#{conv.diaApresentar}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; "/>
                                </td>
                                <td>
                                    <h:outputText value="#{conv.situacao}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; "/>
                                </td>
                            </tr>
                        </a4j:repeat>
                        </tbody>
                    </table>

                    <h:outputText style="margin-top: 50px; font-weight: bold; display: block; text-align: left; margin-left: 20px;" value="Total: #{fn:length(ListaConvidadosRel.listaConvidados)} #{fn:length(ListaConvidadosRel.listaConvidados) > 1 ? 'convites' : 'convite'}"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>

</f:view>

<script>
    document.getElementById("form:dataInicio").focus();
    jQuery.noConflict();
</script>


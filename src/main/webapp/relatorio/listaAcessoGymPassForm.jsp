<%-- 
    Document   : listaAcessoGymPassForm
    Created on : 10/10/2017, 11:41:28
    Author     : arthur
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    th[id*='col_nomeheader'], th[id*='col_situacaoCliente']{
        text-align: left;
    }
</style>
<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Lista Clientes Gympass"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" styleClass="pure-form pure-u-1">
                <c:set var="titulo" scope="session" value="Lista Clientes Gympass"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Inicial:Recurso_GymPass"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="../topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                <h:panelGroup layout="block" >
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <center>
                                            <span CLASS="texto-size-14-real">Lembre-se: você encontra as informações sobre valores na <a
                                                    href="https://www.gympass.com/horarios-de-reserva/my_reservations"
                                                    target="_blank"
                                                    class="texto-size-14-real">GymPass.</a>
                                            </span>
                                        </center>
                                        <h:panelGrid width="100%" style="text-align: right">
                                            <h:panelGroup layout="block">
                                                <a4j:commandLink id="enviarSite" hreflang="www.google.com.br"
                                                                 title="Para acompanhar valores entre no site do GYMPASS Pois podem haver variações de valores"
                                                                 style="margin-left: 8px;"
                                                                 styleClass="btn-print-2" />
                                                <a4j:commandLink id="exportarExcel"
                                                                   style="margin-left: 8px;"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty ListaGymPassRelControle.listaGymPass}"
                                                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{ListaGymPassRelControle.listaGymPass}"/>
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="atributos" value="matricula_Apresentar=Matricula,nome_Apresentar=Cliente,dataRegistro_Apresentar=Data Registro,token=Token,tipoGymPass= Tipo Token"/>
                                                    <f:attribute name="prefixo" value="ListaAcessoClienteGymPass"/>
                                                    <f:attribute name="filtro" value=""/>
                                                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                </a4j:commandLink>
                                                <%--BOTÃO PDF--%>
                                                <a4j:commandLink id="exportarPdf"
                                                                   style="margin-left: 8px;"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty ListaGymPassRelControle.listaGymPass}"
                                                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{ListaGymPassRelControle.listaGymPass}"/>
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <f:attribute name="atributos" value="matricula_Apresentar=Matricula,nome_Apresentar=Cliente,dataRegistro_Apresentar=Data Registro,token=Token,tipoGymPass= Tipo Token"/>
                                                    <f:attribute name="prefixo" value="ListaAcessoClienteGymPass"/>
                                                    <f:attribute name="filtro" value=""/>
                                                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <rich:dataTable id="listaPontosAlunos" width="100%" styleClass="tabelaSimplesCustom"
                                                        value="#{ListaGymPassRelControle.listaGymPass}" var="itemLista">
                                            <rich:column  id="col_matriculacli_cmc" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{itemLista.clienteVO.matricula}" filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.MATRICULA}" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemLista.clienteVO.matricula}" />
                                            </rich:column>

                                            <rich:column id="col_nome_cmc" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{itemLista.clienteVO.pessoa.nome}" filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.CLIENTE}" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{itemLista.nome_Apresentar}" />
                                            </rich:column>

                                            <rich:column id="col_dataRegistro" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{itemLista.dataRegistro}" filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Data Registro" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemLista.dataRegistro_Apresentar}" />
                                            </rich:column>

                                            <rich:column id="col_token" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{itemLista.token}" filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Token" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemLista.token}" />
                                            </rich:column>
                                            
                                            <rich:column id="col_tipoToken" styleClass="col-text-align-center" headerClass="col-text-align-left" sortBy="#{itemLista.tipoGymPass}" filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Tipo Token" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemLista.tipoGymPass}" />
                                            </rich:column>

                                            <rich:column id="col_valorGympass" styleClass="col-text-align-right" headerClass="col-text-align-right" rendered="#{ListaGymPassRelControle.valorTotal>0}" sortBy="#{itemLista.valorEmAberto}" filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Valor Gympass" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemLista.valorEmAberto_Apresentar}" style="margin-right: 20px;"/>
                                            </rich:column>

                                            <rich:column>
                                                <a4j:commandLink id="visualizarCliente" action="#{ListaGymPassRelControle.irParaTelaCliente}" title="Ir para tela Cliente"
                                                                 oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                                                                 styleClass="linkPadrao texto-cor-azul texto-size-14-real tooltipster">
                                                    <i class="fa-icon-search"></i>
                                                </a4j:commandLink>
                                            </rich:column>
                                        </rich:dataTable>

                                        <div style="padding-top: 10px; padding-right: 57px; padding-bottom: 10px; text-align: right; background-color: #EEE" >
                                            <c:if test="${ListaGymPassRelControle.valorTotal > 0}">
                                                <h:outputText styleClass="texto-size-18 texto-cor-cinza" value="TOTAL GYMPASS "/>
                                                <h:outputText styleClass="texto-size-18 texto-bold" value="#{ListaGymPassRelControle.valorTotal_Apresentar}"/>
                                            </c:if>
                                        </div>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:form>
        </body>
    </html>
</h:panelGrid>

</f:view>

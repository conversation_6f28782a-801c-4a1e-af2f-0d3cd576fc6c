<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" src="../script/script.js"></script>
    <script type="text/javascript" src="../hoverform.js"></script>
</head>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="SGP - Modalidades sem turma"/>
    </title>

    <c:set var="titulo" scope="session" value="SGP - Modalidades sem turma"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}relatorio-sgp-modalidades-sem-turma/"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">
                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{SGPModalidadeSemTurmaControle.selecionarEmpresa}"
                                  value="#{msg_aplic.prt_FrequenciaPorTurma_empresa}"/>
                    <h:panelGroup rendered="#{SGPModalidadeSemTurmaControle.selecionarEmpresa}">
                        <rich:spacer width="10"/>
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{SGPModalidadeSemTurmaControle.empresa}">
                            <f:selectItems value="#{SGPModalidadeSemTurmaControle.listaEmpresas}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FechamentoAcessos_PeriodoAcesso}"/>
                    <h:panelGroup>
                        <rich:calendar id="dataInicio" inputSize="10" inputClass="form"
                                       value="#{SGPModalidadeSemTurmaControle.dataInicio}"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2"
                                       showWeeksBar="false"/>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_ClientePorAniversario_ate}:"/>

                        <rich:calendar id="dataFim" inputSize="10" inputClass="form"
                                       value="#{SGPModalidadeSemTurmaControle.dataFim}"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2"
                                       showWeeksBar="false"/>
                        <rich:jQuery selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')"/>
                    </h:panelGroup>

                    <h:outputText value="#{msg_aplic.prt_FrequenciaPorTurma_turmas}:"/>
                    <h:panelGroup id="pnlModalidades" layout="block">
                        <a4j:commandLink value="Selecionar todas as modalidades"
                                         action="#{SGPModalidadeSemTurmaControle.selecionarTodasModalidades}"
                                         reRender="pnlModalidades"/>

                        <rich:dataGrid value="#{SGPModalidadeSemTurmaControle.listaModalidades}"
                                       style="border: white 0 0 0 0;"
                                       var="modalidade" width="100%" columns="3" rowClasses="linhaImpar, linhaPar">
                            <h:selectBooleanCheckbox value="#{modalidade.selecionado}"/>
                            <h:outputText value="#{modalidade.nome}"/>
                        </rich:dataGrid>

                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem" value="#{SGPModalidadeSemTurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" style="font-size: 14px"
                                      value="#{SGPModalidadeSemTurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes"
                                 columnClasses="colunaCentralizada" style="margin-bottom: 10px">
                        <h:panelGroup>
                            <a4j:commandLink id="consultar" reRender="mensagem"
                                             styleClass="botoes nvoBt"
                                             action="#{SGPModalidadeSemTurmaControle.consultar}"
                                             accesskey="2">
                                <i class="fa-icon-print"></i> Gerar Relatório
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>


</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_TotalizadorFrequencia_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_TotalizadorFrequencia_tituloForm} - Total: ${TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.totalizador} acessos"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="form">
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" >
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" >

                                        <h:panelGrid width="100%" columns="2">
                                            <h:panelGrid columnClasses="colunaCentralizada" width="100%" style="border:0;" id="panelFiltrosUtilizados">
                                                <h:panelGrid style="font-size:12px; font-style: italic; font-family: Arial;  text-align: left;" >
                                                    ${TotalizadorFrequenciaControleRel.filtros}
                                                </h:panelGrid>
                                            </h:panelGrid>
                                            <h:panelGroup style="text-align: right" layout="block">
                                                <%--BOTÃO EXCEL--%>
                                                <a4j:commandLink id="exportarExcel" style="margin-left:5px;text-align: right"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty TotalizadorFrequenciaControleRel.listaDeTotalizadores}"
                                                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{TotalizadorFrequenciaControleRel.listaDeTotalizadores}"/>
                                                    <f:attribute name="filtro" value="#{TotalizadorFrequenciaControleRel.filtrosSemHtml}"/>
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="atributos" value="#{TotalizadorFrequenciaControleRel.atributos}"/>
                                                    <f:attribute name="prefixo" value="TotalizadorAcessos"/>
                                                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                </a4j:commandLink>
                                                <%--BOTÃO PDF--%>
                                                <a4j:commandLink id="exportarPdf"
                                                                   style="margin-left: 8px;"
                                                                   actionListener="#{TotalizadorFrequenciaControleRel.exportar}"
                                                                   rendered="#{not empty TotalizadorFrequenciaControleRel.listaDeTotalizadores}"
                                                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{TotalizadorFrequenciaControleRel.listaDeTotalizadores}"/>
                                                    <f:attribute name="filtro" value="#{TotalizadorFrequenciaControleRel.filtrosSemHtml}"/>
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <f:attribute name="atributos" value="#{TotalizadorFrequenciaControleRel.atributos}"/>
                                                    <f:attribute name="prefixo" value="Totalizador de Acessos"/>
                                                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>

                                        </h:panelGrid>
                                        <h:panelGrid columns="2" rendered="#{TotalizadorFrequenciaControleRel.grafico==1}"  columnClasses="classEsquerda, classDireita" width="100%">
                                            <rich:dataTable id="listaFrequencia" width="100%" styleClass="tabelaSimplesCustom noHover"
                                                            value="#{TotalizadorFrequenciaControleRel.listaDeTotalizadores}" rows="16" var="frequencia">

                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.agruparPorPessoa}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Matricula" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{frequencia.matricula}"/>
                                                </rich:column>

                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.agruparPorPessoa}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{frequencia.nome}"/>
                                                </rich:column>

                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.agruparPorPessoa}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Plano" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{frequencia.plano}"/>
                                                </rich:column>

                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.agruparPorPessoa}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Modalidade" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{frequencia.modalidade}"/>
                                                </rich:column>

                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==1 && !TotalizadorFrequenciaControleRel.agruparPorPessoa}"
                                                             styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Data" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{frequencia.dataInicio}" >
                                                        <f:convertDateTime pattern="dd/MM/yyyy" />
                                                    </h:outputText>
                                                </rich:column>

                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==3}" styleClass="col-text-align-left" headerClass="col-text-align-left"  width="30%">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Hora" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{frequencia.hora}" >
                                                        <f:convertNumber pattern="##"/>
                                                    </h:outputText>
                                                </rich:column>
                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==2}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Dia da Semana" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{frequencia.diaDaSemana}" />
                                                </rich:column>
                                                <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Quantidade" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{frequencia.quantidade}" />
                                                </rich:column>

                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==2 and TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.agrupamento == 'SEXTA_SABADO'}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Porcentagem do maior dia (%)" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{frequencia.porcentagemRelacionada}" />
                                                </rich:column>

                                                <rich:column rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==3 ||TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==1 }"
                                                             styleClass="col-text-align-left" headerClass="col-text-align-left" width="30%">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Porcentagem (%)" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{frequencia.porcentagem}" >
                                                        <f:convertNumber pattern="#,###0.00"/>
                                                    </h:outputText>
                                                </rich:column>

                                                <rich:column rendered="#{(TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==3 || TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==1) &&
                                                                        !TotalizadorFrequenciaControleRel.agruparPorPessoa}"
                                                             styleClass="col-text-align-left" headerClass="col-text-align-left" width="30%">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Porcentagem Ativos(%)" />
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{frequencia.porcentagemAtivos}" >
                                                        <f:convertNumber pattern="#,###0.00"/>
                                                    </h:outputText>
                                                </rich:column>
                                            </rich:dataTable>

                                        </h:panelGrid>
                                        <rich:datascroller rendered="#{TotalizadorFrequenciaControleRel.grafico==1}" styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:listaFrequencia" maxPages="10" id="scFrequencia" />
                                        <h:panelGrid columns="1" rendered="#{TotalizadorFrequenciaControleRel.grafico==2}" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" width="100%">
                                            <%--
                                                                <jsfChart:chart id="chart1" rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==1}"  datasource="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.dataSetBarra}"
                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                title="Quantidade Por Data"
                                                                                xlabel="Data"
                                                                                ylabel="Quantidade"
                                                                                height="300" width="470" legend="true">
                                                                </jsfChart:chart>
                                                                <jsfChart:chart id="chart2" rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==2}" datasource="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.dataSetBarra}"
                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                title="Quantidade Por Dia"
                                                                                xlabel="Dia"
                                                                                ylabel="Quantidade"
                                                                                height="300" width="470" legend="true">
                                                                </jsfChart:chart>
                                                                <jsfChart:chart id="chart3" rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia==3}" datasource="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.dataSetBarra}"
                                                                                type="bar" is3d="true" background="#B4CDCD" foreground="#FFFAFA" depth="35" styleClass="semBorda"
                                                                                colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD"
                                                                                lineStokeWidth="2" alpha="65" antialias="true"
                                                                                title="Quantidade Por Hora"
                                                                                xlabel="Hora"
                                                                                ylabel="Quantidade"
                                                                                height="300" width="470" legend="true">
                                                                </jsfChart:chart>
                                            --%>
                                            <rich:panel>
                                                <a4j:mediaOutput  element="img"
                                                                  cacheable="false" session="true" createContent="#{TotalizadorFrequenciaControleRel.montarGrafico}"
                                                                  mimeType="image/gif" />
                                            </rich:panel>

                                        </h:panelGrid>
                                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" styleClass="tabMensagens" >
                                            <h:panelGroup >
                                                <h:outputText styleClass="tituloCamposDestaqueNegrito " value="Total:" id="totalAcessosPDF"/>
                                                <rich:spacer width="5px"/>
                                                <h:outputText styleClass="tituloCamposDestaque " value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.totalizador}" id="qtdeTotalAcesso"/>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

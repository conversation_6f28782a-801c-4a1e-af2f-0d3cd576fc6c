<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    .classeEsquerda {
        width: 25%;
        text-align: right;
        font-weight: bold;
        vertical-align: middle;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        text-transform: none;
        color: #333;
        line-height: 150%;
    }

    .classeDireita {
        width: 25%;
        text-align: left;
    }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_CompetenciaSintetico_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_CompetenciaSintetico_tituloForm}"/>
    <c:set var="urlWiki" scope="session"  value="${SuperControle.urlBaseConhecimento}como-gerar-um-relatorio-competencia-mensal/"/>
    <c:set var="iconeWikiEquivalentes" scope="request" value="true"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid columns="4" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classeEsquerda, classeDireita,classeEsquerda, classeDireita" width="100%">

                    <h:outputText styleClass="tituloCampos" rendered="#{CompetenciaSinteticoControleRel.temEmpresa}"
                                  value="#{msg_aplic.prt_CompetenciaSintetico_empresa}"/>
                    <h:panelGroup rendered="#{CompetenciaSinteticoControleRel.temEmpresa}">
                        <rich:spacer width="10"/>
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{CompetenciaSinteticoControleRel.competenciaSinteticoRel.empresa.codigo}">
                            <f:selectItems value="#{CompetenciaSinteticoControleRel.listaDeEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Período de Pesquisa: "/>
                    <h:panelGroup>
                        <h:panelGroup id="mesBase1">
                            <rich:calendar id="dataInicio"
                                           value="#{CompetenciaSinteticoControleRel.competenciaSinteticoRel.dataInicio}"
                                           inputSize="8"
                                           inputClass="form MMyyyy"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:panelGroup id="mesBase2">
                            <rich:spacer width="10px"/>
                            <h:outputText styleClass="tituloCampos" value=" até "/>
                            <rich:spacer width="10px"/>
                            <rich:calendar id="dataTermino"
                                           value="#{CompetenciaSinteticoControleRel.competenciaSinteticoRel.dataTermino}"
                                           inputSize="8"
                                           inputClass="form MMyyyy"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{!CompetenciaSinteticoControleRel.temEmpresa}"/>
                    <h:panelGroup rendered="#{!CompetenciaSinteticoControleRel.temEmpresa}"/>

                    <h:outputText styleClass="tituloCampos" value="Matrícula, Rematrícula, Renovação"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos"
                                             value="#{CompetenciaSinteticoControleRel.matriculaRenovacaoRematricula}"/>

                    <h:outputText styleClass="tituloCampos" value="Produto Estoque"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.produtoEstoque}"/>

                    <h:outputText styleClass="tituloCampos" value="Manutenção Modalidade"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos"
                                             value="#{CompetenciaSinteticoControleRel.manutencaoModalidade}"/>

                    <h:outputText styleClass="tituloCampos" value="Serviço"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.servico}"/>

                    <h:outputText styleClass="tituloCampos" value="Trancamento"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.trancamento}"/>

                    <h:outputText styleClass="tituloCampos" value="Retorno Trancamento"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos"
                                             value="#{CompetenciaSinteticoControleRel.retornoTrancamento}"/>

                    <h:outputText styleClass="tituloCampos" value="Aula Avulsa"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.aulaAvulsa}"/>

                    <h:outputText styleClass="tituloCampos" value="Diária"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.diaria}"/>

                    <h:outputText styleClass="tituloCampos" value="FreePass"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.freePass}"/>

                    <h:outputText styleClass="tituloCampos" value="Alterar - Horário"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.alterarHorario}"/>

                    <h:outputText styleClass="tituloCampos" value="Sessões"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.sessao}"/>
                    <h:outputText styleClass="tituloCampos" value="Pgto Saldo Devedor"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos"
                                             value="#{CompetenciaSinteticoControleRel.pgtoSaldoDevedor}"/>

                    <h:outputText styleClass="tituloCampos" value="Quitação - Cancelamento"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos"
                                             value="#{CompetenciaSinteticoControleRel.quitacaoCancelamento}"/>

                    <h:outputText styleClass="tituloCampos" value="Acerto C/C Aluno"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos"
                                             value="#{CompetenciaSinteticoControleRel.acertoCCAluno}"/>

                    <h:outputText styleClass="tituloCampos" value="Mês de Referência Plano"/>
                    <h:selectBooleanCheckbox id="mesReferencia" styleClass="tituloCampos"
                                             value="#{CompetenciaSinteticoControleRel.mesReferenciaPlano}">
                        <a4j:support reRender="textoFiltro, comboFiltro" event="onclick"/>
                    </h:selectBooleanCheckbox>

                    <h:outputText styleClass="tituloCampos" value="Taxa de Personal"/>
                    <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{CompetenciaSinteticoControleRel.taxaPersonal}"/>

                    <h:panelGroup id="textoFiltro">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CompetenciaSintetico_agrupamento}"
                                      rendered="#{CompetenciaSinteticoControleRel.mesReferenciaPlano}"/>
                    </h:panelGroup>

                    <h:panelGroup id="comboFiltro">
                        <h:selectOneMenu id="agrupar" value="#{CompetenciaSinteticoControleRel.agrupamento}"
                                         rendered="#{CompetenciaSinteticoControleRel.mesReferenciaPlano}">
                            <f:selectItems value="#{CompetenciaSinteticoControleRel.tipoAgrupamento}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{!CompetenciaSinteticoControleRel.temEmpresa}"/>
                    <h:panelGroup rendered="#{!CompetenciaSinteticoControleRel.temEmpresa}"/>


                </h:panelGrid>





                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem" value="#{CompetenciaSinteticoControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{CompetenciaSinteticoControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <rich:spacer width="20px"/>
                            <a4j:commandLink id="imprimirPDF"
                                               action="#{CompetenciaSinteticoControleRel.imprimir}"
                                               accesskey="2" styleClass="botoes nvoBt">
                                <i class="fa-icon-print"></i>&nbspGerar Relatório (PDF)
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <rich:jQuery id="mskData" selector=".rich-calendar-input:not(.MMyyyy)" timing="onload" query="mask('99/99/9999')" />
            <rich:jQuery selector=".rich-calendar-input.MMyyyy" timing="onload" query="mask('99/9999')" />
        </h:form>
    </h:panelGrid>
</f:view>
<script type="text/javascript">
    document.getElementById("form:dataInicio").focus();
</script>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ClientePorDuracao_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ClientePorDuracao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Contratos_por_Duracao"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>
        <h:form id="form">
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  styleClass="tituloCampos" rendered="#{ClientePorDuracaoRelControle.permissaoConsultaTodasEmpresas}"
                                   value="#{msg_aplic.prt_ClientePorDuracao_empresa}:" />
                    <h:panelGroup layout="block" rendered="#{ClientePorDuracaoRelControle.permissaoConsultaTodasEmpresas}">
                        <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClientePorDuracaoRelControle.filtroEmpresa}" >
                            <f:selectItems value="#{ClientePorDuracaoRelControle.listaEmpresas}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorDuracao_periodoPesquisa}:" />
                    <h:panelGroup>
                        <rich:calendar id="dataPeriodo"
                                       value="#{ClientePorDuracaoRelControle.clientePorDuracaoRel.dataPeriodo}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />                        
                        <h:message for="dataPeriodo"  styleClass="mensagemDetalhada"/>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="Situação:" />
                    <h:outputText  styleClass="tituloCampos" value="" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorDuracao_normal}" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{ClientePorDuracaoRelControle.clientePorDuracaoRel.situacaoNormal}" id="checkBoxNormalRelContDuracao"/>
                        <rich:spacer width="60px"/>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorDuracao_avencer}" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{ClientePorDuracaoRelControle.clientePorDuracaoRel.situacaoAvencer}" id="checkBoxAVencerRelContDuracao"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorDuracao_atestado}" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{ClientePorDuracaoRelControle.clientePorDuracaoRel.situacaoAtestado}" id="checkBoxAtestadoRelContDuracao"/>
                        <rich:spacer width="60px"/>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorDuracao_carencia}" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{ClientePorDuracaoRelControle.clientePorDuracaoRel.situacaoCarencia}" id="checkBoxCarenciaRelContDuracao"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorDuracao_vencido}" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{ClientePorDuracaoRelControle.clientePorDuracaoRel.situacaoVencido}" id="checkBoxVencidoRelContDuracao"/>
                        <rich:spacer width="57px"/>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorDuracao_trancado}" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{ClientePorDuracaoRelControle.clientePorDuracaoRel.situacaoTrancado}" id="checkBoxTrancadoRelContDuracao"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="Plano:" />
                    <h:outputText  styleClass="tituloCampos" value="" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorDuracao_semBolsa}" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{ClientePorDuracaoRelControle.clientePorDuracaoRel.semBolsa}" id="checkBoxSemBolsaRelContDuracao"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{ClientePorDuracaoRelControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ClientePorDuracaoRelControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandLink id="imprimirPDF" reRender="mensagem"
                                             styleClass="botoes nvoBt"
                                             action="#{ClientePorDuracaoRelControle.imprimir}"
                                             accesskey="2">
                                <i class="fa-icon-print"></i> Gerar Relatório
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:dataPeriodo").focus();
</script>


<%--oncomplete="#{ClientePorDuracaoRelControle.abrirPopUp}"--%>
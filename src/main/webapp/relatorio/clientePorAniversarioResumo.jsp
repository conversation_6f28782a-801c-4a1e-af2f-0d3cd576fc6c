<%--
    Document   : faturamentoSinteticoResumoPessoa
    Created on : 26/10/2009, 18:48:13
    Author     : pedro
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<f:view>

    <title>
        <h:outputText value="#{msg_aplic.prt_ClientePorAniversario_tituloForm}"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value=" ${msg_aplic.prt_ClientePorAniversario_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-gerar-uma-lista-de-aniversariantes/"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>
        <h:form id="form" >
            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:commandLink action="#{AniversarioControle.imprimirRelatorio}" id="imprimirRelatorio" style="display: none" />
            <h:inputHidden id="relatorio" value="#{AniversarioControle.relatorio}" />

            <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>

            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" >
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" >
                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="border:0;" id="panelFiltrosUtilizados">
                                            <h:outputText styleClass="tituloCamposAzul" value="#{msg_aplic.prt_ClientePorAniversario_vejaFiltrosUtilizados}"/>
                                            <rich:toolTip
                                                    onclick="true"
                                                    followMouse="true"
                                                    direction="top-right"
                                                    style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                    showDelay="500">
                                                ${AniversarioControle.filtros}
                                            </rich:toolTip>
                                        </h:panelGrid>
                                        <h:panelGrid width="100%">
                                            <h:panelGroup style="text-align: right" layout="block">
                                                <a4j:commandLink id="exportarExcel" style="margin-left:5px;text-align: right"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty AniversarioControle.listaClientePorAniversario}"
                                                                   oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{AniversarioControle.listaClientePorAniversario}"/>
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="atributos" value="#{AniversarioControle.relatorioExcelAtributos}"/>
                                                    <f:attribute name="prefixo" value="#{AniversarioControle.nomeRelatorio}"/>
                                                    <f:attribute name="itemExportacao" value="aniversariantesRel"/>
                                                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                </a4j:commandLink>

                                                <c:if test="${modulo eq 'zillyonWeb'}">
                                                    <a4j:commandLink id="exportarPDF" style="margin-left:5px;text-align: right"
                                                                     actionListener="#{ExportadorListaControle.exportar}"
                                                                     rendered="#{not empty AniversarioControle.listaClientePorAniversario}"
                                                                     oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                     accesskey="2" styleClass="linkPadrao">
                                                        <f:attribute name="lista" value="#{AniversarioControle.listaClientePorAniversario}"/>
                                                        <f:attribute name="tipo" value="pdf"/>
                                                        <f:attribute name="atributos" value="#{AniversarioControle.relatorioExcelAtributos}"/>
                                                        <f:attribute name="itemExportacao" value="aniversariantesRel"/>
                                                        <f:attribute name="prefixo" value="#{AniversarioControle.nomeRelatorio}"/>
                                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                    </a4j:commandLink>
                                                </c:if>

                                                <c:if test="${modulo eq 'centralEventos'}">
                                                    <a4j:commandLink id="imprimirPDF" action="#{AniversarioControle.imprimirRelatorio}" style="margin-left:5px"
                                                                       oncomplete="imprimirRelatorio(this.form);" accesskey="2" styleClass="linkPadrao"
                                                                     rendered="#{not empty AniversarioControle.listaClientePorAniversario}"
                                                                       reRender="form">
                                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                    </a4j:commandLink>

                                                </c:if>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <rich:dataTable width="100%" styleClass="tabelaSimplesCustom"
                                                        value="#{AniversarioControle.listaClientePorAniversario}" rows="50" var="pessoa" id="aniversarios" rowKeyVar="status">
                                            <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                            <rich:column rendered="#{AniversarioControle.pessoaAluno or AniversarioControle.pessoaAmbos}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="#{msg_aplic.prt_ClientePorAniversario_matricula}" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{pessoa.matricula}" />
                                            </rich:column>
                                            <rich:column rendered="#{AniversarioControle.pessoaAmbos}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Tipo" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{pessoa.tipoPessoa}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_ClientePorAniversario_nome}" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{pessoa.nome}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_ClientePorAniversario_dataAniversario}" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{pessoa.dataNasc}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_ClientePorAniversario_situacaoRe}" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{pessoa.situacao}" />
                                            </rich:column>
                                            <rich:column rendered="#{AniversarioControle.pessoaAluno or AniversarioControle.pessoaAmbos}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_ClientePorAniversario_planoRe}" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{pessoa.plano}" />
                                            </rich:column>
                                            <rich:column rendered="#{AniversarioControle.permissaoConsultaTodasEmpresas}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_ClientePorAniversario_empresa}" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{pessoa.nomeEmpresa}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-center">
                                                <a4j:commandLink rendered="#{pessoa.tipoPessoaAluno}"
                                                                 styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                                                 action="#{AniversarioControle.irParaTelaCliente}"
                                                                 oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                    <f:param name="state" value="AC"/>
                                                    <i class="fa-icon-search"></i>
                                                </a4j:commandLink>
                                                <a4j:commandLink rendered="#{!pessoa.tipoPessoaAluno}"
                                                                   styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                                                   action="#{AniversarioControle.irParaTelaColaborador}"
                                                                   oncomplete="abrirPopup('../colaboradorForm.jsp', 'Colaborador', 1024, 700);">
                                                    <f:param name="state" value="AC"/>
                                                    <i class="fa-icon-search"></i>
                                                </a4j:commandLink>
                                            </rich:column>
                                        </rich:dataTable>
                                        <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:aniversarios" maxPages="10" id="scAniversarios" />
                                    </h:panelGrid>
                                    <h:panelGrid columns="2" width="95%" columnClasses="colunaEsquerda" styleClass="tabMensagens">
                                        <h:panelGrid columns="2" width="90%" styleClass="tabBotoes" columnClasses="colunaDireita">
                                            <c:if test="${modulo eq 'zillyonWeb'}">
                                                <h:commandLink id="voltar" action="#{AniversarioControle.voltar}"
                                                               styleClass="pure-button pure-button-small pure-button-primary">
                                                    <i class="fa-icon-backward"></i> &nbsp Voltar
                                                </h:commandLink>
                                            </c:if>

                                            <c:if test="${modulo eq 'centralEventos'}">
                                                <h:commandButton id="voltarCE" alt="Voltar Passo" action="#{AniversarioControle.voltar}"
                                                                 image="../imagens/botoesCE/voltar_sem_fundo.png"/>

                                            </c:if>
                                        </h:panelGrid>
                                        <h:panelGroup>
                                            <h:outputText styleClass="tituloCamposDestaqueNegrito " value="Total: #{AniversarioControle.totalizador}"/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>
    <rich:modalPanel id="panelStatus8" autosized="true">
        <h:panelGrid columns="3">
            <h:graphicImage url="../imagens/carregando.gif" style="border:none"/>
            <h:outputText styleClass="titulo3" value="Carregando..."/>
        </h:panelGrid>
    </rich:modalPanel>
    <a4j:status onstart="Richfaces.showModalPanel('panelStatus8');"
                onstop="#{rich:component('panelStatus8')}.hide();">
    </a4j:status>
</f:view>


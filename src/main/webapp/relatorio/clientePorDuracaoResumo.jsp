<%--
    Document   : faturamentoSinteticoResumoPessoa
    Created on : 26/10/2009, 18:48:13
    Author     : pedro
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ClientePorDuracao_tituloForm}"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value=" ${msg_aplic.prt_ClientePorDuracao_tituloForm}  Total: ${fn:length(ClientePorDuracaoRelControle.listaClientePorDuracao)}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Contratos_por_Duracao"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>
        <html>

            <h:form id="form" >
                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" >
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" >
                                        <h:panelGrid style="width:100%">
                                            <h:panelGroup id="botoes" style="text-align:right;" layout="block">
                                                <a4j:commandLink id="exportarExcel" style="margin-left:5px;text-align: right"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty ClientePorDuracaoRelControle.listaClientePorDuracao}"
                                                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{ClientePorDuracaoRelControle.listaClientePorDuracao}"/>
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="atributos" value="cliente_Apresentar=Matrícula,pessoa_Apresentar=Nome,codigo_Apresentar=N° Contrato,vigenciaDe_Apresentar=Data Início,vigenciaAte_Apresentar=Data Término,numeroMeses=Duração,modalidade_Apresentar=Modalidades,descricao=Plano,situacao_Apresentar=Situação"/>
                                                    <f:attribute name="prefixo" value="ResumoContratosPorDuracao"/>
                                                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                </a4j:commandLink>
                                                <%--BOTÃO PDF--%>
                                                <a4j:commandLink id="exportarPdf"
                                                                   style="margin-left: 8px;"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty ClientePorDuracaoRelControle.listaClientePorDuracao}"
                                                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{ClientePorDuracaoRelControle.listaClientePorDuracao}"/>
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <f:attribute name="atributos" value="cliente_Apresentar=Matrícula,pessoa_Apresentar=Nome,codigo_Apresentar=N° Contrato,vigenciaDe_Apresentar=Data Início,vigenciaAte_Apresentar=Data Término,numeroMeses=Duração,modalidade_Apresentar=Modalidades,descricao=Plano,situacao_Apresentar=Situação"/>
                                                    <f:attribute name="prefixo" value="ResumoContratosPorDuracao"/>
                                                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <rich:dataTable width="100%" value="#{ClientePorDuracaoRelControle.listaClientePorDuracao}"
                                                        styleClass="tabelaSimplesCustom" var="lista" id="item" rowKeyVar="status">
                                            <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                            <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left" filterEvent="onkeyup" sortBy="#{lista.clienteVO.matricula}">
                                                <f:facet name="header" >
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Matrícula" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.cliente_Apresentar}" />
                                            </rich:column>
                                            <rich:column filterEvent="onkeyup" sortBy="#{lista.pessoaVO.nome}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.pessoa_Apresentar}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" filterEvent="onkeyup" sortBy="#{lista.contratoVO.codigo}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="N° Contrato" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.codigo_Apresentar}" />
                                            </rich:column>
                                            <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left" filterEvent="onkeyup" sortBy="#{lista.contratoVO.vigenciaDe_Apresentar}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Data Início" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.vigenciaDe_Apresentar}" />
                                            </rich:column>
                                            <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left" filterEvent="onkeyup" sortBy="#{lista.contratoVO.vigenciaAteAjustada_Apresentar}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Data Término" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.vigenciaAte_Apresentar}" />
                                            </rich:column>
                                            <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left" filterEvent="onkeyup" sortBy="#{lista.numeroMeses}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Duração" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.numeroMeses}" />
                                            </rich:column>
                                            <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left" filterEvent="onkeyup" sortBy="#{lista.modalidade_Apresentar}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Modalidades" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.modalidade_Apresentar}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" filterEvent="onkeyup" sortBy="#{lista.descricao}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Plano" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.descricao}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" rendered="#{ClientePorDuracaoRelControle.apresentarSituacao}" filterEvent="onkeyup" sortBy="#{lista.situacao_Apresentar}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Situação" />
                                                </f:facet>
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.situacao_Apresentar}" />
                                            </rich:column>
                                            <rich:column>
                                                <a4j:commandLink id="visualizarCliente" action="#{ClientePorDuracaoRelControle.irParaTelaCliente}" title="Ir para tela de Edição do Cliente"
                                                                 oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                                                        styleClass="linkPadrao texto-cor-azul texto-size-14-real">
                                                    <f:param name="state" value="AC"/>
                                                    <i class="fa-icon-search"></i>
                                                </a4j:commandLink>
                                            </rich:column>
                                        </rich:dataTable>
                                        <rich:datascroller align="center" for="form:item" maxPages="10" id="scitems" />
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:form>
        </body>
    </html>
</h:panelGrid>

</f:view>


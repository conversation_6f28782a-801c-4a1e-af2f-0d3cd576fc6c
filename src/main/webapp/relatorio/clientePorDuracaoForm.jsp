<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ClientePorDuracao_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_ClientePorDuracao_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Contratos_por_Duracao"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="form">
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" >
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" style="border:0;text-align: center;margin-top: 15px;" id="panelFiltrosUtilizadosPlano">

                                        <h:outputText styleClass="tituloCamposAzul"  value="Veja os Filtros Utilizados "/>
                                        <rich:toolTip
                                                onclick="true"
                                                followMouse="true"
                                                direction="top-right"
                                                style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                showDelay="500">
                                            ${ClientePorDuracaoRelControle.filtros}
                                        </rich:toolTip>
                                    </h:panelGrid>
                                    <h:panelGrid style="width:100%">
                                        <h:panelGroup id="botoes" style="text-align:right;" layout="block">
                                            <a4j:commandLink id="exportarExcel"
                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                               rendered="#{not empty ClientePorDuracaoRelControle.listaApresentar}"
                                                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                               accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista" value="#{ClientePorDuracaoRelControle.listaApresentar}"/>
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="atributos" value="#{ClientePorDuracaoRelControle.atributosImpressao}"/>
                                                <f:attribute name="prefixo" value="ContratosPorDuracao"/>
                                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                            </a4j:commandLink>
                                            <%--BOTÃO PDF--%>
                                            <a4j:commandLink id="exportarPdf"
                                                               style="margin-left: 8px;"
                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                               rendered="#{not empty ClientePorDuracaoRelControle.listaApresentar}"
                                                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                               accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista" value="#{ClientePorDuracaoRelControle.listaApresentar}"/>
                                                <f:attribute name="tipo" value="pdf"/>
                                                <f:attribute name="atributos" value="#{ClientePorDuracaoRelControle.atributosImpressao}"/>
                                                <f:attribute name="prefixo" value="ContratosPorDuracao"/>
                                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 PDF"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                        <rich:dataTable  width="100%"
                                                         styleClass="tabelaSimplesCustom"
                                                         value="#{ClientePorDuracaoRelControle.listaApresentar}" var="lista">

                                            <rich:column  filterEvent="onkeyup" styleClass="col-text-align-right" headerClass="col-text-align-right"
                                                          sortBy="#{lista.numeroMeses}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="DURAÇÃO" />
                                                </f:facet>
                                                <rich:spacer width="5px"/>
                                                <h:outputText  value="#{lista.numeroMeses}" />
                                                <rich:spacer width="5px"/>
                                                <h:outputText value="meses" />
                                            </rich:column>

                                            <rich:column  filterEvent="onkeyup" styleClass="col-text-align-right" headerClass="col-text-align-right"
                                                          rendered="#{ClientePorDuracaoRelControle.apresentarSituacao}"
                                                          sortBy="#{lista.situacao_Apresentar}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="SITUAÇÃO" />
                                                </f:facet>
                                                <h:outputText value="#{lista.situacao_Apresentar}"/>
                                            </rich:column>

                                            <rich:column  filterEvent="onkeyup" styleClass="col-text-align-right" headerClass="col-text-align-right"
                                                          sortBy="#{lista.quantidade}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="QUANTIDADE" />
                                                </f:facet>
                                                <h:panelGroup>
                                                    <rich:spacer width="10px"/>
                                                    <a4j:commandLink  action="#{ClientePorDuracaoRelControle.montarDadosPessoasSituacao}"
                                                                      rendered="#{ClientePorDuracaoRelControle.apresentarSituacao}"
                                                                      oncomplete="abrirPopup('../relatorio/clientePorDuracaoResumo.jsp', 'ClientePorDuracaoResumo', 780, 595);">
                                                        <h:outputText value="#{lista.quantidade}" title="Cliente(s)"/>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink styleClass="texto-font texto-size-14-real texto-cor-azul linkPadrao"
                                                                     action="#{ClientePorDuracaoRelControle.montarDadosPessoas}"
                                                                     rendered="#{!ClientePorDuracaoRelControle.apresentarSituacao}"
                                                                     oncomplete="abrirPopup('../relatorio/clientePorDuracaoResumo.jsp', 'ClientePorDuracaoResumo', 780, 595);">
                                                        <h:outputText value="#{lista.quantidade}" />
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </rich:column>

                                            <rich:column  filterEvent="onkeyup" styleClass="col-text-align-right" headerClass="col-text-align-right"
                                                          sortBy="#{lista.percentual}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="PERCENTUAL"/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText value="#{lista.percentual}%" />
                                                </h:panelGroup>
                                            </rich:column>

                                            <rich:column  filterEvent="onkeyup" styleClass="col-text-align-right" headerClass="col-text-align-right"
                                                          rendered="#{ClientePorDuracaoRelControle.permissaoConsultaTodasEmpresas}"
                                                          sortBy="#{lista.empresaVO.nome}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="EMPRESA"/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <h:outputText value="#{lista.empresaVO.nome}" />
                                                </h:panelGroup>
                                            </rich:column>
                                        </rich:dataTable>
                                    <rich:spacer width="15px"/>
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" styleClass="tabMensagens">
                                        <h:panelGrid columns="1" width="80%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                                            <h:panelGroup>
                                                <h:commandLink id="voltar" action="#{ClientePorDuracaoRelControle.voltar}"
                                                               styleClass="pure-button pure-button-small pure-button-primary">
                                                    <i class="fa-icon-backward"></i> &nbsp  Voltar
                                                </h:commandLink>

                                                <rich:spacer width="150px"/>
                                                <h:outputText styleClass="tituloCamposDestaqueNegrito " value="Total:" />
                                                <rich:spacer width="5px"/>

                                                <a4j:commandLink  action="#{ClientePorDuracaoRelControle.imprimirTudo}"
                                                                  oncomplete="abrirPopup('../relatorio/clientePorDuracaoResumo.jsp', 'ClientePorDuracaoResumo', 780, 595);"
                                                                  styleClass="tituloCamposDestaque"
                                                                  value="#{ClientePorDuracaoRelControle.totalizadorPessoa}" />

                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>


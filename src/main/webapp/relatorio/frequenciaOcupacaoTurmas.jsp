<%-- 
    Document   : frequenciaOcupacaoTurmas
    Created on : 29/11/2011, 10:37:26
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>

<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<title>Frequência e Ocupação de Turmas</title>

<style type="text/css">
    .myHeader{
        background: none;
        margin: 0 0 0 0;
        padding: 4px 4px 4px 4px;
        border: none;
    }
    .label,
    .badge {
        display: inline-block;
        padding: 2px 4px;
        margin: 1px 1px;
        font-weight: bold;
        min-width: 10px;
        line-height: 8px;
        font-size: 9px;
        vertical-align:top;
        color: #ffffff;
        vertical-align: baseline;
        white-space: nowrap;
        text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
        /*background-color: #807579;        */
        background: #c5deea; /* Old browsers */
        /* IE9 SVG, needs conditional override of 'filter' to 'none' */
        background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2M1ZGVlYSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjMxJSIgc3RvcC1jb2xvcj0iIzhhYmJkNyIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwNjZkYWIiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
        background: -moz-linear-gradient(left,  #c5deea 0%, #8abbd7 31%, #066dab 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, right top, color-stop(0%,#c5deea), color-stop(31%,#8abbd7), color-stop(100%,#066dab)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* IE10+ */
        background: linear-gradient(to right,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c5deea', endColorstr='#066dab',GradientType=1 ); /* IE6-8 */


        border-right: 1px solid #ccc;
        border-left: 1px solid #ccc;
        border-top: 1px solid #ccc;

    }
    .label {
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
    }
    .badge {
        padding-left: 9px;
        padding-right: 9px;
        -webkit-border-radius: 9px;
        -moz-border-radius: 9px;
        border-radius: 9px;
    }
    .label:empty,
    .badge:empty {
        display: none;
    }
    a.label:hover,
    a.label:focus,
    a.badge:hover,
    a.badge:focus {
        color: #ffffff;
        text-decoration: none;
        cursor: pointer;
    }
    .btn .label,
    .btn .badge {
        position: relative;
        top: -1px;
    }
    .btn-mini .label,
    .btn-mini .badge {
        top: 0;
    }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_FrequenciaOcupacaoTurma_TituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-verificar-a-frequencia-e-ocupacao-das-turmas/"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <jsp:include page="../topoReduzido_material.jsp"/>
    <h:form id="form">
        <a4j:poll id="pollConsulta" interval="5000" status="none" enabled="#{FrequenciaOcupacaoControleRel.pollEnabled}"
                  reRender="pollConsulta,panelStatusRealizandoConsulta"/>

        <a4j:commandLink action="#{FrequenciaOcupacaoControleRel.imprimirRelatorio}"
                         oncomplete="location.href=\"#{FrequenciaOcupacaoControleRel.nomeArquivoRelatorioGeradoAgora}\""
                         ajaxSingle="true" id="imprimirRelatorio"  style="display: none" />
        <h:inputHidden id="relatorio" value="#{FrequenciaOcupacaoControleRel.relatorio}" />

        <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
            <hr style="border-color: #e6e6e6;">
            <h:panelGrid id="panelFiltros" columns="2" columnClasses="colunaTopCentralizada" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" style="padding-left:0px;">

                    <!-- empresa -->
                    <h:outputText rendered="#{FrequenciaOcupacaoControleRel.mostrarEmpresa}" styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_empresa}"/>
                    <h:panelGroup rendered="#{FrequenciaOcupacaoControleRel.mostrarEmpresa}">
                        <h:selectOneMenu id="empresa"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                         value="#{FrequenciaOcupacaoControleRel.consultarTurma.empresa.codigo}">
                            <f:selectItems value="#{FrequenciaOcupacaoControleRel.consultarTurma.listaEmpresa}"/>
                            <a4j:support event="onchange" action="#{FrequenciaOcupacaoControleRel.montarListas}" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- modalidade -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_modalidade}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="modalidade" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                         value="#{FrequenciaOcupacaoControleRel.consultarTurma.modalidade.codigo}">
                            <f:selectItems value="#{FrequenciaOcupacaoControleRel.consultarTurma.listaModalidade}"/>
                            <a4j:support event="onchange" action="#{FrequenciaOcupacaoControleRel.montarListaTurma}" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- turma -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_turma}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="turma" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{FrequenciaOcupacaoControleRel.consultarTurma.turma.codigo}">
                            <f:selectItems value="#{FrequenciaOcupacaoControleRel.consultarTurma.listaTurma}"/>
                            <a4j:support event="onchange" action="#{FrequenciaOcupacaoControleRel.montarListaProfessor}" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- professor -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_professor}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="professor" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                         value="#{FrequenciaOcupacaoControleRel.consultarTurma.professor.codigo}">
                            <f:selectItems value="#{FrequenciaOcupacaoControleRel.consultarTurma.listaProfessor}"/>
                            <a4j:support event="onchange" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- ambiente -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_ambiente}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="ambiente" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                         value="#{FrequenciaOcupacaoControleRel.consultarTurma.ambiente.codigo}">
                            <f:selectItems value="#{FrequenciaOcupacaoControleRel.consultarTurma.listaAmbiente}"/>
                            <a4j:support event="onchange" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_dataInicioMatricula}" />
                    <h:panelGrid columns="3" rowClasses="linhaPar" >
                        <h:panelGroup>
                            <rich:calendar id="dataInicioMatricula" inputSize="10" inputClass="form"
                                           value="#{FrequenciaOcupacaoControleRel.dataInicio}"
                                           oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy" enableManualInput="true"
                                           zindex="2" showWeeksBar="false">
                                <a4j:support event="onchange" reRender="form:panelFiltros"/>
                            </rich:calendar>
                            <h:message for="dataInicioMatricula" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ate}" />
                        <h:panelGroup>
                            <rich:calendar id="dataTermino" inputSize="10" inputClass="form"
                                           value="#{FrequenciaOcupacaoControleRel.dataTermino}"
                                           oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy" enableManualInput="true"
                                           zindex="2" showWeeksBar="false">
                                <a4j:support event="onchange" reRender="form:panelFiltros"/>
                            </rich:calendar>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_exibirReposicoes}"/>
                    <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.exibirReposicoes}"/>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGrid>
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda" width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_diaSemana}"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.domingo}"/>
                        <h:outputText value="Dom" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.segunda}"/>
                        <h:outputText value="Seg" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.terca}"/>
                        <h:outputText value="Ter" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.quarta}"/>
                        <h:outputText value="Qua" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.quinta}"/>
                        <h:outputText value="Qui" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.sexta}"/>
                        <h:outputText value="Sex" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.sabado}"/>
                        <h:outputText value="Sáb" styleClass="text" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_horarios}"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h0001as0200}"/>
                        <h:outputText value="00:01 - 02:00" styleClass="text" />
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h0201as0400}"/>
                        <h:outputText styleClass="text" value="02:01 - 04:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h0401as0600}"/>
                        <h:outputText styleClass="text" value="04:01 - 06:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h0601as0800}"/>
                        <h:outputText styleClass="text" value="06:01 - 08:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h0801as1000}"/>
                        <h:outputText styleClass="text" value="08:01 - 10:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h1001as1200}"/>
                        <h:outputText styleClass="text" value="10:01 - 12:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h1201as1400}"/>
                        <h:outputText styleClass="text" value="12:01 - 14:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h1401as1600}"/>
                        <h:outputText styleClass="text" value="14:01 - 16:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h1601as1800}"/>
                        <h:outputText styleClass="text" value="16:01 - 18:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h1801as2000}"/>

                        <h:outputText styleClass="text" value="18:01 - 20:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h2001as2200}"/>
                        <h:outputText styleClass="text" value="20:01 - 22:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{FrequenciaOcupacaoControleRel.consultarTurma.h2201as0000}"/>
                        <h:outputText styleClass="text" value="22:01 - 00:00"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens" id="mensagemDetalhada">
                    <h:outputText styleClass="mensagem" value="#{FrequenciaOcupacaoControleRel.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{FrequenciaOcupacaoControleRel.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGroup>
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink id="consultar" action="#{FrequenciaOcupacaoControleRel.consultarTurmas}"
                                         reRender="panelhorarioTurma, modalidadeTurma, mensagemDetalhada, panelAlunos, panelExportar, form:imprimirRelatorio, colunaCentralizada, form:imprimirPDF, form:imprimirExcel"
                                         value="#{msg_bt.btn_consultar}"
                                         status="statusRealizandoConsulta"
                                         title="#{msg.msg_consultar_dados}" accesskey="4"
                                         styleClass="botaoPrimario texto-size-14-real"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="container-botoes" id="panelExportar">
                        <a4j:commandButton id="imprimirPDF"
                                           actionListener="#{FrequenciaOcupacaoControleRel.escolheRelatorio}"
                                           oncomplete="#{FrequenciaOcupacaoControleRel.mensagemNotificar}#{FrequenciaOcupacaoControleRel.msgAlert}" accesskey="2" styleClass="botoes"
                                           reRender="form" image="/imagens/imprimir.png"
                                           rendered="#{not empty FrequenciaOcupacaoControleRel.listaHorarioTurma}">
                            <f:attribute name="tipoRelatorio" value="PDF"/>
                        </a4j:commandButton>
                        <rich:spacer width="10px"/>
                        <a4j:commandButton image="/imagens/btn_excel.png" title="Exportar EXCEL"
                                           rendered="#{!empty FrequenciaOcupacaoControleRel.listaHorarioTurma}"
                                           reRender="mensagem"
                                           value="Excel"
                                           id="imprimirExcel"
                                           actionListener="#{FrequenciaOcupacaoControleRel.escolheRelatorio}"
                                           oncomplete="#{FrequenciaOcupacaoControleRel.mensagemNotificar}#{FrequenciaOcupacaoControleRel.msgAlert}">
                            <f:attribute name="tipoRelatorio" value="EXCEL"/>
                        </a4j:commandButton>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="panelhorarioTurma" columns="1" width="100%" >
                <rich:dataTable id="modalidadeTurma" width="100%" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaCentralizada"
                                value="#{FrequenciaOcupacaoControleRel.listaHorarioTurma}" var="horarioTurma" rowKeyVar="status">
                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column sortBy="#{horarioTurma.horario.identificadorTurma}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloTurma}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.horario.identificadorTurma}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurma.horario.professor.pessoa.nome}" filterEvent="onkeyup" >
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloProfessor}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.horario.professor.pessoa.nome}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurma.horario.ambiente.descricao}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloAmbiente}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.horario.ambiente.descricao}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurma.horario.diaSemana_Apresentar}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloDiaSemana}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.horario.diaSemana_Apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurma.horario.horaInicial}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloInicio}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.horario.horaInicial}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurma.horario.horaFinal}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloFim}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.horario.horaFinal}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurma.horario.nrMaximoAluno}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloVagas}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.horario.nrMaximoAluno}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurma.horario.nrAlunoMatriculado}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloOcupadas}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.qtdAlunos}"/>
                    </rich:column>

                    <rich:column sortBy="#{horarioTurma.horario.txOcupacao}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloTxOcupacao}"/>
                        </f:facet>
                        <h:outputText rendered="#{horarioTurma.horario.txOcupacao <= 50}" style="color: darkred;"
                                      value="#{horarioTurma.horario.txOcupacao}" title="Ruim">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                        <h:outputText rendered="#{horarioTurma.horario.txOcupacao > 50 and horarioTurma.horario.txOcupacao <= 60}" style="color: orange;"
                                      value="#{horarioTurma.horario.txOcupacao}" title="Médio">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                        <h:outputText rendered="#{horarioTurma.horario.txOcupacao > 60 and horarioTurma.horario.txOcupacao <= 80}" style="color: blue;"
                                      value="#{horarioTurma.horario.txOcupacao}" title="Bom">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                        <h:outputText rendered="#{horarioTurma.horario.txOcupacao > 80}" style="color: green;"
                                      value="#{horarioTurma.horario.txOcupacao}" title="Ótimo">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                        <h:outputText value="%"/>
                    </rich:column>

                    <rich:column sortBy="#{horarioTurma.qtdPrevisto}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloPrevisto}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.qtdPrevisto}"/>
                        <h:panelGroup rendered="#{FrequenciaOcupacaoControleRel.exibirReposicoes}">
                            <a4j:commandLink rendered="#{horarioTurma.horario.nrAlunoSairamPorReposicao != 0}"
                                             styleClass="label"
                                             actionListener="#{ReposicaoControle.exibirReposicoes}"
                                             title="#{msg_aplic.prt_ConsultarTurma_xAlunosDesmarcaram}"
                                             reRender="modalReposicoes"
                                             oncomplete="#{ReposicaoControle.onComplete}"
                                             value="-#{horarioTurma.horario.nrAlunoSairamPorReposicao}">
                                <f:attribute name="horarioTurmaAtual" value="#{horarioTurma.horario}"/>
                                <f:attribute name="sairam" value="true"/>
                            </a4j:commandLink>

                            <a4j:commandLink rendered="#{horarioTurma.horario.nrAlunoEntraramPorReposicao != 0}"
                                             actionListener="#{ReposicaoControle.exibirReposicoes}"
                                             styleClass="label"
                                             reRender="modalReposicoes"
                                             title="#{msg_aplic.prt_ConsultarTurma_xAlunosMarcaram}"
                                             oncomplete="#{ReposicaoControle.onComplete}"
                                             value="+#{horarioTurma.horario.nrAlunoEntraramPorReposicao}">
                                <f:attribute name="horarioTurmaAtual" value="#{horarioTurma.horario}"/>
                                <f:attribute name="sairam" value="false"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column sortBy="#{horarioTurma.qtdPresencas}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloPresencas}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurma.qtdPresencas}"/>
                    </rich:column>

                    <rich:column sortBy="#{horarioTurma.horario.freqMedia}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloFreqMedia}"/>
                        </f:facet>
                        <h:outputText rendered="#{horarioTurma.horario.freqMedia <= 50}" style="color: darkred;"
                                      value="#{horarioTurma.horario.freqMedia}" title="Ruim">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                        <h:outputText rendered="#{horarioTurma.horario.freqMedia > 50 and horarioTurma.horario.freqMedia <= 60}" style="color: orange;"
                                      value="#{horarioTurma.horario.freqMedia}" title="Médio">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                        <h:outputText rendered="#{horarioTurma.horario.freqMedia > 60 and horarioTurma.horario.freqMedia <= 80}" style="color: blue;"
                                      value="#{horarioTurma.horario.freqMedia}" title="Bom">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                        <h:outputText rendered="#{horarioTurma.horario.freqMedia > 80}" style="color: green;"
                                      value="#{horarioTurma.horario.freqMedia}" title="Ótimo">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                        <h:outputText value="%"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Op"/>
                        </f:facet>
                        <a4j:commandButton action="#{FrequenciaOcupacaoControleRel.posicionaListaAlunos}"
                                           oncomplete="Richfaces.showModalPanel('panelAlunos')" reRender="panelAlunos"
                                           image="../imagensCRM/iconeVerBv.png" title="Ver + Detalhes."/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>

    <rich:modalPanel width="350" id="panelStatusRealizandoConsulta" autosized="true" styleClass="modal-carregando-ripple"
                     showWhenRendered="#{FrequenciaOcupacaoControleRel.emProcessamento}">
        <div class="loader-inner ball-scale-ripple-multiple" >
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
        <h:panelGroup  layout="block" styleClass="textoImcompleto" style="width: 100%;" >
            <h:outputText id="mensagemBICarregando"  style="color:#094771;font-style: italic;line-height: 6;padding: 10px;background-color: #fff;border:1px solid #094771;border-radius: 3px;"
                          styleClass="texto-font texto-size-20 texto-cor-cinza mensagemBICarregando" value="    Gerando dados para o Relatório... isso pode levar alguns minutos..."/>
        </h:panelGroup>
    </rich:modalPanel>
    <a4j:status id="statusRealizandoConsulta" forceId="true"
                onstart="Richfaces.showModalPanel('panelStatusRealizandoConsulta');"
                onstop="#{rich:component('panelStatusRealizandoConsulta')}.hide();"/>

    <rich:modalPanel headerClass="myHeader" id="panelAlunos" autosized="true" shadowOpacity="true" width="600">
        <f:facet name="header">
            <h:panelGrid columns="1" style="height:25px; background-image:url('../../imagens/fundoBarraTopo.png'); background-repeat: repeat-x;"
                         columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ConsultaAlunoTurma_listaAlunos}"/>
            </h:panelGrid>
        </f:facet>
        <a4j:form id="formAlunos" ajaxSubmit="true" >
            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda" width="100%">
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloTurma}:"/>
                <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.horario.identificadorTurma}"/>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloProfessor}:"/>
                <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.horario.professor.pessoa.nome}"/>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloAmbiente}"/>
                <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.horario.ambiente.descricao}"/>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloDiaSemana}"/>
                <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.horario.diaSemana_Apresentar}"/>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloHorarios}:"/>
                <h:panelGroup>
                    <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.horario.horaInicial} - "/>
                    <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.horario.horaFinal}"/>
                </h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloFreqMedia}"/>
                <h:panelGroup>
                    <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.horario.freqMedia}" title="Frequencia Média dos Ultimos 30 dias.">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                    <h:outputText value="%"/>
                </h:panelGroup>

                <h:outputText value="Previstas:"/>
                <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.qtdPrevisto}"/>

                <h:outputText value="Presenças:"/>
                <h:outputText value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.qtdPresencas}"/>
            </h:panelGrid>
            <div style="padding-top: 10px;"></div>
            <h:panelGroup layout="block" id="oi" style="overflow-y: auto; height: 200px">
                <rich:dataTable id="alunos" width="100%" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaCentralizada"
                                value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.listaAlunos}" var="aluno"
                                rowKeyVar="status">
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.codigo}">
                        <f:facet name="header">
                            <h:outputText value="Código"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.codigo}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.clienteVO.matricula}">
                        <f:facet name="header">
                            <h:outputText value="Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{aluno.clienteVO.matricula}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.clienteVO.pessoa.nome}">
                        <f:facet name="header">
                            <h:outputText value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{aluno.clienteVO.pessoa.nome}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.contratoDuracaoVO.contrato}">
                        <f:facet name="header">
                            <h:outputText value="Contrato"/>
                        </f:facet>
                        <h:outputText value="#{aluno.contratoDuracaoVO.contrato}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.qtdeAulasPeriodo}">
                        <f:facet name="header">
                            <h:outputText value="Previstas"/>
                        </f:facet>
                        <h:outputText value="#{aluno.qtdeAulasPeriodo}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.qtdePresencasPeriodo}">
                        <f:facet name="header">
                            <h:outputText value="Presenças"/>
                        </f:facet>
                        <h:outputText value="#{aluno.qtdePresencasPeriodo}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.frequenciaAluno}">
                        <f:facet name="header">
                            <h:outputText value="Frequencia"/>
                        </f:facet>
                        <h:outputText value="#{aluno.frequenciaAluno}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText value="%"/>
                    </rich:column>
                </rich:dataTable>

                <rich:dataTable id="reposicoes" width="100%" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaCentralizada" var="repo" rowKeyVar="status"
                                rendered="#{not empty FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.listaReposicoes}"
                                value="#{FrequenciaOcupacaoControleRel.horarioTurmaSelecionado.listaReposicoes}">
                    <f:facet name="header">
                        <h:outputText value="Alterações por Reposição"/>
                    </f:facet>

                    <rich:column sortBy="#{repo.cliente.pessoa.nome}">
                        <f:facet name="header">
                            <h:outputText value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{repo.cliente.pessoa.nome}"/>
                    </rich:column>
                    <rich:column sortBy="#{repo.tipo}">
                        <f:facet name="header">
                            <h:outputText value="Situação"/>
                        </f:facet>
                        <h:outputText value="#{repo.tipoComoString}"/>
                    </rich:column>
                    <rich:column sortBy="#{repo.dataReposicao}">
                        <f:facet name="header">
                            <h:outputText value="Dt.Aula"/>
                        </f:facet>
                        <h:outputText value="#{repo.data_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <div style="margin:10px 0 0 0;"></div>
            <h:panelGrid columns="1" width="100%" columnClasses="centralizado">
                <h:panelGroup>
                    <a4j:commandButton id="imprimirPDF" action="#{FrequenciaOcupacaoControleRel.escolheRelatorioAluno}"
                                       oncomplete="imprimirRelatorio(this.form);" accesskey="2" styleClass="botoes"
                                       reRender="form" image="../imagens/imprimirContrato.png"/>
                    <rich:spacer width="10px"/>
                    <a4j:commandButton oncomplete="Richfaces.hideModalPanel('panelAlunos')"
                                       value="#{msg_bt.btn_selecionar}" styleClass="botoes"
                                       image="/imagens/botaoFechar.png"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="../includes/cliente/include_modal_reposicoes.jsp" %>

</f:view>


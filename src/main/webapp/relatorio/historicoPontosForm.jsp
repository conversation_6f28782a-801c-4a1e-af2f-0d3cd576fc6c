<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>

<script type="text/javascript" language="javascript" src="../bootstrap/jquery.js"></script>
<script type="text/javascript" language="javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Pontuacao_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}onde-consigo-ver-o-historico-de-pontos-do-clube-de-vantagens/"/>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Pontuacao_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../topoReduzido_material.jsp"/>
            </f:facet>
        </h:panelGroup>
        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
            <h:outputText style="font-weight: bold" value="Resultados: #{HistoricoPontosControle.qtdTotalResultados}"/>
        </h:panelGrid>
        <h:form id="form">
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" >
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <h:panelGrid columns="1" width="100%" >
                                    <h:panelGrid style="width:100%">
                                        <h:panelGroup id="botoes" style="text-align:right;" layout="block">
                                            <a4j:commandLink id="exportarExcel"
                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                               rendered="#{not empty HistoricoPontosControle.listaTotalPontos}"
                                                               oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                               accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista" value="#{HistoricoPontosControle.listaTotalPontos}"/>
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="atributos" value="#{HistoricoPontosControle.dadosExportacao}"/>
                                                <f:attribute name="prefixo" value="HistoricoPontos"/>
                                                <f:attribute name="itemExportacao" value="relPontuacaoAlunos"/>
                                                <h:outputText title="#{msg_aplic.prt_exportar_form_excel}" styleClass="btn-print-2 excel tooltipster"/>
                                            </a4j:commandLink>
                                            <%--BOTÃO PDF--%>
                                            <a4j:commandLink id="exportarPdf"
                                                               style="margin-left: 8px;"
                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                               rendered="#{not empty HistoricoPontosControle.listaTotalPontos}"
                                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                               accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista" value="#{HistoricoPontosControle.listaTotalPontos}"/>
                                                <f:attribute name="tipo" value="pdf"/>
                                                <f:attribute name="itemExportacao" value="relPontuacaoAlunos"/>
                                                <f:attribute name="atributos" value="#{HistoricoPontosControle.dadosExportacao}"/>
                                                <f:attribute name="prefixo" value="HistoricoPontos"/>
                                                <h:outputText title="#{msg_aplic.prt_exportar_form_pdf}" styleClass="btn-print-2 PDF tooltipster"/>
                                            </a4j:commandLink>
                                        </h:panelGroup> 
                                    </h:panelGrid>
                                    <rich:dataTable id="listaPontosAlunos"  width="100%" styleClass="tabelaSimplesCustom"
                                                    rendered="#{!HistoricoPontosControle.pesquisaDetalhada}"
                                                    value="#{HistoricoPontosControle.listaTotalPontos}" var="lista">
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.cliente.matricula}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.MATRICULA}" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.cliente.matricula}" />
                                        </rich:column>

                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.cliente.pessoa.nome}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.CLIENTE}" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{lista.cliente.pessoa.nome}" />
                                        </rich:column>

                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.pontostotal}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.TOTAL_PONTOS}" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.pontostotal}" />
                                        </rich:column>

                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.nomeEmpresa}" filterEvent="onkeyup"
                                                     rendered="#{HistoricoPontosControle.permissaoConsultaTodasEmpresas}">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Empresa" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.nomeEmpresa}" />
                                        </rich:column>

                                        <rich:column>
                                            <a4j:commandLink id="visualizarCliente" action="#{HistoricoPontosControle.irParaTelaCliente}" title="#{msg_aplic.prt_tela_cliente}"
                                                             oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                                                    styleClass="linkPadrao texto-cor-azul texto-size-14-real tooltipster">
                                                <i class="fa-icon-search"></i>
                                            </a4j:commandLink>
                                        </rich:column>
                                                
                                        <rich:column>
                                            <a4j:commandLink id="visualizarHistoricoPontos" action="#{HistoricoPontosControle.irParaTelaExtratoPontos}" title="#{msg_aplic.prt_historico_pontos}"
                                                             oncomplete="abrirPopup('../relatorio/historicoPontosResumo.jsp', 'Histotico Pontos Por Aluno', 1024, 700);"
                                                    styleClass="linkPadrao texto-cor-azul texto-size-14-real tooltipster">
                                                <i class="fa-icon-file-alt"></i>
                                            </a4j:commandLink>
                                        </rich:column>
                                    </rich:dataTable>
                                    <rich:dataTable id="listaPontosAlunosDetalhado" width="100%" styleClass="tabelaSimplesCustom"
                                                    rendered="#{HistoricoPontosControle.pesquisaDetalhada}"
                                                    value="#{HistoricoPontosControle.listaTotalPontos}" var="lista">
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.cliente.matricula}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.MATRICULA}" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.cliente.matricula}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.cliente.pessoa.nome}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.CLIENTE}" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.cliente.pessoa.nome}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.pontos}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Brinde_label_pontos}" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.pontos}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.dataaula}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.DATA_OPERACAO}" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.dataAula_Apresentar}" />
                                        </rich:column>
                                        
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.brinde.nome}" filterEvent="onkeyup">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.BRINDE}" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.brinde.nome}" />
                                        </rich:column>

                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{lista.nomeEmpresa}" filterEvent="onkeyup"
                                                     rendered="#{HistoricoPontosControle.permissaoConsultaTodasEmpresas}">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Empresa" />
                                            </f:facet>
                                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{lista.nomeEmpresa}" />
                                        </rich:column>
                                        
                                        <rich:column>
                                            <a4j:commandLink id="visualizarCliente" action="#{HistoricoPontosControle.irParaTelaCliente}" title="#{msg_aplic.prt_tela_cliente}"
                                                             oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"
                                                    styleClass="linkPadrao texto-cor-azul texto-size-14-real tooltipster">
                                                <i class="fa-icon-search"></i>
                                            </a4j:commandLink>
                                        </rich:column>
                                    </rich:dataTable>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%" style="text-align: center;font-family: Arial;font-size: 14px;margin-top: 15px;">
                    <h:panelGroup>
                        <a4j:commandLink id="consultar" immediate="true"
                                         action="#{HistoricoPontosControle.voltar}"
                                         styleClass="pure-button pure-button-primary"
                                         accesskey="4">
                                <i class="fa-icon-search"></i> Refazer Consulta
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{HistoricoPontosControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoPontosControle.mensagemDetalhada}"/>
                    </h:panelGrid>
            </h:panelGrid>  

        </h:form>
    </h:panelGrid>
    
    <script>
        function carregarTooltipster(){
            jQuery('.tooltipster').tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        };
        carregarTooltipster();
    </script>
</f:view>

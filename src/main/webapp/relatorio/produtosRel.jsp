<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="${root}/css/otimize.css" rel="stylesheet" type="text/css">
    <link href="${root}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="${root}/css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" language="javascript" src="${root}/hoverform.js"></script>
    <script type="text/javascript" language="javascript" src="${root}/script/script.js"></script>
    <script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript" ></script>
</head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<%@include file="/includes/verificaModulo.jsp" %>
<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value=" Relatório de Produtos com Vigência"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-pendencias-de-clientes-adm/"/>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

<rich:modalPanel id="panelConsultor" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Consultor Responsável"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink1"/>
            <rich:componentControl for="panelConsultor" attachTo="hidelink1" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formConsultor" ajaxSubmit="true" styleClass="font-size-Em-max">
        <h:panelGrid columns="1"  columnClasses="classEsquerda, classDireita"
                     width="100%">
            <h:panelGrid columns="3" footerClass="colunaAlinhamento" width="100%">
                <h:outputText styleClass="tituloCampos" style="float: right;" value="Nome: "/>
                <h:inputText id="valorConsultaResponsavel" size="40"
                             value="#{ProdutoRelControle.valorConsultaConsultor}"/>
                <a4j:commandLink id="btnConsultarResponsavel" style="float: right;"
                                   action="#{ProdutoRelControle.consultarResponsavel}" styleClass="botaoPrimario texto-size-16-real"
                                   value="#{msg_bt.btn_consultar}"
                                   title="#{msg.msg_consultar_dados}"
                                   reRender="formConsultor"/>
            </h:panelGrid>

            <rich:dataTable id="resultadoConsultaConsultor" width="100%" styleClass="tabelaSimplesCustom"
                            rendered="#{not empty ProdutoRelControle.listaConsultores}"
                            value="#{ProdutoRelControle.listaConsultores}" rows="5" var="consultor">
                <rich:column styleClass="col-text-align-left"  headerClass="col-text-align-left">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Usuario_colaborador}"/>
                    </f:facet>
                    <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul" action="#{ProdutoRelControle.selecionarResponsavel}" focus="consultor"
                                     reRender="form" oncomplete="Richfaces.hideModalPanel('panelConsultor')"
                                     value="#{consultor.pessoa.nome}"/>
                </rich:column>
                <rich:column styleClass="col-text-align-right"  headerClass="col-text-align-right">
                    <a4j:commandLink id="btnSelecionarConsultor"
                                       styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                       action="#{ProdutoRelControle.selecionarResponsavel}" focus="consultor"
                                       reRender="form" oncomplete="Richfaces.hideModalPanel('panelConsultor')"
                                       title="#{msg.msg_selecionar_dados}">
                        <span>Selecionar </span>
                        <i class="fa-icon-arrow-right"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" for="formConsultor:resultadoConsultaConsultor" maxPages="10"
                               styleClass="scrollPureCustom" renderIfSinglePage="scrollPureCustom"
                               id="scResultadoResponsavel"/>
            <h:panelGrid id="mensagemConsultaConsultor" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{ProdutoRelControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{ProdutoRelControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelRespLancamento" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Responsável pelo Lançamento"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink2"/>
            <rich:componentControl for="panelRespLancamento" attachTo="hidelink2" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formRespLancamento" ajaxSubmit="true" styleClass="font-size-Em-max">
        <h:panelGrid columns="1"  columnClasses="classEsquerda, classDireita"
                     width="100%">
            <h:panelGrid columns="3" footerClass="colunaAlinhamento" width="100%">
                <h:outputText styleClass="tituloCampos" style="float: right;" value="Nome: "/>
                <h:inputText id="valorConsultaResponsavelLancamento" size="40"
                             value="#{ProdutoRelControle.valorConsultaResponsavelLancamento}"/>
                <a4j:commandLink id="btnConsultarResponsavelLancamento" action="#{ProdutoRelControle.consultarResponsavelLancamento}"
                                   styleClass="botaoPrimario texto-size-16-real"
                                   value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"
                                   reRender="formRespLancamento"/>
            </h:panelGrid>

            <rich:dataTable id="resultadoResponsavelLancamento" width="100%"  styleClass="tabelaSimplesCustom"
                            rendered="#{not empty ProdutoRelControle.listaResponsavelLancamento}"
                            value="#{ProdutoRelControle.listaResponsavelLancamento}" rows="5" var="usuario">
                <rich:column styleClass="texto-col-align-left" headerClass="texto-col-align-left">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Usuario_colaborador}"/>
                    </f:facet>
                    <a4j:commandLink  styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                     action="#{ProdutoRelControle.selecionarResponsavelLancamento}" focus="usuario"
                                     reRender="form" oncomplete="Richfaces.hideModalPanel('panelRespLancamento')"
                                     value="#{usuario.nome}"/>
                </rich:column>
                <rich:column styleClass="texto-col-align-right" headerClass="texto-col-align-right">
                    <a4j:commandLink id="btnSelecionarResponsavelLancamento" action="#{ProdutoRelControle.selecionarResponsavelLancamento}" focus="usuario"
                                       reRender="form" oncomplete="Richfaces.hideModalPanel('panelRespLancamento')"
                                       title="#{msg.msg_selecionar_dados}"
                                       styleClass="linkPadrao texto-size-14-real texto-cor-azul">
                        <span>Selecionar </span>
                        <i class="fa-icon-arrow-right"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" for="formRespLancamento:resultadoResponsavelLancamento"
                               styleClass="scrollPureCustom" renderIfSinglePage="false"
                               id="scResultadoResponsavelLancamento"/>
            <h:panelGrid id="mensagemResponsavelLancamento" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{ProdutoRelControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ProdutoRelControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
<f:facet name="header">
    <jsp:include page="../topoReduzido_material.jsp"/>
</f:facet>

<h:form id="form" target="_blank">
<%--<h:commandLink action="#{ProdutoRelControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"--%>
<%--style="display: none"/>--%>
<%--<h:commandLink action="#{ProdutoRelControle.imprimirRelatorio}" id="imprimirRelatorio"--%>
<%--style="display: none"/>--%>
<h:inputHidden id="relatorio" value="#{ProdutoRelControle.relatorio}"/>
<h:panelGrid id="principal" columns="1" width="100%">
        <rich:panel id="pnlFiltros">
        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                     width="100%">

            <h:outputText rendered="#{ProdutoRelControle.permissaoConsultaTodasEmpresas}" styleClass="tituloCampos"
                          value="Empresa"/>
            <h:selectOneMenu rendered="#{ProdutoRelControle.permissaoConsultaTodasEmpresas}"
                             value="#{ProdutoRelControle.filtroEmpresa}">
                <f:selectItems value="#{ProdutoRelControle.listaEmpresas}"/>
            </h:selectOneMenu>

            <h:outputText
                    title="Considera a configuração que restringe os dados exibidos no BI de Pendências de Clientes, para desconsiderar clique no ícone de apagar."
                    styleClass="tituloCampos tooltipster"
                    value="Clientes cadastrados a partir de"/>
            <h:panelGroup id="clientesCadastradosAPartirDe">
                <h:panelGroup>
                    <rich:calendar id="dataCadastroCliente"
                                   value="#{ProdutoRelControle.produtoRel.dataInicioCadastroCliente}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:message for="dataCadastroCliente" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <a4j:commandButton image="../imagens/limpar.gif" reRender="clientesCadastradosAPartirDe"
                                   action="#{ProdutoRelControle.limparCampoDataCadastroCliente}"/>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="Clientes sem produto"/>
            <h:selectBooleanCheckbox value="#{ProdutoRelControle.produtoRel.semProdutos}" id="checkBoxClientesSemProdutosRel">
                <a4j:support event="onchange" reRender="pnlFiltros"/>
            </h:selectBooleanCheckbox>

            <h:outputText styleClass="tituloCampos" value="Data de Lançamento"/>
            <%--rendered="#{ProdutoRelControle.produtoRel.semProdutos}"--%>
            <h:panelGroup id="datasLancamento">
                <h:panelGroup>
                    <rich:calendar id="dataInicio"
                                   value="#{ProdutoRelControle.produtoRel.dataInicioLanc}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" style="position:relative; top:0; left:10px;"
                              value="#{msg_aplic.prt_CaixaPorOperador_ate}"/>
                <rich:spacer width="12px"/>
                <h:panelGroup>
                    <rich:calendar id="dataTermino"
                                   value="#{ProdutoRelControle.produtoRel.dataFinalLanc}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="true"/>
                    <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <a4j:commandButton image="../imagens/limpar.gif" reRender="datasLancamento"
                                   action="#{ProdutoRelControle.limparCampoDatasLanc}"/>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="Data de Vencimento" rendered="#{!ProdutoRelControle.produtoRel.semProdutos}"/>

            <h:panelGroup id="datasVencimento" rendered="#{!ProdutoRelControle.produtoRel.semProdutos}">
                <h:panelGroup>
                    <rich:calendar id="dataInicioVenc"
                                   value="#{ProdutoRelControle.produtoRel.dataInicioVenc}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" style="position:relative; top:0; left:10px;"
                              value="#{msg_aplic.prt_CaixaPorOperador_ate}"/>
                <rich:spacer width="12px"/>
                <h:panelGroup>
                    <rich:calendar id="dataTerminoVenc"
                                   value="#{ProdutoRelControle.produtoRel.dataFinalVenc}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="true"/>
                    <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGroup>
                <a4j:commandButton image="../imagens/limpar.gif" reRender="datasVencimento"
                                   action="#{ProdutoRelControle.limparCampoDatasVenc}"/>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="Situação"/>

            <h:panelGroup>
                <h:selectManyCheckbox styleClass="tituloCampos" value="#{ProdutoRelControle.situacoes}" id="checkBoxSituacoesProdutosRel">
                    <f:selectItems value="#{ProdutoRelControle.situacoesPossiveis}"/>
                </h:selectManyCheckbox>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="Consultor"/>
            <h:panelGroup>
                <h:inputText id="nomeOperador" size="40" maxlength="40" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form" disabled="true"
                             value="#{ProdutoRelControle.produtoRel.consultorSelecionado.pessoa.nome}"/>
                <a4j:commandButton id="consultarOperador" alt="Consultar Operador"
                                   image="../imagens/informacao.gif"
                                   oncomplete="Richfaces.showModalPanel('panelConsultor'); setFocus(formConsultor,'formConsultor:valorConsultarResponsavel')"/>
                <rich:spacer width="5px"/>
                <a4j:commandButton id="LimparOperador" image="../imagens/limpar.gif" reRender="nomeOperador"
                                   action="#{ProdutoRelControle.limparCampoOperador}"/>
            </h:panelGroup>

            <h:outputText rendered="#{!ProdutoRelControle.produtoRel.semProdutos}" styleClass="tituloCampos" value="Responsável pelo Lançamento"/>
            <h:panelGroup rendered="#{!ProdutoRelControle.produtoRel.semProdutos}">
                <h:inputText id="respLancamento" size="40" maxlength="40" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form" disabled="true"
                             value="#{ProdutoRelControle.produtoRel.responsavelLancamento.nome}"/>
                <a4j:commandButton id="consultarResponsavelLancamento" alt="Consultar pelo Responsável do Lançamento"
                                   image="../imagens/informacao.gif"
                                   oncomplete="Richfaces.showModalPanel('panelRespLancamento'); setFocus(formRespLancamento,'formRespLancamento:valorConsultarResponsavelLancamento')"/>
                <rich:spacer width="5px"/>
                <a4j:commandButton id="LimparRespLancamento" image="../imagens/limpar.gif" reRender="respLancamento"
                                   action="#{ProdutoRelControle.limparCampoResponsavelLancamento}"/>
            </h:panelGroup>

            <h:outputText rendered="#{!ProdutoRelControle.produtoRel.semProdutos}" styleClass="tituloCampos"
                          value="Tipo de Produto"/>
            <h:selectOneMenu rendered="#{!ProdutoRelControle.produtoRel.semProdutos}" id="selecTipoProdutoRel"
                             value="#{ProdutoRelControle.produtoRel.tipoProduto}">
                <f:selectItems value="#{ProdutoRelControle.listaTipoProduto}"/>
                <a4j:support event="onchange" action="#{ProdutoRelControle.acaoSelecionarTipoProduto}"
                             reRender="principal"/>
            </h:selectOneMenu>

            <h:outputText styleClass="tituloCampos" value="Produto"
                          rendered="#{ProdutoRelControle.mostrarListaProduto}"/>
            <h:selectOneMenu value="#{ProdutoRelControle.produtoRel.codProduto}"
                             rendered="#{ProdutoRelControle.mostrarListaProduto}">
                <f:selectItems value="#{ProdutoRelControle.listaProduto}"/>
            </h:selectOneMenu>
        </h:panelGrid>
        <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
            <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                <h:outputText styleClass="mensagem" value="#{ProdutoRelControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{ProdutoRelControle.mensagemDetalhada}"/>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                <h:panelGroup>
                    <rich:spacer width="20px"/>
                    <a4j:commandLink id="imprimirPDF" action="#{ProdutoRelControle.imprimirRelatorio}"
                                       accesskey="2" styleClass="botoes nvoBt"
                                       reRender="form">
                        <i class="fa-icon-print"></i> Gerar Relatório
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>
    </rich:panel>
</h:panelGrid>
<h:panelGroup rendered="#{ProdutoRelControle.mostrarRelatorio}">
    <h:panelGrid rendered="#{!ProdutoRelControle.semProdutos}" width="100%"
                 style="margin-bottom: 8px;text-align: right;">
        <h:panelGroup layout="block">
            <%--BOTÃO EXCEL--%>
            <a4j:commandLink id="exportarExcelProdutos"
                               style="margin-left: 8px;"
                               actionListener="#{ExportadorListaControle.exportar}"
                               rendered="#{not empty ProdutoRelControle.clienteProdutoTOs}"
                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                               accesskey="2" styleClass="botoes linkPadrao">
                <f:attribute name="lista" value="#{ProdutoRelControle.clienteProdutoTOs}"/>
                <f:attribute name="tipo" value="xls"/>
                <f:attribute name="atributos"
                             value="matriculaCliente=Matrícula,nomeCliente=Nome,telefonesCliente=Telefones,descricaoProduto=Produto,responsavellancamento=Responsável,validadeProduto_Apresentar=Validade,valorProduto_Apresentar=Valor,nomeEmpresa=Empresa"/>
                <f:attribute name="prefixo" value="ClientesComProdutosVigencia"/>
                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
            </a4j:commandLink>
            <%--BOTÃO PDF--%>
            <a4j:commandLink id="exportarPdfProdutos"
                               style="margin-left: 8px;"
                               actionListener="#{ExportadorListaControle.exportar}"
                               rendered="#{not empty ProdutoRelControle.clienteProdutoTOs}"
                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                               accesskey="2" styleClass="botoes linkPadrao">
                <f:attribute name="lista" value="#{ProdutoRelControle.clienteProdutoTOs}"/>
                <f:attribute name="tipo" value="pdf"/>
                <f:attribute name="atributos"
                             value="matriculaCliente=Matrícula,nomeCliente=Nome,telefonesCliente=Telefones,descricaoProduto=Produto,responsavellancamento=Responsável,validadeProduto_Apresentar=Validade,valorProduto_Apresentar=Valor,nomeEmpresa=Empresa"/>
                <f:attribute name="prefixo" value="ClientesComProdutosVigencia"/>
                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGrid>
    <h:panelGrid rendered="#{ProdutoRelControle.semProdutos}" width="100%"
                 style="margin-bottom: 8px;text-align: right;">
        <h:panelGroup layout="block">
            <%--BOTÃO EXCEL--%>
            <a4j:commandLink id="exportarExcelSemProdutos"
                               style="margin-left: 8px;"
                               actionListener="#{ExportadorListaControle.exportar}"
                               rendered="#{not empty ProdutoRelControle.clienteProdutoTOs}"
                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                               accesskey="2" styleClass="botoes linkPadrao">
                <f:attribute name="lista" value="#{ProdutoRelControle.clienteProdutoTOs}"/>
                <f:attribute name="tipo" value="xls"/>
                <f:attribute name="atributos"
                             value="matriculaCliente=Matrícula,nomeCliente=Nome,telefonesCliente=Telefones,responsavellancamento=Responsável Lançamento"/>
                <f:attribute name="prefixo" value="ClientesSemProdutosVigencia"/>
                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
            </a4j:commandLink>
            <%--BOTÃO PDF--%>
            <a4j:commandLink id="exportarPdfSemProdutos"
                               style="margin-left: 8px;"
                               actionListener="#{ExportadorListaControle.exportar}"
                               rendered="#{not empty ProdutoRelControle.clienteProdutoTOs}"
                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                               accesskey="2" styleClass="botoes linkPadrao">
                <f:attribute name="lista" value="#{ProdutoRelControle.clienteProdutoTOs}"/>
                <f:attribute name="tipo" value="pdf"/>
                <f:attribute name="atributos"
                             value="matriculaCliente=Matrícula,nomeCliente=Nome,telefonesCliente=Telefones,responsavellancamento=Responsável Lançamento"/>
                <f:attribute name="prefixo" value="ClientesSemProdutosVigencia"/>
                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGrid>
    <rich:dataTable id="clientes" value="#{ProdutoRelControle.clienteProdutoTOs}" var="item" rowKeyVar="status" width="100%"
                    rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" rows="30">
        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
        <rich:column sortBy="#{item.matriculaCliente}">
            <f:facet name="header">
                <h:outputText value="Matrícula"/>
            </f:facet>
            <h:outputText value="#{item.matriculaCliente}"/>
        </rich:column>
        <rich:column sortBy="#{item.nomeCliente}">
            <f:facet name="header">
                <h:outputText value="Nome"/>
            </f:facet>
            <h:outputText value="#{item.nomeCliente}"/>
        </rich:column>
        <rich:column sortBy="#{item.telefonesCliente}">
            <f:facet name="header">
                <h:outputText value="Telefone"/>
            </f:facet>
            <h:outputText value="#{item.telefonesCliente}"/>
        </rich:column>
        <rich:column rendered="#{!ProdutoRelControle.produtoRel.semProdutos}" sortBy="#{item.descricaoProduto}">
            <f:facet name="header">
                <h:outputText value="Produto"/>
            </f:facet>
            <h:outputText value="#{item.descricaoProduto}"/>
        </rich:column>
        <rich:column rendered="#{!ProdutoRelControle.produtoRel.semProdutos}" sortBy="#{item.responsavellancamento}">
            <f:facet name="header">
                <h:outputText value="Responsável Lançamento"/>
            </f:facet>
            <h:outputText value="#{item.responsavellancamento}"/>
        </rich:column>
        <rich:column rendered="#{!ProdutoRelControle.produtoRel.semProdutos}" sortBy="#{item.validadeProduto}">
            <f:facet name="header">
                <h:outputText value="Validade"/>
            </f:facet>
            <h:outputText value="#{item.validadeProduto_Apresentar}"/>
        </rich:column>
        <rich:column rendered="#{!ProdutoRelControle.produtoRel.semProdutos}" sortBy="#{item.valorProduto}">
            <f:facet name="header">
                <h:outputText value="Valor"/>
            </f:facet>
            <h:outputText value="#{item.valorProduto_Apresentar}"/>
        </rich:column>
        <rich:column rendered="#{ProdutoRelControle.permissaoConsultaTodasEmpresas}" sortBy="#{item.nomeEmpresa}">
            <f:facet name="header">
                <h:outputText value="Empresa"/>
            </f:facet>
            <h:outputText value="#{item.nomeEmpresa}"/>
        </rich:column>
        <rich:column>
            <f:facet name="header">
                <h:outputText value="Opções"/>
            </f:facet>
            <a4j:commandLink id="btnVisualizarCli"  styleClass="linkPadrao texto-cor-azul texto-size-16-real" action="#{ProdutoRelControle.irParaTelaCliente}"
                               oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                <f:param name="state" value="AC"/>
                <i class="fa-icon-search"></i>
            </a4j:commandLink>
        </rich:column>
        <f:facet name="footer">
            <h:outputText rendered="#{!ProdutoRelControle.semProdutos}" style="float: right;"
                          value="Total: #{ProdutoRelControle.valorTotalProduto_Apresentar}"/>
        </f:facet>
    </rich:dataTable>
    <rich:datascroller for="clientes" maxPages="20" page="#{ProdutoRelControle.scrollerPage}" id="scClientes"/>
</h:panelGroup>
</h:form>
</h:panelGrid>
    <script type="text/javascript">
        document.getElementById("form:dataInicio").focus();
        carregarTooltipster();
    </script>
</f:view>


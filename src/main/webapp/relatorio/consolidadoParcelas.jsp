<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="../includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="../bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>
<title>Relatório Consolidado de Parcelas</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@include file="/includes/verificaModulo.jsp" %>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<style type="text/css">
    .notificacaoAtividades {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0 1px 1px 0;
        -webkit-font-smoothing: subpixel-antialiased;
        background-clip: padding-box;
        background: #819aa5 -webkit-linear-gradient(top, #96acb6,
        #5d7b89);
        box-shadow: rgba(0, 0, 0, 0.701961) 0 1px 1px 0;
        color: rgb(255, 255, 255) !important;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        height: 13px !important;
        line-height: normal;
        list-style-type: none;
        padding: 1px 3px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        zoom: 1;
        border-radius: 40%;
    }

    .iconCalendar {
        width: 17px;
        height: 17px;
        padding-top: 6px;
        padding-left: 5px;
    }

    input.inputs, select.inputs {
        padding: 5px;
        background-image: none !important;
        font-size: 12px !important;
        border-radius: 4px;
        color: #A1A5AA;
        border: 1px solid #DCDDDF;
        width: 80%;
    }

    .separador-horizontal {
        margin: 10px 0 10px 0;
        width: 100%;
        height: 1px;
        border-bottom: 1px solid #E5E5E5;
    }

    .tituloCampos {
        color: #51555A !important;
        text-align: right;
    }

    .iconeDentro {
        margin-left: -23px;
    }

    .width100 {
        width: 100% !important;
    }
</style>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <c:set var="titulo" scope="session" value="Relatório Consolidado de Parcelas"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}o-sistema-possui-alguma-ferramenta-que-permita-visualizar-as-inadimplencias-da-minha-empresa/"/>


    <h:panelGrid id="panelFiltros" columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="v20_form" target="_blank">
            <a4j:keepAlive beanName="ExportadorListaControle"/>

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:commandLink action="#{ParcelaConsolidadoRelControle.liberarBackingBeanMemoria}"
                           id="v20_idLiberarBackingBeanMemoria" style="display: none"/>
            <h:commandLink action="#{ParcelaConsolidadoRelControle.imprimirRelatorio}" id="v20_imprimirRelatorio"
                           style="display: none"/>
            <h:inputHidden id="v20_relatorio" value="#{ParcelaConsolidadoRelControle.relatorio}"/>

            <h:panelGroup styleClass="separador-horizontal" layout="block"/>

            <h:panelGrid id="v20_groupForm" columns="6" style="display: inline-flex; margin: 10px;">

                <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                    <h:panelGrid columns="1">
                        <h:outputText styleClass="tituloCampos"
                                      value="Pesquisa por data de vencimento"/>
                    </h:panelGrid>
                    <h:panelGrid columns="2">
                        <h:panelGroup style="font-size: 11px !important;" layout="block">
                            <rich:calendar id="v20_dataInicioVencimento"
                                           value="#{ParcelaConsolidadoRelControle.mesVencimento}"
                                           buttonClass="iconeDentro"
                                           inputClass="form inputs width100"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="v20_dataInicioVencimento" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="2" style="padding-top: 20px;">
                    <a4j:commandLink id="exportar"
                                     ajaxSingle="false"
                                     actionListener="#{ParcelaConsolidadoRelControle.exportar}"
                                     styleClass="botaoPrimario texto-size-14"
                                     reRender="v20_form, v20_GridListagemParcelas"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','ConsolidadoParcelas', 800,200);#{ExportadorListaControle.msgAlert};#{ParcelaConsolidadoRelControle.mensagemNotificar}"
                                     value="Exportar">
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos" value="#{ParcelaConsolidadoRelControle.atributos}"/>
                        <f:attribute name="prefixo" value="ParcelaConsolidadoRelControle"/>
                        <h:outputText title="Exportar para o formato Excel" style="margin-left:8px; color: white" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>

                </h:panelGrid>

            </h:panelGrid>


        </h:form>
    </h:panelGrid>
</f:view>

<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="SGP - Modalidades com turma"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <c:set var="titulo" scope="session" value="SGP - Modalidades com turma"/>
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlBaseConhecimento}relatorio-sgp-modalidades-com-turma/"/>

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="form">
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh"
                          styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <h:panelGrid columns="2" width="100%" style="border:0;">
                                    <h:panelGroup style="text-align:left;">
                                        <h:outputText id="filtros" styleClass="tituloCamposAzul"
                                                      value="Veja os Filtros Utilizados "/>
                                        <rich:toolTip onclick="true" followMouse="true" direction="top-right"
                                                      for="filtros"
                                                      style=" background-color: #cedfff; border-color: #000000; text-align: left;"
                                                      showDelay="500">
                                            ${SGPModalidadeComTurmaControle.filtros}
                                        </rich:toolTip>
                                    </h:panelGroup>
                                    <h:panelGroup id="botoes" style="text-align:right;" layout="block">
                                        <a4j:commandLink id="exportarExcel"
                                                         actionListener="#{ExportadorListaControle.exportar}"
                                                         rendered="#{not empty SGPModalidadeComTurmaControle.listaFrequencias}"
                                                         oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                         accesskey="2" styleClass="linkPadrao">
                                            <f:attribute name="lista"
                                                         value="#{SGPModalidadeComTurmaControle.listaFrequencias}"/>
                                            <f:attribute name="tipo" value="xls"/>
                                            <f:attribute name="atributos" value="#{SGPModalidadeComTurmaControle.atributosDinamicos}"/>
                                            <f:attribute name="prefixo" value="FrequenciaPorTurma"/>
                                            <h:outputText title="Exportar para o formato Excel"
                                                          styleClass="btn-print-2 excel"/>
                                        </a4j:commandLink>
                                        <%--BOTÃO PDF--%>
                                        <a4j:commandLink id="exportarPdf"
                                                         style="margin-left: 8px;"
                                                         actionListener="#{ExportadorListaControle.exportar}"
                                                         rendered="#{not empty SGPModalidadeComTurmaControle.listaFrequencias}"
                                                         oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                         accesskey="2" styleClass="linkPadrao">
                                            <f:attribute name="lista"
                                                         value="#{SGPModalidadeComTurmaControle.listaFrequencias}"/>
                                            <f:attribute name="tipo" value="pdf"/>
                                            <f:attribute name="atributos" value="#{SGPModalidadeComTurmaControle.atributosDinamicos}"/>
                                            <f:attribute name="prefixo" value="FrequenciaPorTurma"/>
                                            <h:outputText title="Exportar para o formato PDF"
                                                          styleClass="btn-print-2 PDF"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </h:panelGrid>

                                <rich:dataTable width="100%" value="#{SGPModalidadeComTurmaControle.listaFrequencias}"
                                                var="item" id="relatorio" style="margin-top: 8px"
                                                rendered="#{not empty SGPModalidadeComTurmaControle.listaFrequencias}">
                                    <rich:column id="modalidade" sortBy="#{item.nomeModalidade}">
                                        <f:facet name="header">
                                            <h:outputText value="Modalidade"/>
                                        </f:facet>
                                        <h:outputText value="#{item.nomeModalidade}"/>
                                    </rich:column>
                                    <rich:column id="turma" sortBy="#{item.nomeTurma}" rendered="#{SGPModalidadeComTurmaControle.visualizacaoDetalhadaPorTurma}">
                                        <f:facet name="header">
                                            <h:outputText value="Turma"/>
                                        </f:facet>
                                        <h:outputText value="#{item.nomeTurma}"/>
                                    </rich:column>
                                    <rich:column id="naoSocio" sortBy="#{item.qtdeNaoSocio}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Não Sócio"/>
                                        </f:facet>
                                        <h:outputText rendered="#{item.qtdeNaoSocio <= 0 or item.modalidadeVO.nome eq 'TOTAIS'}" value="#{item.qtdeNaoSocio}"/>
                                        <a4j:commandLink rendered="#{item.qtdeNaoSocio > 0 and item.modalidadeVO.nome ne 'TOTAIS'}" value="#{item.qtdeNaoSocio}"
                                                         action="#{SGPModalidadeComTurmaControle.prepararListaNaoSocio}"
                                                         oncomplete="Richfaces.showModalPanel('mdlClientes');"
                                                         reRender="formMdlClientes"/>
                                    </rich:column>
                                    <rich:column id="aluno" sortBy="#{item.qtdeAlunos}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Alunos"/>
                                        </f:facet>
                                        <h:outputText rendered="#{item.qtdeAlunos <= 0 or item.modalidadeVO.nome eq 'TOTAIS'}" value="#{item.qtdeAlunos}"/>
                                        <a4j:commandLink rendered="#{item.qtdeAlunos > 0 and item.modalidadeVO.nome ne 'TOTAIS'}" value="#{item.qtdeAlunos}"
                                                         action="#{SGPModalidadeComTurmaControle.prepararListaAluno}"
                                                         oncomplete="Richfaces.showModalPanel('mdlClientes');"
                                                         reRender="formMdlClientes"/>
                                    </rich:column>
                                    <rich:column id="socio" sortBy="#{item.qtdeSocios}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Sócios"/>
                                        </f:facet>
                                        <h:outputText rendered="#{item.qtdeSocios <= 0 or item.modalidadeVO.nome eq 'TOTAIS'}" value="#{item.qtdeSocios}"/>
                                        <a4j:commandLink rendered="#{item.qtdeSocios > 0 and item.modalidadeVO.nome ne 'TOTAIS'}" value="#{item.qtdeSocios}"
                                                         action="#{SGPModalidadeComTurmaControle.prepararListaSocio}"
                                                         oncomplete="Richfaces.showModalPanel('mdlClientes');"
                                                         reRender="formMdlClientes"/>
                                    </rich:column>
                                    <rich:column id="comerciario" sortBy="#{item.qtdeComerciario}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Comerciários"/>
                                        </f:facet>
                                        <h:outputText rendered="#{item.qtdeComerciario <= 0 or item.modalidadeVO.nome eq 'TOTAIS'}" value="#{item.qtdeComerciario}"/>
                                        <a4j:commandLink rendered="#{item.qtdeComerciario > 0 and item.modalidadeVO.nome ne 'TOTAIS'}" value="#{item.qtdeComerciario}"
                                                         action="#{SGPModalidadeComTurmaControle.prepararListaComerciario}"
                                                         oncomplete="Richfaces.showModalPanel('mdlClientes');"
                                                         reRender="formMdlClientes"/>
                                    </rich:column>
                                    <rich:column id="dependente" sortBy="#{item.qtdeDependente}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Dependentes"/>
                                        </f:facet>
                                        <h:outputText rendered="#{item.qtdeDependente <= 0 or item.modalidadeVO.nome eq 'TOTAIS'}" value="#{item.qtdeDependente}"/>
                                        <a4j:commandLink rendered="#{item.qtdeDependente > 0 and item.modalidadeVO.nome ne 'TOTAIS'}" value="#{item.qtdeDependente}"
                                                         action="#{SGPModalidadeComTurmaControle.prepararListaDependente}"
                                                         oncomplete="Richfaces.showModalPanel('mdlClientes');"
                                                         reRender="formMdlClientes"/>
                                    </rich:column>
                                    <rich:column id="usuario" sortBy="#{item.qtdeUsuario}" style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Usuários"/>
                                        </f:facet>
                                        <h:outputText rendered="#{item.qtdeUsuario <= 0 or item.modalidadeVO.nome eq 'TOTAIS'}" value="#{item.qtdeUsuario}"/>
                                        <a4j:commandLink rendered="#{item.qtdeUsuario > 0 and item.modalidadeVO.nome ne 'TOTAIS'}" value="#{item.qtdeUsuario}"
                                                         action="#{SGPModalidadeComTurmaControle.prepararListaUsuario}"
                                                         oncomplete="Richfaces.showModalPanel('mdlClientes');"
                                                         reRender="formMdlClientes"/>
                                    </rich:column>
                                    <rich:column id="eventos" sortBy="#{item.qtdeEventos}" style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Eventos"/>
                                        </f:facet>
                                        <h:outputText rendered="#{item.qtdeEventos <= 0 or item.modalidadeVO.nome eq 'TOTAIS'}" value="#{item.qtdeEventos}"/>
                                        <a4j:commandLink rendered="#{item.qtdeEventos > 0 and item.modalidadeVO.nome ne 'TOTAIS'}" value="#{item.qtdeEventos}"
                                                         action="#{SGPModalidadeComTurmaControle.prepararListaEvento}"
                                                         oncomplete="Richfaces.showModalPanel('mdlClientes');"
                                                         reRender="formMdlClientes"/>
                                    </rich:column>
                                    <rich:column id="totalCategoria" sortBy="#{item.totalCategorias}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Total"/>
                                        </f:facet>
                                        <h:outputText style="font-weight: bold" value="#{item.totalCategorias}"/>
                                    </rich:column>
                                    <rich:column id="evasoes" sortBy="#{item.contratosCanceladosDesistentes}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Evasões"/>
                                        </f:facet>
                                        <h:outputText rendered="#{item.contratosCanceladosDesistentes <= 0 or item.modalidadeVO.nome eq 'TOTAIS'}" value="#{item.contratosCanceladosDesistentes}"/>
                                        <a4j:commandLink rendered="#{item.contratosCanceladosDesistentes > 0 and item.modalidadeVO.nome ne 'TOTAIS'}" value="#{item.contratosCanceladosDesistentes}"
                                                         action="#{SGPModalidadeComTurmaControle.prepararListaCanceladosDesistentes}"
                                                         oncomplete="Richfaces.showModalPanel('mdlClientes');"
                                                         reRender="formMdlClientes"/>
                                    </rich:column>
                                    <rich:column id="turmas" sortBy="#{item.qtdTurmasCriadasPeriodo}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Turmas"/>
                                        </f:facet>
                                        <h:outputText value="#{item.qtdTurmasCriadasPeriodo}"/>
                                    </rich:column>
                                    <rich:column id="horasAula" sortBy="#{item.qtdAulasPeriodo}"
                                                 style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Horas/aula"/>
                                        </f:facet>
                                        <h:outputText value="#{item.qtdAulasPeriodo}"/>
                                    </rich:column>
                                    <rich:column id="freq" sortBy="#{item.frequenciaPeriodo}" style="text-align: right">
                                        <f:facet name="header">
                                            <h:outputText value="Frequência"/>
                                        </f:facet>
                                        <h:outputText value="#{item.frequenciaPeriodo}"/>
                                    </rich:column>
                                </rich:dataTable>
                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                                             styleClass="tabMensagens" style="margin-top: 5px;">
                                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes"
                                                 columnClasses="colunaCentralizada" style="margin: 5px;">
                                        <h:commandLink id="voltar" action="#{SGPModalidadeComTurmaControle.voltar}"
                                                       styleClass="pure-button pure-button-small pure-button-primary">
                                            <i class="fa-icon-backward"></i> &nbsp Voltar
                                        </h:commandLink>
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>


    <rich:modalPanel id="mdlClientes" width="400" height="450" shadowOpacity="true" >
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Clientes"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkMdlClientes"/>
                <rich:componentControl for="mdlClientes" attachTo="hidelinkMdlClientes" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formMdlClientes" style="min-height: 80%">
            <rich:dataTable value="#{SGPModalidadeComTurmaControle.listaClientes}" var="cliente" width="100%" id="tblClientes" rows="30">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Matrícula"/>
                    </f:facet>
                    <h:outputText value="#{cliente.matricula}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Nome"/>
                    </f:facet>
                    <h:outputText value="#{cliente.nome_Apresentar}"/>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller for="tblClientes"/>
        </a4j:form>
    </rich:modalPanel>
</f:view>


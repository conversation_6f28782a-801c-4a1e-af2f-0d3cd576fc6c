<%-- 
    Document   : listaAcessoRel
    Created on : 25/10/2010, 10:37:26
    Author     : carla
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Lista de Acessos</title>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .alinharMeio tbody tr td label{
        vertical-align: bottom;
        padding-right: 10px;
    }

</style>
<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ListaAcesso_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Relatorios_de_Acessos#Lista_de_Acessos"/>


    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <rich:modalPanel id="panelCliente" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Cliente"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink2"/>
                <rich:componentControl for="panelCliente" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formCliente" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" >
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%" styleClass="font-size-Em-max">
                    <h:outputText  styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultacliente" value="#{ListaAcessoControleRel.campoConsultarCliente}">
                            <f:selectItems value="#{ListaAcessoControleRel.tipoConsultaComboCliente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaCliente" size="10"  value="#{ListaAcessoControleRel.valorConsultarCliente}"/>
                    <a4j:commandLink  id="btnConsultarCliente" reRender="formCliente:mensagemConsultaCliente, formCliente:resultadoConsultaCliente, formCliente:scResultadoCliente"
                                      action="#{ListaAcessoControleRel.consultarCliente}" styleClass="botaoPrimario texto-size-16"
                                      value="#{msg_bt.btn_consultar}"  title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaCliente" width="100%" styleClass="tabelaSimplesCustom"
                                value="#{ListaAcessoControleRel.listaConsultarCliente}" rows="5" var="cliente">
                    <rich:column headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="NOME"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul" action="#{ListaAcessoControleRel.selecionarCliente}" focus="cliente" reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')" value="#{cliente.pessoa.nome}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink action="#{ListaAcessoControleRel.selecionarCliente}"
                                         styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                         focus="cliente" reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                         title="#{msg.msg_selecionar_dados}">
                            <span class="linkPadrao texto-size-14-real texto-cor-azul">Selecionar </span>
                            <span class="fa-icon-arrow-right"></span>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10"
                                   styleClass="scrollPureCustom" renderIfSinglePage="false"
                                   id="scResultadoCliente" />
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{ListaAcessoControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ListaAcessoControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="../topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{ListaAcessoControleRel.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <a4j:commandLink action="#{ListaAcessoControleRel.imprimirRelatorio}" id="imprimirRelatorio" style="display: none" />
            <h:inputHidden id="relatorio" value="#{ListaAcessoControleRel.relatorio}" />
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  styleClass="tituloCampos" rendered="#{ListaAcessoControleRel.temEmpresa}" value="#{msg_aplic.prt_ListaAcesso_empresa}" />
                    <h:panelGroup rendered="#{ListaAcessoControleRel.temEmpresa}">
                        <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.empresaVO.codigo}" >
                            <f:selectItems value="#{ListaAcessoControleRel.listaDeEmpresa}" />
                            <a4j:support
                                    event="onchange"
                                    action="#{ListaAcessoControleRel.obterEmpresaEscolhida}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_periodoPesquisa}" />
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{ListaAcessoControleRel.listaAcessoRel.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false" >
                                <a4j:support event="onchanged" reRender="form"/>
                            </rich:calendar>
                            <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="12px"/>
                        <h:panelGroup>
                            <h:inputText  id="horaInicial" onkeypress="return mascaraTodos(this.form, 'form:horaInicial', '99:99', event);" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.horarioInicio}" />
                            <h:message for="horaInicial" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="10px"/>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ate}" />
                        <rich:spacer width="10px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{ListaAcessoControleRel.listaAcessoRel.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true">
                                <a4j:support event="onchanged" reRender="form"/>
                            </rich:calendar>
                            <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        </h:panelGroup>
                        <rich:spacer width="12px"/>
                        <h:panelGroup>
                            <h:inputText  id="horaFinal" onkeypress="return mascaraTodos(this.form, 'form:horaFinal', '99:99', event);" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.horarioFim}" />
                            <h:message for="horaFinal" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_faixaHorario}" />
                    <h:panelGroup>
                        <h:outputText  styleClass="tituloCampos" value="" />
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_faixaHoraInicial}" />
                        <h:inputText  id="faixa_horaInicial" onkeypress="return mascaraTodos(this.form, 'form:faixa_horaInicial', '99:99', event);" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.faixaHoraInicial}"/>
                        <h:message for="faixa_horaInicial" styleClass="mensagemDetalhada"/>
                        <rich:spacer width="5px"/>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_faixaHoraFinal}" />
                        <h:panelGroup>
                            <h:inputText  id="faixa_horaFinal"  onkeypress="return mascaraTodos(this.form, 'form:faixa_horaFinal', '99:99', event);" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.faixaHoraFinal}" />
                            <h:message for="faixa_horaFinal" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_tipoConsultaAcesso}" />
                    <h:panelGroup>
                        <h:selectOneRadio id="tipoConsultaPorClienteOuColaborador" styleClass="tituloCampos alinharMeio" style="" value="#{ListaAcessoControleRel.listaAcessoRel.tipoAcessoClienteOuColaborador}" >
                            <f:selectItems value="#{ListaAcessoControleRel.listaSelectItemTipoAcessoClienteOuColaborador}"/>
                            <a4j:support event="onclick"
                                         reRender="form"/>
                        </h:selectOneRadio>
                    </h:panelGroup>
                    <!--Consulta por acesso de cliente-->
                    <c:if test="${ListaAcessoControleRel.listaAcessoRel.tipoAcessoClienteOuColaborador=='CL'}">
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_clienteNome}" />
                        <h:panelGroup>
                            <h:inputText readonly="true" id="nomeCliente" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.clienteVO.pessoa.nome}" />
                            <a4j:commandButton id="consultarCliente" oncomplete="Richfaces.showModalPanel('panelCliente'), setFocus(formCliente,'formCliente:valorConsultarCliente')" alt="Consultar Professor" image="../imagens/informacao.gif" />
                            <rich:spacer width="5px" />
                            <a4j:commandButton id="LimparCliente"  image="../imagens/limpar.gif"  reRender="form:nomeCliente" action="#{ListaAcessoControleRel.limparCampoCliente}"/>
                        </h:panelGroup>
                        <h:outputText  id="nomePlano" styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_plano}" />
                        <h:panelGroup>
                            <h:selectOneMenu  id="plano" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.plano.codigo}" >
                                <f:selectItems  value="#{ListaAcessoControleRel.listaSelectItemPlano}" />
                                <a4j:support
                                        event="onchange"
                                        action="#{ListaAcessoControleRel.obterPlanoEscolhido}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText id="nomeModalidade" styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_modalidade}" />
                        <h:panelGroup>
                            <h:selectOneMenu id="modalidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.modalidade.codigo}" >
                                <f:selectItems  value="#{ListaAcessoControleRel.listaSelectItemModalidade}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText  id="nomeProfessor" styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_professorNome}" />
                        <h:panelGroup>
                            <h:selectOneMenu  id="professor" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                              value="#{ListaAcessoControleRel.listaAcessoRel.professor.codigo}" >
                                <f:selectItems  value="#{ListaAcessoControleRel.listaSelectItemProfessor}" />
                                <a4j:support
                                        event="onchange"
                                        action="#{ListaAcessoControleRel.obterProfessorEscolhido}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_professorVinculo}" />
                        <h:panelGroup>
                            <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{ListaAcessoControleRel.listaAcessoRel.professorVinculo.codigo}" >
                                <f:selectItems  value="#{ListaAcessoControleRel.listaSelectItemProfessor}" />
                                <a4j:support
                                        event="onchange"
                                        action="#{ListaAcessoControleRel.obterProfessorVinculoEscolhido}" />
                            </h:selectOneMenu>
                        </h:panelGroup>


                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_professorTreinoNome}" />
                        <h:panelGroup>
                            <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                             value="#{ListaAcessoControleRel.listaAcessoRel.professorTreino.codigo}" >
                                <f:selectItems  value="#{ListaAcessoControleRel.listaSelectItemProfessorTreino}" />
                                <a4j:support
                                        event="onchange"
                                        action="#{ListaAcessoControleRel.obterProfessorTreinoEscolhido}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText  id="nomeGrupo" styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_grupo}" />
                        <h:panelGroup>
                            <h:selectOneMenu  id="grupos" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                              value="#{ListaAcessoControleRel.listaAcessoRel.grupo.codigo}" >
                                <f:selectItems  value="#{ListaAcessoControleRel.grupos}" />
                                <a4j:support
                                        event="onchange"
                                        action="#{ListaAcessoControleRel.obterGrupo}" />
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </c:if>
                    <!--Consulta por acesso de colaborador-->
                    <c:if test="${ListaAcessoControleRel.listaAcessoRel.tipoAcessoClienteOuColaborador == 'CO'}">
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_colaboradorNome}" />
                        <h:panelGroup>
                            <h:selectOneMenu  id="colaborador" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{ListaAcessoControleRel.listaAcessoRel.colaborador.codigo}" >
                                <f:selectItems  value="#{ListaAcessoControleRel.listaSelectItemColaborador}" />
                                <a4j:support
                                        event="onchange"
                                        action="#{ListaAcessoControleRel.obterColaboradorEscolhido}" />
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </c:if>
                    <h:outputText  id="campoOrdenar" styleClass="tituloCampos" value="Ordernar por:" />
                    <h:panelGroup>
                        <h:selectOneRadio  id="campos"  onfocus="focusinput(this);" styleClass="tituloCampos alinharMeio"
                                           value="#{ListaAcessoControleRel.campoOrdenarSelecionado}" >
                            <f:selectItems  value="#{ListaAcessoControleRel.camposOrdenacao}" />
                        </h:selectOneRadio>
                    </h:panelGroup>
                    <h:outputText  id="primeiroAcesso" styleClass="tituloCampos" value="Exibir somente o primeiro acesso por dia:" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox styleClass="tituloCampos alinharMeio"
                                                 value="#{ListaAcessoControleRel.campoSomentePrimeiroAcesso}">
                            <a4j:support event="onchange" ajaxSingle="true"/>
                        </h:selectBooleanCheckbox>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{ListaAcessoControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ListaAcessoControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <rich:spacer width="20px"/>
                            <a4j:commandLink id="imprimirPDF" ajaxSingle="false" style="margin-right: 10px"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{ListaAcessoControleRel.imprimirPDF}"
                                             oncomplete="abrirPopupPDFImpressao('#{ListaAcessoControleRel.nomeArquivoRelatorioGeradoAgora}','ListaDeAcessos', 780, 595);"
                                             accesskey="2" reRender="form">
                                &nbsp Gerar Relatório PDF
                            </a4j:commandLink>
                            <a4j:commandLink id="imprimirExcel"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{ListaAcessoControleRel.imprimirExcel}"
                                             oncomplete="location.href='#{ListaAcessoControleRel.urlUploadArquivo}'"
                                             accesskey="2">
                                &nbsp Gerar Relatório EXCEL
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>


<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Histórico Dotz"/>
    </title>

    <c:set var="titulo" scope="session" value="Histórico Dotz"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:HistoricoDotz"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>
        <h:form id="form">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid id="gridFiltros" columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText styleClass="tituloCampos" rendered="#{HistoricoPontosParceiroFidelidadeControle.temEmpresa}"
                                  value="Empresa"/>
                    <h:panelGroup rendered="#{HistoricoPontosParceiroFidelidadeControle.temEmpresa}">
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{HistoricoPontosParceiroFidelidadeControle.empresa.codigo}">
                            <f:selectItems value="#{HistoricoPontosParceiroFidelidadeControle.listaDeEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_periodo}:"/>
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{HistoricoPontosParceiroFidelidadeControle.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="5px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_ate}"/>
                        <rich:spacer width="5px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{HistoricoPontosParceiroFidelidadeControle.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <rich:jQuery id="mskData2" selector=".rich-calendar-input" timing="onload"
                                         query="mask('99/99/9999')"/>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>

                        <rich:spacer width="10px"/>
                        <a4j:commandLink id="mes" action="#{HistoricoPontosParceiroFidelidadeControle.selecionarMesAtual}"
                                         value=" Selecionar Mês Atual  "
                                         styleClass="pure-button pure-button-small texto-size-12-real"
                                         accesskey="1" reRender="form">
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:outputText id="tipoPessoa" styleClass="tituloCampos" value="Cliente:"/>
                    <h:panelGroup layout="block" id="panelSuggestionPessoa">
                        <h:inputText id="nomeCliente" size="50"
                                     maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"/>

                        <rich:suggestionbox height="200" width="200"
                                            for="nomeCliente"
                                            fetchValue="#{result.nome}"
                                            suggestionAction="#{HistoricoPontosParceiroFidelidadeControle.executarAutocompleteConsultaPessoa}"
                                            minChars="1" rowClasses="20"
                                            status="statusHora"
                                            nothingLabel="#{msg_aplic.prt_nenhuma_pessoa_encontrada}"
                                            var="result" id="suggestionNomeCliente" reRender="mensagem">
                            <a4j:support event="onselect"
                                         reRender="panelSuggestionPessoa,mensagem"
                                         action="#{HistoricoPontosParceiroFidelidadeControle.selecionarPessoaSuggestionBox}"/>
                            <h:column>
                                <h:outputText value="#{result.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                        <a4j:commandButton id="limparAluno"
                                           onclick="document.getElementById('form:nomeCliente').value = null;"
                                           image="/images/limpar.gif" title="#{msg_aplic.prt_limpar_aluno}."
                                           status="false"
                                           reRender="panelSuggestionPessoa,mensagem"
                                           action="#{HistoricoPontosParceiroFidelidadeControle.limparAluno}"/>
                    </h:panelGroup>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem" value="#{HistoricoPontosParceiroFidelidadeControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoPontosParceiroFidelidadeControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGroup layout="block" id="panelConsultar" style="text-align: center; padding: 15px">
                    <a4j:commandLink id="consultar"
                                     oncomplete="#{HistoricoPontosParceiroFidelidadeControle.mensagemNotificar}"
                                     action="#{HistoricoPontosParceiroFidelidadeControle.consultar}"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     accesskey="2" reRender="mensagem">
                        <i class="fa-icon-search"></i> &nbsp Consultar
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

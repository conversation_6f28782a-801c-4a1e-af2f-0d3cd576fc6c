<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <script type="text/javascript" language="javascript" src="../hoverform.js"></script>
</head>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="SGP - Mapa Estatístico"/>
    </title>

    <c:set var="titulo" scope="session" value="SGP - Mapa Estatístico"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlBaseConhecimento}relatorio-sgp-sgp-mapa-estatistico/"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">
                    <!-- tipoRel -->
                    <h:outputText  styleClass="tituloCampos" value="MODELO RELATÓRIO:"/>
                    <h:panelGroup styleClass="font-size-em-max">
                        <h:selectOneMenu id="modeloRel" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{SGPTurmaControle.modeloRelatorioSelecionado}">
                            <f:selectItems value="#{SGPTurmaControle.modeloRel}"/>
                            <a4j:support event="onchange" reRender="form" oncomplete="carregarMaskInput();"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="PERÍODO:"/>
                    <h:panelGroup>
                        <rich:calendar id="dataInicio" inputSize="10" inputClass="form"
                                       value="#{SGPTurmaControle.dataInicio}"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2"
                                       showWeeksBar="false"/>

                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_ClientePorAniversario_ate}:"/>

                        <rich:calendar id="dataFim" inputSize="10" inputClass="form"
                                       value="#{SGPTurmaControle.dataFim}"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2"
                                       showWeeksBar="false"/>
                        <rich:jQuery selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')"/>
                    </h:panelGroup>

                    <c:if test="${SGPTurmaControle.modeloRelatorioSelecionado eq 0}">
                        <!-- modalidade -->
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_modalidade}"/>
                        <h:panelGroup styleClass="font-size-em-max">
                            <h:selectOneMenu id="modalidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{SGPTurmaControle.modalidade.codigo}">
                                <f:selectItems value="#{SGPTurmaControle.listaModalidade}"/>
                                <a4j:support event="onchange" action="#{SGPTurmaControle.montarListaTurma}"
                                             reRender="form" oncomplete="carregarMaskInput();"/>
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <!-- turma -->
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_turma}"/>
                        <h:panelGroup styleClass="font-size-em-max">
                            <h:selectOneMenu id="turma" onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" value="#{SGPTurmaControle.turma.codigo}">
                                <f:selectItems value="#{SGPTurmaControle.listaTurma}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <!-- vezesSemanaRel -->
                        <h:outputText  styleClass="tituloCampos" value="VEZES NA SEMANA:"/>
                        <h:panelGroup styleClass="font-size-em-max">
                            <h:selectOneMenu id="vezesSemanaRel" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{SGPTurmaControle.vezesSemanaRelatorioSelecionado}">
                                <f:selectItems value="#{SGPTurmaControle.vezesSemanaRel}"/>
                                <a4j:support event="onchange" reRender="form" oncomplete="carregarMaskInput();"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </c:if>
                    <!-- tipoRel -->
                    <h:outputText  styleClass="tituloCampos" value="TIPO RELATÓRIO:"/>
                    <h:panelGroup styleClass="font-size-em-max">
                        <h:selectOneMenu id="tipoRel" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{SGPTurmaControle.tipoRelatorioSelecionado}">
                            <f:selectItems value="#{SGPTurmaControle.tipoRel}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem" value="#{SGPTurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" style="font-size: 14px"
                                      value="#{SGPTurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes"
                                 columnClasses="colunaCentralizada" style="margin-bottom: 10px">
                        <h:panelGroup>
                            <a4j:commandLink id="consultarTurma" reRender="mensagem"
                                             styleClass="botoes nvoBt"
                                             action="#{SGPTurmaControle.consultar}"
                                             accesskey="2">
                                <i class="fa-icon-print"></i> Gerar Relatório
                            </a4j:commandLink>
                            <a4j:commandLink id="consultarTurma2" reRender="mensagem"
                                             styleClass="botoes nvoBt"
                                             oncomplete="#{SGPTurmaControle.msgAlert}"
                                             action="#{SGPTurmaControle.consultarExcel}"
                                             title="Ao gerar o excel neste botão, o sistema não leva em conta o filtro \"TIPO RELATÓRIO\""
                                             accesskey="2">
                                <i class="fa-icon-print"></i> Gerar Excel Geral
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

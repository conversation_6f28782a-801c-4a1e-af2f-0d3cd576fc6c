<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>
  Date: 28/04/2020
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../css/pacto_flat_2.15.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="../hoverform.js"></script>
    <link href="../css/otimize.css" rel="stylesheet" type="text/css">
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
</head>
<title>Clientes com cobrança bloqueada</title>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .alinharMeio tbody tr td label {
        vertical-align: bottom;
        padding-right: 10px;
    }

</style>
<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Clientes com cobrança automática bloqueada"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-obter-um-relatorio-dos-clientes-com-cobranca-automatica-bloqueada/"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" columnClasses="colunaTopCentralizada" width="100%">
                    <h:panelGrid columns="2" rowClasses="linhaImpar"
                                 columnClasses="classEsquerda, classDireita" width="100%" style="padding-left:0px;">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      value="DATA BLOQUEIO:"/>
                        <h:panelGroup styleClass="flex" layout="block">
                            <div class="margenVertical">
                                <h:panelGroup styleClass="dateTimeCustom">
                                    <rich:calendar id="dataInicio"
                                                   value="#{RelatorioPessoasBloqueioCobrancaControle.dataInicio}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"
                                                   buttonIcon="/imagens_flat/calendar-button.svg">
                                    </rich:calendar>
                                </h:panelGroup>
                                <rich:spacer width="5px"/>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              style="margin: 7px 9px 0 0px;margin-left: 15px;"
                                              value="#{msg_aplic.prt_ate_Maisculo}"/>
                                <rich:spacer width="10px"/>
                                <h:panelGroup styleClass="dateTimeCustom">
                                    <rich:calendar id="dataFinal"
                                                   value="#{RelatorioPessoasBloqueioCobrancaControle.dataFinal}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"
                                                   buttonIcon="/imagens_flat/calendar-button.svg">
                                    </rich:calendar>
                                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                                 query="mask('99/99/9999')"/>
                                </h:panelGroup>
                                <rich:spacer width="12px"/>
                            </div>
                        </h:panelGroup>

                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      value="EMPRESA:"/>
                        <h:panelGroup styleClass="font-size-em-max">
                            <h:panelGroup styleClass="cb-container margenVertical" layout="block">
                                <h:panelGroup>
                                    <h:selectOneMenu value="#{RelatorioPessoasBloqueioCobrancaControle.empresaFiltro}">
                                        <f:selectItems
                                                value="#{RelatorioPessoasBloqueioCobrancaControle.listaSelectItemEmpresa}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value=""/>
                        <h:panelGroup layout="block">
                            <a4j:commandLink id="consultarClientes" ajaxSingle="false"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{RelatorioPessoasBloqueioCobrancaControle.consultarClientes}"
                                             oncomplete="#{RelatorioPessoasBloqueioCobrancaControle.mensagemNotificar}"
                                             accesskey="2" reRender="form">
                                Consultar&nbsp<i class="fa-icon-search"></i>
                            </a4j:commandLink>

                            <a4j:commandLink id="excel"
                                             rendered="#{RelatorioPessoasBloqueioCobrancaControle.listaTotal > 0}"
                                             style="margin-left: 15px; vertical-align: inherit;"
                                             actionListener="#{ExportadorListaControle.exportar}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                             accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{RelatorioPessoasBloqueioCobrancaControle.lista}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="itemExportacao" value="relCobrancaAutomaticaBloqueada"/>
                                <f:attribute name="atributos"
                                             value="matricula=Matricula,nome=Nome,dataBloqueioApresentar=Dt. Bloqueio,tipoBloqueioCobrancaApresentar=Tipo Bloqueio,telefones=Telefone,emails=Email,empresaNome=Empresa"/>
                                <f:attribute name="prefixo" value="ClientesBloqueados"/>
                                <h:outputText title="Exportar para o formato Excel"
                                              styleClass="btn-print-2 excel tooltipster"/>
                            </a4j:commandLink>
                        </h:panelGroup>

                        <c:if test="${RelatorioPessoasBloqueioCobrancaControle.consultou}">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          style="padding: 5px"
                                          value=""/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          style="padding: 5px"
                                          value=""/>

                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          value="TOTAL:"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{RelatorioPessoasBloqueioCobrancaControle.listaTotal} cliente(s)"/>
                        </c:if>

                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      style="padding: 5px"
                                      value=""/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      style="padding: 5px"
                                      value=""/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%"
                         rendered="#{not empty RelatorioPessoasBloqueioCobrancaControle.lista}">
                <rich:dataTable id="tblRetorno" width="100%" styleClass="tabelaSimplesCustom"
                                rowKeyVar="status" rows="20"
                                value="#{RelatorioPessoasBloqueioCobrancaControle.lista}" var="cliente">


                    <rich:column styleClass="col-text-align-left"
                                 headerClass="col-text-align-left" sortBy="#{cliente.matricula}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText
                                    styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                    value="Matrícula"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                      value="#{cliente.matricula}"/>
                    </rich:column>

                    <rich:column styleClass="col-text-align-left"
                                 headerClass="col-text-align-left" sortBy="#{cliente.nome}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText
                                    styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                    value="Nome"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                      value="#{cliente.nome}"/>
                    </rich:column>

                    <rich:column styleClass="col-text-align-left"
                                 headerClass="col-text-align-left" sortBy="#{cliente.dataBloqueio}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText
                                    styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                    value="Dt. Bloqueio"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                      value="#{cliente.dataBloqueioApresentar}"/>
                    </rich:column>

                    <rich:column styleClass="col-text-align-left"
                                 headerClass="col-text-align-left" sortBy="#{cliente.tipoBloqueioCobrancaApresentar}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText
                                    styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                    value="Tipo Bloqueio"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                      value="#{cliente.tipoBloqueioCobrancaApresentar}"/>
                    </rich:column>

                    <rich:column styleClass="col-text-align-left"
                                 headerClass="col-text-align-left" sortBy="#{cliente.empresaNome}"
                                 filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText
                                    styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                    value="Empresa"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                      value="#{cliente.empresaNome}"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller for="tblRetorno" maxPages="20"
                                   page="#{RelatorioPessoasBloqueioCobrancaControle.scrollerPage}"
                                   id="sc2"/>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                    <h:outputText styleClass="mensagem" value="#{RelatorioPessoasBloqueioCobrancaControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{RelatorioPessoasBloqueioCobrancaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
    jQuery.noConflict();
</script>


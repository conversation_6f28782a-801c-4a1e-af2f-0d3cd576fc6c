<%-- 
    Document   : faturamentoSinteticoResumoPessoa
    Created on : 26/10/2009, 18:48:13
    Author     : pedro
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_CompetenciaSintetico_tituloFormPessoas}"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="../topoReduzido.jsp"/>

        </f:facet>
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid columns="1" style="height=25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_CompetenciaSintetico_tituloFormPessoas}  - Total: #{fn:length(CompetenciaSinteticoControleRel.competenciaSinteticoProdutoMesVO.listaResumoPessoa)}"/>
                    </h:panelGrid>
                    <h:panelGrid width="100%" style="margin-bottom: 8px;text-align: right;">
                        <h:panelGroup layout="block">
                            <%--BOTÃO EXCEL--%>
                            <a4j:commandButton id="exportarExcel"
                                               image="../imagens/btn_excel.png"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty CompetenciaSinteticoControleRel.listaResumo}"
                                               value="Excel"
                                               oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{CompetenciaSinteticoControleRel.listaResumo}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="itemExportacao" value="competenciaRel"/>
                                <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,pessoaCPF=CPF,contrato_Apresentar=N° Contrato,dataInicio_Apresentar=Data Início,dataAte_Apresentar=Data Término,duracao_Apresentar=Duração,modalidade_Apresentar=Modalidades,plano_Apresentar=Plano,mesLancamentoMovProduto_Apresentar=Mês de Lançamento,valor=Valor"/>
                                <f:attribute name="prefixo" value="CompetenciaMovimentacaoPessoa"/>
                            </a4j:commandButton>
                            <%--BOTÃO PDF--%>
                            <a4j:commandButton id="exportarPdf"
                                               style="margin-left: 8px;"
                                               image="../imagens/imprimir.png"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty CompetenciaSinteticoControleRel.listaResumo}"
                                               value="PDF"
                                               oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="botoes">
                                <f:attribute name="lista" value="#{CompetenciaSinteticoControleRel.listaResumo}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="itemExportacao" value="competenciaRel"/>
                                <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,pessoaCPF=CPF,contrato_Apresentar=N° Contrato,dataInicio_Apresentar=Data Início,dataAte_Apresentar=Data Término,duracao_Apresentar=Duração,modalidade_Apresentar=Modalidades,plano_Apresentar=Plano,mesLancamentoMovProduto_Apresentar=Mês de Lançamento,valor=Valor"/>
                                <f:attribute name="prefixo" value="CompetenciaMovimentacaoPessoa"/>
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable id="listaPessoa" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                    value="#{CompetenciaSinteticoControleRel.listaResumo}" rows="100" var="resumoPessoa"  rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Matrícula" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.cliente.matricula}"
                                          rendered="#{resumoPessoa.cliente.codigo!=null && resumoPessoa.cliente.codigo!=0}"/>
                            <h:outputText rendered="#{resumoPessoa.colaboradorVO.codigo!=null && resumoPessoa.colaboradorVO.codigo!=0}"
                                          value="#{resumoPessoa.colaboradorVO.codAcesso}" />
                        </rich:column>
                        <rich:column >
                            <f:facet name="header">
                                <h:outputText value="Nome" />
                            </f:facet>
                            <h:outputText rendered="#{resumoPessoa.cliente.codigo!=null && resumoPessoa.cliente.codigo!=0}"
                                          value="#{resumoPessoa.cliente.pessoa.nome}" />
                            <h:outputText value="#{resumoPessoa.colaboradorVO.pessoa.nome}"
                                          rendered="#{resumoPessoa.colaboradorVO.codigo!=null && resumoPessoa.colaboradorVO.codigo!=0}"/>
                        </rich:column>

                        <rich:column >
                            <f:facet name="header">
                                <h:outputText value="CPF" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.pessoaCPF}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="N° Contrato" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.codigo}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Data Início" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.vigenciaDe_Apresentar}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Data Término" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.vigenciaAteAjustada_Apresentar}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Duração" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.contratoDuracao.numeroMeses}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Modalidades" />
                            </f:facet>

                            <a4j:commandLink rendered="#{resumoPessoa.apresentarNumeroModalidades}" action="#{CompetenciaSinteticoControleRel.visualizarModalidades}"
                                              oncomplete="abrirPopup('../relatorio/include_contratoModalidade.jsp', 'ContratoModalidade',850,650);"  >
                                <f:setPropertyActionListener target="#{CompetenciaSinteticoControleRel.contratoSelecionado}"
                                                             value="#{resumoPessoa.contrato.codigo}"/>
                                <h:outputText value="#{resumoPessoa.numeroModalidades}" />
                            </a4j:commandLink>
                            <h:outputText rendered="#{!resumoPessoa.apresentarNumeroModalidades}" value="#{resumoPessoa.numeroModalidades}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Plano" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.contrato.plano.descricao}" />
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Mês de Lançamento" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.mesLancamentoMovProduto_Apresentar}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Valor" />
                            </f:facet>
                            <h:outputText value="#{resumoPessoa.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </rich:column>
                        <rich:column >
                            <f:facet name="header">
                                <h:outputText value="Opção"/>
                            </f:facet>
                            <a4j:commandButton id="visualizarCliente" 
                                               rendered="#{resumoPessoa.cliente.codigo!=null && resumoPessoa.cliente.codigo!=0}"
                                               action="#{CompetenciaSinteticoControleRel.irParaTelaCliente}"
                                               alt="Ir para tela de Edição do Cliente" image="../imagens/botaoVisualizar.png"
                                               oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <f:param name="state" value="AC"/>
                            </a4j:commandButton>
                            <a4j:commandButton id="visualizarColaborador" 
                                               rendered="#{resumoPessoa.colaboradorVO.codigo!=null && resumoPessoa.colaboradorVO.codigo!=0}"
                                               action="#{CompetenciaSinteticoControleRel.irParaTelaColaborador}"
                                               alt="Ir para tela de Edição do Colaborador" image="../imagens/botaoVisualizar.png"
                                               oncomplete="abrirPopup('../colaboradorForm.jsp', 'Colaborador', 1024, 700);">
                                <f:param name="state" value="AC"/>
                            </a4j:commandButton>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller
                        align="center"
                        for="form:listaPessoa"
                        id="scResultadoConsultaListaPessoa"
                        rendered="#{!emptyCompetenciaSinteticoControleRel.competenciaSinteticoProdutoMesVO.listaResumoPessoa}" />
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>


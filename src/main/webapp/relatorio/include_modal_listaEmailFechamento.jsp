<%-- 
    Document   : include_modal_listaEmailFechamento
    Created on : 27/01/2012, 11:24
    Author     : carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" src="../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<a4j:outputPanel>
    <rich:modalPanel id="modalListaEmail" autosized="true" shadowOpacity="true" width="700" height="200" showWhenRendered="#{FechamentoAcessosControleRel.mostrarModalPanelListaEmail}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText styleClass="tituloCamposNegrito" value="Lista de Email"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form>
                    <a4j:commandButton image="/imagens/close.png" style="cursor:pointer"
                                       action="#{FechamentoAcessosControleRel.fecharModalListaEmail}"
                                       oncomplete="#{rich:component('modalListaEmail')}.hide();"
                                       id="hidelink3" reRender="form"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>
        <h:form id="formListaEmail">
            <rich:panel>
                <h:outputText styleClass="tituloCampos" value="Informe os emails que receberão o relatório de Fechamento de Controle de Acesso"/>
                <h:panelGrid columns="3"
                             cellpadding="0" cellspacing="5" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classCentralizada, classDireita" >
                    <h:outputText styleClass="tituloCampos" value="Emails:"/>
                    <h:inputText value="#{FechamentoAcessosControleRel.configuracaoSistemaVO.emailsFechamentoAcessos}" size="50" maxlength="1000"/>
                    <h:outputText styleClass="mensagemAzul" value="Obs.: Utilize ';' para informar mais de um email"/>
                </h:panelGrid>
                <h:panelGrid columns="2" columnClasses="colunaDireita,colunaDireita" width="70%">
                    <a4j:commandButton
                        value="Gravar" image="../imagens/btn_gravar.png" reRender="mensagem"
                        action="#{FechamentoAcessosControleRel.gravarEmails}" oncomplete="#{rich:element('nome')}.focus();"/>
                    <a4j:commandButton  action="#{FechamentoAcessosControleRel.realizarConsultaLogAlteracaoEmails}"
                                        oncomplete="abrirPopup('../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                        image="/imagens/botalVisualizarLog.png" alt="Visualizar LOG" title="Visualizar Log"
                                        styleClass="botoes"/>
                </h:panelGrid>
                <rich:spacer width="5"/>
                <h:panelGrid columns="2" width="100%" styleClass="tabMensagens">
                    <h:panelGroup id="mensagemErro">
                        <a4j:commandButton rendered="#{FechamentoAcessosControleRel.sucesso}"
                                           image="/imagens/sucesso.png" />
                        <a4j:commandButton rendered="#{FechamentoAcessosControleRel.erro}"
                                           image="/imagens/erro.png" />
                    </h:panelGroup>

                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{FechamentoAcessosControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{FechamentoAcessosControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:panel>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>
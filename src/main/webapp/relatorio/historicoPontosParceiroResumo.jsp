<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Histórico Dotz"/>
    </title>

    <c:set var="titulo" scope="session" value="Histórico Dotz"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:HistoricoDotz"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>
        <h:form id="form" >
            <input type="hidden" value="${modulo}" name="modulo"/>

            <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>

            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" >
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" >
                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="border:0;" id="panelFiltrosUtilizados">
                                            <h:outputText styleClass="tituloCamposAzul" value="Filtros" style="font-size: 16px;font-weight: bold;"/>
                                            <h:outputText styleClass="tituloCamposAzul" value="#{HistoricoPontosParceiroFidelidadeControle.filtros}" escape="false"/>
                                        </h:panelGrid>
                                        <h:panelGrid width="100%">
                                            <h:panelGroup style="text-align: right" layout="block">
                                                <a4j:commandLink id="btnExcel"
                                                                 styleClass="exportadores"
                                                                 rendered="#{not empty HistoricoPontosParceiroFidelidadeControle.listaHistorico}"
                                                                 actionListener="#{ExportadorListaControle.exportar}"
                                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                 accesskey="3">
                                                    <f:attribute name="lista" value="#{HistoricoPontosParceiroFidelidadeControle.listaHistorico}"/>
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="filtro" value="#{HistoricoPontosParceiroFidelidadeControle.filtrosImpressao}"/>
                                                    <f:attribute name="atributos"
                                                                 value="matricula=Matrícula,nomePessoa=Nome,cpf=CPF,tipoPonto_Apresentar=Tipo Ponto,nomeTabela=Nome Tabela,dataLancamento_Apresentar=Data Lançamento,valor_Apresentar=Valor R$,pontos=Pontos,historico=Descrição"/>
                                                    <f:attribute name="prefixo" value="HistoricoDotz"/>
                                                    <h:outputText title="#{msg_aplic.prt_exportar_form_excel}" styleClass="btn-print-2 excel"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink id="btnPDF"
                                                                 styleClass="exportadores margin-h-10"
                                                                 rendered="#{not empty HistoricoPontosParceiroFidelidadeControle.listaHistorico}"
                                                                 actionListener="#{ExportadorListaControle.exportar}"
                                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                 accesskey="4">
                                                    <f:attribute name="lista" value="#{HistoricoPontosParceiroFidelidadeControle.listaHistorico}"/>
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <f:attribute name="filtro" value="#{HistoricoPontosParceiroFidelidadeControle.filtrosImpressao}"/>
                                                    <f:attribute name="atributos"
                                                                 value="matricula=Matrícula,nomePessoa=Nome,cpf=CPF,tipoPonto_Apresentar=Tipo Ponto,nomeTabela=Nome Tabela,dataLancamento_Apresentar=Data Lançamento,valor_Apresentar=Valor R$,pontos=Pontos,historico=Descrição"/>
                                                    <f:attribute name="prefixo" value="HistoricoDotz"/>
                                                    <f:attribute name="titulo" value="Histórico Dotz"/>
                                                    <h:outputText title="#{msg_aplic.prt_exportar_form_pdf}" styleClass="btn-print-2 pdf"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <rich:dataTable width="100%" styleClass="tabelaSimplesCustom"
                                                        value="#{HistoricoPontosParceiroFidelidadeControle.listaHistorico}" rows="50" id="historico" var="historico" rowKeyVar="status">
                                            <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Matrícula" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.matricula}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.nomePessoa}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="CPF" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.cpf}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Tipo Ponto" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.tipoPonto_Apresentar}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome Tabela" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.nomeTabela}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Data Lançamento" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.dataLancamento_Apresentar}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Valor R$" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.valor_Apresentar}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Pontos" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.pontos}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Descrição" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{historico.historico}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-center" rendered="#{HistoricoPontosParceiroFidelidadeControle.apresentarVoltar}">
                                                <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                                                 action="#{HistoricoPontosParceiroFidelidadeControle.irParaTelaCliente}"
                                                                 oncomplete="abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                    <f:param name="state" value="AC"/>
                                                    <i class="fa-icon-search"></i>
                                                </a4j:commandLink>
                                            </rich:column>
                                        </rich:dataTable>
                                        <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:historico" maxPages="10" id="scAniversarios" />
                                    </h:panelGrid>

                                    <h:panelGroup layout="block" style="text-align: center; padding: 15px">
                                        <h:outputText styleClass="tituloCamposDestaqueNegrito "
                                                      value="Total: #{HistoricoPontosParceiroFidelidadeControle.totalizador}"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="tabMensagens" style="text-align: center"
                                            rendered="#{HistoricoPontosParceiroFidelidadeControle.apresentarVoltar}">
                                            <a4j:commandLink id="voltar"
                                                           action="#{HistoricoPontosParceiroFidelidadeControle.voltar}"
                                                           styleClass="pure-button pure-button-small pure-button-primary">
                                                <i class="fa-icon-backward"></i> &nbsp Voltar
                                            </a4j:commandLink>
                                    </h:panelGroup >
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>
</f:view>


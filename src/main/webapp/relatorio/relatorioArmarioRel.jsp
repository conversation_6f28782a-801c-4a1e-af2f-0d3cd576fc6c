<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 19/10/2015
  Time: 14:33
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
  .classeEsquerda {
    width: 18%;
    text-align: right;
    font-weight: bold;
    vertical-align: middle;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-decoration: none;
    text-transform: none;
    color: #333;
    line-height: 150%;
  }

  .classeDireita {
    width: 25%;
    text-align: left;
  }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
  <title>
    <h:outputText value="#{msg_aplic.prt_RelatorioArmario_tituloForm}"/>
  </title>
  
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_RelatorioArmario_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Relatorio_Armario"/>    
    
  
  <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

  <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
    <f:facet name="header">
      <jsp:include page="../topoReduzido_material.jsp"/>
    </f:facet>
    <h:form id="form">
      <h:panelGrid columns="1" width="100%">
          <hr style="border-color: #e6e6e6;">
          <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                       columnClasses="classeEsquerda, classeDireita" width="100%">

              <h:outputText styleClass="tituloCampos" rendered="#{ArmarioControleRel.temEmpresa}"
                            value="#{msg_aplic.prt_CompetenciaSintetico_empresa}"/>
              <h:panelGroup rendered="#{ArmarioControleRel.temEmpresa}">
                  <rich:spacer width="10"/>
                  <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                   value="#{ArmarioControleRel.armario.empresa.codigo}">
                      <f:selectItems value="#{ArmarioControleRel.listaDeEmpresa}"/>
                  </h:selectOneMenu>
              </h:panelGroup>

              <h:outputText styleClass="tituloCampos" value="Início da Locação "/>
              <h:panelGroup>
                  <h:panelGroup id="mesBase1">
                      <rich:calendar id="dataInicio"
                                     value="#{ArmarioControleRel.filtro.periodoLocacaoDe}"
                                     inputSize="6"
                                     inputClass="form"
                                     oninputblur="blurinput(this);"
                                     oninputfocus="focusinput(this);"
                                     datePattern="dd/MM/yyyy"
                                     enableManualInput="true"
                                     zindex="2"
                                     showWeeksBar="true"/>
                      <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                  </h:panelGroup>
                  <h:panelGroup id="mesBase2">
                      <rich:spacer width="10px"/>
                      <h:outputText styleClass="tituloCampos" value=" até "/>
                      <rich:spacer width="10px"/>
                      <rich:calendar id="dataTermino"
                                     value="#{ArmarioControleRel.filtro.periodoLocacaoAte}"
                                     inputSize="6"
                                     inputClass="form"
                                     oninputblur="blurinput(this);"
                                     oninputfocus="focusinput(this);"
                                     datePattern="dd/MM/yyyy"
                                     enableManualInput="true"
                                     zindex="2"
                                     showWeeksBar="true"/>
                      <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                  </h:panelGroup>
              </h:panelGroup>
              <h:outputText rendered="#{ArmarioControleRel.habilitadoGestaoArmarios}" styleClass="tituloCampos"
                            value="Período de Renovação"/>
              <h:panelGroup rendered="#{ArmarioControleRel.habilitadoGestaoArmarios}">
                  <h:panelGroup>
                      <rich:calendar id="renovacaoInicio"
                                     value="#{ArmarioControleRel.filtro.periodoRenovacaoDe}"
                                     inputSize="6"
                                     inputClass="form"
                                     oninputblur="blurinput(this);"
                                     oninputfocus="focusinput(this);"
                                     datePattern="dd/MM/yyyy"
                                     enableManualInput="true"
                                     zindex="2"
                                     showWeeksBar="true"/>
                      <h:message for="renovacaoInicio" styleClass="mensagemDetalhada"/>
                  </h:panelGroup>
                  <h:panelGroup>
                      <rich:spacer width="10px"/>
                      <h:outputText styleClass="tituloCampos" value=" até "/>
                      <rich:spacer width="10px"/>
                      <rich:calendar id="renovacaoTerminio"
                                     value="#{ArmarioControleRel.filtro.periodoRenovacaoAte}"
                                     inputSize="6"
                                     inputClass="form"
                                     oninputblur="blurinput(this);"
                                     oninputfocus="focusinput(this);"
                                     datePattern="dd/MM/yyyy"
                                     enableManualInput="true"
                                     zindex="2"
                                     showWeeksBar="true"/>
                      <h:message for="renovacaoTerminio" styleClass="mensagemDetalhada"/>
                  </h:panelGroup>
              </h:panelGroup>
              <h:outputText styleClass="tituloCampos" value="Período de Vencimento"/>
              <h:panelGroup>
                  <h:panelGroup>
                      <rich:calendar id="vencimentoDe"
                                     value="#{ArmarioControleRel.filtro.periodoVencimentoDe}"
                                     inputSize="6"
                                     inputClass="form"
                                     oninputblur="blurinput(this);"
                                     oninputfocus="focusinput(this);"
                                     datePattern="dd/MM/yyyy"
                                     enableManualInput="true"
                                     zindex="2"
                                     showWeeksBar="true"/>
                      <h:message for="vencimentoDe" styleClass="mensagemDetalhada"/>
                  </h:panelGroup>
                  <h:panelGroup>
                      <rich:spacer width="10px"/>
                      <h:outputText styleClass="tituloCampos" value=" até "/>
                      <rich:spacer width="10px"/>
                      <rich:calendar id="vencimentoTerminio"
                                     value="#{ArmarioControleRel.filtro.periodoVencimentoAte}"
                                     inputSize="6"
                                     inputClass="form"
                                     oninputblur="blurinput(this);"
                                     oninputfocus="focusinput(this);"
                                     datePattern="dd/MM/yyyy"
                                     enableManualInput="true"
                                     zindex="2"
                                     showWeeksBar="true"/>
                      <h:message for="vencimentoTerminio" styleClass="mensagemDetalhada"/>
                      <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                  </h:panelGroup>
              </h:panelGroup>
              <h:panelGroup>
                  <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_RelatorioArmario_planoLocacao}"/>
              </h:panelGroup>

              <h:panelGroup>
                  <h:selectOneMenu value="#{ArmarioControleRel.filtro.planoLocacao}">
                      <f:selectItems value="#{ArmarioControleRel.planosLocacao}"/>
                  </h:selectOneMenu>
              </h:panelGroup>

              <h:panelGroup id="textoFiltro">
                  <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_RelatorioArmario_tipoArmario}"/>
              </h:panelGroup>

              <h:panelGroup id="comboFiltro">
                  <h:selectOneMenu id="agrupar" value="#{ArmarioControleRel.filtro.tipoArmarioSelecionado}">
                      <f:selectItems value="#{ArmarioControleRel.tiposArmarios}"/>
                  </h:selectOneMenu>
              </h:panelGroup>

              <h:panelGroup>
                  <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_RelatorioArmario_tamanhoArmario}"/>
              </h:panelGroup>

              <h:panelGroup>
                  <h:selectOneMenu value="#{ArmarioControleRel.filtro.tamanhoArmarioSelecionado}">
                      <f:selectItems value="#{ArmarioControleRel.tamanhosArmario}"/>
                  </h:selectOneMenu>
              </h:panelGroup>

              <h:panelGroup rendered="#{ArmarioControleRel.habilitadoGestaoArmarios}">
                  <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_RelatorioArmario_contratoAsssinado}"/>
              </h:panelGroup>
              <h:panelGroup rendered="#{ArmarioControleRel.habilitadoGestaoArmarios}">
                  <h:selectOneMenu value="#{ArmarioControleRel.filtro.contratoAssinado}">
                      <f:selectItems value="#{ArmarioControleRel.itemsContratoAssinado}"/>
                  </h:selectOneMenu>
              </h:panelGroup>

              <h:panelGroup>
                  <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_RelatorioArmario_numeroArmario}"/>
              </h:panelGroup>
              <h:panelGroup>
                 <h:inputText value="#{ArmarioControleRel.filtro.numeroArmario}"/>
              </h:panelGroup>

              <h:panelGroup>
                  <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_RelatorioArmario_parcelasAtrasadas}"/>
              </h:panelGroup>
              <h:panelGroup>
                  <h:selectBooleanCheckbox value="#{ArmarioControleRel.filtro.somenteParcelasAtrasadas}"/>
              </h:panelGroup>
          </h:panelGrid>
          <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
              <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                  <h:outputText styleClass="mensagem"  value="#{ArmarioControleRel.mensagem}"/>
                  <h:outputText styleClass="mensagemDetalhada" value="#{ArmarioControleRel.mensagemDetalhada}"/>
              </h:panelGrid>
              <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                  <h:panelGroup>

                      <rich:spacer width="20px"/>
                      <a4j:commandLink id="imprimirPDF" 
                                       style="text-decoration: none;" 
                                       reRender="mensagem" 
                                       styleClass="botoes nvoBt pure-button pure-button-primary" 
                                       action="#{ArmarioControleRel.consultarArmarios}" accesskey="2" >
                          <i class="fa-icon-search"></i> &nbsp Gerar Relatório
                      </a4j:commandLink>
                  </h:panelGroup>
              </h:panelGrid>
          </h:panelGrid>
        </h:panelGrid>
    </h:form>
  </h:panelGrid>
</f:view>
<script type="text/javascript">
  document.getElementById("form:dataInicio").focus();
</script>


<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_FaturamentoSintetico_tituloForm}" rendered="#{!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
        <h:outputText value="#{msg_aplic.prt_FaturamentoRecebidoSintetico_tituloForm}" rendered="#{FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <c:if test="${!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                <c:set var="titulo" scope="session"  value="${msg_aplic.prt_FaturamentoSintetico_tituloForm}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}onde-vejo-minhas-vendas-do-mes-faturamento-por-periodo/"/>
            </c:if>
            <c:if test="${FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                <c:set var="titulo" scope="session"  value="${msg_aplic.prt_FaturamentoRecebidoSintetico_tituloForm}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}onde-vejo-o-faturamento-recebido-por-periodo-da-minha-empresa/"/>
            </c:if>
            <jsp:include page="../topoReduzido_material.jsp"/>
            
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%" >
                <h:panelGroup >
                    <h:panelGrid columnClasses="colunaCentralizada" width="100%" style="border:0;" id="panelFiltrosUtilizados">
                        <%--
                        <h:outputText
                            styleClass="tituloCamposAzul"
                            value="Veja os Filtros Utilizados "/>
--%>
                        <h:panelGrid style="font-size:12px; font-style: italic; font-family: Arial;  text-align: left;" >
                            ${FaturamentoSinteticoControleRel.filtros}
                        </h:panelGrid>



                    <%--    <rich:spacer width="10px" />
                        <rich:toolTip
                            onclick="true"
                            followMouse="true"
                            direction="top-right"
                            style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                            showDelay="500" >
                            ${FaturamentoSinteticoControleRel.filtros}
                        </rich:toolTip>
--%>
                    </h:panelGrid>
                </h:panelGroup>
                <h:panelGrid width="100%" style="margin-bottom: 8px;text-align: right;">
                    <h:panelGroup layout="block">
                        <%--BOTÃO EXCEL--%>
                        <a4j:commandLink id="exportarExcel"
                                           style="margin-left: 8px;"
                                           actionListener="#{FaturamentoSinteticoControleRel.exportar}"
                                           rendered="#{not empty FaturamentoSinteticoControleRel.listaTipoProdutoVO}"
                                           oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                           accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{FaturamentoSinteticoControleRel.listaExportavel}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <c:if test="${!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                                <f:attribute name="itemExportacao" value="faturamentoRel"/>
                                <f:attribute name="prefixo" value="FaturamentoPorPeriodo"/>
                                <f:attribute name="atributos" value="descricaoProduto=Produto,matriculaCliente=Matrícula,nomeCliente=Nome,nomeResponsavelLancamento=Resp. Lançamento,dataCadastroClienteApresentar=Data de Cadastro,codContrato=N° Contrato,dataInicio=Data Início,dataTermino=Data Término,duracaoPlano=Duração,modalidades=Modalidades,nomePlano=Plano,situacaoContrato=Situação do Contrato,condicaoPagamento=Condicao Pagamento,dataLancamentoProdutoApresentar=Data Lançamento,valorCompetencia=Valor,nomeEmpresa=Empresa"/>
                            </c:if>
                            <c:if test="${FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                                <f:attribute name="itemExportacao" value="faturamentoRecebidoRel"/>
                                <f:attribute name="prefixo" value="FaturamentoRecebidoPorPeriodo"/>
                                <f:attribute name="atributos" value="descricaoProduto=Produto,matriculaCliente=Matrícula,nomeCliente=Nome,nomeResponsavelLancamento=Resp. Venda,nomeResponsavelRecebimento=Resp. Recebimento,dataCadastroClienteApresentar=Data de Cadastro,codContrato=N° Contrato,dataInicio=Data Início,dataTermino=Data Término,duracaoPlano=Duração,modalidades=Modalidades,turma=Turmas,categoria=Categoria,nomePlano=Plano,situacaoContrato=Situação do Contrato,dataLancamentoProdutoApresentar=Data Lançamento,formaPagApresentar=Forma Pagamento,condicaoPagamento=Condicao Pagamento,valorCompetencia=Valor,nomeEmpresa=Empresa"/>
                            </c:if>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                        <%--BOTÃO PDF--%>
                        <c:if test="${!FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                            <a4j:commandLink id="exportarPdf"
                                             style="margin-left: 8px;"
                                             action="#{FaturamentoSinteticoControleRel.imprimirPDF}"
                                             rendered="#{not empty FaturamentoSinteticoControleRel.listaTipoProdutoVO}"
                                             oncomplete="#{FaturamentoSinteticoControleRel.mensagemNotificar}#{FaturamentoSinteticoControleRel.msgAlert}#{ExportadorListaControle.msgAlert}"
                                             accesskey="2" styleClass="linkPadrao">
                                    <f:attribute name="imprimir" value="true"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                        </c:if>
                        <c:if test="${FaturamentoSinteticoControleRel.somenteFaturamentoRecebido}">
                            <a4j:commandLink id="exportarPdf"
                                             style="margin-left: 8px;"
                                             action="#{FaturamentoSinteticoControleRel.imprimirPDF}"
                                             rendered="#{not empty FaturamentoSinteticoControleRel.listaTipoProdutoVO}"
                                             oncomplete="#{FaturamentoSinteticoControleRel.mensagemNotificar}#{FaturamentoSinteticoControleRel.msgAlert}#{ExportadorListaControle.msgAlert}"
                                             accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="imprimir" value="false"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                        </c:if>
                    </h:panelGroup>
                </h:panelGrid>
                <a4j:repeat value="#{FaturamentoSinteticoControleRel.listaTipoProdutoVO}" var="fatTipoProduto" >
                    <rich:dataTable id="itens" rendered="#{fatTipoProduto.apresentarResultado}" width="100%" rowClasses="linhaImpar, linhaPar" 
                                    value="#{fatTipoProduto.listaProdutoSemZero}" var="fatProduto">
                        <f:facet name="header">
                            <h:outputText value="#{fatTipoProduto.tipoProduto}" />
                        </f:facet>
                        <rich:column style="max-width: 200px; min-width: 200px" sortBy="#{fatProduto.descricao}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="Produto" />
                            </f:facet>
                            <h:outputText styleClass="tituloCampos" rendered="#{fatProduto.descricao != 'TOTALIZADOR'}" value="#{fatProduto.descricao}" style="text-align:left;"/>
                            <h:outputText styleClass="tituloCamposNegrito" rendered="#{fatProduto.descricao == 'TOTALIZADOR'}" value="#{fatProduto.descricao}" style="text-align:left;"/>
                        </rich:column>
                        <rich:columns value="#{FaturamentoSinteticoControleRel.periodos}" var="coluna" index="ind" style="max-width: 100px; min-width: 100px">
                            <f:facet name="header">
                                <h:outputText value="#{coluna.mesAnoApresentar}" />
                            </f:facet>
                            <h:panelGrid>
                                <h:panelGroup>
                                    <rich:spacer width="10px"/>
                                    <h:outputText styleClass="tituloCampos " rendered="#{((not (fatProduto.listaProdutoXMes[ind].valor eq 0)) or fatTipoProduto.mostrarValoresZeradosRel)}">
                                        <a4j:commandLink  action="#{FaturamentoSinteticoControleRel.visualizarPessoas}" oncomplete="abrirPopup('../relatorio/faturamentoSinteticoResumoPessoa.jsp', 'FaturamentoSinteticoResumoPessoa', 830, 700);" >
                                            <f:setPropertyActionListener target="#{FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO}" value="#{fatProduto.listaProdutoXMes[ind]}"/>
                                            <h:outputText value="#{fatProduto.listaProdutoXMes[ind].qtd}" styleClass="hierarquia" title="Pessoa(s)"/>
                                        </a4j:commandLink>
                                    </h:outputText>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <rich:spacer width="5px"/>
                                    <h:outputText value="#{(fatProduto.listaProdutoXMes[ind].valor eq 0 and not fatTipoProduto.mostrarValoresZeradosRel) ? null : fatProduto.listaProdutoXMes[ind].valor}" title="" styleClass="tituloboxcentro">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:panelGroup>
                            </h:panelGrid>                            
                        </rich:columns>
                    </rich:dataTable>
                </a4j:repeat>
                <rich:dataTable id="itens" rendered="#{FaturamentoSinteticoControleRel.resumo.apresentarResultado}" width="100%"  rowClasses="linhaImpar, linhaPar"
                                value="#{FaturamentoSinteticoControleRel.resumo.listaProduto}" var="fatProduto" style="margin-top:18px;">
                    <f:facet name="header">
                        <h:outputText value="#{FaturamentoSinteticoControleRel.resumo.tipoProduto}" />
                    </f:facet>
                    <rich:column style="max-width: 200px; min-width: 200px">
                        <f:facet name="header">
                            <h:outputText value="Total Geral" />
                        </f:facet>
                        <h:outputText styleClass="tituloCamposNegrito" value="#{fatProduto.descricao}"/>
                    </rich:column>
                    <rich:columns value="#{FaturamentoSinteticoControleRel.periodos}" var="coluna" index="ind" style="max-width: 100px; min-width: 100px">
                        <f:facet name="header">
                            <h:outputText value="#{coluna.mesAnoApresentar}" />
                        </f:facet>
                        <h:panelGrid>
                            <h:panelGroup>
                                <rich:spacer width="10px"/>
                                <h:outputText styleClass="tituloCampos ">
                                    <a4j:commandLink  action="#{FaturamentoSinteticoControleRel.visualizarPessoas}"
                                                      oncomplete="abrirPopup('../relatorio/faturamentoSinteticoResumoPessoa.jsp', 'FaturamentoSinteticoResumoPessoa', 830, 700);" >
                                        <f:setPropertyActionListener target="#{FaturamentoSinteticoControleRel.faturamentoSinteticoProdutoMesVO}"
                                                                     value="#{fatProduto.listaProdutoXMes[ind]}"/>
                                        <h:outputText value="#{fatProduto.listaProdutoXMes[ind].qtd}" styleClass="hierarquia"/>
                                    </a4j:commandLink>
                                </h:outputText>
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:spacer width="5px"/>
                                <h:outputText value="#{fatProduto.listaProdutoXMes[ind].valor}" styleClass="tituloboxcentro"  id="valorTotalGeral">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:columns>
                </rich:dataTable>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:commandLink id="voltar" title="Voltar Passo" action="#{FaturamentoSinteticoControleRel.voltar}" style="font-size: 19px;text-decoration: none" styleClass="botoes nvoBt">
                            <i class="fa-icon-arrow-left"></i>
                        </h:commandLink>
                        <rich:spacer width="7"/>
                    </h:panelGrid>
                </h:panelGrid>

            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

<%-- 
    Document   : include_modal_justificativaLiberacaoAcesso
    Created on : 16/01/2012, 16:37:26
    Author     : carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" src="../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<a4j:outputPanel>
    <rich:modalPanel id="modalJustificativaGrande" autosized="true" shadowOpacity="true" width="300" height="150" >
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Justificativa"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
             <h:panelGroup>
                <a4j:form>
                    <a4j:commandButton image="/imagens/close.png" style="cursor:pointer"
                                       oncomplete="#{rich:component('modalJustificativaGrande')}.hide();"
                                       id="hidelink12" reRender="form"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>
        <h:form id="formJustificativaGrande">
            <h:outputText style="text-align:left;" value="#{FechamentoAcessosControleRel.liberacaoAcessoVO.justificativa}"/>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>
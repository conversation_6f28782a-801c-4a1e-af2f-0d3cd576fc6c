<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Saldo_Credito_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Saldo_Credito_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Saldo_De_Creditos"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="../topoReduzido_material.jsp"/>
            </c:if>
        </f:facet>
        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
        <h:form id="form">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <h:commandLink action="#{SaldoCreditoRelControle.imprimirRelatorio}" id="imprimirRelatorio" style="display: none" />

            <h:inputHidden id="relatorio" value="#{SaldoCreditoRelControle.relatorio}" />

            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">
                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" >
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" >
                                        <h:panelGrid width="100%">
                                            <h:panelGroup style="text-align: right" layout="block">
                                                <a4j:commandLink id="exportarExcel" style="margin-left:5px;text-align: right"
                                                                 actionListener="#{ExportadorListaControle.exportar}"
                                                                 rendered="#{not empty SaldoCreditoRelControle.listaClienteCredito}"
                                                                 oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                 accesskey="2" styleClass="linkPadrao">
                                                    <f:attribute name="lista" value="#{SaldoCreditoRelControle.listaClienteCredito}"/>
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="atributos" value="#{SaldoCreditoRelControle.relatorioExcelAtributos}"/>
                                                    <f:attribute name="prefixo" value="#{SaldoCreditoRelControle.nomeRelatorio}"/>
                                                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                </a4j:commandLink>

                                                <c:if test="${modulo eq 'zillyonWeb'}">
                                                    <a4j:commandLink id="imprimirPDF" action="#{SaldoCreditoRelControle.imprimirRelatorio}" style="margin-left: 5px"
                                                                     oncomplete="location.href=\"#{SaldoCreditoRelControle.nomeArquivoRelatorioGeradoAgora}\""
                                                                     rendered="#{not empty SaldoCreditoRelControle.listaClienteCredito}"
                                                                     accesskey="2" styleClass="linkPadrao"
                                                                     reRender="form">
                                                        <f:attribute name="atributos" value="#{SaldoCreditoRelControle.relatorioExcelAtributos}"/>
                                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                    </a4j:commandLink>
                                                </c:if>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <rich:dataTable width="100%" styleClass="tabelaSimplesCustom"
                                                value="#{SaldoCreditoRelControle.listaClienteCredito}" rows="10" var="cliente" id="saldoCredito" rowKeyVar="status">
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="MATRÍCULA" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{cliente.matricula}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="NOME" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{cliente.nomeCliente}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="SALDO DE CRÉDITOS" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{cliente.saldoCreditoTreino}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="VENCIMENTO CONTRATO" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{cliente.dataVigenciaAte_Apresentar}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="TELEFONE" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{cliente.primeiroTelefone}" />
                                            </rich:column>
                                            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left"
                                                         rendered="#{SaldoCreditoRelControle.permissaoConsultaTodasEmpresas}">
                                                <f:facet name="header">
                                                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="EMPRESA" />
                                                </f:facet>
                                                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{cliente.nomeEmpresa}" />
                                            </rich:column>
                                        </rich:dataTable>
                                        <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:saldoCredito" maxPages="10" id="scAniversarios" />
                                    </h:panelGrid>
                                    <rich:spacer height="20px;"/>
                                    <div align="center">
                                        <h:panelGrid columns="2" width="180px" >
                                            <h:panelGrid columns="2" >
                                                <c:if test="${modulo eq 'zillyonWeb'}">
                                                    <h:commandLink id="voltar" action="#{SaldoCreditoRelControle.voltar}"
                                                                   styleClass="botaoPrimario linkPadrao">
                                                        <i class="fa-icon-arrow-left"></i>
                                                    </h:commandLink>
                                                </c:if>
                                            </h:panelGrid>
                                            <h:panelGroup>
                                                <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="TOTAL: #{SaldoCreditoRelControle.totalizador}"/>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </div>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>
</f:view>

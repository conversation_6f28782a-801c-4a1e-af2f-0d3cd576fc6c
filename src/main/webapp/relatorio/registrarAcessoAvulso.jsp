<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="../hoverform.js"></script>
    <link href="../css/otimize.css" rel="stylesheet" type="text/css">
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" language="javascript" src="../bootstrap/jquery.js"></script>
    <script type="text/javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>

    <script>
        jQuery.noConflict();

        function carregarTooltipster() {
            carregarTooltipsterConf(jQuery('.tooltipster'));
        }

        function carregarTooltipsterConf(el) {
            el.tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        }

        function tabParaCampo(campo) {
            var tecla = window.event.keyCode;
            if (!window.event.shiftKey && tecla == 9) {
                document.getElementById(campo).focus();
                event.keyCode = 0;
                event.returnValue = false;
            }
        }
    </script>
</head>

<style>
    .btnRegistrar {
        border: 3px #004671 solid !important;
    }

    .btnRegistrar:focus {
        border: 3px #129FEA solid !important;;
    }

    .btnRegistrar:hover {
        border: 3px #129FEA solid !important;;
    }

    .btnLimpar {
        border: 3px #EEEEEE solid !important;;
    }

    .btnLimpar:focus {
        border: 3px #129FEA solid !important;;
    }

    .btnLimpar:hover {
        border: 3px #129FEA solid !important;;
    }

    .btnCancelar {
        border: 3px #cdcdcd solid !important;
    }

    .btnCancelar:focus {
        border: 3px #129FEA solid !important;;
    }

    .btnCancelar:hover {
        border: 3px #129FEA solid !important;;
    }
</style>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<title>Registrar Acesso Manual</title>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Registrar Acesso Manual"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-fazer-um-registro-de-acesso-manual/"/>


    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%">
        <f:facet name="header">

            <jsp:include page="../topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form" style="height: auto;overflow: visible;" styleClass="pure-form">
            <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" switchType="ajax">
                <rich:tab label="Registrar Acesso" id="abaRegistrar">
                    <h:panelGrid columns="1" columnClasses="colunaTopCentralizada" width="100%">

                        <h:panelGroup layout="block" id="panelSupeGeral" style="display: inline-flex; width: 100%;">

                            <h:panelGroup layout="block" id="panelFotoCliente"
                                          style="width: 30%; padding-top: 5%; background: #EEEEEE;">
                                <a4j:mediaOutput
                                        id="mediaOutput"
                                        element="img"
                                        style="width:175px;height:175px;border-radius: 100%" cacheable="false"
                                        session="true"
                                        rendered="#{!SuperControle.fotosNaNuvem}"
                                        createContent="#{RegistrarAcessoAvulsoControle.paintFoto}"
                                        value="#{ImagemData}"
                                        mimeType="image/jpeg">
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="175"/>
                                    <f:param name="altura" value="175"/>
                                </a4j:mediaOutput>
                                <h:graphicImage
                                        id="graphicImage"
                                        rendered="#{SuperControle.fotosNaNuvem}"
                                        width="175" height="175"
                                        style="width:175px;height:175px;border-radius: 100%"
                                        url="#{RegistrarAcessoAvulsoControle.paintFotoDaNuvem}?time=#{SuperControle.timeStamp}">
                                </h:graphicImage>
                            </h:panelGroup>


                            <h:panelGrid columns="2" width="100%" headerClass="headerMeusDados"
                                         rowClasses="linhaImpar, linhaPar"
                                         style="background: #FFF" columnClasses="colunaDireita, colunaEsquerda">

                                <h:outputText styleClass="tituloCampos"
                                              rendered="#{RegistrarAcessoAvulsoControle.usuarioLogado.administrador}"
                                              value="#{msg_aplic.EMPRESA}:"/>
                                <h:panelGroup rendered="#{RegistrarAcessoAvulsoControle.usuarioLogado.administrador}">
                                    <div class="cb-container margenVertical">
                                        <h:selectOneMenu id="empresa" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);"
                                                         value="#{RegistrarAcessoAvulsoControle.empresaSelecionada.codigo}">
                                            <f:selectItems value="#{RegistrarAcessoAvulsoControle.listaEmpresas}"/>
                                        </h:selectOneMenu>
                                    </div>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Local de Acesso"/>
                                <h:selectOneMenu id="localacesso" onblur="blurinput(this);" onfocus="focusinput(this);" disabled="#{RegistrarAcessoAvulsoControle.alterarAcesso}"
                                                 style="font-size: 12px;"
                                                 value="#{RegistrarAcessoAvulsoControle.localAcessoVO.codigo}">
                                    <f:selectItems value="#{RegistrarAcessoAvulsoControle.selectItemLocalAcesso}"/>
                                    <a4j:support event="onchange" reRender="form"
                                                 action="#{RegistrarAcessoAvulsoControle.selecionarLocalAcesso}"/>
                                </h:selectOneMenu>


                                <h:outputText styleClass="tituloCampos" value="Coletor"/>
                                <h:selectOneMenu id="coletor" onblur="blurinput(this);" onfocus="focusinput(this);" disabled="#{RegistrarAcessoAvulsoControle.alterarAcesso}"
                                                 style="font-size: 12px;"
                                                 value="#{RegistrarAcessoAvulsoControle.coletorVO.codigo}">
                                    <f:selectItems value="#{RegistrarAcessoAvulsoControle.selectItemColetor}"/>
                                </h:selectOneMenu>


                                <h:panelGroup layout="block" style="padding: 15px;"/>
                                <h:panelGroup layout="block" style="padding: 15px;"/>


                                <h:outputText styleClass="tituloCampos" value="Matricula"/>
                                <h:inputText styleClass="tituloCampos" size="10" disabled="#{RegistrarAcessoAvulsoControle.alterarAcesso}"
                                             id="matricula"
                                             style="font-size: 12px; text-align: left"
                                             value="#{RegistrarAcessoAvulsoControle.clienteVO.matricula}">
                                    <a4j:support event="onblur"
                                                 action="#{RegistrarAcessoAvulsoControle.buscarMatricula}"
                                                 status="false"
                                                 oncomplete="#{RegistrarAcessoAvulsoControle.onComplete}"
                                                 ajaxSingle="true" reRender="form"/>
                                </h:inputText>


                                <h:outputText styleClass="tituloCampos" value="Nome"/>
                                <h:panelGroup id="panelClienteRegistrarAcesso">
                                    <h:inputText id="clienteOUColaboradorIndicou" disabled="#{RegistrarAcessoAvulsoControle.alterarAcesso}"
                                                 styleClass="tooltipster"
                                                 onkeydown="tabParaCampo('form:dataAcessoInputDate')"
                                                 title="Use o % para ajudar na pesquisa:<br/>Ex: %SILVA - \"Irá apresentar todas as pessoas com este sobrenome\""
                                                 style="width: 50%; font-size: 12px;"
                                                 value="#{RegistrarAcessoAvulsoControle.clienteVO.pessoa.nome}"/>
                                    <rich:suggestionbox
                                            id="suggestionClienteColaboradorIndicou"
                                            height="200" width="400"
                                            for="clienteOUColaboradorIndicou"
                                            fetchValue="#{cliente.pessoa.nome}"
                                            suggestionAction="#{RegistrarAcessoAvulsoControle.executarAutocompleteCliente}"
                                            minChars="1"
                                            status="false"
                                            rowClasses="20"
                                            nothingLabel="Nenhuma Pessoa encontrada !"
                                            var="cliente">
                                        <a4j:support event="onselect"
                                                     reRender="form"
                                                     action="#{RegistrarAcessoAvulsoControle.selecionarClienteSuggestionBox}"
                                                     oncomplete="#{RegistrarAcessoAvulsoControle.onComplete}"/>

                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText styleClass="textverysmall" value="Nome"/>
                                            </f:facet>
                                            <h:outputText value="#{cliente.pessoa.nome}"/>
                                        </h:column>
                                    </rich:suggestionbox>

                                    <a4j:commandLink action="#{RegistrarAcessoAvulsoControle.limparCliente}"
                                                     disabled="#{RegistrarAcessoAvulsoControle.alterarAcesso}"
                                                     reRender="form">
                                        <i class="fa-icon-eraser"></i>
                                    </a4j:commandLink>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Data"/>
                                <h:panelGroup layout="block" style="font-size: 12px;">
                                    <rich:calendar id="dataAcesso"
                                                   inputStyle="font-size: 12px"
                                                   disabled="#{RegistrarAcessoAvulsoControle.alterarAcesso}"
                                                   style="font-size: 12px;"
                                                   buttonClass="margin-left-right-7"
                                                   showWeeksBar="false"
                                                   buttonIcon="/imagens_flat/icon-calendar-check.png"
                                                   inputClass="forcarSemBorda"
                                                   styleClass="dateTimeCustom"
                                                   enableManualInput="true"
                                                   oninputchange="return validar_Data(this.id);"
                                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                   value="#{RegistrarAcessoAvulsoControle.dataAcesso}"
                                                   inputSize="8"
                                                   datePattern="dd/MM/yyyy">
                                    </rich:calendar>
                                    <rich:jQuery
                                            query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                            selector=".btn-toggle-calendar"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Hora Entrada"/>
                                <h:inputText id="horaEntradaRegistroAcesso"
                                             onkeypress="return dois_pontos(this);"
                                             style="font-size: 12px;"
                                             size="10" maxlength="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{RegistrarAcessoAvulsoControle.horaEntradaRegistroAcesso}"/>


                                <h:outputText styleClass="tituloCampos" value="Registrar Saída"/>
                                <h:panelGroup layout="block"
                                              style="padding-top: 8px; padding-bottom: 8px; padding-left: 3px;"
                                              id="panelcheck">
                                    <h:selectBooleanCheckbox id="registrarSaida"
                                                             style="font-size: 12px;"
                                                             value="#{RegistrarAcessoAvulsoControle.registrarSaida}">
                                        <a4j:support event="onclick" reRender="form" oncomplete="document.getElementById('form:registrarSaida').focus()"/>
                                    </h:selectBooleanCheckbox>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Hora Saída"
                                              rendered="#{RegistrarAcessoAvulsoControle.registrarSaida}"/>
                                <h:inputText id="horaSaidaRegistroAcesso"
                                             rendered="#{RegistrarAcessoAvulsoControle.registrarSaida}"
                                             onkeypress="return dois_pontos(this);"
                                             style="font-size: 12px;"
                                             size="10" maxlength="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             value="#{RegistrarAcessoAvulsoControle.horaSaidaRegistroAcesso}"/>

                            </h:panelGrid>
                        </h:panelGroup>
                    </h:panelGrid>

                    <h:panelGroup layout="block" style="padding: 5px;"/>

                    <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar" columnClasses="colunaCentralizada">
                        <h:panelGroup layout="block" style="padding: 20px">
                            <a4j:commandLink id="registrarAcessos" ajaxSingle="false"
                                             value="Registrar"
                                             onkeydown="tabParaCampo('form:localacesso')"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{RegistrarAcessoAvulsoControle.registrarAcessos}"
                                             oncomplete="#{RegistrarAcessoAvulsoControle.mensagemNotificar};#{RegistrarAcessoAvulsoControle.onComplete}"
                                             accesskey="2" reRender="form,modalAcessosDia"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </rich:tab>

                <rich:tab label="Consultar" id="abaConsultar"
                          action="#{RegistrarAcessoAvulsoControle.consultarAcessosLancados}">

                    <h:panelGroup layout="block" id="panelGeralConsultar" style="display: inline-flex; width: 100%;">
                        <h:panelGroup layout="block" id="panelConsultar" style="display: inline-flex; width: 75%;">

                            <h:panelGroup layout="block"
                                          style="padding-right: 5px; padding-top: 5px">
                                <h:outputText styleClass="tituloCampos" value="Data Lançamento"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block">
                                    <rich:calendar id="dataInicialFiltrar"
                                                   buttonClass="margin-left-right-7"
                                                   showWeeksBar="false"
                                                   inputStyle="font-size: 12px"
                                                   buttonIcon="/imagens_flat/icon-calendar-check.png"
                                                   inputClass="forcarSemBorda"
                                                   styleClass="dateTimeCustom"
                                                   enableManualInput="true"
                                                   oninputchange="return validar_Data(this.id);"
                                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                   value="#{RegistrarAcessoAvulsoControle.dataInicialFiltrar}"
                                                   inputSize="8"
                                                   datePattern="dd/MM/yyyy">
                                    </rich:calendar>
                                    <rich:jQuery
                                            query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                            selector=".btn-toggle-calendar"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block"
                                              style="padding-left: 5px; padding-right: 5px; padding-top: 5px">
                                    <h:outputText styleClass="tituloCampos" value="até"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block">
                                    <rich:calendar id="dataFinalFiltrar"
                                                   buttonClass="margin-left-right-7"
                                                   showWeeksBar="false"
                                                   buttonIcon="/imagens_flat/icon-calendar-check.png"
                                                   inputClass="forcarSemBorda"
                                                   styleClass="dateTimeCustom"
                                                   enableManualInput="true"
                                                   inputStyle="font-size: 12px"
                                                   oninputchange="return validar_Data(this.id);"
                                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                   value="#{RegistrarAcessoAvulsoControle.dataFinalFiltrar}"
                                                   inputSize="8"
                                                   datePattern="dd/MM/yyyy">
                                    </rich:calendar>
                                    <rich:jQuery
                                            query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                            selector=".btn-toggle-calendar"/>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="panelConsultarBotao"
                                          style="padding-left: 15px; padding-top: 2px;">
                                <a4j:commandLink id="preencherListaDeAcessosLancados" ajaxSingle="false"
                                                 value="Consultar"
                                                 styleClass="pure-button pure-button-primary"
                                                 action="#{RegistrarAcessoAvulsoControle.consultarAcessosLancados}"
                                                 oncomplete="#{RegistrarAcessoAvulsoControle.mensagemNotificar};#{RegistrarAcessoAvulsoControle.onComplete}"
                                                 reRender="form,modalAcessosDia"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="panelTotal"
                                      style="padding-top: 5px; width: 20%; text-align: right">
                            <h:outputText style="font-size: 14px; font-weight: bold" value="Total:"
                                          rendered="#{not empty RegistrarAcessoAvulsoControle.listaAcessosAdicionados}"/>
                            <h:outputText style="font-size: 16px; font-weight: bold; color: red; padding-left: 5px"
                                          value="#{RegistrarAcessoAvulsoControle.totalLancado} registros"
                                          rendered="#{not empty RegistrarAcessoAvulsoControle.listaAcessosAdicionados}"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <table class="tblHeaderLeft semZebra" style="margin: 0px; width: 100%; margin-top: 10px;">
                        <thead>
                        <tr>

                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="MATRICULA"/>
                            </th>

                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="NOME"/>
                            </th>

                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="ENTRADA"/>
                            </th>

                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="SAÍDA"/>
                            </th>

                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="DATA LANÇAMENTO"/>
                            </th>

                            <th>
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="OPÇÕES"/>
                            </th>

                        </tr>
                        </thead>

                        <tbody>
                        <a4j:repeat var="acesso" value="#{RegistrarAcessoAvulsoControle.listaAcessosAdicionados}">

                            <tr>

                                <td>
                                    <a4j:commandLink
                                            action="#{RegistrarAcessoAvulsoControle.irParaTelaCliente}"
                                            value="#{acesso.cliente.matricula}"
                                            oncomplete="#{RegistrarAcessoAvulsoControle.onComplete}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                                </td>

                                <td>
                                    <a4j:commandLink
                                            action="#{RegistrarAcessoAvulsoControle.irParaTelaCliente}"
                                            value="#{acesso.cliente.nome_Apresentar}"
                                            oncomplete="#{RegistrarAcessoAvulsoControle.onComplete}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                                </td>

                                <td>
                                    <a4j:commandLink
                                            action="#{RegistrarAcessoAvulsoControle.irParaTelaCliente}"
                                            value="#{acesso.dataHoraEntrada}"
                                            oncomplete="#{RegistrarAcessoAvulsoControle.onComplete}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                                </td>
                                <td>
                                    <a4j:commandLink
                                            action="#{RegistrarAcessoAvulsoControle.irParaTelaCliente}"
                                            value="#{acesso.dataHoraSaida}"
                                            oncomplete="#{RegistrarAcessoAvulsoControle.onComplete}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                                </td>
                                <td>
                                    <a4j:commandLink
                                            action="#{RegistrarAcessoAvulsoControle.irParaTelaCliente}"
                                            value="#{acesso.dataRegistro}"
                                            oncomplete="#{RegistrarAcessoAvulsoControle.onComplete}"
                                            style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                                </td>

                                <td>
                                    <a4j:commandLink
                                            action="#{RegistrarAcessoAvulsoControle.excluirAcesso}"
                                            reRender="form"
                                            title="Excluir"
                                            oncomplete="#{RegistrarAcessoAvulsoControle.mensagemNotificar};"
                                            style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;">
                                        <i class="fa-icon-remove linkAzul"></i>
                                    </a4j:commandLink>
                                </td>
                            </tr>
                        </a4j:repeat>
                        </tbody>
                    </table>

                </rich:tab>

            </rich:tabPanel>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                    <h:outputText styleClass="mensagem" value="#{RegistrarAcessoAvulsoControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{RegistrarAcessoAvulsoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="modalAcessosDia"
                     styleClass="novaModal" autosized="true" shadowOpacity="true" width="450">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Acessos no dia"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalAcessos"/>
                <rich:componentControl for="modalAcessosDia" attachTo="hidelinkModalAcessos" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAce" ajaxSubmit="true">

            <h:panelGroup layout="block" id="panelMsgModalAcesso" style="padding: 10px">
                <h:outputText value="Cliente tem registrado os seguintes acessos neste dia:"
                              style="font-size: 14px !important; font-weight: bold; color: red;"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                          style="width: 100%; text-align: center; max-height: 200px; overflow-y: auto;">

                <table class="tblHeaderLeft semZebra" style="margin: 0px; width: 100%;">
                    <thead>
                    <tr>

                        <th>
                            <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                          value="ENTRADA"/>
                        </th>

                        <th>
                            <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                          value="SAÍDA"/>
                        </th>

                    </tr>
                    </thead>

                    <tbody>

                    <a4j:repeat var="acesso" value="#{RegistrarAcessoAvulsoControle.acessosNoDia}">
                        <tr>
                            <td>
                                <h:outputText
                                        value="#{acesso.dataHoraEntrada}"
                                        style="text-decoration: none; text-align: left; font-size: 12px; color: #29AAE2;"/>
                            </td>

                            <td>
                                <h:outputText
                                        value="#{acesso.dataHoraSaida}"
                                        style="text-decoration: none; text-align: left; font-size: 12px; color: #29AAE2;"/>
                            </td>
                            <td>
                                <a4j:commandLink id="editarAcesso" action="#{RegistrarAcessoAvulsoControle.alterarAcesso}" title="Alterar Acesso"
                                                 oncomplete="#{RegistrarAcessoAvulsoControle.mensagemNotificar};#{RegistrarAcessoAvulsoControle.onComplete};" reRender="form"
                                        styleClass="linkPadrao texto-cor-azul texto-size-14-real tooltipster">
                                    <i class="fa-icon-edit"></i>
                                </a4j:commandLink>
                            </td>    
                        </tr>
                    </a4j:repeat>
                    </tbody>
                </table>
            </h:panelGroup>

            <br/>

            <h:panelGrid styleClass="paginaFontResponsiva"
                         columns="2"
                         columnClasses="colunaDireita, colunaEsquerda"
                         style="width: 100%; text-align: center !important; padding-bottom: 10px; padding-top: 10px;">

                <a4j:commandLink action="#{RegistrarAcessoAvulsoControle.registrarAcessosSemValidar}"
                                 reRender="form"
                                 id="btnRegistrarModal"
                                 style="margin-right: 10px"
                                 styleClass="pure-button pure-button-primary"
                                 value="Registrar"
                                 oncomplete="#{RegistrarAcessoAvulsoControle.mensagemNotificar};#{RegistrarAcessoAvulsoControle.onComplete}"/>

                <a4j:commandLink reRender="form"
                                 id="btnCancelarModal"
                                 style="margin-left: 10px"
                                 styleClass="pure-button pure-button-secundary"
                                 value="Cancelar"
                                 oncomplete="Richfaces.hideModalPanel('modalAcessosDia');document.getElementById('form:matricula').focus()"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>



    <SCRIPT>
        document.getElementById("form:matricula").focus();
        carregarTooltipster();
    </SCRIPT>
</f:view>


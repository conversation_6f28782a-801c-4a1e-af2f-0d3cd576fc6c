<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 19/10/2015
  Time: 16:24
  To change this template use File | Settings | File Templates.
--%>
<%--
    Document   : faturamentoSinteticoResumoPessoa
    Created on : 26/10/2009, 18:48:13
    Author     : pedro
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
  <%@include file="../includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
  <title>
    <h:outputText value="#{msg_aplic.prt_RelatorioArmario_tituloForm}"/>
  </title>
  <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
    <h:form id="form" >
      <input type="hidden" value="${modulo}" name="modulo"/>
        <c:set var="titulo" scope="session" value=" ${msg_aplic.prt_RelatorioArmario_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Armarios"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include  page="../topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>
      <h:commandLink action="#{AniversarioControle.imprimirRelatorio}" id="imprimirRelatorio" style="display: none" />
      <h:inputHidden id="relatorio" value="#{AniversarioControle.relatorio}" />

      <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" >
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <h:panelGrid columns="1" width="100%" >
                                    <h:panelGrid width="100%">
                                        <h:panelGroup style="text-align: right" layout="block">
                                            <a4j:commandLink id="exportarExcel" style="margin-left:5px;text-align: right"
                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                               rendered="#{not empty ArmarioControleRel.resultado}"
                                                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Armarios', 800,200);#{ExportadorListaControle.msgAlert}"
                                                               accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista" value="#{ArmarioControleRel.resultado}"/>
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,situacaoAluno=Situação Aluno,numeroArmario=Nº Armário,tamanho=Tamanho,planoLocacao=Plano,tipoArmarioApresentar=Tipo,vigencia=Vigência,dataInicioApresentar=Data Início,dataFimApresentar=Fim,contratoAssinado_Apresentar=Contrato Assinado,dataInicioContratoApresentar=Início Contrato,dataFinalContratoApresentar=Final Contrato,planoContrato=Plano Contrato"/>
                                                <f:attribute name="prefixo" value="GeralArmarios"/>
                                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                            </a4j:commandLink>
                                            <a4j:commandLink id="exportarPdf"
                                                               style="margin-left: 8px;"
                                                               actionListener="#{ExportadorListaControle.exportar}"
                                                               rendered="#{not empty ArmarioControleRel.resultado}"
                                                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','FaturamentoPorPeriodo', 800,200);#{ExportadorListaControle.msgAlert}"
                                                               accesskey="2" styleClass="linkPadrao">
                                                <f:attribute name="lista" value="#{ArmarioControleRel.resultado}"/>
                                                <f:attribute name="tipo" value="pdf"/>
                                                <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,situacaoAluno=Situação Aluno,numeroArmario=Nº Armário,tamanho=Tamanho,planoLocacao=Plano,tipoArmarioApresentar=Tipo,vigencia=Vigência,dataInicioApresentar=Data Início,dataFimApresentar=Fim,contratoAssinado_Apresentar=Contrato Assinado,dataInicioContratoApresentar=Início Contrato,dataFinalContratoApresentar=Final Contrato,planoContrato=Plano Contrato"/>
                                                <f:attribute name="prefixo" value="GeralArmarios"/>
                                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    <rich:dataTable width="100%" styleClass="tabelaSimplesCustom"
                                                    value="#{ArmarioControleRel.resultado}" rendered="#{not empty ArmarioControleRel.resultado}" rows="50" var="armario" id="tabelaResultado" rowKeyVar="status">
                                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Nome" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.nome}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Matrícula" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.matricula}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Nº Armário" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.numeroArmario}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Tamanho" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.tamanho}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Plano" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.planoLocacao}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Tipo" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.tipoArmarioApresentar}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Vigência" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.vigencia}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Data Início" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.dataInicioApresentar}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Data Fim" />
                                            </f:facet>
                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{armario.dataFimApresentar}" />
                                        </rich:column>
                                        <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left" rendered="#{ArmarioControleRel.habilitadoGestaoArmarios}">
                                            <f:facet name="header">
                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Contrato Assinado" />
                                            </f:facet>
                                            <h:panelGroup layout="block" styleClass="botaoAssinarCotrato">
                                                <h:outputText style="font-weight: bold" styleClass="#{armario.iconContratoAssinado}" />
                                            </h:panelGroup>
                                        </rich:column>
                                    </rich:dataTable>
                                    <rich:datascroller align="center" renderIfSinglePage="false"  styleClass="scrollPureCustom" for="form:tabelaResultado" maxPages="10" id="scArmarios" />
                                </h:panelGrid>
                                <h:panelGrid columns="2" width="95%" columnClasses="colunaEsquerda" styleClass="tabMensagens">
                                    <h:panelGrid columns="2" width="90%" styleClass="tabBotoes" columnClasses="colunaDireita">
                                        <c:if test="${modulo eq 'zillyonWeb'}">
                                            <h:commandLink id="voltar" action="#{ArmarioControleRel.voltar}"
                                                           styleClass="pure-button pure-button-small pure-button-primary">
                                                <i class="fa-icon-backward"></i> &nbsp Voltar
                                            </h:commandLink>
                                        </c:if>

                                        <c:if test="${modulo eq 'centralEventos'}">
                                            <h:commandButton id="voltarCE" alt="Voltar Passo" action="#{ArmarioControleRel.voltar}"
                                                             image="../imagens/botoesCE/voltar_sem_fundo.png"/>

                                        </c:if>
                                    </h:panelGrid>
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposDestaqueNegrito " value="Total: #{ArmarioControleRel.totalResultado}"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>


    </h:form>
  </h:panelGrid>
</f:view>


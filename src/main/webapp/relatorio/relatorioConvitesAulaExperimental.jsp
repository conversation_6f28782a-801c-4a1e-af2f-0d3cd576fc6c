<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="../css/telaCliente.css" rel="stylesheet" type="text/css"/>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
</head>

<link href="../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>

<title>Relatório de Convites de Aula Experimental</title>
<style>    
    .dateTimeCustom {
        height: auto;
    }
    *{
        font-family:Arial;
        font-size: 14px;

    }
    .tabelaFiltros{
        margin-left: 20px;
    }
    a.pure-button{
        color: #777777;
        font-weight: bold;
    }
</style>    
<script>


    function verificarTodos() {
        var todos = document.getElementById('form:todos');
        var validou = document.getElementById('form:validou');
        var compareceu = document.getElementById('form:compareceu');
        var agendou = document.getElementById('form:agendou');
        todos.checked = (validou.checked && compareceu.checked && agendou.checked)
                || (!validou.checked && !compareceu.checked && !agendou.checked);

    }

    // Stupid jQuery table plugin.


</script>   
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Relatório de Convites de Aula Experimental"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Convite_Aula_Experimental"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="../topoReduzido_material.jsp"/>

        </f:facet>
        <h:form id="form" target="_blank" styleClass="formNovo">
            <div class="tituloPainelPesquisa cinza nobottom" style="height: auto;">
                <div style="width: 100%; padding: 10px 0;">
                    <table class="tabelaFiltros">
                        <tr style="vertical-align: middle;">
                            <td>
                                <h:outputText styleClass="cinza" value="Período:"/>
                            </td>
                            <td>
                                <div class="dateTimeCustom">
                                    <rich:calendar value="#{BIConviteAulaExperimentalControle.inicio}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                </div>
                            </td>
                            <td>
                                <h:outputText styleClass="cinza"  value="até"/>
                            </td>
                            <td style="padding-right: 5px;">
                                <div class="dateTimeCustom">
                                    <rich:calendar id="datafim"
                                                   value="#{BIConviteAulaExperimentalControle.fim}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"/>
                                </div>
                            </td>
                            <td style="padding-right: 5px;">
                                <h:selectOneMenu id="situacao" styleClass="newComboBox noBorderLeft" 
                                                 style="vertical-align:middle; min-width: 200px;"
                                                 value="#{BIConviteAulaExperimentalControle.tipo}">
                                    <f:selectItem itemValue="" itemLabel="Tipo de convite"/>
                                    <f:selectItems value="#{BIConviteAulaExperimentalControle.tipos}"/>
                                </h:selectOneMenu>
                            </td>


                        <tr>
                        <tr >
                            <td style="padding-top: 5px;">
                                <h:outputText styleClass="cinza" value="Aluno que convidou:"/>
                            </td>
                            <td colspan="3">
                                <h:inputText style="width: 100%;" value="#{BIConviteAulaExperimentalControle.clienteConvidou}"/>
                            </td>

                            <td style="padding-top: 5px;">
                                <h:outputText style="margin-left: 15px;" styleClass="cinza" value="Convidado:"/>
                            </td>
                            <td>
                                <h:inputText style="width: 270px;" value="#{BIConviteAulaExperimentalControle.convidado}"/>
                            </td>
                        </tr>
                        <tr >
                            <td style="padding-top: 5px;">
                                <h:outputText styleClass="cinza" value="Usuário que convidou:"/>
                            </td>
                            <td colspan="3">
                                <h:inputText style="width: 100%;" value="#{BIConviteAulaExperimentalControle.usuarioConvidou}"/>
                            </td>
                            <td colspan="3">
                                <div class="chk-fa-container inline " style="margin-left: 15px; opacity: 0.5    ;">
                                    <h:selectBooleanCheckbox id="todos" onclick="verificarTodos();"
                                                             value="#{BIConviteAulaExperimentalControle.todos}">

                                    </h:selectBooleanCheckbox>

                                    <span/>
                                </div>
                                <h:outputLabel value="Todos" style="color: #29ABE2; cursor: pointer;opacity: 0.5;" for="todos" />

                                <div class="chk-fa-container inline " style="margin-left: 15px;">
                                    <h:selectBooleanCheckbox id="validou" onclick="verificarTodos();"
                                                             value="#{BIConviteAulaExperimentalControle.validou}">
                                    </h:selectBooleanCheckbox>

                                    <span/>
                                </div>
                                <h:outputLabel value="Convites validados" style="color: #29ABE2; cursor: pointer;" for="validou" />

                                <div class="chk-fa-container inline " style="margin-left: 10px;">
                                    <h:selectBooleanCheckbox id="agendou" onclick="verificarTodos();"
                                                             value="#{BIConviteAulaExperimentalControle.agendou}">
                                    </h:selectBooleanCheckbox>

                                    <span/>
                                </div>
                                <h:outputLabel value="Agendaram aula" style="color: #29ABE2; cursor: pointer;" for="agendou" />

                                <div class="chk-fa-container inline " style="margin-left: 10px;">
                                    <h:selectBooleanCheckbox id="compareceu" onclick="verificarTodos();" 
                                                             value="#{BIConviteAulaExperimentalControle.compareceu}">
                                    </h:selectBooleanCheckbox>

                                    <span/>
                                </div>
                                <h:outputLabel value="Compareceram" style="color: #29ABE2; cursor: pointer;" for="compareceu" />

                            </td>
                        </tr>


                    </table>

                </div>            
                <div style="width: calc(100% - 40px); text-align: right; padding: 0 0 20px 0;">            
                    <a4j:commandLink reRender="tabelaConvites"
                                     action="#{BIConviteAulaExperimentalControle.consultarLista}" 
                                     styleClass="pure-button"
                                     title="#{msg.msg_consultar_dados}">
                        <h:outputText style="font-size: 14px" value="Aplicar filtros"/>
                    </a4j:commandLink>
                </div>            

            </div>

            <h:panelGroup id="tabelaConvites">
                <table class="tabelaDados semZebra" id="mytable">
                    <tr>
                        <th>
                            CONVIDADO
                        </th>
                        <th>
                            QUEM CONVIDOU
                        </th>
                        <th>
                            CONVITE
                        </th>
                        <th>
                            DATA DO CONVITE
                        </th>
                        <th>
                            DATA VALIDAÇÃO
                        </th>
                        <th>

                        </th>
                    </tr>
                    <c:forEach items="${BIConviteAulaExperimentalControle.lista}" var="convite">
                        <tr style="border-bottom: 1px solid #DDDDDD !important">
                            <td>
                                ${convite.convidado}
                            </td>
                            <td>
                                ${convite.quemConvidou}
                            </td>
                            <td>
                                ${convite.convite}
                            </td>
                            <td>
                                ${convite.dataLancamentoApresentar}
                            </td>
                            <td>
                                ${convite.dataValidacaoApresentar}
                            </td>
                            <td>
                                ${convite.status}
                            </td>
                        </tr>    
                    </c:forEach>
                </table>
            </h:panelGroup>

        </h:form>
    </h:panelGrid>
    <script>

    </script>
</f:view>


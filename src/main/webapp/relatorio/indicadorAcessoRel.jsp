<%-- 
    Document   : IndicadorAcesso
    Created on : 24/07/2017, 11:44:35
    Author     : arthur
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <script src="../script/chart_amcharts.js" type="text/javascript" language="javascript"></script>
    <script src="../script/chart_pie.js" type="text/javascript" language="javascript"></script>
    <script src="../script/chart_light.js" type="text/javascript" language="javascript"></script>
    <script src="../script/chart_serial.js" type="text/javascript" language="javascript"></script>
    <link href="../beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Indicador de acesso</title>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .alinharMeio tbody tr td label {
        vertical-align: bottom;
        padding-right: 10px;
        font-size: 15px;
        font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
    }

    .bi-crm-box-detail-indicadores {
        background-color: #fff;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .container-bi-crm-indicadores {
        width: 100%;
        margin: 10px;
        -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        -moz-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        background-color: #ffffff;
        transition: transform .1s ease-out;
        -moz-transition: -moz-transform .1s ease-out;
        -o-transition: -o-transform .1s ease-out;
        -webkit-transition: -webkit-transform .1s ease-out;
    }

    .col-text-align-right {
        text-align: right;
    }

    .bi-crm-box-header-indicadores .fa-icon-external-link-square {
        color: #32616b;
    }

    .bi-crm-box-header-indicadores {
        line-height: 25px;
        padding: 15px;
        text-align: center;
        height: 3.5em;
    }

    .tituloBICRM {
        font-family: Arial;
        text-decoration: none;
        font-weight: bold;
        font-size: 16px;
        color: #333333;
    }

    .col-text-align-center {
        text-align: center;
    }

    .indicadoresBICRM {
        font-family: Arial, Helvetica, sans-serif;
        font-size: 40px;
        text-align: center;
        color: #29ABE2;
        margin-bottom: 20px;
    }

    .tituloPersonalizado {
        font-size: 15px;
        font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
    }
</style>
<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0" id="gridGeral">

        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Indicador_Acesso}"/>
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlBaseConhecimento}bi-gestao-de-acessos-adm/"/>

        <f:facet name="header">

            <jsp:include page="../topoReduzido_material.jsp"/>

        </f:facet>
        <h:form id="form">
            <h:panelGrid id="indicadorAcesso" columns="1" width="100%">
                <hr style="border-color: #e6e6e6;">
                <h:panelGroup layout="block" style="text-align: center">
                    <h:inputHidden id="tipoConsultaValor" value="#{IndicadorAcessoControleRel.tipoConsulta}"/>
                    <h:outputText styleClass="tituloCampos" rendered="#{ListaAcessoControleRel.temEmpresa}"
                                  value="#{msg_aplic.prt_ListaAcesso_empresa}"/>
                    <h:panelGroup rendered="#{IndicadorAcessoControleRel.usuarioLogado.administrador}">
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{IndicadorAcessoControleRel.codigoEmpesaSelecinada}">
                            <f:selectItems value="#{IndicadorAcessoControleRel.listaEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <rich:spacer height="20px"/>
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  value="#{msg_aplic.prt_ListaAcesso_tipoConsultaAcesso}"/>
                    <h:panelGroup layout="block" id="panelRadioButton" style="margin-left: 30vw; padding: 7px">
                        <h:selectOneRadio id="tipoConsultaAcessoCliente" styleClass="tituloCampos alinharMeio"
                                          value="#{IndicadorAcessoControleRel.tipoConsulta}">
                            <f:selectItems value="#{IndicadorAcessoControleRel.listaPesquisa}"/>
                            <a4j:support event="onclick"
                                         reRender="form" action="#{IndicadorAcessoControleRel.zerarGraficoAcesso}"/>
                        </h:selectOneRadio>
                    </h:panelGroup>
                    <c:if test="${IndicadorAcessoControleRel.tipoConsulta=='PE'}">
                        <h:panelGroup>
                            <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                                <rich:calendar id="dataInicio"
                                               value="#{IndicadorAcessoControleRel.dataInicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               zindex="2"
                                               showWeeksBar="false"
                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"/>
                                <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <rich:spacer width="10px"/>
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                          value="#{msg_aplic.prt_ate}"/>
                            <h:panelGroup styleClass="dateTimeCustom"
                                          style="font-size: 11px !important;margin-left: 10px;">
                                <rich:calendar id="dataTermino"
                                               value="#{IndicadorAcessoControleRel.dataFinal}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               zindex="2"
                                               showWeeksBar="true"
                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"/>

                                <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                             query="mask('99/99/9999')"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </c:if>
                    <c:if test="${IndicadorAcessoControleRel.tipoConsulta=='DIP'}">
                        <h:panelGroup>
                            <h:panelGroup id="dataInicioCustom" styleClass="dateTimeCustom" style=" display: block !important;; font-size: 11px !important;">
                                <rich:calendar id="dataInicio"
                                               value="#{IndicadorAcessoControleRel.dataInicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                                <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <rich:spacer width="10px"/>
                        </h:panelGroup>
                    </c:if>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandLink id="atualizarQuantidadeCliente"
                                         styleClass="pure-button pure-button-primary"
                                         style="margin-bottom: 10px; margin-top: 10px;"
                                         action="#{IndicadorAcessoControleRel.inicializarDados}"
                                         oncomplete="#{IndicadorAcessoControleRel.mensagemNotificar};#{IndicadorAcessoControleRel.onComplete}"
                                         accesskey="2" reRender="form, mensagem">
                            Atualizar Gráfico
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
            <br/>
            <h:panelGroup layout="block" id="panlGrafico">
                <script type="text/javascript">
                    function carregarGraficoAcesso() {
                        var chart = AmCharts.makeChart("chartdivAcesso", {
                            "type": "serial",
                            "theme": "none",
                            "dataProvider": ${IndicadorAcessoControleRel.listaAcessoGrafico},
                            "valueAxes": [{
                                "dashLength": 0,
                                "integersOnly": true
                            }],
                            "startDuration": 1,
                            "legend": {
                                "autoMargins": true,
                                "equalWidths": false,
                                "horizontalGap": 10,
                                "markerSize": 10,
                                "useGraphSettings": true,
                                "valueAlign": "left",
                                "valueWidth": 0
                            },
                            "graphs": [{
                                "balloonText": "<b>[[value]]</b> Acessos",
                                "fillAlphas": 0.8,
                                "lineAlpha": 0.2,
                                "type": "column",
                                "valueField": "valor",
                                "fillColors": "#074871",
                                "title": "Quantidade de acessos no dia"
                            }],
                            "chartCursor": {
                                "categoryBalloonEnabled": false,
                                "cursorAlpha": 0,
                                "zoomable": false
                            },
                            "categoryField": "campo",
                            "categoryAxis": {
                                "gridPosition": "start",
                                "axisAlpha": 0,
                                "fillAlpha": 0.05,
                                "fillColor": "#074871",
                                "gridAlpha": 0,
                                "labelRotation": 45
                            },
                            "export": {
                                "enabled": true
                            }

                        });

                    }
                </script>
                <script type="text/javascript">
                    function carregarGraficoAcessoNovo() {
                        var chart = AmCharts.makeChart("chartdivAcesso", {
                            "type": "serial",
                            "theme": "light",
                            "dataProvider": ${IndicadorAcessoControleRel.listaAcessoGraficoComparativo},
                            "valueAxes": [{
                                "position": "left",
                            }],
                            "startDuration": 1,
                            "legend": {
                                "autoMargins": true,
                                "equalWidths": false,
                                "horizontalGap": 10,
                                "markerSize": 10,
                                "useGraphSettings": true,
                                "valueAlign": "left",
                                "valueWidth": 0
                            },
                            "graphs": [{
                                "balloonText": "[[category]]: <b>[[value]]</b>",
                                "fillAlphas": 0.8,
                                "lineAlpha": 0.2,
                                "columnWidth": 0.7,
                                "type": "column",
                                "valueField": "passado",
                                "fillColors": "#999999",
                                "title": "Ano passado"
                            }, {
                                "balloonText": "[[category]]: <b>[[value]]</b>",
                                "fillAlphas": 0.8,
                                "lineAlpha": 0.2,
                                "type": "column",
                                "columnWidth": 0.6,
                                "valueField": "atual",
                                "fillColors": "#074871",
                                "title": "Ano atual"
                            }],
                            "chartCursor": {
                                "categoryBalloonEnabled": false,
                                "cursorAlpha": 0,
                                "zoomable": false
                            },
                            "plotAreaFillAlphas": 0.1,
                            "categoryField": "campo",
                            "categoryAxis": {
                                "gridPosition": "start",
                                "axisAlpha": 0,
                                "fillColor": "#074871",
                                "gridAlpha": 0,
                                "labelRotation": 45
                            },
                            "export": {
                                "enabled": true
                            }

                        });

                    }
                </script>
                <div id="chartdivAcesso"
                     style="width: 100%; height: 350px; float: left"></div>
                <script>
                    if (document.getElementById('form:tipoConsultaValor').value === 'ANO') {
                        carregarGraficoAcessoNovo();
                    } else {
                        carregarGraficoAcesso();
                    }
                </script>
                <%--<script>--%>
                    <%--document.getElementById('form:dataInicioCustom').style.display = 'block';--%>
                <%--</script>--%>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>

</f:view>



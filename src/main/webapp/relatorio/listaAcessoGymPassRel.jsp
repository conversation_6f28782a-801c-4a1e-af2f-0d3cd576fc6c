<%-- 
    Document   : listaAcessoGymPassRel
    Created on : 09/10/2017, 16:27:25
    Author     : arthur
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="../hoverform.js"></script>
    <link href="../css/otimize.css" rel="stylesheet" type="text/css">
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
</head>
<title>Relatorio Gympass</title>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .alinharMeio tbody tr td label{
        vertical-align: bottom;
        padding-right: 10px;
    }

</style>
<f:view>

    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ListaAcesso_gympass}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Inicial:Recurso_GymPass"/>
    
    
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <h:panelGrid columns="1" width="100%">
        <f:facet name="header">
            
                <jsp:include page="../topoReduzido_material.jsp"/>
            
        </f:facet>

        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <hr style="border-color: #e6e6e6;">
                    <center>
                        <span CLASS="texto-size-14-real">Lembre-se: você encontra as informações sobre valores na <a
                                href="https://www.gympass.com/horarios-de-reserva/my_reservations"
                                target="_blank"
                                class="texto-size-14-real">GymPass.</a>
                        </span>
                    </center>
                <h:panelGrid  columns="1" columnClasses="colunaTopCentralizada" width="100%">
                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" style="padding-left:0px;">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" rendered="#{ListaGymPassRelControle.usuarioLogado.administrador}" value="EMPRESA: " />
                        <h:panelGroup styleClass="font-size-em-max" rendered="#{ListaGymPassRelControle.usuarioLogado.administrador}">
                            <div class="cb-container margenVertical">
                                <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ListaGymPassRelControle.empresaSelecionada.codigo}" >
                                    <f:selectItems value="#{ListaGymPassRelControle.listaEmpresas}" />
                                </h:selectOneMenu>
                            </div>
                        </h:panelGroup>
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_GymPass_periodoPesquisa}" />
                        <h:panelGroup styleClass="flex" layout="block">
                            <div class="margenVertical">
                                <h:panelGroup  styleClass="dateTimeCustom">
                                    <rich:calendar id="dataInicio"
                                                   value="#{ListaGymPassRelControle.dataInicial}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false" 
                                                   buttonIcon="/imagens_flat/calendar-button.svg">
                                    </rich:calendar>
                                    <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <rich:spacer width="5px"/>
                                <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                                      style="margin: 7px 9px 0 0px;margin-left: 15px;" value="#{msg_aplic.prt_ate_Maisculo}" />
                                <rich:spacer width="10px"/>
                                <h:panelGroup styleClass="dateTimeCustom" >
                                    <rich:calendar id="dataTermino"
                                                   value="#{ListaGymPassRelControle.dataFinal}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   showWeeksBar="false"
                                                   buttonIcon="/imagens_flat/calendar-button.svg">
                                    </rich:calendar>
                                    <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                </h:panelGroup>
                                <rich:spacer width="12px"/>
                            </div>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                    <h:outputText styleClass="mensagem"  value="#{ListaGymPassRelControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{ListaGymPassRelControle.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <rich:spacer width="20px"/>
                        <a4j:commandLink id="gerarRelatorio" ajaxSingle="false"
                                         styleClass="pure-button pure-button-primary"
                                         action="#{ListaGymPassRelControle.gerarRelatorio}"
                                         oncomplete="#{ListaGymPassRelControle.oncomplete}"
                                         accesskey="2" reRender="form">
                            Consultar&nbsp<i class="fa-icon-search"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>    
        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
    jQuery.noConflict();
</script>


<%-- 
    Document   : include_modal_justificativaLiberacaoAcesso
    Created on : 16/01/2012, 16:37:26
    Author     : carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" src="../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<a4j:outputPanel>
    <rich:modalPanel id="modalJustificativa" autosized="false" shadowOpacity="true" width="800" height="350" showWhenRendered="#{FechamentoAcessosControleRel.abrirModalJustificativa}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText styleClass="tituloCamposNegrito" value="#{FechamentoAcessosControleRel.tituloModalPanelJustificativa}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form>
                    <a4j:commandButton image="/imagens/close.png" style="cursor:pointer"
                                       action="#{FechamentoAcessosControleRel.fecharPanelJustificativa}"
                                       oncomplete="#{rich:component('modalJustificativa')}.hide();"
                                       id="hidelink12" reRender="form"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>
        <h:form id="formJustificativa">
            <rich:panel>
                <h:panelGrid columns="2" width="100%" cellpadding="0" cellspacing="5" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerdaMenor, classDireitaMaior" >
                    <h:outputText styleClass="tituloCampos" value="Data de Acesso:"/>
                    <h:outputText id="dataAcesso"
                                  value="#{FechamentoAcessosControleRel.liberacaoAcessoVO.dataHora}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                    <h:outputText styleClass="tituloCampos" value="Sentido:"/>
                    <h:panelGroup>
                        <h:outputText id="sentido"
                                      value="#{FechamentoAcessosControleRel.liberacaoAcessoVO.sentido.descricao}"/>
                        <rich:spacer width="10"/>
                        <h:outputText styleClass="tituloCampos" value="Local:"/>
                        <rich:spacer width="2"/>
                        <h:panelGroup>
                            <h:outputText id="localAcesso"
                                          value="#{FechamentoAcessosControleRel.liberacaoAcessoVO.localAcesso.descricao}"/>
                        </h:panelGroup>
                        <rich:spacer width="60"/>
                        <h:outputText id="posicao" styleClass="tituloboxcentro" style="text-align:right;"
                                      value="#{FechamentoAcessosControleRel.posicaoObjetoListaLiberacoes} -
                                      #{fn:length(FechamentoAcessosControleRel.listaLiberacoes)}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Usuário que Liberou:"/>
                    <h:panelGroup>
                        <h:outputText id="usuarioLiberou"
                                      value="#{FechamentoAcessosControleRel.liberacaoAcessoVO.usuario.nome}"/>

                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Tipo Liberação:"/>
                    <h:panelGroup>
                        <h:outputText id="tipoLiberacao"
                                      value="#{FechamentoAcessosControleRel.liberacaoAcessoVO.tipoLiberacao.descricao}"/>

                    </h:panelGroup>
                    <h:outputText rendered="#{not empty FechamentoAcessosControleRel.nomeLabelPessoa}" id="nomePessoa"
                                  styleClass="tituloCampos" value="#{FechamentoAcessosControleRel.nomeLabelPessoa}:"/>
                    <h:panelGroup rendered="#{not empty FechamentoAcessosControleRel.nomeLabelPessoa}">
                        <h:inputText id="nome" size="50" value="#{FechamentoAcessosControleRel.liberacaoAcessoVO.pessoa.nome}"
                                     onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" tabindex="1"/>
                        <rich:suggestionbox height="200" width="200"
                                            for="nome"
                                            fetchValue="#{result.nome}"
                                            suggestionAction="#{FechamentoAcessosControleRel.executarAutocompleteConsultaPessoa}"
                                            minChars="1" rowClasses="20"
                                            status="statusHora"
                                            nothingLabel="Nenhuma Pessoa encontrada !"
                                            var="result"  id="suggestionNomePessoa">
                            <a4j:support event="onselect" oncomplete="#{rich:element('justificativa')}.focus();"
                                         action="#{FechamentoAcessosControleRel.selecionarPessoaSuggestionBox}"/>
                            <h:column>
                                <h:outputText value="#{result.nome}" />
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Justificativa:"/>
                    <h:panelGroup>
                        <h:inputTextarea id="justificativa"  value="#{FechamentoAcessosControleRel.liberacaoAcessoVO.justificativa}"
                                         cols="50" rows="5" onkeyup="this.value = this.value.substring(0, 500);"/>
                    </h:panelGroup>
                </h:panelGrid>
                <center>
                    <a4j:commandButton
                        id="gravarJustificativaAcesso"
                        value="Gravar" image="../imagens/btn_gravar.png" reRender="formJustificativa,form,nomePessoa"
                        action="#{FechamentoAcessosControleRel.gravarJustificativa}" oncomplete="#{rich:element('nome')}.focus();"
                        actionListener="#{FechamentoAcessosControleRel.setarBotaoGravar}">
                    </a4j:commandButton>
                    <rich:spacer width="5"/>
                    <a4j:commandButton rendered="#{FechamentoAcessosControleRel.posicaoObjetoListaLiberacoes!=1}"
                        value="Primeiro" reRender="formJustificativa,form,nomePessoa,modalConfirmaGravacao" image="../imagens/btn_primeiro.png"
                        action="#{FechamentoAcessosControleRel.analisarPrimeiroItem}"
                        oncomplete="#{FechamentoAcessosControleRel.abrirModalPanelConfirmacaoGravado}"/>
                    <rich:spacer width="5"/>
                    <a4j:commandButton rendered="#{fn:length(FechamentoAcessosControleRel.listaLiberacoes)!=FechamentoAcessosControleRel.posicaoObjetoListaLiberacoes}"
                                       value="Próximo" image="../imagens/btn_proximo.png"
                                       reRender="formJustificativa,modalConfirmaGravacao, form, nomePessoa"
                                       action="#{FechamentoAcessosControleRel.analisarProximoItem}"
                                       oncomplete="#{FechamentoAcessosControleRel.abrirModalPanelConfirmacaoGravado}"/>
                    <rich:spacer width="5"/>
                    <a4j:commandButton rendered="#{fn:length(FechamentoAcessosControleRel.listaLiberacoes)!=FechamentoAcessosControleRel.posicaoObjetoListaLiberacoes}"
                        value="Último"  reRender="formJustificativa,form,nomePessoa,modalConfirmaGravacao" image="../imagens/btn_ultimo.png"
                        action="#{FechamentoAcessosControleRel.analisarUltimoItem}"
                        oncomplete="#{FechamentoAcessosControleRel.abrirModalPanelConfirmacaoGravado}"/>

                </center>
                <rich:spacer width="5"/>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{FechamentoAcessosControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{FechamentoAcessosControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:panel>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>
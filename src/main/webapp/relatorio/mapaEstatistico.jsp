<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Saldo de Conta Corrente do Cliente</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Mapa estatístico"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:MapaEstatistico"/>


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form" target="_blank">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid id="panelFiltros" columns="1" columnClasses="colunaTopCentralizada" width="100%">
                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita" width="100%"
                                 style="padding-left:0px;">

                        <!-- empresa -->
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      value="EMPRESA:"/>
                        <h:panelGroup styleClass="font-size-em-max">
                            <div class="cb-container margenVertical">
                                <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form texto-size-12-real texto-cor-cinza texto-font"
                                                 value="#{MapaEstatisticoControle.empresa.codigo}">
                                    <f:selectItems value="#{MapaEstatisticoControle.listaEmpresas}"/>
                                    <a4j:support event="onchange" action="#{MapaEstatisticoControle.montarModalidades}"
                                                 reRender="form:panelFiltros"/>
                                </h:selectOneMenu>
                            </div>
                        </h:panelGroup>

                        <!-- modalidade -->
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                      value="MODALIDADE:"/>
                        <h:panelGroup styleClass="font-size-em-max">
                            <div class="cb-container margenVertical">
                                <h:selectOneMenu id="modalidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="form texto-size-12-real texto-cor-cinza texto-font"
                                                 value="#{MapaEstatisticoControle.modalidadeVO.codigo}">
                                    <f:selectItems value="#{MapaEstatisticoControle.listaModalidade}"/>
                                </h:selectOneMenu>
                            </div>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>


            <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                <h:panelGroup>
                    <rich:spacer width="20px"/>
                    <a4j:commandLink id="imprimirPDF"
                                     accesskey="2"
                                     styleClass="botoes nvoBt"
                                     reRender="form">
                        <i class="fa-icon-print"></i> Gerar Relatório (PDF)
                    </a4j:commandLink>

                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

</f:view>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@include file="/includes/include_import_minifiles.jsp" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../hoverform.js"></script>
    <link href="../css/otimize.css" rel="stylesheet" type="text/css">
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

    <link href="./css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
    <link href="./css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
</head>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Saldo_Credito_tituloForm}"/>
    </title>
     <style>
         .spinerFontAwesome{
             display: inline-block;
             vertical-align: middle;
         }
         .spinerFontAwesome .rich-spinner-input{
             width: 24px;
             text-align: right;
             font-size: 1.1em;
             color: #29ABE2;
             font-family: Arial;
         }
         .spinerFontAwesome .rich-spinner-input:focus{
             outline: none;
             border: 1px solid #DDDDDD;
             border-radius: 2px;
         }
         .spinerFontAwesome .rich-spinner-btn {
             width: 10px;
             margin: 5px;
         }
         .spinerFontAwesome .rich-spinner-buttons,.spinerFontAwesome .rich-spinner-input-container,.spinerFontAwesome .rich-spinner-input-container{
             background-image: none;
             border-style: none;
             border:none;
             background-color: transparent;
         }

     </style>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Saldo_Credito_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Relatorios:Saldo_De_Creditos"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="../topoReduzido_material.jsp"/>
            </c:if>
        </f:facet>
        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
        <h:form id="form">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <div align="center">
                <h:panelGrid columns="1" width="100%">
                    <h:panelGrid id="gridFiltros" columns="1"  width="100%" >
                        <h:panelGrid rendered="#{SaldoCreditoRelControle.permissaoConsultaTodasEmpresas}" width="100%">
                            <h:panelGroup layout="block" styleClass="col-text-align-center">
                                <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical" value="EMPRESA" />
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="col-text-align-center">
                                <h:panelGroup  styleClass="cb-container margenVertical" layout="block">
                                    <h:panelGroup>
                                        <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{SaldoCreditoRelControle.filtroEmpresa}" >
                                            <f:selectItems value="#{SaldoCreditoRelControle.listaEmpresas}" />
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                            <rich:spacer height="10px"/>
                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        </h:panelGrid>
                        <rich:jQuery query="addClass('teste')" selector=".item89"/>
                            <h:panelGroup layout="block" styleClass="col-text-align-center" rendered="#{LoginControle.permissaoAcessoMenuVO.permissaoFreePass}">
                                <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical" value="SITUAÇÃO" />
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="col-text-align-center">
                                <a4j:commandLink id="checkAtivo" status="none" styleClass="texto-cor-azul botao-checkbox texto-size-14"
                                                 reRender="checkAtivo">
                                    <h:outputText styleClass="icon #{SaldoCreditoRelControle.situacaoAtivo ? 'fa-icon-check' : 'fa-icon-check-empty'}"/>
                                    <h:outputText styleClass="label" value="#{msg_aplic.prt_Saldo_Credito_ativo}"/>
                                    <f:setPropertyActionListener value="#{!SaldoCreditoRelControle.situacaoAtivo}"
                                                                 target="#{SaldoCreditoRelControle.situacaoAtivo}"/>
                                </a4j:commandLink>
                                <rich:spacer width="15px;"/>
                                <a4j:commandLink id="checkInativo" status="none" styleClass="texto-cor-azul botao-checkbox texto-size-14"
                                                 reRender="checkInativo">
                                    <h:outputText styleClass="icon #{SaldoCreditoRelControle.situacaoInativo ? 'fa-icon-check' : 'fa-icon-check-empty'}"/>
                                    <h:outputText styleClass="label" value="#{msg_aplic.prt_Saldo_Credito_inativo}"/>
                                    <f:setPropertyActionListener value="#{!SaldoCreditoRelControle.situacaoInativo}"
                                                                 target="#{SaldoCreditoRelControle.situacaoInativo}"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                        <rich:spacer height="10px"/>
                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        <h:panelGrid width="100%">
                                <h:panelGroup layout="block" styleClass="col-text-align-center" >
                                    <h:outputText   styleClass="rotuloCampos margenVertical" value="Pesquisar de " />
                                <rich:inputNumberSpinner value="#{SaldoCreditoRelControle.creditosDe}"
                                                         style="width: 50px;"
                                                         styleClass="tableSpiner spinerFontAwesome tooltipster tabelaSemBorda"
                                                         maxValue="999"
                                                         inputStyle="width: 33px;"
                                                         minValue="0"
                                                         onmouseover="adicionarTooltipster(this,'Quantidade');"
                                                         cycled="false">
                                </rich:inputNumberSpinner>
                                    <h:outputText   styleClass="rotuloCampos margenVertical" value=" até " />
                                <rich:inputNumberSpinner value="#{SaldoCreditoRelControle.creditosAte}"
                                                         style="width: 50px;"
                                                         styleClass="tableSpiner spinerFontAwesome tooltipster tabelaSemBorda"
                                                         maxValue="999"
                                                         inputStyle="width: 33px;"
                                                         minValue="0"
                                                         onmouseover="adicionarTooltipster(this,'Quantidade');"
                                                         cycled="false">
                                </rich:inputNumberSpinner>
                                    <h:outputText  styleClass="rotuloCampos margenVertical" value=" créditos." />
                                </h:panelGroup>
                                <script>
                                        jQuery('.spinerFontAwesome table tbody tr:first-child .rich-spinner-btn').attr('src','../imagens_flat/caret-up.png');
                                        jQuery('.spinerFontAwesome table tbody tr:last-child .rich-spinner-btn').attr('src','../imagens_flat/caret-down.png');
                                        function adicionarTooltipster(eCall,text){
                                            carregarTooltipster();
                                            if (!jQuery(eCall).hasClass('tooltipsteredEl')) {
                                                jQuery(eCall).tooltipster('content', text);
                                                jQuery(eCall).tooltipster('show');
                                                jQuery(eCall).addClass('tooltipsteredEl');
                                            }
                                        }
                                </script>
                        </h:panelGrid>
                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        <h:panelGrid styleClass="col-text-align-center" width="100%">
                            <rich:spacer height="20px"/>
                            <div>
                                <a4j:commandLink id="imprimir"
                                                 styleClass="botaoPrimarioGrande texto-size-14 linkPadrao"
                                                 action="#{SaldoCreditoRelControle.imprimir}"
                                                 oncomplete="#{SaldoCreditoRelControle.mensagemNotificar}"
                                                 accesskey="2">
                                    <i class="fa-icon-search" style=" padding-bottom: 10px;"></i> &nbsp Gerar Relatório
                                </a4j:commandLink>
                            </div>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </div>
        </h:form>
    </h:panelGrid>
</f:view>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .alinharMeio tbody tr td label {
        vertical-align: bottom;
        padding-right: 10px;
    }

</style>
<script>
    function verificarCamposLimpos() {

        var nomeCliente = document.getElementById('form:nomeCliente').value;
        var nomeCol = document.getElementById('form:nomeColaborador').value;

        if (nomeCol === '') {
            document.getElementById('form:limparColaborador').click();
        }

        if (nomeCliente === '') {
            document.getElementById('form:limparCliente').click();
        }

    }
</script>
<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_TotalizadorFrequencia_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_TotalizadorFrequencia_tituloForm}"/>
    <c:set var="urlWiki" scope="session"
           value="${SuperControle.urlWiki}Relatorios:Relatorios_de_Acessos#Totalizador_de_Acessos"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="../topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid id="dadosConsulta" columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{TotalizadorFrequenciaControleRel.permissaoConsultaTodasEmpresas}"
                                  value="#{msg_aplic.prt_CompetenciaSintetico_empresa}"/>
                    <h:panelGroup rendered="#{TotalizadorFrequenciaControleRel.permissaoConsultaTodasEmpresas}">
                        <h:selectOneMenu id="empresa" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{TotalizadorFrequenciaControleRel.filtroEmpresa}">
                            <f:selectItems value="#{TotalizadorFrequenciaControleRel.listaEmpresas}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_TotalizadorFrequencia_periodoPesquisa}"/>
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="10px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ate}"/>
                        <rich:spacer width="10px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>

                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                         query="mask('99/99/9999')"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Sexo biológico:"/>
                    <h:selectOneMenu
                            value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.generoSelecionado}">
                        <f:selectItems
                                value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.listaGeneros}"/>
                    </h:selectOneMenu>

                    <h:outputText styleClass="tituloCampos" value="Faixa Etária:"/>
                    <h:panelGrid columns="5">
                        <rich:inputNumberSpinner style="width: 35px"
                                                 value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.idadeInicio}"/>
                        <rich:spacer width="10"/>
                        <h:outputText styleClass="tituloCampos" value="até"/>
                        <rich:spacer width="10"/>
                        <rich:inputNumberSpinner style="width: 35px"
                                                 value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.idadeFim}"/>
                    </h:panelGrid>


                    <h:outputText id="nomePlano" styleClass="tituloCampos" value="Plano:"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="plano" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{TotalizadorFrequenciaControleRel.codigoPlanoSelecionado}">
                            <f:selectItems value="#{TotalizadorFrequenciaControleRel.selectItemPlano}"/>
                            <a4j:support
                                    event="onchange" ajaxSingle="true"
                                    action="#{TotalizadorFrequenciaControleRel.obterPlanoEscolhido}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText id="nomeModalidade" styleClass="tituloCampos" value="Modalidade:"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="modalidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         value="#{TotalizadorFrequenciaControleRel.codigoModalidadeSelecionada}">
                            <f:selectItems value="#{TotalizadorFrequenciaControleRel.selectItemModalidade}"/>
                            <a4j:support
                                    event="onchange" requestDelay="100"
                                    action="#{TotalizadorFrequenciaControleRel.obterModalidadeEscolhido}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Cliente:"/>
                    <h:panelGroup id="panelCliente">
                        <h:inputText id="nomeCliente" size="50"
                                     maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"/>
                        <rich:suggestionbox height="200" width="200"
                                            for="nomeCliente"
                                            fetchValue="#{result.pessoa.nome}"
                                            suggestionAction="#{TotalizadorFrequenciaControleRel.executarAutocompleteConsultaCliente}"
                                            minChars="1" rowClasses="20"
                                            status="statusHora"
                                            nothingLabel="Nenhum cliente encontrado"
                                            var="result" id="suggestionNomeCliente" reRender="mensagem">
                            <a4j:support event="onselect"
                                         reRender="panelCliente"
                                         action="#{TotalizadorFrequenciaControleRel.selecionarClienteSuggestionBox}"/>
                            <h:column>
                                <h:outputText value="#{result.pessoa.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                        <a4j:commandButton id="limparCliente"
                                           onclick="document.getElementById('form:nomeCliente').value = null;"
                                           image="/images/limpar.gif" title="Limpar cliente."
                                           status="false"
                                           reRender="form"
                                           action="#{TotalizadorFrequenciaControleRel.limparCliente}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Colaborador:"/>
                    <h:panelGroup id="panelColaborador">
                        <h:inputText id="nomeColaborador" size="50"
                                     maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"/>


                        <rich:suggestionbox height="200" width="200"
                                            for="nomeColaborador"
                                            fetchValue="#{resultCol.pessoa.nome}"
                                            suggestionAction="#{TotalizadorFrequenciaControleRel.executarAutocompleteConsultaColaborador}"
                                            minChars="1" rowClasses="20"
                                            status="statusHora"
                                            nothingLabel="Nenhum colaborador encontrado"
                                            var="resultCol" id="suggestionNomeColaborador" reRender="mensagem">
                            <a4j:support event="onselect"
                                         reRender="panelColaborador"
                                         action="#{TotalizadorFrequenciaControleRel.selecionarColaboradorSuggestionBox}"/>
                            <h:column>
                                <h:outputText value="#{resultCol.pessoa.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                        <a4j:commandButton id="limparColaborador"
                                           onclick="document.getElementById('form:nomeColaborador').value = null;"
                                           image="/images/limpar.gif" title="Limpar colaborador."
                                           status="false"
                                           reRender="form"
                                           action="#{TotalizadorFrequenciaControleRel.limparColaborador}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_TotalizadorFrequencia_frequencia}"/>
                    <h:panelGroup layout="block" style="height: 20px">
                        <h:selectOneRadio id="ordenacao" styleClass="tituloCampos alinharMeio"
                                          value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia}">
                            <f:selectItems value="#{TotalizadorFrequenciaControleRel.listaSelectItemFrequencia}"/>
                            <a4j:support event="onchange" reRender="dadosConsulta"
                                         action="#{TotalizadorFrequenciaControleRel.alterarFrequencia}"/>
                        </h:selectOneRadio>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Agrupar por pessoa:"
                                  rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia == 1}"/>
                    <h:selectBooleanCheckbox id="agruparPorPessoa"
                                             rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia == 1}"
                                             styleClass="tituloCampos alinharMeio"
                                             value="#{TotalizadorFrequenciaControleRel.agruparPorPessoa}">
                        <a4j:support event="onchange"
                                     reRender="dadosConsulta,form:containeSomenteAcessoDia,form:panelGrafico"
                                     action="#{TotalizadorFrequenciaControleRel.marcarAgrupar}"/>
                    </h:selectBooleanCheckbox>

                    <h:outputText id="textoSomenteAcessoDia" styleClass="tituloCampos"
                                  rendered="#{!TotalizadorFrequenciaControleRel.agruparPorPessoa}"
                                  value="#{msg_aplic.prt_TotalizadorFrequencia_contabilizarAcessoDia}"/>
                    <h:panelGroup layout="block" id="containeSomenteAcessoDia"
                                  rendered="#{!TotalizadorFrequenciaControleRel.agruparPorPessoa}">
                        <h:selectBooleanCheckbox id="somenteAcessoDia"
                                                 styleClass="tituloCampos alinharMeio"
                                                 value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.somenteAcessoDia}">
                        </h:selectBooleanCheckbox>
                        <rich:toolTip for="textoSomenteAcessoDia"
                                      value="Irá considerar somente o primeiro acesso do cliente à academia no dia."></rich:toolTip>
                    </h:panelGroup>

                    <h:outputText
                            rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia == 2}"
                            styleClass="tituloCampos" value="#{msg_aplic.prt_TotalizadorFrequencia_agrupamento}"/>
                    <h:panelGroup
                            rendered="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.frequencia == 2}"
                            layout="block" style="height: 20px">
                        <h:selectOneRadio styleClass="tituloCampos alinharMeio"
                                          value="#{TotalizadorFrequenciaControleRel.totalizadorFrequenciaRel.agrupamento}">
                            <f:selectItem itemValue="NENHUM" itemLabel="Nenhum"/>
                            <f:selectItem itemValue="SEXTA_SABADO" itemLabel="Agrupar Sexta e Sábado"/>
                        </h:selectOneRadio>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_TotalizadorFrequencia_grafico}"
                                  rendered="#{!TotalizadorFrequenciaControleRel.agruparPorPessoa}"/>
                    <h:panelGroup layout="block" style="height: 20px" id="panelGrafico"
                                  rendered="#{!TotalizadorFrequenciaControleRel.agruparPorPessoa}">
                        <h:selectOneRadio id="grafico" styleClass="tituloCampos alinharMeio"
                                          value="#{TotalizadorFrequenciaControleRel.grafico}">
                            <f:selectItems value="#{TotalizadorFrequenciaControleRel.listaSelectItemGrafico}"/>
                        </h:selectOneRadio>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem" value="#{TotalizadorFrequenciaControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{TotalizadorFrequenciaControleRel.mensagemDetalhada}"/>
                        <h:panelGroup layout="block" style="height:60px;margin: 0 auto;width: 20%;">
                            <a4j:commandLink id="imprimirPDF" action="#{TotalizadorFrequenciaControleRel.imprimir}"
                                             styleClass="pure-button pure-button-primary"
                                             oncomplete="#{TotalizadorFrequenciaControleRel.abrirPopUp}"
                                             accesskey="2"
                                             reRender="mensagem">
                                &nbsp Gerar Relatório
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>

                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>


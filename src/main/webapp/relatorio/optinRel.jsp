<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="../includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="../bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>
<title>Relatório de Opt-in</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<style type="text/css">
    .notificacaoAtividades {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        background-clip: padding-box;
        background: #819aa5 -webkit-linear-gradient(top, #96acb6,
        #5d7b89);
        box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        color: rgb(255, 255, 255) !important;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        height: 13px !important;
        line-height: normal;
        list-style-type: none;
        padding: 1px 3px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        zoom: 1;
        border-radius: 40%;
    }

    .iconCalendar {
        width: 17px;
        height: 17px;
        padding-top: 6px;
        padding-left: 5px;
    }

    input.inputs, select.inputs {
        padding: 5px;
        background-image: none !important;
        font-size: 12px !important;
        border-radius: 4px;
        color: #A1A5AA;
        border: 1px solid #DCDDDF;
        width: 80%;
    }

    .separador-horizontal {
        margin: 10px 0px 10px 0px;
        width: 100%;
        height: 1px;
        border-bottom: 1px solid #E5E5E5;
    }

    .tituloCampos {
        color: #51555A !important;
        text-align: right;
    }

    .iconeDentro {
        margin-left: -23px;
    }

    .width100 {
        width: 100% !important;
    }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Optin_tituloRel}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-opt-in/"/>

    <h:panelGrid id="panelFiltros" columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="../topoReduzido_material_crm.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="../topoReduzidoCRM.jsp"/>
            </c:if>

        </f:facet>

        <h:form id="v20_form" target="_blank">

            <h:panelGroup styleClass="separador-horizontal" layout="block"/>
            <h:panelGrid columns="4" cellpadding="0" cellspacing="0">
                <h:panelGrid columns="2"
                             style="font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 10px; width: 250px; box-shadow: 0px 2px 4px #E4E5E6;"
                             cellspacing="0" cellpadding="0">

                    <h:outputText styleClass="tituloCampos" value="TOTAL ACEITO"
                                  style="color: black; float: left"></h:outputText>
                    <h:outputText styleClass="tituloCampos" value="#{MalaDiretaControle.totalAceito}"
                                  style="float: right"></h:outputText>

                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>
                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>

                    <h:outputText styleClass="tituloCampos" value="TOTAL NÃO QUER RECEBER"
                                  style="color: black; float: left"></h:outputText>
                    <h:outputText styleClass="tituloCampos" value="#{MalaDiretaControle.totalnaoAceito}"
                                  style="color: black; float: right"></h:outputText>

                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>
                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>

                    <h:outputText styleClass="tituloCampos" value="TOTAL SEM RESPOSTA"
                                  style="color: black; float: left"></h:outputText>
                    <h:outputText styleClass="tituloCampos" value="#{MalaDiretaControle.totalSemResposta}"
                                  style="color: black; float: right"></h:outputText>

                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>
                    <h:panelGroup styleClass="separador-horizontal" layout="block"/>

                    <h:outputText styleClass="tituloCampos" value="TOTAL"
                                  style="color: black; float: left; font-weight: bold"></h:outputText>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{MalaDiretaControle.totalSemResposta + MalaDiretaControle.totalnaoAceito + MalaDiretaControle.totalAceito }"
                                  style="color: black; float: right; font-weight: bold"></h:outputText>

                </h:panelGrid>
                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #DB2C3D; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-repeat.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"></h:graphicImage>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Não quer receber" style="font-size: 16px; color: #BDC3C7; "></h:outputText>
                        <h:outputText value="#{MalaDiretaControle.totalnaoAceito}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"></h:outputText>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #81D742; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-check-circle.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"></h:graphicImage>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Aceito" style="font-size: 16px; color: #BDC3C7; "></h:outputText>
                        <h:outputText value="#{MalaDiretaControle.totalAceito}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"></h:outputText>

                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="2"
                             style="display: inline-flex;
    float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #80858C; box-sizing: border-box;"
                             cellspacing="0" cellpadding="0">
                    <h:graphicImage url="/images/pct-flag.jpg"
                                    style="padding-left: 40px; padding-right: 35px;"></h:graphicImage>
                    <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                        <h:outputText value="Sem Resposta" style="font-size: 16px; color: #BDC3C7; "></h:outputText>
                        <h:outputText value="#{MalaDiretaControle.totalSemResposta}"
                                      style="font-weight: bold; font-size: 32px; color: #51555A;"></h:outputText>

                        <a4j:commandLink onclick="if(!confirm('Confirma o reenvio do e-mail de optin?')){return true;}"
                            action="#{MalaDiretaControle.enviarOptin}"
                                         oncomplete="alert(' Foram enviados, #{MalaDiretaControle.totalSemResposta } e-mails  com sucesso'); window.close()"
                        >
                        <h:outputText
                                value="Reenviar"
                                style="background-color: #80858C; font-weight: bold; font-size: 12px; color: #FFFFFF; padding: 1px 7px 1px 7px;"></h:outputText>
                        </a4j:commandLink>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

</f:view>

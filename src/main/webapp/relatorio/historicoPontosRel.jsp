<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="../includes/imports.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style>
    .tamanhoInputPequeno{
        width: 135px;
    }
    .tamanhoInputMedio{
        width: 270px;
    }
    .tamanhoInputGrande{
        width: 430;
    }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Pontuacao_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}onde-consigo-ver-o-historico-de-pontos-do-clube-de-vantagens/"/>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Pontuacao_tituloForm}"/>
    </title>


    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>
        
        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
            </h:panelGrid>
            <h:panelGroup id="panelCaixaAberto"  layout="block" style="margin-left: 15px;">
                <h:panelGrid id="panelGeral" columns="1" >
                    <h:panelGrid rendered="#{HistoricoPontosControle.permissaoConsultaTodasEmpresas}">
                        <h:outputText  style="display: block" styleClass="rotuloCampos margenVertical" value="EMPRESA" />
                        <h:panelGroup styleClass="font-size-em-max">
                            <h:panelGroup  styleClass="cb-container margenVertical" layout="block">
                                <h:panelGroup>
                                    <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{HistoricoPontosControle.filtroEmpresa}" style="font-size: 14px !important;" >
                                        <f:selectItems value="#{HistoricoPontosControle.listaEmpresas}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <rich:spacer height="10px"/>
                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    </h:panelGrid>
                    <h:panelGroup>
                        <div id="botoesTL" style="display: inline-flex; padding: 10px;">
                            <a4j:commandLink styleClass="botaoModoTimeLine sintetico step4 tudo}" id="selecionarResumido"
                                             style="margin-left: -10px;"
                                             oncomplete="jQuery('.sintetico').addClass('ativo');"
                                             action="#{HistoricoPontosControle.fazerPesquisaSintetica}"
                                             reRender="form:panelGeral"
                                             status="false">
                                Resumido
                            </a4j:commandLink>
                            <a4j:commandLink styleClass="botaoModoTimeLine detalhado step4 tudo}" id="selecionarDetalhado"
                                             style="margin-left: 10px;"
                                             oncomplete="jQuery('.detalhado').addClass('ativo');carregarMaskInput();"
                                             action="#{HistoricoPontosControle.fazerPesquisaDetalhada}"
                                             reRender="form:panelGeral"
                                             status="false">
                                Detalhado
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>
                    <rich:spacer height="20px"/>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_Pontuacao_situacaoAluno_Maisculo}" />
                    <h:panelGroup>
                        <h:panelGroup>
                            <h:panelGroup styleClass="chk-fa-container">
                                 <h:selectBooleanCheckbox  value="#{HistoricoPontosControle.clienteAtivo}"  id="selecionarcheckAlunosAtivos"/>
                                 <span/>
                            </h:panelGroup>   
                         <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" style="margin-left: 1px;" value="#{msg_aplic.ATIVO}" />
                        </h:panelGroup>
                        <h:panelGroup style="margin-left: 38px;">
                            <h:panelGroup styleClass="chk-fa-container">
                                 <h:selectBooleanCheckbox value="#{HistoricoPontosControle.clienteInativo}" id="selecionarcheckAlunosInativos"/>
                                 <span/>
                            </h:panelGroup>
                         <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"  style="margin-left: 1px;" value="#{msg_aplic.INATIVO}" />
                        </h:panelGroup>
                    </h:panelGroup>
                    <rich:spacer height="20px"/>
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_Cliente_label_nome_maiusculo}" />
                    <h:panelGroup id="panelCliente">
                        <h:inputText  id="nomeCliente" style="font-size: 14px !important;" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="inputTextClean"/>
                        <rich:suggestionbox
                                height="200" width="444"
                                for="nomeCliente"
                                fetchValue="#{result.pessoa.nome}"
                                suggestionAction="#{HistoricoPontosControle.executarAutocompleteConsultaCliente}"
                                minChars="1" rowClasses="20"
                                status="statusHora"
                                nothingLabel="Nenhum Cliente encontrado !"
                                var="result" id="suggestionNomeCliente"
                                reRender="panelBotoesControle, mensagem">
                            <a4j:support event="onselect"
                                         action="#{HistoricoPontosControle.selecionarClienteSuggestionBox}"
                                         reRender="panelBotoesControle, mensagem"/>
                            <h:column>
                                <h:outputText styleClass="texto-font texto-size-14-real" value="#{result.pessoa.nome}"/>
                            </h:column>
                            <h:column>
                                <h:outputText styleClass="texto-font texto-size-14-real" value="#{result.situacao_Apresentar}"/>
                            </h:column>
                        </rich:suggestionbox>

                        <a4j:commandLink id="limparCliente"
                                           onclick="document.getElementById('form:nomeCliente').value = null;"
                                           title="Limpar aluno."
                                           status="false"
                                           action="#{HistoricoPontosControle.limparCliente}"
                                           reRender="gridDados, mensagem">
                            <i class="fa-icon-remove texto-size-14-real linkAzul"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                    <rich:spacer height="10px"/>
                    <h:outputText  id="labelPeriodo" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="POR PERÍODO" rendered="#{HistoricoPontosControle.pesquisaDetalhada}"/>
                    <h:panelGrid>
                        <h:panelGroup rendered="#{HistoricoPontosControle.pesquisaDetalhada}" id="panelPeriodo">
                            <h:panelGroup styleClass="dateTimeCustom"  style="position: absolute; font-size: 11px !important;">
                                <rich:calendar value="#{HistoricoPontosControle.dataInicial}"
                                               id="dataInicial"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                            </h:panelGroup>
                                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" value="até" style="padding-left: 9em;"/>
                            <h:panelGroup styleClass="dateTimeCustom"  style="position: absolute;font-size: 11px !important;margin-left: 15px;">
                                <rich:calendar value="#{HistoricoPontosControle.dataFinal}"
                                               id="dataFinal"
                                               inputSize="10"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGrid>

                    <rich:spacer height="10px"/>
                    <h:outputText id="labelBrinde" style="display: block" styleClass="rotuloCampos margenVertical" value="#{msg_aplic.prt_Brinde_tituloForm_Maisculo}" rendered="#{HistoricoPontosControle.pesquisaDetalhada}" />
                    <h:panelGroup styleClass="font-size-em-max" rendered="#{HistoricoPontosControle.pesquisaDetalhada}" id="panelBrinde">
                        <h:panelGroup  styleClass="cb-container margenVertical" layout="block">
                            <h:panelGroup>
                                <h:selectOneMenu  id="mostrartodos" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{HistoricoPontosControle.brindeSelecionado.codigo}" style="font-size: 14px !important;" >
                                    <f:selectItems value="#{HistoricoPontosControle.listaDeBrindes}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{HistoricoPontosControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoPontosControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" style="text-align: center;font-family: Arial;font-size: 14px;">
                        <h:panelGroup>
                            <a4j:commandLink id="gerarRelatorioPontos" reRender="mensagem"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{HistoricoPontosControle.gerarRelatorio}"
                                             accesskey="2">
                                <i class="fa-icon-print"></i> Gerar Relatório
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
            </h:panelGrid>
            <h:panelGroup layout="block" id="containerFuncMask">
                <script>
                    carregarMaskInput();
                    jQuery('.sintetico').addClass('ativo');
                </script>
            </h:panelGroup>

        </h:form>
    </h:panelGrid>
</f:view>
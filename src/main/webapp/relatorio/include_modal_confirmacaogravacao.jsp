<%--
    Document   : include_modal_justificativaLiberacaoAcesso
    Created on : 16/01/2012, 16:37:26
    Author     : carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" src="../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<a4j:outputPanel>
    <rich:modalPanel id="modalConfirmaGravacao" autosized="false" width="400" height="130"  shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkmodalConfirmaGravacao"/>
                <rich:componentControl for="modalConfirmaGravacao" attachTo="hidelinkmodalConfirmaGravacao" operation="hide"  event="onclick" />
            </h:panelGroup>
        </f:facet>
        <h:form id="formConfirmaGravacao">
            <rich:panel>
                <h:outputText styleClass="tituloDemonstrativo" value="A justificativa do acesso atual não foi gravada, deseja gravar ?"/>

            </rich:panel>
            <center>
                <a4j:commandButton id="sim"
                                   action="#{FechamentoAcessosControleRel.gravarJustificativa}"
                                   image="/imagens/botaoSim.png"
                                   reRender="formJustificativa,modalJustificativa,modalConfirmaGravacao"
                                   styleClass="botoes">
                </a4j:commandButton>
                <rich:spacer width="30px;"></rich:spacer>
                <a4j:commandButton rendered="#{!FechamentoAcessosControleRel.clicouNoPrimeiroItem && !FechamentoAcessosControleRel.clicouNoUltimoItem}"
                                   id="naoProximoItem" reRender="formJustificativa,modalJustificativa,modalConfirmaGravacao, form"
                                   action="#{FechamentoAcessosControleRel.proximoItem}"
                                   image="/imagens/botaoNao.png"/>
                <a4j:commandButton rendered="#{FechamentoAcessosControleRel.clicouNoPrimeiroItem}"
                                   id="naoPrimeiroItem" reRender="formJustificativa,modalJustificativa,modalConfirmaGravacao, form"
                                   action="#{FechamentoAcessosControleRel.primeiroItem}"
                                   image="/imagens/botaoNao.png"/>
                <a4j:commandButton rendered="#{FechamentoAcessosControleRel.clicouNoUltimoItem}"
                                   id="naoUltimoItem" reRender="formJustificativa,modalJustificativa,modalConfirmaGravacao, form"
                                   action="#{FechamentoAcessosControleRel.ultimoItem}"
                                   image="/imagens/botaoNao.png"/>
            </center>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>
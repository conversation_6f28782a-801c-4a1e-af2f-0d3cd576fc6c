<%-- 
    Document   : fechamentoAcessosRel
    Created on : 11/01/2012, 16:38:56
    Author     : carla
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" src="../script/demonstrativoFinan.js"></script>
<script type="text/javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<head> <%@include file="/includes/include_import_minifiles.jsp" %></head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <%@include file="../relatorio/include_modal_justificativaGrande.jsp"%>
    <%@include file="../relatorio/include_modal_justificativaLiberacaoAcesso.jsp"%>
    <%@include file="../relatorio/include_modal_confirmacaogravacao.jsp"%>
    <%@include file="../relatorio/include_modal_listaEmailFechamento.jsp"%>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <c:set var="titulo" scope="session" value=" ${msg_aplic.prt_FechamentoAcessos_TituloForm}"/>
        <c:set var="urlWiki" scope="session"  value="${SuperControle.urlBaseConhecimento}relatorio-fechamento-de-acessos/"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="../topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>
        <h:commandLink action="#{FechamentoAcessosControleRel.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
        <a4j:commandLink action="#{FechamentoAcessosControleRel.imprimirRelatorio}" id="imprimirRelatorio" style="display: none" />
        <h:inputHidden id="relatorio" value="#{FechamentoAcessosControleRel.relatorio}" />

        <h:panelGrid columns="1"  width="100%" >
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" >
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid id="panelFiltros" width="100%" columnClasses="colunaDireita">
                                        <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" width="100%">
                                            <!-- empresa -->
                                            <h:panelGroup>
                                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FechamentoAcessos_empresa}"
                                                              rendered="#{FechamentoAcessosControleRel.isPermissaoConsultarInfoTodasEmpresas()}"/>
                                                <h:selectOneMenu id="empresa"  onblur="blurinput(this);" rendered="#{FechamentoAcessosControleRel.isPermissaoConsultarInfoTodasEmpresas()}"
                                                                 onfocus="focusinput(this);" styleClass="form"
                                                                 value="#{FechamentoAcessosControleRel.empresa.codigo}">
                                                    <f:selectItems value="#{FechamentoAcessosControleRel.listaEmpresa}"/>
                                                    <a4j:support
                                                            event="onchange"
                                                            action="#{FechamentoAcessosControleRel.obterEmpresaEscolhida}" />
                                                </h:selectOneMenu>
                                                <rich:spacer width="10"/>
                                                <!-- periodo de acesso -->
                                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ListaAcesso_periodoPesquisa}" />
                                                <h:panelGroup>
                                                    <h:panelGroup>
                                                        <rich:calendar id="dataInicio"
                                                                       value="#{FechamentoAcessosControleRel.dataPeriodoInicial}"
                                                                       inputSize="10"
                                                                       inputClass="form"
                                                                       oninputblur="blurinput(this);"
                                                                       oninputfocus="focusinput(this);"
                                                                       oninputchange="return validar_Data(this.id);"
                                                                       datePattern="dd/MM/yyyy"
                                                                       enableManualInput="true"
                                                                       zindex="2"
                                                                       showWeeksBar="false" >
                                                            <a4j:support event="onchanged" reRender="form"/>
                                                        </rich:calendar>
                                                        <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                                                    </h:panelGroup>
                                                    <rich:spacer width="12px"/>
                                                    <h:panelGroup>
                                                        <h:inputText  id="horaInicial" onkeypress="return mascaraTodos(this.form, 'form:horaInicial', '99:99', event);"
                                                                      size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                                                      value="#{FechamentoAcessosControleRel.horarioPeriodoInicial}" />

                                                        <h:message for="horaInicial" styleClass="mensagemDetalhada"/>
                                                    </h:panelGroup>
                                                    <h:outputText  styleClass="tituloCampos" style="position:relative; top:0px; left:10px;" value="#{msg_aplic.prt_ate}" />
                                                    <rich:spacer width="12px"/>
                                                    <h:panelGroup>
                                                        <rich:calendar id="dataTermino"
                                                                       value="#{FechamentoAcessosControleRel.dataPeriodoFinal}"
                                                                       inputSize="10"
                                                                       inputClass="form"
                                                                       oninputblur="blurinput(this);"
                                                                       oninputfocus="focusinput(this);"
                                                                       oninputchange="return validar_Data(this.id);"
                                                                       datePattern="dd/MM/yyyy"
                                                                       enableManualInput="true"
                                                                       zindex="2"
                                                                       showWeeksBar="true">
                                                            <a4j:support event="onchanged" reRender="form"/>
                                                        </rich:calendar>
                                                        <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                                                    </h:panelGroup>
                                                    <rich:spacer width="12px"/>
                                                    <h:panelGroup>
                                                        <h:inputText  id="horaFinal"
                                                                      onkeypress="return mascaraTodos(this.form, 'form:horaFinal', '99:99', event);" size="5" maxlength="5"
                                                                      onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                                                      value="#{FechamentoAcessosControleRel.horarioPeriodoFinal}" />
                                                        <h:message for="horaFinal" styleClass="mensagemDetalhada"/>
                                                    </h:panelGroup>
                                                </h:panelGroup>
                                                <rich:spacer width="5"/>
                                                <h:selectOneMenu id="periodo"  onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);" styleClass="form"
                                                                 value="#{FechamentoAcessosControleRel.tipoPadraoPeriodo}">
                                                    <f:selectItems value="#{FechamentoAcessosControleRel.listaPeriodosPadrao}"/>
                                                    <a4j:support event="onchange" action="#{FechamentoAcessosControleRel.mudarDatas}" reRender="panelFiltros"/>
                                                </h:selectOneMenu>
                                                <rich:spacer width="10"/>
                                                <h:panelGroup layout="block" styleClass="container-botoes">
                                                    <a4j:commandLink
                                                                   styleClass="botaoPrimario texto-size-14-real"
                                                                   reRender="form"
                                                                   id="pesquisarRelatorioAcesso"
                                                                   action="#{FechamentoAcessosControleRel.pesquisar}" style="vertical-align:middle;">
                                                        <span>Pesquisar</span>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                    <h:panelGrid width="100%" rendered="#{FechamentoAcessosControleRel.pesquisar}" id="resultado1" >
                                        <h:panelGroup>
                                            <h:outputText styleClass="tituloboxcentro" value="Total de Acessos: "/>
                                            <h:outputText styleClass="tituloboxcentro" value="#{FechamentoAcessosControleRel.totalAcessos}"/>

                                            <div class="sep" style="margin:4px 0 5px 0;"><img src="../../images/shim.gif"></div>
                                        </h:panelGroup>
                                        <rich:panel>
                                            <f:facet name="header">
                                                <h:outputText styleClass="tituloCamposNegrito"
                                                              value="dos Acessos foram de Clientes e Colaboradores">
                                                    <h:outputText styleClass="tituloCamposDestaque12Negrito"
                                                                  value="#{FechamentoAcessosControleRel.percentualCliCol}% " />
                                                </h:outputText>
                                            </f:facet>
                                            <h:panelGrid id="colCli" width="50%"  styleClass="tabFormSubordinada"
                                                         columns="3" rowClasses="linhaImparSubordinado, linhaParSubordinado"
                                                         columnClasses="colunaEsquerda,colunaCentralizada, colunaCentralizada">
                                                <h:outputText  value="" />
                                                <h:outputText styleClass="tituloCamposNegrito " value="% Acesso" />
                                                <h:outputText styleClass="tituloCamposNegrito" value="Total Acesso" />
                                                <h:panelGroup>
                                                    <h:outputText  styleClass="tituloCampos" value="Clientes" />
                                                </h:panelGroup>
                                                <h:outputText styleClass="tituloCampos" value="#{FechamentoAcessosControleRel.percentualClientes}%" />
                                                <h:outputText styleClass="tituloCampos" value="#{FechamentoAcessosControleRel.totalAcessosClientes}" />

                                                <h:outputText  styleClass="tituloCampos" value="Colaboradores" />
                                                <h:outputText  styleClass="tituloCampos" value="#{FechamentoAcessosControleRel.percentualColaboradores}%" />
                                                <h:outputText  styleClass="tituloCampos" value="#{FechamentoAcessosControleRel.totalAcessosColaboradores}" />

                                                <h:outputText  styleClass="tituloCamposNegrito" value="Total:" />
                                                <h:outputText  styleClass="tituloCamposNegrito" value="#{FechamentoAcessosControleRel.percentualCliCol}%" />
                                                <h:outputText  styleClass="tituloCamposNegrito" value="#{FechamentoAcessosControleRel.totalAcessosColaboradores + FechamentoAcessosControleRel.totalAcessosClientes}" />
                                            </h:panelGrid>
                                        </rich:panel>
                                    </h:panelGrid>
                                    <h:panelGrid width="100%" rendered="#{FechamentoAcessosControleRel.pesquisar}" id="tabelaContadorLiberacoes">
                                        <rich:panel >
                                            <f:facet name="header">
                                                <h:outputText styleClass="tituloCamposNegrito" value="dos Acessos foram Liberação">
                                                    <h:outputText styleClass="tituloCamposDestaque12Negrito"
                                                                  value="#{FechamentoAcessosControleRel.percentualLiberacao}% " />
                                                </h:outputText>
                                            </f:facet>
                                            <h:dataTable id="liberacao" width="100%" headerClass="header" styleClass="tabFormSubordinada"
                                                         rowClasses="linhaImparSubordinado, linhaParSubordinado"
                                                         columnClasses="colunaEsquerda, colunaCentralizada, colunaCentralizada,
                                 colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                                         value="#{FechamentoAcessosControleRel.listaFechamentoAcessoRel}" var="liberacao">
                                                <h:column>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  value="#{liberacao.tipoLiberacaoEnum.descricao}" />
                                                    <h:outputText styleClass="tituloCamposNegrito" rendered="#{liberacao.tipoLiberacaoEnum==null}"
                                                                  value="Total:" />
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="tituloCamposNegrito" value="% Acesso" />
                                                    </f:facet>
                                                    <h:outputText styleClass="tituloCampos" rendered="#{liberacao.tipoLiberacaoEnum!=null}"
                                                                  value="#{liberacao.percentualAcessos}%" />
                                                    <h:outputText styleClass="tituloCamposNegrito" rendered="#{liberacao.tipoLiberacaoEnum==null}"
                                                                  value="#{liberacao.percentualAcessos}%" />
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="tituloCamposNegrito" value="Total Acesso" />
                                                    </f:facet>
                                                    <h:commandLink rendered="#{liberacao.tipoLiberacaoEnum!=null}"
                                                                   styleClass="tituloboxcentro" value="#{liberacao.totalAcesso}"
                                                                   action="#{FechamentoAcessosControleRel.consultarTotalAcessos}" />
                                                    <h:commandLink rendered="#{liberacao.tipoLiberacaoEnum==null}"
                                                                   styleClass="tituloDestaquePreto" value="#{liberacao.totalAcesso}"
                                                                   action="#{FechamentoAcessosControleRel.consultarTotalAcessos}" />
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="tituloCamposNegrito " value="Já Justificado" />
                                                    </f:facet>

                                                    <h:commandLink rendered="#{liberacao.tipoLiberacaoEnum!=null}"
                                                                   styleClass="tituloboxcentro" value="#{liberacao.jaJustificado}"
                                                                   action="#{FechamentoAcessosControleRel.consultarAcessosJustificados}" />
                                                    <h:commandLink rendered="#{liberacao.tipoLiberacaoEnum==null}"
                                                                   styleClass="tituloDestaquePreto" value="#{liberacao.jaJustificado}"
                                                                   action="#{FechamentoAcessosControleRel.consultarAcessosJustificados}" />
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="tituloCamposNegrito" value="Falta Justificar" />
                                                    </f:facet>
                                                    <h:commandLink rendered="#{liberacao.tipoLiberacaoEnum!=null}"
                                                                   id="AcessoFaltaJustificar"
                                                                   styleClass="tituloboxcentro" value="#{liberacao.faltaJustificar}"
                                                                   action="#{FechamentoAcessosControleRel.consultarAcessosAJustificar}"/>
                                                    <h:commandLink rendered="#{liberacao.tipoLiberacaoEnum==null}"
                                                                   styleClass="tituloDestaquePreto" value="#{liberacao.faltaJustificar}"
                                                                   action="#{FechamentoAcessosControleRel.consultarAcessosAJustificar}" />
                                                </h:column>
                                                <h:column>
                                                    <h:outputText styleClass="tituloCamposNegrito" value="Total BV: " rendered="#{liberacao.tipoLiberacaoEnum.codigo==4}"/>
                                                    <h:outputText styleClass="tituloboxcentro" value="#{FechamentoAcessosControleRel.qtdBVs}" rendered="#{liberacao.tipoLiberacaoEnum.codigo==4}"/>
                                                </h:column>
                                            </h:dataTable>
                                        </rich:panel>
                                        <h:panelGrid columns="2">
                                            <h:outputText style="#{FechamentoAcessosControleRel.estiloLegenda}" value="#{FechamentoAcessosControleRel.percentualJustificados}% Justificado"></h:outputText>
                                            <table border="0" style="border-style: solid;  padding: 5px 0px 00px 5px; color: #474747;
                           font-family: Arial,Verdana,sans-serif;  margin: 0;  font-size: 11px;" >
                                                <th style="border-style:none; text-align: left" >Legenda </th>
                                                <tr>
                                                    <td class="styleLegendaAcesso" style="background-color: red;">0 a 40% Ruim</td>
                                                    <td class="styleLegendaAcesso" style="background-color: chocolate;">41 a 60% Regular</td>
                                                    <td class="styleLegendaAcesso" style="background-color: green;">61 a 85% Bom</td>
                                                    <td class="styleLegendaAcesso" style="background-color: blue;">86 a 100% Ótimo</td>
                                                </tr>
                                            </table>
                                        </h:panelGrid>
                                        <rich:spacer width="50"/>
                                        <a4j:outputPanel>
                                            <rich:panel rendered="#{FechamentoAcessosControleRel.abrirGridMaisDetalhes}">
                                                <h:outputText rendered="#{not empty FechamentoAcessosControleRel.fechamentoAcessoRelTO.tipoLiberacaoEnum.descricao}"
                                                              styleClass="tituloCamposNegrito" value="#{FechamentoAcessosControleRel.fechamentoAcessoRelTO.tipoLiberacaoEnum.descricao} -
                                      #{fn:length(FechamentoAcessosControleRel.listaLiberacoes)} registros" />
                                                <h:outputText rendered="#{empty FechamentoAcessosControleRel.fechamentoAcessoRelTO.tipoLiberacaoEnum.descricao}"
                                                              styleClass="tituloCamposNegrito" value="Todos os tipos -
                                      #{fn:length(FechamentoAcessosControleRel.listaLiberacoes)} registros" />

                                            </rich:panel>
                                        </a4j:outputPanel>

                                        <rich:dataTable id="itens" rendered="#{FechamentoAcessosControleRel.abrirGridMaisDetalhes}" styleClass="tabFormSubordinada"
                                                        width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                                        value="#{FechamentoAcessosControleRel.listaLiberacoes}" var="liberacaoAcesso"
                                                        rows="100" headerClass="colunaEsquerda" rowKeyVar="status">
                                            <f:facet name="header">
                                            </f:facet>
                                            <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                            <rich:column sortBy="#{liberacaoAcesso.dataHora}"
                                                         filterEvent="onkeyup" width="10%">
                                                <f:facet name="header">
                                                    <h:outputText value="Data Acesso" />
                                                </f:facet>
                                                <h:outputText value="#{liberacaoAcesso.dataHora}">
                                                    <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.localAcesso.descricao}"
                                                         filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText value="Local" />
                                                </f:facet>
                                                <h:outputText value="#{liberacaoAcesso.localAcesso.descricao}"/>
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.coletor.descricao}"
                                                         filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText value="Coletor" />
                                                </f:facet>
                                                <h:outputText  value="#{liberacaoAcesso.coletor.descricao}" />
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.sentido}"
                                                         filterEvent="onkeyup" width="3%">
                                                <f:facet name="header">
                                                    <h:outputText value="Sentido" />
                                                </f:facet>
                                                <h:outputText  value="#{liberacaoAcesso.sentido.id}" />
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.tipoLiberacao.descricao}"
                                                         filterEvent="onkeyup" width="20">
                                                <f:facet name="header">
                                                    <h:outputText value="Tipo de Liberação" />
                                                </f:facet>
                                                <h:outputText  value="#{liberacaoAcesso.tipoLiberacao.descricao}"/>
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.usuario.nomeAbreviado}"
                                                         filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText value="Usuário Liberou" />
                                                </f:facet>
                                                <h:outputText  value="#{liberacaoAcesso.usuario.nomeAbreviado}"/>
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.pessoa.nome}"
                                                         filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText value="Pessoa" />
                                                </f:facet>
                                                <h:outputText rendered="#{!liberacaoAcesso.apresentarNomeGenerico}"
                                                              value="#{liberacaoAcesso.pessoa.primeiroNomeConcatenado}"/>
                                                <h:outputText rendered="#{liberacaoAcesso.apresentarNomeGenerico}"
                                                              value="#{liberacaoAcesso.nomeGenerico}"/>
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.justificativa}"
                                                         filterEvent="onkeyup" >
                                                <f:facet name="header">
                                                    <h:outputText value="Justificativa" />
                                                </f:facet>
                                                <h:outputText rendered="#{empty liberacaoAcesso.justificativaPequena}" value="#{liberacaoAcesso.justificativa}" />
                                                <h:outputText rendered="#{!empty liberacaoAcesso.justificativaPequena}"  value="#{liberacaoAcesso.justificativaPequena}" />
                                                <a4j:commandLink rendered="#{!empty liberacaoAcesso.justificativaPequena}" value="[...]"  reRender="modalJustificativaGrande"
                                                                 oncomplete="Richfaces.showModalPanel('modalJustificativaGrande');"
                                                                 action="#{FechamentoAcessosControleRel.apresentarModalComJustificativa}"/>
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.dthrJustificativa}"
                                                         filterEvent="onkeyup" width="7%">
                                                <f:facet name="header">
                                                    <h:outputText value=" Dt.Justificativa" />
                                                </f:facet>
                                                <h:outputText  value="#{liberacaoAcesso.dthrJustificativa}" >
                                                    <f:convertDateTime type="date" dateStyle="short" locale="pt" timeZone="America/Sao_Paulo" pattern="dd/MM/yyyy HH:mm"/>
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column sortBy="#{liberacaoAcesso.usuarioJustificou.nomeAbreviado}"
                                                         filterEvent="onkeyup">
                                                <f:facet name="header">
                                                    <h:outputText value="Usuário Justificou" />
                                                </f:facet>
                                                <h:outputText  value="#{liberacaoAcesso.usuarioJustificou.nomeAbreviado}" />
                                            </rich:column>
                                            <rich:column filterEvent="onkeyup" width="5%">
                                                <f:facet name="header">
                                                    <h:outputText value="Opções" />
                                                </f:facet>
                                                <a4j:commandLink rendered="#{empty liberacaoAcesso.justificativa}"
                                                                 id="justificarAcessoLiberadoCliente"
                                                                 value="Justificar"  reRender="form,modalJustificativa"
                                                                 action="#{FechamentoAcessosControleRel.abrirModalJustificativa}"
                                                                 oncomplete="Richfaces.showModalPanel('modalJustificativa');"/>
                                                <a4j:commandLink rendered="#{not empty liberacaoAcesso.justificativa}" oncomplete="#{FechamentoAcessosControleRel.msgAlert}"
                                                                 value="Editar" reRender="form,modalJustificativa, form:mensagem"
                                                                 action="#{FechamentoAcessosControleRel.abrirModalAlterarJustificativa}"/>
                                            </rich:column>
                                        </rich:dataTable>
                                        <rich:datascroller rendered="#{FechamentoAcessosControleRel.abrirGridMaisDetalhes}" align="center"
                                                           for="form:itens" maxPages="10"
                                                           id="scResultado" />
                                        <h:panelGrid columns="4" columnClasses="colunaEsquerda,colunaEsquerda,colunaEsquerda, colunaDireita" width="100%">
                                            <h:panelGroup layout="block">
                                                <a4j:commandButton id="exportaExcel"
                                                                   image="../imagens/btn_excel.png"
                                                                   style="magin-left: 8px;"
                                                                   actionListener="#{ExportadorListaControle.exportar}"
                                                                   rendered="#{not empty FechamentoAcessosControleRel.listaLiberacoes}"
                                                                   value="Excel"
                                                                   oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                   accesskey="2" styleClass="botoes">
                                                    <f:attribute name="lista" value="#{FechamentoAcessosControleRel.listaLiberacoes}"/>
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="atributos" value="dataHora=Data Acesso,localApresentar=Local,coletorApresentar=Coletor,sentidoApresentar=Sentido,tipoLiberacaoApresentar=Tipo de Liberação
                                         ,usuarioLiberouApresentar=Usuário Liberou,pessoaApresentar=Pessoa,justificativa=Justificativa,dthrJustificativa=Dt.Justificativa,usuarioJustificouApresentar=Usuário Justificou"/>
                                                    <f:attribute name="prefixo" value="FechamentoControleAcesso"/>
                                                </a4j:commandButton>
                                                <rich:spacer width="5px"/>
                                                <a4j:commandButton id="imprime" image="/imagens/imprimir.png"  rendered="#{FechamentoAcessosControleRel.pesquisar}"
                                                                   value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_imprimir}"
                                                                   title="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_imprimir}"
                                                                   action="#{FechamentoAcessosControleRel.imprimirRelatorio}"
                                                                   oncomplete="#{FechamentoAcessosControleRel.mensagemNotificar}#{FechamentoAcessosControleRel.msgAlert}"/>
                                            </h:panelGroup>
                                            <h:panelGrid columns="2">
                                                <a4j:commandButton rendered="#{FechamentoAcessosControleRel.pesquisar}"
                                                                   image="/imagens/btn_enviar-email-fechamento.png"
                                                                   value="Enviar email de Fechamento"
                                                                   reRender="panelAutorizacaoFuncionalidade"
                                                                   action="#{FechamentoAcessosControleRel.abrirModalPanelPermissaoEnvio}"/>
                                                <a4j:commandLink value="Lista Email"  styleClass="tituloboxcentro" reRender="modalListaEmail"
                                                                 action="#{FechamentoAcessosControleRel.abrirModalListaEmail}"/>
                                            </h:panelGrid>
                                            <a4j:commandButton  action="#{FechamentoAcessosControleRel.realizarConsultaLogEnvioEmails}"
                                                                rendered="#{FechamentoAcessosControleRel.pesquisar}"
                                                                oncomplete="abrirPopup('../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                                image="/imagens/botalVisualizarLog.png" alt="Visualizar LOG" title="Visualizar Log"
                                                                styleClass="botoes"/>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                    <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                                        <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                                            <h:outputText styleClass="mensagem"  value="#{FechamentoAcessosControleRel.mensagem}"/>
                                            <h:outputText styleClass="mensagemDetalhada" value="#{FechamentoAcessosControleRel.mensagemDetalhada}"/>
                                        </h:panelGrid>
                                     </h:panelGrid>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

        </h:panelGrid>
        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </h:form>
    <%@include file="/includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:consulta").focus();
</script>

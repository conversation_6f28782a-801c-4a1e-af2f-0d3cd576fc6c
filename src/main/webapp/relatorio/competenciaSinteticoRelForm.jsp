<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Competência Mensal</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <title>
        <h:outputText value="#{msg_aplic.prt_CompetenciaSintetico_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="../topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_CompetenciaSintetico_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columnClasses="colunaCentralizada" width="100%" style="border:0;" id="panelFiltrosUtilizados">
                    <h:outputText
                        styleClass="tituloCamposAzul"
                        value="Veja os Filtros Utilizados "/>
                    <rich:spacer width="10px" />
                    <rich:toolTip
                        onclick="true"
                        followMouse="true"
                        direction="top-right"
                        style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                        showDelay="500" >
                        ${CompetenciaSinteticoControleRel.filtros}
                    </rich:toolTip>
                </h:panelGrid>
                <h:panelGrid width="100%" style="margin-bottom: 8px;text-align: right;">
                    <h:panelGroup layout="block">
                        <%--BOTÃO EXCEL--%>
                        <a4j:commandLink id="exportarExcel"
                                         style="margin-left: 8px;"
                                         actionListener="#{CompetenciaSinteticoControleRel.exportar}"
                                         rendered="#{not empty CompetenciaSinteticoControleRel.listaTipoProdutoVO}"
                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="botoes">
                            <f:attribute name="lista" value="#{CompetenciaSinteticoControleRel.listaExportavel}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos" value="codCliente=Cód. Cliente,nomeCliente=Nome Cliente,codContrato=Cód. Contrato,valorCompetencia=Valor,
                            mesLancamentoProdutoApresentar=Mês Referência,codPlano=Cód. Plano,nomePlano=Nome Plano,duracaoPlano=Duração,nomeEmpresa=Empresa"/>
                            <f:attribute name="prefixo" value="CompetenciaMensal"/>
                            <f:attribute name="itemExportacao" value="competenciaRel"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                        <%--BOTÃO PDF--%>
                        <a4j:commandLink id="exportarPdf"
                                         style="margin-left: 8px;"
                                         actionListener="#{CompetenciaSinteticoControleRel.exportar}"
                                         rendered="#{not empty CompetenciaSinteticoControleRel.listaTipoProdutoVO}"
                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                         accesskey="2" styleClass="botoes">
                            <f:attribute name="lista" value="#{CompetenciaSinteticoControleRel.listaExportavel}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="itemExportacao" value="competenciaRel"/>
                            <f:attribute name="atributos" value="codCliente=Cód. Cliente,nomeCliente=Nome Cliente,codContrato=Cód. Contrato,valorCompetencia=Valor,
                            mesLancamentoProdutoApresentar=Mês Referência,codPlano=Cód. Plano,nomePlano=Nome Plano,duracaoPlano=Duração,nomeEmpresa=Empresa"/>
                            <f:attribute name="prefixo" value="CompetenciaMensal"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
                <a4j:repeat value="#{CompetenciaSinteticoControleRel.listaTipoProdutoVO}" var="fatTipoProduto">
                    <rich:dataTable width="100%" rowClasses="linhaImpar, linhaPar"
                                    value="#{fatTipoProduto.listaProduto}" var="fatProduto">

                        <f:facet name="header">
                            <h:outputText value="#{fatTipoProduto.tipoProduto}" />
                        </f:facet>

                        <rich:column  sortBy="#{fatProduto.descricao}" filterEvent="onkeyup" style="max-width: 200px; min-width: 200px">
                            <f:facet name="header">
                                <h:outputText value="Produto" />
                            </f:facet>
                            <h:outputText styleClass="tituloCampos" rendered="#{fatProduto.descricao != 'TOTALIZADOR'}" value="#{fatProduto.descricao}" style="text-align:left;"/>
                            <h:outputText styleClass="tituloCamposNegrito" rendered="#{fatProduto.descricao == 'TOTALIZADOR'}" value="#{fatProduto.descricao}" style="text-align:left;"/>
                        </rich:column>

                        <rich:columns value="#{CompetenciaSinteticoControleRel.periodos}" var="coluna" index="ind" style="max-width: 100px; min-width: 100px">
                            <f:facet name="header">
                                <h:outputText value="#{coluna.mesAnoApresentar}" />
                            </f:facet>
                            <h:panelGrid>
                                <h:panelGroup>
                                    <h:outputText styleClass="tituloCampos "  />
                                    <rich:spacer width="10px"/>
                                    <a4j:commandLink  action="#{CompetenciaSinteticoControleRel.visualizarPessoas}" oncomplete="abrirPopup('../relatorio/competenciaSinteticoResumoPessoa.jsp', 'CompetenciaSinteticoResumoPessoa',850,650);" >
                                        <f:setPropertyActionListener target="#{CompetenciaSinteticoControleRel.competenciaSinteticoProdutoMesVO}" value="#{fatProduto.listaProdutoXMes[ind]}"/>
                                        <h:outputText value="#{fatProduto.listaProdutoXMes[ind].qtd}" title="Pessoa(s)"  styleClass="hierarquia"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText styleClass="tituloCampos"  />
                                    <rich:spacer width="10px"/>
                                    <h:outputText value="#{fatProduto.listaProdutoXMes[ind].valor}" title="Valor R$" styleClass="tituloboxcentro">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </h:panelGroup>

                            </h:panelGrid>

                        </rich:columns>

                    </rich:dataTable>
                </a4j:repeat>
                <rich:dataTable id="itens" rendered="#{CompetenciaSinteticoControleRel.resumo.apresentarResultado}" width="100%"  rowClasses="linhaImpar, linhaPar"
                                value="#{CompetenciaSinteticoControleRel.resumo.listaProduto}" var="fatProduto" style="margin-top:18px;">
                    <f:facet name="header">
                        <h:outputText value="#{CompetenciaSinteticoControleRel.resumo.tipoProduto}" />
                    </f:facet>
                    <rich:column  style="max-width: 200px; min-width: 200px">
                        <f:facet name="header">
                            <h:outputText value="Total Geral" />
                        </f:facet>
                        <h:outputText styleClass="tituloCamposNegrito" value="#{fatProduto.descricao}"/>
                    </rich:column>
                    <rich:columns value="#{CompetenciaSinteticoControleRel.periodos}" var="coluna" index="ind" style="max-width: 100px; min-width: 100px" >
                        <f:facet name="header">
                            <h:outputText value="#{coluna.mesAnoApresentar}" />
                        </f:facet>
                        <h:panelGrid>
                            <h:panelGroup>
                                <rich:spacer width="10px"/>
                                <h:outputText styleClass="tituloCampos ">
                                    <a4j:commandLink  action="#{CompetenciaSinteticoControleRel.visualizarPessoas}"
                                                      oncomplete="abrirPopup('../relatorio/competenciaSinteticoResumoPessoa.jsp', 'CompetenciaSinteticoResumoPessoa',850,650);"  >
                                        <f:setPropertyActionListener target="#{CompetenciaSinteticoControleRel.competenciaSinteticoProdutoMesVO}"
                                                                     value="#{fatProduto.listaProdutoXMes[ind]}"/>
                                        <h:outputText value="#{fatProduto.listaProdutoXMes[ind].qtd}"  styleClass="hierarquia" />
                                    </a4j:commandLink>
                                </h:outputText>
                            </h:panelGroup>
                            <h:panelGroup>
                                <rich:spacer width="5px"/>
                                <h:outputText value="#{fatProduto.listaProdutoXMes[ind].valor}" styleClass="tituloboxcentro" >
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:columns>
                </rich:dataTable>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:commandButton id="voltar" alt="Voltar Passo" action="#{CompetenciaSinteticoControleRel.voltar}" image="../imagens/botaoVoltar.png" />
                        <rich:spacer width="7"/>
                    </h:panelGrid>
                </h:panelGrid>

            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="panelStatusCompetencia" autosized="true">
        <h:panelGrid columns="3" styleClass="titulo3" columnClasses="titulo3">
            <h:graphicImage url="../imagens/carregando.gif" style="border:none"/>
            <h:outputText styleClass="titulo3" value="Carregando..."/>
        </h:panelGrid>
    </rich:modalPanel>
    <a4j:status onstart="Richfaces.showModalPanel('panelStatusCompetencia');"
                onstop="#{rich:component('panelStatusCompetencia')}.hide();">
    </a4j:status>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

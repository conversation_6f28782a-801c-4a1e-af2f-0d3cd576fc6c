<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="../includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<title>Relatório de Parcelas</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<style type="text/css">
    .notificacaoAtividades {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        -webkit-font-smoothing: subpixel-antialiased;
        background-clip: padding-box;
        background: #819aa5
        -webkit-linear-gradient(top, #96acb6,
                #5d7b89);
        box-shadow: rgba(0, 0, 0, 0.701961) 0px 1px 1px 0px;
        color: rgb(255, 255, 255) !important;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        height: 13px !important;
        line-height: normal;
        list-style-type: none;
        padding: 1px 3px !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        zoom: 1;
        border-radius: 40%;
    }
</style>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ParcelaEmAberto_tituloRel}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}o-sistema-possui-alguma-ferramenta-que-permita-visualizar-as-inadimplencias-da-minha-empresa/"/>
    <rich:modalPanel id="panelResponsavel" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Responsável"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink1"/>
                <rich:componentControl for="panelResponsavel" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formResponsavel" ajaxSubmit="true"  styleClass="font-size-Em-max">
            <h:panelGrid columns="1"  width="100%" >
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza " value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultaresponsavel"
                                         value="#{ParcelaEmAbertoControleRel.campoConsultarResponsavel}">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.tipoConsultaComboResponsavel}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaResponsavel" size="10"  value="#{ParcelaEmAbertoControleRel.valorConsultarResponsavel}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink  id="btnConsultarResponsavel" reRender="formResponsavel" action="#{ParcelaEmAbertoControleRel.consultarResponsavel}"
                                          styleClass="botaoPrimario texto-size-16-real texto-cor-azul" value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink  id="btnConsultarResponsavel" reRender="formResponsavel"
                                            action="#{ParcelaEmAbertoControleRel.consultarResponsavel}" value="#{msg_bt.btn_consultar}"
                                            styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                            title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaResponsavel" width="100%" styleClass="tabelaSimplesCustom"
                                rendered="#{not empty ParcelaEmAbertoControleRel.listaConsultarResponsavel}"
                                value="#{ParcelaEmAbertoControleRel.listaConsultarResponsavel}" rows="5" var="responsavel">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_Usuario_colaborador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink  styleClass="linkPadrao texto-size-14-real texto-cor-azul" action="#{ParcelaEmAbertoControleRel.selecionarResponsavel}" focus="responsavel" reRender="form" oncomplete="Richfaces.hideModalPanel('panelResponsavel')" value="#{responsavel.pessoa.nome}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <a4j:commandLink action="#{ParcelaEmAbertoControleRel.selecionarResponsavel}"
                                         styleClass="linkPadrao texto-size-14-real texto-cor-azul"
                                         focus="responsavel"
                                         reRender="form" oncomplete="Richfaces.hideModalPanel('panelResponsavel')"
                                         title="#{msg.msg_selecionar_dados}">
                            <span>Selecionar </span>
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formResponsavel:resultadoConsultaResponsavel" maxPages="10" renderIfSinglePage="false"
                                   styleClass="scrollPureCustom"
                                   id="scResultadoResponsavel" />
                <h:panelGrid id="mensagemConsultaResponsavel" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{ParcelaEmAbertoControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ParcelaEmAbertoControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>



    <rich:modalPanel id="panelCliente" styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Cliente"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink2"/>
                <rich:componentControl for="panelCliente" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formCliente" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1"  width="100%" >
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza "  value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultacliente"
                                         value="#{ParcelaEmAbertoControleRel.campoConsultarCliente}">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.tipoConsultaComboCliente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaCliente" size="10"  value="#{ParcelaEmAbertoControleRel.valorConsultarCliente}"/>

                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <a4j:commandLink  id="btnConsultarCliente" reRender="formCliente"
                                          action="#{ParcelaEmAbertoControleRel.consultarCliente}"
                                          styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                          value="#{msg_bt.btn_consultar}"
                                          title="#{msg.msg_consultar_dados}"/>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <a4j:commandLink  id="btnConsultarCliente" reRender="formCliente"
                                            action="#{ParcelaEmAbertoControleRel.consultarCliente}" styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                          value="#{msg_bt.btn_consultar}"
                                            title="#{msg.msg_consultar_dados}"/>
                    </c:if>

                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaCliente" width="100%"  styleClass="tabelaSimplesCustom" rendered="#{not empty ParcelaEmAbertoControleRel.listaConsultarCliente}"
                                value="#{ParcelaEmAbertoControleRel.listaConsultarCliente}" rows="5" var="cliente">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="#{msg_aplic.prt_Usuario_nomePessoa}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul" action="#{ParcelaEmAbertoControleRel.selecionarCliente}" focus="cliente" reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')" value="#{cliente.pessoa.nome}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink styleClass="linkPadrao texto-size-14-real texto-cor-azul" action="#{ParcelaEmAbertoControleRel.selecionarCliente}"
                                         focus="cliente" reRender="form" oncomplete="Richfaces.hideModalPanel('panelCliente')"
                                         title="#{msg.msg_selecionar_dados}" >
                            <span>Selecionar </span>
                            <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10" styleClass="scrollPureCustom"
                                   id="scResultadoCliente" renderIfSinglePage="false" />
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{ParcelaEmAbertoControleRel.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ParcelaEmAbertoControleRel.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>



    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="../topoReduzido_material.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="../pages/ce/includes/topoReduzido.jsp"/></c:if>

        </f:facet>

        <h:form id="form" target="_blank" >

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:commandLink action="#{ParcelaEmAbertoControleRel.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:commandLink action="#{ParcelaEmAbertoControleRel.imprimirRelatorio}" id="imprimirRelatorio" style="display: none" />
            <h:inputHidden id="relatorio" value="#{ParcelaEmAbertoControleRel.relatorio}" />
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">


                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_Empresa}" rendered="#{ParcelaEmAbertoControleRel.permissaoConsultaTodasEmpresas}" />
                    <h:panelGroup layout="block" rendered="#{ParcelaEmAbertoControleRel.permissaoConsultaTodasEmpresas}" >
                        <h:selectOneMenu  id="empresas" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ParcelaEmAbertoControleRel.filtroEmpresa}" >
                            <f:selectItems  value="#{ParcelaEmAbertoControleRel.listaEmpresas}"/>
                            <a4j:support action="#{ParcelaEmAbertoControleRel.carregarConvenios}" reRender="form"
                                         event="onchange"/>
                        </h:selectOneMenu>
                    </h:panelGroup>


                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_periodoPesquisa}" />
                    <h:panelGroup id="groupForm">
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataInicioVencimento}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false" />
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                            <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:outputText  styleClass="tituloCampos" style="position:relative; top:0px; left:10px;" value="#{msg_aplic.prt_CaixaPorOperador_ate}" />
                        <rich:spacer width="12px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.dataTerminoVencimento}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true" />
                            <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_situacaoPesquisa}" />
                    <h:panelGroup>
                        <h:selectOneMenu id="situacaoPeriodo" styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.periodo}" >
                            <f:selectItems  value="#{ParcelaEmAbertoControleRel.listaSelectItemTipoSituacaoPeriodo}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>


                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_Situacao}" />
                    <h:panelGroup id="grpSituacoes">
                        <h:selectOneMenu id="situacao" styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.situacao}"  style="float: left">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaSelectItemTipoSituacao}"/>
                        </h:selectOneMenu>

                        <a4j:commandButton id="adicionarSituacaoSelecionado" action="#{ParcelaEmAbertoControleRel.adicionarSituacaoNaLista}"
                                           style="float: left"
                                           reRender="groupForm, situacao, mensagemConsultaResponsavel, grpSituacoes"
                                           image= "../imagens/botaoAdicionar.png" styleClass="botoes"/>

                        <h:panelGrid id="qtdSituacoes" cellpadding="0" cellspacing="0" columns="2"  style="float: left; margin-top: 3px; margin-left: 5px">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                    <h:outputText value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.qtdSituacoesSelecionadas}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <rich:toolTip value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.situacoesSelecionadasApresentar}"
                                      for="qtdSituacoes" followMouse="true"/>

                        <a4j:commandButton id="limparSituacoesSelecionadas"  image="../imagens/limpar.gif"
                                           reRender="groupForm, situacao, mensagemConsultaResponsavel, grpSituacoes"
                                           styleClass="botoes" style="vertical-align: top; margin-top: 6px; margin-left: 6px; float: left"
                                           action="#{ParcelaEmAbertoControleRel.limparSituacoes}"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_recorrenciasomente}" />
                    <h:panelGroup>
                        <h:selectOneMenu id="recorrencia" styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.parcelasRecorrencia}" >
                            <f:selectItems  value="#{ParcelaEmAbertoControleRel.listaSelectItemRecorrencia}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    
                    <h:outputText style="vertical-align:middle;" styleClass="tituloCampos" value="Alunos com Autorização de Cobrança"/>
                    <h:selectOneMenu style="vertical-align:middle;" id="comboConvenios" styleClass="tituloCampos"
                                     value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.convenio.codigo}">
                        <f:selectItems value="#{ParcelaEmAbertoControleRel.convenios}"/>
                        <a4j:support reRender="form" event="onchange"/>
                    </h:selectOneMenu>

                    <h:outputText rendered="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.convenio.codigo != null &&
                                    ParcelaEmAbertoControleRel.parcelaEmAbertoRel.convenio.codigo > 0}"
                                  style="vertical-align:middle;" styleClass="tituloCampos" value="#{msg_aplic.prt_ignorarremessasemretorno}"/>
                    <h:selectBooleanCheckbox
                            rendered="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.convenio.codigo != null &&
                                    ParcelaEmAbertoControleRel.parcelaEmAbertoRel.convenio.codigo > 0}"
                            value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.ignorarParcelasSemRetorno}"/>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_nomeOperador}" />
                    <h:panelGroup>
                        <h:inputText id="nomeOperador" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.colaboradorVO.pessoa.nome}" />
                        <a4j:commandButton id="consultarOperador" oncomplete="Richfaces.showModalPanel('panelResponsavel'), setFocus(formResponsavel,'formResponsavel:valorConsultarResponsavel')" alt="Consultar Operador" image="../imagens/informacao.gif" />
                        <rich:spacer width="5px" />
                        <a4j:commandButton id="LimparOperador"  image="../imagens/limpar.gif" reRender="nomeOperador" action="#{ParcelaEmAbertoControleRel.limparCampoOperador}"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_Clientenome}" />
                    <h:panelGroup>
                        <h:inputText id="nomeCliente" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.clienteVO.pessoa.nome}" />
                        <a4j:commandButton id="consultarCliente" oncomplete="Richfaces.showModalPanel('panelCliente'), setFocus(formCliente,'formCliente:valorConsultarCliente')" alt="Consultar Operador" image="../imagens/informacao.gif" />
                        <rich:spacer width="5px" />
                        <a4j:commandButton id="LimparCliente"  image="../imagens/limpar.gif"  reRender="form:nomeCliente" action="#{ParcelaEmAbertoControleRel.limparCampoCliente}"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_situacaoCliente}" />
                    <h:panelGroup>
                        <h:selectOneMenu id="situacaoCliente" styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.situacaoCliente}" >
                            <f:selectItems  value="#{ParcelaEmAbertoControleRel.listaSelectItemSituacaoCliente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_plano}" />
                    <h:panelGroup id="grpPlano">
                        <h:selectOneMenu id="plano" styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.codigoPlano}"  style="float: left">
                            <f:selectItems  value="#{ParcelaEmAbertoControleRel.listaSelectItemPlano}"/>
                        </h:selectOneMenu>

                        <a4j:commandButton id="adicionarPlanoSelecionado" action="#{ParcelaEmAbertoControleRel.adicionarPlanoNaLista}"
                                           style="float: left"
                                           reRender="groupForm, plano, mensagemConsultaResponsavel, grpPlano"
                                           image= "../imagens/botaoAdicionar.png" styleClass="botoes"/>

                        <h:panelGrid cellpadding="0" cellspacing="0" columns="2" id="qtd" style="float: left; margin-top: 3px; margin-left: 5px">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                    <h:outputText value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.qtdPlanosSelecionados}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <rich:toolTip value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.planosSelecionadosApresentar}"
                                      for="qtd"
                                      followMouse="true"/>

                        <a4j:commandButton id="limparPlanosSelecionados"  image="../imagens/limpar.gif"
                                           reRender="groupForm, plano, mensagemConsultaResponsavel, grpPlano"
                                           styleClass="botoes" style="vertical-align: top; margin-top: 6px; margin-left: 6px; float: left"
                                           action="#{ParcelaEmAbertoControleRel.limparPlanos}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_horario}"/>
                    <h:panelGroup id="grpHorarios">
                        <h:selectOneMenu id="horario" styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.codigoHorario}"  style="float: left">
                            <f:selectItems value="#{ParcelaEmAbertoControleRel.listaSelectItemHorariosPlano}"/>
                        </h:selectOneMenu>

                        <a4j:commandButton id="adicionarHorarioSelecionado" action="#{ParcelaEmAbertoControleRel.adicionarHorarioNaLista}"
                                           style="float: left"
                                           reRender="groupForm, horario, mensagemConsultaResponsavel, grpHorarios"
                                           image= "../imagens/botaoAdicionar.png" styleClass="botoes"/>

                        <h:panelGrid id="qtdHorarios" cellpadding="0" cellspacing="0" columns="2"  style="float: left; margin-top: 3px; margin-left: 5px">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="notificacaoAtividades">
                                    <h:outputText value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.qtdHorariosSelecionados}"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <rich:toolTip value="#{ParcelaEmAbertoControleRel.parcelaEmAbertoRel.horariosSelecionadosApresentar}"
                                      for="qtdHorarios" followMouse="true"/>

                        <a4j:commandButton id="limparHorariosSelecionados"  image="../imagens/limpar.gif"
                                           reRender="groupForm, horario, mensagemConsultaResponsavel, grpHorarios"
                                           styleClass="botoes" style="vertical-align: top; margin-top: 6px; margin-left: 6px; float: left"
                                           action="#{ParcelaEmAbertoControleRel.limparHorarios}"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ParcelaEmAberto_tipoRelatorio}" />
                    <h:panelGroup id="grpTipoRelatorio">
                        <h:selectOneMenu id="tipoRelatorio" styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.tipoRelatorioSelecionado}"  style="float: left">
                            <f:selectItems  value="#{ParcelaEmAbertoControleRel.listaTipoRelatorio}"/>
                            <a4j:support event="onchange" reRender="panelBtn"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <c:if test="${ParcelaEmAbertoControleRel.empresaLogado.cobrarAutomaticamenteMultaJuros}">
                        <h:outputText  styleClass="tituloCampos" value="Gerar Multas e Juros" />
                        <h:panelGroup id="multasJuros">
                            <h:selectBooleanCheckbox id="checkMultasJuros" styleClass="tituloCampos" value="#{ParcelaEmAbertoControleRel.gerarMultasJuros}"  style="float: left"/>
                        </h:panelGroup>
                    </c:if>
                </h:panelGrid>


                <h:panelGrid id="panelBtn" columns="1" style="padding-bottom: 15px;" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{ParcelaEmAbertoControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ParcelaEmAbertoControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1"  width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <rich:spacer width="20px"/>
                            <c:if test="${modulo eq 'zillyonWeb'}">

                                <a4j:commandLink id="btnExcel"
                                                 rendered="#{ParcelaEmAbertoControleRel.relatorioRetrato}"
                                                 styleClass="exportadores linkPadrao"
                                                 actionListener="#{ParcelaEmAbertoControleRel.exportar}"
                                                 oncomplete="#{ParcelaEmAbertoControleRel.abrirRelatorio}"
                                                 accesskey="3">
                                    <f:attribute name="tipo" value="xls"/>
                                    <c:if test="${ParcelaEmAbertoControleRel.gerarMultasJuros}">
                                        <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,parcela=Cod.Parcela,descricaoParcela=Desc.Parcela,dataFatura=Dt.Faturamento,dateVencimento=Dt.Vencimento,dataPagamento=Dt.Pagamento,contrato=Nr.Contrato,situacao_Apresentar=Situação,recorrencia_Apresentar=Recorrência,valor=Valor,email=Email,telefone=Telefones,nomeempresa=Empresa,juros=Juros,multas=Multa,nome_plano=nomePlano"/>
                                    </c:if>
                                    <c:if test="${!ParcelaEmAbertoControleRel.gerarMultasJuros}">
                                        <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,parcela=Cod.Parcela,descricaoParcela=Desc.Parcela,dataFatura=Dt.Faturamento,dateVencimento=Dt.Vencimento,dataPagamento=Dt.Pagamento,contrato=Nr.Contrato,situacao_Apresentar=Situação,recorrencia_Apresentar=Recorrência,valor=Valor,email=Email,telefone=Telefones,nomeempresa=Empresa,nome_plano=nomePlano"/>
                                    </c:if>
                                    <f:attribute name="prefixo" value="ParcelaEmAberto"/>
                                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                </a4j:commandLink>

                                <a4j:commandLink  styleClass="exportadores margin-h-10 linkPadrao"
                                                  action="#{ParcelaEmAbertoControleRel.imprimirHorizontal}"
                                                   rendered="#{ParcelaEmAbertoControleRel.relatorioRetrato}"
                                                   oncomplete="#{ParcelaEmAbertoControleRel.abrirRelatorio}" reRender="form">
                                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                </a4j:commandLink>

                                <a4j:commandLink id="imprimirPDF-Horizontal" action="#{ParcelaEmAbertoControleRel.imprimirVertical}"
                                                 oncomplete="#{ParcelaEmAbertoControleRel.abrirRelatorio}"
                                                 value="Gerar Relatório (Paisagem)"
                                                 rendered="#{ParcelaEmAbertoControleRel.relatorioPaisagem}"
                                                 accesskey="2" styleClass="botoes nvoBt" reRender="form"/>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="imprimirPDF" action="#{ParcelaEmAbertoControleRel.imprimirPDF}"
                                                   oncomplete="#{ParcelaEmAbertoControleRel.nomeRefRelatorioGeradoAgora}"
                                                   value="Gerar Relatório (PDF)"
                                                   accesskey="2" styleClass="botoes" reRender="form"
                                                   image="/imagens/imprimir.png"/>
                            </c:if>

                        </h:panelGroup>
                    </h:panelGrid>
                    <h:outputText style="display: block; text-align: center; color: dimgrey"  styleClass="tituloCampos " value="Obs: As informações de email e telefone estão disponíveis apenas na versão Excel. "/>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<title>Relatório Lista de Chamada</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_MatriculaAlunoHorarioTurma_tituloRel}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-emitir-o-relatorio-lista-de-chamada-de-turma/"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="../topoReduzido_material.jsp"/>

        </f:facet>
        <h:form id="form" target="_blank" >
            <h:commandLink action="#{MatriculaAlunoHorarioTurmaRelControleRel.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <a4j:commandLink action="#{MatriculaAlunoHorarioTurmaRelControleRel.imprimirRelatorio}"
                             oncomplete="location.href=\"#{MatriculaAlunoHorarioTurmaRelControleRel.nomeArquivoRelatorioGeradoAgora}\""
                             ajaxSingle="true" id="imprimirRelatorio"  style="display: none" />
            <h:inputHidden id="relatorio" value="#{MatriculaAlunoHorarioTurmaRelControleRel.relatorio}" />
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText rendered="#{MatriculaAlunoHorarioTurmaRelControleRel.usuario.administrador}" styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_empresa}" />
                    <h:panelGroup rendered="#{MatriculaAlunoHorarioTurmaRelControleRel.usuario.administrador}">
                        <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.empresa.codigo}" >
                            <f:selectItems  value="#{MatriculaAlunoHorarioTurmaRelControleRel.listaSelectItemEmpresa}" />
                            <a4j:support event="onchange" action="#{MatriculaAlunoHorarioTurmaRelControleRel.obterEmpresaEscolhida}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_dataInicioMatricula}" />
                    <h:panelGrid columns="3" rowClasses="linhaPar" >
                        <h:panelGroup>
                            <rich:calendar id="dataInicioMatricula"
                                           value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"                                           
                                           zindex="2"
                                           showWeeksBar="false">
                                <a4j:support event="onchanged" reRender="form"/>
                            </rich:calendar>
                            <h:message for="dataInicioMatricula"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>

                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ate}" />
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"                                           
                                           showWeeksBar="false">
                                <a4j:support event="onchanged" reRender="form"/>
                            </rich:calendar>
                            <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    </h:panelGrid>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_turma}" />
                    <h:selectOneMenu  id="turma" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.turma.codigo}" >
                        <f:selectItems value="#{MatriculaAlunoHorarioTurmaRelControleRel.listaSelectItemTurma}" />
                        <a4j:support event="onchange" action="#{MatriculaAlunoHorarioTurmaRelControleRel.obterTurmaEscolhida}" />
                    </h:selectOneMenu>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_pessoa}" />
                    <h:selectOneMenu  id="colaborador" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.professor.codigo}" >
                        <f:selectItems  value="#{MatriculaAlunoHorarioTurmaRelControleRel.listaSelectItemColaborador}" />
                        <a4j:support
                            event="onchange"
                            action="#{MatriculaAlunoHorarioTurmaRelControleRel.obterColaboradorEscolhido}" />
                    </h:selectOneMenu>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_quebrarPagina}" />
                    <h:selectBooleanCheckbox  id="quebrarPagina" styleClass="form"
                                              value="#{MatriculaAlunoHorarioTurmaRelControleRel.quebrarPagina}">
                        <a4j:support
                            event="onchange"
                            reRender="quebrarPagina" />
                    </h:selectBooleanCheckbox>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_diaSemana}"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.domingo}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="Dom" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.segunda}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="Seg" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.terca}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="Ter" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.quarta}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="Qua" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.quinta}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="Qui" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.sexta}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="Sex" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.sabado}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="Sáb" styleClass="text"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_horarios}"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h0001as0200}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText value="00:01 - 02:00" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h0201as0400}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="02:01 - 04:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h0401as0600}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="04:01 - 06:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h0601as0800}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="06:01 - 08:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h0801as1000}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="08:01 - 10:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h1001as1200}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="10:01 - 12:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h1201as1400}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="12:01 - 14:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h1401as1600}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="14:01 - 16:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h1601as1800}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="16:01 - 18:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h1801as2000}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="18:01 - 20:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h2001as2200}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="20:01 - 22:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{MatriculaAlunoHorarioTurmaRelControleRel.matriculaAlunoHorarioTurmaRel.consultarTurma.h2201as0000}">
                            <a4j:support event="onchange" status="statusHora"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="text" value="22:01 - 00:00"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{MatriculaAlunoHorarioTurmaRelControleRel.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{MatriculaAlunoHorarioTurmaRelControleRel.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:panelGroup>

                            <rich:spacer width="20px"/>
                            <a4j:commandLink id="imprimirPDF" ajaxSingle="true" style="margin-left: 8px;"
                                               action="#{MatriculaAlunoHorarioTurmaRelControleRel.imprimirPDF}"
                                               oncomplete="imprimirRelatorio(this.form);"
                                               accesskey="2" styleClass="botoes nvoBt" reRender="form">
                                    <i class="fa-icon-print"></i> Gerar Relatório (PDF)
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:mes").focus();
</script>

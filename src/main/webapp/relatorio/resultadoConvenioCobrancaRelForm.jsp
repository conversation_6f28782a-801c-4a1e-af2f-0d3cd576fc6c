<%-- 
    Document   : listaAcessoGymPassRel
    Created on : 09/10/2017, 16:27:25
    Author     : arthur
--%>

<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="../hoverform.js"></script>
    <link href="../css/otimize.css" rel="stylesheet" type="text/css">
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
</head>
<title>Resultado Convênio de Cobrança</title>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .alinharMeio tbody tr td label {
        vertical-align: bottom;
        padding-right: 10px;
    }

</style>
<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Resultado Convênio de Cobrança"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Inicial:Recurso_GymPass"/>


    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%">
        <f:facet name="header">

            <jsp:include page="../topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <hr style="border-color: #e6e6e6;">
                <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                              style="width: 100%; text-align: center;">


                    <table class="tblHeaderLeft semZebra" style="margin-bottom: 5px;">
                        <thead>
                        <tr>
                            <th width="50%">
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="CONVÊNIO"/>
                            </th>
                            <th width="25%">
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="QUANTIDADE"/>
                            </th>
                            <th width="25%">
                                <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                              value="VALOR"/>
                            </th>
                        </tr>
                        </thead>

                        <tbody>

                        <a4j:repeat var="totalizador"
                                    value="#{ResultadoConvenioCobrancaControleRel.totalizadorConvenio}">

                            <table class="tblHeaderLeft semZebra" style="margin-bottom: 5px;">
                                <thead>
                                <tr>
                                    <th width="50%">
                                        <h:outputText
                                                style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                                value="#{totalizador.label}"/>
                                    </th>
                                    <th width="25%">
                                        <h:outputText
                                                style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                                value="#{totalizador.quantidade}"/>
                                    </th>
                                    <th width="25%">
                                        <h:outputText
                                                style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                                value="R$ #{totalizador.valorApresentar}"/>
                                    </th>
                                </tr>
                                </thead>

                                <tbody>
                                <a4j:repeat var="filho"
                                            value="#{totalizador.listaFilhoTotalizadorRemessaTO}">
                                    <tr>
                                        <td>
                                            <h:outputText
                                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2; padding-left: 20px"
                                                    value="#{filho.label}"/>
                                        </td>
                                        <td>
                                            <h:outputText
                                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                                    value="#{filho.quantidade}"/>
                                        </td>
                                        <td>
                                            <h:outputText
                                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                                    value="R$ #{filho.valorApresentar}"/>
                                        </td>
                                    </tr>
                                </a4j:repeat>
                                </tbody>
                            </table>
                        </a4j:repeat>
                        </tbody>
                    </table>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda"
                             style="margin-left: 20px; margin-top: 10px">
                    <h:outputText styleClass="mensagem" value="#{ResultadoConvenioCobrancaControleRel.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{ResultadoConvenioCobrancaControleRel.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGrid id="panelInferior" columns="1" width="100%" styleClass="tabBotoes"
                             columnClasses="colunaCentralizada" style="margin: 15px">
                    <a4j:commandLink id="voltar" ajaxSingle="false"
                                     styleClass="pure-button pure-button-primary"
                                     value="Voltar"
                                     action="#{ResultadoConvenioCobrancaControleRel.voltar}"
                                     accesskey="2" reRender="form"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
    jQuery.noConflict();
</script>


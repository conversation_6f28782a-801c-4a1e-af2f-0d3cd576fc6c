<%-- 
    Document   : frequenciaOcupacaoTurmas
    Created on : 29/11/2011, 10:37:26
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>

<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<title>Frequência e Ocupação de Turmas</title>

<style type="text/css">
    .myHeader{
        background: none;
        margin: 0 0 0 0;
        padding: 4px 4px 4px 4px;
        border: none;
    }
    .label,
    .badge {
        display: inline-block;
        padding: 2px 4px;
        margin: 1px 1px;
        font-weight: bold;
        min-width: 10px;
        line-height: 8px;
        font-size: 9px;
        vertical-align:top;
        color: #ffffff;
        vertical-align: baseline;
        white-space: nowrap;
        text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
        /*background-color: #807579;        */
        background: #c5deea; /* Old browsers */
        /* IE9 SVG, needs conditional override of 'filter' to 'none' */
        background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2M1ZGVlYSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjMxJSIgc3RvcC1jb2xvcj0iIzhhYmJkNyIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwNjZkYWIiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
        background: -moz-linear-gradient(left,  #c5deea 0%, #8abbd7 31%, #066dab 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, right top, color-stop(0%,#c5deea), color-stop(31%,#8abbd7), color-stop(100%,#066dab)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(left,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* IE10+ */
        background: linear-gradient(to right,  #c5deea 0%,#8abbd7 31%,#066dab 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c5deea', endColorstr='#066dab',GradientType=1 ); /* IE6-8 */


        border-right: 1px solid #ccc;
        border-left: 1px solid #ccc;
        border-top: 1px solid #ccc;

    }
    .label {
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
    }
    .badge {
        padding-left: 9px;
        padding-right: 9px;
        -webkit-border-radius: 9px;
        -moz-border-radius: 9px;
        border-radius: 9px;
    }
    .label:empty,
    .badge:empty {
        display: none;
    }
    a.label:hover,
    a.label:focus,
    a.badge:hover,
    a.badge:focus {
        color: #ffffff;
        text-decoration: none;
        cursor: pointer;
    }
    .btn .label,
    .btn .badge {
        position: relative;
        top: -1px;
    }
    .btn-mini .label,
    .btn-mini .badge {
        top: 0;
    }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_DescontoOcupacaoTurma_TituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-de-desconto-por-ocupacao-na-turma/"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <jsp:include page="../topoReduzido_material.jsp"/>
    <h:form id="form">
        <a4j:commandLink action="#{DescontoOcupacaoControleRel.imprimirRelatorio}"
                         oncomplete="location.href=\"#{DescontoOcupacaoControleRel.nomeArquivoRelatorioGeradoAgora}\""
                         ajaxSingle="true" id="imprimirRelatorio"  style="display: none" />
        <h:inputHidden id="relatorio" value="#{DescontoOcupacaoControleRel.relatorio}" />

        <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">            
            <hr style="border-color: #e6e6e6;">
            <h:panelGrid id="panelFiltros" columns="2" columnClasses="colunaTopCentralizada" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" style="padding-left:0px;">

                    <!-- empresa -->
                    <h:outputText rendered="#{DescontoOcupacaoControleRel.mostrarEmpresa}" styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_empresa}"/>
                    <h:panelGroup rendered="#{DescontoOcupacaoControleRel.mostrarEmpresa}">
                        <h:selectOneMenu id="empresa"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                         value="#{DescontoOcupacaoControleRel.consultarTurma.empresa.codigo}">
                            <f:selectItems value="#{DescontoOcupacaoControleRel.consultarTurma.listaEmpresa}"/>
                            <a4j:support event="onchange" action="#{DescontoOcupacaoControleRel.montarListas}" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- modalidade -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_modalidade}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="modalidade" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                         value="#{DescontoOcupacaoControleRel.consultarTurma.modalidade.codigo}">
                            <f:selectItems value="#{DescontoOcupacaoControleRel.consultarTurma.listaModalidade}"/>
                            <a4j:support event="onchange" action="#{DescontoOcupacaoControleRel.montarListaTurma}" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- turma -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_turma}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="turma" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{DescontoOcupacaoControleRel.consultarTurma.turma.codigo}">
                            <f:selectItems value="#{DescontoOcupacaoControleRel.consultarTurma.listaTurma}"/>
                            <a4j:support event="onchange" action="#{DescontoOcupacaoControleRel.montarListaProfessor}" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- professor -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_professor}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="professor" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                         value="#{DescontoOcupacaoControleRel.consultarTurma.professor.codigo}">
                            <f:selectItems value="#{DescontoOcupacaoControleRel.consultarTurma.listaProfessor}"/>
                            <a4j:support event="onchange" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- ambiente -->
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_ambiente}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="ambiente" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                         value="#{DescontoOcupacaoControleRel.consultarTurma.ambiente.codigo}">
                            <f:selectItems value="#{DescontoOcupacaoControleRel.consultarTurma.listaAmbiente}"/>
                            <a4j:support event="onchange" reRender="form:panelFiltros"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_dataInicioMatricula}" />
                    <h:panelGrid columns="3" rowClasses="linhaPar" >
                        <h:panelGroup>
                            <rich:calendar id="dataInicioMatricula" inputSize="10" inputClass="form"
                                           value="#{DescontoOcupacaoControleRel.dataInicio}"
                                           oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy" enableManualInput="true"
                                           zindex="2" showWeeksBar="false">
                                <a4j:support event="onchange" reRender="form:panelFiltros"/>
                            </rich:calendar>
                            <h:message for="dataInicioMatricula" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ate}" />
                        <h:panelGroup>
                            <rich:calendar id="dataTermino" inputSize="10" inputClass="form"
                                           value="#{DescontoOcupacaoControleRel.dataTermino}"
                                           oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy" enableManualInput="true"
                                           zindex="2" showWeeksBar="false">
                                <a4j:support event="onchange" reRender="form:panelFiltros"/>
                            </rich:calendar>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGrid>
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda" width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_diaSemana}"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.domingo}"/>
                        <h:outputText value="Dom" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.segunda}"/>
                        <h:outputText value="Seg" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.terca}"/>
                        <h:outputText value="Ter" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.quarta}"/>
                        <h:outputText value="Qua" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.quinta}"/>
                        <h:outputText value="Qui" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.sexta}"/>
                        <h:outputText value="Sex" styleClass="text"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.sabado}"/>
                        <h:outputText value="Sáb" styleClass="text" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConsultarTurma_horarios}"/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h0001as0200}"/>
                        <h:outputText value="00:01 - 02:00" styleClass="text" />
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h0201as0400}"/>
                        <h:outputText styleClass="text" value="02:01 - 04:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h0401as0600}"/>
                        <h:outputText styleClass="text" value="04:01 - 06:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h0601as0800}"/>
                        <h:outputText styleClass="text" value="06:01 - 08:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h0801as1000}"/>
                        <h:outputText styleClass="text" value="08:01 - 10:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h1001as1200}"/>
                        <h:outputText styleClass="text" value="10:01 - 12:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h1201as1400}"/>
                        <h:outputText styleClass="text" value="12:01 - 14:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h1401as1600}"/>
                        <h:outputText styleClass="text" value="14:01 - 16:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h1601as1800}"/>
                        <h:outputText styleClass="text" value="16:01 - 18:00"/>
                    </h:panelGroup>
                    <h:outputText value=""/>
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h1801as2000}"/>

                        <h:outputText styleClass="text" value="18:01 - 20:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h2001as2200}"/>
                        <h:outputText styleClass="text" value="20:01 - 22:00"/>
                        <rich:spacer width="10"/>
                        <h:selectBooleanCheckbox value="#{DescontoOcupacaoControleRel.consultarTurma.h2201as0000}"/>
                        <h:outputText styleClass="text" value="22:01 - 00:00"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:outputText styleClass="mensagem"  value="#{DescontoOcupacaoControleRel.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{DescontoOcupacaoControleRel.mensagemDetalhada}"/>
                </h:panelGrid>
                <h:panelGroup>
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink id="consultar" action="#{DescontoOcupacaoControleRel.consultarTurmas}"
                                         reRender="form" value="#{msg_bt.btn_consultar}"
                                         title="#{msg.msg_consultar_dados}" accesskey="4"
                                         styleClass="botaoPrimario texto-size-14-real"/>
                    </h:panelGroup>
                    <rich:spacer width="40px"/>

                    <a4j:commandButton id="imprimirPDF" actionListener="#{DescontoOcupacaoControleRel.escolheRelatorio}"
                                       oncomplete="#{DescontoOcupacaoControleRel.mensagemNotificar}#{DescontoOcupacaoControleRel.msgAlert}" accesskey="2" styleClass="botoes"
                                       reRender="form" image="/imagens/imprimir.png"
                                       rendered="#{not empty DescontoOcupacaoControleRel.listaHorarioTurma}">
                        <f:attribute name="tipoRelatorio" value="PDF"/>
                    </a4j:commandButton>
                    <rich:spacer width="10px"/>
                    <a4j:commandButton image="/imagens/btn_excel.png" title="Exportar EXCEL"
                                       rendered="#{not empty DescontoOcupacaoControleRel.listaHorarioTurma}"
                                       reRender="mensagem"
                                       value="Excel"                                       
                                       actionListener="#{DescontoOcupacaoControleRel.escolheRelatorio}"
                                       oncomplete="#{DescontoOcupacaoControleRel.mensagemNotificar}#{DescontoOcupacaoControleRel.msgAlert}">
                        <f:attribute name="tipoRelatorio" value="EXCEL"/>
                    </a4j:commandButton>






                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="panelhorarioTurma" columns="1" width="100%" >
                <rich:dataTable id="modalidadeTurma" width="100%" rowClasses="linhaImpar, linhaPar"  columnClasses="colunaCentralizada"
                                value="#{DescontoOcupacaoControleRel.listaHorarioTurma}" var="horarioTurmaCliente" rowKeyVar="status">
                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column sortBy="#{horarioTurmaCliente.contrato}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Contrato"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.contrato}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.dataContrato}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Data Contrato"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.dataContrato}"><f:convertDateTime pattern="dd/MM/yyyy"/></h:outputText>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.aluno}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Aluno"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.aluno}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.horarioTurma.identificadorTurma}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloTurma}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.horarioTurma.identificadorTurma}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.horarioTurma.professor.pessoa.nome}" filterEvent="onkeyup" >
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloProfessor}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.horarioTurma.professor.pessoa.nome}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.horarioTurma.diaSemana_Apresentar}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloDiaSemana}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.horarioTurma.diaSemana_Apresentar}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.horarioTurma.horaInicial}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloInicio}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.horarioTurma.horaInicial}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.horarioTurma.horaFinal}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloFim}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.horarioTurma.horaFinal}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.horarioTurma.nrMaximoAluno}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloVagas}"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.horarioTurma.nrMaximoAluno}"/>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.percOcupacao}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Tx Ocupação"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.percOcupacao}"><f:converter converterId="FormatarPercentual" /></h:outputText>
                    </rich:column>
                    <rich:column sortBy="#{horarioTurmaCliente.percDesconto}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="% Desconto"/>
                        </f:facet>
                        <h:outputText value="#{horarioTurmaCliente.percDesconto}"><f:converter converterId="FormatarPercentual" /></h:outputText>
                    </rich:column>

                </rich:dataTable>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>

    <rich:modalPanel headerClass="myHeader" id="panelAlunos" autosized="true" shadowOpacity="true" width="600">
        <f:facet name="header">
            <h:panelGrid columns="1" style="height:25px; background-image:url('../../imagens/fundoBarraTopo.png'); background-repeat: repeat-x;"
                         columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ConsultaAlunoTurma_listaAlunos}"/>
            </h:panelGrid>            
        </f:facet>
        <a4j:form id="formAlunos" ajaxSubmit="true" >            
            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda" width="100%">
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloTurma}:"/>
                <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.horario.identificadorTurma}"/>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloProfessor}:"/>
                <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.horario.professor.pessoa.nome}"/>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloAmbiente}"/>
                <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.horario.ambiente.descricao}"/>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloDiaSemana}"/>
                <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.horario.diaSemana_Apresentar}"/>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloHorarios}:"/>
                <h:panelGroup>
                    <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.horario.horaInicial} - "/>
                    <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.horario.horaFinal}"/>
                </h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_ConsultarTurma_tituloFreqMedia}"/>
                <h:panelGroup>
                    <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.horario.freqMedia}" title="Frequencia Média dos Ultimos 30 dias.">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                    <h:outputText value="%"/>
                </h:panelGroup>

                <h:outputText value="Previstas:"/>
                <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.qtdPrevisto}"/>

                <h:outputText value="Presenças:"/>
                <h:outputText value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.qtdPresencas}"/>
            </h:panelGrid>
            <div style="padding-top: 10px;"></div>
            <h:panelGroup layout="block" id="oi" style="overflow-y: auto; height: 200px">
                <rich:dataTable id="alunos" width="100%" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaCentralizada"
                                value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.listaAlunos}" var="aluno"
                                rowKeyVar="status">
                    <rich:column sortBy="#{aluno.matriculaAlunoHorarioTurmaVO.codigo}">
                        <f:facet name="header">
                            <h:outputText value="Código"/>
                        </f:facet>
                        <h:outputText value="#{aluno.matriculaAlunoHorarioTurmaVO.codigo}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.clienteVO.matricula}">
                        <f:facet name="header">
                            <h:outputText value="Matrícula"/>
                        </f:facet>
                        <h:outputText value="#{aluno.clienteVO.matricula}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.clienteVO.pessoa.nome}">
                        <f:facet name="header">
                            <h:outputText value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{aluno.clienteVO.pessoa.nome}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.contratoDuracaoVO.contrato}">
                        <f:facet name="header">
                            <h:outputText value="Contrato"/>
                        </f:facet>
                        <h:outputText value="#{aluno.contratoDuracaoVO.contrato}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.qtdeAulasPeriodo}">
                        <f:facet name="header">
                            <h:outputText value="Previstas"/>
                        </f:facet>
                        <h:outputText value="#{aluno.qtdeAulasPeriodo}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.qtdePresencasPeriodo}">
                        <f:facet name="header">
                            <h:outputText value="Presenças"/>
                        </f:facet>
                        <h:outputText value="#{aluno.qtdePresencasPeriodo}"/>
                    </rich:column>
                    <rich:column sortBy="#{aluno.frequenciaAluno}">
                        <f:facet name="header">
                            <h:outputText value="Frequencia"/>
                        </f:facet>
                        <h:outputText value="#{aluno.frequenciaAluno}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText value="%"/>
                    </rich:column>
                </rich:dataTable>

                <rich:dataTable id="reposicoes" width="100%" rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaCentralizada" var="repo" rowKeyVar="status"
                                rendered="#{not empty DescontoOcupacaoControleRel.horarioTurmaSelecionado.listaReposicoes}"
                                value="#{DescontoOcupacaoControleRel.horarioTurmaSelecionado.listaReposicoes}">
                    <f:facet name="header">
                        <h:outputText value="Alterações por Reposição"/>
                    </f:facet>

                    <rich:column sortBy="#{repo.cliente.pessoa.nome}">
                        <f:facet name="header">
                            <h:outputText value="Nome"/>
                        </f:facet>
                        <h:outputText value="#{repo.cliente.pessoa.nome}"/>
                    </rich:column>
                    <rich:column sortBy="#{repo.tipo}">
                        <f:facet name="header">
                            <h:outputText value="Situação"/>
                        </f:facet>
                        <h:outputText value="#{repo.tipoComoString}"/>
                    </rich:column>
                    <rich:column sortBy="#{repo.dataReposicao}">
                        <f:facet name="header">
                            <h:outputText value="Dt.Aula"/>
                        </f:facet>
                        <h:outputText value="#{repo.data_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <div style="margin:10px 0 0 0;"></div>
            <h:panelGrid columns="1" width="100%" columnClasses="centralizado">
                <h:panelGroup>
                    <a4j:commandButton id="imprimirPDF" action="#{DescontoOcupacaoControleRel.escolheRelatorioAluno}"
                                       oncomplete="imprimirRelatorio(this.form);" accesskey="2" styleClass="botoes"
                                       reRender="form" image="../imagens/imprimirContrato.png"/>
                    <rich:spacer width="10px"/>
                    <a4j:commandButton oncomplete="Richfaces.hideModalPanel('panelAlunos')"
                                       value="#{msg_bt.btn_selecionar}" styleClass="botoes"
                                       image="/imagens/botaoFechar.png"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="../includes/cliente/include_modal_reposicoes.jsp" %>

</f:view>


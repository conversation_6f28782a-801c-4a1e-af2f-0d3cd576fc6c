<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view locale="#{SuperControle.idioma}">
    <title>
        <h:outputText value="#{msg_aplic.prt_ClientePorAniversario_tituloForm}"/>
    </title>
    
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ClientePorAniversario_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-gerar-uma-lista-de-aniversariantes/"/>
    
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>
        <h:form id="form">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
                <h:panelGrid id="gridFiltros" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  styleClass="tituloCampos" rendered="#{AniversarioControle.permissaoConsultaTodasEmpresas}" value="#{msg_aplic.prt_ClientePorAniversario_empresa}" />
                    <h:panelGroup rendered="#{AniversarioControle.permissaoConsultaTodasEmpresas}">
                        <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{AniversarioControle.filtroEmpresa}" >
                            <f:selectItems value="#{AniversarioControle.listaEmpresas}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_periodo}:" />
                    <h:panelGroup>
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{AniversarioControle.dataInicio}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false" />
                            <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <rich:spacer width="5px"/>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_ate}" />
                        <rich:spacer width="5px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{AniversarioControle.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true" />
                            <rich:jQuery id="mskData2" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                            <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <%--<h:outputText  styleClass="tituloCampos" value="    " />--%>
                        <rich:spacer width="10px"/>
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <a4j:commandLink id="mes" action="#{AniversarioControle.selecionarMesAtual}"
                                             value=" Selecionar Mês Atual  "
                                             styleClass="pure-button pure-button-small texto-size-12-real"
                                             accesskey="1" reRender="gridFiltros">
                            </a4j:commandLink>
                        </c:if>
                        <c:if test="${modulo eq 'centralEventos'}">
                            <a4j:commandLink id="mes" action="#{AniversarioControle.selecionarMesAtual}"
                                             value="  Selecionar Mês Atual  "
                                             styleClass="pure-button pure-button-small pure-button-primary"
                                             accesskey="1" reRender="dataInicio,dataTermino">
                            </a4j:commandLink>
                        </c:if>
                    </h:panelGroup>

                    <h:outputText  id="tipoPessoa" styleClass="tituloCampos" value="Pessoa:" />

                    <h:selectOneMenu id="pessoa" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     value="#{AniversarioControle.tipoPessoaSelecionada}">
                        <f:selectItems value="#{AniversarioControle.listaSelectItemTipoPessoa}"/>
                        <a4j:support action="#{AniversarioControle.montarDadosPlano}" event="onchange" reRender="nomePlano,panelAluno,gridFiltros"/>
                    </h:selectOneMenu>
                    <c:if test="${modulo eq 'zillyonWeb' && AniversarioControle.tipoPessoaSelecionada eq 'CL' or AniversarioControle.tipoPessoaSelecionada eq 'AM'}">
                        <h:outputText  id="nomePlano" styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_plano}" rendered="#{AniversarioControle.pessoaAluno}" />
                        <h:panelGroup id="panelAluno" rendered="#{AniversarioControle.pessoaAluno}">
                            <h:selectOneMenu   id="plano" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{AniversarioControle.plano.codigo}" >
                                <f:selectItems  value="#{AniversarioControle.listaPlanos}" />
                                <a4j:support
                                        event="onchange"
                                        action="#{AniversarioControle.obterPlanoEscolhido}" />
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </c:if>

                        <h:outputText id="campoSituacao" styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_situacao}" />
                        <h:outputText id="campoEspacamento" styleClass="tituloCampos" value="" />
                        <h:outputText id="rotuloClienteAtivo" styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_ativo}" />
                        <h:selectBooleanCheckbox  id="clienteAtivo" styleClass="tituloCampos" value="#{AniversarioControle.situacaoAtivo}"/>
                        <h:outputText id="rotuloClienteInativo" styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_inativo}" />
                        <h:selectBooleanCheckbox id="clienteInativo" styleClass="tituloCampos" value="#{AniversarioControle.situacaoInativo}"/>

                        <h:outputText rendered="#{AniversarioControle.pessoaAluno or AniversarioControle.pessoaAmbos}" styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_visitante}" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" rendered="#{AniversarioControle.pessoaAluno or AniversarioControle.pessoaAmbos}" value="#{AniversarioControle.situacaoVisitante}"/>
                        <h:outputText rendered="#{AniversarioControle.pessoaAluno or AniversarioControle.pessoaAmbos}" styleClass="tituloCampos" value="#{msg_aplic.prt_ClientePorAniversario_trancado}" />
                        <h:selectBooleanCheckbox styleClass="tituloCampos" rendered="#{AniversarioControle.pessoaAluno or AniversarioControle.pessoaAmbos}" value="#{AniversarioControle.situacaoTrancado}"/>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:outputText styleClass="mensagem"  value="#{AniversarioControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AniversarioControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <rich:spacer width="20px"/>
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="imprimirPDF" action="#{AniversarioControle.imprimir}"
                                                 styleClass="pure-button pure-button-small pure-button-primary"
                                                 accesskey="2" reRender="mensagem">
                                    <i class="fa-icon-search"></i> &nbsp Gerar Relatório
                                </a4j:commandLink>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="imprimirPDF" action="#{AniversarioControle.imprimir}"
                                                   value="Gerar Relatório " accesskey="2" styleClass="botoes"
                                                   image="/imagens/botoesCE/gerar_relatorio.png" reRender="mensagem"/>
                            </c:if>


                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
 <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true" />
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

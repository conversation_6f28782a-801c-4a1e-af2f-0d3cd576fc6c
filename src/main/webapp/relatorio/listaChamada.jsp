<%-- 
    Document   : listaAlunosTurma
    Created on : 23/11/2011, 09:24
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<head>
    <%@include file="../includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script src="script/packJQueryPlugins.min.js" type="text/javascript" ></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="3" cellspacing="0">
            <body>
            <h:form id="form" >
                <h:commandLink action="#{ConsultarTurmaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
                <a4j:commandLink action="#{ConsultarTurmaControle.imprimirRelatorio}"
                                 oncomplete="location.href=\"#{ConsultarTurmaControle.nomeArquivoRelatorioGeradoAgora}\""
                                 ajaxSingle="true" id="imprimirRelatorio"  style="display: none"/>
                <h:inputHidden id="relatorio" value="#{ConsultarTurmaControle.relatorio}"/>

                <h:panelGrid columns="1" width="100%" >

                    <h:panelGrid columns="1" columnClasses="centralizado" style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" width="100%">
                        <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ConsultarTurma_tituloListaChamada}"/>
                    </h:panelGrid>
                    <br/>

                    <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" id="tabela"
                                    value="#{ConsultarTurmaControle.listaChamada}" var="aluno" rowKeyVar="status" >
                        <f:facet name="header">
                            <rich:columnGroup>
                                <rich:column colspan="50">
                                    <h:panelGrid columns="4" columnClasses="colunaEsquerda, colunaDireita" width="100%">
                                        <h:panelGroup>
                                            <rich:spacer width="10px"/>
                                            <h:outputText style="font-weight:bold;" styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaListaChamada.turma.identificador} - "/>
                                            <h:outputText style="font-weight:bold;" styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaListaChamada.ambiente} - "/>
                                            <h:outputText style="font-weight:bold;" styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaListaChamada.professor} - "/>
                                            <h:outputText style="font-weight:bold;" styleClass="tituloCampos" value="(#{ConsultarTurmaControle.horarioTurmaListaChamada.horaInicial} - "/>
                                            <h:outputText style="font-weight:bold;" styleClass="tituloCampos" value="#{ConsultarTurmaControle.horarioTurmaListaChamada.horaFinal})"  />


                                        </h:panelGroup>
                                        <h:panelGroup >
                                            <h:outputText styleClass="tituloCampos" value="Data de Início: "/>
                                            <rich:calendar id="dataInicio" value="#{ConsultarTurmaControle.dataInicio}"
                                                           inputSize="14" inputClass="form" direction="bottom-left"
                                                           oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy" enableManualInput="true"
                                                           zindex="2" showWeeksBar="false" >
                                            </rich:calendar>
                                            <rich:jQuery id="mskDataInicio" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                            <rich:spacer width="10px"/>
                                        </h:panelGroup>
                                        <h:panelGroup>
                                            <h:outputText styleClass="tituloCampos" value="Data de Fim: "/>
                                            <rich:calendar id="dataFim" value="#{ConsultarTurmaControle.dataFim}"
                                                           inputSize="8" inputClass="form" direction="bottom-left"
                                                           oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy" enableManualInput="true"
                                                           zindex="2" showWeeksBar="false" >
                                            </rich:calendar>
                                            <rich:jQuery id="mskDataFim" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <a4j:commandLink id="atualizarLista" action="#{ConsultarTurmaControle.selecionarMesReferenciaTela}"
                                                             reRender="panelMesReferencia, tabela, mensagem"
                                                             oncomplete="#{ConsultarTurmaControle.mensagemNotificar}"
                                                             title="#{msg.msg_consultar_dados}"
                                                             style="line-height: 15px;"
                                                             accesskey="4" styleClass="botoes nvoBt">
                                                Atualizar&nbsp<i class="fa-icon-search"></i>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                </rich:column>
                                <rich:column breakBefore="true" rowspan="2"><h:outputText value="Nome do Aluno" /></rich:column>
                                <rich:columns value="#{ConsultarTurmaControle.colunas}" var="diasMes" index="ind">
                                    <h:outputText value="#{diasMes.diaChamada}"/>
                                </rich:columns>
                                <rich:column breakBefore="true" visible="false"/>
                                <rich:columns value="#{ConsultarTurmaControle.colunas}" var="diasSemana" index="ind">
                                    <h:outputText value="#{diasSemana.diaSemana}"/>
                                </rich:columns>
                            </rich:columnGroup>
                        </f:facet>
                        <rich:column styleClass="colunaEsquerda" width="30%">
                            <h:graphicImage style="width:30px;height:30px; border-radius: 50%;"
                                            styleClass="tooltipsterright"
                                            url="#{aluno.aluno.urlFoto}">
                            </h:graphicImage>&nbsp;&nbsp;
                            <h:outputText value="#{aluno.aluno.nome}"/>
                            <a4j:commandButton rendered="#{aluno.aluno.qtdeTelefones > 0}"
                                                action="#{ConsultarTurmaControle.prepararListaTelefones}"
                                               oncomplete="#{rich:component('modalPanelTelefoneAluno')}.show();"
                                               image="../imagens/telefone.png" title="Telefone(s) p/ Contato"/>
                        </rich:column>
                        <rich:columns value="#{ConsultarTurmaControle.colunas}" var="diasMes" index="ind" styleClass="centralizado">
                            <h:selectBooleanCheckbox
                                style="vertical-align:middle;"

                                value="#{aluno.listaPresencas[ind].presente}"
                                rendered="#{aluno.listaPresencas[ind].aula && !aluno.listaPresencas[ind].carencia}">
                                <a4j:support event="onclick" action="#{ConsultarTurmaControle.gravarPresenca}" reRender="mensagem, tabela:#{status}" ajaxSingle="true" process="this">
                                    <f:setPropertyActionListener target="#{ConsultarTurmaControle.presenca}" value="#{aluno.listaPresencas[ind]}"/>
                                    <f:setPropertyActionListener target="#{ConsultarTurmaControle.pessoa}" value="#{aluno.aluno}"/>
                                </a4j:support>
                            </h:selectBooleanCheckbox>
                            <h:outputText style="vertical-align:middle;" title="Reposição" value="#{aluno.listaPresencas[ind].repo != null ? 'R' : ''}"
                                          rendered="#{aluno.listaPresencas[ind].aula}"/>
                            <h:outputText style="vertical-align:middle;" title="Carência" value="CR"
                                          rendered="#{aluno.listaPresencas[ind].carencia}"/>
                        </rich:columns>
                    </rich:dataTable>

                    <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                        <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                            <h:outputText styleClass="mensagem"  value="#{ConsultarTurmaControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ConsultarTurmaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink styleClass="texto-cor-azul texto-size-14 linkPadrao" action="consultaturma" title="voltar para consulta">
                                <h:outputText styleClass="fa-icon-arrow-left" />
                                <h:outputText value=" Voltar" styleClass="texto-font" />
                            </a4j:commandLink>
                            <rich:spacer width="10px"/>
                            <a4j:commandLink id="imprimirPDF" ajaxSingle="true"
                                               title="Gerar Relatório"
                                               action="#{ConsultarTurmaControle.imprimirPDF}"
                                               oncomplete="imprimirRelatorio(this.form);"
                                               accesskey="2" styleClass="texto-size-14 botaoPrimario" reRender="form">
                                <h:outputText styleClass="fa-icon-print" />
                            </a4j:commandLink>
                            <rich:spacer width="10px"/>
                            <a4j:commandLink  id="visualizarLog"
                                             action="#{ConsultarTurmaControle.realizarConsultaLogObjetoSelecionadoPresenca}"
                                             title="Visualisar Log"
                                             accesskey="5" styleClass="botaoSecundario texto-size-14"
                                 oncomplete="abrirPopup('../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);" >
                                <h:outputText value="" styleClass="fa-icon-list" />
                            </a4j:commandLink>


                    </h:panelGroup>
                </h:panelGrid>
            </h:form>
        </body>
    </h:panelGrid>
    </html>



<rich:modalPanel id="modalPanelTelefoneAluno" autosized="true" shadowOpacity="true" zindex="99"
                 showWhenRendered="#{ConsultarTurmaControle.apresentarModalTelefoneAluno}" width="100"
                 height="80" onshow="focusAt('modalPanelTelefoneFechar');">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Telefone(s)"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formTelefoneAluno" prependId="false">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >
            <rich:dataTable value="#{ConsultarTurmaControle.alunoTelefone.aluno.telefoneVOs}" var="item" 
                            style="border-width: 1px;" width="100%">
                <rich:column style="border-width: 1px;">
                    <f:facet name="header" >
                        <h:outputText value="Número" />
                    </f:facet>
                    <h:outputText value="#{item.numero}" />
                </rich:column>
            </rich:dataTable>            
            <a4j:commandButton id="modalPanelTelefoneFechar" value="Fechar" title="Fechar" action="#{ConsultarTurmaControle.acaoFecharModalTelefoneAluno}" reRender="modalPanelTelefoneAluno" />
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>


    <%@include file="../includes/include_modal_mensagem_generica.jsp" %>
</f:view>
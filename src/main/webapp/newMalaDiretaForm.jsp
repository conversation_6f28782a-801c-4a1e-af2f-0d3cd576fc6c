<%@ page contentType="text/html;charset=ISO-8859-1" pageEncoding="ISO-8859-1" language="java" %>
<%@ include file="includes/imports.jsp" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="Javascript" src="script/scriptSMS.js"></script>
<script type="text/javascript" language="Javascript" src="script/script.js"></script>

<style type="text/css">
    .btnsOptAPP {
        margin-left: 10px;
        float: left;
        display: none;
    }

    .classeEsquerda {
        width: 25%;
        text-align: right;
        font-weight: bold;
        vertical-align: middle;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        text-transform: none;
        color: #333;
        line-height: 150%;
    }

    .classeDireita {
        width: 25%;
        text-align: left;
    }

    .corrTopo {
        vertical-align: top;
    }

    .rich-table-cell {
        border-color: transparent !important;
    }

    .semBordaEsquerda {
        border-left: none !important;
    }

    .bordaEsquerdaTh th {
        border-left: solid 1px #C0C0C0 !important;
    }

    .mensagensTags {
        font-size: 12px;
        color: red;
        margin-left: 7px;
        margin-bottom: 13px;
    }

</style>


<script type="text/javascript">

    function validarRemetente() {
        var validade = true;
        var remetente = document.getElementById('formF:textColaboradorResponsavel').value;

        if ((remetente == null || remetente == "")) {
            validade = confirm('REMETENTE está vazio. Deseja enviar com o remetente padrão?');
        }
        return validade;
    }   

    function mascarahora(field, sMask, evtKeyPress) {
        var i, nCount, sValue, fldLen, mskLen, bolMask, sCod;

        var nTecla = (evtKeyPress.charCode || evtKeyPress.keyCode || evtKeyPress.which);
        if (evtKeyPress.keyCode != 0
            && ((nTecla == 8) || (nTecla == 9) || (nTecla == 18) || (nTecla == 27) || (nTecla == 33) || (nTecla == 34) || (nTecla == 35) || (nTecla == 36)
                || (nTecla == 37) || (nTecla == 38) || (nTecla == 39) || (nTecla == 40) || (nTecla == 45) || (nTecla == 46))) {
            return true;
        }

        sValue = field.value;

        // Limpa todos os caracteres de formatação que já estiverem no campo.
        sValue = sValue.toString().replace(/[_\W]/g, "");
        fldLen = sValue.length;
        mskLen = sMask.length;


        i = 0;
        nCount = 0;
        sCod = "";
        mskLen = fldLen;

        while (i <= mskLen) {
            bolMask = sMask.charAt(i).search(/[_\W]/) >= 0;

            if (bolMask) {
                sCod += sMask.charAt(i);
                mskLen++;
            } else {
                sCod += sValue.charAt(nCount);
                nCount++;
            }

            i++;
        }
        if (sMask.length == sCod.length) {
            return false;
        }

        field.value = sCod;

        if (sMask.charAt(i - 1) == "9") { // apenas números...
            return ((nTecla > 47) && (nTecla < 58));
        } // números de 0 a 9
        else { // qualquer caracter...
            return true;
        }
    }

    function mostrarProximo(item) {
        jQuery('.labelOp' + (item + 1)).show();
        jQuery('.btnOp' + item).hide();
        jQuery('.btnOp' + (item + 1)).show();
    }
    function sumirItem(item) {
        jQuery('.labelOp' + item).hide();
        jQuery('.form.labelOp' + item).val('');
        jQuery('.btnOp' + (item - 1)).show();
        jQuery('.btnOp' + item).hide();
    }




</script>

<link href="css_pacto.css" rel="stylesheet" type="text/css">
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
    <h:form id="formF" style="border: 0; margin: 0">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <h:panelGroup layout="block" rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}">

            <h:panelGrid style="width: 100%;" columnClasses="classEsquerda, classDireita"
                         columns="2">
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_New_Mala_Direta_Form_Codigo}"/>
                <h:panelGroup id="panelCodigoCRMExtra">
                    <h:inputText id="textCodigoCRMExtra" size="10" disabled="true"
                                 styleClass="form"
                                 value="#{MalaDiretaControle.malaDiretaVO.codigo}"/>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid style="width: 100%;" columnClasses="classEsquerda, classDireita"
                         columns="2" rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}" >
                <h:outputText styleClass="tituloCampos" value="Empresa:"/>
                <h:panelGroup id="panelEmpresaCRMExtra">
                    <h:selectOneMenu styleClass="form" id="empresaCRMExtra"
                                     value="#{MalaDiretaControle.malaDiretaVO.empresa.codigo}">
                        <f:selectItems value="#{MalaDiretaControle.listaEmpresas}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid style="width: 100%;" columnClasses="classEsquerda, classDireita"
                         columns="2">
                <h:outputText styleClass="tituloCampos" value="Nome da Meta:"/>
                <h:panelGroup id="panelTituloCRMExtra">
                    <h:inputText id="textTituloCRMExtra" size="50" maxlength="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 onkeypress="bloquearEnter();"
                                 value="#{MalaDiretaControle.malaDiretaVO.titulo}"/>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid columnClasses="classEsquerda, classDireita" columns="2"
                         style="width: 100%;" id="painelDadosMeta">
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_New_Mala_Direta_Form_Periodo}"/>
                <h:panelGroup id="periodoExecucaoCRMExtra">
                    <rich:calendar value="#{MalaDiretaControle.malaDiretaVO.dataEnvio}" id="dataInicialCRMExtra"
                                   datePattern="dd/MM/yyyy"
                                   inputSize="8"
                                   oninputchange="validar_Data(this.id);"
                                   oninputkeypress="bloquearEnter();"
                                   enableManualInput="true" zindex="2" showWeeksBar="false"/>

                    <h:outputText styleClass="tituloCampos"
                                  value="  #{msg_aplic.prt_New_Mala_Direta_Form_ate}  "/>

                    <rich:calendar value="#{MalaDiretaControle.malaDiretaVO.vigenteAte}" id="dataFinalCRMExtra"
                                   datePattern="dd/MM/yyyy"
                                   inputSize="8"
                                   oninputchange="validar_Data(this.id);"
                                   oninputkeypress="bloquearEnter();"
                                   enableManualInput="true" zindex="2" showWeeksBar="false"/>
                    <a4j:commandButton id="limparPeriodoCRMExtra"
                                       action="#{MalaDiretaControle.limparPeriodoExecucaoForm}"
                                       image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                       reRender="periodoExecucaoCRMExtra"/>
                </h:panelGroup>

                <h:outputText  rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}" styleClass="tituloCampos" value="Meta Extra por Grupo:"/>
                <h:selectBooleanCheckbox id="metaExtraGrupo" rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"  value="#{MalaDiretaControle.malaDiretaVO.metaExtraIndividual}"
                                         disabled="#{MalaDiretaControle.malaDiretaVO.listaImportada}">
                    <a4j:support event="onchange" reRender="painelDadosMeta,usuarioMetaExtra"/>
                </h:selectBooleanCheckbox>

                <h:outputText  rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra && MalaDiretaControle.malaDiretaVO.metaExtraIndividual}" styleClass="tituloCampos" value="Grupo desejado:"/>
                <h:selectOneMenu rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra && MalaDiretaControle.malaDiretaVO.metaExtraIndividual}"
                                 id="grupoDesejado"
                                 value="#{MalaDiretaControle.malaDiretaVO.tipoConsultorMetaExtraIndividual}">
                    <f:selectItems value="#{MalaDiretaControle.listSelectItemTipoConsultor}"/>
                </h:selectOneMenu>
                <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra && MalaDiretaControle.malaDiretaVO.metaExtraIndividual}">
                    <i class="fa-icon-question-sign show-help" id="infGrupo" style="display: inline-block;margin-top: -20px;margin-right: -154px;"/>
                </h:panelGroup>
                <rich:toolTip for="metaExtraGrupo">
                    <span>Ao marcar esta opção, será habilitado um novo campo "Grupo desejado" para que essa meta extra seja destinada apenas à um determinado grupo de colaboradores. Exemplo: Professor, Consultor, Coordenador.</span><br/>
                </rich:toolTip>
                <rich:toolTip for="infGrupo">
                    <span>Para que essa meta extra seja gerada para o Colaborador deste grupo selecionado, o Colaborador deve possuir vínculo com os alunos e deve possuir um Cadastro de Usuário.</span><br/>
                </rich:toolTip>
            </h:panelGrid>
        </h:panelGroup>

        <h:panelGroup layout="block" >


            <h:panelGroup id="painelEnviando" layout="block" rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}">
                <!-- Painel Enviando -->
                <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0"  
                       style="padding: 10px; display: block;">
                    <tr>
                        <td align="left" valign="top" style="padding-bottom: 5px;">
                            <h:panelGroup layout="block" style="clear: both; display: grid;" styleClass="text">

                                <h:panelGroup layout="block" rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj}"
                                              style="padding-bottom: 10px">
                                    <h:outputText
                                            style="margin-left: 12px;margin-right: 5px;font-weight: bold" value="#{msg_aplic.prt_New_Mala_Direta_Form_Codigo}:"/>
                                    <h:outputText
                                            style="margin-left: 12px;margin-right: 5px;font-weight: bold" value="#{MalaDiretaControle.malaDiretaVO.codigo}"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panelEmpresaMala" rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}"
                                              style="padding-bottom: 10px">
                                    <h:outputText
                                            style="margin-left: 12px;margin-right: 5px;font-weight: bold" value="Empresa:"/>
                                    <h:selectOneMenu id="empresa" styleClass="form" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);"
                                                     disabled="#{MalaDiretaControle.malaDiretaVO.contatoDireto || !MalaDiretaControle.malaDiretaVO.novoObj}"
                                                     value="#{MalaDiretaControle.malaDiretaVO.empresa.codigo}">
                                        <f:selectItems value="#{MalaDiretaControle.listaEmpresas}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panelTodasEmpresa"
                                              rendered="#{MalaDiretaControle.permiteCriarMailingTodasEmpresas || MalaDiretaControle.malaDiretaVO.todasEmpresas}"
                                              style="padding-bottom: 10px">
                                    <h:outputText style="margin-left: 12px;margin-right: 5px;font-weight: bold"
                                                  value="Buscar alunos todas unidades:"/>
                                    <h:selectBooleanCheckbox id="permiteCriarMailingTodasEmpresas"
                                                             value="#{MalaDiretaControle.malaDiretaVO.todasEmpresas}"
                                                             disabled="#{MalaDiretaControle.malaDiretaVO.contatoDireto || !MalaDiretaControle.malaDiretaVO.novoObj}"/>
                                </h:panelGroup>

                                <h:panelGroup layout="block" id="panelMeioEnvio" style="padding-bottom: 10px">
                                    <h:outputText style="margin-left: 12px;margin-right: 5px;font-weight: bold" value="Enviando:"/>
                                    <h:outputText rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj}"
                                                  styleClass="tituloCampos"
                                                  value="#{MalaDiretaControle.malaDiretaVO.meioDeEnvioEnum.descricao}"/>

                                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     rendered="#{MalaDiretaControle.malaDiretaVO.novoObj}"
                                                     styleClass="form" id="meioDeEnvio"
                                                     value="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio}">
                                        <f:selectItems value="#{MalaDiretaControle.listaSelectItemMeioDeEnvio}"/>
                                        <a4j:support event="onchange" action="#{MalaDiretaControle.limparCampos}"
                                                     reRender="formF"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:outputText style="margin-left: 12px;font-weight: bold;margin-top: 10px;"
                                              rendered="#{MalaDiretaControle.malaDiretaVO.meioDeEnvio == 5 and !MalaDiretaControle.configuracaoFtpValida}"
                                              value="#{msg_aplic.prt_New_Mala_Direta_Form_prametros_conexao_FTP}"/>

                                <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.sms}"
                                              style="margin-left: 12px;"
                                              styleClass="textoConfiguracoes"
                                              value="#{MalaDiretaControle.saldoApresentar}"/>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>


                <!-- Painel Remetente -->
                <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" bgcolor="#fff"
                       style="padding: 10px; display: block;">
                    <tr>
                        <td align="left" valign="top" style="padding-bottom: 5px;">
                            <h:panelGrid style="width: 100%" columnClasses="classEsquerda, classDireita" columns="2">
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_remetente}"/>
                                <h:panelGroup id="panelResponsavelGrupo">
                                    <h:inputText id="textColaboradorResponsavel" size="50"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 value="#{MalaDiretaControle.malaDiretaVO.remetente.nome}"/>
                                    <rich:suggestionbox height="200" width="200"
                                                        for="textColaboradorResponsavel"
                                                        suggestionAction="#{MalaDiretaControle.executarAutocompleteRemetente}"
                                                        minChars="1" rowClasses="20"
                                                        status="statusInComponent"
                                                        nothingLabel="#{msg_aplic.prt_New_Mala_Direta_Form_reponsavel}"
                                                        var="result" fetchValue="#{result.nome}" id="suggestionResponsavel">
                                        <h:column>
                                            <h:outputText value="#{result.nome}"/>
                                        </h:column>
                                        <a4j:support event="onselect" action="#{MalaDiretaControle.setarRemetente}"/>
                                    </rich:suggestionbox>
                                </h:panelGroup>
                            </h:panelGrid>
                        </td>
                    </tr>
                </table>
            </h:panelGroup>

            <!-- Painel Destinatário -->
            <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" style="padding: 10px; display: block;">

                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <h:outputText rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.malaDiretaVO.crmExtra && !MalaDiretaControle.contatoInstantaneo}"
                                          style="margin-left: 12px;font-weight: bold"
                                          value="Selecione o Grupo de Destinatários por:"/>
                            <h:outputText rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && MalaDiretaControle.malaDiretaVO.crmExtra && !MalaDiretaControle.contatoInstantaneo}"
                                          style="margin-left: 12px;font-weight: bold"
                                          value="Selecione o Grupo de Alunos por:"/>
                            <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo}"
                                          style="margin-left: 12px;font-weight: bold"
                                          value="Esta mensagem não pode ser editada pois foi um contato direto com um aluno ou grupo de alunos."/>
                            <h:outputText rendered="#{MalaDiretaControle.contatoInstantaneo}"
                                          style="margin-left: 12px;font-weight: bold"
                                          value="Esta mensagem não pode ser editada pois foi um contato instantâneo."/>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td width="900px">
                        <h:panelGrid width="100%" columns="2" columnClasses="classDireitaConfiguracao" cellpadding="0"
                                     rowClasses="linhaTop" rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo}">

                            <h:panelGroup>
                                <h:panelGrid id="pnlEvtPreDef" columnClasses="classeEsquerda,classeDireita" styleClass="text"
                                             width="100%" columns="2"
                                             style="padding: 0 !important;">

                                    <h:outputText styleClass="text" value="Importar de uma lista"  rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"/>
                                    <h:selectBooleanCheckbox value="#{MalaDiretaControle.malaDiretaVO.importarLista}" rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                             disabled="#{MalaDiretaControle.malaDiretaVO.listaImportada}">
                                        <a4j:support event="onchange" reRender="formF"/>
                                    </h:selectBooleanCheckbox>


                                    <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_pre_definido}" rendered="#{!MalaDiretaControle.malaDiretaVO.importarLista}"/>
                                    <h:selectOneMenu value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.eventoCodigo}"
                                                     rendered="#{!MalaDiretaControle.malaDiretaVO.importarLista}"
                                                     id="eventoPreDefLabel"
                                                     style="width: 100%">
                                        <f:selectItem itemValue="" itemLabel=""/>
                                        <f:selectItems value="#{MalaDiretaControle.listaSelectItemEventos}"/>
                                        <a4j:support event="onchange" reRender="formF"
                                                     action="#{MalaDiretaControle.limparDias}"/>
                                    </h:selectOneMenu>

                                    <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 5 && !MalaDiretaControle.malaDiretaVO.importarLista}"
                                                  styleClass="text"
                                                  value="Evento da Academia:"/>
                                    <h:selectOneMenu rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 5  && !MalaDiretaControle.malaDiretaVO.importarLista}"
                                                     style="width: 100%;"
                                                     value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.evento}">
                                        <f:selectItems value="#{MalaDiretaControle.listaEventos}"/>
                                    </h:selectOneMenu>
                                </h:panelGrid>
                                    <rich:panel rendered="#{MalaDiretaControle.malaDiretaVO.importarLista && !MalaDiretaControle.malaDiretaVO.listaImportada}"
                                                style="width: 100%; height: 100%; border: none;">
                                        <h:outputText styleClass="text" style="text-align: left; font-size: 11px;" value="A lista permite que sejam adicionados alunos que já estão na base e cadastros que ainda não estão. Registros sem matrícula, ou cuja matrícula não existir no sistema, serão adicionados como receptivos."/>
                                    </rich:panel>
                                <rich:panel rendered="#{MalaDiretaControle.malaDiretaVO.listaImportada}"
                                            style="width: 100%; height: 100%; border: none;">
                                    <h:outputText styleClass="text" style="text-align: left; font-size: 11px; font-weight: bold" value="Lista já importada. Não é possível importar outra."/>
                                </rich:panel>
                            </h:panelGroup>


                            <h:panelGrid id="painelCfgImportar" styleClass="text" columns="1" style="min-width: 530px;"  rendered="#{MalaDiretaControle.malaDiretaVO.importarLista}">
                                <rich:fileUpload
                                        fileUploadListener="#{MalaDiretaControle.uploadArquivoListener}"
                                        disabled="#{MalaDiretaControle.malaDiretaVO.listaImportada}"
                                        immediateUpload="true" id="imagemModeloUpload"
                                        acceptedTypes="xls,xlsx" allowFlash="false"
                                        listHeight="58px"
                                        cancelEntryControlLabel="Cancelar"
                                        addControlLabel="Adicionar lista "
                                        clearControlLabel="Remover"
                                        clearAllControlLabel="Remover Todos"
                                        doneLabel="Concluído"
                                        sizeErrorLabel="Limite de tamanho atingido"
                                        uploadControlLabel="Carregar"
                                        transferErrorLabel="Erro na transferência"
                                        stopControlLabel="Parar"
                                        stopEntryControlLabel="Parar"
                                        progressLabel="Carregando"
                                        maxFilesQuantity="1">
                                    <a4j:support event="oncomplete" oncomplete="#{MalaDiretaControle.mensagemNotificar}"/>
                                    <a4j:support event="onclear" reRender="painelCfgImportar" action="#{MalaDiretaControle.limparLista}"/>
                                </rich:fileUpload>

                                <a href="modelo/importar_meta_exemplo.xlsx">Baixar modelo de arquivo</a>

                            </h:panelGrid>

                            <h:panelGrid id="painelCfgEvento" styleClass="text" columns="1" style="min-width: 530px;"  rendered="#{!MalaDiretaControle.malaDiretaVO.importarLista}">

                                <!--configuracao de evento-->
                                <rich:panel rendered="#{MalaDiretaControle.eventoEscolhido}"
                                            style="width: 100%; height: 100%;">
                                    <h:selectOneRadio rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                      id="periodicidadeEvento"
                                                      styleClass="text" layout="lineDirection"
                                                      value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia}">
                                        <f:selectItems
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.listaOcorrencias}"/>
                                        <a4j:support event="onchange" action="#{MalaDiretaControle.alterarTipo}"
                                                     focus="#{MalaDiretaControle.irPara}"
                                                     reRender="formF"/>
                                    </h:selectOneRadio>

                                    <!-- Pos Venda -->

                                    <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.posVenda && MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 6}">
                                        <h:panelGrid columns="3">
                                            <h:outputText styleClass="text" title="#{msg_aplic.prt_titlePosVenda}" value="#{msg_aplic.prt_New_Mala_Direta_Form_qtd_pos} "/>
                                            <rich:inputNumberSpinner
                                                    id="qtdadeDiasPosVendaCRM"
                                                    value="#{MalaDiretaControle.malaDiretaVO.diasPosVenda}"
                                                    minValue="0" maxValue="999999999"/>
                                            <h:outputText styleClass="text" value="Dia(s)" title="#{msg_aplic.prt_titlePosVenda}"/>
                                        </h:panelGrid>
                                        <h:outputText styleClass="text" value=" #{msg_aplic.prt_New_Mala_Direta_Form_serao_considerados} "/><h:outputText style="font-weight: bold;font-size: unset;" value="ATIVOS"/>
                                        <h:outputText styleClass="text" value=" #{msg_aplic.prt_New_Mala_Direta_Form_serao_matriculados_filtro_padrao}"/>
                                    </h:panelGroup>

                                    <%-- Quantidade de acesso em um intervalo de dias --%>
                                    <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.acessosEmIntervaloDias}">
                                        <h:panelGrid columns="2">
                                            <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_qtd_min} "/>
                                            <rich:inputNumberSpinner
                                                    id="quantidadeMinimaDeAcessoCRM"
                                                    value="#{MalaDiretaControle.malaDiretaVO.quantidadeMinimaAcessos}"
                                                    minValue="0" maxValue="31"/>
                                            <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_qtd_max} "/>
                                            <rich:inputNumberSpinner
                                                    id="quantidadeMaximaDeAcessoCRM"
                                                    value="#{MalaDiretaControle.malaDiretaVO.quantidadeMaximaAcessos}"
                                                    minValue="0" maxValue="31"/>
                                            <h:outputText styleClass="text" value="Intervalo de dias: "/>
                                            <rich:inputNumberSpinner
                                                    id="intervaloDeDiasAcessoCRM"
                                                    value="#{MalaDiretaControle.malaDiretaVO.intervaloDias}"
                                                    minValue="0" maxValue="99999"/>
                                        </h:panelGrid>
                                    </h:panelGroup>

                                    <%-- Opções extras do evento "Cancelados" --%>
                                    <h:panelGroup
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.cancelados && MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 10}">
                                        <h:panelGrid columns="4">
                                            <h:outputText styleClass="text" value="Intervalo de dias: " title="Dias após o cancelamento"/>
                                            <rich:inputNumberSpinner
                                                    id="intervaloDiasCancelados"
                                                    value="#{MalaDiretaControle.malaDiretaVO.intervaloDias}"
                                                    minValue="0" maxValue="99999"/>
                                        </h:panelGrid>
                                    </h:panelGroup>

                                    <%-- TIPO CANCELAMENTO --%>
                                    <h:panelGroup
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.cancelados}">
                                        <h:outputText styleClass="text" value="Tipo do cancelamento: " title="Defina o tipo de cancelamento a ser considerado no mailing. TODOS: Será considerado todos
                                             os cancelamentos, independente se foi algum usuário do sistema que realizou ou se foi automático pela recorrência. MANUAL: Considera somente os cancelamentos
                                             realizados por algum usuário do sistema manualmente. AUTOMÁTICO: Considera apenas os cancelamentos realizados automaticamente pelo sistema, pelo usuário Recorrência."/>
                                        <h:selectOneMenu id="tipoCancelamentoEvento"
                                                         value="#{MalaDiretaControle.malaDiretaVO.tipoCancelamento}">
                                            <f:selectItem itemValue="0" itemLabel="Todos"/>
                                            <f:selectItem itemValue="1" itemLabel="Manual"/>
                                            <f:selectItem itemValue="2" itemLabel="Automático"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <!-- RISCO-->
                                    <rich:dataGrid id="dtgRiscos"
                                                   rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.grupoRisco}"
                                                   value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.riscos}"
                                                   width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                                   cellpadding="0" cellspacing="0" var="risco">
                                        <rich:column>
                                            <h:selectBooleanCheckbox value="#{risco.selecionado}" id="grauRiscoCRM">
                                            </h:selectBooleanCheckbox>
                                            <h:outputText value="#{risco.label}"/>
                                        </rich:column>

                                    </rich:dataGrid>
                                    <!-- PENDCIAS-->
                                    <rich:dataGrid id="dtgPendencias"
                                                   rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.evtPendencias}"
                                                   value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.pendencias}"
                                                   width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                                   cellpadding="0" cellspacing="0" var="pendencia">
                                        <rich:column>
                                            <h:selectBooleanCheckbox value="#{pendencia.selecionado}" id="selecionandoPendencia">
                                            </h:selectBooleanCheckbox>
                                            <h:outputText value="#{pendencia.label}"/>
                                        </rich:column>

                                    </rich:dataGrid>

                                    <!-- GENERICO-->
                                    <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.agendamentosProduto
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.compra
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.evtAmbientes
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.produtosVendidos
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.agendar
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.faturar
                                                              || MalaDiretaControle.malaDiretaVO.cfgEvento.profissional}">
                                        <div style="width: 100%; max-height: 340px; overflow-x: visible; overflow-y: scroll;">
                                            <rich:dataGrid id="dtgAgendamentosProduto"
                                                           value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.listaGenerica}"
                                                           width="100%" columns="3" columnClasses="semBorda"
                                                           styleClass="semBorda"
                                                           cellpadding="0" cellspacing="0" var="gen">
                                                <rich:column>
                                                    <h:selectBooleanCheckbox value="#{gen.selecionado}">
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText value="#{gen.label}"/>
                                                </rich:column>

                                            </rich:dataGrid>
                                        </div>
                                    </h:panelGroup>

                                    <!--SALDO_PONTOS-->
                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.saldoPontos}">
                                        <h:outputText styleClass="text" value="Quantidade de pontos: "/>
                                        <rich:inputNumberSpinner
                                                id="quantidadeDePontosCRM"
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.nrFaltas}"
                                                minValue="0" maxValue="999999999"/>
                                    </h:panelGrid>

                                    <!--FALTOSOS-->
                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.faltosos}">
                                        <h:outputText styleClass="text" value="Nr. dias de falta: "/>
                                        <rich:inputNumberSpinner
                                                id="nDiasDeFaltaCRM"
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.nrFaltas}"
                                                inputSize="3"/>
                                    </h:panelGrid>
                                    <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.faltosos}">
                                        <h:selectBooleanCheckbox
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.nrFaltasMaior}"/>
                                        <h:outputText styleClass="text"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_pesquisar_tambem}"/>
                                    </h:panelGroup>

                                    <!--FALTOSOS-->
                                    <h:panelGrid columns="2" rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.freepass}">
                                        <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_pesquisar_dias_inicio}"/>
                                        <rich:inputNumberSpinner value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.nrDiasInicioFreePass}" inputSize="3" id="inicioFreePassCRM"/>
                                    </h:panelGrid>

                                    <!--CONTRATO_CREDITO_TREINO-->
                                    <h:panelGroup layout="block"
                                                  id="creditoTreinoConfg"
                                                  style="display: inline-flex;"
                                                  rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.creditoTreino}">

                                        <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_Qtd_creditos}"/>

                                        <h:outputText styleClass="text" style="margin-left: 15px; margin-right: 5px" value="De "/>

                                        <rich:inputNumberSpinner
                                                id="qtdeCreditoInicialCrm"
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.minValorInt}"
                                                inputSize="3"/>

                                        <h:outputText styleClass="text" value=" #{msg_aplic.prt_New_Mala_Direta_Form_ate} " style="margin-left: 5px; margin-right: 5px"/>

                                        <rich:inputNumberSpinner
                                                id="qtdeCreditoFinalCrm"
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.maxValorInt}"
                                                inputSize="3"/>

                                    </h:panelGroup>

                                    <!--INDICADOS-->
                                    <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}">
                                        <h:selectBooleanCheckbox
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.naoClientes}"/>
                                        <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_nao_tornam_alunos}"/>
                                    </h:panelGroup>
                                    <!--DEBITO-->
                                    <h:panelGrid columns="2" rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.debito}">
                                        <h:outputText styleClass="text" value="Min. de dias em debito: "/>
                                        <rich:inputNumberSpinner
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.minimoDiasVencido}"
                                                minValue="1"
                                                inputSize="5"
                                                maxValue="99999"/>
                                        <h:outputText styleClass="text" value="Faixa de valor: "/>

                                        <h:panelGrid columns="4" cellspacing="0">
                                            <h:outputText styleClass="text" value="#{msg_aplic.prt_de}"/>
                                            <h:inputText size="6" styleClass="form" id="faixaValorInicial"
                                                         value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.minValor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:inputText>
                                            <h:outputText styleClass="text" value="#{msg_aplic.prt_ate}  "/>

                                            <h:inputText size="6" styleClass="form"
                                                         id="faixaFinalInicial"
                                                         value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.maxValor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:inputText>

                                        </h:panelGrid>
                                    </h:panelGrid>

                                    <!--PARCELAS VENCIDAS-->
                                    <h:panelGrid columns="2" rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVencidas}">
                                        <h:outputText styleClass="text" value="Qtd. min de parcelas vencidas: " />
                                        <rich:inputNumberSpinner value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.qtdMinParcelasVencidas}" minValue="1" inputSize="3"/>
                                        <h:outputText styleClass="text" value="Vencidas de: " />
                                        <h:panelGroup layout="block">
                                            <rich:inputNumberSpinner style="float: left;"  value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diasParcelasVencidasInicial}" minValue="0" maxValue="365" inputSize="4"/>
                                            <h:outputText styleClass="text" value=" à " style="float: left; margin-left: 6px; margin-right: 6px;" />
                                            <div style="display: inline;" title="Caso não seja informado o período final, sera considerado as parcelas vencidas com quantidade de dia maiores que o período inicial">
                                                <rich:inputNumberSpinner style="float: left;"  value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diasParcelasVencidasFinal}" minValue="0" inputSize="4" maxValue="365" />
                                            </div>
                                            <h:outputText style="margin-left: 6px" styleClass="text" value=" dias" />
                                        </h:panelGroup>

                                    </h:panelGrid>

                                    <h:panelGrid columns="2"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasRecorrencia}">
                                        <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_Codigo} Retorno: "/>

                                        <h:inputText
                                                value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.codigoErroRemessa}"
                                                size="5" maxlength="5"/>

                                        <h:panelGroup layout="block" style="text-align: right">
                                            <h:outputText styleClass="text"
                                                          value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.labelDatas} >= "/>
                                        </h:panelGroup>
                                        <h:panelGroup layout="block" style="display: inline-flex;">
                                            <rich:inputNumberSpinner
                                                    id="diasRemessa"
                                                    value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diasRemessa}"
                                                    inputSize="3"/>
                                            <h:outputText styleClass="text"
                                                          style="margin-left: 5px"
                                                          value="dias"/>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <!--DATAS-->

                                    <h:panelGrid columns="1">

                                        <h:panelGroup rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.produtosVencidos || MalaDiretaControle.malaDiretaVO.cfgEvento.produtosVencendo
                                         && MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia != 0 || MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo}">
                                            <h:outputText styleClass="text" value="Produto: "
                                                          rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo}"
                                            />
                                            <h:inputText id="itemProdutos" size="30"
                                                         onblur="blurinput(this);" styleClass="form"
                                                         rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo}"
                                                         value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.produtoVO.descricao}"
                                                         onkeydown="bloquearCtrlJ()">
                                            </h:inputText>
                                            <rich:suggestionbox height="200" width="200"
                                                                for="itemProdutos"
                                                                rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo}"
                                                                suggestionAction="#{MalaDiretaControle.executarAutocompleteConsultaProduto}"
                                                                minChars="1" rowClasses="20"
                                                                status="statusHora" immediate="true"
                                                                nothingLabel="Nenhum Produto encontrado !"
                                                                var="result" id="suggestionNomeServicos">

                                                <a4j:support event="onselect" focus="itemProdutos" reRender="formF"
                                                             action="#{MalaDiretaControle.selecionarServicoSuggestionBox}"/>
                                                <h:column>
                                                    <h:outputText value="#{result.descricao}" title="#{result.observacao}"/>
                                                </h:column>
                                            </rich:suggestionbox>
                                            <h:panelGrid columns="1" styleClass="tabFormSubordinada">
                                                <rich:dataTable
                                                        value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.listaProdutosVOs}"
                                                        rendered="#{!empty MalaDiretaControle.malaDiretaVO.cfgEvento.listaProdutosVOs}"
                                                        var="produto" width="100%" id="itensProdutos"
                                                        headerClass="subordinado"
                                                        styleClass="tabFormSubordinada"
                                                        style="position:relative; top:0px; left:50px;"
                                                        rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento">

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="#{msg_aplic.prt_New_Mala_Direta_Form_Codigo}"/>
                                                        </f:facet>
                                                        <h:outputText value="#{produto.codigo}"/>
                                                    </rich:column>

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="#{msg_aplic.prt_New_Mala_Direta_Form_descricao}"/>
                                                        </f:facet>
                                                        <h:outputText value="#{produto.descricao}"/>
                                                    </rich:column>

                                                    <rich:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Excluir"/>
                                                        </f:facet>
                                                        <a4j:commandButton id="removerProduto"
                                                                           action="#{MalaDiretaControle.removerProdutoVencidos}"
                                                                           reRender="formF"
                                                                           value="#{msg_bt.btn_excluir}"
                                                                           image="./imagens/botaoRemover.png"
                                                                           focus="#{MalaDiretaControle.irPara}"
                                                                           styleClass="botoes"/>
                                                    </rich:column>

                                                </rich:dataTable>
                                            </h:panelGrid>


                                        </h:panelGroup>

                                        <h:panelGrid columns="3" rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.d && !MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasRecorrencia}">
                                            <h:outputText styleClass="text"
                                                          value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.labelDatas} há "
                                                          rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDmais}"/>
                                            <rich:inputNumberSpinner
                                                    id="aprDmais"
                                                    value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diaMais}"
                                                    rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDmais}"
                                                    inputSize="5"
                                                    minValue="0"
                                                    maxValue="99999"/>
                                            <h:outputText styleClass="text" value=" dias"
                                                          rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDmais}"/>


                                            <h:outputText styleClass="text"
                                                          value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.labelDatas} daqui a "
                                                          rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDMenos}"/>
                                            <rich:inputNumberSpinner
                                                    id="aprDMenos"
                                                    value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.diaMenos}"
                                                    rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDMenos}"
                                                    inputSize="5"
                                                    minValue="0"
                                                    maxValue="99999"/>
                                            <h:outputText styleClass="text" value=" dias"
                                                          rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.aprDMenos}"/>
                                        </h:panelGrid>


                                        <h:panelGroup
                                                rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.apresentarDatas}">
                                            <h:outputText styleClass="text"
                                                          style="padding-right: 6px"
                                                          value="*#{MalaDiretaControle.malaDiretaVO.cfgEvento.labelDatas} entre:"/>
                                            <rich:calendar id="dataInicioC"
                                                           value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.inicio}"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           datePattern="dd/MM/yyyy"
                                                           oninputchange="return validar_Data(this.id);"
                                                           enableManualInput="true"
                                                           zindex="2"
                                                           showWeeksBar="false"/>
                                            <h:outputText styleClass="text"
                                                          style="position:relative; top:0px; left:10px; padding-right: 6px"
                                                          value="e"/>
                                            <rich:spacer width="12px"/>
                                            <rich:calendar id="dataTerminoC"
                                                           value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.fim}"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true" showWeeksBar="false"
                                                           zindex="2"/>
                                            <rich:spacer width="5px"/>
                                            <a4j:commandButton id="limparPeriodo"
                                                               onclick="document.getElementById('formF:dataInicioCInputDate').value='';document.getElementById('formF:dataTerminoCInputDate').value='';"
                                                               image="/images/limpar.gif" title="Limpar período."
                                                               status="false"/>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                    <!--PARCELAS VENCENDO-->
                                    <h:panelGroup layout="block" id="pnlParcelasVencendo"
                                                  rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.parcelasVecendo && MalaDiretaControle.malaDiretaVO.email}">
                                        <h:selectBooleanCheckbox id="chkBoletoParcelasVencendo" styleClass="tooltipster"
                                                                 title="Essa configuração é utilizada para enviar boletos para o alunos, porém para que ela funcione <br> é necessário que na tela anterior tenha sido configurado a TAG_BOLETO."
                                                                 value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.boletoParcelasVencendo}">
                                            <a4j:support event="onclick" reRender="panelTags,pnlParcelasVencendo"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText id="txtBoleto" styleClass="text tooltipster"
                                                      title="Essa configuração é utilizada para enviar boletos para o alunos, porém para que ela funcione <br> é necessário que na tela anterior tenha sido configurado a TAG_BOLETO."
                                                      value="Pagamentos por convênio (Boleto)"/>

                                        <c:if test="${MalaDiretaControle.malaDiretaVO.cfgEvento.boletoParcelasVencendo}">
                                            <br/>
                                            <h:selectBooleanCheckbox id="modeloPadraoBoleto" styleClass="tooltipster"
                                                                     title="Ao marcar essa configuração, o email que o aluno irá receber terá um corpo padrão Pacto.<br> Neste email padrão terá a linha digitável e também terá o boleto anexado caso o aluno queira baixar. <br> Agora com a configuração desmarcada, não terá boleto anexado e será enviado somente o link para o aluno fazer o download do boleto."
                                                                     value="#{MalaDiretaControle.malaDiretaVO.cfgEvento.modeloPadraoBoleto}">
                                                <a4j:support event="onclick" reRender="formF:inputMensagem,formF:panelGridMaladireta"/>
                                            </h:selectBooleanCheckbox>
                                            <h:outputText styleClass="text tooltipster"
                                                          title="Ao marcar essa configuração, o email que o aluno irá receber terá um corpo padrão Pacto.<br> Neste email padrão terá a linha digitável e também terá o boleto anexado caso o aluno queira baixar. <br> Agora com a configuração desmarcada, não terá boleto anexado e será enviado somente o link para o aluno fazer o download do boleto."
                                                          value="Utilizar modelo padrão de email para boleto"/>
                                        </c:if>
                                    </h:panelGroup>

                                    <%--INFORMACOES CARTÕES VENCENDO--%>
                                    <h:panelGrid columns="1" rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.cartoesVencendo}">
                                        <h:outputText styleClass="text" style="font-style: italic;"
                                                      value="Atenção: Nas datas independente do dia selecionado será considerado o mês completo"
                                                      rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia == 1}" />
                                        <h:outputText styleClass="text" style="font-style: italic;"
                                                      value="Atenção: Este filtro irá apresentar a cada mês os alunos do mês subsequente"
                                                      rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.ocorrencia == 4}" />
                                    </h:panelGrid>

                                    <%-- CARTÕES VENCIDOS--%>
                                    <h:panelGroup
                                            rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.cartoesVencidos}">
                                        <h:panelGrid columns="1">
                                            <h:outputText styleClass="text" style="font-style: italic" value="Considera os cartões vencidos dos alunos, se o cartão do aluno vence este mês, não será considerado aqui, somente os vencidos. Caso queira filtrar uma situação específica do aluno, utilize o filtro de 'Situação' aqui do mailing"/>
                                        </h:panelGrid>
                                    </h:panelGroup>

                                    <%--INFORMACOES PGTOS. NÃO APROVADOS DE REMESSAS--%>
                                    <h:panelGroup layout="block" rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.itensNaoAprovados}">
                                        <h:outputText styleClass="texto-cor-vermelho texto-bold texto-size-16" style="font-style: italic;"
                                                      value="Atenção:"/>
                                        <h:outputText styleClass="texto-cor-vermelho texto-size-16" style="padding-left: 5px"
                                                      value="Esse filtro é compatível somente para remessas da CIELO."/>
                                    </h:panelGroup>

                                </rich:panel>
                            </h:panelGrid>

                        </h:panelGrid>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h:panelGrid columnClasses="colunaEsquerda" columns="1" width="100%" styleClass="crm"
                                     rendered="#{MalaDiretaControle.exibirFiltros}">
                            <h:panelGroup>
                                <a4j:commandLink value="Retrair tudo" action="#{MalaDiretaControle.fecharTodos}"
                                                 reRender="formF" status="false"/>
                                <rich:spacer width="10"/>
                                <a4j:commandLink value="Limpar todos os filtros" action="#{MalaDiretaControle.limparTodos}"
                                                 reRender="formF"/>
                            </h:panelGroup>

                            <%-- -----------CATEGORIA------------- --%>
                            <rich:simpleTogglePanel id="categoria" width="100%" switchType="client" label="Categoria"
                                                    opened="#{MalaDiretaControle.categoria}"
                                                    headerClass="headerSanfona">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Categoria"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <rich:dataGrid id="dtgCategorias" value="#{MalaDiretaControle.categoriaVOs}"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0"
                                               cellspacing="0" var="categoria">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctCategoria" value="#{categoria.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3" value="#{categoria.nome}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>
                            </rich:simpleTogglePanel>
                            <%-- -----------COLABORADORES------------- --%>
                            <rich:simpleTogglePanel id="colaboradores" width="100%" headerClass="headerSanfona"
                                                    switchType="client" label="Colaboradores"
                                                    opened="#{MalaDiretaControle.colaboradores}">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Colaboradores"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <h:panelGroup layout="block">
                                    <a4j:commandButton styleClass="botoes" image="./imagensCRM/botaoAdicionarGrupos.png"
                                                       action="#{MalaDiretaControle.toggleConsultores}"
                                                       reRender="colaboradores"/>
                                    <rich:spacer width="5px;"/>
                                    <a4j:commandLink id="lnkConsultores" styleClass="botoes"
                                                     action="#{MalaDiretaControle.toggleConsultores}"
                                                     reRender="colaboradores">
                                        <h:outputText styleClass="tituloCamposAberturaMeta" value="Consultores"/>
                                    </a4j:commandLink>

                                    <rich:dataGrid id="dtgConsultores" value="#{MalaDiretaControle.consultoresVOs}"
                                                   rendered="#{MalaDiretaControle.mostrarConsultores}" width="100%"
                                                   columns="4"
                                                   columnClasses="semBorda" styleClass="semBorda" cellpadding="0"
                                                   cellspacing="0"
                                                   var="consultor">
                                        <h:panelGroup>
                                            <h:selectBooleanCheckbox id="slctSituacao" value="#{consultor.selecionado}"/>
                                            <rich:spacer width="5"/>
                                            <h:outputText styleClass="titulo3" value="#{consultor.pessoa_Apresentar}"/>
                                        </h:panelGroup>
                                    </rich:dataGrid>
                                </h:panelGroup>
                                <br/>
                                <h:panelGroup layout="block">
                                    <a4j:commandButton styleClass="botoes" image="./imagensCRM/botaoAdicionarGrupos.png"
                                                       action="#{MalaDiretaControle.toggleProfessores}"
                                                       reRender="colaboradores"/>
                                    <rich:spacer width="5px;"/>
                                    <a4j:commandLink id="lnkProfessores" action="#{MalaDiretaControle.toggleProfessores}"
                                                     styleClass="botoes" reRender="colaboradores">
                                        <h:outputText styleClass="tituloCamposAberturaMeta" value="Professores"/>
                                    </a4j:commandLink>
                                    <rich:dataGrid id="dtgProfessores" value="#{MalaDiretaControle.professoresVOs}"
                                                   rendered="#{MalaDiretaControle.mostrarProfessores}" width="100%"
                                                   columns="5"
                                                   columnClasses="semBorda" styleClass="semBorda" cellpadding="0"
                                                   cellspacing="0"
                                                   var="professor">
                                        <h:panelGroup>
                                            <h:selectBooleanCheckbox id="slctSituacao" value="#{professor.selecionado}"/>
                                            <rich:spacer width="5"/>
                                            <h:outputText styleClass="titulo3" value="#{professor.pessoa_Apresentar}"/>
                                        </h:panelGroup>
                                    </rich:dataGrid>
                                </h:panelGroup>
                            </rich:simpleTogglePanel>

                            <%-- -----------DADOS CADASTRAIS------------- --%>
                            <rich:simpleTogglePanel headerClass="headerSanfona" id="dadoscadastrais" width="100%"
                                                    switchType="client" label="Dados Cadastrais"
                                                    opened="#{MalaDiretaControle.dadosCadastrais}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Dados Cadastrais"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <h:panelGrid columns="4" columnClasses="colunaEsquerda">
                                    <h:outputText value="Idade: " styleClass="text" style="font-weight: bold"/>

                                    <h:panelGrid columns="4">
                                        <h:outputText value=" #{msg_aplic.prt_New_Mala_Direta_Form_pre_min} " styleClass="text"/>
                                        <rich:inputNumberSpinner
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.idadeMinStr}"/>
                                        <h:outputText value=" #{msg_aplic.prt_New_Mala_Direta_Form_pre_max} " styleClass="text"/>

                                        <rich:inputNumberSpinner
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.idadeMaxStr}" maxValue="150"/>

                                    </h:panelGrid>

                                    <h:outputText value="Sexo biológico:" styleClass="text" style="font-weight: bold"/>
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.masculino}">
                                        </h:selectBooleanCheckbox>
                                        <h:outputText value="M" styleClass="text"/>

                                        <h:selectBooleanCheckbox
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.feminino}">
                                        </h:selectBooleanCheckbox>
                                        <h:outputText value="F" styleClass="text"/>
                                    </h:panelGroup>
                                    <h:outputText value="Data de Cadastro:" styleClass="text" style="font-weight: bold"/>
                                    <h:panelGrid columns="4">

                                        <h:outputText value=" #{msg_aplic.prt_New_Mala_Direta_Form_pre_min} " styleClass="text"/>
                                        <rich:calendar
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.dataCadastroMin}"
                                                inputSize="10"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                datePattern="dd/MM/yyyy"
                                                enableManualInput="true"
                                                zindex="2"
                                                showWeeksBar="false"/>
                                        <h:outputText value=" #{msg_aplic.prt_New_Mala_Direta_Form_pre_max} " styleClass="text"/>
                                        <rich:calendar
                                                value="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.dataCadastroMax}"
                                                inputSize="10"
                                                inputClass="form"
                                                oninputblur="blurinput(this);"
                                                oninputfocus="focusinput(this);"
                                                oninputchange="return validar_Data(this.id);"
                                                datePattern="dd/MM/yyyy"
                                                enableManualInput="true"
                                                zindex="2"
                                                showWeeksBar="false"/>
                                    </h:panelGrid>
                                </h:panelGrid>
                            </rich:simpleTogglePanel>

                            <%-- -----------DADOS PLANOS------------- --%>
                            <rich:simpleTogglePanel headerClass="headerSanfona" id="dadosplanos" width="100%"
                                                    switchType="client" label="Dados Planos"
                                                    opened="#{MalaDiretaControle.dadosPlanos}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Dados de Planos"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <h:outputText value="#{msg_aplic.prt_New_Mala_Direta_Form_recorrencia}" styleClass="text"
                                              style="font-weight: bold"/>
                                <h:selectBooleanCheckbox value="#{MalaDiretaControle.somenteRecorrencia}">
                                    <a4j:support action="#{MalaDiretaControle.marcaDesmarcaRecorrencia}"
                                                 reRender="dtgPlanos" event="onclick"/>
                                </h:selectBooleanCheckbox>
                                <br/>
                                <h:outputText value="Planos:" styleClass="text" style="font-weight: bold"/>
                                <rich:dataGrid id="dtgPlanos" value="#{MalaDiretaControle.planoVOs}"
                                               width="100%" columns="3" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="plano">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctPlano" value="#{plano.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3" value="#{plano.descricao}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>
                                <br/>
                                <h:outputText value="Duração dos planos:" styleClass="text" style="font-weight: bold"/>
                                <rich:dataGrid id="dtgDuracao" value="#{MalaDiretaControle.contratoDuracaoVOs}"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="contratoDuracao">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctModalide" value="#{contratoDuracao.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3" value="#{contratoDuracao.numeroMeses}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>

                            </rich:simpleTogglePanel>
                            <%-- -----------MODALIDADES------------- --%>
                            <rich:simpleTogglePanel headerClass="headerSanfona" id="modalidades" width="100%"
                                                    switchType="client" label="Modalidades"
                                                    opened="#{MalaDiretaControle.modalidades}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Modalidades"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <rich:dataGrid id="dtgModalidades" value="#{MalaDiretaControle.modalidadeVOs}"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="modalidade">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctModalide" value="#{modalidade.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3" value="#{modalidade.nome}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>

                            </rich:simpleTogglePanel>


                            <%-- -----------SITUACAO------------- --%>

                            <rich:simpleTogglePanel headerClass="headerSanfona" id="situacao" width="100%"
                                                    switchType="client" label="#{msg_aplic.prt_New_Mala_Direta_Form_situacao}"
                                                    rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.posVenda}"
                                                    opened="#{MalaDiretaControle.situacao}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="#{msg_aplic.prt_New_Mala_Direta_Form_situacao}"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <rich:dataGrid id="dtgSituacoes" value="#{MalaDiretaControle.situacaoClienteTOs}"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="situacao">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox id="slctSituacao" value="#{situacao.selecionado}"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3"
                                                      value="#{situacao.situacaoClienteEnum.descricao}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>

                            </rich:simpleTogglePanel>

                            <%-- PESQUISA --%>
                            <rich:simpleTogglePanel headerClass="headerSanfona" id="pesquisa" width="100%"
                                                    rendered="#{MalaDiretaControle.apresentarTags}"
                                                    switchType="client" label="Pesquisa"
                                                    opened="#{MalaDiretaControle.pesquisa}">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Pesquisa"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <h:selectOneMenu id="listaDePesquisa" value="#{MalaDiretaControle.malaDiretaVO.questionario}">
                                    <f:selectItems value="#{MalaDiretaControle.listaSelectItemPesquisa}"/>
                                </h:selectOneMenu>

                            </rich:simpleTogglePanel>
                            <%--CRM Extra--%>
                            <rich:simpleTogglePanel id="extra" width="100%" switchType="client" label="Extra"
                                                    rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                    opened="#{MalaDiretaControle.extra}"
                                                    headerClass="headerSanfona">
                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Meta Extra"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <h:panelGrid cellspacing="5px" cellpadding="5px" >
                                    <h:panelGroup id="dataVencimento">
                                        <h:outputText styleClass="text" value="Vencimento do Contrato: "/>
                                        <rich:calendar id="inicioVencimento"
                                                       value="#{MalaDiretaControle.inicioVencimento}"
                                                       inputSize="6"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2"
                                                       showWeeksBar="false" />

                                        <rich:spacer width="5px"/>
                                        <h:outputText styleClass="text" value=" #{msg_aplic.prt_New_Mala_Direta_Form_ate} "/>
                                        <rich:calendar id="fimVencimento"
                                                       value="#{MalaDiretaControle.fimVencimento}"
                                                       inputSize="6"
                                                       inputClass="form"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputchange="return validar_Data(this.id);"
                                                       datePattern="dd/MM/yyyy"
                                                       enableManualInput="true"
                                                       zindex="2"
                                                       showWeeksBar="false" />
                                        <a4j:commandButton  id="limparVencimento" style="margin-left: 5px; vertical-align: middle"
                                                            action="#{MalaDiretaControle.limparVencimento}"
                                                            image="/images/limpar.gif" title="Limpar período de vencimento."
                                                            reRender="inicioVencimento, fimVencimento"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid cellspacing="5px" cellpadding="5px" >
                                    <h:panelGroup id="diasSemComp">
                                        <h:outputText styleClass="text" value="Dias sem Comparecer: "/>
                                        <h:inputText id="inicioDias" size="5"  value="#{MalaDiretaControle.inicioDiasSemAparecer}"/>
                                        <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_ate}: "/>
                                        <h:inputText id="fimDias" size="5" value="#{MalaDiretaControle.fimDiasSemAparecer}"/>
                                    </h:panelGroup>
                                </h:panelGrid>



                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                        <h:panelGrid columnClasses="centralizado" width="100%">
                            <h:panelGroup id="botoes">
                                <a4j:commandButton id="verAmostra"
                                                   rendered="#{MalaDiretaControle.exibirBotaoAmostra}"
                                                   action="#{MalaDiretaControle.consultarClientes}"
                                                   reRender="panelAmostraClientes, mdlMensagemGenerica"
                                                   oncomplete="#{MalaDiretaControle.modalMensagemGenerica}"
                                                   value="Ver amostra de destinatários"
                                                   accesskey="2" styleClass="botoes nvoBt"/>

                                <a4j:commandButton id="verHistorico" rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj && !MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                   oncomplete="Richfaces.showModalPanel('panelHistorico')"
                                                   value="Histórico de execuções"
                                                   accesskey="2" styleClass="botoes nvoBt btSec"/>

                                <%--VER AMOSTRA DA META EXTRA--%>
                                <a4j:commandButton id="verAmostraCRMExtra"
                                                   rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}"
                                                   action="#{MalaDiretaControle.consultarClientes}"
                                                   reRender="panelAmostraClientes, mdlMensagemGenerica,msgsCRMExtra"
                                                   oncomplete="#{MalaDiretaControle.modalMensagemGenerica}#{MalaDiretaControle.mensagemNotificar}"
                                                   value="Ver amostra de clientes"
                                                   accesskey="2" styleClass="botoes nvoBt"/>
                            </h:panelGroup>
                        </h:panelGrid>



                            <%--USUARIOS DA META EXTRA--%>
                        <h:panelGrid id="usuarioMetaExtra" columnClasses="colunaEsquerda" columns="1" width="100%" styleClass="crm" rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}">
                            <rich:simpleTogglePanel headerClass="headerSanfona" width="100%"
                                                    rendered="#{!MalaDiretaControle.malaDiretaVO.metaExtraIndividual}"
                                                    switchType="client" label="#{msg_aplic.prt_New_Mala_Direta_Form_usuarios}"
                                                    opened="true">

                                <f:facet name="header">
                                    <h:panelGroup>
                                        <h:outputText styleClass="nomeCol" value="Usuários Participantes da Meta"/>
                                        <h:outputText styleClass="vinc" value=""/>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="openMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-right"></i>
                                    </h:panelGroup>
                                </f:facet>
                                <f:facet name="closeMarker">
                                    <h:panelGroup>
                                        <i class="fa-icon-chevron-down"></i>
                                    </h:panelGroup>
                                </f:facet>

                                <rich:dataGrid value="#{MalaDiretaControle.listaUsuariosMetaCRMExtra}"
                                               id="caixaDeSelecaoUsuario"
                                               width="100%" columns="4" columnClasses="semBorda" styleClass="semBorda"
                                               cellpadding="0" cellspacing="0" var="usuario">
                                    <h:panelGroup>
                                        <h:selectBooleanCheckbox value="#{usuario.usuarioEscolhido}" id="selecionarUsuarioME"/>
                                        <rich:spacer width="5"/>
                                        <h:outputText styleClass="titulo3"
                                                      value="#{usuario.nome}"/>
                                    </h:panelGroup>
                                </rich:dataGrid>
                            </rich:simpleTogglePanel>
                        </h:panelGrid>

                    </td>
                </tr>

            </table>

            <h:panelGroup id="painelPeriodicidade"
                          rendered="#{((MalaDiretaControle.usarAgendamento || !MalaDiretaControle.eventoEscolhido) && !MalaDiretaControle.malaDiretaVO.contatoDireto) && !MalaDiretaControle.malaDiretaVO.crmExtra && !MalaDiretaControle.contatoInstantaneo}">
                <!-- Painel Periodicidade -->
                <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" bgcolor="#fff"
                       style="padding: 10px; display: block;">
                    <tr>
                        <td align="left" valign="top" style="padding-bottom: 5px;">
                            <div style="clear: both;" class="text">
                                <h:outputText style="margin-left: 12px;font-weight: bold"
                                              value="Periodicidade" rendered="#{!MalaDiretaControle.eventoEscolhido}"/>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top">
                            <h:panelGroup id="agendamentos">
                                <%-- Aqui inicio Loop agendamentos --%>
                                <h:panelGrid id="idpanelAgendamento" width="100%" columns="3"
                                             columnClasses="colEsquerda, colDireita corrTopo, colDireita corrTopo"
                                             styleClass="panelAgendamento">

                                    <h:panelGroup rendered="#{!MalaDiretaControle.eventoEscolhido}">
                                        <h:selectOneRadio styleClass="text" layout="pageDirection"
                                                          value="#{MalaDiretaControle.ocorrenciaSelecionada}">
                                            <f:selectItems value="#{MalaDiretaControle.listaSelectItemOcorrencia}"/>
                                            <a4j:support event="onchange" action="#{MalaDiretaControle.alterarTipo}"
                                                         reRender="formf, idpanelAgendamento, agendamentos"/>
                                        </h:selectOneRadio>
                                    </h:panelGroup>
                                    <%-- Meses ou Dias da Semana --%>
                                    <h:panelGroup id="grupoAgendamento" rendered="#{MalaDiretaControle.usarAgendamento}">
                                        <h:panelGrid columns="2" columnClasses="colEsquerda" width="100%">
                                            <h:outputText styleClass="text" rendered="#{MalaDiretaControle.mensalmente}"
                                                          value="#{msg_aplic.prt_mailing_agendamento_meses}:"/>
                                            <h:panelGrid columns="1">
                                                <h:panelGroup rendered="#{MalaDiretaControle.mensalmente}">
                                                    <h:selectBooleanCheckbox
                                                            value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosMeses}">
                                                        <a4j:support reRender="idpanelAgendamento" event="onclick"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText styleClass="text"
                                                                  value="#{msg_aplic.prt_mailing_agendamento_todosMeses}"/>
                                                </h:panelGroup>
                                                <rich:dataGrid style="border: none;"
                                                               value="#{MalaDiretaControle.malaDiretaVO.agendamento.mes}"
                                                               var="mes" columns="4"
                                                               rendered="#{MalaDiretaControle.mensalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosMeses}">
                                                    <h:selectBooleanCheckbox value="#{mes.selecionado}"/>
                                                    <h:outputText value="#{mes.label}"/>
                                                </rich:dataGrid>

                                            </h:panelGrid>
                                        </h:panelGrid>

                                        <h:selectBooleanCheckbox rendered="#{MalaDiretaControle.semanalmente}"
                                                                 value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosDiasSemana}">
                                            <a4j:support reRender="idpanelAgendamento" event="onclick"/>
                                        </h:selectBooleanCheckbox>
                                        <h:outputText styleClass="text" rendered="#{MalaDiretaControle.semanalmente}"
                                                      value="#{msg_aplic.prt_mailing_agendamento_todosDiasSemana}"/>
                                        <rich:dataGrid style="border: none;"
                                                       value="#{MalaDiretaControle.malaDiretaVO.agendamento.diasSemana}"
                                                       var="dia" columns="4"
                                                       rendered="#{MalaDiretaControle.semanalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosDiasSemana}">
                                            <h:selectBooleanCheckbox value="#{dia.selecionado}"/>
                                            <h:outputText value="#{dia.label}"/>
                                        </rich:dataGrid>
                                    </h:panelGroup>
                                    <%-- Dias do Mes --%>
                                    <h:panelGroup>
                                        <h:panelGrid columns="2" columnClasses="colEsquerda" width="100%">
                                            <h:outputText styleClass="text" rendered="#{MalaDiretaControle.mensalmente}"
                                                          value="#{msg_aplic.prt_mailing_agendamento_dia}:"/>

                                            <h:panelGrid columns="1">
                                                <h:panelGroup rendered="#{MalaDiretaControle.mensalmente}">
                                                    <h:selectBooleanCheckbox
                                                            value="#{MalaDiretaControle.malaDiretaVO.agendamento.todosDias}">
                                                        <a4j:support reRender="idpanelAgendamento" event="onclick"/>
                                                    </h:selectBooleanCheckbox>
                                                    <h:outputText styleClass="text"
                                                                  value="#{msg_aplic.prt_mailing_agendamento_todosDias}"/>
                                                </h:panelGroup>
                                                <rich:dataGrid style="border: none;"
                                                               value="#{MalaDiretaControle.malaDiretaVO.agendamento.diasMes}"
                                                               var="diaM" columns="8"
                                                               rendered="#{MalaDiretaControle.mensalmente && !MalaDiretaControle.malaDiretaVO.agendamento.todosDias}">
                                                    <h:selectBooleanCheckbox value="#{diaM.selecionado}"/>
                                                    <h:outputText value="#{diaM.label}"/>
                                                </rich:dataGrid>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <%-- AQUI Ok! --%>
                                <h:panelGrid rendered="#{MalaDiretaControle.usarAgendamento}"
                                             columns="4"
                                             columnClasses="classeEsquerda, classeDireita, classeEsquerda, classeDireita"
                                             styleClass="text">
                                    <h:outputText
                                            style="margin-left: 12px;font-weight: bold; text-align: left; display: block; width: 100%;"
                                            value="Periodo"/>
                                    <h:panelGroup/>
                                    <h:panelGroup/>
                                    <h:panelGroup/>
                                    <h:outputText styleClass="text" value="Período de execução:" style="display: block; min-width: 90px;"/>
                                    <h:panelGroup id="periodoExecucaoForm" style="display: block; min-width: 235px;">
                                        <rich:calendar value="#{MalaDiretaControle.malaDiretaVO.dataEnvio}"
                                                       datePattern="dd/MM/yyyy"
                                                       inputSize="8"
                                                       oninputchange="return validar_Data(this.id);"
                                                       enableManualInput="true" zindex="2" showWeeksBar="false"/>

                                        <h:outputText rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                      styleClass="tituloCampos"
                                                      value="#{msg_aplic.prt_New_Mala_Direta_Form_ate}"/>

                                        <rich:calendar value="#{MalaDiretaControle.malaDiretaVO.vigenteAte}"
                                                       datePattern="dd/MM/yyyy"
                                                       rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                       inputSize="8"
                                                       oninputchange="return validar_Data(this.id);"
                                                       enableManualInput="true" zindex="2" showWeeksBar="false"/>
                                        <a4j:commandButton id="limparPeriodoCriacao"
                                                           action="#{MalaDiretaControle.limparPeriodoExecucaoForm}"
                                                           image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                                           reRender="periodoExecucaoForm"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{MalaDiretaControle.usarHorarioEnvio}">
                                        <h:outputText styleClass="text" value="Horário de envio:" style="display: block; min-width: 120px;"/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{MalaDiretaControle.usarHorarioEnvio}">
                                        <h:panelGroup style="display: block; min-width: 235px;">
                                            <rich:inputNumberSpinner id="idhoraInicio" style="float: left"
                                                                     value="#{MalaDiretaControle.malaDiretaVO.agendamento.horaInicio}"
                                                                     rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                                     inputSize="1" maxValue="23" minValue="0"/>
                                            <h:outputText styleClass="text" value="#{msg_aplic.prt_New_Mala_Direta_Form_ate}" style="float: left"/>
                                            <rich:inputNumberSpinner id="idhoraFim" style="float: left"
                                                                     value="#{MalaDiretaControle.malaDiretaVO.agendamento.horaFim}"
                                                                     rendered="#{MalaDiretaControle.malaDiretaVO.usarAgendamento}"
                                                                     inputSize="1" maxValue="23" minValue="0"/>
                                            <h:outputText styleClass="text" value="horas"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </h:panelGroup>
            <!-- Painel Mensagem -->
            <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0"  
                   style="padding: 10px; display: table;">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <h:outputText style="margin-left: 12px;font-weight: bold" value="Mensagem"
                                          rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}"/>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top">
                        <h:panelGrid id="panelGridMaladireta" columns="2" columnClasses="classEsquerda, classDireita" width="100%">
                            <h:panelGrid columns="2" rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}">
                                <c:if test="${!MalaDiretaControle.apresentarModeloEmailPadraoBoleto}">
                                    <h:outputText styleClass="tituloCampos" value="Modelo de Mensagem: "/>
                                    <h:panelGroup>
                                        <h:inputText id="modeloMensagem" size="70" maxlength="100" readonly="true"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     value="#{MalaDiretaControle.malaDiretaVO.modeloMensagem.titulo}"/>
                                        <a4j:commandButton oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"
                                                           reRender="panelModeloMensagem"
                                                           rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo}"
                                                           image="imagensCRM/informacao.gif"
                                                           alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}"/>
                                        <rich:spacer width="5"/>
                                        <a4j:commandButton id="limparModeloMensagem"
                                                           rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo}"
                                                           action="#{MalaDiretaControle.limparCampoModeloMensagem}"
                                                           image="imagensCRM/limpar.gif"
                                                           alt="#{msg_aplic.prt_limparCampo}"
                                                           reRender="formF "/>
                                    </h:panelGroup>
                                </c:if>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_MalaDireta_titulo}"/>
                                <h:panelGroup>
                                    <h:inputText id="titulo" size="#{MalaDiretaControle.malaDiretaVO.sizeTitulo}" maxlength="#{MalaDiretaControle.malaDiretaVO.maxLengthTitulo}"
                                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                                 disabled="#{MalaDiretaControle.malaDiretaVO.contatoDireto}"
                                                 value="#{MalaDiretaControle.malaDiretaVO.titulo}"/>
                                    <rich:toolTip for="titulo" followMouse="true" direction="top-right"
                                                  style="width:300px; height:#{MalaDiretaControle.tamanhoToolTip}; "
                                                  showDelay="200">
                                        <h:outputText styleClass="tituloCampos" escape="false"
                                                      value="#{msg.msg_tip_tituloMail}#{MalaDiretaControle.termosFiscalizados}#{msg.msg_tip_tituloMailPontos}"/>
                                    </rich:toolTip>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Tipo da mensagem:"
                                              rendered="#{MalaDiretaControle.malaDiretaVO.app}"/>
                                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 rendered="#{MalaDiretaControle.malaDiretaVO.app}"
                                                 styleClass="form"
                                                 value="#{MalaDiretaControle.tipoMensagemApp}">
                                    <f:selectItems value="#{MalaDiretaControle.listaSelectTiposPerguntas}"/>
                                    <a4j:support event="onchange" reRender="formF"/>
                                </h:selectOneMenu>


                                <h:outputText styleClass="tituloCampos" value="Opção 1:"
                                              rendered="#{MalaDiretaControle.botoesApp}"/>
                                <h:inputText size="18" maxlength="7"
                                             style="text-transform: uppercase;"
                                             onblur="blurinput(this);"
                                             rendered="#{MalaDiretaControle.botoesApp}"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{MalaDiretaControle.campos.campo1}"/>

                                <h:outputText styleClass="tituloCampos" value="Opção 2:"
                                              rendered="#{MalaDiretaControle.botoesApp}"/>
                                <h:panelGroup rendered="#{MalaDiretaControle.botoesApp}">
                                    <h:inputText size="18" maxlength="7"
                                                 style="text-transform: uppercase;float: left;"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{MalaDiretaControle.campos.campo2}"/>

                                </h:panelGroup>


                                <h:outputText styleClass="tituloCampos labelOp3" value="Opção 3:"
                                              rendered="#{MalaDiretaControle.botoesApp}"/>
                                <h:panelGroup rendered="#{MalaDiretaControle.botoesApp}">
                                    <h:inputText size="18" maxlength="7"
                                                 style="text-transform: uppercase;float: left;"
                                                 onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form labelOp3"
                                                 value="#{MalaDiretaControle.campos.campo3}"/>
                                </h:panelGroup>
                                <h:panelGroup rendered="#{MalaDiretaControle.apresentarTags && !MalaDiretaControle.apresentarModeloEmailPadraoBoleto}">
                                    <h:selectBooleanCheckbox id="enviarIndividualmente" value="#{MalaDiretaControle.enviarEmailIndividualmente}">
                                        <a4j:support
                                                action="#{MalaDiretaControle.acaoSelecionarEnviarIndividualmente}"
                                                event="onclick"
                                                reRender="formF">
                                        </a4j:support>
                                    </h:selectBooleanCheckbox>
                                    <h:outputLabel styleClass="tituloCampos" value="Habilitar Tags para envio personalizado" for="enviarIndividualmente" />
                                </h:panelGroup>

                                <c:if test="${!MalaDiretaControle.apresentarModeloEmailPadraoBoleto}">
                                    <h:panelGroup rendered="#{MalaDiretaControle.apresentarTags}"></h:panelGroup>
                                    <h:outputText
                                            rendered="#{MalaDiretaControle.apresentarTags and MalaDiretaControle.enviarEmailIndividualmente}"
                                            id="labelTags" styleClass="tituloCampos"
                                            value="#{msg_aplic.prt_ModeloMensagem_adicionarTag}"/>
                                    <h:panelGroup layout="block" id="panelTags"
                                                  rendered="#{MalaDiretaControle.apresentarTags and MalaDiretaControle.enviarEmailIndividualmente}">
                                        <a4j:commandButton id="tagNome"
                                                           styleClass="botoes nvoBt btSec" value="Tag Nome"
                                                           title="Tag Nome"
                                                           reRender="textComentario, inputMensagem"
                                                           action="#{MalaDiretaControle.incluirTagNome}"/>
                                        <rich:spacer width="5"/>
                                        <a4j:commandButton id="tagPNome"
                                                           styleClass="botoes nvoBt btSec" value="Tag Primeiro Nome"
                                                           title="Tag Primeiro Nome"
                                                           reRender="textComentario, inputMensagem"
                                                           action="#{MalaDiretaControle.incluirTagPNome}"/>
                                        <rich:spacer width="5"/>
                                        <a4j:commandButton id="tagPesquisa"
                                                           styleClass="botoes nvoBt btSec" value="Tag Pesquisa"
                                                           title="Tag Pesquisa" reRender="textComentario, inputMensagem"
                                                           action="#{MalaDiretaControle.incluirTagPesquisa}"/>
                                        <rich:spacer width="5"/>
                                        <a4j:commandButton id="tagSaldoPontos"
                                                           styleClass="botoes nvoBt btSec" value="Tag Saldo Pontos"
                                                           title="Tag Saldo Pontos"
                                                           rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.saldoPontos}"
                                                           reRender="textComentario, inputMensagem"
                                                           action="#{MalaDiretaControle.incluirTagSaldoPontos}"/>
                                        <rich:spacer width="5"/>
                                        <a4j:commandButton
                                                rendered="#{MalaDiretaControle.email && MalaDiretaControle.apresentarTagBoleto}"
                                                id="tagBoleto"
                                                styleClass="botoes nvoBt btSec" value="Tag Boleto"
                                                title="Enviar todos os boletos não vencidos em anexo"
                                                reRender="textComentario, inputMensagem"
                                                action="#{MalaDiretaControle.incluirTagBoleto}"/>
                                        <a4j:commandButton
                                                rendered="#{MalaDiretaControle.email && MalaDiretaControle.apresentarTagPix}"
                                                id="tagPix"
                                                styleClass="botoes nvoBt btSec" value="Tag Pix"
                                                reRender="textComentario, inputMensagem"
                                                action="#{MalaDiretaControle.incluirTagPix}"/>
                                        <a4j:commandButton rendered="#{MalaDiretaControle.apresentarTagParcelaCobranca}"
                                                           id="tagParcelas"
                                                           styleClass="botoes nvoBt btSec"
                                                           value="Tag Parcelas em Aberto"
                                                           reRender="textComentario, inputMensagem"
                                                           action="#{MalaDiretaControle.incluirTagParcelasCobranca}"/>
                                        <a4j:commandButton id="btnModalTagRemessa"
                                                           rendered="#{MalaDiretaControle.apresentarTagsRemessa}"
                                                           oncomplete="Richfaces.showModalPanel('modalTagRemessa');"
                                                           styleClass="botoes nvoBt btSec" value="Tags Remessa"
                                                           title="Tags Remessa"/>
                                    </h:panelGroup>
                                </c:if>
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGroup rendered="#{MalaDiretaControle.apresentarTags and MalaDiretaControle.enviarEmailIndividualmente}">
                            <div class="mensagensTags">
                                Ao usar tags é gerado um e-mail personalizado para cada destinatário.
                                Dependendo da quantidade de e-mails, seu provedor poderá exceder o limite de envio diário.</div>
                        </h:panelGroup>
                            <%--Comentário SMS--%>
                        <h:panelGrid id="textComentario" columns="1" style="height:25px;" columnClasses="colunaCentralizada"
                                     width="100%"
                                     rendered="#{MalaDiretaControle.malaDiretaVO.sms or MalaDiretaControle.malaDiretaVO.app}">
                            <h:outputLink value="#{SuperControle.urlWikiCRM}Recursos_Extras:Mailing#SMS"
                                          rendered="#{MalaDiretaControle.malaDiretaVO.sms}"
                                          title="Clique e saiba mais: Mailing SMS" target="_blank">
                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                <h:outputText value="Mailing - SMS"/>
                            </h:outputLink>
                            <h:inputTextarea onkeypress="soma(this.value);" onkeyup="soma(this.value);"
                                             style="align:center;" id="comentarioTextArea" cols="100" rows="2"
                                             value="#{MalaDiretaControle.malaDiretaVO.mensagem}"
                                             rendered="#{MalaDiretaControle.malaDiretaVO.sms}"
                                             disabled="#{MalaDiretaControle.malaDiretaVO.contatoDireto}"/>
                            <h:inputTextarea onkeypress="somaAppMala(this.value);" onkeyup="somaAppMala(this.value);"
                                             style="align:center;" id="comentarioTextAreaApp" cols="100" rows="2"
                                             value="#{MalaDiretaControle.malaDiretaVO.mensagem}"
                                             rendered="#{MalaDiretaControle.malaDiretaVO.app}"
                                             disabled="#{MalaDiretaControle.malaDiretaVO.contatoDireto}"/>

                            <rich:toolTip for="comentarioTextArea" followMouse="true"
                                          rendered="#{MalaDiretaControle.toolTipSMS and MalaDiretaControle.malaDiretaVO.sms}"
                                          showDelay="200" direction="top-right"
                                          style="width:300px; height:#{MalaDiretaControle.tamanhoToolTipSMS};">
                                <h:outputText styleClass="tituloCampos" escape="false"
                                              value="#{MalaDiretaControle.termosFiscalizados}"/>
                            </rich:toolTip>

                            <h:panelGroup id="panelTamanhoRestante" style="align:center;" layout="block"
                                          rendered="#{MalaDiretaControle.malaDiretaVO.sms}">
                                <h:outputText styleClass="tituloCampos" escape="false" value="Caracteres Restantes"/>
                                <h:inputText style="align:center;" disabled="true" size="3" id="tamanhoRestante"/>
                            </h:panelGroup>

                            <h:panelGroup id="panelTamanhoRestante1" style="align:center;" layout="block"
                                          rendered="#{MalaDiretaControle.malaDiretaVO.app}">
                                <h:outputText styleClass="tituloCampos" escape="false" value="Caracteres Restantes"/>
                                <h:inputText style="align:center;" disabled="true" size="3" id="tamanhoRestante1"/>
                            </h:panelGroup>
                        </h:panelGrid>

                            <%--Comentário Email--%>
                        <h:panelGrid id="inputMensagem" rendered="#{MalaDiretaControle.malaDiretaVO.email || MalaDiretaControle.malaDiretaVO.ftp}" columns="1"
                                     columnClasses="colunaCentralizada" width="100%">
                            <h:panelGroup layout="block" style="margin: 0 auto"
                                          rendered="#{!MalaDiretaControle.apresentarModeloEmailPadraoBoleto}">
                                <rich:editor configuration="editorpropriedades" viewMode="visual"
                                             theme="advanced" height="500" width="900"
                                             value="#{MalaDiretaControle.malaDiretaVO.mensagem}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="margin: 0 auto"
                                          rendered="#{MalaDiretaControle.apresentarModeloEmailPadraoBoleto}">

                                <div style="padding: 10px 0 10px 0; text-align: left">
                                    <h:outputText style="font-weight: bold;font-family: Arial, Helvetica, sans-serif;font-size: 12px;color: #333;margin-left: 10px;"
                                                  value="Exemplo do modelo do e-mail:"/>
                                </div>

                                <h:outputText value="#{MalaDiretaControle.modeloPadraoBoleto}"
                                              escape="false"/>
                            </h:panelGroup>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>

        </h:panelGroup>

        <h:panelGroup layout="block" style="margin-top: 20px; text-align: center;"
                      rendered="#{!MalaDiretaControle.malaDiretaVO.crmExtra}">
            <a4j:commandButton id="baaaaa"
                               action="#{MalaDiretaControle.alterar}"
                               value="Voltar"
                               styleClass="botoes nvoBt btSec"
                               title="#{msg.msg_novo_dados}" accesskey="1"
                               reRender="mailingPanel"/>
            <a4j:commandButton id="enviar" onclick=";if(!validarRemetente()){return false;}"
                               action="#{MalaDiretaControle.gravar}"
                               reRender="mailingPanel"
                               oncomplete="#{MalaDiretaControle.msgAlert}"
                               rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto && !MalaDiretaControle.contatoInstantaneo}"
                               value="Gravar"
                               title="#{msg.msg_gravar_dados}"
                               accesskey="2" styleClass="botoes nvoBt"/>
            <a4j:commandLink id="visualizarLogMDcrm"
                             rendered="#{MalaDiretaControle.malaDiretaVO.codigo != 0}"
                             immediate="true"
                             action="#{MalaDiretaControle.realizarConsultaLogObjetoSelecionadoTurma}"
                             accesskey="5"
                             style="display: inline-block; padding: 8px 15px;"
                             styleClass="botoes nvoBt btSec"
                             onclick="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                <i style="text-decoration: none" class="fa-icon-list"/>
            </a4j:commandLink>
        </h:panelGroup>

        <%--BOTOES DA META EXTRA--%>
        <h:panelGrid columns="1" width="100%"  columnClasses="colunaCentralizada" rendered="#{MalaDiretaControle.malaDiretaVO.crmExtra}">
            <h:panelGroup layout="block">
                <a4j:commandButton id="novoCRMExtra"
                                   action="#{MalaDiretaControle.novoCRMExtra}"
                                   value="Novo"
                                   title="Criar uma nova Meta Extra"
                                   reRender="formF, mdlMensagemGenerica"
                                   styleClass="botoes nvoBt btSec" accesskey="2"/>

                <a4j:commandButton id="gravarCRMExtra"
                                   action="#{MalaDiretaControle.gravarCRMExtra}"
                                   oncomplete="#{MalaDiretaControle.modalMensagemGenerica}"
                                   value="Salvar"
                                   title="Gravar Meta Extra"
                                   reRender="formF, mdlMensagemGenerica"
                                   styleClass="botoes nvoBt" accesskey="2"/>

                <a4j:commandButton id="excluirCRMExtra"
                                   rendered="#{MalaDiretaControle.permiteExcluirCRMExtra}"
                                   action="#{MalaDiretaControle.confirmarExcluirMetaExtra}"
                                   oncomplete="#{MalaDiretaControle.modalMensagemGenerica}"
                                   value="Excluir"
                                   reRender="formF, mdlMensagemGenerica"
                                   title="Excluir"
                                   styleClass="botoes nvoBt btSec" accesskey="1"/>

                <a4j:commandButton id="voltarCRMExtra"
                                   action="#{MalaDiretaControle.voltarCRMExtra}"
                                   value="#{msg_bt.btn_voltar_lista}"
                                   title="Voltar"
                                   reRender="formF, mdlMensagemGenerica"
                                   styleClass="botoes nvoBt btSec" accesskey="1"/>

                <a4j:commandLink id="visualizarLogCRMMetaExtra"
                                 rendered="#{!MalaDiretaControle.malaDiretaVO.novoObj}"
                                 immediate="true"
                                 action="#{MalaDiretaControle.realizarConsultaLogObjetoSelecionadoTurma}"
                                 accesskey="5"
                                 style="display: inline-block; padding: 8px 15px;"
                                 styleClass="botoes nvoBt btSec"
                                 onclick="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                    <i style="text-decoration: none" class="fa-icon-list"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>

    </h:form>

    <rich:modalPanel id="panelModeloMensagem" autosized="true" shadowOpacity="true" width="550"
                     height="300"
                     onshow="document.getElementById('formModeloMensagem:consultarModeloMensagem').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkModeloMensagem"/>
                <rich:componentControl for="panelModeloMensagem" attachTo="hiperlinkModeloMensagem" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formModeloMensagem">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarModeloMensagem"
                                     value="#{MalaDiretaControle.campoConsultarModeloMensagem}">
                        <f:selectItems value="#{MalaDiretaControle.tipoConsultarComboModeloMensagem}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarModeloMensagem" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{MalaDiretaControle.valorConsultarModeloMensagem}"/>
                    <a4j:commandButton id="btnConsultarModeloMensagem"
                                       reRender="formModeloMensagem:mensagemConsultarModeloMensagem, formModeloMensagem:resultadoConsultaModeloMensagem , formModeloMensagem:scResultadoModeloMensagem , formModeloMensagem"
                                       action="#{MalaDiretaControle.consultarModeloMensagem}" styleClass="botoes"
                                       value="#{msg_bt.btn_consultar}" image="./imagensCRM/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaModeloMensagem" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{MalaDiretaControle.listaConsultarModeloMensagem}" rows="10"
                                var="modeloMensagem">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_titulo}"/>
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.titulo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_ModeloMensagem_tipoMensagem}"/>
                        </f:facet>
                        <h:outputText value="#{modeloMensagem.tipoMensagem_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{MalaDiretaControle.selecionarModeloMensagem}"
                                           focus="modeloMensagem"
                                           reRender="formF, formModeloMensagem"
                                           oncomplete="Richfaces.hideModalPanel('panelModeloMensagem')"
                                           value="#{msg_bt.btn_selecionar}" styleClass="botoes"
                                           image="./imagensCRM/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModeloMensagem:resultadoConsultaModeloMensagem" maxPages="10"
                                   id="scResultadoModeloMensagem"/>
                <h:panelGrid id="mensagemConsultaModeloMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{MalaDiretaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{MalaDiretaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelAmostraClientes" autosized="true" shadowOpacity="true" width="800"
                     height="400">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{MalaDiretaControle.nrTotalAmostra}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAmostraClientes"/>
                <rich:componentControl for="panelAmostraClientes" attachTo="hiperlinkAmostraClientes" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAmostraClientes">
            <rich:panel rendered="#{MalaDiretaControle.apresentarFiltros && MalaDiretaControle.modoDestinatarios && not MalaDiretaControle.malaDiretaVO.importarLista}">
                <h:outputText escape="false" value="#{MalaDiretaControle.filtrosDescricao}"/>
            </rich:panel>

            <%--BOTãO EXCEL--%>
            <a4j:commandButton id="exportarExcelMailing"
                               image="../imagens/btn_excel.png"
                               style="margin-left: 8px; float: right;"
                               actionListener="#{MalaDiretaControle.exportarAmostra}"
                               value="Excel"
                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Mailing', 800,200);#{ExportadorListaControle.msgAlert}"
                               accesskey="2" styleClass="botoes">
                <f:attribute name="tipo" value="xls"/>
                <f:attribute name="atributos"
                             value="matricula=Matrícula,nome=Nome,situacao=Situação,emails=E-mails,telefones=Telefones,nomeEmpresa=Empresa"/>
                <f:attribute name="prefixo" value="Mailing"/>
            </a4j:commandButton>
            <%--BOTÃO PDF--%>
            <a4j:commandButton id="exportarPdfMailing"
                               style="margin-left: 8px; float: right;"
                               image="../imagens/imprimir.png"
                               actionListener="#{MalaDiretaControle.exportarAmostra}"
                               value="PDF"
                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Mailing', 800,200);#{ExportadorListaControle.msgAlert}"
                               accesskey="2" styleClass="botoes">
                <f:attribute name="tipo" value="pdf"/>
                <f:attribute name="atributos"
                             value="matricula=Matrícula,nome=Nome,situacao=Situação,emails=E-mails,telefones=Telefones,nomeEmpresa=Empresa"/>
                <f:attribute name="prefixo" value="Mailing"/>
            </a4j:commandButton>

            <br>
            <br>

            <rich:dataTable value="#{MalaDiretaControle.listaAmostra}" var="amostra" width="100%" id="itensAmostra"
                            rowClasses="linhaPar, linhaImpar" styleClass="semBordaEsquerda bordaEsquerdaTh"
                            columnClasses="centralizado"
                            rows="15">
                <rich:column sortBy="#{amostra.matricula}"
                             rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}">
                    <f:facet name="header">
                        <h:outputText value="Matrícula"/>
                    </f:facet>
                    <h:outputText id="matricula" value="#{amostra.matricula}"/>
                </rich:column>

                <rich:column sortBy="#{amostra.nome}" rendered="#{!MalaDiretaControle.malaDiretaVO.importarLista}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Cliente_label_nome}"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.irParaTelaCliente}"
                                     value="#{amostra.nome}"
                                     id="nome"
                                     rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}"
                                     oncomplete="#{MalaDiretaControle.msgAlert}"/>
                    <h:outputText value="#{amostra.nome}"
                                  rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}"/>
                </rich:column>

                <rich:column sortBy="#{amostra.nome}" rendered="#{MalaDiretaControle.malaDiretaVO.importarLista}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Cliente_label_nome}"/>
                    </f:facet>
                    <h:outputText value="#{amostra.nome}"/>
                </rich:column>

                <rich:column sortBy="#{amostra.situacao}"
                             rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Cliente_label_situacao}"/>
                    </f:facet>
                    <h:outputText id="situacao" value="#{amostra.situacao}"/>
                </rich:column>


                <rich:column sortBy="#{amostra.idade}"
                             rendered="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.mostrarIdade && MalaDiretaControle.modoDestinatarios}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_OrganizadorCarteira_idadeCliente}"/>
                    </f:facet>
                    <h:outputText value="#{amostra.idade}"/>
                </rich:column>

                <rich:column sortBy="#{amostra.dataCadastro}"
                             rendered="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.mostrarDataCadastro && MalaDiretaControle.modoDestinatarios}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Indicacao_dataCadastro}"/>
                    </f:facet>
                    <h:outputText value="#{amostra.dataCadastroApresentar}"/>
                </rich:column>

                <rich:column sortBy="#{amostra.aniversario}"
                             rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.mostrarDataAniversario && MalaDiretaControle.modoDestinatarios}">
                    <f:facet name="header">
                        <h:outputText value="Aniversário"/>
                    </f:facet>
                    <h:outputText value="#{amostra.aniversario}"/>
                </rich:column>

                <rich:column sortBy="#{amostra.risco}"
                             rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.grupoRisco&& MalaDiretaControle.modoDestinatarios}">
                    <f:facet name="header">
                        <h:outputText value="Risco"/>
                    </f:facet>
                    <h:outputText value="#{amostra.risco}"/>
                </rich:column>

                <rich:column sortBy="#{amostra.diasFalta}"
                             rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.faltosos && MalaDiretaControle.modoDestinatarios}">
                    <f:facet name="header">
                        <h:outputText value="Faltas"/>
                    </f:facet>
                    <h:outputText value="#{amostra.diasFalta}"/>
                </rich:column>

                <rich:column rendered="#{MalaDiretaControle.SMS}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Agenda_telefones}"/>
                    </f:facet>
                    <h:outputText value="#{amostra.telefones}" escape="false"/>
                </rich:column>

                <rich:column rendered="#{MalaDiretaControle.email}">
                    <f:facet name="header">
                        <h:outputText value="E-mails"/>
                    </f:facet>
                    <h:outputText value="#{amostra.emails}" escape="false"/>
                </rich:column>

                <rich:column rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.itensNaoAprovados}">
                    <f:facet name="header">
                        <h:outputText value="Cod. Retorno"/>
                    </f:facet>
                    <h:outputText value="#{amostra.codRetorno}" title="#{amostra.descricaoRetorno}"/>
                </rich:column>

                <rich:column rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}">
                    <f:facet name="header">
                        <h:outputText value="Empresa"/>
                    </f:facet>
                    <h:outputText value="#{amostra.nomeEmpresa}"/>
                </rich:column>

                <rich:column rendered="#{MalaDiretaControle.modoNaoEnviados}">
                    <f:facet name="header">
                        <h:outputText value="Erro"/>
                    </f:facet>
                    <h:outputText value="#{amostra.erro}" escape="false"/>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller for="itensAmostra" status="false"/>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelHistorico" autosized="true" shadowOpacity="true" width="700"

                     height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Histórico de envios"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkHistorico"/>
                <rich:componentControl for="panelHistorico" attachTo="hiperlinkHistorico" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formHistorico">
            <rich:dataTable id="itensHistorico" value="#{MalaDiretaControle.malaDiretaVO.historico}"
                            var="historico" width="100%" rows="15"
                            rowClasses="linhaPar, linhaImpar" styleClass="semBordaEsquerda bordaEsquerdaTh"
                            columnClasses="centralizado">

                <rich:column sortBy="#{historico.dataInicio}">
                    <f:facet name="header">
                        <h:outputText value="Início"/>
                    </f:facet>
                    <h:outputText value="#{historico.dataInicio}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </rich:column>

                <rich:column sortBy="#{historico.dataFim}">
                    <f:facet name="header">
                        <h:outputText value="Fim"/>
                    </f:facet>
                    <h:outputText value="#{historico.dataFim}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </rich:column>


                <rich:column sortBy="#{historico.status.descricao}">
                    <f:facet name="header">
                        <h:outputText value="Status"/>
                    </f:facet>
                    <h:outputText value="#{historico.status.descricao}">
                    </h:outputText>
                </rich:column>

                <rich:column rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto}" sortBy="#{historico.nrEnviados}">
                    <f:facet name="header">
                        <h:outputText value="Enviados"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.consultarEnviados}"
                                     reRender="panelAmostraClientes"
                                     rendered="#{historico.nrEnviados > 0}"
                                     oncomplete="#{MalaDiretaControle.msgAlert}">
                        <h:outputText style="font-weight: bold;" value="#{historico.nrEnviados}">
                        </h:outputText>
                    </a4j:commandLink>
                    <h:outputText rendered="#{historico.nrEnviados == 0}" style="font-weight: bold;"
                                  value="#{historico.nrEnviados}">
                    </h:outputText>
                </rich:column>
                <rich:column rendered="#{MalaDiretaControle.malaDiretaVO.contatoDireto}">
                    <f:facet name="header">
                        <h:outputText value="Destinátarios"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.consultarEnviados}"
                                     reRender="panelAmostraClientes"
                                     oncomplete="#{MalaDiretaControle.msgAlert}">
                        <h:outputText style="font-weight: bold;" value="#{MalaDiretaControle.totalClientesAdicionados}">
                        </h:outputText>
                    </a4j:commandLink>
                </rich:column>

                <rich:column rendered="#{!MalaDiretaControle.malaDiretaVO.contatoDireto}" sortBy="#{historico.nrNaoEnviados}">
                    <f:facet name="header">
                        <h:outputText value="Não Enviados"/>
                    </f:facet>

                    <a4j:commandLink action="#{MalaDiretaControle.consultarNaoEnviados}"
                                     reRender="panelAmostraClientes"
                                     rendered="#{historico.nrNaoEnviados > 0}"
                                     oncomplete="#{MalaDiretaControle.msgAlert}">
                        <h:outputText style="font-weight: bold;" value="#{historico.nrNaoEnviados}">
                        </h:outputText>
                    </a4j:commandLink>
                    <h:outputText rendered="#{historico.nrNaoEnviados == 0}" style="font-weight: bold;"
                                  value="#{historico.nrNaoEnviados}">
                    </h:outputText>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Log"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.mostrarLog}"
                                     reRender="panelTextoLivreItem"
                                     oncomplete="Richfaces.showModalPanel('panelTextoLivreItem');">
                        <h:outputText style="font-weight: bold;" value="..." title="LOG">
                        </h:outputText>
                    </a4j:commandLink>
                </rich:column>

                <rich:column rendered="#{MalaDiretaControle.usuarioLogado.administrador}">
                    <f:facet name="header">
                        <h:outputText value="SQL"/>
                    </f:facet>
                    <a4j:commandLink action="#{MalaDiretaControle.mostrarSql}"

                                     reRender="panelTextoLivreItem"
                                     oncomplete="Richfaces.showModalPanel('panelTextoLivreItem');">
                        <h:outputText style="font-weight: bold;" value="..." title="SQL">
                        </h:outputText>
                    </a4j:commandLink>
                </rich:column>

            </rich:dataTable>
            <rich:datascroller align="center"
                               for="itensHistorico" maxPages="100"
                               id="scResultadoHistoricoProdutos"/>

        </a4j:form>

    </rich:modalPanel>

    <rich:modalPanel id="panelTextoLivreItem" autosized="true" shadowOpacity="true" width="420" height="180">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{MalaDiretaControle.labelModal}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTextoLivreItem"/>
                <rich:componentControl for="panelTextoLivreItem" attachTo="hidelinkTextoLivreItem" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:outputText id="textoLivreItem" escape="false" value="#{MalaDiretaControle.logSql}"/>
    </rich:modalPanel>


    <rich:modalPanel id="modalTagRemessa" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Tags Email - Remessa"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideModalTagRemessa" />
                <rich:componentControl for="modalTagRemessa" attachTo="hideModalTagRemessa" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formTagRemessa" ajaxSubmit="true">
            <h:panelGroup layout="block" style="padding-top: 5px; padding-bottom: 10px">
                <h:outputText styleClass="texto-cor-vermelho texto-bold texto-size-16" style="font-style: italic;"
                              value="Atenção:"/>
                <h:outputText styleClass="texto-cor-vermelho texto-size-16" style="padding-left: 5px"
                              value="As tags são compatíveis somente para remessas da CIELO."/>
            </h:panelGroup>
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <rich:dataTable columns="2" width="440px" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" rows="40"
                                columnClasses="colunaAlinhamento" var="tagRemessa"
                                value="#{MalaDiretaControle.listaTagsEmailRemessa}">

                    <rich:column width="170px">
                        <f:facet name="header">
                            <h:outputText value="Tags"/>
                        </f:facet>
                        <h:outputText value="#{tagRemessa.nome}"/>
                    </rich:column>
                    <rich:column width="240px">
                        <f:facet name="header">
                            <h:outputText value="Opções"/>
                        </f:facet>
                        <a4j:commandButton action="#{MalaDiretaControle.inserirTagRemessa}"
                                           reRender="textComentario, inputMensagem"
                                           oncomplete="Richfaces.hideModalPanel('modalTagRemessa');"
                                           image="./imagens/botaoAdicionar.png" value="Adicionar"
                                           styleClass="botoes"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

</h:panelGrid>

<script type="text/javascript">

    function somaAppMala() {
        var limiteAPP = 255;
        var elemento = (document.getElementById('formF:comentarioTextAreaApp'));
        var tamanhoRestante = document.getElementById('formF:tamanhoRestante1');

        var mais_um = eval(elemento.value.length - 1);
        mais_um++;

        if (elemento.value.length > limiteAPP) {
            elemento.value = '';
            elemento.value = valor_limite;
        }
        else {
            valor_limite = elemento.value;
            tamanhoRestante.value = '';
            tamanhoRestante.value = (limiteAPP - mais_um);
        }
        elemento.focus();
    }
</script>

<rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />

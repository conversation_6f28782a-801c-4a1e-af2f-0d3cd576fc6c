<style>
    .dataTexto {
        font-family: Arial;
        font-weight: normal;
        font-size: 12px;
        color: grey;
    }

    .qtdAlunos {
        font-family: Arial;
        display: flex;
        text-align: center;
        flex-direction: column;
        align-items: center;
        font-weight: bold;
        font-size: 48px;
    }

    .alunos {
        padding-bottom: 20px;
        font-family: Arial;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-weight: bold;
        font-size: 12px;
        color: grey;
        /*line-height: 24px;*/
    }

    .listaAlunos {
        /*font-family: Arial;*/
        /*font-size: 14px;*/
        /*font-weight: bold;*/
        padding-right: 15px;
        padding-left: 10px;
        color: #51555A;
    }

    .rich-inslider-track-decor-1 {
        border: none;
    }

    .rich-inslider-track-decor-2 {
        background-image: linear-gradient(to right, #1998FC, #F0F0F0);
        height: 12px;
        border-radius: 8px;
        border: none;
    }

    .rich-inslider-track {
        background-image: none !important;
    }

    .rich-inslider-handler {
        top: -26px;
        background-image: url("../images/pin.png");
        background-repeat: no-repeat;
        background-position: center;
        background-position-y: 2px;
        background-size: contain;
        height: 47px;
        width: 24px;
        margin: 0px;
        /*-webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);*/
        /*-moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);*/
        /*box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);*/
    }

    .rich-inslider-handler-selected {
        top: -28px;
        background-image: url("../images/pin.png") !important;
        background-repeat: no-repeat;
        background-position: center;
        background-position-y: 2px;
        background-position-y: 2px;
        background-size: contain;
        height: 47px;
        width: 24px;
        margin: 0px;
        padding: 0px;
    }

    .rich-inslider-left-num {
        border: none;
        color: white;
    }

    .rich-inslider-right-num {
        border: none;
        color: white;
    }

    .rich-inslider-size {
        border: none;
        background-color: white;
    }

    .txtListaAlunos {
        height: 40px;
        border-color: #D1D4DC !important;
        border-radius: 4px !important;
        border-style: solid !important;
        width: 400px;
        margin-right: 5px;
        color: #777777 !important;
        font-family: Arial !important;
        font-size: 16px !important;
        padding: 5px;
    }

    .colunaChurnRate {
        text-align: center !important;
    }

    .paginacao {
        display: inline-flex;
    }

    /*-----------------------------------------------------Placeholder-----------------------------------------------*/
    ::-webkit-input-placeholder { /* Edge */
        color: #B4B7BB;
    }

    :-ms-input-placeholder { /* Internet Explorer 10-11 */
        color: #B4B7BB;
    }

    ::placeholder {
        color: #B4B7BB;
    }

    /*buscar e adicionar a essa classe as configuracoes do datascroller pacto_flat.css*/
    /*-----------------------------------------------------Inicio Scroll Custom-----------------------------------------------*/
    .scrollPureCustom {
        margin: 10px 0px 10px 0px;
        border: none !important;
        border: 1px solid #D1D4DC !important;
        border-radius: 4px !important;
    }

    .scrollPureCustom .rich-datascr-button {
        font-size: 0;
        width: 27px;
        height: 26px;
        border: none !important;
        border-left: 1px solid #D1D4DC !important;
        border-right: 1px solid #D1D4DC !important;
        border-radius: 0px !important;
        background-color: white !important;
    }

    .scrollPureCustom .rich-dtascroller-table tbody tr td:not(.rich-datascr-button) {
        border: none !important;
        border-left: 1px solid #D1D4DC !important;
        border-right: 1px solid #D1D4DC !important;
        border-radius: 0px !important;
        background-color: white !important;
        font-size: 14px;
        color: grey !important;
        min-width: 30px;
        height: 26px;
    }

    .scrollPureCustom .rich-datascr-inact {
        border: none !important;
    }

    .scrollPureCustom .rich-dtascroller-table tbody tr td.rich-datascr-act {
        font-size: 14px;
        color: blue !important;
    }

    .scrollPureCustom .rich-dtascroller-table {
        border-style: none;
        background-color: transparent;
        border-spacing: 0;
        border: none !important;
    }

    .scrollPureCustom .rich-dtascroller-table tbody tr td:hover {
        text-decoration: none;
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#1a000000', GradientType=0);
        background-image: -webkit-gradient(linear, 0 0, 0 100%, from(transparent), color-stop(40%, rgba(0, 0, 0, .05)), to(rgba(0, 0, 0, .1)));
        background-image: -webkit-linear-gradient(transparent, rgba(0, 0, 0, .05) 40%, rgba(0, 0, 0, .1));
        background-image: -moz-linear-gradient(top, rgba(0, 0, 0, .05) 0, rgba(0, 0, 0, .1));
        background-image: -ms-linear-gradient(transparent, rgba(0, 0, 0, .05) 40%, rgba(0, 0, 0, .1));
        background-image: -o-linear-gradient(transparent, rgba(0, 0, 0, .05) 40%, rgba(0, 0, 0, .1));
        background-image: linear-gradient(transparent, rgba(0, 0, 0, .05) 40%, rgba(0, 0, 0, .1));
    }

    .scrollPureCustom .rich-datascr-button:after {
        font-family: FontAwesome;
        font-size: 16px;
        color: #222;
        vertical-align: middle;
    }

    .scrollPureCustom .rich-datascr-button:first-child:after {
        color: grey !important;
        content: "\f100";
    }

    .scrollPureCustom .rich-datascr-button:nth-child(2):after {
        color: grey !important;
        content: "\f104";
    }

    .scrollPureCustom .rich-datascr-button:nth-last-child(3), .scrollPureCustom .rich-datascr-button:nth-child(3) {
        color: grey !important;
        display: none;
    }

    .scrollPureCustom .rich-datascr-button:nth-last-child(2):after {
        color: grey !important;
        content: "\f105";
    }

    .scrollPureCustom .rich-datascr-button:last-child:after {
        color: grey !important;
        content: "\f101";
    }
</style>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="/includes/include_import_minifiles.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<head>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <style type="text/css">
        .rich-panel {
            background-color: transparent;
            border: none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-panel-body {
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }
    </style>
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Pacto IA - Churn Rate"/>
    </title>
    <html>
    <h:form id="formProbabilidadeEvasao" prependId="true" styleClass="pure-form no-ext-css">
        <c:set var="titulo" scope="session" value="Pacto IA - Churn Rate"/>
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlWiki}Relat\F3rios:IA_-_Churn_Rate"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:panelGroup>
            <div style="display: block;margin-top: 10px;margin-right: 3%">
                <a4j:commandLink id="btnExcel"
                                 style="margin-left: 8px;float: right"
                                 actionListener="#{RelControleProbEvas.exportar}"
                                 rendered="#{fn:length(RelControleProbEvas.listProbabilidadeEvasaoVO) > 0}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="1" styleClass="linkPadrao">

                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="nome=Nome,apresentarChurnRate=Probabilidade de Evas\E3o,apresentarDataEnvio=Status de Envio"/>
                    <f:attribute name="prefixo" value="Probabilidade de Evas\E3o"/>

                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>

                <a4j:commandLink id="btnPdf"
                                 style="margin-left: 8px;float: right"
                                 actionListener="#{RelControleProbEvas.exportar}"
                                 rendered="#{fn:length(RelControleProbEvas.listProbabilidadeEvasaoVO) > 0}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="2" styleClass="linkPadrao">

                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                 value="nome=Nome,apresentarChurnRate=Probabilidade de Evas\E3o,apresentarDataEnvio=Status de Envio"/>
                    <f:attribute name="prefixo" value="Pacto IA - Probabilidade de Evas\E3o"/>

                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>
            </div>
        </h:panelGroup>
        <a4j:outputPanel>
            <h:panelGrid style="margin-bottom: 12px;margin-left: 22px;width:96%">
                <h:panelGroup styleClass="qtdAlunos" id="qtdAlunos">
                    <h:outputLabel value="#{RelControleProbEvas.getQtdAlunos()}"/>
                </h:panelGroup>
                <h:outputLabel styleClass="alunos" value="alunos"/>
                <rich:inputNumberSlider minValue="0" maxValue="90" width="100%" showInput="false" step="10"
                                        showToolTip="false"
                                        enableManualInput="false"
                                        value="#{RelControleProbEvas.valorSlider}">
                    <a4j:support event="onchange" action="#{RelControleProbEvas.consultarAlunosEQtdAlunos}"
                                 oncomplete="fireElementFromAnyParent('btnatualizarchurn')"
                                 reRender="qtdAlunos,pnlTabela,valorSlider"/>
                </rich:inputNumberSlider>
            </h:panelGrid>
            <div style="text-align: center;margin-left: 22px; width: 96%;padding-top: 12px" id="valorSlider">
                <h:outputLabel value="0%" styleClass="texto-cor-cinza texto-font texto-size-16-real"
                               style="margin-left: 3px;float: left"/>
                <h:outputLabel id="valorSlider" value="#{RelControleProbEvas.valorSlider} à 100%"
                               styleClass="texto-font texto-size-18 texto-bold"
                               style="display: inline-flex;margin-left: 20px"/>
                <h:outputLabel value="100%" styleClass="texto-cor-cinza texto-font texto-size-16-real"
                               style="margin-right: 3px;float: right"/>
            </div>
            <div style="width: 100%;margin-left: 22px;padding-bottom: 10px;padding-top: 30px">
                <h:panelGrid columns="3">
                    <h:outputLabel value="Lista de Alunos"
                                   styleClass="listaAlunos texto-cor-cinza texto-font texto-size-16-real"/>
                    <h:inputText styleClass="txtListaAlunos" id="consultaListaAlunos"
                                 value="#{RelControleProbEvas.filtrarNome}"/>
                    <a4j:commandLink id="consultarFiltro"
                                     style="margin: 0 5px; font-size: 20px;text-decoration: none"
                                     action="#{RelControleProbEvas.filtrarAlunos}"
                                     oncomplete="#{RelControleProbEvas.mensagemNotificar}"
                                     reRender="pnlTabela"
                                     title="Filtrar alunos com probabilidade de evas\E3o">
                        <i class="fa-icon-search"></i>
                    </a4j:commandLink>
                </h:panelGrid>
                <h:outputLabel value="Alunos com probabilidade de evasão examinada"
                               styleClass="texto-cor-cinza texto-font texto-size-11"
                               style="padding-left: 10px;"/>
            </div>
            <h:panelGrid columns="1" columnClasses="w50 alinhar,w50 alinhar" width="100%" rowClasses="margin-v-10"
                         styleClass="forcar-v-margin" style="text-align: center" id="pnlTabela">
                <rich:panel rendered="#{fn:length(RelControleProbEvas.listProbabilidadeEvasaoVO) > 0}">
                    <rich:dataTable rowKeyVar="status" id="tblRegistros" width="100%"
                                    styleClass="tabelaDados semZebra"
                                    columnClasses="colunaCentralizada"
                                    value="#{RelControleProbEvas.listProbabilidadeEvasaoVO}"
                                    rows="#{RelControleProbEvas.rowsDataTable}" var="registro">

                        <rich:column width="3%">
                            <f:facet name="header">
                                <h:selectBooleanCheckbox id="selecionarTodosRegistro" style="text-align: center"
                                                         value="#{RelControleProbEvas.marcarTodosRegistros}">
                                    <a4j:support event="onclick"
                                                 action="#{RelControleProbEvas.marcarTodos}"
                                                 reRender="pnlTabela,btnEnviarCrm"/>
                                </h:selectBooleanCheckbox>
                            </f:facet>
                            <h:selectBooleanCheckbox id="selecionarRegistro" style="text-align: center;"
                                                     value="#{registro.registroSelecionado}"
                                                     disabled="#{registro.disabledCheckBox}">
                                <a4j:support event="onclick"
                                             action="#{RelControleProbEvas.checkRegistro}"
                                             reRender="pnlTabela,btnEnviarCrm"/>
                            </h:selectBooleanCheckbox>
                        </rich:column>

                        <rich:column sortBy="#{registro.nome}" width="77%"
                                     style="text-align: left">
                            <f:facet name="header">
                                <h:outputText title="Nome" value="Nome"
                                              styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                                              style="text-transform: capitalize!important;"/>
                            </f:facet>
                            <h:outputText value="#{registro.nome}"
                                          styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
                        </rich:column>

                        <rich:column sortBy="#{registro.chancesair30dias}" width="20%"
                                     headerClass="colunaChurnRate">
                            <f:facet name="header">
                                <h:outputText
                                        title="Probabilidade do aluno deixar de ser cliente da academia em 30 dias"
                                        value="Churn Rate"
                                        styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                        style="text-transform: capitalize!important; text-align: center;"/>
                            </f:facet>
                            <h:outputText value="#{registro.apresentarChurnRate}"
                                          styleClass="texto-font texto-size-14-real texto-cor-cinza texto"/>
                        </rich:column>

                        <rich:column sortBy="#{registro.dataEnvio}" width="20%">
                            <f:facet name="header">
                                <h:outputText
                                        title="Data de envio para meta extra CRM"
                                        value="Status de Envio"
                                        styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                        style="text-transform: capitalize!important;"/>
                            </f:facet>
                            <h:outputText value="#{registro.apresentarDataEnvio}"
                                          styleClass="texto-font texto-size-14-real texto-cor-cinza texto"/>
                        </rich:column>
                    </rich:dataTable>
                    <div style="text-align: center;margin-left: 22px;margin-right: 3%">
                        <hr style="border-color: #e6e6e6;border-width: 1px"/>
                            <%--<h:outputLabel id="totalItens"
                                           styleClass="texto-cor-cinza texto-font texto-size-16-real"
                                           style="margin-top: 10px; margin-left: 3px;float: left"
                                           value=" Total: #{fn:length(RelControleProbEvas.listProbabilidadeEvasaoVO)} #{fn:length(RelControleProbEvas.listProbabilidadeEvasaoVO) > 1 ? 'Itens' : 'Item'}"
                                           rendered="true"/>--%>

                        <h:outputLabel id="qtdAlunosSelecionados"
                                       styleClass="texto-cor-cinza texto-font texto-size-16-real"
                                       style="margin-top: 10px; margin-left: 3px;float: left"
                                       value="#{RelControleProbEvas.qtdAlunosSelecionados}"
                                       rendered="true"/>

                        <rich:datascroller id="paginador" styleClass="scrollPureCustom paginacao" align="center"
                                           for="tblRegistros"
                                           status="false"
                                           maxPages="5"
                                           renderIfSinglePage="false"/>

                        <h:panelGroup styleClass="paginador-itens-pagina" style="float: right;margin-top: 10px">
                            <h:outputText styleClass="texto-cor-cinza texto-font texto-size-16-real"
                                          value="Itens por página "
                                          style="padding-right: 10px"/>

                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu id="qtdItensPorPagina" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);"
                                                 styleClass="texto-cor-cinza texto-font texto-size-16-real"
                                                 style="color:#777777 !important;"
                                                 value="#{RelControleProbEvas.rowsDataTable}">
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{30}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange" reRender="pnlTabela"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>
                    </div>
                </rich:panel>
                <h:panelGroup>
                        <h:panelGroup id="btnEnviarCrm" style="margin-right: 2%;margin-left: 20px;">
                                <h:outputLabel value="#{RelControleProbEvas.msg}" rendered="#{RelControleProbEvas.mostraMsg}"
                                               style="float: left;font-family: Arial"
                                               styleClass="fa-icon-warning-sign texto-cor-vermelho texto-size-20-real"/>
                                <a4j:commandLink id="enviarCrm" disabled="#{!RelControleProbEvas.mostraBtnEnviarCrm}"
                                                 action="#{RelControleProbEvas.enviarCrm}"
                                                 accesskey="2" styleClass="botoes nvoBt"
                                                 style="margin-top: 10px; float: right"
                                                 reRender="formProbabilidadeEvasao"
                                                 oncomplete="#{ExportadorListaControle.msgAlert}">
                                    <i class="fa-icon-envelope"></i>&nbsp;GERAR META EXTRA
                                </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup id="btnIRCrm" style="margin-right: 2%;margin-left: 20px;">
                                <a4j:commandLink action="#{ClienteControle.abrirCRMMetaExtra}"
                                                 rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMeta}"
                                                 id="metaDiaria"
                                                 styleClass="botoes nvoBt"
                                                 style="margin-top: 10px; margin-left: 8px; float: right"
                                                 reRender="superiorPrincipalCRM, colunaCRMEsquerda, colunaCRMCentro, colunaCRMDireita"
                                                 oncomplete="mostrarTodasTelas();adicionarPlaceHolderCRM();">
                                    <i class="fa-icon-envelope"></i> IR PARA CRM
                                </a4j:commandLink>
                        </h:panelGroup>
                </h:panelGroup>

            </h:panelGrid>
        </a4j:outputPanel>
    </h:form>
    <script type="text/javascript" language="javascript">
        document.getElementById("formProbabilidadeEvasao:consultaListaAlunos").setAttribute("placeholder", "Encontre um aluno em específico");
    </script>
    </html>
</f:view>
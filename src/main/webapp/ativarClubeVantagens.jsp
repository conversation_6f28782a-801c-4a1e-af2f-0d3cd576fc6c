<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/css_bi_1.4.css" rel="stylesheet" type="text/css"/>

<%@include file="/includes/imports.jsp" %>
<head>
    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="../css/telaCliente.css" rel="stylesheet" type="text/css"/>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>

    <script src="script/packJQueryPlugins.min.js" type="text/javascript" ></script>
    <link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
</head>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Clube de Vantagens Ativação"/>
    </title>
    <h:form id="form">
        <a4j:keepAlive beanName="AtivarClubeVantagensControle"/>
        <html>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 70%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup id="panelConteudo">


                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup id="test" layout="block" style="width: auto;">
                                        <a4j:commandLink
                                                id="bannerAdquiraVendas"
                                                reRender="modalAtivarClube"
                                                action="#{AtivarClubeVantagensControle.abrilModalAtivacao}"
                                                oncomplete="#{AtivarClubeVantagensControle.msgAlert}">
                                            <img style="position: absolute; height: 100%; width: 100%;"
                                                 src="./imagens/clube-de-vantagens.png"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

        </body>
        </html>
    </h:form>
    <rich:modalPanel id="modalAtivarClube"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="720" height="400">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Ativando Clube de Vantagens"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="closeAtivarClube"/>
                <rich:componentControl for="modalAtivarClube" attachTo="closeAtivarClube" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form ajaxSubmit="true">
            <h:panelGrid>
                <h:panelGroup layout="block" style="margin: 10 20 10 20;">
                    <h:outputText
                            styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                            value="O Clube de Vantagens é um programa de fidelização onde você pode classificar os tipos de ações que os alunos fazem na academia,
                            e a cada ação executada eles recebem pontos que depois podem ser trocados por prêmios!"/>
                    <h:panelGrid style="margin-top: 1vh;" columns="1" width="100%">
                    <h:outputText
                            styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                            value="O aluno vai ganhar prêmios e você vai mantê-los mais engajados e fidelizados com a sua academia."/>
                    </h:panelGrid>

                    <h:panelGrid style="margin-top: 2vh;"
                                 columnClasses="colunaCentralizada" width="100%">
                        <span class="texto-size-14 texto-bold texto-font texto-cor-cinza">
                            Quer saber mais? <a class="texto-size-14-real texto-bold"
                                                href="" onClick="javascript:window.open('https://pactosolucoes.com.br/ajuda/conhecimento/como-trabalhar-com-o-clube-de-vantagens/', 'popup'); return false">Clique aqui</a> e veja o conteúdo na Central de Ajuda.
                        </span>
                    </h:panelGrid>

                    <h:panelGrid style="margin-top: 2vh;"
                                 columnClasses="colunaCentralizada" width="100%">
                        <span class="texto-size-14 texto-bold texto-font texto-cor-cinza">
                            Deseja realizar um curso sobre esta ferramenta? <a class="texto-size-14-real texto-bold"
                                                href="" onClick="javascript:window.open('https://universidade.sistemapacto.com.br/courses/enrolled/1325432/', 'popup'); return false">Clique aqui</a> e acesse o curso na Universidade Pacto.
                        </span>
                    </h:panelGrid>

                    <h:panelGrid style="margin-top: 2vh;"
                                 columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="texto-size-14 texto-bold texto-font texto-cor-verde"
                                      value="Quer ativar agora? É só clicar no botão abaixo!"/>
                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGroup style="position: absolute; width: 100%; bottom: 7%; text-align: center">
                    <a4j:commandLink
                            accesskey="9"
                            value="Ativar"
                            reRender="mensagemAdquiraVendasOnline, panelAutorizacaoFuncionalidade"
                            action="#{AtivarClubeVantagensControle.permisaoAtivar}"
                            oncomplete="#{AtivarClubeVantagensControle.mensagemNotificar};#{AtivarClubeVantagensControle.msgAlert}"
                            styleClass="botoes nvoBt botaoPrimarioGrande"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalFinalizaClubeVantagens"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="816" height="377">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Conclusão ativação clube de vantagens"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
        </f:facet>
        <a4j:form ajaxSubmit="true">
            <h:panelGrid>
                <h:panelGroup layout="block" style="margin: 10 20 10 20;">
                    <h:panelGrid id="panelDescConclusaoClub" style="margin-top: 1vh;" columns="1">
                        <span class="texto-size-14 texto-bold texto-font texto-cor-cinza">
                            Pronto! O Clube de Vantagens já está ativo! Quer saber como aproveitar o máximo dele? <br>
                            <a class="texto-size-14-real texto-bold"
                               href=""
                               onClick="javascript:window.open('https://pactosolucoes.com.br/ajuda/conhecimento/como-trabalhar-com-o-clube-de-vantagens/', 'popup'); return false">Clique aqui</a>
                            para acessar o conteúdo na Central de Ajuda ensinando como configurar essa poderosa ferramenta de fidelização! <br>

                            Deseja realizar um curso sobre esta ferramenta? <a class="texto-size-14-real texto-bold"
                                                                               href=""
                                                                               onClick="javascript:window.open('https://universidade.sistemapacto.com.br/courses/enrolled/1325432/', 'popup'); return false">Clique aqui</a> e acesse o curso na Universidade Pacto.
                            </span>
                        </span>
                    </h:panelGrid>

                    <h:panelGrid id="panelDescConclusaoClub3" style="margin-top: 2vh;" columns="1" columnClasses="colunaCentralizada">
                        <h:outputText
                                styleClass="fa-icon-exclamation-triangle-sign texto-size-14 texto-bold texto-font texto-cor-amarelo"/>
                        <h:outputText
                                styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                                value="Para acessar seu novo recurso, basta clicar em sair para que você seja deslogado, e assim que seu login for realizado novamente, você já consegue acessar o \"Clube de Vantagens\"."/>
                    </h:panelGrid>
                </h:panelGroup>
                <h:panelGroup style="position: absolute; width: 100%; bottom: 6%; text-align: center">
                    <a4j:jsFunction action="#{LogoutControle.updateCookiesLogado}" name="doLogout" oncomplete="document.location.href = '#{LogoutControle.redirectLogout}'" />
                    <a id="btnLogout" href="#" class="textSmallFlat" style="font-size: 21px" onclick="doLogout();">
                        <h:outputText style="vertical-align: middle;" styleClass="fa-icon-remove"/> Sair
                    </a>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>

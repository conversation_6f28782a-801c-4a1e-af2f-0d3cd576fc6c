<%-- 
    Document   : include_modal_local_impressao
    Created on : 01/04/2015, 20:34:20
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/imports.jsp"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>



<rich:modalPanel id="modalLocalImpressao" width="400" height="120" showWhenRendered="false" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Informe o Local de ImpressÒo"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="lnkHideModalModalLocalImpressao"/>
            <rich:componentControl for="modalLocalImpressao" attachTo="lnkHideModalModalLocalImpressao" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formModalLocalImpressao">
		<h:panelGrid id="panelLocalImpressao" columns="1" width="100%" rowClasses="linhaPar, linhaImpar" >
		
              <h:panelGroup>
                    <h:outputText rendered="#{LocalImpressaoControle.usuarioLogado.administrador}"  style="vertical-align:middle" styleClass="tituloCampos" value="Empresa:" />
                    <h:panelGroup rendered="#{LocalImpressaoControle.usuarioLogado.administrador}">
                        <h:selectOneMenu  id="comboEmpresa" onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          styleClass="form"
                                          value="#{LocalImpressaoControle.codigoEmpresa}" >
                            <f:selectItems  value="#{LocalImpressaoControle.listaEmpresas}" />
                             <a4j:support event="onchange" action="#{LocalImpressaoControle.montarComboLocalImpressao}" reRender="comboLocalImpressao"/>                            
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_comboEmpresa" action="#{LocalImpressaoControle.montarListaEmpresas}" style="vertical-align:middle" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="formModalLocalImpressao:comboEmpresa"/>
                    </h:panelGroup>
              </h:panelGroup>
		
		
              <h:panelGroup>                   
                 <h:outputText styleClass="tituloCampos" style="vertical-align:middle" value="Local ImpressÒo:" />

                  <h:selectOneMenu  id="comboLocalImpressao" onblur="blurinput(this);"
                                    onfocus="focusinput(this);"
                                    styleClass="form"
                                    value="#{LocalImpressaoControle.nomeComputadorImpressao}" >
                      <f:selectItems  value="#{LocalImpressaoControle.listaLocalImpressao}" />
                  </h:selectOneMenu>
                  <a4j:commandButton id="atualizar_comboLocalImpressao" style="vertical-align:middle" action="#{LocalImpressaoControle.montarComboLocalImpressao}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="formModalLocalImpressao:comboLocalImpressao"/>
              </h:panelGroup>
                   
                   
		</h:panelGrid>
		
		<h:panelGrid id="panelMensagem" columns="1" width="100%"
			styleClass="tabMensagens">
			<h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
				<h:panelGrid columns="1" width="100%">
		
					<h:outputText value=" " />
		
				</h:panelGrid>
				<h:commandButton rendered="#{LocalImpressaoControle.sucesso}"
					image="/imagens/sucesso.png" />
				<h:commandButton rendered="#{LocalImpressaoControle.erro}"
					image="/imagens/erro.png" />
				<h:panelGrid columns="1" width="100%">
					<h:outputText styleClass="mensagem"
						value="#{LocalImpressaoControle.mensagem}" />
						
		        <h:panelGroup>				
					<h:outputText styleClass="mensagemDetalhada"
						value="#{LocalImpressaoControle.mensagemDetalhada}" />
		         </h:panelGroup>            
						
				</h:panelGrid>
			</h:panelGrid>
		</h:panelGrid>		
		
					
		<h:panelGrid id="panelBotoes" style="padding-top:15px;" columns="1" width="100%"  >
		     <h:panelGroup >
			     <h:panelGroup styleClass="pure-form"
			                   style="padding-left:15px; font-size:80%">
				     <a4j:commandLink id="btnGravar"
				                      styleClass="pure-button pure-button-primary"
				                      action="#{LocalImpressaoControle.gravarLocalAcessoNaSessao}"
				                      oncomplete="#{LocalImpressaoControle.msgAlert}"
				                      reRender="panelMensagem, pgMenuLateralLocalImpressao"
		                              title="Gravar"		                      
				                      accesskey="1"> 
				          <i class="fa-icon-check" ></i> Gravar
				     </a4j:commandLink>
			     </h:panelGroup>
		    </h:panelGroup>		
		</h:panelGrid>
		

    </a4j:form>
</rich:modalPanel>


<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_Objecao_tituloForm}" /></title>


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Objecao_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-objecoes-no-crm/"/>

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topoReduzido_material_crm.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="form">
            <h:commandLink action="#{ObjecaoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Objecao_tituloForm}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-cadastrar-objecoes-no-crm/"
                                      title="Clique e saiba mais: Objeção" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" styleClass="tabForm" width="100%">

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Objecao_codigo}" />
                    <h:inputText id="codigo" size="10" disabled="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{ObjecaoControle.objecaoVO.codigo}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Objecao_descricao}" />
                    <h:inputText id="descricao" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{ObjecaoControle.objecaoVO.descricao}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Objecao_grupo}" />
                    <h:inputText id="grupo" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{ObjecaoControle.objecaoVO.grupo}" />
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Objecao_ativo}" />
                    <h:selectBooleanCheckbox id="checkboxObjecao" value="#{ObjecaoControle.objecaoVO.ativo}"/>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Objecao_tipoGrupo}" />
                    <h:selectOneMenu id="opcoesTipoGrupo" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" style="border: none;" value="#{ObjecaoControle.objecaoVO.tipoGrupo}">
                        <f:selectItems value="#{ObjecaoControle.listaSelectItemTipoGrupo}" />
                    </h:selectOneMenu>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Objecao_comentario}" />
                    <h:inputTextarea id="comentario" rows="5" cols="100" onblur="blurinput(this);" onfocus="focusinput(this);" value="#{ObjecaoControle.objecaoVO.comentario}" />

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{ObjecaoControle.sucesso}" image="./imagensCRM/sucesso.png"/>
                        <h:commandButton rendered="#{ObjecaoControle.erro}" image="./imagensCRM/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgObjecao" styleClass="mensagem"  value="#{ObjecaoControle.mensagem}"/>
                            <h:outputText  id="msgObjecaoDet" styleClass="mensagemDetalhada" value="#{ObjecaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{ObjecaoControle.novo}" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec" />
                            <rich:spacer width="10" />
                            <a4j:commandButton id="salvar" action="#{ObjecaoControle.gravar}" value="#{msg_bt.btn_gravar}" reRender="form,panelGridMensagens" oncomplete="#{ObjecaoControle.msgAlert}"  title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt" />
                            <rich:spacer width="10" />

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{ObjecaoControle.msgAlert}" action="#{ObjecaoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <rich:spacer width="10" />
                            <h:commandButton id="consultar" immediate="true" action="#{ObjecaoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec" />
                            <a4j:commandLink action="#{ObjecaoControle.realizarConsultaLogs}" reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);" title="Visualizar Log" style="padding: 6px 12px;position: relative;top: 10px;left: 9px;" styleClass="pure-button">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>


    <rich:modalPanel id="modalObjecaoDesistencia"
                     showWhenRendered="#{ObjecaoControle.objecaoDesistencia}"
                     width="400" styleClass="novaModal"
                     domElementAttachment="parent" autosized="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Objeção de Desistência"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formObjecaoDesistencia">
            <h:panelGrid columns="1" width="100%"
                         rendered="#{!ObjecaoControle.sucessoTransferencia}"
                         footerClass="colunaCentralizada" headerClass="subordinado" styleClass="paginaFontResponsiva">

                <h:panelGroup layout="block" styleClass="paginaFontResponsiva" style="text-align: center; padding-bottom: 10px">
                    <h:outputText styleClass="negrito"
                                  style="color: red; font-size: 1.2em; font-weight: bold;"
                                  value="ATENÇÃO essa é uma OBJEÇÃO DE DESISTÊNCIA!"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="paginaFontResponsiva" style="text-align: justify">
                    <h:outputText styleClass="negrito"
                                  style="color: red; font-size: 1.2em; font-weight: bold;"
                                  value="Objeções do tipo 'desistência' possuem o mesmo comportamento das do tipo 'objeção', devido à isso os cadastros estão sendo unificados. Esse processo visa evitar objeções duplicadas e melhor visualização no novo BI de objeções (Gráfico) no Business Inteligence do CRM."/>
                    <br/>
                    <br/>
                    <h:outputText styleClass="negrito"
                                  style="font-size: 1.2em; font-weight: bold;"
                                  value="Selecione abaixo a objeção que irá receber a transferência:"/>
                    <br/>
                    <br/>
                    <h:selectOneMenu id="objecaoTransferir" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     style="background: #F6F6F6; color: #333333; font-size: 16px;"
                                     value="#{ObjecaoControle.objecaoTransferir}">
                        <f:selectItems value="#{ObjecaoControle.listaObjecao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="container-botoes" style="padding-top: 10px">

                    <a4j:commandLink reRender="formObjecaoDesistencia"
                                     title="Serão transferidos os vinculos já existentes com essa objeção e após isso, esse cadastro será excluído."
                                     action="#{ObjecaoControle.transferirObjecoesExcluir}"
                                     styleClass="botaoPrimario texto-size-14">
                        <h:outputText style="font-size: 14px" value="Transferir e Excluir"/>
                    </a4j:commandLink>

                    <a4j:commandLink style="margin-left: 8px"
                                     title="Voltar para tela de consulta de objeções e nenhuma ação será executada."
                                     styleClass="botaoSecundario texto-size-14"
                                     action="#{ObjecaoControle.inicializarConsultar}"
                                     reRender="formObjecaoDesistencia">
                        <h:outputText style="font-size: 14px" value="Voltar"/>
                    </a4j:commandLink>
                </h:panelGroup>

                <h:panelGrid id="panelMensagemObjecaoDesistencia" columns="3" width="100%" styleClass="tabMensagens" rendered="#{not empty ObjecaoControle.mensagemDetalhada}">
                    <h:commandButton  rendered="#{ObjecaoControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{ObjecaoControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ObjecaoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ObjecaoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%"
                         rendered="#{ObjecaoControle.sucessoTransferencia}"
                         footerClass="colunaCentralizada" headerClass="subordinado" styleClass="paginaFontResponsiva">

                <h:panelGroup layout="block" id="testeLuiz" styleClass="paginaFontResponsiva" style="text-align: center; min-width: 500px;">
                    <a4j:commandButton  rendered="#{ClassificacaoControle.sucesso}" image="./imagens/sucesso.png"/>
                    <a4j:commandButton rendered="#{ConfiguracaoSiClassificacaoControlestemaControle.erro}" image="./imagens/erro.png"/>
                    <h:outputText styleClass="negrito"
                                  style="font-size: 1.2em; font-weight: bold; color: darkgreen;"
                                  value="#{ObjecaoControle.mensagemDetalhada}"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="container-botoes" style="padding-top: 10px">
                    <a4j:commandLink styleClass="botaoSecundario texto-size-14"
                                     action="#{ObjecaoControle.inicializarConsultar}"
                                     reRender="formObjecaoDesistencia">
                        <h:outputText style="font-size: 14px" value="Fechar"/>
                    </a4j:commandLink>
                </h:panelGroup>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

<script>
    document.getElementById("form:descricao").focus();
</script>
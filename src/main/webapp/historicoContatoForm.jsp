<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
body {
	margin: 0px;
	padding: 0px;
}
</style>

<f:view>
	<jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
	<c:set var="titulo" scope="session" value="${msg_aplic.prt_HistoricoContato_tituloForm}"/>
	<c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-historico-de-contatos-crm/"/>
	<c:set var="titleWiki" scope="request" value="Clique e saiba mais: Consulta Histórico Contato"/>
	<h:panelGrid columns="1" styleClass="tabForm" width="100%">

		<h:panelGrid columns="1" style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
			<h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_historicoContato}" />
		</h:panelGrid>
		<f:facet name="controls">
			<h:panelGroup>
				<h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAgendamento" />
				<rich:componentControl for="panelAgendamento" attachTo="hiperlinkAgendamento" operation="hide" event="onclick" />
			</h:panelGroup>
		</f:facet>
		<h:form id="form">
			<h:panelGrid columns="1" styleClass="tabForm" width="100%">

				<h:panelGrid columns="1" width="100%" style="border:1px solid black">

					<h:panelGrid columns="2" width="100%" style="border:1px solid black" columnClasses="colunaEsquerda">
						<h:panelGrid columns="1" width="50px" cellpadding="0" cellspacing="0" columnClasses="colunaEsquerda">
							<a4j:outputPanel id="panelFoto">
								<a4j:mediaOutput element="img" id="imagem1" style="width:60px;height:80px " 
                                                                                 cacheable="false" session="true"
                                                                                 rendered="#{!SuperControle.fotosNaNuvem}" 
                                                                                 createContent="#{HistoricoContatoControle.paintFoto}" 
                                                                                 value="#{ImagemData}" mimeType="image/jpeg">
									<f:param value="#{SuperControle.timeStamp}" name="time" />
                                                                        <f:param name="largura" value="60"/>
                                                                        <f:param name="altura" value="80"/>
								</a4j:mediaOutput>
                                                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                                                                width="60" height="80"                                        
                                                                                style="width:60px;height:80px"
                                                                                url="#{HistoricoContatoControle.paintFotoDaNuvem}"/>
							</a4j:outputPanel>
						</h:panelGrid>
						<h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0" style="text-align: top;" width="100%">
							<h:panelGrid columns="4" columnClasses="colunaEsquerda" width="100%">
								<h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
									<h:panelGroup>
										<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_aluno}" />
									</h:panelGroup>
									<h:panelGroup>
										<h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.nome}" />
									</h:panelGroup>
								</h:panelGrid>
								<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
									<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_idade}" />
									<h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.idade}" />
								</h:panelGrid>
								<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
									<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_estadoCivil}" />
									<h:outputText styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.estadoCivil_Apresentar}" />
									<rich:spacer height="22px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}" />
								</h:panelGrid>
								<h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
									<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
									<h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.dataCadastro_Apresentar}" />
								</h:panelGrid>
							</h:panelGrid>

							<rich:spacer width="30px;" />

							<h:panelGroup>
								<h:panelGrid columns="7" width="100%">

									<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_diasUltAcesso}" />
										<h:outputText styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" value="#{HistoricoContatoControle.historicoContatoVO.diasUltAcesso_Apresentar}" />
										<rich:spacer height="15px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" />
									</h:panelGrid>

									<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_diasCadastrado}" />
										<h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.diasCadastrado}" />
									</h:panelGrid>

									<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_ligacoes}" />
										<h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.totalLigacao}" />
									</h:panelGrid>
									<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdEmail}" />
										<h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdEmail}" />
									</h:panelGrid>
									<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdPessoal}" />
										<h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdPessoal}" />
									</h:panelGrid>
								</h:panelGrid>
							</h:panelGroup>
						</h:panelGrid>
					</h:panelGrid>

					<h:panelGrid id="listaContatos" columns="1" width="100%" columnClasses="colunaCentralizada">
						<rich:dataTable value="#{HistoricoContatoControle.historicoContatoVO.contatoVos}" id="resTelefone" width="100%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" var="contatos">
							<rich:column>
								<f:facet name="header">
									<h:outputText value="#{msg_aplic.prt_Agenda_dataHora}" />
								</f:facet>
								<h:outputText value="#{contatos.dia_Apresentar}" />
							</rich:column>
							<rich:column>
								<f:facet name="header">
									<h:outputText value="#{msg_aplic.prt_Agenda_fase}" />
								</f:facet>
								<h:outputText value="#{contatos.fase}" />
							</rich:column>
							<rich:column>
								<f:facet name="header">
									<h:outputText value="#{msg_aplic.prt_Agenda_resultado}" />
								</f:facet>
								<h:outputText value="#{contatos.resultado}" />
							</rich:column>
							<rich:column>
								<f:facet name="header">
									<h:outputText value="#{msg_aplic.prt_Agenda_usuario}" />
								</f:facet>
								<h:outputText value="#{contatos.responsavelCadastro.colaboradorVO.pessoa.primeiroNomeConcatenado}" />
							</rich:column>
							<rich:column colspan="4" breakBefore="true">
								<h:outputText value="#{contatos.observacao}" escape="false" />

							</rich:column>
						</rich:dataTable>
					</h:panelGrid>

				</h:panelGrid>
			</h:panelGrid>
		</h:form>
	</h:panelGrid>
</f:view>


<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 12/06/2015
  Time: 14:50
  To change this template use File | Settings | File Templates.
--%>

<script src="../script/amcharts.js" type="text/javascript"></script>
<script src="../script/serial.js" type="text/javascript"></script>
<script src="../script/gauge.js" type="text/javascript"></script>
<script>

  var chart;





  //document.getElementById('chartdiv').style.display = "none";
     ${ListasERelatoriosControle.dadosGrafico}


         AmCharts.ready( function() {
        // SERIAL CHART
        chart = new AmCharts.AmSerialChart();
        chart.dataProvider = chartData;
        chart.categoryField = "year";
        chart.startDuration = 1;
        chart.plotAreaBorderColor = "#DADADA";
        chart.plotAreaBorderAlpha = 1;
        // this single line makes the chart a bar chart


        // AXES
        // Category
        var categoryAxis = chart.categoryAxis;
        categoryAxis.gridPosition = "start";
        categoryAxis.gridAlpha = 0.1;
        categoryAxis.axisAlpha = 0;

        // Value
        var valueAxis = new AmCharts.ValueAxis();
        valueAxis.title = "Resumo Sexo Biol�gico / Faixa Et�ria";
        valueAxis.axisAlpha = 0;
        valueAxis.gridAlpha = 0.1;

        chart.addValueAxis(valueAxis);

        // GRAPHS
        // first graph
        var graph1 = new AmCharts.AmGraph();
        graph1.type = "column";
        graph1.title = "Homem";
        graph1.valueField = "homen";
        graph1.balloonText = "Homens [[category]]: [[value]]";
        graph1.lineAlpha = 0;
        graph1.fillColors = "#ADD981";
        graph1.fillAlphas = 1;
        chart.addGraph(graph1);

        // second graph
        var graph2 = new AmCharts.AmGraph();
        graph2.type = "column";
        graph2.title = "Mulher";
        graph2.valueField = "mulher";
        graph2.balloonText = "Mulheres [[category]]: [[value]]";
        graph2.lineAlpha = 0;
        graph2.fillColors = "#81acd9";
        graph2.fillAlphas = 1;
        chart.addGraph(graph2);

        // LEGEND
        var legend = new AmCharts.AmLegend();
        chart.addLegend(legend);


        // WRITE
        chart.write("chartdiv");

      });


</script>

<div id="chartdiv" style="width:800px; height:400px;"/>
<script>

      window.scrollBy(0,+900); // velocidade para baixo
      scrolldelay = setTimeout('0',0); // tempo da rolagem



</script>




<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Convenio_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Convenio_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/> 
                    <h:selectOneMenu styleClass="campos" id="consulta" required="true" value="#{ConvenioControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{ConvenioControle.tipoConsultaCombo}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" styleClass="campos" value="#{ConvenioControle.controleConsulta.valorConsulta}"/>
                    <h:commandButton id="consultar" type="submit" styleClass="botoes" value="#{msg_bt.btn_consultar}" action="#{ConvenioControle.irPaginaInicial}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="2"/>
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{ConvenioControle.apresentarResultadoConsulta}" binding="#{ConvenioControle.apresentarLinha}">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false" binding="#{ConvenioControle.apresentarPrimeiro}" action="#{ConvenioControle.irPaginaInicial}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false" binding="#{ConvenioControle.apresentarAnterior}" action="#{ConvenioControle.irPaginaAnterior}"/> 
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{ConvenioControle.paginaAtualDeTodas}" rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false" binding="#{ConvenioControle.apresentarPosterior}" action="#{ConvenioControle.irPaginaPosterior}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false" binding="#{ConvenioControle.apresentarUltimo}" action="#{ConvenioControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{ConvenioControle.listaConsulta}" rendered="#{ConvenioControle.apresentarResultadoConsulta}" rows="10" var="convenio">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Convenio_codigo}"/>
                        </f:facet>
                        <h:commandLink action="#{ConvenioControle.editar}" id="codigo" value="#{convenio.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Convenio_descricao}"/>
                        </f:facet>
                        <h:commandLink action="#{ConvenioControle.editar}" id="descricao" value="#{convenio.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Convenio_dataAssinatura}"/>
                        </f:facet>
                        <h:commandLink action="#{ConvenioControle.editar}" id="dataAssinatura" value="#{convenio.dataAssinatura_Apresentar}"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:commandButton action="#{ConvenioControle.editar}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ConvenioControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ConvenioControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{ConvenioControle.novo}" value="#{msg_bt.btn_novo}" styleClass="botoes" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>
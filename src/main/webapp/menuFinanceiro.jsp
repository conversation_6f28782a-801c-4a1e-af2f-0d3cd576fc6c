<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>


<!-- inicio box -->

<h:panelGroup layout="block" styleClass="menuLateral">
    <!-- Menu Lateral -->
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-briefcase"></i> Config. Financeiras
        </h:panelGroup>

        <!-- Consulta Receibo -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{msg_menu.Menu_estornoRecibo}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONSULTA_DE_RECIBOS" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-estornar-o-recibo-de-pagamento-de-um-colaborador/"
                          title="Clique e saiba mais: Consulta de Recibos" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Consulta Receibo -->    

        <!-- Consulta de Cupons Fiscais -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.cupomFiscal}">
            <a4j:commandLink  value="Cupons Fiscais"
                              title="#{msg_menu.Menu_cuponsFiscais}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONSULTA_DE_CUPONS_FISCAIS" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Consulta_de_Cupons_Fiscais#Emiss.C3.A3o_do_Cupom_Fiscal"
                          title="Clique e saiba mais: Consulta de Cupons Fiscais" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Consulta de Cupons Fiscais -->

        <!-- Gest�o de notas -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.gestaoNotas}">
            <a4j:commandLink  value="#{msg_menu.Menu_gestaoNotas}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="GESTAO_NOTAS" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-emitir-nota-fiscal-pelo-gestao-de-notas-ou-pelo-gestao-de-nfc-e/"
                          title="Clique e saiba mais: Gest�o de Notas" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>        
        <!-- //Gest�o de notas -->
        
        <!-- Venda de Consumidor -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.vendaConsumidor}">
            <a4j:commandLink  value="#{msg_menu.Menu_vendaConsumidor}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="VENDA_CONSUMIDOR" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-ver-as-vendas-realizadas-para-consumidor/"
                          title="Clique e saiba mais: Venda de Consumidor" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Venda de Consumidor -->
        
        <!-- Movimento de Conta Corrente do Cliente -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.movimentoContaCorrenteCliente}">
            <a4j:commandLink  value="Mov. CC Cliente"
                              title="#{msg_menu.Menu_contaCorrenteCliente}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="MOVIMENTO_CC_CLIENTE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-lancar-credito-ou-debito-na-conta-corrente-do-aluno/"
                          title="Clique e saiba mais: Mov. Conta Corrente do Cliente" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Movimento de Conta Corrente do Cliente -->
        
        <!-- Banco -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.banco}">
            <a4j:commandLink  value="#{msg_menu.Menu_banco}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="BANCO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Banco"
                          title="Clique e saiba mais: Banco" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>        
        <!-- //Banco -->
        
        <!-- Conta Corrente -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.contaCorrente}">
            <a4j:commandLink  value="#{msg_menu.Menu_contaCorrente}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONTA_CORRENTE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Conta_Corrente"
                          title="Clique e saiba mais: Conta Corrente" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>         
        <!-- //Conta Corrente -->
        
        <!-- Tipo Retorno -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.tipoRetorno}">
            <a4j:commandLink  value="#{msg_menu.Menu_tipoRetorno}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="TIPO_RETORNO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-tipo-de-retorno/"
                          title="Clique e saiba mais: Tipo Retorno" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Tipo Retorno -->
        
        <!-- Tipo Remessa -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.tipoRemessa}">
            <a4j:commandLink  value="#{msg_menu.Menu_tipoRemessa}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="TIPO_REMESSA" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-tipo-de-remessa/"
                          title="Clique e saiba mais: Tipo Remessa" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Tipo Remessa -->

        <!-- Conv�nio de Cobran�a -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.convenioCobranca}">
            <a4j:commandLink  value="#{msg_menu.Menu_convenioCobranca}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONVENIO_COBRANCA" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-convenio-de-cobranca-da-stone/"
                          title="Clique e saiba mais: Conv�nio de Cobran�a" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Conv�nio de Cobran�a -->   
        
        <!-- Formas de Pagamento -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.formaPagamento}">
            <a4j:commandLink  value="#{msg_menu.Menu_formaPagamento}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="FORMA_PAGAMENTO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-uma-forma-de-pagamento/"
                          title="Clique e saiba mais: Formas de Pagamento" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Formas de Pagamento --> 
        
        <!-- Operadora de Cart�o -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.operadoraCartao}">
            <a4j:commandLink  value="#{msg_menu.Menu_operadoraCartao}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="OPERADORA_CARTAO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Operadora_de_Cart�o"
                          title="Clique e saiba mais: Operadora de Cart�o" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Operadora de Cart�o -->    
        
        <!-- Metas do Financeiro -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink  value="#{msg_menu.Menu_metasFinanceiro}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="METAS_FINANCEIRO_VENDA" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}bi-metas-financeiras-de-venda-adm/"
                          title="Clique e saiba mais: Metas do Financeiro" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Metas do Financeiro -->    
        
        <!-- Taxas de Comiss�o -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.taxasComissao}">
            <a4j:commandLink  value="#{msg_menu.Menu_taxasComissao}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="TAXA_COMISSAO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-comissao-para-consultor/"
                          title="Clique e saiba mais: Taxas de Comiss�o" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Taxas de Comiss�o -->    
        <!-- INICIO - �ndice Financeiro -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.indiceFinanceiroReajustePreco}">
            <a4j:commandLink  value="#{msg_menu.Menu_indiceFinanceiro}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="INDICE_FINANCEIRO_REAJUSTE_PRECO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Indice_Financeiro"
                          title="Clique e saiba mais: Indice Financeiro" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- FIM - �ndice Financeiro -->

        <!-- Formas de Pagamento -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" >
            <a4j:commandLink  value="#{msg_menu.Menu_adquirente}"
                              styleClass="titulo3 linkFuncionalidade"
                              action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                              actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                              oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="ADQUIRENTE" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Adquirentes"
                          title="Clique e saiba mais: Adquirentes" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Formas de Pagamento -->


    </h:panelGroup>
    <!-- //Menu Lateral -->    
</h:panelGroup>

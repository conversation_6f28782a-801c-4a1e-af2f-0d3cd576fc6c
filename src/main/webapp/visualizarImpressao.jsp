<%--
    Document   : visualizarImpressao
    Created on : 20/06/2012, 17:42:00
    Author     : Carla
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">


<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{RelatorioBVsControle.relatorioPesquisa ? 'Relatório de Pesquisas' : 'Relatório de BVs'}"/>
    </title>
    <c:set var="titulo" scope="session" value="${RelatorioBVsControle.relatorioPesquisa ? 'Relatório de Pesquisas' : 'Relatório de BVs'}"/>

    <c:if test="${RelatorioBVsControle.relatorioPesquisa}">
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlBaseConhecimento}onde-posso-ver-as-respostas-das-pesquisas-enviadas-para-os-clientes/"/>
    </c:if>

    <c:if test="${!RelatorioBVsControle.relatorioPesquisa}">
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlBaseConhecimento}como-tirar-um-relatorio-das-respostas-dos-bvs-boletins-de-visitas-questionarios-respondidos-pelos-clientes/"/>
    </c:if>

    <a4j:form id="form">
        <body >
            <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" >
                <f:facet name="header">
                    <jsp:include page="topoReduzido.jsp"/>
                </f:facet>
                <tr>
                    <td align="left" width="100%" valign="top" >
                        <h:panelGrid columns="1" width="100%" >
                            <h:panelGrid columns="1" style=" background-image:url('../imagens/fundoBarraTopo.png');" columnClasses="colunaCentralizada" width="100%">
                                <h:outputText styleClass="tituloFormulario" style="text-align:left;" value="Relatório de BVs" >

                                    <c:if test="${RelatorioBVsControle.relatorioPesquisa}">
                                        <h:outputLink value="${SuperControle.urlBaseConhecimento}onde-posso-ver-as-respostas-das-pesquisas-enviadas-para-os-clientes/"
                                                      title="Clique e saiba mais: Relatório BVs" target="_blank" />
                                    </c:if>

                                    <c:if test="${!RelatorioBVsControle.relatorioPesquisa}">
                                        <h:outputLink value="${SuperControle.urlBaseConhecimento}como-tirar-um-relatorio-das-respostas-dos-bvs-boletins-de-visitas-questionarios-respondidos-pelos-clientes/"
                                                      title="Clique e saiba mais: Relatório BVs" target="_blank" />
                                    </c:if>
                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                    </h:outputLink>
                                </h:outputText>
                            </h:panelGrid>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
            <rich:panel id="panelFiltros">
                <h:outputText rendered="#{!empty RelatorioBVsControle.filtrosConsulta.filtrosApresentar}"
                              styleClass="tituloCampos" style="font-weight: bold;"
                              escape ="false" value="Filtros: " />
                <h:outputText rendered="#{!empty RelatorioBVsControle.filtrosConsulta.filtrosApresentar}"
                              styleClass="tituloCampos" escape ="false"
                              value="#{RelatorioBVsControle.filtrosConsulta.filtrosApresentar}" />
            </rich:panel>
            <%@include file="includes/include_resultado_relatorio_bv.jsp" %>
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="background-color:#FFFFFF;">
                <h:commandLink
                    onclick="window.print();return false;">
                    <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0px; width: 65;"/>
                </h:commandLink>
            </h:panelGrid>
        </body>
    </a4j:form>
</f:view>

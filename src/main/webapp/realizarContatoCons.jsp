<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_Agenda_realizarContato}" /></title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp" />
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{HistoricoContatoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_realizarContato}" />
                </h:panelGrid>

                <h:panelGrid columns="4" styleClass="tabForm" width="100%">

                    <h:panelGroup>
                        <h:outputText style="vertical-align:middle;" value="#{msg.msg_consultar_por}" />
                        <f:verbatim>
                            <h:outputText value="    " />
                        </f:verbatim>
                        <h:selectOneMenu style="vertical-align:middle;" id="consulta" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" required="true" value="#{HistoricoContatoControle.controleConsulta.campoConsulta}">
                            <f:selectItems value="#{HistoricoContatoControle.tipoConsultaComboCliente}" />
                            <a4j:support event="onchange" action="#{HistoricoContatoControle.decidirTabela}" reRender="form:dataTables" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:panelGroup>
                        <f:verbatim>
                            <h:outputText value="    " />
                        </f:verbatim>
                        <h:inputText id="valorConsulta" style="vertical-align:middle;" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.controleConsulta.valorConsulta}" />
                        <rich:hotKey selector="#valorConsulta" key="return" handler="#{rich:element('consultar')}.onclick();return false;" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText style="vertical-align:middle;" value="Situação" />
                        <f:verbatim>
                            <h:outputText value="    " />
                        </f:verbatim>
                        <h:selectOneMenu id="situacao" style="vertical-align:middle;" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{HistoricoContatoControle.consultaSituacao}">
                            <f:selectItems value="#{HistoricoContatoControle.listaSelectItemSituacaoCliente}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:panelGroup>
                        <f:verbatim>
                            <h:outputText value="    " />
                        </f:verbatim>
                        <a4j:commandButton id="consultar" type="submit" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                           reRender="paginaAtual, painelPaginacao, form"
                                           actionListener="#{HistoricoContatoControle.consultarPaginadoListener}"
                                           image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="2">
                            <f:attribute name="paginaInicial" value="paginaInicial"/>
                        </a4j:commandButton>
                       <%-- <h:commandButton id="consultar1" style="vertical-align:middle;" action="#{HistoricoContatoControle.consultarCliente}" image="./images/btn_buscar2.png" alt="#{msg.msg_consultar_dados}" accesskey="2" />--%>
                    </h:panelGroup>
                    <h:commandLink style="display:none;" id="botaoInvisivelTela3" action="#{HistoricoContatoControle.consultarCliente}" />
                </h:panelGrid>


                <h:panelGrid id="dataTables" columns="1" styleClass="tabForm" width="100%">
                    <rich:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaPar,linhaImpar " columnClasses="1, 2, centralizado, centralizado, centralizado, centralizado, centralizado" value="#{HistoricoContatoControle.listaConsulta}" rendered="#{HistoricoContatoControle.apresentarResultadoConsulta}" rows="20" var="cliente">

                        <rich:column sortBy="#{cliente.matricula}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Cliente_matricula}" />
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarClienteParaAgendamento}" 
                                             id="matricula" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{cliente.matricula}" />
                            </a4j:commandLink>
                        </rich:column>
                        <rich:column sortBy="#{cliente.pessoa.nome}" filterEvent="onkeyup">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Cliente_pessoa}" />
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarClienteParaAgendamento}" 
                                             id="pessoa" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{cliente.pessoa.nome}" />
                            </a4j:commandLink>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Cliente_categoria}" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{HistoricoContatoControle.selecionarClienteParaAgendamento}" 
                                                 id="categoria" reRender="form, formAgendamento, panelAgendamento"
                                                 oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                    <h:outputText value="#{cliente.categoria.nome}" />
                                </a4j:commandLink>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Inicio Contrato" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{HistoricoContatoControle.selecionarClienteParaAgendamento}" 
                                                 reRender="form, formAgendamento, panelAgendamento"
                                                 oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                    <h:outputText value="#{cliente.situacaoClienteSinteticoVO.dataVigenciaDe}" >
                                         <f:convertDateTime pattern="dd/MM/yyyy" locale="pt" timeZone="America/Sao_Paulo"/>
                                    </h:outputText>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Término Contrato" />
                            </f:facet>
                            <h:panelGroup>
                                <a4j:commandLink action="#{HistoricoContatoControle.selecionarClienteParaAgendamento}" 
                                                 reRender="form, formAgendamento, panelAgendamento"
                                                 oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                    <h:outputText value="#{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustada}" >
                                         <f:convertDateTime pattern="dd/MM/yyyy" locale="pt" timeZone="America/Sao_Paulo"/>
                                    </h:outputText>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Cliente_situacao}" />
                            </f:facet>
                            <h:dataTable id="clienteSituacao" width="100%" columnClasses="w10" value="#{cliente.clienteSituacaoVOs}" var="clienteSituacao">
                                <h:column>
                                    <h:graphicImage value="./images/botaoFreePass.png" rendered="#{clienteSituacao.visitanteFreePass}" />
                                    <h:graphicImage value="./images/botaoAtivo.png" rendered="#{clienteSituacao.ativo}" />
                                    <h:graphicImage value="./images/botaoInativo.png" rendered="#{clienteSituacao.inativo}" />
                                    <h:graphicImage value="./images/botaoVisitante.png" rendered="#{clienteSituacao.visitante}" />
                                    <h:graphicImage value="./images/botaoTrancamento.png" rendered="#{clienteSituacao.trancado}" />

                                    <h:graphicImage value="./images/botaoNormal.png" rendered="#{clienteSituacao.ativoNormal}" />
                                    <h:graphicImage value="./images/botaoTrancadoVencido.png" rendered="#{clienteSituacao.trancadoVencido}" />
                                    <h:graphicImage value="./images/botaoAulaAvulsa.png" rendered="#{clienteSituacao.visitanteAulaAvulsa}" />
                                    <h:graphicImage value="./images/botaoDiaria.png" rendered="#{clienteSituacao.visitanteDiaria}" />

                                    <h:graphicImage value="./images/botaoCancelamento.png" rendered="#{clienteSituacao.inativoCancelamento}" />
                                    <h:graphicImage value="./images/botaoDesistente.png" rendered="#{clienteSituacao.inativoDesistente}" />
                                    <h:graphicImage value="./images/botaoAvencer.png" rendered="#{clienteSituacao.ativoAvencer}" />
                                    <h:graphicImage value="./images/botaoVencido.png" rendered="#{clienteSituacao.inativoVencido}" />
                                    <h:graphicImage value="./images/botaoCarencia.png" rendered="#{clienteSituacao.ativoCarencia}" />
                                    <h:graphicImage value="./images/botaoAtestado.png" rendered="#{clienteSituacao.ativoAtestado}" />
                                </h:column>
                            </h:dataTable>
                        </rich:column>
                    </rich:dataTable>
                    <rich:dataTable id="passivo" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" rendered="#{HistoricoContatoControle.tabelaPassivo}" value="#{HistoricoContatoControle.listaConsulta}" rows="10" var="passivo">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Passivo_codigo}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                                             id="codigoPassivo" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{passivo.codigo}" />
                            </a4j:commandLink>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Passivo_nome}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                                             id="nomePassivo" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{passivo.nome}" />
                            </a4j:commandLink>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Passivo_usuarioResponsavelCadastro}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                                             id="responsavelNomePassivo" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{passivo.responsavelCadastro.nome}" />
                            </a4j:commandLink>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Passivo_dia}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                                             id="diaApresentarPassivo" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{passivo.dia_Apresentar}" />
                            </a4j:commandLink>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Passivo_colaborador}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarPassivoParaAgendamento}"
                                             id="colaboradorPassivo" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{passivo.colaboradorResponsavel.nome}" />
                            </a4j:commandLink>
                        </h:column>
                    </rich:dataTable>
                    <rich:datascroller align="center" for="form:passivo"  id="scResultadoConsultaPass" rendered="#{HistoricoContatoControle.tabelaPassivo}" />
                    <rich:dataTable id="indicacao" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada" rendered="#{HistoricoContatoControle.tabelaIndicacao}" value="#{HistoricoContatoControle.listaConsulta}" rows="10" var="indicacao">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Indicacao_codigo}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarIndicacaoParaAgendamento}"
                                             id="codigoIndicado" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{indicacao.codigo}" />
                            </a4j:commandLink>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Indicado_nomeIndicado}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarIndicacaoParaAgendamento}"
                                             id="quemIndicouIndicado" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{indicacao.nomeIndicado}" />
                            </a4j:commandLink>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Indicado_telefone}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarIndicacaoParaAgendamento}"
                                             id="diaApresentarIndicado" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{indicacao.telefone}" />
                            </a4j:commandLink>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Indicado_email}"/>
                            </f:facet>
                            <a4j:commandLink action="#{HistoricoContatoControle.selecionarIndicacaoParaAgendamento}"
                                             id="eventoIndicado" reRender="form, formAgendamento, panelAgendamento"
                                             oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                <h:outputText value="#{indicacao.email}"/>
                            </a4j:commandLink>
                        </h:column>
                    </rich:dataTable>
                    <rich:datascroller align="center" for="form:indicacao"  id="scResultadoConsultaInd" rendered="#{HistoricoContatoControle.tabelaIndicacao}" />
                </h:panelGrid>
              <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup id="painelPaginacao" rendered="#{HistoricoContatoControle.confPaginacao.existePaginacao}">
                        <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtual, painelPaginacao" rendered="#{HistoricoContatoControle.confPaginacao.apresentarPrimeiro}" actionListener="#{HistoricoContatoControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagInicial" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="items, paginaAtual, painelPaginacao" rendered="#{HistoricoContatoControle.confPaginacao.apresentarAnterior}" actionListener="#{HistoricoContatoControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagAnterior" />
                        </a4j:commandLink>
                        <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{HistoricoContatoControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                        <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="items, paginaAtual, painelPaginacao" rendered="#{HistoricoContatoControle.confPaginacao.apresentarPosterior}" actionListener="#{HistoricoContatoControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagPosterior" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="items, paginaAtual, painelPaginacao" rendered="#{HistoricoContatoControle.confPaginacao.apresentarUltimo}" actionListener="#{HistoricoContatoControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagFinal" />
                        </a4j:commandLink>
                        <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{HistoricoContatoControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <f:verbatim>
                        <h:outputText value=" " />
                    </f:verbatim>
                </h:panelGrid>
                <h:commandButton rendered="#{HistoricoContatoControle.sucesso}" image="./imagensCRM/sucesso.png" />
                <h:commandButton rendered="#{HistoricoContatoControle.erro}" image="./imagensCRM/erro.png" />
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{HistoricoContatoControle.mensagem}" />
                    <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoContatoControle.mensagemDetalhada}" />
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script type="text/javascript">
    document.getElementById("form:consulta").focus();
</script>

<%@include file="includes/imports.jsp" %>

<h:panelGroup rendered="#{not MensagemBuilderControle.building and not MensagemBuilderControle.visaoAgendados and not MensagemBuilderControle.meioWhatsApp}" layout="block"
              style="margin-top: 20px;">

    <h:panelGroup layout="block" styleClass="#{MensagemBuilderControle.meioEmail ? 'modelosEmail' : 'modelosSms'}">
        <div layout="block" class="caixa-build add" onclick="novoModelo()">
            <div>
                <i class="fa-icon-plus-sign"></i>
            </div>
            <div class="rodape-build">
                Criar um novo
            </div>
        </div>

        <h:inputHidden id="codigoselecionado" value="#{MensagemBuilderControle.codigoSelecionado}"/>
        <a4j:jsFunction name="enviarModelo"
                        oncomplete="init(body)"
                        action="#{MensagemBuilderControle.enviarModelo}"
                        reRender="mensagembuilder"></a4j:jsFunction>

        <a4j:jsFunction name="editModelo"
                        oncomplete="init(body)"
                        action="#{MensagemBuilderControle.editar}"
                        reRender="mensagembuilder"></a4j:jsFunction>

        <a4j:jsFunction name="excluirModelo"
                        oncomplete="init(body)"
                        action="#{MensagemBuilderControle.initExcluir}"
                        reRender="panelAutorizacaoFuncionalidade"></a4j:jsFunction>

        <script>
            function edit(codigo) {
                var hiddencodigoselecionado = document.getElementById('form:codigoselecionado');
                hiddencodigoselecionado.value = codigo;
                editModelo();
            }
            function enviar(codigo) {
                var hiddencodigoselecionado = document.getElementById('form:codigoselecionado');
                hiddencodigoselecionado.value = codigo;
                enviarModelo();
            }
            function excluir(codigo) {
                var hiddencodigoselecionado = document.getElementById('form:codigoselecionado');
                hiddencodigoselecionado.value = codigo;
                excluirModelo();
            }
        </script>

        <c:forEach items="#{MensagemBuilderControle.predefinidos}" var="pre" varStatus="var">
            <div class="caixa-build add">
                <c:if test="${not MensagemBuilderControle.meioEmail}">
                    ${pre.html}
                </c:if>
                <c:if test="${MensagemBuilderControle.meioEmail}">
                    <div class="thumb-html">
                            ${pre.html}
                    </div>
                </c:if>


                <div class="hover-layer">

                    <a class="btn-builder" onclick="enviar(${pre.codigo})" style="margin-top: ${MensagemBuilderControle.meioEmail ? '90' : '10'}px;" >
                        <i class="fa-icon-paper-plane"></i>Enviar
                    </a>
                    <a class="btn-builder" onclick="edit(${pre.codigo}); RichFaces.showModalPanel('modalConfirmarExcluir')">
                        <i class="fa-icon-pencil"></i>Editar
                    </a>
                    <a class="btn-builder" onclick="excluir(${pre.codigo}); ">
                        <i class="fa-icon-trash"></i>Excluir
                    </a>
                </div>

                <div class="rodape-build">
                        ${pre.nome}
                </div>
            </div>
        </c:forEach>
    </h:panelGroup>
</h:panelGroup>

<h:panelGroup rendered="#{not MensagemBuilderControle.building and not MensagemBuilderControle.visaoAgendados and  MensagemBuilderControle.meioWhatsApp}" layout="block"
              style="margin-top: 20px;">

    <h:panelGroup rendered="#{MensagemBuilderControle.predefinidos.size() > 0? false : true}">
        <div style="text-align: center; background-color: #0F4C6B; color: white">Nenhum template encontrado!</div>
    </h:panelGroup>


    <a4j:jsFunction name="novoTemplate"
                    oncomplete="#{MensagemBuilderControle.msgAlert}"
                    action="#{MensagemBuilderControle.openPopup}"
                    reRender="mensagembuilder"></a4j:jsFunction>


    <script>
    function novoTemplateWhatsApp() {
                 novoTemplate();
            }
    </script>
    <h:panelGroup layout="block" styleClass="modelosSms">

        <div layout="block" class="caixa-build add" onclick="novoTemplateWhatsApp()">
            <div>
                <i class="fa-icon-plus-sign"></i>
            </div>
            <div class="rodape-build">
                Criar um novo
            </div>
        </div>
        <h:inputHidden id="codigoselecionados" value="#{MensagemBuilderControle.codigoSelecionado}"/>
        <a4j:jsFunction name="prosseguirEnvio"
                        oncomplete="init(body)"
                        action="#{MensagemBuilderControle.enviarModeloWhatsApp}"
                        reRender="mensagembuilder"></a4j:jsFunction>


        <script>
            function enviar(codigo) {
                var hiddencodigoselecionado = document.getElementById('form:codigoselecionados');
                hiddencodigoselecionado.value = codigo;
                prosseguirEnvio();

            }
        </script>

        <c:forEach items="#{MensagemBuilderControle.predefinidos}" var="pre" varStatus="var">
            <div class="caixa-build add">

                <c:if test="${MensagemBuilderControle.meioWhatsApp}">
                    <div>
                            ${pre.html}
                    </div>
                </c:if>
                <div class="hover-layer">
                    <a class="btn-builder" onclick="enviar(${pre.codigo})" style="margin-top: 10px;" >
                        <i class="fa-icon-paper-plane"></i>Enviar
                    </a>
                </div>

                <div class="rodape-build">
                        ${pre.nome}
                </div>
            </div>
        </c:forEach>
    </h:panelGroup>

</h:panelGroup>


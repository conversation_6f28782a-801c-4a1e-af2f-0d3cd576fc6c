<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de vendas online"/>
    </title>
    <html>
    <body>
    <h:form id="form">
        <c:set var="titulo" scope="session" value="${GestaoVendasOnlineControle.titulo}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-controlar-minhas-vendas-online/"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:panelGrid width="100%" style="text-align: right">
            <h:panelGroup layout="block">
                <a4j:commandLink id="exportarExcel"
                                 style="margin-left: 8px;"
                                 actionListener="#{ExportadorListaControle.exportar}"
                                 rendered="#{not empty GestaoVendasOnlineControle.lista}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="2" styleClass="linkPadrao">
                    <f:attribute name="lista" value="#{GestaoVendasOnlineControle.lista}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos" value="matricula=MATRÍCULA,nome=CLIENTE,plano=PLANO,produto=PRODUTO,dataLancamento=DATA LANÇAMENTO,mensagemVendasOnline=MOTIVO ESTORNO"/>
                    <f:attribute name="prefixo" value="#{GestaoVendasOnlineControle.titulo}"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>
                <%--BOTÃO PDF--%>
                <a4j:commandLink id="exportarPdf"
                                 style="margin-left: 8px;"
                                 actionListener="#{ExportadorListaControle.exportar}"
                                 rendered="#{not empty GestaoVendasOnlineControle.lista}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="2" styleClass="linkPadrao">
                    <f:attribute name="lista" value="#{GestaoVendasOnlineControle.lista}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos" value="matricula=MATRÍCULA,nome=CLIENTE,plano=PLANO,produto=PRODUTO,dataLancamento=DATA LANÇAMENTO,mensagemVendasOnline=MOTIVO ESTORNO"/>
                    <f:attribute name="prefixo" value="#{GestaoVendasOnlineControle.titulo}"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
        <a4j:commandButton id="botaoAtualizarPagina" reRender="form:panelGroup,form:listaRisco" style="display:none"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block">
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <div align="right">
                                <h:panelGroup>
                                    <rich:spacer height="5px"/>


                                    </div>
                                </h:panelGroup>

                                <rich:dataTable width="100%"
                                                rows="15" id="listavendas"
                                                styleClass="tabelaSimplesCustom"
                                                value="#{GestaoVendasOnlineControle.lista}"
                                                var="item" rowKeyVar="status">


                                    <rich:column headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                 width="12%" sortBy="#{item.matricula}"
                                                 filterEvent="onkeyup">
                                        <f:facet name="header">
                                            <h:outputText styleClass="rotuloCampos" value="MATRÍCULA"/>
                                        </f:facet>
                                        <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                         action="#{GestaoVendasOnlineControle.irParaTelaCliente}"
                                                         value="#{item.matricula}"
                                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                            <f:param name="state" value="AC"/>
                                        </a4j:commandLink>
                                    </rich:column>


                                    <rich:column headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                 width="12%" label="Nome" sortBy="#{item.nome}"
                                                 filterEvent="onkeyup">
                                        <f:facet name="header">
                                            <h:outputText styleClass="rotuloCampos" value="NOME"/>
                                        </f:facet>
                                        <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                         action="#{GestaoVendasOnlineControle.irParaTelaCliente}"
                                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">

                                            <h:outputText value="#{item.nome}"/>
                                            <f:param name="state" value="AC"/>
                                        </a4j:commandLink>
                                    </rich:column>

                                    <rich:column headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                 width="12%" label="Plano" sortBy="#{item.plano}"
                                                 filterEvent="onkeyup">
                                        <f:facet name="header">
                                            <h:outputText styleClass="rotuloCampos" value="PLANO"/>
                                        </f:facet>
                                        <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                         action="#{GestaoVendasOnlineControle.irParaTelaCliente}"
                                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">

                                            <h:outputText escape="false" value="#{item.plano}"/>
                                            <f:param name="state" value="AC"/>
                                        </a4j:commandLink>
                                    </rich:column>

                                    <rich:column headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                 width="12%" label="Produto" sortBy="#{item.produto}"
                                                 filterEvent="onkeyup">
                                        <f:facet name="header">
                                            <h:outputText styleClass="rotuloCampos" value="PRODUTO"/>
                                        </f:facet>
                                        <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                         action="#{GestaoVendasOnlineControle.irParaTelaCliente}"
                                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">

                                            <h:outputText escape="false" value="#{item.produto}">
                                            </h:outputText>
                                            <f:param name="state" value="AC"/>
                                        </a4j:commandLink>
                                    </rich:column>

                                    <rich:column headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                 width="12%" label="Data Lançamento" sortBy="#{item.dataLancamento}"
                                                 filterEvent="onkeyup">
                                        <f:facet name="header">
                                            <h:outputText styleClass="rotuloCampos" value="DATA LANÇAMENTO"/>
                                        </f:facet>
                                        <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                         action="#{GestaoVendasOnlineControle.irParaTelaCliente}"
                                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                            <h:outputText value="#{item.dataLancamentoApresentar}"/>
                                            <f:param name="state" value="AC"/>
                                        </a4j:commandLink>
                                    </rich:column>

                                    <rich:column headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                 width="12%" label="MOTIVO ESTORNO" sortBy="#{item.mensagemVendasOnline}"
                                                 filterEvent="onkeyup">
                                        <f:facet name="header">
                                            <h:outputText styleClass="rotuloCampos" value="MOTIVO ESTORNO"/>
                                        </f:facet>
                                        <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                         action="#{GestaoVendasOnlineControle.irParaTelaCliente}"
                                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">

                                            <h:outputText value="#{item.mensagemVendasOnline}"
                                                          styleClass="texto-upper"/>
                                            <f:param name="state" value="AC"/>
                                        </a4j:commandLink>
                                    </rich:column>

                                </rich:dataTable>
                                <rich:datascroller id="tabelaRisco" renderIfSinglePage="false" for="listavendas" styleClass="scrollPureCustom"/>
                                <h:panelGroup layout="block" styleClass="container-botoes" style="text-align: left;">
                                    <h:outputText id="totalRisco" styleClass="texto-font texto-size-20 texto-cor-cinza" value="Total #{fn:length(GestaoVendasOnlineControle.lista)} itens"/>
                                </h:panelGroup>

                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:form>

    </body>
    </html>
</f:view>

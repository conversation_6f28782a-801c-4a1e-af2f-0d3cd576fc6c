<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 28/01/2016
  Time: 09:10
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="/includes/imports.jsp" %>
<link href="${root}/css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
<link href="${root}/beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css"/>

<%--BLOCOS DE MODULOS ATIVOS--%>
<h:panelGroup styleClass="topoModulos" layout="block" style="">
  <h:panelGroup layout="block" styleClass="blocoModulos col-md-6 pull-right">
    <h:panelGroup layout="block" styleClass="moduloAberto #{LoginControle.moduloAberto.sigla}"/>
    <h:panelGroup layout="block" styleClass="moduloFechados">
      <a4j:repeat value="#{LoginControle.modulosHabilitados}" var="modulo">
        <h:panelGroup layout="block" style="width: #{LoginControle.tamanhoModulosAbertos}%"
                      rendered="#{not fn:contains(modulo.sigla,LoginControle.moduloAberto.sigla)}"
                      styleClass="moduloFechado #{modulo.sigla}">
          <h:commandLink styleClass="btnModulo" action="#{LoginControle.abrirModulo}" style="width: 100%;height: 100%;"/>
        </h:panelGroup>
      </a4j:repeat>
    </h:panelGroup>

  </h:panelGroup>
</h:panelGroup>
<%--OPCOES MODULOS--%>
<h:panelGroup layout="block" styleClass="topoOpcoes">
  <h:panelGroup layout="block" style="line-height: 3.2;" styleClass="botoesOpcoes pull-right">
    <h:panelGroup layout="block" styleClass="configuracoes" style="height: 70px">
      <h:panelGroup layout="block" style="height: 100%;display: inline-block">
        <h:panelGroup layout="block" styleClass="dropDownMenu"
                      style="float: right;position: relative">
          <div class="itemsOpcoes menuModulos dropdown-toggle dropdown-border"  data-toggle="dropdown">
            <h:graphicImage styleClass="iconePadrao aberto" url="/imagens_flat/marcaGente.png"
                            style="padding-top:31%;padding-left: 26%;display: block"
                            width="25" height="26"/>
            <h:graphicImage styleClass="iconeLogado fechado"
                            url="/imagens_flat/#{LoginControle.moduloAberto.iconeModulo}"
                            style="padding-top:31%;padding-left: 26%;display:none;" width="25"
                            height="26"/>
          </div>
          <ul class="dropMenuModulo dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
              <%--<a4j:repeat value="#{LoginControle.modulosHabilitados}" var="modulo">--%>
              <%--<c:if test="${modulo.codigo != LoginControle.moduloAberto.codigo && not LoginControle.moduloIndependente}">--%>
            <c:if test="${LoginControle.moduloAberto.sigla !=  'ZW'}">
              <li>
                <h:panelGroup layout="block" styleClass="item">
                  <a4j:commandLink action="#{LoginControle.abrirZillyonWeb}"
                                   value="adm" styleClass="tituloMenu">
                    <h:graphicImage style="float: left;margin-top: 10px"
                                    url="/imagens_flat/pct-icone-fundo-administrativo.svg" width="25"
                                    height="26"/>
                  </a4j:commandLink>
                </h:panelGroup>
              </li>
            </c:if>
            <c:if test="${LoginControle.apresentarLinkParaModuloCRM and LoginControle.moduloAberto.sigla !=  'CRM'}">
              <li>
                <h:panelGroup layout="block" styleClass="item">
                  <a4j:commandLink value="crm" actionListener="#{SkinControle.definirSkinCrm}"
                                   action="#{LoginControle.abrirModuloCRM}"
                                   styleClass="tituloMenu">
                    <h:graphicImage style="float: left;margin-top: 10px"
                                    url="/imagens_flat/pct-icone-fundo-crm.svg" width="25"
                                    height="26"/>
                  </a4j:commandLink>
                </h:panelGroup>
              </li>
            </c:if>
            <c:if test="${LoginControle.apresentarLinkFinanceiro and LoginControle.moduloAberto.sigla !=  'FIN'}">
              <li>
                <h:panelGroup layout="block" styleClass="item">
                  <a4j:commandLink action="#{LoginControle.abrirModuloFinanceiro}"
                                   value="financeiro"
                                   actionListener="#{SkinControle.definirSkinFinanceiro}"
                                   styleClass="tituloMenu">
                    <h:graphicImage style="float: left;margin-top: 10px"
                                    url="/imagens_flat/pct-icone-fundo-financeiro.svg" width="25"
                                    height="26"/>
                  </a4j:commandLink>
                </h:panelGroup>
              </li>
            </c:if>
            <c:if test="${LoginControle.apresentarLinkCE and LoginControle.moduloAberto.sigla !=  'CE'}">
              <li>
                <h:panelGroup layout="block" styleClass="item">
                  <a4j:commandLink value="central eventos"
                                   oncomplete="#{LoginControle.msgAlert}"
                                   action="#{LoginControle.abrirCE}" styleClass="tituloMenu">
                    <h:graphicImage style="float: left;margin-top: 10px"
                                    url="/imagens_flat/pct-icone-fundo-administrativo.svg" width="25"
                                    height="26"/>
                  </a4j:commandLink>
                </h:panelGroup>
              </li>
            </c:if>
            <c:if test="${LoginControle.apresentarLinkTREINO and LoginControle.validarTreinoLoginApp}">
              <li>
                <h:panelGroup layout="block" styleClass="item">
                  <h:outputLink value="#{LoginControle.abrirTreino}" styleClass="tituloMenu">
                    <h:graphicImage style="float: left;margin-top: 10px"
                                    url="/imagens_flat/pct-icone-fundo-novo-treino.svg" width="25"
                                    height="26"/>
                    <h:outputText value="treino"/>
                  </h:outputLink>
                </h:panelGroup>
              </li>
            </c:if>
            <c:if test="${LoginControle.apresentarLinkAulaCheia && LoginControle.validarTreinoLoginApp}">
              <li>
                <h:panelGroup layout="block" styleClass="item">
                  <h:outputLink value="#{LoginControle.abrirAulaCheia}" styleClass="tituloMenu">
                    <h:graphicImage style="float: left;margin-top: 10px"
                                    url="/imagens_flat/genteAC.png" width="25"
                                    height="26"/>
                    <h:outputText value="aula cheia"/>
                  </h:outputLink>
                </h:panelGroup>
              </li>
            </c:if>
          </ul>
        </h:panelGroup>
      </h:panelGroup>
      <h:panelGroup layout="block" styleClass="boxOpcoes transition-all pull-right"
                    style="display: table;">
        <h:panelGroup layout="block"
                      styleClass="dropDownMenu col-celula">
          <h:panelGroup layout="block">
            <div class="itemsOpcoes  dropdown-toggle dropdown-border"  data-toggle="dropdown">
              <h:outputText styleClass="fa-icon-question-sign btnDropDown aberto"/>
              <h:outputText style="display: none;"
                            styleClass="fa-icon-question-sign btnDropDown fechado"/>
            </div>
            <ul class="dropDownFAQ dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
              <li>
                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                              style="float: right;width: 90%;height: 100%;text-align: left;">
                  <h:graphicImage url="/faces/beta/imagens/live-help.png" width="16"
                                  height="16"></h:graphicImage>
                  <h:outputLink styleClass="textSmallFlat" target="_blank"
                                value='#{SuporteControle.urlFAQ}/index.html'>
                    FAQ Personalizado
                  </h:outputLink>
                </h:panelGroup>
              </li>
              <li>
                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                              style="float: right;width: 90%;height: 100%;text-align: left;">
                  <h:graphicImage url="/faces/beta/imagens/live-help.png" width="16"
                                  height="16"></h:graphicImage>
                  <h:outputLink styleClass="textSmallFlat" target="_blank"
                                value='#{SuperControle.contextPath}/redir?up'>
                    Minha UP!
                  </h:outputLink>
                </h:panelGroup>
              </li>
              <li>
                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                              style="float: right;width: 90%;height: 100%;text-align: left;">
                  <h:graphicImage url="/faces/beta/imagens/mini-icons-wiki.png" width="16"
                                  height="16"></h:graphicImage>
                  <h:outputLink styleClass="textSmallFlat" id="linkwiki"
                                target="_blank"
                                value='#{SuperControle.urlWikiRaiz}/P%C3%A1gina_principal'>
                    WikiPacto
                  </h:outputLink>
                </h:panelGroup>
              </li>
              <li>
                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                              style="float: right;width: 90%;height: 100%;text-align: left;">
                  <h:graphicImage url="/faces/beta/imagens/mini-icons-suport.png"
                                  width="16"
                                  height="16"></h:graphicImage>
                  <h:outputLink styleClass="textSmallFlat" id="linksolicitacao"
                                target="_blank" value="#{SuporteControle.urlSolicitacao}">
                    Suporte
                    <a4j:support event="onclick"
                                 action="#{SuporteControle.prepareUserService}"
                                 oncomplete="#{SuporteControle.loginService}"/>
                  </h:outputLink>
                </h:panelGroup>
              </li>
              <c:if test="${LoginControle.permissaoAcessoMenuVO.boletosSistema}">
                <li>
                  <h:panelGroup styleClass="alinhaMentoItemMenu" layout="block"
                                style="float: right;width: 90%;height: 100%;text-align: left;">
                    <h:graphicImage url="/imagens/dinheiroContaCorrente.png" width="16"
                                    height="16"></h:graphicImage>
                    <a4j:commandLink styleClass="textSmallFlat" id="linkFinanceiro"
                                     action="financeiroPacto">
                      Boleto Pacto
                    </a4j:commandLink>
                  </h:panelGroup>
                </li>
              </c:if>
            </ul>
          </h:panelGroup>

        </h:panelGroup>
        <h:panelGroup layout="block"
                      styleClass="dropDownMenu col-celula" rendered="#{!SuperControle.usuarioLogado.administrador && SuperControle.empresaLogado.codigo != 0}">
          <h:panelGroup layout="block">
            <div style="position: relative;"
                 class="itemsOpcoes iconeTrocaEmpresa  dropdown-toggle  dropdown-border"  data-toggle="dropdown">
              <h:outputText style="display:block;"
                            styleClass="fa-icon-exchange iconeEmpresa aberto"/>
              <h:outputText style="display: none;"
                            styleClass="fa-icon-exchange iconeEmpresa fechado"/>
            </div>
            <ul class="dropMenuEmpresa dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
              <li>
                <h:panelGroup layout="block"
                              style="width: calc(#{fn:length(InicioControle.empresaLogado.nome)} * 11.1px);min-width:200px;">
                  <h:outputText styleClass="sink textSmallFlat3"
                                style="color:#BBBDBF;  margin-left: 0px;vertical-align: top;text-decoration: none;"
                                value="#{InicioControle.empresaLogado.nome}"></h:outputText>
                </h:panelGroup>
              </li>
              <c:forEach items="#{InicioControle.usuarioLogado.usuarioPerfilAcessoVOs}"
                         var="perfil">
                <c:if test="${perfil.empresa.codigo != InicioControle.empresaLogado.codigo}">
                  <li>
                    <h:panelGroup layout="block"
                                  style="width: calc(#{fn:length(perfil.empresa.nome)} * 11.1px);min-width:200px;">
                      <a4j:commandLink
                              style="color:#BBBDBF;  margin-left: 0px;vertical-align: top;text-decoration: none;"
                              value="#{perfil.empresa.nome}"
                              action="#{InicioControle.confirmarTrocaEmpresa}"
                              styleClass="sink textSmallFlat">
                        <f:setPropertyActionListener value="#{perfil.empresa.codigo}"
                                                     target="#{InicioControle.codigoEmpresa}"/>
                      </a4j:commandLink>
                    </h:panelGroup>
                  </li>
                </c:if>
              </c:forEach>
            </ul>
          </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="col-celula">
          <h:panelGroup layout="block" styleClass="itemsOpcoes">
            <a4j:commandLink action="#{LoginControle.abrirSocialMailing}" reRender="nrMsgLidas"
                             oncomplete="#{LoginControle.msgAlert}" style="display: block;text-decoration: none;">
              <i class="fa-icon-comment itemsOpcoes"></i>
            </a4j:commandLink>
            <h:panelGroup id="nrlida" rendered="#{UsuarioControle.usuario.nrMensagensNaoLidas > 0}"
                          layout="block">
              <div class="notificacaoAtividades" style="display: inline-block">
                <h:outputText value="#{UsuarioControle.usuario.nrMensagensNaoLidas}"></h:outputText>
              </div>
            </h:panelGroup>
          </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block" rendered="#{not LoginControle.moduloAtualCe}"
                      styleClass="dropDownMenu col-celula">
          <h:panelGroup layout="block">
            <h:panelGroup layout="block" style="position: relative;"
                          styleClass="itemsOpcoes iconeTrocaEmpresa" >
                <a4j:commandLink action="#{configuracaoEstudioControle.acaoEntrar}"
                                 oncomplete="#{configuracaoEstudioControle.msgAlert}"
                                 styleClass="textSmallFlat">
                  <h:outputText style="display:block;"
                                styleClass="fa-icon-cog iconeEmpresa itemsOpcoes aberto"/>
                </a4j:commandLink>
            </h:panelGroup>
          </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="col-celula fotoUsuario fechado dropDownMenu " style="width: 52px;height: 52px;border-radius:50%">

          <div class="dropdown-toggle col-vert-align"   data-toggle="dropdown" style="width: 100%;height: 100%;position: relative;" >
            <a4j:mediaOutput element="img"
                             rendered="#{!SuperControle.fotosNaNuvem}"
                             align="left"
                             style="width:50px;height:50px; border:1px solid #094771;border-radius:50%; "
                             cacheable="false" session="false"
                             title="#{SuperControle.usuarioLogado.colaboradorVO.pessoa.foto}"
                             createContent="#{SuperControle.paintFotoUsuario}"
                             value="#{ImagemData}" styleClass="dropdown-toggle" mimeType="image/jpeg">
              <f:param value="#{SuperControle.timeStamp}" name="time"/>
              <f:param name="largura" value="50"/>
              <f:param name="altura" value="50"/>
            </a4j:mediaOutput>
            <h:outputText styleClass="icon-hidden fa-icon-caret-down"></h:outputText>
            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                            width="50" height="50"
                            styleClass="dropdown-toggle"
                            url="#{SuperControle.fotoKeyUsuarioLogado}">
            </h:graphicImage>
          </div>
          <ul class="dropMenuUsuario dropdown-menu" style="display: none;" role="menu" aria-labelledby="dropdownMenu">
            <li>
              <h:panelGroup layout="block" styleClass="container-fluid img-responsive">
                <a4j:mediaOutput element="img" id="foto" style="width:100%;height:100% "
                                 cacheable="false"
                                 createContent="#{SuperControle.paintFotoSemPesquisarNoBanco}"
                                 value="#{ImagemData}" mimeType="image/jpeg">
                  <f:param value="#{SuperControle.timeStamp}" name="time"/>
                  <f:param name="largura" value="120"/>
                  <f:param name="altura" value="100"/>
                </a4j:mediaOutput>
              </h:panelGroup>
            </li>
            <li>
              <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                            style="width: 85%;float: right;text-align: left;height: 100%;">

                <a4j:commandLink
                        styleClass="textSmallFlat"
                        rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}"
                        action="#{UsuarioControle.novoMeusDados}"
                        oncomplete="abrirPopup('alterarDadosCadastraisUsuario.jsp', 'Dados Cadastrais', 820, 600);">
                  <h:outputText style="vertical-align: middle;" styleClass="fa-icon-key"/>&nbspMeus Dados
                </a4j:commandLink>
              </h:panelGroup>
            </li>
            <div style="width: 80%;height: 1px;margin-left: 10%;background-color: black"></div>
            <li>
              <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                            style="width: 85%;float: right;text-align: left;height: 100%;">
                <a href="#" class="textSmallFlat"
                     onclick="document.location.href = '${LogoutControle.redirectLogout}'">
                  <h:outputText style="vertical-align: middle;" styleClass="fa-icon-remove"/>&nbspSair
                </a>
              </h:panelGroup>
            </li>
          </ul>

        </h:panelGroup>
        <script>
          function fecharTodos() {
            jQuery('.dropDownMenu .fechado').css('display', 'none');
            jQuery('.dropDownMenu .fechado').parent().find('.aberto').css('display', 'block');
          }

          jQuery('div.dropdown-toggle').click(function (){
            var el = jQuery(this).parent().children('ul');
            //  console.log(jQuery(this).attr('class'));
            jQuery('.dropdown-menu').slideUp('fast');
            if (el.css('display') === 'block') {
              jQuery('.dropdown-menu').parent().removeClass('open');
            } else {
              jQuery('.dropdown-menu').parent().removeClass('open');
              el.slideDown('fast',function(){
                el.parent().addClass('open');
              });

            }
          });
          jQuery('.moduloFechado').click(function(){
            jQuery(this).children('.btnModulo').click();
          });

          jQuery(document).click(function(e) {
            try {
              var classe = jQuery(e.target).parent().attr('class');
              if (classe.indexOf('dropdown-toggle') < 0 && classe.indexOf('menuModulos') < 0
                      && classe.indexOf('iconeTrocaEmpresa') < 0 && classe.indexOf('dropDownMenu') < 0
                      && classe.indexOf('naofecharmenufluxo') < 0) {
                jQuery('.dropDownMenu ul').slideUp('fast', function () {
                  jQuery('.dropdown-menu').parent().removeClass('open');

                });

              }
            }catch(ignored){}
          });

          jQuery('.itemsOpcoes').click(function () {
            if(jQuery(this).children('.aberto').css('display') === 'block'){
              fecharTodos();
              jQuery(this).children('.aberto').css('display', 'none');
              jQuery(this).children('.aberto').parent().find('.fechado').css('display', 'block');
            }else{
              fecharTodos();
            }
          });
        </script>
      </h:panelGroup>
    </h:panelGroup>
  </h:panelGroup>
  <h:panelGroup layout="block" styleClass="logoIcones col-celula pull-left">

    <h:panelGroup layout="block" style="margin-left: 20px;width: 50%;display: inline-block;" styleClass="logo-topo">
      <h:graphicImage height="55px"
              url="/imagens_flat/#{LoginControle.moduloAberto.logoModulo}" />
    </h:panelGroup>
    <rich:spacer width="15"/>

    <a4j:jsFunction name="verificarTemNova" reRender="painelFeed"
                    status="false"
                    action="#{FeedGestaoControle.verificarTemNova}">
    </a4j:jsFunction>
    <h:panelGroup id="painelFeed" layout="block" style="display: inline-block" rendered="true">

      <a4j:commandLink oncomplete="Richfaces.showModalPanel('feedGestao'); indiceAtual = 1;"
                       reRender="feedGestao, painelFeed"
                       status="false"
                       rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                       action="#{FeedGestaoControle.abrirFeed}">
        <%--<h:graphicImage--%>
        <%--rendered="#{FeedGestaoControle.nrMsgsNaoLidas == null or FeedGestaoControle.nrMsgsNaoLidas == 0}"--%>
        <%--url="/feed_gestao/assistente_sem_notif.png" width="60" height="60"--%>
        <%--title="Assistente de Gestão Pacto"/>--%>

        <h:graphicImage url="/feed_gestao/assistente_com_notif.gif" width="60" height="60"
                        title="Assistente de Gestão Pacto"/>
      </a4j:commandLink>

      <h:panelGroup id="nrlidaFeed" rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                    layout="block" style="float:right;display: inline;position: absolute;">
        <div class="notificacaoAtividades">
          <h:outputText value="#{FeedGestaoControle.nrMsgsNaoLidas}"></h:outputText>
        </div>
      </h:panelGroup>

      <h:outputLink styleClass="linkWiki"
                    rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                    value="#{SuperControle.urlWiki}ZillyonWeb:Assistente_Gestao_Pacto"
                    title="Clique e saiba mais: Feed de gestão"
                    target="_blank">
        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
      </h:outputLink>

      <script type="text/javascript">
        window.addEventListener('load',function() {
          verificarTemNova();
        }, false);
      </script>

    </h:panelGroup>
  </h:panelGroup>
</h:panelGroup>
<%--MENU OPÇÕES--%>

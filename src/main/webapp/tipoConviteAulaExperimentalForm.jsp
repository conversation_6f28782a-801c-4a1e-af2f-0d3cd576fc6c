<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Convite Aula Experimental"/>
    </title>

    <c:set var="titulo" scope="session" value="Convite Aula Experimental"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:PPT:Plano"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            
                <jsp:include page="topoReduzido.jsp"/>
            
        </f:facet>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
        <h:form id="form">
            <h:commandLink action="#{TipoConviteAulaExperimentalControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Convite Aula Experimental"/>
                </h:panelGrid>
                <rich:tabPanel width="100%" >
                    <rich:tab id="dadosBasico" label="Dados Básico">
                        <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText  styleClass="tituloCampos"  value="Código:" />
                            <h:panelGroup>
                                <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  rendered="#{TipoConviteAulaExperimentalControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="* Empresa" />
                            <h:panelGroup  rendered="#{TipoConviteAulaExperimentalControle.usuarioLogado.administrador}">
                                <h:selectOneMenu  id="empresa"  onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                  disabled="#{(TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.codigo > 0)}"
                                                  style="vertical-align: middle"
                                                  styleClass="form" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.empresaVO.codigo}" >
                                    <f:selectItems  value="#{TipoConviteAulaExperimentalControle.listaEmpresas}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_empresa" style="vertical-align: middle; padding-left: 5px" action="#{TipoConviteAulaExperimentalControle.montarListaEmpresas}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:empresa"/>
                            </h:panelGroup>

                            <h:outputText  styleClass="tituloCampos"  value="* Descrição:" />
                            <h:panelGroup>
                                <h:inputText  id="nome"  size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.descricao}" />
                                <h:message for="nome" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>


                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_vigenciaDe}" />
                            <h:panelGroup>
                                <rich:calendar id="vigenciaDe"
                                               value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.vigenciaInicial}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" />
                                <h:message for="vigenciaDe" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_vigenciaAte}" />
                            <h:panelGroup>
                                <rich:calendar id="vigenciaAte"
                                               value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.vigenciaFinal}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false" />
                                <rich:jQuery id="mskDataAte" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                <h:message for="vigenciaAte" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>

                            <h:outputText  styleClass="tituloCampos"  value="* Quantidade aula experimental que o convidado pode fazer:" />
                            <h:panelGroup>
                                <h:inputText  id="qtdConvite"
                                              title="Informe a quantidade de aula experimental que o convidado poderá agendar com este convite."
                                              onkeypress="return mascaraTodos(this.form, 'form:qtdConvite', '999', event);"
                                              size="5" maxlength="3" onblur="blurinput(this);"
                                              onfocus="focusinput(this);" styleClass="form" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.quantidadeAulaExperimental}" />
                                <h:outputText style="padding-left: 10px; vertical-align: middle"  styleClass="tituloCampos"  value="Agendar as aulas em dias seguidos:" />
                                <h:selectBooleanCheckbox id="agendConvAulaDiasSeguidos"  style="vertical-align: middle" styleClass="campos" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.aulasAgendadasEmDiasSeguido}"/>
                            </h:panelGroup>

                            <h:outputText  styleClass="tituloCampos"  value="Quantidade máxima de convites o aluno pode enviar:" />
                            <h:inputText  id="qtdConviteAlunoPodeEnviar"
                                          title="Informe a quantidade máxima deste convite que o aluno pode enviar para amigos."
                                          onkeypress="return mascaraTodos(this.form, 'form:qtdConviteAlunoPodeEnviar', '999', event);"
                                          size="5" maxlength="3" onblur="blurinput(this);"
                                          onfocus="focusinput(this);" styleClass="form" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.quantidadeConviteAlunoPodeEnviar}" />

                            <h:outputText   styleClass="tituloCampos" value="Aluno pode enviar convite:" />
                            <h:selectBooleanCheckbox id="alunoPodeConv" styleClass="campos" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.alunoPodeEnviarConvite}"/>
                            <h:outputText  styleClass="tituloCampos"  value="Colaborador pode enviar convite:" />
                            <h:selectBooleanCheckbox id="ColaboradorPodeConv" styleClass="campos" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.colaboradorPodeEnviarConvite}"/>


                        </h:panelGrid>

                    </rich:tab>
                    <rich:tab id="abaModalidade" action="#{TipoConviteAulaExperimentalControle.inicializarDadosAbaRestricao}" label="Restringir modalidades">
                        <h:panelGrid id="panelModalidades" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                             <h:panelGrid id="panelInfo" rendered="#{(TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.codigo == 0) && (TipoConviteAulaExperimentalControle.usuarioLogado.administrador)}" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                 <h:outputText  styleClass="tituloCampos" style="color: red;" value="Para incluir as restrições, é necessário informar os dados da aba 'Dados básico' primeiro e depois clicar no no botão 'Gravar'. "  />
                             </h:panelGrid>
                            <h:panelGrid id="panelModalidadeGeral" rendered="#{(TipoConviteAulaExperimentalControle.mostrarConfiguracaoRestricao)}" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                <h:panelGrid id="panelModalidadeConvite" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                                    <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" footerClass="colunaCentralizada">
                                        <h:outputText  styleClass="tituloCampos" value="Modalidade com turma:" />
                                        <h:panelGroup>
                                            <h:selectOneMenu  id="ConviteModalidade_modalidade" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                              style="vertical-align: middle"
                                                              styleClass="form"
                                                              value="#{TipoConviteAulaExperimentalControle.codigoModalidade}" >
                                                <f:selectItems  value="#{TipoConviteAulaExperimentalControle.listaSelectItemModalidade}" />
                                            </h:selectOneMenu>
                                            <a4j:commandButton id="atualizar_ConviteModalidade_modalidade"
                                                               action="#{TipoConviteAulaExperimentalControle.montarListaSelectItemModalidade}"
                                                               style="vertical-align: middle; padding-left: 10px"
                                                               image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                               reRender="form:ConviteModalidade_modalidade"/>
                                            <rich:spacer width="5px;"/>
                                            <a4j:commandButton id="cadastroNovaModalidade" alt="Cadastrar Modalidade"
                                                               style="vertical-align: middle"
                                                               onclick="abrirPopup('modalidadeCons.jsp', 'Modalidade', 800, 595);"
                                                               action="#{ModalidadeControle.novo}" image="./images/icon_add.gif"/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    <a4j:commandButton id="addModalidade" action="#{TipoConviteAulaExperimentalControle.adicionarModalidade}"
                                                       reRender="panelModalidadeConvite, panelMensagem"  focus="form:ConviteModalidade_modalidade" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="8" styleClass="botoes"/>


                                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                        <h:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada, colunaCentralizada"
                                                     value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalVO.listaModalidade}" var="conviteModalidade">
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText  value="Modalidade com turma:" />
                                                </f:facet>
                                                <h:outputText  value="#{conviteModalidade.modalidadeVO.nome}" />
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                                </f:facet>
                                                <h:panelGroup>
                                                    <h:outputText value="    "/>

                                                    <a4j:commandButton id="removerConviteMod" reRender="form" ajaxSingle="true" immediate="true"
                                                                       style="vertical-align: middle"
                                                                       onclick="if(!confirm('Confirma exclusão?')) {return false;}"
                                                                       action="#{TipoConviteAulaExperimentalControle.removerModalidade}"
                                                                       value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes">

                                                        <f:setPropertyActionListener value="#{conviteModalidade}" target="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeVO}"></f:setPropertyActionListener>
                                                     </a4j:commandButton>

                                                    <h:outputText value="    "/>

                                                    <h:panelGroup>
                                                        <a4j:commandButton id="btnConfConviteHorario"
                                                                           action="#{TipoConviteAulaExperimentalControle.configurarConviteModalidadeHorario}"
                                                                           value="Horário"
                                                                           focus="horarioInicial_Modalidade"
                                                                           reRender="panelConviteHorario, panelMensagem"
                                                                           alt="Restringir horários."
                                                                           styleClass="botoes nvoBt btSec"
                                                                           style="vertical-align: middle"
                                                                           accesskey="9" >
                                                            <f:setPropertyActionListener value="#{conviteModalidade}" target="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeVO}"></f:setPropertyActionListener>
                                                        </a4j:commandButton>

                                                    </h:panelGroup>

                                                </h:panelGroup>
                                            </h:column>
                                        </h:dataTable>
                                    </h:panelGrid>
                                </h:panelGrid>

                                <h:panelGrid id="panelConviteHorario"  width="100%" columns="1" >
                                    <h:panelGrid columns="1"  width="100%" rendered="#{TipoConviteAulaExperimentalControle.apresentarConfiguracaoHorario}"  columnClasses="colunaCentralizada">
                                        <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                                            <h:outputText styleClass="tituloFormulario" value="Configuração de horário"/>
                                        </h:panelGrid>
                                        <h:panelGrid id="panelhoraConvite" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                                            <h:outputText styleClass="tituloCampos" value="Modalidade:" />
                                            <h:outputText styleClass="tituloCampos" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeVO.modalidadeVO.nome}" />

                                            <h:outputText styleClass="tituloCampos" value="Dias da Semana: " />

                                            <rich:dataGrid id="gridDiasSemana" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeHorarioVO.listaDiaSemana}" var="diaSemanaHorario"
                                                           width="100%" columns="7" columnClasses="semBorda"
                                                           styleClass="semBorda"
                                                           cellpadding="0" cellspacing="0">
                                                <h:panelGroup>
                                                    <h:selectBooleanCheckbox style="vertical-align: middle" value="#{diaSemanaHorario.selecionado}"/>
                                                    <rich:spacer width="5"/>
                                                    <h:outputText style="vertical-align: middle"  styleClass="titulo3" value="#{diaSemanaHorario.diaSemana.descricaoSimples}" />
                                                </h:panelGroup>
                                            </rich:dataGrid>
                                            <h:outputText styleClass="tituloCampos" value="Agendar aula no intervalo de " />
                                            <h:panelGroup>
                                                <h:inputText id="horarioInicial_Modalidade"
                                                             size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                             onkeypress="return mascaraTodos(this.form, 'form:horarioInicial_Modalidade', '99:99', event);"
                                                             styleClass="form" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeHorarioVO.horaInicial}" />

                                                <h:outputText styleClass="tituloCampos" style="padding-left: 5px;padding-right: 5px" value="até" />
                                                <h:inputText  id="horarioFinal_Modalidade"
                                                              onkeypress="return mascaraTodos(this.form, 'form:horarioFinal_Modalidade', '99:99', event);"
                                                              size="5" maxlength="5" onblur="blurinput(this);"
                                                              onfocus="focusinput(this);" styleClass="form" value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeHorarioVO.horaFinal}" />
                                                <h:outputText styleClass="tituloCampos" value=" horas" />
                                            </h:panelGroup>

                                        </h:panelGrid>
                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada"  width="100%">
                                            <a4j:commandButton id="addhorConvite" action="#{TipoConviteAulaExperimentalControle.adicionarHorario}"
                                                               reRender="panelConviteHorario, panelMensagem" focus="horarioInicial_Modalidade"
                                                               value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png"
                                                               accesskey="8" styleClass="botoes"/>
                                        </h:panelGrid>
                                        <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                            <rich:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                                            rowClasses="linhaImpar, linhaPar"  rows="7" columnClasses="colunaAlinhamento"
                                                            value="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeVO.listaHorario_Apresentar}" var="conviteHorario">
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Hora inicial" />
                                                    </f:facet>
                                                    <h:outputText  value="#{conviteHorario.horaInicial}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Hora final" />
                                                    </f:facet>
                                                    <h:outputText  value="#{conviteHorario.horaFinal}" />
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Dias Semana" />
                                                    </f:facet>
                                                    <h:outputText  value="#{conviteHorario.diasSemanaApresentarNaGrid}" />
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                                    </f:facet>
                                                    <h:panelGroup>
                                                        <a4j:commandButton id="editarConviteHorario" reRender="panelConviteHorario, panelMensagem"
                                                                           style="vertical-align: middle"
                                                                           ajaxSingle="true"  action="#{TipoConviteAulaExperimentalControle.editarHorario}"
                                                                           value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes">
                                                            <f:setPropertyActionListener value="#{conviteHorario}" target="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeHorarioVO}"></f:setPropertyActionListener>
                                                        </a4j:commandButton>

                                                        <h:outputText value="    "/>

                                                        <a4j:commandButton id="removerConviteHorario" reRender="panelConviteHorario, panelMensagem" ajaxSingle="true"
                                                                           style="vertical-align: middle"
                                                                           action="#{TipoConviteAulaExperimentalControle.removerHorario}" value="#{msg_bt.btn_excluir}"
                                                                           onclick="if(!confirm('Confirma exclusão?')) {return false;}"
                                                                           image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes">
                                                            <f:setPropertyActionListener value="#{conviteHorario}" target="#{TipoConviteAulaExperimentalControle.tipoConviteAulaExperimentalModalidadeHorarioVO}"></f:setPropertyActionListener>
                                                        </a4j:commandButton>

                                                    </h:panelGroup>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGrid>
                                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                            <h:panelGroup>
                                                <a4j:commandButton id="salvarConviteHorario" reRender="panelModalidadeGeral, panelMensagem" action="#{TipoConviteAulaExperimentalControle.fechaJanelaConviteHorario}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>


                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagem" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            
                                <h:outputText value=" "/>
                            
                        </h:panelGrid>
                        <h:commandButton  rendered="#{TipoConviteAulaExperimentalControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{TipoConviteAulaExperimentalControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgBasica" styleClass="mensagem"  value="#{TipoConviteAulaExperimentalControle.mensagem}"/>
                            <h:outputText id="msgDetalhada" styleClass="mensagemDetalhada" value="#{TipoConviteAulaExperimentalControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{TipoConviteAulaExperimentalControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            
                                <h:outputText value="    "/>
                            
                            <a4j:commandButton id="salvar" action="#{TipoConviteAulaExperimentalControle.gravar}"
                                             value="#{msg_bt.btn_gravar}"
                                             reRender="form"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            
                                <h:outputText value="    "/>
                            
                            <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{TipoConviteAulaExperimentalControle.excluir}" value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            
                                <h:outputText value="    "/>
                            
                            <a4j:commandButton id="consultar" immediate="true" action="#{TipoConviteAulaExperimentalControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            
                                <h:outputText value="    "/>


                            <a4j:commandLink
                                    action="#{TipoConviteAulaExperimentalControle.realizarConsultaLogObjetoSelecionado}"
                                    reRender="form"
                                    oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                    title="Visualizar Log"
                                    style="display: inline-block; padding: 8px 15px;"
                                    styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-search"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>
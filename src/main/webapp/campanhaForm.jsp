<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${contexto}/css/ce.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">var contexto = '${contexto}';</script>
<script type="text/javascript" language="javascript" src="${contexto}/script/ajuda.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_campanha_duracao_tituloForm}"/>
    </title>
    
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_campanha_duracao_tituloForm}"/>
    
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Campanha"/>
        <c:set var="urlWiki" scope="session" value="${ClubeVantagensControle.linkWiki}"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
            </h:panelGrid>
            <h:panelGroup id="panelCadastroBrinde"  layout="block" style="margin-left: 15px;">
                <h:panelGroup>
                    <div id="botoesTL" style="display: inline-flex; padding: 10px;">
                        
                    </div>
                </h:panelGroup>
                <h:panelGrid columns="2" width="100%">
                <h:panelGrid id="panelGeral" columns="1" >
                        <h:panelGrid columns="1">
                            <h:outputText   style="display: block" styleClass="rotuloCampos margenVertical" value="#{msg_aplic.prt_Brinde_empresa}" />
                            <h:panelGroup id="groupEmpresa" layout="block"  styleClass="font-size-em-max">
                                <h:panelGroup  styleClass="cb-container margenVertical" layout="block">
                                <h:selectOneMenu id="empresaSelecionadaCD" tabindex="8" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 disabled="#{ClubeVantagensControle.campanha.codigo>0}"  style="font-size: 14px !important;"
                                                 value="#{ClubeVantagensControle.campanha.empresa.codigo}" >
                                    <f:selectItems value="#{ItemCampanhaControle.listaEmpresas}" />
                                </h:selectOneMenu>
                            </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>

                        <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Nome" />
                    <rich:spacer height="3px"/>
                        <h:inputText  id="nomeBrinde"  size="#{ClubeVantagensControle.QTD_MAX_NOME + 10}" maxlength="#{ClubeVantagensControle.QTD_MAX_NOME}"  style="font-size: 14px !important;" styleClass="inputTextClean" value="#{ClubeVantagensControle.campanha.nome}"/>
                    <rich:spacer height="10px"/>
                        <h:outputText  id="labelPeriodo" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Data Vig�ncia"/>
                    <h:panelGroup id="panelPeriodo">
                        <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;">
                                <rich:calendar value="#{ClubeVantagensControle.campanha.dataInicial}"
                                           id="dataInicial"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font" value="at�" style="margin-left: 15px;"/>
                        <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px !important;margin-left: 15px;">
                                    <rich:calendar value="#{ClubeVantagensControle.campanha.dataFinal}"
                                               id="dataFinal"
                                               inputSize="10"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                         </h:panelGroup>
                    </h:panelGroup>
                    <rich:spacer height="10px"/>
                        <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Descri��o" />
                    <rich:spacer height="3px"/>
                        <h:inputText  id="descricao" style="font-size: 14px !important;"  size="#{ClubeVantagensControle.QTD_MAX_DESCRICAO + 10}" maxlength="#{ClubeVantagensControle.QTD_MAX_DESCRICAO}" styleClass="inputTextClean" value="#{ClubeVantagensControle.campanha.descricao}"/>
                    </h:panelGrid>
                    <h:panelGroup>
                        <jsp:include page="includes/clubedevantagens/tabela_categorias.jsp"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" width="100%" style="margin-top: 13px;">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandLink reRender="@form,panelGeral,listaCampanha,tblComPontos"
                                             styleClass="pure-button"
                                             style="margin-top: 13px;"
                                             accesskey="2"
                                             id="novo"
                                             action="#{ClubeVantagensControle.montarNovaCampanha}">
                                Novo
                            </a4j:commandLink>
                            <h:outputText value="    "/>
                            <a4j:commandLink reRender="@form,panelGeral,listaCampanha,tblComPontos"
                                             id="salvar"
                                             styleClass="pure-button pure-button-primary"
                                             accesskey="2"
                                             action="#{ClubeVantagensControle.gravar}"
                                             oncomplete="#{ClubeVantagensControle.mensagemNotificar};fireElementFromAnyParent('form:btnAtualizaCampanhaDuracao');">
                                Gravar
                            </a4j:commandLink>
                            <h:outputText value="    "/>
                            <a4j:commandLink reRender="@form,mdlAvisoExcluirBrinde,listaCampanha,tblComPontos"
                                             styleClass="pure-button"
                                             accesskey="2"
                                             id="excluir"
                                             action="#{ClubeVantagensControle.validarDadosExclusao}"
                                             oncomplete="#{ClubeVantagensControle.oncomplete}">
                                Excluir
                            </a4j:commandLink>
                            <h:outputText value="    "/>
                            <a4j:commandLink
                                    action="#{ClubeVantagensControle.realizarConsultaLogObjetoSelecionado}"
                                    reRender="form"
                                    oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo');abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                    title="Visualizar Log"
                                    style="display: inline-block; padding: 8px 15px;"
                                    styleClass="pure-button">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
                                
    <rich:modalPanel id="mdlExcluirCampanha" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmar Exclus�o Campanha"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                
                <h:panelGroup layout="block">
                    <a4j:commandLink value="Sim" action="#{ClubeVantagensControle.excluirCampanha}" reRender="form"
                                       id="confirmacaoOpercaoSim"
                                       oncomplete="#{ClubeVantagensControle.mensagemNotificar};#{ClubeVantagensControle.oncomplete};fireElementFromAnyParent('form:btnAtualizaCampanhaDuracao');"
                                       styleClass="pure-button pure-button-primary"/>
                    <h:outputText value="    "/>
                    <a4j:commandLink value="N�o" onclick="Richfaces.hideModalPanel('mdlExcluirCampanha');" reRender="mdlExcluirCampanha" id="confirmacaoOpercaoNao" styleClass="pure-button"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>

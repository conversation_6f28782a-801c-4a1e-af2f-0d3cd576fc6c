<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<script src="beta/js/jquery.js"></script>
<script src="script/jquery.flip.min.js"></script>
<script type="text/javascript">
    jQuery.noConflict();

    function mostrarBackVendas() {
        jQuery('.cardVendas').addClass('flippedVendas');
    }
</script>

<style type="text/css">

    .flipVendas {
        -webkit-perspective: 800;
        -ms-perspective: 800;
        -moz-perspective: 800;
        -o-perspective: 800;
        height: 450px;
        position: relative;
    }

    .flipVendas .cardVendas.flippedVendas {
        transform: rotatey(-180deg);
        -ms-transform: rotatey(-180deg); /* IE 9 */
        -moz-transform: rotatey(-180deg); /* Firefox */
        -webkit-transform: rotatey(-180deg); /* Safari and Chrome */
        -o-transform: rotatey(-180deg); /* Opera */
    }

    .flipVendas .cardVendas {
        padding: 0;
        border: 1px solid black !important;
        width: 800px;
        height: 450px;
        margin: 0 auto;
        -webkit-transform-style: preserve-3d;
        -webkit-transition: 2.5s -moz-transform-style : preserve-3d;
        -moz-transition: 2.5s -ms-transform-style : preserve-3d;
        -ms-transition: 2.5s -o-transform-style : preserve-3d;
        -o-transition: 2.5s transform-style : preserve-3d;
        transition: 2.5s
    }

    .flipVendas .cardVendas .faceVendas {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 2;
        -webkit-backface-visibility: hidden; /* Safari & Chrome */
        -moz-backface-visibility: hidden; /* Firefox */
        -ms-backface-visibility: hidden; /* Internet Explorer */
        -o-backface-visibility: hidden; /* Opera */
    }

    .flipVendas .cardVendas .frontVendas {
        background: white;
        position: absolute;
        z-index: 1;
    }

    .flipVendas .cardVendas .backVendas {
        background: white;
        transform: rotatey(-180deg);
        -ms-transform: rotatey(-180deg); /* IE 9 */
        -moz-transform: rotatey(-180deg); /* Firefox */
        -webkit-transform: rotatey(-180deg); /* Safari and Chrome */
        -o-transform: rotatey(-180deg); /* Opera */
    }

</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_acesso_autorizacao}"/>
    </title>

    <c:set var="titulo" scope="session" value=" ${msg_aplic.prt_sorteio_titulo}"/>
    <c:set var="urlWiki" scope="session"  value="semWiki"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topo_reduzido_popUp.jsp"/>
        </f:facet>

        <a4j:form id="form">
            <h:panelGrid columns="1" width="100%">

                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">

                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <a4j:commandLink id="btnConfig"
                                         styleClass="pure-button pure-button-small"
                                         action="consultar"
                                         accesskey="1">
                            <i class="fa-icon-list"></i> &nbsp ${msg_aplic.prt_sorteio_ultimosSorteios}
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>

                <rich:spacer height="10px"/>

                <h:panelGrid columnClasses="colunaCentralizada" width="100%">
                    <h:panelGroup layout="block" styleClass="flipVendas">
                        <h:panelGroup layout="block" styleClass="cardVendas">
                            <h:panelGroup layout="block" styleClass="faceVendas frontVendas">

                                <h:panelGrid columns="1">
                                    <h:outputText value="Sorteio está realizado!"/>
                                    <h:outputText value="Chegou a hora de revelar o vencedor..."/>
                                    <h:outputText value="Preparados?"/>
                                </h:panelGrid>

                                <a4j:commandLink id="btnRevelar"
                                                 status="false"
                                                 oncomplete="mostrarBackVendas();"
                                                 styleClass="pure-button pure-button-primary pure-button-small margin-h-10"
                                                 reRender="biVendas"
                                                 action="novo"
                                                 accesskey="1">
                                    <i class="fa-icon-random"></i> &nbsp Revelar Ganhador
                                </a4j:commandLink>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="faceVendas backVendas">
                                <h:panelGrid id="pnlSorteio" columns="2" width="100%" columnClasses="w30,w70"
                                             style="position: relative; margin-top: 15px">
                                    <h:panelGroup layout="block" style="width: 100%">

                                        <h:panelGroup layout="block" id="panelFoto"
                                                      style="width: 150px; margin: 0 auto">
                                            <a4j:mediaOutput rendered="#{!SuperControle.fotosNaNuvem}"
                                                             element="img" id="imagem1"
                                                             style="width:150px;height:180px "
                                                             cacheable="false" session="false"
                                                             createContent="#{ClienteControle.paintFoto}"
                                                             value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                                <f:param name="largura" value="150"/>
                                                <f:param name="altura" value="180"/>
                                            </a4j:mediaOutput>
                                            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                            width="150" height="180"
                                                            style="width:150px;height:180px"
                                                            url="#{SorteioControle.paintFotoDaNuvem}"/>
                                        </h:panelGroup>

                                    </h:panelGroup>
                                    <h:panelGroup layout="block" style="padding-right: 15px">

                                        <h:outputText
                                                value="#{SorteioControle.clienteVO.matricula} - #{SorteioControle.clienteVO.nome_Apresentar}"/>

                                        <rich:dataTable id="parcelasCliente" style="margin-top: 10px" width="100%"
                                                        value="#{SorteioControle.parcelaVOs}" var="parcela" rows="10">
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Código"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.codigo}"/>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Contrato"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.contrato_Apresentar}"/>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Descrição"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.descricao}"/>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Vencimento"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.dataVencimento_Apresentar}"/>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Situação"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.situacao_Apresentar}"/>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Valor"/>
                                                </f:facet>
                                                <h:outputText value="#{parcela.valorParcela_Apresentar}"/>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Opções"/>
                                                </f:facet>
                                                <%--<a4j:commandLink--%>
                                                        <%--rendered="#{(parcela.situacao eq 'EA') and (!SorteioControle.sorteioValidado)}"--%>
                                                        <%--actionListener="#{SorteioControle.validarResultado}"--%>
                                                        <%--reRender="pnlMensagem,pnlSorteio"--%>
                                                        <%--value="Validar Resultado"/>--%>

                                                <h:selectBooleanCheckbox rendered="#{(parcela.situacao eq 'EA') and (!SorteioControle.sorteioValidado)}"
                                                        value="#{parcela.parcelaEscolhida}"/>
                                            </rich:column>
                                        </rich:dataTable>
                                        <rich:datascroller align="center" for="parcelasCliente" maxPages="5"
                                                           id="scParcelasCliente"/>
                                    </h:panelGroup>
                                </h:panelGrid>

                                <h:panelGrid id="pnlMensagem" width="100%" columns="1" styleClass="centralizada"
                                             style="text-align: center; margin-top: 12px">

                                    <a4j:commandLink
                                            styleClass="pure-button pure-button-primary pure-button-small margin-h-10"
                                            rendered="#{!SorteioControle.sorteioValidado}"
                                            action="#{SorteioControle.validarResultado}"
                                            reRender="pnlMensagem,pnlSorteio"
                                            value="Validar Resultado"/>

                                    <h:outputText rendered="#{SorteioControle.sorteioValidado}"
                                                  value="Parabéns ao nosso ganhador!"/>
                                </h:panelGrid>

                            </h:panelGroup>


                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>

            </h:panelGrid>
        </a4j:form>
    </h:panelGrid>
</f:view>
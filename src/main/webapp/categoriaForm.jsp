<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Categoria_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Categoria_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros_Auxiliares:Categoria_de_Clientes"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_codigo}" />

                    <h:panelGroup>
                        <f:verbatim>
                            <h:outputText value="                      "/> 
                        </f:verbatim>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{CategoriaControle.categoriaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_nome}" />
                    <h:panelGroup>
                        <h:inputText  id="nome" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CategoriaControle.categoriaVO.nome}" />
                        <h:message for="nome" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_tipoCategoria}" />                    
                    <h:selectOneMenu  id="tipoCategoria" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CategoriaControle.categoriaVO.tipoCategoria}" >

                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <f:selectItems  value="#{CategoriaControle.listaSelectItemTipoCategoriaCategoria}" />
                        </c:if>
                        <c:if test="${modulo eq 'centralEventos'}">
                            <f:selectItems  value="#{CategoriaControle.listaSelectItemTipoCategoriaCategoriaCE}" /></c:if>
                    </h:selectOneMenu>

                    <h:outputText styleClass="tituloCampos" value="Nome externo:" title="Nome para integração de alguns sistemas"/>
                    <h:inputText id="nomeExterno" size="50" maxlength="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 title="Nome para integração de alguns sistemas"
                                 value="#{CategoriaControle.categoriaVO.nomeExterno}"/>
                    <%--<h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_nrConvitePermitido}" />
                    <h:inputText  id="nrConvitePermitido" onkeypress="return Tecla(event);" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{CategoriaControle.categoriaVO.nrConvitePermitido}" />--%>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{CategoriaControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{CategoriaControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgCategoria" styleClass="mensagem"  value="#{CategoriaControle.mensagem}"/>
                            <h:outputText id="msgCategoriaDet" styleClass="mensagemDetalhada" value="#{CategoriaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{CategoriaControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <h:commandButton id="salvar" action="#{CategoriaControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>

                                <h:panelGroup id="grupoBtnExcluir">
                                    <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                       oncomplete="#{CategoriaControle.msgAlert}" action="#{CategoriaControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                                       alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec" style="border: 0px" />
                                </h:panelGroup>

                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <h:commandButton id="consultar" immediate="true" action="#{CategoriaControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4"    styleClass="botoes nvoBt btSec"/>
                                <rich:spacer width="15px"/>
                                <a4j:commandLink  action="#{CategoriaControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                                    oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                    style="display: inline-block; padding: 8px 15px;"
                                                    styleClass="botoes nvoBt btSec"  title="Visualizar Log">
                                    <i style="text-decoration: none" class="fa-icon-list"/>
                                </a4j:commandLink>


                            </h:panelGroup>
                        </c:if>

                        <!-- ------------------CE------------------------------ -->
                        <c:if test="${modulo eq 'centralEventos'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{CategoriaControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="salvar"
                                                 action="#{CategoriaControle.gravarCE}"
                                                 value="#{msg_bt.btn_gravar}"
                                                 alt="#{msg.msg_gravar_dados}"
                                                 accesskey="2"
                                                 styleClass="botoes nvoBt"
                                                 actionListener="#{CategoriaControle.autorizacao}">
                                    <!-- Entidade.categoria -->
                                    <f:attribute name="entidade" value="110" />
                                    <!-- operacao.gravar -->
                                    <f:attribute name="operacao" value="G" />
                                </a4j:commandButton>


                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="excluir"
                                                 onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                 action="#{CategoriaControle.excluirCE}" value="#{msg_bt.btn_excluir}"
                                                 alt="#{msg.msg_excluir_dados}"
                                                 accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                                                 actionListener="#{CategoriaControle.autorizacao}">
                                    <!-- Entidade.categoria -->
                                    <f:attribute name="entidade" value="110" />
                                    <!-- operacao.excluir -->
                                    <f:attribute name="operacao" value="E" />
                                </a4j:commandButton>

                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="consultar"
                                                 immediate="true"
                                                 action="#{CategoriaControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_consultar}"
                                                 alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"
                                                 actionListener="#{CategoriaControle.autorizacao}">
                                    <!-- Entidade.categoria -->
                                    <f:attribute name="entidade" value="110" />
                                    <!-- operacao.consultar -->
                                    <f:attribute name="operacao" value="C" />
                                </a4j:commandButton>
                            </h:panelGroup>
                        </c:if>

                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>

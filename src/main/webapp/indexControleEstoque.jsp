<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<jsp:include page="include_head.jsp" flush="true"/>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box form-flat">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Controle de Estoque" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlWiki}"
                                                      title="Clique e saiba mais: Controle de Estoque"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" columnClasses="w100" width="100%" cellpadding="0"
                                                 cellspacing="0">
                                        <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%"
                                                     cellpadding="0" cellspacing="0">
                                            <h:panelGrid columns="1" style="height:1000%" width="100%" cellpadding="5"
                                                         cellspacing="5">
                                                <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.cadastrarBalanco or LoginControle.permissaoAcessoMenuVO.cancelarBalanco}">
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoBalanco"
                                                                         styleClass="tituloCampos"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                        >
                                                            <h:outputText value="Balanço"/>
                                                            <f:attribute name="funcionalidade" value="BALANCO"/>
                                                        </a4j:commandLink>
                                                    </div>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="O Zillyon Web trabalha com o conceito de multi-empresa, onde através de apenas um sistema será possível controlar o estoque de várias filiais de uma mesma franquia. Cadastre nesta tela os balanços para ajustar o estoque de produtos."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.cadastrarCompra or LoginControle.permissaoAcessoMenuVO.cancelarCompra}">
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="compra"
                                                                         styleClass="tituloCampos"
                                                                         action="#{CompraControle.abrirTelaCompra}"
                                                                         oncomplete="#{CompraControle.msgAlert}">
                                                            <h:outputText value="Compra"/>
                                                        </a4j:commandLink>
                                                    </div>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Cadastre nesta tela as compras dos produtos do tipo 'estoque'."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarCardex}">
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/><a4j:commandLink id="cardex"
                                                                                                   styleClass="tituloCampos"
                                                                                                   action="#{CardexControle.abrirTelaCardex}"
                                                                                                   oncomplete="#{CardexControle.msgAlert}">
                                                        <h:outputText value="Cardex"/>
                                                    </a4j:commandLink></div>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Nesta tela será possível consultar todas as movimentações do produto que originaram o estoque."/>
                                                </h:panelGroup>

                                            </h:panelGrid>

                                            <h:panelGrid style="height:100%" columns="1" width="100%" cellpadding="5"
                                                         cellspacing="5">
                                                <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.configurarProdutoEstoque}">

                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/><a4j:commandLink  styleClass="tituloCampos"
                                                            id="configurarProdutoEstoque"

                                                            oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                            actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}">
                                                        <f:attribute name="funcionalidade" value="CONFIGURAR_PRODUTO_ESTOQUE"/>
                                                        <h:outputText value="Configurar Produto Estoque"/>
                                                    </a4j:commandLink></div>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Esta tela é utilizada para definir os produtos que terão controle de estoque. "/>

                                                </h:panelGroup>


                                                <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarPosicaoEstoque}">
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="posicaoEstoque"  styleClass="tituloCampos"
                                                                                                   action="#{RelatorioEstoqueProdutoControle.abrirTelaPosicaoEstoque}"
                                                                                                   oncomplete="#{RelatorioEstoqueProdutoControle.msgAlert}">
                                                        <h:outputText value="Posição do Estoque"/>
                                                    </a4j:commandLink></div>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Esta tela é utilizada para informar a posição do estoque atual e estoque mínimo.   "/>

                                                </h:panelGroup>

                                            </h:panelGrid>
                                        </h:panelGrid>

                                    </h:panelGrid>


                                </h:panelGroup>
                            </h:panelGroup>


                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-CADASTROS_CONTROLE_ESTOQUE" />
                        </jsp:include>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>

        </h:panelGroup>


    </h:form>
</f:view>

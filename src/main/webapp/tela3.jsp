<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<c:if test="${empty contexto}">
    <jsp:include page="include_head.jsp" flush="true"/>
</c:if>
<c:if test="${not empty contexto}">
    <%@include file="pages/ce/includes/include_head.jsp" %>
</c:if>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:keepAlive beanName="ConsultaClienteControle"/>
    <title>
        <h:outputText value="Clientes"/>
    </title>
    <h:form id="form">
        <html>

        <body>
        <table width="100%" border="0" cellpadding="0" cellspacing="0">

            <c:if test="${empty contexto}">
                <tr>
                <tr>
                <td height="77" align="left" valign="top" class="bgtop topoZW">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                    <link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>
                </td>
                </tr>
                    <rich:jQuery selector=".item4" query="addClass('menuItemAtual')"/>
                </tr>
            </c:if>
            <c:if test="${not empty contexto}">
                <%@include file="pages/ce/includes/include_topo.jsp" %>
            </c:if>
                
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="180" align="left" valign="top" class="bglateraltop"
                                style="padding: 0 !important;">
                                <c:if test="${empty contexto}">
                                    <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                                </c:if>
                                <c:if test="${not empty contexto}">
                                    <%@include file="pages/ce/includes/include_box_menulateral.jsp" %>
                                </c:if>
                            </td>
                            <td align="center" valign="top">
                                <jsp:include page="/includes/cliente/include_box_pesquisa_clientes.jsp" flush="true"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td height="93" align="left" valign="top" class="bgrodape">
                    <jsp:include page="include_rodape_flat.jsp" flush="true" />
                </td>
            </tr>
        </table>
        </body>
        </html>
    </h:form>
              
        <%@include file="/include_load_configs.jsp" %>
</f:view>
<script type="text/javascript">
    document.getElementById("form:valorConsulta").focus();
</script>

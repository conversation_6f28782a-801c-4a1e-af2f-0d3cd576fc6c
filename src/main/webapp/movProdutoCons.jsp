<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="script/jquery.maskedinput-1.7.6.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<style>
    .dataTables_filter {
        height: auto;
        margin-top: -90px;
    }
    .titulo-topo {
        margin-bottom: 0;
    }
</style>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_MovProduto_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_MovProduto_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-movimento-do-produto/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
            <h:inputHidden id="filtroData" value="#{MovProdutoControle.filtroData}" />

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{MovProdutoControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,contrato_Apresentar=Contrato,responsavel_Apresentar=Responsável,produto_Apresentar=Produto,pessoa_Apresentar=Cliente,empresa_Apresentar=Empresa"/>
                                <f:attribute name="prefixo" value="MovimentoProduto"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{MovProdutoControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,contrato_Apresentar=Contrato,responsavel_Apresentar=Responsável,produto_Apresentar=Produto,pessoa_Apresentar=Cliente,empresa_Apresentar=Empresa"/>
                                <f:attribute name="prefixo" value="MovimentoProduto"/>
                                <f:attribute name="titulo" value="Movimento Produto"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1 margin-v-10" style="margin-left: 46px;margin-right:46px;width: calc(100% - 92px);">
                    <h:panelGroup layout="block" style="width: 100%;">
                        <h:panelGroup layout="block" style="float: left;">
                            <h:panelGroup layout="block">
                                <h:outputText value="Período: "/>
                                <rich:calendar id="dtInicio" value="#{MovProdutoControle.dtInicio}"
                                               inputSize="10"
                                               showWeekDaysBar="false"
                                               inputClass="form"
                                               showWeeksBar="false"
                                               oninputfocus="focusinput(this);"
                                               oninputblur="blurinput(this);"
                                               enableManualInput="true"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block">
                                <h:outputText value=" até "/>
                                <rich:calendar id="dtFim" value="#{MovProdutoControle.dtFim}"
                                               inputSize="10"
                                               showWeekDaysBar="false"
                                               inputClass="form"
                                               showWeeksBar="false"
                                               oninputfocus="focusinput(this);"
                                               oninputblur="blurinput(this);"
                                               enableManualInput="true"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <a4j:commandButton id="limparPeriodoEmissao"
                                                   onclick="document.getElementById('form:dtInicioInputDate').value = '';
                                                       document.getElementById('form:dtFimInputDate').value='';"
                                                   image="/images/limpar.gif" title="Limpar data de Emissao."
                                                   status="false"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block">
                                <a4j:commandLink styleClass="pure-button pure-button-primary margin-h-10 pull-right"
                                                 value="Filtrar" oncomplete="recarregarTabela()"/>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblMovProduto" class="tabelaMovProduto pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_contrato_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_responsavel_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_produto_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_cliente_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_empresa_maiusculo}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{MovProdutoControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{MovProdutoControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{MovProdutoControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty MovProdutoControle.mensagem}"
                              value=" #{MovProdutoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              id="msgDetalhada"
                              rendered="#{not empty MovProdutoControle.mensagemDetalhada}"
                              value=" #{MovProdutoControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>


    </h:panelGroup>

    <script src="beta/js/dt-server.js" type="text/javascript"></script>

    <script>

        function recarregarTabela() {
            var dtInicio = document.getElementById("form:dtInicioInputDate").value;
            var dtFim = document.getElementById("form:dtFimInputDate").value;

            if((dtInicio != "" && dtInicio != "__/__/____") && (dtFim != "" && dtFim != "__/__/____")){
                var diaInicio = dtInicio.substring(0,2);
                var mesInicio = dtInicio.substring(3,5);
                var anoInicio = dtInicio.substring(6,10);

                var diaFim = dtFim.substring(0,2);
                var mesFim = dtFim.substring(3,5);
                var anoFim = dtFim.substring(6,10);

                var dtInicioAux = new Date(mesInicio + "/" + diaInicio + "/" + anoInicio);
                var dtFimAux = new Date(mesFim + "/" + diaFim + "/" + anoFim);

                var difference= Math.abs(dtFimAux - dtInicioAux);
                var days = difference/(1000 * 3600 * 24);
                if(days > 90){
                    document.getElementById("form:btnExcel").style.display  = "none";
                    document.getElementById("form:btnPDF").style.display  = "none";
                    Notifier.error("O campo período informado não pode ser maior que 3 meses", "Data incorreta!");
                } else {
                    var configs = tabelaAtual.dataTableSettings[0];
                    var sEcho = configs.iDraw;
                    var iDisplayStart = configs._iDisplayStart;
                    var iDisplayLength = configs._iDisplayLength;
                    var sSearch = jQuery(".filtroDT").val().toLowerCase();
                    var iSortCol_0 = configs.aaSorting[0][0];
                    var sSortDir_0 = configs.aaSorting[0][1];

                    var data = {
                        "dtInicio": dtInicio, "dtFim": dtFim,
                        "sEcho": sEcho, "iDisplayStart": iDisplayStart,
                        "iDisplayLength": iDisplayLength, "sSearch": sSearch,
                        "iSortCol_0": iSortCol_0, "sSortDir_0": sSortDir_0
                    };
                    document.getElementById("form:filtroData").value = sSearch;
                    tabelaAtual.dataTable().fnDestroy(0);
                    iniTblServer("tabelaMovProduto", "${contexto}/prest/contrato/movProduto", data);
                    document.getElementById("form:btnExcel").style.display  = "inline";
                    document.getElementById("form:btnPDF").style.display  = "inline";
                }
            } else {
                document.getElementById("form:btnExcel").style.display  = "none";
                document.getElementById("form:btnPDF").style.display  = "none";
                Notifier.error("Informe uma data para emitir o relatório", "Formato ou data inválida!");
            }
        }

        jQuery(window).on("load", function () {
            var date = new Date();
            var dtInicioAux = new Date(date.getFullYear(), date.getMonth(), 1);
            var dtFimAux = new Date(date.getFullYear(), date.getMonth() + 1, 0);
            var dtInicio = dtInicioAux.toLocaleDateString('pt-BR', {timeZone: 'UTC'});
            var dtFim = dtFimAux.toLocaleDateString('pt-BR', {timeZone: 'UTC'});
            iniTblServer("tabelaMovProduto", "${contexto}/prest/contrato/movProduto?dtInicio=" + dtInicio + "&dtFim=" + dtFim);
        });

    </script>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>
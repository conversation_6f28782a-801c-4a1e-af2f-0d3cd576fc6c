<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_Balanco_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Balanco_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Controle_de_Estoque:Balan%C3%A7o"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                            <a4j:commandLink id="ImprimirFormulario"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             styleClass="exportadores"
                                             title="Imprimir Formulário"
                                             reRender="panelErroImp, formConfereBalanco"
                                             action="#{RelatorioBalancoControle.limparMensagens}"
                                             oncomplete="Richfaces.showModalPanel('panelConferirBalanco')">
                                <i class="fa-icon-print"></i>
                            </a4j:commandLink>
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{BalancoControle.exportar}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,dataCadastro_Apresentar=Data,situacao_Apresentar=Situação,empresa_Apresentar=Empresa,descricao=Descrição"/>
                                <f:attribute name="prefixo" value="Balanco"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{BalancoControle.exportar}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,dataCadastro_Apresentar=Data,situacao_Apresentar=Situação,empresa_Apresentar=Empresa,descricao=Descrição"/>
                                <f:attribute name="prefixo" value="Balanco"/>
                                <f:attribute name="titulo" value="Balanço"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>

                             <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 action="#{BalancoControle.realizarConsultaLogObjetoGeral}"
                                                        oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{BalancoControle.novo}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblBalanco" class="tabelaBalanco pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_data_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_situacao_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_empresa_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_descricao_maiusculo}</th>
                </thead>
                <tbody></tbody>
            </table>

            <a4j:jsFunction name="jsEditar" action="#{BalancoControle.editar}" reRender="mensagem"/>

        </h:panelGroup>
        <%-- FIM CONTENT --%>

        <%-- INICIO FOOTER --%>
        <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
            <h:graphicImage id="iconSucesso" rendered="#{BalancoControle.sucesso}" value="./imagens/sucesso.png" />
            <h:graphicImage id="iconErro" rendered="#{BalancoControle.erro}" value="./imagens/erro.png" />

            <h:outputText styleClass="mensagem" rendered="#{not empty BalancoControle.mensagem}" value=" #{BalancoControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty BalancoControle.mensagemDetalhada}" value=" #{BalancoControle.mensagemDetalhada}"/>
        </h:panelGroup>
        <%-- FIM FOOTER --%>
    </h:form>


    <rich:modalPanel id="panelConferirBalanco" autosized="false" shadowOpacity="true" width="450" height="200">
        <f:facet name="header" >
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Informe a Empresa que será realizado o Balanço"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink1" />
                <rich:componentControl for="panelConferirBalanco" attachTo="hidelink1" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConfereBalanco" ajaxSubmit="true" prependId="false">
            <h:panelGrid columns="1" width="100%" bgcolor="#EEEEEE" >
                <h:panelGroup  >
                    <h:outputText style="padding-right:5px; vertical-align:middle; width:10px "  styleClass="tituloCampos" value="Empresa:" />

                    <h:selectOneMenu  id="ConfBalanco_empresa" onblur="blurinput(this);"
                                      disabled="#{!RelatorioBalancoControle.usuarioLogado.administrador}"
                                      onfocus="focusinput(this);"
                                      styleClass="form"
                                      value="#{RelatorioBalancoControle.codigoEmpresa}" >
                        <f:selectItems  value="#{RelatorioBalancoControle.listaSelectItemEmpresa}" />
                    </h:selectOneMenu>

                    <a4j:commandButton style="vertical-align:middle;" id="atualizar_ConfBalanco_empresa" action="#{BalancoControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:ConfBalanco_empresa"/>
                </h:panelGroup>

                <h:panelGroup  >
                    <h:outputText  style="padding-right:5px; vertical-align: middle; width:10px"  styleClass="tituloCampos" value="Categoria Produto:" />

                    <h:selectOneMenu  id="comboCategoria" onblur="blurinput(this);"
                                      onfocus="focusinput(this);"
                                      style = "vertical-align: middle; width: 150px;"
                                      styleClass="tituloCampos"
                                      value="#{RelatorioBalancoControle.codigoCategoria}" >
                        <f:selectItems  value="#{RelatorioBalancoControle.listaSelectItemCategoria}" />
                    </h:selectOneMenu>

                    <a4j:commandButton id="imprimirConfBalanco2"
                                       style="vertical-align:middle; padding-left:15px;"
                                       action="#{RelatorioBalancoControle.imprimirFormularioConfereBalancoPDF}"
                                       oncomplete="#{!RelatorioBalancoControle.erro} ? abrirPopupPDFImpressao('relatorio/#{RelatorioBalancoControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595) : '' ;"
                                       reRender="panelErroImp"
                                       image="/imagens/botaoOk.png"   />
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid id="panelErroImp" columns="1" width="100%" bgcolor="#EEEEEE" columnClasses="">
                <h:outputText styleClass="mensagem"  value="#{RelatorioBalancoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{RelatorioBalancoControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

</h:panelGroup>

<script src="beta/js/ext-funcs.js" type="text/javascript"></script>

<script>
    jQuery(window).on("load", function(){
        iniciarTabela("tabelaBalanco", "${contexto}/prest/estoque/balanco", 0, "desc", 1, true);
    });
</script>

</f:view>
<%--
    Author             : <PERSON>lis<PERSON>
    Data               : 19/01/2011
    Objetivo da Tela   : Cadastrar os coletores do Local
    Onde a tela � usada: LocalAcessoForm.jsp
--%>

<%@include file="includes/imports.jsp" %>

<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

    <h:panelGrid id="panelTopColetor" columns="3" width="100%" columnClasses="colunaEsquerda" styleClass="tabMensagens">
        <a4j:commandButton id="btnAdicColetor"
                           rendered="true"
                           action="#{LocalAcessoControle.adicionarColetor}"
                           title="#{msg_bt.btn_novoColetor}"
                           value="Novo"
                           ajaxSingle="true"
                           process="panelEdicaoColetor"
                           reRender="panelEdicaoColetor, panelListaColetor, form:mensagem, form:mensagemDetalhada, panelTopColetor"
                           styleClass="botoes nvoBt btSec"/>


        <a4j:commandLink id="btnCopiarColetor"
                         value="Copiar Informa��es de outro Coletor"
                         rendered="#{LocalAcessoControle.mostrarEsconderFormColetor}"
                         action="#{LocalAcessoControle.copiarColetor}"
                         ajaxSingle="true"
                         reRender="richModalColetor"
                         styleClass="botoes"/>

        <h:panelGroup rendered="#{LocalAcessoControle.mostrarEsconderFormColetor}">
            <h:outputText value="Coletor Selecionado: "/>
            <h:outputText value="#{LocalAcessoControle.coletor.descricao}"/>
        </h:panelGroup>
    </h:panelGrid>

    <h:panelGrid  id="panelListaColetor" columns="1" width="100%">
        <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Coletor_tituloForm}"/>
        </h:panelGrid>

        <h:dataTable id="coletorVO" width="100%" headerClass="subordinado"
                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                     value="#{LocalAcessoControle.localAcesso.listaColetores}" var="coletor">
            <h:column>
                <f:facet name="header">
                    <h:outputText  value="#{msg_aplic.prt_Coletor_descricao}" />
                </f:facet>
                <h:outputText  value="#{coletor.descricao}" />
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText  value="#{msg_aplic.prt_Coletor_modelo}" />
                </f:facet>
                <h:outputText  value="#{coletor.modelo.descricao}" />
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText  value="#{msg_aplic.prt_Produto_desativado}" />
                </f:facet>
                <h:outputText  value="#{coletor.situacao_Apresentar}" />
            </h:column>

            <h:column>
                <f:facet name="header">
                    <h:outputText  value="#{msg_bt.btn_opcoes}" />
                </f:facet>
                <h:panelGroup>
                    <a4j:commandButton id="editarItemColetor"
                                       action="#{LocalAcessoControle.editarColetor}"
                                       value="#{msg_bt.btn_editar}"
                                       title="#{msg_bt.btn_editar}"
                                       image="./imagens/botaoEditar.png"
                                       accesskey="6"
                                       styleClass="botoes"
                                       ajaxSingle="true"
                                       process="panelEdicaoColetor"
                                       reRender="panelEdicaoColetor, panelListaColetor, panelTopColetor"/>
                    <f:verbatim>
                        <h:outputText value="    "/>
                    </f:verbatim>

                    <a4j:commandButton id="excluirItemColetor"
                                       action="#{LocalAcessoControle.removerColetor}"
                                       value="#{msg_bt.btn_excluir}"
                                       title="#{msg_bt.btn_excluir}"
                                       image="./imagens/botaoRemover.png"
                                       styleClass="botoes"
                                       ajaxSingle="true"
                                       reRender="panelEdicaoColetor, panelListaColetor, panelTopColetor"
                                       />
                    <%--<a4j:commandButton id="setarValoresDefault"--%>
                                       <%--action="#{LocalAcessoControle.setarValoresDefaultColetor}"--%>
                                       <%--title="#{msg_aplic.prt_valorDefaultColetor}"--%>
                                       <%--ajaxSingle="true"--%>
                                       <%--reRender="panelEdicaoColetor,panelListaColetor"--%>
                                       <%--image="./imagens/btn_default.png"--%>
                                       <%--styleClass="botoes"/>--%>

                </h:panelGroup>
            </h:column>
        </h:dataTable>
    </h:panelGrid>

    <h:panelGroup id="panelEdicaoColetor" layout="block">
        <h:panelGrid rendered="#{LocalAcessoControle.mostrarEsconderFormColetor}"  id="panelColetorLocal" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
            <f:facet name="header">
            </f:facet>
            <rich:tabPanel>
                <rich:tab label="Dados b�sicos">
                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                        <h:outputText value="#{msg_aplic.prt_Produto_desativado}"/>
                        <h:selectBooleanCheckbox  styleClass="campos" value="#{LocalAcessoControle.coletor.desativado}"/>

                        <h:outputText    value="#{msg_aplic.prt_Coletor_numeroTerminal}" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:inputText  id="terminalColetor" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.numeroTerminal}" />
                        </h:panelGroup>

                        <c:if test="${LoginControle.usuarioLogado.administrador}">
                            <h:outputText value="#{msg_aplic.prt_Coletor_numeroTerminalAcionamento}" styleClass="tituloCampos"/>
                            <h:panelGroup>
                                <h:inputText id="numTerminalAcionamento" size="5" maxlength="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{LocalAcessoControle.coletor.numTerminalAcionamento}"/>
                            </h:panelGroup>
                        </c:if>

                        <h:outputText    value="#{msg_aplic.prt_Coletor_descricao}" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:inputText  id="descColetor" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.descricao}" />
                        </h:panelGroup>
                        <h:outputText    value="#{msg_aplic.prt_Coletor_sentidoAcesso}" styleClass="tituloCampos"/>
                        <h:selectOneMenu  id="sentido" styleClass="camposObrigatorios" value="#{LocalAcessoControle.coletor.sentidoAcesso}" >
                            <f:selectItems  value="#{LocalAcessoControle.listaSelectItemSentidoAcesso}" />
                        </h:selectOneMenu>

                        <h:outputText    value="#{msg_aplic.prt_Coletor_CodigoNFC}" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:inputText  id="codigoNFC" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.codigoNFC}" />
                        </h:panelGroup>
                    </h:panelGrid>
                </rich:tab>

                <rich:tab label="Hardware" id="tabHardware">
                    <h:panelGrid id="tabelaAtributos" columns="2" rowClasses="linhaImpar,linhaPar"
                                 cellpadding="0" cellspacing="2"
                                 columnClasses="classEsquerda, classDireita" width="100%">
                        <h:outputText    value="#{msg_aplic.prt_Coletor_modelo}" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:selectOneMenu id="modelo" style="vertical-align: top" styleClass="camposObrigatorios" value="#{LocalAcessoControle.coletor.modelo}" >
                                <f:selectItems  value="#{LocalAcessoControle.listaSelectItemModeloColetor}" />
                                <a4j:support event="onchange" ajaxSingle="true" action="#{LocalAcessoControle.setarValoresDefaultColetor}"
                                             reRender="tabelaAtributos" />
                            </h:selectOneMenu>
                            <a4j:commandButton id="setarValoresDefault"
                                               action="#{LocalAcessoControle.setarValoresDefaultColetor}"
                                               title="#{msg_aplic.prt_valorDefaultColetor}"
                                               ajaxSingle="true"
                                               reRender="panelEdicaoColetor,panelListaColetor"
                                               image="./imagens/btn_default.png"
                                               styleClass="botoes"/>

                            <a4j:commandButton id="setInverterGiro" style="vertical-align: top;"
                                               rendered="#{LocalAcessoControle.mostrarEsconderFormColetor}"
                                               action="#{LocalAcessoControle.inverterGiroCatraca}"
                                               value="#{msg_bt.btn_inverterGiroCatraca}"
                                               ajaxSingle="true"
                                               process="releEntrada, sensorEntrada, releSaida, sensorSaida"
                                               reRender="releEntrada, sensorEntrada, releSaida, sensorSaida"
                                               styleClass="botoes"/>
                        </h:panelGroup>

                        <h:outputText    value="#{msg_aplic.prt_Coletor_msgDisplay}" styleClass="tituloCampos" rendered="#{LocalAcessoControle.coletor.temMsgDisplay}"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.temMsgDisplay}">
                            <h:inputText  id="msgDisplay" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.msgDisplay}" />
                        </h:panelGroup>

                        <h:outputText rendered="#{LocalAcessoControle.coletor.temIpServidor}" value="#{msg_aplic.prt_Coletor_ipServidor}" styleClass="tituloCampos" />
                        <h:inputText rendered="#{LocalAcessoControle.coletor.temIpServidor}" id="ipServidor" size="15" maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.ipServidor}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.temPortaServidor}" value="#{msg_aplic.prt_Coletor_portaServidor}" styleClass="tituloCampos"/>
                        <h:inputText  rendered="#{LocalAcessoControle.coletor.temPortaServidor}" id="portaServidor" size="4" maxlength="4" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.portaServidor}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.temUsuario}" value="#{msg_aplic.prt_msg_usuario}" styleClass="tituloCampos" />
                        <h:inputText rendered="#{LocalAcessoControle.coletor.temUsuario}" id="usuario" size="15" maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.usuarioColetor}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.temSenha}" value="#{msg_aplic.SENHA}" styleClass="tituloCampos" />
                        <h:inputText rendered="#{LocalAcessoControle.coletor.temSenha}" id="senha" size="15" maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.senhaColetor}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.temIpPorta}" value="#{LocalAcessoControle.portaServidor ? msg_aplic.prt_Coletor_ip_servidor : msg_aplic.prt_Coletor_ip}" styleClass="tituloCampos" />
                        <h:inputText rendered="#{LocalAcessoControle.coletor.temIpPorta}" id="ipColetor" size="15" maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.ip}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.coletorZKTF1700}" value="#{msg_aplic.prt_Coletor_mascaraSubrede}" styleClass="tituloCampos" />
                        <h:inputText rendered="#{LocalAcessoControle.coletor.coletorZKTF1700}" id="mascaraSubrede" size="15" maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.mascaraSubrede}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.temIpPorta}" value="#{msg_aplic.prt_Coletor_porta}" styleClass="tituloCampos"/>
                        <h:inputText  rendered="#{LocalAcessoControle.coletor.temIpPorta}" id="portaColetor" size="4" maxlength="4" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.porta}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.validarMaxTemperatura}" value="Temperatura M�xima: " styleClass="tituloCampos"/>
                        <h:inputText  rendered="#{LocalAcessoControle.coletor.validarMaxTemperatura}" id="maxTemperatura" size="4" maxlength="6" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.maxTemperatura}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.validarUsoMascara}" value="Uso obrigat�rio de m�scara: " styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.validarUsoMascara}">
                            <h:selectBooleanCheckbox id="usoMascaraObrigatorio" value="#{LocalAcessoControle.coletor.usoMascaraObrigatorio}"/>
                        </h:panelGroup>

                        <h:panelGroup id="groupTxtPortaComunicacao" rendered="#{LocalAcessoControle.coletor.temPortaCOM}">
                            <h:outputText id="txtPortaComunicacao"
                                          value="#{msg_aplic.prt_Coletor_portaComunicacao}" styleClass="tituloCampos"/>
                        </h:panelGroup>
                        <h:panelGroup id="groupSelectPortaComunicacao" rendered="#{LocalAcessoControle.coletor.temPortaCOM}">
                            <h:selectOneMenu
                                             id="selectPortaComunicacao" value="#{LocalAcessoControle.coletor.portaComunicacao}" >
                                <f:selectItems  value="#{LocalAcessoControle.listaPortaCom}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:panelGroup id="groupTxtPortaParalela">
                            <h:outputText rendered="#{LocalAcessoControle.coletor.temPortaParalela}"
                                          id="txtPortaParalela" value="#{msg_aplic.prt_Coletor_portaParalela}"
                                          styleClass="tituloCampos"/>
                        </h:panelGroup>
                        <h:panelGroup id="groupInputPortaParalela">
                            <h:inputText  rendered="#{LocalAcessoControle.coletor.temPortaParalela}"
                                          id="inputPortaParalela" title="Informe o endere�o da Porta Paralela em Hexadecimal"
                                          size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                          styleClass="form" value="#{LocalAcessoControle.coletor.portaParalela}" />
                            <h:outputText rendered="#{LocalAcessoControle.coletor.temPortaParalela}"
                                          id="lblPortaParalela" styleClass="textsmall"
                                          value="informe '378' para LPT1 ou '278' para  LPT2"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="groupTxtLeitorSerial">
                            <h:outputText rendered="#{LocalAcessoControle.coletor.temLeitorSerial}"
                                          value="#{msg_aplic.prt_Coletor_portaLeitorSerial}" styleClass="tituloCampos"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" id="groupSelectLeitorSerial">
                            <h:selectOneMenu rendered="#{LocalAcessoControle.coletor.temLeitorSerial}"
                                             value="#{LocalAcessoControle.coletor.portaLeitorSerial}" >
                                <f:selectItems  value="#{LocalAcessoControle.listaPortaComSerial}" />
                            </h:selectOneMenu>
                        </h:panelGroup>


                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.temPortaCOM}">
                            <h:outputText value="#{msg_aplic.prt_Coletor_modoTransmissao}" styleClass="tituloCampos"/>
                        </h:panelGroup>

                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.temPortaCOM}">
                            <h:selectOneMenu  id="modoTR" styleClass="camposObrigatorios"
                                              value="#{LocalAcessoControle.coletor.modoTransmissao}" >
                                <f:selectItems  value="#{LocalAcessoControle.listaSelectItemModoTransmissao}" />
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <%-- 03/03/11 Ulisses... Campo comentado porque o mesmo n�o est� sendo utilizado no momento.
                                                 Caso haja necessidade de utiliz�-lo posteriormente, � s� descomentar o c�digo.
                        <h:outputText    value="#{msg_aplic.prt_Coletor_velocTransmissao}" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:inputText  id="velTRColetor" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.velocTransmissao}" />
                        </h:panelGroup>
                        --%>
                        <c:if test="${LocalAcessoControle.coletor.temReleEntrada}">
                            <h:outputText  rendered="#{!LocalAcessoControle.coletor.coletorZKTF1700}" value="#{msg_aplic.prt_Coletor_releEntrada}" styleClass="tituloCampos"/>
                            <h:outputText  rendered="#{LocalAcessoControle.coletor.coletorZKTF1700}"  value="#{msg_aplic.prt_Coletor_rele}" styleClass="tituloCampos"/>

                            <h:panelGroup>
                                <h:selectOneMenu  id="releEntrada" styleClass="camposObrigatorios"
                                                  value="#{LocalAcessoControle.coletor.releEntrada}" >
                                    <f:selectItems  value="#{LocalAcessoControle.itemsReleEntrada}" />
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </c:if>
                        <h:outputText rendered="#{LocalAcessoControle.coletor.temSensorEntrada}"
                                      value="#{msg_aplic.prt_Coletor_sensorEntrada}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.temSensorEntrada}">
                            <h:selectOneMenu  id="sensorEntrada" styleClass="camposObrigatorios"
                                              value="#{LocalAcessoControle.coletor.sensorEntrada}" >
                                <f:selectItems  value="#{LocalAcessoControle.itemsSensorEntrada}" />
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <h:outputText  rendered="#{LocalAcessoControle.coletor.usaTempoAcionamentoGenerico}"   value="#{msg_aplic.prt_Coletor_tempoRele}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.usaTempoAcionamentoGenerico}">
                            <h:inputText  id="tempoEntGenerico" size="5" maxlength="5"
                                          onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          styleClass="form"
                                          value="#{LocalAcessoControle.coletor.tempoReleEntrada}" />
                        </h:panelGroup>
                        <h:outputText  rendered="#{LocalAcessoControle.coletor.usaTempoAcionamentoEntrada}"   value="#{msg_aplic.prt_Coletor_tempoReleEntrada}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.usaTempoAcionamentoEntrada}">
                            <h:inputText  id="tempoEntColetor" size="5" maxlength="5"
                                          onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          styleClass="form"
                                          value="#{LocalAcessoControle.coletor.tempoReleEntrada}" />
                        </h:panelGroup>
                        <h:outputText  rendered="#{LocalAcessoControle.coletor.temReleSaida}"  value="#{msg_aplic.prt_Coletor_releSaida}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.temReleSaida}">
                            <h:selectOneMenu  id="releSaida" styleClass="camposObrigatorios"
                                              value="#{LocalAcessoControle.coletor.releSaida}" >
                                <f:selectItems  value="#{LocalAcessoControle.itemsReleSaida}" />
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <h:outputText rendered="#{LocalAcessoControle.coletor.temSensorSaida}"
                                      value="#{msg_aplic.prt_Coletor_sensorSaida}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.temSensorSaida}">
                            <h:selectOneMenu  id="sensorSaida" styleClass="camposObrigatorios"
                                              value="#{LocalAcessoControle.coletor.sensorSaida}" >
                                <f:selectItems  value="#{LocalAcessoControle.itemsSensorSaida}" />
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <h:outputText rendered="#{LocalAcessoControle.coletor.usaTempoAcionamentoSaida}"
                                       value="#{msg_aplic.prt_Coletor_tempoReleSaida}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.usaTempoAcionamentoSaida}">
                        <h:inputText  id="tempoSaidaColetor" size="5" maxlength="5"
                                          onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          styleClass="form"
                                          value="#{LocalAcessoControle.coletor.tempoReleSaida}" />
                        </h:panelGroup>

                        <h:panelGroup id="groupTxtPortaGertec" rendered="#{LocalAcessoControle.coletor.usaLeitorGertec}" >
                            <h:outputText id="txtPortaGertec"
                                          value="#{msg_aplic.prt_Coletor_tecladoGertec}" styleClass="tituloCampos"/>
                        </h:panelGroup>
                        <h:panelGroup id="groupSelectPortaGertec" rendered="#{LocalAcessoControle.coletor.usaLeitorGertec}">
                            <h:selectOneMenu
                                    id="selectPortaGertec" value="#{LocalAcessoControle.coletor.leitorGertec}" >
                                <f:selectItems  value="#{LocalAcessoControle.listaPortaSerial}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:panelGroup id="groupTxtDispAlternativo" rendered="#{LocalAcessoControle.coletor.temDispAlternativo}">
                            <h:outputText id="txtDispAlternativo"
                                          value="#{msg_aplic.prt_Coletor_DispAlternativo}" styleClass="tituloCampos"/>
                        </h:panelGroup>

                        <h:panelGroup id="groupSelectDispAlternativo" rendered="#{LocalAcessoControle.coletor.temDispAlternativo}">
                            <h:selectOneMenu id="selectDispAlternativo" value="#{LocalAcessoControle.coletor.dispAlternativo}">
                                <f:selectItems  value="#{LocalAcessoControle.listaSelectItemDispAlternativos}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:outputText id="txtDigitosCartao"
                                      rendered="#{LocalAcessoControle.coletor.temDigitosLeituraCartao}"
                                      value="#{msg_aplic.prt_Coletor_DigitosCartao}" styleClass="tituloCampos"/>

                        <h:inputText rendered="#{LocalAcessoControle.coletor.temDigitosLeituraCartao}"
                                     title="Informe a quantidade de d�gitos a serem considerados pelo Dispositivo Alternativo"
                                     id="inputDigitosCartao" value="#{LocalAcessoControle.coletor.digitosLeituraCartao}"/>

                        <h:outputText id="txtInverterSinal"
                                      rendered="#{LocalAcessoControle.coletor.temInversaoSinal}"
                                      value="#{msg_aplic.prt_Coletor_InverterSinal}" styleClass="tituloCampos"/>
                        <h:selectBooleanCheckbox rendered="#{LocalAcessoControle.coletor.temInversaoSinal}"
                                     title="Selecione se os sinais DTR e RTS est�o configurados fisicamente na catraca de forma invertida em rela��o a dire��o esperada"
                                     id="chkInveterSinal" value="#{LocalAcessoControle.coletor.inverterSinal}"/>


                        <h:outputText rendered="#{LocalAcessoControle.coletor.apresentarCartaoMaster}" value="#{msg_aplic.prt_Coletor_cartaoMaster}" styleClass="tituloCampos" />
                        <h:inputText rendered="#{LocalAcessoControle.coletor.apresentarCartaoMaster}" id="cartaoMaster" size="15" maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{LocalAcessoControle.coletor.cartaoMaster}" />

                        <h:outputText rendered="#{LoginControle.usuarioLogado.administrador && (LocalAcessoControle.coletor.modelo eq LocalAcessoControle.modeloColetorIntegraFacil)}"
                                      styleClass="tituloCampos"
                                      title="Ao marcar essa configura��o, o sistema de acesso ir� entender a senha digitada na catraca como a matr�cula do aluno. Automaticamente a matr�cula passa a ser a senha na catraca do aluno, n�o sendo necess�rio fazer mais nada. Obs: S� funciona com 6 d�gitos na matr�cula."
                                      value="Utilizar matr�cula como senha na catraca:" />
                        <h:selectBooleanCheckbox rendered="#{LoginControle.usuarioLogado.administrador && (LocalAcessoControle.coletor.modelo eq LocalAcessoControle.modeloColetorIntegraFacil)}"
                                        value="#{LocalAcessoControle.coletor.utilizarMatriculaComoSenha}" />

                        <h:outputText rendered="#{LocalAcessoControle.coletor.podeAguardarGiro}" value="#{msg_aplic.prt_Coletor_aguardaGiro}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.podeAguardarGiro}">
                            <h:selectBooleanCheckbox id="aguardarGiro" value="#{LocalAcessoControle.coletor.aguardaGiro}"/>
                        </h:panelGroup>

                        <h:outputText rendered="#{LocalAcessoControle.coletor.podeUsarFacial}" value="#{msg_aplic.prt_Coletor_usaFacial}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.podeUsarFacial}">
                            <h:selectBooleanCheckbox id="usaFacial" value="#{LocalAcessoControle.coletor.usaFacial}"/>
                        </h:panelGroup>

                        <h:outputText rendered="#{LocalAcessoControle.coletor.apresentarBiometria}" value="#{msg_aplic.prt_Coletor_biometriaNaCatraca}" styleClass="tituloCampos" />
                        <h:selectBooleanCheckbox rendered="#{LocalAcessoControle.coletor.apresentarBiometria}" id="biometrico" styleClass="form" value="#{LocalAcessoControle.coletor.biometrico}" />

                        <c:if test="${LocalAcessoControle.coletor.usaColetorComoBiometria}">
                            <h:outputText value="Utilizar senha de acesso como c�digo da biometria" styleClass="tituloCampos"/>
                            <h:selectBooleanCheckbox id="usarSenhaAcessoComoBiometria" styleClass="form"
                                                     value="#{LocalAcessoControle.coletor.usarSenhaAcessoComoBiometria}"/>
                        </c:if>

                        <c:if test="${LocalAcessoControle.coletor.usaCatracaOffline}">
                            <h:outputText value="Utilizar catraca offline"  title="#{msg_aplic.prt_usar_catraca_offiline_hint}" styleClass="tituloCampos"/>
                            <h:selectBooleanCheckbox id="usarCatracaOffline" styleClass="form"
                                                     title="#{msg_aplic.prt_usar_catraca_offiline_hint}"
                                                     value="#{LocalAcessoControle.coletor.usarCatracaOffline}"/>
                        </c:if>

                    </h:panelGrid>
                </rich:tab>
                <rich:tab label="Impress�es digitais">
                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                        <c:if test="${!LocalAcessoControle.coletor.usarNumSerieFinger}">
                            <h:outputText    value="#{msg_aplic.prt_Coletor_numSerie}" styleClass="tituloCampos"/>
                            <h:panelGroup>
                                <h:inputText  id="numSerieColetor" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.numSerie}" />


                            <c:if test="${LocalAcessoControle.coletor.coletorZKTF1700}">
                                <script type="text/javascript" language="javascript">
                                    document.getElementById("form:numSerieColetor").setAttribute("placeholder","Ex: ZK\\123310367");
                                </script>
                            </c:if>
                            <c:if test="${!LocalAcessoControle.coletor.coletorZKTF1700}">
                                <script type="text/javascript" language="javascript">
                                    document.getElementById("form:numSerieColetor").setAttribute("placeholder","");
                                </script>
                            </c:if>

                            </h:panelGroup>

                            <h:outputText    value="#{msg_aplic.prt_Coletor_resolucaoDPI}" styleClass="tituloCampos"/>
                            <h:panelGroup>
                                <h:inputText  id="DPIColetor" size="5" maxlength="5" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.resolucaoDPI}" />
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${LocalAcessoControle.coletor.usarNumSerieFinger}">
                            <h:outputText    value="#{msg_aplic.prt_Coletor_numSerie_Finger}" styleClass="tituloCampos"/>
                            <h:panelGroup>
                                <h:inputText  id="numSerieColetorPlc" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.numSerie}" />
                            </h:panelGroup>

                            <h:outputText    value="#{msg_aplic.prt_Coletor_numSerie_Plc}" styleClass="tituloCampos"/>
                            <h:panelGroup>
                                <h:inputText  id="numSerieColetorFinger" size="40" maxlength="40" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{LocalAcessoControle.coletor.numSeriePlc}" />
                            </h:panelGroup>
                        </c:if>
                        <h:outputText    value="#{msg_aplic.prt_Coletor_padraoCadastro}" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="padraoCadastro" value="#{LocalAcessoControle.coletor.padraoCadastro}"/>
                        </h:panelGroup>

                    </h:panelGrid>
                </rich:tab>

                <rich:tab label="Facial">
                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                        <h:outputText value="Padr�o para cadastro facial:" styleClass="tituloCampos"/>
                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="padraoCadastroFacial" value="#{LocalAcessoControle.coletor.padraoCadastroFacial}"/>
                        </h:panelGroup>

                        <h:outputText rendered="#{LocalAcessoControle.coletor.podeUsarRtsp}" value="#{msg_aplic.prt_Coletor_usaRtsp}" styleClass="tituloCampos"/>
                        <h:panelGroup rendered="#{LocalAcessoControle.coletor.podeUsarRtsp}">
                            <h:selectBooleanCheckbox id="usaRtsp" value="#{LocalAcessoControle.coletor.usaRtsp}"/>
                        </h:panelGroup>

                        <c:if test="${LoginControle.usuarioLogado.administrador}">
                            <h:outputText value="Webcam: " styleClass="tituloCampos"/>
                            <h:panelGroup>
                                <h:inputText id="indiceCamera" size="5" maxlength="5" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form"
                                             value="#{LocalAcessoControle.coletor.indiceCamera}"/>
                            </h:panelGroup>
                        </c:if>



                    </h:panelGrid>
                </rich:tab>

                <rich:tab id="tabValidacoes" label="Valida��es de Acesso">
                    <%-- Incluir Formul�rio de cadastro de Valida��es de Acesso do Coletor. --%>
                    <%@include file="include_ValidacoesColetor.jsp" %>

                </rich:tab>
            </rich:tabPanel>
        </h:panelGrid>
    </h:panelGroup>


</h:panelGrid>


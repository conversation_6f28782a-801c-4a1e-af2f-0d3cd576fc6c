<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<jsp:include page="include_head.jsp" flush="true" />
<body>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="mostraesconde.js"></script>
<table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td align="center" valign="top" bgcolor="#ffffff">
	<table width="500" border="0" cellpadding="0" cellspacing="0" style="padding:10px;">
  <tr>
    <td align="left" valign="top">
	<!-- inicio bot�es -->
			  <form class="nomargin">
			  <div style="clear:both;margin-bottom:15px;">
			    <label>
			    <input onClick="location='javascript:window.close();'" type="button" name="Submit" value="Confirmar">
			    </label>
				<label></label>
				<label></label>
			    <span class="text" style="clear:both;">
			    <input onClick="location='javascript:window.close();'" type="button" name="Submit2" value="Cancelar">
			    </span></div>
			</form>
				<!-- fim bot�es -->
				<!-- inicio item -->
				<div style="clear:both;" class="text">
				  	<p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Grupos</p>
		    </div>
				  <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
				  <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">
  
  <tr>
    <td width="48%" height="27" align="center" valign="middle"><span class="text" style="font-weight: bold">Grupos cadastrados:</span></td>
    <td align="center" valign="middle">&nbsp;</td>
    <td width="48%" align="center" valign="middle"><span class="text" style="font-weight: bold">Grupos selecionados:</span></td>
  </tr>
  
  <tr>
    <td height="27" align="center" valign="middle">
			<select onclick="adicionarCategoria();" style="width: 180px;" size="7" class="form3" name="categorias" id="categorias">
			<option value="Grupo 01">Grupo 01</option>
			<option value="Grupo 04">Grupo 04</option>
			<option value="Grupo 05">Grupo 05</option>
			<option value="Grupo 06">Grupo 06</option>
			<option value="Grupo 07">Grupo 07</option>
			<option value="Grupo 08">Grupo 08</option>
			<option value="Grupo 09">Grupo 09</option>
			<option value="Grupo 10">Grupo 10</option>
			<option value="Grupo 11">Grupo 11</option>
			</select>	</td>
    <td align="center" valign="middle"><img src="images/arrow_select.png" width="24" height="24"></td>
    <td align="center" valign="middle">
		<select onclick="adicionarCategoria();" style="width: 180px;" size="7" class="form3" name="categorias" id="categorias">
			<option value="Grupo 02">Grupo 02</option>
			<option value="Grupo 03">Grupo 03</option>
			</select>	</td>
    </tr>
</table>
<div class="sep" style="margin:10px 0;"><img src="images/shim.gif"></div>
				  <!-- fim item -->
				  <!-- inicio bot�es -->
<form class="nomargin">
			  <div style="clear:both;margin-bottom:5px;">
			    <label>
			    <input onClick="location='javascript:window.close();'" type="button" name="Submit" value="Confirmar">
			    </label>
				<label></label>
				<label>
			    <input onClick="javascript:window.close();" type="button" name="Submit" value="Cancelar">
			    </label>
			  </div>
			</form>
				<!-- fim bot�es -->
	</td>
  </tr>
</table>
	</td>
  </tr>
</table>
</body>
</html>

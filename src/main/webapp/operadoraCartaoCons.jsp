<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_OperadoraCartao_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_OperadoraCartao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Config._Financeiras:Operadora_de_Cartão"/>

        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-2-3 text-right">
                        <h:panelGroup layout="block" style="line-height: 44px;" >
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" value="Situação "/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="margin-right: 10px;">
                                <h:selectOneMenu id="situacao" styleClass="exportadores" style="margin-right: 10px;"
                                                 value="#{OperadoraCartaoControle.situacaoFiltro}">
                                    <f:selectItems value="#{OperadoraCartaoControle.listaSelectItemSituacao}"/>
                                    <a4j:support event="onchange" oncomplete="location.reload()"/>
                                </h:selectOneMenu>
                            </h:panelGroup>


                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{OperadoraCartaoControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,codigoOperadora=Código Operadora,descricao=Descrição,situacao_Apresentar=Situação"/>
                                <f:attribute name="prefixo" value="OperadoraCartao"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="#{msg_aplic.prt_exportar_form_excel}" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{OperadoraCartaoControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,codigoOperadora=Código Operadora,descricao=Descrição,situacao_Apresentar=Situação"/>
                                <f:attribute name="prefixo" value="OperadoraCartao"/>
                                <f:attribute name="titulo" value="Operadora Cartão"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="#{msg_aplic.prt_exportar_form_pdf}" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 action="#{OperadoraCartaoControle.realizarConsultaLogObjetoGeral}"
                                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText title="#{msg_aplic.prt_visualizar_log_geral_entidade}" styleClass="btn-print-2 log"/>
                                </a4j:commandLink>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 reRender="formLog"
                                                 actionListener="#{LogControle.entidadeListener}"
                                                 oncomplete="Richfaces.showModalPanel('panelMasterLog');">
                                    <f:attribute name="nomeEntidade" value="OPERADORA CARTAO CE"/>
                                    <f:attribute name="funcao" value="118"/>
                                    <i class="fa-icon-list"></i>
                                </a4j:commandLink>
                            </c:if>

                            <!--A situação da Banderira ser um Cadastro, está gerando problemas, pois fica gerando vários cadastros para mesma Bandeira, atrapalhando integrações com outros sistemas.
                            O ideal é ter apenas um para cada Bandeira. Então, geramos no Banco de Importação um cadastro para cada Operadora (Bandeira).
                            Agora, bloqueamos as opções de Cadastrar ou Editar para apenas o Admin da Pacto.-->
                            <a4j:commandLink id="btnNovo"
                                             rendered="#{LoginControle.usuarioLogado.usuarioAdminPACTO}"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{OperadoraCartaoControle.novo}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblOperadoraCartao" class="tabelaOperadoraCartao pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_codigoOperadora_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_descricao_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_situacao_maiusculo}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{OperadoraCartaoControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{OperadoraCartaoControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{OperadoraCartaoControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty OperadoraCartaoControle.mensagem}"
                              value=" #{OperadoraCartaoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty OperadoraCartaoControle.mensagemDetalhada}"
                              value=" #{OperadoraCartaoControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <%@include file="/pages/ce/includes/include_modal_exibeLogEntidade.jsp" %>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        function recarregarTabelaOperadoraCartao() {

            var situacao = document.getElementById("form:situacao").value;
            tabelaAtual.dataTable().fnDestroy(0);
            iniciarTabela("tabelaOperadoraCartao", "${contexto}/prest/arquitetura/operadoraCartao?ativo="+situacao, 1, "asc", "", true);
        }

        jQuery(window).on("load", function () {
            iniciarTabela("tabelaOperadoraCartao", "${contexto}/prest/financeiro/operadoraCartao?ativo=${OperadoraCartaoControle.situacaoFiltro}", 1, "asc", "", true);
        });
    </script>

</f:view>

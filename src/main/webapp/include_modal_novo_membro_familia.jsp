<%@include file="include_imports.jsp" %>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

<rich:modalPanel id="mdlNovoMembroFamilia" styleClass="novaModal" autosized="true" shadowOpacity="true" width="550">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Adicionar novo membro da fam�lia"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    style="cursor:pointer" id="hidemdlNovoMembroFamilia"/>
            <rich:componentControl for="mdlNovoMembroFamilia" attachTo="hidemdlNovoMembroFamilia" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formNovoMembroFamilia" styleClass="font-size-Em-max">

        <h:inputText styleClass="newfam"
                     value="#{FamiliaresControle.nome}"
                     style="line-height: 40px; width: calc(100% - 20px); margin: 10px; border: solid #c5c5c5 2px !important; height: 40px;" id="pessoa">
        </h:inputText>
        <rich:suggestionbox height="200" width="500"
                            for="pessoa"
                            fetchValue="#{result}"
                            suggestionAction="#{FamiliaresControle.executarAutocompleteConsultaPessoa}"
                            minChars="1" rowClasses="20"
                            status="true"
                            ajaxSingle="false"
                            reRender="mensagem, pgNovaPessoa"
                            nothingLabel="Nenhuma pessoa encontrada!"
                            var="result" id="suggestionPessoa">
            <a4j:support event="onselect" ignoreDupResponses="true"
                         action="#{FamiliaresControle.selecionarPessoaSuggestionBox}" reRender="formNovoMembroFamilia"/>

            <h:column>
                <f:facet name="header">
                    <h:outputText styleClass="textverysmall" value="Nome"/>
                </f:facet>
                <h:outputText value="#{result.nomeCompleto}"/>
            </h:column>
        </rich:suggestionbox>


        <script>
            var inputTextNew = jQuery('.newfam');
            inputTextNew.attr('placeholder', "Informe o nome do aluno que deseja adicionar");
        </script>


        <h:panelGroup layout="block" style="font-size: 16px; color: #094771; font-weight: bold; margin: 10px">
            Fam�lia adicionada
        </h:panelGroup>
        <div class="familia" style="margin: 10px">
            <a4j:repeat value="#{FamiliaresControle.familiares}" var="f">
                <h:panelGroup layout="block" styleClass="familiar" style="display: inline-block; width: calc(50% - 10px); margin-bottom: 10px;">
                    <div style="position: relative; display: inline-block">
                        <h:graphicImage styleClass="fotopessoa" style="width:43px;height:43px; border-radius: 50%;"
                                        url="#{f.urlFoto}">
                        </h:graphicImage>

                        <div style="position: absolute; bottom: -5px; left: 0; width: 50px; display: flex">
                            <h:graphicImage styleClass="btnsituacao" id="alunoAtivo" value="/images/botaoAtivo.png" rendered="#{f.ativo}"   />
                            <h:graphicImage styleClass="btnsituacao" id="alunoInativo" value="/images/botaoInativo.png" rendered="#{f.inativo}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoVisitante" value="/images/botaoVisitante.png" rendered="#{f.visitante}"   />
                            <h:graphicImage styleClass="btnsituacao" id="alunoTrancado" value="/images/botaoTrancamento.png" rendered="#{f.trancado}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoNormal" value="/images/botaoNormal.png" rendered="#{f.ativoNormal}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoTrancadoVencido" value="/images/botaoTrancadoVencido.png" rendered="#{f.trancadoVencido}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoFreePass" value="/images/botaoFreePass.png" rendered="#{f.visitanteFreePass}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoAulaAvulsa" value="/images/botaoAulaAvulsa.png" rendered="#{f.visitanteAulaAvulsa}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoDiaria" value="/images/botaoDiaria.png" rendered="#{f.visitanteDiaria}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoCancelado" value="/images/botaoCancelamento.png" rendered="#{f.inativoCancelamento}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoDesistente" value="/images/botaoDesistente.png" rendered="#{f.inativoDesistente}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoAVencer" value="/images/botaoAvencer.png" rendered="#{f.ativoAvencer}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoVencido" value="/images/botaoVencido.png" rendered="#{f.inativoVencido}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoCarencia" value="/images/botaoCarencia.png" rendered="#{f.ativoCarencia}"  />
                            <h:graphicImage styleClass="btnsituacao" id="alunoAtestado" value="/images/botaoAtestado.png" rendered="#{f.ativoAtestado}"  />
                        </div>
                    </div>

                    <div style="display: inline-block;vertical-align: top;">
                        <span class="nomeFamiliar">
                            <h:outputText value="#{fn:toLowerCase(f.nome)}"/>
                        </span>
                        <span class="dadosFamiliar">
                            <h:outputText rendered="#{f.vencimento ne null}" value="Venc. #{f.vencimentoApresentar}"/>
                        </span>
                        <a4j:commandLink reRender="formNovoMembroFamilia" action="#{FamiliaresControle.removerFamiliar}"
                                         style="color: red;margin: 5px; display: block;"
                                         oncomplete="#{FamiliaresControle.msgAlert}">
                            <i class="tooltipster fa-icon-minus-sign "></i>&nbsp Remover membro
                        </a4j:commandLink>
                    </div>
                </h:panelGroup>
            </a4j:repeat>
        </div>

        <div style="text-align: center; margin-bottom: 20px">
            <a4j:commandLink id="salvarfamiliares" reRender="caixaexternafamilia"
                             action="#{FamiliaresControle.gravar}"
                             oncomplete="#{FamiliaresControle.msgAlert}"
                               accesskey="2" styleClass="botoes nvoBt">
                <i class="fa-icon-save"></i> Salvar
            </a4j:commandLink>
        </div>

    </h:form>
</rich:modalPanel>
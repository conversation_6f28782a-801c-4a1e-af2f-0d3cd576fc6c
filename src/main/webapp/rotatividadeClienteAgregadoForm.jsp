<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head> <%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>


<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <html>
    <header>
        <style>
            ::-webkit-scrollbar{
                height: 10px; !important;

            }
        </style>
        <title>
            <h:outputText value="BI - Movimentação de Contratos"/>
        </title>
    </header>

    <body style=" overflow: scroll">

        <h:form id="form" style="overflow-x: visible;" >
            <c:set var="titulo" scope="session" value="${RotatividadeAnaliticoDWControle.tituloListaResumo} - ${fn:length(RotatividadeAnaliticoDWControle.listaApresentarContratos)} objetos"/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-movimentacao-de-contratos-adm/"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
                <f:facet name="header">
                    <jsp:include page="topo_reduzido_popUp.jsp"/>
                </f:facet>
            </h:panelGroup>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">

                                        <tr>
                                            <td align="left" valign="top" width="100%"  style="padding:0 0 0 0;">
                                                <h:panelGroup id="panelGroup">
                                                    <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">

                                                        <tr>
                                                            <td align="left" valign="top" style="padding:5px 15px 5px 15px;">
                                                                <h:panelGrid id="panelCliente" width="100%">
                                                                    <h:outputText styleClass="mensagemDetalhada" value="#{RotatividadeAnaliticoDWControle.mensagemDetalhada}"/>
                                                                    <h:panelGrid cellpadding="0" cellspacing="0" columns="2" width="100%" style="border:none;">
                                                                        <rich:column style="border:none;" width="50" colspan="2">
                                                                            <h:selectBooleanCheckbox style="vertical-align:middle;" id="mostrarPaginacao" value="#{RotatividadeAnaliticoDWControle.mostrarPaginacao}">
                                                                                <a4j:support event="onclick" action="#{DataScrollerControle.resetDatascroller}" reRender="panelCliente,item,panelGroup"/>
                                                                            </h:selectBooleanCheckbox>
                                                                            <h:outputText value="Mostrar Paginação" styleClass="texto-font texto-size-14-real texto-cor-cinza" style="font-weight: bold;vertical-align:middle;"/>
                                                                        </rich:column>
                                                                        <rich:column style="border:none;" styleClass="colunaDireita">
                                                                            <a4j:commandLink id="exportarPdf"
                                                                                             styleClass="linkPadrao"
                                                                                             style="margin-left: 8px;"
                                                                                             actionListener="#{ExportadorListaControle.exportar}"
                                                                                             rendered="#{not empty RotatividadeAnaliticoDWControle.listaApresentarContratos}"
                                                                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                                             accesskey="2">
                                                                                <f:attribute name="lista" value="#{RotatividadeAnaliticoDWControle.listaApresentarContratos}"/>
                                                                                <f:attribute name="tipo" value="pdf"/>
                                                                                <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,tipoAgregador_Apresentar=Agregador,qtdCheckInAgregador_Apresentar=QTD. Check-in,dataUltimoAcessoAgregador_Apresentar=Ult. Periodo Acesso,situacaoCliente=Situação"/>
                                                                                <f:attribute name="prefixo" value="MovimentacaoContratos"/>
                                                                                <f:attribute name="itemExportacao" value="#{RotatividadeAnaliticoDWControle.itemExportacao}"/>
                                                                                <f:attribute name="titulo" value="Movimentação de contratos"/>
                                                                                <h:outputText title="Exportar para PDF" styleClass="btn-print-2 PDF"/>
                                                                            </a4j:commandLink>

                                                                            <a4j:commandLink id="exportarExcel"
                                                                                             styleClass="linkPadrao"
                                                                                             style="margin-left: 8px;"
                                                                                             actionListener="#{ExportadorListaControle.exportar}"
                                                                                             rendered="#{not empty RotatividadeAnaliticoDWControle.listaApresentarContratos}"
                                                                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                                             accesskey="2">
                                                                                <f:attribute name="lista" value="#{RotatividadeAnaliticoDWControle.listaApresentarContratos}"/>
                                                                                <f:attribute name="tipo" value="xls"/>
                                                                                <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,tipoAgregador_Apresentar=Agregador,qtdCheckInAgregador_Apresentar=QTD. Check-in,dataUltimoAcessoAgregador_Apresentar=Ult. Periodo Acesso,situacaoCliente=Situação"/>
                                                                                <f:attribute name="prefixo" value="MovimentacaoContratos"/>
                                                                                <f:attribute name="itemExportacao" value="#{RotatividadeAnaliticoDWControle.itemExportacao}"/>
                                                                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                                            </a4j:commandLink>
                                                                        </rich:column>
                                                                    </h:panelGrid>
                                                                    <rich:dataTable id="item" width="100%" styleClass="tabelaSimplesCustom"
                                                                                    value="#{RotatividadeAnaliticoDWControle.listaApresentarContratos}"
                                                                                    rows="#{RotatividadeAnaliticoDWControle.numeroPaginacao}"
                                                                                    var="contrato" rowKeyVar="status">

                                                                        <rich:column width="7,5%" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{contrato.cliente.matricula}"  filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Matricula"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"   value="#{contrato.cliente.matricula}" />
                                                                        </rich:column>

                                                                        <rich:column width="22,5%" styleClass="col-text-align-left" headerClass="col-text-align-left"  sortBy="#{contrato.pessoa.nome}"  filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza"  value="#{contrato.pessoa.nome}" />
                                                                        </rich:column>

                                                                        <rich:column width="12,5%" styleClass="col-text-align-center" headerClass="col-text-align-center" sortBy="#{contrato.tipoAgregador}" filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Agregador"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{contrato.tipoAgregador_Apresentar}" />
                                                                        </rich:column>

                                                                        <rich:column width="17,5%" styleClass="col-text-align-center" headerClass="col-text-align-center" sortBy="#{contrato.qtdCheckInAgregador}" filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Qtd Check-in"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{contrato.qtdCheckInAgregador_Apresentar}" />
                                                                        </rich:column>

                                                                        <rich:column width="12,5%" styleClass="col-text-align-center" headerClass="col-text-align-center" sortBy="#{contrato.dataUltimoAcessoAgregador}"  filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Ult. Período de acesso"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{contrato.dataUltimoAcessoAgregador_Apresentar}" />
                                                                        </rich:column>

                                                                        <rich:column width="12,5%" styleClass="col-text-align-center" headerClass="col-text-align-center" sortBy="#{contrato.cliente.situacao}"  filterEvent="onkeyup">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="SITUAÇÃO"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{contrato.cliente.situacao}" />
                                                                        </rich:column>

                                                                        <rich:column width="12,5%" styleClass="col-text-align-center" headerClass="col-text-align-center" sortBy="#{contrato.cliente.empresaNome}"  filterEvent="onkeyup" style="text-align: center">
                                                                            <f:facet name="header">
                                                                                <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="EMPRESA"/>
                                                                            </f:facet>
                                                                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{contrato.cliente.empresaNome}" />
                                                                        </rich:column>

                                                                        <rich:column width="12,5%" headerClass="col-text-align-center"  styleClass="col-text-align-center">
                                                                            <a4j:commandLink id="visualizarAluno"
                                                                                             styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                                                                             action="#{RotatividadeAnaliticoDWControle.irParaTelaCliente}"
                                                                                             oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                                <f:param name="state" value="AC"/>
                                                                                <i class="fa-icon-search"></i>
                                                                            </a4j:commandLink>
                                                                        </rich:column>
                                                                    </rich:dataTable>

                                                                    <rich:datascroller styleClass="scrollPureCustom" binding="#{DataScrollerControle.dataScroller}"
                                                                                       renderIfSinglePage="false"
                                                                                       rendered="#{RotatividadeAnaliticoDWControle.mostrarPaginacao}"
                                                                                       align="center" for="form:item" maxPages="20" id="scitems" />

                                                                </h:panelGrid>
                                                                <h:panelGrid id="panelTotalizadorAgregadores"
                                                                             rendered="#{RotatividadeAnaliticoDWControle.exibirAgregadoresMovimentacaoContrato}"
                                                                             width="100%">
                                                                    <table width="100%" border="0" cellspacing="0"
                                                                           cellpadding="0" class="tablepadding2"
                                                                           style="margin-top: 20px">
                                                                        <tr>
                                                                            <td align="left" valign="top">
                                                                                <div style="clear:both;"
                                                                                     class="text">
                                                                                    <p class="texto-cor-cinza texto-bold texto-size-16-real texto-font">
                                                                                        Clientes do contrato com agregadores</p>
                                                                                    <h:panelGrid
                                                                                            id="panelTotalizadorAgregadores1"
                                                                                            columns="2"
                                                                                            width="100%">
                                                                                    <h:panelGrid columns="1">
                                                                                    <h:panelGroup>
                                                                                        <h:outputText
                                                                                                value="Gympass = "
                                                                                                styleClass="texto-cor-cinza texto-size-14-real texto-font"/>
                                                                                        <h:outputText id="totalGympass"
                                                                                                      styleClass="texto-cor-cinza texto-size-14-real texto-font"
                                                                                                      value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdAgregadorGympass}"/>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup>
                                                                                        <h:outputText
                                                                                                value="Gogood = "
                                                                                                styleClass="texto-cor-cinza texto-size-14-real texto-font"/>
                                                                                        <h:outputText id="totalGoGood"
                                                                                                      styleClass="texto-cor-cinza texto-size-14-real texto-font"
                                                                                                      value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdAgregadorGogood}"/>
                                                                                    </h:panelGroup>
                                                                                    <h:panelGroup>
                                                                                        <h:outputText
                                                                                                value="TotalPass = "
                                                                                                styleClass="texto-cor-cinza texto-size-14-real texto-font"/>
                                                                                        <h:outputText
                                                                                                id="totalTotalPass"
                                                                                                styleClass="texto-cor-cinza texto-size-14-real texto-font"
                                                                                                value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdAgregadorTotalpass}"/>
                                                                                    </h:panelGroup>

                                                                                    <h:panelGroup>
                                                                                        <h:outputText
                                                                                                value="Total de Clientes do contrato com agregadores = "
                                                                                                styleClass="texto-cor-cinza texto-size-14-real texto-font"/>
                                                                                        <h:outputText
                                                                                                id="totalAgregadoresMes"
                                                                                                styleClass="texto-cor-cinza texto-size-14-real texto-font"
                                                                                                value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdAgregadoresVinculadosMes}"/>
                                                                                    </h:panelGroup>
                                                                                    </h:panelGrid>
                                                                                    </h:panelGrid>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </h:panelGrid>

                                                            </td>
                                                            <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>

                                                        </tr>

                                                    </table>
                                                </h:panelGroup>
                                            </td>
                                        </tr>
                                    </table>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>


        </h:form>
    </body>
</html>



</f:view>

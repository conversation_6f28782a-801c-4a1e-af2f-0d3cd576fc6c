<%-- 
    Document   : modulo_incluir_cliente
    Created on : 10/05/2012, 14:57:44
    Author     : Waller
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="include_imports.jsp" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
    function buildZebraTable(tableId) {
        var table=document.getElementById(tableId);
        if(!table){return};

        // get all <tr> table elements
        var trows=table.getElementsByTagName('tr');
        //table.className = 'tablelistras textsmall';
        for(var j = 0; j < trows.length; j++){
            // assign CSS class to even and odd rows
            trows[j].className = j % 2 == 0 ? 'par' : '';
            if (j % 2 == 0){
                for (var k = 0; k < trows[j].childNodes.length; k ++){
                    trows[j].childNodes[k].className = 'par';
                }

            }
        }
    }

    function validar(){

        if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
            && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
            && document.getElementById('formConsultarCEP:bairroCEP').value == ""
            && document.getElementById('formConsultarCEP:logradouroCEP').value == ""){

            alert("Ao menos um parâmetro deve ser informado!");
            return false;
        }


        return true;
    }

    // run 'buildZebraTable()' function when web page is loaded
    window.onload=function zebra() {
        buildZebraTable('tabela1');
        buildZebraTable('tabela2');
        buildZebraTable('tabela3');
    }
</script>
<%@page pageEncoding="ISO-8859-1"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<%@include file="/includes/include_identificadorModuloEstudio.jsp" %>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <!-- Inclui o elemento HEAD da página -->
    <head>
        <jsp:include page="${contextoModulo}/includes/include_head.jsp" />
    </head>


    <h:form id="form">
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
            <!-- Topo e menu superior -->
            <c:if test="${MenuControle.apresentarTopo}">
                <tr>
                    <td height="77" align="left" valign="top" class="bgtop">
                        <jsp:include page="${contextoModulo}/includes/include_top.jsp" flush="true" />
                    </td>
                </tr>

                <tr>
                    <td height="48" align="left" valign="top" class="bgmenu">
                        <jsp:include page="${contextoModulo}/includes/include_menu.jsp" flush="true" />
                    </td>
                </tr>
            </c:if>
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" height="100%" align="center" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">                                
                                <jsp:include page="${contextoModulo}/includes/include_box_menulateral_sc.jsp" />
                                <jsp:include page="${contextoModulo}/includes/include_box_descricao.jsp" />
                            </td>
                            <td align="center" valign="top" style="padding-left: 5px; padding-right: 0px;">
                                <jsp:include page="include_cadastro_novo_cliente.jsp"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="/include_rodape.jsp" flush="true" /></td>
            </tr>
        </table>
    </h:form>
    <jsp:include page="include_modais_tela_cadastro_cliente.jsp" flush="true"/>

</f:view>
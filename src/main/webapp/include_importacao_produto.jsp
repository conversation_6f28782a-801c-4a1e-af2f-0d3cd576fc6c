<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@include file="includes/imports.jsp" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
</style>
<h:panelGroup layout="block" id="panelProduto">

    <h:panelGroup layout="block" styleClass="panelObjetivo">
        <h:outputLabel value="Objetivo:" styleClass="textoObjetivo"
                       style="font-weight: bold; font-style: italic;"/>
        <h:outputLabel styleClass="textoObjetivo"
                       value="Realizar importação de produtos através de uma planilha modelo disponibilizada para download."/>
    </h:panelGroup>

    <h:panelGroup layout="block" style="padding: 15px;">

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="1º passo:" styleClass="passosImportacao"/>
            <a4j:commandLink  target="_blank"
                              style="padding-left: 5px; font-size: 15px;"
                              value="Baixar planilha modelo"
                              oncomplete="location.href='../DownloadSV?mimeType=application/vnd.ms-excel&diretorio=modelo&relatorio=modelo_importacao_produtos.xlsx'"/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="2º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Informe as configurações:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno"
                          style="display: inline-flex; width: 100%"
                          id="panelConfigAbaProduto">
                <h:panelGrid columns="2" width="100%"
                             columnClasses="colunaEsquerdaImport, colunaDireitaImport">

                    <h:outputLabel value="Tipo Produto:"
                                   title="Será utilizado para os produtos que não foi informado tipo na planilha.<br/>
                                             Caso não seja selecionado uma opção, o produto que não tem tipo não será importado."
                                   styleClass="passosImportacaoDescricao tooltipster"/>

                    <h:selectOneMenu id="tipoProduto" onblur="blurinput(this);"
                                     title="Será utilizado para os produtos que não foi informado tipo na planilha.<br/>
                                             Caso não seja selecionado uma opção, o produto que não tem tipo não será importado."
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.configProdutoTO.tipoProduto}">
                        <f:selectItems value="#{ImportacaoControle.listaSelectItemTipoProduto}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Categoria:"
                                   title="Será utilizado para os produtos que não foi informado categoria na planilha.<br/>
                                           Caso não seja selecionado uma opção, o produto que não tem categoria não será importado."
                                   styleClass="passosImportacaoDescricao tooltipster"/>

                    <h:selectOneMenu id="categoria" onblur="blurinput(this);"
                                     title="Será utilizado para os produtos que não foi informado categoria na planilha.<br/>
                                           Caso não seja selecionado uma opção, o produto que não tem categoria não será importado."
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.configProdutoTO.categoria}">
                        <f:selectItems value="#{ImportacaoControle.listaCategoria}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Validar se existe produto com o mesmo nome cadastrado:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectBooleanCheckbox id="validarProdutoMesmoNome" styleClass="form tooltipster"
                                             title="Caso exista um produto cadastrado com o mesmo nome o produto não será importado."
                                             value="#{ImportacaoControle.configProdutoTO.validarProdutoMesmoNome}"/>

                    <h:outputLabel value="Máscara campos de data:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu id="mascaraDataAbaProduto" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.configProdutoTO.mascaraDataEnum}">
                        <f:selectItems value="#{ImportacaoControle.listaSelectItemMascaraData}"/>
                    </h:selectOneMenu>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="3º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Preencher a planilha seguindo as seguintes regras:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno" style="font-size: 15px;">
                       <span>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> <b>Campos Obrigatórios</b> para importar: <b>ID_EXTERNO, DESCRIÇÃO, TIPO e CATEGORIA</b></li>
                            </ul>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Caso não exista uma categoria com o mesmo nome informado na planilha, será criado uma nova categoria.</li>
                            </ul>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Coluna ATIVO informar:
                                  <ol style="padding-top: 5px;"><b>"SIM"</b> para Ativo</ol>
                                  <ol><b>"NÃO"</b> para Inativo</ol>
                                  <ol style="padding-top: 5px;"><i>*Caso não seja informado será importado como <b>ATIVO</b></i></ol>
                                </li>
                            </ul>
                           <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Para produtos com VIGÊNCIA informar:
                                  <ol style="padding-top: 5px;"><b>"VIGENCIA_NR_DIAS"</b> para produtos com número de dias de vigência</ol>
                                  <ol><b>"VIGENCIA_DT_INICIO e VIGENCIA_DT_FINAL"</b> para produto com período específico de vigência</ol>
                                    <ol style="padding-top: 5px;"><i>*Caso não seja informado nenhum dos campos o produto não será considerado de vigência</i></ol>
                                </li>
                            </ul>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px">
                                    Coluna BLOQUEAR_APOS_VIGENCIA informar:
                                    <ol style="padding-top: 5px;"><b>"SIM"</b> para bloquear</ol>
                                    <ol><b>"NÃO"</b> para não bloquear</ol>
                                    <ol style="padding-top: 5px;"><i>*Caso não seja informado será importado como <b>NÃO BLOQUEAR</b></i></ol>
                                </li>
                            </ul>
                       </span>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="4º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Fazer o Upload da planilha baixada e realizar a importação:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelUploadFileProduto" style="padding-top: 5px; padding-left: 30px">
            <rich:fileUpload
                    fileUploadListener="#{ImportacaoControle.uploadArquivoProduto}"
                    immediateUpload="true" id="imagemModeloUploadProduto"
                    acceptedTypes="xls,xlsx" allowFlash="false"
                    listHeight="58px"
                    cancelEntryControlLabel="Cancelar"
                    addControlLabel="Adicionar"
                    clearControlLabel="Remover"
                    clearAllControlLabel="Remover Todos"
                    doneLabel="Concluído"
                    sizeErrorLabel="Limite de tamanho atingido"
                    uploadControlLabel="Carregar"
                    transferErrorLabel="Erro na transferência"
                    stopControlLabel="Parar"
                    stopEntryControlLabel="Parar"
                    progressLabel="Carregando"
                    maxFilesQuantity="1">
                <a4j:support event="onerror" reRender="panelBotoesProduto"
                             action="#{ImportacaoControle.removerArquivo}"/>
                <a4j:support event="onupload" reRender="panelBotoesProduto, panelBotoesProduto"/>
                <a4j:support event="onuploadcomplete" reRender="panelBotoesImportacao, panelBotoesProduto"/>
                <a4j:support event="onclear" reRender="panelBotoesProduto, panelUploadFileProduto"
                             action="#{ImportacaoControle.removerArquivo}"/>
            </rich:fileUpload>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" id="panelBotoesProduto"
                  styleClass="panelBotoesImportacao">
        <a4j:commandLink id="btnImportarProduto" value="Ler Arquivo"
                         style="padding-left: 15px"
                         onclick="atualizarTempoImportacao()"
                         rendered="#{ImportacaoControle.apresentarImportar}"
                         action="#{ImportacaoControle.processarArquivoProduto}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         title="Processar Importação de Dados"
                         reRender="panelGeralModalConfirmarImportacao, formModImpo"
                         styleClass="botoes nvoBt"/>
    </h:panelGroup>
</h:panelGroup>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Indicacao_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoCRM.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{IndicacaoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Indicacao_tituloForm}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-emitir-um-relatorio-de-todas-as-indicacoes-feitas-no-mes/"
                                      title="Clique e saiba mais: Indicação" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <h:panelGrid id="informacoesIndicador" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_clienteQueIndicou}" />

                    <h:panelGroup id="panelClienteQueIndicou">
                        <h:inputText id="textClienteQueIndicou" size="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" value="#{IndicacaoControle.indicacaoVO.clienteQueIndicou.pessoa.nome}"/>
                        <rich:suggestionbox   height="200" width="200"
                                              for="textClienteQueIndicou"
                                              fetchValue="#{result.pessoa.nome}"
                                              suggestionAction="#{IndicacaoControle.executarAutocompleteCliente}"
                                              minChars="1" rowClasses="20"
                                              reRender="form:panelColaboradorQueIndicou"
                                              nothingLabel="Nenhum Cliente encontrado !"
                                              var="result" id="suggestionCliente" status="indicacao">
                            
                            <a4j:support event="onselect" action="#{IndicacaoControle.selecionarClienteSuggestionBox}" />
                            <h:column>
                                <h:outputText value="#{result.pessoa.nome}" />
                            </h:column>
                        </rich:suggestionbox>
                        <a4j:commandButton action="#{IndicacaoControle.limparCampoClienteSuggestion}" alt="Limpar Nome do Cliente"
                                           reRender="form:informacoesIndicador" image="./images/limpar.gif"/>

                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_colaboradorQueIndicou}" />
                    <h:panelGroup id="panelColaboradorQueIndicou" >
                        <h:inputText id="textColaboradorQueIndicou" size="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{IndicacaoControle.indicacaoVO.colaboradorQueIndicou.pessoa.nome}"/>
                        <rich:suggestionbox   height="200" width="200"
                                              for="textColaboradorQueIndicou"
                                              fetchValue="#{result.pessoa.nome}"
                                              suggestionAction="#{IndicacaoControle.executarAutocompleteColaborador}"
                                              minChars="1" rowClasses="20"
                                              reRender="form:textClienteQueIndicou,form:suggestionCliente"
                                              nothingLabel="Nenhum Colaborador encontrado !"
                                              var="result"  id="suggestionColaboradorQueIndicou" status="indicacao">
                            <a4j:support event="onselect" action="#{IndicacaoControle.selecionarColaboradorSuggestionBox}" />
                            <h:column>
                                <h:outputText value="#{result.pessoa.nome}" />
                            </h:column>
                        </rich:suggestionbox>
                        <a4j:commandButton action="#{IndicacaoControle.limparCampoColaboradorSuggestion}" 
                                           alt="Limpar Nome do Colaborador"
                                           reRender="form:informacoesIndicador" image="./images/limpar.gif"/>

                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_evento}" />
                    <h:panelGroup id="panelEvento" >
                        <h:inputText id="textEvento" size="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{IndicacaoControle.indicacaoVO.evento.descricao}"/>
                        <rich:suggestionbox   height="200" width="200"
                                              for="textEvento"
                                              fetchValue="#{result.descricao}"
                                              suggestionAction="#{IndicacaoControle.executarAutocompleteEvento}"
                                              minChars="1" rowClasses="20"
                                              nothingLabel="Nenhum Evento encontrado !"
                                              var="result"  id="suggestionEvento" status="indicacao">
                            <a4j:support event="onselect" action="#{IndicacaoControle.selecionarEventoSuggestionBox}"/>
                            <h:column>
                                <h:outputText value="#{result.descricao}" />
                            </h:column>
                        </rich:suggestionbox>
                         <a4j:commandButton action="#{IndicacaoControle.limparCampoEventoSuggestion}" alt="Limpar Evento" 
                                            reRender="form:panelEvento" image="./images/limpar.gif"/>

                 
                    </h:panelGroup>
                </h:panelGrid>
                <rich:hotKey selector="#textEvento" key="return"
                                             handler="#{rich:element('tituloFormulario')}" />

                <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Indicacao_tituloInformacaoIndicador}"/>
                    </f:facet>
                </h:panelGrid>

                <h:panelGrid id="pg_identificadorCliente1" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" >

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_nomeIndicado}" />

                    <h:inputText id="nomeIndicado" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{IndicacaoControle.indicadoVO.nomeIndicado}" />

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_telefoneIndicado}" />
                    <h:panelGroup>
                        <h:inputText  id="telefoneIndicado" size="13"
                                      maxlength="13"
                                      onchange="return validar_Telefone(this.id);"
                                      onblur="blurinput(this);"
                                      onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                      onfocus="focusinput(this);"
                                      styleClass="form" value="#{IndicacaoControle.indicadoVO.telefoneIndicado}" />
                        <rich:spacer width="67" />
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_telefone}" />
                        <rich:spacer width="5" />
                        <h:inputText  id="telefone" size="13"
                                      maxlength="13"
                                      onchange="return validar_Telefone(this.id);"
                                      onblur="blurinput(this);"
                                      onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                      onfocus="focusinput(this);"
                                      styleClass="form" value="#{IndicacaoControle.indicadoVO.telefone}" />
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_email}" />
                    <h:panelGroup>
                        <h:inputText  id="email" size="60" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{IndicacaoControle.indicadoVO.email}" />
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid id="botaoAdicionar" width="100%" columns="1" columnClasses="colunaCentralizada">
                    <h:panelGrid width="100%" columns="2" columnClasses="colunaDireita, colunaEsquerda">
                        <a4j:commandButton id="addIndicados" action="#{IndicacaoControle.adicionarIndicados}" focus="form:nomeIndicado" reRender="form" value="#{msg_bt.btn_adicionar}" image= "./imagensCRM/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                        <a4j:commandButton id="btnHistorico" action="#{IndicacaoControle.consultarIndicacoes}"
                                           oncomplete="abrirPopup('historicoIndicacao.jsp', 'HistoricoIndicacao', 512, 530);"
                                           image="./imagensCRM/botaoHistorico.png" />
                    </h:panelGrid>
                </h:panelGrid>

                <h:dataTable id="resIndicados"  width="100%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{IndicacaoControle.indicacaoVO.indicadoVOs}" rendered="#{!empty IndicacaoControle.indicacaoVO.indicadoVOs}" var="indicados">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="Contato realizado" />
                        </f:facet>
                        <h:outputText styleClass="fa-icon-phone-sign" style="#{indicados.stiloIconeContato}" title="#{indicados.contatoRealizado_Apresentar}"></h:outputText>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="#{msg_aplic.prt_Indicado_nomeIndicado}" />
                        </f:facet>
                        <h:outputText  value="#{indicados.nomeIndicado}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="#{msg_aplic.prt_Indicado_telefoneIndicado}" />
                        </f:facet>
                        <h:outputText  value="#{indicados.telefoneIndicado}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="#{msg_aplic.prt_Indicado_telefone}" />
                        </f:facet>
                        <h:outputText  value="#{indicados.telefone}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText  value="#{msg_aplic.prt_Indicado_opcoes}" />
                        </f:facet>
                        <h:commandButton id="removerItemLista" immediate="true" action="#{IndicacaoControle.removerIndicadosLista}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                        <a4j:commandButton id="editarItemLista" reRender="pg_identificadorCliente1" action="#{IndicacaoControle.editarIndicadosLista}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>
                    </h:column>

                </h:dataTable>

                <h:panelGrid id="informacoes" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" styleClass="tabForm" width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_observacao}" />
                    <h:panelGroup>
                        <h:inputTextarea id="observacao" value="#{IndicacaoControle.indicacaoVO.observacao}" onblur="blurinput(this);" onfocus="focusinput(this);" cols="80" rows="5" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_usuarioResponsavelCadastro}" />
                    <h:panelGroup>
                        <h:inputText  id="responsavelCadastro" size="40" maxlength="2" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{IndicacaoControle.indicacaoVO.responsavelCadastro.nome}" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Indicacao_colaboradorResponsavel}" />
                    <h:panelGroup>
                        <h:inputText  id="colaboradorResponsavel" size="40" maxlength="2" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{IndicacaoControle.indicacaoVO.colaboradorResponsavel.nome}" />
                    </h:panelGroup>                    
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{IndicacaoControle.sucesso}" image="./imagensCRM/sucesso.png"/>
                        <h:commandButton rendered="#{IndicacaoControle.erro}" image="./imagensCRM/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgIndicado" styleClass="mensagem"  value="#{IndicacaoControle.mensagem}"/>
                            <h:outputText id="msgIndicadoDet" styleClass="mensagemDetalhada" value="#{IndicacaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <%--<h:commandLink id="novo" action="#{IndicacaoControle.novo}" title="#{msg_bt.btn_novo}"  value="#{msg_bt.btn_novo}" accesskey="1" styleClass="botoes nvoBt btSec"/>--%>
                            <%--<rich:spacer width="10"/>--%>
                            <a4j:commandLink id="salvar" action="#{IndicacaoControle.gravar}" reRender="form" oncomplete="#{IndicacaoControle.msgAlert};recarregarMetas();" value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <rich:spacer width="10"/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandLink id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{IndicacaoControle.msgAlert}" action="#{IndicacaoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <rich:spacer width="10"/>
                            <h:commandLink id="consultar" immediate="true" action="#{IndicacaoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

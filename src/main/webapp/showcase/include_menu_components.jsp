<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 20/06/2016
  Time: 10:10
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../includes/imports.jsp"%>

<%----------------------------INICIO MENUS-----------------------------------------%>
<h:panelGroup layout="block" styleClass="menuLateral">

    <%------------------------GRUPO DE MENUS----------------------%>
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">

        <%--HEADER GRUPO--%>
        <%--<h:panelGroup layout="block" styleClass="grupoMenuTitulo">--%>
        <%--<i class="fa-icon-briefcase"></i> Bot�es--%>
        <%--</h:panelGroup>--%>

        <h:panelGroup id="panelCor" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="cor" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Cor
                <f:attribute name="pagina" value="cor.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelTipo" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="tipografica" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Tipografia
                <f:attribute name="pagina" value="tipografia.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelIcon" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="icones" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                �cones
                <f:attribute name="pagina" value="icones.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelButtons" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="buttons" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Buttons
                <f:attribute name="pagina" value="buttons.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelCards" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="cards" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Cards
                <f:attribute name="pagina" value="Cards.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelCheckERadio" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="checkERadio" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                CheckBox & RadioButtons
                <f:attribute name="pagina" value="checkBox.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelCollapse" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="collapse" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Collapse
                <f:attribute name="pagina" value="collapse.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelDivivers" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="dividers" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Dividers
                <f:attribute name="pagina" value="dividers.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelDropdowns" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="dropdowns" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Dropdowns
                <f:attribute name="pagina" value="dropdowns.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelFilterBar" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="filterBar" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Filter Bar
                <f:attribute name="pagina" value="filterBar.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="panelGrowl" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="growlNotifi" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Growl Notifications
                <f:attribute name="pagina" value="growlNotifications.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="painelModal" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="modal" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Modal
                <f:attribute name="pagina" value="modal.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="painelPagination" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="pagination" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Pagination
                <f:attribute name="pagina" value="pagination.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="painelPopover" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="popoverEtooltip" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Popover & Tooltip
                <f:attribute name="pagina" value="popoverAndTooltip.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="painelPopups" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="popups" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Popups
                <f:attribute name="pagina" value="popups.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="painelTabs" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="tabs" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Tabs (pills)
                <f:attribute name="pagina" value="tabsPills.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="grupoMenuItem dropDownSimples menu-lateral ">

            <h:panelGroup layout="block" styleClass="botao-hidden">

                <a4j:commandLink id="textInputs" styleClass="linkFuncionalidade" >
                    Inputs
                    <i class=" fa-icon-caret-right linkFuncionalidade"
                       style="font-family: FontAwesome;margin-left: 4px;"></i>
                </a4j:commandLink>

                <h:panelGroup layout="block" styleClass="dropdown-content" style="align-self: end; transition-property: none !important; top: 0; left: 200px;">
                    <h:panelGroup layout="block" styleClass="dropdown-item">
                        <h:panelGroup layout="block" styleClass="container-item">
                            <a4j:commandLink reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                                <h:outputText value="Input Text"/>
                                <f:attribute name="pagina" value="textInputs.jsp" />
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

        </h:panelGroup>
        <h:panelGroup id="painelTablesELists" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="tablesELists" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Tables & Lists
                <f:attribute name="pagina" value="tablesAndLists.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="painelpadraoRelatorio" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="padraoRelatorio" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Relat�rios
                <f:attribute name="pagina" value="Relatorios.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup id="painelOutrosPadroes" layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink id="outrosPadroes" styleClass="titulo3 linkFuncionalidade" reRender="conteudo" actionListener="#{ShowCaseControle.preparaAbrirConteudo}">
                Outros padr�es
                <f:attribute name="pagina" value="OutrosPadroes.jsp" />
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
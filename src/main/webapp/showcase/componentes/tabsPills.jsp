<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 23/06/2016
  Time: 16:42
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .line-space{
        margin-bottom: 20px;
    }
</style>
<script>
    jQuery.noConflict();
    function trocarBloco(bloco, container, tipoBotao, sumirLinha) {
        jQuery(container + '.visivel').slideUp();
        jQuery(container + bloco).slideDown();
        jQuery(container + bloco).addClass('visivel');
        jQuery(tipoBotao).removeClass('ativo');
        jQuery(tipoBotao + bloco).addClass('ativo');
        if (sumirLinha) {
            jQuery('.containerLinhaTempoContrato').hide();
            jQuery('.informacaoMatriculaAluno').hide();
        } else {
            jQuery('.containerLinhaTempoContrato').show();
            jQuery('.informacaoMatriculaAluno').show();
        }
    }
    function desembacar(classe) {
        jQuery('.bola.item').addClass('embacado');
        jQuery('.caixaPeriodo').addClass('embacado');
        jQuery(classe).removeClass('embacado');
    }
</script>
<a4j:keepAlive beanName="LinhaTempoContratoControle"/>
<h:panelGroup id="containerTabsPills" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoTabsPills" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Tabs (pills)</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-text-align-left col-md-12 line-space">
                    <h:panelGroup style="display: inline-flex; padding: 10px;"
                                  layout="block">
                        <a4j:commandLink status="false" styleClass="botaoModoTimeLine ativo operacao blocoContratos botaoBlocoPrincipal" style="text-decoration: none;"
                                         onclick="desembacar('.operacao');trocarBloco('.blocoContratos', '.blocosPrincipais', '.botaoBlocoPrincipal', false);">
                            <h:outputText value="Label 01"/>
                        </a4j:commandLink>
                        <a4j:commandLink status="false" onclick="desembacar('.financeiro');trocarBloco('.blocoFinanceiro', '.blocosPrincipais', '.botaoBlocoPrincipal', false);"
                                         styleClass="botaoModoTimeLine financeiro blocoFinanceiro botaoBlocoPrincipal"
                                         style="text-decoration: none;">
                            <h:outputText value="Label 02"/>
                        </a4j:commandLink>
                        <a4j:commandLink status="false"
                                         styleClass="botaoModoTimeLine relacionamento blocoRelacionamento botaoBlocoPrincipal"
                                         style="text-decoration: none;"
                                         onclick="desembacar('.relacionamento');trocarBloco('.blocoRelacionamento', '.blocosPrincipais', '.botaoBlocoPrincipal', false);">
                            <h:outputText value="Label 03"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </div>
                <div class="col-text-align-left col-md-11 col-md-offset-1">
                    <div class="col-text-align-left col-md-12 line-space">
                        <div class="col-text-align-left col-md-1">
                            <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Ativo:</span>
                        </div>
                        <div class="col-md-9">
                            <span class="texto-size-14 texto-cor-cinza texto-font">Fonte: Estilo complementar(14px) + color: #FFF + Pill -> BG:#29ABE2</span>
                        </div>
                    </div>
                    <div class="col-text-align-left col-md-12">
                        <div class="col-text-align-left col-md-1">
                            <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Normal:</span>
                        </div>
                        <div class="col-md-9">
                            <span class="texto-size-14 texto-cor-cinza texto-font">Fonte: A��o hierarquia secund�ria</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>
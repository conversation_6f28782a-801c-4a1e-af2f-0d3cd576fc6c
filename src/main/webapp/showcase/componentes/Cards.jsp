<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 20/06/2016
  Time: 11:43
  To change this template use File | Settings | File Templates
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .linha{
        margin-top: 10px;
        padding-bottom: 10px;
        height: 50px;
    }
</style>
<h:panelGroup id="containerCard" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabacalhoCard" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Cards</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <h:panelGroup layout="block"  styleClass="margin-box" style="display: table;">
        <h:panelGroup layout="block" styleClass="col-md-6">
            <h:panelGroup layout="block" styleClass="col-md-12">
                <span class="texto-size-16 texto-cor-cinza texto-font texto-bold" >Estilo</span>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="col-md-12">
                <h:panelGroup layout="block" styleClass="container-box zw_ui especial " style="min-height: 300px;margin: 20px 0px 20px 0px;">
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="col-md-12">
                <span class="texto-size-16 texto-cor-complementar texto-font" >#FFF box-shadow:0px 2px 10px 0px rgba(0,0,0,0.35)</span>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="col-md-12" style="margin-top: 20px;">
                <span class="texto-size-16 texto-cor-cinza texto-font texto-bold" >Organiza��o padr�o</span>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="col-md-12">
                <h:panelGroup layout="block" styleClass="container-box zw_ui especial " style="margin: 10px 0px 10px 0px;min-height: 200px">
                    <h:panelGroup layout="block" styleClass="container-box-header">
                        <h:panelGroup layout="block"  styleClass="margin-box" style="display: table;">
                            <h:panelGroup layout="block" styleClass="pull-left ">
                                <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Cards</span>
                                <h:outputLink styleClass="linkWiki texto-size-11"
                                              value="[linkWiki]"
                                              title="[titulo]"
                                              target="_blank">
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="pull-right ">
                                <span class="texto-size-14 texto-font texto-cor-azul" href="#">A��es da box /</span>
                                <span class="texto-size-14 texto-font texto-cor-cinza" href="#">Info adicional</span>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="box-filtro">
                        <h:panelGroup layout="block"  styleClass="margin-box" style="display: table;">
                            <h:panelGroup layout="block" styleClass="pull-left ">
                                <span class="texto-size-16 texto-font texto-cor-azul" href="#">Filtro da box</span>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="pull-right ">
                                <a class="linkPadrao texto-size-16 texto-font texto-cor-azul" href="#">A��o ("proxima
                                    tela" ou "voltar")</a>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="margin-box" style="min-height: 250px">

                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="box-footer">
                        <h:panelGroup layout="block"  styleClass="margin-box" style="display: table;">
                            <h:panelGroup layout="block" styleClass="pull-right">
                                <a class="linkPadrao texto-size-16 texto-font texto-cor-azul" href="#">A��o adicional</a>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="col-md-6">
            <h:panelGroup layout="block" styleClass="col-md-12">
                <span class="texto-size-16 texto-cor-cinza texto-font texto-bold" >Composi��o</span>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="col-md-12 bi-bg-verde" style="height: 200px;margin-top: 20px">
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="col-md-12 bg-cinza" style="height: 200px;margin-top: 20px">
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="col-md-6 " >

                <h:panelGroup layout="block" styleClass="col-md-12 bi-bg-verde" style="height: 300px;margin-top: 20px">
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="col-md-12 bg-cinza" style="height: 220px;margin-top: 20px">
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="col-md-5 col-md-offset-1 " >

                <h:panelGroup layout="block" styleClass="col-md-12 bg-cinza" style="height: 150px;margin-top: 20px">
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="col-md-12 bi-bg-verde" style="height: 200px;margin-top: 20px">
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="col-md-12 bi-bg-verde" style="height: 150px;margin-top: 20px">
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="col-md-12" style="height: 150px;margin-top: 20px">
                    <span class="texto-cor-complementar texto-font texto-size-16 italico">
                    Quando houverem duas colunas,
                    o alinhamento vertical deve ser
                    mantido sempre, bem como o
                    espa�amento vertical entre os cards.
                    Ainda que, para isso, se perca o
                    alinhamento horizontal (com a
                    coluna ao lado).
                    </span>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

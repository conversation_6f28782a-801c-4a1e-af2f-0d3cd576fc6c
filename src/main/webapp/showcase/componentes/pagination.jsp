<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 01/07/2016
  Time: 14:47
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="../../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<style>
    .line-space{
        margin-top: 40px;
    }
    .title-space{
        margin-bottom: 15px;
    }
</style>
<script>
    jQuery.noConflict();
</script>

<h:panelGroup id="containerPagination" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoPagination" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Pagination</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-text-align-left col-md-2">
                    <div class="col-text-align-left col-md-12 title-space" >
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >IMPORT�NCIA</span>
                    </div>
                    <div class="col-text-align-left col-md-12" style="padding-bottom: 30px;">
                        <span class="texto-size-16 texto-cor-cinza texto-font">Supre casos nos quais uma tabela
                            apresenta informa��es fixas, ou pouco mut�veis. O uso da pagina��o auxilia a encontrar e
                            referenciar informa��es nesse tipo de tabela
                        </span>
                    </div>
                    <div class="col-text-align-left col-md-12" style="padding-bottom: 30px;">
                        <span class="texto-size-16 texto-cor-cinza texto-font">Supre casos nos quais uma tabela apresenta
                            informa��es fixas, ou pouco mut�veis. O uso da pagina��o auxilia a encontrar e referenciar
                            informa��es nesse tipo de tabela
                        </span>
                    </div>
                    <div class="col-text-align-left col-md-12 title-space" >
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >INFINITE SCROLLING</span>
                    </div>
                    <div class="col-text-align-left col-md-12" style="padding-bottom: 30px;">
                        <span class="texto-size-16 texto-cor-cinza texto-font">No entanto, o padr�o do sistema � o Infinite
                            Scrolling, ou Rolagem Infinita, que possui a vantagem de uma tela mais limpa e uma navega��o
                            mais fluida. Quando somado aos filtros certos, � um �timo recurso que agiliza o trabalho do
                            usu�rio.
                        </span>
                    </div>
                </div>
                <div class="col-text-align-left col-md-10 pure-form ">
                    <table id="tblProduto" class="tabelaProduto pure-g-r pure-u-11-12 margin-0-auto">
                        <thead>
                        <th>${msg_aplic.prt_Produto_label_codigo}</th>
                        <th>${msg_aplic.prt_Produto_label_descricao}</th>
                        <th>${msg_aplic.prt_Produto_label_situacao}</th>
                        <th>${msg_aplic.prt_Produto_label_categoriaProduto}</th>
                        <th>${msg_aplic.prt_Produto_label_tipoProduto}</th>
                        <th style="text-align: right">${msg_aplic.prt_Produto_label_valor}</th>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <script src="${root}/beta/js/dt-server.js" type="text/javascript"></script>
    <script>
        jQuery(window).ready(function () {
            iniTblServer("tabelaProduto", "${root}/prest/plano/produto?situacao=${ProdutoControle.situacaoFiltro}", null, 2, "asc", "true");
        });
    </script>
</h:panelGroup>
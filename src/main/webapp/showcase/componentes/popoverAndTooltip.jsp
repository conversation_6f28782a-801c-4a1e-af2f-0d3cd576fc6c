<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 23/06/2016
  Time: 09:36
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .title-space{
        margin-bottom: 10px;
    }
    .line-space{
        margin-bottom: 30px;
    }
</style>
<script>
    // A declara��o a seguir � extrema
    jQuery('.tooltipster').tooltipster({
        theme: 'tooltipster-light',
        position: 'top',
        animation: 'grow',
        contentAsHTML: true
    });
    jQuery.noConflict();
</script>
<h:panelGroup id="containerPopoverTooltip" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoPopoverTooltip" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Popover & Tooltip</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-3">
                    <div class="col-text-align-left col-md-12 title-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >TOOLTIP</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Seu prop�sito � oferecer esclarecimento
                            sobre certa ferramenta do site. Seu conte�do deve ser curto e simples.</span>
                    </div>
                    <div class="col-text-align-left col-md-12 title-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >POPOVER</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Oferece informa��es adicional ao manter o
                            mouse sobre certa �rea do site, exemplo disso ocorre quando um lavel exprime diversos objetos
                            de forma reduzida, como "3 alunos". Ao colocar o mouse em cima, o popover indica quais os
                            alunos.</span>
                    </div>
                </div>
                <div class="col-md-8 col-md-offset-1">
                    <div class="col-md-12 line-space">
                        <input class="inputTextClean tooltipster" title="Campo de texto" value="Passe o mouse aqui"/>
                    </div>
                    <div class="col-md-12 line-space">
                        <h:commandLink value="Passe o mouse aqui" styleClass="botaoPrimarioGrande texto-size-16 tooltipster" title="Clique neste bot�o<br>para salvar as configura��es<br> modificadas." />
                    </div>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>
<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 23/06/2016
  Time: 15:13
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .line-space{
        margin-bottom: 15px;
    }
</style>
<h:panelGroup id="containerPopups" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoPopups" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Popups</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-3">
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Popups devem ser abertas sempre centralizadas
                            e nunca maximizadas.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Se uma popup j� est� aberta e for solicitado para
                            abrir novamente, ela n�o deve ser aberta uma nova popup, e sim apresentar a j� aberta (stay on top).
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Cuidado com duplica��o de barras de
                            rolagem verticais.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Evitar barras de rolagem horizontais.</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Se houver muita informa��o, evite o uso
                            de popups.
                        </span>
                    </div>
                </div>
                <div class="col-md-6 col-md-offset-3">
                    <a4j:commandLink value="Abrir Popup"
                                       oncomplete="abrirPopup('','Exemplo Popup',800,200);"
                                       accesskey="2" styleClass="botaoPrimarioGrande texto-size-16">
                    </a4j:commandLink>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>

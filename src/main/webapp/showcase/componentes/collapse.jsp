<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 28/06/2016
  Time: 08:49
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<h:panelGroup id="containerCollapse" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup  id="cabecalhoCollapse" layout="block" styleClass="container-box-header">
        <h:panelGroup  layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Collapse</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class=" col-md-3">
                    <div class="col-text-align-left col-md-12" style="padding-bottom: 30px;">
                        <span class="texto-size-16 texto-cor-cinza texto-font" >Toda a barra do Collapse deve ser clic�vel.</span>
                    </div>
                </div>
                <div class="col-md-8 col-md-offset-1">
                    <div class="col-md-12">
                        <a4j:form ajaxSubmit="true" style="overflow: hidden;">
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block"  styleClass="bg-cinza accodionPanel">
                                    <h:panelGroup styleClass="accordionBody semBorda" layout="block">
                                        <h:panelGrid columns="1" width="100%">
                                            <h:panelGroup layout="block" style="width:100%;display: block; text-align: center;" >
                                                <h:outputText value="CONTE�DO" styleClass="texto-cor-branco texto-font texto-size-20 texto-bold"/>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" style="height: 51px;width: 100%;text-align: center;line-height: 51px;" styleClass="accordionHeader">
                                        <h:outputText styleClass="texto-font texto-size-14 texto-bold texto-cor-cinza" value="LABEL" style="margin-left: 40px;float: left;line-height: 3.5;"/>
                                        <h:outputText styleClass="linkPadrao fa-icon-chevron-up texto-size-16 texto-cor-cinza bg-cinza-4 btnHandler rotate180"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <script>
                                    eventosModalidade();
                                </script>
                            </h:panelGroup>
                        </a4j:form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>


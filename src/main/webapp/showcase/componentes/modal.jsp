<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 23/06/2016
  Time: 08:29
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .title-space{
        margin-bottom: 15px;
    }
    .line-space{
        margin-bottom: 15px;
    }
</style>
<script>
    jQuery('.tooltipster').tooltipster({
        theme: 'tooltipster-light',
        position: 'top',
        animation: 'grow',
        contentAsHTML: true
    });
    jQuery.noConflict();
</script>

<h:panelGroup id="containerModal" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoModal" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Modal</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-3">
                    <div class="col-text-align-left col-md-12 title-space" style="padding-bottom: 5px;">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >IMPORT�NCIA</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Oferece uma navega��o secund�ria moment�nea.
                            As possibilidade s�o infinitas, desde um cadastro r�pido, at� a visualiza��o de informa��es
                            pertinentes, mas secund�rias o bastante para n�o estar na interface.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">As modais devem aparecer sempre centralizadas,
                            seguidas pelo overlay padr�o.
                        </span>
                    </div>
                </div>
                <div class="col-md-8 col-md-offset-1">
                    <div class="col-md-8 col-md-offset-3">
                        <a4j:commandLink id="btnAbrirModal"
                                         title="Abrir Modal"
                                         styleClass="botaoPrimarioGrande texto-size-16"
                                         value="Abrir Modal"
                                         reRender="modalExemplo"
                                         oncomplete="Richfaces.showModalPanel('modalExemplo')">
                        </a4j:commandLink>
                    </div>
                    <rich:modalPanel id="modalExemplo"  styleClass="novaModal noMargin" shadowOpacity="true"
                                     width="500" height="200">
                        <f:facet name="header">
                            <h:panelGroup>
                                <h:outputText value="Titulo + #FFF + BG: #074871, 60px "/>
                            </h:panelGroup>
                        </f:facet>
                        <f:facet name="controls">
                            <h:panelGroup>
                                <h:outputText
                                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                                        id="hidelink7"/>
                                <rich:componentControl for="modalExemplo" attachTo="hidelink7" operation="hide"
                                                       event="onclick"/>
                            </h:panelGroup>
                        </f:facet>
                        <a4j:form ajaxSubmit="true">
                            <h:panelGroup layout="block" style="margin: 10 20 10 20;">
                                <div class="col-md-7 col-md-offset-5" style="margin-top: 35px;">
                                    <h:outputText styleClass="texto-size-14 texto-bold texto-font texto-cor-cinza"
                                                  value="CONTE�DO"/>
                                </div>
                            </h:panelGroup>
                        </a4j:form>
                    </rich:modalPanel>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>
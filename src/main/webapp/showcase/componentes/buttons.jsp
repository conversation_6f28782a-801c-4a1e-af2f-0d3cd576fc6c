<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 21/06/2016
  Time: 15:51
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .line-space{
        margin-top: 40px;
    }
    .title-space{
        margin-top: 15px;
    }
</style>

<h:panelGroup id="containerButton" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabacalhoButton" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Buttons</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-text-align-left col-md-3">
                    <div class="col-text-align-left col-md-12" style="padding-bottom: 30px;">
                        <span class="texto-size-16 texto-cor-cinza texto-font" >Tamanho de fonte referente � resolu��o 1366.</span>
                    </div>
                    <div class="col-text-align-left col-md-12" style="padding-bottom: 30px;">
                        <span class="texto-size-16 texto-cor-cinza texto-font" >Toda a �rea do bot�o deve ser clic�vel.</span>
                    </div>
                    <div class="col-text-align-left col-md-12" style="padding-bottom: 30px;">
                        <span class="texto-size-16 texto-cor-cinza texto-font" >A aplica��o de hierarquia proposta deve
                            aconter de acordo com a import�ncia das a��es que a tela apresenta. Por Exemplo: a a��o
                            principal de uma tela, como uma a��o de conclus�o, deve vir sempre com o estilo Normal.
                        </span>
                    </div>
                    <div class="col-text-align-left col-md-12" style="padding-bottom: 30px;">
                        <span class="texto-size-16 texto-cor-cinza texto-font" >Bot�es somente com �cone s� s�o
                            permitidos caso o �cone seja globalmente conhecido, como �cones para imprimir, salvar, sair,etc.
                            Todos sempre devem vir seguidos por uma tooltip descritiva.
                        </span>
                    </div>
                </div>
                <div class="col-text-align-left col-md-8 col-md-offset-1">
                    <div class="col-text-align-left col-md-6">
                        <div class="col-md-12">
                            <div class="col-md-12">
                                <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Bot�o Primario / Bot�o Primario Desabilitado</span>
                            </div>
                            <div class="col-md-12 title-space">
                                <div class="col-md-4">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >Normal / Over </span>
                                </div>
                                <div class="col-md-3">
                                    <h:commandLink styleClass="botaoPrimario texto-size-16" value="Label" />
                                </div>
                                <div class="col-md-5">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >#074871 / #0A3858</span>
                                </div>
                            </div>
                            <div class="col-md-12 line-space ">
                                <div class="col-md-4">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >Desabilitado</span>
                                </div>
                                <div class="col-md-3">
                                    <h:commandLink value="Label" styleClass="botaoPrimarioDesabilitado texto-size-16"/>
                                </div>
                                <div class="col-md-5">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >#D5D2D2</span>
                                </div>
                            </div>
                            <div class="col-md-12 title-space ">
                                <span class="texto-size-14 texto-cor-cinza texto-font" >Font: Arial, regular, 16px, #FFF (desab.: #BOBOBO)</span>
                            </div>
                        </div>
                        <div class="col-md-12 line-space">
                            <div class="col-md-12">
                                <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Bot�o Primario Grande / Bot�o Primario Grande Desabilitado</span>
                            </div>
                            <div class="col-md-12 title-space">
                                <div class="col-md-4">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >Normal / Over </span>
                                </div>
                                <div class="col-md-5">
                                    <h:commandLink styleClass="botaoPrimarioGrande texto-size-16" value="Label" />
                                </div>
                                <div class="col-md-3">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >#074871 / #0A3858</span>
                                </div>
                            </div>
                            <div class="col-md-12 line-space ">
                                <div class="col-md-4">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >Desabilitado</span>
                                </div>
                                <div class="col-md-5">
                                    <h:commandLink value="Label" styleClass="botaoPrimarioGrandeDesabilitado texto-size-16" />
                                </div>
                                <div class="col-md-3">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >#D5D2D2</span>
                                </div>
                            </div>
                            <div class="col-md-12 title-space ">
                                <span class="texto-size-14 texto-cor-cinza texto-font" >Font: Arial, regular, 16px, #FFF (desab.: #BOBOBO)</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-text-align-left col-md-6">
                        <div class="col-md-12 ">
                            <div class="col-md-12">
                                <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Bot�o Secundario</span>
                            </div>
                            <div class="col-md-12 title-space">
                                <div class="col-md-4">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >Normal / Over </span>
                                </div>
                                <div class="col-md-3">
                                    <h:commandLink styleClass="botaoSecundario texto-size-16" value="Label" />
                                </div>
                                <div class="col-md-5">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >#074871 / #0A3858</span>
                                </div>
                            </div>
                            <div class="col-md-12 line-space ">
                                <div class="col-md-4">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >Desabilitado</span>
                                </div>
                                <div class="col-md-3">
                                    <h:commandLink value="Label" styleClass="botaoPrimarioDesabilitado texto-size-16" />
                                </div>
                                <div class="col-md-3">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >#D5D2D2</span>
                                </div>
                            </div>
                            <div class="col-md-12 title-space ">
                                <span class="texto-size-14 texto-cor-cinza texto-font" >Font: Arial, regular, 16px, #FFF (desab.: #BOBOBO)</span>
                            </div>
                        </div>
                        <div class="col-md-12 line-space" style="background-color: #e6f1f1;">
                            <div class="col-md-12" >
                                <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Bot�o Background</span>
                            </div>
                            <div class="col-md-12 title-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font" >Estilo para bot�es fora de um board</span>
                            </div>
                            <div class="col-md-12 title-space" >
                                <div class="col-md-4">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >Normal / Over </span>
                                </div>
                                <div class="col-md-3" >
                                    <h:commandLink styleClass="botaoBackground texto-size-16" value="Label" />
                                </div>
                                <div class="col-md-5">
                                    <span class="texto-size-14 texto-cor-cinza texto-font" >#FFF</span>
                                </div>
                            </div>
                            <div class="col-md-12 title-space ">
                                <span class="texto-size-14 texto-cor-cinza texto-font" >Font: Arial, regular, 16px, #FFF (desab.: #BOBOBO)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




</h:panelGroup>


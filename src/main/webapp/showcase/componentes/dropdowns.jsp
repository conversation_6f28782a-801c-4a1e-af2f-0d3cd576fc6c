<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 22/06/2016
  Time: 13:33
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="../../includes/imports.jsp" %>
<style>
    .line-space{
        margin-bottom: 10px;
    }
</style>
<h:panelGroup id="containerDropdowns" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoDropdowns" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Dropdowns</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-6">
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Tamanho de fonte à resolução 1366.</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Toda a área do dropdown deve ser clicável.</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Quando está ativo a área da linha da opção deve ser clicável.</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Título</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <div class="col-md-6 line-space cb-container margenVertical">
                            <h:selectOneMenu >
                                <f:selectItem itemValue="Label"/>
                                <f:selectItem itemValue="Label"/>
                            </h:selectOneMenu>
                        </div>
                    </div>
                    <div class="col-md-12 line-space">
                        <div class="col-md-3 line-space">
                            <span class="texto-size-14 texto-cor-cinza texto-font">Normal </span>
                        </div>
                        <div class="col-md-6 line-space">
                            <span class="texto-size-14 texto-cor-cinza texto-font">(border: 1px, #E5E5E5, radius: 3px)</span>
                        </div>
                    </div>
                    <div class="col-md-12 line-space">
                        <div class="col-md-3 line-space">
                            <span class="texto-size-14 texto-cor-cinza texto-font">Over </span>
                        </div>
                        <div class="col-md-6 line-space">
                            <span class="texto-size-14 texto-cor-cinza texto-font">(border: 1px, #9D9D9D, radius: 3px)</span>
                        </div>
                    </div>
                    <div class="col-md-12 line-space">
                        <div class="col-md-3 line-space">
                            <span class="texto-size-14 texto-cor-cinza texto-font">Ativo</span>
                        </div>
                        <div class="col-md-6 line-space">
                            <span class="texto-size-14 texto-cor-cinza texto-font">(border: 1px, #29AAE2)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>
<%--
  Created by IntelliJ IDEA.
  User: estulano
  Date: 04/01/2018
  Time: 16:48
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .line-space{
        margin-bottom: 10px;
    }
</style>
<h:panelGroup id="containerRelatorios" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoRelatorios" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Padr�es para Relat�rios</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-12">
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" style="display: block">
                            Validar relat�rio com o desenvolvedor, se o relat�rio monta uma lista de objetos ou se ele � baseado em *count.
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font" style="display: block">
                           Se o relat�rio exibir uma lista, com nome de pessoas, e outras informa��es b�sicas, ent�o deve ser validado e garantido com o desenvolvedor que o relat�rio monte os dados somente do que est� sendo visualizado no relat�rio.
                        </span>
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                        Se o relat�rio for de totalizadores, ent�o garantir com o desenvolvedor que este relat�rio est� sendo montado utilizando *count, pois utilizar montar dados neste caso � desnecess�rio devido � perda de permormance.
                            </span>
                    </div>
</h:panelGroup>
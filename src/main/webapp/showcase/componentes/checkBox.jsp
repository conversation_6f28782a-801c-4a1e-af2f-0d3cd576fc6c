<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 20/06/2016
  Time: 17:08
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .linha{
        margin-top: 10px;
        padding-bottom: 10px;
        height: 50px;
    }
</style>
<h:panelGroup id="containerCheckRadio" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabacalhoCheckRadio" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">CheckBox,RadioButtons & Accordion</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <h:panelGroup layout="block"  styleClass="margin-box" style="display: table;">
        <h:panelGroup layout="block" styleClass="col-md-12">
            <h:panelGroup layout="block" styleClass="col-md-6">
                <h:panelGroup layout="block" styleClass="col-md-12" style="margin-bottom: 10px">
                    <span class="texto-size-16 texto-cor-cinza texto-font texto-bold">CheckBox</span>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="col-md-2">

                     <%--N�o � utilizado JS devido problemas para manter o estado do elemento--%>
                    <%--ADICIONADO A STATUS = 'NONE' PARA QUE N�O EXIBIA O LOADING, ISTO � PRESUMINDO QUE O TEMPO DE ENVIO E REPOSTA DO FORMULARIO SEJA MUITO PEQUENO--%>
                    <a4j:commandLink id="check1" status="none" styleClass="texto-cor-azul botao-checkbox texto-size-14"
                                     reRender="check1,lchk1">
                        <%--aqui se encontra o icone no caso o font awesome--%>
                        <%--que ser� exibido o icon de acordo com o valor do booleano--%>
                        <h:outputText styleClass="icon #{ShowCaseControle.check1 ? 'fa-icon-check' : 'fa-icon-check-empty'}"/>
                        <h:outputText styleClass="label" value="Label 1"/>
                         <%--Negativa o booleano --%>
                        <f:setPropertyActionListener value="#{!ShowCaseControle.check1}"
                                                     target="#{ShowCaseControle.check1}"/>
                    </a4j:commandLink>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="col-md-8 col-md-offset-1">
                    <h:outputText id="lchk1" styleClass="text-font texto-cor-complementar texto-size-14 italico"
                                  value="#{ShowCaseControle.check1 ? 'Selecionado' : 'N�o selecionado'}"/>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="col-md-12">
                    <a4j:commandLink  styleClass="botao-checkbox desabilitado texto-size-14">
                        <h:outputText styleClass="icon fa-icon-sign-blank"/>
                        <h:outputText styleClass="label" value="Desabilitado"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="col-md-6">
                <h:panelGroup layout="block" styleClass="col-md-12" style="margin-bottom: 10px">
                    <span class="texto-size-16 texto-cor-cinza texto-font texto-bold">Radio Button</span>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="col-md-2">
                    <%--POR PADR�O O COMPORTAMENTO DE UM RADIOBUTTON � DE SEMPRE SOMENTE TRUE E N�O O INVERSO TRUE -> FALSE --%>
                    <%--ADICIONADO A STATUS = 'NONE' PARA QUE N�O EXIBIA O LOADING, ISTO � PRESUMINDO QUE O TEMPO DE ENVIO E REPOSTA DO FORMULARIO SEJA MUITO PEQUENO--%>
                    <a4j:commandLink id="check2" status="none" styleClass="texto-cor-azul botao-checkbox texto-size-14"
                                     reRender="check2,lchk2">

                        <h:outputText styleClass="icon #{ShowCaseControle.check2 ? 'fa-icon-circle-check' : 'fa-icon-circle-blank'}"/>
                        <h:outputText styleClass="label" value="Label 1"/>
                        <f:setPropertyActionListener value="#{!ShowCaseControle.check2}"
                                                     target="#{ShowCaseControle.check2}"/>
                    </a4j:commandLink>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="col-md-8 col-md-offset-1">
                    <h:outputText id="lchk2"  styleClass="text-font texto-cor-complementar texto-size-14 italico"
                                  value="#{ShowCaseControle.check2 ? 'Selecionado' : 'N�o selecionado'}"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="col-md-12">
            <h:panelGroup layout="block" styleClass="col-md-12" style="margin-bottom: 10px">
                <span class="texto-size-16 texto-cor-cinza texto-font texto-bold">CheckBox</span>
            </h:panelGroup>

        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 22/06/2016
  Time: 15:12
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<script>
    jQuery.noConflict();
</script>
<style>
    .line-space{
        margin-bottom: 10px;
    }
</style>
<h:panelGroup id="containerFilterBar" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabacalhoFilterBar" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Filter Bar</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-3">
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >IMPORT�NCIA</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Seu uso � primordial na organiza��o de
                            telas com muitos filtros, auxilia a organizar e mant�m a tela mais simples. Ao manter sua
                            posi��o, o usu�rio sempre saber� onde encontrar os filtros durante suas a��es.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Sua posi��o � sempre a mesma na composi��o,
                            e deve ser vinculado a um card como demonstrado na tela Cards.
                        </span>
                    </div>
                </div>
                <div class="col-md-7 col-md-offset-1" >
                    <div class="tituloPainelPesquisa cinza">
                        <h:inputText styleClass="tituloAzul inputBuscaFiltro"
                                     style="color: #29ABE2; vertical-align:middle; float:left; padding-bottom: 10px !important;color: #777777  !important;  width: 15vw !important; margin-right: 10px;"/>

                        <a4j:commandLink style="font-size: 1.2vw; text-decoration: none;" accesskey="2">
                            <i class="fa-icon-search" style="color: #29ABE2; vertical-align:middle; float:left; margin-right: 10px; margin-top: 10px;"></i>
                        </a4j:commandLink>

                        <h:selectOneMenu  styleClass="newComboBox noBorderRight" style="vertical-align:middle; float:left;">
                            <f:selectItem itemValue="" itemLabel="Label 1"/>
                            <f:selectItem itemValue="" itemLabel="Label"/>
                            <f:selectItem itemValue="" itemLabel="Label"/>
                        </h:selectOneMenu>

                        <h:selectOneMenu  styleClass="newComboBox" style="vertical-align:middle; float:left;">
                            <f:selectItem itemValue="" itemLabel="Label 2"/>
                            <f:selectItem itemValue="" itemLabel="Label"/>
                            <f:selectItem itemValue="" itemLabel="Label"/>
                        </h:selectOneMenu>

                        <h:commandLink styleClass="botaoSecundario texto-size-16" value="Filtrar" style="vertical-align:middle; float:left; max-width: 10vw; margin-left: 10px; margin-top: 3px; margin-right: 10px;" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>
<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 20/06/2016
  Time: 08:44
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="../../includes/imports.jsp" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="${root}/css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
    <link href="${root}/css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
    <link href="${root}/beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>

    <link href="${root}/beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="${root}/beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
    <link href="${root}/beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>

    <script type="text/javascript" language="javascript" src="${root}/bootstrap/jquery.js"></script>
    <script type="text/javascript" src="${root}/script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript" language="javascript" src="${root}/script/negociacaoContrato_1.0.min.js"></script>

    <script src="${root}/beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
    <script src="${root}/beta/js/DT_bootstrap.js" type="text/javascript"></script>
    <script src="${root}/beta/js/bootstrap-tab.js" type="text/javascript"></script>
</head>
<script>
    jQuery.noConflict();
</script>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form" style="margin-bottom: 0px">

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../include_menu_showcase.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel" style="min-height: calc(100vh - 143px);">
                        <h:panelGroup id="conteudo" layout="block" styleClass="container-imagem container-conteudo-central" style="position:relative;">
                            <jsp:include page="${ShowCaseControle.paginaExibir}" flush="true"/>
                        </h:panelGroup>
                        <jsp:include page="../include_menu_components.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

    </h:form>
</f:view>


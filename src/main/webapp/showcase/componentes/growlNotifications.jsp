<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 22/06/2016
  Time: 16:26
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .title-space{
        margin-bottom: 15px;
    }
    .line-space{
        margin-bottom: 15px;
    }
    .group-space{
        margin-bottom: 100px;
    }
    .center-vertical{
        line-height: 50px;
    }
</style>
<h:panelGroup id="containerGrow" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabacalhoGrow" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Growl Notifications</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-3">
                    <div class="col-text-align-left col-md-12 title-space" style="padding-bottom: 5px;">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >IMPORT�NCIA</span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Toda a��o importante de um usu�rio deve
                            gerar feedback, essa ferramenta � de suma import�ncia para manter o usu�rio ciente de suas
                            a��es, bem como das respostas do sistema.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Uma a��o importante � toda aquela que
                            conclui um processo de navega��o, ou que causa impacto em a��es subsequentes.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">A notifica��o informativa (azul) foge �
                            regra, e pode ser usada em qualquer momento que houver uma informa��o interessante ao usu�rio,
                            como uma novidade no sistema, ou para fornecer alguma dica.
                        </span>
                    </div>
                </div>
                <div class="col-md-8 col-md-offset-1">
                    <div  class="linha group-space">
                        <div class="col-md-12">
                            <div class="col-md-1">
                                <div class="col-md-12 ">
                                    <a4j:commandLink styleClass="botaoPrimario texto-size-16" value="Erro" action="#{ShowCaseControle.abrirNotificacaoErro}" oncomplete="#{ShowCaseControle.mensagemNotificar}"/>
                                 </div>
                            </div>
                            <div class="col-md-2 col-md-offset-1">
                                <div class="col-md-12 line-space">
                                    <a4j:commandLink styleClass="botaoPrimario texto-size-16" value="Informativo" action="#{ShowCaseControle.abrirNotificacaoInformativa}" oncomplete="#{ShowCaseControle.mensagemNotificar}"/>
                                </div>
                            </div>
                            <div class="col-md-2 col-md-offset-1">
                                <div class="col-md-12 ">
                                    <a4j:commandLink styleClass="botaoPrimario texto-size-16" value="Sucesso" action="#{ShowCaseControle.abrirNotificacaoSucesso}" oncomplete="#{ShowCaseControle.mensagemNotificar}"/>
                                </div>
                            </div>
                            <div class="col-md-1 col-md-offset-1">
                                <div class="col-md-12 line-space">
                                    <a4j:commandLink styleClass="botaoPrimario texto-size-16" value="Alerta" action="#{ShowCaseControle.abrirNotificacaoAlerta}" oncomplete="#{ShowCaseControle.mensagemNotificar}"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div  class="linha">
                        <div class="col-md-12">
                            <div class="col-md-4">
                                <div class="col-text-align-left col-md-12 title-space" style="padding-bottom: 5px;">
                                    <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Todos os Growl seguem o seguinte estilo:</span>
                                </div>
                                <div class="col-md-12 line-space">
                                    <span class="texto-size-10 texto-cor-cinza texto-font">BG: #000, opacity: 0.8, drop-shadow: 0px 2px 43px 11px rgba(0,0,0,0,4)</span>
                                </div>
                                <div class="col-text-align-left col-md-12 title-space" style="padding-bottom: 5px;">
                                    <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Titulo:Estilo Subt�tulo +</span>
                                </div>
                                <div class="col-md-12 line-space">
                                    <span class="texto-size-10 texto-cor-cinza texto-font">#FFF</span>
                                </div>
                                <div class="col-text-align-left col-md-12 title-space" style="padding-bottom: 5px;">
                                    <span class="texto-size-14 texto-cor-cinza texto-font texto-bold" >Texto: Estilo Base +</span>
                                </div>
                                <div class="col-md-12 line-space">
                                    <span class="texto-size-10 texto-cor-cinza texto-font">#FFF, opacity: 0.8</span>
                                </div>
                            </div>
                            <div class="col-md-7 col-md-offset-1">
                                <div class="col-md-2 col-text-align-center texto-cor-branco texto-bold" style="background-color: #FF5555; min-height: 50px;">
                                    <div class="center-vertical">#FF5555</div>
                                </div>
                                <div class="col-md-2 col-md-offset-1 col-text-align-center texto-cor-branco texto-bold" style="background-color: #2BAF50; min-height: 50px;">
                                    <div class="center-vertical">#2BAF50</div>
                                </div>
                                <div class="col-md-2 col-md-offset-1 col-text-align-center texto-cor-branco texto-bold" style="background-color: #29ABE2; min-height: 50px;">
                                    <div class="center-vertical">#29ABE2</div>
                                </div>
                                <div class="col-md-2 col-md-offset-1 col-text-align-center texto-cor-branco texto-bold" style="background-color: #F6A700; min-height: 50px;">
                                    <div class="center-vertical">#F6A700</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>
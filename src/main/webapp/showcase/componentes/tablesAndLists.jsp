<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 27/06/2016
  Time: 16:48
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .line-space{
        margin-bottom: 10px;
    }
</style>
<h:panelGroup id="containerTablesLists" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoTablesLists" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Tables & Lists</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-3">
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">Se a informa��o n�o couber na c�lula da
                            seguinte forma:
                            <ul class="line-space">
                                <li class="line-space">Nomes pr�prios apresentar�o o primeiro nome, os demais ser�o
                                    abreviados(Ana J. P.);</li>
                                <li>Demais informa��es devem preencher o campo at� sua largura m�xima, ao alcan�a-la,
                                    ser�o abreviados com retic�ncias(Nata��o Adul...).</li>
                            </ul>
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Todas as informa��es devem ser alinhadas � esquerda, exceto informa��es monet�rias, que devem
                            ser alinhados � direita.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Informa��es monet�rias, de porcentagem, ou demais que possuam simbologia (R$, %, etc), devem
                            vir junto ao seu s�mbolo.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Caso exista, checkbox sempre � esquerda.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Sempre deve ser poss�vel organizar pelo t�tulo da coluna.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Sempre deve ser poss�vel organizar pelo t�tulo da coluna.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            A aquisi��o m�xima de uma lista � de 10 itens.
                        </span>
                    </div>
                </div>
                <div class="col-md-8 col-md-offset-1">
                    <rich:dataTable id="listaRecorrencia" width="100%"
                                    headerClass="consulta"
                                    columnClasses="" styleClass="tabelaSimplesCustom showCellEmpty"
                                    rows="100"
                                    value="#{ShowCaseControle.listaExemplo}" var="obj" >

                        <rich:column width="30%" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText value="Label" styleClass="texto-size-16 texto-cor-cinza text-bold "/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza" value="#{obj.codigo}"/>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column width="30%" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText value="Label"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza" value="#{obj.data}"/>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column style="text-align: right;margin-right: 4%;" >
                            <f:facet name="header">
                                <h:outputText value="Label" styleClass=""/>
                            </f:facet>
                            <h:panelGroup rendered="#{obj.valor != 0.0}">
                                <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza" style="font-weight:bold;" value=" R$ "/>
                                <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza" style="font-weight:bold;margin-right: 13%;"
                                              value="#{obj.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column styleClass="col-text-align-right">
                            <a4j:commandLink id="abrirobj" styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                             value="A��o (icon ou texto)">
                                <f:attribute name="obj" value="#{obj}"/>
                            </a4j:commandLink>
                        </rich:column>
                    </rich:dataTable>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>
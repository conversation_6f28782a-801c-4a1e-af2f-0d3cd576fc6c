<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 24/06/2016
  Time: 08:36
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../../includes/imports.jsp" %>
<style>
    .title-space{
        margin-bottom: 10px;
    }
    .line-space{
        margin-bottom: 15px;
    }
</style>
<h:panelGroup id="containerTextInput" layout="block" styleClass="container-box paginaFontResponsiva">
    <h:panelGroup id="cabecalhoTextInput" layout="block" styleClass="container-box-header">
        <h:panelGroup layout="block" styleClass="margin-box">
            <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Text Inputs</span>
        </h:panelGroup>
    </h:panelGroup>

    <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
    <div  class="margin-box" style="display: table;">
        <div  class="linha">
            <div class="col-md-12">
                <div class="col-md-3">
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            Tamanho de fonte referente � resolu��o 1366.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                            O primeiro contato do usu�rio com os inputs (estilo normal), deve fornecer informa��o breve
                            para o t�tulo e um complemento no placeholder.<br>O over cria uma intera��o, para garantir
                            que � um objeto clic�vel, ao clicar, o placeholder some e o usu�rio pode adicionar a
                            informa��o ao campo.
                        </span>
                    </div>
                    <div class="col-md-12 line-space">
                        <span class="texto-size-14 texto-cor-cinza texto-font">
                        </span>
                    </div>
                </div>
                <div class="col-md-8 col-md-offset-1">
                    <div class="col-md-6">
                        <div class="col-md-12 title-space">
                            <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">T�tulo</span>
                        </div>
                        <div class="col-md-12 line-space">
                            <h:inputText styleClass="inputTextClean" title="Campo de texto" value="Placeholder" />
                        </div>
                        <div class="col-md-12 title-space">
                            <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">T�tulo</span>
                        </div>
                        <div class="col-md-12 title-space">
                            <h:inputTextarea styleClass="inputTextClean" cols="30" rows="3" style="height: 50px;" value="Placeholder"></h:inputTextarea>
                        </div>
                        <div class="col-md-12 title-space ">
                            <div class="col-md-12 title-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">T�tulo</span>
                            </div>
                            <div class="col-md-3 cb-container margenVertical">
                                <h:selectOneMenu styleClass="" id="consulta" required="true"
                                                 value="Label">
                                    <f:selectItem itemValue="Label" />
                                    <f:selectItem itemValue="Label" />
                                    <f:selectItem itemValue="Label" />
                                </h:selectOneMenu>
                            </div>
                            <div class="col-md-3 title-space margenVertical">
                                <h:inputText styleClass="inputTextClean" title="Campo de texto" value="Placeholder" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="col-md-12 line-space">
                            <div class="col-md-3 line-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font">Normal</span>
                            </div>
                            <div class="col-md-9 line-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font">border: 1px, #E5E5E5, radius:3px</span>
                            </div>
                        </div>
                        <div class="col-md-12 line-space">
                            <div class="col-md-3 line-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font">Over</span>
                            </div>
                            <div class="col-md-9 line-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font">border: 1px, #9D9D9D, radius:3px</span>
                            </div>
                        </div>
                        <div class="col-md-12 line-space">
                            <div class="col-md-3 line-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font">Active</span>
                            </div>
                            <div class="col-md-9 line-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font">border: 1px, #29AAE2, radius: 3px,<br>shadow: 0px 0px 5px 0px <br>rgba(41, 170, 226, 0.8)</span>
                            </div>
                        </div>
                        <div class="col-md-12 line-space">
                            <div class="col-md-3 line-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font">Titulo</span>
                            </div>
                            <div class="col-md-9 line-space">
                                <span class="texto-size-14 texto-cor-cinza texto-font">arial, 12px, #777 (full caps)</span>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</h:panelGroup>

<%--
  Created by IntelliJ IDEA.
  User: fabio
  Date: 04/07/2016
  Time: 11:03
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<style>
    a.botaoShowCase{
        border-style: solid;
        border-width: 1px;
        border-color: #074871;
        padding: 32px 52px 32px 52px;
        border-radius: 4px;
        background-color: #074871; !important;
        color: #fff; !important;
        text-decoration: none; !important;
    }

    .botaoShowCase:hover{
        border-width: 1px;
        border-radius: 4px;
        border-style: solid;
        background-color: #0A3858;
        text-decoration: none;
        color: #fff;!important;
    }
</style>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form" style="margin-bottom: 0px">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_showcase.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel" style="min-height: calc(100vh - 143px);">
                        <h:panelGroup id="conteudo" layout="block" styleClass="" style="position:relative;" >
                            <h:panelGroup layout="block" styleClass="container-box paginaFontResponsiva" >
                                <h:panelGroup layout="block" styleClass="container-box-header">

                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <span class="texto-size-16 texto-cor-cinza-2 texto-font texto-bold">Show Case</span>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <%-----------------------------------------PADRAO GRIDS BOOTSTRAP-----------------------------------------%>
                                <div  class="margin-box" style="display: table;">
                                    <div  class="linha">
                                        <div class="col-md-12" style="margin-top: 60px; ">
                                            <div class="col-md-3 col-md-offset-2" style="padding-bottom: 80px;" >
                                                <a4j:commandLink action="#{ShowCaseControle.redirectShowCaseComponentes}"
                                                                 styleClass="botaoShowCase texto-size-16" style="min-width: 400px; !important;" >
                                                    <h:outputText value="Show Case Components"/>
                                                </a4j:commandLink>
                                            </div>
                                            <div class="col-md-3 " style="padding-bottom: 80px;">
                                                <a onclick="abrirPopupPDFImpressao('../downloads/DocumentoDeQualidade-ZW2016.pdf','', 1000, 600);"
                                                   class="botaoShowCase texto-size-16">&nbsp;&nbsp;Interface ZW 2016&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a>
                                            </div>
                                            <div class="col-md-3 " >
                                                <a onclick="abrirPopupPDFImpressao('../downloads/ZW-Padraodequalidades.pdf','', 1000, 600);"
                                                   class="botaoShowCase texto-size-16"><h:outputText value="Padrões de qualidade"/></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:form>
</f:view>

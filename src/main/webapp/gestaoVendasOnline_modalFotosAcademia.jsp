<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>

<style>
    .imagemacademia img{
        width: 100%;
    }
    .imagemacademia{
        vertical-align: top;
        display: inline-block;
        width: 150px;
        margin: 10px;
    }
</style>

<rich:modalPanel id="modalFotos" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="650" autosized="true" minHeight="200">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Imagens da academia"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkfotos"/>
            <rich:componentControl for="modalFotos" attachTo="hidelinkfotos" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form ajaxSubmit="true" id="mdfotos" style="margin-top: 10px;">
        <script>
            function sleep(milliseconds) {
                var start = new Date().getTime();
                for (var i = 0; i < 1e7; i++) {
                    if ((new Date().getTime() - start) > milliseconds) {
                        break;
                    }
                }
            }
        </script>

        <h:panelGroup>
            <h:outputText styleClass="texto-size-14 cinza" style="margin-left: 11px;"
                          value="As imagens que são incluídas aqui, irão aparecer somente no aplicativo do aluno, quando ele clicar em "/>
            <h:outputText styleClass="texto-size-14 cinza" style="margin-left: 11px"
                          value="\"Não sou aluno\" e pesquisar o nome da academia."/>
        </h:panelGroup>

        <h:panelGroup id="painelfotos" layout="block" style="margin: 20px 10px;">
            <a4j:repeat var="imagem" value="#{GestaoVendasOnlineControle.imagens}">
                <h:panelGroup layout="block" styleClass="imagemacademia">
                    <h:graphicImage url="#{imagem.urlFoto}"/>
                    <div style="text-align: center;margin-top: 15px">
                        <a4j:commandLink style="font-size: 20px;" action="#{GestaoVendasOnlineControle.removerImagem}" reRender="painelfotos">
                            <i class="fa-icon-trash"></i>
                        </a4j:commandLink>
                    </div>
                </h:panelGroup>
            </a4j:repeat>


            <div style="margin-top: 30px; margin-bottom: 30px">
                <rich:fileUpload listHeight="0" listWidth="91" noDuplicate="false"
                                 fileUploadListener="#{GestaoVendasOnlineControle.uploadImagem}" maxFilesQuantity="1"
                                 addControlLabel="Adicionar" cancelEntryControlLabel="Cancelar" doneLabel="Pronto"
                                 sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 10 MB"
                                 progressLabel="Enviando" stopControlLabel="Parar" uploadControlLabel="Enviar"
                                 transferErrorLabel="Falha de Transmissão" stopEntryControlLabel="Parar"
                                 id="uploadAssinaturaDigital" immediateUpload="true" autoclear="true"
                                 acceptedTypes="png,gif,jpg,jpeg,ico,bmp,PNG,GIF,JPG,JPEG,ICO,BMP">

                    <a4j:support event="onuploadcomplete" reRender="painelfotos"
                                 oncomplete="sleep(2000);" />
                    <a4j:support event="oncomplete" reRender="painelfotos"
                                 oncomplete="sleep(2000);" />
                </rich:fileUpload>
            </div>


        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

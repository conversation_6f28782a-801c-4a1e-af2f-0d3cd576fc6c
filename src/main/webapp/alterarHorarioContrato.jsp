<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);

     window.addEventListener("load", function (event) {
         executePostMessage({loaded: true})
     });
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }

    td.w48{
        width: 48%;
    }

</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Alterar Horário Contrato" />
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

   <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Alterar Horário"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-mudo-o-aluno-de-um-horario-livre-para-economico/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
   </h:panelGrid>

    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

    <h:form id="form">
         <h:panelGrid  columns="1" styleClass="font-size-em-max" style="width: 100%">
                    <h:panelGrid  rendered="#{AlterarHorarioContratoControle.planoVencido}" columns="1"  width="100%">
                        <br><br>
                        <h:outputText styleClass="tituloCamposAzulGrande" value="Não será possível modificiar o Horário do contrato, pois o plano está vencido ou ainda nem começou. "/>
                        <br><br>
                        <h:panelGrid>
                            <h:panelGroup >
                                <h:commandButton id="fechar1" alt="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});" image="./images/btn_fechar.gif" />
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid id="pnlValores" rendered="#{!AlterarHorarioContratoControle.planoVencido}" columns="1" width="100%" styleClass="font-size-em-max">
                        <h:panelGrid columns="1" styleClass="font-size-em-max">
                            <h:outputText value="NOME DO CLIENTE:" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" />
                            <h:outputText id="nomeClienteHr" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AlterarHorarioContratoControle.contratoAntigo.pessoa.nome}"/>
                        </h:panelGrid>
                        <rich:spacer height="5px"/>
                        <h:panelGroup>
                            <h:outputText value="DETALHES DO CONTRATO " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                            <rich:dataTable id="contrato" styleClass="tabelaDados semZebra" style="margin: 0; width: 100%;" width="100%" rowClasses="tablelistras textsmall"
                                            value="#{AlterarHorarioContratoControle.listaContrato}" var="contrato">
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_numeroContrato}"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AlterarHorarioContratoControle.contratoAntigo.codigo}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_plano}"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.plano.descricao}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_dataInicio}"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaDe_Apresentar}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_dataTermino}"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada_Apresentar}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="MODALIDADES"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.nomeModalidades}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DURAÇÃO EM MESES"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.contratoDuracao.numeroMeses}"/>
                                </rich:column>
                                <rich:column>
                                    <f:facet name="header">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold tituloCaixaAlta" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_valorBaseContrato}"/>
                                    </f:facet>
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.valorBaseCalculo}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                </rich:column>
                            </rich:dataTable>
                        </h:panelGroup>

                            <h:outputText value="DATA OPERAÇÃO" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                            <rich:spacer height="10px"/>
                               <h:panelGroup>
                                   <h:panelGroup id="pnlBoolDataOperacao" styleClass="chk-fa-container" style="display:inline-block" >
                                       <h:selectBooleanCheckbox value="#{AlterarHorarioContratoControle.apresentarDataOperacao}" id="alteraDataOperacao">
                                           <a4j:support event="onchange" action="#{AlterarHorarioContratoControle.autorizarApresentarDataOperacao}"
                                                        reRender="panelAutorizacaoFuncionalidade, pnlBoolDataOperacao, pnlCalDataOperacao, tblHorarioAntigo,pnlHorarioAntigo,pnlValores"/>
                                       </h:selectBooleanCheckbox>
                                       <span></span>
                                   </h:panelGroup>
                                   <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="ALTERAR DATA DA OPERAÇÃO"/>
                               </h:panelGroup>
                                    <h:panelGrid id="pnlCalDataOperacao" styleClass="font-size-em-max">
                                        <h:panelGroup rendered="#{AlterarHorarioContratoControle.apresentarDataOperacao}">
                                            <h:panelGrid columns="1">
                                                <h:outputText value="DATA OPERAÇÃO" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                                                <h:panelGroup styleClass="dateTimeCustom font-size-em-max" >
                                                    <rich:calendar id="dataOperacao"
                                                                   value="#{AlterarHorarioContratoControle.dataOperacao}"
                                                                   inputSize="10"
                                                                   inputClass="form"
                                                                   oninputblur="blurinput(this);"
                                                                   oninputfocus="focusinput(this);"
                                                                   oninputchange="return validar_Data(this.id);"
                                                                   datePattern="dd/MM/yyyy"
                                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                                   enableManualInput="true"
                                                                   zindex="2"
                                                                   showWeeksBar="false">
                                                        <a4j:support event="onchanged" actionListener="#{AlterarHorarioContratoControle.alterarDataOperacao}"
                                                                     reRender="tblHorarioAntigo,pnlHorarioAntigo,pnlValores"/>
                                                    </rich:calendar>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </h:panelGroup>
                                    </h:panelGrid>
                        <rich:spacer height="5px"/>
                        <h:panelGrid id="tblHorarioAntigo" width="100%" border="0" cellspacing="0" cellpadding="0">
                            <h:outputText value="HORÁRIO ANTIGO " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                            <rich:spacer height="10px"/>
                            <h:panelGrid  id="pnlHorarioAntigo" columns="3" columnClasses="cinza texto-size-14 w48,w4 semBorda, cinza texto-size-14 w48"  styleClass="col-text-align-left font-size-em-max" width="100%">
                                        <h:panelGroup>
                                            <h:outputText value="NOME: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                                            <h:outputText id="hrAntigo" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AlterarHorarioContratoControle.contratoAntigo.contratoHorario.horario.descricao}"/>
                                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        </h:panelGroup>
                                        <h:panelGroup></h:panelGroup>
                                        <h:panelGroup>
                                            <h:outputText id="diasTotalCont" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="QUANTIDADE DE DIAS CONTRATO: "/>
                                            <h:outputText id="nDias" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AlterarHorarioContratoControle.nrDiasContrato} "/>
                                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        </h:panelGroup>
                                        <%--h:panelGroup>
                                            <h:outputText value="Valor dia para horário: "/>
                                            <rich:spacer width="5px"/>
                                            <h:outputText styleClass="tituloCamposVerde"value="R$ "/>
                                            <h:outputText styleClass="tituloCamposVerde" value="#{AlterarHorarioContratoControle.valorDiaHorarioAntigo}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </h:panelGroup>
                                        <h:panelGroup>
                                            <h:outputText value="Saldo restante valor horário: "/>
                                            <rich:spacer width="5px"/>
                                            <h:outputText styleClass="tituloCamposVerde" value="R$ "/>
                                            <h:outputText  styleClass="tituloCamposVerde" value="#{AlterarHorarioContratoControle.valorRestanteHorarioAntigo}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                        </h:panelGroup--%>
                                        <h:panelGroup>
                                            <h:outputText value="DIAS UTILIZADOS DO CONTRATO: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                                            <h:outputText id="diasUtilizadosCont" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AlterarHorarioContratoControle.nrDiasContratoUtilizado}"/>
                                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        </h:panelGroup>
                                        <h:panelGroup></h:panelGroup>
                                        <h:panelGroup>
                                            <h:outputText value="DIAS RESTANTES DO CONTRATO: " styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                                            <h:outputText id="diasRestantesCont" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{AlterarHorarioContratoControle.restanteDiasContrato}"/>
                                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        </h:panelGroup>
                                    </h:panelGrid>


                        </h:panelGrid>
                        <h:panelGroup rendered="#{AlterarHorarioContratoControle.jaUtilizouTodosOsDiasUteis}">
                            <h:outputText id="msgContratoEmBonus" styleClass="mensagemDetalhada"
                                          value="*Aluno já utilizou os dias úteis desse contrato, portanto alterações de horário não serão calculadas" />
                        </h:panelGroup>
                            <h:outputText value="HORÁRIOS DISPONÍVEIS" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                                 <rich:spacer height="10px"/>
                            <h:outputText value="SELECIONE NOVO HORÁRIO" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                            <rich:dataTable id="planoHorarioVO" width="100%" rowClasses="linhaImpar" columnClasses="colunaEsquerda"
                                            value="#{AlterarHorarioContratoControle.novoPlanoHorario.planoHorarioVOs}"  var="planoHorario">
                                <rich:column width="50%">
                                    <h:selectBooleanCheckbox id="selectHorarioCont" disabled="#{planoHorario.apresentarValor}" value="#{planoHorario.horario.horarioEscolhida}">
                                        <a4j:support event="onclick" action="#{AlterarHorarioContratoControle.selecionarHorario}" reRender="panelGeral, planoHorarioVO, panelMensagem,panelDescricao,alterarValor"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText  value="#{planoHorario.horario.descricao} " />
                                </rich:column>
                                <rich:column width="25%" rendered="#{planoHorario.apresentarValorEspecifico || planoHorario.apresentarValorDesconto }">
                                    <h:outputText value="#{planoHorario.tipoOperacao_Apresentar} "/>
                                </rich:column>
                                <rich:column width="25%" rendered="#{planoHorario.apresentarValorEspecifico}">
                                    <h:outputText value="R$ "/>
                                    <h:outputText value="#{planoHorario.valorEspecifico}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                </rich:column>
                                <rich:column width="25%"  rendered="#{planoHorario.apresentarValorDesconto}">
                                    <h:outputText value="#{planoHorario.percentualDesconto} ">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                    <h:outputText value=" %"/>
                                </rich:column>
                            </rich:dataTable>
                            <rich:spacer height="10px"/>
                            <h:panelGrid id="panelGeral" width="100%" columns="1" cellspacing="0" cellpadding="0" >
                                <h:panelGrid rendered="#{AlterarHorarioContratoControle.apresentarDadosNovoHorario}" width="100%" columns="1" cellspacing="0" cellpadding="0" styleClass="fonteTrebuchet">
                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="VALOR DO CONTRATO NO HORÁRIO: #{AlterarHorarioContratoControle.contratoNovo.planoHorario.horario.descricao}: "/>
                                        <rich:spacer width="5px"/>
                                        <h:outputText styleClass="tituloCamposVerde" value="R$ "/>
                                        <h:outputText id="valorNovoHorario" styleClass="tituloCamposVerde" value="#{AlterarHorarioContratoControle.contratoNovo.valorBaseCalculo}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="VALOR DA DIFERENÇA: "/>
                                        <rich:spacer width="px"/>
                                        <h:outputText styleClass="tituloCamposVerde" value="R$ "/>
                                        <h:outputText id="valorDiferenca" styleClass="tituloCamposVerde" value="#{AlterarHorarioContratoControle.valorAlteracaoHorarioNovo}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="VALOR DIA PARA HORÁRIO: "/>
                                        <rich:spacer width="5px"/>
                                        <h:outputText styleClass="tituloCamposVerde" value="R$ "/>
                                        <h:outputText id="valorDiferencaPorDia" styleClass="tituloCamposVerde" value="#{AlterarHorarioContratoControle.valorDiaHorarioNovo}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText rendered="#{AlterarHorarioContratoControle.jaUtilizouTodosOsDiasUteis}" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="VALOR HORÁRIO PARA 0 DIA(S): "/>
                                        <h:outputText  rendered="#{!AlterarHorarioContratoControle.jaUtilizouTodosOsDiasUteis}" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="VALOR HORÁRIO PARA #{AlterarHorarioContratoControle.restanteDiasContrato} DIA(S): "/>
                                        <h:outputText styleClass="tituloCamposVerde" value="R$ "/>
                                        <rich:spacer width="5px"/>
                                        <h:outputText id="valorParaDiasRestantes"  styleClass="tituloCamposVerde" value="#{AlterarHorarioContratoControle.valorHorarioNovo}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:outputText>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGrid>
                            <rich:spacer height="10px"/>
                            <h:panelGrid id="panelDescricao" width="100%" columns="1" cellspacing="0" cellpadding="0" >
                            <h:panelGrid rendered="#{AlterarHorarioContratoControle.apresentarDadosNovoHorario}" styleClass="font-size-em-max">
                                <%--h:outputText  rendered="#{AlterarHorarioContratoControle.apresentarSaldoRestanteMaiorHorarioNovo}"
                                styleClass="tituloCamposAzulGrande" value="Valor = (Saldo restante valor horário - Valor horário para #{AlterarHorarioContratoControle.restanteDiasContrato} dia(s) )"/>
                                <h:outputText  rendered="#{AlterarHorarioContratoControle.apresentarHorarioNovoMaiorSaldoRestante}" styleClass="tituloCamposAzulGrande"
                                value="Valor = (Valor horário para #{AlterarHorarioContratoControle.restanteDiasContrato} dia(s) - Saldo restante valor horário )"/--%>
                                <h:panelGroup rendered="#{AlterarHorarioContratoControle.apresentarDepositoEmConta}">
                                    <h:outputText id="textoDevolucao" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="VALOR A DEPOSITAR NA CONTA CORRENTE OU ABATER NAS PARCELAS EM ABERTO DO ALUNO: "/>
                                    <h:outputText styleClass="tituloCamposVerdeGrande" value=" R$ "/>
                                    <h:outputText id="valorDevolucao" styleClass="tituloCamposVerdeGrande" value="#{AlterarHorarioContratoControle.valorTotalAlteracaoHorario}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                </h:panelGroup>
                                <h:panelGroup rendered="#{AlterarHorarioContratoControle.apresentarParcela}" >
                                    <h:outputText id="textoPagamento" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="VALOR DA PARCELA A SER GERADA: "/>
                                    <h:outputText styleClass="tituloCamposVerdeGrande" value=" R$ "/>
                                    <h:outputText id="valorPagamento" styleClass="tituloCamposVerdeGrande" value="#{AlterarHorarioContratoControle.valorTotalAlteracaoHorario}">
                                        <f:converter converterId="FormatadorNumerico"/>
                                    </h:outputText>
                                </h:panelGroup>
                            </h:panelGrid>
                            </h:panelGrid>
                        <h:panelGrid id="panelMensagem" columns="3" width="100%" >
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText id="msgAlterarHorario" styleClass="mensagem"  value="#{AlterarHorarioContratoControle.mensagem}"/>
                                <h:outputText id="msgAlterarHorarioDet" styleClass="mensagemDetalhada" value="#{AlterarHorarioContratoControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGroup id="alterarValor" >
                            <h:panelGrid rendered="#{AlterarHorarioContratoControle.apresentarDadosNovoHorario}" columns="2" styleClass="font-size-em-max">
                                <h:panelGroup>
                                    <h:panelGroup styleClass=" chk-fa-container" style="display:inline-block" >
                                        <h:selectBooleanCheckbox id="liberacaoAlterarHorario" value="#{AlterarHorarioContratoControle.abrirRichConfirmacaoLiberacao}">
                                            <a4j:support event="onclick" action="#{AlterarHorarioContratoControle.autorizarLiberacao}"
                                                         reRender="panelAutorizacaoFuncionalidade, panelGeral, alterarValor"/>
                                        </h:selectBooleanCheckbox>
                                        <span/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="LIBERAR"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:panelGroup styleClass="chk-fa-container" style="display:inline-block" >
                                        <h:selectBooleanCheckbox id="liberacaoAlterarValor" value="#{AlterarHorarioContratoControle.alterarValor}">
                                            <a4j:support event="onclick" reRender="panelGeral,alterarValor"/>
                                        </h:selectBooleanCheckbox>
                                        <span/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="ALTERAR VALOR"/>
                                </h:panelGroup>
                                <h:inputText id="valorNovo" rendered="#{AlterarHorarioContratoControle.alterarValor}" size="8" value="#{AlterarHorarioContratoControle.valorTotalAlteracaoValor}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="inputTextClean" style="margin-left: 8px">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:inputText>
                                <a4j:commandLink id="aplicarvalorNovo" value="Aplicar"
                                                   rendered="#{AlterarHorarioContratoControle.alterarValor}"
                                                   action="#{AlterarHorarioContratoControle.aplicarAlterarValor}"
                                                   styleClass="pure-button"
                                                   reRender="form, panelAutorizacaoFuncionalidade"/>
                            </h:panelGrid>
                        </h:panelGroup>
                        <h:panelGroup id="panelBotoes" >
                            <h:panelGrid
                                    rendered="#{AlterarHorarioContratoControle.apresentarBotao && !AlterarHorarioContratoControle.processandoOperacao}"
                                    columns="3">
                                <a4j:commandLink id="confirmar" title="Finalizar"
                                                 action="#{AlterarHorarioContratoControle.gravar}"
                                                 reRender="form,panelAutorizacaoFuncionalidade, panelGeral "
                                                 styleClass="pure-button pure-button-primary">
                                    <i class="fa-icon-ok"></i>&nbsp;Confirmar
                                </a4j:commandLink>

                                <h:commandLink id="cancelar" onclick="fecharJanela();executePostMessage({close: true});" styleClass="pure-button">
                                    <i class="fa-icon-remove"></i>&nbsp;Fechar
                                </h:commandLink>
                            </h:panelGrid>
                            <h:panelGroup  rendered="#{!AlterarHorarioContratoControle.apresentarBotao && !AlterarHorarioContratoControle.processandoOperacao}">
                                <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});" styleClass="pure-button">
                                    &nbsp;Fechar
                                </h:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                         <h:panelGrid rendered="#{AlterarHorarioContratoControle.processandoOperacao}" columns="1">
                            <h:outputText id="msgProcessando" styleClass="mensagem"  value="Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída"/>
                            <rich:spacer height="7"/>
                            <a4j:commandLink id="atualizar" title="Atulaizar" onclick="window.location.reload();fireElementFromParent('form:btnAtualizaCliente');"  styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                            </a4j:commandLink>
                         </h:panelGrid>                    
                    </h:panelGrid>
                </h:panelGrid>
         <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </h:form>

    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>

</f:view>

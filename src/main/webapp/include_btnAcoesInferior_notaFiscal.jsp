<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@include file="./includes/imports.jsp" %>

<style>
    .panelBtnAcoesNotaFiscal {
        padding: 20px;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        height: 40px;
    }

</style>

<div class="panelBtnAcoesNotaFiscal">
    
        <a4j:commandLink id="sincronizarTodasNotasComEnotas" 
                         styleClass="botoes nvoBt btSec tooltipster"
                         action="#{NotaFiscalControle.sincronizarTodasNotasComEnotas}"
                         rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete};addPlaceHolderNotaFiscal()"
                         value="Sincronizar Todas Notas"
                         title="Consulta e sincroniza todas as notas filtradas com o eNotas"/>

    
        <a4j:commandLink id="prepararInutilizarNotasSelecionadas" 
                         styleClass="botoes nvoBt btSec tooltipster"
                         action="#{NotaFiscalControle.prepararInutilizarNotasSelecionadas}"
                         rendered="#{LoginControle.permissaoAcessoMenuVO.permiteInutilizarNotaFiscal && NotaFiscalControle.apresentarInutilizarMarcadas}"
                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete};addPlaceHolderNotaFiscal()"
                         reRender="formInu"
                         value="Inutilizar"
                         title="Solicitar inutilização das notas selecionadas"/>

    
        <a4j:commandLink id="prepararCancelarNotasSelecionadas"
                         styleClass="botoes nvoBt btSec tooltipster"
                         style="background-color: darkred; color: #fff;"
                         rendered="#{LoginControle.permissaoAcessoMenuVO.permiteCancelarNotaFiscal && NotaFiscalControle.apresentarCancelarMarcadas}"
                         action="#{NotaFiscalControle.prepararCancelarNotasSelecionadas}"
                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete};addPlaceHolderNotaFiscal()"
                         reRender="formCance:panelCancelarNotas"
                         value="Cancelar"
                         title="Solicitar cancelamento das notas selecionadas"/>

    
        <a4j:commandLink id="prepararAlterarStatusNota"
                         styleClass="botoes nvoBt btSec tooltipster"
                         style="background-color: darkred; color: #fff;"
                         rendered="#{LoginControle.permissaoAcessoMenuVO.permiteAlterarStatusNotaFiscal}"
                         action="#{NotaFiscalControle.prepararAlterarStatusNotasSelecionadas}"
                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete};addPlaceHolderNotaFiscal()"
                         reRender="formAlterarStatusNotas:panelAlterarStatusNotas"
                         value="Alterar Status Nota"
                         title="Alterar status das notas selecionadas"/>

    
        <a4j:commandLink id="downloadPDFSelecionadas"
                         styleClass="botoes nvoBt tooltipster" 
                         action="#{NotaFiscalControle.downloadPDFSelecionadas}"
                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"
                         reRender="form:panelENotas, form:menuLateralNotaFiscal,modalSolicitacoes"
                         value="Download PDF"
                         title="Download do PDF das notas selecionadas"/>

        
            <a4j:commandLink id="downloadXMLSelecionadas" 
                             styleClass="botoes nvoBt tooltipster"
                             action="#{NotaFiscalControle.downloadXMLSelecionadas}"
                             oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"
                             reRender="form:panelENotas, form:menuLateralNotaFiscal,modalSolicitacoes"
                             value="Download XML"
                             title="Download do XML das notas selecionadas"/>

            
                <a4j:commandLink id="excelNotasFiscaisInferior" 
                                 rendered="#{not empty NotaFiscalControle.listaNotas}"
                                 styleClass="botoes nvoBt tooltipster"
                                 actionListener="#{NotaFiscalControle.exportarExcel}"
                                 title="Exportar lista para o formato excel"
                                 oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','NotasFiscais', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="prefixo" value="NotasFiscais"/>
                    <f:attribute name="atributos"
                                 value="#{NotaFiscalControle.atributosExcel}"/>
                    <h:outputText styleClass="btn-print-2 excel tooltipster"
                                  style="color: #fff; font-size: 12px;"/>
                    <h:outputText style="padding-left: 5px; font-family: Arial,Verdana,sans-serif; font-size: 12px;" value="Excel"/>
                </a4j:commandLink>

</div>


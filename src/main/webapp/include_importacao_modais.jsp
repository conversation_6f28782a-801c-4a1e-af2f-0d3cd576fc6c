<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="includes/imports.jsp" %>

<rich:modalPanel id="modalConfirmarImportacao" autosized="true"
                 onshow="adicionarPlaceHolderImportacao()"
                 shadowOpacity="true" width="600" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Importação de #{ImportacaoControle.nomeAbaAtual}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalConfirmarImportacao"/>
            <rich:componentControl for="modalConfirmarImportacao"
                                   attachTo="hideLinkModalConfirmarImportacao" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formModImpo">
        <h:panelGroup layout="block" id="panelGeralModalConfirmarImportacao" style="padding: 20px;">

            <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                          style="width: 100%; text-align: center;">
                <a4j:repeat var="imp" value="#{ImportacaoControle.listaTotalImportacao}">
                    <h:outputText value="Total de"
                                  style="font-size: 14px;"/>
                    <h:outputText value="#{imp.quantidade}"
                                  style="font-size: 14px; font-weight: bold; padding-left: 5px; padding-right: 5px"/>
                    <h:outputText value="#{imp.nome} para importar"
                                  style="font-size: 14px;"/>
                    <br/>
                    <br/>
                </a4j:repeat>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelEmailAdd" style="text-align: center">
                <h:inputText id="emailAdicionar" size="30"
                             value="#{ImportacaoControle.emailAdicionar}"
                             onkeypress="adicionarEmail()"
                             title="Informe um email para receber o resultado da importação"
                             styleClass="passosImportacaoDescricao tooltipster"/>
                <a4j:commandLink id="adicionarEmail"
                                 styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                 onclick="atualizarTempoImportacao()"
                                 action="#{ImportacaoControle.adicionarEmail}"
                                 oncomplete="#{ImportacaoControle.mensagemNotificar};adicionarPlaceHolderImportacao()"
                                 reRender="panelGeralModalConfirmarImportacao">
                    <i class="fa-icon-plus-sign" style="font-size: large;  color: #000"> </i>
                </a4j:commandLink>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelEmailsImportacao" style="padding-top: 7px;"
                          rendered="#{not empty ImportacaoControle.listaEmails}">
                <rich:dataTable width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{ImportacaoControle.listaEmails}" rows="5"
                                var="email">
                    <rich:column>
                        <h:panelGroup>
                            <h:outputText value="#{email}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <h:panelGroup>
                            <a4j:commandLink id="removerEmail"
                                             styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                             action="#{ImportacaoControle.removerEmail}"
                                             onclick="atualizarTempoImportacao()"
                                             oncomplete="#{ImportacaoControle.mensagemNotificar};adicionarPlaceHolderImportacao()"
                                             reRender="panelEmailsImportacao">
                                <i class="fa-icon-minus-sign"
                                   style="font-size: large;  color: #000"> </i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block" style="padding: 15px; text-align: center;">
            <a4j:commandLink id="iniciarImportacao"
                             value="Iniciar Importação de #{ImportacaoControle.nomeAbaAtual}"
                             styleClass="botoes nvoBt"
                             reRender="form"
                             onclick="atualizarTempoImportacao()"
                             action="#{ImportacaoControle.iniciarImportacao}"
                             oncomplete="#{ImportacaoControle.mensagemNotificar};#{ImportacaoControle.onComplete}"/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalImportacaoLog" autosized="true"
                 shadowOpacity="true" width="800" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Log"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalImportacaoLog"/>
            <rich:componentControl for="modalImportacaoLog"
                                   attachTo="hideLinkModalImportacaoLog" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form>
        <h:panelGrid width="100%" style="text-align: right;">
            <h:panelGroup layout="block">
                <a4j:commandButton id="exportarExcel"
                                   image="imagens/btn_excel.png"
                                   actionListener="#{ExportadorListaControle.exportar}"
                                   value="Excel"
                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                   accesskey="2" styleClass="botoes">
                    <f:attribute name="lista" value="#{ImportacaoControle.logImportacao}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="dataRegistroApresentar=Data,tipoLog.descricao=Tipo,mensagem=Log"/>
                    <f:attribute name="prefixo" value="LogImportacao"/>
                </a4j:commandButton>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGroup layout="block" id="panelGeralModalImportacaoLog" style="padding: 0px;">
            <rich:dataTable width="100%" headerClass="consulta" id="tblLog"
                            rowClasses="linhaImpar, linhaPar"
                            columnClasses="colunaAlinhamento,colunaAlinhamento,colunaEsquerda"
                            value="#{ImportacaoControle.logImportacao}" rows="10"
                            var="log">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Data"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{log.dataRegistroApresentar}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Tipo"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText
                                title="#{log.tipoLog.descricao}"
                                styleClass="#{log.tipoLog.styleClass} tooltipster"
                                style="#{log.tipoLog.style}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Log"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:inputTextarea styleClass="campos" readonly="true" cols="90" rows="2" id="descricao" value="#{log.mensagem}"/>
                    </h:panelGroup>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller for="tblLog" maxPages="20" page="#{ImportacaoControle.scrollerPage}"
                               rendered="#{ImportacaoControle.logImportacaoSize > 10}"/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalImportacaoDetalhe" autosized="true"
                 shadowOpacity="true" width="800" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Detalhes"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalImportacaoDetalhe"/>
            <rich:componentControl for="modalImportacaoDetalhe"
                                   attachTo="hideLinkModalImportacaoDetalhe" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form>
        <h:panelGroup layout="block" id="panelGeralModalImportacaoDetalhe" style="padding: 20px;">
            <rich:dataTable width="100%" headerClass="consulta" id="tblItem"
                            rowClasses="linhaImpar, linhaPar"
                            columnClasses="colunaEsquerda,colunaAlinhamento,colunaAlinhamento,colunaEsquerda"
                            value="#{ImportacaoControle.itensImportacao}" rows="10"
                            var="det">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Nome"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{det.nome}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Código Gerado"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{det.codigoGerado}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Importado"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{det.importado}"/>
                    </h:panelGroup>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Mensagem"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{det.msgRetorno}"/>
                    </h:panelGroup>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller for="tblItem" maxPages="20" page="#{ImportacaoControle.scrollerPage}"
                               rendered="#{ImportacaoControle.itensImportacaoSize > 10}"/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

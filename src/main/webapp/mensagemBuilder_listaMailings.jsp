<%@include file="includes/imports.jsp" %>

<h:panelGroup style="margin-top: 20px;" layout="block" rendered="#{MensagemBuilderControle.visaoAgendados and not MensagemBuilderControle.building}">
    <h:panelGroup styleClass="novaModal" id="filtrosMailing" layout="block">
        <div class="block">
            <div class="inlineBlock" style="margin-right: 10px;">
                <h:outputText rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}"
                              styleClass="tituloCampos  upper flex" value="Empresa:"/>

                <h:panelGroup layout="block" styleClass="cb-container"
                              rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}">
                    <h:selectOneMenu styleClass="form" id="empresa"
                                     value="#{MalaDiretaControle.filtroEmpresa}">
                        <f:selectItems value="#{MalaDiretaControle.listaEmpresas}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </div>
        </div>


        <div class="block">
            <div class="inlineBlock" style="margin-right: 10px;">
                <h:outputText styleClass="tituloCampos upper flex" value="C�digo"/>
                <h:inputText id="campoCodigo" styleClass="form" size="10"
                             value="#{MalaDiretaControle.codigoMailing}"/>
            </div>
        </div>


        <div class="block">
            <div class="inlineBlock" style="margin-right: 10px;">
                <h:outputText styleClass="tituloCampos upper flex" style="margin-top: 6px" value="T�tulo"/>
                <h:inputText id="campoTitulo" styleClass="form" size="40"
                             value="#{MalaDiretaControle.consultarDescricao}"/>
            </div>
            <div class="inlineBlock" style="margin-right: 10px;">
                <h:outputText styleClass="tituloCampos upper flex" value="Remetente"/>
                <h:inputText styleClass="form" size="40"
                             value="#{MalaDiretaControle.consultarRemetente}"/>
            </div>
        </div>


        <div class="block">
            <div class="inlineBlock" style="margin-right: 10px;">
                <h:outputText styleClass="tituloCampos  upper flex" value="TIPO"/>
                <div class="cb-container">
                    <h:selectOneMenu value="#{MalaDiretaControle.codigoTipoAgendamento}"
                                     style="width: 140px;margin-left: 2px;">
                        <f:selectItems value="#{MalaDiretaControle.tiposAgendamento}"/>
                        <a4j:support event="onchange" reRender="filtrosMailing"/>
                    </h:selectOneMenu>
                </div>
            </div>
            <div class="inlineBlock">
                <h:outputText styleClass="tituloCampos  upper flex" value="VIG�NCIA"
                              rendered="#{MalaDiretaControle.agendamentoOuTodos}"/>
                <h:panelGroup rendered="#{MalaDiretaControle.agendamentoOuTodos}" styleClass="cb-container"
                              layout="block">
                    <h:selectOneMenu style="width: 140px; margin-left: 2px;"
                                     value="#{MalaDiretaControle.tipoVigencia}">
                        <f:selectItems value="#{MalaDiretaControle.tiposVigencia}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </div>
        </div>
        <div class="block">
            <div class="inlineBlock" style="margin-right: 15px;">
                <h:panelGroup id="periodoCriacao">
                    <h:outputText styleClass="tituloCampos upper flex" value="PER�ODO DE CRIA��O"/>
                    <h:panelGroup styleClass="flex" layout="block">
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">
                            <rich:calendar value="#{MalaDiretaControle.dataInicialCriacao}"
                                           inputSize="10"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           oninputchange="return validar_Data(this.id);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>

                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      style="margin: 10px;"
                                      value="#{msg_aplic.prt_ate}"/>

                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">

                            <rich:calendar value="#{MalaDiretaControle.dataFinalCriacao}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true" showWeeksBar="false"
                                           zindex="2"/>
                        </h:panelGroup>

                        <a4j:commandLink action="#{MalaDiretaControle.limparPeriodoCriacao}"
                                         style="position:relative; top:7px; left:5px; font-size: 11px;"
                                         reRender="periodoCriacao"
                                         title="Limpar per�odo de compensa��o.">
                            <i class="fa-icon-eraser"></i>
                        </a4j:commandLink>

                    </h:panelGroup>
                </h:panelGroup>
            </div>
            <div class="inlineBlock">
                <h:panelGroup id="periodoExecucao"
                              rendered="#{MalaDiretaControle.consultaAgendamentos || MalaDiretaControle.consultaAvulsos}">
                    <h:outputText styleClass="tituloCampos upper flex" value="PER�ODO DE EXECU��O"
                                  style="margin-top: 8px"
                                  rendered="#{MalaDiretaControle.consultaAgendamentos}"/>
                    <h:outputText styleClass="tituloCampos upper flex" value="PER�ODO DE ENVIO" style="margin-top: 8px"
                                  rendered="#{MalaDiretaControle.consultaAvulsos}"/>

                    <h:outputText/>
                    <h:panelGroup styleClass="flex" layout="block">
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">
                            <rich:calendar value="#{MalaDiretaControle.dataInicial}"
                                           inputSize="10"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           oninputchange="return validar_Data(this.id);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>

                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      style="margin: 10px;"
                                      value="#{msg_aplic.prt_ate}"/>

                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">

                            <rich:calendar value="#{MalaDiretaControle.dataFinal}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true" showWeeksBar="false"
                                           zindex="2"/>
                        </h:panelGroup>

                        <a4j:commandLink action="#{MalaDiretaControle.limparPeriodoExecucao}"
                                         style="position:relative; top:7px; left:5px; font-size: 11px;"
                                         reRender="periodoExecucao"
                                         title="Limpar per�odo de compensa��o.">
                            <i class="fa-icon-eraser"></i>
                        </a4j:commandLink>

                    </h:panelGroup>
                </h:panelGroup>
            </div>
        </div>


    </h:panelGroup>

    <div style="margin-top: 20px; ">
        <a4j:commandButton id="consultarAtivos" styleClass="botoes nvoBt "
                           value="Aplicar filtros"
                           action="#{MalaDiretaControle.consultarPaginadoInit}"
                           title="#{msg.msg_consultar_dados}" accesskey="2"
                           reRender="itens, painelPaginacaoOUT, panelGridMensagens, paginacaoTela"/>
    </div>

    <a4j:region ajaxListener="#{MalaDiretaControle.consultaPaginadoOrdenacao}">
        <rich:dataTable reRender="paginador" id="itens" width="100%" headerClass="consulta"
                        columnClasses="columnClass"
                        styleClass="pure-g-r pure-u-11-12 margin-0-auto dataTable tableCliente"
                        style="margin-top: 10px; width: 100%"
                        value="#{MalaDiretaControle.listaConsulta}"
                        binding="#{MalaDiretaControle.htmlDataTable}"
                        rows="10" var="malaDireta">

        <rich:column id="ml_codigo" sortBy="#{malaDireta.codigo}">
            <f:facet name="header">
                <h:outputText value="C�digo"/>
            </f:facet>
            <a4j:commandLink action="#{MensagemBuilderControle.visualizarMensagemGrupo}" id="codigo"
                             value="#{malaDireta.codigo}"
                             reRender="mensagembuilder, itens, paginaAtual, painelPaginacao"/>
        </rich:column>

            <rich:column id="ml_titulo" sortBy="#{malaDireta.titulo}">
                <f:facet name="header">
                    <h:outputText value="T�tulo"/>
                </f:facet>
                <a4j:commandLink action="#{MensagemBuilderControle.visualizarMensagemGrupo}" id="titulo"
                                 value="#{malaDireta.titulo}"

                                 reRender="mensagembuilder, itens, paginaAtual, painelPaginacao"/>
            </rich:column>

            <rich:column id="u_nome" sortBy="#{malaDireta.remetenteApresentar}">
                <f:facet name="header">
                    <h:outputText value="Remetente"/>
                </f:facet>
                <a4j:commandLink action="#{MensagemBuilderControle.visualizarMensagemGrupo}" id="remetente"

                                 value="#{malaDireta.remetenteApresentar}"
                                 reRender="mensagembuilder, itens, paginaAtual, painelPaginacao"/>
            </rich:column>

            <rich:column id="ml_datacriacao" sortBy="#{malaDireta.dataCriacao}">
                <f:facet name="header">
                    <h:outputText value="Data de Cria��o"/>
                </f:facet>
                <a4j:commandLink action="#{MensagemBuilderControle.visualizarMensagemGrupo}" id="dataCriacao"

                                 reRender="mensagembuilder, itens, paginaAtual, painelPaginacao">
                    <h:outputText value="#{malaDireta.dataCriacao}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </a4j:commandLink>
            </rich:column>

            <rich:column id="ml_dataenvio" sortBy="#{malaDireta.dataEnvio}">
                <f:facet name="header">
                    <h:outputText value="#{MalaDiretaControle.labelData}"/>

                </f:facet>
                <a4j:commandLink action="#{MensagemBuilderControle.visualizarMensagemGrupo}" id="envioData"

                                 reRender="mensagembuilder, itens, paginaAtual, painelPaginacao">
                    <h:outputText id="dataEnvio" value="#{malaDireta.dataApresentar}"/>
                </a4j:commandLink>
            </rich:column>

            <rich:column id="ml_empresa" sortBy="#{malaDireta.nomeEmpresa}"
                         rendered="#{MalaDiretaControle.permissaoConsultaTodasEmpresas}">
                <f:facet name="header">
                    <h:outputText value="Empresa"/>

                </f:facet>
                <a4j:commandLink action="#{MensagemBuilderControle.visualizarMensagemGrupo}" id="malaEmpresa"

                                 reRender="mensagembuilder, itens, paginaAtual, painelPaginacao">
                    <h:outputText id="nomeEmpresa" value="#{malaDireta.nomeEmpresa}"/>
                </a4j:commandLink>
            </rich:column>

            <rich:column id="ml_entregabilidade" sortBy="#{malaDireta.statusEntregabilidade}">
                <f:facet name="header">
                    <h:outputText id="titulo_entregabilidade" value="Entregabilidade"/>
                </f:facet>

                <a4j:commandLink id="status_entregabilidade_link"
                                 action="#{MensagemBuilderControle.msgEntregabilidade}"
                                 oncomplete="#{MensagemBuilderControle.modalMensagemEntregabilidade}"
                                 reRender="mdlMensagemEntregabilidade"
                                 rendered="#{malaDireta.statusEntregabilidade == false}"
                >
                    <h:outputText id="status_entregabilidade_interrompido" style="font-size: 16px;font-weight: bold;  color: #ec2626"
                                  value="Falha">
                    </h:outputText>
                </a4j:commandLink>

                <h:outputText id="status_entregabilidade_funcionando"
                              rendered="#{malaDireta.statusEntregabilidade == true}" style="font-size: 16px; font-weight: bold; color: #8ad20e"
                              value="Funcionando">
                </h:outputText>
            </rich:column>

        </rich:dataTable>
    </a4j:region>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="painelPaginacaoOUT">
        <h:panelGroup id="painelPaginacao" rendered="#{MalaDiretaControle.confPaginacao.existePaginacao}">
            <a4j:commandLink id="pagiInicial" styleClass="tituloCampos"
                             value="  <<  " reRender="itens, paginaAtual, painelPaginacao"
                             rendered="#{MalaDiretaControle.confPaginacao.apresentarPrimeiro}"
                             actionListener="#{MalaDiretaControle.consultarPaginadoListener}">
                <f:attribute name="pagNavegacao" value="pagInicial" />
            </a4j:commandLink>
            <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacao.apresentarAnterior}" actionListener="#{MalaDiretaControle.consultarPaginadoListener}">
                <f:attribute name="pagNavegacao" value="pagAnterior" />
            </a4j:commandLink>
            <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{MalaDiretaControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
            <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacao.apresentarPosterior}" actionListener="#{MalaDiretaControle.consultarPaginadoListener}">
                <f:attribute name="pagNavegacao" value="pagPosterior" />
            </a4j:commandLink>
            <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  "  reRender="itens, paginaAtual, painelPaginacao"  rendered="#{MalaDiretaControle.confPaginacao.apresentarUltimo}" actionListener="#{MalaDiretaControle.consultarPaginadoListener}">
                <f:attribute name="pagNavegacao" value="pagFinal" />
            </a4j:commandLink>
            <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{MalaDiretaControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
        </h:panelGroup>
    </h:panelGrid>

</h:panelGroup>


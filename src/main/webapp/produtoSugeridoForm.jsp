<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ProdutoSugerido_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ProdutoSugerido_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ProdutoSugerido_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{ProdutoSugeridoControle.produtoSugeridoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ProdutoSugerido_modalidade}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="modalidade" styleClass="camposObrigatorios" value="#{ProdutoSugeridoControle.produtoSugeridoVO.modalidade.codigo}" >
                            <f:selectItems  value="#{ProdutoSugeridoControle.listaSelectItemModalidade}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_modalidade" action="#{ProdutoSugeridoControle.montarListaSelectItemModalidade}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:modalidade"/>
                        <h:message for="modalidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ProdutoSugerido_produto}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="produto" styleClass="camposObrigatorios" value="#{ProdutoSugeridoControle.produtoSugeridoVO.produto.codigo}" >
                            <f:selectItems  value="#{ProdutoSugeridoControle.listaSelectItemProduto}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_produto" action="#{ProdutoSugeridoControle.montarListaSelectItemProduto}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:produto"/>
                        <h:message for="produto" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ProdutoSugerido_Obrigatorio}" />
                    <h:selectBooleanCheckbox id="Obrigatorio"styleClass="campos"value="#{ProdutoSugeridoControle.produtoSugeridoVO.obrigatorio}"/>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ProdutoSugeridoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ProdutoSugeridoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{ProdutoSugeridoControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{ProdutoSugeridoControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{ProdutoSugeridoControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{ProdutoSugeridoControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:modalidade").focus();
</script>
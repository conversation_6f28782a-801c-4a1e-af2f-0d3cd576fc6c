<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/ce.css" rel="stylesheet" type="text/css">
<link href="./css_pacto.css" rel="stylesheet" type="text/css">
<link href="./css/agenda_estilos.css" rel="stylesheet" type="text/css">

<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>

<%
    pageContext.setAttribute("modulo", "centralEventos");

%>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Caixa em Aberto"/>
    </title>
    <h:form id="form">

        <input type="hidden" value="${modulo}" name="modulo"/>

        <html>
        <body>
        <%@include file="pages/ce/includes/include_head.jsp" %>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_ce_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <%@include file="include_tela_caixaAberto.jsp" %>
                        </h:panelGroup>
                        <%@include file="pages/ce/includes/include_box_menulateral.jsp" %>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')"/>
        </h:panelGroup>
        </body>
        </html>
    </h:form>
    <%@include file="include_modal_renegociacao.jsp" %>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>
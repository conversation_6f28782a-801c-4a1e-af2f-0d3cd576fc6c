<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" src="script/script.js"></script>
    <jsp:include page="include_head.jsp" flush="true" />
    <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <%-- <link href="${root}/css/pacto_flat.css" rel="stylesheet" type="text/css"/>--%>
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">


</head>


<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
        min-width: 0 !important;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:panelGrid columns="1" styleClass="tabForm" width="100%">
        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                     style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;">
            <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_historicoContato}" />
        </h:panelGrid>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkAgendamento" />
                <rich:componentControl for="panelAgendamento" attachTo="hiperlinkAgendamento" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <h:form id="form">
            <h:panelGrid id="historicoContatoCliente" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" width="100%" style="border:1px solid black">
                    <h:panelGrid columns="2" width="100%" style="border:1px solid black" columnClasses="colunaEsquerda">
                        <h:panelGrid columns="1" width="50px" cellpadding="0" cellspacing="0" columnClasses="colunaEsquerda">
                            <a4j:outputPanel id="panelFoto">
                                <a4j:mediaOutput element="img" id="imagem1" style="width:60px;height:80px " cacheable="false" session="true"
                                                 rendered="#{!SuperControle.fotosNaNuvem}"
                                                 createContent="#{HistoricoContatoControle.paintFoto}" value="#{ImagemData}" mimeType="image/jpeg">
                                    <f:param name="largura" value="60"/>
                                    <f:param name="altura" value="80"/>
                                </a4j:mediaOutput>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                width="60" height="80"
                                                style="width:60px;height:80px"
                                                url="#{HistoricoContatoControle.paintFotoDaNuvem}"/>
                            </a4j:outputPanel>
                        </h:panelGrid>

                        <h:panelGrid columns="1" columnClasses="colunaEsquerda" cellpadding="0" cellspacing="0" style="text-align: top;" width="100%">
                            <h:panelGrid columns="4" columnClasses="colunaEsquerda" width="100%">
                                <h:panelGrid columns="1" columnClasses="colunaEsquerda" width="100%">
                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_aluno}" />
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.nome}" />
                                    </h:panelGroup>
                                </h:panelGrid>

                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_idade}" />
                                    <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.idade}" />
                                </h:panelGrid>

                                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_estadoCivil}" />
                                    <h:outputText styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}"
                                                  value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.estadoCivil_Apresentar}" />
                                    <rich:spacer height="22px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}" />
                                </h:panelGrid>

                                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                                    <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_dataCadastro}" />
                                    <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.dataCadastro_Apresentar}" />
                                </h:panelGrid>
                            </h:panelGrid>

                            <rich:spacer width="30px;" />

                            <h:panelGroup>
                                <h:panelGrid columns="7" width="100%">
                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_diasUltAcesso}" />
                                        <h:outputText styleClass="camposAgenda" rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}"
                                                      value="#{HistoricoContatoControle.historicoContatoVO.nrDiasUltimoAcesso}" />
                                        <rich:spacer height="15px" rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" />
                                    </h:panelGrid>

                                    <h:panelGrid rendered="#{HistoricoContatoControle.historicoContatoVO.vencimentoContrato != null}"
                                                 columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_vencimentoContrato}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.vencimentoContrato_Apresentar}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_ligacoes}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.totalLigacao}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdEmail}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdEmail}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdPessoal}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdPessoal}" />
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                                        <h:outputText styleClass="tituloCamposAgenda" value="#{msg_aplic.prt_Agenda_qtdSMS}" />
                                        <h:outputText styleClass="camposAgenda" value="#{HistoricoContatoControle.historicoContatoVO.qtdSMS}" />
                                    </h:panelGrid>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                                 style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;">
                        <h:outputText styleClass="tituloFormulario" value="Telefones" />
                    </h:panelGrid>

                    <h:panelGrid id="listaTelefonesCliente" columns="1" columnClasses="colunaCentralizada" width="100%">
                        <rich:dataTable id="tabelaTelefones" width="100%" headerClass="subordinado"
                                        rowClasses="linhaImpar, linhaPar" columnClasses="colunaCentralizada"
                                        value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.telefoneVOs}"
                                        var="telefone">

                            <%--<h:dataTable id="telefoneVO" width="100%" headerClass="subordinado"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ClienteControle.pessoaVO.telefoneVOs}" var="telefone">
                                   <h:column>
                                       <f:facet name="header">
                                           <h:outputText  value="#{msg_aplic.prt_Telefone_numero}" />
                                       </f:facet>
                                       <h:outputText  value="#{telefone.numero}" />
                                   </h:column>--%>

                            <rich:column width="33%">
                                <f:facet name="header">
                                    <h:outputText value="Tipo" />
                                </f:facet>
                                <h:outputText value="#{telefone.tipoTelefone_Apresentar}" />
                            </rich:column>

                            <rich:column width="33%">
                                <f:facet name="header">
                                    <h:outputText value="Número" />
                                </f:facet>
                                <h:outputText value="#{telefone.numero}" />
                            </rich:column>

                            <rich:column width="33%">
                                <f:facet name="header">
                                    <h:outputText value="Descrição" />
                                </f:facet>
                                <h:outputText value="#{telefone.descricao}" />
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>

                    <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                                 style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;">
                        <h:outputText styleClass="tituloFormulario" value="Histórico" />
                    </h:panelGrid>


                    <h:panelGroup id="listaContatos" >
                        <%-- <h:outputLabel value="#{HistoricoContatoControle.calcTempo()}" />--%>
                        <rich:dataTable id="resTelefone" width="100%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                        value="#{HistoricoContatoControle.listaHistoricoContatoCliente}"
                                        columnClasses="colunaCentralizada" var="contatos">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Agenda_dataHora}" />
                                </f:facet>
                                <h:outputText value="#{contatos.dia_Apresentar}" />
                            </rich:column>

                            <  <rich:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Agenda_fase}" />
                            </f:facet>
                            <h:outputText value="#{contatos.fase_Apresentar}" />
                            <h:outputText rendered="#{contatos.contatoAvulso}" value=" - avulso" />
                        </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Agenda_resultado}" />
                                </f:facet>
                                <h:outputText value="#{contatos.resultado}" />
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_Agenda_usuario}" />
                                </f:facet>
                                <h:outputText value="#{contatos.responsavelCadastro.colaboradorVO.pessoa.primeiroNomeConcatenado}" />
                            </rich:column>

                            <rich:column rendered="#{LoginControle.permissaoAcessoMenuVO.crmEditarSimplesRegistro}">
                                <f:facet name="header">
                                    <h:outputText value="Opções"/>
                                </f:facet>
                                <a4j:commandLink rendered="#{contatos.resultado == 'Simples Registro'}"
                                                 value="Editar"
                                                 oncomplete="#{HistoricoContatoControle.onCompleteEdicaoSimplesRegistro}"
                                                 reRender="mdlEditarSimplesRegistro"
                                                 action="#{HistoricoContatoControle.prepararEdicaoSimplesRegistro}"/>
                            </rich:column>

                            <rich:column colspan="5" width="100%" breakBefore="true" style="vertical-align:top; text-align:left;">
                                <h:outputText value="#{contatos.observacao}" escape="false" />
                                <h:outputText rendered="#{contatos.app}" value="<br/>Resposta: #{contatos.resposta}" escape="false" />

                            </rich:column>


                        </rich:dataTable>


                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                                     rendered="#{HistoricoContatoControle.listaClienteHistoricoContato.count > 0}">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td align="center" valign="middle">
                                        <h:panelGroup layout="block"
                                                      styleClass="paginador-container">
                                            <h:panelGroup styleClass="pull-left"
                                                          layout="block">
                                                <h:outputText
                                                        styleClass="texto-size-14 texto-cor-cinza"
                                                        value=" Total de #{HistoricoContatoControle.listaClienteHistoricoContato.count} itens"></h:outputText>
                                            </h:panelGroup>


                                            <h:panelGroup layout="block"
                                                          style="align-items: center">
                                                <a4j:commandLink
                                                        styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                        reRender="listaContatos"
                                                        actionListener="#{HistoricoContatoControle.primeiraPagina}">
                                                    <i class="fa-icon-double-angle-left" id="primPaginaHistorico"></i>
                                                    <f:attribute name="tipo"
                                                                 value="LISTA_CONTATOS"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink
                                                        styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                        reRender="listaContatos"
                                                        actionListener="#{HistoricoContatoControle.paginaAnterior}">
                                                    <i class="fa-icon-angle-left" id="pagAntHistorico"></i>
                                                    <f:attribute name="tipo"
                                                                 value="LISTA_CONTATOS"/>
                                                </a4j:commandLink>
                                                <h:outputText
                                                        styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                        value="#{msg_aplic.prt_msg_pagina} #{HistoricoContatoControle.listaClienteHistoricoContato.paginaAtualApresentar}"
                                                        rendered="true"/>
                                                <a4j:commandLink
                                                        styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                                        reRender="listaContatos"
                                                        actionListener="#{HistoricoContatoControle.proximaPagina}">
                                                    <i class="fa-icon-angle-right" id="proxPagHistorico"></i>
                                                    <f:attribute name="tipo"
                                                                 value="LISTA_CONTATOS"/>
                                                </a4j:commandLink>

                                                <a4j:commandLink
                                                        styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                        reRender="listaContatos"
                                                        actionListener="#{HistoricoContatoControle.ultimaPagina}">
                                                    <i class="fa-icon-double-angle-right" id="ultimaPaginaHistorico"></i>
                                                    <f:attribute name="tipo"
                                                                 value="LISTA_CONTATOS"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>


                                            <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                                <h:panelGroup styleClass="pull-right" layout="block">
                                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza" value="Itens por página " />
                                                </h:panelGroup>

                                                <h:panelGroup styleClass="cb-container pl20" layout="block">
                                                    <h:selectOneMenu
                                                            value="#{HistoricoContatoControle.listaClienteHistoricoContato.limit}"
                                                            id="qtdeItensPaginaHistorico">
                                                        <f:selectItem itemValue="#{10}"></f:selectItem>
                                                        <f:selectItem itemValue="#{30}"></f:selectItem>
                                                        <f:selectItem itemValue="#{50}"></f:selectItem>
                                                        <f:selectItem itemValue="#{100}"></f:selectItem>
                                                        <a4j:support event="onchange"
                                                                     actionListener="#{HistoricoContatoControle.atualizarNumeroItensPagina}"
                                                                     reRender="listaContatos">
                                                            <f:attribute name="tipo" value="LISTA_CONTATOS"/>
                                                        </a4j:support>
                                                    </h:selectOneMenu>

                                                </h:panelGroup>
                                            </h:panelGroup>

                                        </h:panelGroup>

                                    </td>
                                </tr>
                            </table>
                        </h:panelGrid>


                        <a4j:commandLink id="log"
                                         rendered="#{LoginControle.permissaoAcessoMenuVO.crmEditarSimplesRegistro}"
                                         action="#{HistoricoContatoControle.realizarConsultaLogObjetoSelecionado}"
                                         reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                         title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                         style="display: inline-block; padding: 8px 15px;">
                            <i class="fa-icon-list"></i>
                        </a4j:commandLink>

                        <h:panelGrid id="panelGridMensagens" columns="1" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagemDetalhada" value="#{HistoricoContatoControle.mensagemDetalhada}" />
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="mdlEditarSimplesRegistro" width="300" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                Edição de Registro
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkmdlEditarSimplesRegistro"/>
                <rich:componentControl for="mdlEditarSimplesRegistro" attachTo="hidelinkmdlEditarSimplesRegistro" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formEditarSimplesRegistro">
            <h:panelGroup>
                <h:inputTextarea styleClass="inputTextClean" value="#{HistoricoContatoControle.historicoContatoVOEdicao.observacao}"
                                 style="width: calc(100% - 1vw); margin-top:0.5vw; margin-left:0.5vw; height: 8vw;"/>
                <a4j:commandLink styleClass="botoes nvoBt" style="float: right; margin: 5px 0.5vw;"
                                 id="confirmarEdicaosimplesRegistro"
                                 action="#{HistoricoContatoControle.salvarSimplesRegistro}"
                                 oncomplete="#{HistoricoContatoControle.mensagemNotificar};Richfaces.hideModalPanel('mdlEditarSimplesRegistro');"
                                 reRender="form:listaContatos">
                    Salvar
                </a4j:commandLink>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>
</f:view>



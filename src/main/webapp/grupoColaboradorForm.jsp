<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_GrupoColaborador_tituloForm}"/>
    </title>


    <rich:modalPanel id="panelColaborador" autosized="true" shadowOpacity="true" width="750" height="500" onshow="document.getElementById('formColaborador:consultarColaborador').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_GrupoColaboradorParticipante_consultarColaborador}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkColaborador"/>
                <rich:componentControl for="panelColaborador" attachTo="hiperlinkColaborador" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formColaborador" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarColaborador" value="#{GrupoColaboradorControle.campoConsultarColaborador}">
                        <f:selectItems value="#{GrupoColaboradorControle.tipoConsultarComboColaborador}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarColaborador" styleClass="campos" value="#{GrupoColaboradorControle.valorConsultarColaborador}"/>
                    <a4j:commandButton id="btnConsultarColaborador" reRender="formColaborador:mensagemConsultarColaborador, formColaborador:resultadoConsultaColaborador , formColaboradorscResultadoColaborador , formColaborador" action="#{GrupoColaboradorControle.consultarColaborador}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaColaborador" width="100%" headerClass="consulta"  rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{GrupoColaboradorControle.listaConsultarColaborador}" rows="10" var="grupoColaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_nome}"/>
                        </f:facet>
                        <h:outputText value="#{grupoColaborador.colaboradorParticipante.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_tipoVisao}"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:selectOneMenu  id="tipoVisao" disabled="#{grupoColaborador.naoPermitirAlterarTipoVisao}" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{grupoColaborador.tipoVisao}" >
                                <f:selectItems  value="#{GrupoColaboradorControle.listaSelectItemTipoVisao}" />
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_situacao}"/>
                        </f:facet>
                        <h:outputText value="#{grupoColaborador.colaboradorParticipante.situacao_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:selectBooleanCheckbox id="selecionarColaborador" styleClass="campos" value="#{grupoColaborador.colaboradorParticipante.colaboradorEscolhidoIndiceConversao}" >
                            <a4j:support event="onclick" ajaxSingle="true"/>
                        </h:selectBooleanCheckbox>
                        
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" ajaxSingle="false" for="formColaborador:resultadoConsultaColaborador" maxPages="10" id="scResultadoColaborador"/>
                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                    <a4j:commandButton id="addParticipanteGru" 
                    				   action="#{GrupoColaboradorControle.adicionarGrupoColaboradorParticipanteLista}" 
                    				   oncomplete="#{GrupoColaboradorControle.msgAlert}"  
                    				   reRender="mensagemConsultaColaborador, form:panelGrupoColaboradorParticipanteVO" value="#{msg_bt.btn_adicionar}" 
                    				   image= "./imagensCRM/botaoAdicionar.png" accesskey="5" styleClass="botoes"
                    				   />
                </h:panelGrid>
                <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgAddPar" styleClass="mensagem" value="#{GrupoColaboradorControle.mensagem}"/>
                        <h:outputText id="msgAddParDet" styleClass="mensagemDetalhada" value="#{GrupoColaboradorControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelGerente" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formGerente:consultarGerente').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_GrupoColaborador_consultarGerente}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkColaboradorGerente"/>
                <rich:componentControl for="panelGerente" attachTo="hiperlinkColaboradorGerente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formGerente" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarColaboradorGerente" value="#{GrupoColaboradorControle.campoConsultarGerente}">
                        <f:selectItems value="#{GrupoColaboradorControle.tipoConsultarComboGerente}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarColaboradorGerente" styleClass="campos" value="#{GrupoColaboradorControle.valorConsultarGerente}" />
                    <a4j:commandButton id="btnConsultarGerente" reRender="formGerente:mensagemConsultaColaboradorGerente, formGerente:resultadoConsultaGerente , formGerentescResultadoGerente , formGerente" action="#{GrupoColaboradorControle.consultarColaboradorGerente}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaColaboradorGerente" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{GrupoColaboradorControle.listaConsultarGerente}" rows="10" var="usuario">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_GrupoColaborador_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{usuario.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_GrupoColaborador_nome}"/>
                        </f:facet>
                        <h:outputText value="#{usuario.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{GrupoColaboradorControle.selecionarColaboradorGerente}" focus="colaborador" reRender="form, formGerente" oncomplete="Richfaces.hideModalPanel('panelGerente')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formGerente:resultadoConsultaColaboradorGerente" maxPages="10" id="scResultadoColaboradorGerente"/>
                <h:panelGrid id="mensagemConsultaColaboradorGerente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{GrupoColaboradorControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{GrupoColaboradorControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>



    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <c:set var="titulo" scope="session" value="${msg_aplic.prt_GrupoColaborador_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-grupo-colaborador-no-crm/"/>

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topoReduzido_material_crm.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="form">
            <h:commandLink action="#{GrupoColaboradorControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                     <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_GrupoColaborador_tituloForm}">
                    <h:outputLink rendered="#{LoginControle.permissaoAcessoMenuVO.grupoColaborador}" value="#{SuperControle.urlBaseConhecimento}como-cadastrar-grupo-colaborador-no-crm/"
                                      title="Clique e saiba mais: Grupo Colaborador" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                	
                	<h:outputText  rendered="#{GrupoColaboradorControle.usuarioLogado.administrador}"  styleClass="tituloCampos" value="#{msg_aplic.prt_Colaborador_empresa}" />
                    <h:panelGroup rendered="#{GrupoColaboradorControle.usuarioLogado.administrador}">
                        <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{GrupoColaboradorControle.grupoColaboradorVO.empresa.codigo}" >
                        	<f:selectItem itemValue="" itemLabel="-- Selecione --"/>
                            <f:selectItems  value="#{GrupoColaboradorControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_GrupoColaborador_descricao}" />
                    <h:inputText  id="descricao" size="50"  maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{GrupoColaboradorControle.grupoColaboradorVO.descricao}"/>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_GrupoColaborador_gerente}" />
                    <h:panelGroup id="panelResponsavelGrupo">
                        <h:inputText id="textResponsavelGrupo" size="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{GrupoColaboradorControle.grupoColaboradorVO.gerente.nome}"/>
                        <rich:suggestionbox   height="200" width="200"
                                              for="textResponsavelGrupo"
                                              status="statusInComponent"
                                              fetchValue="#{result.nome}"
                                              immediate="true"
                                              suggestionAction="#{GrupoColaboradorControle.executarAutocompleteResponsavel}"
                                              minChars="1" rowClasses="20"
                                              nothingLabel="Nenhum Responsável encontrado !"
                                              var="result"  id="suggestionResponsavel">

                            <h:column>
                                <h:outputText value="#{result.nome}" />
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"  value="#{msg_aplic.prt_GrupoColaborador_tipoGrupo}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="tipoColaborador" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{GrupoColaboradorControle.grupoColaboradorVO.tipoGrupo}" >
                            <f:selectItems  value="#{GrupoColaboradorControle.listaSelectItemTipoColaborador}" />
                            <a4j:support action="#{GrupoColaboradorControle.apresentarBotaoColaboradorParticipante}" reRender="form:botaoParticipante, form:panelGrupoColaboradorParticipanteVO, form:panelMensagem" event="onchange" ajaxSingle="true" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    
                </h:panelGrid>
                <h:panelGrid id="botaoParticipante" columns="1" width="100%" headerClass="subordinado">
                    <h:panelGrid columns="1" width="100%" headerClass="subordinado" rendered="#{GrupoColaboradorControle.apresentarBotaoParticipante}" columnClasses="colunaCentralizada">
                        <f:facet name="header">
                            <h:outputText id="ttParticipante" value="#{msg_aplic.prt_GrupoColaboradorParticipante_tituloForm}"/>
                        </f:facet>
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_GrupoColaboradorParticipante_mensagemConsultarParticipante}" />
                            <a4j:commandButton id="consultarParticipante" reRender="panelColaborador" action="#{GrupoColaboradorControle.zerarListaUsuarioColaborador}" oncomplete="Richfaces.showModalPanel('panelColaborador')" image="imagens/informacao.gif" alt="#{msg_aplic.prt_GrupoColaboradorParticipante_consultarColaborador}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="panelGrupoColaboradorParticipanteVO" columns="1" width="100%" styleClass="tabFormSubordinada">
                    <h:dataTable id="grupoColaboradorParticipanteVO" rows="10" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                 rowClasses="linhaImpar, linhaPar"  rendered="#{!empty GrupoColaboradorControle.grupoColaboradorVO.grupoColaboradorParticipanteVOs}" columnClasses="colunaCentralizada"
                                 value="#{GrupoColaboradorControle.grupoColaboradorVO.grupoColaboradorParticipanteVOs}" var="grupoColaboradorParticipante">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_GrupoColaboradorParticipante_colaborador}" />
                            </f:facet>
                            <h:outputText  value="#{grupoColaboradorParticipante.colaboradorParticipante.pessoa.nome}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_GrupoColaboradorParticipante_tipoVisao}" />
                            </f:facet>
                            <h:outputText  value="#{grupoColaboradorParticipante.tipoVisao_Apresentar}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Colaborador_situacao}"/>
                            </f:facet>
                            <h:outputText value="#{grupoColaboradorParticipante.colaboradorParticipante.situacao_Apresentar}"/>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                            </f:facet>
                            <h:panelGroup>
                                <h:commandButton id="editarItemVenda" immediate="true" action="#{GrupoColaboradorControle.editarGrupoColaboradorParticipante}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <h:commandButton id="removerItemVenda" immediate="true" action="#{GrupoColaboradorControle.removerGrupoColaboradorParticipante}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                            </h:panelGroup>
                        </h:column>
                    </h:dataTable>

                    <h:panelGrid rendered="#{!empty GrupoColaboradorControle.grupoColaboradorVO.grupoColaboradorParticipanteVOs}" columns="1" width="100%">
                        <rich:datascroller align="center" for="form:grupoColaboradorParticipanteVO" maxPages="10" id="scgrupoColaboradorParticipanteVO"/>
                    </h:panelGrid>


                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" id="panelEdicaoColaborador">
                        <h:panelGrid columns="1" rendered="#{GrupoColaboradorControle.apresentarPanelEdicaoColaborador}" width="100%" columnClasses="colunaEsquerda" headerClass="subordinado">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_GrupoColaboradorParticipante_editarParticipante}"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_GrupoColaboradorParticipante_colaborador}" />
                                <rich:spacer width="20px" />
                                <h:panelGroup>
                                    <h:inputText  id="grupoColaboradorParticipante_colaborador" size="70" maxlength="70" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{GrupoColaboradorControle.grupoColaboradorParticipanteVO.colaboradorParticipante.pessoa.nome}" />
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_GrupoColaboradorParticipante_tipoVisao}" />
                                <rich:spacer width="32px" />
                                <h:panelGroup>
                                    <h:selectOneMenu  id="tipoVisao" disabled="#{GrupoColaboradorControle.grupoColaboradorParticipanteVO.naoPermitirAlterarTipoVisao}"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{GrupoColaboradorControle.grupoColaboradorParticipanteVO.tipoVisao}" >
                                        <f:selectItems  value="#{GrupoColaboradorControle.listaSelectItemTipoVisao}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGrid>
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" rendered="#{GrupoColaboradorControle.apresentarPanelEdicaoColaborador}">
                            <h:commandButton action="#{GrupoColaboradorControle.adicionarGrupoColaboradorParticipante}" value="#{msg_bt.btn_adicionar}" image= "./imagensCRM/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{GrupoColaboradorControle.sucesso}" image="./imagensCRM/sucesso.png"/>
                        <h:commandButton rendered="#{GrupoColaboradorControle.erro}" image="./imagensCRM/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgGrupo" styleClass="mensagem"  value="#{GrupoColaboradorControle.mensagem}"/>
                            <h:outputText  id="msgGrupodet" styleClass="mensagemDetalhada" value="#{GrupoColaboradorControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{GrupoColaboradorControle.novo}"   value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="5px"/>
                            <a4j:commandButton id="salvar" action="#{GrupoColaboradorControle.gravar}" reRender="form" oncomplete="#{GrupoColaboradorControle.msgAlert}" value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" styleClass="botoes nvoBt" accesskey="2"/>
                            <rich:spacer width="5px"/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{GrupoColaboradorControle.msgAlert}" action="#{GrupoColaboradorControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <rich:spacer width="5px"/>
                            <h:commandButton id="consultar" immediate="true" action="#{GrupoColaboradorControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" styleClass="botoes nvoBt btSec" accesskey="4" />
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>

</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
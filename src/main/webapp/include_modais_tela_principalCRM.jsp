<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">

<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<style type="text/css">
    td.gridMetas {
        text-align: center;
        width: 240px;
    }

    .rich-table-cell.historicoContato {
        padding: 5px 8px 5px 8px !important;
    }

    .rich-mpnl-content.historicoContato {
        border: none !important;
        border-color: white;
    }

    .rich-table-thead.historicoContato {
        background: none !important;
        border: none !important;
    }

    .mensagemTextoPadrao{
        display: block;
        height: 435px;
        overflow: auto;
    }
</style>

<%-- INICIO -- MODAL VER HISTORICO LIGA??ES --%>
<rich:modalPanel id="modalHistoricoContadorCliente" width="700" height="400" styleClass="novaModal"
                 domElementAttachment="parent" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{MetaCRMControle.tituloModalContador}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideModalHistoricoContadorCliente"/>
            <rich:componentControl for="modalHistoricoContadorCliente" attachTo="hideModalHistoricoContadorCliente"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form>
        <h:panelGroup layout="block" style="overflow-y: auto; max-height: 400px; ">
            <rich:dataTable width="100%" headerClass="subordinado"
                            rowClasses="linhaImpar, linhaPar"
                            value="#{MetaCRMControle.listaModalContadorCliente}"
                            var="contatos">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="#{msg_aplic.prt_Agenda_dataHora}"/>
                    </f:facet>
                    <h:outputText value="#{contatos.dia_Apresentar}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="#{msg_aplic.prt_Agenda_fase}"/>
                    </f:facet>
                    <h:outputText value="#{contatos.fase_Apresentar}"/>
                    <h:outputText rendered="#{contatos.contatoAvulso}" value=" - avulso"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="#{msg_aplic.prt_Agenda_resultado}"/>
                    </f:facet>
                    <h:outputText value="#{contatos.resultado}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="#{msg_aplic.prt_Agenda_usuario}"/>
                    </f:facet>
                    <h:outputText
                            value="#{contatos.responsavelCadastro.nome}"/>
                </rich:column>

                <rich:column style="padding-top: 8px" colspan="4" width="100%" breakBefore="true">
                    <h:outputText value="#{contatos.observacao}" escape="false"/>
                    <h:outputText rendered="#{contatos.app}" value="<br/>Resposta: #{contatos.resposta}"
                                  escape="false"/>
                    <a4j:commandLink rendered="#{contatos.malaDiretaVO.codigo != 0}"
                                     value="Abrir Mailing: #{contatos.malaDiretaVO.codigo}"
                                     action="#{MetaCRMControle.consultarMailing}"
                                     oncomplete="#{MetaCRMControle.onComplete}"/>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<%-- FIM -- MODAL VER HISTORICO LIGA??ES --%>


<%--MODAL DE TAG DA EMPRESA--%>
<rich:modalPanel id="panelEmail" autosized="true" shadowOpacity="true" width="300" height="150" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Marcadores de Email"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidePanelEmail"/>
            <rich:componentControl for="panelEmail" attachTo="hidePanelEmail"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formMarcadorEmail" ajaxSubmit="true">
        <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
            <rich:dataTable id="MarcadoEmail" width="440px" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                            columnClasses="colunaAlinhamento" var="marcadorEmail" rows="40"
                            value="#{MetaCRMControle.listaSelectItemMarcadoEmail}">
                <f:facet name="header">
                    <h:outputText value="Email"/>
                </f:facet>
                <rich:column width="170px">
                    <f:facet name="header">
                        <h:outputText value="Tags"/>
                    </f:facet>
                    <h:outputText styleClass="campos" value="#{marcadorEmail.nome}"/>
                </rich:column>
                <rich:column width="240px">
                    <f:facet name="header">
                        <h:outputText value="Op??es"/>
                    </f:facet>
                    <a4j:commandLink action="#{MetaCRMControle.executarInsercaoTag}" reRender="mensagemEmail"
                                     oncomplete="Richfaces.hideModalPanel('panelEmail');"
                                     title="Adicionar">
                        <i class="fa-icon-plus-sign" style="font-size: large;  color: darkblue"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>


<%--MODAL TEXTO PADR?O--%>
<rich:modalPanel id="panelTextoPadrao" domElementAttachment="parent" autosized="true" width="700" height="500" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Script"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidePanelTextoPadrao"/>
            <rich:componentControl for="panelTextoPadrao" attachTo="hidePanelTextoPadrao"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formTextoPadrao">
        <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaImpar" columnClasses="classEsquerda, classDireita" style="margin-bottom: 10px" id="testeLuiz">
            <h:outputText value="Script:" style="font-weight: bold; font-size: 12px"/>
            <h:selectOneMenu id="listaTextoPadrao"
                             style="background: #F6F6F6; color: #333333; font-size: 14px; float: left"
                             value="#{MetaCRMControle.textoPadraoVO.codigo}">
                <f:selectItems value="#{MetaCRMControle.listaTextoPadrao}"/>
                <a4j:support event="onchange" action="#{MetaCRMControle.setarMensagemTextoPadrao}"
                             reRender="formTextoPadrao"/>
            </h:selectOneMenu>

            <h:outputText value="Link(Google Docs):" style="font-weight: bold; font-size: 12px"/>
            <h:outputLink value="#{MetaCRMControle.textoPadraoVO.linkDocs}" target="_blank">
                <h:outputText value="#{MetaCRMControle.textoPadraoVO.linkDocs}"/>
            </h:outputLink>
        </h:panelGrid>

        <h:panelGrid id="mensagemTextoPadrao" columns="1" width="100%" style="border: 1px #000 solid; height: 100%">
            <h:outputText styleClass="mensagemTextoPadrao" value="#{MetaCRMControle.textoPadraoVO.mensagemPadrao}" escape="false"/>
        </h:panelGrid>

        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" style="margin-top: 10px;">
            <a4j:commandLink id="inserirTextoPadrao"
                             value="Usar Script"
                             styleClass="pure-button pure-button-small pure-button-primary"
                             action="#{MetaCRMControle.inserirTextoPadrao}"
                             oncomplete="#{MetaCRMControle.onComplete};adicionarPlaceHolderCRM()"
                             reRender="panelTextoPadrao, panelGridRight">
            </a4j:commandLink>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>


<%--LISTA DE CLIENTE BI-CRM--%>
<rich:modalPanel id="modalListaClientesBI" width="1000" height="400" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Lista da Meta - #{BusinessIntelligenceCRMControle.totalListaClientesMeta}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidemodalListaClientesBI"/>
            <rich:componentControl for="modalListaClientesBI" attachTo="hidemodalListaClientesBI"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form>
        <h:panelGrid columns="2" width="100%">

            <h:outputText style="float: left" value="#{BusinessIntelligenceCRMControle.totalListaClientesMeta}"/>

            <h:panelGroup layout="block" style="float: right; padding-bottom: 5px">
                <%--BOT?O PDF--%>
                <a4j:commandLink
                        id="exportarPDF"
                        styleClass="exportadores linkPadrao"
                        style="float: right"
                        actionListener="#{ExportadorListaControle.exportar}"
                        rendered="#{not empty BusinessIntelligenceCRMControle.listaClientesMeta}"
                        oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaClientesMeta}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos" value="#{BusinessIntelligenceCRMControle.atributosExportacao}"/>
                    <f:attribute name="prefixo" value="ListaBICRM"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>

                <rich:spacer width="7" style="float: right"/>

                <%--BOT?O EXCEL--%>
                <a4j:commandLink
                        id="exportarExcel"
                        styleClass="exportadores linkPadrao"
                        style="float: right"
                        actionListener="#{ExportadorListaControle.exportar}"
                        rendered="#{not empty BusinessIntelligenceCRMControle.listaClientesMeta}"
                        oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaClientesMeta}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos" value="#{BusinessIntelligenceCRMControle.atributosExportacao}"/>
                    <f:attribute name="prefixo" value="ListaBICRM"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGroup layout="block" style="overflow-y: auto; max-height: 400px; " id="modalHistoricoContadorClienteGroup">
            <rich:dataTable  width="100%" headerClass="subordinado" id="modalHistoricoContadorClienteTable"
                            rowClasses="linhaImpar, linhaPar"
                            value="#{BusinessIntelligenceCRMControle.listaClientesMeta}"
                            var="cliente">
                <rich:column sortBy="#{cliente.matricula}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Matr�cula"/>
                    </f:facet>
                    <h:outputText value="#{cliente.matricula}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.nomePessoaRel}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Nome"/>
                    </f:facet>
                    <h:outputText value="#{cliente.nomePessoaRel}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.fecharMeta.dataRegistro}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Data Cadastro"/>
                    </f:facet>
                    <h:outputText value="#{cliente.fecharMeta.dataRegistro_ApresentarBI}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.estadoCivilPessoa}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Estado Civil"/>
                    </f:facet>
                    <h:outputText value="#{cliente.estadoCivilPessoa}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.idadePessoa}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Idade"/>
                    </f:facet>
                    <h:outputText value="#{cliente.idadePessoa}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.fecharMeta.aberturaMetaVO.colaboradorResponsavel.nome}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Colaborador"/>
                    </f:facet>
                    <h:outputText value="#{cliente.fecharMeta.aberturaMetaVO.colaboradorResponsavel.nome}"/>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>


<rich:modalPanel id="modalListaObjecoes" width="700" height="410" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Lista de Obje��es"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidemodalListaObjecoes"/>
            <rich:componentControl for="modalListaObjecoes" attachTo="hidemodalListaObjecoes"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form>
        <h:panelGroup layout="block" style="overflow-y: auto; max-height: 400px; overflow-x: hidden">
            <h:panelGroup layout="block">
                <h:outputText style="float: left"
                              value="Total de Obje��es: #{BusinessIntelligenceCRMControle.indObjecoes}"/>
            </h:panelGroup>
            <h:panelGroup layout="block">
                <h:panelGrid columns="2" width="100%">

                    <h:outputText style="float: left"
                                  value="Obje��es por Fase"/>

                    <h:panelGroup layout="block" style="float: right; padding-bottom: 5px">
                        <%--BOT?O PDF--%>
                        <a4j:commandLink
                                id="exportarObPDF"
                                value="PDF"
                                styleClass="pure-button pure-button-small"
                                style="float: right"
                                actionListener="#{ExportadorListaControle.exportar}"
                                rendered="#{not empty BusinessIntelligenceCRMControle.listaIndObjecoesTotais}"
                                oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                            <f:attribute name="lista"
                                         value="#{BusinessIntelligenceCRMControle.listaIndObjecoesTotais}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="objecao=Obje��o,faseApresentar=Fase,quantidade=Quantidade,porcentagem=Porcentagem"/>
                            <f:attribute name="prefixo" value="ObjecoesPorFase"/>
                            <f:attribute name="titulo" value="Obje��es por Fase"/>
                            <i class="fa-icon-pdf"></i>
                        </a4j:commandLink>

                        <rich:spacer width="7" style="float: right"/>

                        <%--BOT?O EXCEL--%>
                        <a4j:commandLink
                                id="exportarObExcel"
                                value="Excel"
                                styleClass="pure-button pure-button-small"
                                style="float: right"
                                actionListener="#{ExportadorListaControle.exportar}"
                                rendered="#{not empty BusinessIntelligenceCRMControle.listaIndObjecoesTotais}"
                                oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                            <f:attribute name="lista"
                                         value="#{BusinessIntelligenceCRMControle.listaIndObjecoesTotais}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="objecao=Obje��o,faseApresentar=Fase,quantidade=Quantidade,porcentagem=Porcentagem"/>
                            <f:attribute name="prefixo" value="ObjecoesPorFase"/>
                            <f:attribute name="titulo" value="Obje��es por Fase"/>
                            <i class="fa-icon-excel"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>

                <rich:dataTable width="100%" headerClass="subordinado"
                                rowClasses="linhaImpar, linhaPar"
                                value="#{BusinessIntelligenceCRMControle.listaIndObjecoesTotais}"
                                var="totais">

                    <rich:column sortBy="#{totais.faseApresentar}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Fase"/>
                        </f:facet>
                        <h:outputText value="#{totais.faseApresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{totais.quantidade}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Quantidade"/>
                        </f:facet>
                        <h:outputText value="#{totais.quantidade}"/>
                    </rich:column>

                    <rich:column sortBy="#{totais.quantidade}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Porcentagem(%)"/>
                        </f:facet>
                        <h:outputText value="#{totais.porcentagem}"/>
                    </rich:column>

                </rich:dataTable>

            </h:panelGroup>


            <h:panelGroup layout="block" style="padding-top: 10px">
                <h:panelGrid columns="2" width="100%">

                    <h:outputText style="float: left"
                                  value="Obje��es por Fase Detalhada"/>

                    <h:panelGroup layout="block" style="float: right; padding-bottom: 5px">
                        <%--BOT?O PDF--%>
                        <a4j:commandLink
                                id="exportarObPDFDet"
                                value="PDF"
                                styleClass="pure-button pure-button-small"
                                style="float: right"
                                actionListener="#{ExportadorListaControle.exportar}"
                                rendered="#{not empty BusinessIntelligenceCRMControle.listaIndObjecoes}"
                                oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                            <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaIndObjecoes}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="objecao=Obje��o,faseApresentar=Fase,quantidade=Quantidade,porcentagem=Porcentagem"/>
                            <f:attribute name="prefixo" value="ObjecoesPorFase-Detalhada"/>
                            <f:attribute name="titulo" value="Obje��es por Fase - Detalhada"/>
                            <i class="fa-icon-pdf"></i>
                        </a4j:commandLink>

                        <rich:spacer width="7" style="float: right"/>

                        <%--BOT?O EXCEL--%>
                        <a4j:commandLink
                                id="exportarObExcelDet"
                                value="Excel"
                                styleClass="pure-button pure-button-small"
                                style="float: right"
                                actionListener="#{ExportadorListaControle.exportar}"
                                rendered="#{not empty BusinessIntelligenceCRMControle.listaIndObjecoes}"
                                oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}">
                            <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaIndObjecoes}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="objecao=Obje��o,faseApresentar=Fase,quantidade=Quantidade,porcentagem=Porcentagem"/>
                            <f:attribute name="prefixo" value="ObjecoesPorFase-Detalhada"/>
                            <f:attribute name="titulo" value="Obje��es por Fase - Detalhada"/>
                            <i class="fa-icon-excel"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGrid>

                <rich:dataTable width="100%" headerClass="subordinado"
                                rowClasses="linhaImpar, linhaPar"
                                value="#{BusinessIntelligenceCRMControle.listaIndObjecoes}"
                                var="objecoes">

                    <rich:column sortBy="#{objecoes.objecao}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Obje��o"/>
                        </f:facet>
                        <h:outputText value="#{objecoes.objecao}"/>
                    </rich:column>

                    <rich:column sortBy="#{objecoes.faseApresentar}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Fase"/>
                        </f:facet>
                        <h:outputText value="#{objecoes.faseApresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{objecoes.quantidade}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Quantidade"/>
                        </f:facet>
                        <h:outputText value="#{objecoes.quantidade}"/>
                    </rich:column>

                    <rich:column sortBy="#{objecoes.quantidade}">
                        <f:facet name="header">
                            <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                          value="Porcentagem(%)"/>
                        </f:facet>
                        <h:outputText value="#{objecoes.porcentagem}"/>
                    </rich:column>

                </rich:dataTable>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>


<rich:modalPanel id="modalObjecaoDefinitiva"
                 showWhenRendered="#{MetaCRMControle.apresentarModalObjecaoDefinitiva}"
                 width="400" styleClass="novaModal"
                 domElementAttachment="parent" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Obje��o Definitiva"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form>
        <h:panelGrid columns="1" width="100%"
                     footerClass="colunaCentralizada" headerClass="subordinado" styleClass="paginaFontResponsiva">

            <h:panelGroup layout="block" styleClass="paginaFontResponsiva" style="text-align: center">
                <h:outputText styleClass="negrito"
                              style="color: red; font-size: 1.2em; font-weight: bold;"
                              value="ATEN��O cliente possui uma Obje��o DEFINITIVA!"/>
            </h:panelGroup>

            <h:panelGrid columns="2"  styleClass="paginaFontResponsiva" cellpadding="8"
                         columnClasses="classEsquerda, classDireita"
                         width="100%">

                <h:outputText styleClass="font14 cinza negrito"
                              value="Obje��o: "/>
                <h:outputText styleClass="font14 cinza" value="#{MetaCRMControle.historicoContatoVO.clienteVO.objecao.descricao}"/>

            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="paginaFontResponsiva" style="text-align: center">
                <h:outputText styleClass="negrito"
                              style="font-size: 1.2em;"
                              value="Para que o(a) aluno(a) volte �s metas do CRM � necess�rio remover a Obje��o definitiva, acesse a matricula e clique no �cone do lado esquerdo da foto."/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="container-botoes">

                <a4j:commandLink reRender="formRemoverObjecao"
                                 oncomplete="Richfaces.hideModalPanel('modalObjecaoDefinitiva');"
                                 title="Em 'CONTINUAR' o contato ser� realizado, por�m, a Obje��o n�o ser� removida."
                                 styleClass="botaoPrimario texto-size-14">
                    <h:outputText style="font-size: 14px" value="Continuar"/>
                </a4j:commandLink>


                <a4j:commandLink style="margin-left: 8px"
                                 styleClass="botaoSecundario texto-size-14"
                                 title="Ao 'CANCELAR' a tela de contato ser� fechada."
                                 oncomplete="window.close();fireElementFromParent('form:btnAtualizaPagina');"
                                 reRender="form,modalObjecaoDefinitiva">
                    <h:outputText style="font-size: 14px" value="Cancelar"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalListaObjecoesClientes" width="1000" height="410" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Lista de Clientes Com Obje��o Definitiva"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidemodalListaObjecoesClientes"/>
            <rich:componentControl for="modalListaObjecoesClientes" attachTo="hidemodalListaObjecoesClientes"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form>
        <h:panelGroup layout="block" style="display: inline-flex; width: 100%;">
            <h:panelGroup layout="block" style="width: 50%; text-align: left">
                <h:outputText value="Total de Clientes: #{BusinessIntelligenceCRMControle.indClientesObjecaoDefinitiva}" style="font-size: 15px; color: #474747; font-family: Arial,Verdana,sans-serif;"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="float: right; padding-bottom: 10px; width: 50%; text-align: right">

                <%--BOT?O EXCEL--%>
                <a4j:commandLink
                        id="exportarObExcelDetClienteObje"
                        styleClass="exportadores"
                        style="margin-right: 10px"
                        actionListener="#{ExportadorListaControle.exportar}"
                        rendered="#{not empty BusinessIntelligenceCRMControle.listaClientesObjecaoDefinitiva}"
                        oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','ClienteObjecaoDefinitiva', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaClientesObjecaoDefinitiva}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="matricula=Matricula,nome=Nome,situacao=Situa��o,telefone=Telefone(s),email=Email(s),observacao=Obje��o,consultor=Consultor,plano=Plano,situacaoContrato=Situa��o do Contrato"/>
                    <f:attribute name="prefixo" value="ClienteObjecaoDefinitiva-Detalhada"/>
                    <f:attribute name="titulo" value="Clientes Com Obje��o Definitiva"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>

                <%--BOT?O PDF--%>
                <a4j:commandLink
                        id="exportarObPDFDetClienteObje"
                        styleClass="exportadores"
                        actionListener="#{ExportadorListaControle.exportar}"
                        rendered="#{not empty BusinessIntelligenceCRMControle.listaClientesObjecaoDefinitiva}"
                        oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','ClienteObjecaoDefinitiva', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaClientesObjecaoDefinitiva}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                 value="matricula=Matricula,nome=Nome,situacao=Situa��o,telefone=Telefone(s),email=Email(s),observacao=Obje��o,consultor=Consultor,plano=Plano,situacaoContrato=Situa��o do Contrato"/>
                    <f:attribute name="prefixo" value="ClienteObjecaoDefinitiva-Detalhada"/>
                    <f:attribute name="titulo" value="Clientes Com Obje��o Definitiva"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGroup>

        <h:panelGroup layout="block" style="overflow-y: auto; max-height: 400px; padding-bottom: 5px" id="teste">
            <rich:dataTable width="100%" headerClass="subordinado"
                            rowClasses="linhaImpar, linhaPar"
                            value="#{BusinessIntelligenceCRMControle.listaClientesObjecaoDefinitiva}"
                            var="cliente">

                <rich:column sortBy="#{cliente.matricula}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Matr�cula"/>
                    </f:facet>
                    <h:outputText value="#{cliente.matricula}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.nome}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Nome"/>
                    </f:facet>
                    <h:outputText value="#{cliente.nome}"/>
                </rich:column>
                <rich:column sortBy="#{cliente.situacao}" style="text-align: center">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Situa��o"/>
                    </f:facet>
                    <h:outputText value="#{cliente.situacao}"/>
                </rich:column>
                <rich:column sortBy="#{cliente.telefone}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Telefone(s)"/>
                    </f:facet>
                    <h:outputText value="#{cliente.telefone}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.observacao}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Obje��o"/>
                    </f:facet>
                    <h:outputText value="#{cliente.observacao}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.consultor}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Consultor"/>
                    </f:facet>
                    <h:outputText value="#{cliente.consultor}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.plano}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Plano"/>
                    </f:facet>
                    <h:outputText value="#{cliente.plano}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.situacaoContrato}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Sit. Contrato"/>
                    </f:facet>
                    <h:outputText value="#{cliente.situacaoContrato}"/>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
                            
<rich:modalPanel id="modalListaPorObjecoesClientes" width="800" height="410" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Lista de Clientes Com Obje��o"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidemodalListaPorObjecoesClientes"/>
            <rich:componentControl for="modalListaPorObjecoesClientes" attachTo="hidemodalListaPorObjecoesClientes"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form>
        <h:panelGroup layout="block" style="display: inline-flex; width: 100%;">
            <h:panelGroup layout="block" style="width: 50%; text-align: left">
                <h:outputText value="Total de Clientes: #{BusinessIntelligenceCRMControle.quantidadeClientePorObjecao}" style="font-size: 15px; color: #474747; font-family: Arial,Verdana,sans-serif;"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="float: right; padding-bottom: 10px; width: 50%; text-align: right">

                <%--BOT?O EXCEL--%>
                <a4j:commandLink
                        id="exportarObExcelDetClienteObje"
                        styleClass="exportadores"
                        style="margin-right: 10px"
                        actionListener="#{ExportadorListaControle.exportar}"
                        rendered="#{not empty BusinessIntelligenceCRMControle.listaClientePorObjecao}"
                        oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','ClientePorObjecaoDefinitiva', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaClientePorObjecao}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                value="matricula=Matricula,nome=Nome,situacao=Situa��o,telefone=Telefone(s),email=Email(s),observacao=Obje��o,dataHoraCadastroApresentar=Dt. Obje��o,consultor=Consultor,tipoCliente=Tipo"/>
                    <f:attribute name="prefixo" value="ClientePorObjecaoDefinitiva-Detalhada"/>
                    <f:attribute name="titulo" value="Clientes por Obje��o Definitiva"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>

                <%--BOT?O PDF--%>
                <a4j:commandLink
                        id="exportarObPDFDetClienteObje"
                        styleClass="exportadores"
                        actionListener="#{ExportadorListaControle.exportar}"
                        rendered="#{not empty BusinessIntelligenceCRMControle.listaClientePorObjecao}"
                        oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','ClientePorObjecaoDefinitiva', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaClientePorObjecao}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                value="matricula=Matricula,nome=Nome,situacao=Situa��o,telefone=Telefone(s),email=Email(s),observacao=Obje��o,dataHoraCadastroApresentar=Dt. Obje��o,consultor=Consultor,tipoCliente=Tipo"/>
                    <f:attribute name="prefixo" value="ClientePorObjecaoDefinitiva-Detalhada"/>
                    <f:attribute name="titulo" value="Clientes por Obje��o Definitiva"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGroup>

        <h:panelGroup layout="block" style="overflow-y: auto; max-height: 400px; padding-bottom: 5px" id="teste">
            <rich:dataTable width="100%" headerClass="subordinado"
                            rowClasses="linhaImpar, linhaPar"
                            value="#{BusinessIntelligenceCRMControle.listaClientePorObjecao}"
                            var="cliente">

                <rich:column sortBy="#{cliente.matricula}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Matr�cula"/>
                    </f:facet>
                    <h:outputText value="#{cliente.matricula}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.nome}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Nome"/>
                    </f:facet>
                    <h:outputText value="#{cliente.nome}"/>
                </rich:column>
                <rich:column sortBy="#{cliente.situacao}" style="text-align: center">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Situa��o"/>
                    </f:facet>
                    <h:outputText value="#{cliente.situacao}"/>
                </rich:column>
                <rich:column sortBy="#{cliente.telefone}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Telefone(s)"/>
                    </f:facet>
                    <h:outputText value="#{cliente.telefone}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.observacao}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Obje��o"/>
                    </f:facet>
                    <h:outputText value="#{cliente.observacao}"/>
                </rich:column>
                
                <rich:column sortBy="#{cliente.dataHoraCadastroApresentar}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Dt. Obje��o"/>
                    </f:facet>
                    <h:outputText value="#{cliente.dataHoraCadastroApresentar}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.consultor}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Consultor"/>
                    </f:facet>
                    <h:outputText value="#{cliente.consultor}"/>
                </rich:column>
                
                <rich:column sortBy="#{cliente.tipoCliente}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Tipo"/>
                    </f:facet>
                    <h:outputText value="#{cliente.tipoCliente}"/>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
                            
<rich:modalPanel id="modalListaPorObjecoesClientesReduzido" width="800" height="410" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Lista de Clientes por Obje��o"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidemodalListaPorObjecoesClientesReduzido"/>
            <rich:componentControl for="modalListaPorObjecoesClientesReduzido" attachTo="hidemodalListaPorObjecoesClientesReduzido"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form>
        <h:panelGroup layout="block" style="display: inline-flex; width: 100%;">
            <h:panelGroup layout="block" style="width: 50%; text-align: left">
                <h:outputText value="Total de Clientes: #{BusinessIntelligenceCRMControle.quantidadeClientePorObjecao}" style="font-size: 15px; color: #474747; font-family: Arial,Verdana,sans-serif;"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="float: right; padding-bottom: 10px; width: 50%; text-align: right">

                <%--BOT?O EXCEL--%>
                <a4j:commandLink
                        id="exportarObExcelDetClienteObje"
                        styleClass="exportadores"
                        style="margin-right: 10px"
                        actionListener="#{ExportadorListaControle.exportar}"
                        rendered="#{not empty BusinessIntelligenceCRMControle.listaClientePorObjecao}"
                        oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','ClientePorObjecao', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaClientePorObjecao}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                value="nome=Nome,telefone=Telefone(s),email=Email(s),observacao=Obje��o,dataHoraCadastroApresentar=Dt. Obje��o,tipoCliente=Tipo"/>
                    <f:attribute name="prefixo" value="ClientePorObjecao-Detalhado"/>
                    <f:attribute name="titulo" value="Clientes por Obje��o"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>

                <%--BOT?O PDF--%>
                <a4j:commandLink
                        id="exportarObPDFDetClienteObje"
                        styleClass="exportadores"
                        actionListener="#{ExportadorListaControle.exportar}"
                        rendered="#{not empty BusinessIntelligenceCRMControle.listaClientePorObjecao}"
                        oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','ClientePorObjecao', 800,200);#{ExportadorListaControle.msgAlert}">
                    <f:attribute name="lista" value="#{BusinessIntelligenceCRMControle.listaClientePorObjecao}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                value="nome=Nome,telefone=Telefone(s),email=Email(s),observacao=Obje��o,dataHoraCadastroApresentar=Dt. Obje��o,tipoCliente=Tipo"/>
                    <f:attribute name="prefixo" value="ClientePorObjecao-Detalhado"/>
                    <f:attribute name="titulo" value="Clientes por Obje��o"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGroup>

        <h:panelGroup layout="block" style="overflow-y: auto; max-height: 400px; padding-bottom: 5px" id="teste">
            <rich:dataTable width="100%" headerClass="subordinado"
                            rowClasses="linhaImpar, linhaPar"
                            value="#{BusinessIntelligenceCRMControle.listaClientePorObjecao}"
                            var="cliente">

                <rich:column sortBy="#{cliente.nome}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Nome"/>
                    </f:facet>
                    <h:outputText value="#{cliente.nome}"/>
                </rich:column>
                <rich:column sortBy="#{cliente.telefone}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Telefone(s)"/>
                    </f:facet>
                    <h:outputText value="#{cliente.telefone}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.observacao}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Obje��o"/>
                    </f:facet>
                    <h:outputText value="#{cliente.observacao}"/>
                </rich:column>
                
                <rich:column sortBy="#{cliente.dataHoraCadastroApresentar}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Dt. Obje��o"/>
                    </f:facet>
                    <h:outputText value="#{cliente.dataHoraCadastroApresentar}"/>
                </rich:column>
                
                <rich:column sortBy="#{cliente.tipoCliente}">
                    <f:facet name="header">
                        <h:outputText style="font-size: 16px; font-weight: bold; color: #888888"
                                      value="Tipo"/>
                    </f:facet>
                    <h:outputText value="#{cliente.tipoCliente}"/>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<%@include file="/include_load_configs.jsp" %>

<h:panelGroup id="includesCRM">
    <%@include file="/includes/crm/include_crm_metasMensais.jsp" %>
    <%@include file="/include_modal_expiracaoSenha.jsp" %>
</h:panelGroup>



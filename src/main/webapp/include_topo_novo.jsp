<%@ taglib prefix="tags" tagdir="/WEB-INF/tags" %>
<%@page contentType="text/html"  %>
<%@page pageEncoding="ISO-8859-1" %>
<%
    response.setHeader("Cache-Control", "private");
    response.setDateHeader("Expires", System.currentTimeMillis() + 86400000L); // um dia
    response.setDateHeader("Age", System.currentTimeMillis() + 86400000L);

%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="${root}/beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css"/>
<script>
    var ctxForJS = "${pageContext.request.contextPath}"
</script>

<c:if test="${SuperControle.ativarGoogleAnalytics}">
    <tags:google-analytics/>
</c:if>

<c:if test="${SuperControle.ativarWeHelp and not LoginControle.usuarioPacto}">
    <tags:weHelp/>
</c:if>

<jsp:include page="include_localize_traducao_linguagem.jsp"/>
<jsp:include page="include_topo_menu_zw_ui.jsp"/>

<%--BLOCOS DE MODULOS ATIVOS--%>
<c:if test="${!SuperControle.menuZwUi}">
<h:panelGroup styleClass="topoModulos" layout="block" style="">

    <c:if test="${SuperControle.empresaProducao}">
        <jsp:include page="include_topo_conhecimento_UCP.jsp"/>
    </c:if>
    <c:if test="${!SuperControle.empresaProducao}">
        <jsp:include page="include_topo_tipo_empresa.jsp"/>
    </c:if>

    <h:panelGroup layout="block" styleClass="blocoModulos col-md-4 pull-right">
        <h:panelGroup layout="block" styleClass="moduloAberto #{LoginControle.moduloAberto.sigla}"/>
        <h:panelGroup layout="block" styleClass="moduloFechados">
            <a4j:repeat value="#{LoginControle.modulosHabilitados}" var="modulo" id="modulos">
                <a4j:commandLink action="#{LoginControle.getAbrirModulo}"
                                 style="width: #{LoginControle.tamanhoModulosAbertos}%"
                                 rendered="#{not fn:contains(modulo.sigla,LoginControle.moduloAberto.sigla) && not fn:contains(modulo.sigla,'TR') && not fn:contains(modulo.sigla,'SLC') && not fn:contains(modulo.sigla,'NFSE') && not fn:contains(modulo.sigla,'UCP')}"
                                 styleClass="moduloFechado #{modulo.sigla}"
                                 id="abrirModulo">
                </a4j:commandLink>
                <h:outputLink  value="#{LoginControle.abrirModulo}" target="_blank"
                               style="width: #{LoginControle.tamanhoModulosAbertos}%"
                               rendered="#{not fn:contains(modulo.sigla,LoginControle.moduloAberto.sigla) && ((fn:contains(modulo.sigla,'TR') || fn:contains(modulo.sigla,'SLC') || fn:contains(modulo.sigla,'NFSE') || fn:contains(modulo.sigla,'UCP')))}"
                               styleClass="moduloFechado #{modulo.sigla} ">
                </h:outputLink>
            </a4j:repeat>
        </h:panelGroup>

    </h:panelGroup>
</h:panelGroup>
</c:if>
<%--OPCOES MODULOS--%>
<c:if test="${!SuperControle.menuZwUi}">
<h:panelGroup id="topoNovo" layout="block" styleClass="topoOpcoes">
    <h:panelGroup layout="block" style="line-height: 3.2;" styleClass="botoesOpcoes pull-right">
        <h:panelGroup layout="block" styleClass="configuracoes" style="height: 70px">
            <h:panelGroup layout="block" style="height: 100%;display: inline-block" styleClass="tudo">
                <h:panelGroup layout="block" styleClass="dropDownMenu"
                              style="float: right;position: relative">
                    <div class="itemsOpcoes menuModulos dropdown-toggle dropdown-border"  data-toggle="dropdown">
                        <h:graphicImage styleClass="iconePadrao" url="/imagens_flat/marcaGente.png"
                                        style="padding-top:39%;padding-left: 26%;display: none"
                                        width="25" height="26"/>
                        <h:graphicImage styleClass="iconeLogado pull-left"
                                        id="moduloAbertoSistema"
                                        url="/imagens_flat/#{LoginControle.moduloAberto.iconeModulo}"
                                        style="padding-top:39%;padding-left: 26%;" width="25"
                                        height="26"/>
                    </div>
                    <ul class="dropMenuModulo dropdown-menu" role="menu" aria-labelledby="dropdownMenu">

                        <%-- ADM --%>
                        <c:if test="${LoginControle.moduloAberto.sigla !=  'ZW'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink action="#{LoginControle.abrirZillyonWeb}"
                                                     style="font-size: 20px;"
                                                     value="adm" styleClass="tituloModulos" id="abrirModulotopoZW">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-administrativo.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- NOVO - TREINO  --%>
                        <c:if test="${LoginControle.apresentarModuloNovaPlataforma}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirTreinoNovo}"
                                                  styleClass="tituloModulos" id="abrirModulotopoTreinoNovo">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-novo-treino.svg" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="treino"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- CRM --%>
                        <c:if test="${LoginControle.apresentarLinkParaModuloCRM and LoginControle.moduloAberto.sigla !=  'CRM'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink value="crm" actionListener="#{SkinControle.definirSkinCrm}"
                                                     id="abrirModulotopoCRM"
                                                     style="font-size: 20px;"
                                                     action="#{LoginControle.abrirModuloCRM}"
                                                     styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-crm.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- FINANCEIRO --%>
                        <c:if test="${LoginControle.apresentarLinkFinanceiro and LoginControle.moduloAberto.sigla !=  'FIN'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink action="#{LoginControle.abrirModuloFinanceiro}"
                                                     value="financeiro"
                                                     style="font-size: 20px;"
                                                     id="abrirModulotopoFIN"
                                                     actionListener="#{SkinControle.definirSkinFinanceiro}"
                                                     styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-financeiro.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- PACTOPAY --%>
                        <c:if test="${LoginControle.apresentarModuloPactoPay}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirPactoPay}"
                                                  styleClass="tituloModulos" id="abrirPactoPay">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/gentePAY.svg" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="pactopay"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- NOVO - ADM  --%>
<%--                        <c:if test="${LoginControle.possuiModuloNZW}">--%>
<%--                            <li>--%>
<%--                                <h:panelGroup layout="block" styleClass="item">--%>
<%--                                    <h:outputLink value="#{LoginControle.abrirNZW}"--%>
<%--                                                  styleClass="tituloModulos" id="abrirModulotopoNZW">--%>
<%--                                        <h:graphicImage style="float: left;margin-top: 10px"--%>
<%--                                                        url="/imagens_flat/pct-icone-fundo-administrativo.svg" width="25"--%>
<%--                                                        height="26"/>--%>
<%--                                        <h:outputText style="font-size: 20px;" value="Novo ADM"/>--%>
<%--                                    </h:outputLink>--%>
<%--                                </h:panelGroup>--%>
<%--                            </li>--%>
<%--                        </c:if>--%>

                        <%-- AULA CHEIA --%>
                        <c:if test="${LoginControle.apresentarModuloAulaCheia}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirAulaCheia}" styleClass="tituloModulos" id="abrirModulotopoSL">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/genteAC.png" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="aula cheia"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- CANAL DO CLIENTE --%>
                        <c:if test="${LoginControle.moduloAberto.sigla !=  'CANAL'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink styleClass="tituloModulos" id="abrirModuloCanalCliente"
                                                     oncomplete="#{CanalPactoControle.mensagemNotificar}"
                                                     action="#{LoginControle.abrirModuloCanalCliente}">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-canal-do-cliente.svg" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="canal do cliente"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- CENTRAL DE EVENTOS --%>
                        <c:if test="${LoginControle.apresentarLinkCE and LoginControle.moduloAberto.sigla !=  'CE'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink value="central eventos"
                                                     style="font-size: 20px;"
                                                     id="abrirModulotopoCE"
                                                     oncomplete="#{LoginControle.msgAlert}"
                                                     action="#{LoginControle.abrirCE}" styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-administrativo.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- GAME OF RESULTS --%>
                        <c:if test="${LoginControle.permissaoAcessoMenuVO.gameOfResults}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink target="_blank"
                                                  value="#{BIControle.urlGame}" styleClass="tituloModulos" id="abrirModulotopoGAME">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-game-of-results.svg" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="game of results" />
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- NOTA FISCAL - ENOTAS --%>
                        <c:if test="${LoginControle.apresentarNotaFiscal and LoginControle.moduloAberto.sigla !=  'NOTAS'}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <a4j:commandLink value="nota fiscal"
                                                     style="font-size: 20px;"
                                                     id="abrirModuloNotaFiscal"
                                                     action="#{LoginControle.abrirModuloNotaFiscal}"
                                                     styleClass="tituloModulos">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/genteNOTAS.svg" width="25"
                                                        height="26"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- TREINO --%>
                        <c:if test="${LoginControle.validarTreinoLoginApp && LoginControle.apresentarLinkTREINO}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirTreino}" styleClass="tituloModulos" id="abrirModulotopoTR">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-novo-treino.svg" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="treino descontinuado"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- GRADUAÇÃO  --%>
                        <c:if test="${LoginControle.apresentarModuloNovaPlataforma}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirGraduacao}"
                                                  styleClass="tituloModulos" id="abrirModulotopoGraduacao">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-graduacao.svg" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="graduação"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- AVALIAÇÃO FISICA  --%>
                        <c:if test="${LoginControle.apresentarModuloNovaPlataforma}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirAvaliacaoFisica}"
                                                  styleClass="tituloModulos" id="abrirModulotopoAvaliacaoFisica">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-avaliacao.svg" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="avaliação física"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- CROSS  --%>
                        <c:if test="${LoginControle.apresentarModuloNovaPlataforma}">
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink value="#{LoginControle.abrirCross}"
                                                  styleClass="tituloModulos" id="abrirModulotopoCross">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/pct-icone-fundo-cross.svg" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="cross"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <%-- UCP
                            <li>
                                <h:panelGroup layout="block" styleClass="item">
                                    <h:outputLink target="_blank"
                                                  value="#{LoginControle.linkModuloUCP}" styleClass="tituloModulos"
                                                  id="abrirModulotopoUCP">
                                        <h:graphicImage style="float: left;margin-top: 10px"
                                                        url="/imagens_flat/genteUCP.png" width="25"
                                                        height="26"/>
                                        <h:outputText style="font-size: 20px;" value="ucp"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        --%>
                    </ul>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="boxOpcoes transition-all pull-right"
                          style="display: table;margin-right: 20px;">
                <h:panelGroup layout="block"
                              styleClass="dropDownMenu col-celula finalStep tudo">
                    <h:panelGroup layout="block">
                        <h:outputLink value="https://pactosolucoes.com.br/ajuda/"
                                      title="Central de ajuda" target="_blank" styleClass="tooltipster">
                            <div class="itemsOpcoes dropdown-toggle dropdown-border">
                                <h:outputText styleClass="fa-icon-question-sign btnDropDown aberto"/>
                            </div>
                        </h:outputLink>

                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              styleClass="dropDownMenu col-celula tudo" rendered="#{!SuperControle.usuarioLogado.administrador && SuperControle.empresaLogado.codigo != 0 && InicioControle.usuarioMultiEmpresa}">
                    <h:panelGroup layout="block">
                        <div style="position: relative;"
                             class="itemsOpcoes iconeTrocaEmpresa  dropdown-toggle  dropdown-border"  data-toggle="dropdown">
                            <h:outputText style="display:block;line-height: 59px;"
                                          styleClass="fa-icon-exchange iconeEmpresa aberto"/>
                            <h:outputText style="display: none;"
                                          styleClass="fa-icon-exchange iconeEmpresa fechado"/>
                        </div>
                        <ul class="dropMenuEmpresa dropdown-menu" role="menu" aria-labelledby="dropdownMenu" style="overflow: scroll; max-height: 500px">
                            <li>
                                <h:panelGroup layout="block"  style="margin-left: 20px;margin-right:20px;text-align: left;width: calc((9.8px * #{InicioControle.tamanhoMaiorNomeEmpresa}) );">
                                    <h:outputText styleClass="sink textSmallFlat3"
                                                  style="color:#BBBDBF; vertical-align: top;text-decoration: none;"
                                                  value="#{InicioControle.empresaLogado.nome}"></h:outputText>
                                </h:panelGroup>
                            </li>
                            <c:forEach items="#{InicioControle.usuarioLogado.usuarioPerfilAcessoVOs}"
                                       var="perfil">
                                <c:if test="${perfil.empresa.codigo != InicioControle.empresaLogado.codigo}">
                                    <li>

                                        <h:panelGroup layout="block" style="margin-left: 20px;margin-right:20px;text-align: left;width: calc((9.8px * #{InicioControle.tamanhoMaiorNomeEmpresa}) );">
                                            <a4j:commandLink
                                                    style="color:#BBBDBF; vertical-align: top;text-decoration: none;"
                                                    value="#{perfil.empresa.nome}"

                                                    action="#{InicioControle.confirmarTrocaEmpresa}"
                                                    styleClass="sink textSmallFlat">
                                                <f:setPropertyActionListener value="#{perfil.empresa.codigo}"
                                                                             target="#{InicioControle.codigoEmpresa}"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </li>
                                </c:if>
                            </c:forEach>
                        </ul>
                    </h:panelGroup>
                </h:panelGroup>

                <!-- inicio qrcodes -->
                <c:if test="${!LoginControle.usuarioLogado.administrador}">
                    <h:panelGroup layout="block" style="height: 100%;display: inline-block" styleClass="tudo">
                        <h:panelGroup layout="block" styleClass="dropDownMenu"
                                      style="float: right;position: relative">
                            <style>
                                .open>.dropdown-border{
                                    border-right: 1px solid #aebbbc !important;
                                }
                                .dropDownMenu .dropMenuModulo.qrcodes{
                                    left: calc(100% - 3 *(100%) - 101px) !important;
                                    right: auto;
                                }
                                .open .mensagemCmarc{
                                    display: none;
                                }
                            </style>
                            <div class="itemsOpcoes menuModulos dropdown-toggle dropdown-border clienteMarcado"  data-toggle="dropdown"
                                 style="border-right: none; padding-top: 39%;display: table-cell;">
                                <i class="fa-icon-qrcode itemsOpcoes"></i>
                                <div class="mensagemCmarc" style="color: #777; font-size: 15px !important; width: 176px; height: 35px; line-height: 21px; margin: 15px -74px;">
                                    <h:outputText value="Acesso rápido aos Apps da Pacto" escape="false"/>
                                </div>
                            </div>
                            <ul class="dropMenuModulo dropdown-menu qrcodes" role="menu" aria-labelledby="dropdownMenu">
                                <li>
                                    <h:panelGroup layout="block" styleClass="col-celula tudo clienteMarcado" style="display: table-cell !important;">
                                        <h:panelGroup layout="block" styleClass="itemsOpcoes" id="appAssinaturaLink" style="height: 1.1em">
                                            <a4j:commandLink action="#{LoginControle.abrirModalDadosAppAssinatura}"
                                                             reRender="idmdlqrcode"
                                                             styleClass="tituloModulos"
                                                             status="false"
                                                             oncomplete="#{LoginControle.msgAlert}" style="display: block;text-decoration: none;">
                                                <img class="itemsOpcoes"
                                                     src="${LoginControle.moduloAberto.imgAssinaturaDigital}"
                                                     style="float: left;margin-top: 13px; width: 25px; margin-left: 10px;"/>
                                                <h:outputText value="assinatura digital"
                                                              style="margin-left: 5px; line-height: 50px;"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>

                                    </h:panelGroup>
                                </li>

                                <li>
                                    <h:panelGroup layout="block" styleClass="col-celula tudo clienteMarcado" style="display: table-cell !important;">
                                        <h:panelGroup layout="block" styleClass="itemsOpcoes" id="appCartaoVacinaLink" style="height: 1.1em">
                                            <a4j:commandLink action="#{LoginControle.abrirModalDadosAppCartaoVacinacao}"
                                                             reRender="idmdlqrcode"
                                                             styleClass="tituloModulos"
                                                             status="false"
                                                             oncomplete="#{LoginControle.msgAlert}" style="display: block;text-decoration: none;">
                                                <img class="itemsOpcoes"
                                                     src="${LoginControle.moduloAberto.imgCartaoVacina}"
                                                     style="float: left;margin-top: 13px; width: 25px; margin-left: 10px;"/>
                                                <h:outputText value="cartão de Vacina"
                                                              style="margin-left: 5px; line-height: 50px;"/>
                                            </a4j:commandLink>
                                        </h:panelGroup>

                                    </h:panelGroup>
                                </li>
                            </ul>
                        </h:panelGroup>
                    </h:panelGroup>
                </c:if>
                <!-- fim qrcodes -->

                <h:panelGroup layout="block" styleClass="col-celula tudo">
                    <h:panelGroup layout="block" styleClass="itemsOpcoes" id="nrMsgLidas" style="height: 1.35em;">
                        <a4j:commandLink action="#{LoginControle.abrirSocialMailing}" reRender="nrMsgLidas"
                                         oncomplete="#{LoginControle.msgAlert}" style="display: block;text-decoration: none;">
                            <i class="fa-icon-comment itemsOpcoes"></i>
                        </a4j:commandLink>
                        <h:panelGroup layout="block"
                                      rendered="#{UsuarioControle.usuario.nrMensagensNaoLidas > 0}"
                                      style="height: 0;">
                            <a4j:commandLink styleClass="notificacaoSocialMailing" id="nrMsgNaoLidas"
                                             action="#{LoginControle.abrirSocialMailing}"
                                             oncomplete="#{LoginControle.msgAlert}"
                                             reRender="nrMsgLidas"
                                             value="#{UsuarioControle.usuario.nrMensagensNaoLidas}"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" rendered="#{not LoginControle.moduloAtualCe && ((LoginControle.moduloAtualZw && LoginControle.permissaoAcessoMenuVO.configuracaoSistema) ||
                (LoginControle.moduloAtualCrm && LoginControle.permissaoAcessoMenuVO.configuracaoSistemaCRM) || (LoginControle.moduloAtualFin && LoginControle.permissaoAcessoMenuVO.configuracaoFinanceiro))}"
                              styleClass="dropDownMenu col-celula tudo">
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" style="position: relative;"
                                      styleClass="itemsOpcoes iconeTrocaEmpresa" >
                            <a4j:commandLink styleClass="textSmallFlat"
                                             id="btnConfiguracaoZW"
                                             rendered="#{LoginControle.moduloAtualZw}"
                                             action="#{ConfiguracaoSistemaControle.novo}"
                                             oncomplete="abrirPopup('configuracaoSistemaForm.jsp', 'ConfiguracaoSistema', 1024, 768);">
                                <h:outputText style="display:block;"
                                              styleClass="fa-icon-cog iconeEmpresa itemsOpcoes aberto"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="btnConfiguracaoCRM"
                                             rendered="#{LoginControle.moduloAtualCrm}"
                                             action="#{ConfiguracaoSistemaCRMControle.iniciar}"
                                             styleClass="textSmallFlat"
                                             oncomplete="abrirPopup('configuracaoSistemaCRMForm.jsp', 'configuracaoSistemaCRM', 900, 595);">
                                <h:outputText style="display:block;"
                                              styleClass="fa-icon-cog iconeEmpresa itemsOpcoes aberto"/>
                            </a4j:commandLink>
                            <a4j:commandLink action="#{ConfiguracaoFinanceiroControle.preparaEdicao}"
                                             id="btnConfiguracaoFIN"
                                             rendered="#{LoginControle.moduloAtualFin}"
                                             oncomplete="#{ConfiguracaoFinanceiroControle.msgAlert}"
                                             styleClass="textSmallFlat">
                                <h:outputText style="display:block;"
                                              styleClass="fa-icon-cog iconeEmpresa itemsOpcoes aberto"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup id="fotoUsuario" layout="block" styleClass="col-celula fotoUsuario fechado dropDownMenu tudo" style="width: 42px;height: 42px;border-radius:50%">

                    <div class="dropdown-toggle col-vert-align"   data-toggle="dropdown" style="width: 100%;height: 100%;position: relative;display: inline-block;width: 40px;border-radius: 50%;" >
                        <a4j:mediaOutput id="fotosNaNuvemFalse"
                                         element="img"
                                         rendered="#{!SuperControle.fotosNaNuvem}"
                                         align="left"
                                         style="#{SuperControle.styleUsuario}"
                                         cacheable="false" session="false"
                                         title="#{SuperControle.titleUsuario}"
                                         createContent="#{SuperControle.paintFotoUsuario}"
                                         value="#{ImagemData}" styleClass="dropdown-toggle" mimeType="image/jpeg">
                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                            <f:param name="largura" value="50"/>
                            <f:param name="altura" value="50"/>
                        </a4j:mediaOutput>
                        <h:outputText styleClass="icon-hidden fa-icon-caret-down"></h:outputText>
                        <h:graphicImage id="fotosNaNuvemTrue"
                                        rendered="#{SuperControle.fotosNaNuvem}"
                                        width="40" height="40"
                                        style="#{SuperControle.styleUsuario}"
                                        title="#{SuperControle.titleUsuario}"
                                        styleClass="dropdown-toggle"
                                        url="#{SuperControle.fotoKeyUsuarioLogado}">
                        </h:graphicImage>
                    </div>
                    <ul class="dropMenuUsuario dropdown-menu" style="display: none;" role="menu" aria-labelledby="dropdownMenu">
                        <li class="notHover">
                            <div style="width: 100%;text-align: center;height: 100%;line-height: 50px;padding-top:30px; padding-bottom: 10px">
                                <h:outputText style="line-height: 20px" styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-font" value="#{SuperControle.usuarioLogado.nome}"/>
                                <br/>
                                <div style="padding-top: 10px;"/>
                                <h:outputText style="line-height: 20px" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{SuperControle.empresaLogado.nome}"/>
                            </div>
                        </li>
                        <li class="notHover" >
                            <h:panelGroup layout="block" styleClass="container-fluid img-responsive">
                                <h:graphicImage  id="foto" style="max-width:200px;max-height: 200px"
                                                 value="#{SuperControle.urlFotoEmpresa}" >
                                </h:graphicImage>
                            </h:panelGroup>
                        </li>

                        <c:if test="${not empty LoginControle.empresaLogado.urlLinkSiteCadastro}">

                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>

                            <li>
                                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                              style="width: 84%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                    <i class="fa-icon-align-justify"></i>
                                    <h:outputLink target="_blank"
                                                  styleClass="textSmallFlat"
                                                  value="#{LoginControle.empresaLogado.urlLinkSiteCadastro}" >
                                        <h:outputText value="Site Cadastro"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <c:if test="${!LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                            <li>
                                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                    <a4j:commandLink styleClass="textSmallFlat"
                                                     rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}"
                                                     actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                     oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                     action="#{FuncionalidadeControle.abrirComCasoNavegacao}">
                                        <f:attribute name="funcionalidade" value="DADOS_USUSARIO"/>
                                        <h:outputText style="vertical-align: middle;"
                                                      styleClass="fa-icon-user"/> Meus Dados
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                            <li>
                                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                    <h:commandLink styleClass="textSmallFlat"
                                                   value="#{SuperControle.integracaoNovoLogin ? 'Desativar Novo Login' : 'Ativar Novo Login'}"
                                                   action="#{SuperControle.mudarIntegracaoNovoLogin}">
                                    </h:commandLink>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <c:if test="${not LoginControle.moduloAtualCe}">
                            <div style="width: 100%;height: 1px;background-color: rgb(174, 187, 188);margin:10px 0px;"></div>
                            <li>
                                <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                              style="width: 85%;float: right;text-align: left;height: 100%;margin-left: 7.5%;">
                                    <h:commandLink styleClass="textSmallFlat"
                                                   value="Ativar Novo Menu"
                                                   action="#{SuperControle.mudarMenu}"/>
                                </h:panelGroup>
                            </li>
                        </c:if>

                        <div style="width: 80%;height: 1px;margin-left: 10%;background-color: black"></div>
                        <li>
                            <h:panelGroup layout="block" styleClass="alinhaMentoItemMenu"
                                          style="width: 84%;float: right;text-align: left;height: 100%; margin-left: 7.5%;">
                                <a4j:jsFunction action="#{LogoutControle.updateCookiesLogado}" name="doLogout" oncomplete="document.location.href = '#{LogoutControle.redirectLogout}'" />
                                <a id="btnLogout" href="#" class="textSmallFlat" onclick="doLogout();">
                                    <h:outputText style="vertical-align: middle;" styleClass="fa-icon-remove"/> Sair
                                </a>
                            </h:panelGroup>
                        </li>
                    </ul>

                </h:panelGroup>
                <script>
                                                         function fecharTodos() {
                                                             jQuery('.dropDownMenu .fechado').css('display', 'none');
                                                             jQuery('.dropDownMenu .fechado').parent().find('.aberto').css('display', 'block');
                                                         }

                                                         jQuery('div.dropdown-toggle').click(function() {
                                                             var el = jQuery(this).parent().children('ul');
                                                             //  console.log(jQuery(this).attr('class'));
                                                             jQuery('.dropdown-menu').slideUp('fast');
                                                             if (el.css('display') === 'block') {
                                                                 jQuery('.dropdown-menu').parent().removeClass('open');
                                                             } else {
                                                                 jQuery('.dropdown-menu').parent().removeClass('open');
                                                                 el.slideDown('fast', function() {
                                                                     el.parent().addClass('open');
                                                                 });

                                                             }
                                                         });

                                                         jQuery(document).click(function(e) {
                                                             try {
                                                                 var classe = jQuery(e.target).parent().attr('class');
                                                                 if (classe.indexOf('dropdown-toggle') < 0 && classe.indexOf('menuModulos') < 0
                                                                         && classe.indexOf('iconeTrocaEmpresa') < 0 && classe.indexOf('dropDownMenu') < 0
                                                                         && classe.indexOf('naofecharmenufluxo') < 0) {
                                                                     jQuery('.dropDownMenu ul').slideUp('fast', function() {
                                                                         jQuery('.dropdown-menu').parent().removeClass('open');

                                                                     });

                                                                 }
                                                             } catch (ignored) {
                                                             }
                                                         });

                                                         jQuery('.itemsOpcoes').click(function() {
                                                             if (jQuery(this).children('.aberto').css('display') === 'block') {
                                                                 fecharTodos();
                                                                 jQuery(this).children('.aberto').css('display', 'none');
                                                                 jQuery(this).children('.aberto').parent().find('.fechado').css('display', 'block');
                                                             } else {
                                                                 fecharTodos();
                                                             }
                                                         });
                </script>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
    <h:panelGroup styleClass="pull-right painelAlunosMarcados"  id="painelAlunosMarcados">
        <rich:dragIndicator id="indicator" >
        <f:facet name="single">
            <h:panelGroup>
               {testDrop}
            </h:panelGroup>
        </f:facet>
    </rich:dragIndicator>
        <a4j:repeat value="#{ClientesMarcadosControle.clientesMarcados}" var="clienteM">
            <a4j:outputPanel>
                <rich:dragSupport  dragIndicator=":form:indicator"
                                   dragType="alunos" dragValue="#{clienteM}"
                                   ondragstart="subirPainelRemoverAluno()"
                                   ondragend="sumirPainelRemoverAluno()">
                    <rich:dndParam name="testDrop">
                        <h:graphicImage url="#{(SuperControle.fotosNaNuvem ? clienteM.pessoa.urlFoto : clienteM.pessoa.urlFotoContexto )}"
                                        styleClass="imagemAluno pequena"/>
                    </rich:dndParam>
                </rich:dragSupport>
                <div class="clienteMarcado" style="font-family: Arial; font-size: 14px;">
                    <a4j:commandLink style="margin-left: 3px;" action="#{ClientesMarcadosControle.abrirCliente}"
                                  styleClass="tooltipclientesMarcados">
                        <h:graphicImage styleClass="imagemAluno pequena"
                                        url="#{(SuperControle.fotosNaNuvem ? ClientesMarcadosControle.fotoNuvem : clienteM.pessoa.urlFotoContexto )}">
                        </h:graphicImage>
                    </a4j:commandLink>
                    <div class="mensagemCmarc">
                        <h:outputText value="#{clienteM.nomeLembrete}" escape="false"/>
                    </div>
                    <div class="cmarc">
                        <a4j:commandLink title="Adicionar um lembrete"
                                         styleClass="tooltipsterright"
                                         onclick="jQuery('.cliente#{clienteM.codigo}').toggle()"
                                         status="false">
                        <i class="fa-icon-comments" style="line-height: 24px; margin-left: 5px;"></i>
                        </a4j:commandLink>
                        <h:panelGroup styleClass="caixaLembrete cliente#{clienteM.codigo}" layout="block"
                                      >
                            <h:inputTextarea styleClass="inputTextClean" value="#{clienteM.observacao}"
                                             style="width: calc(100% - 20px); margin-top:10px; margin-left:10px; height: 70px;"></h:inputTextarea>
                            <a4j:commandLink styleClass="botaoPrimarioSmall texto-size-14-real"
                                             action="#{ClientesMarcadosControle.gravarObservacao}"
                                             reRender="painelAlunosMarcados"
                                             style="float: right; margin-right: 10px;margin-top: 5px">
                                Gravar
                            </a4j:commandLink>
                        </h:panelGroup>

                    </div>
                </div>

            </a4j:outputPanel>

        </a4j:repeat>

        <script>

            carregarTooltipster();
        </script>

    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="logoIcones col-celula pull-left tudo" style="max-width: 680px; text-align: left;">

        <h:panelGroup layout="block" style="margin-left: 10px;display: inline-block;" styleClass="logo-topo">
            <a4j:commandLink action="#{LoginControle.abrirModulo}" id="idTopo">
                <h:graphicImage height="55px"
                    url="/imagens_flat/#{LoginControle.moduloAberto.logoModulo}" />
            </a4j:commandLink>
        </h:panelGroup>
        <rich:spacer width="15"/>

        <a4j:jsFunction name="verificarTemNova" reRender="painelFeed"
                        status="false"
                        action="#{FeedGestaoControle.verificarTemNova}">
        </a4j:jsFunction>
        <a4j:commandButton style="display: none;" reRender="topoNovo"
                           status="false"
                           id="btnAtualizaFotoUsuario">
        </a4j:commandButton>

        <h:panelGroup layout="block" style="width: 340px; display: inline-block;vertical-align: top;margin-left: 15px;margin-top: 5px;">
            <jsp:include page="includes/include_expiracao.jsp" flush="true"/>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
</c:if>
<%--MENU OPCOES--%>
<jsp:include page="includes/include_modal_feedGestao.jsp" flush="true"/>
<script type="text/javascript">
    function blinker() {
        jQuery('.blink_me').fadeIn(500);
        jQuery('.blink_me').fadeOut(500);
    }
    setInterval(blinker, 500);

    function subirPainelRemoverAluno(){
        jQuery('.painelRemoverAluno').slideDown('slow');
    }
    function sumirPainelRemoverAluno(){
        jQuery('.painelRemoverAluno').slideUp();
    }
</script>

<c:if test="${LoginControle.apresentarHotjar}">
    <script>
        function hotjarParams(empresa, usuario, perfil) {
            hj('tagRecording', ['Empresa', empresa]);
            hj('tagRecording', ['Username', usuario]);
            hj('tagRecording', ['Perfil', perfil]);
        }

        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            //hotjarParams('${LoginControle.empresa.nome}', '${LoginControle.usuario.username}', '${LoginControle.perfilAcesso.nome}');
            h._hjSettings={hjid:2500298,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'//static.hotjar.com/c/hotjar-','.js?sv=');

    </script>
</c:if>

<c:if test="${LoginControle.utilizaChatMovidesk}">
    <!-- Chat do Movidesk -->
    <script type="text/javascript">var mdChatClient="${LoginControle.grupoMoviDesk}";</script>
    <script src="https://chat.movidesk.com/Scripts/chat-widget.min.js"></script>
    <script type="text/javascript">
        // var TEMPO_MINIMIZAR_CHAT = 300;
        function corrigirTelefone(telefone) {
            if (telefone == undefined || telefone == '') {
                return telefone;
            }

            telefone = telefone.replace(/[^0-9.]/g, "");
            if (telefone.length < 9) {
                return telefone;
            }

            if (telefone.length == 9) {
                telefone = telefone.substring(0, 2) + "3" + telefone.substring(2);
            } else if (telefone.length == 10
                && !telefone.substring(2, 3) == "2"
                && !telefone.substring(2, 3) == "3"
                && !telefone.substring(2, 3) == "4"
                && !telefone.substring(2, 3) == "5") {
                telefone = telefone.substring(0, 2) + "9" + telefone.substring(2);
            }

            if (telefone.length == 10) {
                telefone = "(" + telefone.substring(0, 2) + ") " + telefone.substring(2, 6) + "-" + telefone.substring(6, 10);
            } else if (telefone.length == 11) {
                telefone = "(" + telefone.substring(0, 2) + ") " + telefone.substring(2, 7) + "-" + telefone.substring(7, 11);
            }

            return telefone;
        }

        function getTelefone() {
            var telefoneColaborador = '${LoginControle.usuarioLogado.colaboradorVO.primeiroTelefoneNaoNulo}';
            var telefoneEmpresa = '${LoginControle.empresa.primeiroTelefoneNaoNulo}';

            if (telefoneColaborador !== 'SEM TELEFONE') {
                return corrigirTelefone(telefoneColaborador);
            } else if (telefoneEmpresa !== 'SEM TELEFONE') {
                return corrigirTelefone(telefoneEmpresa);
            }
        }

        movideskLogin({
            name: "${LoginControle.usuarioLogado.nome}",
            CodeReference: "${LoginControle.codigoColaboradorMovidesk}",
            CodRefAdditional: "${LoginControle.codigoEmpresaFinMovidesk}",
            OrganizationCodeReference: "${LoginControle.codigoEmpresaMovidesk}",
            PhoneNumber: getTelefone(),
            email: "${LoginControle.emailUsuarioLogado}",
            stayConnected: false,
            emptySubject: false,
            startChat: false
        });

         function ocultarMovChat() {
             waitForElm('.md-chat-widget-btn-wrapper').then(
                 element => {
                     element.style.visibility = 'hidden';
                     movideskChatWidgetChangeWindowState('minimized');
                 }
             )
         }

         window.onload = ocultarMovChat();

        function waitForElm(selector) {
            return new Promise(resolve => {
                if (document.querySelector(selector)) {
                    return resolve(document.querySelector(selector));
                }

                const observer = new MutationObserver(mutations => {
                    if (document.querySelector(selector)) {
                        resolve(document.querySelector(selector));
                        observer.disconnect();
                    }
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            });
        }

        /*var interval = setInterval(function () {
            if (document.readyState == 'complete') {
                movideskChatWidgetChangeWindowState('minimized');
                clearInterval(interval);
            }
        }, TEMPO_MINIMIZAR_CHAT);*/

        // window.onload = clickOnMovChat();

        /*document.addEventListener("DOMContentLoaded", function() {
            debugger
            document.addEventListener("click", function(event) {
                if (event.target) {
                    if (event.target.classList.contains('md-chat-widget-btn-wrapper') ||
                        event.target.classList.contains('md-chat-widget-btn-title') ||
                        event.target.classList.contains('md-chat-widget-btn-icon')) {
                        debugger
                    }
                }
            });
            var container = document.querySelector('md-chat-widget-btn-wrapper');
            visible[0].style.visibility = 'hidden';
        });*/

    </script>


    <!-- Chat do Movidesk fim -->
</c:if>

<c:if test="${LoginControle.utilizaOctadesk}">
    <tags:octadesk/>
</c:if>


<rich:panel styleClass="painelRemoverAluno">

    <rich:dropSupport acceptedTypes="alunos" dropValue="aluno"
                      reRender="#{SuperControle.menuZwUi ? 'painelAlunosMarcadosNovoMenu' : 'painelAlunosMarcados'}"
                      dropListener="#{ClientesMarcadosControle.processDropDelete}">
    </rich:dropSupport>

    <div class="textoMarcarAluno">
        <i class="fa-icon-remove" style="margin-right: 10px;"></i>
        <h:outputText value="Desmarcar aluno"/>
    </div>


</rich:panel>

<style>
    .novaModal.mdlSimples{
        -webkit-box-shadow: 0 2px 10px 0 rgba(0,0,0,0.35);
        -moz-box-shadow: 0 2px 10px 0 rgba(0,0,0,0.35);
        box-shadow: 0 2px 10px 0 rgba(0,0,0,0.35);
        background-color: #ffffff;
    }
</style>
<jsp:include page="include_modal_acesso_app_gestor.jsp"/>
<jsp:include page="includes/include_modal_popUpBloqueado.jsp"/>

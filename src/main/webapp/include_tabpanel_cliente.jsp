<%--
Document   : include_tabpanel_cliente
Created on : 05/11/2010, 15:53:40
Author     : Waller
--%>

<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<meta name="viewport" content="width=device-width, initial-scale=1.0">

<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">

<style type="text/css">
    .rich-tab-header {
        padding: 5px 5px 5px 5px;
        font-weight: bold;
    }

</style>
<a4j:region
        rendered="#{not empty clienteEstudioControle.listaAAgendar || not empty clienteEstudioControle.listaAFaturar}">
    <a4j:jsFunction name="updateAbaState"
                    onbeforedomupdate="document.getElementById('form:abaStudio_lbl').style.fontWeight = 'bold';document.getElementById('form:abaStudio_lbl').style.color = 'red';document.getElementById('form:abaStudio_lbl').title = \"#{clienteEstudioControle.title}\";"/>
</a4j:region>

<script>
    updateAbaState();
</script>
<rich:modalPanel id="modalPanelHistoricoVinculo" autosized="true"
                 shadowOpacity="true" width="750" height="500">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Histórico Vínculo"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkHistoricoVinculo"/>
            <rich:componentControl for="modalPanelHistoricoVinculo" attachTo="hiperlinkHistoricoVinculo"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:panelGroup layout="block" id="panelHistoricoVinculo">
        <jsp:include page="historicoVinculo.jsp" flush="true"/>
    </h:panelGroup>
</rich:modalPanel>

<rich:tabPanel tabClass="aba" width="100%" switchType="ajax" immediate="true" headerAlignment="left"
               selectedTab="#{ClienteControle.clienteVO.abaSelecionada}" id="richPanel">

    <rich:tab label="#{msg_aplic.prt_Cliente_basico}" reRender="clienteSituacao,listaContrato"
              oncomplete="updateAbaState();" id="abaBasico">
        <f:attribute name="codigoCliente" value="#{ClienteControle.clienteVO.codigo}"/>

        <jsp:include page="includes/cliente/include_panelgrid_mensagens_cliente.jsp" flush="true"/>

        <h:panelGrid columns="2" width="100%" columnClasses="colunaCentralizada" cellspacing="5">
            <jsp:include page="includes/cliente/include_panelgrid_dados_pessoais_cliente.jsp" flush="true"/>
            <jsp:include page="includes/cliente/include_panelgrid_dados_aluno_cliente.jsp" flush="true"/>
        </h:panelGrid>

        <jsp:include page="includes/cliente/include_panelgrid_lista_contratos.jsp" flush="true"/>

        <h:panelGrid columns="2" width="100%" columnClasses="colunaCentralizada" cellspacing="5">

            <jsp:include page="includes/cliente/include_panelgroup_dados_acesso_cliente.jsp" flush="true"/>
            <h:panelGroup layout="block">
                <jsp:include page="includes/cliente/include_panelgroup_produtos_validade_cliente.jsp" flush="true"/>
                <rich:spacer height="10"/>
                <jsp:include page="includes/cliente/include_panelgroup_armario_cliente.jsp" flush="true"/>
            </h:panelGroup>
        </h:panelGrid>

    </rich:tab>

    <rich:tab rendered="#{LoginControle.apresentarLinkEstudio}" label="#{msg_aplic.prt_Cliente_studio}"
              actionListener="#{clienteEstudioControle.consultarPaginadoListener}"
              oncomplete="updateAbaState();"
              reRender="abaStudio" id="abaStudio">
        <f:attribute name="codigoCliente" value="#{ClienteControle.clienteVO.codigo}"/>
        <jsp:include page="includes/cliente/include_panelgrid_estudio_cliente.jsp" flush="true"/>
    </rich:tab>

    <!-- tempo: 30 ms -->
    <rich:tab label="#{msg_aplic.prt_Contrato_tituloForm}" id="abaContrato" oncomplete="updateAbaState();"
              rendered="#{ClienteControle.clienteVO.apresentarAbaContrato}">
        <div style="clear: both;">
            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" bgcolor=""
                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;"><img
                                    src="images/arrow2.gif" width="16" height="16"
                                    style="vertical-align: middle; margin-right: 6px;"/>Dados
                                do Contrato <h:outputText style="margin-bottom:6px"
                                                          value="#{ClienteControle.contratoVO.codigo}"/></p>

                            <div class="sep" style="margin: 4px 0 10px 0;"><img
                                    src="images/shim.gif"></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top"><h:panelGrid columns="1"
                                                               width="100%" footerClass="colunaCentralizada"
                                                               headerClass="subordinado">
                        <h:panelGrid columns="2" headerClass="subordinado"
                                     width="100%">
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold" value="Valor Contrato: "/>
                                <h:outputText styleClass="red" style="font-weight: bold"
                                              value="#{MovPagamentoControle.empresaLogado.moeda} "/>
                                <h:outputText id="vlrContrato" styleClass="red" style="font-weight: bold"
                                              value="#{ClienteControle.contratoVO.valorFinal}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText style="font-weight: bold;" styleClass="red" value="CONTRATO IMPORTADO"
                                              rendered="#{ClienteControle.contratoVO.importacao}"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold"
                                              value="Valor Base do Contrato: "/>
                                <h:outputText styleClass="textsmall"
                                              value="#{MovPagamentoControle.empresaLogado.moeda} "/>
                                <h:outputText id="vlrBaseContrato" styleClass="textsmall"
                                              value="#{ClienteControle.contratoVO.valorBaseCalculo}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                            <h:panelGroup id="agendadoEspontaneo">
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold" value="Tipo Contrato: "/>
                                <a4j:commandLink id="linkContratoEspontaneo" styleClass="blueGrande"
                                                 rendered="#{!ClienteControle.contratoVO.contratoAgendado}"
                                                 value="#{msg_aplic.prt_Contrato_espontaneo}"
                                                 actionListener="#{ContratoControle.prepararAlteracaoTipoContrato}"
                                                 oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaTipoContrato');"
                                                 reRender="panelUsuarioSenhaTipoContrato">
                                    <f:attribute value="#{ClienteControle.contratoVO}" name="contratoAlteracaoTipo"/>
                                </a4j:commandLink>
                                <rich:toolTip for="linkContratoEspontaneo" followMouse="true" direction="top-right"
                                              style="width:200px; height:75px;">
                                    <h:outputText styleClass="textoConfiguracoes"
                                                  value="#{msg_aplic.prt_Contrato_Tipodica}"/>
                                </rich:toolTip>
                                <a4j:commandLink id="linkContratoAgendamento" styleClass="blueGrande"
                                                 rendered="#{ClienteControle.contratoVO.contratoAgendado}"
                                                 value="#{msg_aplic.prt_Contrato_agendado}"
                                                 actionListener="#{ContratoControle.prepararAlteracaoTipoContrato}"
                                                 oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaTipoContrato');"
                                                 reRender="panelUsuarioSenhaTipoContrato">
                                    <f:attribute value="#{ClienteControle.contratoVO}" name="contratoAlteracaoTipo"/>
                                </a4j:commandLink>
                                <rich:toolTip for="linkContratoAgendado" followMouse="true" direction="top-right"
                                              style="width:200px; height:75px;">
                                    <h:outputText styleClass="textoConfiguracoes"
                                                  value="#{msg_aplic.prt_Contrato_Tipodica}"/>
                                </rich:toolTip>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall" style="font-weight: bold" value="Empresa: "/>
                                <h:outputText styleClass="textsmall"
                                              value="#{ClienteControle.contratoVO.empresa.nome}"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold" value="Plano: "/>
                                <h:outputText id="descPlano" styleClass="textsmall"
                                              value="#{ClienteControle.contratoVO.plano.descricao}"/>
                            </h:panelGroup>
                            <c:if test="${ClienteControle.apresentarDataAlteracaoManual}">
                                <h:panelGroup>
                                    <h:outputText styleClass="textsmall"
                                                  style="font-weight: bold" value="Alteração Data Base:"/>
                                    <h:outputText styleClass="blue" style="font-weight: bold" value=" "/>
                                    <h:outputText id="dtBaseContrato" styleClass="blue"
                                                  value="#{ClienteControle.contratoVO.dataAlteracaoManual_Apresentar}"/>
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText styleClass="textsmall"
                                                  style="font-weight: bold" value="Data Lançamento:"/>
                                    <h:outputText styleClass="blue" style="font-weight: bold" value=" "/>
                                    <h:outputText id="dtLancamentoContrato" styleClass="textsmall"
                                                  value="#{ClienteControle.contratoVO.dataLancamento_Apresentar2}"/>
                                </h:panelGroup>
                            </c:if>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold" value="Data Início:"/>
                                <h:outputText styleClass="blue" style="font-weight: bold" value=" "/>
                                <h:outputText id="dtInicioAuto" styleClass="textsmall"
                                              value="#{ClienteControle.contratoVO.vigenciaDe_Apresentar}"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold"
                                              value="Data Término Original: "/>
                                <h:outputText id="dtFimAutoOrig" styleClass="textsmall"
                                              value="#{ClienteControle.contratoVO.vigenciaAte_Apresentar}"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"  style="font-weight: bold"
                                              value="Data Término Ajustada: "/>
                                <c:choose>
                                    <c:when test="${ClienteControle.contratoVO.vigenciaAte_Apresentar eq ClienteControle.contratoVO.vigenciaAteAjustada_Apresentar}">
                                        <h:outputText id="dtFimAutoAjus" styleClass="blue"
                                                      value="#{ClienteControle.contratoVO.vigenciaAteAjustada_Apresentar}"/>
                                    </c:when>
                                    <c:otherwise>
                                        <h:outputText id="dtFimAutoAjus" styleClass="red"
                                                      value="#{ClienteControle.contratoVO.vigenciaAteAjustada_Apresentar}"/>
                                    </c:otherwise>
                                </c:choose>
                                <h:outputLabel styleClass="textsmall"
                                               rendered="#{ClienteControle.contratoVO.mostrarLabelPrevisaoRenovacaoAntecipada}"
                                               style="font-weight: bold; padding-left: 10px"
                                               value="Previsão Renovação Original:#{ClienteControle.contratoVO.dataPrevistaRenovar_Apresentar}"></h:outputLabel>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold"
                                              value="Duração: "/>
                                <h:outputText  id="plnDuracao" styleClass="blue"
                                              value="#{ClienteControle.contratoVO.contratoDuracao.descricaoDuracao}"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold" value="Horário: "/>
                                <h:outputText id="hrContrato" styleClass="textsmall"
                                              value="#{ClienteControle.contratoVO.descricaoHorarioContrato_Apresentar}"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold"
                                              value="Condição de Pagamento: "/>
                                <h:outputText styleClass="blue" style="font-size: 13px;"
                                              value="#{ClienteControle.contratoVO.contratoCondicaoPagamento.condicaoPagamento.descricao}"/>


                                <a4j:commandButton style="font-weight:bold;"
                                                   rendered="#{ClienteControle.contratoVO.regimeRecorrencia}"
                                                   reRender="abaContrato,modalTrocarCartaoContratoCliente,panelConteudo2"
                                                   id="btnTrocarCartaoRecorrencia"
                                                   title="Trocar o cartão de crédito associado a este Contrato de Recorrência"
                                                   value="Trocar Cartão"
                                                   oncomplete="#{rich:component('modalTrocarCartaoContratoCliente')}.show();"
                                                   actionListener="#{TrocarCartaoRecorrenciaControle.listenerCodigoContrato}">
                                    <f:attribute name="codigoContrato" value="#{ClienteControle.contratoVO.codigo}"/>
                                </a4j:commandButton>

                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold" value="Bolsa: "/>
                                <h:outputText styleClass="blue"
                                              value="#{ClienteControle.contratoVO.bolsa_Apresentar}"/>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:panelGrid columns="1" headerClass="subordinado"
                                             width="100%">
                                    <h:outputText styleClass="textsmall"
                                                  style="font-weight: bold"
                                                  value="Observação do Contrato: "/>
                                    <h:outputText styleClass="textsmall" escape="false"
                                                  value="#{ClienteControle.contratoVO.observacao}"/>
                                </h:panelGrid>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold"
                                              value="Responsável Pelo Contrato: "/>
                                <h:outputText styleClass="textsmall"
                                              value="#{ClienteControle.contratoVO.responsavelContrato.nome}"/>
                            </h:panelGroup>

                            <h:panelGroup id="consultorContrato">
                                <h:outputText styleClass="textsmall" style="font-weight: bold"
                                              value="Consultor Responsável: "/>
                                <h:outputText styleClass="textsmall" rendered="#{!ClienteControle.alterandoConsultor}"
                                              value="#{ClienteControle.contratoVO.consultor.pessoa.nome}"/>

                                <h:panelGroup rendered="#{ClienteControle.permiteAlterarConsultorContrato}">
                                    <h:panelGroup rendered="#{ClienteControle.alterandoConsultor}">
                                        <rich:spacer width="5px"/>
                                        <h:selectOneMenu value="#{ClienteControle.contratoVO.consultor.codigo}">
                                            <f:selectItems value="#{ClienteControle.listaSelectConsultor}"/>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                    <h:panelGroup>
                                        <rich:spacer width="5px"/>
                                        <a4j:commandLink rendered="#{!ClienteControle.alterandoConsultor}"
                                                         styleClass="textsmall blue"
                                                         action="#{ClienteControle.permitirAlterarConsultorContrato}"
                                                         reRender="consultorContrato" value="Alterar"/>
                                        <a4j:commandLink rendered="#{ClienteControle.alterandoConsultor}"
                                                         styleClass="textsmall blue"
                                                         action="#{ClienteControle.alterarConsultorContrato}"
                                                         reRender="consultorContrato" value="Salvar"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:outputText styleClass="textsmall"
                                              style="font-weight: bold"
                                              value="Convênio de desconto: "/>
                                <h:outputText styleClass="textsmall"
                                              value="#{ClienteControle.contratoVO.convenioDesconto.descricao}"/>
                            </h:panelGroup>
                            <h:panelGroup id="pgInfoCreditoTreinoContrato">
                                <h:panelGroup rendered="#{ClienteControle.contratoVO.vendaCreditoTreino}" >
                                    <h:outputText styleClass="textsmall"  style="font-weight: bold"  value="Quantidade #{ClienteControle.configNomenclaturaVendaCredito} Compra: "/>
                                    <h:outputText styleClass="blue" value="#{ClienteControle.contratoVO.contratoDuracao.contratoDuracaoCreditoTreinoVO.quantidadeCreditoCompra}"/>
                                    <h:outputText rendered="#{ClienteControle.contratoVO.contratoDuracao.contratoDuracaoCreditoTreinoVO.creditoTreinoNaoCumulativo}"
                                                  style="font-weight: bold; padding-left: 10px" value="Quantidade Mensal: " />
                                    <h:outputText rendered="#{ClienteControle.contratoVO.contratoDuracao.contratoDuracaoCreditoTreinoVO.creditoTreinoNaoCumulativo}"
                                                  value="#{ClienteControle.contratoVO.contratoDuracao.contratoDuracaoCreditoTreinoVO.quantidadeCreditoMensal}" />
                                    <h:outputText styleClass="textsmall" style="font-weight: bold; padding-left: 10px"  value="Saldo: "/>
                                    <h:outputText styleClass="blue" value="#{ClienteControle.contratoVO.contratoDuracao.contratoDuracaoCreditoTreinoVO.quantidadeCreditoDisponivel}"/>
                                </h:panelGroup>
                            </h:panelGroup>


                        </h:panelGrid>
                        <h:panelGroup>
                            <h:panelGrid columns="1" width="100%"
                                         headerClass="subordinado"
                                         columnClasses="colunaCentralizada">
                                <f:facet name="header">
                                    <h:outputText value="Modalidades"/>
                                </f:facet>
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%"
                                         styleClass="tabFormSubordinada">
                                <rich:dataTable id="modalidade" width="100%"
                                                rowClasses="linhaImpar, linhaPar"
                                                headerClass="subordinado" columnClasses="textsmall"
                                                value="#{ClienteControle.contratoVO.contratoModalidadeVOs}"
                                                styleClass="tabFormSubordinada" var="contratoModalidade">
                                    <h:column
                                            rendered="#{contratoModalidade.modalidade.modalidadeEscolhida}">
                                        <h:outputText style="font-weight: bold"
                                                      value="Modalidade: "/>
                                        <h:outputText
                                                value="#{contratoModalidade.modalidade.nome}"/>
                                    </h:column>
                                    <h:column
                                            rendered="#{contratoModalidade.modalidade.modalidadeEscolhida}">
                                        <h:outputText style="font-weight: bold"
                                                      value="Número de Vezes por Semana: "/>
                                        <h:outputText
                                                value="#{contratoModalidade.nrVezesSemana} "/>
                                    </h:column>
                                </rich:dataTable>
                            </h:panelGrid>
                        </h:panelGroup>
                        <h:panelGroup id="panelTurmas">

                            <rich:dataTable id="turma" width="100%"
                                            columnClasses="colunaEsquerda"
                                            value="#{ClienteControle.contratoVO.contratoModalidadeVOs}"
                                            var="contratoModalidade">
                                <h:column
                                        rendered="#{contratoModalidade.modalidade.modalidadeEscolhida && not empty contratoModalidade.contratoModalidadeTurmaVOs}">
                                    <rich:dataTable id="modalidadeTurma" width="100%"
                                                    rowClasses="linhaImpar, linhaPar"
                                                    columnClasses="textsmall"
                                                    value="#{contratoModalidade.contratoModalidadeTurmaVOs}"
                                                    var="modalidadeTurma">
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"
                                                              value="Turma"/>
                                            </f:facet>
                                            <h:panelGrid columns="1" columnClasses="textsmall">
                                                <h:column>
                                                    <h:outputText
                                                            value="#{modalidadeTurma.turma.identificador}"/>

                                                    <h:outputText
                                                            value=" - #{modalidadeTurma.turma.descricao}"/>
                                                </h:column>


                                                <h:column>
                                                    <h:outputText
                                                            value="#{modalidadeTurma.turma.dataInicioMatricula_Apresentar}">
                                                    </h:outputText>

                                                    <h:outputText
                                                            value=" à #{modalidadeTurma.turma.dataFimMatricula_Apresentar}">
                                                    </h:outputText>
                                                </h:column>
                                            </h:panelGrid>

                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Modalidade"/>
                                            </f:facet>

                                            <h:outputText
                                                    value="#{contratoModalidade.modalidade.nome}"/>

                                        </rich:column>

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="margin-left:10px ;font-weight: bold"
                                                              value="Horários"/>
                                            </f:facet>
                                            <rich:dataTable id="turmaHorario" rowClasses="textsmall"
                                                            columnClasses="colunaEsquerda"
                                                            value="#{modalidadeTurma.contratoModalidadeHorarioTurmaVOs}"
                                                            var="horario">
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="margin-left:10px ;font-weight: bold"
                                                                      value="Dia da Semana"/>
                                                    </f:facet>

                                                    <h:outputText
                                                            value="#{horario.horarioTurma.diaSemana_Apresentar}    "/>
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText
                                                                style="margin-left:10px ; font-weight: bold"
                                                                value="Hora da Aula"/>
                                                    </f:facet>

                                                    <h:outputText
                                                            value="#{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal}"/>
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="margin-left:10px ;font-weight: bold"
                                                                      value="Professor"/>
                                                    </f:facet>

                                                    <h:outputText
                                                            value="#{horario.horarioTurma.professor.pessoa.nome}"/>
                                                </rich:column>
                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="margin-left:10px ;font-weight: bold"
                                                                      value="Ambiente"/>
                                                    </f:facet>

                                                    <h:outputText
                                                            value="#{horario.horarioTurma.ambiente.descricao}"/>
                                                </rich:column>

                                                <rich:column>
                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold" value="Nível"/>
                                                    </f:facet>

                                                    <h:outputText
                                                            value="#{horario.horarioTurma.nivelTurma.descricao}"/>

                                                </rich:column>

                                                <rich:column styleClass="colunaCentralizada" style="text-align: center;"
                                                             width="10%">

                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold;" value=" Repor "/>
                                                    </f:facet>
                                                    <div style="width: 100%">
                                                        <div style="text-align: center;    width: 50px;margin: 0 auto;">

                                                            <a4j:commandButton image="/images/agendar_reposicao.png"
                                                                               ajaxSingle="true"
                                                                               title="Agendar Reposição para substituir o horário de: #{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal}"
                                                                               actionListener="#{ClienteControle.prepararConsultaTurmaListener}"
                                                                               oncomplete="abrirPopupMaximizada('consultarTurmaForm.jsp', 'ConsultarTurma');">
                                                                <f:attribute name="horarioTurma"
                                                                             value="#{horario.horarioTurma}"/>
                                                            </a4j:commandButton>

                                                                <%--
                                                                 <a4j:commandButton image="/images/agendar_reposicao.png"
                                                                                    ajaxSingle="true"
                                                                                    title="Agendar Reposição para substituir o horário de: #{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal}"
                                                                                    actionListener="#{ClienteControle.prepararConsultaTurmaListener}"
                                                                                    reRender="formAulaDesmarcada, tituloModal"
                                                                                    oncomplete="Richfaces.showModalPanel('modalPanelAulaDesmarcada');">
                                                                     <f:attribute name="horarioTurma" value="#{horario.horarioTurma}"/>
                                                                 </a4j:commandButton>
                                                                 --%>

                                                        </div>
                                                    </div>
                                                </rich:column>


                                                <rich:column styleClass="colunaCentralizada">

                                                    <f:facet name="header">
                                                        <h:outputText style="font-weight: bold;" value="Desmarcar"/>
                                                    </f:facet>
                                                    <div style="width: 100%">
                                                        <div style="padding-left: 20%">
                                                            <a4j:commandButton image="/images/agendar_remover.png"
                                                                               ajaxSingle="true"
                                                                               title="Desmarcar o horário de: #{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal}"
                                                                               actionListener="#{ClienteControle.prepararConsultaTurmaListenerDesmarcar}"
                                                                               reRender="formAulaDesmarcada, tituloModal"
                                                                               oncomplete="Richfaces.showModalPanel('modalPanelAulaDesmarcada');">
                                                                <f:attribute name="horarioTurma"
                                                                             value="#{horario.horarioTurma}"/>
                                                            </a4j:commandButton>
                                                        </div>
                                                    </div>
                                                </rich:column>


                                            </rich:dataTable>
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold;" value="Totalizadores"/>
                                            </f:facet>
                                            <h:panelGrid columns="2" columnClasses="textsmall, textsmall">
                                                <h:outputText style="font-weight: bold;" value="Aulas:"/>
                                                <h:outputText
                                                        value="#{modalidadeTurma.turma.totalAulasAteHoje}/#{modalidadeTurma.turma.totalAulas}"/>

                                                <h:outputText style="font-weight: bold;" value="Presenças:"/>
                                                <h:outputText value="#{modalidadeTurma.turma.totalPresencas}"/>

                                                <h:outputText style="font-weight: bold;" value="Faltas:"/>
                                                <h:outputText
                                                        value="#{modalidadeTurma.turma.totalAulasAteHoje - modalidadeTurma.turma.totalPresencas - modalidadeTurma.turma.totalAulasDesmarcadas}"/>

                                                <h:outputText style="font-weight: bold;" value="Reposições:"/>
                                                <a4j:commandLink value="#{modalidadeTurma.turma.totalReposicoes}"
                                                                 oncomplete="#{ReposicaoControle.onComplete}"
                                                                 reRender="modalReposicoes"
                                                                 actionListener="#{ReposicaoControle.exibirReposicoes}">
                                                    <f:attribute name="turmaReposicao"
                                                                 value="#{modalidadeTurma.turma.codigo}"/>
                                                    <f:attribute name="contrato"
                                                                 value="#{ClienteControle.contratoVO.codigo}"/>
                                                </a4j:commandLink>

                                                <h:outputText style="font-weight: bold;" value="Desmarcadas:"/>
                                                <a4j:commandLink value="#{modalidadeTurma.turma.totalAulasDesmarcadas}"
                                                                 oncomplete="#{ClienteControle.mostrarModalAulasDesmarcadas}"
                                                                 reRender="formListaAulasDesmarcadas"
                                                                 actionListener="#{ClienteControle.exibirListaAulasDesmarcadas}">
                                                    <f:attribute name="turmaReposicao"
                                                                 value="#{modalidadeTurma.turma.codigo}"/>
                                                    <f:attribute name="contrato"
                                                                 value="#{ClienteControle.contratoVO.codigo}"/>
                                                </a4j:commandLink>

                                                <h:outputText style="font-weight: bold;" value="Presenças:"/>
                                                <h:outputText
                                                        value="#{modalidadeTurma.turma.totalReposicoesPresentes}"/>

                                            </h:panelGrid>
                                        </rich:column>
                                    </rich:dataTable>
                                </h:column>
                            </rich:dataTable>
                        </h:panelGroup>
                    </h:panelGrid></td>
                </tr>
            </table>
        </div>
        <%--
             <div style="clear: both;">
                    <jsfChart:chart  id="chartVigenciaContrato" datasource="#{ClienteControle.dataSetGantt}"
                                    title="Vigência do Contrato"
                                    type="gantt" is3d="true" background="#F0F0F0" foreground="white" depth="24"
                                    colors="#6CA6CD, #00BFFF,#7CFC00, #00FF00, #FFD700, #EE9A00, #7D26CD, #00C5CD" antialias="true"
                                    legend="false" height="120" width="1165">
                    </jsfChart:chart>
             </div>
        --%>
<%--
        <h:panelGroup id="pgPesqControleCredito">
            <h:panelGroup rendered="#{ClienteControle.contratoVO.vendaCreditoTreino}">
                <a4j:commandLink id="pesqControleCredito"
                                 styleClass="pure-button pure-button-small"
                                 style="margin-bottom: 10px; font-size: 12px;font-family:Arial,Verdana,sans-serif;"
                                 rendered="#{ClienteControle.contratoVO.listaControleCreditoTreino == null}"
                                 action="#{ClienteControle.consultarControleCreditoTreino}"
                                 reRender="logControleCreditoTreino,pgPesqControleCredito"
                                 accesskey="2" >
                    <i class="fa-icon-search" ></i> &nbsp Controle Crédito de Treino
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup> --%>
        <h:panelGroup id="logControleCreditoTreino" rendered="#{ClienteControle.contratoVO.vendaCreditoTreino}">

                <h:panelGroup id="gridInfoContCredTreino" rendered="#{ClienteControle.contratoVO.listaControleCreditoTreino != null}">
                   <div style="clear: both;" id="divControleCredito">
                    <table width="98%" border="0" align="left" cellpadding="0"
                           cellspacing="0" bgcolor="#e6e6e6"
                           style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                        <tr>
                            <td align="left" valign="top" style="padding-bottom: 5px;">
                                <div style="clear: both;" class="text">
                                    <p style="margin-bottom: 6px;"><img
                                            src="images/arrow2.gif" width="16" height="16"
                                            style="vertical-align: middle; margin-right: 6px;">
                                        <h:outputText value="Extrato #{ClienteControle.configNomenclaturaVendaCredito}"></h:outputText>
                                    </p>
                                </div>
                                <div class="sep" style="margin: 4px 0 10px 0;"><img
                                        src="images/shim.gif"></div>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" valign="top">
                                <h:panelGrid id="pgCreditoTreino" columns="1" width="100%">
                                    <h:panelGroup>
                                        <h:panelGroup>
                                            <a4j:commandLink id="novoControleCredito"
                                                             styleClass="pure-button pure-button-small"
                                                             action="#{ClienteControle.novoAjusteManualControleCreditoTreino}"
                                                             rendered="#{ClienteControle.contratoVO.listaControleCreditoTreino != null}"
                                                             reRender="listaHistoricoCreditoTreino, formControleCreditoTreino"
                                                             oncomplete="#{ClienteControle.msgAlert}"
                                                             accesskey="2" >
                                                <i class="fa-icon-plus" ></i> &nbsp Novo
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                        <h:panelGroup style="padding-left: 10px; padding-right: 10px">
                                            <a4j:commandLink id="btnExcelControleCredito"
                                                             styleClass="pure-button pure-button-small"
                                                             rendered="#{ClienteControle.contratoVO.listaControleCreditoTreino != null}"
                                                             actionListener="#{ClienteControle.exportarControleCreditoTreino}"
                                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','controleCredito', 800,200);#{ExportadorListaControle.msgAlert}"

                                                             accesskey="3">
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="lista" value="#{ClienteControle.contratoVO.listaControleCreditoTreino}"/>
                                                <f:attribute name="atributos" value="datalancamento=Data,operacao=Operaçao,usuario_Apresentar=Usuario,observacao=Observação,aulaDesmarcada_Apresentar=Aula Desmarcada,aulaMarcada_Apresentar=Aula Marcada,quantidade=Quantidade,saldo=Saldo"/>
                                                <f:attribute name="prefixo" value="ControleCreditoTreino#{ClienteControle.contratoVO.pessoa.nome}"/>
                                                <f:attribute name="titulo" value="Controle #{ClienteControle.configNomenclaturaVendaCredito}"/>
                                                <f:attribute name="subTitulo" value="Aluno: #{ClienteControle.contratoVO.pessoa.nome}"/>
                                                <i class="fa-icon-excel" ></i> &nbsp Excel
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                        <h:panelGroup>
                                            <a4j:commandLink id="btnPDFControleCredito"
                                                             styleClass="pure-button pure-button-small"
                                                             actionListener="#{ClienteControle.exportarControleCreditoTreino}"
                                                             rendered="#{ClienteControle.contratoVO.listaControleCreditoTreino != null}"
                                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','controleCredito', 800,200);#{ExportadorListaControle.msgAlert}"
                                                             accesskey="4">
                                                <f:attribute name="tipo" value="pdf"/>
                                                <f:attribute name="lista" value="#{ClienteControle.contratoVO.listaControleCreditoTreino}"/>
                                                <f:attribute name="atributos" value="datalancamento=Data,operacao=Operaçao,usuario_Apresentar=Usuario,observacao=Observação,aulaDesmarcada_Apresentar=Aula Desmarcada,aulaMarcada_Apresentar=Aula Marcada,quantidade=Quantidade,saldo=Saldo"/>
                                                <f:attribute name="prefixo" value="ControleCreditoTreino"/>
                                                <f:attribute name="titulo" value="Controle #{ClienteControle.configNomenclaturaVendaCredito}"/>
                                                <f:attribute name="subTitulo" value="Aluno: #{ClienteControle.contratoVO.pessoa.nome}"/>
                                                <i class="fa-icon-pdf" ></i> &nbsp PDF
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                </h:panelGrid>
                                <h:panelGroup id="pgTableCreditoTreino" >
                                    <rich:dataTable
                                            id="listaHistoricoCreditoTreino" width="100%" border="0"
                                            cellspacing="0" cellpadding="0" styleClass="textsmall"
                                            rows="5"
                                            rendered="#{ClienteControle.contratoVO.listaControleCreditoTreino != null}"
                                            columnClasses="centralizado, centralizado, centralizado,centralizado, centralizado, centralizado, colunaDireita,colunaDireita"
                                            value="#{ClienteControle.contratoVO.listaControleCreditoTreino}"
                                            var="controleCreditoTreino">

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold"  value="Data Operaçao"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{ClienteControle.visualizarControleCreditoTreino}" oncomplete="#{ClienteControle.msgAlert}" reRender="formControleCreditoTreino">
                                                <h:outputText style="font-weight: bold"  value="#{controleCreditoTreino.dataOperacao_Apresentar}"/>
                                                <f:setPropertyActionListener value="#{controleCreditoTreino}" target="#{ClienteControle.controleCreditoTreinoVO}"/>
                                            </a4j:commandLink>

                                        </rich:column>

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Operação"/>
                                            </f:facet>
                                            <a4j:commandLink action="#{ClienteControle.visualizarControleCreditoTreino}" oncomplete="#{ClienteControle.msgAlert}" reRender="formControleCreditoTreino">
                                                <h:outputText style="font-weight: bold"  styleClass="blue" 
                                                              value="#{controleCreditoTreino.tipoOperacaoCreditoTreinoEnum.descricao}"/>
                                                <f:setPropertyActionListener value="#{controleCreditoTreino}" target="#{ClienteControle.controleCreditoTreinoVO}"/>
                                            </a4j:commandLink>
                                        </rich:column>
                                        <rich:column >
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Usuário"/>
                                            </f:facet>
                                            <h:outputText style="font-weight: bold"  value="#{controleCreditoTreino.nomeUsuarioCurto}"/>
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Observação"/>
                                            </f:facet>

                                            <h:outputText style="font-weight: bold"  value="#{controleCreditoTreino.observacaoCurta}"/>

                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Aula Desmarcada"/>
                                            </f:facet>
                                            <h:outputText style="font-weight: bold"  value="#{controleCreditoTreino.aulaDesmarcadaCurta}"
                                                          rendered="#{controleCreditoTreino.tipoOperacaoCreditoTreinoEnum != 'MARCOU_AULA'}"/>
                                        </rich:column>
                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Aula Marcada"/>
                                            </f:facet>
                                            <h:outputText style="font-weight: bold"  value="#{controleCreditoTreino.aulaMarcadaCurta}"/>
                                        </rich:column>

                                        <rich:column headerClass=".rich-table-subheadercell colunaDireita">
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Quantidade"/>
                                            </f:facet>
                                            <h:outputText style="font-weight: bold"   value="#{controleCreditoTreino.quantidade}"/>
                                        </rich:column>
                                        <rich:column headerClass=".rich-table-subheadercell colunaDireita">
                                            <f:facet name="header">
                                                <h:outputText style="font-weight: bold" value="Saldo"/>
                                            </f:facet>
                                            <h:outputText style="font-weight: bold"   value="#{controleCreditoTreino.saldo}"/>
                                        </rich:column>

                                    </rich:dataTable>
                                    <rich:datascroller align="center" for="listaHistoricoCreditoTreino" maxPages="5"  id="scControleCreditoTreino"/>

                                </h:panelGroup>

                            </td>
                        </tr>
                    </table>
                </div>
                </h:panelGroup>


        </h:panelGroup>


        <div style="clear: both;">
            <table width="98%" border="0" align="left" cellpadding="0"
                   cellspacing="0" bgcolor="#e6e6e6"
                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;"><img
                                    src="images/arrow2.gif" width="16" height="16"
                                    style="vertical-align: middle; margin-right: 6px;">Histórico</p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                src="images/shim.gif"></div>
                    </td>
                </tr>
                <tr>
                    <td align="center" valign="top"><rich:dataTable
                            id="listaHistoricoContrato" width="100%" border="0"
                            cellspacing="0" cellpadding="2"
                            styleClass="textsmall"
                            columnClasses="1, centralizado, centralizado, centralizado, centralizado, centralizado"
                            value="#{ClienteControle.listaSelectItemHistoricoContrato}"
                            var="historicoContrato">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoContrato_descricao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoContrato.descricaoManual}"/>

                        </rich:column>


                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoContrato_dataLancamento}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoContrato.dataRegistro_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoContrato_dataInicioSituacao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoContrato.dataInicioSituacao_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoContrato_dataFinalSituacao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoContrato.dataFinalSituacao_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Legenda"/>
                            </f:facet>
                            <img src="./imagens/fundoGrid.png" width="70" height="20"
                                 border="0"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold; width:20px "
                                              value="Opções"/>
                            </f:facet>
                            <a4j:commandButton style="text-align: right"
                                               action="#{ClienteControle.selecionarDadosHistoricoContrato}"
                                               reRender="formHistoricoContrato,form:panelMensagemSuperior, form:panelMensagemInferior"
                                               oncomplete="Richfaces.showModalPanel('panelHistoricoContrato')"
                                               alt="Visualizar Detalhes Histórico"
                                               image="./imagens/botaoVisualizar.png"/>
                        </rich:column>
                    </rich:dataTable></td>
                </tr>
            </table>
        </div>

        <div style="clear: both;">
            <table width="98%" border="0" align="left" cellpadding="0"
                   cellspacing="0" bgcolor="#e6e6e6"
                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;"><img
                                    src="images/arrow2.gif" width="16" height="16"
                                    style="vertical-align: middle; margin-right: 6px;">Operações</p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                src="images/shim.gif"></div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top"><rich:dataTable
                            id="listaContratoOperacao" width="100%" border="0"
                            cellspacing="0" cellpadding="2" styleClass="textsmall"
                            columnClasses="1, centralizado, centralizado, centralizado, centralizado"
                            value="#{ClienteControle.listaSelectItemContratoOperacao}"
                            var="contratoOperacao">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_ContratoOperacao_tipoOperacao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{contratoOperacao.tipoOperacao_Apresentar}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoContrato_dataLancamento}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{contratoOperacao.dataOperacao_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoContrato_dataInicioSituacao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{contratoOperacao.dataInicioEfetivacaoOperacao_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoContrato_dataFinalSituacao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{contratoOperacao.dataFimEfetivacaoOperacao_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Opções"/>
                            </f:facet>
                            <a4j:commandButton id="btnVisualizarOper" style="text-align: right"
                                               action="#{ClienteControle.selecionarDadosContratoOperacao}"
                                               reRender="formContratoOperacao"
                                               oncomplete="Richfaces.showModalPanel('panelContratoOperacao')"
                                               alt="Visualizar Detalhes Operação"
                                               image="./imagens/botaoVisualizar.png"/>

                            <a4j:commandButton id="estornarOperacao" reRender="panelAutorizacaoFuncionalidade"
                                               action="#{ClienteControle.estornarAtestadoCarencia}"
                                               rendered="#{contratoOperacao.permiteEstorno}"
                                               style="margin-left: 10px;"
                                               image="./imagensCRM/botaoRemover.png"/>

                            <a4j:commandButton id="btnImprimirComprovanteOp"
                                               title="Comprovante de Operação"
                                               action="#{ClienteControle.imprimirComprovanteOperacao}"
                                               image="./imagens/imprimir.png"
                                               style="margin-left: 10px;"
                                               oncomplete="abrirPopupPDFImpressao('relatorio/#{ClienteControle.nomeArquivoComprovanteOperacao}','', 780, 595);"/>

                            <a4j:commandButton id="btnEnviarCancelamentoEmail"
                                               rendered="#{contratoOperacao.tipoOperacao == 'CA'}"
                                               title="Enviar e-mail com informações do cancelamento"
                                               action="#{ClienteControle.enviarEmailCancelamentoContrato}"
                                               image="./imagens/email.png"
                                               reRender="mdlMensagemGenerica"
                                               style="margin-left: 10px;"
                                               oncomplete="#{ClienteControle.modalMensagemGenerica}"/>

                        </rich:column>
                    </rich:dataTable></td>
                </tr>
            </table>
        </div>


        <div style="clear: both;"><a4j:commandButton
                action="#{ClienteControle.maisDetalhes}" reRender="richPanel"
                value="+ Ver mais">
        </a4j:commandButton></div>


        <rich:spacer height="10px" style="display:block;"/>
        <h:panelGroup id="maisDetalhes"
                      rendered="#{ClienteControle.apresentarMaisDetalhes}"
                      layout="block">
            <h:panelGrid width="100%">
                <table width="100%" border="0" align="left" cellpadding="0"
                       cellspacing="0" bgcolor="#e6e6e6"
                       style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                    <tr>
                        <td align="left" valign="top" style="padding-bottom: 5px;">
                            <div style="clear: both;" class="text">
                                <p style="margin-bottom: 6px;"><img
                                        src="images/arrow2.gif" width="16" height="16"
                                        style="vertical-align: middle; margin-right: 6px;">Histórico
                                    de Compras</p>
                            </div>
                            <div class="sep" style="margin: 4px 0 10px 0;"><img
                                    src="images/shim.gif"></div>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top">
                            <rich:dataTable
                                    id="listaHistoricoComprasContrato" width="100%"
                                    border="0"
                                    cellspacing="0" cellpadding="0" styleClass="textsmall"
                                    columnClasses="centralizado, centralizado, centralizado"
                                    value="#{ClienteControle.clienteContratoVO.listaHistoricoProduto}"
                                    var="historicoCompras">
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_contrato}"/>
                            </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoCompras.contrato.codigo}"/>
                            </rich:column>
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="Código"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.codigo}"/>
                            </rich:column>
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_descricao}"/>
                            </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoCompras.descricao}"/>
                            </rich:column>
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}"/>
                            </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoCompras.dataLancamentoComHora_Apresentar}"/>
                            </rich:column>
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_quantidade}"/>
                            </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoCompras.quantidade}"/>
                            </rich:column>
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_unitario}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold"
                                          styleClass="blue"
                                          value="#{historicoCompras.precoUnitario}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            </rich:column>
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_desconto}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold"
                                          styleClass="blue"
                                          value="#{historicoCompras.valorDesconto}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            </rich:column>
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_totalFinal}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold"
                                          styleClass="blue"
                                          value="#{historicoCompras.totalFinal}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            </rich:column>
                            <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_situacao}"/>
                            </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{historicoCompras.mudarCorSituacaoEmAberto}"
                                              value="#{historicoCompras.situacao_Apresentar}"/>
                            </rich:column>
                            </rich:dataTable>
                    </tr>
                    <tr>
                        <td align="left" valign="top" style="padding-bottom: 5px;">
                            <div style="clear: both;" class="text">
                                <p style="margin-bottom: 6px;"><img
                                        src="images/arrow2.gif" width="16" height="16"
                                        style="vertical-align: middle; margin-right: 6px;">Histórico
                                    de Parcelas Geradas</p>
                            </div>
                            <div class="sep" style="margin: 4px 0 10px 0;"><img
                                    src="images/shim.gif"></div>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top"><rich:dataTable
                                id="listaHistoricoParcelaContrato" width="100%"
                                border="0"
                                cellspacing="0" cellpadding="0" styleClass="textsmall"
                                columnClasses="centralizado, centralizado, centralizado"
                                value="#{ClienteControle.clienteContratoVO.listaParcelas}"
                                var="historicoParcela">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoParcela.contrato.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Código"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoParcela.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoParcela.descricao}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_DataLancada}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoParcela.dataRegistro_Apresentar}">
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoParcela.dataVencimento_Apresentar}">
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoParcela.valorParcela}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{historicoParcela.mudarCorSituacaoEmAberto}"
                                              value="#{historicoParcela.situacao_Apresentar}"/>
                            </rich:column>
                        </rich:dataTable></td>
                    </tr>
                    <tr>
                        <td align="left" valign="top" style="padding-bottom: 5px;">
                            <div style="clear: both;" class="text">
                                <p style="margin-bottom: 6px;"><img
                                        src="images/arrow2.gif" width="16" height="16"
                                        style="vertical-align: middle; margin-right: 6px;">Histórico
                                    de Pagamentos Efetuados</p>
                            </div>
                            <div class="sep" style="margin: 4px 0 10px 0;"><img
                                    src="images/shim.gif"></div>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top"><rich:dataTable
                                id="listaHistoricoPagamentosContrato" width="100%"
                                border="0"
                                cellspacing="0" cellpadding="0" styleClass="textsmall"
                                columnClasses="centralizado, centralizado, centralizado"
                                value="#{ClienteControle.clienteContratoVO.listaHistoricoPagamento}"
                                var="historicoPagamentos">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_codigo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoPagamentos.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_nrRecibo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoPagamentos.reciboPagamento.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoPagamentos.nomePagador}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoPagamentos.dataLancamento_Apresentar}">
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar}"/>
                                <h:outputText rendered="#{historicoPagamentos.credito}" title="Crédito Conta Corrente do Aluno" style="font-weight: bold" styleClass="blue"
                                              value="#{historicoPagamentos.creditoApresentar}" />
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_valor}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoPagamentos.valorTotal}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_recibo}"/>
                                </f:facet>
                                <a4j:commandButton id="imprimir"
                                                   value="#{msg_bt.btn_ImprimirReciboPagamento}"
                                                   image="imagens/imprimir.png"
                                                   actionListener="#{ReciboControle.prepareRecibo}"
                                                   action="#{ReciboControle.imprimirReciboPDF}"
                                                   oncomplete="abrirPopupPDFImpressao('relatorio/#{ReciboControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                    <f:attribute name="reciboPagamentoVO"
                                                 value="#{historicoPagamentos.reciboPagamento}"/>
                                </a4j:commandButton>
                                &nbsp;
                                <a4j:commandButton id="enviarReciboC"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, modalEnviarContratoEmail"
                                                   value="Enviar"
                                                   title="Enviar recibo"
                                                   oncomplete="#{ReciboControle.mensagemNotificar}#{ReciboControle.msgAlert}"
                                                   image="./imagens/email.png"
                                                   actionListener="#{ReciboControle.prepararModalEnvioReciboPorEmail}">
                                    <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                    <f:attribute name="pessoaVO" value="#{ClienteControle.clienteVO.pessoa}"/>
                                </a4j:commandButton>
                                &nbsp;
                                <a4j:commandButton id="editarReciboC"
                                                   actionListener="#{EdicaoPagamentoControle.prepareRecibo}"
                                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao"
                                                   image="./images/bnt_editar.jpg"
                                                   oncomplete="#{EdicaoPagamentoControle.msgAlert}#{EdicaoPagamentoControle.mensagemNotificar}">
                                    <f:attribute name="pagamentoVO" value="#{historicoPagamentos}"/>
                                    <f:attribute name="tipoEdicao"
                                                 value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento}"/>
                                </a4j:commandButton>
                                &nbsp;
                                <a4j:commandButton id="gerarNotaFiscal" reRender="panelAutorizacaoFuncionalidade"
                                                   value="NFSe"
                                                   onclick="#{historicoPagamentos.reciboPagamento.onclickNFSe}"
                                                   actionListener="#{ReciboControle.prepareRecibo}"
                                                   image="./images/bot-nfse-20.png"
                                                   action="#{ReciboControle.emitirNFSe}"
                                                   rendered="#{ClienteControle.clienteVO.empresa.apresentarBotoesNFSe}"
                                                   oncomplete="#{ReciboControle.onComplete}">
                                    <f:attribute name="reciboPagamentoVO"
                                                 value="#{historicoPagamentos.reciboPagamento}"/>
                                    <f:attribute name="maisDetalhes" value="true"/>
                                </a4j:commandButton>
                                &nbsp;
                                <a4j:commandButton id="visualizarReciboC"
                                                   title="Visualizar Recibo"
                                                   value="#{msg_bt.btn_ImprimirReciboPagamento}"
                                                   image="./imagens/botaoVisualizar.png"
                                                   actionListener="#{EstornoReciboControle.preparaRecibo}"
                                                   action="#{EstornoReciboControle.preencherRecibo}"
                                                   oncomplete="abrirPopup('estornoReciboForm.jsp', 'EstornoRecibo', 1000, 650);">
                                    <f:attribute name="reciboPagamentoVO"
                                                 value="#{historicoPagamentos.reciboPagamento}"/>
                                </a4j:commandButton>
                            </rich:column>
                        </rich:dataTable></td>
                    </tr>
                    <tr>
                        <td align="left" valign="top" style="padding-bottom: 5px;">
                            <div style="clear: both;" class="text">
                                <p style="margin-bottom: 6px;"><img
                                        src="images/arrow2.gif" width="16" height="16"
                                        style="vertical-align: middle; margin-right: 6px;">Histórico
                                    de Cheques</p>
                            </div>
                            <div class="sep" style="margin: 4px 0 10px 0;"><img
                                    src="images/shim.gif"></div>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top"><rich:dataTable
                                id="listaHistoricoCheques" width="100%" border="0"
                                cellspacing="0" cellpadding="0" styleClass="textsmall"
                                columnClasses="centralizado, centralizado, centralizado"
                                value="#{ClienteControle.listaCheques}"
                                var="historicoCheques">
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_codigo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoCheques.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_banco}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoCheques.banco.nome}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_agencia}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoCheques.agencia}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_conta}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoCheques.conta}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_numeroCheque}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoCheques.numero}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_nomeNoCheque}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoCheques.nomeNoCheque}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_data}"/>
                                </f:facet>
                                <a4j:outputPanel
                                        title="Data de compensação original: #{historicoCheques.dataOriginalApresentar}">
                                    <h:outputText style="font-weight: bold"
                                                  styleClass="blue"

                                                  value="#{historicoCheques.dataCompensacao}">
                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                    </h:outputText>
                                </a4j:outputPanel>

                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_situacao}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue"
                                              value="#{historicoCheques.situacao_Apresentar}">
                                </h:outputText>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_Cheque_valor}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="blue" value="#{historicoCheques.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>
                        </rich:dataTable></td>
                    </tr>

                </table>
            </h:panelGrid>
        </h:panelGroup>
    </rich:tab>

    <rich:tab label="#{msg_aplic.prt_TipoAcessoCliente_tituloForm}" oncomplete="updateAbaState();"
              action="#{ClienteControle.montarListaPeriodoAcessoCliente}" id="abaPeriodoAcessoCliente">
        <f:attribute name="codigoCliente"
                     value="#{ClienteControle.clienteVO.codigo}"/>
        <div style="clear: both;">

            <table width="98%" border="0" align="left" cellpadding="0"
                   cellspacing="0" bgcolor="#e6e6e6"
                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;"><img
                                    src="images/arrow2.gif" width="16" height="16"
                                    style="vertical-align: middle; margin-right: 6px;"><h:outputText
                                    style="font-weight: bold"
                                    value="#{msg_aplic.prt_AcessoCliente_TipoAcessoAutorizado}"/></p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                src="images/shim.gif"></div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top"><rich:dataTable
                            id="listaPeriodoAcessoCliente" width="100%" border="0"
                            rows="5" cellspacing="0" cellpadding="2"
                            styleClass="textsmall"
                            columnClasses="centralizado, centralizado, centralizado"
                            value="#{ClienteControle.listaSelectItemPeriodoAcessoCliente}"
                            var="periodoAcessoCliente">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_PeriodoAcessoCliente_contrato}"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText style="font-weight: bold" styleClass="blue"
                                              value="#{periodoAcessoCliente.contrato}"/>
                                <rich:spacer width="5px"/>
                                <h:outputText
                                        rendered="#{periodoAcessoCliente.contratoBaseadoRenovacao != 0}"
                                        style="font-weight: bold" styleClass="blue"
                                        value=" - Renovação Referente ao #{periodoAcessoCliente.contratoBaseadoRenovacao} "/>
                            </h:panelGroup>

                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_PeriodoAcessoCliente_dataInicioAcesso}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{periodoAcessoCliente.dataInicioAcesso_Apresentar}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_PeriodoAcessoCliente_dataFinalAcesso}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="red"
                                          value="#{periodoAcessoCliente.dataFinalAcesso_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_PeriodoAcessoCliente_tipoAcesso}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{periodoAcessoCliente.tipoAcesso_Apresentar}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold;"
                                              value="#{msg_aplic.prt_PeriodoAcessoCliente_legenda}"/>
                            </f:facet>
                            <h:graphicImage
                                    value="#{periodoAcessoCliente.legenda_Apresentar}"/>
                        </rich:column>
                    </rich:dataTable> <rich:datascroller align="center"
                                                         for="listaPeriodoAcessoCliente" maxPages="10"
                                                         id="scResultadoPerfilAcesso"/></td>
                </tr>

                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;"><img
                                    src="images/arrow2.gif" width="16" height="16"
                                    style="vertical-align: middle; margin-right: 6px;"><h:outputText
                                    style="font-weight: bold"
                                    value="#{msg_aplic.prt_AcessoCliente_UltimosAcessos}"/></p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                src="images/shim.gif"></div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top"><rich:dataTable
                            id="listaUltimosAcessosCliente" width="100%" border="0"
                            cellspacing="0" cellpadding="2"
                            styleClass="textsmall"
                            columnClasses="centralizado, centralizado, centralizado"
                            value="#{ClienteControle.listaUltimosAcessos}"
                            var="ultimoAcesso">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_AcessoCliente_codigo}"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText style="font-weight: bold" styleClass="blue"
                                              value="#{ultimoAcesso.codigo}"/>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_AcessoCliente_sentido}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{ultimoAcesso.sentido}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_AcessoCliente_localAcesso}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{ultimoAcesso.localAcesso.descricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold;"
                                              value="#{msg_aplic.prt_AcessoCliente_coletor}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{ultimoAcesso.coletor.descricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold;"
                                              value="#{msg_aplic.prt_AcessoCliente_dataHoraEntrada}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{ultimoAcesso.dataHoraEntrada}">
                                <f:convertDateTime type="date" dateStyle="short"
                                                   locale="pt" timeZone="America/Sao_Paulo"
                                                   pattern="dd/MM/yyyy HH:mm"/>
                            </h:outputText>
                            -
                            <h:outputText
                                    style="font-weight: bold" styleClass="blue"
                                    value="#{ultimoAcesso.nomeDiaSemanaAcesso}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold;"
                                              value="#{msg_aplic.prt_AcessoCliente_dataHoraSaida}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{ultimoAcesso.dataHoraSaida}">
                                <f:convertDateTime type="date" dateStyle="short"
                                                   locale="pt" timeZone="America/Sao_Paulo"
                                                   pattern="dd/MM/yyyy HH:mm"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold;"
                                              value="#{msg_aplic.prt_AcessoCliente_meioIdentificacaoEntrada}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{ultimoAcesso.meioIdentificacaoEntrada.descricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold;"
                                              value="#{msg_aplic.prt_AcessoCliente_bloqueio}"/>
                            </f:facet>
                            <h:outputText rendered="#{ultimoAcesso.situacao.id!= 'RV_LIBACESSOAUTORIZADO'}"
                                          style="font-weight: bold" styleClass="blue"
                                          value="#{ultimoAcesso.situacao.descricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold;"
                                              value="#{msg_aplic.prt_AcessoCliente_usuarioLiberou}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{ultimoAcesso.usuario.nome}"/>
                        </rich:column>
                    </rich:dataTable>
                        <h:panelGrid columns="1" columnClasses="colunaCentralizada,colunaCentralizada"
                                     width="100%">
                            <h:panelGroup>
                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                    <rich:inputNumberSpinner inputSize="5" styleClass="form"
                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                             value="#{ClienteControle.nrRegistrosUltimosAcessos}">
                                        <a4j:support event="onchange"
                                                     action="#{ClienteControle.montarListaPeriodoAcessoClienteComLimiteDeterminadoPeloUsuario}"
                                                     reRender="listaUltimosAcessosCliente"/>
                                    </rich:inputNumberSpinner>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>


        </div>
    </rich:tab>

    <!-- tempo: 30 ms -->
    <rich:tab label="Histórico de Compras" action="#{ClienteControle.montarListaHistoricoCompras}"
              oncomplete="updateAbaState();" id="abaHistoricoCompras">
        <f:attribute name="codigoCliente"
                     value="#{ClienteControle.clienteVO.codigo}"/>
        <div style="clear: both;">

            <table width="98%" border="0" align="left" cellpadding="0"
                   cellspacing="0" bgcolor="#e6e6e6"
                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;"><img
                                    src="images/arrow2.gif" width="16" height="16"
                                    style="vertical-align: middle; margin-right: 6px;">
                                <h:outputText style="font-weight: bold;"
                                              value="#{msg_aplic.prt_HistóricoCompras}"/></p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                src="images/shim.gif"></div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top"><rich:dataTable
                            id="listaHistoricoCompras" width="100%" border="0"
                            rows="#{ClienteControle.nrPaginaMovProduto}"
                            cellspacing="0" cellpadding="0" styleClass="textsmall"
                            columnClasses="centralizado, centralizado, centralizado"
                            value="#{ClienteControle.clienteVO.listaHistoricoProduto}"
                            var="historicoCompras">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_contrato}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.contrato.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="Código"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_descricao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.descricao}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.dataLancamentoComHora_Apresentar}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_quantidade}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.quantidade}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_unitario}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.precoUnitario}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>

                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_desconto}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.valorDesconto}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_totalFinal}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.totalFinal}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column rendered="#{ClienteControle.mostrarColunaCupomDesconto}">
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="Cupom Desconto"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.numeroCupomDesconto}">
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_situacao}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold"
                                          styleClass="#{historicoCompras.mudarCorSituacaoEmAberto}"
                                          value="#{historicoCompras.situacao_Apresentar}"/>
                        </rich:column>
                        <rich:column
                                rendered="#{ClienteControle.apresentarValorParcialmentePago}">
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Parc. Pago"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoCompras.valorParcialmentePagoApresentar}"/>
                        </rich:column>
                        <rich:column width="15%"
                                     rendered="#{ClienteControle.apresentarOpcaoEstornoProduto || ClienteControle.apresentarOpcaoCancelarSessao}">
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Opções"/>
                            </f:facet>
                            <a4j:commandButton id="estornoMovProduto"
                                               rendered="#{historicoCompras.aprensetarBotaoEstorno}"
                                               image="./imagens/botaoEstornaProduto.png"
                                               action="#{EstornoMovProdutoControle.novo}"
                                               value="Estornar"
                                               oncomplete="abrirPopup('estornoMovProdutoForm.jsp', 'Produto', 950, 600);"/>
                            <rich:spacer width="5px"/>
                            <a4j:commandButton id="cancelarSessao"
                                               rendered="#{historicoCompras.apresentarBotaoCancelarSessao}"
                                               image="./imagens/btnCancelar.png"
                                               action="#{CancelamentoSessaoControle.novo}"
                                               value="Cancelar"
                                               oncomplete="abrirPopup('cancelamentoSessaoForm.jsp', 'Produto', 800, 680);"/>
                            <rich:spacer width="5px"/>
                            <a4j:commandButton id="btnImprimirReciboProdutoCan"
                                               action="#{ClienteControle.imprimirReciboDevolucaoMovProduto}"
                                               title="Imprimir Recibo Devolução"
                                               rendered="#{historicoCompras.possuiReciboDevolucao}"
                                               image="/imagens/imprimir.png"
                                               oncomplete="abrirPopupPDFImpressao('relatorio/#{ClienteControle.nomeArquivoReciboDevolucao}','', 780, 595);"/>
                            <a4j:commandButton rendered="#{historicoCompras.apresentarBotaoImprimirContrato}"
                                               id="btnContratoProduto"
                                               title="Imprimir Contrato de  Prestação de Serviço"
                                               value="Imprimir Contrato de  Prestação de Serviço"
                                               image="./imagens/imprimir.png"
                                               action="#{ClienteControle.prepararVendaAvulsa}"
                                               reRender="form:panelMensagemSuperior,form:panelMensagemInferior,panelIncludeContratoPrestacao"
                                               oncomplete="#{ClienteControle.mostrarRichModalPanelContratoPrestacaoServico}"/>
                        </rich:column>
                    </rich:dataTable> <h:panelGrid columns="1" columnClasses="colunaCentralizada,colunaCentralizada"
                                                   width="100%">
                        <h:panelGroup>
                            <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                <rich:datascroller align="center"
                                                   for="listaHistoricoCompras" maxPages="100"
                                                   id="scResultadoHistoricoProdutos"/>
                                <rich:inputNumberSpinner id="numRegistrosCompras" inputSize="5" styleClass="form"
                                                         enableManualInput="true" minValue="1" maxValue="100"
                                                         value="#{ClienteControle.nrPaginaMovProduto}">
                                    <a4j:support event="onchange"
                                                 focus="scResultadoHistoricoProdutos"
                                                 reRender="listaHistoricoCompras,scResultadoHistoricoProdutos"/>
                                </rich:inputNumberSpinner>
                            </h:panelGrid>
                        </h:panelGroup>
                    </h:panelGrid></td>
                </tr>
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;"><img
                                    src="images/arrow2.gif" width="16" height="16"
                                    style="vertical-align: middle; margin-right: 6px;"> <h:outputText
                                    style="font-weight: bold;"
                                    value="#{msg_aplic.prt_HistoricoParcelasGeradas}"/></p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                src="images/shim.gif"></div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top">

                        <a4j:commandLink style="font-weight:bold; float: right;"
                                         reRender="modalAlterarVencimento"
                                         id="btnAbrirModalAlterarVencimento"
                                         title="Alterar data de Vencimento das Parcelas"
                                         value="Alterar data de Vencimento das Parcelas"
                                         oncomplete="#{rich:component('modalAlterarVencimento')}.show();"
                                         actionListener="#{AlterarVencimentoParcelasControle.preparar}">
                            <f:attribute name="cliente" value="#{ClienteControle.clienteVO}"/>
                        </a4j:commandLink>

                        <rich:dataTable id="listaHistoricoParcela" width="100%" border="0"
                                        rows="#{ClienteControle.nrPaginaMovParcela}"
                                        cellspacing="0" cellpadding="0" styleClass="textsmall"
                                        columnClasses="centralizado, centralizado, centralizado"
                                        value="#{ClienteControle.clienteVO.listaParcelas}"
                                        var="historicoParcela">

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="blue"
                                              value="#{historicoParcela.contrato.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Código"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="blue"
                                              value="#{historicoParcela.codigo}"/>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="blue"
                                              value="#{historicoParcela.descricao}"/>
                            </rich:column>

                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_DataLancada}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="blue"
                                              value="#{historicoParcela.dataRegistro_Apresentar}">

                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="blue"
                                              value="#{historicoParcela.dataVencimento_Apresentar}">

                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="blue"
                                              value="#{historicoParcela.valorParcela}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </rich:column>
                            <rich:column>
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}"/>
                                </f:facet>
                                <h:outputText style="font-weight: bold"
                                              styleClass="#{historicoParcela.mudarCorSituacaoEmAberto}"
                                              value="#{historicoParcela.situacao_Apresentar}"/>
                            </rich:column>
                        </rich:dataTable> <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                                                       width="100%">
                        <h:panelGroup>
                            <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                <rich:datascroller for="listaHistoricoParcela"
                                                   maxPages="100" id="scResultadoHistoricoParcela"/>
                                <rich:inputNumberSpinner id="numRegistrosParcelas" inputSize="5" styleClass="form"
                                                         enableManualInput="true" minValue="1" maxValue="100"
                                                         value="#{ClienteControle.nrPaginaMovParcela}">
                                    <a4j:support event="onchange"
                                                 focus="scResultadoHistoricoParcela"
                                                 reRender="listaHistoricoParcela,scResultadoHistoricoParcela"/>
                                </rich:inputNumberSpinner>
                            </h:panelGrid>

                        </h:panelGroup>
                    </h:panelGrid></td>
                </tr>
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;"><img
                                    src="images/arrow2.gif" width="16" height="16"
                                    style="vertical-align: middle; margin-right: 6px;"><h:outputText
                                    style="font-weight: bold;"
                                    value="#{msg_aplic.prt_HistoricoPagamentosEfetuados}"/></p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                src="images/shim.gif"></div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top"><rich:dataTable
                            id="listaHistoricoPagamentos" width="100%" border="0"
                            rows="#{ClienteControle.nrPaginaMovPagamento}"
                            cellspacing="0" cellpadding="0" styleClass="textsmall"
                            columnClasses="centralizado, centralizado, centralizado"
                            value="#{ClienteControle.clienteVO.listaHistoricoPagamento}"
                            var="historicoPagamentos">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_codigo}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoPagamentos.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_nrRecibo}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoPagamentos.reciboPagamento.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoPagamentos.nomePagador}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoPagamentos.dataLancamento_Apresentar}">
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar}"/>
                             <h:outputText rendered="#{historicoPagamentos.credito}" title="Crédito Conta Corrente do Aluno" style="font-weight: bold" styleClass="blue"
                                              value="#{historicoPagamentos.creditoApresentar}" />
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_valor}"/>
                            </f:facet>
                            <h:outputText style="font-weight: bold" styleClass="blue"
                                          value="#{historicoPagamentos.valorTotal}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold"
                                              value="#{msg_aplic.prt_HistoricoComprasCliente_recibo}"/>
                            </f:facet>
                            <a4j:commandButton id="imprimir"
                                               title="Imprimir recibo"
                                               value="#{msg_bt.btn_ImprimirReciboPagamento}"
                                               image="imagens/imprimir.png"
                                               actionListener="#{ReciboControle.prepareRecibo}"
                                               action="#{ReciboControle.imprimirReciboPDF}"
                                               oncomplete="abrirPopupPDFImpressao('relatorio/#{ReciboControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                            </a4j:commandButton>
                            &nbsp;
                            <a4j:commandButton id="enviarRecibo"
                                               reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, modalEnviarContratoEmail"
                                               value="Enviar"
                                               title="Enviar recibo"
                                               oncomplete="#{ReciboControle.mensagemNotificar}#{ReciboControle.msgAlert}"
                                               image="./imagens/email.png"
                                               actionListener="#{ReciboControle.prepararModalEnvioReciboPorEmail}">
                                <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                <f:attribute name="pessoaVO" value="#{ClienteControle.clienteVO.pessoa}"/>
                            </a4j:commandButton>
                            &nbsp;
                            <a4j:commandButton id="editarRecibo"
                                               actionListener="#{EdicaoPagamentoControle.prepareRecibo}"
                                               title="Editar Recibo"
                                               reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao"
                                               image="./images/bnt_editar.jpg"
                                               oncomplete="#{EdicaoPagamentoControle.msgAlert}#{EdicaoPagamentoControle.mensagemNotificar}">
                                <f:attribute name="pagamentoVO" value="#{historicoPagamentos}"/>
                                <f:attribute name="tipoEdicao"
                                             value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento}"/>
                            </a4j:commandButton>
                            &nbsp;
                            <a4j:commandButton id="gerarNotaFiscal2" reRender="panelAutorizacaoFuncionalidade"
                                               value="NFSe"
                                               image="./images/bot-nfse-20.png"
                                               onclick="#{historicoPagamentos.reciboPagamento.onclickNFSe}"
                                               actionListener="#{ReciboControle.prepareRecibo}"
                                               action="#{ReciboControle.emitirNFSe}"
                                               rendered="#{ClienteControle.clienteVO.empresa.apresentarBotoesFaturamentoNFSe && LoginControle.permissaoAcessoMenuVO.apresentarNfseRecibo}"
                                               oncomplete="#{ReciboControle.onComplete}">
                                <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                <f:attribute name="maisDetalhes" value="false"/>
                            </a4j:commandButton>
                            &nbsp;
                            <a4j:commandButton id="visualizarRecibo"
                                               title="Visualizar Recibo"
                                               value="#{msg_bt.btn_ImprimirReciboPagamento}"
                                               image="./imagens/botaoVisualizar.png"
                                               actionListener="#{EstornoReciboControle.preparaRecibo}"
                                               action="#{EstornoReciboControle.preencherRecibo}"
                                               oncomplete="abrirPopup('estornoReciboForm.jsp', 'EstornoRecibo', 1000, 650);">
                                <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                            </a4j:commandButton>

                        </rich:column>
                    </rich:dataTable>
                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                            <h:panelGroup>
                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                    <rich:datascroller align="center"
                                                       for="listaHistoricoPagamentos" maxPages="10"
                                                       id="scResultadoHistoricoPgto"/>
                                    <rich:inputNumberSpinner inputSize="5" styleClass="form"
                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                             value="#{ClienteControle.nrPaginaMovPagamento}">
                                        <a4j:support event="onchange"
                                                     focus="scResultadoHistoricoPgto"
                                                     reRender="listaHistoricoPagamentos,scResultadoHistoricoPgto"/>
                                    </rich:inputNumberSpinner>
                                </h:panelGrid>

                            </h:panelGroup>
                        </h:panelGrid> <h:panelGroup>

                        </h:panelGroup></td>
                </tr>
            </table>
        </div>
    </rich:tab>

    <rich:tab label="Histórico Cobrança" action="#{ClienteControle.preencherItensCobranca}" id="abaHistoricoCobranca">
        <div style="clear: both;">
            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" bgcolor="#e6e6e6"
                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;">
                                <img src="images/arrow2.gif" width="16" height="16"
                                     style="vertical-align: middle; margin-right: 6px;">
                                <h:outputText style="font-weight: bold;" value="#{msg_aplic.prt_HistoricoCobranca}"/>
                            </p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;">
                            <img src="images/shim.gif">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top">
                        <jsp:include page="includes/remessas/include_itens_remessaestorno.jsp" flush="true"/>
                        <jsp:include page="includes/remessas/include_itens_remessa_boleto.jsp" flush="true"/>
                        <jsp:include page="includes/transacoes/include_table_transacoes.jsp" flush="true"/>
                    </td>
                </tr>
            </table>
        </div>
    </rich:tab>

    <rich:tab label="Observação" id="abaObservacaoCliente"
              action="#{ClienteControle.editarClienteMensagemObservacaoGeral}">
        <div style="clear: both;">
            <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" bgcolor="#e6e6e6"
                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                <tr>
                    <td align="left" valign="top" style="padding-bottom: 5px;">
                        <div style="clear: both;" class="text">
                            <p style="margin-bottom: 6px;">
                                <img src="images/arrow2.gif" width="16" height="16"
                                     style="vertical-align: middle; margin-right: 6px;">
                                <h:outputText style="font-weight: bold;" value="#{msg_aplic.prt_Observacao}"/>
                            </p>
                        </div>
                        <div class="sep" style="margin: 4px 0 10px 0;">
                            <img src="images/shim.gif">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top">
                        <h:panelGrid id="panelObservacao" columns="1" width="100%"
                                     styleClass="tabMensagens">
                            <%--<h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">--%>
                            <%--<f:facet name="header">--%>
                            <%--<h:outputText value="#{msg_aplic.prt_ClienteMensagem_tituloForm}" />--%>
                            <%--</f:facet>--%>
                            <%--</h:panelGrid>--%>
                            <h:panelGrid columns="2" columnClasses="w30,w70" width="100%">

                                <h:panelGroup layout="block" style="max-height: 400px; height: 100%;">
                                    <rich:editor id="imputMensagemObservacaoCliente" height="399"
                                                 configuration="editorpropriedades" viewMode="visual" theme="advanced"
                                                 value="#{ClienteControle.mensagemObservacaoCliente.mensagem}"
                                                 readonly="#{!ClienteControle.permitiAlteracaoMensagemObservacao}">
                                        <f:param name="width" value="100%"/>
                                    </rich:editor>
                                </h:panelGroup>

                                <h:panelGroup layout="block" style="overflow-y: auto; max-height: 407px; height: 100%;">
                                    <rich:dataTable width="100%" headerClass="subordinado"
                                                    rowClasses="linhaImpar, linhaPar"
                                                    value="#{ClienteControle.clienteObservacoes}"
                                                    var="observacao">

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText value="Código"/>
                                            </f:facet>
                                            <h:outputText value="#{observacao.codigo}"/>
                                        </rich:column>

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText value="Usuário"/>
                                            </f:facet>
                                            <h:outputText value="#{observacao.usuarioVO.nomeAbreviado}"/>
                                        </rich:column>

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText value="Data Cadastro"/>
                                            </f:facet>
                                            <h:outputText value="#{observacao.dataCadastro}"/>
                                        </rich:column>

                                        <rich:column>
                                            <f:facet name="header">
                                                <h:outputText value="Observação"/>
                                            </f:facet>
                                            <h:outputText value="#{observacao.observacao}" escape="false"/>
                                        </rich:column>

                                    </rich:dataTable>
                                </h:panelGroup>
                            </h:panelGrid>

                            <h:panelGroup layout="block" style="margin-top: 5px; width: 30%; text-align: center;">
                                <%--<a4j:commandLink--%>
                                <%--oncomplete="abrirPopup('clienteObservacaoCons.jsp', 'Observações', 820, 630)"--%>
                                <%--styleClass="botoes nvoBt">--%>
                                <%--<i class="fa-icon-search"></i>&nbsp Consultar--%>
                                <%--</a4j:commandLink>--%>

                                <%--<rich:spacer width="10"/>--%>
                                <a4j:commandLink id="autorizarObservacaoCliente2"
                                                 styleClass="botoes nvoBt"
                                                 action="#{ClienteControle.permisaoClienteMensagemObservacaoGeral}"
                                                 rendered="#{ClienteControle.permitiAlteracaoMensagemObservacao}"
                                                 reRender="panelObservacao">
                                    <i class="fa-icon-plus"></i> &nbsp Incluir
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                                <h:panelGrid columns="1" width="100%">
                                    <f:verbatim>
                                        <h:outputText value=" "/>
                                    </f:verbatim>
                                </h:panelGrid>
                                <a4j:commandButton rendered="#{ClienteControle.sucesso}" image="./imagens/sucesso.png"/>
                                <a4j:commandButton rendered="#{ClienteControle.erro}" image="./imagens/erro.png"/>
                                <h:panelGrid columns="1" width="100%">
                                    <h:outputText styleClass="mensagem" value="#{ClienteControle.mensagem}"/>
                                    <h:outputText styleClass="mensagemDetalhada"
                                                  value="#{ClienteControle.mensagemDetalhada}"/>
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
        </div>
    </rich:tab>

</rich:tabPanel>

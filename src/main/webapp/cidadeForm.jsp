<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="#{msg_aplic.prt_Cidade_tituloForm}" /></title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Cidade_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros_Auxiliares:Cidade"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">

            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>

            <h:commandLink action="#{CidadeControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_codigo}" />
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{CidadeControle.cidadeVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada" />
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_nome}" />
                    <h:panelGroup>
                        <h:inputText id="nome" size="40" maxlength="40" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{CidadeControle.cidadeVO.nome}" />
                        <h:message for="nome" styleClass="mensagemDetalhada" />
                    </h:panelGroup>
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_pais}" />
                    <h:panelGroup>
                        <h:selectOneMenu id="pais" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{CidadeControle.cidadeVO.pais.codigo}">
                            <a4j:support event="onchange" reRender="estado" action="#{CidadeControle.montarListaSelectItemEstado}" />
                            <f:selectItems value="#{CidadeControle.listaSelectItemPais}" />
                        </h:selectOneMenu>
                        <h:panelGroup>
                            <rich:spacer width="5px" />
                            <a4j:commandButton id="atualizar_pais" action="#{CidadeControle.montarListaSelectItemPais}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:pais" />
                            <h:message for="pais" styleClass="mensagemDetalhada" />
                            <rich:spacer width="5px" />
                            <a4j:commandButton id="consultaDadosPais" alt="Cadastrar País" oncomplete="abrirPopup('paisForm.jsp', 'Pais', 780, 595);" action="#{PaisControle.reset}" image="./images/icon_add.gif" />
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_estado}" />
                    <h:panelGroup>
                        <h:selectOneMenu id="estado" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{CidadeControle.cidadeVO.estado.codigo}">
                            <f:selectItems value="#{CidadeControle.listaSelectItemEstado}" />
                            <a4j:support event="onchange" action="#{CidadeControle.atualizarEstado}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_estado" action="#{CidadeControle.montarListaSelectItemEstado}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:estado" />
                        <h:message for="estado" styleClass="mensagemDetalhada" />
                    </h:panelGroup>
                    
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_codMun}" />
                    <h:panelGroup>
                        <h:inputText id="codmunicipio" size="40" maxlength="40" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{CidadeControle.cidadeVO.codigoMunicipio}" />
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{CidadeControle.usuarioLogado.usuarioPactoSolucoes}"
                                  value="Homologada eNotas:"/>
                    <h:selectBooleanCheckbox id="homologada"
                                             rendered="#{CidadeControle.usuarioLogado.usuarioPactoSolucoes}"
                                             value="#{CidadeControle.cidadeVO.homologada}"/>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">

                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{CidadeControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec" />
                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="salvar" action="#{CidadeControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt" />
                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="excluir" action="#{CidadeControle.confirmarExcluir}" oncomplete="#{CidadeControle.msgAlert}" reRender="form,mdlMensagemGenerica" value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo" />
                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="consultar" immediate="true" action="#{CidadeControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec" />
                                <rich:spacer width="15px"/>
                                <a4j:commandLink  action="#{CidadeControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                                    oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                    title="Visualizar Log"
                                                  style="display: inline-block; padding: 8px 15px; margin-left: -6px;"
                                                    styleClass="botoes nvoBt btSec">
                                    <i style="text-decoration: none" class="fa-icon-list"/>
                                </a4j:commandLink>

                            </h:panelGroup>

                        </c:if>

                        <c:if test="${modulo eq 'centralEventos'}">
                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{CidadeControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec" />
                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="salvar" action="#{CidadeControle.gravarCE}"
                                                 value="#{msg_bt.btn_gravar}"
                                                 alt="#{msg.msg_gravar_dados}"
                                                 accesskey="2" styleClass="botoes nvoBt"
                                                 actionListener="#{CidadeControle.autorizacao}">
                                    <!-- entidade.cidade  -->
                                    <f:attribute name="entidade" value="116"/>
                                    <!-- operacao.gravar  -->
                                    <f:attribute name="operacao" value="G"/>
                                </a4j:commandButton>


                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="excluir"
                                                 action="#{CidadeControle.excluirCE}"
                                                 value="#{msg_bt.btn_excluir}"
                                                 alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                                                 actionListener="#{CidadeControle.autorizacao}">
                                    <!-- entidade.cidade  -->
                                    <f:attribute name="entidade" value="116"/>
                                    <!-- operacao.gravar  -->
                                    <f:attribute name="operacao" value="E"/>
                                </a4j:commandButton>


                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="consultar" immediate="true" action="#{CidadeControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_consultar}"
                                                 alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"
                                                 actionListener="#{CidadeControle.autorizacao}">
                                    <!-- entidade.cidade  -->
                                    <f:attribute name="entidade" value="116"/>
                                    <!-- operacao.consultar  -->
                                    <f:attribute name="operacao" value="C"/>
                                </a4j:commandButton>



                            </h:panelGroup>

                        </c:if>
                    </h:panelGrid>
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" " />
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{CidadeControle.sucesso}" image="./imagens/sucesso.png" />
                        <h:commandButton rendered="#{CidadeControle.erro}" image="./imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{CidadeControle.mensagem}" />
                            <h:outputText styleClass="mensagemDetalhada" value="#{CidadeControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>
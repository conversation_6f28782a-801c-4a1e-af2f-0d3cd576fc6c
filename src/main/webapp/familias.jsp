<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Famílias"/>
    </title>
    <html>
        <body onload="fireElement('form:botaoAtualizarPagina')">
            <h:form id="form">
                <c:set var="titulo" scope="session" value="Famílias - ${RiscoControle.tituloResumoClientes}"/>
                <c:set var="urlWiki" scope="session" value="semWiki"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form:panelGroup,form:listaRisco" style="display:none"/>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                         <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                             <h:panelGroup layout="block">
                                 <h:panelGroup layout="block" styleClass="margin-box">
                                     <div align="right">
                                     <h:panelGroup>
                                         <rich:spacer height="5px"/>

                                         <a4j:commandLink id="imprimirPDF" styleClass="linkPadrao"
                                                            action="#{RiscoControle.imprimirRelatorio}"
                                                            style="margin-bottom: 8px"
                                                            oncomplete="abrirPopupPDFImpressao('relatorio/#{ReciboControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                             <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                         </a4j:commandLink>

                                         <a4j:commandLink id="exportarExcel"
                                                            style="margin-left: 8px;margin-bottom: 8px;"
                                                            actionListener="#{ExportadorListaControle.exportar}"
                                                            rendered="#{not empty RiscoControle.listaRiscoVOs}"
                                                            oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                            accesskey="2" styleClass="linkPadrao">
                                             <f:attribute name="lista" value="#{RiscoControle.listaRiscoVOs}"/>
                                             <f:attribute name="tipo" value="xls"/>
                                             <f:attribute name="atributos"
                                                          value="matriculaCliente=Matrícula,nomeCliente=Nome,peso=Peso,foneCliente=Telefone"/>
                                             <f:attribute name="prefixo" value="GrupoDeRisco"/>
                                             <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                         </a4j:commandLink>
                                         </div>
                                     </h:panelGroup>

                                     <rich:dataTable id="listaRisco"
                                                     width="100%"
                                                     rows="15"
                                                     styleClass="tabelaSimplesCustom"
                                                     value="#{BIFamiliaControle.familias}"
                                                     var="familia" rowKeyVar="status">


                                         <rich:column headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                      width="12%" label="Integrantes" sortBy="#{familia.nomes}"
                                                      filterEvent="onkeyup">
                                             <f:facet name="header">
                                                 <h:outputText styleClass="rotuloCampos" value="MATRÍCULA"/>
                                             </f:facet>
                                             <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza" action="#{RiscoControle.irParaTelaCliente}"
                                                              value="#{familia.nomes}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>
                                         </rich:column>
                                         <rich:column  headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                      width="12%" label="Situações"
                                                      filterEvent="onkeyup">
                                             <f:facet name="header">
                                                 <h:outputText styleClass="rotuloCampos" value="SITUAÇÕES"/>
                                             </f:facet>
                                             <a4j:commandLink  styleClass="linkPadrao texto-size-16 texto-cor-cinza" action="#{RiscoControle.irParaTelaCliente}"
                                                              value="#{familia.situacoes}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>
                                         </rich:column>

                                         <rich:column  headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                      width="12%" label="Situações"
                                                      filterEvent="onkeyup">
                                             <f:facet name="header">
                                                 <h:outputText styleClass="rotuloCampos" value="VALOR CONTRATOS"/>
                                             </f:facet>
                                             <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza" action="#{RiscoControle.irParaTelaCliente}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">

                                                 <h:outputText value="#{familia.valorContratos}">
                                                     <f:converter
                                                             converterId="FormatadorNumerico"/>
                                                 </h:outputText>
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>
                                         </rich:column>

                                         <rich:column headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                      width="12%" label="Situações"
                                                      filterEvent="onkeyup">
                                             <f:facet name="header">
                                                 <h:outputText styleClass="rotuloCampos" value="ACESSOS"/>
                                             </f:facet>
                                             <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-cinza" action="#{RiscoControle.irParaTelaCliente}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">

                                                 <h:outputText value="#{familia.acessos}">
                                                 </h:outputText>
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>
                                         </rich:column>



                                     </rich:dataTable>
                                     <rich:datascroller id="tabelaRisco" renderIfSinglePage="false" for="listaRisco" styleClass="scrollPureCustom"/>
                                <h:panelGroup layout="block" styleClass="container-botoes" style="text-align: left;">
                                    <h:outputText id="totalRisco" styleClass="texto-font texto-size-20 texto-cor-cinza" value="Total #{fn:length(RiscoControle.listaRiscoVOs)} itens"/>
                                </h:panelGroup>

                                 </h:panelGroup>
                             </h:panelGroup>
                         </h:panelGroup>

                    </h:panelGroup>
                 </h:panelGroup>
            </h:panelGroup>
         </h:form>

</body>
    </html>
</f:view>

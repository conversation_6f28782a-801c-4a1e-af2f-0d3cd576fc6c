<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
  <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo de Cliente(s)"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <body onload="fireElement('form:botaoAtualizarPagina')"/>
        <h:form id="form" >
            <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
          <c:set var="titulo" scope="session" value="Alunos com mais de um professor :${fn:length(RenovacaoSinteticoControle.listaClientes)} "></c:set>
          <c:set var="urlWiki" scope="session" value="semWiki"></c:set>
          <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
              <jsp:include page="topo_reduzido_popUp.jsp"/>
            </f:facet>
          </h:panelGroup>
          <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

            <h:panelGroup layout="block" styleClass="caixaCorpo">
              <h:panelGroup layout="block" style="height: 80%;width: 100%">
                <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                  <h:panelGroup layout="block" >
                    <h:panelGroup layout="block" styleClass="margin-box">
                      <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block">
                          <a4j:commandLink id="exportarExcel"
                                             style="margin-left: 8px;"
                                             actionListener="#{ExportadorListaControle.exportar}"
                                             rendered="#{not empty RenovacaoSinteticoControle.listaClientes}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{RenovacaoSinteticoControle.listaClientes}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,situacao_Apresentar=Situação"/>
                            <f:attribute name="prefixo" value="ResumoClientes"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                          </a4j:commandLink>
                          <%--BOTÃO PDF--%>
                          <a4j:commandLink id="exportarPdf"
                                             style="margin-left: 8px;"
                                             actionListener="#{ExportadorListaControle.exportar}"
                                             rendered="#{not empty RenovacaoSinteticoControle.listaClientes}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="2" styleClass="linkPadrao">
                            <f:attribute name="lista" value="#{RenovacaoSinteticoControle.listaClientes}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,situacao_Apresentar=Situação"/>
                            <f:attribute name="prefixo" value="ResumoClientes"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                          </a4j:commandLink>
                        </h:panelGroup>
                      </h:panelGrid>
                      <h:panelGroup layout="block" style="margin-left: 5px;">
                        <h:panelGroup layout="block" style="display: inline-block">
                          <h:outputText styleClass="tituloCampos" rendered="#{PendenciaControleRel.pendenciaRelVO.tpClienteMesmoCartao}" value="Somente alunos ativos:" />
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="display: inline-block;vertical-align: top;">
                          <h:selectBooleanCheckbox rendered="#{PendenciaControleRel.pendenciaRelVO.tpClienteMesmoCartao}" value="#{PendenciaControleRel.somenteClientesAtivos}"  styleClass="tituloFormulario">
                            <a4j:support event="onchange" action="#{RelContratosRecorrenciaControle.consultarClientesMesmoCartao}" reRender="form"/>
                          </h:selectBooleanCheckbox>
                        </h:panelGroup>
                      </h:panelGroup>
                      <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                      value="#{RenovacaoSinteticoControle.listaClientes}" var="cliente" rowKeyVar="status">
                        <rich:column sortBy="#{cliente.matricula}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                          <f:facet name="header">
                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Matrícula" />
                          </f:facet>
                          <h:outputText styleClass="texto-font texto-size-16-real texto-cor-cinza" value="#{cliente.matricula}" />
                        </rich:column>
                        <rich:column sortBy="#{cliente.nome}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                          <f:facet name="header">
                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Nome" />
                          </f:facet>
                          <h:outputText styleClass="texto-font texto-size-16-real texto-cor-cinza"  value="#{cliente.nome}" />
                        </rich:column>
                        <rich:column sortBy="#{cliente.situacao_Apresentar}"  filterEvent="onkeyup" styleClass="col-text-align-left" headerClass="col-text-align-left">
                          <f:facet name="header">
                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Situação" />
                          </f:facet>
                          <h:outputText styleClass="texto-font texto-size-16-real texto-cor-cinza"  value="#{cliente.situacao_Apresentar}" />
                        </rich:column>
                        <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                          <f:facet name="header">
                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold"  value="Opção"/>
                          </f:facet>
                          <a4j:commandLink action="#{RenovacaoSinteticoControle.irParaTelaClienteLista}"
                                           value="#{resumoPessoa.clienteVO.pessoa.nome}" styleClass="fa-icon-search linkPadrao texto-cor-azul texto-font-16-real" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                            <f:param name="state" value="AC"/>
                          </a4j:commandLink>
                        </rich:column>
                      </rich:dataTable>
                    </h:panelGroup>
                  </h:panelGroup>
                </h:panelGroup>

              </h:panelGroup>
            </h:panelGroup>
          </h:panelGroup>
        </h:form>
    </body>
</h:panelGrid>
</f:view>


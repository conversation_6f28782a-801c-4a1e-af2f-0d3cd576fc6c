<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
		
<!-- <PERSON><PERSON> -->
<h:form id="formBotoes">
<h:panelGroup id="painelBotoes">
		<table width="100%" border="0" align="center" cellpadding="0"
			cellspacing="0">
			<tr>
				<td width="10"><img
					src="../../imagens/agenda_imgs/menu_esq.jpg" width="10"
					height="69"></td>
				<td background="../../imagens/agenda_imgs/menu_fundo.jpg">
				<table width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td align="center"><a4j:commandButton value="Editar"
									action="#{PlanoContasControle.editar}" reRender="dados"
									rendered="#{PlanoContasControle.btEditar}"></a4j:commandButton>
								<a4j:commandButton value="Incluir"
									action="#{PlanoContasControle.incluir}"
									rendered="#{PlanoContasControle.btIncluir}" reRender="dados"></a4j:commandButton>
								<a4j:commandButton action="#{PlanoContasControle.copiar}"
									rendered="#{PlanoContasControle.btCopiar}" value="Copiar"></a4j:commandButton>
								<a4j:commandButton action="#{PlanoContasControle.colar}" reRender="form"
									rendered="#{PlanoContasControle.btColar}" value="Colar"></a4j:commandButton>
								<a4j:commandButton value="Excluir" reRender="dados"
									onclick="if(!confirm('Deseja realmente excluir este plano de contas?')) {return false;}"
									action="#{PlanoContasControle.excluir}"
									rendered="#{PlanoContasControle.btExcluir}"></a4j:commandButton>
								<a4j:commandButton value="Cancelar"
									action="#{PlanoContasControle.cancelar}" reRender="dados"
									rendered="#{PlanoContasControle.btCancelar}"></a4j:commandButton>
								<a4j:commandButton value="Consultar"
									action="#{PlanoContasControle.consultar}"></a4j:commandButton>
								<a4j:commandButton value="Incluir Plano Raiz"
									action="#{PlanoContasControle.incluirRaiz}"></a4j:commandButton>
						</td>
							
						<td align="center">
							<h:panelGroup rendered="#{CadastroInicialControle.eventoOrcado}">
								<h:graphicImage value="../../imagens/bt_ajuda.jpg" style="border: 0px;" onclick="ativarAjuda('verificar');"/>
							</h:panelGroup>
						</td>	
					</tr>					
				</table>
				</td>
				<td width="10"><img
					src="../../imagens/agenda_imgs/menu_dir.jpg" width="10"
					height="69"></td>
			</tr>
		</table>
		</h:panelGroup>
</h:form>
<!-- Fim da barra de bot�es -->
	<!-- Mensagens -->
						<h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
							<h:panelGrid columns="1" width="100%">
								<f:verbatim>
									<h:outputText value=" " />
								</f:verbatim>
							</h:panelGrid>
							<h:panelGrid columns="1" width="100%">
								<h:outputText styleClass="mensagem"
									value="#{PlanoContasControle.mensagem}" />
								<h:outputText styleClass="mensagemDetalhada"
									value="#{PlanoContasControle.mensagemDetalhada}" />
							</h:panelGrid>
						</h:panelGrid>
						<!-- fim das Mensagens -->
	
		<h:form id="form">
			<h:panelGrid columns="1" id="tudo" style="vertical-align:top;">
				<!-- TREE VIEW -->
	<div id="content">
				<table id="trv">
					<colgroup>
						<col width="200" />
						<col width="0*">
					</colgroup>
					<tr>
						<th>Plano de Conta</th>
					</tr>
					<c:forEach var="plano" varStatus="indexDia"
						items="${PlanoContasControle.lista}">
						<tr id="table<c:out value="${plano.codigoTrv}"></c:out>">
							<td>
							<a
								href="javascript:toggle('linkMais${plano.codigoTrv}');"
								onclick="treetable_toggleRow('table${plano.codigoTrv}');"><b
								id="linkMais${plano.codigoTrv}">(-)<b></a>
								<a href="#"
								onclick="preencherHiddenChamarBotao('form:botaoSelecao','form:codigoSelecao','<c:out value="${plano.codigo}"></c:out>')"><c:out
								value="${plano.descricaoDetalhada}"></c:out></a>
								</td>
						</tr>
					</c:forEach>
				</table>
				</div>
				<!-- fim: TREE VIEW -->
				<!-- Controles para a tree view -->
				<a4j:commandButton style="visibility: hidden;" reRender="painelBotoes" id="botaoSelecao"
					action="#{PlanoContasControle.processSelection}"></a4j:commandButton>
				<h:inputHidden id="codigoSelecao" value="#{PlanoContasControle.codigoBancoPlanoContas}" />
				<!-- fim: Controles para a tree view -->
			</h:panelGrid>

		</h:form>
	</h:panelGrid>
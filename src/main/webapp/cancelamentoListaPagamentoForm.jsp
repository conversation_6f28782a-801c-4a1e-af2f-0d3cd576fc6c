<%--
    Document   : cancelamentoListaPagamentoForm
    Created on : 26/06/2009, 08:23:21
    Author     : pedro
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
    #formCancelamentoDevolucaoAuto\:panelDataFinalContrato.dateTimeCustom .rich-calendar-exterior:before {
        border-left: 0px solid transparent !important;
        border-right: 0px solid transparent !important;
        border-bottom: 0px solid #ccc !important;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"/>
        <c:set var="titulo" scope="session" value="Cancelamento"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="formCancelamentoDevolucaoAuto">
        <h:panelGrid columns="2" width="100%" >
            <h:panelGrid columns="1" width="100%" cellpadding="2" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" />
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                <br>
                <p id="tituloDetalhes" style="margin-bottom:0px;font-weight: bold;margin-top: 15px;margin-bottom: 15px;" class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">
                    <c:if test="${!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                        DETALHES DA DEVOLUÇÃO
                    </c:if>
                    <c:if test="${CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                        DETALHES DO CANCELAMENTO
                    </c:if>
                </p>


                    <h:outputText rendered="#{!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.mensagemDevolucao}" styleClass="texto-size-14-real texto-cor-cinza texto-font"/>

                    <h:panelGrid id="parcelasContrato" rendered="#{CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}"
                                 style="vertical-align: middle;" columns="1">

                        <h:dataTable headerClass="consulta"
                                     rowClasses="tablelistras textsmall" width="100%"
                                     columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, centralizado, centralizado"
                                     value="#{CancelamentoContratoControle.cancelamentoContratoVO.listaParcelas}" var="parcela">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Nº Contrato"  />
                                </f:facet>
                                <h:outputText id="codContrato" value="#{parcela.contrato_Apresentar}" styleClass="form"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Codigo"  />
                                </f:facet>
                                <h:outputText id="codParcela" value="#{parcela.codigo}" styleClass="form"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Descricao"  />
                                </f:facet>
                                <h:outputText id="codDescricao" value="#{parcela.descricao}" styleClass="form"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Lançamento"  />
                                </f:facet>
                                <h:outputText id="dtLancamento" value="#{parcela.dataRegistro_Apresentar}" styleClass="form"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Vencimento"  />
                                </f:facet>
                                <h:outputText id="dtVencimento" value="#{parcela.dataVencimento_Apresentar}" styleClass="form"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Valor"  />
                                </f:facet>
                                <h:outputText id="valorParcela" value="#{parcela.valorParcela_Apresentar}" styleClass="form"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Situação"  />
                                </f:facet>
                                <h:outputText id="situacaoEmAberto" rendered="#{parcela.situacao == 'EA'}" style="color: red" value="#{parcela.situacao_Apresentar}" styleClass="form"/>
                                <h:outputText id="situacao" rendered="#{parcela.situacao != 'EA'}" value="#{parcela.situacao_Apresentar}" styleClass="form"/>
                            </h:column>


                        </h:dataTable>

                        <h:outputText id="msgCancelamentoAntecipado" escape="false" style="color: red"
                                      value="#{CancelamentoContratoControle.cancelamentoContratoVO.mensagemCancelamentoAntecipado}" styleClass="tituloCampos"/>

                    </h:panelGrid>

                    <h:panelGrid style="vertical-align: middle;" columns="4" rendered="#{!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                        <h:outputText value="ALTERAR MULTA" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                        <h:panelGroup>
                            <h:outputLink styleClass="linkWiki" value="#{SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"
                                          title="Editar valor da Multa" target="_blank" >
                                <i class="fa-icon-external-link-square texto-size-14 texto-cor-cinza"/>
                            </h:outputLink>
                        </h:panelGroup>
                        <h:selectBooleanCheckbox id="liberacaoAlterarValor" value="#{CancelamentoContratoControle.usuarioDesejaAlterarMulta}">
                            <a4j:support event="onclick" action="#{CancelamentoContratoControle.alterarMulta}"
                                         oncomplete="converterPorcentagemParaReais('formCancelamentoDevolucaoAuto','valorNovoPorcentagem','valorNovoReais','#{CancelamentoContratoControle.contratoVO.vendaCreditoTreino ? 'vlrRestanteUsadoCredito' : 'vlrRestanteUsado'}')"
                                         reRender="formCancelamentoDevolucaoAuto"/>
                        </h:selectBooleanCheckbox>
                    </h:panelGrid>

                    <h:panelGrid style="vertical-align: middle;" columns="4" rendered="#{CancelamentoContratoControle.usuarioDesejaAlterarMulta}">
                        <h:outputText value="ALTERAR VALOR DA MULTA" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"  style="margin-right: 10px;"/>
                        <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} " styleClass="texto-size-14-real texto-cor-cinza texto-font"/>
                        <h:inputText id="valorNovoReais" size="7"

                                     onkeypress="return formatar_moeda(this,'.',',',event);" maxlength="10"
                                     onfocus="focusinput(this);" styleClass="inputTextClean" style="margin-left: 8px">
                            <a4j:support event="onblur"
                                         oncomplete="converterReaisParaPorcentagem('formCancelamentoDevolucaoAuto','valorNovoPorcentagem','valorNovoReais','#{CancelamentoContratoControle.contratoVO.vendaCreditoTreino ? 'vlrRestanteUsadoCredito' : 'vlrRestanteUsado'}')" />
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>
                    </h:panelGrid>

                    <h:panelGrid style="vertical-align: middle;" columns="4" rendered="#{CancelamentoContratoControle.usuarioDesejaAlterarMulta}">
                		<h:outputText value="ALTERAR % DA MULTA" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" style="margin-right: 39px"/>
                        <h:outputText value="%" styleClass="texto-size-14-real texto-cor-cinza texto-font"/>
                        <h:panelGroup  id="inputsMulta">
                            <h:inputText id="valorNovoPorcentagem" size="7"
                                         onkeypress="return formatar_moeda(this,'.',',',event);" maxlength="10"
                                         value="#{CancelamentoContratoControle.valorAlteracaoPercentualMulta}"
                                         onfocus="focusinput(this);" styleClass="inputTextClean" style="margin-left: 21px">
                                <a4j:support event="onblur"
                                             oncomplete="converterPorcentagemParaReais('formCancelamentoDevolucaoAuto','valorNovoPorcentagem','valorNovoReais','#{CancelamentoContratoControle.contratoVO.vendaCreditoTreino ? 'vlrRestanteUsadoCredito' : 'vlrRestanteUsado'}')" />
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>
                        </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid style="padding-bottom: 10px; padding-top: 10px;">
                    <a4j:commandLink id="aplicarvalorNovo" value="Aplicar" rendered="#{CancelamentoContratoControle.usuarioDesejaAlterarMulta}"
                                       action="#{CancelamentoContratoControle.aplicarAlterarPercentual}" style="margin-bottom: 30px" styleClass="botaoPrimario"
                                       oncomplete="#{CancelamentoContratoControle.msgAlert}"
                                       reRender="formCancelamentoDevolucaoAuto, panelAutorizacaoFuncionalidade"/>
                </h:panelGrid>
                <h:panelGroup rendered="#{!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <h:outputText value="VALOR PAGO PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font " value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" id="vlrPago" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalPagoPeloCliente}" style="color:green">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.existeContratoRenovacao}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR PAGO CONTRATOS RENOVADOS: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font " value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalBaseContratoRenovacao}" style="font-weight: bold;">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.devolucao && !CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR BASE DO CONTRATO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="Valor do contrato - valor dos produtos"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font " value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrBaseContrato"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  title="Valor do contrato - valor dos produtos"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorBaseContrato}"
                                  style="color: green">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                <h:panelGroup rendered="#{CancelamentoContratoControle.devolverTransferenciaSaldoCredito}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR TRANSFERÊNCIA DE SALDO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="Valor da transferência de saldo do contrato renovado antecipadamente."/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrTransfSaldoCreditoTreino"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="Valor da transferência de saldo do contrato renovado antecipadamente."
                                  value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.totalTransferenciaSaldo}"
                                  style="color: green">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                <h:panelGroup rendered="#{(CancelamentoContratoControle.cancelamentoContratoVO.devolucao) && (!CancelamentoContratoControle.contratoVO.vendaCreditoTreino) && !CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR UTILIZADO PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="#{CancelamentoContratoControle.contratoVO.empresa.retrocederValorMensalPlanoCancelamento ? '(Valor do plano mensal / 30) x nr.dias utilizados' : '(Valor base do contrato / nr.dias do contrato) x nr.dias utilizados'}"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrUsado" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorUtilizadoPeloClienteMensal}"
                                  style="color: red"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  title="#{CancelamentoContratoControle.contratoVO.empresa.retrocederValorMensalPlanoCancelamento ? '(Valor do plano mensal / 30) x nr.dias utilizados' : '(Valor base do contrato / nr.dias do contrato) x nr.dias utilizados'}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <h:panelGroup rendered="#{(CancelamentoContratoControle.cancelamentoContratoVO.devolucao) && (CancelamentoContratoControle.contratoVO.vendaCreditoTreino) && (!CancelamentoContratoControle.contratoVO.plano.creditoTreinoNaoCumulativo)}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR UTILIZADO PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="(Valor unitário crédito x nr.créditos utilizados)"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrUsadoCredito" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUtilizadoMensal}"
                                  style="color: red"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  title="(Valor unitário crédito x nr.créditos utilizados)">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <h:panelGroup rendered="#{(CancelamentoContratoControle.cancelamentoContratoVO.devolucao) && (CancelamentoContratoControle.contratoVO.vendaCreditoTreino) && (CancelamentoContratoControle.contratoVO.plano.creditoTreinoNaoCumulativo)}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR UTILIZADO PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="(Valor unitário crédito x nr.créditos utilizados)"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrUsadoCreditoCumulativo" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUtilizadoTotal}"
                                  style="color: red"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  title="(Valor unitário crédito x nr.créditos utilizados)">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>


                <h:panelGroup rendered="#{(CancelamentoContratoControle.cancelamentoContratoVO.devolucao) && (!CancelamentoContratoControle.contratoVO.vendaCreditoTreino) && !CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR RESTANTE DO CONTRATO: "
                                  title="Valor base - valor utilizado"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrRestanteUsado" 
                                  title="Valor base - valor utilizado"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorCreditoRestanteContratoValorMensalSemTaxaEMulta}"
                                  style="color: green">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <h:panelGroup rendered="#{(CancelamentoContratoControle.cancelamentoContratoVO.devolucao) && (CancelamentoContratoControle.contratoVO.vendaCreditoTreino)}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR RESTANTE DO CONTRATO: "
                                  title="Valor base - valor utilizado"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrRestanteUsadoCredito"
                                  title="Valor base - valor utilizado"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{CancelamentoContratoControle.valorCreditoTreinoRestante}"
                                  style="color: green">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <c:if test="${!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <h:panelGroup>
                    <h:outputText value="VALOR DOS PRODUTO(S) NA COMPRA DO CONTRATO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrProdutos" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalSomaProdutoContratos}" style="color: red">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                    <c:if test="${CancelamentoContratoControle.cancelamentoContratoVO.valorTotalSomaProdutoContratosDevolver > 0.0}">
                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        <h:panelGroup>
                            <h:outputText value="VALOR RESTANTE DOS PRODUTO(S) NA COMPRA DO CONTRATO: "
                                          styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{MovPagamentoControle.empresaLogado.moeda}  "/>
                            <h:outputText id="vlrProdutosDevolver" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalSomaProdutoContratosDevolver}"
                                          style="color: green">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                    </c:if>
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                </c:if>


                <c:if test="${CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                    <c:if test="${CancelamentoContratoControle.contratoVO.empresa.permitirAlterarDataFinalContratoNoCancelamento}">
                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                        <h:panelGroup id="panelDataFinalContrato" styleClass="dateTimeCustom" style="font-size: 11px">
                            <h:outputText value="DATA FINAL CONTRATO:" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"/>
                            <rich:spacer width="5px;"/>
                            <rich:calendar id="dataFinalContrato"
                                           value="#{CancelamentoContratoControle.novaDataFinalContrato}"
                                           rendered="#{CancelamentoContratoControle.dataFinalContratoEmEdicao}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="false"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           zindex="2"
                                           jointPoint="top-left"
                                           direction="top-right"
                                           showWeeksBar="false"/>
                            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                           rendered="#{!CancelamentoContratoControle.dataFinalContratoEmEdicao}"
                                           value="#{CancelamentoContratoControle.cancelamentoContratoVO.dataFinalAcessoCancelamentoAntecipado}"><f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                            <h:message for="dataFinalContrato"  styleClass="mensagemDetalhada"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                            <a4j:commandLink action="#{CancelamentoContratoControle.editarCampoData}"
                                             style="margin-left: 10px;"
                                             rendered="#{!CancelamentoContratoControle.dataFinalContratoEmEdicao}"
                                             reRender="formCancelamentoDevolucaoAuto, panelAutorizacaoFuncionalidade">
                                <i class="fa-icon-edit texto-size-18 linkAzul tooltipster"></i>
                            </a4j:commandLink>
                            <a4j:commandLink action="#{CancelamentoContratoControle.aplicarData}"
                                             style="margin-left: 10px;"
                                             rendered="#{CancelamentoContratoControle.dataFinalContratoEmEdicao}"
                                             oncomplete="#{CancelamentoContratoControle.mensagemNotificar}"
                                             reRender="formCancelamentoDevolucaoAuto">
                                <i class="fa-icon-ok texto-size-18 linkAzul tooltipster"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </c:if>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:panelGroup>
                        <h:outputText value="VALOR CANCELAMENTO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                        <h:outputText id="valorTotalCancelamentoAntecipado" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalCancelamentoAntecipado}" style="color: red">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                </c:if>

                <h:panelGroup>
                    <h:outputText value="SALDO DA CONTA CORRENTE DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrCC" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.saldoContaCorrenteCliente}"
                                  style="color: #{(CancelamentoContratoControle.cancelamentoContratoVO.saldoContaCorrenteCliente < 0)?'red':'green'}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                <c:if test="${!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <h:panelGroup>
                    <h:outputText value="VALOR DOS CUSTOS ADMINISTRATIVOS: "
                                  title="Valor do Produto Taxa Cancelamento no plano "
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrTaxa" 
                                  title="Valor do Produto Taxa Cancelamento no plano "
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTaxaCancelamento}" style="color: red">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <h:panelGroup>
                    <h:outputText value="VALOR DA MULTA: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="Valor restante do contrato X Percentual de multa"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrMulta" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorMensalComBaseNoPercentual}" 
                                  title="Valor restante do contrato X Percentual de multa"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  style="color:red">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>
                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <h:panelGroup>
                    <h:selectBooleanCheckbox id="liberacaoMultaCustos" value="#{CancelamentoContratoControle.liberarMultaCustos}">
                        <a4j:support event="onclick"
                                     reRender="formCancelamentoDevolucaoAuto, panelAutorizacaoFuncionalidade"
                                     action="#{CancelamentoContratoControle.obterLiberacaoMultaCustos}"
                                     oncomplete="#{CancelamentoContratoControle.onCompleteQuitacao}"/>
                    </h:selectBooleanCheckbox>
                    <rich:spacer width="10px"/>
                    <h:outputText value="ISENTAR CLIENTE DA MULTA E CUSTOS" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" style="font-weight: bold;"/>
                </h:panelGroup>
                </c:if>

                <%-- VALOR DA MULTA RESTANTES DAS PARCELAS EM ABERTO --%>
                <h:panelGroup layout="block"  rendered="#{CancelamentoContratoControle.contratoVO.cancelamentoAntecipado && CancelamentoContratoControle.cancelamentoContratoVO.valorMultaRestanteCancelamentoAntecipado > 0}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR DA MULTA RESTANTES DAS PARCELAS EM ABERTO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrMultaCancAnte" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorMultaRestanteCancelamentoAntecipado}"
                                  title="Valor Multa do cancelamento antecipado"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  style="color:red">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <%--VALOR MULTA SOBRE PROXIMA PARCELA--%>
                <h:panelGroup layout="block"
                              rendered="#{!CancelamentoContratoControle.contratoVO.cancelamentoObrigatoriedadePagamento && CancelamentoContratoControle.contratoVO.cancelamentoAntecipado && CancelamentoContratoControle.cancelamentoContratoVO.movParcelaCancelamentoAntecipado != 0 && CancelamentoContratoControle.cancelamentoContratoVO.planoEspecialCancelamentoAntecipado}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:outputText value="VALOR DA MULTA SOBRE PRÓXIMA PARCELA: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  title="Valor da Multa que será adicionada nas próximas parcela"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda}  " />
                    <h:outputText id="vlrMultaProximaParcelaCancAnte" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorMultaProximaParcelaCancelamentoAntecipado}"
                                  title="Valor Multa que será adicionada na próxima parcela"
                                  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  style="color:red">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:panelGroup>

                <%-- LIBERAR PAGAR PRÓXIMA PARCELA CANCELAMENTO ANTECIPADO --%>
                <h:panelGroup layout="block"  rendered="#{CancelamentoContratoControle.contratoVO.cancelamentoAntecipado && CancelamentoContratoControle.cancelamentoContratoVO.movParcelaCancelamentoAntecipado != 0}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:selectBooleanCheckbox id="liberacaoProximaParcelaCancAnte" value="#{CancelamentoContratoControle.liberarProximaParcelaCancelamentoAntecipado}">
                        <a4j:support event="onclick"
                                     reRender="formCancelamentoDevolucaoAuto, panelAutorizacaoFuncionalidade"
                                     action="#{CancelamentoContratoControle.obterLiberacaoIsentarProximaParcela}"
                                     oncomplete="#{CancelamentoContratoControle.onCompleteQuitacao}"/>
                    </h:selectBooleanCheckbox>
                    <rich:spacer width="10px"/>
                    <h:outputText value="NÃO COBRAR PRÓXIMA PARCELA EM ABERTO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                </h:panelGroup>


                <%-- ISENTAR MULTA SOBRE VALOR RESTANTE DAS PARCELAS EM ABERTO --%>
                <h:panelGroup layout="block"  rendered="#{!CancelamentoContratoControle.contratoVO.cancelamentoObrigatoriedadePagamento && CancelamentoContratoControle.contratoVO.cancelamentoAntecipado && not empty CancelamentoContratoControle.cancelamentoContratoVO.parcelasEmAtrasoCancelamentoAntecipado}">
                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                    <h:selectBooleanCheckbox id="liberacaoMultaCancAnte" value="#{CancelamentoContratoControle.liberarMultaCancelamentoAntecipado}">
                        <a4j:support event="onclick"
                                     reRender="formCancelamentoDevolucaoAuto, panelAutorizacaoFuncionalidade"
                                     action="#{CancelamentoContratoControle.obterLiberacaoMultaCancelamentoAntecipado}"
                                     oncomplete="#{CancelamentoContratoControle.onCompleteQuitacao}"/>
                    </h:selectBooleanCheckbox>
                    <rich:spacer width="10px"/>
                    <h:outputText value="ISENTAR MULTA SOBRE VALOR RESTANTE DAS PARCELAS EM ABERTO" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                </h:panelGroup>

                <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                <h:panelGrid width="100%" columns="2" >
                    <h:panelGrid  style="float: right;">
                        <h:panelGroup >
                            <h:commandLink id="voltar" title="Voltar Passo"  styleClass="pure-button" action="#{CancelamentoContratoControle.voltarTelaCancelamento}">
                                  <i class="fa-icon-arrow-left"></i>
                                    </h:commandLink>
                            <rich:spacer width="7"/>
                            <a4j:commandLink reRender="formCancelamentoDevolucaoAuto"
                                             id="proximo"
                                             action="#{CancelamentoContratoControle.existePagamentoConjunto}"
                                             styleClass="pure-button pure-button-primary"
                                             oncomplete="#{CancelamentoContratoControle.mensagemNotificar}">
                                <i class="fa-icon-arrow-right"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>

</f:view>

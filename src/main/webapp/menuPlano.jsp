<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<!-- inicio box -->


<h:panelGroup layout="blokc" styleClass="menuLateral">
    <!-- Menu Lateral -->
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-briefcase"></i> Produtos
        </h:panelGroup>

        <!-- Categoria de Produto -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.categoriaProduto}">
            <a4j:commandLink value="#{msg_menu.Menu_categoriaProduto}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CATEGORIA_PRODUTO" />
            </a4j:commandLink>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT:Categoria_de_Produto"
                          title="Clique e saiba mais: Categoria de Produtos" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Categoria de Produto -->

        <!-- Produto -->        
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.produto}">
            <a4j:commandLink value="#{msg_menu.Menu_produto}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PRODUTO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-produto-de-estoque/"
                          title="Clique e saiba mais: Produtos" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Produto -->  

        <!-- Produto Coletivo -->        
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.lancamentoProdutoColetivo}">
            <a4j:commandLink value="#{msg_menu.Menu_produtoColetivo}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="LANCAMENTO_PRODUTO_COLETIVO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT:Produtos_Coletivos"
                          title="Clique e saiba mais: Produto Coletivo" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- //Produto Coletivo -->

        <!-- Controle de Estoque -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a class="titulo3 linkFuncionalidade" href="indexControleEstoque.jsp">Controle de Estoque</a>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Controle_de_Estoque"
                          title="Clique e saiba mais: Controle de Estoque" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>    
        <!-- //Controle de Estoque -->

        <!-- Tamanho de Armário -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.tamanhoArmario}">
            <a4j:commandLink value="#{msg_menu.Menu_TamanhoArmario}"
                             styleClass="titulo3 linkFuncionalidade"
                             actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="TAMANHO_ARMARIO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:TamanhoArmario"
                          title="Clique e saiba mais: Armário" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>       
        <!-- //Tamanho de Armário -->
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-file-alt"></i> Planos
        </h:panelGroup>

        <!-- Plano -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.plano && LoginControle.apresentarLinkZW}">
            <a4j:commandLink value="#{msg_menu.Menu_plano}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PLANO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT:Plano"
                          title="Clique e saiba mais: Planos" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>       
        <!-- //Plano -->

        <!-- Tipo de Plano -->
        <h:panelGroup layout="block"
                      styleClass="grupoMenuItem"
                      rendered="#{LoginControle.apresentarLinkZW && LoginControle.permissaoAcessoMenuVO.planoTipo}">
            <a4j:commandLink value="Tipo de plano"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PLANOTIPO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT:Plano"
                          title="Clique e saiba mais: Planos" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- Tipo de Plano -->

        <!-- Horário -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.apresentarLinkZW && LoginControle.permissaoAcessoMenuVO.horario}">
            <a4j:commandLink value="#{msg_menu.Menu_horario}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="HORARIO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT:Horario"
                          title="Clique e saiba mais: Horário" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Horário -->

        <!-- Desconto -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.desconto}">
            <a4j:commandLink value="#{msg_menu.Menu_desconto}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="DESCONTO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT:Desconto"
                          title="Clique e saiba mais: Desconto" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>      
        <!-- //Desconto -->

        <!-- Pacote -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.composicao}">
            <a4j:commandLink value="#{msg_menu.Menu_composicao}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PACOTE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT:Pacote"
                          title="Clique e saiba mais: Pacote" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>           
        <!-- //Pacote -->

        <!-- Condição de pagamento -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.apresentarLinkZW && LoginControle.permissaoAcessoMenuVO.condicaoPagamento}">
            <a4j:commandLink value="#{msg_menu.Menu_codPagamento}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONDICAO_DE_PAGAMENTO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:PPT:Condicao_de_Pagamento"
                          title="Clique e saiba mais: Condição de Pagamento" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>           
        <!-- //Condição de pagamento -->

        <!-- Convenio de Desconto -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.convenioDesconto}">
            <a4j:commandLink value="#{msg_menu.Menu_convenioDesconto}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONVENIO_DE_DESCONTO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._de_Contrato:Convénio_de_Desconto"
                          title="Clique e saiba mais: Convénio de Desconto" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Convenio de Desconto -->
    </h:panelGroup>
    <!-- //Grupo_Planos -->

    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-file-alt"></i> Turmas
        </h:panelGroup>


        <!-- Modalidades -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.apresentarLinkZW && LoginControle.permissaoAcessoMenuVO.modalidade}">
            <a4j:commandLink value="#{msg_menu.Menu_modalidade}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="MODALIDADE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-uma-nova-modalidade/"
                          title="Clique e saiba mais: Modalidade" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>        
        <!-- //Modalidades -->

        <!-- Ambiente -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.ambiente}">
            <a4j:commandLink value="#{msg_menu.Menu_ambiente}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="AMBIENTE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-ambiente/"
                          title="Clique e saiba mais: Ambiente" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup> 
        <!-- //Ambiente -->    

        <!-- Nivel Turma -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.apresentarLinkZW && LoginControle.permissaoAcessoMenuVO.nivelTurma}">
            <a4j:commandLink value="#{msg_menu.Menu_nivelTurma}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="NIVEL_TURMA" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-niveis-para-serem-utilizados-nas-turmas/"
                          title="Clique e saiba mais: Nivel Turma" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>        
        <!-- //Nivel Turma -->

        <!-- Turma -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.apresentarLinkZW && LoginControle.permissaoAcessoMenuVO.turma}">
            <a4j:commandLink value="#{msg_menu.Menu_turma}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="TURMA" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-turma/"
                          title="Clique e saiba mais: Turma" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>          
        <!-- //Turma -->  
    </h:panelGroup>

</h:panelGroup>

<!-- fim box -->


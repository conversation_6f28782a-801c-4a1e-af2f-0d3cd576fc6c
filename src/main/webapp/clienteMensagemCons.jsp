<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
		padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ClienteMensagem_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        
        <h:form id="form">
            <h:commandLink action="#{ClienteMensagemControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ClienteMensagem_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/> 
                    <h:selectOneMenu styleClass="campos" id="consulta" required="true" value="#{ClienteMensagemControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{ClienteMensagemControle.tipoConsultaCombo}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" styleClass="campos" value="#{ClienteMensagemControle.controleConsulta.valorConsulta}"/>
                    <h:commandButton id="consultar" styleClass="botoes" value="#{msg_bt.btn_consultar}" action="#{ClienteMensagemControle.consultar}" image="./imagens/botaoConsultar.png" title="#{msg.msg_consultar_dados}" accesskey="2"/>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{ClienteMensagemControle.listaConsulta}" rendered="#{ClienteMensagemControle.apresentarResultadoConsulta}" rows="10" var="clienteMensagem">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_ClienteMensagem_codigo}"/>
                                </f:facet>
                                <h:commandLink action="#{ClienteMensagemControle.editar}" id="codigo" value="#{clienteMensagem.codigo}"/> 
                            </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:commandButton action="#{ClienteMensagemControle.editar}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" title="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>
                <rich:datascroller align="center" for="form:items"  id="scResultadoConsulta" rendered="#{ClienteMensagemControle.apresentarResultadoConsulta}" />

                <h:panelGrid id="panelGridMensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{ClienteMensagemControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ClienteMensagemControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{ClienteMensagemControle.novo}" value="#{msg_bt.btn_novo}" styleClass="botoes" image="./imagens/botaoNovo.png" title="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>
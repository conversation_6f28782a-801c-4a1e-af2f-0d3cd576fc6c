<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText
            value="#{msg_aplic.prt_Finan_Conta_Contabil_tituloForm}"/></title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Finan_Conta_Contabil_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}tipo-de-contas-e-seus-comportamentos/"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <!-- O FORMULARIO -->
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_codigo}" />
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{ContaContabilControle.contaContabilVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada" />
                    </h:panelGroup>

                    <!-- Descrição -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Tipo_Conta_descricao}:"/>

                    <h:inputText id="valorConsulta" styleClass="form" size="50"
                                 maxlength="100" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 value="#{ContaContabilControle.contaContabilVO.descricao}"/>
                    <h:outputText styleClass="tituloCampos" value="Código Integração:"/>

                    <h:inputText  id="idCodigoIntegracao"
                                  title="Informe o código equivalente ao sistema de integração."
                                  onkeypress="return mascaraTodos(this.form, 'form:idCodigoIntegracao', '999999999', event);"
                                  size="5" maxlength="6" onblur="blurinput(this);"
                                  onfocus="focusinput(this);" styleClass="form" value="#{ContaContabilControle.contaContabilVO.codigoIntegracao}" />

                </h:panelGrid>
                <!-- FIM DO FORMULÁRIO -->
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <!-- mensagens -->
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{ContaContabilControle.sucesso}"
                                         image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ContaContabilControle.erro}"
                                         image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{ContaContabilControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{ContaContabilControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton immediate="true"
                                             action="#{ContaContabilControle.novo}"
                                             value="#{msg_bt.btn_novo}"
                                             styleClass="botoes nvoBt btSec"
                                             title="#{msg.msg_novo_dados}"/>

                            <a4j:commandButton id="salvar" action="#{ContaContabilControle.gravar}"
                                               value="Gravar"
                                               reRender="panelMensagem"
                                               oncomplete="#{ContaContabilControle.msgAlert}"
                                               alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <a4j:commandButton id="excluir" action="#{ContaContabilControle.confirmarExcluir}"
                                               oncomplete="#{ContaContabilControle.msgAlert}" reRender="form,mdlMensagemGenerica"
                                               value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="3"
                                               styleClass="botoes nvoBt btSec btPerigo" />

                            <h:commandButton id="consultar" immediate="true"
                                             action="#{ContaContabilControle.consultar}"
                                             value="#{msg_bt.btn_consultar}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4"
                                             styleClass="botoes nvoBt btSec"/>

                        </h:panelGroup>
                    </h:panelGrid>
                    <!-- fim botoes -->
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

<script>
    document.getElementById("form:valorConsulta").focus();
</script>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<style>
    .fontListaNotaFiscal {
        /*font-size: 0.857em !important;*/
        font-size: 0.85em !important;
    }

    .corAzulClickNota {
        color: #29abe2 !important;
    }

    .fontBtnAcoes {
        text-decoration: none;
        font-size: 16px;
        /*font-size: 1em !important;*/
    }

    .statusGerada {
        color: #777 !important;
    }

    .statusAguardando {
        color: #777 !important;
    }

    .statusAutorizada {
        color: darkgreen !important;
    }

    .statusCancelando {
        color: darkorange !important;
    }

    .statusCancelada {
        color: darkred !important;
    }

    .statusCancelamentoNegado {
        color: darkred !important;
    }

    .statusErro {
        color: #004671 !important;
    }

    .statusNegada {
        color: #004671 !important;
    }

    .statusNenhum {
        color: #004671 !important;
    }

    .panelTblListaNotas {
        min-height: calc(100vh - 400px);
    }
</style>

<h:panelGroup layout="block" id="panelTblListaNotas" styleClass="panelTblListaNotas">

    <rich:dataTable styleClass="tabelaDados semZebra"
                    id="tblListaNotas"
                    value="#{NotaFiscalControle.listaNotas}"
                    rows="#{NotaFiscalControle.listaNotasPaginada.limit}"
                    var="nota" rowKeyVar="status">
        <rich:column>
            <f:facet name="header">
                <h:selectBooleanCheckbox value="#{NotaFiscalControle.marcarTodos}">
                    <a4j:support event="onclick" action="#{NotaFiscalControle.marcarTodosItens}"
                                 reRender="form:panelENotas"/>
                </h:selectBooleanCheckbox>
            </f:facet>
            <h:selectBooleanCheckbox styleClass="texto-size-14 cinza"
                                     value="#{nota.selecionado}">
                <a4j:support event="onchange"
                             actionListener="#{NotaFiscalControle.selecionarNotaCheckBox}"
                             reRender="panelTblListaNotas">
                    <f:attribute name="codNota" value="#{nota.codigo}"/>
                </a4j:support>
            </h:selectBooleanCheckbox>
        </rich:column>

        <rich:column sortBy="#{nota.codigo}">
            <f:facet name="header">
                <h:outputText value="COD."/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.codigo}"/>
        </rich:column>

        <rich:column sortBy="#{nota.tipo.descricao}">
            <f:facet name="header">
                <h:outputText value="TIPO"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.tipo.descricao}"/>
        </rich:column>

        <rich:column sortBy="#{nota.configuracaoNotaFiscalVO.descricao}"
                     rendered="#{NotaFiscalControle.filtro.configuracaoNotaFiscal == 0}">
            <f:facet name="header">
                <h:outputText value="CONFIG. EMISSÃO"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.configuracaoNotaFiscalVO.descricao}"/>
        </rich:column>

        <rich:column sortBy="#{nota.serie}">
            <f:facet name="header">
                <h:outputText value="SÉRIE"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.serie}"/>
        </rich:column>

        <rich:column sortBy="#{nota.rps}"
                     rendered="#{NotaFiscalControle.apresentarRPS}">
            <f:facet name="header">
                <h:outputText value="RPS"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.rps}"/>
        </rich:column>

        <rich:column sortBy="#{nota.numeroNota}">
            <f:facet name="header">
                <h:outputText value="NR. NOTA"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.numeroNota}"/>
        </rich:column>

        <rich:column sortBy="#{nota.cpfCnpj}" id="colCpfCnpj">
            <f:facet name="header">
                <h:outputText value="CPF/CNPJ" id="hCpfCnpj"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal" id="vCpfCnpj"
                          value="#{nota.cpfCnpjApresentar}"/>
        </rich:column>

        <rich:column sortBy="#{nota.razaoSocial}">
            <f:facet name="header">
                <h:outputText value="RAZÃO SOCIAL"/>
            </f:facet>
            <a4j:commandLink value="#{nota.razaoSocial}"
                             rendered="#{empty nota.hintNomeAluno}"
                             styleClass="fontListaNotaFiscal corAzulClickNota"
                             style="text-decoration: none"
                             action="#{NotaFiscalControle.abrirCadastroClienteColaborador}"
                             oncomplete="#{NotaFiscalControle.onComplete}"/>

            <a4j:commandLink styleClass="tooltipster"
                             style="text-decoration: none"
                             rendered="#{not empty nota.hintNomeAluno}"
                             title="#{nota.hintNomeAluno}"
                             action="#{NotaFiscalControle.abrirCadastroClienteColaborador}"
                             oncomplete="#{NotaFiscalControle.onComplete}">
                <h:outputText value="#{nota.razaoSocial}"
                              styleClass="fontListaNotaFiscal corAzulClickNota tooltipster"/>
                <i class="fa-icon-info-circle corAzulClickNota tooltipster" style="margin-left: 3px"></i>
            </a4j:commandLink>
        </rich:column>

        <rich:column sortBy="#{nota.statusNotaApresentar}">
            <f:facet name="header">
                <h:outputText value="STATUS"/>
            </f:facet>
            <a4j:commandLink value="#{nota.statusNotaApresentar}"
                             action="#{NotaFiscalControle.selecionarNota}"
                             oncomplete="Richfaces.showModalPanel('modalMotivoNota');"
                             reRender="formMotivo:panelModalMotivoNota"
                             styleClass="tooltipster fontListaNotaFiscal #{nota.statusNotaEnum.css}"
                             style="font-weight: bold; text-transform: uppercase; text-decoration: none;"
                             title="#{nota.statusNotaHint}"/>
        </rich:column>

        <rich:column sortBy="#{nota.valor}">
            <f:facet name="header">
                <h:outputText value="VALOR"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.valorApresentar}"/>
        </rich:column>

        <rich:column sortBy="#{nota.dataEmissao}">
            <f:facet name="header">
                <h:outputText value="DT. EMISSÃO"/>
            </f:facet>

            <h:outputText styleClass="fontListaNotaFiscal"
                          rendered="#{not empty nota.hintNotaRetroativa}">
                <h:outputText value="#{nota.dataEmissaoApresentar}"
                              styleClass="tooltipster"
                              title="#{nota.hintNotaRetroativa}"/>
                <i class="fa-icon-info-circle tooltipster" style="margin-left: 3px"></i>
            </h:outputText>

            <h:outputText styleClass="fontListaNotaFiscal"
                          rendered="#{empty nota.hintNotaRetroativa}"
                          value="#{nota.dataEmissaoApresentar}">
            </h:outputText>
        </rich:column>

        <rich:column sortBy="#{nota.dataAutorizacao}">
            <f:facet name="header">
                <h:outputText value="DT. AUTORIZAÇÃO"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.dataAutorizacaoApresentar}"/>
        </rich:column>

        <rich:column sortBy="#{nota.excluido}"
                     rendered="#{NotaFiscalControle.filtro.apresentarExcluidas}">
            <f:facet name="header">
                <h:outputText value="EXCLUÍDO"/>
            </f:facet>
            <h:outputText styleClass="fontListaNotaFiscal"
                          value="#{nota.excluidoApresentar}"/>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="AÇÕES"/>
            </f:facet>
            <h:panelGroup id="panelAcoes" style="display: inline-flex">

                <a4j:commandLink id="btnVisualizarNotaFiscal"
                                 styleClass="tooltipster fontBtnAcoes"
                                 style="color: #444"
                                 reRender="panelVisualizarNotaFiscal"
                                 action="#{NotaFiscalControle.selecionarNota}"
                                 oncomplete="Richfaces.showModalPanel('modalVisualizarNotaFiscal');"
                                 title="Visualizar informações da nota">
                    <i class="fa-icon-eye-open"></i>
                </a4j:commandLink>

                <a4j:commandLink id="btnReenviar"
                                 styleClass="tooltipster fontBtnAcoes"
                                 style="margin-left: 8px; color: #444"
                                 rendered="#{nota.apresentaReenviar}"
                                 action="#{NotaFiscalControle.selecionarNotaReenviar}"
                                 title="Reenviar Nota"
                                 reRender="panelReenviarNota"
                                 oncomplete="#{NotaFiscalControle.onComplete}">
                    <i class="fa-icon-share-alt"></i>
                </a4j:commandLink>

                <h:outputLink id="btnLinkPDF"
                              styleClass="tooltipster fontBtnAcoes"
                              style="margin-left:8px;"
                              rendered="#{nota.apresentaPDF}"
                              value="#{nota.linkPDF}"
                              title="Download do PDF da nota fiscal"
                              target="_blank">
                    <i class="fa-icon-file-pdf" style="color: #444444;"></i>
                </h:outputLink>

                <h:outputLink id="btnLinkXML"
                              styleClass="tooltipster fontBtnAcoes"
                              rendered="#{nota.apresentaXML}"
                              value="#{nota.linkXML}"
                              style="margin-left:8px;"
                              title="Download do XML da nota fiscal"
                              target="_blank">
                    <i class="fa-icon-file-excel" style="color: #444444;"></i>
                </h:outputLink>

                <a4j:commandLink id="btnCancelarNota"
                                 style="margin-left: 8px; color: darkred"
                                 rendered="#{nota.podeCancelar && LoginControle.permissaoAcessoMenuVO.permiteCancelarNotaFiscal}"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 reRender="panelCancelarNota"
                                 action="#{NotaFiscalControle.selecionarNota}"
                                 title="Solicitar cancelamento da nota"
                                 oncomplete="Richfaces.showModalPanel('modalCancelar');addPlaceHolderNotaFiscal()">
                    <i class="fa-icon-ban-circle" style="color: #444444;"></i>
                </a4j:commandLink>

                <a4j:commandLink id="btnInutilizarNota"
                                 style="margin-left: 8px; color: #777"
                                 rendered="#{nota.podeInutilizar && LoginControle.permissaoAcessoMenuVO.permiteInutilizarNotaFiscal}"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 reRender="panelModalInutilizar"
                                 action="#{NotaFiscalControle.selecionarNota}"
                                 title="Solicitar inutilização da nota"
                                 value="INU"
                                 oncomplete="Richfaces.showModalPanel('modalInutilizar');addPlaceHolderNotaFiscal()">
                    <%--                        <i class="fa-icon-ban-circle"></i>--%>
                </a4j:commandLink>


                <a4j:commandLink id="prepararEnviarEmail"
                                 style="margin-left: 8px; color: #444"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 rendered="#{nota.apresentaPDF}"
                                 reRender="panelEnviarEmailNotaFiscal"
                                 action="#{NotaFiscalControle.prepararEnviarEmail}"
                                 oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete};addPlaceHolderNotaFiscal()"
                                 title="Enviar nota por e-mail">
                    <i class="fa-icon-paper-plane"></i>
                </a4j:commandLink>

                <a4j:commandLink reRender="panelParametrosNota"
                                 rendered="false"
                                 styleClass="tooltipster fontListaNotaFiscal"
                                 style="margin-left: 8px"
                                 title="Visualizar os parâmetros de envio"
                                 actionListener="#{NotaFiscalControle.exibirParams}"
                                 oncomplete="#{NotaFiscalControle.onComplete}">
                    <h:graphicImage id="paramEnvio" style="border:none;" value="images/search_button.png"/>
                    <f:attribute name="params" value="envio"/>
                </a4j:commandLink>

                <a4j:commandLink reRender="panelParametrosNota"
                                 rendered="false"
                                 styleClass="tooltipster fontListaNotaFiscal"
                                 style="margin-left: 8px"
                                 title="Visualizar os parâmetros de retorno"
                                 actionListener="#{NotaFiscalControle.exibirParams}"
                                 oncomplete="#{NotaFiscalControle.onComplete}">
                    <h:graphicImage id="paramRetorno" style="border:none;" value="images/search_button.png"/>
                    <f:attribute name="params" value="retorno"/>
                </a4j:commandLink>

                <a4j:commandLink reRender="panelHistoricoNotaFiscal"
                                 id="btnHistoricoNota"
                                 styleClass="tooltipster fontBtnAcoes"
                                 style="margin-left: 10px; color: #444"
                                 title="Visualizar histórico da nota"
                                 action="#{NotaFiscalControle.visualizarHistoricoNotaFiscal}"
                                 oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}">
                    <i class="fa-icon-history"></i>
                </a4j:commandLink>

                <a4j:commandLink id="btnConsultarEnotas"
                                 style="margin-left: 10px; color: #444"
                                 rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 reRender="panelNotaEnotas"
                                 action="#{NotaFiscalControle.consultarNotaEnotas}"
                                 oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"
                                 title="Consultar situação da nota no eNotas">
                    <i class="fa-icon-refresh"></i>
                </a4j:commandLink>

                <a4j:commandLink id="btnJsonEnvio"
                                 style="margin-left: 10px; color: #444"
                                 rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 reRender="panelModalJsonEnvio"
                                 action="#{NotaFiscalControle.visualizarJsonEnvio}"
                                 oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"
                                 title="Visualizar JSON Envio para eNotas">
                    <i class="fa-icon-file-alt"></i>
                </a4j:commandLink>

            </h:panelGroup>
        </rich:column>
    </rich:dataTable>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                 rendered="#{fn:length(NotaFiscalControle.listaNotas) > 0}">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">

                            <h:outputText styleClass="texto-size-12 texto-cor-cinza"
                                          value="Total #{NotaFiscalControle.listaNotasPaginada.count} itens"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                    reRender="panelTblListaNotas"
                                    actionListener="#{NotaFiscalControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left" id="primPagina"></i>
                            </a4j:commandLink>
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                    reRender="panelTblListaNotas"
                                    actionListener="#{NotaFiscalControle.paginaAnterior}">
                                <i class="fa-icon-angle-left" id="pagAnt"></i>
                            </a4j:commandLink>
                            <h:outputText
                                    styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                    value="#{msg_aplic.prt_msg_pagina} #{NotaFiscalControle.listaNotasPaginada.paginaAtualApresentar}"
                                    rendered="true"/>
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                    reRender="panelTblListaNotas"
                                    actionListener="#{NotaFiscalControle.proximaPagina}">
                                <i class="fa-icon-angle-right" id="proxPag"></i>
                            </a4j:commandLink>
                            <a4j:commandLink
                                    styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                    reRender="panelTblListaNotas"
                                    actionListener="#{NotaFiscalControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right" id="ultimaPagina"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText styleClass="texto-size-12 texto-cor-cinza" value="Itens por página "/>
                            </h:panelGroup>
                            &nbsp;
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{NotaFiscalControle.listaNotasPaginada.limit}"
                                                 style="font-size: 14px !important;" id="qtdeItensPaginaProd">
                                    <f:selectItem itemValue="#{10}"/>
                                    <f:selectItem itemValue="#{50}"/>
                                    <f:selectItem itemValue="#{250}"/>
                                    <f:selectItem itemValue="#{500}"/>
                                    <f:selectItem itemValue="#{1000}"/>
                                    <a4j:support event="onchange"
                                                 actionListener="#{NotaFiscalControle.atualizarNumeroItensPagina}"
                                                 reRender="panelTblListaNotas">
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>

    <h:panelGroup layout="block"
                  id="panelLimparNotasFiscais"
                  style="margin: 0 20px 10px 20px;"
                  rendered="#{fn:length(NotaFiscalControle.listaNotas) > 0}">
        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real tooltipster"
                         reRender="panelTblListaNotas"
                         title="Limpar notas selecionadas"
                         action="#{NotaFiscalControle.limparNotasSelecionadas}">
            <h:outputText styleClass="texto-size-14"
                          rendered="#{fn:length(NotaFiscalControle.notasSelecionadas) == 1}"
                          value="#{fn:length(NotaFiscalControle.notasSelecionadas)} nota selecionada"/>
            <h:outputText styleClass="texto-size-14"
                          rendered="#{fn:length(NotaFiscalControle.notasSelecionadas) > 1}"
                          value="#{fn:length(NotaFiscalControle.notasSelecionadas)} notas selecionadas"/>
        </a4j:commandLink>
    </h:panelGroup>

    <script>
        carregarTooltipsterNotaFiscal();
    </script>
</h:panelGroup>

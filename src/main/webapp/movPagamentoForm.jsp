<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_MovPagamento_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_MovPagamento_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{MovPagamentoControle.movPagamentoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_pessoa}" />                   

                    <%--<h:panelGroup>
                        <h:selectOneMenu  id="pessoa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.pessoa.codigo}" >
                            <f:selectItems  value="#{MovPagamentoControle.listaSelectItemPessoa}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_pessoa" action="#{MovPagamentoControle.montarListaSelectItemPessoa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:pessoa"/>
                        <h:message for="pessoa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup> --%>


                    <h:panelGroup>
                        <h:inputText   readonly="true"  id="Pessoa"  styleClass="camposSomenteLeitura"  value="#{MovPagamentoControle.movPagamentoVO.pessoa.nome}" />
                        <a4j:commandButton id="consultaDadosPessoa" focus="valorConsultaPessoa" alt="Consulta Pessoa" reRender="formPessoa" oncomplete="Richfaces.showModalPanel('panelPessoa'), setFocus(formPessoa,'formPessoa:valorConsultaPessoa');" image="./imagens/informacao.gif" />
                    </h:panelGroup> 

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_dataPagamento}" />
                    <h:panelGroup>
                        <rich:calendar id="dataPagamento" 
                                       value="#{MovPagamentoControle.movPagamentoVO.dataPagamento}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />

                        <%--h:inputText  id="dataPagamento" onchange="return mascara(this.form, 'form:dataPagamento', '99/99/9999', event);"  size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:dataPagamento');"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.dataPagamento}" >
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:inputText--%>
                        <h:message for="dataPagamento" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_dataLancamento}" />
                    <h:panelGroup>
                        <rich:calendar id="dataLancamento"
                                       value="#{MovPagamentoControle.movPagamentoVO.dataLancamento}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <%--h:inputText  id="dataLancamento" onchange="return mascara(this.form, 'form:dataLancamento', '99/99/9999', event);"  size="10" maxlength="10" onblur="blurinput(this);return validar_Data('form:dataLancamento');"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.dataLancamento}" >
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:inputText--%>
                        <h:message for="dataLancamento" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_formaPagamento}" />
                    <h:panelGroup>
                        <h:inputText  id="formaPagamento" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.formaPagamento.descricao}" />
                        <%--<f:selectItems  value="#{MovPagamentoControle.listaSelectItemFormaPagamento}" />
                            <a4j:support event="onchange" focus="formaPagamento" reRender="form" action="#{MovPagamentoControle.apresentaOpcaoPagamento}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_formaPagamento" action="#{MovPagamentoControle.montarListaSelectItemFormaPagamento}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:formaPagamento"/>--%>
                        <h:message for="formaPagamento" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_nomePagador}" />
                    <h:panelGroup>
                        <h:inputText  id="nomePagador"  size="45" maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.nomePagador}" />
                        <h:message for="nomePagador" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>


                    <%--
                        <h:outputText rendered="#{MovPagamentoControle.opcaoPagamentoCheque}" styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_numeroCheque}" />
                        <h:panelGroup rendered="#{MovPagamentoControle.opcaoPagamentoCheque}">
                            <h:inputText  id="numeroCheque"  size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.numeroCheque}" />
                            <h:message for="numeroCheque" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:outputText rendered="#{MovPagamentoControle.opcaoPagamentoCheque}" styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_agenciaCheque}" />
                        <h:panelGroup rendered="#{MovPagamentoControle.opcaoPagamentoCheque}">
                            <h:inputText  id="agenciaCheque"  size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.agenciaCheque}" />
                            <h:message for="agenciaCheque" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:outputText rendered="#{MovPagamentoControle.opcaoPagamentoCheque}" styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_bancoCheque}" />
                        <h:panelGroup rendered="#{MovPagamentoControle.opcaoPagamentoCheque}">
                            <h:inputText  id="bancoCheque"  size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.bancoCheque}" />
                            <h:message for="bancoCheque" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>


                        <h:outputText rendered="#{MovPagamentoControle.opcaoPagamentoCartao}" styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_numeroCartao}" />
                        <h:panelGroup rendered="#{MovPagamentoControle.opcaoPagamentoCartao}">
                            <h:inputText  id="numeroCartao"  size="15" maxlength="15" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.numeroCartao}" />
                            <h:message for="numeroCartao" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:outputText rendered="#{MovPagamentoControle.opcaoPagamentoCartao}" styleClass="tituloCampos" value="#{msg_aplic.prt_MovPagamento_codigoOperacaoCartao}" />
                        <h:panelGroup rendered="#{MovPagamentoControle.opcaoPagamentoCartao}">
                            <h:inputText  id="codigoOperacaoCartao"  size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovPagamentoControle.movPagamentoVO.codigoOperacaoCartao}" />
                            <h:message for="codigoOperacaoCartao" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>--%>

                </h:panelGrid>               
                <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText  value="#{msg_aplic.prt_MovParcela_tituloForm}"/>
                    </f:facet>

                    <h:dataTable id="pagamentoMovParcelaVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                 rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                 value="#{MovPagamentoControle.movPagamentoVO.pagamentoMovParcelaVOs}" var="pagamentoMovParcela">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_PagamentoMovParcela_codigo}" />
                            </f:facet>
                            <h:outputText  value="#{pagamentoMovParcela.movParcela.codigo}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_PagamentoMovParcela_descricao}" />
                            </f:facet>
                            <h:outputText  value="#{pagamentoMovParcela.movParcela.descricao}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_PagamentoMovParcela_valorParcela}" />
                            </f:facet>
                            <h:outputText  value="#{pagamentoMovParcela.movParcela.valorParcela}" >
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_PagamentoMovParcela_situacao}" />
                            </f:facet>
                            <h:outputText  value="#{pagamentoMovParcela.movParcela.situacao_Apresentar}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_PagamentoMovParcela_valorPago}" />
                            </f:facet>
                            <h:outputText  value="#{pagamentoMovParcela.valorPago}" >
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                            </f:facet>
                            <h:panelGroup>
                                <h:commandButton id="editarItemVenda" immediate="true" action="#{MovPagamentoControle.editarPagamentoMovParcela}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                <h:outputText value="    "/>

                                <h:commandButton id="removerItemVenda" immediate="true" action="#{MovPagamentoControle.removerPagamentoMovParcela}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                            </h:panelGroup>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{MovPagamentoControle.sucesso}"image="./imagens/sucesso.png"/>                        
                        <h:commandButton rendered="#{MovPagamentoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{MovPagamentoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{MovPagamentoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>                           
                            <h:commandButton id="consultar" immediate="true" action="#{MovPagamentoControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>
<script>
    document.getElementById("form:pessoa").focus();
</script>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="includes/imports.jsp" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
    .select-uniform {
        width: 250px !important;
    }

</style>
<h:panelGroup layout="block" id="panelGeralImportacaoContratos">

    <h:panelGroup layout="block" styleClass="panelObjetivo">
        <h:outputLabel value="Objetivo:" styleClass="textoObjetivo"
                       style="font-weight: bold; font-style: italic;"/>
        <h:outputLabel styleClass="textoObjetivo"
                       value=" Realizar importação de contratos através de uma planilha modelo disponibilizada para download."/>
    </h:panelGroup>

    <h:panelGroup layout="block" style="padding: 15px;">

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="1º passo:" styleClass="passosImportacao"/>
            <a4j:commandLink target="_blank"
                             style="padding-left: 5px; font-size: 15px;"
                             value="Baixar planilha modelo"
                             oncomplete="location.href='../DownloadSV?mimeType=application/vnd.ms-excel&diretorio=modelo&relatorio=modelo_importacao_contrato.xlsx'"/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="2º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Informe as configurações padrão:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <br/>
            <h:outputLabel styleClass="passosImportacaoDescricao" style="padding-top: 10px" value="Se as informações não forem preenchidas na planilha, os valores definidos aqui serão aplicados automaticamente."/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno"
                          style="display: inline-flex; width: 100%"
                          id="panelConfiguracoesImportacaoContratos">
                <h:panelGrid columns="2" width="100%"
                             columnClasses="colunaEsquerdaImport, colunaDireitaImport">

                    <h:outputLabel value="Plano:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:selectOneMenu id="plano" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster select-uniform"
                                     value="#{ImportacaoControle.configClienteTO.plano}">
                        <f:selectItems value="#{ImportacaoControle.listaPlano}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Modalidade:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:selectOneMenu id="modalidade" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster select-uniform"
                                     title="São apresentadas somente modalidades que não utilizam turma."
                                     value="#{ImportacaoControle.configClienteTO.modalidade}">
                        <f:selectItems value="#{ImportacaoControle.listaModalidade}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Horário:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:selectOneMenu id="horario" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster select-uniform"
                                     value="#{ImportacaoControle.configClienteTO.horario}">
                        <f:selectItems value="#{ImportacaoControle.listaHorarios}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>
                    <h:outputLabel value="Gerar Parcelas Pagas:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:selectOneMenu id="importacaoParcelasSituacaoEnum" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster select-uniform"
                                     value="#{ImportacaoControle.configClienteTO.importacaoParcelasSituacaoEnum}">
                        <f:selectItems value="#{ImportacaoControle.listaImportacaoParcelasSituacao}"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Máscara campos de data:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:selectOneMenu id="mascaraDataContrato" onblur="blurinput(this);"
                                     style="margin-top: 1rem"
                                     onfocus="focusinput(this);" styleClass="form tooltipster select-uniform"
                                     value="#{ImportacaoControle.configClienteTO.mascaraDataEnum}">
                        <f:selectItems value="#{ImportacaoControle.listaSelectItemMascaraData}"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Dias Carência:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:inputText id="diasCarencia" styleClass="form tooltipster"
                                 title="Número de dias de carência"
                                 maxlength="2" size="2"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.configClienteTO.diasCarencia}"/>


                    <!--

                    <h:outputLabel value="Gerar parcelas de acordo com a duração:"
                                   styleClass="passosImportacaoDescricao tooltipster"/>
                    <h:selectBooleanCheckbox id="gerarParcelasDeAcordoDuracao"
                                             styleClass="form tooltipster"
                                             style="padding-left: 5px"
                                             title="Será gerado uma parcela para cada mês do contrato. Caso não marcado, será gerada somente uma parcela com o valor total do plano."
                                             value="#{ImportacaoControle.configClienteTO.gerarParcelasDeAcordoDuracao}"/>

                    -->
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos" id="panelPasso3Contrato">
            <h:outputLabel value="3º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Preencher a planilha seguindo as seguintes regras:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno" style="font-size: 15px;">
                <ul style="padding: 5px;margin: 0;">
                    <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Campos Obrigatórios para importar o contrato: <b>MATRICULA, CODIGO_EMPRESA, DT_INICIO_CONTRATO, DT_FIM_CONTRATO.</b></li>
                    <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px">Campo <b>HORARIO_TURMA</b>: deve ser informado nesse padrão: 00:00 - 00:00 (Hora inicial e hora final separados por hífen. Ex: 08:00 - 09:30)</li>
                    <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px">Campo <b>DIAS_TURMA</b>: deve ser informado nesse padrão: SG.TR.QU.QI.SX.SB (Dias da semana separados por ponto. Ex: SG.QU.SX = segunda, quarta e sexta)</li>
                </ul>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="4º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Fazer o Upload da planilha baixada e realizar a importação:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelUploadFileContrato" style="padding-top: 5px; padding-left: 30px">
            <rich:fileUpload
                    fileUploadListener="#{ImportacaoControle.uploadArquivoContrato}"
                    immediateUpload="true" id="arquivoContratoUpload"
                    acceptedTypes="xls,xlsx" allowFlash="false"
                    listHeight="58px"
                    cancelEntryControlLabel="Cancelar"
                    addControlLabel="Adicionar"
                    clearControlLabel="Remover"
                    clearAllControlLabel="Remover Todos"
                    doneLabel="Concluído"
                    sizeErrorLabel="Limite de tamanho atingido"
                    uploadControlLabel="Carregar"
                    transferErrorLabel="Erro na transferência"
                    stopControlLabel="Parar"
                    stopEntryControlLabel="Parar"
                    progressLabel="Carregando"
                    maxFilesQuantity="1">
                <a4j:support event="onerror" reRender="panelBotoesImportacaoContrato"
                             action="#{ImportacaoControle.removerArquivo}"/>
                <a4j:support event="onupload" reRender="panelBotoesImportacaoContrato"/>
                <a4j:support event="onuploadcomplete" reRender="panelBotoesImportacaoContrato"/>
                <a4j:support event="onclear" reRender="panelBotoesImportacaoContrato, panelUploadFileContrato"
                             action="#{ImportacaoControle.removerArquivo}"/>
            </rich:fileUpload>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" id="panelBotoesImportacaoContrato"
                  styleClass="panelBotoesImportacao">
        <a4j:commandLink id="btnImportarContrato" value="Ler Arquivo"
                         style="padding-left: 15px"
                         onclick="atualizarTempoImportacao()"
                         rendered="#{ImportacaoControle.apresentarImportar}"
                         action="#{ImportacaoControle.processarArquivoContrato}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         title="Processar Importação de Dados"
                         reRender="panelGeralModalConfirmarImportacao, formModImpo"
                         styleClass="botoes nvoBt"/>
    </h:panelGroup>
</h:panelGroup>

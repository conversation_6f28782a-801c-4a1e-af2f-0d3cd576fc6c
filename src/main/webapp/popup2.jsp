<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
    <jsp:include page="include_head.jsp" flush="true" />
    <body>
        <script type="text/javascript" language="javascript" src="hoverform.js"></script>
        <script type="text/javascript" language="javascript" src="mostraesconde.js"></script>
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td align="center" valign="top" bgcolor="#ffffff">
                    <table width="500" border="0" cellpadding="0" cellspacing="0" style="padding:10px;">
                        <tr>
                            <td align="left" valign="top">
                                <!-- inicio bot�es -->
                                <form class="nomargin">
                                    <div style="clear:both;margin-bottom:15px;">
                                        <label>
                                            <input onClick="location='javascript:window.close();'" type="button" name="Submit" value="Confirmar">
                                        </label>
                                        <label></label>
                                        <label></label>
                                        <span class="text" style="clear:both;">
                                            <input onClick="location='javascript:window.close();'" type="button" name="Submit2" value="Cancelar">
                                        </span></div>
                                </form>
                                <!-- fim bot�es -->
                                <!-- inicio item -->
                                <div style="clear:both;" class="text">
                                    <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Editar Familiares</p>
                                </div>
                                <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">

                                    <tr>
                                        <td width="25%" height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Nome do Familiar:</span></td>
                                        <td align="left" valign="middle"><input name="textfield2" type="text" class="form" style="width:315px;" onFocus="focusinput(this);" onBlur="blurinput(this);"></td>
                                    </tr>

                                    <tr>
                                        <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Parentesco:</span> </td>
                                        <td align="left" valign="middle"><select onBlur="blurinput(this);" onFocus="focusinput(this);" style="vertical-align:middle;" class="form2" name="">
                                                <option>Selecione:</option>
                                                <option>M�e</option>
                                                <option>Pai</option>
                                                <option>Irm�o(�)</option>
                                                <option>Tio(a)</option>
                                                <option>Primo(a)</option>
                                                <option>Av�(�)</option>
                                            </select>
                                            <a href="javascript:;"><img border="0" style="margin-left:4px;vertical-align:middle;" src="images/icon_add.gif" title="Adicionar" width="12" height="13"></a></td>
                                    </tr>
                                    <tr>
                                        <td height="27" align="right" valign="middle">&nbsp;</td>
                                        <td align="left" valign="middle"><input onClick="mostraEsconde('novoParente');" type="button" name="Submit3" value="Adicionar"></td>
                                    </tr>
                                </table>
                                <div class="sep" style="margin:10px 0;"><img src="images/shim.gif"></div>
                                <!-- fim item -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">
                                    <tr class="impar">
                                        <td height="27" align="center" valign="middle">Nome</td>
                                        <td width="120" align="center" valign="middle"><span class="titulo4">Parentesco</span></td>
                                        <td width="50" align="center" valign="middle"><span class="titulo5">Excluir</span></td>
                                    </tr>
                                    <tr id="parente1">
                                        <td height="27" align="left" valign="middle"><span class="text" style="font-weight: bold">Jack Bauer da Silva </span></td>
                                        <td align="center" valign="middle"><span class="titulo4">Pai</span></td>
                                        <td align="center" valign="middle"><a href="javascript:mostraEsconde('parente1');"><img src="images/icon_delete.png" alt="Excluir" border="0"></a></td>
                                    </tr>
                                    <tr id="parente2">
                                        <td height="27" align="left" valign="middle"><span class="text" style="font-weight: bold">Jeff Mcquillan Gordon Brown </span></td>
                                        <td align="center" valign="middle"><span class="titulo4">Irm&atilde;o(&atilde;)</span></td>
                                        <td align="center" valign="middle"><a href="javascript:mostraEsconde('parente2');"><img src="images/icon_delete.png" alt="Excluir" border="0"></a></td>
                                    </tr>

                                    <tr id="parente3">
                                        <td height="27" align="left" valign="middle"><span style="font-weight: bold"><span class="text" style="font-weight: bold">Camila Souza Silva Santos</span></span></td>
                                        <td align="center" valign="middle"><span class="titulo4">Av&ocirc;(&oacute;)</span></td>
                                        <td align="center" valign="middle"><a href="javascript:mostraEsconde('parente3');"><img src="images/icon_delete.png" alt="Excluir" border="0"></a></td>
                                    </tr>
                                    <tr id="parente4">
                                        <td height="27" align="left" valign="middle"><span class="text" style="font-weight: bold">Maria Jos&eacute; da Silva</span></td>
                                        <td align="center" valign="middle"><span class="titulo4">Pai</span></td>
                                        <td align="center" valign="middle"><a href="javascript:mostraEsconde('parente4');"><img src="images/icon_delete.png" alt="Excluir" border="0"></a></td>
                                    </tr>
                                    <tr id="parente5">
                                        <td height="27" align="left" valign="middle"><span class="text" style="font-weight: bold">Maria Auxiliadora da Silva</span></td>
                                        <td align="center" valign="middle"><span class="titulo4">Irm&atilde;o(&atilde;)</span></td>
                                        <td align="center" valign="middle"><a href="javascript:mostraEsconde('parente5');"><img src="images/icon_delete.png" alt="Excluir" border="0"></a></td>
                                    </tr>

                                    <tr id="parente6">
                                        <td height="27" align="left" valign="middle"><span style="font-weight: bold"><span class="text" style="font-weight: bold">Maria L&uacute;cia da Silva</span></span></td>
                                        <td align="center" valign="middle"><span class="titulo4">Av&ocirc;(&oacute;)</span></td>
                                        <td align="center" valign="middle"><a href="javascript:mostraEsconde('parente6');"><img src="images/icon_delete.png" alt="Excluir" border="0"></a></td>
                                    </tr>
                                    <tr id="parente7">
                                        <td height="27" align="left" valign="middle"><span style="font-weight: bold"><span class="text" style="font-weight: bold">Maria Aparecida da Silva</span></span></td>
                                        <td align="center" valign="middle"><span class="titulo4">Av&ocirc;(&oacute;)</span></td>
                                        <td align="center" valign="middle"><a href="javascript:mostraEsconde('parente7');"><img src="images/icon_delete.png" alt="Excluir" border="0"></a></td>
                                    </tr>
                                    <tr id="novoParente" style="display:none;">
                                        <td height="27" align="left" valign="middle"><span class="text" style="font-weight: bold">Nome do novo parente adicionado</span></td>
                                        <td align="center" valign="middle"><span class="titulo4">Av&ocirc;(&oacute;)</span></td>
                                        <td align="center" valign="middle"><a href="javascript:mostraEsconde('novoParente');"><img src="images/icon_delete.png" alt="Excluir" border="0"></a></td>
                                    </tr>
                                </table>
                                <!-- inicio bot�es -->
                                <form class="nomargin">
                                    <div style="clear:both;margin-bottom:5px;">
                                        <label>
                                            <input onClick="location='javascript:window.close();'" type="button" name="Submit" value="Confirmar">
                                        </label>
                                        <label></label>
                                        <label>
                                            <input onClick="javascript:window.close();" type="button" name="Submit" value="Cancelar">
                                        </label>
                                    </div>
                                </form>
                                <!-- fim bot�es -->
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
</html>

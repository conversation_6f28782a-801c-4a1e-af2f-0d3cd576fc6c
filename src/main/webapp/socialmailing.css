
body { font: 1em "Trebuchet MS", verdana, arial, sans-serif; font-size: 100%; }

label { display: block; }

.infiniteCarousel {
    width: 310px;
    position: relative;
}

.infiniteCarousel .wrapper {
    width: 310px; /* .infiniteCarousel width - (.wrapper margin-left + .wrapper margin-right) */
    overflow: auto;
    min-height: 10em;
    margin: 0 40px;
    position: absolute;


}



.infiniteCarousel .wrapper ul {
    width: 9999px;
    list-style-image:none;
    list-style-position:outside;
    list-style-type:none;
    margin:0 0px;
    padding:0;
    position: absolute;
    top: 0;
}

.infiniteCarousel ul li {
    display:block;
    float:left;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 7px;
    height: 20px;
    width: 20px;
}

.infiniteCarousel ul li a img {
    display:block;
}

.infiniteCarousel .arrow {
    display: block;
    height: 18px;
    width: 18px;
    text-indent: -999px;
    position: absolute;
    top: 20px;
    cursor: pointer;
    background-image: transparent url('images/socialmail/but-carrossel-dir-18px.png');

}

.infiniteCarousel .forward {
    right: 10;
    background: transparent url('images/socialmail/but-carrossel-dir-18px.png') no-repeat 0 -42;

}

.infiniteCarousel .back {
    background: transparent url('images/socialmail/but-carrossel-esq-18px.png') no-repeat 0 -42;
    left: 15;
}

.infiniteCarousel .forward:hover {
    background-position: 0 -21px;
}

.infiniteCarousel .back:hover {
    background-position: 0 -21px;
}

html, body {
    height: 100%;
    padding: 0;
    margin: 0;
}

.conversa{
    background-color:#FFFFFF;
    overflow-x:hidden;
    overflow-y:auto;
    width: 100%;
    resize:both;
    float: inherit;
    height: 220px;
}

.textoNormal{
    font-weight:bold;font-size: 10pt; font-family: 'Trebuchet MS', verdana; color: #2372a3;
}


/* ---------------------------------refactor----------------------------------------------------- */
::-webkit-scrollbar{width:8px;height:6px;margin-right:2px;}
::-webkit-scrollbar-button:start:decrement,::-webkit-scrollbar-button:end:increment{display:block;height:0px;}
::-webkit-scrollbar-button:vertical:end:increment{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-button:vertical:increment{background-color:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-bottom:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-track-piece{background:#FCF9F9;border-right:1px solid #ccc;border-left:1px solid #ccc;border-top:1px solid #ccc;}
::-webkit-scrollbar-thumb:vertical{background-color:#ccc;}
::-webkit-scrollbar-thumb:vertical:hover{background-color:#666;}
::-webkit-scrollbar-thumb:vertical:active{background-color:#333;}



.caixaCentralizada {
    /*border: 2px solid gray;*/
    background-color:#ffffff;
    height:600px;
    margin:5px 0 0 10px;
    position:absolute;
    width:700px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
    /*behavior: url(./socialmailing/border-radius.htc);*/
}
.caixaTop{
    position:absolute;
    width: 100%;
    height: 5%;
    margin-left: 2.5%;
    margin-top: 1%;
}
.caixaMensagens{
    border: 1px solid #999999;
    /*border-left-width: medium;*/
    position:absolute;
    width: 64.6%;
    height: 75%;
    margin-left: 2.5%;
    margin-top: 7.7%;
}
.caixaPesquisa{
    position:absolute;
    margin-left: 68%;
    margin-top: 7.7%;
}
.caixaContatos{
    /*border: thin solid gray; */
    position:absolute;
    width: 30%;
    height: 498px;
    margin-left: 68%;
    margin-top: 82px;
    overflow-x:hidden;
    overflow-y:auto;
}
.caixaEscrever{
    position:absolute;
    width: 456px;
    height: 70px;
    margin-left: 2.5%;
    margin-top: 513px;
}
.campoEscrever{
    border: 1px solid #999999;
    /*border-left-width: 2px;*/
    /*border-top-width: 2px;*/
    resize:none;
    width: 100%;
    height: 67px;
}
.campoBuscar{
    border: 1px solid #3a7dc4 !important;
    /*border-left-width: 2px !important;*/
    /*border-top-width: 2px !important;*/
    padding-left:10px;
    font-family: Helvetica, Arial,sans-serif !important;
    font-size: 12px;
    color: #333 !important;
}
.tituloGeral{
    font-family: Helvetica, Arial,sans-serif;
    font-size: 21px;
    font-style: normal;
    color: #333;
    background-color: transparent;
}


#thumbs-wrapper {
    background-color: #3a7dc4;
    position:absolute;
    width: 100%;
    height: 65px;
    overflow: hidden;
}

.caixaAdd{
    margin-left: 85%;
    background-color: #d0d0d0;
    position: absolute;
    width: 15%;
    height: 45px;
}
.addContato{
    background-color: #FFFFFF;
    margin-top: 14%;
    margin-left: 28%;
    width: 65%;
    position: absolute;
    min-height: 12%;
    max-height:80%;
    display:none;
    overflow: auto;
}

.caixaConversas{
    position: absolute;
    width: 100%;
    margin-top: 50px;
    bottom: 0;
    height: 400px;
    overflow: auto;
    z-index: 1;
}
.botaoEnviar{
    border: none !important;
    background: url("./images/socialmail/send-normal.png") no-repeat 0 0  !important;
    height: 67px;
    width: 100px;
}
#botaoFechar{
    background: url("./images/socialmail/but-fechar-18px.png") no-repeat 0 0;
    height: 18px;
    width: 18px;
}
.botaoFecharCons{
    border: none !important;
    background: url("./images/socialmail/fecharcinza.png") no-repeat 0 0 !important;
    height: 18px;
    width: 18px;
}
.botaoAddC{
    border: none !important;
    background: url("./images/socialmail/addc.png") no-repeat 0 0 !important;
    height: 18px;
    width: 18px;

}

.botaoFecharCons:active{
    border: none !important;
    background: url("./images/socialmail/fecharcinza.png") no-repeat 0 100% !important;
    height: 18px;
    width: 18px;
}
#botaoFechar:active{
    background: url("./images/socialmail/but-fechar-18px.png") no-repeat 0 100%;
    height: 18px;
    width: 18px;
}
.botaoAddC:active{
    border: none !important;
    background: url("./images/socialmail/addc.png") no-repeat 0 100% !important;
    height: 18px;
    width: 18px;
}
.botaoEnviar:hover{
    border: none !important;
    background: url("./images/socialmail/send-active.png") no-repeat 0 100%  !important;
    height: 67px;
    width: 100px;
}

.fundo{
    /*background-image:url('./images/socialmail/pattern-bg.png');*/
    background-repeat:repeat;
}
.linha{
    width: 454px;
    position:absolute;
    margin-left: 2.5%;
    margin-top: 505px;
}

.caixaTituloContatos{
    background-color: #3a7dc4;
    /*width: 190px;*/
    height: 25px;
    padding-left: 10px;

}
.caixaTituloContatosPesquisa{
    background: #a09fa0;
    /*width: 190px;*/
    height: 25px;
    padding-left: 10px;

}
.tituloContatosGrupos{
    font-family: Helvetica, Arial,sans-serif;
    font-size: 12px;
    font-weight: bold;
    color: #FFF;
}
.nomeContatosGrupos{
    font-family: Helvetica, Arial,sans-serif;
    font-size: 10px;
    font-style:normal;
    color: #333;
}
.shadow {
    box-shadow: 1px 2px 6px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 1px 2px 6px rgba(0, 0, 0, 0.5);
    -webkit-box-shadow: 1px 2px 6px rgba(0, 0, 0, 0.5);
}
.shadowinterna {
    /* sombra interna */
    -moz-box-shadow:inset 0 0 5px #000000;
    -webkit-box-shadow:inset 0 0 5px #000000;
    box-shadow:inset 0 0 5px #000000;
    /* termina sombra interna*/
}
.rich-table-row:hover {
    background-color: #EEEEEE;
}

.row-selected {
    background-color: #2a496d;
    color: #FFFFFF;
    font-weight: bold;
}
.row-selected {
    background-color: #2a496d;
    color: #FFFFFF;
    font-weight: bold;
}
.eu{
    font-family: Helvetica, Arial,sans-serif;
    font-size: 12px;
    font-style:normal;
    font-weight: bold;
    color: #013e6a;
}
.participante{
    font-family: Helvetica, Arial,sans-serif;
    font-size: 12px;
    font-style:normal;
    font-weight: bold;
    color: #c75236;
}
.dataEnviada{
    font-family: Helvetica, Arial,sans-serif;
    font-size: 10px;
    font-style: normal;
    color: #999;
}
.colunaFoto{
    width: 10%;
}
.colunaTexto{
    width: 90%;
}

.selected{
    background-color: #2a496d;
    font-family: Helvetica, Arial,sans-serif !important;
    font-size: 10px !important;
    font-style: normal !important;
    font-weight: bold !important;
    color: #FFFFFF !important;
}
.notselected{
    font-family: Helvetica, Arial,sans-serif !important;
    font-size: 10px !important;
    font-style: normal !important;
    font-weight: bold !important;
    color: #333 !important;
}
td.coluna1 {
    text-align: left;
    vertical-align: middle;
    width: 20%;
}
td.coluna2 {
    text-align: left;
    vertical-align: middle;
    width: 80%;
}
.fotos{
    margin-left:30px;
    margin-right: 30px;
    margin-top: 10px;
    position:absolute;
}

.prev {
    background: transparent url('.images/socialmail/but-carrossel-esq-18px.png') no-repeat 0 0;
    display: block;
    width: 18px;
    height: 18px;
    margin-top: -10px;
    position: absolute;
    top: 50%;
}

.next {
    background: transparent url('images/socialmail/but-carrossel-dir-18px.png') no-repeat 0 0;
    display: block;
    width: 18px;
    height: 18px;
    margin-top: -10px;
    position: absolute;
    top: 50%;
}
.botaoMore{
    padding: 6px 10px !important;
    -webkit-border-radius: 2px 2px !important;
    border: solid 1px rgb(153, 153, 153) !important;
    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(rgb(255, 255, 255)), to(rgb(221, 221, 221))) !important;
    color: #333 !important;
    text-decoration: none !important;
    cursor: pointer !important;
    display: inline-block !important;
    text-align: center !important;
    text-shadow: 0px 1px 1px rgba(255,255,255,1) !important;
    line-height: 1 !important;
}

.ulSimples{
    width: 9999px;
    list-style-image:none;
    list-style-position:outside;
    list-style-type:none;
    margin:0 0px;
    padding:10px;
    position: absolute;
    top: 0;
}
.ulSimples li {
    display:block;
    float:left;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 7px;
    height: 20px;
    width: 20px;
}

.ulSimples li a img {
    display:block;

}
.ulSimples li a {

    padding-left: 10px;
}

.semBorda td{
    border: none !important;
}

.panelSuperiorSocial {
    background-color: #d0d0d0;
    position: absolute;
    width: 100%;
    height: 45px;
}

.caixaConfiguracoesSocial {
    background-color: #d0d0d0;
    height: 45px;
}

.styleFotoUsuarioSocial {
    left: 0px;
    width: 40px;
    height: 40px;
    border-radius: 100%;
    margin: 5px 5px 5px 0px;
    box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.3);
    -webkit-box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.3);
}

.styleFotoUsuarioSocialSuperior {
    left: 0px;
    width: 35px;
    height: 35px;
    border-radius: 100%;
    margin: 5px 5px 5px 0px;
    box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.3);
    -webkit-box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.3);
}

.caixaContatosView {
    /*border: thin solid gray; */
    position:absolute;
    width: 30%;
    height: 498px;
    margin-left: 68%;
    margin-top: 55px;
    overflow-x:hidden;
    overflow-y:auto;
}
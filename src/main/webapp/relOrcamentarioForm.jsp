<%@page pageEncoding="ISO-8859-1" %>
<%@include file="pages/finan/includes/include_imports.jsp" %>
<%@include file="pages/finan/includes/include_head_finan.jsp" %>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Relatório Orçamentário" />
    </title>
    <body style="background-color:#FFFFFF;">
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">
                <!-- FORMULARIO -->
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" 
                             columnClasses="colunaD<PERSON>ita, colunaEs<PERSON>da, colunaEsquerda" width="100%">

                    <h:panelGroup >
                        <!-- Empresa -->
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa}"
                            rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas}"
                        />
                    </h:panelGroup >
                    <h:panelGroup >
                        <h:selectOneMenu rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas}"
                                         id="selectEmpresaMetaFinanceira"
                                         styleClass="form" value="#{RelatorioOrcamentarioConfigControle.previsao.empresa.codigo}"
                        >
                            <f:selectItems value="#{RelatorioOrcamentarioConfigControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <!-- mes e ano -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_mes}" />
                    <h:panelGrid columns="3" columnClasses="colunaEsquerda, colunaDireita, colunaEsquerda" cellpadding="0" cellspacing="0">
                        <h:selectOneMenu id="mes" styleClass="form" value="#{RelatorioOrcamentarioConfigControle.previsao.mes}">
                            <f:selectItems value="#{RelatorioOrcamentarioConfigControle.listaSelectItemMeses}" />
                        </h:selectOneMenu>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_ano}" />
                        <h:inputText id="ano" styleClass="form" size="5" value="#{RelatorioOrcamentarioConfigControle.previsao.ano}"/>
                    </h:panelGrid>

                    <!-- descricao -->
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_descricao}"/>
                    <h:inputText id="descricao" styleClass="form" value="#{RelatorioOrcamentarioConfigControle.previsao.descricao}" size="70"/>

                    <h:outputLabel value="Planilha Modelo" styleClass="tituloCampos"/>
                    <a4j:commandLink target="_blank"
                                     style="padding-left: 5px; font-size: 15px;"
                                     value="Baixar planilha modelo para configuração"
                                     action="#{RelatorioOrcamentarioConfigControle.gerarPlanilhaExcelPlanoDeContas}"
                                     oncomplete="location.href='#{RelatorioOrcamentarioConfigControle.urlPlanilhaRelOrcamentarioConfig}'"/>
                    <h:outputText value="Upload planilha" styleClass="tituloCampos"/>
                    <h:panelGroup layout="block" id="panelUploadPlanilhaConfiguracao">
                        <rich:fileUpload
                                fileUploadListener="#{RelatorioOrcamentarioConfigControle.uploadPlanilhaConfiguracao}"
                                immediateUpload="true" id="ModeloUpload"
                                acceptedTypes="xls,xlsx" allowFlash="false"
                                listHeight="58px"
                                cancelEntryControlLabel="Cancelar"
                                addControlLabel="Adicionar"
                                clearControlLabel="Remover"
                                clearAllControlLabel="Remover Todos"
                                doneLabel="Concluído"
                                sizeErrorLabel="Limite de tamanho atingido"
                                uploadControlLabel="Carregar"
                                transferErrorLabel="Erro na transferência"
                                stopControlLabel="Parar"
                                stopEntryControlLabel="Parar"
                                progressLabel="Carregando"
                                maxFilesQuantity="1">
                            <a4j:support event="onerror" reRender="pnl"
                                         action="#{RelatorioOrcamentarioConfigControle.removerPlanihaConfiguracao}"/>
                            <a4j:support event="onupload" reRender="pnlAplicarValoresPlanilha"/>
                            <a4j:support event="onuploadcomplete" reRender="pnlLerPlanilha"/>
                            <a4j:support event="onclear" reRender="panelUploadPlanilhaConfiguracao"
                                         action="#{RelatorioOrcamentarioConfigControle.removerPlanihaConfiguracao}"/>
                        </rich:fileUpload>
                    </h:panelGroup>

                    <h:outputText></h:outputText>
                    <h:panelGroup layout="block" id="pnlLerPlanilha" style="margin-top: 10px">
                        <a4j:commandLink id="btnImportar" value="Aplicar valores da planilha"
                                         rendered="#{RelatorioOrcamentarioConfigControle.apresetarBotaoLerPlanilhaConfiguracao}"
                                         action="#{RelatorioOrcamentarioConfigControle.processarPlanilhaConfiguracao}"
                                         oncomplete="#{RelatorioOrcamentarioConfigControle.onComplete};#{RelatorioOrcamentarioConfigControle.mensagemNotificar}"
                                         title="Ler valores planilha"
                                         reRender="dataTablePlanoDeContas, panelUploadPlanilhaConfiguracao, pnlLerPlanilha"
                                         styleClass="botoes nvoBt"/>
                    </h:panelGroup>
                </h:panelGrid>

                <!-- PLANO DE CONTAS -->
                <rich:spacer width="10px" height="10px"/>
                <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="centralizado" >
                    <f:facet name="header">
                        <h:outputText value="Previsão por Plano de Contas"/>
                    </f:facet>

                    <h:panelGrid id="panelPlanoDeContas" columns="2" columnClasses="colunaDireita, colunaEsquerda"   width="100%" rowClasses="linhaImpar">
                        <h:dataTable id="dataTablePlanoDeContas" value="#{RelatorioOrcamentarioConfigControle.previsao.valores}" var="previsao"
                                     width="100%" headerClass="subordinado" rowClasses="linhaImpar"
                                     columnClasses="colunaEsquerda, colunaDireita, colunaDireita, colunaDireita, colunaDireita, colunaDireita, centralizado">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Plano de Contas"/>
                                </f:facet>
                                <h:outputText styleClass="tituloCampos" value="#{previsao.planoContaTO.codigoPlano} - #{previsao.planoContaTO.descricao}"/>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Valor Previsão"/>
                                </f:facet>
                                <h:inputText rendered="#{previsao.planoContaTO.codigoPlano.contains('.')}" size="10" maxlength="40" onfocus="focusinput(this);" style="text-align: right;"
                                             onkeyup="return moeda(this);"
                                             styleClass="form" value="#{previsao.valor}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <!-- mensagens -->
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:commandButton rendered="#{RelatorioOrcamentarioConfigControle.sucesso}"
                                         image="imagens/sucesso.png" />
                        <h:commandButton rendered="#{RelatorioOrcamentarioConfigControle.erro}"
                                         image="imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{RelatorioOrcamentarioConfigControle.mensagem}" />
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{RelatorioOrcamentarioConfigControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>

                <!-- botões -->
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="salvar" action="#{RelatorioOrcamentarioConfigControle.gravar}"
                                           alt="#{msg.msg_gravar_dados}"
                                           value="#{msg_bt.btn_gravar}"
                                           styleClass="botoes nvoBt" reRender="form" />
                        <rich:spacer width="10px"/>
                        <h:commandButton rendered="#{RelatorioOrcamentarioConfigControle.previsao.codigo > 0}" id="excluir" onclick="return confirm('Confirma exclusão desta meta?');"
                                         action="#{RelatorioOrcamentarioConfigControle.excluir}"
                                         value="#{msg_bt.btn_excluir}"
                                         alt="#{msg.msg_excluir_dados}"
                                         styleClass="botoes nvoBt btSec btPerigo" />
                        <rich:spacer width="10px"/>
                        <h:commandButton id="consultar" immediate="true"
                                         action="#{RelatorioOrcamentarioConfigControle.voltarTelaConsulta}"
                                         value="#{msg_bt.btn_voltar_lista}"
                                         alt="#{msg.msg_consultar_dados}"
                                         styleClass="botoes nvoBt btSec" />
                        <rich:spacer width="10px"/>
                    </h:panelGroup>
                </h:panelGrid>

            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    </body>

    <script>
        // Horizontal, Vertical
        window.resizeTo(600, 600);
    </script>

</f:view>

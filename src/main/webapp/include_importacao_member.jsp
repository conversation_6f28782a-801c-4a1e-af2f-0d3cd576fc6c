<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="includes/imports.jsp" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
</style>
<h:panelGroup layout="block" id="panelGeralPosImportacaoMember">
    <h:panelGroup layout="block" style="padding: 15px;">

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="Configurações:" styleClass="passosImportacao"/>
            <br/>

            <h:panelGroup layout="block" styleClass="panelPassosInterno"
                          style="display: inline-flex; width: 100%"
                          id="panelConfiguracoesImportacaoMember">
                <h:panelGrid columns="2" width="100%"
                             columnClasses="colunaEsquerdaImport, colunaDireitaImport">

                    <h:outputLabel value="DNS:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:inputText id="integracaoMemberDns" styleClass="form tooltipster"
                                 title="DNS"
                                 maxlength="100" size="40"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.integracaoMember.username}"/>

                    <h:outputLabel value="Token:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:inputText id="integracaoMemberToken" styleClass="form tooltipster"
                                 title="Token"
                                 maxlength="255" size="40"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.integracaoMember.token}"/>

                    <h:outputLabel value="Consultor:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu id="consultorPadrao" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.integracaoMember.consultorPadrao}">
                        <f:selectItems value="#{ImportacaoControle.listaConsultor}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Professor TreinoWeb:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:selectOneMenu id="professorPadraoTreinoWeb" onblur="blurinput(this);"
                                     title="Caso não seja adicionado não será criado vincúlo com professor."
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.integracaoMember.professorPadrao}">
                        <f:selectItems value="#{ImportacaoControle.listaProfessorTreinoWeb}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="DDD:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:inputText id="dddPadrao" styleClass="form tooltipster"
                                 title="DDD Padrão para os números sem o DDD informado. (Somente números)"
                                 maxlength="3" size="3"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.integracaoMember.dddPadrao}"/>

                    <h:outputLabel value="Plano:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:selectOneMenu id="planoPadraoMembers" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.integracaoMember.planoPadrao}">
                        <f:selectItems value="#{ImportacaoControle.listaPlano}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Modalidade:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu id="modalidadePadraoMembers" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     title="São apresentado somente modalidades que não utilizam turma."
                                     value="#{ImportacaoControle.integracaoMember.modalidadePadrao}">
                        <f:selectItems value="#{ImportacaoControle.listaModalidade}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Horário:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu id="horarioMembers" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.integracaoMember.horarioPadrao}">
                        <f:selectItems value="#{ImportacaoControle.listaHorarios}"/>
                        <a4j:support event="onchange" status="false"
                                     oncomplete="atualizarTempoImportacao()"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Dias Carência:" styleClass="passosImportacaoDescricao"/>
                    <h:inputText id="diasCarenciaContratoMembers" styleClass="form tooltipster"
                                 title="Número de dias de carência"
                                 maxlength="2" size="2"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.integracaoMember.diasCarencia}"/>

                    <h:outputLabel value="Data inicial considerar lançamentos:" styleClass="passosImportacaoDescricao"/>
                    <rich:calendar id="dataInicialConsiderarLancamentos"
                                   value="#{ImportacaoControle.integracaoMember.dataInicialConsiderarLancamentos}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <h:message for="dataInicialConsiderarLancamentos"  styleClass="mensagemDetalhada"/>

                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="Filtros:" styleClass="passosImportacao"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno"
                          style="display: inline-flex; width: 100%"
                          id="panelFiltrosImportacaoMember">
                <h:panelGrid columns="2" width="100%"
                             columnClasses="colunaEsquerdaImport, colunaDireitaImport">

                    <h:outputLabel value="Operação:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu id="tipoOperacaoIntegracaoMembers" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.tipoOperacaoIntegracaoMembers}">
                        <f:selectItems value="#{ImportacaoControle.listaSelectItemTipoOperacaoIntegracaoMembers}"/>
                    </h:selectOneMenu>

                    <h:outputLabel value="Matriculas separadas por vírgula:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:inputText id="idsMembers" styleClass="form tooltipster"
                                 title="Matrículas\Ids Members separados por vírgula"
                                 maxlength="99999" size="40"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.idsMembers}"/>


                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>

<%--        <h:panelGroup layout="block" styleClass="panelPassos">--%>
<%--            <h:outputLabel value="Correspondencia de planos:" styleClass="passosImportacao"/>--%>
<%--            <br/>--%>
<%--            <h:outputLabel value="1º passo:" styleClass="passosImportacao"/>--%>
<%--            <a4j:commandLink target="_blank"--%>
<%--                             style="padding-left: 5px; font-size: 15px;"--%>
<%--                             value="Baixar planilha com planos para correspondencia"--%>
<%--                             action="#{ImportacaoControle.gerarPlanilhaPlanosParaCorrespondencia}"--%>
<%--                             oncomplete="#{ImportacaoControle.onComplete}"/>--%>
<%--            <h:outputLabel value="2º passo:" styleClass="passosImportacao"/>--%>
<%--            <h:outputLabel value="Fazer o Upload da planilha preenchida para salvar a correspondencia:"--%>
<%--                           styleClass="passosImportacaoDescricao"--%>
<%--                           style="padding-left: 5px"/>--%>
<%--            <rich:fileUpload--%>
<%--                    fileUploadListener="#{ImportacaoControle.uploadArquivoCorrepondencia}"--%>
<%--                    immediateUpload="true" id="updateLoadPlanilhaCorrespondencia"--%>
<%--                    acceptedTypes="xls,xlsx" allowFlash="false"--%>
<%--                    listHeight="58px"--%>
<%--                    cancelEntryControlLabel="Cancelar"--%>
<%--                    addControlLabel="Adicionar"--%>
<%--                    clearControlLabel="Remover"--%>
<%--                    clearAllControlLabel="Remover Todos"--%>
<%--                    doneLabel="Concluído"--%>
<%--                    sizeErrorLabel="Limite de tamanho atingido"--%>
<%--                    uploadControlLabel="Carregar"--%>
<%--                    transferErrorLabel="Erro na transferência"--%>
<%--                    stopControlLabel="Parar"--%>
<%--                    stopEntryControlLabel="Parar"--%>
<%--                    progressLabel="Carregando"--%>
<%--                    maxFilesQuantity="1">--%>
<%--                <a4j:support event="onerror" reRender="panelBotoesImportacaoMember"--%>
<%--                             action="#{ImportacaoControle.removerArquivo}"/>--%>
<%--                <a4j:support event="onupload" reRender="panelBotoesImportacaoMember"/>--%>
<%--                <a4j:support event="onuploadcomplete" reRender="panelBotoesImportacaoMember"/>--%>
<%--                <a4j:support event="onclear" reRender="panelBotoesImportacaoMember, panelUploadFileCorrespondencia"--%>
<%--                             action="#{ImportacaoControle.removerArquivo}"/>--%>
<%--            </rich:fileUpload>--%>
<%--        </h:panelGroup>--%>
<%--        <h:panelGroup layout="block" id="panelUploadFileCorrespondencia" style="padding-top: 5px; padding-left: 30px">--%>

<%--        </h:panelGroup>--%>

    </h:panelGroup>

    <h:panelGroup layout="block" id="panelBotoesImportacaoMember"
                  styleClass="panelBotoesImportacao">
        <a4j:commandLink id="btnGravarConfig" value="Gravar Configurações"
                         style="padding-left: 15px"
                         action="#{ImportacaoControle.gravarConfiguracaoIntegracaoMember}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         title="Gravar Configurações"
                         styleClass="botoes nvoBt"/>
        <a4j:commandLink id="btnImportarMember" value="Processar"
                         style="padding-left: 15px"
                         onclick="atualizarTempoImportacao()"
                         action="#{ImportacaoControle.processarImportacaoMembers}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         title="Processar Importação de Dados"
                         reRender="panelGeralModalConfirmarImportacao, formModImpo"
                         styleClass="botoes nvoBt"/>
    </h:panelGroup>
</h:panelGroup>

body {
    display: flex;
    justify-content: center;
    margin: 0px;
    font-family: Arial;
    color: #1B4166;
}

.box {
    width: 780px;
}

.box-header {
    background: #026ABC;
    border-radius: 0px;
    transform: matrix(-1, 0, 0, 1, 0, 0);
    height: 12px;
}

.box-header-logo {
    height: 64px;
    padding-top: 24px;
}

.box-header-logo > .logo {
    width: 110px;
    height: 36px;
    margin-left: 45%;
}

.box-header-logo > .hash {
    font-weight: bold;
    font-size: 16px;
    line-height: 18px;
    float: right;
}

.box-card {
    background: #FFFFFF;
    border: 1px solid #E1E2E4;
    box-sizing: border-box;
}

.box-card > .title {
    font-size: 41px;
    line-height: 52px;
    margin: 24px 67px 24px 67px;
}

.box-card-content {
    background: #FAFAFA;
    border-radius: 0px 4px 0px 0px;
    margin: 24px 67px 24px 67px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.box-card-content > .subtitle {
    font-size: 20px;
    line-height: 24px;
    padding: 24px 67px 24px 67px;
}

.box-card-content > .box-value {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
}

.box-card-content > .box-value > .subtitle {
    font-size: 16px;
    line-height: 18px;
}

.box-card-content > .box-value > .value {
    font-size: 54px;
    line-height: 62px;
    font-weight: bold;
    padding-top: 18px;
    padding-bottom: 18px;
}

.box-card-content > .subtitle2 {
    font-size: 18px;
    line-height: 18px;
    padding-top: 32px;
}

.box-card-content > .box-data {
    display: flex;
    align-items: center;
    margin-top: 25px;
}

.box-card-content > .box-data > .box-img {
    margin-left: 144px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.box-card-content > .box-data > .box-img > img {
    margin-left: 25px;
    width: 24px;
    height: 24px;
}

.box-card-content > .divider {
    border: 1px solid #C7C9CC;
    margin: 24px 35px 24px 35px;
    width: 572px;
}

.box-card-content > .divider {
    border: 1px solid #C7C9CC;
    margin: 24px 35px 24px 35px;
    width: 572px;
}

.box-qrcode {
    margin-top: 42px;
}

.box-card-content > .box-qrcode {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 42px;
}

.box-card-content > .box-qrcode > span {
    padding-bottom: 16px;
    font-size: 18px;
    line-height: 18px;
}

.box-card-content > .box-qrcode > img {
    border: 4px solid #2B68A3;
    box-sizing: border-box;
    border-radius: 4px;
    width: 246px;
    height: 246px;
    margin-bottom: 24px;
}

.box-card-content > .box-button {
    background: #0380E3;
    box-shadow: 0px 4px 6px #E4E5E6;
    border-radius: 4px;
    width: 282px;
    height: 42px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    color: #FFFFFF;
    margin-bottom: 32px;
    cursor: pointer;
}

.box-card-content > .box-button > img {
    margin-left: 12px;
}

.box-footer {
    margin-top: 12px;
    display: flex;
    justify-content: center;
    font-size: 12px;
    line-height: 24px;
    color: #5F6369;
}

.box-footer > img {
    padding-left: 12px;
    padding-right: 12px;
    height: 14px;
    width: 14px;
}

@media only screen and (max-width: 600px) {
    .box {
        width: 100%;
    }

    .box-header-logo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 16px;
    }

    .box-header-logo > .logo {
        margin-left: 16px;
    }

    .box-header-logo > .hash {
        margin-right: 16px;
        font-size: 12px;
    }

    .box-card {
        border: none;
        padding: 16px;
    }

    .box-card > .title {
        font-size: 24px;
        line-height: 32px;
        margin-left: 16px;
        margin-right: 16px;
    }

    .box-card-content {
        margin: 0px;
    }

    .box-card-content > .subtitle {
        font-size: 12px;
        line-height: 16px;
        padding: 24px 16px 24px 16px;
    }

    .box-card-content > .box-qrcode > span {
        width: 176px;
        font-size: 14px;
        line-height: 18px;
        margin-top: 25px;
    }

    .box-card-content > .divider {
        display: none;
    }

    .box-card-content > .box-value > .subtitle {
        font-size: 12px;
        line-height: 18px;
    }

    .box-card-content > .box-value > .subtitle.subtitle1 {
        font-size: 14px;
        line-height: 18px;
    }

    .box-card-content > .box-value > .value {
        font-size: 36px;
        line-height: 36px;
    }

    .box-card-content > .box-qrcode > img {
        width: 170px;
        height: 170px;
    }

    .box-card-content > .box-button {
        width: 236px;
        font-size: 14px;
        line-height: 24px;
    }

    .box-footer {
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        background-color: #FAFAFA;
    }

    .box-header-logo {
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        height: 80px;
    }
    .box-header-logo > .logo {
        margin-left: 0px;
    }
    .box-header-logo > .hash {
        float: none;
        margin-top: 10px;
    }

    .box-card-content > .box-qrcode > span {
        text-align: center;
    }
}
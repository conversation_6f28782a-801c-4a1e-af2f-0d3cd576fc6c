<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="../includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="../bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>
<title>Relatório de Transações Pix</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Relatório de transações Pix"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-transacoes-pix/"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

                <c:if test="${modulo eq 'zillyonWeb'}">
                    <jsp:include page="../topoReduzido_material.jsp"/>
                </c:if>
                <c:if test="${modulo eq 'centralEventos'}">
                    <jsp:include page="../pages/ce/includes/topoReduzido.jsp"/>
                </c:if>
        </f:facet>

        <h:form id="form" target="_blank" >
            <h:panelGroup layout="block" style="display: flex; justify-content: center; align-items: center; flex-direction: column;margin-top: 30px;">
            <h:panelGroup layout="block" style="display: flex; justify-content: center; align-items: center;">
                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_de}" />
                <h:panelGroup layout="block" style="padding-left: 12px; padding-right: 12px;">
                    <rich:calendar id="dataInicio"
                                   value="#{PixRelatorioController.dataInicial}"
                                   inputSize="10"
                                   style="padding-left: 12px; height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <h:message for="dataInicio"  styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ate}" />
                <h:panelGroup layout="block" style="padding-left: 12px;">
                    <rich:calendar id="dataTermino"
                                   value="#{PixRelatorioController.dataFinal}"
                                   inputSize="10"
                                   style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"/>
                    <h:message for="dataTermino"  styleClass="mensagemDetalhada"/>
                </h:panelGroup>
            </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-botoes" style="margin-top: 30px;">
                    <a4j:commandLink id="btnRelatorioPix"
                                     reRender="form"
                                     action="#{PixRelatorioController.consultarTransacoesPix}"
                                     styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                     style="margin-bottom: 20px;"
                                     value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}" />
                </h:panelGroup>
                <h:panelGrid id="tabelaRelatorioPix" rendered="#{not empty PixRelatorioController.pixVOS}" columns="1"
                             width="100%" cellpadding="0" cellspacing="0">
                    <h:panelGrid columns="1" width="100%">
                        <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes" style="line-height: 45px; white-space: nowrap; margin-bottom: 45px"
                                        value="#{PixRelatorioController.pixVOS}" rows="50" var="pix"
                                        rowKeyVar="status">
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Código Pix"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{pix.txid}"/>
                            </rich:column>
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Nome do aluno"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                              value="#{pix.devedorNome}"/>
                            </rich:column>
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Parcelas"/>
                                </f:facet>
                                <h:panelGroup layout="block">
                                <h:outputFormat styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                              value="#{pix.parcelasRelPix}"
                                              title="#{pix.parcelasRelPixTitle}"/>
                                </h:panelGroup>
                            </rich:column>
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="CPF/CNPJ"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                              value="#{pix.devedorCpfOuCnpjFormatado}"/>
                            </rich:column>
                            <rich:column styleClass="col-text-align-center"
                                         headerClass="col-text-align-center">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Valor"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{pix.valorFormatado}">
                                </h:outputText>
                            </rich:column>
                            <rich:column sortBy="#{pix.status}" styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Situação"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                              title="#{pix.status}"
                                              value="#{pix.statusDescricao}"/>
                                <h:outputText rendered="#{pix.pagoOrigemWebhook}"
                                              id="checkedPixWebhookRel"
                                              title="Este pix teve a baixa realizada via webhook enviado pelo banco"
                                              styleClass="fa-icon-ok-sign tooltipster" style="color: #3cdb5c; margin-left: 6px;"/>
                            </rich:column>
                            <rich:column sortBy="#{pix.dataFormatada}" styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Data/Hora"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                              value="#{pix.dataFormatada}"/>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>

</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

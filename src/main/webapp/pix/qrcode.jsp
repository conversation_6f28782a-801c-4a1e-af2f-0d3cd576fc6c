<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="./qrcode.css" rel="stylesheet" type="text/css">
    <%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
    <%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
    <%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
    <%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
    <%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
    <%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
    <c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
    <title>Pix Pacto</title>
</head>

<body style="background-color: #FAFAFA">
    <f:view>
        <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
        <a4j:keepAlive beanName="PixController" />
        <div class="box">
            <div class="box-header"></div>
            <div class="box-header-logo">
                <img class="logo" src="${contexto}/images/pix/icon-pacto.svg" />
                <span class="hash">#agenteseenvolve</span>
            </div>
            <c:if test="${not PixController.tudoCerto}">
            <div class="box-card">
                <div class="title">
                    Sorry! Esta url est� incompleta.
                </div>
                <div class="box-card-content">
                    <div class="subtitle">
                        Por favor, entre em contato com sua academia para enviar o link novamente.
                    </div>
                </div>
            </div>
            </c:if>
            <c:if test="${PixController.tudoCerto}">
                <div class="box-card">
                <div class="title">
                    Se � pra deixar tudo mais simples, <strong>a gente te ajuda!</strong>
                </div>
                <div class="box-card-content">
                    <div class="subtitle">
                        Sua conta j� est� anexada nesse QR Code, basta pagar usando s� uma informa��o.
                    </div>
                    <div class="box-value">
                        <span class="subtitle subtitle1">Transferindo</span>
                        <span class="value">R$ ${PixController.pixVO.valorFormatado}</span>
                        <span class="subtitle">para ${PixController.empresaVO.nome}</span>
                    </div>
                    <div class="box-qrcode">
                        <span>Use o <strong>QR Code do PIX</strong> para efetuar o pagamento</span>
                        <img src="${PixController.pixVO.textoQRCodeSemEncodeSomenteJSP}&w=246&h=246" alt="Copiar QRcode" />
                    </div>
                    <div id="button_whats" class="box-button" onclick="copyQr('qrcode_pix', 'button_whats')">
                        <h:inputHidden id="qrcode_pix" value="#{PixController.pixVO.textoImagemQrcode}" />
                        <span>Copiar c�digo do QR Code</span>
                        <img src="${contexto}/images/pix/copy-white.svg" alt="Copiar QRcode" />
                    </div>
                </div>
            </div>
            </c:if>
            <div class="box-footer">
                <span>� 2020 SistemaPacto.com.br</span>
                <img src="${contexto}/images/pix/coracao.svg" alt="" />
                <span>A gente se envolve #FITNESSLOVERS</span>
            </div>
        </div>
    </f:view>
</body>
<script>
    function copyQr(elementValueId, elementFeedbackId){
        const textArea = document.createElement("textarea");
        textArea.value = document.getElementById(elementValueId).value;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            document.getElementById(elementFeedbackId).style = 'background-color: #4caf50;';
        } catch (err) {
            console.error('Async: Could not copy text: ', err);
        }
        document.body.removeChild(textArea);
    }
</script>
</html>

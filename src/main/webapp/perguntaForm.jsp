<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Pergunta_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Pergunta_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Pergunta"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{PerguntaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
                           style="display: none"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pergunta_codigo}"/>
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                     value="#{PerguntaControle.perguntaVO.codigo}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pergunta_descricao}"/>
                    <h:panelGroup>
                        <h:inputText id="descricao" size="70" maxlength="100" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{PerguntaControle.perguntaVO.descricao}"/>
                    </h:panelGroup>
                    <%--<h:outputText  styleClass="tituloCampos" value="Pergunta NPS" />--%>
                    <%--<h:selectBooleanCheckbox styleClass="form" value="#{PerguntaControle.perguntaVO.nps}" >--%>
                    <%--<a4j:support event="onchange" action="#{PerguntaControle.selecionouNPS}" reRender="form" />--%>
                    <%--</h:selectBooleanCheckbox>--%>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Pergunta_tipoPergunta}"/>
                    <h:panelGroup>
                        <h:selectOneMenu id="tipoPergunta" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form" value="#{PerguntaControle.perguntaVO.tipoPergunta}">
                            <a4j:support event="onchange" action="#{PerguntaControle.atualizarDadosTela}"
                                         reRender="form" focus="tipoPergunta"/>
                            <f:selectItems value="#{PerguntaControle.listaSelectItemTipoPerguntaPergunta}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                </h:panelGrid>
                <h:panelGrid id="panelRespostaPergunte" columns="1" width="100%"
                             rendered="#{PerguntaControle.apresentarGrid && !PerguntaControle.perguntaVO.nps}">
                    <h:panelGrid columns="1" width="100%" rendered="#{PerguntaControle.apresentarGridVO}"
                                 headerClass="subordinado" columnClasses="colunaCentralizada">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_RespostaPergunta_tituloForm}"/>
                        </f:facet>
                        <h:panelGrid columns="3" width="100%" styleClass="tabFormSubordinada"
                                     footerClass="colunaCentralizada">
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_RespostaPergunta_descricaoRespota}"/>
                            <h:inputText id="respostaPergunta_descricaoRespota" size="50" maxlength="50"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{PerguntaControle.respostaPerguntaVO.descricaoRespota}"/>
                        </h:panelGrid>
                        <a4j:commandButton id="btnAdicionar" action="#{PerguntaControle.adicionarRespostaPergunta}"
                                           reRender="panelRespostaPergunte, panelMensagemErro"
                                           focus="form:respostaPergunta_descricaoRespota"
                                           value="#{msg_bt.btn_adicionar}" image="./imagens/botaoAdicionar.png"
                                           accesskey="5" styleClass="botoes"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                        <h:dataTable id="respostaPerguntaVO" width="100%" headerClass="subordinado"
                                     styleClass="tabFormSubordinada"
                                     rowClasses="linhaImparSubordinado, linhaParSubordinado"
                                     columnClasses="colunaAlinhamento"
                                     value="#{PerguntaControle.perguntaVO.respostaPerguntaVOs}" var="respostaPergunta">

                            <rich:column>
                                <a4j:commandLink id="moverBaixo" reRender="respostaPerguntaVO"
                                                 action="#{PerguntaControle.moverParaBaixo}"
                                                 title="Mover Resposta Para Baixo"
                                                 accesskey="6" styleClass="fa fa-icon-arrow-down fa-icon-large"/>
                                <a4j:commandLink id="moverCima" reRender="respostaPerguntaVO"
                                                 action="#{PerguntaControle.moverParaCima}"
                                                 title="Mover Resposta Para Cima"
                                                 accesskey="6" styleClass="fa fa-icon-arrow-up fa-icon-large"/>
                            </rich:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_RespostaPergunta_descricaoRespota}"/>
                                </f:facet>
                                <h:outputText value="#{respostaPergunta.descricaoRespota}"/>
                            </h:column>
                            <h:column rendered="#{PerguntaControle.apresentarGridVO}">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <h:commandButton id="editarItemVenda" immediate="true"
                                                     action="#{PerguntaControle.editarRespostaPergunta}"
                                                     rendered="#{PerguntaControle.apresentarGridVO}"
                                                     value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png"
                                                     accesskey="6" styleClass="botoes"/>

                                    <h:outputText value="    "/>

                                    <h:commandButton id="removerItemVenda" immediate="true"
                                                     action="#{PerguntaControle.removerRespostaPergunta}"
                                                     value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                                     accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{PerguntaControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{PerguntaControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgPergunta" styleClass="mensagem" value="#{PerguntaControle.mensagem}"/>
                            <h:outputText id="msgPerguntaDet" styleClass="mensagemDetalhada"
                                          value="#{PerguntaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{PerguntaControle.novo}"
                                               value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                               styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{PerguntaControle.gravar}"
                                               value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2"
                                               styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                   oncomplete="#{PerguntaControle.msgAlert}"
                                                   action="#{PerguntaControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}"
                                                   accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true"
                                               action="#{PerguntaControle.inicializarConsultar}"
                                               value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                               accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="15px"/>
                            <a4j:commandLink id="log"
                                             action="#{PerguntaControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             style="display: inline-block; padding: 8px 15px; margin-left: -6px;"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>

                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
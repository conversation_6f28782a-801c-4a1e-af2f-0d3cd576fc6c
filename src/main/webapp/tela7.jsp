<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="css_pacto.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="menus/menubotoes.css" type="text/css" />
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <%@include file="/includes/include_modal_usuarioSenhaTipoContrato.jsp" %>
    <title>
        <h:outputText value="Fechamento da Negociação"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <rich:modalPanel id="panelUsuarioSenha" autosized="true" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formUsuarioSenha:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Fechamento do Contrato"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkUsuarioSenha"/>
                <rich:componentControl for="panelUsuarioSenha" attachTo="hidelinkUsuarioSenha" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formUsuarioSenha">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Confirmação do Fechamento do Contrato"/>
                </h:panelGrid>
                <h:panelGrid id="panelConfimacao" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Código:" />
                        <h:inputText id="codigoUsuario" size="3" maxlength="100" style="margin-left:5px" value="#{ContratoControle.contratoVO.responsavelContrato.codigo}">
                            <a4j:support event="onchange" focus="formUsuarioSenha:senha" action="#{ContratoControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{ContratoControle.contratoVO.responsavelContrato.username}"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Usuário:" />
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{ContratoControle.contratoVO.responsavelContrato.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text"  value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{ContratoControle.contratoVO.responsavelContrato.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('loginCaixa')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                              value="#{ContratoControle.mensagemDetalhada}"/>
                <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber}" id="loginCaixa" value="#{msg_bt.btn_confirmar}"
                                   image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                                   action="#{ContratoControle.validarUsuarioResponsavelEmpresaPermissaoFecharContrato}"/>
                <a4j:commandLink style="display:none;" id="botaoInvisivelTela71"
                                 action="#{ContratoControle.validarUsuarioResponsavelEmpresaPermissaoFecharContrato}"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelConfimacaoPagamento" autosized="true"
                     shadowOpacity="true" width="450" height="250"
                     onshow="document.getElementById('formConfimacaoPagamento:senha2').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Fechamento do Contrato"></h:outputText>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>

                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkConfirmacaoPagamento"/>

                <rich:componentControl for="panelConfimacaoPagamento" attachTo="hidelinkConfirmacaoPagamento" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formConfimacaoPagamento">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Confirmação do Fechamento do Contrato"/>
                </h:panelGrid>
                <h:panelGrid id="panelConfimacao" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Código:" />
                        <h:inputText id="codigoUsuario" size="5" maxlength="7" style="margin-left:5px" value="#{ContratoControle.contratoVO.responsavelContrato.codigo}">
                            <a4j:support event="onchange" focus="formConfimacaoPagamento:senha2" action="#{ContratoControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden2" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{ContratoControle.contratoVO.responsavelContrato.username}"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Usuário:" />
                        <h:outputText styleClass="text" style="margin-left:6px"  value="#{ContratoControle.contratoVO.responsavelContrato.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText  styleClass="text" value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha2" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{ContratoControle.contratoVO.responsavelContrato.senha}"/>
                        <rich:hotKey selector="#senha2" key="return"
                                     handler="#{rich:element('login')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:outputText id="mensagem" styleClass="mensagemDetalhada" value="#{ContratoControle.mensagemDetalhada}"/>
                <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber}" id="login" value="#{msg_bt.btn_confirmar}" image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}" action="#{ContratoControle.validarUsuarioResponsavelEmpresaGravarEDepoisIrTelaPagamento}"/>
                <a4j:commandLink style="display:none;" id="botaoInvisivelTela72" action="#{ContratoControle.validarUsuarioResponsavelEmpresaGravarEDepoisIrTelaPagamento}"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <head>
        <link rel="shortcut icon" href="./favicon.ico">
        <title><h:outputText value="#{msg_aplic.prt_Cliente_tituloForm}" /></title>
        <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    </head>

    <h:form id="form">
        <html>
            <jsp:include page="include_head.jsp" flush="true" />
            <body>
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                    <c:if test="${MenuControle.apresentarTopo}">
                        <tr>
                            <td height="77" align="left" valign="top" class="bgtop topoZW">
                                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                            </td>
                        </tr>
                            <rich:jQuery selector=".item4" query="addClass('menuItemAtual')"/>
                    </c:if>
                    <tr>
                        <td align="left" valign="top" class="bglateral">
                            <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;"><jsp:include page="include_box_menulateral.jsp" flush="true" /><jsp:include page="include_box_descricao.jsp" flush="true" />
                                    </td>
                                    <td align="left" valign="top" style="padding:7px 20px 0 20px;">

                                        <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                            <tr>
                                                <td height="28" colspan="3" align="left" valign="top" style="padding-left:17px;"><a class="hierarquialink" href="tela1.jsp"><img border="0" style="margin-right:4px;vertical-align:middle;" src="images/arrow_back.gif" alt="Inicial">Inicial</a> <span class="hierarquia">> Fechamento de Negocia&ccedil;&atilde;o</span></td>
                                            </tr>
                                            <tr>
                                                <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"></td>
                                                <td id="tituloFechamentoNeg" align="left" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Fechamento da Negocia&ccedil;&atilde;o</td>
                                                <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"></td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif"></td>
                                                <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;">
                                                    <!-- inicio item -->
                                                    <div>
                                                        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0" class="tablepadding2 textsmall" style="margin-bottom:25px;">

                                                            <tr>
                                                                <td width="60%" align="left" valign="top" style="padding:0 10px 10px 0;">
                                                                    <h:outputText rendered="#{ContratoControle.mensagemDadosObrigatorios != ''}" id="dadosObrigatorios"
                                                                                  styleClass="tituloCamposAzulClaroGrande"
                                                                                  value="Para finalizar a negociação você precisará dos seguintes dados do cliente: #{ContratoControle.mensagemDadosObrigatorios}"/>
                                                                    <!-- inicio botões -->



                                                                    <!-- fim botões -->

                                                                    <!-- inicio item -->
                                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" >
                                                                        <tr>
                                                                            <td align="left" valign="top">
                                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">
                                                                                    <tr>
                                                                                        <td align="left" valign="top" colspan="2" style="background-color: white">
                                                                                            <div style="clear:both;" class="text">
                                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Perfil do Contrato</p>
                                                                                            </div>
                                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td height="27" align="left" valign="middle">
                                                                                            <b><h:outputText  value="Data Início Contrato: " /></b>
                                                                                            <h:outputText  value="#{ContratoControle.contratoVO.vigenciaDe_ApresentarTela7}" />
                                                                                        </td>
                                                                                        <td>
                                                                                            <b><h:outputText  value="Data Término " /></b>
                                                                                            <h:outputText id="vigencia" value="#{ContratoControle.contratoVO.vigenciaAteAjustada_Apresentar}" />
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td height="27" align="left" valign="middle">
                                                                                            <b><h:outputText  value="Empresa: " /></b>
                                                                                            <h:outputText  value="#{ContratoControle.contratoVO.empresa.nome}" />

                                                                                        </td>
                                                                                        <td>
                                                                                            <b><h:outputText  value="Duração: " /></b>
                                                                                            <h:outputText  value="#{ContratoControle.contratoVO.descricaoDuracaoTelaNegociacao}" />
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td height="27" align="left" valign="middle">
                                                                                            <b><h:outputText  value="Plano: " /></b>
                                                                                            <h:outputText  value="#{ContratoControle.contratoVO.plano.descricao}" />
                                                                                        </td>
                                                                                        <td>
                                                                                            <b><h:outputText  value="Horário: " /></b>
                                                                                            <h:outputText  value="#{ContratoControle.contratoVO.planoHorario.horario.descricao}" />
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td height="27" align="left" valign="middle" colspan="2">
                                                                                            <b><h:outputText  value="Bolsa: " /></b>
                                                                                            <h:outputText  value="#{ContratoControle.contratoVO.plano.bolsa_Apresentar}" />
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td align="left" valign="top" >
                                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom:25px;">
                                                                                    <tr>
                                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Modalidades</p>
                                                                                            </div>
                                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td height="27" align="left" valign="middle" class="textsmall">
                                                                                            <rich:dataTable id="modalidade" width="100%" rowClasses="linhaImpar, linhaPar"
                                                                                                            headerClass="subordinado"
                                                                                                            value="#{ContratoControle.contratoModalidades}"
                                                                                                            styleClass="tabFormSubordinada semBorda" var="contratoModalidade">
                                                                                                <rich:column styleClass="semBorda">
                                                                                                    <h:outputText value="Modalidade: " />
                                                                                                    <h:outputText value="#{contratoModalidade.modalidade.nome}" />
                                                                                                </rich:column>
                                                                                                <rich:column styleClass="semBorda">
                                                                                                    <h:outputText value="Número de Vezes por Semana: " />
                                                                                                    <h:outputText value="#{contratoModalidade.planoVezesSemanaVO.nrVezes} " />
                                                                                                </rich:column>
                                                                                            </rich:dataTable>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>

                                                                        <tr>
                                                                            <td align="left" valign="top">

                                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom:25px;">
                                                                                    <tr>
                                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Turmas</p>
                                                                                            </div>
                                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td height="27" align="left" valign="middle" class="textsmall">
                                                                                            <rich:dataTable id="turma" width="100%" columnClasses="colunaEsquerda" styleClass="semBorda"
                                                                                                            value="#{ContratoControle.contratoVO.contratoModalidadeVOs}" var="contratoModalidade" >
                                                                                                <rich:column rendered="#{contratoModalidade.modalidade.modalidadeEscolhida && contratoModalidade.modalidade.utilizarTurma}" styleClass="semBorda">
                                                                                                    <rich:dataTable id="modalidadeTurma" width="100%" rowClasses="linhaImpar, linhaPar"  columnClasses="textsmall"
                                                                                                                    value="#{contratoModalidade.contratoModalidadeTurmaVOs}" var="modalidadeTurma" styleClass="semBorda">
                                                                                                        <rich:column rendered="#{modalidadeTurma.turma.turmaEscolhida}" styleClass="semBorda">
                                                                                                            <h:outputText style="font-weight: bold"  value="Turma: " />
                                                                                                            <h:outputText value="#{modalidadeTurma.turma.identificador}" />
                                                                                                            <h:outputText value=" (Modalidade: " />
                                                                                                            <h:outputText value="#{contratoModalidade.modalidade.nome})" />
                                                                                                        </rich:column>
                                                                                                        <rich:column rendered="#{modalidadeTurma.turma.turmaEscolhida}" styleClass="semBorda">
                                                                                                            <rich:dataTable id="turmaHorario" width="100%" var="horario" rowClasses="linhaImpar, linhaPar"
                                                                                                                            value="#{modalidadeTurma.contratoModalidadeHorarioTurmaVOs}">
                                                                                                                <rich:column rendered="#{horario.horarioTurma.horarioTurmaEscolhida}" width="150">
                                                                                                                    <h:outputText style="font-weight: bold" value="Dia da Semana: " />
                                                                                                                    <h:outputText value="#{horario.horarioTurma.diaSemana_Apresentar}"/>
                                                                                                                </rich:column>
                                                                                                                <rich:column rendered="#{horario.horarioTurma.horarioTurmaEscolhida}">
                                                                                                                    <h:outputText style="margin-left:5px ; font-weight: bold"  value="Hora da Aula: "/>
                                                                                                                    <h:outputText value="#{horario.horarioTurma.horaInicial} as #{horario.horarioTurma.horaFinal}" />
                                                                                                                </rich:column >
                                                                                                            </rich:dataTable>
                                                                                                        </rich:column>
                                                                                                    </rich:dataTable>
                                                                                                </rich:column>
                                                                                            </rich:dataTable>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>

                                                                        <tr>
                                                                            <td>
                                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                                    <tr>
                                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Mensalidades</p>
                                                                                            </div>
                                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td align="left" valign="top">
                                                                                            <rich:dataTable id="movProduto" width="100%" rowClasses="linhaImpar, linhaPar"  columnClasses="w200,w10"
                                                                                                            value="#{ContratoControle.contratoVO.movProdutoVOs}" var="movProduto" styleClass="semBorda">
                                                                                                <rich:column rendered="#{movProduto.apresentarMovProduto}" styleClass="semBorda">
                                                                                                    <h:outputText value="#{movProduto.descricao}"/>
                                                                                                </rich:column>
                                                                                                <rich:column rendered="#{movProduto.apresentarMovProduto}" styleClass="semBorda">
                                                                                                    <h:outputText value="#{movProduto.totalFinal}">
                                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                                    </h:outputText>
                                                                                                </rich:column>
                                                                                            </rich:dataTable>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>
                                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2">
                                                                                    <tr>
                                                                                        <td align="left" valign="top"><div style="clear:both;" class="text">
                                                                                                <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Produtos</p>
                                                                                            </div>
                                                                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td align="left" valign="top">
                                                                                            <rich:dataTable id="contratoVOPlanoProdutoSugerido" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="w200,w10"
                                                                                                            value="#{ContratoControle.contratoVO.contratoPlanoProdutoSugeridoVOs}"
                                                                                                            var="contratoPlanoProdutoSugerido" styleClass="semBorda">
                                                                                                <rich:column styleClass="semBorda">
                                                                                                    <h:outputText value="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.produto.descricao}" />
                                                                                                </rich:column>
                                                                                                <rich:column styleClass="semBorda">
                                                                                                    <h:outputText value="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.quantidade}X "
                                                                                                                  rendered="#{contratoPlanoProdutoSugerido.planoProdutoSugerido.quantidade > 1}">
                                                                                                    </h:outputText>
                                                                                                    <h:outputText value="#{contratoPlanoProdutoSugerido.valorFinalProduto}" >
                                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                                    </h:outputText>
                                                                                                </rich:column>
                                                                                            </rich:dataTable>
                                                                                            <rich:dataTable id="contratoVOModalidadeProdutoSugerido" width="100%" rowClasses="linhaImpar, linhaPar"
                                                                                                            columnClasses="w200,w10" styleClass="semBorda"
                                                                                                            value="#{ContratoControle.listaProdutoApresentar}"  var="contratoProdutoSugerido">
                                                                                                <rich:column styleClass="semBorda">
                                                                                                    <h:outputText value="#{contratoProdutoSugerido.produtoSugerido.produto.descricao}" />
                                                                                                </rich:column>
                                                                                                <rich:column styleClass="semBorda">
                                                                                                    <h:outputText value="#{contratoProdutoSugerido.valorFinalProduto}" >
                                                                                                        <f:converter converterId="FormatadorNumerico"/>
                                                                                                    </h:outputText>
                                                                                                </rich:column>
                                                                                            </rich:dataTable>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>

                                                                    <!-- fim item -->

                                                                    <!-- inicio botões -->


                                                                    <!-- fim botões -->

                                                                    <!-- fim botões -->
                                                                </td>
                                                                <td align="left" valign="top" class="sombrapreview" style="padding:10px;">

                                                                    <table width="100%" height="100%" border="0" cellspacing="0" cellpadding="0">
                                                                        <tr>
                                                                            <td align="left" valign="top">

                                                                                <!-- inicio item -->
                                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreview" style="margin-bottom:15px;">
                                                                                    <tr>
                                                                                        <td align="right" valign="middle">
                                                                                            <a4j:mediaOutput element="img" id="imagemFoto"  style="width:120px;height:150px "  cacheable="false"
                                                                                                             rendered="#{!SuperControle.fotosNaNuvem}" 
                                                                                                             createContent="#{ContratoControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                                                                                                <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                                                                                <f:param name="largura" value="120"/>
                                                                                                <f:param name="altura" value="150"/>
                                                                                            </a4j:mediaOutput>
                                                                                            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                                                                                            width="120" height="150"
                                                                                                            style="left:0px;width:120px;height:150px "
                                                                                                            url="#{ContratoControle.paintFotoDaNuvem}"/>
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td align="right" valign="middle"><span style="font-weight: bold">Cliente: </span><h:outputText id="pessoaNome" value="#{ContratoControle.contratoVO.pessoa.nome}"/></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td align="right" valign="middle"><span style="font-weight: bold">Tipo do Contrato: </span>
                                                                                            <h:panelGroup id="agendadoEspontaneo">
                                                                                                <a4j:commandLink id="linkContratoEspontaneo" style="font-weight: bold; font-size: 16px; color: #0F4C6B;"
                                                                                                                 rendered="#{!ContratoControle.contratoVO.contratoAgendado}"
                                                                                                                 value="#{msg_aplic.prt_Contrato_espontaneo}"
                                                                                                                 actionListener="#{ContratoControle.prepararAlteracaoTipoContrato}"
                                                                                                                 oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaTipoContrato');"
                                                                                                                 reRender="panelUsuarioSenhaTipoContrato">
                                                                                                    <f:attribute value="#{ContratoControle.contratoVO}" name="contratoAlteracaoTipo" />
                                                                                                </a4j:commandLink>
                                                                                                <rich:toolTip for="linkContratoEspontaneo" followMouse="true" direction="top-right" style="align: left; width:200px; height:75px;">
                                                                                                    <h:outputText styleClass="textoConfiguracoes" value="#{msg_aplic.prt_Contrato_Tipodica}"></h:outputText>
                                                                                                </rich:toolTip>
                                                                                                <a4j:commandLink id="linkContratoAgendado" style="font-weight: bold; font-size: 16px; color: #0F4C6B;"
                                                                                                                 rendered="#{ContratoControle.contratoVO.contratoAgendado}"
                                                                                                                 value="#{msg_aplic.prt_Contrato_agendado}"
                                                                                                                 actionListener="#{ContratoControle.prepararAlteracaoTipoContrato}"
                                                                                                                 oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaTipoContrato');"
                                                                                                                 reRender="panelUsuarioSenhaTipoContrato">
                                                                                                    <f:attribute value="#{ContratoControle.contratoVO}" name="contratoAlteracaoTipo" />
                                                                                                </a4j:commandLink>
                                                                                                <rich:toolTip for="linkContratoAgendado" followMouse="true" direction="top-right" style="align: left; width:200px; height:75px;">
                                                                                                    <h:outputText styleClass="textoConfiguracoes" value="#{msg_aplic.prt_Contrato_Tipodica}"></h:outputText>
                                                                                                </rich:toolTip>
                                                                                            </h:panelGroup>
                                                                                        </td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td align="right" valign="middle"><span class="tituloboxcentro" style="clear:both;text-align:right;margin-bottom:5px;">Total Plano<span class="tituloboxcentro" style="clear:both;text-align:right;margin-bottom:5px;"><img style="vertical-align:middle;margin-left:7px;" src="images/tick.png" width="16" height="13"></span></span></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td align="right" valign="middle"><span style="font-weight: bold">+ #{MovPagamentoControle.empresaLogado.moeda} <h:outputText id="totalMensalidade" value="#{ContratoControle.contratoVO.valorBaseCalculo}"><f:converter converterId="FormatadorNumerico"/></h:outputText> </span><span id="tipoNegociacao"></span></td>
                                                                                        </tr>
                                                                                    </table>
                                                                                    <!-- fim item -->
                                                                                    <!-- inicio item -->
                                                                                <%--table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreview" style="margin-bottom:25px;">
                                                                                    <tr>
                                                                                        <td align="right" valign="middle"><span class="tituloboxcentro" style="clear:both;text-align:right;margin-bottom:5px;">Condição de Pagamento<span class="tituloboxcentro" style="clear:both;text-align:right;margin-bottom:5px;"><img style="vertical-align:middle;margin-left:7px;" src="images/tick.png" width="16" height="13"></span></span></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td align="right" valign="middle"><span style="font-weight: bold"> R$ <h:outputText id="valorDescontoCondicaoPagamento" value="#{ContratoControle.valorDescontoCondicaoPagamento}"><f:converter converterId="FormatadorNumerico"/></h:outputText> </span><span id="tipoNegociacao"></span></td>
                                                                                    </tr>

                                                                            </table>--%>
                                                                                <!-- fim item -->
                                                                                <!-- inicio item -->
                                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreview" style="margin-bottom:25px;">
                                                                                    <tr>
                                                                                        <td align="right" valign="middle"><span class="tituloboxcentro" style="clear:both;text-align:right;margin-bottom:5px;">Produtos<span class="tituloboxcentro" style="clear:both;text-align:right;margin-bottom:5px;"><img style="vertical-align:middle;margin-left:7px;" src="images/tick.png" width="16" height="13"></span></span></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td align="right" valign="middle"><span style="font-weight: bold">+ #{MovPagamentoControle.empresaLogado.moeda} <h:outputText value="#{ContratoControle.contratoVO.somaProduto}"><f:converter converterId="FormatadorNumerico"/></h:outputText>  </span><span id="tipoNegociacao"></span>
                                                                                            </td>
                                                                                        </tr>

                                                                                    <c:if test="${(!ContratoControle.contratoVO.plano.regimeRecorrencia) && (!ContratoControle.contratoVO.plano.vendaCreditoTreino)}">
                                                                                        <tr>
                                                                                            <td>
                                                                                                <h:panelGroup>

                                                                                                    <h:outputLink styleClass="linkWiki"
                                                                                                                  value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                                                                                  title="Clique e saiba mais: Pró-Rata" target="_blank" >
                                                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                                                    </h:outputLink>
                                                                                                    <h:outputText styleClass="text" style="font-weight: bold"  value=" Pró-Rata"/>
                                                                                                    <h:selectOneMenu id="diasVencimento" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ContratoControle.indiceDiaVencimento}">
                                                                                                        <a4j:support event="onchange" reRender="panelProdutoParcela, movProduto, vigencia, valorFinalContrato, totalMensalidade, groupPrimeiraParcela"
                                                                                                                     action="#{ContratoControle.selecionarProrata}" />
                                                                                                        <f:selectItems value="#{ContratoControle.listaDiasVencimento}" />
                                                                                                    </h:selectOneMenu>


                                                                                                </h:panelGroup>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </c:if>
                                                                                    <c:if test="${(!ContratoControle.contratoVO.plano.vendaCreditoTreino)}">
                                                                                        <tr>
                                                                                            <td align="left" valign="top">
                                                                                                <div style="clear:both;" class="text">
                                                                                                    <h:panelGroup id="groupPrimeiraParcela">
                                                                                                        <h:outputLink styleClass="linkWiki"
                                                                                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                                                                                      title="Clique e saiba mais: Data Primeira Parcela" target="_blank" >
                                                                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                                                        </h:outputLink>
                                                                                                        <h:outputText styleClass="text" style="font-weight: bold"  value="Data Primeira Parcela: "/>
                                                                                                        <c:choose>

                                                                                                            <c:when test="${ContratoControle.contratoVO.plano.regimeRecorrencia}">
                                                                                                                <h:outputText value="#{ContratoControle.contratoVO.dataPrimeiraParcela}">
                                                                                                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                                                                                </h:outputText>

                                                                                                            </c:when>
                                                                                                            <c:otherwise>

                                                                                                                <rich:calendar id="dataPrimeiraParcela"
                                                                                                                               value="#{ContratoControle.contratoVO.dataPrimeiraParcela}"
                                                                                                                               inputSize="10"
                                                                                                                               oninputblur="blurinput(this);"
                                                                                                                               oninputfocus="focusinput(this);"
                                                                                                                               oninputchange="return validar_Data(this.id);"
                                                                                                                               datePattern="dd/MM/yyyy"
                                                                                                                               enableManualInput="true"
                                                                                                                               zindex="2"
                                                                                                                               showWeeksBar="false" />
                                                                                                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                                                                            </c:otherwise>
                                                                                                        </c:choose>



                                                                                                        <h:message for="dataPrimeiraParcela"  styleClass="mensagemDetalhada"/>

                                                                                                    </h:panelGroup>
                                                                                                </div>
                                                                                                <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </c:if>
                                                                                </table> <!-- fim item -->

                                                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreviewtotal">
                                                                                    <tr>         <td align="right" valign="middle">Total Final =  <span class="verde">#{MovPagamentoControle.empresaLogado.moeda}<h:outputText  id="valorFinalContrato" value="#{ContratoControle.contratoVO.valorFinal}"><f:converter converterId="FormatadorNumerico"/></h:outputText> </span></td>     </tr>
                                                                                    </table> <!-- inicio item -->
                                                                                <h:panelGrid id="panelProdutoParcela"  columns="1" columnClasses="colunaDireita" width="100%">

                                                                                    <h:outputText rendered="#{!ContratoControle.contratoVO.dividirProdutosNasParcelas}" id="produtoPrimeiraParcela" value="Valor do(s) Produto(s) será somado na primeira Parcela."  style="clear:both;text-align:right;margin-bottom:5px;font-weight: bold;"/>
                                                                                    <h:outputText rendered="#{ContratoControle.contratoVO.dividirProdutosNasParcelas}" id="produtoDivididoParcela" value="Valor do(s) Produto(s) será dividido na(s) Parcela(s)." style="clear:both;text-align:right;margin-bottom:5px;font-weight: bold;"/>

                                                                                    <h:panelGroup>
                                                                                        <h:selectBooleanCheckbox  value="#{ContratoControle.contratoVO.dividirProdutosNasParcelas}">
                                                                                            <a4j:support event="onclick"  action="#{ContratoControle.selecionarDivisaoProdutoParcelaTirandoArredondamento}"
                                                                                                         reRender="panelProdutoParcela, valorFinalContrato"/>
                                                                                        </h:selectBooleanCheckbox>
                                                                                        <h:outputText styleClass="text" style="font-weight: bold"  value="Dividir os Produtos nas Parcelas "/>
                                                                                    </h:panelGroup>

                                                                                    <h:outputText value="Parcela(s)" styleClass="tituloboxcentro" style="clear:both;text-align:right;margin-bottom:5px;"/>

                                                                                    <h:panelGroup  rendered="#{ContratoControle.contratoVO.gerarParcelaParaProdutos && ContratoControle.contratoVO.somaAdesao > 0
                                                                                                               && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}" >

                                                                                                   <h:outputText id="nrCondicaoPagamento1" style="font-weight: bold" value=" (Adesão) #{ContratoControle.contratoVO.nrParcelasAdesao} x #{MovPagamentoControle.empresaLogado.moeda}"/>
                                                                                                   <h:outputText id="valorCondicaoPagamento1" style="font-weight: bold" value="#{ContratoControle.contratoVO.valorParcelasAdesao}">
                                                                                                       <f:converter converterId="FormatadorNumerico"/>
                                                                                                   </h:outputText>
                                                                                    </h:panelGroup>

                                                                                    <h:panelGroup rendered="#{ContratoControle.contratoVO.gerarParcelaAnuidadeSeparada && ContratoControle.contratoVO.nrParcelasAnuidades > 0}" >
                                                                                        <h:outputText id="nrAnuidades" style="font-weight: bold" value=" (Anuidades) #{ContratoControle.contratoVO.nrParcelasAnuidades} x#{MovPagamentoControle.empresaLogado.moeda}"/>
                                                                                        <h:outputText id="valorAnuidade" style="font-weight: bold" value="#{ContratoControle.contratoVO.valorPorAnuidade}">
                                                                                            <f:converter converterId="FormatadorNumerico"/>
                                                                                        </h:outputText>
                                                                                    </h:panelGroup>

                                                                                    <h:panelGroup  rendered="#{!ContratoControle.contratoVO.dividirProdutosNasParcelas
                                                                                                               && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}" >

                                                                                                   <h:outputText id="nrCondicaoPagamento" style="font-weight: bold" value=" 1 x #{MovPagamentoControle.empresaLogado.moeda}"/>
                                                                                                   <h:outputText id="valorCondicaoPagamento" style="font-weight: bold" value="#{ContratoControle.valorParcelaComProdutoContrato}">
                                                                                                       <f:converter converterId="FormatadorNumerico"/>
                                                                                                   </h:outputText>
                                                                                    </h:panelGroup>

                                                                                    <h:panelGroup  rendered="#{!ContratoControle.contratoVO.dividirProdutosNasParcelas 
                                                                                                               && !ContratoControle.parcelaAvistaContrato
                                                                                                               && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}" >

                                                                                                   <h:outputText  style="font-weight: bold" value="#{ContratoControle.numeroParcelaContrato } x #{MovPagamentoControle.empresaLogado.moeda}"/>
                                                                                                   <h:outputText  style="font-weight: bold" value="#{ContratoControle.valorParcelaContrato}">
                                                                                                       <f:converter converterId="FormatadorNumerico"/>
                                                                                                   </h:outputText>
                                                                                    </h:panelGroup>

                                                                                    <h:panelGroup rendered="#{ContratoControle.contratoVO.dividirProdutosNasParcelas}">
                                                                                        <h:outputText  style="font-weight: bold" value="#{ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.nrParcelas} x#{MovPagamentoControle.empresaLogado.moeda}"/>
                                                                                        <h:outputText  style="font-weight: bold" value="#{ContratoControle.valorParcelaContrato}">
                                                                                            <f:converter converterId="FormatadorNumerico"/>
                                                                                        </h:outputText>
                                                                                    </h:panelGroup>


                                                                                    <a4j:commandLink action="#{ContratoControle.apresentarArrendondamento}"
                                                                                                     reRender="formArredondamento,panelProdutoParcela,valorFinalContrato" 
                                                                                                     styleClass="pure-button pure-button-xsmall"
                                                                                                     oncomplete="Richfaces.showModalPanel('painelArredondamento');"
                                                                                                     rendered="#{ContratoControle.usarArredondamento}">
                                                                                        <h:outputText value="#{msg_aplic.prt_arredondamento_parcelas}"/>
                                                                                    </a4j:commandLink>

                                                                                </h:panelGrid>
                                                                                <!-- fim item -->

                                                                                <div style="clear:both;text-align:right;">
                                                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" >
                                                                                        <tr><td>
                                                                                                <h:panelGrid id="panelMesangem" columns="2" width="100%">
                                                                                                    <h:panelGrid columns="1" width="100%">
                                                                                                        <f:verbatim>
                                                                                                            <h:outputText value=" "/>
                                                                                                        </f:verbatim>
                                                                                                    </h:panelGrid>
                                                                                                    <h:panelGrid columns="1" width="100%">
                                                                                                        <h:outputText styleClass="mensagem"  value="#{ContratoControle.mensagem}"/>
                                                                                                        <h:outputText styleClass="mensagemDetalhada" value="#{ContratoControle.mensagemDetalhada}"/>
                                                                                                    </h:panelGrid>
                                                                                                </h:panelGrid>
                                                                                            </td>
                                                                                        </tr>
                                                                                    </table>
                                                                                </div>
                                                                                <div style="clear:both;text-align:right;">
                                                                                    <h:panelGrid columns="2" rendered="#{ContratoControle.contratoVO.plano.permitePagarComBoleto}">
                                                                                        <h:selectBooleanCheckbox styleClass="campos" value="#{ContratoControle.contratoVO.pagarComBoleto}"/>
                                                                                        <h:outputText styleClass="tituloboxcentro" value="Pagar com Boleto"/>
                                                                                    </h:panelGrid>
                                                                                </div>

                                                                                <%-- BOTÕES DE CONTROLE --%>
                                                                                <%@include file="includes/menus/include_menubotoesfixos_tela7.jsp"%>

                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table> <!-- fim item -->
                                                    </div>
                                                </td>
                                                <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>
                                            </tr>
                                            <tr>
                                                <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                                <td align="left" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                                <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="include_rodape.jsp" flush="true" /></td>
                    </tr>
                </table>
            </body>
        </html>
    </h:form>



    <rich:modalPanel id="painelArredondamento" autosized="true"
                     shadowOpacity="true" width="280" >
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Valores sugeridos"
                              style="font-weight: bold;"></h:outputText>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>

                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkArredondamento"/>

                <rich:componentControl for="painelArredondamento" attachTo="hidelinkArredondamento" operation="hide"  event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formArredondamento">

            <rich:dataGrid value="#{ContratoControle.valoresArredondados}"
                           var="parcela" width="100%" columns="#{ContratoControle.removerCentavos ? 1 : 2}"
                           id="tableResults2" styleClass="pure-table pure-table-horizontal pure-table-striped pure-table-links"
                           rendered="#{fn:length(ContratoControle.valoresArredondados) > 0}"
                           columnClasses="centralizado,direita">

                <rich:column>
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('painelArredondamento');"
                                     reRender="form"
                                     action="#{ContratoControle.selecionarArredondamento}">

                        <h:outputText value="#{parcela.valorParcelas}" styleClass="text" >
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </a4j:commandLink>
                </rich:column>

            </rich:dataGrid>

            <br/>
            <center>

                <a4j:commandLink reRender="form"
                                 styleClass="pure-button"
                                 oncomplete="Richfaces.hideModalPanel('painelArredondamento');">
                    Cancelar
                </a4j:commandLink>
            </center>
        </a4j:form>
    </rich:modalPanel>
</f:view> 

<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_ContaCorrente_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ContaCorrente_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Config._Financeiras:Conta_Corrente"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{ContaCorrenteControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,agencia=Agência,agenciaDV=Dígito Verificador da Agência,contaCorrente=Conta Corrente,contaCorrenteDV=Dígito Verificador da Conta Corrente,banco_Apresentar=Banco"/>
                                <f:attribute name="prefixo" value="ContaCorrente"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{ContaCorrenteControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,agencia=Agência,agenciaDV=Dígito Verificador da Agência,contaCorrente=Conta Corrente,contaCorrenteDV=Dígito Verificador da Conta Corrente,banco_Apresentar=Banco"/>
                                <f:attribute name="prefixo" value="ContaCorrente"/>
                                <f:attribute name="titulo" value="Conta Corrente"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                            
                            <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 action="#{ContaCorrenteControle.realizarConsultaLogObjetoGeral}"
                                                        oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{ContaCorrenteControle.novo}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblContaCorrente" class="tabelaContaCorrente pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Cadastro_label_codigo_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_agencia_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_digitoVerificadorAgencia_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_contaCorrente_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_digitoVerificadorContaCorrente_maiusculo}</th>
                    <th>${msg_aplic.prt_Cadastro_label_banco_maiusculo}</th>
                </thead>
                <tbody></tbody>
            </table>

            <a4j:jsFunction name="jsEditar" action="#{ContaCorrenteControle.editar}" reRender="mensagem"/>

        </h:panelGroup>
        <%-- FIM CONTENT --%>

        <%-- INICIO FOOTER --%>
        <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
            <h:graphicImage id="iconSucesso" rendered="#{ContaCorrenteControle.sucesso}" value="./imagens/sucesso.png" />
            <h:graphicImage id="iconErro" rendered="#{ContaCorrenteControle.erro}" value="./imagens/erro.png" />

            <h:outputText styleClass="mensagem" rendered="#{not empty ContaCorrenteControle.mensagem}" value=" #{ContaCorrenteControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty ContaCorrenteControle.mensagemDetalhada}" value=" #{ContaCorrenteControle.mensagemDetalhada}"/>
        </h:panelGroup>
        <%-- FIM FOOTER --%>
    </h:form>
</h:panelGroup>

<script src="beta/js/ext-funcs.js" type="text/javascript"></script>

<script>
    jQuery(window).on("load", function(){
        iniciarTabela("tabelaContaCorrente", "${contexto}/prest/financeiro/contaCorrente", 0, "asc", "", true);
    });
</script>

</f:view>
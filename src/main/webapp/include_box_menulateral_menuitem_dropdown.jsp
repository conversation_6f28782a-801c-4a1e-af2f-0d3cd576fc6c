<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<h:panelGroup layout="block" styleClass="dropdown-content" style="align-self: end; transition-property: none !important; top: 0; left: 0;">
    <a4j:repeat value="#{funcionalidade.subFuncionalidades}" var="subFuncionalidade">
        <h:panelGroup layout="block" styleClass="dropdown-item" rendered="#{subFuncionalidade.renderizar}">
            <h:panelGroup layout="block" styleClass="container-item">
                <a4j:commandLink value="#{subFuncionalidade.descricaoTruncadaMenuLateral}"
                                 title="#{subFuncionalidade.descricao}"
                                 styleClass="titulo3 MENU-#{funcionalidade.name}"
                                 actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                 action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                 oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                 reRender="#{subFuncionalidade.reRenderElement}">
                    <f:attribute name="funcionalidade" value="#{subFuncionalidade.name}" />
                    <f:attribute name="idLocalizacaoMenu" value="LATERAL_RECURSO"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:repeat>
</h:panelGroup>
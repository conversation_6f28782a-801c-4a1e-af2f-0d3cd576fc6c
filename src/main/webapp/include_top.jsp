<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<a4j:loadStyle src="/css/toggle_menu.css"/>
<a4j:loadScript src="/script/toggle_menu.js"/>


<h:commandLink value="sair" id="sairlink" action="#{LogoutControle.doLogout}" style="display:none;"/>
        
<table width="100%" height="77" align="left" border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td width="15%" align="left" valign="top">

            <c:set property="tmp" var="tmp" scope="request" value="<%=request.getRequestURI()%>"/>
            <c:set property="name" var="name" scope="request" value="smartbox"/>
            <h:panelGroup style="padding:-1000px 0 0 0;" styleClass="logoSmartBox"
                          layout="block" rendered="#{fn:containsIgnoreCase(tmp, name)}"/>
            <h:panelGroup style="padding:-1000px 0 0 0;" styleClass="#{SuporteControle.classTopo}"
                          layout="block" rendered="#{!fn:containsIgnoreCase(tmp, name)}"/>
            
            
        </td>
        <c:if test="${!InicioControle.NFe}">
            <%@include file="includes/include_feedGestao.jsp" %>
        </c:if>

        <%@include file="includes/include_expiracao.jsp" %>

        <%--<td style=" vertical-align: top;">--%>

        <%--<c:if test="${InicioControle.qntEmpresasPermitidas > 1}">--%>
        <%--<%@include file="includes/include_modal_trocarEmpresa.jsp" %>--%>
        <%--</c:if>--%>
        <%--</td>--%>

         <td align="right" valign="top" style="padding-right:13px;">
              <c:choose>
                <c:when test="${!InicioControle.NFe}">
                    <%@include file="include_top_menu_superior.jsp" %>
                </c:when>
                <c:otherwise>
                    <%@include file="nfe/include_top_menu_superiorNFe.jsp" %>
                </c:otherwise>
            </c:choose>
        </td>
    </tr>
</table>

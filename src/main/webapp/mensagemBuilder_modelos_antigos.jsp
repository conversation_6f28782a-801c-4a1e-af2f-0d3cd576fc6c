<%@include file="includes/imports.jsp" %>
<h:panelGroup rendered="#{not MensagemBuilderControle.mostrarAntigos and not MensagemBuilderControle.building and not empty MensagemBuilderControle.predefinidosAntigos and not MensagemBuilderControle.visaoAgendados and MensagemBuilderControle.meioEmail}" layout="block"
              style="margin-top: 20px; ">

    <h:panelGroup style="display: inline-flex; padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%; position: relative; height: 40px; margin-top: 25px"
                  layout="block">
        <a4j:commandLink action="#{MensagemBuilderControle.acaoMostrarAntigos}"
                         reRender="mensagembuilder" styleClass="configs"
                         style="color: #0090FF; font-size: 14px;">
            Ver os modelos da vers�o antiga <i class="fa-icon-chevron-down" style="margin-left: 10px" ></i>
        </a4j:commandLink>
    </h:panelGroup>
</h:panelGroup>
<h:panelGroup rendered="#{MensagemBuilderControle.mostrarAntigos and not MensagemBuilderControle.building and not MensagemBuilderControle.visaoAgendados and MensagemBuilderControle.meioEmail}" layout="block"
              style="margin-top: 20px; ">

    <h:panelGroup style="display: inline-flex; padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%; position: relative; height: 40px; margin-top: 25px"
                  layout="block">
        <a4j:commandLink action="#{MensagemBuilderControle.acaoEsconderAntigos}"
                         reRender="mensagembuilder" styleClass="configs"
                         style="color: #0090FF; font-size: 14px;">
            Esconder os modelos da vers�o antiga <i style="margin-left: 10px" class="fa-icon-chevron-up"></i>
        </a4j:commandLink>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="#{MensagemBuilderControle.meioEmail ? 'modelosEmail' : 'modelosSms'}">


        <a4j:jsFunction name="editModeloOld"
                        oncomplete="abrirPopup('modeloMensagemForm.jsp', 'modeloMensagem', 800, 595);"
                        action="#{MensagemBuilderControle.editarOld}"></a4j:jsFunction>

        <script>
            function editOld(codigo) {
                var hiddencodigoselecionado = document.getElementById('form:codigoselecionado');
                hiddencodigoselecionado.value = codigo;
                editModeloOld();
            }
        </script>

        <c:forEach items="#{MensagemBuilderControle.predefinidosAntigos}" var="pre" varStatus="var">
            <div class="caixa-build add">
                <c:if test="${not MensagemBuilderControle.meioEmail}">
                    ${pre.html}
                </c:if>
                <c:if test="${MensagemBuilderControle.meioEmail}">
                    <div class="thumb-html">
                            ${pre.html}
                    </div>
                </c:if>


                <div class="hover-layer">

                    <a class="btn-builder" onclick="enviar(${pre.codigo})" style="margin-top: ${MensagemBuilderControle.meioEmail ? '90' : '10'}px;" >
                        <i class="fa-icon-paper-plane"></i>Enviar
                    </a>
                    <a class="btn-builder" onclick="editOld(${pre.codigo}); RichFaces.showModalPanel('modalConfirmarExcluir')">
                        <i class="fa-icon-pencil"></i>Editar
                    </a>
                    <a class="btn-builder" onclick="excluir(${pre.codigo}); RichFaces.showModalPanel('modalConfirmarExcluir')">
                        <i class="fa-icon-trash"></i>Excluir
                    </a>
                </div>

                <div class="rodape-build">
                        ${pre.nome}
                </div>
            </div>
        </c:forEach>
    </h:panelGroup>
</h:panelGroup>


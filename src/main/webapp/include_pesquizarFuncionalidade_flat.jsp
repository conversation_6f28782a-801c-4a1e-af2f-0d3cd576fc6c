<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 03/09/2015
  Time: 17:29
  To change this template use File | Settings | File Templates.
--%>
  <rich:suggestionbox  width="337"
                       height="300"
                       for="campoBusca"
                       frequency="0"
                       popupStyle="margin-left: 12px;background-color: #000066;"
                       fetchValue="#{FuncionalidadeControle.rotulo}"
                       suggestionAction="#{FuncionalidadeControle.executarAutocompleteFuncionalidade}"
                       minChars="1" rowClasses="20"
                       status="true"
                       nothingLabel="Nenhuma funcionalidade Encontrada!"
                       styleClass="suggestionCampoBusca"
                       var="result" id="suggestionCampoBusca">
    <a4j:support  event="onselect" onsubmit="setDocumentCookie('popupsImportante', 'close',1);setTimeout(function() { setDocumentCookie('popupsImportante', '',1); }, 1000);"
                 action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                 actionListener="#{FuncionalidadeControle.abrirComAcaoListener}"
                 oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                 focus="buscaFuncionalidade" reRender="textfield2,suggestionFuncionalidade,menuLateral">
      <f:attribute name="paginaInicial" value="paginaInicial" />
      <f:attribute name="tipoConsulta" value="parametrizado"/>
    </a4j:support>
    <h:column>
      <h:graphicImage rendered="#{!result.cliente && result.funcionalidadeSistemaEnumAux.funcionalidadeSistemaEnum !=null}" style="width:28px;height:25px;" url="#{result.funcionalidadeSistemaEnumAux.funcionalidadeSistemaEnum.iconeModulo}"/>

      <a4j:mediaOutput     element="img"
                           rendered="#{!SuperControle.fotosNaNuvem && result.cliente}"
                           align="left" style="left:0px;width:25px;height:25px; border:none;border-radius:5px; "
                           cacheable="false" session="false"

                           title="#{result.clienteVO.pessoa.nome}"
                           createContent="#{FuncionalidadeControle.paintFoto}"
                           styleClass="shadow"
                           value="#{ImagemData}" mimeType="image/jpeg" >
        <f:param value="#{SuperControle.timeStamp}" name="time"/>
        <f:param name="largura" value="25"/>
        <f:param name="altura" value="25"/>
        <f:param name="pessoa" value="#{result.clienteVO.codigo}"></f:param>
      </a4j:mediaOutput>
      <h:graphicImage  rendered="#{SuperControle.fotosNaNuvem && result.cliente}"
                       width="150" height="180"
                       style="left:0px;width:25px;height:25px; border:none;border-radius:5px;"
                       url="#{FuncionalidadeControle.paintFotoDaNuvem}">
      </h:graphicImage>
      <h:panelGroup layout="block" style="height: 25px" rendered="#{!result.cliente && result.funcionalidadeClienteEnumAux.funcionalidadeClienteEnum !=null}"/>
    </h:column>

    <h:column>

      <h:panelGroup layout="block" rendered="#{!result.descricao and !result.subTitulo}" style="width: 250px;text-align: left;">
        <h:outputText rendered="#{!result.cliente && result.funcionalidadeClienteEnumAux.funcionalidadeClienteEnum !=null}" style="font-size: 15px;text-align: right;" styleClass="fa-icon-arrow-right"/>
        <h:outputText rendered="#{!FuncionalidadeControle.podeConsultarEmTodasEmpresas}"
                      style="text-align: left"
                      styleClass="textSmallFlat"
                      value="#{result.rotulo}"/>
        <h:panelGroup rendered="#{FuncionalidadeControle.podeConsultarEmTodasEmpresas}">
          <h:outputText
                  style="text-align: left"
                  styleClass="textSmallFlat"
                  value="#{result.rotulo}"/>
          <div>
            <h:outputText style="text-align: left" value="#{result.clienteVO.empresa.nome}"/>
          </div>
        </h:panelGroup>
      </h:panelGroup>

      <h:outputText style="text-align: left" rendered="#{result.autorizacaoCobranca}" value="#{result.itemDescricao }"/>

      <h:panelGroup layout="block" rendered="#{result.descricao and !result.subTitulo}" style="width: 250px;text-align: left;height: 20px;vertical-align: baseline;line-height:19px">
        <h:outputText style="text-align: left;color: #BBBDBF" styleClass="textSmallFlat" value="#{result.itemDescricao}"/>
      </h:panelGroup>
      <h:panelGroup layout="block" rendered="#{result.subTitulo}" style="width: 250px;text-align: left;height: 20px;vertical-align: baseline;line-height:19px">
        <h:outputText style="text-align: left;color: #BBBDBF" styleClass="textSmallFlatSubTitle" value="#{result.itemDescricao}"/>
      </h:panelGroup>
    </h:column>


  </rich:suggestionbox>
  <script>
    jQuery(document.getElementById("form:campoBusca")).bind('keypress', function(e) {

      if(e.keyCode==13){
        setDocumentCookie('popupsImportante', 'close',1);  
        fireElement('form:botaoBuscar');
        return false;
      }
    });
  </script>

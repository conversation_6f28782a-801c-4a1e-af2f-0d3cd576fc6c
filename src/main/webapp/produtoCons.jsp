<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style>
    .tabelaProduto tbody tr td:nth-child(6){
        text-align: right;
    }

    .caixaCarregandoCadastro{
        position: absolute;
        line-height: 35px;
        left: 33.3%;
        top: 125px;
    }
</style>
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_Produto_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Produto_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-um-produto-de-estoque/"/>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-2-3 text-right">
                        <h:panelGroup layout="block" style="line-height: 44px;display: flex;flex-wrap: nowrap;align-items: center;justify-content: flex-end;gap: 4px;"  >

                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" value="Situação "/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="margin-right: 10px;">
                                <h:selectOneMenu id="situacao" styleClass="exportadores" style="margin-right: 40px;"
                                                 value="#{ProdutoControle.situacaoFiltro}">
                                    <f:selectItems value="#{ProdutoControle.listaSelectItemSituacao}"/>
                                    <a4j:support event="onchange" oncomplete="recarregarTabelaProduto()"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             actionListener="#{ProdutoControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,descricao=Descrição,situacao=Situação,categoria_Apresentar=Categoria,tipoProduto_Apresentar=Tipo,valorFinal_Apresentar=Valor,qtdePontos=Pontos,
                                             codigoBarras=CodigoBarras,cfop=CFOP,ncm=NCM,cest=CEST,ncmNFCe=NCMNFCe,codigoListaServico=CodListaServiço,codigoBeneficioFiscal=CodBeneficioFisca,
                                             codigoTributacaoMunicipio=CodTribMunicípio,descricaoServicoMunicipio=DescSerMunicípio,enviarPercentualImposto=EnviarPercImposto,
                                             percentualFederal=PercFederal,percentualMunicipal=PercMunicipio,percentualEstadual=PercEstadual,situacaoTributariaICMS=SituaçãoICMS,
                                             isentoICMS=IsentoICMS,enviaAliquotaNFeICMS=AlíquotaNFeICMS,aliquotaICMS=AlíquotaICMS,situacaoTributariaPIS=SituaçãoPIS,isentoPIS=IsentoPIS,
                                             enviaAliquotaNFePIS=AlíquotaNFePIS,aliquotaPIS=AlíquotaPIS,situacaoTributariaCOFINS=SituaçãoCOFINS,isentoCOFINS=IsentoCOFINS,enviaAliquotaNFeCOFINS=AlíquotaNFeCOFIN,
                                             aliquotaCOFINS=AlíquotaCOFINS,situacaoTributariaISSQN=SituaçãoISSQN,aliquotaISSQN=AlíquotaISSQN"/>
                                <f:attribute name="prefixo" value="Produto"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             actionListener="#{ProdutoControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,descricao=Descrição,situacao=Situação,categoria_Apresentar=Categoria,tipoProduto_Apresentar=Tipo,valorFinal_Apresentar=Valor,qtdePontos=Pontos"/>
                                <f:attribute name="prefixo" value="Produto"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="btnLog"
                                             styleClass="exportadores margin-h-10"
                                             action="#{ProdutoControle.realizarConsultaLogObjetoGeral}"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnCadastrarViaXml"
                                             styleClass="pure-button pure-button-xml-nfe"
                                             action="#{ProdutoControle.irParaUploadNfe}">
                                &nbsp ${msg_bt.btn_cadastrar_via_xml}
                            </a4j:commandLink>

                            <a4j:commandLink id="btnNovo"
                                             styleClass="pure-button pure-button-primary"
                                             action="#{ProdutoControle.novo}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblProduto" class="tabelaProduto pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Produto_label_codigo}</th>
                    <th>${msg_aplic.prt_Produto_label_descricao}</th>
                    <th>${msg_aplic.prt_Produto_label_situacao}</th>
                    <th>${msg_aplic.prt_Produto_label_categoriaProduto}</th>
                    <th>${msg_aplic.prt_Produto_label_tipoProduto}</th>
                    <th style="text-align: right">${msg_aplic.prt_Produto_label_valor}</th>
                    <th>QTD PONTOS</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{ProdutoControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{ProdutoControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{ProdutoControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty ProdutoControle.mensagem}"
                              value=" #{ProdutoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty ProdutoControle.mensagemDetalhada}"
                              value=" #{ProdutoControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>


        <%@include file="/pages/ce/includes/include_modal_exibeLogEntidade.jsp" %>
    </h:panelGroup>
</f:view>
<script src="beta/js/dt-server.js" type="text/javascript"></script>

<script>
    function recarregarTabelaProduto() {
        var situacao = document.getElementById("form:situacao").value;
        tabelaAtual.dataTable().fnDestroy(0);
        iniTblServer("tabelaProduto", "${contexto}/prest/plano/produto?situacao="+situacao, null, 1, "asc", "true");
    }

    jQuery(window).on("load", function () {
        iniTblServer("tabelaProduto", "${contexto}/prest/plano/produto?situacao=${ProdutoControle.situacaoFiltroPadrao}", null, 1, "asc", "true");
    });
</script>

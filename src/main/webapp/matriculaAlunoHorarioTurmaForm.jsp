<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{MatriculaAlunoHorarioTurmaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_contrato}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="contrato" styleClass="camposObrigatorios" value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.contrato.codigo}" >
                            <f:selectItems  value="#{MatriculaAlunoHorarioTurmaControle.listaSelectItemContrato}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_contrato" action="#{MatriculaAlunoHorarioTurmaControle.montarListaSelectItemContrato}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:contrato"/>
                        <h:message for="contrato" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_pessoa}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="pessoa" styleClass="camposObrigatorios" value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.pessoa.codigo}" >
                            <f:selectItems  value="#{MatriculaAlunoHorarioTurmaControle.listaSelectItemPessoa}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_pessoa" action="#{MatriculaAlunoHorarioTurmaControle.montarListaSelectItemPessoa}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:pessoa"/>
                        <h:message for="pessoa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_modalidade}" />
                    <h:inputText  id="modalidade" size="10" maxlength="10" styleClass="campos" value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.modalidade}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_turma}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="turma" styleClass="camposObrigatorios" value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.turma.codigo}" >
                            <f:selectItems  value="#{MatriculaAlunoHorarioTurmaControle.listaSelectItemTurma}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_turma" action="#{MatriculaAlunoHorarioTurmaControle.montarListaSelectItemTurma}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:turma"/>
                        <h:message for="turma" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_horarioTurma}" />
                    <h:panelGroup>
                        <h:inputText  id="horarioTurma" required="true" size="10" maxlength="10" styleClass="camposObrigatorios" value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.horarioTurma}" />
                        <h:message for="horarioTurma" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_dataInicioMatricula}" />
                    <h:panelGroup>
                        <rich:calendar id="dataInicioMatricula"
                                       value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.dataInicioMatricula}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataInicioMatricula" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <%--h:inputText  id="dataInicioMatricula" onkeypress="return mascara(this.form, 'form:dataInicioMatricula', '99/99/9999', event);" size="10" maxlength="10" styleClass="campos" value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.dataInicioMatricula}" >
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:inputText--%>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_dataFimMatricula}" />
                    <h:panelGroup>
                        <rich:calendar id="dataFimMatricula"
                                       value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.dataFimMatricula}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataFimMatricula" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <%--h:inputText  id="dataFimMatricula" onkeypress="return mascara(this.form, 'form:dataFimMatricula', '99/99/9999', event);" size="10" maxlength="10" styleClass="campos" value="#{MatriculaAlunoHorarioTurmaControle.matriculaAlunoHorarioTurmaVO.dataFimMatricula}" >
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:inputText--%>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{MatriculaAlunoHorarioTurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{MatriculaAlunoHorarioTurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{MatriculaAlunoHorarioTurmaControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="salvar" action="#{MatriculaAlunoHorarioTurmaControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{MatriculaAlunoHorarioTurmaControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="consultar" immediate="true" action="#{MatriculaAlunoHorarioTurmaControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:contrato").focus();
</script>
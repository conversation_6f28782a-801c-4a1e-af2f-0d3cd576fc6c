<%--
    Document   : controleOperacoesResumoClienteRes
    Author     : Carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo de Cliente(s)"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <c:set var="titulo" scope="session" value="Resumo de Cliente(s) Total Itens:${RelControleOperacoesControle.qtdItensLista}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-controle-de-operacoes-de-excecoes-adm/"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block">
                            <a4j:commandLink id="exportarExcel"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,situacao_Apresentar=Situação,dataOperacao_Apresentar=Data de Lançamento,dataInicio_Apresentar=Data de Início,dataFim_Apresentar=Data Final,justificativa_Apresentar=Justificativa"/>
                                <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>
                            <%--BOTÃO PDF--%>
                            <a4j:commandLink id="exportarPdf"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,situacao_Apresentar=Situação,dataOperacao_Apresentar=Data de Lançamento,dataInicio_Apresentar=Data de Início,dataFim_Apresentar=Data Final,justificativa_Apresentar=Justificativa"/>
                                <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                    value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}" rows="50" var="resumoPessoa" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{resumoPessoa.clienteVO.matricula}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="MATRÍCULA" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.clienteVO.matricula}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.clienteVO.pessoa.nome}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="NOME" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.clienteVO.pessoa.nome}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.clienteVO.situacao_Apresentar}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="SITUAÇÃO" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.clienteVO.situacao_Apresentar}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoOperacaoVO.dataOperacao}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DATA DE LANÇAMENTO" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.contratoOperacaoVO.dataOperacao}" >
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoOperacaoVO.dataInicioEfetivacaoOperacao}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DATA DE INÍCIO" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.contratoOperacaoVO.dataInicioEfetivacaoOperacao}" >
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoOperacaoVO.dataFimEfetivacaoOperacao}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DATA FINAL" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.contratoOperacaoVO.dataFimEfetivacaoOperacao}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contratoOperacaoVO.justificativaApresentar}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="JUSTIFICATIVA" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.contratoOperacaoVO.justificativaApresentar}">
                            </h:outputText>
                        </rich:column>
                        <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center" >
                            <a4j:commandLink styleClass="linkPadrao texto-size-16-real texto-cor-azul" action="#{RelControleOperacoesControle.irParaTelaCliente}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <f:param name="state" value="AC"/>
                                <i class="fa-icon-search"></i>
                            </a4j:commandLink>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>


<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 02/02/2016
  Time: 09:43
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="H" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="ui" uri="http://richfaces.org/a4j" %>

   <rich:modalPanel domElementAttachment="parent" id="panelRenovarSessao" styleClass="novaModal"
                 width="550"
                 onshow="setTotalProgressiveBar();"
                 autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Para sua segurança, sua sessão finalizará em.."/>
        </h:panelGroup>

    </f:facet>
    <h:panelGroup layout="block">
        <h:panelGroup id="regressiveBar" layout="block"  styleClass="bg-vermelho progressiveBar"/>
        <h:panelGroup layout="block" id="tblRenovarSessao" style="text-align: center;margin-top: 15px;">

            <h:outputText  id="regressiveTime" styleClass="texto-cor-vermelho texto-size-60-real texto-font" value=""/>
            <h:panelGroup layout="block" styleClass="container-botoes" style="text-align: right;border-top: 1px solid #E5E5E5;margin-top: 25px;line-height: 40px;height: 29px;">
                <a4j:commandLink   status="statusHora"
                                   title="Clique aqui apra renovar sua sessão"
                                   reRender="horaSistema"
                                   action="#{SuporteControle.poll}"
                                   styleClass="linkPadrao texto-size-16-real texto-cor-azul rotate-hover"
                                   oncomplete="resetTime(tempoEmMillis);#{SuperControle.enableSetLastActionTime ? 'setLastActionTime();' : ''}Richfaces.hideModalPanel('panelRenovarSessao');">
                    <i class="fa-icon-refresh"></i>  Retornar minha sessão

                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</rich:modalPanel>
<c:if test="${!SuperControle.menuZwUi and MenuControle.apresentarMenu}">
<h:panelGroup styleClass="container-navbar" layout="block">
    <h:panelGroup layout="block" styleClass="navbar navbar-default">
        <h:panelGroup layout="block" styleClass="container-fluid" id="navBarTopo">
            <h:panelGroup layout="block" styleClass="nav navbar-nav">

                <%--Menu Inicio--%>
                <h:panelGroup id="item1" layout="block" styleClass="item1 menuItem">

                        <a4j:commandLink action="#{NavegacaoControle.abrirTelaInicialZW}"
                                       style="text-decoration: none">
                            <i style="font-size:21px;" class="fa-icon-home"></i>
                        </a4j:commandLink>

                </h:panelGroup>

                <%--Menu Cadastro--%>
                <h:panelGroup id="item2" layout="block" styleClass="item2 menuItem">
                    <a4j:commandLink  id="linkCadastroMenuSuperior" action="#{NavegacaoControle.abrirTelaCadastros}"
                                   style="text-decoration: none" value="Cadastros"/>
                </h:panelGroup>

                <%--Business Intelligence--%>
                <h:panelGroup id="item3" rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarBI}"
                              layout="block" styleClass="item3 menuItem">

                        <a4j:commandLink rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarBI}"
                                       id="linkBIMenuSuperior"
                                       action="#{BIControle.irTelaBI}"
                                       style="text-decoration: none"
                                       value="Business Intelligence"/>

                </h:panelGroup>

                <%--Relatórios--%>
                <h:panelGroup id="itemRelatorios" rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarBI}"
                              layout="block" styleClass="itemRelatorios menuItem">

                    <a4j:commandLink rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarBI}"
                                     id="linkRelatoriosMenuSuperior"
                                     action="#{LoginControle.irParaTelaRelatorios}"
                                     style="text-decoration: none"
                                     value="Relatórios"/>

                </h:panelGroup>

                <%--Menu Clientes--%>
                <h:panelGroup rendered="#{LoginControle.permissaoAcessoMenuVO.cliente}" id="item4" layout="block" styleClass="item4 menuItem">

                        <a4j:commandLink id="linkClienteMenuSuperior"
                                         action="#{ConsultaClienteControle.todosClientes}"
                                         value="Clientes">
                            
                        </a4j:commandLink>

                </h:panelGroup>

                <%--Menu Incluir Clientes--%>
                <h:panelGroup id="item5" rendered="#{LoginControle.permissaoAcessoMenuVO.cliente}" layout="block" styleClass="item5 menuItem">

                        <a4j:commandLink id="idCLIncluirCliente" 
                                         action="#{PreCadastroClienteControle.novo}"
                                         value="#{LoginControle.priorizarVendaRapida ?  'Incluir Visitante' : 'Incluir Cliente' }" accesskey="2">
                            <c:if test="${modulo eq '4bf2add2267962ea87f029fef8f75a2f'}">
                                <f:attribute name="modulo" value="4bf2add2267962ea87f029fef8f75a2f"/>
                            </c:if>
                        </a4j:commandLink>

                </h:panelGroup>

                <%--Menu Venda rapida--%>
                <h:panelGroup id="item8"
                              rendered="#{LoginControle.permissaoAcessoMenuVO.vendaRapida}"
                              layout="block" styleClass="item8 menuItem">

                    <h:outputLink id="linkVendaRapidaSup" value="inclusaoAlunoVenda.jsp"
                                  styleClass="titulo3 linkFuncionalidade">
                        <a4j:support
                                event="onclick"
                                action="#{LoginControle.notificarRecursoEmpresaVendaRapida}"/>
                        Venda Rápida</h:outputLink>

                </h:panelGroup>

                <%--Menu Agenda--%>
                <h:panelGroup id="item6" layout="block" styleClass="item6 menuItem"
                              rendered="#{LoginControle.apresentarLinkEstudio}">

                        <h:commandLink id="linkStudioMenuSuperior"
                                         action="#{LoginControle.abrirModuloEstudio}">
                            Agenda
                        </h:commandLink>

                </h:panelGroup>


                <%--Menu Game --%>
                <h:panelGroup id="itemGame" rendered="#{LoginControle.permissaoAcessoMenuVO.gameOfResults}"
                              layout="block" styleClass="itemGame menuItem">

                    <h:outputLink target="_blank"
                                  value="#{BIControle.urlGame}" id="linkGameMenuSuperior">
                        <h:outputText  value="Game of Results"/>
                        <a4j:support event="onclick" action="#{LoginControle.notificarRecursoEmpresaGameOfResults}"/>
                    </h:outputLink>

                </h:panelGroup>

                <h:panelGroup id="itemClubeVantagens" layout="block" styleClass="itemClubeVantagens menuItem">
                    <a4j:commandLink id="linkClubeVantagensMenuSuperior"
                                         action="#{ClubeVantagensControle.carregarBI}"
                                         value="Clube de Vantagens">
                            
                    </a4j:commandLink>
                </h:panelGroup>

            <h:panelGroup id="itemProtheus" rendered="#{LoginControle.integraProtheus}"
                          layout="block" styleClass="itemProtheus menuItem">

                <h:outputLink id="linkProtheusSup" value="logprotheus.jsp?chave=#{LoginControle.key}"
                              target="integprotheus"
                              styleClass="titulo3 linkFuncionalidade">Integração</h:outputLink>

            </h:panelGroup>

            </h:panelGroup>

            <script>
                jQuery(window).scroll(function () {
                    topAtual = jQuery(window).scrollTop();
                    if (topAtual > 82) {
                        jQuery('.navbar').addClass('barraFixa');
                    } else if (topAtual <= 82) {
                        jQuery('.navbar').removeClass('barraFixa');
                    }

                });
            </script>

            <c:if test="${not SuperControle.menuZwUi}">
                <jsp:include page="include_menu_campoBusca.jsp" flush="true"/>
            </c:if>


        </h:panelGroup>
    </h:panelGroup>
  </h:panelGroup>
</c:if>

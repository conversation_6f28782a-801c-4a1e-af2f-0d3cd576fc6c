<%--
    Document   : resumoAlteracoesPagamentoRes.jsp
    Author     : Carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo das Alterações de Pagamentos"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" style="overflow: hidden" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <c:set var="titulo" scope="session" value="Resumo das Alterações de Pagamentos   -   Total:${fn:length(RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs)} "/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-controle-de-operacoes-de-excecoes-adm/"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" styleClass="font-size-Em-max" >
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block">
                            <a4j:commandLink id="exportarExcel"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                <f:attribute name="atributos" value="chavePrimaria=Chave Primária,nomeEntidade=Nome Entidade,nome_Apresentar=Nome do Pagador,dataLogAlteracao=Data Alteração,operacoes=Operações,responsavelAlteracao=Responsável Alteração"/>
                                <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>
                            <%--BOTÃO PDF--%>
                            <a4j:commandLink id="exportarPdf"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"
                                             oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="itemExportacao" value="#{RelControleOperacoesControle.controleOperacoesRelVO.itemExportar}"/>
                                <f:attribute name="atributos" value="chavePrimaria=Chave Primária,nomeEntidade=Nome Entidade,nome_Apresentar=Nome do Pagador,dataLogAlteracao=Data Alteração,operacoes=Operações,responsavelAlteracao=Responsável Alteração"/>
                                <f:attribute name="prefixo" value="ControleOpExcecao"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable width="100%" styleClass="tabelaSimplesCustom font-size-Em-max" id="tabelaRes"
                                    value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}" rows="50" var="resumoLog" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{resumoLog.logAgrupadoChavePrimaria.chavePrimaria}" styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font"  value="CHAVE PRIMÁRIA" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  value="#{resumoLog.logAgrupadoChavePrimaria.chavePrimaria}"/>
                            <h:outputText value="" />
                        </rich:column>
                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left"  >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font" value="NOME ENTIDADE" />
                            </f:facet>
                            <h:outputText id="nomeEntidade" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{resumoLog.logAgrupadoChavePrimaria.nomeEntidade}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoLog.clienteVO.pessoa.nome}" styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font" value="NOME DO PAGADOR" />
                            </f:facet>
                            <h:outputText id="nomePessoa" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{resumoLog.clienteVO.pessoa.nome}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoLog.logAgrupadoChavePrimaria.dataHoraAlteracao_Apresentar}" styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font" value="DATA ALTERAÇÃO" />
                            </f:facet>
                            <h:outputText id="dataHora" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{resumoLog.logAgrupadoChavePrimaria.dataHoraAlteracao_Apresentar}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoLog.logAgrupadoChavePrimaria.operacao}" styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font" value="OPERAÇÕES" />
                            </f:facet>
                            <h:outputText id="operacao" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{resumoLog.logAgrupadoChavePrimaria.operacao}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoLog.logAgrupadoChavePrimaria.responsavelAlteracao}" styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <f:facet name="header">
                                <h:outputText styleClass="texto-bold  texto-size-14-real texto-cor-cinza texto-font" value="RESPONSÁVEL ALTERAÇÃO" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"  id="responsavel"
                                          value="#{resumoLog.logAgrupadoChavePrimaria.responsavelAlteracao}"/>
                        </rich:column>
                        <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" >
                            <a4j:commandLink styleClass="linkPadrao texto-size-16-real texto-cor-azul" action="#{RelControleOperacoesControle.selecionarListaLog}" reRender="form, formLog" oncomplete="Richfaces.showModalPanel('panelLog')">
                                <i class="fa-icon-search"></i>
                                <f:param name="state" value="AC"/>
                            </a4j:commandLink>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
                </h:panelGrid>
            </h:form>
            <%@include file="include_logLista.jsp" %>
        </body>
    </html>

</h:panelGrid>
</f:view>


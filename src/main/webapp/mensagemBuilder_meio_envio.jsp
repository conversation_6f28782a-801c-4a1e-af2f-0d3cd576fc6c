<%@include file="includes/imports.jsp" %>
<h:panelGroup id="botoesTL"
              style="display: inline-flex; padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%; position: relative; height: 40px; margin-top: 25px"
              rendered="#{not MensagemBuilderControle.building}"
              layout="block">
    <a4j:commandLink
            id="btnEnvioEmail"
            styleClass="botaoModoTimeLine filtrosPrincipais #{MensagemBuilderControle.meioEmail ? 'ativo' : ''}"
            action="#{MensagemBuilderControle.selecionarMeioEmail}"
            oncomplete="location.reload();">
        <h:outputText value="E-mail"/>
    </a4j:commandLink>

    <a4j:commandLink styleClass="botaoModoTimeLine filtrosLote #{MensagemBuilderControle.meioSms ? 'ativo' : ''}"
                     id="btnEnvioSms"
                     action="#{MensagemBuilderControle.selecionarMeioSms}"
                     oncomplete="location.reload();">
        <h:outputText value="SMS"/>
    </a4j:commandLink>

    <a4j:commandLink styleClass="botaoModoTimeLine cpfnomematricula #{MensagemBuilderControle.meioApp ? 'ativo' : ''}"
                     id="btnEnvioApp"
                     action="#{MensagemBuilderControle.selecionarMeioApp}"
                     oncomplete="location.reload();">
        <h:outputText value="APP"/>
    </a4j:commandLink>


    <a4j:commandLink styleClass="botaoModoTimeLine cpfnomematricula #{MensagemBuilderControle.meioBotConversa ? 'ativo' : ''}"
                     id="btnEnviooBtoConversa"
                     action="#{MensagemBuilderControle.selecionarMeioBotConversa}"
                     oncomplete="location.reload();"
                     rendered="#{MensagemBuilderControle.habilitarbotconversa}"
                    >
        <h:outputText value="GymBot"/>
    </a4j:commandLink>

    <a4j:commandLink styleClass="botaoModoTimeLine cpfnomematricula #{MensagemBuilderControle.meioGymbotPro ? 'ativo' : ''}"
                     id="btnEnviooBtoConversa2"
                     action="#{MensagemBuilderControle.selecionarMeioGymbotPro}"
                     oncomplete="location.reload();"
                     rendered="#{MensagemBuilderControle.habilitargymbotpro}"
    >
        <h:outputText value="GymBotPro"/>
    </a4j:commandLink>

    <c:if test="${MalaDiretaControle.existeConfiguracaoGupshupWhatsApp()}">
        <a4j:commandLink styleClass="botaoModoTimeLine cpfnomematricula #{MensagemBuilderControle.meioWhatsApp ? 'ativo' : ''}"
                         id="btnEnvioWhatsApp"
                         action="#{MensagemBuilderControle.selecionarMeioWhatsapp}"
                         oncomplete="location.reload();">
            <h:outputText value="WhatsApp"/>
        </a4j:commandLink>
    </c:if>

    <h:panelGroup rendered="#{MensagemBuilderControle.meioEmail and (MensagemBuilderControle.limite > 0 or MalaDiretaControle.configCRM.integracaoPacto)}" layout="block"
                  styleClass="saldos">
        <h:panelGroup layout="block" styleClass="saldo tooltipster" rendered="#{MensagemBuilderControle.limite > 0}">

            <a4j:commandLink reRender="mensagembuilder "
                             action="#{MensagemBuilderControle.atualizarEmailsHoje}"
                             style="color: #0090FF; font-size: 12px; float: right">
                <i class="fa-icon-refresh tooltipster" title="Atualizar saldo" style="font-size: 12px;"></i>
            </a4j:commandLink>


            <span class="titulo-saldo">Limite di�rio</span>

            <div class="saldo-valor">
                <div>Enviados hoje: ${MensagemBuilderControle.emailsHoje}</div>
                <div class="saldo-expira">Limite di�rio de ${MensagemBuilderControle.limite}</div>
            </div>

            <h:panelGroup styleClass="barra-saldo">
                <h:panelGroup styleClass="inside-barra-saldo" layout="block"
                              style="width: #{MensagemBuilderControle.percentualEnviadosEmail}%;"></h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup rendered="#{MalaDiretaControle.configCRM.integracaoPacto}" layout="block" styleClass="saldo tooltipster">
            <a4j:commandLink reRender="mensagembuilder "
                             action="#{MensagemBuilderControle.atualizarEmailsHoje}"
                             style="color: #0090FF; font-size: 12px; float: right">
                <i class="fa-icon-refresh tooltipster" title="Atualizar saldo" style="font-size: 12px;"></i>
            </a4j:commandLink>

            <a4j:commandLink action="#{MensagemBuilderControle.abrirPactoStore}"
                             rendered="#{LoginControle.apresentarPactoStore}"
                             style="color: #0090FF; font-size: 12px; margin-right: 10px; float: right">
                <i class="fa-icon-shopping-cart tooltipster" title="Adquirir mais limite" style="font-size: 12px;"></i>
            </a4j:commandLink>

            <span class="titulo-saldo">Limite Mensal</span>

            <div class="saldo-valor">
                <div>Enviados no m�s: ${MensagemBuilderControle.emailsMes}</div>
                <div class="saldo-expira">Limite mensal de ${MalaDiretaControle.configCRM.limiteMensalPacto}</div>
            </div>

            <h:panelGroup styleClass="barra-saldo">
                <h:panelGroup styleClass="inside-barra-saldo" layout="block"
                              style="width: #{MensagemBuilderControle.percentualEnviadosEmailMes}%;"></h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup rendered="#{MensagemBuilderControle.meioSms and not MalaDiretaControle.apresentarOpcoesSMS}"
                  layout="block" styleClass="saldos">
        <div class="saldo tooltipster" title="${MalaDiretaControle.saldo.title}">
            <a4j:commandLink reRender="mensagembuilder "
                             action="#{MalaDiretaControle.consultarSaldo}"
                             style="color: #0090FF; font-size: 12px; float: right">
                <i class="fa-icon-refresh tooltipster" title="Atualizar saldo" style="font-size: 12px;"></i>
            </a4j:commandLink>

            <span class="titulo-saldo">Transacional</span>

            <div class="saldo-valor">
                <div>Saldo restante: ${MalaDiretaControle.saldo.balance}</div>
                <div class="saldo-expira">Expira em ${MalaDiretaControle.saldo.validade}</div>
            </div>

            <h:panelGroup styleClass="barra-saldo">
                <h:panelGroup styleClass="inside-barra-saldo" layout="block"
                              style="width: #{MalaDiretaControle.saldo.percentual}%;"></h:panelGroup>
            </h:panelGroup>
        </div>

        <c:if test="${MalaDiretaControle.saldoMarketing ne null}">
            <div class="saldo tooltipster" title="${MalaDiretaControle.saldoMarketing.title}">
                <a4j:commandLink reRender="mensagembuilder "
                                 action="#{MalaDiretaControle.consultarSaldo}"
                                 style="color: #0090FF; font-size: 12px; float: right">
                    <i class="fa-icon-refresh tooltipster" title="Atualizar saldo" style="font-size: 12px;"></i>
                </a4j:commandLink>

                <span class="titulo-saldo">Marketing</span>

                <div class="saldo-valor">
                    <div>Saldo restante: ${MalaDiretaControle.saldoMarketing.balance}</div>
                    <div class="saldo-expira">Expira em ${MalaDiretaControle.saldoMarketing.validade}</div>
                </div>

                <h:panelGroup styleClass="barra-saldo">
                    <h:panelGroup styleClass="inside-barra-saldo" layout="block"
                                  style="width: #{MalaDiretaControle.saldoMarketing.percentual}%;"></h:panelGroup>
                </h:panelGroup>
            </div>
        </c:if>
    </h:panelGroup>
</h:panelGroup>
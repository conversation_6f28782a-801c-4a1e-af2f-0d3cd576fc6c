<%--
  Created by IntelliJ IDEA.
  User: anderson
  Date: 05/06/19
  Time: 08:45
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="../sample.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="editorModelopropriedades" basename="propriedades.editorModelopropriedades"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Modelo de Orçamento"/>
    </title>

    <script type="text/javascript">
        function FCKeditor_OnComplete(editorInstance) {
            window.status = editorInstance.Description;
        }
    </script>

    <meta name="robots" content="noindex, nofollow" />
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="Modelo de Orçamento"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}o-que-e-orcamento/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <a4j:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{ModeloOrcamentoControle.liberarBackingBeanMemoria}"
                           onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                           id="idLiberarBackingBeanMemoria" style="display: none" />

            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_codigo}" />

                    <h:panelGroup>
                        <h:inputText  id="codigo"
                                      size="10"
                                      maxlength="10"
                                      onblur="blurinput(this);"
                                      onfocus="focusinput(this);"
                                      styleClass="camposSomenteLeitura"
                                      readonly="true"
                                      value="#{ModeloOrcamentoControle.modeloOrcamentoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_descricao}" />

                    <h:inputText  id="descricao"  size="45" maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ModeloOrcamentoControle.modeloOrcamentoVO.descricao}" />

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_situacao}" />

                    <h:selectOneMenu  id="situacao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ModeloOrcamentoControle.modeloOrcamentoVO.situacao}" >
                        <f:selectItems  value="#{ModeloOrcamentoControle.listaSelectItemSituacao}" />
                    </h:selectOneMenu>

                    <h:outputText  styleClass="tituloCampos" value="* Modalidade:" />

                    <h:selectOneMenu  id="modalidade" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ModeloOrcamentoControle.modeloOrcamentoVO.codModalidade}" >
                        <f:selectItems  value="#{ModeloOrcamentoControle.listaSelectItemModalidade}" />
                    </h:selectOneMenu>

                    <h:outputText  styleClass="tituloCampos" value="Pacote:" />

                    <h:selectOneMenu  id="pacote" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ModeloOrcamentoControle.modeloOrcamentoVO.codPacote}" >
                        <f:selectItems  value="#{ModeloOrcamentoControle.listaSelectItemPacote}" />
                    </h:selectOneMenu>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_dataDefinicao}" />

                    <h:panelGroup>
                        <rich:calendar id="dataDefinicao"
                                       value="#{ModeloOrcamentoControle.modeloOrcamentoVO.dataDefinicao}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataDefinicao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_PlanoTextoPadrao_responsavelDefinicao}" />

                    <h:panelGroup>
                        <h:inputText id="responsavelDefinicao" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura"   size="5" maxlength="10" value="#{PlanoTextoPadraoControle.planoTextoPadraoVO.responsavelDefinicao.codigo}"/>
                        <h:outputText  styleClass="tituloCampos" value="#{ModeloOrcamentoControle.modeloOrcamentoVO.responsavelDefinicao.nome}" />
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid id="gridMarcadores" columns="1" columnClasses="colunaCentralizada" headerClass="subordinado" width="100%">
                    <f:facet name="header">
                        <h:outputText value="Texto"/>
                    </f:facet>

                </h:panelGrid>

                <h:panelGrid id="panelGridEditor" columns="1" rowClasses="linhaImpar, linhaPar" width="100%">
                    <f:facet name="footer">
                        <rich:editor id="richEditor" viewMode="visual" theme="advanced" configuration="editorModelopropriedades"
                                     height="250" width="735"  value="#{ModeloOrcamentoControle.modeloOrcamentoVO.texto}"  />
                    </f:facet>
                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:outputText value="Tag = [exemplo_exemplo]"/>

                    <h:outputText value="Nome da modalidade = [ORCA]NomeModalidade"/>

                    <h:outputText value="Turma desta modalidade com horarios e vagas = [ORCA]HorariosTurma"/>

                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>

                        <h:commandButton rendered="#{ModeloOrcamentoControle.sucesso}" image="./imagens/sucesso.png"/>

                        <h:commandButton rendered="#{ModeloOrcamentoControle.erro}" image="./imagens/erro.png"/>

                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgModelo" styleClass="mensagem"  value="#{ModeloOrcamentoControle.mensagem}"/>
                            <h:outputText id="msgModeloDet" styleClass="mensagemDetalhada" value="#{ModeloOrcamentoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{ModeloOrcamentoControle.novo}" value="#{msg_bt.btn_novo}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <h:commandButton id="salvar" action="#{ModeloOrcamentoControle.gravar}" value="#{msg_bt.btn_gravar}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <h:commandButton id="excluir" onclick="fireElementFromAnyParent('form:btnAtualizaTempo');return confirm('Confirma exclusão dos dados?');"
                                             action="#{ModeloOrcamentoControle.excluir}" value="#{msg_bt.btn_excluir}"
                                             alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>

                            <h:outputText value="    "/>
                            <h:panelGroup>
                                <a4j:commandLink id="imprimir" value="Imprimir Orçamento"
                                                 onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                 action="#{ModeloOrcamentoControle.imprimirOrcamento}" reRender="panelMensagem"
                                                 styleClass="botoes nvoBt btSec"
                                                 style="display: inline-block; padding: 6px 15px;"
                                                 oncomplete="#{ModeloOrcamentoControle.abrirPDF}">
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:outputText value="    "/>

                            <h:commandButton id="consultar" immediate="true" action="#{ModeloOrcamentoControle.inicializarConsultar}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="#{msg_bt.btn_consultar}" alt="#{msg.msg_consultar_dados}"
                                             accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink action="#{ModeloOrcamentoControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>

<script>
    document.getElementById("form:descricao").focus();
</script>

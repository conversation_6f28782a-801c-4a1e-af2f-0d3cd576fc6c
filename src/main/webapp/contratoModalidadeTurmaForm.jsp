<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ContratoModalidadeTurma_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ContratoModalidadeTurma_tituloForm}"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ContratoModalidadeTurma_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContratoModalidadeTurma_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{ContratoModalidadeTurmaControle.contratoModalidadeTurmaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContratoModalidadeTurma_contratoModalidade}" />
                    <h:panelGroup>
                        <h:inputText  id="contratoModalidade" required="true" size="10" maxlength="10" styleClass="camposObrigatorios" value="#{ContratoModalidadeTurmaControle.contratoModalidadeTurmaVO.contratoModalidade}" />
                        <h:message for="contratoModalidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContratoModalidadeTurma_turma}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="turma" styleClass="camposObrigatorios" value="#{ContratoModalidadeTurmaControle.contratoModalidadeTurmaVO.turma.codigo}" >
                            <f:selectItems  value="#{ContratoModalidadeTurmaControle.listaSelectItemTurma}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_turma" action="#{ContratoModalidadeTurmaControle.montarListaSelectItemTurma}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:turma"/>
                        <h:message for="turma" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ContratoModalidadeHorarioTurma_tituloForm}"/>
                    </f:facet>
                    <h:panelGrid columns="2" width="100%" styleClass="tabFormSubordinada" footerClass="colunaCentralizada">
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContratoModalidadeHorarioTurma_horarioTurma}" />
                        <h:inputText  size="10" maxlength="10" styleClass="camposObrigatorios" value="#{ContratoModalidadeTurmaControle.contratoModalidadeHorarioTurmaVO.horarioTurma}" />
                    </h:panelGrid>
                    <h:commandButton action="#{ContratoModalidadeTurmaControle.adicionarContratoModalidadeHorarioTurma}" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                    <h:dataTable id="contratoModalidadeHorarioTurmaVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                 rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                 value="#{ContratoModalidadeTurmaControle.contratoModalidadeTurmaVO.contratoModalidadeHorarioTurmaVOs}" var="contratoModalidadeHorarioTurma">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_aplic.prt_ContratoModalidadeHorarioTurma_horarioTurma}" />
                            </f:facet>
                            <h:outputText  value="#{contratoModalidadeHorarioTurma.horarioTurma}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                            </f:facet>
                            <h:panelGroup>
                                <h:commandButton id="editarItemVenda" immediate="true" action="#{ContratoModalidadeTurmaControle.editarContratoModalidadeHorarioTurma}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                <h:outputText value="    "/>

                                <h:commandButton id="removerItemVenda" immediate="true" action="#{ContratoModalidadeTurmaControle.removerContratoModalidadeHorarioTurma}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                            </h:panelGroup>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ContratoModalidadeTurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ContratoModalidadeTurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{ContratoModalidadeTurmaControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{ContratoModalidadeTurmaControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{ContratoModalidadeTurmaControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{ContratoModalidadeTurmaControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:contratoModalidade").focus();
</script>
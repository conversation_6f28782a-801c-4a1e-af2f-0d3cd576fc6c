<%@include file="includes/imports.jsp" %>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/font-awesome.css" type="text/css" rel="stylesheet"/>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
<style type="text/css">
    .panelRichEditor100 .mceLayout {
        width: 100% !important;
        border-style: none !important;
    }

    .richEditorSemBorda .richfacesSimpleSkin iframe {
        border-style: none !important;
    }

    .richEditorSemTollBar .mceToolbar {
        display: none !important;

    }

    .colunaBotaoExpandir {
        width: 2%;
        text-align: left;
    }

    .colunaCodigoAtividade {
        float: left;
    }

    .td.colunaDescricaoAtividade {
        float: left;
    }

    .colunaAberturaAtividade {
        float: right;
    }

    .colunaTempoAtividade {
        text-align: left;
    }

    .labelTableSol {
        color: #777777 !important;;
        font-size: 16px !important;
        font-family: Arial, Verdana, sans-serif;
    }

    .fontRichEditor .mceContentBody {
        color: #777777 !important;;
        font-size: 16px !important;
        font-family: Arial, Verdana, sans-serif;
    }

    .rich-table.richTabelaSolicitacoes {
        border-style: none !important;
        color: #777777 !important;;
        font-size: 16px !important;
    }

    .table.tabelaSolicitacoes {
        border-style: none !important;
        color: #777777 !important;
        font-size: 16px !important;
    }

    .colunaSemBorda {
        border-style: none !important;
    }

</style>

<h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
    <h:panelGroup styleClass="container-box-header" layout="block">
        <h:panelGroup id="pgAtualizarSolConcluida" layout="block" styleClass="margin-box">
            <a4j:commandLink action="#{CanalPactoControle.atualizarConsultaSolicitacoesConcluida}"
                             title="Clique aqui para atualizar as solicita��es conclu�das."
                             reRender="tabelaSolicitacaoConcluida, pgAtualizarSolConcluida"
                             style="text-decoration: none">
                <h:outputText value="Solicita��es conclu�das at� #{CanalPactoControle.dataSolConcluida}"
                              style="font-family: Arial; font-weight: bold; font-size: 16px; text-decoration: none"/>
            </a4j:commandLink>

            <a4j:commandLink action="#{CanalPactoControle.expandirTudoSolConcluida}"
                             style="float: right; text-decoration: none"
                             reRender="tabelaSolicitacaoConcluida" value="Expandir Tudo"/>

            <a4j:commandLink action="#{CanalPactoControle.retrairTudoSolConcluida}"
                             reRender="tabelaSolicitacaoConcluida"
                             style="float: right;  margin-right: 10px; text-decoration: none"
                             value="Retrair Tudo"/>

        </h:panelGroup>
    </h:panelGroup>

    <rich:dataTable id="tabelaSolicitacaoConcluida" value="#{CanalPactoControle.listaSolicitacoesConcluidas}" var="item"
                    width="100%"
                    rowClasses="linhaPar, linhaImpar" styleClass="richTabelaSolicitacoes"
                    columnClasses="colunaSemBorda"
                    cellpadding="0" cellspacing="0">
        <f:facet name="footer">
            <h:outputText style="float: right"
                          styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold"
                          value="#{fn:length(CanalPactoControle.listaSolicitacoesConcluidas)} registros"/>
        </f:facet>

        <rich:column style="width: 100%;" id="t1">

            <h:panelGrid id="pglink" styleClass="tabelaSolicitacoes" width="100%" columns="1" cellpadding="10"
                         cellspacing="0">

                <h:panelGrid width="100%" styleClass="tabelaSolicitacoes"
                             columnClasses="colunaCodigoAtividade,colunaAberturaAtividade" columns="2">

                    <a4j:commandLink title="#{item.titleMostrarOcultarAndamentos}"
                                     styleClass="labelTableSol"
                                     action="#{CanalPactoControle.exibirAndamentosDaAtividade}"
                                     reRender="tabelaSolicitacaoConcluida">
                        <h:outputText value="#{item.codigo}"/>
                        <h:outputText style="padding-left: 20px" value="#{item.titulo}"/>
                    </a4j:commandLink>


                    <a4j:commandLink title="#{item.labelDataAberturaSolicitacao}"
                                     action="#{CanalPactoControle.exibirAndamentosDaAtividade}"
                                     styleClass="labelTableSol tooltipster"
                                     rendered="#{item.mostrarTempoEsperaAtendimento}"
                                     reRender="tabelaSolicitacaoEmAberto">
                        <h:outputText value="#{item.dataCadastroComHora_Apresentar}"/>
                    </a4j:commandLink>

                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="pgAndamentos" styleClass="colunaCentralizada">
                <rich:dataTable id="tabelaAndamentos" width="95%"
                                columnClasses="colunaEsquerda, colunaEsquerda, colunaEsquerda,colunaEsquerda"
                                headerClass="colunaEsquerda"
                                styleClass="tabelaSolicitacoes"
                                style="margin: 0 auto 3%;"
                                value="#{item.andamentos}" var="andamento"
                                rendered="#{item.exibirAndamentos}">
                    <f:facet name="header">
                        <h:panelGroup layout="block" id="pnlDescricao" style="margin-bottom: 10px"
                                      rendered="#{item.exibirAndamentos}">
                            <h:panelGrid columns="1">
                                <h:outputText styleClass="labelTableSol" style="font-weight: bold;" value="Descri��o do cliente:"/>
                            </h:panelGrid>
                            <h:panelGrid columns="1">
                                <h:outputText styleClass="subtitulo" style="color: #777777 !important; padding-left: 10px" value="#{item.descricao}"/>
                            </h:panelGrid>
                        </h:panelGroup>

                    </f:facet>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="labelTableSol" value="Data de Lan�amento"/>
                        </f:facet>
                        <h:outputText styleClass="labelTableSol" value="#{andamento.dataCadastro}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="labelTableSol" value="Descri��o"/>
                        </f:facet>
                        <h:outputText escape="false"
                                      style="text-align: left; color: #777777 !important;font-size: 16px !important;font-family: Arial,Verdana,sans-serif;"
                                      value="#{andamento.descricao}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="labelTableSol" value="Status"/>
                        </f:facet>
                        <h:outputText styleClass="labelTableSol" value="#{andamento.atividade}"/>
                    </rich:column>


                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="labelTableSol" value="Respons�vel"/>
                        </f:facet>
                        <h:outputText styleClass="labelTableSol" value="#{andamento.usuario}"/>
                    </rich:column>

                </rich:dataTable>

            </h:panelGrid>

        </rich:column>

    </rich:dataTable>

</h:panelGroup>

<script>
    carregarTooltipster();
</script>

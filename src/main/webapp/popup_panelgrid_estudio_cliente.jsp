<%-- 
    Document   : popup_panelgrid_estudio_cliente
    Created on : 13/07/2012, 10:22:16
    Author     : Waller
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><script type="text/javascript" language="javascript" src="/script/script.js"></script>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/estudio.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Histórico de Agendamento"/>
    </title>
    <html>
        <h:form id="form">            
            
            <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">               

                <tr>
                    <td align="left" valign="top" width="100%"  style="padding:7px 20px 0 20px;">
                        <h:panelGroup id="panelGroup">
                            <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                <tr>
                                    <td width="19" height="50" align="left" valign="top"></td>
                                    <td align="left" valign="top"  
                                        style="padding-bottom: 10px;padding-top: 10px; border-bottom:#E5E5E5 1px solid;">

                                        <h:outputText styleClass="texto-size-18 negrito cinzaEscuro pl20" 
                                                      value="Histórico de Agendamento"/>

                                    </td>
                                    <td width="19" align="left" valign="top"></td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top"></td>
                                    <td align="left" valign="top" bgcolor="#ffffff" style="padding:5px 15px 5px 15px;">
                                        <h:panelGrid id="mainPanelModal" columns="1" width="100%" columnClasses="colunaCentralizada" >

                                            <h:panelGrid columns="2" >

                                                <h:outputText style="font-weight: bold;" value="Ver todos" styleClass="text"/>
                                                
                                                <h:panelGroup layout="block" styleClass="chk-fa-container inline ">
                                                <h:selectBooleanCheckbox
                                                    id="selecionado-verTodosModal"
                                                    value="#{clienteEstudioControle.verTodosModal}">
                                                    <a4j:support event="onclick" action="#{clienteEstudioControle.acaoListarHistorico}"
                                                                 ajaxSingle="true"
                                                                 reRender="clienteEstudioControle-listaHistoricoAgenda-modal"/>
                                                </h:selectBooleanCheckbox>
                                                    <span style="top: 0px;"/>
                                                </h:panelGroup>
                                            </h:panelGrid>

                                            <rich:dataTable
                                                id="clienteEstudioControle-listaHistoricoAgenda-modal"
                                                width="100%"
                                                style="margin: 0 !important;"
                                                reRender="scResultadoListaAVendaModal"
                                                styleClass="tabelaDados semZebra"
                                                value="#{clienteEstudioControle.listaHistoricoAgenda}"
                                                var="item">

                                                <rich:column
                                                    id="item-dataAula"
                                                    sortable="true"
                                                    width="10%"
                                                    selfSorted="true"
                                                    sortBy="#{item.dataAula}"
                                                    filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText value="Data Aula"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{clienteEstudioControle.editarAgendamento}" reRender="toolTipAgenda" oncomplete="Richfaces.showModalPanel('toolTipAgenda')">
                                                        <h:outputText value="#{item.dataAula}" converter="dataConverter"/>
                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{item}" />
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column
                                                    id="item-diaSemana"
                                                    width="8%"
                                                    sortable="true"
                                                    selfSorted="true"
                                                    sortBy="#{item.diaSemana_Apresentar}"
                                                    filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText value="Dia Sem"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{clienteEstudioControle.editarAgendamento}" reRender="toolTipAgenda" oncomplete="Richfaces.showModalPanel('toolTipAgenda')">
                                                        <h:outputText value="#{item.diaSemana_Apresentar}" />
                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{item}" />
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column
                                                    id="item-horaInicio"
                                                    width="10%"
                                                    sortable="true"
                                                    selfSorted="true"
                                                    sortBy="#{item.horaInicio}"
                                                    filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText value="Horário"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{clienteEstudioControle.editarAgendamento}" reRender="toolTipAgenda" oncomplete="Richfaces.showModalPanel('toolTipAgenda')">
                                                        <h:outputText value="#{item.horaInicio_Apresentar}"/>
                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{item}" />
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column
                                                    id="item-produto-venda"
                                                    width="15%"
                                                    sortable="true"
                                                    selfSorted="true"
                                                    sortBy="#{item.produtoVO.descricao}"
                                                    filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText value="Produto"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{clienteEstudioControle.editarAgendamento}" reRender="toolTipAgenda" oncomplete="Richfaces.showModalPanel('toolTipAgenda')">
                                                        <h:outputText value="#{item.produtoVO.descricao}"/>
                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{item}" />
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column
                                                    id="item-professor"
                                                    width="20%"
                                                    sortable="true"
                                                    selfSorted="true"
                                                    sortBy="#{item.colaboradorVO.pessoa.nome}"
                                                    filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText value="Colaborador"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{clienteEstudioControle.editarAgendamento}" reRender="toolTipAgenda" oncomplete="Richfaces.showModalPanel('toolTipAgenda')">
                                                        <h:outputText value="#{item.colaboradorVO.pessoa.nome}"/>
                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{item}" />
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column
                                                    id="item-ambiente"
                                                    width="12%"
                                                    sortable="true"
                                                    selfSorted="true"
                                                    sortBy="#{item.ambienteVO.descricao}"
                                                    filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText value="Ambiente"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{clienteEstudioControle.editarAgendamento}" reRender="toolTipAgenda" oncomplete="Richfaces.showModalPanel('toolTipAgenda')">
                                                        <h:outputText value="#{item.ambienteVO.descricao}"/>
                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{item}" />
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column
                                                    id="item-status"
                                                    width="15%"
                                                    sortable="true"
                                                    selfSorted="true"
                                                    sortBy="#{item.status.descricao}"
                                                    filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText value="Status"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{clienteEstudioControle.editarAgendamento}" reRender="toolTipAgenda" oncomplete="Richfaces.showModalPanel('toolTipAgenda')">
                                                        <h:outputText value="#{item.status.descricao}"/>
                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{item}" />
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column
                                                    id="item-tipoHorarioVO"
                                                    sortable="true"
                                                    width="13%"
                                                    selfSorted="true"
                                                    sortBy="#{item.tipoHorarioVO.descricao}"
                                                    filterEvent="onkeyup">
                                                    <f:facet name="header">
                                                        <h:outputText value="Tipo Horário"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{clienteEstudioControle.editarAgendamento}" reRender="toolTipAgenda" oncomplete="Richfaces.showModalPanel('toolTipAgenda')">
                                                        <h:outputText value="#{item.tipoHorarioVO.descricao}"/>
                                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{item}" />
                                                    </a4j:commandLink>
                                                </rich:column>
                                            </rich:dataTable>
                                        </h:panelGrid>


                                    </td>
                                    <td align="left" valign="top"></td>

                                </tr>
                                <tr>
                                    <td height="20" align="left" valign="top"></td>
                                    <td align="left" valign="top"></td>
                                    <td align="left" valign="top"></td>
                                </tr>
                            </table>
                        </h:panelGroup>
                    </td>
                </tr>
            </table>
        </h:form>
        <%@include  file="pages/estudio/includes/include_modal_dados_aluno.jsp" %>
        <%@include  file="pages/estudio/includes/include_modal_agenda_aluno.jsp" %>
        <%@include file="pages/estudio/includes/include_modal_erro.jsp" %>
        <%@include file="pages/estudio/includes/include_modal_sucesso.jsp" %>
    </f:view>
</body>
</html>



<%@page contentType="text/html" pageEncoding="UTF-8"%>

<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@include file="include_imports.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${root}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="${root}/css/ce.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">var contexto = '${root}';</script>
<script type="text/javascript" language="javascript" src="${root}/script/ajuda.js"></script>
<%@ taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>


<%@include file="includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view locale="#{SuperControle.idioma}">

    <title>
        <h:outputText value="#{msg_aplic.prt_Ambiente_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Ambiente_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-ambiente/"/>
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">

            <input type="hidden" value="${modulo}" name="modulo"/>

            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" >

                    <rich:tab label="Dados do Ambiente" switchType="client">

                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText value="#{msg_aplic.prt_Ambiente_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{AmbienteControle.ambienteVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <h:panelGroup>
                                    <%@include file="pages/ce/includes/include_obrigatorio.jsp" %>
                                    <h:outputText value="#{CElabels['entidade.descricao']}:" />
                                </h:panelGroup>
                            </c:if>
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <h:outputText   value="#{msg_aplic.prt_Ambiente_descricao}" />
                            </c:if>
                            <h:panelGroup>
                                <h:inputText  id="descricao"  size="45" maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{AmbienteControle.ambienteVO.descricao}" />
                                <h:message for="descricao" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <h:outputText   value="#{msg_aplic.prt_Ambiente_capacidade}" />

                                <h:panelGroup>
                                    <h:inputText  id="capacidade"  size="2" maxlength="3" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{AmbienteControle.ambienteVO.capacidade}" />
                                    <h:message for="capacidade" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>
                                <h:outputText   value="#{msg_aplic.prt_Ambiente_tipoAmbiente}" />

                                <h:selectOneMenu
                                        onfocus="focusinput(this);" styleClass="form" id="tiposAmbiente"
                                        value="#{AmbienteControle.ambienteVO.tipoModulo}">
                                        <f:selectItem itemValue="" itemLabel="--Selecione--"/>
                                        <f:selectItems value="#{AmbienteControle.listaAmbienteModulo}" />
                                    </h:selectOneMenu>


                                    <h:outputText   value="#{msg_aplic.prt_acesso_integracao_coletor}" />
                                    <h:selectOneMenu
                                            onfocus="focusinput(this);" styleClass="form" id="coletor"
                                            value="#{AmbienteControle.ambienteVO.coletor}">
                                        <f:selectItem itemValue="" itemLabel="--Selecione--"/>
                                        <f:selectItems value="#{AmbienteControle.listaColetores}" />
                                    </h:selectOneMenu>

                            </c:if>

                            <c:if test="${modulo eq 'centralEventos'}">
                                <h:panelGroup>
                                    <%@include file="pages/ce/includes/include_obrigatorio.jsp" %>
                                    <h:outputText value="#{CElabels['menu.cadastros.ambiente.tipo']}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:selectOneMenu
                                        onfocus="focusinput(this);" styleClass="form" id="tiposAmbiente"
                                        value="#{AmbienteControle.ambienteVO.tipoAmbiente}">
                                        <f:selectItem itemValue="" itemLabel="--Selecione--"/>
                                        <f:selectItems value="#{AmbienteControle.listaAmbiente}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <h:outputText value="Capacidade Máxima de Convidados" />
                                <h:panelGroup>
                                    <rich:inputNumberSpinner
                                        value="#{AmbienteControle.ambienteVO.capacidadeMaximaConvidados}"
                                        cycled="false" minValue="0" step="50" maxValue="1000" />
                                </h:panelGroup>
                            </c:if>
                            <h:panelGroup>
                                <c:if test="${modulo eq 'centralEventos'}">
                                    <%@include file="pages/ce/includes/include_obrigatorio.jsp" %>
                                </c:if>
                                <h:outputText value="#{msg_aplic.prt_Ambiente_situacaoAmbiente}" />
                            </h:panelGroup>
                            <h:panelGroup>
                                <h:selectOneMenu  id="situacao" onblur="blurinput(this);"  onfocus="focusinput(this);"
                                                  styleClass="form" value="#{AmbienteControle.ambienteVO.situacaoAmbiente}" >
                                    <f:selectItems  value="#{AmbienteControle.listaSituacaoAmbiente}" />
                                </h:selectOneMenu>
                            </h:panelGroup>

                            <h:outputText rendered="#{AmbienteControle.mgbIntegrado}" value="#{msg_aplic.prt_Cadastro_label_codigoPiscinaMgb}:" />
                            <h:panelGroup styleClass="cb-container margenVertical tamanhoInputMedio required cidadeid"
                                          layout="block"
                                          rendered="#{AmbienteControle.mgbIntegrado}">
                                <h:selectOneMenu  id="codigoPiscinaMgb" onblur="blurinput(this);"
                                                  onfocus="focusinput(this);"
                                                  styleClass="form"
                                                  value="#{AmbienteControle.ambienteVO.codigoPiscinaMgb}" >
                                    <f:selectItems value="#{AmbienteControle.listaSelectItemCodigoPiscinaMgb}" />
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGrid>
                    </rich:tab>
                    <%-- Indisponibilidade --%>
                    <rich:tab label="Indisponibilidade"
                              rendered="#{LoginControle.apresentarLinkEstudio}"
                              action="#{configuracaoEstudioControle.acaoListarAmbienteIndisp}">
                        <%@include file="pages/estudio/indisponibilidadeAmbiente.jsp" %>
                    </rich:tab>
                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  id="icAmbienteSuc" rendered="#{AmbienteControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton id="icAmbienteFal" rendered="#{AmbienteControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgAmbiente" styleClass="mensagem"  value="#{AmbienteControle.mensagem}"/>
                            <h:outputText id="msgAmbienteDet" styleClass="mensagemDetalhada" value="#{AmbienteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>

                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandButton id="novo" immediate="true" action="#{AmbienteControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="salvar" action="#{AmbienteControle.gravar}"
                                                 value="#{msg_bt.btn_gravar}"
                                                 alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt">
                                </a4j:commandButton>

                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>

                                <h:panelGroup id="grupoMensagem">
                                    <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                       oncomplete="#{AmbienteControle.msgAlert}" action="#{AmbienteControle.confirmarExcluir}" value="#{msg_bt.btn_excluir}"
                                                       alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec" style="border: 0px" />
                                </h:panelGroup>

                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="consultar" immediate="true" action="#{AmbienteControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandLink action="#{AmbienteControle.realizarConsultaLogObjetoSelecionado}"
                                           reRender="form"
                                           oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                           title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                        <i class="fa-icon-list"></i>
                                </a4j:commandLink>
                                
                            </c:if>

                            <!-- -----------------------------CE------------------------------ -->

                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandButton id="novo" immediate="true" action="#{AmbienteControle.novo}"
                                                 value="#{msg_bt.btn_novo}"
                                                 alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="salvar"
                                                 action="#{AmbienteControle.gravarCE}"
                                                 value="#{msg_bt.btn_gravar}"
                                                 alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                                 actionListener="#{AmbienteControle.autorizacao}">
                                    <!-- Entidade.AMBIENTE -->
                                    <f:attribute name="entidade" value="101" />
                                    <!-- Operacao.GRAVAR -->
                                    <f:attribute name="operacao" value="G" />
                                </a4j:commandButton>

                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="excluir"
                                                 onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                                 action="#{AmbienteControle.excluirCE}"
                                                 value="#{msg_bt.btn_excluir}"
                                                 alt="#{msg.msg_excluir_dados}" accesskey="3"
                                                 styleClass="botoes nvoBt btSec btPerigo"
                                                 actionListener="#{AmbienteControle.autorizacao}">
                                    <!-- Entidade.AMBIENTE -->
                                    <f:attribute name="entidade" value="101" />
                                    <!-- Operacao.EXCLUIR -->
                                    <f:attribute name="operacao" value="E" />
                                </a4j:commandButton>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="consultar" immediate="true" action="#{AmbienteControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}"
                                                 alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                            </c:if>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>

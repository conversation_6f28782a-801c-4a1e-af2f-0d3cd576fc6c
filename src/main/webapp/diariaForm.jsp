<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
<script src="bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<link href="./css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="./css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="script/jquery.maskedinput-1.7.6.js"></script>

<script type="text/javascript" language="javascript">
    setDocumentCookie('popupsImportante', 'close',1);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    .valorTotal {
        background: #777;

    }

</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Diária "/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.7.6.js" />

    <rich:modalPanel id="panelCliente" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formCliente:consultarCliente').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_AulaAvulsa_consultarCliente}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkCliente"/>
                <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCliente" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarCliente" value="#{AulaAvulsaDiariaControle.campoConsultarCliente}">
                        <f:selectItems value="#{AulaAvulsaDiariaControle.tipoConsultarComboCliente}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarCliente" styleClass="campos" value="#{AulaAvulsaDiariaControle.valorConsultarCliente}"/>
                    <a4j:commandButton id="btnConsultarCliente" reRender="formCliente:mensagemConsultarCliente, formCliente:resultadoConsultaCliente , formCliente:scResultadoCliente , formCliente" action="#{AulaAvulsaDiariaControle.consultarCliente}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCliente" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AulaAvulsaDiariaControle.listaConsultarCliente}" rows="10" var="cliente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText  value="#{msg_aplic.prt_Cliente_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_situacao}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.situacao_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cliente_matricula}"/>
                        </f:facet>
                        <h:outputText value="#{cliente.matricula}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="selecionarClientes" action="#{AulaAvulsaDiariaControle.selecionarCliente}" focus="cliente" reRender="form:panelGeral, formCliente, form:mensagem" oncomplete="Richfaces.hideModalPanel('panelCliente')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCliente:resultadoConsultaCliente" maxPages="10" id="scResultadoCliente"/>
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AulaAvulsaDiariaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <%--rich:modalPanel id="panelColaborador" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formColaborador:consultarColaborador').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_AulaAvulsa_consultarColaborador}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkColaborador"/>
                <rich:componentControl for="panelColaborador" attachTo="hiperlinkColaborador" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formColaborador" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarColaborador" value="#{AulaAvulsaDiariaControle.campoConsultarColaborador}">
                        <f:selectItems value="#{AulaAvulsaDiariaControle.tipoConsultarComboColaborador}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarColaborador"styleClass="campos" value="#{AulaAvulsaDiariaControle.valorConsultarColaborador}"/>
                    <a4j:commandButton id="btnConsultarColaborador" reRender="formColaborador:mensagemConsultarColaborador, formColaborador:resultadoConsultaColaborador , formColaborador:scResultadoColaborador , formColaborador" action="#{AulaAvulsaDiariaControle.consultarColaborador}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaColaborador" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AulaAvulsaDiariaControle.listaConsultarColaborador}" rows="10" var="colaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{colaborador.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_pessoa}"/>
                        </f:facet>
                        <h:outputText value="#{colaborador.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{AulaAvulsaDiariaControle.selecionarColaborador}" focus="colaborador" reRender="form:panelGeral, formColaborador, form:mensagem" oncomplete="Richfaces.hideModalPanel('panelColaborador')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formColaborador:resultadoConsultaColaborador" maxPages="10" id="scResultadoColaborador"/>
                <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AulaAvulsaDiariaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel--%>

    <rich:modalPanel id="panelModalidade" shadowOpacity="true" width="550" height="350" onshow="document.getElementById('formModalidade:consultarModalidade').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_AulaAvulsa_consultarModalidade}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkModalidade"/>
                <rich:componentControl for="panelModalidade" attachTo="hiperlinkModalidade" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formModalidade" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consultarModalidade" value="#{AulaAvulsaDiariaControle.campoConsultarModalidade}">
                        <f:selectItems value="#{AulaAvulsaDiariaControle.tipoConsultarComboModalidade}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsultarModalidade" styleClass="campos" value="#{AulaAvulsaDiariaControle.valorConsultarModalidade}"/>
                    <a4j:commandButton id="btnConsultarModalidade" reRender="formModalidade:mensagemConsultarModalidade, formModalidade:resultadoConsultaModalidade , formModalidade:scResultadoModalidade , formModalidade" action="#{AulaAvulsaDiariaControle.consultarModalidade}" styleClass="botoes" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaModalidade" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{AulaAvulsaDiariaControle.listaConsultarModalidade}" rows="5" var="modalidade">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Modalidade_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{modalidade.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_AulaAvulsa_consultarModalidade_modalidade}"/>
                        </f:facet>
                        <h:outputText value="#{modalidade.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton id="selecionarModalidade" action="#{AulaAvulsaDiariaControle.selecionarModalidade}" focus="modalidade" reRender="form:panelGeral, formModalidade, form:mensagem" oncomplete="Richfaces.hideModalPanel('panelModalidade')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formModalidade:resultadoConsultaModalidade" maxPages="10" id="scResultadoModalidade"/>
                <h:panelGrid id="mensagemConsultaModalidade" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{AulaAvulsaDiariaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:form id="form" >
        <jsp:include page="include_head.jsp" flush="true" />
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central" style="position:relative;">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Diária" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-realizar-uma-venda-de-diaria/"
                                                      title="Clique e saiba mais: Diária" target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup id="panelCaixaAberto"  layout="block" styleClass="margin-box">
                                    <h:panelGrid id="panelGeral" columns="1"  width="100%" styleClass="font-size-Em-max">
                                        <h:panelGroup rendered="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.apresentarEmpresa}">
                                            <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="#{msg_aplic.prt_AulaAvulsa_empresa}" />
                                            <rich:spacer width="10"/>
                                            <h:selectOneMenu  id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.empresa.codigo}" >
                                                <f:selectItems value="#{AulaAvulsaDiariaControle.listaSelectItemEmpresa}" />
                                            </h:selectOneMenu>
                                            <a4j:commandButton id="atualizar_empresa" action="#{AulaAvulsaDiariaControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:empresa"/>
                                        </h:panelGroup>

                                        <h:panelGrid columns="1" id="panelGroupComprador" styleClass="font-size-Em-max">
                                            <h:panelGroup id="painelDataLancamento">
                                                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="DATA LANÇAMENTO" />
                                                <h:panelGroup layout="block" style="padding: 5px 0;">
                                                    <h:outputText  styleClass="texto-size-16 texto-font texto-cor-cinza-3" rendered="#{!AulaAvulsaDiariaControle.editarLancamento}"
                                                                   value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.dataLancamento}">
                                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                    </h:outputText>

                                                    <a4j:commandLink title="Editar data de lançamento"
                                                                     styleClass="linkPadrao texto-size-14 tooltipster"
                                                                     action="#{AulaAvulsaDiariaControle.editarCampoData}"
                                                                     oncomplete="#{AulaAvulsaDiariaControle.onCompleteDesconto}"
                                                                     rendered="#{!AulaAvulsaDiariaControle.editarLancamento}"
                                                                     reRender="panelAutorizacaoFuncionalidade">
                                                        <i class="fa-icon-key"></i>
                                                    </a4j:commandLink>
                                                    <h:panelGroup layout="block" styleClass="dateTimeCustom">
                                                        <rich:calendar rendered="#{AulaAvulsaDiariaControle.editarLancamento}"
                                                                       value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.dataLancamento}"
                                                                       inputSize="10"
                                                                       inputClass="form"
                                                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                                                       oninputblur="blurinput(this);"
                                                                       oninputfocus="focusinput(this);"
                                                                       oninputchange="return validar_Data(this.id);"
                                                                       datePattern="dd/MM/yyyy"
                                                                       enableManualInput="true"
                                                                       zindex="2"
                                                                       showWeeksBar="false" />
                                                        <rich:spacer width="10"></rich:spacer>
                                                        <a4j:commandLink title="Aplicar Data" rendered="#{AulaAvulsaDiariaControle.editarLancamento}"
                                                                         styleClass="linkPadrao texto-size-20 texto-cor-verde tooltipster"
                                                                           action="#{AulaAvulsaDiariaControle.aplicarData}" reRender="form:panelCaixaAberto">
                                                            <i class="fa-icon-ok"></i>
                                                        </a4j:commandLink>
                                                        <a4j:commandLink rendered="#{AulaAvulsaDiariaControle.editarLancamento}"
                                                                           action="#{AulaAvulsaDiariaControle.limparAlterarData}"
                                                                           title="Limpar Alteração"
                                                                         styleClass="linkPadrao texto-size-18 tooltipster"
                                                                           reRender="form:panelCaixaAberto">
                                                            <i class="fa-icon-eraser"></i>
                                                        </a4j:commandLink>
                                                </h:panelGroup>

                                                </h:panelGroup>
                                                <script>
                                                    jQuery('.tooltipster').tooltipster({
                                                        theme: 'tooltipster-light',
                                                        position: 'top',
                                                        animation: 'grow',
                                                        delay: 100,
                                                        contentAsHTML: true
                                                    });
                                                </script>
                                            </h:panelGroup>
                                            <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="CLIENTE" />
                                            <h:panelGroup>
                                                <h:inputText  id="nomeCliente" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="inputTextClean"
                                                              value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.nomeComprador}"/>
                                                <rich:suggestionbox
                                                        height="200" width="444"
                                                        for="nomeCliente"
                                                        fetchValue="#{result.pessoa.nome}"
                                                        suggestionAction="#{AulaAvulsaDiariaControle.executarAutocompleteConsultaCliente}"
                                                        minChars="1" rowClasses="20"
                                                        status="statusHora"
                                                        nothingLabel="Nenhum Cliente encontrado !"
                                                        var="result" id="suggestionNomeCliente"
                                                        reRender="mensagem">
                                                    <a4j:support event="onselect"
                                                                 action="#{AulaAvulsaDiariaControle.selecionarClienteSuggestionBox}"/>
                                                    <h:column>
                                                        <h:outputText styleClass="texto-font texto-size-14-real" value="#{result.pessoa.nome}"/>
                                                    </h:column>
                                                </rich:suggestionbox>
                                                <rich:spacer width="5" />
                                                <a4j:commandLink id="limparCliente"
                                                                   onclick="document.getElementById('form:nomeCliente').value = null;"
                                                                   title="Limpar aluno."
                                                                   status="false"
                                                                   action="#{AulaAvulsaDiariaControle.limparCliente}">
                                                    <i class="fa-icon-remove texto-size-14-real linkAzul"/>
                                                </a4j:commandLink>

                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <h:panelGrid columns="1" id="panelGroupProduto" styleClass="font-size-Em-max">
                                            <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="PRODUTO" />
                                            <h:panelGroup>
                                                <h:inputText  id="produto" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.produto.descricao}" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="inputTextClean"/>
                                                <rich:suggestionbox
                                                        height="200" width="444"
                                                        for="produto"
                                                        fetchValue="#{resultProduto.descricao}"
                                                        suggestionAction="#{AulaAvulsaDiariaControle.executarAutocompleteConsultaProduto}"
                                                        minChars="1" rowClasses="20"
                                                        status="statusHora"
                                                        nothingLabel="Nenhum Produto encontrado !"
                                                        var="resultProduto" id="suggestionProduto"
                                                        reRender="mensagem, form:totalLancado">
                                                    <a4j:support event="onselect"
                                                                 action="#{AulaAvulsaDiariaControle.selecionarProdutoSuggestionBox}"
                                                                 reRender="form:totalLancado"
                                                                 oncomplete="#{AulaAvulsaDiariaControle.msgAlert}"/>
                                                    <h:column>
                                                        <h:outputText styleClass="texto-font texto-size-14-real" value="#{resultProduto.descricao} "/>
                                                    </h:column>
                                                    <h:column>
                                                        <h:outputText styleClass="texto-font texto-size-14-real" value="#{resultProduto.valorFinal_Apresentar} "/>
                                                    </h:column>
                                                </rich:suggestionbox>
                                                <rich:spacer width="5" />
                                                <a4j:commandLink id="limparProduto"
                                                                   onclick="document.getElementById('form:produto').value = null;"
                                                                   title="Limpar produto."
                                                                   status="false"
                                                                   action="#{AulaAvulsaDiariaControle.limparProduto}"
                                                                   reRender="form:totalLancado">
                                                <i class="fa-icon-remove texto-size-14-real linkAzul"/>
                                            </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <h:panelGrid columns="1" id="panelGroupModalidade" styleClass="font-size-Em-max">
                                            <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="MODALIDADE" />
                                            <h:panelGroup>
                                                <h:inputText  id="modalidade" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.modalidade.nome}" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="inputTextClean"/>
                                                <rich:suggestionbox
                                                        height="200" width="444"
                                                        for="modalidade"
                                                        fetchValue="#{resultModalidade.nome}"
                                                        suggestionAction="#{AulaAvulsaDiariaControle.executarAutocompleteConsultaModalidade}"
                                                        minChars="1" rowClasses="20"
                                                        status="statusHora"
                                                        nothingLabel="Nenhuma Modalidade encontrada !"
                                                        var="resultModalidade" id="suggestionModalidade"
                                                        reRender="mensagem">
                                                    <a4j:support event="onselect"
                                                                 action="#{AulaAvulsaDiariaControle.selecionarModalidadeSuggestionBox}"/>
                                                    <h:column>
                                                        <h:outputText styleClass="texto-font texto-size-14-real" value="#{resultModalidade.nome}"/>
                                                    </h:column>
                                                </rich:suggestionbox>
                                                <rich:spacer width="5" />
                                                <a4j:commandLink id="limparModalidade"
                                                                   onclick="document.getElementById('form:modalidade').value = null;"
                                                                   title="Limpar modalidade."
                                                                   status="false"
                                                                   action="#{AulaAvulsaDiariaControle.limparModalidade}">
                                                    <i class="fa-icon-remove texto-size-14-real linkAzul"/>
                                                </a4j:commandLink>

                                            </h:panelGroup>

                                        </h:panelGrid>

                                        <h:panelGrid columns="1" styleClass="font-size-Em-max">
                                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="DESCONTO"/>
                                            <h:panelGroup id="panelGroupDesconto">
                                                <h:selectOneMenu styleClass="inputTextClean" style="margin-bottom: 5px; width: 404.5px;" id="descontoCadastrados"
                                                                 value="#{AulaAvulsaDiariaControle.selectItemDesconto}"
                                                                 converter="simpleIndexConverter">
                                                    <f:selectItems value="#{AulaAvulsaDiariaControle.listaSelectItemDecontos}"/>
                                                    <a4j:support event="onchange"
                                                                 action="#{AulaAvulsaDiariaControle.validaPermissaoDesconto}"
                                                                 oncomplete="#{AulaAvulsaDiariaControle.onCompleteDesconto} #{AulaAvulsaDiariaControle.mensagemNotificar}"
                                                                 reRender="form:panelCaixaAberto, totalLancado, panelAutorizacaoFuncionalidade"/>
                                                </h:selectOneMenu>
                                                <br/>
                                                <span class="texto-size-11 texto-cor-cinza texto-font texto-bold">Agora é possível cadastrar descontos predefinidos" Acesse o cadastro de desconto e utilize o tipo "diária".</span>
                                                <br/>
                                                <h:inputText disabled="#{!AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.descontoManual}" size="10" maxlength="50"
                                                             onblur="blurinput(this);"
                                                             onkeypress="return formatar_moeda(this,'.',',',event);"
                                                             id="valorDescontoManual"
                                                             onfocus="focusinput(this);"
                                                             styleClass="inputTextClean"
                                                             value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.valorDescontoManual}">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:inputText>
                                                <rich:spacer width="5"/>
                                                <a4j:commandButton id="btnAplicarDesconto" image="./images/tick.png" title="Aplicar Novo Valor"
                                                                   rendered="#{AulaAvulsaDiariaControle.aplicarValor}"
                                                                   action="#{AulaAvulsaDiariaControle.editarDescontoProduto}" reRender="form:panelCaixaAberto"
                                                                   oncomplete="#{AulaAvulsaDiariaControle.onCompleteDesconto} #{AulaAvulsaDiariaControle.mensagemNotificar}"/>
                                                <a4j:commandButton id="btnChave"
                                                                   image="./images/icon_chave.png"
                                                                   rendered="#{!AulaAvulsaDiariaControle.aplicarValor}"
                                                                   action="#{AulaAvulsaDiariaControle.editarCampoDescontoProduto}"
                                                                   oncomplete="#{AulaAvulsaDiariaControle.onCompleteDesconto} #{AulaAvulsaDiariaControle.mensagemNotificar}"
                                                                   title="Informar Valor do Desconto"
                                                                   reRender="panelAutorizacaoFuncionalidade"/>
                                                <rich:spacer width="5"/>
                                                <a4j:commandLink id="limparTabelaDesconto"
                                                                 action="#{AulaAvulsaDiariaControle.limparDescontoManual}"
                                                                 reRender="form:panelCaixaAberto,panelGeral, panelGroupDesconto, totalLancado">
                                                    <i class="fa-icon-remove texto-size-14-real linkAzul"/>
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <h:panelGroup id="panelGroupDataInicio">
                                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="DATA INÍCIO" />
                                            <rich:spacer width="21"/>
                                        <h:panelGroup styleClass="dateTimeCustom" layout="block" style="height: 40px;">
                                            <rich:calendar id="dataInicio"
                                                           value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.dataInicio}"
                                                           inputSize="10"
                                                           inputClass="form"
                                                           oninputblur="blurinput(this);"
                                                           oninputfocus="focusinput(this);"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy"
                                                           enableManualInput="true"
                                                           zindex="2"
                                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                                           showWeeksBar="false"/>
                                            <rich:spacer width="3"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                    <h:panelGrid id="mensagem" columns="2" width="100%" >
                                        <h:panelGrid columns="3" width="100%">
                                            <h:panelGrid columns="1" width="100%">
                                                <f:verbatim>
                                                    <h:outputText value=" "/>
                                                </f:verbatim>
                                            </h:panelGrid>
                                            <h:panelGrid columns="1" width="100%">
                                                <h:outputText id="msgDiaria" styleClass="mensagem" value="#{AulaAvulsaDiariaControle.mensagem}"/>
                                                <h:outputText id="msgDiariaDet" styleClass="mensagemDetalhada" value="#{AulaAvulsaDiariaControle.mensagemDetalhada}"/>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </h:panelGrid>

                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2" >
                                        <tr>
                                            <td align="left" valign="top">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreviewtotal">
                                                    <tr>
                                                        <td width="50%" align="left" valign="middle">Total Lan&ccedil;ado = <span class="verde">${MovPagamentoControle.empresaLogado.moeda}&nbsp;<h:outputText  id="totalLancado" value="#{AulaAvulsaDiariaControle.aulaAvulsaDiariaVO.valor}"><f:converter converterId="FormatadorNumerico"/></h:outputText></span></td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>


                                    <!-- inicio botões -->
                                    <h:panelGrid id="panelBotoesControle"  style="margin-right:0px" columns="1">
                                        <h:panelGrid   style="margin-right:0px" columns="3">
                                            <a4j:commandLink id="botaoPagar"
                                                             styleClass="pure-button pure-button-primary"
                                                             action="#{AulaAvulsaDiariaControle.validarDadosAulaAvulsa}"
                                                             rendered="#{!AulaAvulsaDiariaControle.processandoOperacao}"
                                                             onclick="this.disabled=true;"
                                                             oncomplete="this.disabled=false;"
                                                             reRender="form:panelGeral, form:totalLancado,form:mensagem, panelAutorizacaoFuncionalidade, panelBotoesControle">
                                                <i class="fa-icon-money"></i> &nbsp Receber
                                            </a4j:commandLink>

                                            <h:outputLink styleClass="pure-button" id="botaoCancelar" value="tela1.jsp">
                                                Cancelar
                                            </h:outputLink>
                                        </h:panelGrid>
                                    </h:panelGrid>

                                </h:panelGroup>

                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>

    </h:form>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp" flush="true"/>
    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade_nova.jsp" flush="true"/>
</f:view>

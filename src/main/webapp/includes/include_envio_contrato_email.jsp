<%@include file="imports.jsp" %>
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<rich:modalPanel domElementAttachment="parent" id="modalEnviarContratoEmail" 
                 styleClass="novaModal"
                 autosized="true" width="400"
                 height="150" shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Solicitar envio de Recibo por Email" rendered="#{!EnvioEmailContratoReciboControle.envioDeContrato}"/>
            <h:outputText value="Solicitar envio de Contrato por Email" rendered="#{EnvioEmailContratoReciboControle.envioDeContrato}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkEmail"/>
                <rich:componentControl for="modalEnviarContratoEmail" attachTo="hidelinkEmail" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formModalEnviarContratoEmail">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

            <h:panelGrid id="panelEnvioContratoEmail"
                         rendered="#{!EnvioEmailContratoReciboControle.sucesso}"
                         columns="3" width="100%">
                <h:outputText styleClass="tituloCampos" value="Email:"/>
                <h:inputText id="novoEmailEnviarContrato" size="50" maxlength="50" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{EnvioEmailContratoReciboControle.novoEmailEnviar}"/>
                <a4j:commandLink id="adicionarEmailEnviarContrato"
                                 styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                 action="#{EnvioEmailContratoReciboControle.adicionarEmailEnviar}"
                                 reRender="panelEmailEnviarContratro, novoEmailEnviarContrato">
                    <i class="fa-icon-plus-sign" style="font-size: large;  color: #000"> </i>
                </a4j:commandLink>
            </h:panelGrid>

            <h:panelGroup id="panelEmailEnviarContratro">
                <h:dataTable width="100%" headerClass="subordinado"
                             rendered="#{!EnvioEmailContratoReciboControle.sucesso}"
                             styleClass="tabelaDados semZebra"
                             style="margin: 0;"
                             id="emailsselecionadosenviar"
                             value="#{EnvioEmailContratoReciboControle.listaEmailsEnviar}"
                             var="emailsEnviar">
                    <h:column>
                        <h:outputText style="text-transform: none;" value="#{emailsEnviar.email}"/>
                    </h:column>

                    <h:column>
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                            <a4j:commandLink id="removerEmailEnviarContrato"
                                             rendered="#{emailsEnviar.permiteRemover}"
                                             styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                             action="#{EnvioEmailContratoReciboControle.removerEmailEnviar}"
                                             reRender="panelEmailEnviarContratro">
                                <i class="fa-icon-minus-sign" style="font-size: large; "> </i>
                            </a4j:commandLink>
                        </h:panelGrid>
                    </h:column>
                </h:dataTable>

                <h:panelGroup layout="block" styleClass="tituloDemonstrativo" style="padding-top: 7px; padding-bottom: 7px;"
                              rendered="#{EnvioEmailContratoReciboControle.sucesso}">
                    <h:outputText styleClass="tituloCampos" value="#{EnvioEmailContratoReciboControle.mensagemDetalhada}"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="enviarContrato"
                                     rendered="#{EnvioEmailContratoReciboControle.envioDeContrato && !EnvioEmailContratoReciboControle.sucesso}"
                                     value="Enviar"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     action="#{EnvioEmailContratoReciboControle.enviarContrato}"
                                     reRender="formModalEnviarContratoEmail"/>
                    <a4j:commandLink id="enviarContratoAssinatura"
                                     rendered="#{EnvioEmailContratoReciboControle.envioDeContrato && !EnvioEmailContratoReciboControle.sucesso && EnvioEmailContratoReciboControle.enviarContratoParaAssinatura}"
                                     value="Enviar para Assinar"
                                     style="margin-left: 8px"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     action="#{EnvioEmailContratoReciboControle.enviarContratoParaAssinar}"
                                     reRender="formModalEnviarContratoEmail"/>

                    <a4j:commandLink id="enviarReciboCliente"
                                     rendered="#{(!EnvioEmailContratoReciboControle.envioDeContrato && !EnvioEmailContratoReciboControle.sucesso) && !EnvioEmailContratoReciboControle.reciboColaborador && !EnvioEmailContratoReciboControle.reciboPagamento}"
                                     value="Enviar"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     action="#{ReciboControle.enviarReciboEmailCliente}"
                                     reRender="formModalEnviarContratoEmail"/>

                    <a4j:commandLink id="enviarReciboColaborador"
                                     rendered="#{(!EnvioEmailContratoReciboControle.envioDeContrato && !EnvioEmailContratoReciboControle.sucesso) && EnvioEmailContratoReciboControle.reciboColaborador}"
                                     value="Enviar"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     action="#{ReciboControle.enviarReciboEmailColaborador}"
                                     reRender="formModalEnviarContratoEmail"/>

                    <a4j:commandLink id="enviarReciboPagamento"
                                     rendered="#{EnvioEmailContratoReciboControle.reciboPagamento && (!EnvioEmailContratoReciboControle.envioDeContrato && !EnvioEmailContratoReciboControle.sucesso)}"
                                     value="Enviar"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     action="#{ReciboControle.enviarReciboEmailPagamento}"
                                     reRender="formModalEnviarContratoEmail"/>

                    <a4j:commandLink id="btnFecharEmail"
                                     style="margin-left: 8px"
                                     styleClass="pure-button pure-button-small"
                                     value="Fechar"
                                     onclick="#{rich:component('modalEnviarContratoEmail')}.hide();"/>
                </h:panelGroup>

                <h:panelGrid columns="1" width="100%" rendered="#{EnvioEmailContratoReciboControle.erro}">
                    <h:outputText id="msgRealizarContato" styleClass="mensagem"
                                  value="#{EnvioEmailContratoReciboControle.mensagem}"/>
                    <h:outputText id="msgRealizarContatoDet" styleClass="mensagemDetalhada"
                                  value="#{EnvioEmailContratoReciboControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
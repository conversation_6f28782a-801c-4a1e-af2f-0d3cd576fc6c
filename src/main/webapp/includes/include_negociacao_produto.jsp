<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 14/04/2016
  Time: 09:36
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="imports.jsp" %>
<h:panelGroup layout="block" id="pnlProduto"  styleClass="itemLinha">
  <h:panelGroup layout="block" style="margin-top: 40px;margin-bottom: 20px;" rendered="#{not empty ContratoControle.contratoVO.plano.planoProdutoSugeridoVOs}">



    <rich:dataTable id="planoProdutoVO" width="100%"  styleClass="tabelaSimplesCustom"
                    value="#{ContratoControle.contratoVO.plano.planoProdutoSugeridoVOs}" var="planoProduto">
      <rich:column width="40%" headerClass="col-text-align-left">
        <f:facet name="header">
          <h:outputText value="PRODUTOS" styleClass="texto-font texto-cor-cinza texto-font texto-bold texto-size-14"/>
        </f:facet>
       <h:outputText id="planoProdutoSugeridoNome" style="max-width: 90%;display: inline-block" styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster" title="#{planoProduto.produto.descricao}" value="#{planoProduto.produto.descricao}" />
      </rich:column>
      <rich:column style="text-align: center;width: 30px">
        <f:facet name="header">
          <h:outputText value="QTD." styleClass="texto-font texto-font texto-bold texto-size-14 texto-cor-cinza"/>
        </f:facet>

        <h:outputText rendered="#{planoProduto.obrigatorio}"
                      id="planoProdutoSugeridoQuantidade" title="Quantidade"
                      styleClass="texto-font texto-font texto-size-16 texto-cor-azul tooltipster"
                      value="#{planoProduto.quantidade}">

        </h:outputText>
        <h:panelGroup layout="block" rendered="#{!planoProduto.obrigatorio}">
          <rich:inputNumberSpinner value="#{planoProduto.quantidade}"
                                   styleClass="tableSpiner spinerFontAwesome tooltipster tabelaSemBorda"
                                   maxValue="99"
                                   style="width: 100%"
                                   minValue="0"
                                   onmouseover="adicionarTooltipster(this,'Quantidade');"
                                   cycled="false">


            <a4j:support event="onchange"
                         reRender="planoProdutoVO,containerCondicaoPagamento, dados, planoProdutoMarcada,#{ContratoControle.updateComponente.valoresContrato}"
                         action="#{ContratoControle.selecionarPlanoProdutoSugerido}" oncomplete="atualizarEventosRenderizar();"/>
          </rich:inputNumberSpinner>

        </h:panelGroup>

      </rich:column>

      <rich:column style="text-align: center">
        <f:facet name="header">
          <h:outputText value="VALOR" styleClass="texto-font texto-font texto-bold  texto-cor-cinza texto-size-14"/>
        </f:facet>
        <h:outputText  id="planoProdutoSugeridoValor" value="#{planoProduto.valorProduto}"
                       styleClass="texto-font texto-font texto-size-16 texto-cor-cinza tooltipster"
                       title="Valor unitário do produto">
          <f:converter converterId="FormatadorNumerico"/>
        </h:outputText>
      </rich:column>
      <rich:column style="text-align: center;position: relative">
        <f:facet name="header">
          <h:outputText value="DESC." styleClass="texto-font texto-font texto-cor-cinza texto-bold texto-size-14"/>
        </f:facet>
        <a4j:commandLink   action="#{ContratoControle.consultarDescontoPlanoProdutoSugerido}" title="Alterar/Editar Desconto"
                           id="descontoPlano"
                         reRender="formDescontoPlanoProdutoSugerido,formSenhaDesconto " oncomplete="Richfaces.showModalPanel('panelDescontoPlanoProdutoSugerido');"
                         rendered="#{planoProduto.valorProduto > 0}"
                         styleClass="linkPadrao">
          <h:outputText rendered="#{planoProduto.valorDesconto == 0}" styleClass="fa-icon-plus-sign texto-font texto-size-20 texto-cor-azul"></h:outputText>
          <h:outputText rendered="#{planoProduto.valorDesconto > 0}" title="Editar Desconto"  styleClass="tooltipster texto-font texto-font texto-size-16 texto-cor-azul" value="#{planoProduto.valorDesconto}">
            <f:converter converterId="FormatadorNumerico" />
          </h:outputText>
          <f:setPropertyActionListener value="#{true}" target="#{ContratoControle.exibirDescontoProduto}"/>
        </a4j:commandLink>
        <a4j:commandLink id="btnLimparDesconto" action="#{ContratoControle.limparDescontoPlanoProdutoSugerido}"
                         rendered="#{planoProduto.valorDesconto > 0}"
                           styleClass="linkPadrao texto-size-16" style="margin-left: 4px;"
                           reRender="planoDuracaoVO,pnlProduto,containerCondicaoPagamento, planoProdutoMarcada, #{ContratoControle.updateComponente.valoresContrato}">
            <h:outputText rendered="#{planoProduto.valorDesconto > 0}" title="Limpar desconto" styleClass="fa-icon-minus-sign texto-font texto-cor-vermelho tooltipster"></h:outputText>
        </a4j:commandLink>
        <h:outputText rendered="#{planoProduto.valorProduto <= 0}" styleClass="fa-icon-plus-sign texto-font texto-size-20 texto-cor-disabilitado"></h:outputText>
      </rich:column>
      <rich:column style="text-align: center">
        <f:facet name="header">
          <h:outputText value="TOTAL" styleClass="texto-font texto-font texto-bold texto-cor-cinza texto-size-14"/>
        </f:facet>
        <h:outputText id="planoProdutoSugeridoTotal" title="Valor final do produto"
                      styleClass="texto-font texto-font texto-size-16 texto-cor-cinza tooltipster"
                      rendered="#{!ContratoControle.planoProdutoUsaCupom}"
                      value="#{planoProduto.valorProdutoQtdDesconto}" >
          <f:converter converterId="FormatadorNumerico"/>
        </h:outputText>
        <h:outputText id="planoProdutoSugeridoCupom"
                    styleClass="fa-icon-tags texto-size-16 texto-cor-verde tooltipster"
                    rendered="#{ContratoControle.planoProdutoUsaCupom}"
                    title="Cupom : #{planoProduto.numeroCupomDesconto}" >
        </h:outputText>
      </rich:column>
      <rich:column style="text-align: right;">
        <a4j:commandLink id="btnRemoverProdutoPlano" action="#{ContratoControle.removerProdutoPlano}" styleClass="linkPadrao" reRender="planoProdutoVO, planoProdutoMarcada, detalhesNegociacao, panelProdutoParcela,#{ContratoControle.updateComponente.valoresContrato}">
          <h:outputText style="width: 20px;float: right;" rendered="#{not planoProduto.obrigatorio && planoProduto.quantidade > 0}"
                        styleClass="fa-icon-minus-sign texto-font texto-cor-vermelho texto-bold texto-size-20"/>
        </a4j:commandLink>
      </rich:column>
    </rich:dataTable>
      <script>
          atualizarEventosRenderizar();
      </script>
  </h:panelGroup>
  <h:panelGroup layout="block" styleClass="container-botoes">
    <a4j:commandLink rendered="#{ContratoControle.mostrarBotaoAdicionarCupomDesconto}"
                     id="botaoAdicionarCupomDesconto"
                     reRender="#{ContratoControle.updateComponente.condicaoPagamento},confNegociacao, mensagemCupomDesc"
                     action="#{ContratoControle.abrirModalCupomDesconto}"
                     oncomplete="Richfaces.showModalPanel('panelCupomDesconto');document.getElementById('formCupomDesc:nrCupom').focus()"
                     style="margin-right: 10px"
                     styleClass="linkPadrao texto-cor-azul texto-size-16">
      <i class="fa-icon-tags" ></i>
      Adicionar Cupom
    </a4j:commandLink>
    <a4j:commandLink rendered="#{ContratoControle.mostrarBotaoExcluirCupomDesconto}"
                     id="botaoRetirarCupomDesconto"
                     reRender="#{ContratoControle.updateComponente.condicaoPagamento},confNegociacao"
                     action="#{ContratoControle.retirarCupomDescontoContrato}"
                     oncomplete="#{ContratoControle.mensagemNotificar}"
                     style="margin-right: 10px"
                     styleClass="linkPadrao texto-cor-vermelho texto-size-16">
      <i class="fa-icon-tags" ></i>
      Remover Cupom
    </a4j:commandLink>
    <a4j:commandLink  id="btnAdicionarProdutoPlano" styleClass="botaoPrimario texto-cor-branco texto-size-16" value="Adicionar produto"
                      rendered="#{ContratoControle.desenharBotaoAdicionarProduto}"
                      reRender="formProduto"
                      oncomplete="Richfaces.showModalPanel('panelProduto')"/>

  </h:panelGroup>
</h:panelGroup>
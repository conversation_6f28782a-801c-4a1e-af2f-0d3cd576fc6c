<%@include file="imports.jsp" %>
<link href="${root}/dicas/css/jquery.stepy.css" rel="stylesheet" type="text/css">
<link href="https://fonts.googleapis.com/css?family=Nunito+Sans:400,600,700&display=swap" rel="stylesheet">

<style>
    .font-anuncio {
        font-family: Nunito Sans;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 27px;
        letter-spacing: 0;
        text-align: center;
    }

    .font-anuncio-novidade {
        font-size: 36px !important;
        line-height: 44px !important;
        text-align: left !important;
    }

    .font-titulo-anuncio {
        font-size: 48px !important;
        line-height: 50px !important;
    }

    .font-destaque-anuncio {
        font-family: Nunito Sans;
        font-size: 20px;
        font-style: normal;
        line-height: 27px;
        letter-spacing: 0;
    }

    .font-botao-anuncio {
        font-size: 16px !important;
        font-weight: 600 !important;
        line-height: 18px !important;
    }

    .font-descricao-anuncio {
        font-size: 16px !important;
        font-weight: 400 !important;
        line-height: 18px !important;
        text-align: left !important;
    }

    .font-negrito {
        font-weight: 700 !important;
    }

    .font-termo-uso {
        font-weight: 400 !important;
        font-size: 16px !important;
        line-height: 18px !important;
        text-align: left !important;
        color: #80858C;
    }

    .font-botao-anuncio.nao-quero-lucrar {
        color: #B4B7BB;
        text-decoration: none;
    }

    .font-botao-anuncio.nao-quero-lucrar:hover {
        color: #B4B7BB;
        text-decoration: none;
    }

    .font-black-pri {
        color: #51555A !important;
    }

    .botao-anuncio {
        height: 48px;
        width: 423px;
        left: 0;
        top: 0;
        border-radius: 4px;
        padding: 15px 24px 15px 24px !important;
        margin-right: 16px;
    }

    .botao-li-aceito-desabilitado {
        color: #B4B7BB !important;
    }

    .botao-anuncio.nao-quero-lucrar {
        border: 1px solid #B4B7BB
    }

    .painel-termo-uso {
        width: calc(100% - 42px);
        height: 288px;
        margin-left: 22px;
        margin-top: 16px;
        top: 305px;
        background: #F3F3F4;
        border-radius: 8px;
    }

    .texto-termo-uso {
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 16px;
        overflow-y: auto;
        height: 245px;
    }

    .input-recusa {
        justify-content: space-between;
        align-items: left;
        padding: 11px 16px;
        width: 588px;
        height: 48px;
        left: calc(50% - 588px / 2 - 102px);
        bottom: 25px;
        margin-right: 16px !important;

        text-align: left !important;
        font-weight: 400 !important;
        font-size: 14px !important;
        line-height: 18px !important;
    }

    .inputEmailUsuarioGeral {
        background-image: none !important;
        padding: 25px 10px !important;
        border: 1px solid #ddd !important;
        background-color: #fff !important;
        border-radius: 3px;
        color: #666666;
        font-size: 14px !important;
        background-image: none;
        -webkit-transition: all .6s;
        -moz-transition: all .6s;
        -ms-transition: all .6s;
        -o-transition: all .6s;
        transition: all .6s;
        align-items: center;
        text-align: center;
    }

    .inputEmailUsuarioGeralCodigo {
        background-image: none !important;
        padding: 25px 10px !important;
        border: 1px solid #ddd !important;
        background-color: #fff !important;
        border-radius: 3px;
        color: #666666;
        font-size: 20px !important;
        background-image: none;
        -webkit-transition: all .6s;
        -moz-transition: all .6s;
        -ms-transition: all .6s;
        -o-transition: all .6s;
        transition: all .6s;
        align-items: center;
        text-align: center;
    }

    .botaoModalAtualizarEmail {
        background: #1E60FA;
        background-image: -webkit-linear-gradient(top, #1E60FA, #1E60FA) !important;
        background-image: -moz-linear-gradient(top, #1E60FA, #1E60FA) !important;
        background-image: -ms-linear-gradient(top, #1E60FA, #1E60FA) !important;
        background-image: -o-linear-gradient(top, #1E60FA, #1E60FA) !important;
        background-image: linear-gradient(to bottom, #1E60FA, #1E60FA) !important;
        border-radius: 7px !important;
        font-family: Arial !important;
        color: #ffffff !important;
        font-size: 20px !important;
        padding: 10px 10px 10px 10px !important;
        width: 80% !important;
        text-decoration: none !important;
    }

</style>

<script type="text/javascript" src="${root}/dicas/js/jquery.stepy.min.js"></script>
<script type="text/javascript">
    var $j = jQuery.noConflict();
    $j(document).ready(function () {
        $j('.default').stepy({
            description: false,
            legend: false,
            titleClick: true,
            titleTarget: '#title-target'
        });
    });
</script>
<rich:modalPanel id="modalUsuarioGeral" domElementAttachment="parent" autosized="true" shadowOpacity="false" width="650"
                 styleClass="novaModal" showWhenRendered="#{LoginControle.apresentarModalUsuarioGeral}">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Validar e-mail"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <a4j:form>
            <a4j:commandLink id="btnValidarDepois"
                             action="#{LoginControle.validarEmailDepois}"
                             reRender="formModalUsuarioGeral"
                             oncomplete="#{LoginControle.msgAlert}#{LoginControle.mensagemNotificar}">
                <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkUsuarioGeral"/>
            </a4j:commandLink>
        </a4j:form>
    </f:facet>
    <a4j:form id="formModalUsuarioGeral" styleClass="pure-form" ajaxSubmit="true">
        <h:panelGrid id="divGeralModalUsuarioGeral" width="100%"
                      style="padding: 20px; text-align: center !important;">

            <h:panelGrid width="100%" rendered="#{empty LoginControle.tokenDTO.token}">

                <h:panelGrid columns="1" style="text-align: center !important;" width="100%">
                    <h:graphicImage url="../imagens/imgModalAlterarEmail-fechado.jpg" width="150px"/>
                </h:panelGrid>
                <br/>
                <c:if test="${!LoginControle.usuarioEmailVO.verificado && empty LoginControle.usuarioEmailAlterarVO.email}">
                    <h:outputText style="font-size: 16px; text-align: left" styleClass="font16 cinza"
                                  value="N�o encontramos seu e-mail em nosso cadastro."/>
                    <br/>
                    <br/>
                    <h:outputText style="font-size: 16px; text-align: left" styleClass="font16 cinza"
                                  value="Por favor informe para complet�-lo e j� seguir utilizando o sistema."/>
                </c:if>

                <c:if test="${!LoginControle.usuarioEmailVO.verificado && not empty LoginControle.usuarioEmailAlterarVO.email}">
                    <h:outputText style="font-size: 16px; text-align: left" styleClass="font16 cinza"
                                  value="Por favor, confirme seu e-mail."/>
                </c:if>
                </br>
                <h:panelGrid columns="1" style="text-align: center !important; background-color: #FDFDB4; border-radius: 10px;">
                    <rich:spacer height="15px"/>
                    <h:outputText style="font-size: 16px; text-align: left" styleClass="font16 cinza" escape="false"
                                  value="Por favor, valide seu e-mail. Em breve o acesso ao <strong>Sistema Pacto</strong> ser� poss�vel apenas pelo e-mail ou pelo n�mero de telefone."/>
                    <rich:spacer height="15px"/>
                </h:panelGrid>
                <rich:spacer height="15px"/>

                <h:inputText id="usuarioEmailEmail"
                             styleClass="inputEmailUsuarioGeral"
                             style="width: 100%; font-size: 20px !important; padding: 20px; text-align: left"
                             rendered="#{!LoginControle.usuarioEmailVO.verificado}"
                             value="#{LoginControle.usuarioEmailAlterarVO.email}"/>
                </br>
                <h:panelGrid columns="1" style="text-align: center !important;" width="100%">
                    <a4j:commandButton id="btnSolicitarNovoEmail"
                                     value="Validar"
                                     action="#{LoginControle.soliticarTokenNovoEmail}"
                                     reRender="formModalUsuarioGeral"
                                     focus="usuarioTokenEmailLogin"
                                     oncomplete="#{LoginControle.msgAlert}#{LoginControle.mensagemNotificar}"
                                     styleClass="botaoModalAtualizarEmail"/>
                </h:panelGrid>
                </br>
            </h:panelGrid>

            <h:panelGrid columns="1" style="text-align: center !important;" width="100%" rendered="#{not empty LoginControle.tokenDTO.token}">
                <h:graphicImage url="../imagens/imgModalAlterarEmail.jpg" width="150px"/>
                <br/>
                <h:outputText style="font-size: 16px; text-align: left" styleClass="font16 cinza" escape="false"
                              value="Enviamos um c�digo para <strong>#{LoginControle.usuarioEmailAlterarVO.email}</strong>. Verifique sua caixa de entrada e insira-o no campo abaixo.<br/>
                                Este c�digo <strong>expira</strong> em <strong>08 dias</strong>."/>

                <br/>

                <h:panelGrid columns="1" style="text-align: center !important; background-color: #FDFDB4; border-radius: 10px;">
                    <rich:spacer height="15px"/>
                    <h:outputText style="font-size: 16px; text-align: left" styleClass="font16 cinza" escape="false"
                                  value="Por favor, valide seu e-mail. Em breve o acesso ao <strong>Sistema Pacto</strong> ser� poss�vel apenas pelo e-mail ou pelo n�mero de telefone."/>
                    <rich:spacer height="15px"/>
                </h:panelGrid>
                <rich:spacer height="15px"/>
            </h:panelGrid>

            <h:panelGrid columns="1" style="text-align: center !important;" width="100%" rendered="#{not empty LoginControle.tokenDTO.token}">
                <h:inputText id="usuarioTokenEmailLogin"
                             styleClass="inputEmailUsuarioGeralCodigo" size="6" maxlength="6"
                             style="font-size: 24px !important;"
                             rendered="#{not empty LoginControle.tokenDTO.token}"
                             value="#{LoginControle.tokenDTO.codigoVerificacao}"/>

                </br>
                <a4j:commandLink id="btnReenviarNovoEmail"
                                 action="#{LoginControle.soliticarTokenNovoEmail}"
                                 rendered="#{not empty LoginControle.tokenDTO.token}"
                                 reRender="formModalUsuarioGeral"
                                 oncomplete="#{LoginControle.msgAlert}#{LoginControle.mensagemNotificar}"
                                 styleClass="texto-size-14 modulo-opener"
                                 style="position: relative; left: 265px;">
                    <h:outputText style="font-size: 14px" value="Reenviar c�digo"/>
                </a4j:commandLink>
                </br>

                <a4j:commandButton id="btnValidarTokenEmailLogin"
                                 rendered="#{not empty LoginControle.tokenDTO.token}"
                                 action="#{LoginControle.validarCodigoVerificacaoEmail}"
                                 reRender="mensagem, formModalUsuarioGeral"
                                 oncomplete="#{LoginControle.msgAlert}#{LoginControle.mensagemNotificar}"
                                 style="width: 80%; background-color: #1E60FA"
                                 styleClass="botaoModalAtualizarEmail"
                                 value="Confirmar">
                </a4j:commandButton>


                <%--                    <a4j:commandLink id="btnValidarDepois"--%>
                <%--                                     action="#{LoginControle.validarEmailDepois}"--%>
                <%--                                     reRender="formModalUsuarioGeral"--%>
                <%--                                     style="margin-left: 20px;"--%>
                <%--                                     oncomplete="#{LoginControle.msgAlert}#{LoginControle.mensagemNotificar}"--%>
                <%--                                     styleClass="botaoSecundario texto-size-14">--%>
                <%--                        <h:outputText style="font-size: 14px" value="Mais Tarde"/>--%>
                <%--                    </a4j:commandLink>--%>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>


<rich:modalPanel id="modalAnuncio" style="border-radius: 8px" domElementAttachment="parent" autosized="true"
                 shadowOpacity="false" width="926" height="500"
                 showWhenRendered="#{LoginControle.apresentarModalAnuncio}">
    <a4j:form id="formModalAnuncio" styleClass="pure-form" ajaxSubmit="true" style="max-height: 500px;overflow-x: hidden; overflow-y: auto">
        <h:panelGrid columns="1" width="100%" headerClass="subordinado" styleClass="paginaFontResponsiva"
                     style="text-align: left">

            <h:panelGroup layout="block" styleClass="font-anuncio font-titulo-anuncio font-negrito"
                          style="margin-top: 12px; width: 100%; text-align: center">
                Solu��o de <span
                    style="color: #2EC750 !important">consulta com nutricionistas</span> para os seus alunos <span
                    style="color: #2EC750 !important">sem custo!</span>
            </h:panelGroup>

            <h:panelGroup layout="block" style="height: 235px">
                <h:graphicImage url="/imagens/anuncio-01.svg" width="100%" height="235"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="font-anuncio"
                          style="margin-top: 12px; width: 100%; text-align: center;">
                Criamos uma plataforma de consultas online com nutricionistas de verdade para seus alunos. Voc� n�o vai precisar
                <span class="font-destaque-anuncio font-negrito">pagar nada</span> por isso e ainda <span
                    class="font-destaque-anuncio font-negrito">ganhar� 50% dos lucros</span> por cada consulta!
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGroup layout="block" style="margin: 40px 22px 22px;width: 100%; text-align: center">

            <a4j:commandLink reRender="mensagem, formModalAnuncioSaibaMais"
                             action="#{LoginControle.acaoSaibaMais}"
                             oncomplete="Richfaces.showModalPanel('modalAnuncioSaibaMais');Richfaces.hideModalPanel('modalAnuncio');"
                             styleClass="botaoPrimario botao-anuncio" style="background-color: #2EC750 !important;">
                <h:outputText styleClass="font-anuncio font-botao-anuncio" style="padding-left: 14%;padding-right: 14%;"
                              value="Saber mais"/>
            </a4j:commandLink>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalAnuncioSaibaMais" style="border-radius: 8px" domElementAttachment="parent" autosized="true"
                 shadowOpacity="false" width="926" height="500" showWhenRendered="false">
    <a4j:form id="formModalAnuncioSaibaMais" styleClass="pure-form" ajaxSubmit="true" style="max-height: 500px; overflow-x: hidden; overflow-y: auto">
        <h:panelGrid columns="1" width="100%" headerClass="subordinado" styleClass="paginaFontResponsiva"
                     style="text-align: left">

            <h:panelGroup layout="block" style="margin-top: 44px; width: 100%;display: flex">

                <h:panelGroup layout="block" style="width: 50%; margin-left: 22px; display: inline-grid;">
                    <h:panelGroup layout="block">
                        <h:outputText styleClass="font-anuncio font-anuncio-novidade font-negrito"
                                      value="Aceita ser nosso parceiro?!"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block">
                        <h:outputText styleClass="font-anuncio font-descricao-anuncio font-negrito"
                                      value="Vamos te oferecer toda plataforma de nutricionistas por R$0 de custo e participa��o de 50% dos lucros. � isso mesmo!"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block">
                        <h:outputText styleClass="font-anuncio font-descricao-anuncio"
                                      value="Voc� s� precisa estar de acordo com este novo recurso, todo o resto pode deixar com a gente. Vamos cuidar de todo o processo e suporte por voc�. "/>
                    </h:panelGroup>

                    <h:panelGroup layout="block">
                        <h:outputText styleClass="font-anuncio font-descricao-anuncio"
                                      value="J� estamos preparando tudo para fazer o grande lan�amento e deixar a plataforma dispon�vel ainda nos pr�ximos meses!"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block">
                        <h:outputLink value="https://vitio.com.br/parceiro-pacto" target="_blank">
                            <h:outputText styleClass="font-anuncio font-botao-anuncio" value="Assista ao v�deo e saiba como funciona"/>
                        </h:outputLink>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" style="height: 257px; text-align: center;margin-left: 20px">
                    <h:graphicImage url="/imagens/logo-vitio.svg" width="75%" height="140"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="painel-termo-uso">
                <h:panelGroup layout="block" styleClass="font-anuncio font-termo-uso texto-termo-uso">
                    TERMO DE ACEITE PARA OFERTA E VENDA DE PRODUTOS E SERVI�OS VITIO
                    <br/>
                    <br/>Este termo tem como finalidade informar voc�, gestor de neg�cio fitness, contratante dos servi�os de software da Pacto Solu��es, sobre o nosso mais novo servi�o denominado VITIO, que estar� em breve dispon�vel para seus alunos e como voc� pode faturar com essa novidade.
                    <br/>
                    <br/>O VITIO � um servi�o de software que permite aos seus alunos receberem atendimento de nutricionistas por meio de teleconsulta, al�m de poderem adquirir outros produtos e servi�os relacionados � nutri��o.
                    <br/>
                    <br/>Voc� j� sabe como os servi�os de nutri��o s�o fortes aliados do seu aluno para potencializar resultados dos treinos e alcan�ar melhorias na sa�de. Ent�o, siga na leitura para entender como voc� e a Pacto Solu��es poder�o, em conjunto, proporcionar a eles acesso f�cil a essa nova tecnologia.
                    <br/>
                    <br/>Leia este Termo com aten��o! Afinal, o seu aceite representa que voc� concorda plenamente com todos os termos.
                    <br/>
                    <br/>Como dito, com o acesso ao VITIO seu aluno, al�m de poder adquirir outros produtos e servi�os, receber�, mediante contrata��o, atendimento direto do consult�rio online de um nutricionista. Para tanto, a Pacto Solu��es, atrav�s de sua equipe de marketing preparou uma campanha para divulgar o VITIO, o que envolve o envio de mensagens informativas aos seus alunos por e-mail, msn, redes sociais, google ads dentre outros.
                    <br/>
                    <br/>Por isso, a Pacto Solu��es, vem aqui solicitar o seu aceite consentindo com o envio, aos seus alunos, de mensagens de promo��o do VITIO e de seu nome e logomarca. Voc� concorda? Continue lendo e veja as vantagens de realizar uma verdadeira parceria com a Pacto Solu��es.
                    <br/>
                    <br/>Confirmado o pagamento da consulta pelo aluno, a Pacto Solu��es realizar� as dedu��es de honor�rios do profissional, taxas administrativas e tributos aplic�veis e repassar� a voc� 50% (cinquenta por cento) sobre os lucros apurados, os quais ser�o convertidos em descontos em sua mensalidade com a Pacto Solu��es. E caso o b�nus seja superior ao valor da mensalidade voc� ter� direito a receber o valor remanescente em moeda corrente. Tudo depender� da quantidade de servi�os adquiridos pelos seus alunos.
                    <br/>
                    <br/>�timo! E, ent�o, como funcionar� o VITIO?
                    <br/>
                    <br/>Uma vez interessado no servi�o, o aluno poder� acessar, por meio de link, uma p�gina com uma lista de nutricionistas para escolher e agendar um atendimento online, realizar um cadastro com dados pessoais e de pagamento dos servi�os. Os valores variam de acordo com a campanha em vigor, que estar� dispon�vel para voc� em nossas plataformas digitais. Todo o atendimento ao aluno ocorrer� por e-mail com as informa��es referentes a consulta e com explica��es sobre como ocorrer� a v�deo chamada com o nutricionista escolhido. Simples assim!
                    <br/>
                    <br/>Consulta realizada, o aluno receber� seu plano nutricional pelo aplicativo ou outro meio de sua escolha e ter� o agendamento da sua pr�xima consulta feita pelo pr�prio nutricionista. Mas, se o aluno quiser, poder� reagendar ou cancelar o atendimento pelo aplicativo, assim como ver o resumo da consulta realizada.
                    <br/>
                    <br/>Al�m disso, caso o aluno tenha qualquer d�vida, reclama��o, sugest�o ou elogio ele ter� um canal de atendimento direto com a Pacto Solu��es, que far� as primeiras tratativas, realizando a intermedia��o entre aluno, nutricionista e plataforma de teleconsultas.
                    <br/>
                    <br/>E, como voc� ir� receber? No final de cada m�s ser� enviado para seu email, um documento com a rela��o de todas as consultas realizadas pelos seus alunos, assim como o valor do b�nus que ir� receber no m�s subsequente. Esse valor ser� automaticamente descontado na sua mensalidade dos servi�os do Sistema Pacto. Caso o valor a receber seja superior ao de sua mensalidade, a diferen�a ser� repassada por meios banc�rios ap�s a sua apresenta��o da nota pelo servi�o. Nesse caso, precisamos que voc� entre em contato conosco, <EMAIL> para manifestar o melhor meio para realizarmos essa opera��o.
                    <br/>
                    <br/>Caso tenha d�vidas, ou n�o queira aceitar o presente termo, tudo bem! Mas, sugerimos que entre em contato conosco pelo canal de atendimento: <EMAIL> e fale com um de nossos especialistas para obter maiores informa��es e entender melhor as vantagens que voc� ter� em estabelecer uma parceria com a Pacto Solu��es ao oportunizar aos seus alunos o acesso aos nossos servi�os VITIO.
                    <br/>
                    <br/>Para mais informa��es sobre o uso de nossas plataformas digitais e aplicativos, acesse aqui os termos e condi��es de uso.
                </h:panelGroup>
            </h:panelGroup>


            <h:panelGroup layout="block" styleClass="font-anuncio"
                          rendered="#{not LoginControle.naoQueroLucrar}"
                          style="margin-left: 22px;margin-top: 26px; width: 100%; text-align: left;">
                <h:panelGroup layout="block">
                    <h:selectBooleanCheckbox value="#{LoginControle.concordoTermosUsoAnuncio}">
                        <a4j:support event="onchange" reRender="pnlBotoesSaibaMais"/>
                    </h:selectBooleanCheckbox>
                    <h:outputText styleClass="font-anuncio font-botao-anuncio nao-quero-lucrar font-black-pri"
                                  style="padding-left: 1%;" value="Concordo com os termos de uso"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGroup id="pnlBotoesSaibaMais" layout="block" style="margin: 36px 26px 26px;width: 100%">

            <h:panelGroup layout="block" rendered="#{LoginControle.naoQueroLucrar}">

                <h:inputText id="inputRecusa" styleClass="input-recusa font-anuncio" value="#{LoginControle.motivoRecusa}"/>
                <script type="text/javascript">
                    document.getElementById("formModalAnuncioSaibaMais:inputRecusa").setAttribute("placeholder","Poderia nos informar o motivo da recusa?");
                </script>


                <a4j:commandLink reRender="mensagem"
                                 action="#{LoginControle.enviarRejeicao}"
                                 oncomplete="Richfaces.hideModalPanel('modalAnuncioSaibaMais');"
                                 style="background-color: #2EC750 !important; cursor: ponter;"
                                 styleClass="botaoPrimario botao-anuncio">
                    <h:outputText
                            styleClass="font-anuncio font-botao-anuncio"
                            style="padding-left: 5%;padding-right: 5%;" value="Enviar resposta"/>
                </a4j:commandLink>
            </h:panelGroup>

            <h:panelGroup layout="block" rendered="#{!LoginControle.naoQueroLucrar}">
                <a4j:commandLink reRender="mensagem, formModalAnuncioSaibaMais"
                                 action="#{LoginControle.acaoNaoQueroLucrar}"
                                 styleClass="botao-anuncio nao-quero-lucrar">
                    <h:outputText styleClass="font-anuncio font-botao-anuncio nao-quero-lucrar"
                                  style="padding-left: 15%;padding-right: 15%;" value="N�o quero lucrar"/>

                </a4j:commandLink>

                <a4j:commandLink disabled="#{not LoginControle.concordoTermosUsoAnuncio}"
                                 action="#{LoginControle.enviarAceite}"
                                 reRender="mensagem"
                                 oncomplete="Richfaces.showModalPanel('modalAnuncioParabens');Richfaces.hideModalPanel('modalAnuncioSaibaMais');"
                                 style="#{LoginControle.style_ConcordoTermoUsoAnuncio};"
                                 styleClass="botaoPrimario botao-anuncio">
                    <h:outputText
                            styleClass="font-anuncio font-botao-anuncio #{LoginControle.class_ConcordoTermoUsoAnuncio}"
                            style="padding-left: 15%;padding-right: 15%;" value="Li e aceito"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalAnuncioParabens" style="border-radius: 8px" domElementAttachment="parent" autosized="true"
                 shadowOpacity="false" width="926" height="500" showWhenRendered="false">
    <a4j:form id="formModalAnuncioParabens" styleClass="pure-form" ajaxSubmit="true" style="max-height: 500px; overflow-x: hidden; overflow-y: auto">
        <h:panelGrid columns="1" width="100%" headerClass="subordinado" styleClass="paginaFontResponsiva"
                     style="text-align: left">

            <h:panelGroup layout="block" style="margin-top: 18px; width: 100%; text-align: center">
                <h:outputText styleClass="font-anuncio font-titulo-anuncio font-negrito font-black-pri"
                              value="Parab�ns pela sua escolha"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="font-anuncio font-titulo-anuncio"
                          style="margin-top: 20px; width: 100%; text-align: center">
                <h:graphicImage url="/imagens/anuncio-final.svg" width="100%" height="240"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="font-anuncio"
                          style="margin-top: 20px; text-align: center;">
                Mandamos um e-mail para
                <h:outputText style="color: #2EC750 !important" value="#{LoginControle.usuarioEmailVO.email}"/> com a confirma��o da sua participa��o e todas as informa��es sobre a parceria. Se j� quiser saber mais sobre esse novo recurso,
                <h:outputLink styleClass="font-anuncio" style="color: #2EC750 !important" value="https://vitio.com.br/parceiro-pacto" target="_blank">
                    <h:outputText styleClass="font-anuncio" value="clique aqui!"/>
                </h:outputLink>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGroup layout="block" style="margin: 18px 22px 22px;width: 100%">
            <a4j:commandLink reRender="mensagem"
                             oncomplete="Richfaces.hideModalPanel('modalAnuncioParabens');"
                             styleClass="botaoPrimario botao-anuncio" style="background-color: #2EC750 !important;">
                <h:outputText styleClass="font-anuncio font-botao-anuncio" style="padding-left: 44%;padding-right: 43%;"
                              value="OK!"/>
            </a4j:commandLink>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

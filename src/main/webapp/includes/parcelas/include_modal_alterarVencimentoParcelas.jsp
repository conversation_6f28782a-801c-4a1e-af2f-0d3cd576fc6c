<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%--
    Document   : include_modal_alterarVencimentoParcelas
    Created on : 10/10/2011, 16:00:29
    Author     : Waller
--%>

<%@include file="/includes/imports.jsp" %>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
<a4j:outputPanel>    
    <rich:modalPanel id="modalAlterarVencimento"  styleClass="novaModal" 
                     showWhenRendered="#{AlterarVencimentoParcelasControle.exibirModal
                                         && !AlterarVencimentoParcelasControle.gestaoRemessas}" width="750"
                     autosized="true" shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Alterar Vencimento" />
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form id="formFA">
                    <a4j:commandLink id="btnFecharAlt" style="cursor:pointer"
                                     action="#{AlterarVencimentoParcelasControle.fecharPanelDadosParametros}"
                                     oncomplete="#{rich:component('modalAlterarVencimento')}.hide();">
                        <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            id="hiperlinkHistoricoVinculo"/>
                    </a4j:commandLink>
                </a4j:form>
            </h:panelGroup>
        </f:facet>        
        <a4j:form id="formAlterar" styleClass="paginaFontResponsiva">
        <h:panelGroup id="panelConteudo">
            
            <h:panelGroup rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas}">
                <rich:panel rendered="#{AlterarVencimentoParcelasControle.cliente != null
                                        && AlterarVencimentoParcelasControle.gestaoRemessas}">
                    <h:outputText style="font-weight:bold;" value="Cliente: "/>
                    <h:outputText value="#{AlterarVencimentoParcelasControle.cliente.pessoa.nome }"/>

                    <h:panelGroup style="position: absolute; right: 12px;">
                        <a4j:commandButton id="btnRealizarContato"
                                           style="padding: 2px;"
                                           value="Realizar contato"
                                           action="#{AlterarVencimentoParcelasControle.abrirContatoCliente}" 
                                           oncomplete="#{AlterarVencimentoParcelasControle.msgAlert}"/>
                    </h:panelGroup>
                </rich:panel>
                
                <rich:panel rendered="#{AlterarVencimentoParcelasControle.cliente == null
                                        && AlterarVencimentoParcelasControle.gestaoRemessas}">
                    <rich:calendar id="dataInicio"
                                   value="#{AlterarVencimentoParcelasControle.dataTodos}"
                                   inputSize="10"
                                   inputClass="form calendarAlterarVencimento"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <a4j:commandButton value="Aplicar a todos" reRender="panelConteudo"
                                       title="Aplicar o dia escolhido em todas as parcelas abaixo"
                                       action="#{AlterarVencimentoParcelasControle.aplicarDataTodos}"/>

                </rich:panel>
                <rich:panel style="height:55px" rendered="#{AlterarVencimentoParcelasControle.cliente == null
                                                            && !AlterarVencimentoParcelasControle.gestaoRemessas}">
                    <h:panelGroup style="margin-botton: 5px; margin-top: 5px;">
                        <h:outputText style="font-weight:bold;" value="Contrato: "/>
                        <h:outputText value="#{AlterarVencimentoParcelasControle.contrato.codigo }"/>
                        <rich:spacer width="20px"/>
                        <h:outputText style="font-weight:bold;" value="Cliente: "/>
                        <h:outputText value="#{AlterarVencimentoParcelasControle.contrato.pessoa.nome }"/><br/>
                    </h:panelGroup>
                    <h:panelGroup>

                            <h:outputText style="font-weight:bold;" value="Dia Vencimento: "/>
                            <rich:spacer width="5px"/>
                            <h:inputText id="diaVencimento" value="#{AlterarVencimentoParcelasControle.diaVencimento}"
                                         size="2"
                                         readonly="#{empty AlterarVencimentoParcelasControle.listaParcelas}"
                                         maxlength="2"
                                         onkeypress="return mascara(this.form, this.id, '9999999999999999999', event);"/>

                            <a4j:commandButton status="statusAlteraVencimento" rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas}"
                                               value="Alterar" reRender="panelConteudo" title="Aplicar o dia escolhido em todas as parcelas abaixo"
                                               action="#{AlterarVencimentoParcelasControle.aplicarNovaDataVencimento}"/>

                            <h:panelGroup style="vertical-align:middle;">
                                <h:graphicImage id="imageLoading" style="visibility: hidden;vertical-align: middle;align:center"
                                                url="images/loading.gif"/>
                            </h:panelGroup>

                    </h:panelGroup>
                </rich:panel>
            </h:panelGroup>
            <br/>
                <rich:dataTable rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas}"
                                id="tblParcelas" rowClasses="linhaImpar,linhaPar" width="100%"
                                rows="8"
                                value="#{AlterarVencimentoParcelasControle.listaParcelas}" var="parcela">

                    <rich:column width="40">
                        <f:facet name="header">
                            <h:outputText value="Cod."/>
                        </f:facet>
                        <h:outputText value="#{parcela.codigo}" style="font-size: 11px !important;" />
                    </rich:column>

                    <rich:column rendered="#{AlterarVencimentoParcelasControle.gestaoRemessas}">
                        <f:facet name="header">
                            <h:outputText title="Nome da Pessoa" value="Pessoa"/>
                        </f:facet>
                        <h:outputText style="font-size: 11px !important;" value="#{parcela.pessoa.nome}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição"/>
                        </f:facet>
                        <h:outputText style="font-size: 11px !important;" value="#{parcela.descricao}"/>
                    </rich:column>

                    <rich:column width="60" rendered="#{!AlterarVencimentoParcelasControle.gestaoRemessas}">
                        <f:facet name="header">
                            <h:outputText value="Contrato"/>
                        </f:facet>
                        <h:outputText value="#{parcela.contrato.codigo}" style="font-size: 11px !important;" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{AlterarVencimentoParcelasControle.gestaoRemessas ? 'Cobr.' : 'Venc.'}"/>

                        </f:facet>
                        <h:panelGroup styleClass="dateTimeCustom"
                                      rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas &&
                                                   !AlterarVencimentoParcelasControle.gestaoRemessas}">
                            <rich:calendar id="dataVenc" value="#{parcela.dataVencimento}"
                                       
                                   inputSize="10" 
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   inputClass="form calendarAlterarVencimento"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="validar_Data(this.id);alteraVencimento();"
                                   onchanged="alteraVencimento();"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" >
                        </rich:calendar>
                        </h:panelGroup>
                        <h:panelGroup  styleClass="dateTimeCustom"
                                       rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas && AlterarVencimentoParcelasControle.gestaoRemessas}">
                            <rich:calendar id="dataCobranca" value="#{parcela.dataCobranca}"
                                  
                                   inputSize="10"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   inputClass="form calendarAlterarVencimento"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="validar_Data(this.id);alteraVencimento();"
                                   onchanged="alteraVencimento();"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false" >
                        </rich:calendar>
                        </h:panelGroup>
                        <a4j:jsFunction name="alteraVencimento"   reRender="tblParcelas" status="statusAlteraVencimento"/>

                        
                    </rich:column>

                    <rich:column width="60">
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText value="R$ #{parcela.valorParcelaNumerico}"
                                      style="font-size: 11px !important;" />
                    </rich:column>

                    <rich:column width="60" rendered="#{!AlterarVencimentoParcelasControle.gestaoRemessas}">
                        <f:facet name="header">
                            <h:outputText value="Sit."/>
                        </f:facet>
                        <h:outputText value="#{parcela.situacao_Apresentar}"
                                      style="font-size: 11px !important;" />
                    </rich:column>
                </rich:dataTable>   
                    <rich:datascroller for="tblParcelas" rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas}"></rich:datascroller>
                    <h:panelGrid id="avisoParRem">
                        <h:outputText styleClass="mensagem" value="* Parcelas que estão em remessas geradas ou aguardando retorno, não são apresentadas."/>
                    </h:panelGrid>
                     <rich:jQuery id="mskData" selector=".calendarAlterarVencimento" timing="onload" query="mask('99/99/9999')" />
                <h:panelGroup id="botoes">
                    <a4j:commandButton rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas && AlterarVencimentoParcelasControle.gestaoRemessas}"
                                       id="btnGravarRem" title="Grava as alterações de vencimento realizadas"
                                       value="Gravar" style="float: right;"
                                       reRender="panelAutorizacaoFuncionalidade"
                                       action="#{AlterarVencimentoParcelasControle.gravarComPermissao}"/>


                    <a4j:commandButton rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas &&
                                                   !AlterarVencimentoParcelasControle.gestaoRemessas}"

                                       id="btnGravar" title="Grava as alterações de vencimento realizadas"
                                       value="Gravar" style="float: right;"
                                       onclick="if (!confirm('O vencimento do contrato de recorrência e as parcelas em aberto serão alteradas permanentemente. Deseja continuar?')){return false;}"
                                       reRender="panelConteudo, panelContratoRecorrencia"
                                       oncomplete="#{AlterarVencimentoParcelasControle.mensagemNotificar}"
                                       action="#{AlterarVencimentoParcelasControle.gravar}"/>

                    <a4j:commandButton id="btnGerarDatas" value="Gerar Datas" style="float: right; margin-right: 15px"
                                       rendered="#{!empty AlterarVencimentoParcelasControle.listaParcelas && !AlterarVencimentoParcelasControle.gestaoRemessas}"
                                       title="Programa as Datas de 30 em 30 dias"
                                       oncomplete="#{AlterarVencimentoParcelasControle.mensagemNotificar}"
                                       action="#{AlterarVencimentoParcelasControle.gerarDatas}"
                                       reRender="panelConteudo"/>
                </h:panelGroup>
            

            <h:panelGrid id="mensagemModalAlterarVencimento" columns="1" width="100%" styleClass="tabMensagens" style="margin:0 0 0 0;">
                <h:outputText styleClass="mensagem" value="#{AlterarVencimentoParcelasControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{AlterarVencimentoParcelasControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <a4j:status forceId="true" id="statusAlteraVencimento"
                onstart="document.getElementById('formAlterar:imageLoading').style.visibility = '';"
                onstop="document.getElementById('formAlterar:imageLoading').style.visibility = 'hidden';"/>

</a4j:outputPanel>

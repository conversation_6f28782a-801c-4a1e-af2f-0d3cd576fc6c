<%--
  Created by IntelliJ IDEA.
  User: <PERSON>mberto
  Date: 03/05/2019
  Time: 08:27
  Tabela paginada de planos em campanha Utilizado no Clube de Vantagens
--%>
<%@include file="/includes/imports.jsp" %>
<rich:dataTable id="tblComPontos" style="width:97% !important"
                styleClass="tabelaSimplesCustom"
                rendered="#{ItemCampanhaControle.mostrarPlanosComPonto}"
                value="#{ItemCampanhaControle.listaPlanosComPonto}"
                var="planoCP">
    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left"
                 sortBy="#{planoCP.planoReferencia.descricao}" filterEvent="onkeyup">
        <f:facet name="header">
            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"
                          value="#{msg_aplic.prt_descricao}"/>
        </f:facet>
        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                      value="#{planoCP.planoReferencia.descricao}"/>
        <a4j:support event="onclick"
                     actionListener="#{ItemCampanhaControle.selecionarPlanoSuggestionBox}"
                     oncomplete="#{ItemCampanhaControle.onComplete}"
                     reRender="panelBotoesControle, mensagem,grupoPlanoDuracao,grupoPlano,grupoPlanoPontuacao">
            <f:attribute name="pequisaPlano" value="#{planoCP.planoReferencia}"/>
        </a4j:support>
    </rich:column>
    <rich:column>
        <a4j:commandLink id="visualizarPlano"
                         actionListener="#{ItemCampanhaControle.irParaTelaPlano}"
                         title="#{msg_aplic.prt_ir_para_tela_plano}"
                         oncomplete="#{ItemCampanhaControle.onComplete}"
                         styleClass="linkPadrao texto-cor-azul texto-size-14-real tooltipster">
            <i class="fa-icon-search"></i>
            <f:attribute name="planoComPonto" value="#{planoCP.planoReferencia}"/>
        </a4j:commandLink>
    </rich:column>
</rich:dataTable>

<h:panelGrid id="gridPaginadorCP" columns="1" width="100%" columnClasses="colunaCentralizada"
             rendered="#{ItemCampanhaControle.mostrarPlanosComPonto && ItemCampanhaControle.paginadorListaPlanosCP.count>0}">
    <table width="100%" border="0" cellspacing="0" cellpadding="0"  style="font-size: small">
        <tr>
            <td align="center" valign="middle">
                <h:panelGroup layout="block"
                              styleClass="paginador-container">
                    <h:panelGroup styleClass="pull-left"
                                  layout="block">
                        <h:outputText
                                styleClass="texto-size-14 texto-cor-cinza"
                                value="#{msg_aplic.prt_total_de}#{ItemCampanhaControle.paginadorListaPlanosCP.count} #{msg_aplic.ITENS}"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block"
                                  style="align-items: center">
                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaPlanosCP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblComPontos, gridPaginadorCP"
                                actionListener="#{ItemCampanhaControle.primeiraPagina}">
                            <i class="fa-icon-double-angle-left" id="primPaginaHistorico"></i>
                            <f:attribute name="tipo"
                                         value="Planos"/>
                        </a4j:commandLink>

                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaPlanosCP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblComPontos, gridPaginadorCP"
                                actionListener="#{ItemCampanhaControle.paginaAnterior}">
                            <i class="fa-icon-angle-left" id="pagAntHistorico"></i>
                            <f:attribute name="tipo"
                                         value="Planos"/>
                        </a4j:commandLink>
                        <h:outputText
                                styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                value="#{msg_aplic.prt_msg_pagina} #{ItemCampanhaControle.paginadorListaPlanosCP.paginaAtualApresentar}"
                                rendered="true"/>
                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaPlanosCP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                reRender="tblComPontos, gridPaginadorCP"
                                actionListener="#{ItemCampanhaControle.proximaPagina}">
                            <i class="fa-icon-angle-right" id="proxPagHistorico"></i>
                            <f:attribute name="tipo"
                                         value="Planos"/>
                        </a4j:commandLink>

                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaPlanosCP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblComPontos, gridPaginadorCP"
                                actionListener="#{ItemCampanhaControle.ultimaPagina}">
                            <i class="fa-icon-double-angle-right" id="ultimaPaginaHistorico"></i>
                            <f:attribute name="tipo"
                                         value="Planos"/>
                        </a4j:commandLink>
                    </h:panelGroup>


                    <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                        <h:panelGroup styleClass="pull-right" layout="block">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza"
                                          value="#{msg_aplic.prt_itens_por_pagina}"/>
                        </h:panelGroup>
                        <h:panelGroup styleClass="cb-container pl20" layout="block">
                            <h:selectOneMenu
                                    value="#{ItemCampanhaControle.paginadorListaPlanosCP.limit}"
                                    id="qtdeItensPaginaPlanoCP">
                                <f:selectItem itemValue="#{10}"></f:selectItem>
                                <f:selectItem itemValue="#{30}"></f:selectItem>
                                <f:selectItem itemValue="#{50}"></f:selectItem>
                                <f:selectItem itemValue="#{100}"></f:selectItem>
                                <a4j:support event="onchange"
                                             actionListener="#{ItemCampanhaControle.atualizarNumeroItensPagina}"
                                             reRender="tblComPontos, gridPaginadorCP">
                                    <f:attribute name="tipo" value="Planos"/>
                                </a4j:support>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </td>
        </tr>
    </table>
</h:panelGrid>
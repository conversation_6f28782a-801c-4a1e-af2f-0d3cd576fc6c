<%--
  Created by IntelliJ IDEA.
  User: <PERSON>mberto
  Date: 22/05/2019
  Time: 17:03
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/imports.jsp" %>
<style>

    table.tabelaSimplesCustom>thead .rich-table-subheadercell:first-child>div {
        margin-left: 0 !important;
    }

    .rotuloCampos {
        color: black !important;
        font-size: 0.7vw !important;
    }

    .fonteItens {
        font-size: 0.7vw !important;
    }

</style>
<rich:dataTable id="tblComPontos" width="100%"
                styleClass="tabelaSimplesCustom"
                value="#{ClubeVantagensControle.campanha.listaItem}"
                var="tipoItemCampanha">

    <rich:column styleClass="col-text-align-left texto-cor-cinza texto-font fonteItens" headerClass="col-text-align-left"  sortBy="#{tipoItemCampanha.tipoItemCampanha.descricao}" filterEvent="onkeyup">
        <f:facet name="header">
            <h:outputText styleClass="rotuloCampos texto-bold" value="Categoria"/>
        </f:facet>
        <h:outputText value="#{tipoItemCampanha.tipoItemCampanha.descricao}"/>
        <a4j:support event="onclick" reRender="selecionarCategoria"  focus="multipicadorCategoria" />
    </rich:column>
    <rich:column styleClass="col-text-align-left texto-cor-cinza texto-cor-cinza texto-font fonteItens" headerClass="col-text-align-left" sortBy="#{tipoItemCampanha.pontos}"
                 filterEvent="onkeyup">
        <f:facet name="header">
            <h:outputText styleClass="rotuloCampos texto-bold"
                          value="Multiplicador"/>
        </f:facet>
        <h:inputText id="multipicadorCategoria" value="#{tipoItemCampanha.pontos}" onkeypress="if(event.which < 48 || event.which > 57 ) if(event.which != 8) return false;"
                     title="Valores para multiplicador deve ser maior que 1!"
                     styleClass="inputTextClean" style="font-size: small !important; width: 6vw !important;"  />

    </rich:column>
</rich:dataTable>
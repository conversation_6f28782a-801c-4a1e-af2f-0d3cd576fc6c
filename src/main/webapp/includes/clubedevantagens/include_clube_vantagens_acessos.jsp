<%--
  Created by IntelliJ IDEA.
  User: <PERSON>mberto
  Date: 02/05/2019
  Time: 09:07
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/imports.jsp" %>
<h:panelGroup id="groupAcesso" layout="block" styleClass="panelCampanhas acesso" style="width: 100%; display:none;font-weight: normal; margin-right: 10px;height: auto;overflow: auto;">
    <h:panelGrid columns="1">
        <h:panelGroup id="pontosclimagroup" >
            <rich:spacer height="13px"/>
            <div>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="Pontos por acesso " />
                <h:inputText  id="campanhaPontosAcesso" size="5" style="font-size: small !important;"
                              styleClass="inputTextClean" onkeyup="somenteNumerosInteiros(this);" value="#{ItemCampanhaControle.itemCampanhaAcesso.pontos}"/>
            </div>
            <rich:spacer height="20px"/>
            <div>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{msg_aplic.prt_minutos_para_creditar_pontos} " />
                <h:inputText  id="campanhaMinutosParaCreditar" size="5" style="font-size: small !important;"
                              styleClass="inputTextClean" onkeyup="somenteNumerosInteiros(this);"
                              disabled="#{ItemCampanhaControle.empresaSelecionada.apenasPrimeiroAcessoClubeVantagens}"
                              value="#{ItemCampanhaControle.empresaSelecionada.minutosCreditarProximoPontoClubeVantagens}"/>
            </div>
            <rich:spacer height="20px"/>
            <div>

                <h:panelGrid columns="2">
                    <h:panelGroup>
                        <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{msg_aplic.prt_considerar_apenas_primeiro_acesso}" />
                        <h:panelGroup styleClass="chk-fa-container">
                            <h:selectBooleanCheckbox tabindex="2"
                                                     title="Pontuar apenas primeiro acesso!"
                                                     value="#{ItemCampanhaControle.empresaSelecionada.apenasPrimeiroAcessoClubeVantagens}">
                                <a4j:support event="onchange" reRender="chkPontuarApenasCampanhaAtiva,campanhaMinutosParaCreditar"/>
                            </h:selectBooleanCheckbox>
                            <span/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>

            </div>
            <rich:spacer height="20px"/>
            <h:panelGroup>
                <div style="display: flex; gap: 5px;" title="Selecione os dias da semana nos quais os pontos por acesso estar�o ativos.">
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Dias da Semana:" />
                    <i class="fa-icon-question-sign" style="font-size: 18px; color: #D3D3D3"></i>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center; font-size: 12px; margin-top: 10px;">
                    <span style="font-weight: bold">Atalhos:</span>
                    <a4j:commandLink action="#{ItemCampanhaControle.atalhoDiasSelecionados('uteis')}" reRender="diasSelecionadosContainer">Dias �teis (Seg-Sex)</a4j:commandLink>
                    <a4j:commandLink action="#{ItemCampanhaControle.atalhoDiasSelecionados('fds')}" reRender="diasSelecionadosContainer">Final de semana (S�b-Dom)</a4j:commandLink>
                    <a4j:commandLink action="#{ItemCampanhaControle.atalhoDiasSelecionados('todos')}" reRender="diasSelecionadosContainer">Todos os dias</a4j:commandLink>
                </div>
                    <h:panelGroup id="diasSelecionadosContainer">
                        <div style="font-size: 12px; margin-top: 10px; margin-bottom: 10px; background-color: #F3F6F9; padding: 12px 24px; border-radius: 10px; border: 1px solid #E5EAF2;">
                            <h:selectManyCheckbox style="padding-left: 40px;" value="#{ItemCampanhaControle.itemCampanhaAcesso.diasDaSemanaAtivos}" layout="lineDirection">
                                <div style="display: flex; gap: 1rem;">
                                    <f:selectItem itemValue="SEG" itemLabel="Seg " />
                                    <f:selectItem itemValue="TER" itemLabel="Ter " />
                                    <f:selectItem itemValue="QUA" itemLabel="Qua " />
                                    <f:selectItem itemValue="QUI" itemLabel="Qui " />
                                    <f:selectItem itemValue="SEX" itemLabel="Sex " />
                                    <f:selectItem itemValue="SAB" itemLabel="Sab " />
                                    <f:selectItem itemValue="DOM" itemLabel="Dom " />
                                </div>
                            </h:selectManyCheckbox>
                        </div>
                    </h:panelGroup>
            </h:panelGroup>
            <rich:spacer height="40px"/>
            <div>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Situa��es Especiais" />
                <h:outputLink styleClass="linkWiki" value="#{ClubeVantagensControle.linkiWikiAcesso}" title="Clique e saiba mais: Situa��es Especiais " target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </div>
            <rich:spacer height="13px"/>
            <div>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="Pontos por acesso na CHUVA "  />
                <h:inputText  id="pontosAcessoChuva" size="5" style="font-size: small !important;"
                              styleClass="inputTextClean" onkeyup="somenteNumerosInteiros(this);" value="#{ItemCampanhaControle.itemCampanhaAcessoChuva.pontos}"/>
            </div>
            <rich:spacer height="20px"/>
            <div>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="Pontos por acesso no FRIO "  />
                <h:inputText  id="pontosAcessoFrio" size="5" style="font-size: small !important;"
                              styleClass="inputTextClean" onkeyup="somenteNumerosInteiros(this);" value="#{ItemCampanhaControle.itemCampanhaAcessoFrio.pontos}"/>
            </div>
            <rich:spacer height="20px"/>
            <div>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="Pontos por acesso no CALOR " />
                <h:inputText  id="pontosAcessoCalor" size="5" style="font-size: small !important;"
                              styleClass="inputTextClean" onkeyup="somenteNumerosInteiros(this);" value="#{ItemCampanhaControle.itemCampanhaAcessoCalor.pontos}"/>
            </div>
        </h:panelGroup>
        <rich:spacer height="40px"/>
        </h:panelGrid>
        <h:panelGroup>
            <a4j:commandLink id="salvarItemTipoAcesso"
                             styleClass="pure-button pure-button-primary"
                             action="#{ItemCampanhaControle.salvarItemCampanhaAcesso}"
                             oncomplete="#{ItemCampanhaControle.mensagemNotificar};efeitoClick();"
                             accesskey="2">
                Salvar
            </a4j:commandLink>
        </h:panelGroup>
</h:panelGroup>

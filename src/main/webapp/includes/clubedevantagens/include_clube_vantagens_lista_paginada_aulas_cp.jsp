<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>to
  Date: 09/05/2019
  Time: 09:06
--%>
<%@include file="/includes/imports.jsp" %>
<rich:dataTable id="tblAulasComPontos" width="100%"
                 styleClass="tabelaSimplesCustom"
                 rendered="#{ItemCampanhaControle.mostrarAulasComPonto}"
                 value="#{ItemCampanhaControle.listaAulasComPonto}"
                 var="aula">
    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{aula.aulaReferencia.nome}" filterEvent="onkeyup">
        <f:facet name="header">
            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_descricao}" />
        </f:facet>
        <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{aula.aulaReferencia.nome}" />
        <a4j:support event="onclick"
                     actionListener="#{ItemCampanhaControle.selecionarAulaSuggestionBox}"
                     oncomplete="#{ItemCampanhaControle.onComplete}"
                     reRender="panelBotoesControle, mensagem, grupoAula,grupoAulaDetalhes,grupoAulaPontuacao">
            <f:attribute name="pequisaTurma" value="#{aula.aulaReferencia}"/>
        </a4j:support>
    </rich:column>
    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{aula.aulaReferencia.dias}" filterEvent="onkeyup">
        <f:facet name="header">
            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Dias" />
        </f:facet>
        <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{aula.aulaReferencia.dias}" />
    </rich:column>
    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{aula.pontos}" filterEvent="onkeyup">
        <f:facet name="header">
            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_campanha_pontuacao}" />
        </f:facet>
        <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{aula.pontos}" />
    </rich:column>
</rich:dataTable>
<h:panelGrid id="gridPaginadorAulaCP" columns="1" width="100%" columnClasses="colunaCentralizada"
             rendered="#{ItemCampanhaControle.mostrarAulasComPonto && ItemCampanhaControle.paginadorListaAulaCP.count>0}">
    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="font-size: small">
        <tr>
            <td align="center" valign="middle">
                <h:panelGroup layout="block"
                              styleClass="paginador-container">
                    <h:panelGroup styleClass="pull-left"
                                  layout="block">
                        <h:outputText
                                styleClass="texto-size-14 texto-cor-cinza"
                                value="#{msg_aplic.prt_total_de}#{ItemCampanhaControle.paginadorListaAulaCP.count} #{msg_aplic.ITENS}"></h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block"
                                  style="align-items: center">
                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaAulaCP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblAulasComPontos, gridPaginadorAulaCP, detalhesAulas"
                                actionListener="#{ItemCampanhaControle.primeiraPagina}">
                            <i class="fa-icon-double-angle-left" id="primPaginaHistorico"></i>
                            <f:attribute name="tipo"
                                         value="Aula Coletiva"/>
                        </a4j:commandLink>

                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaAulaCP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblAulasComPontos, gridPaginadorAulaCP, detalhesAulas"
                                actionListener="#{ItemCampanhaControle.paginaAnterior}">
                            <i class="fa-icon-angle-left" id="pagAntHistorico"></i>
                            <f:attribute name="tipo"
                                         value="Aula Coletiva"/>
                        </a4j:commandLink>
                        <h:outputText
                                styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                value="#{msg_aplic.prt_msg_pagina} #{ItemCampanhaControle.paginadorListaAulaCP.paginaAtualApresentar}"
                                rendered="true"/>
                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaAulaCP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                reRender="tblAulasComPontos, gridPaginadorAulaCP, detalhesAulas"
                                actionListener="#{ItemCampanhaControle.proximaPagina}">
                            <i class="fa-icon-angle-right" id="proxPagHistorico"></i>
                            <f:attribute name="tipo"
                                         value="Aula Coletiva"/>
                        </a4j:commandLink>

                        <a4j:commandLink
                                disabled="#{ItemCampanhaControle.paginadorListaAulaCP.desabilitarAcoes}"
                                styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                reRender="tblAulasComPontos, gridPaginadorAulaCP, detalhesAulas"
                                actionListener="#{ItemCampanhaControle.ultimaPagina}">
                            <i class="fa-icon-double-angle-right" id="ultimaPaginaHistorico"></i>
                            <f:attribute name="tipo"
                                         value="Aula Coletiva"/>
                        </a4j:commandLink>
                    </h:panelGroup>


                    <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                        <h:panelGroup styleClass="pull-right" layout="block">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza"
                                          value="#{msg_aplic.prt_itens_por_pagina}"/>
                        </h:panelGroup>

                        <h:panelGroup styleClass="cb-container pl20" layout="block">
                            <h:selectOneMenu
                                    value="#{ItemCampanhaControle.paginadorListaAulaCP.limit}"
                                    id="qtdeItensPaginaAulaCP">
                                <f:selectItem itemValue="#{10}"></f:selectItem>
                                <f:selectItem itemValue="#{30}"></f:selectItem>
                                <f:selectItem itemValue="#{50}"></f:selectItem>
                                <f:selectItem itemValue="#{100}"></f:selectItem>
                                <a4j:support event="onchange"
                                             actionListener="#{ItemCampanhaControle.atualizarNumeroItensPagina}"
                                             reRender="tblAulasComPontos, gridPaginadorAulaCP, detalhesAulas">
                                    <f:attribute name="tipo" value="Aula Coletiva"/>
                                </a4j:support>
                            </h:selectOneMenu>

                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGroup>

            </td>
        </tr>
    </table>
</h:panelGrid>
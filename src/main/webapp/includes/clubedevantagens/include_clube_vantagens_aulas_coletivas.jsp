<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>to
  Date: 02/05/2019
  Time: 09:32
  --%>
<%@include file="/includes/imports.jsp" %>
<h:panelGroup id="groupAulas" layout="block" styleClass="panelCampanhas aulasColetivas"
              style="width: 100%; display:none; margin-right: 10px;height: auto;overflow: auto; margin-top: 6px">
    <!--Tabela Aulas com e sem ponto -->
    <h:panelGroup id="panelAulaRelatorio" styleClass="menuLateralPontos" style="float: right;height:auto;width: fit-content;">
        <h:panelGroup id="aulas" layout="block" style="position: relative;width: 100%;">
                <h:panelGroup id="detalhesAulas"  layout="block" styleClass="tudo detalhesEsconder">
                    <h:panelGroup layout="block">
                        <br/>
                        <h:outputText rendered="#{ItemCampanhaControle.mostrarAulasComPonto}"
                                      style="margin-left: 11px;font-size: 17px !important;"
                                      styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                      value="#{msg_aplic.prt_clube_vantagens_itens_pontuacao}"/>

                        <h:outputText rendered="#{!ItemCampanhaControle.mostrarAulasComPonto}"
                                      style="margin-left: 11px; font-size: 17px !important;"
                                      styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                      value="Aulas Ativas n�o Pontuadas"/>

                        <a4j:commandLink id="aulasSemPonto" reRender="aulas"
                                         style="float: right;font-size: 15px;margin-right: 20px;"
                                         rendered="#{!ItemCampanhaControle.mostrarAulasComPonto}"
                                         action="#{ItemCampanhaControle.verificarAulaComPontuacaoESemPontuacao}"
                                         accesskey="2">
                            <f:param name="aulaComPonto" value="true"/>
                            <i class="fa-icon-toggle-off"></i>
                        </a4j:commandLink>
                        <a4j:commandLink id="sulasComPonto" reRender="aulas"
                                         style="float: right;font-size: 15px;margin-right: 20px;"
                                         rendered="#{ItemCampanhaControle.mostrarAulasComPonto}"
                                         action="#{ItemCampanhaControle.verificarAulaComPontuacaoESemPontuacao}"
                                         accesskey="2">
                            <f:param name="aulaComPonto" value="false"/>
                            <i class="fa-icon-toggle-on"></i>
                        </a4j:commandLink>

                        <!--Tabela Aula com pontuacao -->
                        <jsp:include page="include_clube_vantagens_lista_paginada_aulas_cp.jsp" flush="true"/>

                        <!--Tabela Aulas sem pontuacao -->
                        <jsp:include page="include_clube_vantagens_lista_paginada_aulas_sp.jsp" flush="true"/>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
    <!--Pesquisa de Aulas -->
    <h:panelGroup>
        <h:panelGrid columns="2">
            <h:panelGroup id="grupoAula">
                <h:panelGrid columns="1">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  value="#{msg_aplic.prt_campanha_titulo_aula}"/>
                    <h:panelGroup>
                        <h:inputText id="nomeAula" size="30" value="#{ItemCampanhaControle.descricaoTurma}"
                                     maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     style="font-size: 14px !important;"
                                     styleClass="inputTextClean"/>
                        <rich:suggestionbox
                                height="200" width="444"
                                for="nomeAula"
                                style="font-size: 14px !important;"
                                fetchValue="#{result.nome}"
                                suggestionAction="#{ItemCampanhaControle.executarAutocompleteConsultaAula}"
                                minChars="1" rowClasses="20"
                                status="statusHora"
                                nothingLabel="Nenhum Turma encontrado !"
                                var="result" id="suggestionAula"
                                reRender="panelBotoesControle, mensagem">
                            <a4j:support event="onselect"
                                         actionListener="#{ItemCampanhaControle.selecionarAulaSuggestionBox}"
                                         oncomplete="#{ItemCampanhaControle.onComplete}"
                                         reRender="panelBotoesControle, mensagem, grupoAula,grupoAulaDetalhes,grupoAulaPontuacao">
                                <f:attribute name="pequisaTurma" value="#{result}"/>
                            </a4j:support>
                            <h:column>
                                <h:outputText styleClass="texto-font texto-size-14-real"
                                              value="#{result.nome} | #{result.dias} "/>
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGroup id="grupoAulaPontuacao" >
                <h:panelGrid columns="1" style="margin-left: 5%;">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  value="#{msg_aplic.prt_campanha_pontuacao}"/>
                    <h:inputText id="pontosAula" size="4" styleClass="inputTextClean" onkeyup="somenteNumerosInteiros(this);" style="font-size: 14px !important;"
                                 value="#{ItemCampanhaControle.aulaPesquisada.pontosBonus}"/>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGrid>
        <rich:spacer height="30px"/>
        <!--Tabela Detalhes da Turma -->
            <h:panelGroup id="grupoAulaDetalhes" >
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Detalhes da Aula" rendered="#{not empty ItemCampanhaControle.aulaPesquisada.nomeProfessor}"/>

                <h:panelGrid id="aulaDetalhesTabelaProf" style="border: 10px;" columns="2" cellpadding="5px" rendered="#{not empty ItemCampanhaControle.aulaPesquisada.nomeProfessor}">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Professor:"/>
                    <h:outputText id="nomeProf" styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                  value="#{ItemCampanhaControle.aulaPesquisada.nomeProfessor}"/>

                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Ambiente:"/>
                    <h:outputText id="nomeAmbiente" styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                  value="#{ItemCampanhaControle.aulaPesquisada.nomeAmbiente}"/>

                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Dias:"/>
                    <h:outputText id="diasAula" styleClass="texto-font texto-size-14-real texto-cor-cinza"
                                  value="#{ItemCampanhaControle.aulaPesquisada.dias}"/>

                </h:panelGrid>
                        </br>
            </h:panelGroup>
        <rich:spacer height="40px"/>
        <h:panelGroup>
            <a4j:commandLink id="salvarItemTipoAula" reRender="grupoAula, grupoAulaDetalhes, panelAulaRelatorio,grupoAulaPontuacao,form:pontosAula"
                             styleClass="pure-button pure-button-primary"
                             action="#{ItemCampanhaControle.salvarItemCampanhaAula}"
                             oncomplete="#{ItemCampanhaControle.mensagemNotificar};efeitoClick();"
                             accesskey="2">
                Salvar
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
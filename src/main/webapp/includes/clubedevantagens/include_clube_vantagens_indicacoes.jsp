<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>to
  Date: 02/05/2019
  Time: 09:07
  To change this template use File | Settings | File Templates.
--%>
<style>
    .rich-list-picklist-button-content{
        display: block!important;
        color: #FFF!important;
        background-color: #29abe2!important;
        border-radius: 10px!important;
    }

    .rich-list-picklist-button{
        background: none !important;
    }
   .rich-picklist-control-disabled, .rich-picklist-control-top, .rich-picklist-control-bottom, .rich-picklist-control-up, .rich-picklist-control-down, .rich-picklist-control-copyall, .rich-picklist-control-copy, .rich-picklist-control-remove, .rich-picklist-control-removeall{
        border: none!important;
    }

    .rich-list-picklist-button-light{
        border-radius: 10px !important;
        border-color: #074871 !important;
    }

</style>
<%@include file="/includes/imports.jsp"%>
<h:panelGroup layout="block" styleClass="panelCampanhas indicacoes" style="width: 100%; display:none;font-weight: normal; margin-right: 10px;height: auto;overflow: auto;">
    <h:panelGrid columns="1">
        <h:panelGroup  id="groupIndicacoes">
            <rich:spacer height="13px"/>
            <div>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="Pontos por indica��es " />
                <h:inputText  id="campanhaPontosIndicacoes" size="5" style="font-size: small !important;"
                              styleClass="inputTextClean" onkeyup="somenteNumerosInteiros(this);"
                              value="#{ItemCampanhaControle.itemCampanhaIndicacao.pontos}"/>
            </div>
            <rich:spacer height="20px"/>
            <div>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="Pontos por indica��es convertidas " />
                <h:inputText id="campanhaIdicacaoConvertida" size="5" style="font-size: small !important;" onkeyup="somenteNumerosInteiros(this);"
                             styleClass="inputTextClean" value="#{ItemCampanhaControle.itemCampanhaIndicacaoConvertida.pontos}">
                </h:inputText>
            </div>
            <rich:spacer height="20px"/>
            <div>

                <h:panelGrid columns="2">
                    <h:panelGroup>
                        <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="Pontuar por indica��es convertidas de qualquer plano " />
                        <h:panelGroup styleClass="chk-fa-container">
                            <h:selectBooleanCheckbox tabindex="2"
                                value="#{ItemCampanhaControle.empresaSelecionada.aplicarIndicacaoQlqrPlano}">
                                <a4j:support event="onchange" reRender="pnlSelecionarPlanos, campanhaIdicacaoQlqr"/>
                            </h:selectBooleanCheckbox>
                            <span/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGrid>
            </div>
            <rich:spacer height="20px"/>
            <h:panelGrid columns="1" styleClass="form-item" id="pnlSelecionarPlanos" >
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" rendered="#{!ItemCampanhaControle.empresaSelecionada.aplicarIndicacaoQlqrPlano}"
                              value="Somente os Planos adicionados ao campo da direita ser�o considerados como indica��es convertidas: " />
                <rich:spacer height="20px"/>
                <rich:pickList value="#{ItemCampanhaControle.codigoPlanos}"
                               rendered="#{!ItemCampanhaControle.empresaSelecionada.aplicarIndicacaoQlqrPlano}"
                               copyAllControlLabel="ADICIONAR PLANOS" copyControlLabel="ADICIONAR PLANO SELECIONADO"
                               removeAllControlLabel="REMOVER PLANOS" removeControlLabel="REMOVER PLANO SELECIONADO" >
                    <f:selectItems value="#{ItemCampanhaControle.planos}"/>
                </rich:pickList>
            </h:panelGrid>
        </h:panelGroup>
        <rich:spacer height="40px"/>
        </h:panelGrid>
        <h:panelGroup>
            <a4j:commandLink id="salvarItemTipoIndicacoes"
                             styleClass="pure-button pure-button-primary"
                             action="#{ItemCampanhaControle.salvarItemCampanhaIndicacao}"
                             oncomplete="#{ItemCampanhaControle.mensagemNotificar};efeitoClick();"
                             accesskey="2">
                Salvar
            </a4j:commandLink>
        </h:panelGroup>
</h:panelGroup>

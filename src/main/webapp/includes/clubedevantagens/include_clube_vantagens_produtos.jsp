<%--
  Created by IntelliJ IDEA.
  User: Humberto
  Date: 02/05/2019
  Time: 09:40
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/imports.jsp" %>
<h:panelGroup id="grupoProduto" styleClass="panelCampanhas produtos" style="width:100%;display:none;font-weight: normal; margin-right: 10px;height: auto;overflow: auto;">
    <h:panelGrid columns="2" style="width: 100%;">
        <h:panelGroup id="painelBuscaProd" style="position: absolute;top: 206px;">
            <h:panelGrid columns="2" >
                <h:panelGroup >
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_campanha_titulo_produto}" />
                    <br>
                    <h:inputText  id="nomeProduto" value="#{ItemCampanhaControle.produtoPesquisa.descricao}" size="30" maxlength="50" onblur="blurinput(this);"  style="font-size: 14px !important;" onfocus="focusinput(this);" styleClass="inputTextClean" />
                    <rich:suggestionbox
                                height="200"
                                for="nomeProduto"
                                fetchValue="#{pesquisaProduto.descricao}"
                                suggestionAction="#{ItemCampanhaControle.executarAutocompleteConsultaProduto}"
                                minChars="1" rowClasses="20"
                                status="statusHora"
                                nothingLabel="Nenhum Produto encontrado !"
                                var="pesquisaProduto" id="suggestionProduto"
                                reRender="panelBotoesControle, mensagem">
                            <a4j:support event="onselect"
                                         actionListener="#{ItemCampanhaControle.selecionarProdutoSuggestionBox}"
                                         oncomplete="#{ItemCampanhaControle.onComplete}"
                                         reRender="panelBotoesControle, mensagem, panelProdutoRelatorio, painelBuscaProd">
                                <f:attribute name="pesquisaProduto" value="#{pesquisaProduto}"/>
                            </a4j:support>
                            <h:column>
                                <h:outputText styleClass="texto-font texto-size-14-real" value="#{pesquisaProduto.descricao}"/>
                            </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>
                <h:panelGroup style=" width: auto">
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="#{msg_aplic.prt_campanha_pontuacao}" style="margin-left: 5%;" />
                    <br>
                    <h:inputText id="pontosProduto" maxlength="8" size="4" styleClass="inputTextClean" onkeyup="somenteNumerosInteiros(this)" style="margin-left: 5%; font-size: 14px !important;"
                                 value="#{ItemCampanhaControle.produtoPesquisa.pontos}"/>
                </h:panelGroup>

                <a4j:commandLink id="salvarItemTipoProduto" reRender="panelBotoesControle, mensagem, panelProdutoRelatorio, painelBuscaProd"
                                 styleClass="pure-button pure-button-primary"
                                 style="margin-top: 5%; font-size: small !important;"
                                 action="#{ItemCampanhaControle.salvarItemCampanhaProduto}"
                                 oncomplete="#{ItemCampanhaControle.mensagemNotificar};limparNomeProduto();"
                                 accesskey="2">
                    Salvar
                </a4j:commandLink>
            </h:panelGrid>
        </h:panelGroup>


        <h:panelGroup id="panelProdutoRelatorio" styleClass="menuLateralPontos" style="height: auto;width: 460px !important;">
            <h:panelGroup id="produto"  style="position: relative;width: 460px !important;">
                <h:panelGroup id="detalhesProdutos" styleClass="detalhesNegociacao">
                    <h:panelGroup styleClass="tudo detalhesEsconder">
                    <h:panelGroup layout="block">
                        <br/>

                        <h:outputText rendered="#{ItemCampanhaControle.mostrarProdutosComPonto}"
                                      style="margin-left: 11px;"
                                          styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                      value="#{msg_aplic.prt_clube_vantagens_itens_pontuacao}"/>

                        <h:outputText rendered="#{!ItemCampanhaControle.mostrarProdutosComPonto}"
                                      style="margin-left: 11px;"
                                          styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                          value="Produtos Ativos n�o Pontuados"/>

                        <a4j:commandLink id="produtosSemPonto" reRender="produto"
                                         style="float: right;font-size: 15px;margin-right: 20px;"
                                         rendered="#{!ItemCampanhaControle.mostrarProdutosComPonto}"
                                         action="#{ItemCampanhaControle.verificarProdutoComPontuacaoESemPontuacao}"
                                         accesskey="2">
                            <f:param name="produtoComPonto" value="true"/>
                            <i class="fa-icon-toggle-off"></i>
                        </a4j:commandLink>
                        <a4j:commandLink id="produtoComPonto" reRender="produto"
                                         style="float: right;font-size: 15px;margin-right: 20px;"
                                         rendered="#{ItemCampanhaControle.mostrarProdutosComPonto}"
                                         action="#{ItemCampanhaControle.verificarProdutoComPontuacaoESemPontuacao}"
                                         accesskey="2">
                            <f:param name="produtoComPonto" value="false"/>
                            <i class="fa-icon-toggle-on"></i>
                        </a4j:commandLink>
                        <!--Tabela Produtos com ponto -->
                            <jsp:include page="include_clube_vantagens_lista_paginada_produtos_cp.jsp" flush="true"/>

                        <!--Tabela Produto sem ponto -->
                            <jsp:include page="include_clube_vantagens_lista_paginada_produtos_sp.jsp" flush="true"/>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>

            </h:panelGrid>


</h:panelGroup>
<%--
  Created by IntelliJ IDEA.
  User: Humberto
  Date: 21/05/2019
  Time: 17:34
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/imports.jsp" %>
<h:panelGroup id="panelConfigGeral" layout="block" styleClass="panelCampanhas geral"
              style="width: 100%; display:none;font-weight: normal; margin-right: 10px;height: auto;overflow: auto;">
    <h:panelGrid id="gridConfigGeral" columns="1">
        <rich:spacer height="13px"/>

    <h:outputText style="display: block" styleClass="rotuloCampos margenVertical" value="#{msg_aplic.prt_clube_vantagens_zerar_pontuacao_vencido} " />
    <h:panelGroup styleClass="font-size-em-max" >
        <h:panelGroup  styleClass="cb-container margenVertical" layout="block">
            <h:panelGroup>
                <h:selectOneMenu  id="campanhaZerarPontosVencido" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ItemCampanhaControle.zerarPontosAposVencimentoNaEmpresa}" style="font-size: 14px !important;" >
                    <f:selectItems value="#{ItemCampanhaControle.temporalRemocaoPontoEnumToSelectedItens}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
        <rich:spacer height="13px"/>


        <h:panelGrid columns="2">
            <h:panelGroup>
                <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="Pontuar apenas categorias em campanhas ativas " />
                <h:panelGroup styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox tabindex="2"
                                             value="#{ItemCampanhaControle.empresaSelecionada.pontuarApenasCategoriasEmCampanhasAtivas}">
                        <a4j:support event="onchange" reRender="chkPontuarApenasCampanhaAtiva"/>
                    </h:selectBooleanCheckbox>
                    <span/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGrid>
    </h:panelGrid>
    <rich:spacer height="40px"/>
    <h:panelGroup>
        <a4j:commandLink id="salvarItemTipoGeral"
                         reRender="panelBotoesControle, mensagem, gridConfigGeral"
                         styleClass="pure-button pure-button-primary"
                         action="#{ItemCampanhaControle.salvarConfigPontos}"
                         oncomplete="#{ItemCampanhaControle.mensagemNotificar};limparNomePlano();"
                         accesskey="2">
            Salvar
        </a4j:commandLink>
    </h:panelGroup>
</h:panelGroup>
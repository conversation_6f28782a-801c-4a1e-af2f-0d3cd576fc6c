<%--
  Created by IntelliJ IDEA.
  User: Humberto
  Date: 02/05/2019
  Time: 09:07
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/imports.jsp" %>
<h:panelGroup id="groupPlanoz" styleClass="panelCampanhas plano" style="width:100%;display:none;font-weight: normal; margin-right: 10px;height: auto;overflow: auto;">
    <!--Pesquisa de Planos -->
    <h:panelGrid columns="2" id="grupoPlano" style="width: 100%;" >
        <h:panelGroup id="groupEscolhaUmPlano" style="position: absolute;top: 206px;" >
            <h:panelGrid columns="2" style="margin-bottom: 3%;">
                    <h:panelGroup>
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  value="#{msg_aplic.prt_campanha_titulo_plano}"/>
                        <br>
                    <h:inputText id="nomePlano" value="#{ItemCampanhaControle.descricaoPlano}" size="30" maxlength="50" style="font-size: 14px !important;"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="inputTextClean"/>
                        <rich:suggestionbox
                                height="200" width="444"
                                for="nomePlano"
                                fetchValue="#{result.descricao}"
                                suggestionAction="#{ItemCampanhaControle.executarAutocompleteConsultaPlano}"
                                minChars="1" rowClasses="20"
                                status="statusHora"
                                nothingLabel="Nenhum Plano encontrado !"
                                var="result" id="suggestionPlano"
                                reRender="panelBotoesControle, mensagem">
                            <a4j:support event="onselect"
                                     actionListener="#{ItemCampanhaControle.selecionarPlanoSuggestionBox}"
                                         oncomplete="#{ItemCampanhaControle.onComplete}"
                                         reRender="panelBotoesControle, mensagem,grupoPlanoDuracao,grupoPlano,grupoPlanoPontuacao" >
                            <f:attribute name="pequisaPlano" value="#{result}"/>
                            </a4j:support>
                            <h:column>
                                <h:outputText styleClass="texto-font texto-size-14-real" value="#{result.descricao}"/>
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>
                <h:panelGroup >
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" style="margin: 0 5%"
                                  value="#{msg_aplic.prt_campanha_pontuacao}"/>
                    <br>

                    <h:inputText id="pontos" maxlength="8" size="4" styleClass="inputTextClean" style="margin: 0 5%; font-size: 14px !important;"
                                 onkeyup="somenteNumerosInteiros(this)"
                                 value="#{ItemCampanhaControle.planoPesquisado.pontos}"/>
                    </div
            </h:panelGroup>



        </h:panelGrid>
        <!--Tabela Plano Dura��o -->
            <h:panelGroup id="grupoPlanoDuracao" >
                <rich:dataTable id="planoDuracaoTabela"
                                title="#{msg_aplic.prt_campanha_titulo_planoDuracao}"
                                style="width: 10% !important;"
                                styleClass="tabelaSimplesCustom"
                                rendered="#{not empty ItemCampanhaControle.planoPesquisado.planoDuracaoVOs}"
                                value="#{ItemCampanhaControle.planoPesquisado.planoDuracaoVOs}"
                                var="planoDuracao">
                    <rich:column  colspan="2">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" style="font-size: 14px !important;"
                                          value="#{msg_aplic.prt_Duracao_caixa_baixa}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                      value="#{planoDuracao.descricaoDuracao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" style="font-size: 14px !important;"
                                          value="#{msg_aplic.prt_campanha_pontuacao}"/>
                        </f:facet>
                        <h:inputText id="pontos" size="4" styleClass="inputTextClean" style="font-size: 14px !important;"
                                     onkeyup="somenteNumerosInteiros(this)" value="#{planoDuracao.pontos}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <h:panelGroup style="float: left; margin-top: 5%; font-size: small;">
            <a4j:commandLink id="salvarItemTipoPlano"
                                 reRender="panelBotoesControle, mensagem, grupoPlanoDuracao, grupoPlano, grupoPlanoPontuacao,detalhesPlanos,form:panelPlanoRelatorio"
                             styleClass="pure-button pure-button-primary"
                             action="#{ItemCampanhaControle.salvarItemCampanhaPlano}"
                             oncomplete="#{ItemCampanhaControle.mensagemNotificar};limparNomePlano();"
                             accesskey="2">
                Salvar
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>
        <!--Tabela Planos com e sem ponto -->
        <h:panelGroup id="panelPlanoRelatorio" styleClass="menuLateralPontos" style="height: auto;width: 460px !important;" >
            <h:panelGroup id="planos" style="position: relative;">
                <h:panelGroup id="detalhesPlanos" styleClass="detalhesNegociacao">
                    <h:panelGroup styleClass="tudo detalhesEsconder">
                        <h:panelGroup >
                            <br/>
                            <h:outputText rendered="#{ItemCampanhaControle.mostrarPlanosComPonto}"
                                          style="margin-left: 11px;"
                                          styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                          value="#{msg_aplic.prt_clube_vantagens_itens_pontuacao}"/>

                            <h:outputText rendered="#{!ItemCampanhaControle.mostrarPlanosComPonto}"
                                          style="margin-left: 11px;"
                                          styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                          value="Planos Vigentes n�o Pontuados"/>

                            <a4j:commandLink id="planosSemPonto" reRender="planos"
                                             style="float: right;font-size: 15px;margin-right: 11%;"
                                             rendered="#{!ItemCampanhaControle.mostrarPlanosComPonto}"
                                             action="#{ItemCampanhaControle.verificarPlanoComPontuacaoESemPontuacao}"
                                             accesskey="2">
                                <f:param name="planoComPonto" value="true"/>
                                <i class="fa-icon-toggle-off"></i>
                            </a4j:commandLink>
                            <a4j:commandLink id="planosComPonto" reRender="planos"
                                             style="float: right;font-size: 15px;margin-right: 11%;"
                                             rendered="#{ItemCampanhaControle.mostrarPlanosComPonto}"
                                             action="#{ItemCampanhaControle.verificarPlanoComPontuacaoESemPontuacao}"
                                             accesskey="2">
                                <f:param name="planoComPonto" value="false"/>
                                <i class="fa-icon-toggle-on"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <!--Tabela Planos com ponto -->
                        <jsp:include page="include_clube_vantagens_lista_paginada_plano_cp.jsp" flush="true"/>

                        <!--Tabela Planos sem ponto -->
                        <jsp:include page="include_clube_vantagens_lista_paginada_plano_sp.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGrid>
</h:panelGroup>
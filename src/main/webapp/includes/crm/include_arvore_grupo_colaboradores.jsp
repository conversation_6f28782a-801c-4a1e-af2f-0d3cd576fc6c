<%-- 
    Document   : include_arvore_grupo_colaboradores
    Created on : 11/01/2012, 10:44:14
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="../imports.jsp" %>

<style>
    .rich-tree-node-icon {
        display:none;
    }
    .rich-tree-node-icon-leaf{
        display:none;

    }
</style>

<h:panelGrid id="panelArvore" columns="1" columnClasses="tituloCamposMiddle,tituloCamposTop">

    <h:panelGrid columnClasses="tituloCamposMiddle,tituloCamposMiddle,tituloCamposMiddle,tituloCamposMiddle" columns="4" style="text-align:left;">
        <h:panelGrid style="cursor:pointer;" columnClasses="tituloCamposMiddle,tituloCamposMiddle" columns="2">
            <h:panelGrid columns="2" columnClasses="tituloCamposMiddle,tituloCamposMiddle" title="Marcar todos colaboradores">
                <h:graphicImage url="images/checkbox_yes.png" rendered="#{TreeViewControle.marcarTodos}"/>
                <h:graphicImage url="images/checkbox_no.png" rendered="#{!TreeViewControle.marcarTodos}"/>
                <h:outputText value="Todos"/>
                <a4j:support event="onclick" actionListener="#{TreeViewControle.marcarNenhumOuTodosListener}" reRender="panelArvore,totalSelecionado" status="statusInComponent">
                    <f:attribute name="opcao" value="#{true}"/>
                </a4j:support>
            </h:panelGrid>

        </h:panelGrid>
        <h:panelGrid style="cursor:pointer;" columnClasses="tituloCamposMiddle,tituloCamposMiddle" columns="2">
            <h:panelGrid columns="2" columnClasses="tituloCamposMiddle,tituloCamposMiddle" title="Desmarcar todos colaboradores">
                <h:graphicImage url="images/checkbox_no.png" rendered="#{TreeViewControle.marcarTodos}"/>
                <h:graphicImage url="images/checkbox_yes.png" rendered="#{!TreeViewControle.marcarTodos}"/>
                <h:outputText value="Nenhum"/>
                <a4j:support event="onclick" actionListener="#{TreeViewControle.marcarNenhumOuTodosListener}" reRender="panelArvore,totalSelecionado" status="statusInComponent">
                    <f:attribute name="opcao" value="#{false}"/>
                </a4j:support>
            </h:panelGrid>
        </h:panelGrid>
    </h:panelGrid>

    <rich:tree styleClass="tituloCamposMiddle"                                             
               nodeFace="node"
               status="statusInComponent"
               selectedClass="none"
               reRender="panelArvore,totalSelecionado" ajaxSubmitSelection="true" switchType="client"
               value="#{TreeViewControle.treeNode}" var="item" ajaxKeys="#{null}">
        <rich:treeNode nodeClass="#{item.css}" id="tNodeP" type="node">
            <h:panelGroup>
                <h:graphicImage style="vertical-align:middle;" rendered="#{item.marcado && item.objeto != null}" url="images/checkbox_yes.png">
                    <a4j:support status="statusInComponent" reRender="panelArvore,totalSelecionado" actionListener="#{TreeViewControle.processSelection}" event="onclick">
                        <f:attribute name="nodeSelecionado" value="#{item}"/>
                    </a4j:support>
                </h:graphicImage>

                <h:graphicImage style="vertical-align:middle;" rendered="#{!item.marcado && item.objeto != null}" url="images/checkbox_no.png">
                    <a4j:support status="statusInComponent" reRender="panelArvore,totalSelecionado" actionListener="#{TreeViewControle.processSelection}" event="onclick">
                        <f:attribute name="nodeSelecionado" value="#{item}"/>
                    </a4j:support>
                </h:graphicImage>

                <h:outputLabel styleClass="tituloCamposMiddle" value="#{item.descricao}">
                    <a4j:support status="statusInComponent" reRender="panelArvore,totalSelecionado" actionListener="#{TreeViewControle.processSelection}" event="onclick">
                        <f:attribute name="nodeSelecionado" value="#{item}"/>
                    </a4j:support>
                </h:outputLabel>
            </h:panelGroup>


        </rich:treeNode>        
    </rich:tree>
</h:panelGrid>

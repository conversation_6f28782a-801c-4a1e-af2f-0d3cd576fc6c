<%--
  Created by IntelliJ IDEA.
  User: luiz
  Date: 11/12/2015
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>
<h:panelGrid columns="7" width="100%"
             id="biIndicadores"
             style="clear:both; padding-bottom: 10px"
             cellpadding="0"
             cellspacing="0">

    <%--AGENDAMENTO DE LIGAÇÃO PENDENTE--%>
    <h:panelGroup layout="block" styleClass="container-bi-crm">

        <h:panelGroup layout="block" styleClass="bi-crm-box-header">
            <h:panelGroup layout="block" styleClass="col-text-align-left pull-left"
                          style="display: inline-block;margin:0px 5px">

                <h:outputText styleClass="tituloBICRM" value="Agendamento de Ligação Pendente"
                              escape="false"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}bi-agendamento-de-ligacao-pendente-crm/"
                              title="Clique e saiba mais: Agendamento de Ligações Pendentes"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="col-text-align-center" style="padding: 10px">
            <a4j:commandLink
                    id="abrirListaAgendamentoLigacaoPendente"
                    styleClass="indicadoresBICRM"
                    value="#{BusinessIntelligenceCRMControle.indAgendaLigacoesPendentes}"
                    ajaxSingle="true"
                    action="#{BusinessIntelligenceCRMControle.abrirListaAgendamentoLigacaoPendente}"
                    oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                    reRender="modalListaClientesBI"/>
        </h:panelGroup>

    </h:panelGroup>

    <%--INDICAÇÕES SEM CONTATO--%>
    <h:panelGroup layout="block" styleClass="container-bi-crm" id="indIndicacoes">

        <h:panelGroup layout="block" styleClass="bi-crm-box-header">
            <h:panelGroup layout="block" styleClass="col-text-align-left pull-left"
                          style="display: inline-block;margin:0px 5px">

                <h:outputText styleClass="tituloBICRM" value="Indicações Sem Contato"
                              escape="false"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}bi-indicacoes-sem-contato-crm/"
                              title="Clique e saiba mais: Indicações sem Contato"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="col-text-align-center" style="padding: 10px">
            <a4j:commandLink
                    id="abrirListaIndicacoesSemContato"
                    styleClass="indicadoresBICRM"
                    value="#{BusinessIntelligenceCRMControle.indIndicacoesSemContato}"
                    ajaxSingle="true"
                    action="#{BusinessIntelligenceCRMControle.abrirListaIndicacoesSemContato}"
                    oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                    reRender="modalListaClientesBI"/>
        </h:panelGroup>

    </h:panelGroup>

    <%--CONTATO RECEPTIVO--%>
    <h:panelGroup layout="block" styleClass="container-bi-crm" id="indReceptivo">

        <h:panelGroup layout="block" styleClass="bi-crm-box-header">
            <h:panelGroup layout="block" styleClass="col-text-align-left pull-left"
                          style="display: inline-block;margin:0px 5px">

                <h:outputText styleClass="tituloBICRM" value="Contato Receptivo"
                              escape="false"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}bi-contato-receptivo-crm/"
                              title="Clique e saiba mais: Contato Receptivo"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="col-text-align-center" style="padding: 10px">
            <a4j:commandLink
                    id="abrirListaContatoReceptivo"
                    styleClass="indicadoresBICRM"
                    value="#{BusinessIntelligenceCRMControle.indContatoReceptivo}"
                    ajaxSingle="true"
                    action="#{BusinessIntelligenceCRMControle.abrirListaContatoReceptivo}"
                    oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                    reRender="modalListaClientesBI"/>
        </h:panelGroup>

    </h:panelGroup>


    <%-- CLIENTES COM OBJEÇÃO DEFINITIVA --%>
    <h:panelGroup layout="block" styleClass="container-bi-crm" id="indObjecoes">

        <h:panelGroup layout="block" styleClass="bi-crm-box-header">
            <h:panelGroup layout="block" styleClass="col-text-align-left pull-left"
                          style="display: inline-block;margin:0px 5px">

                <h:outputText styleClass="tituloBICRM" value="Clientes Com Objeção Definitiva"
                              escape="false"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlWikiCRM}Operações:Objeções_por_Fases"
                              title="Clique e saiba mais: Clientes Com Objeção Definitiva"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="col-text-align-center" style="padding: 10px">
            <a4j:commandLink
                    id="abrirListaObjecoes"
                    styleClass="indicadoresBICRM"
                    value="#{BusinessIntelligenceCRMControle.indClientesObjecaoDefinitiva}"
                    action="#{BusinessIntelligenceCRMControle.abrirListaClientesObjecoesDefinitiva}"
                    oncomplete="Richfaces.showModalPanel('modalListaObjecoesClientes')"
                    reRender="modalListaObjecoesClientes"
                    ajaxSingle="true"/>
        </h:panelGroup>

    </h:panelGroup>

</h:panelGrid>
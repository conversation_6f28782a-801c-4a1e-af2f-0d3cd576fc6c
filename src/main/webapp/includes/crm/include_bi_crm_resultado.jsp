<%--
  Created by IntelliJ IDEA.
  User: luiz
  Date: 11/12/2015
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../imports.jsp" %>

<script type="text/javascript">
    jQuery.noConflict();

    function mostrarBackResultado() {
        jQuery('.cardResultado').addClass('flippedResultado');
    }
</script>

<style type="text/css">

    .flipResultado {
        -webkit-perspective: 800;
        -ms-perspective: 800;
        -moz-perspective: 800;
        -o-perspective: 800;
        height: 310px;
        position: relative;
    }

    .flipResultado .cardResultado.flippedResultado {
        transform: rotatex(-180deg);
        -ms-transform: rotatex(-180deg); /* IE 9 */
        -moz-transform: rotatex(-180deg); /* Firefox */
        -webkit-transform: rotatex(-180deg); /* Safari and Chrome */
        -o-transform: rotatex(-180deg); /* Opera */
    }

    .flipResultado .cardResultado {
        padding: 0;
        border: none !important;
        width: 100%;
        height: 100%;
        -webkit-transform-style: preserve-3d;
        -webkit-transition: 0.5s;
        -moz-transform-style: preserve-3d;
        -moz-transition: 0.5s;
        -ms-transform-style: preserve-3d;
        -ms-transition: 0.5s;
        -o-transform-style: preserve-3d;
        -o-transition: 0.5s;
        transform-style: preserve-3d;
        transition: 0.5s;
    }

    .flipResultado .cardResultado .faceResultado {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 2;
        -webkit-backface-visibility: hidden; /* Safari & Chrome */
        -moz-backface-visibility: hidden; /* Firefox */
        -ms-backface-visibility: hidden; /* Internet Explorer */
        -o-backface-visibility: hidden; /* Opera */
        border-radius: 8px;
    }

    .flipResultado .cardResultado .frontResultado {
        background: white;
        position: absolute;
        z-index: 1;
    }

    .flipResultado .cardResultado .backResultado {
        background: white;
        transform: rotatex(-180deg);
        -ms-transform: rotatex(-180deg); /* IE 9 */
        -moz-transform: rotatex(-180deg); /* Firefox */
        -webkit-transform: rotatex(-180deg); /* Safari and Chrome */
        -o-transform: rotatex(-180deg); /* Opera */
    }

</style>


<h:panelGroup layout="block"
              id="biResultado"
              styleClass="container-bi-crm">

    <h:panelGroup layout="block" styleClass="bi-crm-box-header">

        <h:panelGroup layout="block" styleClass="col-text-align-left pull-left"
                      style="display: inline-block;">
            <h:outputText id="tituloBICRMResultado" styleClass="tituloBICRM" value="Resultado"/>
            <h:outputLink value="#{SuperControle.urlBaseConhecimento}bi-resultado-crm/"
                          styleClass="linkWiki" title="Clique e saiba mais: BI - Resultados"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <%--LISTA SELECIONADO--%>
        <h:panelGroup layout="block"
                      rendered="#{!BusinessIntelligenceCRMControle.graficoResultado}">
            <%--BOT�O SELECIONAR GRAFICO--%>
            <a4j:commandLink
                    id="resultadoEmGrafico"
                    status="false"
                    styleClass="linkPadrao padding-5 "
                    style="margin:5px;color: #444;"
                    action="#{BusinessIntelligenceCRMControle.apresentarGraficoResultado}"
                    reRender="biResultado">
                <h:outputText styleClass="fa-icon-line-chart linkPadrao texto-size-16-real "/>
            </a4j:commandLink>

            <%--ICONE DE LISTA SELECIONADO--%>
            <h:outputText styleClass="fa-icon-table linkPadrao texto-size-16-real crm-toggle-view"/>


        </h:panelGroup>
        <%--GRAFICO SELECIONADO --%>
        <h:panelGroup layout="block"
                      rendered="#{BusinessIntelligenceCRMControle.graficoResultado}">


            <%--ICONE DE GRAFICO SELECIONADO--%>
            <h:outputText styleClass="fa-icon-line-chart linkPadrao texto-size-16-real crm-toggle-view"/>

            <%--BOT�O SELECIONAR LISTA--%>
            <a4j:commandLink
                    id="resultadoEmLista"
                    status="false"
                    styleClass="linkPadrao padding-5 "
                    style="margin:5px;color: #444;"
                    action="#{BusinessIntelligenceCRMControle.apresentarGraficoResultado}"
                    reRender="biResultado">
                <h:outputText styleClass="fa-icon-table linkPadrao texto-size-16-real"/>
            </a4j:commandLink>

        </h:panelGroup>
    </h:panelGroup>


    <h:panelGroup layout="block" styleClass="bi-crm-box-detail">
        <h:panelGroup layout="block" styleClass="flipResultado"
                      rendered="#{BusinessIntelligenceCRMControle.graficoResultado}">
            <h:panelGroup layout="block" styleClass="cardResultado">
                <h:panelGroup layout="block" styleClass="faceResultado frontResultado">
                    <script type="text/javascript">
                        function carregarGraficoCRMResultado() {
                            var chart = AmCharts.makeChart("chartdivResultado", {
                                "type": "serial",
                                "theme": "light",
                                "dataProvider": ${BusinessIntelligenceCRMControle.jsonBIResultado},
                                "valueAxes": [{
                                    "position": "left"
                                }],
                                "startDuration": 1,
                                "legend": {
                                    "autoMargins": true,
                                    "equalWidths": false,
                                    "horizontalGap": 10,
                                    "markerSize": 10,
                                    "useGraphSettings": true,
                                    "valueAlign": "left",
                                    "valueWidth": 0
                                },
                                "graphs": [{
                                    "balloonText": "[[title]]: <b>[[value]]</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "lineColor": "#ece42d",
                                    "type": "column",
                                    "columnWidth": 0.7,
                                    "valueField": "meta",
                                    "title": "Meta"
                                }, {
                                    "balloonText": "[[title]]: <b>[[value]]</b> <br> <b>[[porcentagem]]%</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "type": "column",
                                    "clustered": false,
                                    "lineColor": "#3aec97",
                                    "columnWidth": 0.6,
                                    "valueField": "metaatingida",
                                    "title": "Atingida"
                                }],
                                "chartCursor": {
                                    "categoryBalloonEnabled": false,
                                    "cursorAlpha": 0,
                                    "zoomable": false
                                },
                                "plotAreaFillAlphas": 0.1,
                                "categoryField": "fase",
                                "categoryAxis": {
                                    "gridPosition": "start",
                                    "labelRotation": 45
                                },
                                "export": {
                                    "enabled": true
                                }

                            });

                            chart.addListener("clickGraphItem", function resultado(event) {
                                var meta = event.item.category;
                                var metabuscar = document.getElementById('form:metaBuscarDetalhadaResultado');
                                metabuscar.value = meta;
                                document.getElementById('form:montarGraficoDetalhadoResultado').click();
                            });

                        }
                    </script>

                    <div id="chartdivResultado"
                         style="height: 350px;"></div>
                    <script>
                        carregarGraficoCRMResultado();
                    </script>
                </h:panelGroup>


                <h:panelGroup layout="block" styleClass="faceResultado backResultado">
                    <script type="text/javascript">
                        function carregarGraficoCRMDetalhadoResultado() {
                            var chart = AmCharts.makeChart("chartdivDetalhadoResultado", {
                                "type": "serial",
                                "theme": "light",
                                "titles": [{
                                    "text": "${BusinessIntelligenceCRMControle.metaBuscarDetalhadaResultado}",
                                    "size": 12
                                }],
                                "dataProvider": ${BusinessIntelligenceCRMControle.jsonDetalhadoResultado},
                                "valueAxes": [{
                                    "position": "left"
                                }],
                                "startDuration": 1,
                                "legend": {
                                    "autoMargins": true,
                                    "equalWidths": false,
                                    "horizontalGap": 10,
                                    "markerSize": 10,
                                    "useGraphSettings": true,
                                    "valueAlign": "left",
                                    "valueWidth": 0
                                },
                                "graphs": [{
                                    "balloonText": "[[title]]: <b>[[value]]</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "lineColor": "#ece42d",
                                    "columnWidth": 0.7,
                                    "type": "column",
                                    "valueField": "meta",
                                    "title": "Meta"
                                }, {
                                    "balloonText": "[[title]]: <b>[[value]]</b> <br> <b>[[porcentagem]]%</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "type": "column",
                                    "clustered": false,
                                    "lineColor": "#3aec97",
                                    "columnWidth": 0.6,
                                    "valueField": "metaatingida",
                                    "title": "Atingida"
                                }],
                                "chartCursor": {
                                    "categoryBalloonEnabled": false,
                                    "cursorAlpha": 0,
                                    "zoomable": false
                                },
                                "plotAreaFillAlphas": 0.1,
                                "categoryField": "usuario",
                                "categoryAxis": {
                                    "gridPosition": "start",
                                    "labelRotation": 45
                                },
                                "export": {
                                    "enabled": true
                                }

                            });

                            chart.addListener("clickGraphItem", function mostrarFrontResultado() {
                                jQuery('.cardResultado').removeClass('flippedResultado');
                            });

                        }
                    </script>

                    <div id="chartdivDetalhadoResultado"
                         style="height: 350px;"></div>
                    <script>
                        carregarGraficoCRMDetalhadoResultado();
                    </script>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>


        <%--BI EM FORMA DE LISTA--%>
        <h:dataTable id="itemsFecharMetaResultado"
                     rendered="#{!BusinessIntelligenceCRMControle.graficoResultado}"
                     value="#{BusinessIntelligenceCRMControle.totaisResultado}"
                     var="fecharMeta"
                     styleClass="font-size-em classLinhasCRM"
                     rowClasses="linhasBICRM">

            <%--IDENTIFICADOR DE METAS--%>
            <rich:column style="text-align: left">

                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorBICRMSemHover marginLeftCRMBI"
                                  style="float: left;"
                                  value="#{msg_aplic.prt_BICRM_fase}"/>
                </f:facet>

                <h:outputText id="faseMetaResultado"
                              style="float: left;"
                              styleClass="camposBICRMMetasSemHover marginLeftCRMBI"
                              value="#{fecharMeta.identificadorMeta_Apresentar}"/>

            </rich:column>

            <%--METAS--%>
            <rich:column style="width: 18%;">

                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorSuperiorBICRM"
                                  style="text-align: right"
                                  value="#{msg_aplic.prt_FechamentoDia_meta}"/>
                </f:facet>

                <a4j:commandLink
                        id="abrirListaBIResultado"
                        styleClass="camposBICRMMetas"
                        title="A meta do BI de resultados � originada a partir da soma do resultado atingido e repescagem do BI metas de vendas"
                        value="#{fecharMeta.meta_Apresentar}"
                        ajaxSingle="true"
                        action="#{BusinessIntelligenceCRMControle.abrirListaBIResultado}"
                        oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                        reRender="modalListaClientesBI"/>

            </rich:column>

            <%--META ATINGIDA--%>
            <rich:column style="width: 18%;">

                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorSuperiorBICRM"
                                  value="#{msg_aplic.prt_BICRM_atingida}"/>
                </f:facet>

                <a4j:commandLink
                        id="abrirListaBIResultadoMetaAtingida"
                        styleClass="camposBICRMMetas"
                        value="#{fecharMeta.metaAtingida_Apresentar}"
                        ajaxSingle="true"
                        action="#{BusinessIntelligenceCRMControle.abrirListaBIResultadoMetaAtingida}"
                        oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                        reRender="modalListaClientesBI"/>

            </rich:column>

            <%--PORCENTAGEM %--%>
            <rich:column style="width: 18%;">
                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorSuperiorBICRM marginRightCRMBI"
                                  value="%"/>
                </f:facet>

                <h:outputText style="#{fecharMeta.corPorcentagemBox}"
                              value="#{fecharMeta.porcentagem_ApresentarBI}"
                              id="porcentagem"
                              styleClass="camposBICRMMetasSemHover marginRightCRMBI"/>
            </rich:column>

        </h:dataTable>
    </h:panelGroup>
</h:panelGroup>

<h:inputHidden id="metaBuscarDetalhadaResultado"
               value="#{BusinessIntelligenceCRMControle.metaBuscarDetalhadaResultado}"/>

<a4j:commandLink id="montarGraficoDetalhadoResultado"
                 style="visibility: hidden;"
                 status="false"
                 action="#{BusinessIntelligenceCRMControle.montarGraficoDetalhadoResultado}"
                 oncomplete="mostrarBackResultado();"
                 reRender="biResultado"/>
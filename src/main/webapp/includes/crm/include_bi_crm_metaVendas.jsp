<%--
  Created by IntelliJ IDEA.
  User: luiz
  Date: 11/12/2015
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../imports.jsp" %>
<script type="text/javascript" language="javascript" src="../../bootstrap/jquery.js"></script>
<script type="text/javascript" src="../../script/tooltipster/jquery.tooltipster.min.js"></script>

<script type="text/javascript">
    jQuery.noConflict();

    function mostrarBackVendas() {
        jQuery('.cardVendas').addClass('flippedVendas');
    }
</script>

<style type="text/css">

    .flipVendas {
        -webkit-perspective: 800;
        -ms-perspective: 800;
        -moz-perspective: 800;
        -o-perspective: 800;
        height: 310px;
        position: relative;
    }

    .flipVendas .cardVendas.flippedVendas {
        transform: rotatex(-180deg);
        -ms-transform: rotatex(-180deg); /* IE 9 */
        -moz-transform: rotatex(-180deg); /* Firefox */
        -webkit-transform: rotatex(-180deg); /* Safari and Chrome */
        -o-transform: rotatex(-180deg); /* Opera */
    }

    .flipVendas .cardVendas {
        padding: 0;
        border: none !important;
        width: 100%;
        height: 100%;
        -webkit-transform-style: preserve-3d;
        -webkit-transition: 0.5s;
        -moz-transform-style: preserve-3d;
        -moz-transition: 0.5s;
        -ms-transform-style: preserve-3d;
        -ms-transition: 0.5s;
        -o-transform-style: preserve-3d;
        -o-transition: 0.5s;
        transform-style: preserve-3d;
        transition: 0.5s;
    }

    .flipVendas .cardVendas .faceVendas {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 2;
        -webkit-backface-visibility: hidden; /* Safari & Chrome */
        -moz-backface-visibility: hidden; /* Firefox */
        -ms-backface-visibility: hidden; /* Internet Explorer */
        -o-backface-visibility: hidden; /* Opera */
        border-radius: 8px;
    }

    .flipVendas .cardVendas .frontVendas {
        background: white;
        position: absolute;
        z-index: 1;
    }

    .flipVendas .cardVendas .backVendas {
        background: white;
        transform: rotatex(-180deg);
        -ms-transform: rotatex(-180deg); /* IE 9 */
        -moz-transform: rotatex(-180deg); /* Firefox */
        -webkit-transform: rotatex(-180deg); /* Safari and Chrome */
        -o-transform: rotatex(-180deg); /* Opera */
    }

</style>

<h:panelGroup layout="block" id="biVendas" styleClass="container-bi-crm">

    <h:panelGroup layout="block" styleClass="bi-crm-box-header">
        <h:panelGroup layout="block" styleClass="col-text-align-left pull-left"
                      style="display: inline-block;">
            <h:outputText id="tituloBICRM" styleClass="tituloBICRM" value="Metas de Vendas"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}bi-metas-de-vendas-_-crm/"
                          title="Clique e saiba mais: BI - Metas de Vendas"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>


        <%--LISTA SELECIONADO--%>
        <h:panelGroup layout="block"
                      rendered="#{!BusinessIntelligenceCRMControle.graficoVendas}"
                      style="display: inline-block">


            <%--BOT�O SELECIONAR GRAFICO--%>
            <a4j:commandLink
                    id="vendasEmGrafico"
                    status="false"
                    style="margin:5px;color: #444;"
                    styleClass="linkPadrao  padding-5 "
                    action="#{BusinessIntelligenceCRMControle.apresentarGraficoVendas}"
                    reRender="biVendas">
                <h:outputText styleClass="fa-icon-line-chart linkPadrao texto-size-16-real "/>
            </a4j:commandLink>
            <%--ICONE DE LISTA SELECIONADO--%>
            <h:outputText styleClass="fa-icon-table linkPadrao texto-size-16-real crm-toggle-view"/>
        </h:panelGroup>

        <%--GRAFICO SELECIONADO --%>
        <h:panelGroup layout="block"
                      rendered="#{BusinessIntelligenceCRMControle.graficoVendas}"
                      style="display: inline-block">


            <%--ICONE DE GRAFICO SELECIONADO--%>
            <h:outputText styleClass="fa-icon-line-chart linkPadrao texto-size-16-real crm-toggle-view"/>

            <%--BOT�O SELECIONAR LISTA--%>
            <a4j:commandLink
                    id="vendasEmLista"
                    status="false"
                    styleClass="linkPadrao padding-5 "
                    style="margin:5px;color: #444;"
                    action="#{BusinessIntelligenceCRMControle.apresentarGraficoVendas}"
                    reRender="biVendas">
                <h:outputText styleClass="fa-icon-table  linkPadrao texto-size-16-real "/>
            </a4j:commandLink>


        </h:panelGroup>

    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="bi-crm-box-detail">
        <h:panelGroup layout="block" styleClass="flipVendas"
                      rendered="#{BusinessIntelligenceCRMControle.graficoVendas}">
            <h:panelGroup layout="block" styleClass="cardVendas">
                <h:panelGroup layout="block" styleClass="faceVendas frontVendas">
                    <script type="text/javascript">
                        function carregarGraficoCRMVendas() {
                            var chart = AmCharts.makeChart("chartdivVendas", {
                                "type": "serial",
                                "theme": "light",
                                "dataProvider": ${BusinessIntelligenceCRMControle.jsonBIMetaVendas},
                                "valueAxes": [{
                                    "position": "left",
                                }],
                                "startDuration": 1,
                                "legend": {
                                    "autoMargins": true,
                                    "equalWidths": false,
                                    "horizontalGap": 10,
                                    "markerSize": 10,
                                    "useGraphSettings": true,
                                    "valueAlign": "left",
                                    "valueWidth": 0
                                },
                                "graphs": [{
                                    "balloonText": "[[title]]: <b>[[value]]</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "lineColor": "#ece42d",
                                    "columnWidth": 0.7,
                                    "type": "column",
                                    "valueField": "meta",
                                    "title": "Meta"
                                }, {
                                    "balloonText": "[[title]]: <b>[[value]]</b> <br> <b>[[porcentagem]]%</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "type": "column",
                                    "clustered": false,
                                    "lineColor": "#3aec97",
                                    "columnWidth": 0.6,
                                    "valueField": "metaatingida",
                                    "title": "Atingida"
                                }, {
                                    "balloonText": "[[title]]: <b>[[value]]</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "type": "column",
                                    "clustered": false,
                                    "lineColor": "#000000",
                                    "columnWidth": 0.5,
                                    "valueField": "repescagem",
                                    "title": "Repescagem"
                                }],
                                "chartCursor": {
                                    "categoryBalloonEnabled": false,
                                    "cursorAlpha": 0,
                                    "zoomable": false
                                },
                                "plotAreaFillAlphas": 0.1,
                                "categoryField": "fase",
                                "categoryAxis": {
                                    "gridPosition": "start",
                                    "labelRotation": 45
                                },
                                "export": {
                                    "enabled": true
                                }

                            });

                            chart.addListener("clickGraphItem", function vendas(event) {
                                var meta = event.item.category;
                                var metabuscar = document.getElementById('form:metaBuscarDetalhadaVendas');
                                metabuscar.value = meta;
                                document.getElementById('form:montarGraficoDetalhadoVendas').click();
                            });

                        }
                    </script>

                    <div id="chartdivVendas"
                         style="height: 350px;"></div>
                    <script>
                        carregarGraficoCRMVendas();
                    </script>
                </h:panelGroup>


                <h:panelGroup layout="block" styleClass="faceVendas backVendas">
                    <script type="text/javascript">
                        function carregarGraficoCRMDetalhadoVendas() {
                            var chart = AmCharts.makeChart("chartdivDetalhadoVendas", {
                                "type": "serial",
                                "theme": "light",
                                "titles": [{
                                    "text": "${BusinessIntelligenceCRMControle.metaBuscarDetalhadaVendas}",
                                    "size": 12
                                }],
                                "dataProvider": ${BusinessIntelligenceCRMControle.jsonDetalhadoVendas},
                                "valueAxes": [{
                                    "position": "left"
                                }],
                                "startDuration": 1,
                                "legend": {
                                    "autoMargins": true,
                                    "equalWidths": false,
                                    "horizontalGap": 10,
                                    "markerSize": 10,
                                    "useGraphSettings": true,
                                    "valueAlign": "left",
                                    "valueWidth": 0
                                },
                                "graphs": [{
                                    "balloonText": "[[title]]: <b>[[value]]</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "lineColor": "#ece42d",
                                    "columnWidth": 0.7,
                                    "type": "column",
                                    "valueField": "meta",
                                    "title": "Meta"
                                }, {
                                    "balloonText": "[[title]]: <b>[[value]]</b> <br> <b>[[porcentagem]]%</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "type": "column",
                                    "clustered": false,
                                    "lineColor": "#3aec97",
                                    "columnWidth": 0.6,
                                    "valueField": "metaatingida",
                                    "title": "Atingida"
                                }, {
                                    "balloonText": "[[title]]: <b>[[value]]</b>",
                                    "fillAlphas": 0.9,
                                    "lineAlpha": 0.2,
                                    "type": "column",
                                    "clustered": false,
                                    "lineColor": "#000000",
                                    "columnWidth": 0.5,
                                    "valueField": "repescagem",
                                    "title": "Repescagem"
                                }],
                                "chartCursor": {
                                    "categoryBalloonEnabled": false,
                                    "cursorAlpha": 0,
                                    "zoomable": false
                                },
                                "plotAreaFillAlphas": 0.1,
                                "categoryField": "usuario",
                                "categoryAxis": {
                                    "gridPosition": "start",
                                    "labelRotation": 45
                                },
                                "export": {
                                    "enabled": true
                                }

                            });

                            chart.addListener("clickGraphItem", function mostrarFrontVendas() {
                                jQuery('.cardVendas').removeClass('flippedVendas');
                            });

                        }
                    </script>

                    <div id="chartdivDetalhadoVendas"
                         style="height: 350px;"></div>
                    <script>
                        carregarGraficoCRMDetalhadoVendas();
                    </script>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <%--BI EM FORMA DE LISTA--%>
        <h:dataTable
                id="itemsFecharMetaRenovacao"
                rendered="#{!BusinessIntelligenceCRMControle.graficoVendas}"
                value="#{BusinessIntelligenceCRMControle.fasesRenovacao}"
                var="fecharMeta"
                styleClass="font-size-em classLinhasCRM"
                rowClasses="linhasBICRM">

            <%--IDENTIFICADOR DE METAS--%>
            <rich:column style="text-align: left">

                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorBICRMSemHover marginLeftCRMBI"
                                  style="float: left;"
                                  value="#{msg_aplic.prt_BICRM_fase}"/>
                </f:facet>

                <h:outputText id="faseMeta"
                              style="float: left;"
                              styleClass="camposBICRMMetasSemHover marginLeftCRMBI"
                              value="#{fecharMeta.identificadorMeta_Apresentar}"/>

                <f:facet name="footer">
                    <h:outputText id="totalMeta"
                                  style="float: left;"
                                  styleClass="camposTotalizadorSuperiorBICRM marginLeftCRMBI"
                                  value="Total"/>
                </f:facet>

            </rich:column>

            <%--METAS--%>
            <rich:column style="width: 18%;">

                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorSuperiorBICRM"
                                  style="text-align: right"
                                  value="#{msg_aplic.prt_FechamentoDia_meta}"/>
                </f:facet>

                <a4j:commandLink
                        id="abrirListaMeta"
                        styleClass="camposBICRMMetas"
                        rendered="#{!fecharMeta.faseMetaIndicados}"
                        value="#{fecharMeta.meta_Apresentar}"
                        ajaxSingle="true"
                        action="#{BusinessIntelligenceCRMControle.abrirListaMeta}"
                        oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                        reRender="modalListaClientesBI">
                    <f:setPropertyActionListener value="false" target="#{BusinessIntelligenceCRMControle.metaExtra}"/>
                </a4j:commandLink>

                <h:outputText styleClass="tooltipster"
                              title="Esse n�mero � um c�lculo de uma meta previamente configurada. F�rmula: Qtd. de colaboradores x Meta por colaborador configurada"
                              id="abrirListaMetaIndicados"
                              rendered="#{fecharMeta.faseMetaIndicados}"
                              style="font-family: Arial; text-align: right; color: #777 !important; font-size: 14px;"
                              value="#{fecharMeta.meta_Apresentar}"/>

                <f:facet name="footer">
                    <%--ABRIR META TOTAL--%>
                    <a4j:commandLink
                            id="abrirListaMetaTotalVendas"
                            styleClass="camposTotalizadorBICRM"
                            value="#{BusinessIntelligenceCRMControle.metaRenovacao.meta_Apresentar}"
                            ajaxSingle="true"
                            action="#{BusinessIntelligenceCRMControle.abrirListaMetaTotalVendas}"
                            oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                            reRender="modalListaClientesBI"/>
                </f:facet>

            </rich:column>

            <%--META ATINGIDA--%>
            <rich:column style="width: 18%;">

                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorSuperiorBICRM"
                                  value="#{msg_aplic.prt_BICRM_atingida}"/>
                </f:facet>

                <a4j:commandLink
                        id="abrirListaMetaAtingidaVendas"
                        styleClass="camposBICRMMetas"
                        value="#{fecharMeta.metaAtingida_Apresentar}"
                        ajaxSingle="true"
                        action="#{BusinessIntelligenceCRMControle.abrirListaMetaAtingida}"
                        oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                        reRender="modalListaClientesBI">
                    <f:setPropertyActionListener value="false" target="#{BusinessIntelligenceCRMControle.metaExtra}"/>
                </a4j:commandLink>

                <f:facet name="footer">
                    <%--ABRIR META ATINGIDA TOTAL--%>
                    <a4j:commandLink
                            id="abrirListaMetaAtingidaTotalVendas"
                            styleClass="camposTotalizadorBICRM"
                            value="#{BusinessIntelligenceCRMControle.metaRenovacao.metaAtingida_Apresentar}"
                            ajaxSingle="true"
                            action="#{BusinessIntelligenceCRMControle.abrirListaMetaAtingidaTotalVendas}"
                            oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                            reRender="modalListaClientesBI"/>
                </f:facet>

            </rich:column>

            <%--REPESCAGEM--%>
            <rich:column style="width: 18%;">

                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorSuperiorBICRM"
                                  value="#{msg_aplic.prt_FechamentoDia_repescagem}"/>
                </f:facet>

                <a4j:commandLink
                        id="abrirListaMetaRepescagem"
                        styleClass="camposBICRMMetas"
                        value="#{fecharMeta.repescagem_Apresentar}"
                        ajaxSingle="true"
                        action="#{BusinessIntelligenceCRMControle.abrirListaMetaRepescagem}"
                        oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                        reRender="modalListaClientesBI"/>

                <f:facet name="footer">
                    <%--ABRIR META REPESCAGEM TOTAL--%>
                    <a4j:commandLink
                            id="abrirListaMetaRepescagemTotalVendas"
                            styleClass="camposTotalizadorBICRM"
                            value="#{BusinessIntelligenceCRMControle.metaRenovacao.repescagem_Apresentar}"
                            ajaxSingle="true"
                            action="#{BusinessIntelligenceCRMControle.abrirListaMetaRepescagemTotalVendas}"
                            oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                            reRender="modalListaClientesBI"/>
                </f:facet>

            </rich:column>

            <%--PORCENTAGEM %--%>
            <rich:column style="width: 18%;">
                <f:facet name="header">
                    <h:outputText styleClass="camposTotalizadorSuperiorBICRM marginRightCRMBI"
                                  value="%"/>
                </f:facet>

                <h:outputText style="#{fecharMeta.corPorcentagemBox}"
                              value="#{fecharMeta.porcentagem_ApresentarBI}"
                              id="porcentagem"
                              styleClass="camposBICRMMetasSemHover marginRightCRMBI"/>

                <f:facet name="footer">
                    <h:outputText
                            id="porcentagemTotalVendas"
                            styleClass="camposTotalizadorPorcentaemBICRM marginRightCRMBI"
                            style="#{BusinessIntelligenceCRMControle.metaRenovacao.corPorcentagemBox}"
                            value="#{BusinessIntelligenceCRMControle.metaRenovacao.porcentagem_ApresentarBI}"/>
                </f:facet>
            </rich:column>

        </h:dataTable>

        <rich:toolTip  direction="bottom-left"
                       for="porcentagemTotalVendas"
                       value="Indica��es n�o entra no total das metas"/>

        <rich:toolTip  direction="bottom-left"
                       for="abrirListaMetaRepescagemTotalVendas"
                       value="Indica��es n�o entra no total das metas"/>

        <rich:toolTip  direction="bottom-left"
                       for="abrirListaMetaAtingidaTotalVendas"
                       value="Indica��es n�o entra no total das metas"/>

        <rich:toolTip  direction="bottom-left"
                       for="abrirListaMetaTotalVendas"
                       value="Indica��es n�o entra no total das metas"/>

        <rich:toolTip  direction="bottom-right"
                       for="totalMeta"
                       value="Indica��es n�o entra no total das metas"/>

    </h:panelGroup>
</h:panelGroup>

<h:inputHidden id="metaBuscarDetalhadaVendas" value="#{BusinessIntelligenceCRMControle.metaBuscarDetalhadaVendas}"/>

<a4j:commandLink id="montarGraficoDetalhadoVendas"
                 style="visibility: hidden;"
                 action="#{BusinessIntelligenceCRMControle.montarGraficoDetalhadoVendas}"
                 status="false"
                 oncomplete="mostrarBackVendas();"
                 reRender="biVendas"/>

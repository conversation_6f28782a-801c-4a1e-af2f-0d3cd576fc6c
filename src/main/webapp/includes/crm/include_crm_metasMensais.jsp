<!-- MODAL DA EXIBI��O DAS METAS NO MES  -->
<%@include file="../imports.jsp" %>

<rich:modalPanel id="panelMetas" autosized="true" shadowOpacity="true" width="450" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Metas Mensais"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkpanelMetas" />
            <rich:componentControl for="panelMetas" attachTo="hidelinkpanelMetas" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <h:form id="formMetas">
        <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
            <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="Metas Mensais - #{PendenciasCRMControle.exibicaoMetasTotal}" />
            </h:panelGrid>

            <!-- ---------------- PAINEL CABE�ALHO -------------------------------  -->
            <h:panelGrid columns="6" footerClass="colunaCentralizada" width="100%">
            </h:panelGrid>

            <!-- ---------------- TABELA dias metas-------------------------------  -->
            <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{PendenciasCRMControle.listaExibicaoMetas}" rows="15" var="fechamentoDia">

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_FechamentoDia_dia}" />
                    </f:facet>
                    <h:outputText id="dia" value="#{fechamentoDia.dia_Apresentar}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_FechamentoDia_colaboradorResponsavel}" />
                    </f:facet>
                    <h:outputText id="colaboradorResponsavel" value="#{fechamentoDia.colaboradorResponsavel.nome}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_FechamentoDia_responsavelCadstroColuna}" />
                    </f:facet>
                    <h:outputText id="responsavelCadastro" value="#{fechamentoDia.responsavelCadastro.nome}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_FechamentoDia_metaEmAberto}" />
                    </f:facet>
                    <h:outputText id="metaEmAberto" rendered="#{fechamentoDia.codigo != 0}" value="#{fechamentoDia.metaEmAberto_Apresentar}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Indicado_opcoes}" />
                    </f:facet>


                    <a4j:commandLink rendered="#{fechamentoDia.codigo != null}" id="verMeta" value="Ver Meta"
                                     action="#{AberturaMetaControle.selecionarAberturaDia}"
                                     reRender="formAberturaMeta, panelGridPrincipal, panelIndicador"
                                     oncomplete="Richfaces.hideModalPanel('panelMetas'); #{AberturaMetaControle.mostrarTelaFormularioFechamentoDia}"/> &nbsp;
                    <!-- ---------------- fechar meta pro dia -------------------------------  -->
                    <a4j:commandLink rendered="#{fechamentoDia.codigo != null && fechamentoDia.metaEmAberto}" id="fecharMeta" value="Fechar Meta"
                                     action="#{AberturaMetaControle.selecionarFechamentoAberturaDia}"
                                     reRender="formFecharAberturaMetaCons, panelGridPrincipal, panelIndicador"
                                     oncomplete="Richfaces.hideModalPanel('panelMetas');#{AberturaMetaControle.mostrarTelaFormularioFechamentoDiaGrava}" />
                    <!-- ---------------- abrir meta pro dia -------------------------------  -->
                    <a4j:commandLink rendered="#{fechamentoDia.codigo == null}" id="abrirMeta" value="Abrir Meta"
                                     actionListener="#{AberturaMetaControle.aberturaListener}"
                                     reRender="panelGridPrincipal, formAberturaMeta"
                                     oncomplete="Richfaces.hideModalPanel('panelMetas'); Richfaces.showModalPanel('panelAberturaMeta');">
                        <f:attribute value="#{fechamentoDia.dia}" name="dia"/>
                    </a4j:commandLink>
                </h:column>
            </h:dataTable>
            <rich:datascroller align="center" for="formMetas:items" maxPages="5" id="scResultadoMetas" />

            <h:panelGrid id="panelGridMensagens" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText value=" " />
                    </h:panelGrid>
                    <h:commandButton rendered="#{PendenciasCRMControle.sucesso}" image="./imagensCRM/sucesso.png" />
                    <h:commandButton rendered="#{PendenciasCRMControle.erro}" image="./imagensCRM/erro.png" />
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{PendenciasCRMControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada" value="#{PendenciasCRMControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>

    </h:form>
</rich:modalPanel>

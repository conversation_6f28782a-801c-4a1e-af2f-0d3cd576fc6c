<%--
  Created by IntelliJ IDEA.
  User: luiz
  Date: 08/04/2017
  To change this template use File | Settings | File Templates.
--%>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>

<script type="text/javascript">
    jQuery.noConflict();
</script>

<h:panelGroup layout="block" id="biObjecoes"
              styleClass="container-bi-crm">
    <h:panelGroup layout="block" styleClass="bi-crm-box-header">
        <h:panelGroup layout="block" styleClass="col-text-align-left pull-left"
                      style="display: inline-block;margin:0px 5px">
            <h:outputText styleClass="tituloBICRM" value="Objeções"/>
            <h:outputLink value="#{SuperControle.urlBaseConhecimento}bi-objecoes-crm/"
                          styleClass="linkWiki" title="Clique e saiba mais: BI - Objeções"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="col-text-align-right pull-right">
            <h:outputText styleClass="tituloBICRM"
                          value="Total de #{BusinessIntelligenceCRMControle.indObjecoes} objeções"/>
    </h:panelGroup>
    </h:panelGroup>


    <h:panelGroup layout="block" styleClass="bi-crm-box-detail" id="panelGeralBIOBjecoes" style="height: auto">

        <h:panelGroup layout="block" id="objecoesPorQuantidade"
                              rendered="#{BusinessIntelligenceCRMControle.tipoBIObjecao == 1}">

            <h:panelGroup layout="block" id="panelOpcoesObjecoesPorQuantidade"
                          style="display: inline-flex; width: 100%">

                <h:panelGroup layout="block" style="width: 50%">
                    <h:selectOneRadio id="tipoBIObjecaoQtd"
                                      value="#{BusinessIntelligenceCRMControle.tipoBIObjecao}"
                                      style="margin-left: 15px"
                                      layout="pageDirection">
                        <f:selectItems value="#{BusinessIntelligenceCRMControle.tiposBIObjecoes}"/>
                        <a4j:support event="onchange" reRender="panelGeralBIOBjecoes"/>
                    </h:selectOneRadio>
                </h:panelGroup>

                <h:panelGroup layout="block" style="width: 50%">

                    <%--BOTÃO PDF--%>
                    <a4j:commandLink id="exportarPdfObjQuantidade"
                                     styleClass="exportadores"
                                     style="margin-left: 8px; float: right; margin-right: 20px;"
                                     actionListener="#{ExportadorListaControle.exportar}"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Desempenho Mensal', 800,200);#{ExportadorListaControle.msgAlert}"
                                     accesskey="2">
                        <f:attribute name="lista"
                                     value="#{BusinessIntelligenceCRMControle.listaBIObjecoesPorQuantidade}"/>
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos"
                                     value="objecao=Objeção,quantidade=Quantidade"/>
                        <f:attribute name="prefixo" value="ObjecoesPorQuantidade"/>
                        <f:attribute name="titulo" value="Objeções por Quantidade"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>

                    <%--BOTÃO EXCEL--%>
                    <a4j:commandLink id="exportarExcelObjQuantidade"
                                     styleClass="exportadores"
                                     style="margin-left: 8px; float: right"
                                     actionListener="#{ExportadorListaControle.exportar}"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                     accesskey="3">
                        <f:attribute name="lista"
                                     value="#{BusinessIntelligenceCRMControle.listaBIObjecoesPorQuantidade}"/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos"
                                     value="objecao=Objeção,quantidade=Quantidade"/>
                        <f:attribute name="prefixo" value="ObjecoesPorQuantidade"/>
                        <f:attribute name="titulo" value="Objeções por Quantidade"/>
                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>

                </h:panelGroup>

            </h:panelGroup>

                    <script type="text/javascript">
                        function LegendClickPorQuantidade( graph ) {
                            var objecao = graph.title;
                            var objecaoInput = document.getElementById('form:objecaoBuscaDetalhada');
                            objecaoInput.value = objecao;

                            var objecaoCodigo = document.getElementById('form:objecaoBuscaDetalhadaCodigo');
                            objecaoCodigo.value = null;
                            if (graph.dataContext !== undefined && graph.dataContext.codigo !== undefined) {
                                objecaoCodigo.value = graph.dataContext.codigo;
                            }

                            var porFase = document.getElementById('form:objecaoBuscaDetalhadaPorFase');
                            porFase.value = false;
                            document.getElementById('form:montarTabelaDetahadaPorObjecao').click();
                        }
                        function carregarGraficoCRMObjecoesPorQuantidade() {
                            var chart = AmCharts.makeChart("chartdivObjePorQtd", {
                                "type": "pie",
                                "startDuration": 0,
                                "theme": "light",
                                "addClassNames": true,
                                "legend":{
                                    "position": "bottom",
                                    "autoMargins": false,
                                    "clickMarker": LegendClickPorQuantidade,
                                    "clickLabel": LegendClickPorQuantidade
                                },
                                "innerRadius": "0%",
                                "dataProvider": ${BusinessIntelligenceCRMControle.jsonBIObjecoesPorQuantidade},
                                "titleField": "title",
                                "valueField": "value",
                                "colorField": "color",
                                "labelText": "",
                                "labelRadius": 5,
                                "radius": "45%",
                                "export": {
                                    "enabled": false
                                }
                            });
                    /*if (chart.chartData.length > 10) {
                        document.getElementById("chartdivObjePorQtd").style.height = ((chart.chartData.length / 2)*50)+"px";
                    }*/
                        }
                    </script>

                    <div id="chartdivObjePorQtd"
                 style="height: 450px;"></div>
                    <script>
                        carregarGraficoCRMObjecoesPorQuantidade();
                    </script>
                </h:panelGroup>

        <%----%>
        <h:panelGroup layout="block" id="objecoesPorFase"
                              rendered="#{BusinessIntelligenceCRMControle.tipoBIObjecao == 2}">

            <h:panelGroup layout="block" id="panelOpcoesObjecoesPorFase"
                          style="display: inline-flex; width: 100%">

                <h:panelGroup layout="block" style="width: 50%">
                    <h:selectOneRadio id="tipoBIObjecaoFase"
                                      value="#{BusinessIntelligenceCRMControle.tipoBIObjecao}"
                                      style="margin-left: 15px"
                                      layout="pageDirection">
                        <f:selectItems value="#{BusinessIntelligenceCRMControle.tiposBIObjecoes}"/>
                        <a4j:support event="onchange" reRender="panelGeralBIOBjecoes"/>
                    </h:selectOneRadio>
                </h:panelGroup>

                <h:panelGroup layout="block" style="width: 50%">

                    <%--BOTÃO PDF--%>
                    <a4j:commandLink id="exportarPdfObjPorFase"
                                     styleClass="exportadores"
                                     style="margin-left: 8px; float: right; margin-right: 20px;"
                                     actionListener="#{ExportadorListaControle.exportar}"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Desempenho Mensal', 800,200);#{ExportadorListaControle.msgAlert}"
                                     accesskey="2">
                        <f:attribute name="lista"
                                     value="#{BusinessIntelligenceCRMControle.listaBIObjecoesPorFase}"/>
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos"
                                     value="objecao=Fase,quantidade=Quantidade"/>
                        <f:attribute name="prefixo" value="ObjecoesPorFase"/>
                        <f:attribute name="titulo" value="Objeções por Fase"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>

                    <%--BOTÃO EXCEL--%>
                    <a4j:commandLink id="exportarExcelObjPorFase"
                                     styleClass="exportadores"
                                     style="margin-left: 8px; float: right"
                                     actionListener="#{ExportadorListaControle.exportar}"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                     accesskey="3">
                        <f:attribute name="lista"
                                     value="#{BusinessIntelligenceCRMControle.listaBIObjecoesPorFase}"/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos"
                                     value="objecao=Fase,quantidade=Quantidade"/>
                        <f:attribute name="prefixo" value="ObjecoesPorFase"/>
                        <f:attribute name="titulo" value="Objeções por Fase"/>
                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>


                    <script type="text/javascript">
                        function LegendClickPorFase( graph ) {
                            var objecao = graph.title;
                            var objecaoInput = document.getElementById('form:objecaoBuscaDetalhada');
                            objecaoInput.value = objecao;
                            var objecaoCodigo = document.getElementById('form:objecaoBuscaDetalhadaCodigo');
                            objecaoCodigo.value = null;
                            var porFase = document.getElementById('form:objecaoBuscaDetalhadaPorFase');
                            porFase.value = true;
                            document.getElementById('form:montarTabelaDetahadaPorObjecao').click();
                        }
                        function carregarGraficoCRMObjecoesPorFase() {
                            var chart = AmCharts.makeChart("chartdivObjePorFase", {
                                "type": "pie",
                                "startDuration": 0,
                                "theme": "light",
                                "addClassNames": true,
                                "legend":{
                                    "position": "bottom",
                                    "autoMargins": false,
                                    "clickMarker": LegendClickPorFase,
                                    "clickLabel": LegendClickPorFase
                                },
                                "innerRadius": "0%",
                                "dataProvider": ${BusinessIntelligenceCRMControle.jsonBIObjecoesPorFase},
                                "titleField": "title",
                                "valueField": "value",
                                "colorField": "color",
                                "labelText": "",
                                "labelRadius": 5,
                                "radius": "45%",
                                "export": {
                                    "enabled": false
                                }
                            });
                        }
                    </script>

                    <div id="chartdivObjePorFase"
                 style="height: 450px;"></div>
                    <script>
                        carregarGraficoCRMObjecoesPorFase();
                    </script>
                </h:panelGroup>
            </h:panelGroup>
</h:panelGroup>

<h:inputHidden id="objecaoBuscaDetalhadaCodigo" value="#{BusinessIntelligenceCRMControle.codigoObjecao}"/>
<h:inputHidden id="objecaoBuscaDetalhada" value="#{BusinessIntelligenceCRMControle.tituloObjecao}"/>
<h:inputHidden id="objecaoBuscaDetalhadaPorFase" value="#{BusinessIntelligenceCRMControle.porFase}"/>

<a4j:commandLink id="montarTabelaDetahadaPorObjecao"
                 style="visibility: hidden;"
                 action="#{BusinessIntelligenceCRMControle.montarTabelaPorObjecao}"
                 status="false"
                 oncomplete="#{BusinessIntelligenceCRMControle.oncompleteRetorno}"
                 reRender="modalListaPorObjecoesClientes, modalListaPorObjecoesClientesReduzido"/>
<%--
  Created by IntelliJ IDEA.
  User: luiz
  Date: 11/12/2015
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>

<style type="text/css">
    .container-bi-crm-indicadores {
        width: 100%;
        margin: 10px;
        -webkit-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        -moz-box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.35);
        background-color: #ffffff;
        transition: transform .1s ease-out;
        -moz-transition: -moz-transform .1s ease-out;
        -o-transition: -o-transform .1s ease-out;
        -webkit-transition: -webkit-transform .1s ease-out;
    }

    .bi-crm-box-header-indicadores {
        line-height: 25px;
        padding: 15px;
        text-align: center;
        height: 3.5em;
    }

    .bi-crm-box-header-indicadores .fa-icon-external-link-square{
        color : #32616b;
    }

    .bi-crm-box-detail-indicadores {
        background-color: #fff;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>

<h:panelGroup id="biIndicadores"
              layout="block">

    <h:panelGroup layout="block" id="superiorBIIndicadores" style="display: flex; justify-content: space-around">

        <%--AGENDAMENTO DE LIGAÇÃO PENDENTE--%>
        <h:panelGroup layout="block" styleClass="container-bi-crm-indicadores" id="agendamentoLigacaoPendente">

            <h:panelGroup layout="block" styleClass="bi-crm-box-header-indicadores">
                <h:outputText styleClass="tituloBICRM" value="Agendamento de Ligação Pendente"
                              escape="false"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}bi-agendamento-de-ligacao-pendente-crm/"
                              title="Clique e saiba mais: Agendamento de Ligações Pendentes"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-crm-box-detail-indicadores col-text-align-center">
                <a4j:commandLink
                        id="abrirListaAgendamentoLigacaoPendente"
                        styleClass="indicadoresBICRM"
                        value="#{BusinessIntelligenceCRMControle.indAgendaLigacoesPendentes}"
                        ajaxSingle="true"
                        action="#{BusinessIntelligenceCRMControle.abrirListaAgendamentoLigacaoPendente}"
                        oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                        reRender="modalListaClientesBI"/>
            </h:panelGroup>

        </h:panelGroup>

        <%--INDICAÇÕES SEM CONTATO--%>
        <h:panelGroup layout="block" styleClass="container-bi-crm-indicadores" id="indIndicacoes">

            <h:panelGroup layout="block" styleClass="bi-crm-box-header-indicadores">
                <h:outputText styleClass="tituloBICRM" value="Indicações Sem Contato"
                              escape="false"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}bi-indicacoes-sem-contato-crm/"
                              title="Clique e saiba mais: Indicações sem Contato"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-crm-box-detail-indicadores col-text-align-center">
                <a4j:commandLink
                        id="abrirListaIndicacoesSemContato"
                        styleClass="indicadoresBICRM"
                        value="#{BusinessIntelligenceCRMControle.indIndicacoesSemContato}"
                        ajaxSingle="true"
                        action="#{BusinessIntelligenceCRMControle.abrirListaIndicacoesSemContato}"
                        oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                        reRender="modalListaClientesBI"/>
            </h:panelGroup>

        </h:panelGroup>
        <%--CONTATO RECEPTIVO--%>
        <h:panelGroup layout="block" styleClass="container-bi-crm-indicadores" id="indReceptivo">

            <h:panelGroup layout="block" styleClass="bi-crm-box-header-indicadores">
                <h:outputText styleClass="tituloBICRM" value="Contato Receptivo"
                              escape="false"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}bi-contato-receptivo-crm/"
                              title="Clique e saiba mais: Contato Receptivo"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>

            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-crm-box-detail-indicadores col-text-align-center">
                <a4j:commandLink
                        id="abrirListaContatoReceptivo"
                        styleClass="indicadoresBICRM"
                        value="#{BusinessIntelligenceCRMControle.indContatoReceptivo}"
                        ajaxSingle="true"
                        action="#{BusinessIntelligenceCRMControle.abrirListaContatoReceptivo}"
                        oncomplete="Richfaces.showModalPanel('modalListaClientesBI')"
                        reRender="modalListaClientesBI"/>
            </h:panelGroup>

        </h:panelGroup>

        <%-- CLIENTES COM OBJEÇÃO DEFINITIVA --%>
        <h:panelGroup layout="block" styleClass="container-bi-crm-indicadores" id="indObjecoes">

            <h:panelGroup layout="block" styleClass="bi-crm-box-header-indicadores">


                <h:outputText styleClass="tituloBICRM" value="Clientes Com Objeção Definitiva"
                              escape="false"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}bi-clientes-com-objecao-definitiva-crm/"
                              title="Clique e saiba mais: Clientes Com Objeção Definitiva"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>

            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-crm-box-detail-indicadores col-text-align-center">
                <a4j:commandLink
                        id="abrirListaObjecoes"
                        styleClass="indicadoresBICRM"
                        value="#{BusinessIntelligenceCRMControle.indClientesObjecaoDefinitiva}"
                        action="#{BusinessIntelligenceCRMControle.abrirListaClientesObjecoesDefinitiva}"
                        oncomplete="Richfaces.showModalPanel('modalListaObjecoesClientes')"
                        reRender="modalListaObjecoesClientes"
                        ajaxSingle="true"/>
            </h:panelGroup>

        </h:panelGroup>

    </h:panelGroup>
</h:panelGroup>

<%--
  Created by IntelliJ IDEA.
  User: luiz
  Date: 11/12/2015
  To change this template use File | Settings | File Templates.
--%>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>

<script type="text/javascript">
    jQuery.noConflict();

    function mostrarBackDesemp() {
        jQuery('.cardDesemp').addClass('flippedDesemp');
    }
</script>

<style type="text/css">

    .flipDesemp {
        -webkit-perspective: 800;
        -ms-perspective: 800;
        -moz-perspective: 800;
        -o-perspective: 800;
        height: 540px;
        position: relative;
    }

    .flipDesemp .cardDesemp.flippedDesemp {
        transform: rotatex(-180deg);
        -ms-transform: rotatex(-180deg); /* IE 9 */
        -moz-transform: rotatex(-180deg); /* Firefox */
        -webkit-transform: rotatex(-180deg); /* Safari and Chrome */
        -o-transform: rotatex(-180deg); /* Opera */
    }

    .flipDesemp .cardDesemp {
        padding: 0;
        border: none !important;
        width: 100%;
        height: 100%;
        -webkit-transform-style: preserve-3d;
        -webkit-transition: 0.5s;
        -moz-transform-style: preserve-3d;
        -moz-transition: 0.5s;
        -ms-transform-style: preserve-3d;
        -ms-transition: 0.5s;
        -o-transform-style: preserve-3d;
        -o-transition: 0.5s;
        transform-style: preserve-3d;
        transition: 0.5s;
    }

    .flipDesemp .cardDesemp .faceDesemp {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 2;
        -webkit-backface-visibility: hidden; /* Safari & Chrome */
        -moz-backface-visibility: hidden; /* Firefox */
        -ms-backface-visibility: hidden; /* Internet Explorer */
        -o-backface-visibility: hidden; /* Opera */
        border-radius: 8px;
    }

    .flipDesemp .cardDesemp .frontDesemp {
        background: white;
        position: absolute;
        z-index: 1;
    }

    .flipDesemp .cardDesemp .backDesemp {
        background: white;
        transform: rotatex(-180deg);
        -ms-transform: rotatex(-180deg); /* IE 9 */
        -moz-transform: rotatex(-180deg); /* Firefox */
        -webkit-transform: rotatex(-180deg); /* Safari and Chrome */
        -o-transform: rotatex(-180deg); /* Opera */
    }

</style>

<h:panelGroup layout="block" id="biDesempenho"
              styleClass="container-bi-crm">
                                    <h:panelGroup layout="block" styleClass="bi-crm-box-header">
        <h:panelGroup layout="block" styleClass="col-text-align-left pull-left"
                      style="display: inline-block;margin:0px 5px">
                                            <h:outputText styleClass="tituloBICRM" value="Desempenho Mensal dos Colaboradores"/>
                                            <h:outputLink value="#{SuperControle.urlBaseConhecimento}bi-desempenho-mensal-dos-colaboradores-crm/"
                                                          styleClass="linkWiki" title="Clique e saiba mais: BI - Desempenho Mensal dos Colaboradores"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="bi-crm-box-detail" style="height: auto">
                                        <h:panelGroup layout="block" styleClass="flipDesemp">
                                            <h:panelGroup layout="block" styleClass="cardDesemp">
                <h:panelGroup layout="block" styleClass="faceDesemp frontDesemp col-text-align-left"
                              style="top: 4px;">

                    <h:panelGroup layout="block" id="paneldatasDesem" style="width: 100%; display: inline-flex">
                        <h:panelGroup layout="block" id="panelPeriodoDesem" style="margin-top: 5px; margin-left: 15px;">
                            <h:outputText styleClass="textos-size-14" value="Período:"/>
                        </h:panelGroup>

                        <h:panelGroup id="containerDataInicialBIDesempenho" layout="block"
                                      style="font-size: 12px !important; margin: 0; padding: 0;"
                                      styleClass="bi-panel-bi-crm pull-left dateTimeCustom bi-btn-filtroCol bi-filtro-dataBase tudo step1">
                    <rich:calendar id="dataInicialBIDesempenho"
                                   buttonClass="margin-left-right-7"
                                       buttonIcon="/imagens_flat/icon-calendar-check.png"
                                       inputClass="forcarSemBorda"
                                       styleClass="dateTimeCustom"
                                       inputStyle="height: 2em;border: none !important;" inputSize="6"
                                       datePattern="dd/MM/yyyy"
                                                                   value="#{BusinessIntelligenceCRMControle.dataInicioBIDesempenho}"
                                       showWeeksBar="false">
                        </rich:calendar>
                        <rich:jQuery
                                query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                selector=".btn-toggle-calendar"/>
                        </h:panelGroup>


                        <h:panelGroup layout="block" id="panelAteDesem"
                                      style="margin-top: 5px; margin-left: 5px; margin-right: 5px">
                            <h:outputText value="até" styleClass="textos-size-14"/>
                        </h:panelGroup>

                        <h:panelGroup id="containerDataFinalBIDesempenho" layout="block"
                                      style="font-size: 12px !important; margin: 0; padding: 0;"
                                      styleClass="bi-panel-bi-crm pull-left dateTimeCustom bi-btn-filtroCol bi-filtro-dataBase tudo step1">
                    <rich:calendar id="dataFinalBIDesempenho"
                                   buttonClass="margin-left-right-7"
                                       buttonIcon="/imagens_flat/icon-calendar-check.png"
                                       inputClass="forcarSemBorda"
                                       styleClass="dateTimeCustom"
                                       inputStyle="height: 2em;border: none !important;" inputSize="6"
                                       datePattern="dd/MM/yyyy"
                                                                   value="#{BusinessIntelligenceCRMControle.dataFinalBIDesempenho}"
                                       showWeeksBar="false">
                        </rich:calendar>
                        <rich:jQuery
                                query="click(function(){jQuery(this).parent().find('.rich-calendar-button').trigger('click');})"
                                selector=".btn-toggle-calendar"/>
                        </h:panelGroup>


                        <h:panelGroup layout="block" id="pnlTipoColaborador" style="margin-top: 5px; margin-left: 5px; margin-right: 5px"
                                      rendered="#{!BusinessIntelligenceCRMControle.configuracaoSistemaCRMVO.apresentarColaboradoresPorTipoColaborador}">

                            <h:selectOneMenu id="slctTipoColaborador" styleClass="form" value="#{BusinessIntelligenceCRMControle.tipoColaborador}">
                                <f:selectItems value="#{BusinessIntelligenceCRMControle.listaSelectItemTipoColaborador}" />
                            </h:selectOneMenu>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="paneAtualizarDesem"
                                      style="margin-top: 5px; margin-left: 5px; margin-right: 5px">

                                                    <a4j:commandButton id="atualizarBIDesempenho" reRender="biDesempenho"
                                                                       style="vertical-align:middle; width:16px;height:16px; padding-left: 1%"
                                                                       title="Atualizar BI Desempenho Mensal dos Colaboradores"
                                                                       action="#{BusinessIntelligenceCRMControle.montarJSONDesempenhoConsultores}"
                                                                       image="/images/update.png"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" id="panelExportarDesem"
                                      style="margin-top: 5px; margin-left: 5px; margin-right: 5px; width: 100%;">

                                                    <a4j:commandLink id="exportarPdfSemProdutos"
                                                                     styleClass="exportadores"
                                                                     style="margin-left: 8px; float: right; margin-right: 20px;"
                                                                     actionListener="#{BusinessIntelligenceCRMControle.exportar}"
                                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Desempenho Mensal', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                     accesskey="2">
                                                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="atributos"
                                     value="codUsuario=Código Usuário,nome=Nome,mesAno=Mes/Ano,meta=Meta,metaAtingida=Meta Atingida,repescagem=Repescagem,porcentagem=Porcentagem"/>
                                                        <f:attribute name="prefixo" value="Desempenho Mensal dos Colaboradores"/>
                                                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                                    </a4j:commandLink>

                                                    <a4j:commandLink id="exportarExcelSemProdutos"
                                                                     styleClass="exportadores"
                                                                     style="margin-left: 8px; float: right"
                                                                     actionListener="#{BusinessIntelligenceCRMControle.exportar}"
                                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                     accesskey="3">
                                                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos"
                                     value="codUsuario=Código Usuário,nome=Nome,mes=Mes,ano=Ano,mesAno=Mes/Ano,meta=Meta,metaAtingida=Meta Atingida,repescagem=Repescagem,porcentagem=Porcentagem"/>
                                                        <f:attribute name="prefixo" value="Desempenho Mensal dos Colaboradores"/>
                                                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                    </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>

                                                    <script type="text/javascript">
                                                        function carregarGraficoCRMDesempenhoCons() {
                                                            var chart = AmCharts.makeChart("chartdivDesemCons", {
                                                                "type": "serial",
                                                                "theme": "none",
                                                                "dataProvider": ${BusinessIntelligenceCRMControle.jsonBIDesempenhoConsultores},
                                                                "valueAxes": [{
                                                                    "unit": "%",
                                                                    "dashLength": 0
                                                                }],
                                                                "startDuration": 1,
                                                                "legend": {
                                                                    "autoMargins": true,
                                                                    "equalWidths": false,
                                                                    "horizontalGap": 10,
                                                                    "markerSize": 10,
                                                                    "useGraphSettings": true,
                                                                    "valueAlign": "left",
                                                                    "valueWidth": 0
                                                                },
                                                                "graphs": ${BusinessIntelligenceCRMControle.jsonBIDesempenhoConsultoresLegenda},
                                                                "chartCursor": {
                                                                    "categoryBalloonEnabled": false,
                                                                    "cursorAlpha": 0,
                                                                    "zoomable": false
                                                                },
                                                                "categoryField": "mesAno",
                                                                "categoryAxis": {
                                                                    "gridPosition": "start",
                                                                    "axisAlpha": 0,
                                                                    "fillAlpha": 0.05,
                                                                    "fillColor": "#000000",
                                                                    "gridAlpha": 0,
                                                                    "labelRotation": 45
                                                                },
                                                                "export": {
                                                                    "enabled": true
                                                                }

                                                            });

                                                        }
                                                    </script>

                                                    <div id="chartdivDesemCons"
                                                         style="width: 100%; height: 530px;"></div>
                                                    <script>
                                                        carregarGraficoCRMDesempenhoCons();
                                                    </script>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGroup>
</h:panelGroup>
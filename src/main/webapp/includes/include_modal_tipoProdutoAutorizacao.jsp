<head>
    <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <style>
        .rich-picklist-list-content {
            width: 200px !important;
            height: auto !important;
            overflow: hidden !important;
            min-height: 320px !important;
        }
    </style>
</head>

<%@include file="imports.jsp" %>

<a4j:outputPanel>

    <rich:modalPanel id="modalTipoProdutoAuto"
                     styleClass="novaModal noMargin" shadowOpacity="true"
                     width="650" top="65" autosized="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Tipo de Produto"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalTipoProdutoAuto"/>
                <rich:componentControl for="modalTipoProdutoAuto" attachTo="hidelinkModalTipoProdutoAuto"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formTipoAutorz">
            <h:panelGroup id="painelConfigTipoProdutoAuto" layout="block"
                          style="margin: 20px 10px;">
                <rich:pickList value="#{AutorizacaoCobrancaControle.tiposProdutosSelecionados}"
                               style="width: 100%"
                               copyAllControlLabel="Adicionar todos"
                               copyControlLabel="Adicionar selecionado"
                               removeAllControlLabel="Remover todos"
                               removeControlLabel="Remover selecionado">
                    <f:selectItems value="#{AutorizacaoCobrancaControle.tiposProdutosDisponiveis}"/>
                </rich:pickList>

                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink action="#{AutorizacaoCobrancaControle.gravarTiposProdutos}"
                                     oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};#{AutorizacaoCobrancaControle.onComplete}"
                                     reRender="form:tabAutorizacao,form:tipProd,form:panelParACobrar,form:panelAutorizacaoCobrancaCliente,button-adicionar-produtos"
                                     value="Gravar"
                                     styleClass="botaoPrimario texto-size-14-real"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

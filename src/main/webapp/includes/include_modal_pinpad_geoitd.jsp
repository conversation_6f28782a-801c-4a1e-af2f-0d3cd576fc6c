<%@include file="imports.jsp" %>

<rich:modalPanel id="mdlPinpadGeoitd" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Operacion - Geoitd"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkMensagemGenerica" rendered="#{MovPagamentoControle.codMsgGeoitd != 10}"/>
                <rich:componentControl for="mdlPinpadGeoitd" attachTo="hidelinkMensagemGenerica" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
    </f:facet>


        <h:panelGroup id="formMdlPinpadGeoitd">
            <h:panelGroup styleClass="margin-box">
                <h:panelGroup layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                    <h:outputText value="#{MovPagamentoControle.msgGeoitd}"/>
                    <c:if test="${MovPagamentoControle.codMsgGeoitd == 10}">
                        <h:graphicImage url="images/tres_pontos_loading.gif" width="180px" height="90px" style="margin: 0 25%"/>
                    </c:if>
                </h:panelGroup>

            </h:panelGroup>
            <h:outputText id="msgDetalhada"
                          value="#{MensagemGenericaControle.mensagemDetalhada}"/>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup rendered="#{MensagemGenericaControle.labelBotaoFecharTela == null}" >

                   <c:if test="${MovPagamentoControle.codMsgGeoitd == 10}">
                        <a4j:commandLink action="#{MovPagamentoControle.posPinpadGeoitd}"  value="OK"
                                         id="ok" oncomplete="location.reload()"
                                         styleClass="botaoPrimario texto-size-16-real"
                                         reRender="mdlPinpadGeoitd, panelMsgGeoitd, @form" style="margin: 50px !important;"/>
                    </c:if>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

</rich:modalPanel>
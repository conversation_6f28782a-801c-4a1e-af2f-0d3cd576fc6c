<h:panelGroup id="painelStoneConnect">
    <pre id="respostaStone"
         style="position: static; text-align: right;">
    </pre>

    <h:panelGroup>


        <script>

        </script>
        <script type="text/javascript" src="script/cappta-checkout.js"></script>

        <style>
            #cappta-checkout-iframe {
                z-index: 999999;
                display: none;
            }
            .BrandCappta{
                display: none !important;
            }

        </style>

        <script>
            var authenticationRequest = {
                authenticationKey: '${MovPagamentoControle.authenticationKeyCappta}'
            };
            var onAuthenticationSuccess = function (response) {
                console.log(response);
                document.getElementById('resposta').innerHTML = 'Pinpad Autenticado com sucesso' + '<br>' + 'Checkout GUID: ' + response.merchantCheckoutGuid;
            };
            var onAuthenticationError = function (response) {
                console.log(response);
                document.getElementById('resposta').innerHTML = 'C�digo: ' + response.reasonCode + '<br>' + response.reason;
            };
            var onPendingPayments = function (response) {
                console.log(response);
            };
            var checkout;
            function fazerCheckout(){
                if (checkout == null) {
                    checkout = CapptaCheckout.authenticate(authenticationRequest, onAuthenticationSuccess, onAuthenticationError, onPendingPayments);
                }
            }
            var multiplePaymentsSessionInProgress = false;

            function canStartMultiplePaymentsSession() {
                return multiplePaymentsSessionInProgress === false && $('input[name="rbMultiplePayments"]:checked').val() === 'true';
            }

            function startMultiplePayments() {
                try {
                    var numberOfPayments = parseInt(document.getElementById('txtNumberOfPayments').value);

                    checkout.startMultiplePayments(numberOfPayments, function () {
                        alert('Sess�o multiplos pagamentos encerrada!');
                        document.getElementById('txtNumberOfPayments').value = 0;
                        handlerMultiplePaymentsElements(false);

                    });

                    multiplePaymentsSessionInProgress = true;
                    handlerMultiplePaymentsElements(true);
                } catch (ex) {
                    alert(ex);
                }
            }

            function handlerMultiplePaymentsElements(disabled) {
                document.getElementById('txtNumberOfPayments').disabled = disabled;
                document.getElementById('rbUseMultiplePayments').disabled = disabled;
                document.getElementById('rbNotUseMultiplePayments').disabled = disabled;
            }


            var onPaymentError = function (response) {
                console.log(response);
                document.getElementById('resposta').innerHTML = 'C�digo: ' + response.reasonCode + '<br>' + response.reason;
                erroPagamentoPinpad(JSON.stringify(response));
                jQuery('#cappta-checkout-iframe').hide();
            };


            function splittedDebitPayment() {
                if (canStartMultiplePaymentsSession()) {
                    startMultiplePayments();
                }

                var splittedDebitRequest = {
                    amount: parseFloat(document.getElementById('txtSplittedDebitAmount').value.replace(',', '')),
                    installments: document.getElementById('txtSplittedDebitInstallments').value
                };

                checkout.splittedDebitPayment(splittedDebitRequest, onPaymentSuccess, onPaymentError);
            }

            function selectInstallmenteType(value) {
                if (value) {
                    document.getElementById('installmentDetails').classList.add('show');
                    return;
                }
                document.getElementById('installmentDetails').classList.remove('show');
            }

            function paymentReversal() {

                var paymentReversalRequest = {
                    administrativePassword: document.getElementById('administrativePassword').value,
                    administrativeCode: document.getElementById('administrativeCode').value
                };

                CapptaCheckout.paymentReversal(paymentReversalRequest, onPaymentSuccess, onPaymentError);
            }

            function pinpadInput() {
                var elInputType = document.getElementById("pinpadInputType");
                var inputType = elInputType.options[elInputType.selectedIndex].value;

                var success = function (response) {
                    updateResult(response.pinpadValue);
                };

                var error = function (response) {
                    updateResult(response.reason);
                };

                checkout.getPinpadInformation({inputType: inputType}, success, error);
            }

            function confirmPayments() {
                multiplePaymentsSessionInProgress = false;

                checkout.confirmPayments();

                alert('Pagamentos confirmados com sucesso!');
            }

            function undoPayments() {
                multiplePaymentsSessionInProgress = false;

                checkout.undoPayments();

                alert('Pagamentos desfeitos com sucesso!');
            }

            function updateResult(message) {
//    document.getElementById('resposta').innerHTML = message;
            }

            $(function () {

                $('#rbUseMultiplePayments').prop('checked', false);
                $('#rbNotUseMultiplePayments').prop('checked', true);

                $('#txtDebitAmount').maskMoney();
                $('#txtCreditAmount').maskMoney();
                $('#txtSplittedDebitAmount').maskMoney();

                $('input[name=rbMultiplePayments]').change(function () {
                    var isMultiplePayments = this.value === 'true' ? true : false;

                    if (isMultiplePayments) {
                        document.getElementById('txtNumberOfPayments').classList.remove('hide');
                        document.getElementById('multiplePaymentsButtons').classList.remove('hide');
                        return;
                    }

                    document.getElementById('txtNumberOfPayments').classList.add('hide');
                    document.getElementById('multiplePaymentsButtons').classList.add('hide');
                    multiplePaymentsSessionInProgress = false;
                });
            });


            function start() {
                // document.getElementById('resposta').innerHTML = 'Aguardando Pinpad...';
            }
            start();

            var onPaymentSuccess = function (response) {
                // console.log(response);
                var bandeira = document.getElementById('form:bandeiraCappta');
                if (bandeira != null) {
                    bandeira.value = response.cardBrandCode;
                }
                concluirPinpad(response.receipt.customerReceipt, response.uniqueSequentialNumber, JSON.stringify(response));
                jQuery('#cappta-checkout-iframe').hide();
            };

            function openWin(conteudo) {
                var divText = '<div style="text-align: center;">'+conteudo+'</div>';
                var myWindow = window.open('', '', 'width=600,height=800');
                var doc = myWindow.document;
                doc.open();
                doc.write(divText);
                doc.close();
                myWindow.onfocus= myWindow.print();
            }

            function matarConexaoWs() {
                if(document.getElementById('cappta-checkout-iframe')){
                    document.getElementById('cappta-checkout-iframe').remove();
                }
                document.getElementById('resposta').innerHTML = 'C�digo: 9' + '<br>' + 'Conex�o iniciada.' + '<br>' + 'Escolha um PDV!';
                if (ws) {
                    ws.close();
                }
            }

        </script>

    </h:panelGroup>
    <h:panelGroup id="painelStoneCone" rendered="#{MovPagamentoControle.pinpad.pinpadCappta}">
        <script>

            function debitPayment() {
                var amount = parseFloat(${MovPagamentoControle.pinpad.valorPinpad});
                checkout.debitPayment({amount: amount}, onPaymentSuccess, onPaymentError);
                jQuery('#cappta-checkout-iframe').show();
            }

            function creditPayment() {
                var installmentType = ${MovPagamentoControle.pinpad.tipo};
                var installments = '${MovPagamentoControle.pinpad.nrParcelas}';
                var creditRequest = {
                    amount: parseFloat(${MovPagamentoControle.pinpad.valorPinpad}),
                    installments: installments === '' ? 0 : installments,
                    installmentType: installmentType
                };

                checkout.creditPayment(creditRequest, onPaymentSuccess, onPaymentError);
                jQuery('#cappta-checkout-iframe').show();
            }
        </script>

    </h:panelGroup>
</h:panelGroup>

<%-- 
    Document   : include_bi_indiceconversaofiltrotipo
    Created on : 09/08/2012, 13:54:51
    Author     : carla
--%>
<%@include file="imports.jsp" %>
<rich:modalPanel id="panelFiltroConvenioCobranca" width="480" minHeight="300" styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtro Conv�nio"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <span class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkFiltroConvenioCobranca"
                    onclick="Richfaces.hideModalPanel('panelFiltroConvenioCobranca'); jQuery('.consultarCCobranca').click();"
                    />
        </h:panelGroup>
    </f:facet>

    <h:form id="formPanelFiltroConvenioCobranca" style="height: 100%">
        <script>
            function toggleTudo(val) {
                var checked = jQuery(val).is(':checked');
                var table= jQuery('.chckfiltros');
                jQuery('td input:checkbox',table).prop('checked',checked);
            }

            jQuery('.chckfiltros input:checkbox').on('click', function () {
                jQuery('.chktudo').prop('checked', (jQuery(this).closest('table').find('tbody :checkbox:checked').length == jQuery(this).closest('table').find('tbody :checkbox').length));
            });
        </script>

        <h:panelGroup layout="block" styleClass="paginaFontResponsiva">
            <h:panelGroup layout="block" style="display: inline-block;padding: 5px; width: 100%;">
                <h:panelGroup layout="block" style="display: table;margin: 0 auto;margin-bottom:5px;">
                    <h:outputText styleClass="rotuloCampos" value=""/>
                </h:panelGroup>

                <table style="width: calc(100% - 10px); margin-bottom: 5px; border-bottom: 1px solid #cecece">
                    <tr>
                        <td>
                            <h:selectBooleanCheckbox value="#{RelContratosRecorrenciaControle.marcarTodos}"
                                                     styleClass="chktudo"
                                                     onclick="toggleTudo(this)"></h:selectBooleanCheckbox>
                            <label>Marcar/Desmarcar Todos</label>
                        </td>
                    </tr>
                </table>

                <h:selectManyCheckbox
                        id="convenioSelect" styleClass="chckfiltros"
                        style="text-align: left;"
                        layout="pageDirection"
                        value="#{RelContratosRecorrenciaControle.listaConveniosSelecionados}">
                    <f:selectItems value="#{RelContratosRecorrenciaControle.conveniosCobranca}" />
                </h:selectManyCheckbox>
            </h:panelGroup>
        </h:panelGroup>

        <br>
        <h:panelGroup layout="block" styleClass="container-botoes"
                      id="blocoBotaoAtualizar">
            <a4j:commandLink reRender="containerRecorrencia,panelFiltroConvenioCobranca"
                             action="#{RelContratosRecorrenciaControle.atualizarAgora}"
                             styleClass="botaoPrimario texto-size-16 consultarCCobranca"
                             oncomplete="montarTips(); Richfaces.hideModalPanel('panelFiltroConvenioCobranca');">
                <span class="texto-size-16 texto-font  texto-cor-branco">Atualizar </span>
                <i style="vertical-align: inherit" class="fa-icon-refresh texto-size-16  texto-cor-branco">
            </a4j:commandLink>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

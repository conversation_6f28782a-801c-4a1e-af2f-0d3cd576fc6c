<%-- 
    Document   : include_modal_validacaoCpf
    Created on : 08/06/2015, 10:59:25
    Author     : marcosandre
--%>
<head>
    <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <style>

        .centralizabotoes {
            width: 80%;
            padding-left: 10%;
        }
        .th3fix th:nth-child(3) {
            width: 119px !important;
        }
    </style>
</head>

<%@include file="imports.jsp" %>

<a4j:outputPanel>
    <rich:modalPanel id="modalValidacaoCPF" styleClass="novaModal" autosized="true" shadowOpacity="true" width="750" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText  value="Pessoas com mesmo CPF"/>
            </h:panelGroup>
        </f:facet>
        <style>
            .linhainformacoesaluno td{
                background-color: transparent !important;
            }
        </style>

        <h:form id="formValidacaoCPF">
            <h:panelGroup id="painelDados">
                <rich:dataTable value="#{ClienteControle.clientesComMesmoCpf}"
                                var="cliente" width="100%"
                                id="tableResults" styleClass="pure-table pure-table-striped pure-table-noCellPadding pure-table-links th3fix"
                                rendered="#{fn:length(ClienteControle.clientesComMesmoCpf) > 0}"
                                columnClasses="centralizado,esquerda,centralizado,centralizado,esquerda">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Aluno"
                                    styleClass="text"
                                    style="font-weight: bold;"/>
                        </f:facet>
                        <h:panelGrid columns="2" styleClass="linhainformacoesaluno">
                            <a4j:mediaOutput     element="img" 
                                                 align="left" style="left:0px;width:70px;height:90px; border:none; "
                                                 cacheable="false" session="true"
                                                 title="#{cliente.pessoa.nome}"
                                                 createContent="#{ClienteControle.paintFotoModal}"
                                                 styleClass="shadow"
                                                 value="#{ImagemData}" mimeType="image/jpeg" >
                                <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                <f:param name="largura" value="70"/>
                                <f:param name="altura" value="90"/>
                                <f:param name="pessoa" value="#{cliente.pessoa.codigo}"></f:param>
                            </a4j:mediaOutput>
                            <h:panelGroup>
                                <h:panelGrid columns="1" width="100%"  styleClass="linhainformacoesaluno">

                                    <h:panelGroup>
                                        <h:outputText value="Matricula: "
                                                      styleClass="text"
                                                      style="font-weight: bold;"/>
                                        <h:outputText value="#{cliente.matricula}" styleClass="text"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText value="Nome: "
                                                      styleClass="text"
                                                      style="font-weight: bold;"/>
                                        <h:outputText value="#{cliente.pessoa.nome}" styleClass="text"/>
                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText value="#{msg_aplic.prt_ConfiguracaoSistema_cfpOb}: "
                                                      styleClass="text"
                                                      style="font-weight: bold;"/>
                                        <h:outputText value="#{cliente.pessoa.cfp}" styleClass="text"/>

                                    </h:panelGroup>
                                    <h:panelGroup>
                                        <h:outputText value="Empresa: "
                                                      styleClass="text"
                                                      style="font-weight: bold;"/>
                                        <h:outputText value="#{cliente.empresa.nome}" styleClass="text"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                            </h:panelGroup>
                        </h:panelGrid>
                    </rich:column>
                    <rich:column style="padding: 0px 5px 0px 5px">
                        <f:facet name="header">
                            <h:outputText value="Dependentes"
                                          styleClass="text"
                                          style="font-weight: bold;"/>
                        </f:facet>
                        <h:outputText rendered="#{fn:length(cliente.clienteDenpentedes) == 0}" value="Ainda nao tem v�nculos" styleClass="text"/>
                        <rich:dataTable value="#{cliente.clienteDenpentedes}"
                                        var="dep" width="100%"
                                        id="tableResultsDep" styleClass="pure-table pure-table-horizontal pure-table-striped pure-table-noCellPadding pure-table-links"
                                        rendered="#{fn:length(cliente.clienteDenpentedes) > 0}"
                                        columnClasses="centralizado,esquerda,centralizado,centralizado,esquerda"
                                        >
                            <rich:column>
                                <a4j:mediaOutput     element="img" 
                                                     align="left" style="left:0px;width:35px;height:35px; border:none; "
                                                     cacheable="false" session="true"
                                                     title="#{dep.pessoa.nome}"
                                                     createContent="#{ClienteControle.paintFotoModal}"
                                                     styleClass="shadow"
                                                     value="#{ImagemData}" mimeType="image/jpeg" >
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="35"/>
                                    <f:param name="altura" value="35"/>
                                    <f:param name="pessoa" value="#{dep.pessoa.codigo}"></f:param>
                                </a4j:mediaOutput>

                            </rich:column>
                            <rich:column>
                                <h:outputText value="#{dep.pessoa.nome}" styleClass="text"/>
                            </rich:column>
                            <rich:column rendered="#{!cliente.apresentarBotaoTransferirClienteEmpresa}" style="padding: 0px 5px 0px 5px">
                                <a4j:commandLink
                                                 styleClass="button-small pure-button pure-button-primary"
                                                 style="font-size: 11px !important;padding: 5px 0px 5px 0px;"
                                                 actionListener="#{ClienteControle.preparaClienteOperacaoCadastro}"
                                                 action="#{ClienteControle.editarCliente}"
                                                 accesskey="1" rendered="#{!dep.apresentarBotaoTransferirClienteEmpresa}">
                                    <f:attribute name="identificador" value="dep"/>
                                    <i class="fa-icon-edit" ></i> &nbsp Editar
                                </a4j:commandLink>
                            </rich:column>

                        </rich:dataTable>

                    </rich:column>
                    <rich:column style="padding: 0px 9px 0px 5px">
                        <f:facet name="header">
                            <h:outputText value="Op��es"
                                          styleClass="text"
                                          style="font-weight: bold;"/>
                        </f:facet>
                        <h:panelGrid columns="1" styleClass="centralizabotoes">
                            <a4j:commandLink id="btnTranferir"
                                             style="font-size: 11px !important;"
                                             styleClass="pure-button pure-button-small"
                                             action="#{ClienteControle.gravarClienteTrocandoEmpresa}"
                                             actionListener="#{ClienteControle.preparaClienteOperacaoCadastro}"
                                             oncomplete="Richfaces.hideModalPanel('modalValidacaoCPF');
                                            fireElementFromParent('form:btnRederCliente');#{ClienteControle.mensagemNotificar}"
                                            reRender="form"
                                             accesskey="1" rendered="#{cliente.apresentarBotaoTransferirClienteEmpresa}">
                                <f:attribute name="identificador" value="cliente"/>
                                <i class="fa-icon-random" ></i> &nbsp Transferir
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="pure-button pure-button-small"
                                             style="font-size: 11px !important;"
                                             actionListener="#{ClienteControle.preparaClienteOperacaoCadastro}"
                                             action="#{ClienteControle.editarCliente}"
                                             oncomplete="Richfaces.hideModalPanel('modalValidacaoCPF');
                                                fireElementFromParent('form:btnRederCliente');"
                                             reRender="form"
                                             accesskey="1" rendered="#{!cliente.apresentarBotaoTransferirClienteEmpresa}">
                                <f:attribute name="identificador" value="cliente"/>
                                <i class="fa-icon-edit" ></i> &nbsp Editar
                            </a4j:commandLink>

                            <a4j:commandLink id="btnUsarCpf"
                                             styleClass="pure-button pure-button-small"
                                             style="font-size: 11px !important;"
                                             action="#{ClienteControle.selecionarPessoaResponsavel}"
                                             accesskey="1" rendered="#{!cliente.apresentarBotaoTransferirClienteEmpresa}"
                                             reRender="cpfMsk,modalValidacaoCPF,panelDadosPessoais">
                                <i class="fa-icon-plus" ></i> &nbsp Adicionar
                                <rich:toolTip>
                                <h:outputText styleClass="textoConfiguracoes" value="Adicionar como Respons�vel."></h:outputText>
                                </rich:toolTip>
                            </a4j:commandLink>
                        </h:panelGrid>
                    </rich:column>

                </rich:dataTable>
                <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.msgNrAlunosMesmoCpf}" />
                <br/>
                  <a4j:commandLink id="btnInformarOutro"
                                 styleClass="pure-button pure-button-primary pull-right"
                                 style="margin: 3px;"
                                 action="#{ClienteControle.informarNovoCpf}"
                                 accesskey="1"
                                 reRender="cpfMsk"
                                 onclick="Richfaces.hideModalPanel('modalValidacaoCPF');"
                                 oncomplete="setFocusCampoCpf();">
                    <i class="fa-icon-refresh" ></i> &nbsp Informar outro CPF
                </a4j:commandLink>

                <a4j:commandLink id="btnIgnorar"
                                 styleClass="pure-button pure-button-primary pull-right"
                                 style="margin: 3px;"
                                 reRender="panelMensagemErro,panelExisteCliente"
                                 action="#{ClienteControle.escolherIgnorarCpfDuplicado}"
                                 accesskey="1" rendered="#{ClienteControle.apresentarBotaoIgnorarValidacaoCPF}"
                                 onclick="Richfaces.hideModalPanel('modalValidacaoCPF');"
                                 oncomplete="setFocusCampoCpf();">
                    <i class="fa-icon-remove" ></i> &nbsp Ignorar
                </a4j:commandLink>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>
    <script>
        function setFocusCampoCpf(){
            window.onload = function () {
                fecharMenu()
            }
            document.getElementById("form:cpf").focus();
        }

        function setFocusCampoNome(){
            document.getElementById("form:nomeCompleto").focus();
        }
    </script>
</a4j:outputPanel>

<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>

<style>
    .divNovidadeFav{
        background: #ff6e1e;
        font-weight: 600;
        border-top-left-radius: 15px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        border-top-right-radius: 15px;
        display: flex;
        height: 20px;
        padding-left: 7px;
        padding-right: 7px;
        position: relative;
    }

    .new {
        position: relative;
        top: -4px;
        color: #ffffff;
        font-size: 12px;
    }
</style>

<h:panelGroup
        rendered="#{MenuControle.exibirFavoritos}"
        layout="block"
        styleClass="grupoMenuLateral menuAcessoRapido">
    <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
        <i class="fa-icon-rocket"></i> <h:outputText value="Acesso rápido"/>
        <h:outputLink id="outputlinkMenuAcessoRapido"
                      styleClass="linkWiki"
                      value="#{SuperControle.urlWiki}Menu_Acesso_Rapido"
                      title="Clique e saiba mais: Acesso rápido"
                      target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>

    </h:panelGroup>

    <%--Favoritos--%>
    <h:panelGroup >
        <a4j:repeat value="#{MenuAcessoFacilControle.funcionalidades.favorito}" var="funcionalidade">
            <h:panelGroup id="menuFuncionalidade${funcionalidade}" rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}"
                          layout="block"
                          styleClass="grupoMenuItem grupoMenuItemContainer">

                <h:panelGroup layout="block"
                              styleClass="grupoMenuItemNomeContainer"
                              style="max-width: 156px;">
                    <a4j:commandLink value="#{funcionalidade.descricaoMenulateral}"
                                     styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                     style="margin-left: 2px;margin-right: 2px;"
                                     actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                     oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                     action="#{FuncionalidadeControle.abrirComCasoNavegacao}">
                        <f:attribute name="funcionalidade" value="#{funcionalidade.name}" />
                    </a4j:commandLink>
                    <h:panelGroup layout="block" styleClass="divNovidadeFav"
                                  rendered="#{funcionalidade.novidade}">
                        <h:outputText styleClass="new"
                                      value="NOVIDADE"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="grupoMenuItemLinksContainer" >
                    <a4j:commandLink  styleClass="menuItemLinkFixed" style="visibility: visible; margin: 0px;"
                                      actionListener="#{MenuAcessoFacilControle.removerFavorito}"
                                      oncomplete="#{MenuAcessoFacilControle.resultFavorito}"
                                      title="Remover" reRender="panelMenuAcessoRapido, panelMsg" id="idRemoverFavorito">
                        <icon>
                            <img src="${pageContext.request.contextPath}/imagens/pct-star-checked.svg"
                                 width="14.68px" height="14.01px"
                                 alt="Remover"/>
                        </icon>
                        <f:attribute name="funcionalidade" value="#{funcionalidade}" />
                    </a4j:commandLink>
                </h:panelGroup>

            </h:panelGroup>

            <h:panelGroup rendered="#{funcionalidade.tipo == 'menudropdown' and funcionalidade.renderizar}"
                          layout="block"
                          styleClass="grupoMenuItem dropDownSimples menu-lateral menu-flex">

                <h:panelGroup layout="block"
                              styleClass="botao-hidden"
                              style="display: flex;justify-content: center; align-items: center;">

                    <h:panelGroup layout="block"
                                  style="max-width: 156px;"
                                  styleClass="grupoMenuItemNomeContainer">
                    <a4j:commandLink value="#{funcionalidade.descricaoMenulateral}"
                                     styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                     style="margin-left: 2px;margin-right: 2px;"
                                     actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                     oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                     action="#{FuncionalidadeControle.abrirComCasoNavegacao}">
                            <f:attribute name="funcionalidade" value="#{funcionalidade.name}" />
                        </a4j:commandLink>

                        <i class="titulo3 fa-icon-caret-right linkFuncionalidadeNovo"
                           style="font-family: FontAwesome;margin-left: 1px;"></i>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="grupoMenuItemLinksContainer">
                        <h:commandLink styleClass="menuItemLinkFixed" style="visibility: visible; margin: 0px;"
                                       actionListener="#{MenuAcessoFacilControle.removerFavorito}"
                                       title="Remover">

                                <i class="pct pct-star"></i>

                            <f:attribute name="funcionalidade" value="#{funcionalidade}" />
                        </h:commandLink>
                    </h:panelGroup>

                    <jsp:include page="../include_box_menulateral_menuitem_dropdown.jsp" />

                </h:panelGroup>

            </h:panelGroup>
        </a4j:repeat>
    </h:panelGroup>

    <%--Historico--%>
    <h:panelGroup>
        <a4j:repeat value="#{MenuAcessoFacilControle.funcionalidades.historico}" var="funcionalidade">

            <h:panelGroup id="menuHistoricoFuncionalidade${funcionalidade}" layout="block"
                          styleClass="grupoMenuItem grupoMenuItemContainer"
                          rendered="#{funcionalidade.tipo == 'menulink' and funcionalidade.renderizar}">

                <h:panelGroup layout="block"
                              style="max-width: 156px;"
                              styleClass="grupoMenuItemNomeContainer">
                <a4j:commandLink value="#{funcionalidade.descricaoMenulateral}"
                                    styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                    style="margin-left: 2px;margin-right: 2px;"
                                    title="#{funcionalidade.descricao}"
                                    actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                    oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                    action="#{FuncionalidadeControle.abrirComCasoNavegacao}">
                        <f:attribute name="funcionalidade" value="#{funcionalidade.name}" />
                        <f:attribute name="idLocalizacaoMenu" value="LATERAL_FAVORITO"/>
                    </a4j:commandLink>
                    <h:panelGroup layout="block" styleClass="divNovidadeFav"
                                  rendered="#{funcionalidade.novidade}">
                        <h:outputText styleClass="new"
                                      value="NOVIDADE"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="grupoMenuItemLinksContainer">
                    <a4j:commandLink styleClass="menuItemLinkFixed" style="visibility: visible;"
                                     actionListener="#{MenuAcessoFacilControle.addFavoritoFuncionalidade}"
                                     title="Fixar"
                                     oncomplete="#{MenuAcessoFacilControle.resultFavorito}"
                                     reRender="panelMenuAcessoRapido,  panelMsg" id="idFixarFavorito">
                    <i class="pct pct-star"></i>
                    <f:attribute name="funcionalidade" value="#{funcionalidade}" />
                    </a4j:commandLink>
                </h:panelGroup>

            </h:panelGroup>

            <h:panelGroup rendered="#{funcionalidade.tipo == 'menudropdown' and funcionalidade.renderizar}"
                          layout="block"
                          styleClass="grupoMenuItem dropDownSimples menu-lateral menu-flex">

                <h:panelGroup layout="block"
                              styleClass="botao-hidden"
                              style="display: flex;justify-content: center; align-items: center;">

                    <h:panelGroup layout="block"
                                  style="max-width: 156px;"
                                  styleClass="grupoMenuItemNomeContainer">
                    <a4j:commandLink value="#{funcionalidade.descricaoMenulateral}"
                                     styleClass="titulo3 linkFuncionalidade linkFuncionalidadeAcessoRapido"
                                     style="margin-left: 2px;margin-right: 2px;"
                                     actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                     oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                     action="#{FuncionalidadeControle.abrirComCasoNavegacao}">
                            <f:attribute name="funcionalidade" value="#{funcionalidade.name}" />
                        </a4j:commandLink>
                        <i class="titulo3 fa-icon-caret-right linkFuncionalidadeNovo"
                           style="font-family: FontAwesome;margin-left: 1px;"></i>
                    </h:panelGroup>

                    <jsp:include page="../include_box_menulateral_menuitem_dropdown.jsp" />

                </h:panelGroup>

            </h:panelGroup>

        </a4j:repeat>
    </h:panelGroup>
</h:panelGroup>

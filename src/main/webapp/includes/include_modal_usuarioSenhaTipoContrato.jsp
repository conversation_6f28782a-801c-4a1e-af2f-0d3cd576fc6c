<%@include file="imports.jsp" %>

    <rich:modalPanel id="panelUsuarioSenhaTipoContrato" resizeable="true" styleClass="novaModal" shadowOpacity="true" width="420">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Contrato_Tipotitulo}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkUsuarioSenhaTipo"/>
                <rich:componentControl for="panelUsuarioSenhaTipoContrato" attachTo="hidelinkUsuarioSenhaTipo" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formUsuarioSenhaTipoContrato">
            <h:panelGroup layout="block" >
                <h:panelGrid columnClasses="colunaCentralizada" columns="1" width="100%">

                    <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
                        <h:selectOneRadio layout="pageDirection"
                                          styleClass="texto-size-16-real texto-cor-cinza texto-font"
                                          value="#{ContratoControle.valorTipoContrato}">
                            <f:selectItems value="#{ContratoControle.itensAlteracaoTipoContrato}"/>
                        </h:selectOneRadio>
                    </h:panelGrid>

                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="salvarAlteracao" value="#{msg_bt.btn_confirmar}"
                                     title="#{msg.msg_gravar_dados}"
                                     styleClass="botaoPrimario texto-size-16-real"
                                     action="#{ContratoControle.realizaAlteracaoTipoContrato}"
                                     reRender="panelUsuarioSenhaTipoContrato,panelAutorizacaoFuncionalidade"
                                     oncomplete="Richfaces.hideModalPanel('panelUsuarioSenhaTipoContrato');#{ClienteControle.mensagemNotificar}">
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
           </a4j:form>
    </rich:modalPanel>

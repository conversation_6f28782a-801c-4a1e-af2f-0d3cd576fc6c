<%@page pageEncoding="ISO-8859-1" %>
<script type="text/javascript" language="javascript" src="script/scriptModalTokenOperacao.js"></script>

<%--
    Created on : 16/11/2023, 16:05:03
    Author     : Estulano
--%>

<%@include file="/includes/imports.jsp" %>

<style>
    .vertical-line {
        width: 100%;
        border: 1px solid #C9CBCF;
        height: 0px
    }
</style>

<%--  MODAL SOLICITAÇÃO DE TOKEN PARA GRAVAR OPERAÇÕES--%>
<rich:modalPanel id="modalTokenOperacao" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="650"
                 styleClass="novaModal">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Autenticação de segurança"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup style="margin-top: 7px; display: block">
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkModal"/>
            <rich:componentControl for="modalTokenOperacao"
                                   attachTo="hidelinkModal"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalTokenOperacao" ajaxSubmit="true">
        <h:panelGroup id="panelModalTokenOperacao"
                      layout="block" style="padding: 20px">

            <h:panelGroup layout="block"
                          style="text-align: center">
                <h:outputText style="font-family: Arial; font-size: 15px; color: #444444"
                              value="#{TokenOperacaoControle.montarTextoAnteriorCamposExibirNoModalTokenOperacao}"/>
                <h:outputText style="font-family: Arial; font-size: 15px; color: #444444; font-weight: bold;"
                              value="#{TokenOperacaoControle.labelsCamposExibirNoModalTokenOperacao}"/>
                <h:outputText style="font-family: Arial; font-size: 15px; color: #444444"
                              value="#{TokenOperacaoControle.montarTextoPosteriorCamposExibirNoModalTokenOperacao}"/>
            </h:panelGroup>

            <div style="margin-top: 20px;" class="vertical-line"></div>

            <h:panelGroup layout="block"
                          style="text-align: center; margin-top: 25px;">
                <%--INPUT 1--%>
                <h:inputText
                        style="width: 44px; height: 60px; font-size: 3em!important; text-transform: uppercase;"
                        onkeypress="if (apertouTeclaEnter(event.keyCode)) {document.getElementById('formModalTokenOperacao:btnValidararTokenOperacao').click(); return false;}"
                        onkeyup="if (digitouTextoOuNumero(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken2').focus();}"
                        id="inputToken1"
                        maxlength="1"
                        tabindex="1"
                        value="#{TokenOperacaoControle.input1}"/>
                <%--INPUT 2--%>
                <h:inputText
                        style="width: 44px; height: 60px; margin-left: 8px; font-size: 3em!important; text-transform: uppercase;"
                        onkeypress="if (apertouTeclaEnter(event.keyCode)) {document.getElementById('formModalTokenOperacao:btnValidararTokenOperacao').click(); return false;}"
                        onkeyup="if (!apertouTeclaBackspace(event.keyCode) && digitouTextoOuNumero(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken3').focus();}
                                 if (apertouTeclaBackspace(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken1').focus(); return false;}"
                        id="inputToken2"
                        maxlength="1"
                        tabindex="2"
                        value="#{TokenOperacaoControle.input2}"/>
                <%--INPUT 3--%>
                <h:inputText
                        style="width: 44px; height: 60px; margin-left: 8px; font-size: 3em!important; text-transform: uppercase;"
                        onkeypress="if (apertouTeclaEnter(event.keyCode)) {document.getElementById('formModalTokenOperacao:btnValidararTokenOperacao').click(); return false;}"
                        onkeyup="if (!apertouTeclaBackspace(event.keyCode) && digitouTextoOuNumero(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken4').focus();}
                                 if (apertouTeclaBackspace(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken2').focus(); return false;}"
                        id="inputToken3"
                        maxlength="1"
                        tabindex="3"
                        value="#{TokenOperacaoControle.input3}"/>

                <%--INPUT 4--%>
                <h:inputText
                        style="width: 44px; height: 60px; margin-left: 24px; font-size: 3em!important; text-transform: uppercase;"
                        onkeypress="if (apertouTeclaEnter(event.keyCode)) {document.getElementById('formModalTokenOperacao:btnValidararTokenOperacao').click(); return false;}"
                        onkeyup="if (!apertouTeclaBackspace(event.keyCode) && digitouTextoOuNumero(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken5').focus();}
                                 if (apertouTeclaBackspace(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken3').focus(); return false;}"
                        id="inputToken4"
                        maxlength="1"
                        tabindex="4"
                        value="#{TokenOperacaoControle.input4}"/>
                <%--INPUT 5--%>
                <h:inputText
                        style="width: 44px; height: 60px; margin-left: 8px; font-size: 3em!important; text-transform: uppercase;"
                        onkeypress="if (apertouTeclaEnter(event.keyCode)) {document.getElementById('formModalTokenOperacao:btnValidararTokenOperacao').click(); return false;}"
                        onkeyup="if (!apertouTeclaBackspace(event.keyCode) && digitouTextoOuNumero(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken6').focus();}
                                 if (apertouTeclaBackspace(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken4').focus(); return false;}"
                        id="inputToken5"
                        maxlength="1"
                        tabindex="5"
                        value="#{TokenOperacaoControle.input5}"/>
                <%--INPUT 6--%>
                <h:inputText
                        style="width: 44px; height: 60px; margin-left: 8px; font-size: 3em!important; text-transform: uppercase;"
                        onkeypress="if (apertouTeclaEnter(event.keyCode)) {document.getElementById('formModalTokenOperacao:btnValidararTokenOperacao').click(); return false;}"
                        onkeyup=" if (apertouTeclaBackspace(event.keyCode)) {document.getElementById('formModalTokenOperacao:inputToken5').focus(); return false;}"
                        id="inputToken6"
                        maxlength="1"
                        tabindex="6"
                        value="#{TokenOperacaoControle.input6}"/>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          style="text-align: center; margin-top: 25px;">
                <h:outputText style="font-family: Arial; font-size: 15px; color: #444444"
                              value="Insira o código de autenticação enviado para o Pacto App"/>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelBotoesRodape"
                          style="margin-top: 50px;">

                <h:panelGroup layout="block">
                    <a4j:commandLink value="Como receber o código?"
                                     style="font-family: Arial; font-size: 15px; color: #0090FF"
                                     id="btnAbrirModalExplicacao"
                                     oncomplete="Richfaces.showModalPanel('modalTokenOperacaoExplicacao')">
                    </a4j:commandLink>
                </h:panelGroup>

                <h:panelGroup layout="block" id="divBtnValidar" style="text-align: right; margin-top: -15px;">
                    <a4j:commandLink style="margin-left: 5px"
                                     id="btnValidararTokenOperacao"
                                     styleClass="botoes nvoBt botaoPrimario"
                                     value="Validar código"
                                     action="#{TokenOperacaoControle.invokeBotaoValidar}"
                                     reRender="#{TokenOperacaoControle.reRenderComponents}"
                                     oncomplete="#{TokenOperacaoControle.mensagemNotificar};#{TokenOperacaoControle.onCompleteBotaoValidar};#{TokenOperacaoControle.msgAlert}">
                    </a4j:commandLink>
                </h:panelGroup>

            </h:panelGroup>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<%-- 
    Document   : include_resultado_bv
    Created on : 19/06/2012, 08:18:52
    Author     : carla
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">


<h:panelGroup id="panelRelatorio" layout="block">
    <h:panelGrid id="areaResultados" width="100%">
        <c:if test="${RelatorioBVsControle.mostrarConteudo}">
            <h:panelGrid width="100%" columns="1" rendered="#{!RelatorioBVsControle.mostrarNPS}">
                <h:panelGroup id="resultadoRelPag" layout="block" style="padding: 10px;">
                    <h:outputText escape="false" value="Pergunta: "/>
                    <h:outputText escape="false" style="font-weight: bold;"
                                  value="#{RelatorioBVsControle.bVsRelatorioTO.perguntaVO.descricao}"/>
                    <rich:dataTable
                            rendered="#{RelatorioBVsControle.bVsRelatorioTO.perguntaVO.tipoPergunta eq 'TE'}"
                            id="itens" value="#{RelatorioBVsControle.bVsRelatorioTO.listaBVsRelatorioListasTO}"
                            var="respostas" width="100%">
                        <rich:column id="respostas" width="80%" sortable="true"
                                     sortBy="#{respostas.respostaVO.descricaoRespota}">
                            <f:facet name="header">
                                <h:outputText value="Respostas"/>
                            </f:facet>
                            <a4j:outputPanel>
                                <h:outputText escape="false" value="#{respostas.respostaVO.descricaoRespota}"/>
                            </a4j:outputPanel>
                        </rich:column>
                        <rich:column id="qtdRespondidas" width="20%">
                            <f:facet name="header">
                                <h:outputText value="Nº Respostas"/>
                            </f:facet>
                            <a4j:outputPanel>
                                <h:outputText escape="false" value="#{respostas.totalRespondidoPorResposta}"/>
                            </a4j:outputPanel>
                        </rich:column>
                        <rich:column id="qtdConversoes" width="20%"
                                     rendered="#{!RelatorioBVsControle.relatorioPesquisa}">
                            <f:facet name="header">
                                <h:outputText value="Conversões" styleClass="tooltipster"
                                              title="Número de conversões da resposta, podendo uma conversão estar em mais de uma resposta quando de multipla escolha."/>
                            </f:facet>
                            <a4j:outputPanel>
                                <h:outputText styleClass="tooltipster"
                                              title="Número de conversões da resposta, podendo uma conversão estar em mais de uma resposta quando de multipla escolha."
                                              escape="false" value="#{respostas.totalConvertidos}"/>
                            </a4j:outputPanel>
                        </rich:column>
                        <rich:column width="20%">
                            <f:facet name="header">
                                <h:outputText value="Detalhes"/>
                            </f:facet>
                            <a4j:commandButton image="../imagens/botaoVisualizar.png"
                                               action="#{RelatorioBVsControle.verDetalhes}"
                                               oncomplete="abrirPopup('./resumoClientesBV.jsp', 'ResumoCliente', 850, 660);">
                                <f:param name="state" value="AC"/>
                            </a4j:commandButton>
                        </rich:column>
                    </rich:dataTable>

                    <br>
                    <br>

                    <h:outputText escape="false" value="Total Resposta: "/>
                    <h:outputText escape="false" style="font-weight: bold;"
                                  value="#{RelatorioBVsControle.bVsRelatorioTO.totalGeral}"/>

                    <c:if test="${!RelatorioBVsControle.relatorioPesquisa}">
                        <br>
                        <h:outputText escape="false"
                                      value="Total Conversões: "/>
                        <h:outputText escape="false" styleClass="tooltipster"
                                      title="Exibe a quantidade de conversões de acordo com o filtro informado, caso marcado somente perguntas do questionário irá exibir somente conversões do mesmo."
                                      style="font-weight: bold;"
                                      value="#{RelatorioBVsControle.bVsRelatorioTO.totalConversoes}"/>
                        <br>
                    </c:if>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              rendered="#{RelatorioBVsControle.bVsRelatorioTO.totalGeral > 0 and RelatorioBVsControle.bVsRelatorioTO.perguntaVO.tipoPergunta ne 'TE'}"
                              style="text-align: center; width: 100%"
                              id="graficos">
                    <h:panelGroup layout="block"
                                  styleClass="col-md-10 col-md-offset-1">
                        <script type="text/javascript">
                            function carregarGraficoBarra() {
                                var chart = AmCharts.makeChart("chartdivObjePorQtd", {
                                    "type": "serial",
                                    "theme": "none",
                                    "marginRight": 70,
                                    "dataProvider": ${RelatorioBVsControle.bVsRelatorioTO.jsonGrafico},
                                    "startDuration": 0,
                                    "graphs": [{
                                        "balloonText": "[[title]]: <b>[[value]]</b>",
                                        "fillColorsField": "color",
                                        "fillAlphas": 0.9,
                                        "lineAlpha": 0.2,
                                        "lineColor": "#FEC514",
                                        "columnWidth": 0.7,
                                        "type": "column",
                                        "valueField": "qtd",
                                        "title": "Qtd respostas"
                                    }, {
                                        "balloonText": "[[title]]: <b>[[value]]</b>",
                                        "fillColorsField": "color",
                                        "fillAlphas": 0.9,
                                        "lineAlpha": 0.2,
                                        "type": "column",
                                        "clustered": false,
                                        "lineColor": "#DB4C3C",
                                        "columnWidth": 0.6,
                                        "valueField": "convertido",
                                        "title": "Qtd convertido"
                                    }],
                                    "chartCursor": {
                                        "categoryBalloonEnabled": false,
                                        "cursorAlpha": 0,
                                        "zoomable": false
                                    },
                                    "categoryField": "resp",
                                    "categoryAxis": {
                                        "gridPosition": "start",
                                        "labelRotation": 45
                                    },
                                    "export": {
                                        "enabled": true
                                    }
                                });

                                chart.addListener("clickGraphItem", function vendas(event) {
                                    var meta = event.item.category;
                                    var metabuscar = document.getElementById('form:respostaAbrir');
                                    metabuscar.value = meta;
                                    document.getElementById('form:verListaResposta').click();
                                });
                            }
                        </script>

                        <h:inputHidden id="respostaAbrir"
                                       value="#{RelatorioBVsControle.respostaAbrir}"/>

                        <a4j:commandLink id="verListaResposta"
                                         style="visibility: hidden;"
                                         action="#{RelatorioBVsControle.verListaResposta}"
                                         oncomplete="abrirPopup('./resumoClientesBV.jsp', 'ResumoCliente', 850, 660);"/>

                        <div id="chartdivObjePorQtd"
                             style="height: 400px;"></div>
                        <script>
                            carregarGraficoBarra();
                        </script>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGrid rendered="#{RelatorioBVsControle.mostrarNPS}" width="100%" columns="2">
                <h:panelGroup>
                    <h:outputText escape="false" value="Pergunta: "/>
                    <h:outputText escape="false" style="font-weight: bold;"
                                  value="#{RelatorioBVsControle.bVsRelatorioTO.perguntaVO.descricao}"/>
                    <rich:dataTable
                            id="itensNS" value="#{RelatorioBVsControle.quantidadeRespostasNPS}"
                            var="resposta" width="100%" rows="3">
                        <rich:column id="respostasNS" width="80%" sortable="false">
                            <f:facet name="header">
                                <h:outputText value="Clientes" style="padding-right: 80px"/>
                            </f:facet>
                            <a4j:outputPanel>
                                <h:outputText value="#{resposta.nomeCliente}"/>
                            </a4j:outputPanel>
                        </rich:column>
                        <rich:column id="qtdRespondidasNS" width="20%">
                            <f:facet name="header">
                                <h:outputText value="Quantidade"/>
                            </f:facet>
                            <a4j:outputPanel>
                                <h:outputText escape="false" value="#{resposta.quantidade}"/>
                            </a4j:outputPanel>
                        </rich:column>
                    </rich:dataTable>
                    <rich:dataTable>
                        <rich:column id="relatorioRespostasNS" width="80%" sortable="true"
                                     sortBy="#{respostas.respostaVO.descricaoRespota}">
                            <a4j:outputPanel>
                                <h:outputText escape="false" value="#{respostas.respostaVO.descricaoRespota}"/>
                            </a4j:outputPanel>
                        </rich:column>
                    </rich:dataTable>
                    <h:outputText escape="false" value="Total Geral: "/>
                    <h:outputText escape="false"
                                  value="#{RelatorioBVsControle.valorTotalRespondidoApresentar}"/>
                </h:panelGroup>
                <div align="center" class="panelPrincipalNPS">
                    <h2 style="background-color: #c4d6de; padding:30px 0 20px 0 ">Relatório de Pesquisas (%)</h2>
                    <div class="corpo"><h1 style="margin-top: -40px;">
                        NPS: ${RelatorioBVsControle.resultadoNPSPorcentagem}
                        <a class="linkWiki" href="${SuperControle.urlWiki}NPS"
                           title="Clique e saiba mais: NPS" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </a></h1>
                        <div style="padding: 10px 0 10px 0; color: ${RelatorioBVsControle.corResultadoNPS}">
                            <h3>${RelatorioBVsControle.resultadoNPSMostrar}</h3></div>
                        <div style="margin-bottom: -50px; padding-right: 40px; text-align: left;">${RelatorioBVsControle.mensagemNPS}</div>
                    </div>
                </div>
            </h:panelGrid>
            <script>
                jQuery('.tooltipster').tooltipster({
                    theme: 'tooltipster-light',
                    position: 'bottom',
                    animation: 'grow',
                    contentAsHTML: true
                });
            </script>
        </c:if>
    </h:panelGrid>
</h:panelGroup>

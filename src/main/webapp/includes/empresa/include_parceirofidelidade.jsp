<%-- 
    Document   : include_parceirofidelidade
    Created on : 02/01/2019, 15:30:49
    Author     : waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="/includes/include_import_minifiles.jsp" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<c:set var="pagina" scope="request" value="<%=request.getRequestURI()%>"/>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request"/>
<style>
    .subordinado {
        padding: 5px !important;
    }

    .rich-table-cell {
        font-size: 12px !important;
    }
</style>

<h:panelGrid id="panelParceiroFidelidade" columns="1" width="100%" headerClass="subordinado"
             columnClasses="colunaCentralizada">

    <f:facet name="header">
        <h:outputText value="Parceiro Fidelidade"/>
    </f:facet>

    <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita">

        <h:outputText styleClass="tituloCampos" value="Habilitar Parceiro Fidelidade: "/>
        <h:selectBooleanCheckbox styleClass="tituloCampos" value="#{EmpresaControle.empresaVO.usarParceiroFidelidade}">
            <a4j:support event="onclick" reRender="form"/>
        </h:selectBooleanCheckbox>

    </h:panelGrid>
</h:panelGrid>

<h:panelGrid id="panelIdentParceiro"
             rendered="#{EmpresaControle.empresaVO.usarParceiroFidelidade}"
             columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
    <f:facet name="header">
        <h:outputText
                value="#{msg_aplic.prt_Empresa_parceiroFidelidade_tituloForm} DOTZ"/>
    </f:facet>
    <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">

        <h:outputText styleClass="tituloCampos" value="Ambiente de Produção:"/>
        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                 value="#{EmpresaControle.parceiroFidelidade.ambienteProducao}"/>

        <h:outputText styleClass="tituloCampos" value="Client ID (Rewards):"/>
        <h:inputText id="clientID"
                     value="#{EmpresaControle.parceiroFidelidade.clientID}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos" value="Client Secret (Rewards):"/>
        <h:inputText id="clientSecret" value="#{EmpresaControle.parceiroFidelidade.clientSecret}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos" value="Client ID (Redemption):"/>
        <h:inputText id="clientIDRedemption" value="#{EmpresaControle.parceiroFidelidade.clientIDRedemption}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos" value="Client Secret (Redemption):"/>
        <h:inputText id="clientSecretRedemption" value="#{EmpresaControle.parceiroFidelidade.clientSecretRedemption}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos" value="Código Loja (StoreCode):"/>
        <h:inputText id="codigoLoja" value="#{EmpresaControle.parceiroFidelidade.codigoLoja}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos" value="Código Máquina (DeviceCode):"/>
        <h:inputText id="codigoMaquina" value="#{EmpresaControle.parceiroFidelidade.codigoMaquina}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos" value="Código Oferta (OfferCode):"/>
        <h:inputText id="codigoOferta" value="#{EmpresaControle.parceiroFidelidade.codigoOferta}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos" value="Código Resgate (locationId):"/>
        <h:inputText id="codigoResgate" value="#{EmpresaControle.parceiroFidelidade.codigoResgate}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos" value="Tags:"/>
        <h:inputText id="tagsParceiro" value="#{EmpresaControle.parceiroFidelidade.tags}"
                     size="15" maxlength="40"/>
        <h:outputText styleClass="tituloCampos"
                      value="Gerar pontos somente para cliente já cadastrado no DOTZ: "/>
        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                 value="#{EmpresaControle.parceiroFidelidade.validarCliente}"/>
        <h:outputText styleClass="tituloCampos"
                      value="Gerar pontos para pagamento automático(Recorrência) de parcela atrasada: "/>
        <h:selectBooleanCheckbox styleClass="tituloCampos"
                                 value="#{EmpresaControle.parceiroFidelidade.parcelaVencidaGeraPonto}"/>
        <h:outputText styleClass="tituloCampos"
                      value="CPF para acúmulo e resgate caso o usuário não tenha CPF cadastrado:"/>
        <h:inputText value="#{EmpresaControle.parceiroFidelidade.cpf}"
                     id="cpfParceiroFidelidade"
                     onblur="blurinput(this);"
                     onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                     onfocus="focusinput(this);" size="15" maxlength="14"/>
    </h:panelGrid>


    <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada"
                 id="produtosParceiroFidelidade">
        <f:facet name="header">
            <h:outputText value="Tabela Resgate DOTZ"/>
        </f:facet>

        <a4j:commandButton action="#{EmpresaControle.consultarProdutosParceiroFidelidade}"
                           reRender="formProdutoParceiro"
                           value="Consultar Produtos DOTZ"
                           styleClass="botoes nvoBt"
                           oncomplete="#{EmpresaControle.msgAlert};#{EmpresaControle.mensagemNotificar}"/>

        <rich:dataTable id="tblProdutosParceiro" value="#{EmpresaControle.parceiroFidelidade.produtos}"
                        var="produto"
                        rowClasses="colunaDireita" width="100%">

            <rich:column width="200" style="text-align:left;">
                <f:facet name="header">
                    <h:outputText value="Descrição"/>
                </f:facet>
                <h:panelGroup>
                    <h:outputText value="#{produto.descricao}"/>
                </h:panelGroup>
            </rich:column>
            <rich:column width="200" style="text-align:right;">
                <f:facet name="header">
                    <h:outputText value="SKU"/>
                </f:facet>
                <h:panelGroup>
                    <h:outputText value="#{produto.codigoExterno}"/>
                </h:panelGroup>
            </rich:column>
            <rich:column width="200" style="text-align:right;">
                <f:facet name="header">
                    <h:outputText value="Pontos"/>
                </f:facet>
                <h:panelGroup>
                    <h:outputText value="#{produto.pontos}"/>
                </h:panelGroup>
            </rich:column>
            <rich:column width="200" style="text-align:right;">
                <f:facet name="header">
                    <h:outputText value="Valor R$"/>
                </f:facet>
                <h:panelGroup>
                    <h:outputText value="#{produto.valor_Apresentar}"/>
                </h:panelGroup>
            </rich:column>
            <rich:column width="60" style="text-align:center;">
                <f:facet name="header">
                    <h:outputText value="Remover"/>
                </f:facet>
                <a4j:commandButton id="btnRemoverProdutoTabelaParceiro"
                                   action="#{EmpresaControle.removerProduto}"
                                   reRender="produtosParceiroFidelidade"
                                   image="./imagens/botaoRemover.png"/>
            </rich:column>
        </rich:dataTable>

    </h:panelGrid>

    <h:panelGrid columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
        <f:facet name="header">
            <h:outputText value="Tabelas Acumular DOTZ"/>
        </f:facet>
    </h:panelGrid>

    <a4j:commandButton value="Nova Tabela" action="#{EmpresaControle.adicionarTabelaParceiro}"
                       reRender="panelIdentParceiro" styleClass="botoes nvoBt"/>

    <a4j:repeat value="#{EmpresaControle.parceiroFidelidade.itens}" var="tblParceiro">

        <rich:simpleTogglePanel switchType="client" label="#{tblParceiro.nomeTabelaComDefault}"
                                opened="#{false}"
                                style="margin-top: 8px">

            <h:panelGrid columns="2" headerClass="subordinado" columnClasses="classEsquerda, classDireita"
                         style="width: 100%">

                <f:facet name="header">
                    <h:outputText value="Configurações"/>
                </f:facet>

                <h:outputText value="#{msg_aplic.prt_Empresa_parceiroFidelidade_nomeTabela}"/>
                <h:panelGrid columns="2" columnClasses="classEsquerda, classDireita">
                    <h:inputText id="nomeTblParceiro" value="#{tblParceiro.nomeTabela}" size="50" maxlength="50">
                        <a4j:support event="onchange" reRender="panelIdentParceiro"/>
                    </h:inputText>

                    <a4j:commandButton value="Excluir Tabela" action="#{EmpresaControle.excluirTabelaParceiro}"
                                       reRender="panelIdentParceiro" styleClass="botoes nvoBt btSec btPerigo"/>
                </h:panelGrid>

                <h:outputText value="#{msg_aplic.prt_Empresa_parceiroFidelidade_tabelaDefaultRecorrencia}"/>
                <h:selectBooleanCheckbox styleClass="campos" value="#{tblParceiro.defaultRecorrencia}"/>
            </h:panelGrid>


            <h:panelGrid columns="2" headerClass="subordinado" columnClasses="classEsquerda, classDireita"
                         style="width: 100%">

                <f:facet name="header">
                    <h:outputText value="Itens"/>
                </f:facet>

                <h:outputText value="#{msg_aplic.prt_Empresa_parceiroFidelidade_valorInicial}"/>
                <h:panelGroup layout="block">
                    <h:inputText id="vlrInicial" value="#{tblParceiro.itemAdicionar.valorInicio}"
                                 onkeypress="return formatar_moeda(this,'.',',',event);">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                </h:panelGroup>

                <h:outputText value="#{msg_aplic.prt_Empresa_parceiroFidelidade_valorFinal}"/>
                <h:inputText id="vlrFinal" value="#{tblParceiro.itemAdicionar.valorFim}"
                             onkeypress="return formatar_moeda(this,'.',',',event);">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>

                <h:outputText value="#{msg_aplic.prt_Empresa_parceiroFidelidade_multiplicador}"/>
                <rich:inputNumberSpinner minValue="0" maxValue="100"
                                         value="#{tblParceiro.itemAdicionar.multiplicador}"
                                         step="0.1"/>

                <h:outputText value=""/>
                <a4j:commandButton action="#{EmpresaControle.adicionarItemTabelaParceiro}"
                                   reRender="panelIdentParceiro,panelErroMensagem"
                                   focus="form:nomeTblParceiro" value="#{msg_bt.btn_adicionar}"
                                   image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

            </h:panelGrid>

            <h:panelGroup layout="block" style="display: block; text-align: center; padding: 10px">

                <rich:dataTable id="tblItensTabelaParceiro" value="#{tblParceiro.itens}" var="item"
                                rowClasses="colunaDireita" width="100%">
                    <rich:column width="200" style="text-align:right;">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Empresa_parceiroFidelidade_valorInicial}"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.valorInicio}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column width="200" style="text-align:right;">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Empresa_parceiroFidelidade_valorFinal}"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.valorFim}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column width="200" style="text-align: right;">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Empresa_parceiroFidelidade_multiplicador}"/>
                        </f:facet>
                        <h:outputText value="#{item.multiplicador}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column width="60" style="text-align:center;">
                        <f:facet name="header">
                            <h:outputText value="Opções"/>
                        </f:facet>
                        <a4j:commandButton id="btnRemoverItemTabelaParceiro"
                                           action="#{EmpresaControle.removerItem}"
                                           reRender="panelIdentParceiro"
                                           image="./imagens/botaoRemover.png"/>
                        <a4j:commandButton id="btnEditarItemTabelaParceiro"
                                           action="#{EmpresaControle.editarItemTabelaParceiro}"
                                           reRender="panelIdentParceiro"
                                           image="./imagens/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </rich:simpleTogglePanel>

    </a4j:repeat>
</h:panelGrid>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>

<div style="display: flex; justify-content: center;">
    <h:graphicImage id="iconPagoLivre"
                    styleClass="tooltipster"
                    width="100px"
                    value="../imagens/logo_pagolivre.png"
                    title="PagoLivre é um gateway de pagamentos ao qual o Sistema Pacto possui integração"/>
</div>

<h:panelGrid width="100%" columns="1" id="panelCadastroMerchantPagoLivre">

    <%--CADASTRAR NOVO--%>
    <h:panelGrid id="panelCadastrarMerchantPagoLivre"
                 rendered="#{!EmpresaControle.possuiMerchantPagoLivre && !EmpresaControle.editandoMerchantPagoLivre}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Realizar Integração com a PagoLivre"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos tooltipster"
                      value="Ambiente:"
                      title="Informe o ambiente para o cadastro do Merchant na integração com a PagoLivre"/>
        <h:outputText
                rendered="#{!(SuperControle.ambienteDesenvolvimentoTeste && LoginControle.usuarioLogado.usuarioPactoSolucoes)}"
                value="#{EmpresaControle.ambientePagoLivre.descricao}"/>
        <h:selectOneMenu id="listaSelectItemAmbiente" styleClass="form"
                         rendered="#{SuperControle.ambienteDesenvolvimentoTeste && LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                         value="#{EmpresaControle.ambientePagoLivre}">
            <f:selectItems value="#{EmpresaControle.listaSelectItemAmbiente}"/>
        </h:selectOneMenu>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o nome da empresa para o cadastro do Merchant na integração com a PagoLivre"
                      value="Nome da empresa:"/>
        <h:inputText value="#{EmpresaControle.nomeEmpresaPagoLivre}" size="40"/>

        <h:outputText styleClass="tituloCampos"
                      value="CNPJ da empresa:"/>
        <h:inputText size="20" maxlength="18" id="cnpjPagoLivre"
                     onkeypress="return mascara(this.form, 'form:cnpjPagoLivre', '99.999.999/9999-99', event);"
                     onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                     value="#{EmpresaControle.cnpjPagoLivre}"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o telefone da empresa para o cadastro do Merchant na integração com a PagoLivre"
                      value="Telefone:"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.dddTelefonePagoLivre}" size="2" maxlength="2"/>
            <h:inputText value="#{EmpresaControle.telefonePagoLivre }" size="10" maxlength="9"/>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o usuário para o cadastro do Merchant na integração com a PagoLivre. Informar um email. A senha será enviada para este email. <br> Este usuário é o que poderá acessar depois o portal da pagolivre caso necessário"
                      value="Usuário do portal (email):"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.emailUserPagoLivre}" size="40"
                         styleClass="tooltipster"
                         title="Informe o usuário para o cadastro do Merchant na integração com a PagoLivre. Informar um email. A senha será enviada para este email. <br> Este usuário é o que poderá acessar depois o portal da pagolivre caso necessário">
            <a4j:support event="onchange"
                         reRender="form"/>
            </h:inputText>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o nome da pessoa do usuário para o cadastro do Usuário na integração com a PagoLivre"
                      value="Nome do usuário (Portal Pagolivre):"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.nomeUserPagoLivre}"
                         title="Informe o nome da pessoa do usuário para o cadastro do Usuário na integração com a PagoLivre"
                         styleClass="tooltipster" size="40"/>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe uma conta bancária ao qual os recebimentos irão cair lá na Pagolivre quando compensados."
                      value="Conta para recebimento:"/>
        <h:selectOneMenu id="contaEmpresaPagoLivre" styleClass="form tooltipster" onblur="blurinput(this);"
                         title="Informe uma conta bancária ao qual os recebimentos irão cair lá na Pagolivre quando compensados."
                         value="#{EmpresaControle.contaCorrentePagoLivre}"
                         onfocus="focusinput(this);">
            <f:selectItems value="#{EmpresaControle.listaSelectItemContaEmpresa}"/>
        </h:selectOneMenu>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Este email aqui é criado com o padrão '...@pagolivre.com.br', mas não se preocupe, o login no portal da PagoLivre será realizado com o email que você preencher lá no campo Usuário do Portal (Email)."
                      value="Email da empresa:"/>
        <h:outputText value="#{EmpresaControle.emailPagoLivre}"
                      styleClass="tooltipster"
                      title="Este email aqui é criado com o padrão '...@pagolivre.com.br', mas não se preocupe, o login no portal da PagoLivre será realizado com o email que você preencher lá no campo Usuário do Portal (Email)."/>

        <h:outputText value=""/>
        <h:panelGroup layout="block" style="padding: 10px 0 10px">
            <a4j:commandLink value="Cadastrar"
                             style="margin: 0"
                             id="integrarPagoLivre"
                             styleClass="botoes nvoBt btSec tooltipster"
                             title="Cadastrar Merchant na PagoLivre e iniciar Integração"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.cadastrarMerchantPagoLivre}"
                             reRender="panelCadastroMerchantPagoLivre"/>
        </h:panelGroup>
    </h:panelGrid>


    <%--EXIBIR CAMPOS QUANDO POSSUI INTEGRAÇÃO--%>
    <h:panelGrid id="panelPagoLivreCadastrado"
                 rendered="#{EmpresaControle.possuiMerchantPagoLivre && !EmpresaControle.editandoMerchantPagoLivre && !EmpresaControle.editandoContaBancariaPagoLivre}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Dados da Integração PagoLivre (Merchant)"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Ambiente informado anteriormente no ato da integração com a PagoLivre"
                      value="Ambiente:"/>
        <h:outputText value="#{EmpresaControle.ambientePagoLivre.descricao}"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="CNPJ da empresa informado anteriormente no ato da integração com a PagoLivre"
                      value="CNPJ:"/>
        <h:outputText value="#{EmpresaControle.merchantPagoLivreDto.cnpj}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Email da empresa informado anteriormente no ato da integração com a PagoLivre.<br>Este email é fictício, no momento você não precisa fazer nada com este email."
                      value="Email da empresa:"/>
        <h:outputText value="#{EmpresaControle.merchantPagoLivreDto.email}"
                      title="Email da empresa informado anteriormente no ato da integração com a PagoLivre.<br>Este email é fictício, no momento você não precisa fazer nada com este email."
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Nome da empresa informado anteriormente no ato da integração com a PagoLivre"
                      value="Nome do Merchant:"/>
        <h:outputText value="#{EmpresaControle.merchantPagoLivreDto.companyName}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Telefone informado anteriormente no ato da integração com a PagoLivre"
                      value="Telefone:"/>
        <h:outputText value="#{EmpresaControle.merchantPagoLivreDto.phoneNumber}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Este é o código Merchant_Id gerado no ato da integração com a PagoLivre"
                      value="Merchant_id:"/>
        <h:outputText id="MerchantIdPagoLivre"
                      value="#{EmpresaControle.merchantIdPagoLivre}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText value="Conta para Recebimento:"
                      title="Esta é a conta que está sendo realizada os recebimentos lá no portal da PagoLivre. É a conta informada no ato da integração. Caso queira alterar, clique no botão 'Alterar'"
                      styleClass="tituloCampos tooltipster"/>

        <h:panelGrid columns="2">
            <h:outputText value="#{EmpresaControle.contaBancariaPagoLivre}"
                          title="Esta é a conta que está sendo realizada os recebimentos lá no portal da PagoLivre. É a conta informada no ato da integração. Caso queira alterar, clique no botão 'Alterar'"
                          styleClass="tituloCampos tooltipster"/>
            <%--BOTÃO ALTERAR CONTA BANCÁRIA--%>
            <a4j:commandLink value="Alterar"
                             id="editarContaBancaria"
                             styleClass="tooltipster"
                             title="Alterar Conta Bancária de recebimentos do Merchant na PagoLivre"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.editandoContaPagoLivre}"
                             reRender="panelPagoLivreAlterar, panelPagoLivreCadastrado, panelPagoLivreBotoes, form"/>
        </h:panelGrid>
        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Este é o usuário (email do usuário) informado anteriormente no ato da integração com a PagoLivre. Utilizado para acessar ao portal da PagoLivre caso desejado.
                            <br>Note que para fazer o login você também precisará informar o 'portaluser_' antes do seu email."
                      value="Usuário do portal PagoLivre:"/>
        <h:outputText value="#{EmpresaControle.emailUserPagoLivre}"
                      title="Este é o usuário (email do usuário) informado anteriormente no ato da integração com a PagoLivre. Utilizado para acessar ao portal da PagoLivre caso desejado.
                            <br>Note que para fazer o login você também precisará informar o 'portaluser_' antes do seu email."
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="A senha do usuário dá acesso ao portal da PagoLivre caso desejado. A senha é enviada para o email do usuário informado no campo anterior a este. caso não tenha recebido, entre em contato com a Pacto"
                      value="Senha:"/>
        <h:outputText value="#{EmpresaControle.textoSenhaPagoLivreOuFacilite}"
                      styleClass="tooltipster"
                      title="A senha do usuário dá acesso ao portal da PagoLivre caso desejado. A senha é enviada para o email do usuário informado no campo anterior a este. caso não tenha recebido, entre em contato com a Pacto"
                      style="font-size: 14px; padding: 5px;"/>
    </h:panelGrid>

    <%--BOTÕES GERAIS--%>
    <h:panelGroup id="panelPagoLivreBotoes"
                  rendered="#{EmpresaControle.possuiMerchantPagoLivre && !EmpresaControle.editandoMerchantPagoLivre && !EmpresaControle.editandoContaBancariaPagoLivre}">

        <div style="margin-left: 35%; margin-top: 10px;">
            <a4j:commandLink value="Editar"
                             id="editarPagoLivre"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.editandoMerchant}"
                             reRender="panelPagoLivreAlterar, panelPagoLivreCadastrado, panelPagoLivreBotoes, form"/>
        </div>
    </h:panelGroup>

    <%--EDITAR--%>
    <h:panelGrid id="panelPagoLivreAlterar"
                 rendered="#{EmpresaControle.editandoMerchantPagoLivre}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Dados do Merchant PagoLivre para atualização"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos" value="Merchant_id:"/>
        <h:outputText id="MerchantIdPagoLivreAlterar"
                      value="#{EmpresaControle.merchantIdPagoLivre}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos" value="Email da empresa:"/>
        <h:inputText value="#{EmpresaControle.emailPagoLivre}" size="40"/>

        <h:outputText styleClass="tituloCampos" value="Nome da empresa:"/>
        <h:inputText value="#{EmpresaControle.nomeEmpresaPagoLivre}" size="40"/>

        <h:outputText styleClass="tituloCampos" value="Telefone:"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.dddTelefonePagoLivre}" size="2" maxlength="2"/>
            <h:inputText value="#{EmpresaControle.telefonePagoLivre }" size="10" maxlength="9"/>
        </h:panelGroup>
    </h:panelGrid>


    <%--ALTERAR SOMENTE CONTA BANCÁRIA--%>
    <h:panelGrid id="panelPagoLivreAlterarConta"
                 rendered="#{EmpresaControle.editandoContaBancariaPagoLivre}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Alterar Conta Bancária de recebimento PagoLivre"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos" value="Merchant_id:"/>
        <h:outputText id="ContaBancariaagoLivreAlterar"
                      value="#{EmpresaControle.merchantIdPagoLivre}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText value="Conta para Recebimento:"
                      title="Selecione a nova conta para recebimento e clique no botão 'Gravar'"
                      styleClass="tituloCampos tooltipster"/>
        <h:selectOneMenu id="contaEmpresaPagoLivreEditar" styleClass="tituloCampos"
                         value="#{EmpresaControle.contaCorrentePagoLivre}">
            <f:selectItems value="#{EmpresaControle.listaSelectItemContaEmpresa}"/>
        </h:selectOneMenu>


    </h:panelGrid>


    <%--BOTÕES EDITAR MERCHANT--%>
    <h:panelGroup id="panelPagoLivreBotoesAlterar"
                  rendered="#{EmpresaControle.editandoMerchantPagoLivre}">

        <div style="margin-left: 35%; margin-top: 10px;">
            <a4j:commandLink value="Gravar"
                             id="alterarMerchant"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.alterarMerchantPagoLivre}"
                             reRender="panelCadastroMerchantPagoLivre"/>

            <a4j:commandLink value="Voltar"
                             id="voltar"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.carregarDadosMerchantPagoLivre}"
                             reRender="panelPagoLivreAlterar, panelPagoLivreCadastrado, panelPagoLivreBotoes, form"/>
        </div>
    </h:panelGroup>

    <%--BOTÕES EDITAR CONTA BANCÁRIA--%>
    <h:panelGroup id="panelPagoLivreBotoesAlterarContaBancaria"
                  rendered="#{EmpresaControle.editandoContaBancariaPagoLivre}">
        <div style="margin-left: 35%; margin-top: 10px;">
            <a4j:commandLink value="Gravar"
                             id="alterarContaMerchant"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.alterarContaBancariaMerchantPagoLivre}"
                             reRender="panelCadastroMerchantPagoLivre"/>

            <a4j:commandLink value="Voltar"
                             id="voltarPrincipal"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.carregarDadosMerchantPagoLivre}"
                             reRender="panelPagoLivreBotoesAlterarContaBancaria, panelPagoLivreCadastrado, panelPagoLivreBotoes, form"/>
        </div>
    </h:panelGroup>
</h:panelGrid>

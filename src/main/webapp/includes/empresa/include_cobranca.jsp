<%--
  Created by IntelliJ IDEA.
  User: lfdeus
  Date: 14/07/2020
  Time: 18:42
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
</style>
<h:panelGroup layout="block">
    <rich:tabPanel id="tabPanelConfigCobranca" width="100%" activeTabClass="true" headerAlignment="rigth"
                   switchType="ajax">
        <rich:tab label="Geral" id="abaGeralConfigCobranca">

            <%--GERAIS--%>
            <h:panelGrid id="panelGridGeralConfigCobranca" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configurações Gerais"/>
                </f:facet>

                <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_ConfiguracaoSistema_urlRecorrencia}:"/>
                    <h:inputText id="urlRecorrencia" size="80" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{EmpresaControle.empresaVO.urlRecorrencia}"/>
                </c:if>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Empresa_diasChequeAVista}"/>
                <h:inputText id="nrDiasChequeAVista" size="10" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{EmpresaControle.empresaVO.nrDiasChequeAVista}"/>

                <h:outputText styleClass="tituloCampos"
                              value="#{msg_aplic.prt_Empresa_diasCompensacaoDebito}"/>
                <h:panelGroup>
                    <h:inputText id="nrDiasCompensacao" size="10" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{EmpresaControle.empresaVO.nrDiasCompensacao}"/>

                    <a4j:commandButton rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                       id="atualizarDtCompCtDebito" action="#{EmpresaControle.confirmarExcluir}"
                                       title="Atualizar pagamentos" reRender="form,mdlMensagemGenerica"
                                       oncomplete="Richfaces.showModalPanel('modalProcessoRealizado'); #{EmpresaControle.msgAlert};"
                                       accesskey="3">
                        <f:param name="metodochamar" value="rodarAtualizacaoPagamentoCartaoDebito"/>
                    </a4j:commandButton>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos tooltipster"
                              title="#{EmpresaControle.cobrancaOnlineAutomaticamenteCaixaEmAberto}"
                              value="Ir automaticamente para a tela de Pagamento Online quando estiver no caixa em aberto (somente parcelas de contratos do tipo Recorrência)"/>
                <h:selectBooleanCheckbox id="irTelaPagamentoCartaoCreditoRecorrente" styleClass="campos"
                                         value="#{EmpresaControle.empresaVO.irTelaPagamentoCartaoCreditoRecorrente}"/>

<%--                <h:outputText styleClass="tituloCampos tooltipster"--%>
<%--                              title="#{EmpresaControle.cobrancaOnlineAutomaticamenteFormaDePagamento}"--%>
<%--                              value="Ir automaticamente para a tela de Pagamento Online quando estiver na tela de Formas de Pagamento (somente recebimento com a forma de pagamento Cartão de Crédito):"/>--%>
<%--                <h:selectBooleanCheckbox id="irTelaPagamentoCartaoCreditoRecorrenteFP" styleClass="campos"--%>
<%--                                         value="#{EmpresaControle.empresaVO.irTelaPagamentoCartaoCreditoFormaPagamento}"/>--%>


                <h:outputText styleClass="tituloCampos"
                              value="Tipos de produto enviar cobrança automática:"/>
                <h:panelGroup>
                    <h:inputText id="tiposProduto" size="60" onblur="blurinput(this);"
                                 disabled="true" onfocus="focusinput(this);" styleClass="form"
                                 value="#{EmpresaControle.empresaVO.tiposProduto}"/>

                    <a4j:commandButton id="btnTiposProduto"
                                       value="Alterar"
                                       reRender="formTipoProdutoEmpresa"
                                       action="#{EmpresaControle.abrirModalTiposProduto}"
                                       oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.onComplete}"/>

                    <a4j:commandLink id="btnAtualizarAutorizacoesCobranca"
                                     value="Atualizar autorizações"
                                     style="padding-left: 10px"
                                     reRender="mdlMensagemGenerica"
                                     oncomplete="#{EmpresaControle.msgAlert};"
                                     action="#{EmpresaControle.atualizarAutorizacoesCobranca}"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCamposNegrito tooltipster"
                              value="Utilizar Retentativa Automática de Múltiplos Convênios de Cobrança:"
                              title="Ao habilitar esta configuração, o sistema irá exibir uma nova aba acima chamada \"Retentativa Automática Múltiplos Convênios de Cobrança\". </br>
                                                     O sistema seguirá as instruções que configurar lá nessa aba mencionada (Convênio de Cobrança X Número de Tentativas)."
                />
                <h:selectBooleanCheckbox styleClass="campos" id="habilitarReenvioAutomaticoRemessa"
                                         value="#{EmpresaControle.empresaVO.habilitarReenvioAutomaticoRemessa}">
                    <a4j:support event="onclick" reRender="form"
                                 action="#{EmpresaControle.marcouHabilitarReenvioAutomaticoRemessa}"/>
                </h:selectBooleanCheckbox>

                <c:if test="${LoginControle.usuarioLogado.usuarioAdminPACTO && EmpresaControle.empresaVO.habilitarReenvioAutomaticoRemessa}">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="<b>RECOMENDADO DEIXAR MARCADA</b></br>Com essa configuração desmarcada, o sistema não irá efetuar a cobrança de
                                  parcelas que nunca tiveram uma cobrança no sistema</br> a não ser que o vencimento dessa parcela que nunca teve tentativa
                                   esteja dentro do período definido na configuração</br> \"Limite de dias após o vencimento para parcela entrar na Retentativa de Cobrança\""
                                  value="Cobrar automaticamente qualquer parcela que nunca teve tentativa de cobrança:"/>
                    <h:selectBooleanCheckbox styleClass="campos tooltipster"
                                             id="cobrarParcelaVencidaSemTentativaCobranca"
                                             title="<b>RECOMENDADO DEIXAR MARCADA</b></br>Com essa configuração desmarcada, o sistema não irá efetuar a cobrança de
                                  parcelas que nunca tiveram uma cobrança no sistema</br> a não ser que o vencimento dessa parcela que nunca teve tentativa
                                   esteja dentro do período definido na configuração</br> \"Limite de dias após o vencimento para parcela entrar na Retentativa de Cobrança\""
                                             value="#{EmpresaControle.empresaVO.cobrarParcelaVencidaSemTentativaCobranca}"/>
                </c:if>

                <c:if test="${!EmpresaControle.empresaVO.habilitarReenvioAutomaticoRemessa}">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  value="Tentar cobrar somente 1 vez automaticamente:"
                                  title="Ao habilitar não será realizado a retentativa de cobrança da parcela.<br/>Será realizada uma única tentativa automática de cobrança da parcela.<br/>
                                              <br/>Essa configuração respeita a configuração \"Limite de dias após o vencimento para parcela entrar na Retentativa de Cobrança\""/>
                    <h:selectBooleanCheckbox styleClass="campos tooltipster" id="tentativaUnicaDeCobranca"
                                             title="Ao habilitar não será realizado a retentativa de cobrança da parcela.<br/>Será realizada uma única tentativa automática de cobrança da parcela.<br/>
                                              <br/>Essa configuração respeita a configuração \"Limite de dias após o vencimento para parcela entrar na Retentativa de Cobrança\" "
                                             value="#{EmpresaControle.empresaVO.tentativaUnicaDeCobranca}"/>
                </c:if>
                <h:outputText styleClass="tituloCampos tooltipster"
                              value="Realizar cobrança no ato da venda:"
                              title="Ao habilitar essa configuração, será realizado a cobrança automática do sistema no ato da negociação do contrato.<br/>"/>

                <h:selectBooleanCheckbox styleClass="campos tooltipster" id="habilitarCobrancaAutomaticaNaVenda"
                                         title="Ao habilitar essa configuração, será realizado a cobrança automática do sistema no ato da negociação do contrato.<br/>"
                                         value="#{EmpresaControle.empresaVO.habilitarCobrancaAutomaticaNaVenda}"/>
            </h:panelGrid>

            <%--CARTÃO--%>
            <h:panelGrid id="panelGridGeralConfigCartao" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configurações de Cartões"/>
                </f:facet>

                <h:outputText styleClass="tooltipster tituloCampos"
                              value="Validar data de vencimento do cartão de crédito ao incluir autorização de cobrança:"/>
                <h:selectBooleanCheckbox styleClass="tooltipster campos"
                                         id="validarVencimentoCartaoAutorizacao"
                                         value="#{EmpresaControle.empresaVO.validarVencimentoCartaoAutorizacao}"/>

                <h:outputText styleClass="tituloCampos tooltipster"
                              value="Agrupar cobranças do mesmo cartão:"
                              title="#{EmpresaControle.empresaVO.agruparCobrancasMesmoCartaoTitle}"/>
                <h:selectBooleanCheckbox styleClass="campos"
                                         id="agruparParcelasPorCartao"
                                         value="#{EmpresaControle.empresaVO.agruparParcelasPorCartao}">
                    <a4j:support event="onclick" reRender="form"/>
                </h:selectBooleanCheckbox>

                <c:if test="${EmpresaControle.empresaVO.agruparParcelasPorCartao}">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  value="Valor máximo da cobrança agrupada do mesmo cartão:"
                                  title="#{EmpresaControle.empresaVO.valorMaximoAgrupamentoTitle}"/>
                    <h:inputText id="agruparParcelasPorCartaoValorLimite"
                                 size="10" maxlength="10" onblur="blurinput(this);"
                                 onkeypress="return formatar_moeda(this,'.',',',event);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{EmpresaControle.empresaVO.agruparParcelasPorCartaoValorLimite}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                </c:if>

                <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  value="Quando houver mais de uma parcela para o mesmo cartão, enviar na próxima tentativa:"
                                  title="#{EmpresaControle.empresaVO.bloqueioMaisDeUmaParcelaMesmoCartaoTitle}"/>
                    <h:selectBooleanCheckbox styleClass="campos"
                                             id="somenteUmEnvioCartaoTentativa"
                                             value="#{EmpresaControle.empresaVO.somenteUmEnvioCartaoTentativa}">
                        <a4j:support event="onclick" reRender="form"/>
                    </h:selectBooleanCheckbox>
                </c:if>

                <h:outputText styleClass="tituloCampos tooltipster"
                              value="Permite cadastrar cartão mesmo com a verificação de cobrança negada:"
                              title="#{EmpresaControle.empresaVO.cadastrarCartaoMesmoAssimTitle}"/>
                <h:selectBooleanCheckbox styleClass="campos"
                                         id="permitircadastrarcartao"
                                         value="#{EmpresaControle.empresaVO.permiteCadastrarCartaoMesmoAssim}"/>

                <h:outputText styleClass="tituloCampos tooltipster"
                              value="Convênio de Cobrança padrão para verificação de cartão:"
                              title="Selecione o convênio que será utilizado para realizar a verificação de cobrança no cartão do cliente.<br/>
                              Caso não seja selecionado um convênio será utilizado o convênio selecionado ao incluir a autorização de cobrança."/>
                <h:selectOneMenu styleClass="form"
                                 id="convenioVerificacaoCartao"
                                 value="#{EmpresaControle.empresaVO.convenioVerificacaoCartao.codigo}">
                    <f:selectItems value="#{EmpresaControle.listaSelectItemConvenioCobrancaVerificacaoCartao}"/>
                </h:selectOneMenu>

            </h:panelGrid>

			<%--BOLETO--%>
            <h:panelGrid id="panelGridGeralConfigBoleto" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configurações de Boleto"/>
                </f:facet>


                <h:outputText styleClass="tituloCampos" value="Permite gerar Boleto no caixa em aberto mesmo sem autorização de cobrança:"/>
                <h:selectBooleanCheckbox styleClass="campos"
                                         value="#{EmpresaControle.empresaVO.gerarBoletoCaixaAberto}"/>

                <h:outputText styleClass="tituloCampos tooltipster"
                              title="#{EmpresaControle.qtdDiasVencBoletoTitle}"
                              value="Dias para calcular o vencimento do Boleto:"/>
                <h:panelGroup>
                    <rich:inputNumberSpinner id="qtdDiasVencimentoBoleto" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form tooltipster"
                                             value="#{EmpresaControle.empresaVO.qtdDiasVencimentoBoleto}" maxValue="999"
                                             minValue="0"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos tooltipster" id="nomeRespBoletoLabel"
                              value="Utilizar Nome e CPF do responsável pelo aluno menor de idade no Boleto: "
                              title="#{EmpresaControle.boletoNomeCPFResponsavelTitle}"/>
                <h:selectBooleanCheckbox styleClass="campos tooltipster" id="nomeRespBoleto"
                                         value="#{EmpresaControle.empresaVO.utilizarNomeResponsavelNoBoleto}"
                                         title="#{EmpresaControle.boletoNomeCPFResponsavelTitle}"/>

                <h:outputText styleClass="tituloCampos tooltipster" id="nomeRespBoletoLabelMaiorIdade"
                              value="Utilizar Nome e CPF do responsável pelo aluno maior de idade no Boleto: "
                              title="#{EmpresaControle.boletoNomeCPFResponsavelMaiorTitle}"/>
                <h:selectBooleanCheckbox styleClass="campos tooltipster" id="nomeRespBoletoMaiorIdade"
                                         value="#{EmpresaControle.empresaVO.utilizarNomeResponsavelNoBoletoMaiorIdade}"
                                         title="#{EmpresaControle.boletoNomeCPFResponsavelMaiorTitle}"/>

                <h:outputText styleClass="tituloCampos" value="Convênio Boleto Padrão:"/>
                <h:panelGroup>
                    <h:selectOneMenu id="comboConvenioBoletoPadrao" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaControle.empresaVO.convenioBoletoPadrao.codigo}">
                        <f:selectItems value="#{EmpresaControle.listaSelectItemConvenioBoleto}"/>
                    </h:selectOneMenu>
                    <a4j:commandButton id="atualizar_comboConvenio"
                                       action="#{EmpresaControle.montarListaSelectItemConvenioBoleto}"
                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                       reRender="form:comboConvenioBoletoPadrao"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos"
                              title="Ao habilitar esta configuração. No CRM, quando for enviado e-mail de cobrança, se o aluno tiver parcela a ser cobrada e não tiver convênio vinculado a ele, o sistema irá vincular o Convênio Boleto Padrão configurado acima."
                              value="Permitir Mailling criar autorização cobrança Boleto ao gerar email:"/>
                <h:selectBooleanCheckbox styleClass="campos"
                                         id="permitirMaillingGerarAutorizacaoCobrancaBoleto"
                                         value="#{EmpresaControle.empresaVO.permitirMaillingGerarAutorizacaoCobrancaBoleto}"/>

                <h:outputText styleClass="tituloCampos"
                              title="Ao habilitar esta configuração. No CRM, quando for enviado e-mail de cobrança, se o aluno tiver parcela a ser cobrada e não tiver boleto gerado ou tiver boleto de parcela vencida, o sistema irá gerar um novo boleto atualizado e enviar para o aluno. Sem essa configuração, a geração de boleto é feita manualmente."
                              value="Permitir Mailling criar Boleto ao enviar email:"/>
                <h:selectBooleanCheckbox styleClass="campos"
                                         id="permiteMaillingCriarBoleto"
                                         value="#{EmpresaControle.empresaVO.permiteMaillingCriarBoleto}"/>

                <h:outputText styleClass="tituloCampos tooltipster"
                              value="Cobrar automaticamente parcela mesmo com Boleto online pendente:"/>
                <h:selectBooleanCheckbox styleClass="campos"
                                         id="cobrarParcelaComBoletoGerado"
                                         value="#{EmpresaControle.empresaVO.cobrarParcelaComBoletoGerado}"/>
            </h:panelGrid>


            <%--PIX--%>
            <h:panelGrid id="panelGridGeralConfigPix" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configurações de Pix"/>
                </f:facet>

                <h:outputText styleClass="tituloCampos tooltipster" id="nomeRespPixLabel"
                              value="Utilizar Nome e CPF do responsável pelo aluno menor de idade no Pix: "
                              title="#{EmpresaControle.pixNomeCPFResponsavelMenorTitle}"/>
                <h:selectBooleanCheckbox styleClass="campos tooltipster" id="nomeRespPix"
                                         value="#{EmpresaControle.empresaVO.utilizarNomeResponsavelNoPixMenorIdade}"
                                         title="#{EmpresaControle.pixNomeCPFResponsavelMenorTitle}"/>

                <h:outputText styleClass="tituloCampos tooltipster" id="nomeRespPixLabelMaiorIdade"
                              value="Utilizar Nome e CPF do responsável pelo aluno maior de idade no Pix: "
                              title="#{EmpresaControle.pixNomeCPFResponsavelMaiorTitle}"/>
                <h:selectBooleanCheckbox styleClass="campos tooltipster" id="nomeRespPixoMaiorIdade"
                                         value="#{EmpresaControle.empresaVO.utilizarNomeResponsavelNoPixMaiorIdade}"
                                         title="#{EmpresaControle.pixNomeCPFResponsavelMaiorTitle}"/>
            </h:panelGrid>


            <h:panelGrid id="panelReenvioCobrancaConfiguracao" columns="1" width="100%"
                         headerClass="subordinado" columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Configuração Retentativa de Cobrança"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita">

                    <h:panelGrid columns="2">
                        <h:graphicImage style="margin-left: 17px"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"
                                        value="/imagens/icon_token_necessary.svg"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      style="display: flex"
                                      title="#{EmpresaControle.empresaVO.qtdDiasLimiteCobrancaParcelasRecorrencia_Title}"
                                      value="Limite de dias após o vencimento para parcela entrar na Retentativa de Cobrança:"/>
                    </h:panelGrid>

                    <h:panelGroup layout="block" style="display: flex;">
                        <rich:inputNumberSpinner id="qtdDiasLimiteCobrancaParcelasRecorrencia"
                                                 inputSize="2"
                                                 value="#{EmpresaControle.empresaVO.qtdDiasLimiteCobrancaParcelasRecorrencia}"
                                                 minValue="0" maxValue="999"/>
                        <h:outputText styleClass="tituloCampos"
                                      style="padding-left: 5px"
                                      value="dias"/>
                    </h:panelGroup>

                    <h:panelGrid columns="2" style="display: inline;">
                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      title="#{EmpresaControle.empresaVO.qtdDiasRepetirCobrancaParcelasRecorrencia_Title}"
                                      value="Intervalo de dias para Retentativa de Cobrança da parcela:"/>
                    </h:panelGrid>


                    <h:panelGroup layout="block" style="display: flex;">
                        <rich:inputNumberSpinner id="qtdDiasRepetirCobrancaParcelasRecorrencia"
                                                 inputSize="2"
                                                 value="#{EmpresaControle.empresaVO.qtdDiasRepetirCobrancaParcelasRecorrencia}"
                                                 minValue="0" maxValue="99"/>
                        <h:outputText styleClass="tituloCampos"
                                      style="padding-left: 5px"
                                      value="dias"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </rich:tab>

        <rich:tab label="Multa/Juros" id="abaGeralMultaJuros">

            <h:panelGrid id="panelGridGeralConfigMultaJuros" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configurações de Multa e Juros"/>
                </f:facet>

                <h:outputText styleClass="tituloCampos" value="Cobrança de Multa/Juros:"/>
                <h:selectBooleanCheckbox id="cobrancaMultaJuras" styleClass="campos"
                                         value="#{EmpresaControle.empresaVO.cobrarAutomaticamenteMultaJuros}">
                    <a4j:support event="onchange" reRender="form" focus="multaCobrancaAutomatica"/>
                </h:selectBooleanCheckbox>

                <c:if test="${EmpresaControle.empresaVO.cobrarAutomaticamenteMultaJuros}">

                    <h:outputText styleClass="tituloCampos"
                                  value="Cobrança de Multa/Juros - Pix:"/>
                    <h:selectBooleanCheckbox id="cobrarMultaJurosPix" styleClass="campos"
                                             value="#{EmpresaControle.empresaVO.cobrarMultaJurosPix}"/>

                    <h:outputText styleClass="tituloCampos"
                                  value="Cobrança de Multa/Juros - Cartão de Crédito (DCC - Transação Online):"/>
                    <h:selectBooleanCheckbox id="cobrarMultaJurosTransacao" styleClass="campos"
                                             value="#{EmpresaControle.empresaVO.cobrarMultaJurosTransacao}"/>

                    <h:outputText styleClass="tituloCampos"
                                  value="Cobrança de Multa/Juros - Cartão de Crédito (DCC - EDI):"/>
                    <h:selectBooleanCheckbox id="cobrarMultaJurosDCC" styleClass="campos"
                                             value="#{EmpresaControle.empresaVO.cobrarMultaJurosDCC}"/>

                    <h:outputText styleClass="tituloCampos" value="Cobrança de Multa/Juros - Débito em conta (DCO):"/>
                    <h:selectBooleanCheckbox id="cobrarMultaJurosDCO" styleClass="campos"
                                             value="#{EmpresaControle.empresaVO.cobrarMultaJurosDCO}"/>

                    <h:outputText styleClass="tituloCampos" value="Multa por parcela atrasada:"/>
                    <h:panelGroup layout="block">
                        <h:inputText id="multaCobrancaAutomatica" size="4" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaControle.empresaVO.multaCobrancaAutomatica}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>

                        <h:selectBooleanCheckbox id="utilizarMultaValorAbsoluto"
                                                 value="#{EmpresaControle.empresaVO.utilizarMultaValorAbsoluto}"/>
                        <h:outputText styleClass="tituloCampos" value="Valor Absoluto"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Juros (dia) por parcela atrasada:"/>
                    <h:panelGroup layout="block">
                        <h:inputText id="jurosCobrancaAutomatica" size="4" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaControle.empresaVO.jurosCobrancaAutomatica}">
                            <f:converter converterId="FormatadorNumerico7Casa"/>
                        </h:inputText>

                        <h:selectBooleanCheckbox id="utilizarJurosValorAbsoluto"
                                                 value="#{EmpresaControle.empresaVO.utilizarJurosValorAbsoluto}"/>
                        <h:outputText styleClass="tituloCampos" value="Valor Absoluto"/>

                    </h:panelGroup>

                    <%--Multa/Juros Asaas--%>
                    <h:outputText styleClass="tituloCampos tooltipster" value="Cobrança de Multa/Juros - Integração ASAAS:"
                                  title="#{EmpresaControle.multaJurosAsaasTitle}"/>
                    <h:selectBooleanCheckbox id="cobrarMultaJurosAsaas" styleClass="campos tooltipster"
                                             title="#{EmpresaControle.multaJurosAsaasTitle}"
                                             value="#{EmpresaControle.empresaVO.cobrarMultaJurosAsaas}">
                        <a4j:support event="onchange" reRender="form" focus="multaAsaas"/>
                    </h:selectBooleanCheckbox>

                    <c:if test="${EmpresaControle.empresaVO.cobrarMultaJurosAsaas}">
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      value="Multa por parcela atrasada Asaas (porcentagem):"
                                      title="A multa será somada ao valor da parcela caso o pagamento seja feito após a data do vencimento. Disponível apenas em porcentagem. Utilize ',' caso o valor seja quebrado."/>
                        <h:panelGroup layout="block">
                            <h:inputText id="multaAsaas" size="4" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         title="A multa será somada ao valor da parcela caso o pagamento seja feito após a data do vencimento.. Disponível apenas em porcentagem. Utilize ',' caso o valor seja quebrado."
                                         value="#{EmpresaControle.empresaVO.valorMultaAsaas}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos tooltipster"
                                      value="Juros (dia) por parcela atrasada Asaas (porcentagem):"
                                      title="Os juros acumulativos serão somados diariamente ao valor da parcela até o pagamento. Disponível apenas em porcentagem. Utilize ',' caso o valor seja quebrado."/>
                        <h:panelGroup layout="block">
                            <h:inputText id="jurosAsaas" size="4" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form tooltipster"
                                         title="Os juros acumulativos serão somados diariamente ao valor da parcela até o pagamento. Disponível apenas em porcentagem. Utilize ',' caso o valor seja quebrado."
                                         value="#{EmpresaControle.empresaVO.valorJurosAsaas}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:inputText>
                        </h:panelGroup>

                    </c:if>
                </c:if>
            </h:panelGrid>
        </rich:tab>

        <rich:tab label="Retentativa Automática Múltiplos Convênios de Cobrança"
                  rendered="#{EmpresaControle.empresaVO.habilitarReenvioAutomaticoRemessa}">

            <h:panelGrid id="panelacoes" columns="1" width="100%"
                         headerClass="subordinado" columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Ações"/>
                </f:facet>

            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita">
                <h:panelGroup id="labelTipoExecucaoRetentativa">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  value="Quantidade de tentativas de cobrança da parcela:"
                                  title="Este campo define quantas vezes o sistema irá tentar cobrar uma parcela de forma automática.</br>
                                       <strong>Obs.</strong>: Lembrando que a parcela não pode ter tido um retorno de bloqueio definitivo (Cartão bloqueado, vencido, etc)."/>
                </h:panelGroup>
                <h:panelGroup id="comboQtdVezesExecucao">
                    <rich:inputNumberSpinner id="spinnerQtdExecucoesRetentativa"
                                             value="#{EmpresaControle.empresaVO.qtdExecucoesRetentativa}"
                                             maxValue="999"
                                             minValue="0"/>
                </h:panelGroup>
                    <c:if test="${LoginControle.usuarioLogado.usuarioPactoSolucoes}">
                        <h:outputText styleClass="tituloCampos"
                                      value="Replicar configuração para outras empresas:"/>
                        <a4j:commandLink id="preparaReplicarConfiguracaoRetentativa"
                                         action="#{EmpresaControle.preparaReplicarConfiguracaoRetentativa}"
                                         oncomplete="Richfaces.showModalPanel('modalReplicarRetentativa')"
                                         reRender="panelReenvioCobranca, panelErroMensagem, modalReplicarRetentativa"
                                         value="Replicar configuração" styleClass="botoes"/>

                        <h:outputText styleClass="tituloCampos"
                                      value="Atualizar autorização de cobrança dos alunos:"/>
                        <a4j:commandLink id="atualizarAutorizacaoCobrancaCliente"
                                         onclick="Richfaces.showModalPanel('modalAtualizarAutorizacao')"
                                         reRender="panelReenvioCobranca, panelErroMensagem"
                                         value="Atualizar alunos já lançados" styleClass="botoes"/>
                    </c:if>
                </h:panelGrid>
            </h:panelGrid>


            <h:panelGrid id="panelReenvioCobranca" columns="1" width="100%" headerClass="subordinado"
                         columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Retentativa Automática Múltiplos Convênios de Cobrança"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita">

                    <h:outputText styleClass="tituloCampos" value="Convênio de Cobrança:"/>
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     value="#{EmpresaControle.configuracaoReenvioMovParcelaEmpresaVO.convenioCobrancaVO.codigo}">
                        <a4j:support event="onchange" reRender="panelReenvioCobranca"
                                     action="#{EmpresaControle.selecionarConvenioCobranca}"/>
                        <f:selectItems value="#{EmpresaControle.listaSelectConvenioCobrancaRetentativa}"/>
                    </h:selectOneMenu>
                </h:panelGrid>

                <a4j:commandButton action="#{EmpresaControle.adicionarConfiguracaoReenvioMovParcelaEmpresa}"
                                   reRender="tabPanelConfigCobranca, panelErroMensagem"
                                   oncomplete="#{EmpresaControle.mensagemNotificar}"
                                   image="./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada"
                             style="text-align: center">
                    <h:dataTable id="configuracaoReenvioMovParcelaEmpresaVOS" width="100%"
                                 headerClass="subordinado" styleClass="tabFormSubordinada"
                                 rowClasses="linhaImpar, linhaPar"
                                 columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                                 value="#{EmpresaControle.empresaVO.configuracaoReenvioMovParcelaEmpresaVOS}"
                                 var="reenvio">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Posição"/>
                            </f:facet>
                            <h:outputText value="#{reenvio.posicao}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Convênio"/>
                            </f:facet>
                            <h:outputText value="#{reenvio.convenioCobrancaVO.descricao}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Tipo Convênio"/>
                            </f:facet>
                            <h:outputText value="#{reenvio.convenioCobrancaVO.tipo.descricao}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Forma de Envio"/>
                            </f:facet>
                            <h:outputText value="#{reenvio.tipoConvenio}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_bt.btn_opcoes}"/>
                            </f:facet>
                            <h:panelGroup layout="block" style="display: inline-flex; align-items: center;">
                                <a4j:commandButton id="moverConfiguracaoReenvioParaCima"
                                                   style="margin-right: 5px"
                                                   action="#{EmpresaControle.moverConfiguracaoReenvioParaCima}"
                                                   oncomplete="#{EmpresaControle.mensagemNotificar}"
                                                   reRender="tabPanelConfigCobranca, panelErroMensagem"
                                                   styleClass="botoes" image="./imagens/setaCima.png"/>

                                <a4j:commandButton id="moverConfiguracaoReenvioParaBaixo"
                                                   style="margin-right: 10px"
                                                   action="#{EmpresaControle.moverConfiguracaoReenvioParaBaixo}"
                                                   oncomplete="#{EmpresaControle.mensagemNotificar}"
                                                   reRender="tabPanelConfigCobranca, panelErroMensagem"
                                                   styleClass="botoes" image="./imagens/setaBaixo.png"/>

                                <a4j:commandButton
                                        action="#{EmpresaControle.removerConfiguracaoReenvioMovParcelaEmpresa}"
                                        oncomplete="#{EmpresaControle.mensagemNotificar}"
                                        reRender="tabPanelConfigCobranca, panelErroMensagem"
                                        image="./imagens/sair.gif" styleClass="botoes"/>
                            </h:panelGroup>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>
            </h:panelGrid>
        </rich:tab>

        <rich:tab label="Link de Pagamento e de Cadastro de Cartão (Tela do Aluno, Mailing, Outros)">
            <h:panelGrid id="cobrancaLinkPagamento" columns="1" width="100%"
                         headerClass="subordinado" columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Configurações válidas para Link de Pagamento / Link de Cadastro de Cartão"
                    styleClass="tooltipster"
                    title="As configurações presentes aqui são válidas para o Link de Pagamento e também para o Link de Cadastro de Cartão"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita">

                    <h:panelGrid columns="2" style="display: inline;">
                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      title="<b>Configuração válida para 'Link Cadastrar Cartão Online' e 'Link para Pagamento Online.</b></br></br>Defina o convênio de cobrança padrão que será usado tanto para o link de pagamento quanto para o link de cadastro de cartão"
                                      value="Convênio de cobrança Cartão de Crédito:"/>
                    </h:panelGrid>

                    <h:panelGroup>
                        <h:selectOneMenu id="comboConvenioCobrancaCartao" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{EmpresaControle.empresaVO.convenioCobrancaCartao.codigo}">
                            <f:selectItems value="#{EmpresaControle.listaSelectItemConvenioCartao}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_conveniosCartao"
                                           action="#{EmpresaControle.atualizarListaConvenioCobranca}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:cobrancaLinkPagamento"/>
                    </h:panelGroup>

                    <h:panelGrid columns="2" style="display: inline;">
                        <h:graphicImage style="margin-left: 14px;"
                                        value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      style="display: flex;"
                                      value="Tipo de parcelas a cobrar padrão:"
                                      title="<b>Configuração válida para 'Link Cadastrar Cartão Online' e 'Link para Pagamento Online.</b></br></br> Informe o tipo de parcelas a cobrar que será utilizado como padrão quando um aluno cadastrar um cartão através do 'Link Cadastrar Cartão Online'</br> ou realizar um pagamento através do 'Link para Pagamento Online'"/>
                    </h:panelGrid>

                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="listaSuspensa"
                                     value="#{EmpresaControle.empresaVO.tipoParcelasCobrarVendaSite}">
                        <f:selectItems value="#{EmpresaControle.listaSelectItemtipoParcelasCobrarVendaSite}"/>
                    </h:selectOneMenu>

                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="linkpgtoconfigs" columns="1" width="100%"
                         headerClass="subordinado" columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Configurações válidas apenas para Link de Pagamento"
                                  styleClass="tooltipster"
                                  title="As configurações presentes aqui são válidas somente para o Link de Pagamento"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita">

                    <h:panelGrid columns="2" style="display: inline">
                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      title="<b>Configuração válida apenas para 'Link para Pagamento Online'.</b></br></br>Defina o convênio de cobrança padrão que será usado para geração de QRCode pix"
                                      value="Convênio de cobrança Pix:"/>
                    </h:panelGrid>

                    <h:panelGroup>
                        <h:selectOneMenu id="comboConvenioCobrancaPix" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{EmpresaControle.empresaVO.convenioCobrancaPix.codigo}">
                            <f:selectItems value="#{EmpresaControle.listaSelectItemConvenioPix}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_conveniosPix"
                                           action="#{EmpresaControle.atualizarListaConvenioCobranca}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:cobrancaLinkPagamento"/>
                    </h:panelGroup>

                    <h:panelGrid columns="2" style="display: inline">
                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      title="<b>Configuração válida apenas para 'Link para Pagamento Online'.</b></br></br>Defina o convênio de cobrança padrão que será usado para geração de Boletos"
                                      value="Convênio de cobrança Boleto:"/>
                    </h:panelGrid>

                    <h:panelGroup>
                        <h:selectOneMenu id="comboConvenioCobrancaBoleto" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{EmpresaControle.empresaVO.convenioCobrancaBoleto.codigo}">
                            <f:selectItems value="#{EmpresaControle.listaSelectItemConvenioBoletoOnline}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_conveniosBoleto"
                                           action="#{EmpresaControle.atualizarListaConvenioCobranca}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:cobrancaLinkPagamento"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="<b>Configuração válida apenas para 'Link para Pagamento Online'.</b></br></br>Com essa configuração <b>marcada</b>,
                                   após o sucesso de cada pagamento via cartão ou via pix através do link de pagamento, será enviado um email de sucesso de pagamento ao aluno.</br>Com essa configuração <b>desmarcada</b> não será enviado nenhum email."
                                  value="Enviar o comprovante de pagamento para o e-mail:"/>
                    <h:selectBooleanCheckbox styleClass="campos tooltipster" id="config"
                                             title="<b>Configuração válida apenas para 'Link para Pagamento Online'.</b></br></br>Com essa configuração <b>marcada</b>,
                                   após o sucesso de cada pagamento via cartão ou via pix através do link de pagamento, será enviado um email de sucesso de pagamento ao aluno.</br>Com essa configuração <b>desmarcada</b> não será enviado nenhum email."
                                             value="#{EmpresaControle.empresaVO.enviarEmailPagamento}"/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="linkcadcartaoconfigs" columns="1" width="100%"
                         headerClass="subordinado" columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Configurações válidas apenas para Link de Cadastro de Cartão"
                                  styleClass="tooltipster"
                                  title="As configurações presentes aqui são válidas somente para o Link de Cadastro de Cartão"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Com essa configuração <b>marcada</b>, ao cadastrar um novo cartão pelo link de cadastro de cartão, a autorização de cobrança do aluno será incluída com as cobranças automáticas bloqueadas.</br>
                                  Essa configuração geralmente é marcada quando a empresa não quer realizar nenhuma cobrança automática até que o aluno assine o contrato dele. </br>
                                  Com essa configuração <b>desmarcada</b>, todas as autorizações já serão incluídas com cobranças automáticas desbloqueadas."
                                  value="Gerar autorização de cobrança com cobrança automática Bloqueada:"/>
                    <h:selectBooleanCheckbox styleClass="campos tooltipster"
                                             id="cobautbloqueada"
                                             title="Com essa configuração <b>marcada</b>, ao cadastrar um novo cartão pelo link de cadastro de cartão, a autorização de cobrança do aluno será incluída com as cobranças automáticas bloqueadas.</br>
                                  Essa configuração geralmente é marcada quando a empresa não quer realizar nenhuma cobrança automática até que o aluno assine o contrato dele. </br>
                                  Com essa configuração <b>desmarcada</b>, todas as autorizações já serão incluídas com cobranças automáticas desbloqueadas."
                                             value="#{EmpresaControle.empresaVO.gerarAutCobrancaComCobAutBloqueada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </rich:tab>


        <rich:tab label="Link de Pagamento e de Cadastro de Cartão (Régua de Cobrança)">
            <h:panelGrid id="cobrancaLinkPagamentoRegua" columns="1" width="100%"
                         headerClass="subordinado" columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Configurações válidas para Link de Pagamento / Link de Cadastro de Cartão"
                                  styleClass="tooltipster"
                                  title="As configurações presentes aqui são válidas para o Link de Pagamento e também para o Link de Cadastro de Cartão"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita">

                    <h:panelGrid columns="2" style="display: inline;">
                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      title="<b>Configuração válida para 'Link Cadastrar Cartão Online' e 'Link para Pagamento Online.</b></br></br>Defina o convênio de cobrança padrão que será usado tanto para o link de pagamento quanto para o link de cadastro de cartão"
                                      value="Convênio de cobrança Cartão de Crédito:"/>
                    </h:panelGrid>

                    <h:panelGroup>
                        <h:selectOneMenu id="comboConvenioCobrancaCartaoRegua" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{EmpresaControle.empresaVO.convenioCobrancaCartaoRegua.codigo}">
                            <f:selectItems value="#{EmpresaControle.listaSelectItemConvenioCartaoRegua}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_conveniosCartaoRegua"
                                           action="#{EmpresaControle.atualizarListaConvenioCobranca}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:cobrancaLinkPagamentoRegua"/>
                    </h:panelGroup>

                    <h:panelGrid columns="2" style="display: inline;">
                        <h:graphicImage style="margin-left: 14px;"
                                        value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      style="display: flex;"
                                      value="Tipo de parcelas a cobrar padrão:"
                                      title="<b>Configuração válida para 'Link Cadastrar Cartão Online' e 'Link para Pagamento Online.</b></br></br> Informe o tipo de parcelas a cobrar que será utilizado como padrão quando um aluno cadastrar um cartão através do 'Link Cadastrar Cartão Online'</br> ou realizar um pagamento através do 'Link para Pagamento Online'"/>
                    </h:panelGrid>

                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="listaSuspensa"
                                     value="#{EmpresaControle.empresaVO.tipoParcelasCobrarVendaSiteRegua}">
                        <f:selectItems value="#{EmpresaControle.listaSelectItemtipoParcelasCobrarVendaSiteRegua}"/>
                    </h:selectOneMenu>

                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="linkpgtoconfigsRegua" columns="1" width="100%"
                         headerClass="subordinado" columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Configurações válidas apenas para Link de Pagamento"
                                  styleClass="tooltipster"
                                  title="As configurações presentes aqui são válidas somente para o Link de Pagamento"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita">

                    <h:panelGrid columns="2" style="display: inline">
                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      title="<b>Configuração válida apenas para 'Link para Pagamento Online'.</b></br></br>Defina o convênio de cobrança padrão que será usado para geração de QRCode pix"
                                      value="Convênio de cobrança Pix:"/>
                    </h:panelGrid>

                    <h:panelGroup>
                        <h:selectOneMenu id="comboConvenioCobrancaPixRegua" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{EmpresaControle.empresaVO.convenioCobrancaPixRegua.codigo}">
                            <f:selectItems value="#{EmpresaControle.listaSelectItemConvenioPixRegua}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_conveniosPixRegua"
                                           action="#{EmpresaControle.atualizarListaConvenioCobranca}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:cobrancaLinkPagamento"/>
                    </h:panelGroup>

                    <h:panelGrid columns="2" style="display: inline">
                        <h:graphicImage value="/imagens/icon_token_necessary.svg"
                                        styleClass="tooltipster"
                                        title="Necessário código de autenticação para alterar"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      title="<b>Configuração válida apenas para 'Link para Pagamento Online'.</b></br></br>Defina o convênio de cobrança padrão que será usado para geração de Boletos"
                                      value="Convênio de cobrança Boleto:"/>
                    </h:panelGrid>

                    <h:panelGroup>
                        <h:selectOneMenu id="comboConvenioCobrancaBoletoRegua" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="form"
                                         value="#{EmpresaControle.empresaVO.convenioCobrancaBoletoRegua.codigo}">
                            <f:selectItems value="#{EmpresaControle.listaSelectItemConvenioBoletoOnlineRegua}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_conveniosBoletoRegua"
                                           action="#{EmpresaControle.atualizarListaConvenioCobranca}"
                                           image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                           reRender="form:cobrancaLinkPagamento"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="<b>Configuração válida apenas para 'Link para Pagamento Online'.</b></br></br>Com essa configuração <b>marcada</b>,
                                   após o sucesso de cada pagamento via cartão ou via pix através do link de pagamento, será enviado um email de sucesso de pagamento ao aluno.</br>Com essa configuração <b>desmarcada</b> não será enviado nenhum email."
                                  value="Enviar o comprovante de pagamento para o e-mail:"/>
                    <h:selectBooleanCheckbox styleClass="campos tooltipster" id="configRegua"
                                             title="<b>Configuração válida apenas para 'Link para Pagamento Online'.</b></br></br>Com essa configuração <b>marcada</b>,
                                   após o sucesso de cada pagamento via cartão ou via pix através do link de pagamento, será enviado um email de sucesso de pagamento ao aluno.</br>Com essa configuração <b>desmarcada</b> não será enviado nenhum email."
                                             value="#{EmpresaControle.empresaVO.enviarEmailPagamentoRegua}"/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="linkcadcartaoconfigsRegua" columns="1" width="100%"
                         headerClass="subordinado" columnClasses="colunaCentralizada">

                <f:facet name="header">
                    <h:outputText value="Configurações válidas apenas para Link de Cadastro de Cartão"
                                  styleClass="tooltipster"
                                  title="As configurações presentes aqui são válidas somente para o Link de Cadastro de Cartão"/>
                </f:facet>

                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Com essa configuração <b>marcada</b>, ao cadastrar um novo cartão pelo link de cadastro de cartão, a autorização de cobrança do aluno será incluída com as cobranças automáticas bloqueadas.</br>
                                  Essa configuração geralmente é marcada quando a empresa não quer realizar nenhuma cobrança automática até que o aluno assine o contrato dele. </br>
                                  Com essa configuração <b>desmarcada</b>, todas as autorizações já serão incluídas com cobranças automáticas desbloqueadas."
                                  value="Gerar autorização de cobrança com cobrança automática Bloqueada:"/>
                    <h:selectBooleanCheckbox styleClass="campos tooltipster"
                                             id="cobautbloqueadaRegua"
                                             title="Com essa configuração <b>marcada</b>, ao cadastrar um novo cartão pelo link de cadastro de cartão, a autorização de cobrança do aluno será incluída com as cobranças automáticas bloqueadas.</br>
                                  Essa configuração geralmente é marcada quando a empresa não quer realizar nenhuma cobrança automática até que o aluno assine o contrato dele. </br>
                                  Com essa configuração <b>desmarcada</b>, todas as autorizações já serão incluídas com cobranças automáticas desbloqueadas."
                                             value="#{EmpresaControle.empresaVO.gerarAutCobrancaComCobAutBloqueadaRegua}"/>
                </h:panelGrid>
            </h:panelGrid>
        </rich:tab>

        <rich:tab label="PJBank" id="tabPJBank" action="#{EmpresaControle.carregarDadosIntegracaoPJBank}">
            <jsp:include flush="true" page="/includes/empresa/include_credenciar_empresa_pjbank.jsp" />
        </rich:tab>

        <rich:tab label="Pagolivre" id="tabPagoLivre" action="#{EmpresaControle.carregarDadosMerchantPagoLivre}" oncomplete="#{EmpresaControle.mensagemNotificar}">
            <jsp:include flush="true" page="/includes/empresa/include_cadastrar_empresa_pagolivre.jsp" />
        </rich:tab>

<%--        não apresentar até Pacto finalizar parceria com Asaas (solicitado remoção pelo Leonardo)--%>
<%--        <rich:tab label="Asaas" id="tabAsaas" action="#{EmpresaControle.carregarDadosEmpresaAsaas}" oncomplete="#{EmpresaControle.mensagemNotificar}">--%>
<%--            <jsp:include flush="true" page="/includes/empresa/include_cadastrar_empresa_asaas.jsp" />--%>
<%--        </rich:tab>--%>


    </rich:tabPanel>
</h:panelGroup>

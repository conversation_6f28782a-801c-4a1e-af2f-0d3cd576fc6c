<%-- 
    Document   : include_combo_empresa
    Created on : 11/01/2012, 12:35:31
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="../imports.jsp" %>
<%--Espera-se que a página que inclui este include também inclua no seu header o JavaScript 'script.js'--%>

<h:panelGroup>
    <h:outputText style="vertical-align:middle;" styleClass="tituloCampos" value="Empresa:" />
    <rich:spacer width="10px" />
    <h:selectOneMenu  onblur="blurinput(this);"
                      style="margin:6 6 6 6;"
                      onfocus="focusinput(this);"
                      value="#{ComboBoxEmpresaControle.empresaVO.codigo}" >
        <f:selectItems  value="#{ComboBoxEmpresaControle.listaSelectItemEmpresa}" />
        <a4j:support event="onchange" action="#{ComboBoxEmpresaControle.selecionarEmpresa}" status="statusInComponent"
                     oncomplete="#{ComboBoxEmpresaControle.gatilhosFuncoes}"/>
    </h:selectOneMenu>
    <rich:spacer width="10px"/>    
</h:panelGroup>

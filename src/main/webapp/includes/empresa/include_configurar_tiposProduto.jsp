<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<style>
    .rich-picklist-list-content {
        width: 200px !important;
        height: auto !important;
        overflow: hidden !important;
        min-height: 320px !important;
    }
</style>
<a4j:outputPanel>
    <rich:modalPanel id="modalTipoProdutoEmpresa"
                     styleClass="novaModal noMargin" shadowOpacity="true"
                     width="650" top="65" autosized="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Alterar tipos de produtos que emitem cobrança automática"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalTipoProdutoEmpresa"/>
                <rich:componentControl for="modalTipoProdutoEmpresa" attachTo="hidelinkModalTipoProdutoEmpresa"
                                       operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formTipoProdutoEmpresa">
            <h:panelGroup id="painelConfigTipoProdutoAuto" layout="block"
                          style="margin: 20px 10px;">
                <rich:pickList value="#{EmpresaControle.tiposProdutosSelecionados}"
                               style="width: 100%"
                               copyAllControlLabel="Adicionar todos"
                               copyControlLabel="Adicionar selecionado"
                               removeAllControlLabel="Remover todos"
                               removeControlLabel="Remover selecionado">
                    <f:selectItems value="#{EmpresaControle.tiposProdutosDisponiveis}"/>
                </rich:pickList>

                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink action="#{EmpresaControle.confirmarTiposProduto}"
                                     oncomplete="#{EmpresaControle.mensagemNotificar};#{EmpresaControle.onComplete}"
                                     reRender="form:tiposProduto"
                                     value="Confirmar"
                                     styleClass="botaoPrimario texto-size-14-real"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

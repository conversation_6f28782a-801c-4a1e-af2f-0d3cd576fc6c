<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>

<div style="margin-left: 43%;">
    <h:graphicImage id="iconPjBank"
                    style="margin-left: 10px;"
                    styleClass="tooltipster"
                    width="87px"
                    value="../imagens/pjbank-logo.png"
                    title="Para criar automaticamente uma conta na PJBank e obter as credenciais, informe os dados abaixo e clique no botão 'Realizar integração'.
                    </br>A PJBank possui soluções de Boleto e Pix "/>
</div>

<h:panelGrid width="100%" columns="1" id="panelIntegrarPJBankCredenciais">

    <h:panelGrid id="panelCredenciamentoPjbank"
                 rendered="#{!EmpresaControle.possuiCredenciaisPJBank}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Realizar Integração com a PJBank"/>
        </f:facet>

        <h:outputText styleClass="tooltipster"
                      value="Selecione uma Conta para Recebimento:"
                      title="Informe a conta bancária na qual você deseja receber os repasses da PJbank."/>
        <h:selectOneMenu id="contaEmpresaReceberPjbank" styleClass="form" onblur="blurinput(this);"
                         value="#{EmpresaControle.empresaVO.valueConta}"
                         onfocus="focusinput(this);">
            <f:selectItems value="#{EmpresaControle.listaSelectItemContaEmpresa}"/>
        </h:selectOneMenu>

        <h:outputText value="Telefone:"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.empresaVO.ddPjbank}" size="2" maxlength="2"/>
            <h:inputText value="#{EmpresaControle.empresaVO.fonePjbank }" size="10" maxlength="9"/>
        </h:panelGroup>

        <h:outputText value="Email:"/>
        <h:inputText value="#{EmpresaControle.empresaVO.emailPjbank}" size="40"/>

        <h:outputText value=""/>
        <h:panelGroup layout="block" style="margin-top: 10px">
            <a4j:commandLink value="Realizar integração"
                             style="margin: 0"
                             id="integrarPjBank"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.gerarCredencialPJBank}"
                             reRender="form"/>
        </h:panelGroup>
    </h:panelGrid>


    <h:panelGrid id="panelPjbankCredenciais"
                 rendered="#{EmpresaControle.possuiCredenciaisPJBank}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Integração PJBank"/>
        </f:facet>

        <h:outputText id="empresapjbank" styleClass="tituloCampos" value="Nome Fantasia:"/>
        <h:outputText id="emprespjbank" style="font-size: 14px; padding: 5px;"
                      value="#{EmpresaControle.empresaLogado.nome}"/>

        <h:outputText styleClass="tituloCampos" value="Razão Social:"/>
        <h:outputText value="#{EmpresaControle.empresaVO.razaoSocial}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText id="cnpjEmpresapjbank" styleClass="tituloCampos" value="CNPJ:"/>
        <h:outputText id="cnpjEmprespjbank" value="#{EmpresaControle.empresaVO.CNPJ}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ChavePJBANK}:"/>
        <h:outputText id="chavePJBank" value="#{EmpresaControle.empresaVO.convenioBoletoPadrao.chavePJBank}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_CredencialPJBANK}:"/>
        <h:outputText id="credencialPJBank"
                      value="#{EmpresaControle.empresaVO.convenioBoletoPadrao.credencialPJBank}"
                      style="font-size: 14px; padding: 5px;"/>
    </h:panelGrid>
</h:panelGrid>

<%@page contentType="text/html"  %>
<%@page pageEncoding="ISO-8859-1" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
</style>
<h:panelGroup layout="block">
    <rich:tabPanel id="tabPanelNotaFiscal" width="100%" activeTabClass="true" headerAlignment="rigth" switchType="ajax">
        <rich:tab label="Geral" id="abaGeralModuloNotas">

            <h:panelGrid id="habilitarNotaFiscal" columns="2" rowClasses="linhaImpar, linhaPar"
                         rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Habilitar Nota Fiscal"/>
                </f:facet>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}" value="Chave Módulo Notas: "/>
                <h:inputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}" size="60" id="chaveNFSe"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{EmpresaControle.empresaVO.chaveNFSe}"/>

                <h:outputText value="Usar NFS-e:"/>
                <h:selectBooleanCheckbox styleClass="campos" id="nfse"
                                         value="#{EmpresaControle.empresaVO.usarNFSe}">
                    <a4j:support event="onclick" reRender="form"/>
                </h:selectBooleanCheckbox>


                <h:outputText value="Usar NFC-e:"/>
                <h:selectBooleanCheckbox styleClass="campos" id="nfce"
                                         value="#{EmpresaControle.empresaVO.usarNFCe}">
                    <a4j:support event="onclick" reRender="form"/>
                </h:selectBooleanCheckbox>

            </h:panelGrid>

            <h:panelGrid id="configEmissaoNFSe" columns="2" rowClasses="linhaImpar, linhaPar"
                         rendered="#{EmpresaControle.empresaVO.usarNFSe}"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configuração Nota Fiscal - NFSe"/>
                </f:facet>

                <h:outputText value="Configuração padrão de Nota Fiscal NFSe:"/>
                <h:panelGroup layout="block">
                    <h:selectOneMenu id="configuracaoNotaFiscalNFSe" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{EmpresaControle.empresaVO.configuracaoNotaFiscalNFSe.codigo}">
                        <f:selectItems value="#{EmpresaControle.listaConfigEmissaoNFSe}"/>
                        <a4j:support event="onchange" reRender="form"
                                     action="#{EmpresaControle.preencherConfigEmissao}"/>
                    </h:selectOneMenu>
                    <a4j:commandButton id="montarComboConfigEmissaoNFSe"
                                       action="#{EmpresaControle.montarComboConfigEmissao}"
                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                       reRender="form:tabNFSE"/>
                </h:panelGroup>

            </h:panelGrid>

            <h:panelGrid id="configEmissaoNFCe" columns="2" rowClasses="linhaImpar, linhaPar"
                         rendered="#{EmpresaControle.empresaVO.usarNFCe}"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configuração Nota Fiscal - NFCe"/>
                </f:facet>


                <h:outputText value="Configuração padrão de Nota Fiscal NFCe:"/>
                <h:panelGroup layout="block">
                    <h:selectOneMenu id="configuracaoNotaFiscalNFCe" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form"
                                     value="#{EmpresaControle.empresaVO.configuracaoNotaFiscalNFCe.codigo}">
                        <f:selectItems value="#{EmpresaControle.listaConfigEmissaoNFCe}"/>
                        <a4j:support event="onchange" reRender="form"
                                     action="#{EmpresaControle.preencherConfigEmissao}"/>
                    </h:selectOneMenu>

                    <a4j:commandButton id="montarComboConfigEmissaoNFCe"
                                       action="#{EmpresaControle.montarComboConfigEmissao}"
                                       image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                       reRender="form:abaNFCE"/>
                </h:panelGroup>

            </h:panelGrid>

            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                         rendered="#{EmpresaControle.empresaVO.usarNFSe || EmpresaControle.empresaVO.usarNFCe}"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configuração de Notificações"/>
                </f:facet>

                <h:outputText value="Meta valor mensal: "
                              styleClass="tooltipster"
                              title="Campo de meta mensal, que será estabelecida no sistema, de forma que,
                              uma mensagem será exibida a partir do dia 21 de cada mês apenas para administradores ao entrar no sistema,
                              informando que o valor da meta ainda não foi alcançado,
                              essa mensagem será mostrada até a meta ser atingida, ou até o primeiro dia do próximo mês."/>
                <h:inputText id="valorMensalEmitirNFSe" value="#{EmpresaControle.empresaVO.valorMensalEmitirNFSe}"
                             onkeypress="return formatar_moeda(this,'.',',',event);" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>

                <h:outputText value="Receber notificações de notas canceladas e não autorizadas NFS-e: "
                              rendered="#{EmpresaControle.empresaVO.usarNFSe}"/>
                <h:selectBooleanCheckbox value="#{EmpresaControle.empresaVO.envioNotificacaoNotasNFSe}"
                                         rendered="#{EmpresaControle.empresaVO.usarNFSe}">
                    <a4j:support event="onclick" reRender="panelsEmpresa"/>
                </h:selectBooleanCheckbox>

                <h:outputText value="Receber notificações de notas canceladas e não autorizadas NFC-e: "
                              rendered="#{EmpresaControle.empresaVO.usarNFCe}"/>
                <h:selectBooleanCheckbox value="#{EmpresaControle.empresaVO.envioNotificacaoNotasNFCe}"
                                         rendered="#{EmpresaControle.empresaVO.usarNFCe}">
                    <a4j:support event="onclick" reRender="panelsEmpresa"/>
                </h:selectBooleanCheckbox>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_abaNotas_emailsNotificacao}"
                              rendered="#{EmpresaControle.empresaVO.envioNotificacaoNotasNFSe || EmpresaControle.empresaVO.envioNotificacaoNotasNFCe}"/>
                <h:panelGroup
                        rendered="#{EmpresaControle.empresaVO.envioNotificacaoNotasNFSe || EmpresaControle.empresaVO.envioNotificacaoNotasNFCe}">
                    <table cellpadding="0" cellspacing="0">
                        <tr>
                            <td valign="top">
                                <h:inputText id="emailResponsavel" size="60"
                                             value="#{EmpresaControle.emailNotificacaoAutomaticaNotas}"/>
                                &nbsp;
                            </td>
                            <td valign="top">
                                <a4j:commandButton
                                        action="#{EmpresaControle.adicionarEmailNotificacaoAutomaticaNotas}"
                                        reRender="panelErroMensagem, emailResponsavel, panelmensagem,listaEmails"
                                        value="#{msg_bt.btn_adicionar}" accesskey="5" styleClass="botoes"
                                        image="./imagens/botaoAdicionar.png"/>
                            </td>
                        </tr>
                    </table>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" rendered="#{EmpresaControle.empresaVO.envioNotificacaoNotasNFSe
                                                   || EmpresaControle.empresaVO.envioNotificacaoNotasNFCe}"/>
                <h:panelGrid id="listaEmails" columns="1" width="100%" styleClass="tabFormSubordinada"
                             rendered="#{EmpresaControle.empresaVO.envioNotificacaoNotasNFSe
                                                  || EmpresaControle.empresaVO.envioNotificacaoNotasNFCe}">
                    <c:if test="${not empty EmpresaControle.emailsNotificacaoAutomaticaNotas}">
                        <h:dataTable width="60%" headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaAlinhamento"
                                     value="#{EmpresaControle.emailsNotificacaoAutomaticaNotas}" var="email">
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText
                                            value="#{msg_aplic.prt_abaNotas_listaEmailsNotificacao}"/>
                                </f:facet>
                                <h:outputText value="#{email}"/>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_bt.btn_opcoes}"/>
                                </f:facet>
                                <h:panelGroup>
                                    <a4j:commandButton id="removerEmail"
                                                       reRender="panelErroMensagem, emailResponsavel, panelmensagem,listaEmails"
                                                       action="#{EmpresaControle.removerEmailNotificacaoAutomaticaNotas}"
                                                       value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png"
                                                       accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>
                            </h:column>
                        </h:dataTable>
                    </c:if>
                </h:panelGrid>
            </h:panelGrid>
        </rich:tab>

        <rich:tab label="NFS-e" id="tabNFSE" rendered="#{EmpresaControle.empresaVO.usarNFSe}">
            <h:panelGrid id="abaNFSe" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configuração NFS-e"/>
                </f:facet>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              value="Validar certificado na emissão de notas: "/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{EmpresaControle.empresaVO.validarCertificado}"/>

                <h:outputText value="Gestão de Notas: "
                              rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              styleClass="tooltipster"
                              title="Somente usuário administrador pode realizar essa alteração."/>
                <h:selectOneMenu disabled="#{!EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                 rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                 value="#{EmpresaControle.empresaVO.tipoGestaoNFSe}">
                    <f:selectItems value="#{EmpresaControle.listaSelectItemTipoGestaoNFSe}"/>
                    <a4j:support event="onchange" reRender="abaNFSe" ajaxSingle="#{true}"/>
                </h:selectOneMenu>

                <h:outputText value="Emitir valor total do produto/competência: "
                              rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes and (EmpresaControle.empresaVO.tipoGestaoNFSe eq 2 or EmpresaControle.empresaVO.tipoGestaoNFSe eq 8)}"
                              styleClass="tooltipster"/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes and (EmpresaControle.empresaVO.tipoGestaoNFSe eq 2 or EmpresaControle.empresaVO.tipoGestaoNFSe eq 8)}"
                                         value="#{EmpresaControle.empresaVO.emiteValorTotalCompetencia}"/>

                <h:outputText value="Emitir valor total do produto na nota: "
                              rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes and EmpresaControle.empresaVO.tipoGestaoNFSe eq 3}"
                              styleClass="tooltipster"/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes and EmpresaControle.empresaVO.tipoGestaoNFSe eq 3}"
                                         value="#{EmpresaControle.empresaVO.emiteValorTotalFaturamento}"/>

                <h:outputText value="Emitir duplicata dos pagamentos parcelados: "
                              rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              styleClass="tooltipster"/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{EmpresaControle.empresaVO.emitirDuplicataNFSe}"/>

                <h:outputText value="Tipos de produto que emite NFSe: "/>
                <h:panelGroup>
                    <h:inputText id="tipoProdutoNFSe" value="#{EmpresaControle.empresaVO.tipoProdutoEmissaoNFSe}" disabled="true" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"/>
                    <a4j:commandButton value="Alterar"
                                       action="#{EmpresaControle.alterarTipoProdutoNFSe}"
                                       oncomplete="#{EmpresaControle.msgAlert}"
                                       reRender="form,modalEditarTipoProdutoNFSe"
                                       style="vertical-align: middle"
                                       styleClass="botoes nvoBt btSec" />
                </h:panelGroup>

                <h:outputText value="Produto emissão Financeiro: "/>
                <h:selectOneMenu id="produtoEmissaoNFSeFinanceiro" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{EmpresaControle.empresaVO.produtoEmissaoNFSeFinanceiro}">
                    <f:selectItems value="#{EmpresaControle.listaProdutos}"/>
                </h:selectOneMenu>

                <h:outputText rendered="#{EmpresaControle.checkNotaAutoPgRetroativo}"
                              value="Gerar nota automaticamente para pagamentos retroativos "/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.checkNotaAutoPgRetroativo}"
                                         value="#{EmpresaControle.empresaVO.notasAutoPgRetroativo}"/>

                <h:outputText value="Enviar NFSe automático: "/>
                <h:selectBooleanCheckbox value="#{EmpresaControle.empresaVO.enviarNFSeAutomatico}">
                    <a4j:support event="onclick" reRender="abaNFSe"/>
                </h:selectBooleanCheckbox>

                <h:outputText value="Consultar desde o início do mês (Envio Automático):"
                              rendered="#{EmpresaControle.empresaVO.enviarNFSeAutomatico}"/>
                <h:selectBooleanCheckbox id="consultarDiasAnterioresNFSe" styleClass="campos"
                                         rendered="#{EmpresaControle.empresaVO.enviarNFSeAutomatico}"
                                         value="#{EmpresaControle.empresaVO.consultarDiasAnterioresNFSe}"/>

                <h:outputText rendered="#{EmpresaControle.empresaVO.tipoGestaoNFSe == 3 && EmpresaControle.empresaVO.enviarNFSeAutomatico}"
                              value="Enviar NFSe automático somente pagamentos com usuário Recorrência: "/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.empresaVO.tipoGestaoNFSe == 3 && EmpresaControle.empresaVO.enviarNFSeAutomatico}"
                                         value="#{EmpresaControle.empresaVO.emitirNotaSomenteRecorrencia}"/>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              value="Emitir NFSe por pagamento: "/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{EmpresaControle.empresaVO.usarNFSePorPagamento}"/>

                <h:outputText value="Emitir nome do aluno na nota (Nota em Grupo): "/>
                <h:selectBooleanCheckbox value="#{EmpresaControle.empresaVO.emitirNomeAlunoNotaFamilia}"
                                         id="emitirNomeAlunoNotaFamilia"/>

                <h:outputText
                        rendered="#{EmpresaControle.empresaVO.tipoGestaoNFSe == 1 && EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                        value="Emitir NFSe por data de compensação: "/>
                <h:selectBooleanCheckbox
                        rendered="#{EmpresaControle.empresaVO.tipoGestaoNFSe == 1 && EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                        value="#{EmpresaControle.empresaVO.emiteNFSEPorDataCompensacao}"/>

                <h:outputText rendered="#{EmpresaControle.empresaVO.tipoGestaoNFSe == 1}"
                              value="Utilizar data de compensação original na consulta: "/>
                <h:selectBooleanCheckbox
                        id="usarDataOriginalCompensacaoNFSe"
                        rendered="#{EmpresaControle.empresaVO.tipoGestaoNFSe == 1}"
                        value="#{EmpresaControle.empresaVO.usarDataOriginalCompensacaoNFSe}"/>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              value="Permite alterar data de emissão NFSe (Gestão de Notas): "/>
                <h:selectBooleanCheckbox
                        id="alterarDataEmissao"
                        rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                        value="#{EmpresaControle.empresaVO.permiteAlterarDataEmissaoNFSe}"/>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              value="Permite gerar Nota Manual (Gestão de Notas):"/>
                <h:selectBooleanCheckbox
                        id="notaManual"
                        rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                        value="#{EmpresaControle.empresaVO.permiteGerarNotaManual}"/>

                <h:outputText
                        rendered="#{EmpresaControle.empresaVO.tipoGestaoNFSe == 1 && EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                        value="Apresentar valores em conta corrente: "/>
                <h:selectBooleanCheckbox
                        rendered="#{EmpresaControle.empresaVO.tipoGestaoNFSe == 1 && EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                        value="#{EmpresaControle.empresaVO.gerarNFSeContaCorrente}"/>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              title="Disponivel para as cidades de SÃO PAULO e MARABÁ"
                              value="Permite Gerar Arquivo Lote RPS: "/>
                <h:selectBooleanCheckbox
                        rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                        title="Disponivel para as cidades de SÃO PAULO e MARABÁ"
                        value="#{EmpresaControle.empresaVO.permiteGerarArquivoLoteRPS}"/>


                <h:outputText value="Mostrar nota por dia de competência: "
                              rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"/>
                <h:selectBooleanCheckbox
                        rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                        value="#{EmpresaControle.empresaVO.mostrarNotaPorDiaCompetencia}"/>

                <h:outputText value="Formas de pagamento para emissão automática de NFSe: "
                              rendered="#{EmpresaControle.empresaVO.enviarNFSeAutomatico}"/>
                <h:panelGroup rendered="#{EmpresaControle.empresaVO.enviarNFSeAutomatico}">
                    <h:inputText id="formaPagamentoNFSe" value="#{EmpresaControle.empresaVO.formasPagamentoEmissaoNFSe}"
                                 disabled="true" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"/>
                    <a4j:commandButton value="Alterar"
                                       rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                       action="#{EmpresaControle.alterarFormaPagamentoNFSe}"
                                       oncomplete="#{EmpresaControle.msgAlert}"
                                       reRender="form,modalEditarFormasPagamentoNFSe"
                                       style="vertical-align: middle"
                                       styleClass="botoes nvoBt btSec"/>
                </h:panelGroup>
            </h:panelGrid>
        </rich:tab>

        <rich:tab label="NFC-e" rendered="#{EmpresaControle.empresaVO.usarNFCe}" id="abaNFCE">
            <h:panelGrid id="abaNFCe" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%"
                         headerClass="subordinado">

                <f:facet name="header">
                    <h:outputText value="Configuração NFC-e"/>
                </f:facet>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              value="Validar certificado na emissão de notas: "/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{EmpresaControle.empresaVO.validarCertificado}"/>

                <h:outputText value="Tipos de produto que emite NFCe: "/>
                <h:panelGroup>
                    <h:inputText id="tipoProdutoNFCe" value="#{EmpresaControle.empresaVO.tipoProdutoEmissaoNFCe}" disabled="true" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"/>
                    <a4j:commandButton value="Alterar"
                                       rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                       action="#{EmpresaControle.alterarTipoProdutoNFCe}"
                                       oncomplete="#{EmpresaControle.msgAlert}"
                                       reRender="form,modalEditarTipoProdutoNFCe"
                                       style="vertical-align: middle"
                                       styleClass="botoes nvoBt btSec" />
                </h:panelGroup>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              value="Emitir NFCe por pagamento: "/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{EmpresaControle.empresaVO.usarNFCePorPagamento}"/>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              value="Discriminar os descontos no envio da nota: "/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{EmpresaControle.empresaVO.gerarNotaFiscalComDesconto}"/>

                <h:outputText rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                              value="Ignorar o código de barras no envio das notas: "/>
                <h:selectBooleanCheckbox rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                         value="#{EmpresaControle.empresaVO.ignorarCodigoDeBarrasEmissaoNfce}"/>

                <h:outputText value="Enviar NFCe automático: "/>
                <h:selectBooleanCheckbox value="#{EmpresaControle.empresaVO.enviarNFCeAutomatico}">
                    <a4j:support event="onclick" reRender="abaNFCe"/>
                </h:selectBooleanCheckbox>

                <h:outputText value="Consultar desde o início do mês (Envio Automático):"
                              rendered="#{EmpresaControle.empresaVO.enviarNFCeAutomatico}"/>
                <h:selectBooleanCheckbox id="consultarDiasAnterioresNFCe" styleClass="campos"
                                         rendered="#{EmpresaControle.empresaVO.enviarNFCeAutomatico}"
                                         value="#{EmpresaControle.empresaVO.consultarDiasAnterioresNFCe}"/>

                <h:outputText value="Enviar NFCe automático somente pagamentos com usuário Recorrência: "/>
                <h:selectBooleanCheckbox value="#{EmpresaControle.empresaVO.emitirNFCeSomenteRecorrencia}"/>

                <h:outputText value="#{msg_aplic.prt_Empresa_usarNomeResponsavelNFCe}"/>
                <h:selectBooleanCheckbox id="usarNomeResponsavelNFCe" value="#{EmpresaControle.empresaVO.usarNomeResponsavelNFCe}"/>

<%--                <h:outputText value="Emitir mês de referência do produto: "/>--%>
<%--                <h:selectBooleanCheckbox value="#{EmpresaControle.empresaVO.emitirMesReferenciaNFCe}"/>--%>

                <h:outputText value="Formas de pagamento para emissão automática de NFCe: "
                              rendered="#{EmpresaControle.empresaVO.enviarNFCeAutomatico}"/>
                <h:panelGroup rendered="#{EmpresaControle.empresaVO.enviarNFCeAutomatico}">
                    <h:inputText id="formaPagamentoNFCe" value="#{EmpresaControle.empresaVO.formasPagamentoEmissaoNFCe}"
                                 disabled="true" size="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"/>
                    <a4j:commandButton value="Alterar"
                                       rendered="#{EmpresaControle.usuarioLogado.usuarioPactoSolucoes}"
                                       action="#{EmpresaControle.alterarFormaPagamentoNFCe}"
                                       oncomplete="#{EmpresaControle.msgAlert}"
                                       reRender="form,modalEditarFormasPagamentoNFCe"
                                       style="vertical-align: middle"
                                       styleClass="botoes nvoBt btSec"/>
                </h:panelGroup>

                <h:outputText value="Produto emissão Financeiro: "/>
                <h:selectOneMenu id="produtoEmissaoNFCeFinanceiro" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{EmpresaControle.empresaVO.produtoEmissaoNFCeFinanceiro}">
                    <f:selectItems value="#{EmpresaControle.listaProdutos}"/>
                </h:selectOneMenu>
            </h:panelGrid>

            <h:panelGrid id="prodConfiNFCeTitle" columns="1" width="100%" headerClass="subordinado"
                         columnClasses="colunaCentralizada">
                <f:facet name="header">
                    <h:outputText
                            value="Configuração para produtos do tipo Serviço (somente PRODUTO GENÉRICO) e Quitação de dinheiro - Cancelamento"/>
                </f:facet>
            </h:panelGrid>
            <h:panelGrid id="prodConfiNFCe"
                         columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                         width="100%">


                <h:outputText value="#{msg_aplic.prt_Produto_cfop}"/>
                <h:panelGroup>
                    <h:inputText id="cfop" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 size="10" maxlength="50" value="#{EmpresaControle.produtoZillyon.cfop}"/>
                </h:panelGroup>

                <h:outputText value="#{msg_aplic.prt_Produto_ncmNFCe}"/>
                <h:panelGroup>
                    <h:inputText id="ncmNFCe" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 size="10" maxlength="50" value="#{EmpresaControle.produtoZillyon.ncmNFCe}"/>
                </h:panelGroup>

                <h:outputText value="#{msg_aplic.prt_Produto_codigoListaServico}"/>
                <h:panelGroup>
                    <h:inputText id="codigoListaServico" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 size="10" maxlength="50" value="#{EmpresaControle.produtoZillyon.codigoListaServico}"/>
                </h:panelGroup>

                <h:outputText value="#{msg_aplic.prt_Produto_codigoTributacaoMunicipio}"/>
                <h:panelGroup>
                    <h:inputText id="codigoTributacaoMunicipio" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 size="10" maxlength="50"
                                 value="#{EmpresaControle.produtoZillyon.codigoTributacaoMunicipio}"/>
                </h:panelGroup>

                <h:outputText value="#{msg_aplic.prt_Produto_aliquotaPIS}"/>
                <h:panelGroup>
                    <h:inputText id="aliquotaPIS" value="#{EmpresaControle.produtoZillyon.aliquotaPIS}"
                                 onkeypress="return formatar_moeda(this,'.',',',event);" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" size="10">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                </h:panelGroup>

                <h:outputText value="#{msg_aplic.prt_Produto_aliquotaCOFINS}"/>
                <h:panelGroup>
                    <h:inputText id="aliquotaCOFINS" value="#{EmpresaControle.produtoZillyon.aliquotaCOFINS}"
                                 onkeypress="return formatar_moeda(this,'.',',',event);" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" size="10">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                </h:panelGroup>

                <h:outputText value="#{msg_aplic.prt_Produto_aliquotaISSQN}"/>
                <h:panelGroup>
                    <h:inputText id="aliquotaISSQN" value="#{EmpresaControle.produtoZillyon.aliquotaISSQN}"
                                 onkeypress="return formatar_moeda(this,'.',',',event);" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" size="10">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                </h:panelGroup>

            </h:panelGrid>
        </rich:tab>


    </rich:tabPanel>
</h:panelGroup>

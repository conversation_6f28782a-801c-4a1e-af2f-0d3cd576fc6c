<%-- 
    Document   : include_bi_config_ltv
    Created on : 30/03/2020, 08:50:29
    Author     : <PERSON><PERSON>
--%>
<%@include file="imports.jsp" %>
<rich:modalPanel id="panelConfigBiLtv" width="400" minHeight="600" styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Configurar BI LTV"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <span class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                  id="hidelinkFiltroConvenioCobranca"
                  onclick="Richfaces.hideModalPanel('panelConfigBiLtv');"></span>
        </h:panelGroup>
    </f:facet>

    <h:form id="formPanelConfigLtv" style="height: 100%">
        <h:panelGroup layout="block" styleClass="paginaFontResponsiva">
            <h:panelGroup layout="block" style="display: inline-block;padding: 5px; width: 100%;">
                <h:panelGroup layout="block" style="margin-bottom:5px;">
                    <h:outputText styleClass="rotuloCampos tooltipster" value="Per�odo"
                                  title="
<div style='text-align: left !important'>
<h3><b>Per�odo:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      Todo o c�lculo ser� realizado nesse intervalo de tempo contando todas as despesas,<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      receitas e contratos finalizados, n�o renovados e cancelamentos.<br /><br />
     </spam>
     <spam style='list-style-type: lower-alpha;'>
      Obs.: O filtro de datas impacta somente no c�lculo do LTV.<br /><br /><br />
     </span>
</div>"/>
                </h:panelGroup>
                <div class="tooltipster dateTimeCustom" style="display: inline-flex;" title="Data inicial">
                    <rich:calendar id="inicioLtv"
                                   value="#{LtvControle.inicio}"
                                   inputSize="10"
                                   showInput="true"
                                   showApplyButton="false"
                                   inputClass="rich-calendar-input, forcarSemBorda"
                                   buttonClass="tooltipster"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   enableManualInput="false"
                                   zindex="2"
                                   datePattern="dd/MM/yyyy"
                                   showWeeksBar="false"
                                   buttonIcon="../imagens_flat/calendar-button.svg"
                                   style="float: left;">
                        <a4j:support event="onchanged"
                                     action="#{LtvControle.buscaTotalDespesas}"
                                     reRender="formPanelConfigLtv"
                                    />
                    </rich:calendar>
                </div>
                <rich:spacer height="1" width="10"/>
                <div style="display: inline-flex;" title="Data final"
                     class="tooltipster dateTimeCustom alignToRight">
                    <rich:calendar id="ifimLtv" style="float: left; margin-left: 10px;"
                                   value="#{LtvControle.fim}"
                                   inputSize="10"
                                   showInput="true"
                                   inputClass="rich-calendar-input, forcarSemBorda"
                                   buttonClass="tooltipster"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   enableManualInput="false"
                                   zindex="2"
                                   datePattern="dd/MM/yyyy"
                                   buttonIcon="../imagens_flat/calendar-button.svg"
                                   showWeeksBar="false">
                        <a4j:support event="onchanged"
                                     action="#{LtvControle.buscaTotalDespesas}"
                                     reRender="formPanelConfigLtv"
                        />
                    </rich:calendar>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block" style="margin-bottom:5px; color: #888;">
                <h:selectBooleanCheckbox id="produtosLtv" style="margin-left: 3px;" styleClass="tooltipster"
                                         title="
<div style='text-align: left !important'>
<h3><b>Produtos vendidos separadamente dos contratos:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      Marque esta op��o para selecionar todos os produtos ou selecione produtos independentes abaixo.<br /><br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Obs.: Este filtro impacta somente no c�lculo do LTV.<br /><br /><br />
     </span>
</div>"
                                         value="#{LtvControle.produtos}">
                    <a4j:support event="onchange" reRender="listaProdutosDTO"
                                 action="#{LtvControle.marcarTodosProdutos}"/>
                </h:selectBooleanCheckbox>
                <h:outputText styleClass="tooltipster" value="Produtos (#{LtvControle.quantidadeProdutos})"
                              title="<div style='text-align: left !important'>
<h3><b>Produtos vendidos separadamente dos contratos:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      Marque esta op��o para selecionar todos os produtos ou selecione produtos independentes abaixo.<br /><br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Obs.: Este filtro impacta somente no c�lculo do LTV.<br /><br /><br />
     </span>
</div>"/>
                <rich:spacer height="1" width="20"/>
                <h:selectBooleanCheckbox id="servicosLtv" style="margin-left: 3px;" styleClass="tooltipster"
                                         title="
<div style='text-align: left !important'>
<h3><b>Contratos e outros servi�os:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      Marque esta op��o para calcular contando todos os contratos.<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Por padr�o, esta op��o j� inicia marcada.<br /><br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Obs.: Este filtro impacta somente no c�lculo do LTV.<br /><br /><br />
     </span>
</div>"
                                         value="#{LtvControle.servicos}">
                </h:selectBooleanCheckbox>
                <h:outputText styleClass="tooltipster" value="Servi�os"
                              title="
<div style='text-align: left !important'>
<h3><b>Contratos e outros servi�os:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      Marque esta op��o para calcular contando todos os contratos.<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Por padr�o, esta op��o j� inicia marcada.<br /><br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Obs.: Este filtro impacta somente no c�lculo do LTV.<br /><br /><br />
     </span>
</div>"/>
                <!-- CONFIG - NOVO CALCULO LTV REALIZADO ---->
                <rich:spacer height="1" width="20"/>
                <h:selectBooleanCheckbox id="ltvRealizadoCheckbox" style="margin-left: 3px;" styleClass="tooltipster"
                                         title="#{LtvControle.ltvRealizadoTooltipContent}"
                                         value="#{LtvControle.configLtvRealizado}">
                </h:selectBooleanCheckbox>
                <h:outputText styleClass="tooltipster" value="LTV Realizado"
                              title="#{LtvControle.ltvRealizadoTooltipContent}"/>
                <!------------------------------------------>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          style="margin-bottom:5px; max-height: 150px; overflow:auto; border: solid 1px #CCC; border-radius: 5px;">
                <rich:dataGrid value="#{LtvControle.listaProdutos}" var="produto" border="0"
                               style="margin-left: -12px; width: 100%;" id="listaProdutosDTO">
                    <rich:column style="border: 0px; padding: 0px;">
                        <h:selectBooleanCheckbox styleClass="" value="#{produto.marcado}"/>
                        <h:outputText styleClass="" value="#{produto.descricao}"
                                      style="font-size: 10px; color: #888; text-transform: uppercase;"/>
                    </rich:column>
                </rich:dataGrid>

            </h:panelGroup>

            <h:panelGroup layout="block" style="margin-bottom:5px; margin-top: 30px; color: #888;">
                <h:selectBooleanCheckbox id="marcarTodasDespesasLtv" style="margin-left: 3px;" styleClass="tooltipster"
                                         title="
<div style='text-align: left !important'>
<h3><b>Despesas:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      Marque esta op��o para selecionar todas as despesas da empresa, se preferir,<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      sepecione separadamente na listagem abaixo.<br /><br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Para uma despesa j� iniciar selecionada, v� ao financeiro / plano de contas,<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      selecione a despesa e marque a op��o \"Incide C�lculo CAC\".<br /><br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Obs.: Este filtro impacta no c�lculo do CAC e do LTV.<br /><br /><br />
     </span>
</div>"
                                         value="#{LtvControle.marcarTodasAsDespesas}">

                    <a4j:support event="onchange" reRender="listaPlanoContaDespesaDTO, totalDespesasMarcadas"
                                 action="#{LtvControle.marcarTodasDespesas}"/>
                </h:selectBooleanCheckbox>
                <h:outputText styleClass="tooltipster" value="Despesas (#{LtvControle.quantidadeDeDespesas})"
                              title="
<div style='text-align: left !important'>
<h3><b>Despesas:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      Marque esta op��o para selecionar todas as despesas da empresa, se preferir,<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      selecione separadamente na listagem abaixo.<br /><br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Para uma despesa j� iniciar selecionada, v� ao financeiro / plano de contas,<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      selecione a despesa e marque a op��o \"Incide C�lculo CAC\".<br /><br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Obs.: Este filtro impacta no c�lculo do CAC e do LTV.<br /><br /><br />
     </span>
</div>"/>
                <h:outputText styleClass="tooltipster" value="#{LtvControle.totalDespesasMarcadas}"
                              style="float: right; margin-top: 4px;" id="totalDespesasMarcadas">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
                <rich:spacer height="1" width="5" style="float: right;" />
                <h:outputText styleClass="tooltipster" value="Total selecionado: "
                              style="float: right; font-weight: bolder; margin-top: 4px;" id="totalDespesasMarcadasLabel"/>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          style="margin-bottom:5px; max-height: 150px; overflow:auto; border: solid 1px #CCC; border-radius: 5px;">
                <rich:dataGrid value="#{LtvControle.listaPlanoContaDespesaDTO}" var="despesa" border="0"
                               style="margin-left: -10px; width: 100%;" id="listaPlanoContaDespesaDTO">
                    <rich:column style="border: 0px; padding: 0px;">
                        <h:selectBooleanCheckbox styleClass="" value="#{despesa.planoContaTO.insidirLTV}">
                            <a4j:support event="onchange" reRender="totalDespesasMarcadas"
                                         action="#{LtvControle.calculaTotalDespesasMarcadas}"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="" value="#{despesa.planoContaTO.descricao}"
                                      style="font-size: 10px; color: #888; text-transform: uppercase;"/>
                        <h:outputText styleClass="" value="#{despesa.valorTotal}"
                                      style="font-size: 10px; color: #888; text-transform: uppercase; float: right;">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                </rich:dataGrid>
            </h:panelGroup>

        </h:panelGroup>

        <br/>
        <h:panelGroup layout="block" styleClass="container-botoes" style="float: right;"
                      id="painelCalcularMetricas">
            <a4j:commandLink reRender="bi-ltv"
                             action="#{LtvControle.recalcularMetricas}"
                             styleClass="botaoPrimario texto-size-16 consultarCCobranca"
                             oncomplete="montarTips(); Richfaces.hideModalPanel('panelConfigBiLtv');">
                <span class="texto-size-16 texto-font  texto-cor-branco">Atualizar </span>
                <i style="vertical-align: inherit" class="fa-icon-refresh texto-size-16 texto-cor-branco">
            </a4j:commandLink>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

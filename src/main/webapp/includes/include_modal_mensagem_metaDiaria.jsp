<%@include file="imports.jsp" %>

<rich:modalPanel id="mdlMensagemMetaDiaria" styleClass="novaModal" width="500" height="300" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Meta Di�ria"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <a4j:form>
                <a4j:commandButton image="#{context}/imagens/close.png"
                                   reRender="dataInicialCRM, dataFinalCRM"
                                   oncomplete="Richfaces.hideModalPanel('mdlMensagemMetaDiaria');mostrarTodasTelas();adicionarPlaceHolderCRM();"
                                   id="hidelink12"/>
            </a4j:form>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formMdlMensagemMetaDiaria">
        <h:panelGroup>
            <h:panelGroup styleClass="margin-box">
                <h:panelGroup layout="block" styleClass="text-center texto-size-16-real texto-font texto-cor-cinza">
                    <h:outputText
                                  value="Ol�! Para ver dados com mais de 7 dias de intervalo, utilize o B.I (Business Intelligence)."/>
                </h:panelGroup>
            </h:panelGroup>
            <h:outputText id="msgDetalhadaMetaDiaria" styleClass="negrito text-center" style="width: 55%; margin-left: 23%"
                          value="Com o B.I voc� ir� encontrar informa��es detalhadas e por periodos maiores que 7 dias."/>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup styleClass="margin-box">
                    <a4j:commandLink action="#{BusinessIntelligenceCRMControle.carregarBusinessIntelligence}" value="Acessar B.I"
                                       id="sim"
                                       styleClass="botaoPrimario texto-size-16-real"
                                       oncomplete="Richfaces.hideModalPanel('mdlMensagemMetaDiaria');"
                                       reRender="mdlMensagemMetaDiaria"/>
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink value="Fechar"
                                     id="btnFecharModalMetaDiaria"
                                     styleClass="botaoSecundario texto-size-16-real"
                                     reRender="dataInicialCRM, dataFinalCRM"
                                     oncomplete="Richfaces.hideModalPanel('mdlMensagemMetaDiaria');mostrarTodasTelas();adicionarPlaceHolderCRM();"/>
                </h:panelGroup>
            </h:panelGroup>
            <br/>
            <br/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
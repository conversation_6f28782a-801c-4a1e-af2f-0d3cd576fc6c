<%@include file="imports.jsp" %>
<rich:modalPanel id="panelFiltroDCC" width="350" autosized="true" styleClass="novaModal ">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtro"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkDCC"/>
            <rich:componentControl for="panelFiltroDCC" attachTo="hidelinkDCC" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formPanelFiltroDCC">
        <h:panelGrid id="listaFiltrosDCC"  border="0"
                     cellspacing="0" styleClass="margin-container"
                     columnClasses="colunaEsquerda, colunaEsquerda" columns="2">
            <h:panelGroup layout="block" styleClass="chk-fa-container">
                <h:selectBooleanCheckbox id="matriculas" value="#{RelContratosRecorrenciaControle.somenteParcelasMes}"/>
                <span></span>
            </h:panelGroup>
            <h:outputText styleClass="tooltipster texto-size-16 texto-cor-cinza texto-font" value="Parcelas do m�s" title="Parcelas em remessas no per�odo de refer�ncia com vencimento no pr�prio m�s"/>
            <h:panelGroup layout="block" styleClass="chk-fa-container">
                <h:selectBooleanCheckbox id="rematriculas" value="#{RelContratosRecorrenciaControle.somenteParcelasForaMes}"/>
                <span></span>
            </h:panelGroup>
            <h:outputText styleClass="tooltipster texto-size-16 texto-cor-cinza texto-font" value="Parcelas de outros meses" title="Parcelas em remessas no per�odo de refer�ncia com vencimento em outros meses"/>

        </h:panelGrid>
        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink   reRender="containerRecorrencia"
                               styleClass="botaoPrimario texto-size-16"
                               action="#{RelContratosRecorrenciaControle.prepararTotalizador}"
                               oncomplete="Richfaces.hideModalPanel('panelFiltroDCC')"
                               style="vertical-align:middle;" value="Atualizar"/>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

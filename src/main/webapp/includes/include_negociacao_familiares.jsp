<%@include file="imports.jsp" %>

<style>
    .familiar {
        display: block;
        height: 50px;
        padding-left: 10px;
        margin-top: 10px;
    }

    .familiar .fotopessoa {
        border: 2px solid #094771;
    }

    .nomeFamiliar {
        text-align: left;
        vertical-align: top;
        text-transform: capitalize;
        color: #094771;
        font-weight: bold;
        display: block;
        margin-top: 5px;
        margin-left: 5px;
    }

    .dadosFamiliar {
        margin-top: 5px;
        margin-left: 5px;
        display: block;
        color: #777777;
        text-transform: capitalize;
    }

    .btnsituacao {
        margin-right: 10px;
        height: 20px !important;
    }

    .cfoto {
        border-right: #777 solid;
        text-align: center;
    }

    .cfamilia {
        vertical-align: top;
        padding-left: 20px;
    }
</style>

<h:panelGrid columns="2" width="100%" columnClasses="w40 cfoto, w60 cfamilia" id="negociacaofamiliar">

    <h:panelGroup>
        <h:panelGroup layout="block" styleClass="containerFotoCliente">
            <a4j:mediaOutput element="img" cacheable="false"
                             rendered="#{!SuperControle.fotosNaNuvem}"
                             createContent="#{ContratoControle.paintFoto}"
                             value="#{ImagemData}" mimeType="image/jpeg">
            </a4j:mediaOutput>
            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                            url="#{ContratoControle.paintFotoDaNuvem}"/>
        </h:panelGroup>

        <h:panelGroup layout="block">
            <h:outputText styleClass="texto-size-20 texto-font texto-cor-cinza-2 texto-bold"
                          value="#{ContratoControle.contratoVO.pessoa.nome}"/>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup>
        <div style="font-size: 16px; color: #094771; font-weight: bold">Aproveite para renovar tamb�m o plano da sua
            fam�lia
        </div>

        <a4j:repeat value="#{NegociacaoFamiliaresControle.familiares}" var="f">
            <h:panelGroup layout="block" styleClass="familiar" rendered="#{f.codigoPessoa != ContratoControle.contratoVO.pessoa.codigo}">

                <div class="chk-fa-container inline "
                     style="vertical-align: top; margin-top: 17px; display: inline-block !important;">
                    <h:selectBooleanCheckbox value="#{f.selecionado}">
                        <a4j:support reRender="detalhesNegociacao"
                                     event="onclick"/>
                    </h:selectBooleanCheckbox>
                    <span/>
                </div>

                <h:panelGroup layout="block" style="position: relative; display: inline-block">
                    <h:graphicImage styleClass="fotopessoa" style="width:43px;height:43px; border-radius: 50%;"
                                    url="#{f.urlFoto}">
                    </h:graphicImage>
                    <div style="position: absolute; bottom: -5px; left: 0; width: 50px; display: flex">
                        <h:graphicImage styleClass="btnsituacao" id="alunoAtivo" value="/images/botaoAtivo.png"
                                        rendered="#{f.ativo}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoInativo"
                                        value="/images/botaoInativo.png"
                                        rendered="#{f.inativo}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoVisitante"
                                        value="/images/botaoVisitante.png"
                                        rendered="#{f.visitante}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoTrancado"
                                        value="/images/botaoTrancamento.png"
                                        rendered="#{f.trancado}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoNormal"
                                        value="/images/botaoNormal.png"
                                        rendered="#{f.ativoNormal}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoTrancadoVencido"
                                        value="/images/botaoTrancadoVencido.png"
                                        rendered="#{f.trancadoVencido}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoFreePass"
                                        value="/images/botaoFreePass.png"
                                        rendered="#{f.visitanteFreePass}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoAulaAvulsa"
                                        value="/images/botaoAulaAvulsa.png"
                                        rendered="#{f.visitanteAulaAvulsa}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoDiaria"
                                        value="/images/botaoDiaria.png"
                                        rendered="#{f.visitanteDiaria}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoCancelado"
                                        value="/images/botaoCancelamento.png"
                                        rendered="#{f.inativoCancelamento}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoDesistente"
                                        value="/images/botaoDesistente.png"
                                        rendered="#{f.inativoDesistente}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoAVencer"
                                        value="/images/botaoAvencer.png"
                                        rendered="#{f.ativoAvencer}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoVencido"
                                        value="/images/botaoVencido.png"
                                        rendered="#{f.inativoVencido}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoCarencia"
                                        value="/images/botaoCarencia.png"
                                        rendered="#{f.ativoCarencia}"/>
                        <h:graphicImage styleClass="btnsituacao" id="alunoAtestado"
                                        value="/images/botaoAtestado.png"
                                        rendered="#{f.ativoAtestado}"/>
                    </div>
                </h:panelGroup>


                <h:panelGroup style="display: inline-block;vertical-align: top;">
                    <span class="nomeFamiliar">
                        <h:outputText value="#{f.primeiroNome} "/>
                    </span>
                    <span class="dadosFamiliar">
                        <h:outputText rendered="#{f.vencimento ne null}" value="Venc. #{f.vencimentoApresentar}"/>
                    </span>


                </h:panelGroup>
                <a4j:commandLink style="float: right; margin-right: 20px; font-size: 20px; margin-top: 20px"
                                 rendered="#{f.selecionado}"
                                 action="#{NegociacaoFamiliaresControle.editarIndividualmente}"
                                 reRender="form"
                                 styleClass="tooltipster"
                                 title="Clique para editar individualmente a negocia��o desse familiar.">
                    <i class="fa-icon-edit"></i>
                </a4j:commandLink>

            </h:panelGroup>
        </a4j:repeat>
    </h:panelGroup>

</h:panelGrid>

<%@include file="imports.jsp" %>
<style>
    .iconCalendarNone {
        width: 0;
    }
</style>
<rich:modalPanel id="panelFiltroInadimplencia" width="500" styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtro"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkInadimplenciaFiltro"/>
            <rich:componentControl for="panelFiltroInadimplencia" attachTo="hidelinkInadimplenciaFiltro" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formFiltro">
        <h:panelGroup layout="block" style="display: inline-block; margin-left: 4.5%;"
                      styleClass="col-text-align-center">
            <h:panelGrid columns="2">

                <h:outputText styleClass="texto-size-16 bi-font-family bi-cor-cinza tooltipster pull-left"
                              title="Ao adicionar uma data, nos indicadores abaixo ser� apresentado apenas os dados referente aos alunos matriculados desta data em diante."
                              value="Alunos matriculados a partir do dia "/>

                <h:panelGrid columns="2">

                    <h:panelGroup layout="block" styleClass="pull-right">
                        <div class="dateTimeCustom tooltipster alignToRight">
                            <rich:calendar id="dataBIMatricula"
                                           showInput="true"
                                           inputSize="10"
                                           buttonIcon="/imagens_flat/icon-calendar-check.png"
                                           buttonClass="iconCalendarNone"
                                           inputClass="form"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="false"
                                           zindex="2"
                                           value="#{BIInadimplenciaControle.filtroDataMatricula}"
                                           showWeeksBar="false">
                            </rich:calendar>
                        </div>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="pull-right">
                        <div class="tooltipster alignToRight">
                            <a4j:commandLink id="limparMatricula"
                                             styleClass="tooltipster"
                                             action="#{BIInadimplenciaControle.limparData}"
                                             title="Limpar data matr�cula"
                                             status="none"
                                             reRender="dataBIMatricula"
                                             style="font-size: 18px; color: rgba(41, 171, 226,1)">
                                <i class="fa-icon-eraser"></i>
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>

                </h:panelGrid>

                <h:outputText styleClass="texto-size-16 bi-font-family bi-cor-cinza tooltipster"
                              title="Considerar como efici�ncia pagamentos realizados at� o dia X do m�s subsequente ao vencimento da parcela.<br/>Ex: Se informado dia 5, a parcela que venceu no dia 20/04 e foi paga s� no dia 08/05. Ser� considerada como inadimpl�ncia."
                              value="Considerar pagamento at� o dia "/>
                <h:panelGroup styleClass="cb-container margenVertical" layout="block">
                    <h:selectOneMenu id="diaPagamento"
                                     style="font-size: 14px !important;"
                                     value="#{BIInadimplenciaControle.filtroDiaPagamento}">
                        <f:selectItem itemValue="" itemLabel=""/>
                        <f:selectItem itemValue="5" itemLabel="5"/>
                        <f:selectItem itemValue="10" itemLabel="10"/>
                        <f:selectItem itemValue="15" itemLabel="15"/>
                        <f:selectItem itemValue="20" itemLabel="20"/>
                        <f:selectItem itemValue="25" itemLabel="25"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:outputText styleClass="texto-size-16 bi-font-family bi-cor-cinza tooltipster"
                              title="Desconsidera parcelas canceladas no bi."
                              value="Desconsiderar Canceladas"/>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="desconsideraCnceladas"
                                             value="#{BIInadimplenciaControle.desconsiderarCanceladasBi}"/>
                    <span/>
                </h:panelGroup>

            </h:panelGrid>

            <br>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink id="botaAtualizarFiltro"
                                 action="#{BIInadimplenciaControle.atualizarInadimplencia}"
                                 oncomplete="Richfaces.hideModalPanel('painelFiltroInadimplencia')"
                                 reRender="bi-container-inadimplencia, panelFiltroInadimplencia"
                                 style="vertical-align:middle;line-height: 50px"
                                 styleClass="botaoPrimario texto-size-16">
                    <span class="texto-size-16 texto-font">Atualizar </span>
                    <i style="vertical-align: inherit" class="fa-icon-refresh texto-size-20 texto-cor-branco">
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGroup>
    </h:form>
</rich:modalPanel>

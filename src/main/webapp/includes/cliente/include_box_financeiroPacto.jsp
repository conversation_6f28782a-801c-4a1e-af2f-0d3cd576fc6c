<%@include file="../../include_imports.jsp" %>

<style>
    .texto {
        font-size: 12px;
    }

    a.texto {
        color: dodgerblue !important;
    }

    .azul {
        color: blue !important;
    }
</style>

<table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
    <tr>
        <td width="19" height="50" align="left" valign="top">
            <img src="${root}/images/box_centro_top_left.gif" width="19" height="50">
        </td>
        <td align="left" valign="top" background="${root}/images/box_centro_top.gif" class="tituloboxcentro"
            style="padding:11px 0 0 0;">
            Boletos Dispon�veis
        </td>
        <td width="19" align="left" valign="top">
            <img src="${root}/images/box_centro_top_right.gif" width="19" height="50">
        </td>
    </tr>
    <tr>
        <td align="left" valign="top" background="${root}/images/box_centro_left.gif"></td>
        <td align="left" valign="top" bgcolor="#ffffff" style="padding:5px 5px 5px 5px;">
            <rich:dataTable id="boletos" style="width: 100%" columnClasses="colunaCentralizada"
                            value="#{FinanceiroPactoControle.parcelas.parcelasEmAberto}" var="parcela">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto" value="C�digo"/>
                    </f:facet>
                    <h:outputText styleClass="texto" value="#{parcela.codigoParcela}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto" value="Descri��o - Clique para emitir"/>
                    </f:facet>
                    <a4j:commandLink id="btnImprimirBoleto" style="margin-left:8px;" styleClass="texto"
                                     actionListener="#{FinanceiroPactoControle.downloadBoleto}"
                                     title="Download do Boleto" reRender="panelMensagem, form"
                                     target="_blank" oncomplete="#{FinanceiroPactoControle.msgAlert}">
                        <h:outputText  value="#{parcela.descricao}"/>
                    </a4j:commandLink>

                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto" value="Data de Vencimento"/>
                    </f:facet>
                    <h:outputText styleClass="texto" value="#{parcela.dataVencimentoApresentar}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="texto" value="Valor"/>
                    </f:facet>
                    <h:outputText styleClass="texto" value="#{parcela.valor}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </rich:column>

                <%--<rich:column>--%>
                    <%--<f:facet name="header">--%>
                        <%--<h:outputText value="Op��es"/>--%>
                    <%--</f:facet>--%>
                    <%--<a4j:commandLink id="btnImprimirBoleto" style="margin-left:8px;"--%>
                                     <%--actionListener="#{FinanceiroPactoControle.downloadBoleto}"--%>
                                     <%--title="Download do Boleto" reRender="panelMensagem, form"--%>
                                     <%--target="_blank" oncomplete="#{FinanceiroPactoControle.msgAlert}">--%>
                        <%--<h:graphicImage url="../imagens/nfe/imprimir_pdf.png"/>--%>
                    <%--</a4j:commandLink>--%>
                <%--</rich:column>--%>
            </rich:dataTable>


            <a4j:commandButton style="margin: 8px; float: right" value="Atualizar"
                               action="#{FinanceiroPactoControle.obterParcelasEmAberto}"
                               reRender="form"/>

            <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText id="msgPanelCadas" styleClass="mensagem" value="#{FinanceiroPactoControle.mensagem}"/>
                    <h:outputText id="msgPanelCadasDet" styleClass="mensagemDetalhada azul"
                                  value="#{FinanceiroPactoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </td>
        <td align="left" valign="top" background="images/box_centro_right.gif">
            <img src="${root}/images/shim.gif">
        </td>
    </tr>
    <tr>
        <td height="20" align="left" valign="top">
            <img src="${root}/images/box_centro_bottom_left.gif" width="19" height="20">
        </td>
        <td align="left" valign="top" background="${root}/images/box_centro_bottom.gif">
            <img src="${root}/images/shim.gif">
        </td>
        <td align="left" valign="top">
            <img src="${root}/images/box_centro_bottom_right.gif" width="19" height="20">
        </td>
    </tr>
</table>

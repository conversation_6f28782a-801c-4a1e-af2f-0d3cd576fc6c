<%-- 
    Document   : include_modal_alterar_vigencia_final_produto_cliente
    Created on : 19/07/2012, 10:49:54
    Author     : XiquiN
--%>

<%@include file="/includes/imports.jsp" %>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
<a4j:outputPanel>
    <rich:modalPanel id="modalEditarVigenciaFinalProdutoCliente"
                     width="400" autosized="true"
                     styleClass="novaModal"
                     showWhenRendered="#{MovProdutoControle.modalEditarVigenciaFinalProdutoCliente}"
                     shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>

                <h:outputText value="Editar data final de vig�ncia" />

            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form>
                    <a4j:commandLink id="btnFecharAlt" style="cursor:pointer" 
                                     action="#{MovProdutoControle.fecharModalEditarVigenciaFinalProduto}"
                                     onclick="#{rich:component('modalEditarVigenciaFinalProdutoCliente')}.hide();">
                        <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "/>
                    </a4j:commandLink>     
                </a4j:form>
            </h:panelGroup>
        </f:facet>

        <h:panelGroup id="panelConteudo2">

            <a4j:form id="formEdicaoVigencia">
                    <h:panelGrid columns="2" columnClasses="text,text">
                        <h:outputText value="Produto:"/>
                        <h:outputText value="#{MovProdutoControle.movProdutoVO.produto.descricao}"/>
                        <h:outputText value="Vig�ncia:"/>
                        <h:panelGroup styleClass="dateTimeCustom" layout="block" style="height: 30px;">
                            <rich:calendar id="dataVigencia" oninputchange="return validar_Data(this.id);"
                                           inputStyle="margin: 0 0 0 0; padding: 0 0 0 0;"
                                           value="#{MovProdutoControle.movProdutoVO.dataFinalVigencia}"
                                           enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy"
                                           showApplyButton="false"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputClass="campos" showWeeksBar="false" firstWeekDay="0"/>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        </h:panelGroup>
                        
                    </h:panelGrid>
                <h:panelGroup id="botoes">
                    <a4j:commandLink id="btnGravar" title="Grava as altera��es realizadas"
                                     style="margin-top:10px; margin-right:10px;"
                                       styleClass="pure-button pure-button-primary tooltipster"
                                       action="#{MovProdutoControle.gravarEdicaoDataFinalVigencia}"
                                       reRender="formEdicaoVigencia:mensagemDetalhada, form:panelProdutosValidadeCliente, form:avisosCliente"
                                       oncomplete="#{MovProdutoControle.acaoAjaxGravarFinalVigencia}">
                        <h:outputText value="#{msg_bt.btn_gravar}" style="font-size: 14px"/>
                    </a4j:commandLink>

                    <a4j:commandLink id="btnCancelar"
                                     styleClass="pure-button tooltipster"
                                       reRender="form:panelProdutosValidadeCliente"
                                       action="#{ClienteControle.obterListaProdutoComValidadeCliente}"
                                       onclick="Richfaces.hideModalPanel('modalEditarVigenciaFinalProdutoCliente');">
                        <h:outputText value="#{msg_bt.btn_cancelar}" style="font-size: 14px"/>
                    </a4j:commandLink>    
                </h:panelGroup>

                <h:panelGroup id="mensagemDetalhada" layout="block">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens" rendered="#{MovProdutoControle.erro}">
                        <h:commandButton image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagemDetalhada" value="#{MovProdutoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>
            </a4j:form>
        </h:panelGroup>

    </rich:modalPanel>

    <a4j:status forceId="true" id="statusEdicaoVigencia"
                onstart="document.getElementById('formEdicaoVigencia:imageLoading').style.visibility = '';"
                onstop="document.getElementById('formEdicaoVigencia:imageLoading').style.visibility = 'hidden';"/>

</a4j:outputPanel>
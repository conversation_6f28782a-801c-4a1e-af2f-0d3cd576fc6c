<%-- 
    Document   : include_panelgroup_produtos_validade_cliente
    Created on : 30/04/2012, 16:22:59
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup id="panelProdutosValidadeCliente">
    <table width="100%" border="0" align="left" cellpadding="0"
           cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
        <tr>
            <td align="left" valign="top" style="padding-bottom: 5px;">
                <div style="clear: both;" class="text">
                    <p style="margin-bottom: 6px;"><img
                            src="images/arrow2.gif" width="16" height="16"
                            style="vertical-align: middle; margin-right: 6px;">
                        <h:outputText style="font-weight: bold"
                                      value="Produtos com Validade" /></p>
                    <div class="sep" style="margin: 4px 0 5px 0;"><img
                            src="images/shim.gif"></div>
                </div>
            </td>
        </tr>
        <tr>
            <td align="left" valign="top">
                <rich:dataTable id="listaProdutoComValidade" width="100%" border="0"
                                rows="#{ClienteControle.nrPaginaMovProdutoValidade}"
                                cellspacing="0" cellpadding="10" styleClass="textsmall"
                                columnClasses="centralizado, centralizado, centralizado "
                                value="#{ClienteControle.clienteVO.listaProdutosComValidade}"
                                var="movProduto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Produto" />
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="blue"
                                      value="#{movProduto.produto.descricao}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Data Compra" />
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="blue"
                                      value="#{movProduto.dataLancamento_Apresentar}" />
                    </rich:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Data Inicio Vig�ncia" />
                        </f:facet>
                        <h:outputText styleClass="cinza"
                                      value="#{movProduto.dataInicioVigencia_Apresentar}" />
                    </h:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Data Final Vig�ncia" />
                        </f:facet>
                        <h:outputText style="font-weight: bold" styleClass="red"
                                      value="#{movProduto.dataFinalVigencia_Apresentar}" />
                        <rich:spacer width="10px"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Renov�vel Automaticamente" />
                        </f:facet>
                        <h:outputText style="font-weight: bold" styleClass="red"
                                      value="#{movProduto.renovavelAutomaticamente_Apresentar}" />
                        <rich:spacer width="10px"/>
                    </rich:column>
                </rich:dataTable>
                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                    <h:panelGroup>
                        <h:panelGrid columns="2">
                            <rich:datascroller align="center"
                                               for="listaProdutoComValidade" maxPages="100"
                                               id="scResultadoListaProdutoComValidade" />
                            <rich:inputNumberSpinner inputSize="4"
                                                     styleClass="form" enableManualInput="true"
                                                     minValue="1" maxValue="100"
                                                     value="#{ClienteControle.nrPaginaMovProdutoValidade}">
                                <a4j:support event="onchange"
                                             focus="scResultadoListaProdutoComValidade"
                                             reRender="listaProdutoComValidade,scResultadoListaProdutoComValidade" />
                            </rich:inputNumberSpinner>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>
            </td>
        </tr>
    </table>
</h:panelGroup>
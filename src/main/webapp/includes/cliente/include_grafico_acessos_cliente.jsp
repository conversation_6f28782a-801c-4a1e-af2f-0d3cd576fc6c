<%@include file="../imports.jsp" %>

<h:panelGroup style="width: 96%; height: auto; margin-bottom: 20px;" styleClass="painelDadosAluno tudo"
              layout="block"
              id="painelGraficosAcesso">


    <c:if test="${!TelaClienteControle.graficosAcessoCarregado}">
        <div style="width: 100%; text-align: center;">
            <script src="script/telaCliente1.3.js" type="text/javascript"></script>
            <script>
                function carregaMais() {
                    document.getElementById('form:carregadorAcessos').click();
                }
            </script>
            <a4j:commandLink id="carregadorAcessos" style="display: none;"
                             action="#{TelaClienteControle.carregarGraficosAcessos}"
                             reRender="painelGraficosAcesso"
                             status="false"
                             oncomplete="construirGraficos();">
            </a4j:commandLink>
        </div>
        <div id="divfundo"> </div>
    </c:if>
    <c:if test="${TelaClienteControle.graficosAcessoCarregado}">
        <div class="tituloPainelAluno">
            <h:outputText value="Acessos" styleClass="texto-size-16 negrito cinzaEscuro pl20"/>
            <h:outputLink styleClass="pl5" 
                          value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                          title="Clique e saiba mais: Acessos"
                          target="_blank" >
                <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
            </h:outputLink>
            <a4j:commandLink value="#{msg_menu.Menu_listaAcessos}"
                             id="listaAcessoTelaCliente"
                             styleClass="linkAzul texto-size-18 icon pull-right tooltipsterright"
                             style="margin:1vw;"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{TelaClienteControle.abrirPopUpListaAcessosRel};#{TelaClienteControle.mensagemNotificar}"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             title="Gerar relat�rio de acessos">
                <f:attribute name="funcionalidade" value="LISTA_ACESSOS" />
                <i id="gerarRelatorioAcessosPdf" class="fa-icon-pdf-o linkAzul texto-size-18"/>
            </a4j:commandLink>
        </div>

        <div style="width: 75%; padding-top: 15px; margin-bottom: 15px; float: left;  text-align: center;">
            <div style="width: 50%; float: left;">
                <h:outputText styleClass="texto-size-14 cinza" style="margin-left: 15px;" value="Acessos nas �ltimas semanas"/>
            </div>	
            <div style="width: 50%; float: left;">
                <h:outputText styleClass="texto-size-14 cinza" style="margin-left: 15px;" value="Acessos nos �ltimos meses"/>
            </div>	
            <div id="chartdivsemanas" style="width: 49%; margin-bottom: 15px; height: 300px; float: left;"></div>	
            <div id="chartdivmes" style="width: 49%; height: 300px;float: left;border-right: #E5E5E5 1px solid"></div>	
        </div> 
        <div style="width: 25%; padding-top: 15px; margin-bottom: 15px; float: left; text-align: center;">
            <h:outputText styleClass="texto-size-14 cinza" value="�ltimo acesso"/>

            <h:outputText rendered="#{empty TelaClienteControle.dataUltimoAcesso}"
                          styleClass="cinza texto-size-60"
                          style="display: block;margin-bottom: 20px; margin-top: 20px;" value="-"/>
            <h:outputText rendered="#{!empty TelaClienteControle.dataUltimoAcesso}"
                          id="ultimoAcessoCliente"
                          styleClass="cinza texto-size-30"
                          style="display: block;margin-bottom: 20px; margin-top: 20px;"
                          value="#{TelaClienteControle.dataUltimoAcesso}">
                <f:convertDateTime type="date" dateStyle="short"
                                   locale="pt"
                                   pattern="dd/MM/yyyy HH:mm" />
            </h:outputText>
            <h:outputText styleClass="texto-size-14 cinza" value="M�dia de frequ�ncia nas �ltimas 4 semanas"/>
            <h:outputText style="display: block;margin-top: 14px;"
                          styleClass="cinza texto-size-60"
                          value="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.mediaUltimasSemanas}"/>      
            <h:outputText styleClass="texto-size-14 cinza" value="M�dia de frequ�ncia dos �ltimos 4 meses"/>
            <h:outputText style="display: block;margin-top: 14px;margin-bottom: 20px;"
                          styleClass="cinza texto-size-60"
                          value="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.mediaUltimosMeses}"/>



        </div>



        <script>
            function construirGraficos() {
                var chartsemana = AmCharts.makeChart("chartdivmes", {
                    "type": "serial",
                    "theme": "light",
                    "startDuration": 1,
                    "balloon": {
                        "borderThickness": 1,
                        "shadowAlpha": 0
                    },
                    "chartCursor": {
                        "valueLineEnabled": true,
                        "valueLineBalloonEnabled": true,
                        "cursorColor": "#29ABE2",
                        "valueLineAlpha": 0.2
                    },
                    "dataProvider": [{
                            "mes": "4� M�s",
                            "acessos": ${TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoMes4}
                        }, {
                            "mes": "3� M�s",
                            "acessos": ${TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoMes3}
                        }, {
                            "mes": "2� M�s",
                            "acessos": ${TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoMes2}
                        }, {
                            "mes": "�ltimo m�s",
                            "acessos": ${TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoUltimoMes}
                        }],
                    "valueAxes": [{
                            "stackType": "regular",
                            "axisAlpha": 0.3,
                            "gridAlpha": 0
                        }],
                    "graphs": [{
                            "balloon": {
                                "drop": true,
                                "adjustBorderColor": false,
                                "color": "#ffffff"
                            },
                            "bullet": "round",
                            "bulletBorderAlpha": 1,
                            "bulletColor": "#FFFFFF",
                            "bulletSize": 5,
                            "hideBulletsCount": 50,
                            "lineThickness": 2,
                            "useLineColorForBulletBorder": true,
                            "balloonText": "<span class='texto-size-14'>[[category]]: <b>[[value]]</b></span>",
                            "labelText": "[[value]]",
                            "lineColor": "#29ABE2",
                            "valueField": "acessos"
                        }],
                    "categoryField": "mes",
                    "categoryAxis": {
                        "dashLength": 1,
                    }

                });

                var chart = AmCharts.makeChart("chartdivsemanas", {
                    "type": "serial",
                    "theme": "light",
                    "startDuration": 1,
                    "balloon": {
                        "borderThickness": 1,
                        "shadowAlpha": 0
                    },
                    "chartCursor": {
                        "valueLineEnabled": true,
                        "valueLineBalloonEnabled": true,
                        "cursorAlpha": 1,
                        "cursorColor": "#002C74",
                        "valueLineAlpha": 0.2
                    },
                    "dataProvider": [{
                            "semana": "Semana 04",
                            "acessos": ${TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoSemana4 != -1 ? TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoSemana4 : '0'}
                        }, {
                            "semana": "Semana 03",
                            "acessos": ${TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoSemana3 != -1 ? TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoSemana3 : '0'}
                        }, {
                            "semana": "Semana 02",
                            "acessos": ${TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoSemana2 != -1 ? TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoSemana2 : '0'}
                        }, {
                            "semana": "Semana 01",
                            "acessos": ${TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoSemanaPassada != -1 ? TelaClienteControle.cliente.situacaoClienteSinteticoVO.diasAcessoSemanaPassada : '0'}
                        }],
                    "valueAxes": [{
                            "stackType": "regular",
                            "axisAlpha": 0.3,
                            "gridAlpha": 0
                        }],
                    "graphs": [{
                            "bullet": "round",
                            "bulletBorderAlpha": 1,
                            "bulletColor": "#FFFFFF",
                            "bulletSize": 5,
                            "hideBulletsCount": 50,
                            "lineThickness": 2,
                            "useLineColorForBulletBorder": true,
                            "balloonText": "<span class='texto-size-14'>[[category]]: <b>[[value]]</b></span>",
                            "labelText": "[[value]]",
                            "lineColor": "#002C74",
                            "valueField": "acessos"
                        }],
                    "categoryField": "semana",
                    "categoryAxis": {
                        "dashLength": 1
                    }

                });
            }
            carregarTooltipster();
        </script>

        <div id="separatorACESSOSPERIODO"
             style="border-top: #E5E5E5 1px solid; display: table; margin-top: 20px; width: 100%;">
            
            <table class="tabelaDados semZebra">
                <tr class="noHover">
                    <td colspan="5">
                        <h:outputText value="Per�odos de Acesso"
                                      style="margin-bottom: 20px; display: block; text-transform: none;"
                                      styleClass="texto-size-14 negrito cinzaEscuro"/>
                    </td>
                </tr>
                <c:if test="${not empty TelaClienteControle.listaSelectItemPeriodoAcessoCliente}">
                    <tr>
                        <th>CONTRATO</th>
                        <th>IN�CIO DO ACESSO</th>
                        <th>FINAL DO ACESSO</th>
                        <th>TIPO DE ACESSO</th>
                        <th>LEGENDA</th>
                    </tr>
                </c:if>
                <c:if test="${empty TelaClienteControle.listaSelectItemPeriodoAcessoCliente}">
                    <tr>
                        <td style="text-transform: none;">O cliente ainda n�o tem per�odo de acesso.</td>
                    </tr>
                </c:if>
                

                <c:forEach var="periodo" items="${TelaClienteControle.listaSelectItemPeriodoAcessoCliente}" varStatus="myIndex">
                    <tr>
                        <td id="listaPeriodoAcessoClienteContrato${myIndex.index}">${periodo.contrato}
                            <c:if test="${periodo.contratoBaseadoRenovacao != 0}">
                                <span style="font-weight: bold">
                                    - Renova��o Referente ao ${periodo.contratoBaseadoRenovacao}
                                </span>
                            </c:if>
                        </td>
                        <td id="listaPeriodoAcessoClienteDataIni${myIndex.index}">${periodo.dataInicioAcesso_Apresentar}</td>
                        <td id="listaPeriodoAcessoClienteDataFim${myIndex.index}">${periodo.dataFinalAcesso_Apresentar}</td>
                        <td id="listaPeriodoAcessoClienteApresentar${myIndex.index}">${periodo.tipoAcesso_Apresentar}</td>
                        <td><i class="fa-icon-circle" style="color: ${periodo.permiteAcesso ? '#2BAF50' : '#FF5555'};"></i></img></td>
                    </tr>
                </c:forEach>
            </table>
        </div>
    </c:if>


    <div class="rodapePainelAluno" style="text-align: right;">
        <a4j:commandLink id="abaPeriodoAcessoCliente" styleClass="linkAzul" style="margin-right: 20px;"
                         oncomplete="abrirPopup('acessosCliente.jsp', 'Acessos', 880, 622);"
                         action="#{ClienteControle.pegarClienteTelaClienteComTratamento}">
            <i class="fa-icon-angle-right"></i> Ver acessos
            
        </a4j:commandLink>
    </div>
</h:panelGroup>


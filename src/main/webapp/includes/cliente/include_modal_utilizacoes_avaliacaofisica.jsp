<%@include file="../imports.jsp" %>

<rich:modalPanel id="modalUtilizacoesAvaliacaoFisica" autosized="true" shadowOpacity="true" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Avalia��es f�sicas pelo MovProduto"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkModalUtilizacoes"/>
            <rich:componentControl for="modalUtilizacoesAvaliacaoFisica" attachTo="hidelinkModalUtilizacoes" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formUtilizacoes">
        <h:panelGroup>
            <rich:dataTable value="#{TelaClienteControle.utilizacoes}" rows="10" id="tblUtilizacoes" rowKeyVar="status" var="item">
                <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>

                <rich:column sortBy="#{item.codAvaliacaoFisica}">
                    <f:facet name="header">
                        <h:outputText value="C�d. Avalia��o"/>
                    </f:facet>
                    <h:outputText value="#{item.codAvaliacaoFisica}"/>
                </rich:column>

                <rich:column sortBy="#{item.dataAvaliacaoFisica}">
                    <f:facet name="header">
                        <h:outputText value="Data da Avalia��o"/>
                    </f:facet>
                    <h:outputText style="font-weight:bold;font-size:9px;" value="#{item.dataAvaliacaoFisicaApresentar}"/>
                </rich:column>

                <rich:column sortBy="#{item.primeiraavaliacao}">
                    <f:facet name="header">
                        <h:outputText value="Primeira avalia��o?"/>
                    </f:facet>
                    <h:selectBooleanCheckbox disabled="true" readonly="true" value="#{item.primeiraavaliacao}"/>
                </rich:column>

                <f:facet name="footer">
                    <rich:datascroller for="tblUtilizacoes"/>
                </f:facet>
            </rich:dataTable>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>



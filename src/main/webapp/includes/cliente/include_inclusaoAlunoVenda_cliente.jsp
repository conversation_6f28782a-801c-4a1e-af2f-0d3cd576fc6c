<%@ taglib prefix="rick" uri="http://java.sun.com/jsf/html" %>
<%@include file="../../include_imports.jsp" %>
<h:panelGroup id="form" styleClass="col-md-12" style="margin-top: 20px">
    <h:panelGroup id="dadosCliente">
        <h:panelGroup styleClass="col-md-6">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Consultor"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="col-md-11 cb-container margenVertical">
                <h:selectOneMenu id="consultor"
                                 value="#{InclusaoVendaRapidaControle.consultor}">
                    <f:selectItems value="#{InclusaoVendaRapidaControle.listaSelectConsultor}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGroup>

        <c:if test="${not empty InclusaoVendaRapidaControle.listaSelectEventoVigente}">
            <h:panelGroup styleClass="col-md-6">
                <h:panelGroup layout="block" styleClass="title-space mtop20">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Evento"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="col-md-11 cb-container margenVertical">
                    <h:selectOneMenu value="#{InclusaoVendaRapidaControle.codigoEventosSelecionado}"
                                     id="comboEvento">
                        <f:selectItems value="#{InclusaoVendaRapidaControle.listaSelectEventoVigente}"/>
                        <a4j:support event="onchange" ajaxSingle="true"
                                     action="#{InclusaoVendaRapidaControle.enviarEventoSelecionado}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>
        </c:if>
        <h:panelGroup styleClass="col-md-6" id="pnlCPF">
            <c:if test="${!InclusaoVendaRapidaControle.pessoaEstrangeira}">
                <h:panelGroup styleClass="title-space mtop20" layout="block">
                    <h:outputText id="titleCPF" value="#{ClienteControle.displayIdentificadorFront[0]} "
                                  styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="col-md-12 margenVertical ">
                    <h:inputText styleClass="inputTextClean required cpfid col-md-11"
                                 onkeypress="return mascara(this.form, this.id , '999.999.999-99', event);"
                                 maxlength="14" id="cfp"
                                 value="#{InclusaoVendaRapidaControle.pessoaVO.cfp}">
                        <a4j:support event="onblur" action="#{InclusaoVendaRapidaControle.validarCpfExiste}"
                                     oncomplete="#{InclusaoVendaRapidaControle.msgAlert}"
                                     reRender="modalValidacaoCPF,formpendenciaprotheus, modalColaboradoresPorCpf,painelEndereco,pnlDadosCliente,comboplano,pnlMsgClienteRestricao"
                                     ajaxSingle="true"/>
                    </h:inputText>
                    <h:panelGroup id="pnlMsgClienteRestricao">
                        <h:panelGroup styleClass="col-md-11 msg-cliente-restricao"
                                     rendered="#{not empty InclusaoVendaRapidaControle.mensagemClienteRestricao}">
                            <i class="pct pct-alert-triangle" style="margin: 0px 10px 0px 0px"></i>
                            <h:outputText styleClass="" value="#{InclusaoVendaRapidaControle.mensagemClienteRestricao}"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="col-md-12">
                        <h:outputText
                                rendered="#{InclusaoVendaRapidaControle.pessoaVO.constaRestricaoSPC && !InclusaoVendaRapidaControle.pessoaVO.menorIdade}"
                                styleClass="texto-size-14 texto-cor-cinza texto-font"
                                style="margin-left: 8px; color: red"
                                id="restricaoCPF"
                                value="Consta uma restri��o para este CPF"/>
                    </h:panelGroup>
                </h:panelGroup>

                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional && ClienteControle.configuracaoSistema.cfpOb}">
                    <h:panelGroup styleClass="title-space mtop20">
                        <h:outputText id="titleIDIntercional"
                                      value="#{ClienteControle.displayIdentificadorFront[0]}"
                                      styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                    </h:panelGroup>
                    <h:inputText styleClass="col-md-11 inputTextClean required margenVertical cpfid"
                                 maxlength="14"
                                 id="cfpIn"
                                 value="#{InclusaoVendaRapidaControle.pessoaVO.cfp}">
                        <a4j:support event="onblur" action="#{InclusaoVendaRapidaControle.validarCpfExiste}"
                                     oncomplete="#{InclusaoVendaRapidaControle.msgAlert}"
                                     reRender="modalValidacaoCPF,formpendenciaprotheus,modalColaboradoresPorCpf,dadosCliente,painelEndereco,pnlDadosCliente"
                                     ajaxSingle="true"/>
                    </h:inputText>
                </c:if>

            </c:if>

            <c:if test="${InclusaoVendaRapidaControle.pessoaEstrangeira}">
                <h:panelGroup styleClass="mtop20" layout="block">
                    <h:outputText id="titleRNE" value="RNE (preencher somente se estrangeiro)"
                                  styleClass="col-md-11 texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                    <h:inputText styleClass="inputTextClean required margenVertical cpfid col-md-11"
                                 maxlength="32" id="rne"
                                 value="#{InclusaoVendaRapidaControle.pessoaVO.rne}">
                        <a4j:support event="onblur" action="#{InclusaoVendaRapidaControle.validarRneExiste}"
                                     oncomplete="#{InclusaoVendaRapidaControle.msgAlert}"
                                     reRender="modalValidacaoCPF,formpendenciaprotheus, modalColaboradoresPorCpf, painelEndereco"
                                     ajaxSingle="true"/>
                    </h:inputText>
                </h:panelGroup>
            </c:if>
        </h:panelGroup>
        <h:panelGroup
                styleClass="#{InclusaoVendaRapidaControle.temParcelaEmAberto ? 'col-md-1 mtop20': 'col-md-3 mtop20'}"
                layout="block" id="pnlPesquisarCpfSPC">
            <h:panelGroup styleClass="mtop20" layout="block">
                <a4j:commandButton id="pesquisarCpfSPC"
                                   styleClass="botoes nvoBt mtop20"
                                   onclick="limparMsgCarregando();"
                                   value="Consultar CPF no SPC"
                                   alt="Consultar cpf no spc"
                                   reRender="pnlCPF, email,dataNasc, restricaoCPF, nomeCompleto, sexo"
                                   rendered="#{!InclusaoVendaRapidaControle.pessoaVO.menorIdade &&
                                                                        InclusaoVendaRapidaControle.empresa.consultarNovoCadastroSPC &&
                                                                        !InclusaoVendaRapidaControle.empresa.pesquisaAutomaticaSPC}"
                                   action="#{InclusaoVendaRapidaControle.consultarCpfSpcManual}"/>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup styleClass="col-md-2  mtop20">
            <h:panelGroup layout="block" styleClass="title-space mtop20">
                <a4j:commandButton id="ircaixa"
                                   value="Ir para caixa em aberto"
                                   alt="Ir para caixa em aberto"
                                   styleClass="botoes nvoBt btSec"
                                   rendered="#{InclusaoVendaRapidaControle.temParcelaEmAberto}"
                                   style="margin-left: 10px; vertical-align: middle;"
                                   action="#{InclusaoVendaRapidaControle.irParaCaixa}"/>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup id="rowDocuments"
                      styleClass="#{InclusaoVendaRapidaControle.temParcelaEmAberto ? 'col-md-2 mtop20' : 'col-md-3'}"
                      layout="block">
            <h:panelGroup
                    styleClass="#{InclusaoVendaRapidaControle.temParcelaEmAberto ? 'title-space mtop20' : 'title-space'}"
                    layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                              id="titlePessoaEstrangeira"
                              value="Aluno estrangeiro"/>
                <h:selectBooleanCheckbox styleClass="inputTextClean required cpfid"
                                         id="boolPessoaEstrangeira"
                                         style="margin-left: 10px;"
                                         value="#{InclusaoVendaRapidaControle.pessoaEstrangeira}">
                    <a4j:support event="onchange" reRender="pnlCPF"/>
                </h:selectBooleanCheckbox>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup styleClass="col-md-12 title-space mtop20" id="pnlDadosCliente">
            <h:panelGroup styleClass="col-md-6" id="pnlNome">
                <h:panelGroup styleClass="title-space mtop20">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  value="Nome Completo"/>
                </h:panelGroup>
                <h:panelGroup layout="block">
                    <h:inputText styleClass="inputTextClean margenVertical required nomeid col-md-11"
                                 id="nomeCompleto"
                                 tabindex="1"
                                 value="#{InclusaoVendaRapidaControle.pessoaVO.nome}">
                        <a4j:support event="onblur" action="#{InclusaoVendaRapidaControle.validarNomeExiste}"
                                     focus="dataNasc"
                                     oncomplete="#{InclusaoVendaRapidaControle.msgAlert}"
                                     reRender="modalValidacaoCPF,formpendenciaprotheus, modalColaboradoresPorCpf"
                                     ajaxSingle="true"/>
                    </h:inputText>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup styleClass="col-md-6">
                <c:if test="${!InclusaoVendaRapidaControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
                    <h:panelGroup styleClass="col-md-6">
                        <h:panelGroup styleClass="title-space mtop20">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          value="Nascimento"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block">
                            <h:inputText id="dataNasc" size="14" tabindex="2" maxlength="10" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         onkeypress="return mascara(this.form, this.id , '99/99/9999', event);"
                                         styleClass="inputTextClean tamanhoInputPequeno margenVertical required nascimentoid col-md-11"
                                         value="#{InclusaoVendaRapidaControle.pessoaVO.dataNasc}">
                                <f:convertDateTime pattern="dd/MM/yyyy"
                                                   locale="#{SuperControle.localeDefault}"
                                                   timeZone="#{SuperControle.timeZoneDefault}"/>
                                <a4j:support event="onchange"
                                             reRender="pesquisarCpfSPCMae, pnlEmailObrigatorio, pnlNomeResponsavelObrigatorio, pnlCPFObrigatorio, pnlDataNascimentoObrigatorio, pnlResponsavel, pnlPesquisarCpfSPC"
                                             action="#{InclusaoVendaRapidaControle.validarIdade}"
                                             ajaxSingle="true"/>
                            </h:inputText>
                        </h:panelGroup>
                    </h:panelGroup>
                </c:if>

                <c:if test="${InclusaoVendaRapidaControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
                    <h:panelGroup styleClass="col-md-6">
                        <h:panelGroup styleClass="title-space mtop20">
                            <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                          value="Nascimento (MM/DD/YYYY):"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="col-md-11">
                            <h:inputText id="dataNascMMDDYYYY" size="14" tabindex="2" maxlength="10"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         onkeypress="return mascara(this.form, this.id , '99/99/9999', event);"
                                         styleClass="inputTextClean tamanhoInputPequeno margenVertical required nascimentoid"
                                         value="#{InclusaoVendaRapidaControle.pessoaVO.dataNasc}">
                                <f:convertDateTime pattern="MM/dd/yyyy"
                                                   locale="#{SuperControle.localeDefault}"
                                                   timeZone="#{SuperControle.timeZoneDefault}"/>
                                <a4j:support event="onchange"
                                             reRender="pesquisarCpfSPCMae, pnlEmailObrigatorio, pnlNomeResponsavelObrigatorio, pnlCPFObrigatorio, pnlDataNascimentoObrigatorio, pnlPesquisarCpfSPCMae, pnlPesquisarCpfSPC"
                                             oncomplete="#{InclusaoVendaRapidaControle.erro ? '' : 'setFocusOnNextInput(this);'}"
                                             focus="dataNascMMDDYYYY"
                                             action="#{InclusaoVendaRapidaControle.validarIdade}"/>
                            </h:inputText>
                        </h:panelGroup>
                    </h:panelGroup>
                </c:if>

                <h:panelGroup styleClass="col-md-6">
                    <h:panelGroup styleClass="title-space mtop20">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="Sexo biol�gico"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:selectOneRadio
                                styleClass="texto-size-14-real required sexoid texto-cor-cinza texto-font row-md-9"
                                style="vertical-align: top; margin-top: 10px;font-size: 16px"
                                id="sexo"
                                value="#{InclusaoVendaRapidaControle.pessoaVO.sexo}">
                            <f:selectItem itemLabel="Feminino" itemValue="F"/>
                            <f:selectItem itemLabel="Masculino" itemValue="M"/>
                        </h:selectOneRadio>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup styleClass="col-md-6" id="pnlEmail">
                <h:panelGroup styleClass="title-space mtop20" layout="block">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold " value="Email"/>
                </h:panelGroup>
                <h:panelGroup layout="block">
                    <h:inputText id="email" tabindex="3"
                                 styleClass="inputTextClean margenVertical required emailid col-md-11"
                                 value="#{InclusaoVendaRapidaControle.emailVO.email}"/>
                </h:panelGroup>
            </h:panelGroup>

            <c:if test="${!InclusaoVendaRapidaControle.configuracaoSistema.usarSistemaInternacional}">
                <h:panelGroup styleClass="col-md-3">
                    <h:panelGroup styleClass="title-space mtop20" layout="block">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="Tel. celular"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:inputText styleClass="inputTextClean margenVertical required celularid col-md-11"
                                     id="telefoneCelularVO"
                                     tabindex="4"
                                     onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                     maxlength="13"
                                     value="#{InclusaoVendaRapidaControle.telefoneCelularVO.numero}"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup styleClass="col-md-3">
                    <h:panelGroup styleClass="title-space mtop20" layout="block">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="Tel. fixo"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:inputText styleClass="inputTextClean margenVertical required telefoneid col-md-10"
                                     id="telefoneResidencialVO"
                                     tabindex="5"
                                     onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                     maxlength="13"
                                     value="#{InclusaoVendaRapidaControle.telefoneResidencialVO.numero}"/>
                    </h:panelGroup>
                </h:panelGroup>
            </c:if>
        </h:panelGroup>

        <h:panelGroup
                layout="block"
                styleClass="col-md-6"
                rendered="#{InclusaoVendaRapidaControle.exibirInputIndicadoPor}">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Indicado por"/>
            </h:panelGroup>
            <h:panelGroup layout="block">
                <h:inputText id="indicadorPor"
                             size="50"
                             maxlength="50"
                             onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             styleClass="inputTextClean col-md-11"/>
            </h:panelGroup>
            <rich:suggestionbox
                    height="200"
                    width="490"
                    for="indicadorPor"
                    fetchValue="#{cliente.pessoa.nome}"
                    suggestionAction="#{InclusaoVendaRapidaControle.consultarClientes}"
                    minChars="3"
                    rowClasses="20"
                    status="statusHora"
                    nothingLabel="Nenhum Cliente encontrado !"
                    var="cliente"
                    id="suggestionIndicadoPor">
                <a4j:support event="onselect"
                             action="#{InclusaoVendaRapidaControle.selecionarClienteIndicacao}"
                             reRender="panelBotoesControle, mensagem,panelDadosProdutosPeriodo"/>
                <h:column>
                    <h:outputText styleClass="texto-font texto-size-14-real" value="#{cliente.pessoa.nome}"/>
                </h:column>
                <h:column>
                    <h:outputText styleClass="texto-font texto-size-14-real" value="#{cliente.matricula}"/>
                </h:column>
                <h:column>
                    <h:outputText styleClass="texto-font texto-size-14-real" value="#{cliente.situacao_Apresentar}"/>
                </h:column>
            </rich:suggestionbox>
        </h:panelGroup>

        <c:if test="${InclusaoVendaRapidaControle.configuracaoSistema.usarSistemaInternacional}">
            <h:panelGroup styleClass="col-md-3">
                <h:panelGroup styleClass="title-space mtop20" layout="block">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  value="Tel. celular"/>
                </h:panelGroup>
                <h:panelGroup layout="block">
                    <h:inputText styleClass="inputTextClean margenVertical required celularid col-md-11"
                                 id="telefoneCelularVOSemMascara"
                                 tabindex="6"
                                 maxlength="13"
                                 value="#{InclusaoVendaRapidaControle.telefoneCelularVO.numero}"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup styleClass="col-md-3">
                <h:panelGroup styleClass="title-space mtop20">
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Tel. fixo"/>
                </h:panelGroup>
                <h:panelGroup layout="block">
                    <h:inputText styleClass="inputTextClean margenVertical required telefoneid col-md-11"
                                 tabindex="7"
                                 id="telefoneResidencialVOSemMascara"
                                 maxlength="13"
                                 value="#{InclusaoVendaRapidaControle.telefoneResidencialVO.numero}"/>
                </h:panelGroup>
            </h:panelGroup>
        </c:if>
       <h:panelGroup id="pnlResponsavel">
           <c:if test="${!InclusaoVendaRapidaControle.plano.planoPersonal}">
               <h:panelGroup styleClass="md-12">
                   <h:panelGroup styleClass="col-md-6" id="pnlCpfMae">
                       <h:panelGroup styleClass="title-space mtop20" layout="block">
                           <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                         rendered="#{not InclusaoVendaRapidaControle.responsavelObrigatorio}"
                                         value="#{ClienteControle.displayIdentificadorFront[0]} do respons�vel: "/>
                           <h:panelGroup id="pnlCPFObrigatorio">
                               <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                             rendered="#{InclusaoVendaRapidaControle.responsavelObrigatorio}"
                                             value="CPF do respons�vel: (obrigat�rio para menor de idade)"/>
                           </h:panelGroup>
                       </h:panelGroup>
                       <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                           <h:panelGroup layout="block">
                               <h:inputText styleClass="inputTextClean margenVertical required cpfrespon col-md-11"
                                            id="cpfCNPJTerceiro"
                                            onkeypress="return mascara(this.form, this.id , '999.999.999-99', event);"
                                            maxlength="14"
                                            value="#{InclusaoVendaRapidaControle.pessoaVO.cpfMae}">
                                   <a4j:support event="onblur"
                                                focus="nomeMae"
                                                action="#{InclusaoVendaRapidaControle.validarCPFMaeVendaRapida}"
                                                reRender="modalValidacaoCPF,formpendenciaprotheus, modalColaboradoresPorCpf, pesquisarCpfSPCMae"
                                                ajaxSingle="true"/>
                               </h:inputText>
                           </h:panelGroup>
                       </c:if>
                       <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                           <h:panelGroup layout="block">
                               <h:inputText styleClass="inputTextClean margenVertical required cpfrespon col-md-11"
                                            id="cpfCNPJTerceiro"
                                            maxlength="14"
                                            value="#{InclusaoVendaRapidaControle.pessoaVO.cpfMae}">
                                   <a4j:support event="onblur"
                                                focus="nomeMae"
                                                action="#{InclusaoVendaRapidaControle.validarCPFMaeVendaRapida}"
                                                reRender="modalValidacaoCPF,formpendenciaprotheus, modalColaboradoresPorCpf, pesquisarCpfSPCMae"
                                                ajaxSingle="true"/>
                               </h:inputText>
                           </h:panelGroup>
                       </c:if>
                       <h:panelGroup layout="block" styleClass="col-md-12" id="textoRestricaoMae">
                           <h:outputText
                                   rendered="#{InclusaoVendaRapidaControle.pessoaVO.constaRestricaoSPC && InclusaoVendaRapidaControle.pessoaVO.menorIdade}"
                                   styleClass="texto-size-14 texto-cor-cinza texto-font"
                                   style="margin-left: 8px; color: red"
                                   id="restricaoCPFMae"
                                   value="Consta uma restri��o para este CPF"/>
                       </h:panelGroup>
                   </h:panelGroup>

                   <h:panelGroup styleClass="col-md-6"
                                 id="pnlPesquisarCpfSPCMae"
                                 rendered="#{InclusaoVendaRapidaControle.empresa.consultarNovoCadastroSPC &&
                                                           !InclusaoVendaRapidaControle.empresa.pesquisaAutomaticaSPC
                                                           && InclusaoVendaRapidaControle.pessoaVO.menorIdade}">
                       <h:panelGroup styleClass="title-space" layout="block" style="margin-top: 20px; margin-bottom: 20px">
                           <a4j:commandButton id="pesquisarCpfSPCMae"
                                              onclick="limparMsgCarregando();"
                                              styleClass="botoes nvoBt"
                                              value="Consultar CPF no SPC"
                                              style="margin-top: 30px"
                                              alt="Consultar cpf no SPC"
                                              reRender="textoRestricaoMae, pnlNome, pnlCpfMae, nomeMae, dataNascResponsavel, pnlNomeResponsavel"
                                              action="#{InclusaoVendaRapidaControle.consultarCpfSpcManual}"/>
                       </h:panelGroup>
                   </h:panelGroup>

                   <h:panelGroup styleClass="col-md-6">
                       <h:panelGroup styleClass="title-space mtop20" layout="block">
                           <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                         value="Nome do respons�vel: "/>
                           <h:panelGroup id="pnlNomeResponsavelObrigatorio">
                               <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                             rendered="#{InclusaoVendaRapidaControle.responsavelObrigatorio}"
                                             value="(obrigat�rio para menor de idade)"/>
                           </h:panelGroup>
                       </h:panelGroup>
                       <h:panelGroup>
                           <h:inputText id="nomeMae"
                                        styleClass="inputTextClean margenVertical required nomerespon col-md-11"
                                        tabindex="9"
                                        value="#{InclusaoVendaRapidaControle.pessoaVO.nomeMae}"/>
                       </h:panelGroup>
                   </h:panelGroup>

                   <h:panelGroup styleClass="col-md-6" id="pnlDataNascimento">
                       <h:panelGroup styleClass="title-space mtop20" layout="block">
                           <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                         value="Nascimento do respons�vel: "/>
                           <h:panelGroup id="pnlDataNascimentoObrigatorio">
                               <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                             rendered="#{InclusaoVendaRapidaControle.responsavelObrigatorio}"
                                             value="(obrigat�rio para menor de idade)"/>
                           </h:panelGroup>
                       </h:panelGroup>
                       <h:panelGroup>
                           <h:inputText id="dataNascResponsavel" size="14" maxlength="10"
                                        onblur="blurinput(this);"
                                        onfocus="focusinput(this);"
                                        tabindex="10"
                                        onkeypress="return mascara(this.form, this.id , '99/99/9999', event);"
                                        styleClass="inputTextClean tamanhoInputPequeno margenVertical required nascimentoid col-md-11"
                                        value="#{InclusaoVendaRapidaControle.pessoaVO.dataNascimentoResponsavel}">
                               <f:convertDateTime pattern="dd/MM/yyyy"
                                                  locale="#{SuperControle.localeDefault}"
                                                  timeZone="#{SuperControle.timeZoneDefault}"/>
                           </h:inputText>
                       </h:panelGroup>
                   </h:panelGroup>

                   <h:panelGroup styleClass="col-md-6">
                       <h:panelGroup styleClass="title-space mtop20" layout="block">
                           <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                         value="E-mail respons�vel: "/>
                           <h:panelGroup id="pnlEmailObrigatorio">
                               <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                             rendered="#{InclusaoVendaRapidaControle.responsavelObrigatorio}"
                                             value="(obrigat�rio para menor de idade)"/>
                           </h:panelGroup>
                       </h:panelGroup>
                       <h:panelGroup>
                           <h:inputText id="emailResponsavel"
                                        tabindex="11"
                                        styleClass="inputTextClean margenVertical required emailResponsavel col-md-11"
                                        value="#{InclusaoVendaRapidaControle.pessoaVO.emailMae}">
                           </h:inputText>
                       </h:panelGroup>
                   </h:panelGroup>
               </h:panelGroup>
           </c:if>
           <h:panelGroup id="pnlDependente" styleClass="col-md-12 title-space mtop20">
               <c:if test="${InclusaoVendaRapidaControle.apresentarDependente}">
                   <h:panelGroup layout="block" styleClass="col-md-6">
                       <h:outputText styleClass="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold"
                                     value="Nome do dependente:"/>
                       <h:inputText id="dependente" styleClass="inputTextClean margenVertical required emailid col-md-11"
                                    tabindex="12"
                                    value="#{InclusaoVendaRapidaControle.responsavel}"/>

                       <rich:suggestionbox height="200" width="610"
                                           for="dependente"
                                           status="statusInComponent"
                                           fetchValue="#{result.nome_Apresentar}"
                                           immediate="true"
                                           suggestionAction="#{InclusaoVendaRapidaControle.executarAutoCompletePossivelDependente}"
                                           minChars="1" rowClasses="20"
                                           nothingLabel="Nenhum cliente encontrado!"
                                           var="result"
                                           id="suggestionDependente">
                           <a4j:support event="onselect"
                                        reRender="pnlDependente"
                                        action="#{InclusaoVendaRapidaControle.selecionarDependenteSuggestionBox}"/>
                           <h:column>
                               <h:outputText value="#{result.nome_Apresentar}"/>
                           </h:column>
                       </rich:suggestionbox>
                   </h:panelGroup>
               </c:if>
           </h:panelGroup>
       </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup styleClass="col-md-12 mtop20">
        <hr class="dividerFundoEscuro">
    </h:panelGroup>

    <h:panelGroup id="painelEndereco" layout="block" styleClass="col-md-12">

        <h:panelGroup styleClass="col-md-11">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="CEP"/>
            </h:panelGroup>

            <a4j:jsFunction name="consultarCep" oncomplete="#{InclusaoVendaRapidaControle.mensagemNotificar}"
                            action="#{InclusaoVendaRapidaControle.consultarCEP}"
                            reRender="painelEndereco"/>
            <h:panelGroup>
                <h:inputText id="CEP" tabindex="13"
                             styleClass="inputTextClean margenVertical required cepid col-md-6"
                             maxlength="10"
                             onkeypress="return mascara(this.form, this.id, '99.999-999', event);"
                             onkeyup="if(this.value.length === 10){consultarCep();}"
                             value="#{InclusaoVendaRapidaControle.enderecoResidencialVO.cep}"/>
            </h:panelGroup>
            <h:panelGroup styleClass="title-space mtop20 col-md-4" layout="block" style="margin-left: 4%">
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12 mtop20"
                              rendered="#{InclusaoVendaRapidaControle.configuracaoSistema.cepPendente}"/>

                <h:outputText styleClass="texto-size-14 texto-cor-cinza mtop20"
                              value="N�o sabe seu CEP?"/>

                <a4j:commandLink id="consultaDadosCep" title="Consultar CEP" reRender="formConsultarCEP"
                                 value="Clique aqui!"
                                 oncomplete="Richfaces.showModalPanel('panelCEP'), setFocus(formConsultarCEP,'formConsultarCEP:estadoCEP');"
                                 styleClass="texto-size-14 texto-cor-azul linkPadrao padding-5 mtop20"/>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup styleClass="col-md-6">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Pa�s"/>
            </h:panelGroup>
            <h:panelGroup styleClass="cb-container margenVertical tamanhoInputMedio required paisid col-md-11"
                          layout="block">
                <h:selectOneMenu id="pais"
                                 value="#{InclusaoVendaRapidaControle.pessoaVO.pais.codigo}">
                    <a4j:support event="onchange" ajaxSingle="true" reRender="estado" focus="pais"
                                 action="#{InclusaoVendaRapidaControle.montarListaSelectItemEstado}"/>
                    <f:selectItems value="#{InclusaoVendaRapidaControle.listaSelectItemPais}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup styleClass="col-md-3">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="UF"/>
            </h:panelGroup>
            <h:panelGroup styleClass="cb-container margenVertical tamanhoInputMedio required estadoid col-md-11"
                          layout="block">
                <h:selectOneMenu id="estado"
                                 value="#{InclusaoVendaRapidaControle.pessoaVO.estadoVO.codigo}">
                    <a4j:support event="onchange" ajaxSingle="true" reRender="cidade"
                                 action="#{InclusaoVendaRapidaControle.montarListaSelectItemCidade}"
                                 focus="estado"/>
                    <f:selectItems value="#{InclusaoVendaRapidaControle.listaSelectItemEstado}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup styleClass="col-md-3">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Cidade"/>
            </h:panelGroup>
            <h:panelGroup id="cidade"
                          styleClass="cb-container margenVertical tamanhoInputMedio required cidadeid col-md-10"
                          layout="block">
                <h:selectOneMenu value="#{InclusaoVendaRapidaControle.pessoaVO.cidade.codigo}">
                    <f:selectItems value="#{InclusaoVendaRapidaControle.listaSelectItemCidade}"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGroup styleClass="col-md-6">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Endere�o"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText styleClass="inputTextClean margenVertical enderecoid required col-md-11"
                             tabindex="14"
                             id="endereco"
                             value="#{InclusaoVendaRapidaControle.enderecoResidencialVO.endereco}"/>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup styleClass="col-md-6">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Bairro"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText styleClass="inputTextClean margenVertical col-md-11"
                             tabindex="15"
                             id="bairro"
                             value="#{InclusaoVendaRapidaControle.enderecoResidencialVO.bairro}"/>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGroup styleClass="col-md-6">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="Complemento"/>
            </h:panelGroup>
            <h:inputText value="#{InclusaoVendaRapidaControle.enderecoResidencialVO.complemento}"
                         id="complemento"
                         tabindex="16"
                         styleClass="inputTextClean margenVertical col-md-11"/>
        </h:panelGroup>
        <h:panelGroup styleClass="col-md-3">
            <h:panelGroup styleClass="title-space mtop20" layout="block">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="N�mero"/>
            </h:panelGroup>
            <h:inputText value="#{InclusaoVendaRapidaControle.enderecoResidencialVO.numero}"
                         id="numero"
                         tabindex="17"
                         styleClass="inputTextClean margenVertical"/>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup id="enderecos">
        <h:panelGroup layout="block"
                      styleClass="col-md-12" style="margin-top: 20px">
            <a4j:commandLink id="adicionarEnderecoComercial" title="Clique para adicionar endere�o comercial"
                             reRender="enderecos"
                             action="#{InclusaoVendaRapidaControle.acaoAdicionarEnderecoComercial}"
                             value="Adicionar endere�o comercial"
                             styleClass="texto-size-14 texto-cor-azul linkPadrao padding-5">
                <c:if test="${InclusaoVendaRapidaControle.adicionarEnderecoComercial}">
                    <i class="fa-icon-double-angle-down"></i>
                </c:if>
                <c:if test="${!InclusaoVendaRapidaControle.adicionarEnderecoComercial}">
                    <i class="fa-icon-double-angle-up"></i>
                </c:if>
            </a4j:commandLink>
        </h:panelGroup>

        <c:if test="${InclusaoVendaRapidaControle.adicionarEnderecoComercial}">
            <h:panelGroup id="painelEnderecoComercial"
                          layout="block"
                          styleClass="col-md-12" style="margin-top: 20px">

                <h:outputText styleClass="col-md-12 texto-size-16 texto-cor-cinza-2 texto-font texto-bold"
                              value="ENDERE�O COMERCIAL"/>

                <h:panelGroup styleClass="col-md-11 title-space mtop20">
                    <h:panelGroup styleClass="title-space mtop20" layout="block">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold" value="CEP"/>
                    </h:panelGroup>

                    <a4j:jsFunction name="consultarCepComercial"
                                    oncomplete="#{InclusaoVendaRapidaControle.mensagemNotificar}"
                                    action="#{InclusaoVendaRapidaControle.consultarCEPComercial}"
                                    reRender="painelEnderecoComercial"/>

                    <h:inputText id="CEPComercial" tabindex="18"
                                 styleClass="inputTextClean margenVertical required cepid col-md-6"
                                 maxlength="10"
                                 onkeypress="return mascara(this.form, this.id, '99.999-999', event);"
                                 onkeyup="if(this.value.length == 10){consultarCepComercial();}"
                                 value="#{InclusaoVendaRapidaControle.enderecoComercialVO.cep}"/>

                    <h:panelGroup styleClass="title-space mtop20 col-md-4" layout="block" style="margin-left: 4%">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza" value="N�o sabe seu CEP?"/>
                        <a4j:commandLink id="consultaDadosCepComercial" title="Consultar CEP"
                                         reRender="formConsultarCEPComercial"
                                         value="Clique aqui!"
                                         oncomplete="Richfaces.showModalPanel('panelCEPComercial'), setFocus(formConsultarCEPComercial,'formConsultarCEPComercial:estadoCEPComercial');"
                                         styleClass="texto-size-14 texto-cor-azul linkPadrao padding-5"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup styleClass="col-md-6">
                    <h:panelGroup styleClass="title-space mtop20" layout="block">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="Endere�o:"/>
                    </h:panelGroup>
                    <h:inputText styleClass="col-md-11 inputTextClean margenVertical enderecoid required"
                                 tabindex="19"
                                 value="#{InclusaoVendaRapidaControle.enderecoComercialVO.endereco}"/>
                </h:panelGroup>

                <h:panelGroup styleClass="col-md-6">
                    <h:panelGroup styleClass="title-space mtop20" layout="block">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="Bairro:"/>
                    </h:panelGroup>
                    <h:inputText styleClass="col-md-11 inputTextClean margenVertical"
                                 tabindex="20"
                                 value="#{InclusaoVendaRapidaControle.enderecoComercialVO.bairro}"/>
                </h:panelGroup>

                <h:panelGroup styleClass="col-md-6 title-space mtop20">
                    <h:panelGroup styleClass="title-space mtop20" layout="block">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="Complemento"/>
                    </h:panelGroup>
                    <h:inputText value="#{InclusaoVendaRapidaControle.enderecoComercialVO.complemento}"
                                 tabindex="21"
                                 styleClass="col-md-11 inputTextClean margenVertical"/>
                </h:panelGroup>

                <h:panelGroup styleClass="col-md-3 title-space mtop20">
                    <h:panelGroup styleClass="title-space mtop20" layout="block">
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                      value="N�mero:"/>
                    </h:panelGroup>
                    <h:inputText value="#{InclusaoVendaRapidaControle.enderecoComercialVO.numero}"
                                 tabindex="22"
                                 styleClass="inputTextClean margenVertical"/>
                </h:panelGroup>
            </h:panelGroup>
        </c:if>
    </h:panelGroup>
    <h:panelGroup styleClass="col-md-12 mtop20">
        <hr class="dividerFundoEscuro">
    </h:panelGroup>
</h:panelGroup>

<%@include file="../../include_imports.jsp" %>

<style>

    .disabled {
        background-color: #D3D3D3 !important;
    }

</style>

<h:panelGroup styleClass="col-md-12 required formaid" style="margin-top: 20px" layout="block" id="formaspagamento">


    <div class="col-md-3 title-space mtop20">
        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Forma de Pagamento:</span>
        <h:panelGroup layout="block" styleClass="cb-container margenVertical tamanhoInputMedio required">
            <h:selectOneMenu id="cbFormaPagamentoVendaDireta" value="#{InclusaoVendaRapidaControle.formaPagamentoSelecinada}">
                <f:selectItems value="#{InclusaoVendaRapidaControle.listaFormaPagamento}" />
                <a4j:support event="onchange" reRender="form:formaspagamento,form:idValorMensalidade,form:pnlBotoes,form:formaspagamento"/>
            </h:selectOneMenu>
        </h:panelGroup>
    </div>

    <div class="col-md-3 title-space mtop20">
        <h:panelGroup layout="block">
            <h:panelGroup rendered="#{InclusaoVendaRapidaControle.mostrarTipoParcelamentoOperadora}" layout="block">
                <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Tipo parcelamento:</span>
                <h:panelGroup layout="block" styleClass="cb-container margenVertical tamanhoInputMedio required">
                    <h:selectOneMenu id="cbTipoParcelamentoVendaDireta"
                                     style="#{not InclusaoVendaRapidaControle.permitirMudarTipoParcelamentoVendaRapida ? 'background-color: #d3d3d3 !important;' : ''}"
                                     disabled="#{not InclusaoVendaRapidaControle.permitirMudarTipoParcelamentoVendaRapida}"
                                     value="#{InclusaoVendaRapidaControle.tipoParcelamentoEscolhido}">
                        <f:selectItems value="#{InclusaoVendaRapidaControle.listaTipoParcelamento}"/>
                        <a4j:support event="onchange" reRender="form:formaspagamento, form:idValorMensalidade"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          rendered="#{InclusaoVendaRapidaControle.apresentarSelectItemConvenioCobrancaBoleto}">
                <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Conv�nio:</span>
                <h:panelGroup layout="block" styleClass="cb-container margenVertical tamanhoInputMedio required">
                    <h:selectOneMenu id="selectItemConvenioCobrancaBoleto"
                                     value="#{InclusaoVendaRapidaControle.convenioCobrancaBoletoSelecionado}">
                        <f:selectItems value="#{InclusaoVendaRapidaControle.selectItemConvenioCobrancaBoleto}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </div>

    <div class="col-md-3 title-space mtop20">
        <h:panelGroup id="pgQuantidadeParcelamento" layout="block">
            <h:panelGroup rendered="#{InclusaoVendaRapidaControle.tipoParcelamentoOperadora}" layout="block">
                <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Quantidade de parcelas:</span>
                <h:panelGroup layout="block" styleClass="cb-container margenVertical tamanhoInputMedio required">
                    <h:selectOneMenu id="cbQuantidadeParcelas"
                                     value="#{InclusaoVendaRapidaControle.quantidadeParcelamento}">
                        <f:selectItems value="#{InclusaoVendaRapidaControle.listaQuantidadeParcelas}"/>
                        <a4j:support event="onchange" reRender="form:idValorMensalidade"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </div>


    <h:panelGroup id="pgOpcoesFormaPagamento" layout="block">
        <h:panelGroup layout="block" styleClass="col-md-12 mtop20"
                      rendered="#{InclusaoVendaRapidaControle.mostrarPagamentoCredito}">
            <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Cart�o de cr�dito:</span>

            <div class="col-md-12 mtop20">
                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="VISA"
                                 reRender="formaspagamento"
                                 oncomplete="adicionarPlaceHolderAutorizacaoCobrancaVendaRapida(),adicionarPlaceHolderAutorizacaoCobrancaVendaRapida()">
                    <h:graphicImage url="./imagens/bandeiras/visa.png" style="width: 90px;"/>
                    <f:attribute name="t" value="VISA"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="MASTERCARD"
                                 reRender="formaspagamento"
                                 oncomplete="adicionarPlaceHolderAutorizacaoCobrancaVendaRapida(),adicionarPlaceHolderAutorizacaoCobrancaVendaRapida()">
                    <h:graphicImage url="./imagens/bandeiras/mastercard.png" style="width: 90px; margin-left: 20px"/>
                    <f:attribute name="t" value="MASTERCARD"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="AMEX"
                                 reRender="formaspagamento"
                                 oncomplete="adicionarPlaceHolderAutorizacaoCobrancaVendaRapida(),adicionarPlaceHolderAutorizacaoCobrancaVendaRapida()">
                    <h:graphicImage url="./imagens/bandeiras/amex.png" style="width: 90px; margin-left: 20px"/>
                    <f:attribute name="t" value="AMEX"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="DINERS"
                                 reRender="formaspagamento"
                                 oncomplete="adicionarPlaceHolderAutorizacaoCobrancaVendaRapida(),adicionarPlaceHolderAutorizacaoCobrancaVendaRapida()">
                    <h:graphicImage url="./imagens/bandeiras/diners.png" style="width: 90px; margin-left: 20px"/>
                    <f:attribute name="t" value="DINERS"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="ELO"
                                 reRender="formaspagamento"
                                 oncomplete="adicionarPlaceHolderAutorizacaoCobrancaVendaRapida(),adicionarPlaceHolderAutorizacaoCobrancaVendaRapida()">
                    <h:graphicImage url="./imagens/bandeiras/elo.png" style="width: 90px; margin-left: 20px"/>
                    <f:attribute name="t" value="ELO"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="HIPERCARD"
                                 reRender="formaspagamento"
                                 oncomplete="adicionarPlaceHolderAutorizacaoCobrancaVendaRapida(),adicionarPlaceHolderAutorizacaoCobrancaVendaRapida()">
                    <h:graphicImage url="./imagens/bandeiras/hipercard_new.png" style="width: 90px; margin-left: 20px; border-radius: 5px; height: 60px;"/>
                    <f:attribute name="t" value="HIPERCARD"/>
                </a4j:commandLink>

            </div>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="col-md-12 mtop20"
                      rendered="#{InclusaoVendaRapidaControle.mostrarPagamentoDebitoConta}">
            <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">D�bito autom�tico em conta corrente:</span>
            <div class="col-md-12 mtop20">
                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="ITAU"
                                 reRender="formaspagamento">
                    <h:graphicImage url="./imagens/bancos/itau.png" style="width: 90px;border-radius: 5px;"/>
                    <f:attribute name="t" value="ITAU"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="BRADESCO"
                                 reRender="formaspagamento">
                    <h:graphicImage url="./imagens/bancos/bradesco.png"
                                    style="width: 90px; margin-left: 20px; border-radius: 5px;"/>
                    <f:attribute name="t" value="BRADESCO"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="SANTANDER"
                                 reRender="formaspagamento">
                    <h:graphicImage url="./imagens/bancos/santander.png"
                                    style="width: 90px; margin-left: 20px; border-radius: 5px;"/>
                    <f:attribute name="t" value="SANTANDER"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="BANCOBRASIL"
                                 reRender="formaspagamento">
                    <h:graphicImage url="./imagens/bancos/bancobrasil.png"
                                    style="width: 90px; margin-left: 20px; border-radius: 5px;"/>
                    <f:attribute name="t" value="BANCOBRASIL"/>
                </a4j:commandLink>

                <a4j:commandLink actionListener="#{InclusaoVendaRapidaControle.selecionar}"
                                 id="CAIXA"
                                 reRender="formaspagamento">
                    <h:graphicImage url="./imagens/bancos/caixa.png" style="width: 90px; margin-left: 20px;border-radius: 5px;"/>
                    <f:attribute name="t" value="CAIXA"/>
                </a4j:commandLink>

            </div>
        </h:panelGroup>

        <h:panelGroup id="autorizacao">

            <h:panelGroup styleClass="col-md-12 title-space mtop20" layout="block"
                          rendered="#{InclusaoVendaRapidaControle.convenios ne null}">
                <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Conv�nio:</span>
                <h:panelGroup layout="block" styleClass="cb-container margenVertical">
                    <h:selectOneMenu id="convenio"
                                     value="#{InclusaoVendaRapidaControle.convenioSelecionado}">
                        <f:selectItems value="#{InclusaoVendaRapidaControle.convenios}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup styleClass="col-md-12 title-space mtop20" layout="block"
                          rendered="#{InclusaoVendaRapidaControle.tipoAutorizacao ne null and InclusaoVendaRapidaControle.convenioSelecionado eq null}">

                            <span class="col-md-12 ">
                    <a4j:commandLink
                            style="display: inline-block; vertical-align: top; margin-right: 10px; font-size: 25px;"
                            reRender="formaspagamento" action="#{InclusaoVendaRapidaControle.limparForma}">
                        <i class="fa-icon-chevron-left" style="margin-top: 20px"></i>
                    </a4j:commandLink>
                    <h:graphicImage url="#{InclusaoVendaRapidaControle.imagemForma}"
                                    rendered="#{InclusaoVendaRapidaControle.tipoPagamentoAluno ne null}"
                                    style="width: 90px; border-radius: 5px;"/>
                </span>

                <span class="col-md-12 texto-size-14 mtop20 texto-cor-cinza texto-font texto-bold">N�o foi encontrado nenhum conv�nio cadastrado para esse tipo de pagamento.</span>
            </h:panelGroup>

            <h:panelGroup styleClass="col-md-12 title-space mtop20" layout="block"
                          rendered="#{InclusaoVendaRapidaControle.tipoAutorizacao eq 'CARTAOCREDITO' and InclusaoVendaRapidaControle.convenioSelecionado ne null}">
                <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Dados do cart�o</span>

                <h:panelGroup layout="block" styleClass="col-md-12 mtop20">
                    <a4j:commandLink
                            style="display: inline-block; vertical-align: top; margin-right: 10px; font-size: 25px;"
                            reRender="formaspagamento" action="#{InclusaoVendaRapidaControle.limparForma}">
                        <i class="fa-icon-chevron-left" style="margin-top: 20px"></i>
                    </a4j:commandLink>
                    <h:graphicImage url="#{InclusaoVendaRapidaControle.imagemForma}"
                                    rendered="#{InclusaoVendaRapidaControle.tipoPagamentoAluno ne null}"
                                    style="width: 90px; border-radius: 5px;"/>
                </h:panelGroup>


                <div class="col-md-12 title-space mtop20">
                    <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Titular do cart�o:</span>
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 style="width: 500px"
                                 id="tituloCartao"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.nomeTitularCartao}"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}" />
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 style="width: 500px"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.nomeTitularCartao}"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}" />
                </div>

                <div class="col-md-12 title-space mtop20">
                    <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">N�mero do cart�o:</span>
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 style="width: 500px"
                                 maxlength="19"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.numeroCartao}"
                                 id="numeroCartao"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 onkeypress="mascaraCartaoCreditoVendaRapida()" />
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 style="width: 500px"
                                 id="numeroCartaoCol"
                                 maxlength="19"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.numeroCartao}"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 onkeypress="mascaraCartaoCreditoVendaRapidaCol()" />
                </div>

                <div class="col-md-3 title-space mtop20" style="width: 10%">
                    <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Validade:</span>
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 style="width: 70px"
                                 id="validadeCartao"
                                 onkeypress="return mascara(this.form, this.id , '99/99', event);"
                                 maxlength="5"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.validadeCartao}" />
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 style="width: 70px"
                                 id="validadeCartaoCol"
                                 onkeypress="return mascara(this.form, this.id , '99/99', event);"
                                 maxlength="5"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.validadeCartao}" />
                </div>

                <div class="col-md-3 title-space mtop20">
                    <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">CVV:</span>
                    <h:inputSecret styleClass="inputTextClean margenVertical"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.cvv}"
                                 id="autorizacaoCobrancaCvv"
                                 onkeypress="return mascara(this.form, this.id , '9999', event);"
                                 size="5"
                                 maxlength="4"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}" />
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 id="autorizacaoCobrancaCvvCol"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.cvv}"
                                 onkeypress="return mascara(this.form, this.id , '9999', event);"
                                 size="5"
                                 maxlength="4"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}" />
                </div>

                <div class="col-md-12 title-space mtop20">
                    <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">CPF Titular:</span>
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.cpfTitular}"
                                 id="cpfTitular"
                                 onkeypress="return mascara(this.form, this.id , '999.999.999-99', event);"
                                 size="25"
                                 maxlength="14"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}" />
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 id="cpfTitularCol"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.cpfTitular}"
                                 onkeypress="return mascara(this.form, this.id , '999.999.999-99', event);"
                                 size="25"
                                 maxlength="14"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}" />
                </div>

                <h:panelGroup layout="block"
                              styleClass="col-md-12 title-space mtop20"
                              rendered="#{InclusaoVendaRapidaControle.empresa.agruparParcelasPorCartao}"
                              style="display: grid">
                    <div style="display:flex; align-items: center">
                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold tooltipster"
                              title="Esta op��o deve ser utilizada caso este mesmo cart�o seja utilizado para outro(s) alunos(s).<br/>
                                     Isso influenciar� na gera��o de notas fiscais e na informa��o do pagador principal.<br/>
                                     As notas fiscais e o pagador de todas as parcelas que forem transacionadas neste mesmo cart�o ficar�o no nome do aluno que possuir essa configura��o marcada."
                              style="margin-right: 5px">Cliente � o titular do cart�o:</span>
                        <h:selectBooleanCheckbox id="clienteTitularCartao"
                                                 value="#{InclusaoVendaRapidaControle.autorizacao.clienteTitularCartao}"
                                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"/>
                        <h:selectBooleanCheckbox id="clienteTitularCartaoCol"
                                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.clienteTitularCartao}"
                                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"/>
                    </div>
                </h:panelGroup>

            </h:panelGroup>

            <h:panelGroup styleClass="col-md-12 title-space mtop20" layout="block"
                          rendered="#{InclusaoVendaRapidaControle.tipoAutorizacao eq 'DEBITOCONTA' and InclusaoVendaRapidaControle.convenioSelecionado ne null}">
                <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Dados da conta</span>

                <h:inputHidden id="dcoCodigoBanco" value="#{InclusaoVendaRapidaControle.convenioSelecionadoCodigoBanco}" />
                <h:inputHidden id="dcoContaValida" value="#{InclusaoVendaRapidaControle.dcoContaValida}" />
                <h:inputHidden id="dcoCpfValido" value="#{InclusaoVendaRapidaControle.dcoCpfValido}" />

                <span class="col-md-12 mtop20">
                    <a4j:commandLink
                            style="display: inline-block; vertical-align: top; margin-right: 10px; font-size: 25px;"
                            reRender="formaspagamento" action="#{InclusaoVendaRapidaControle.limparForma}">
                        <i class="fa-icon-chevron-left" style="margin-top: 20px"></i>
                    </a4j:commandLink>
                    <h:graphicImage url="#{InclusaoVendaRapidaControle.imagemForma}"
                                    rendered="#{InclusaoVendaRapidaControle.tipoPagamentoAluno ne null}"
                                    style="width: 90px; border-radius: 5px;"/>
                </span>

                <div style="margin-top: 15px;margin-bottom: 15px; display: inline-table;" id="erroValidacaoDcoConta"></div>
                <div style="margin-top: 15px; margin-bottom: 15px; display: inline-table;">
                    <span id="erroValidacaoCpf" class="col-md-12 texto-size-14 texto-cor-vermelho texto-font"/>
                </div>

                <div class="col-md-12 title-space mtop20">
                    <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Ag�ncia - D�gito:</span>
                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 150px"
                                 id="dcoAgencia"
                                 onchange="validarConta()"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.agencia}"
                                 onkeypress="return mascara(this.form, this.id , '999999999', event);"
                                 maxlength="9"/>
                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 150px"
                                 onchange="validarContaColaborador()"
                                 id="dcoAgenciaCol"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.agencia}"
                                 onkeypress="return mascara(this.form, this.id , '999999999', event);"
                                 maxlength="9"/>

                    <h:outputText value=" - "/>

                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 40px"
                                 id="dcoAgenciaDV"
                                 onchange="validarConta()"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.agenciaDV}"
                                 maxlength="1"/>
                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 40px"
                                 onchange="validarContaColaborador()"
                                 id="dcoAgenciaDVCol"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.agenciaDV}"
                                 maxlength="1"/>
                </div>

                <div class="col-md-12 title-space mtop20">
                    <h:outputText styleClass="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  rendered="#{InclusaoVendaRapidaControle.debitoCaixa}"
                                  value="Opera��o - Conta corrente - D�gito:"/>

                    <h:outputText styleClass="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  rendered="#{!InclusaoVendaRapidaControle.debitoCaixa}"
                                  value="Conta corrente - D�gito:"/>

                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 40px; margin-right: 10px;"
                                 id="dcoOperacao"
                                 onchange="validarConta()"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal && InclusaoVendaRapidaControle.debitoCaixa}"
                                 onkeypress="return mascara(this.form, this.id , '999', event);"
                                 maxlength="3"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.codigoOperacao}" />

                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 40px; margin-right: 10px;"
                                 id="dcoOperacaoCol"
                                 onchange="validarConta()"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal && InclusaoVendaRapidaControle.debitoCaixa}"
                                 onkeypress="return mascara(this.form, this.id , '999', event);"
                                 maxlength="3"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.codigoOperacao}" />

                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 150px"
                                 id="dcoConta"
                                 onchange="validarConta()"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 onkeypress="return mascara(this.form, this.id , '99999999999999', event);"
                                 maxlength="14"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.contaCorrente}" />
                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 150px"
                                 onchange="validarContaColaborador()"
                                 id="dcoContaCol"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 onkeypress="return mascara(this.form, this.id , '99999999999999', event);"
                                 maxlength="14"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.contaCorrente}" />

                    <h:outputText value=" - "/>

                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 40px"
                                 id="dcoContaDV"
                                 onchange="validarConta()"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.contaCorrenteDV}"
                                 maxlength="1"/>
                    <h:inputText styleClass="inputTextClean margenVertical" style="width: 40px"
                                 onchange="validarContaColaborador()"
                                 id="dcoContaDVCol"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.contaCorrenteDV}"
                                 maxlength="1"/>
                </div>
                <div class="col-md-12 title-space mtop20">
                    <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Titular da conta:</span>
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 style="width: 500px"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.nomeTitularCartao}" />
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 style="width: 500px"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.nomeTitularCartao}" />
                </div>
                <div class="col-md-12 title-space mtop20">
                    <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">CPF:</span>
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 id="cpftitularconta"
                                 size="25"
                                 maxlength="14"
                                 rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 onkeypress="return mascara(this.form, this.id , '999.999.999-99', event);"
                                 value="#{InclusaoVendaRapidaControle.autorizacao.cpfTitular}"
                                 onblur="validaCPF(this, 'form:dcoCpfValido','erroValidacaoCpf')" />
                    <h:inputText styleClass="inputTextClean margenVertical"
                                 size="25"
                                 id="cpftitularcontaCol"
                                 maxlength="14"
                                 rendered="#{InclusaoVendaRapidaControle.plano.planoPersonal}"
                                 onkeypress="return mascara(this.form, this.id , '999.999.999-99', event);"
                                 value="#{InclusaoVendaRapidaControle.autorizacaoColaborador.cpfTitular}"
                                 onblur="validaCPF(this, 'form:dcoCpfValido','erroValidacaoCpf')" />
                </div>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
<script>
    function validarConta() {
        var selectorInputCodigoBanco = "form:dcoCodigoBanco";
        var selectorInputAgencia = "form:dcoAgencia";
        var selectorInputAgenciaDV = "form:dcoAgenciaDV";
        var selectorInputConta = "form:dcoConta";
        var selectorInputContaDV = "form:dcoContaDV";
        var selectorInputContaValida = "form:dcoContaValida";
        var selectorOutputMensagemErro = "erroValidacaoDcoConta";
        var selectorInputOperacao = "form:dcoOperacao";

        if (jQuery("[id='form:dcoCodigoBanco']").val() === '104') {
            validarContaBancaria(selectorInputCodigoBanco,
                selectorInputAgencia,
                selectorInputAgenciaDV,
                selectorInputConta,
                selectorInputContaDV,
                selectorInputContaValida,
                selectorOutputMensagemErro,
                selectorInputOperacao
            );
        } else {
            validarContaBancaria(selectorInputCodigoBanco,
                selectorInputAgencia,
                selectorInputAgenciaDV,
                selectorInputConta,
                selectorInputContaDV,
                selectorInputContaValida,
                selectorOutputMensagemErro
            );
        }

    }

    function validarContaColaborador() {
        var selectorInputCodigoBanco = "form:dcoCodigoBanco";
        var selectorInputAgencia = "form:dcoAgenciaCol";
        var selectorInputAgenciaDV = "form:dcoAgenciaDVCol";
        var selectorInputConta = "form:dcoContaCol";
        var selectorInputContaDV = "form:dcoContaDVCol";
        var selectorInputContaValida = "form:dcoContaValida";
        var selectorOutputMensagemErro = "erroValidacaoDcoConta";
        var selectorInputOperacao = "form:dcoOperacao";

        if (jQuery("[id='form:dcoCodigoBanco']").val() === '104') {
            validarContaBancaria(selectorInputCodigoBanco,
                selectorInputAgencia,
                selectorInputAgenciaDV,
                selectorInputConta,
                selectorInputContaDV,
                selectorInputContaValida,
                selectorOutputMensagemErro,
                selectorInputOperacao
            );
        } else {
            validarContaBancaria(selectorInputCodigoBanco,
                selectorInputAgencia,
                selectorInputAgenciaDV,
                selectorInputConta,
                selectorInputContaDV,
                selectorInputContaValida,
                selectorOutputMensagemErro
            );

        }

    }
    function mascaraCartaoCreditoVendaRapida() {
        try {
            var v = document.getElementById('form:numeroCartao').value;
            v = v.replace(/^(\d{4})(\d)/g, "$1 $2");
            v = v.replace(/^(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3");
            v = v.replace(/^(\d{4})\s(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3 $4");
            document.getElementById('form:numeroCartao').value = v;
        } catch (e) {
            console.log("ERRO mascaraCartaoCredito: " + e);
        }
    }

    function mascaraCartaoCreditoVendaRapidaCol() {
        try {
            var v = document.getElementById('form:numeroCartaoCol').value;
            v = v.replace(/^(\d{4})(\d)/g, "$1 $2");
            v = v.replace(/^(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3");
            v = v.replace(/^(\d{4})\s(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3 $4");
            document.getElementById('form:numeroCartaoCol').value = v;
        } catch (e) {
            console.log("ERRO mascaraCartaoCredito: " + e);
        }
    }

    function adicionarPlaceHolderAutorizacaoCobrancaVendaRapida() {
        try {
            if (document.getElementById("form:validadeCartao") != null) {
                document.getElementById("form:validadeCartao").setAttribute("placeholder", "MM/AA");
            }
        } catch (e) {
            console.log("ERRO adicionarPlaceHolderAutorizacaoCobranca: " + e);
        }
    }

    function adicionarPlaceHolderAutorizacaoCobrancaVendaRapidaCol() {
        try {
            if (document.getElementById("form:validadeCartaoCol") != null) {
                document.getElementById("form:validadeCartaoCol").setAttribute("placeholder", "MM/AA");
            }
        } catch (e) {
            console.log("ERRO adicionarPlaceHolderAutorizacaoCobranca: " + e);
        }
    }
</script>

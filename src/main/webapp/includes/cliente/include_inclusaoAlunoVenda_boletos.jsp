<%@include file="../../include_imports.jsp" %>

<h:panelGroup id="panelBoletosVendaRap" styleClass="col-md-12" style="margin-top: 20px">

    <div class="title-space mtop20" style="margin-left: 20px; margin-right: 20px">
        <h:panelGroup styleClass="pure-form">
            <a4j:commandLink id="btnImprimirBoletoVendaRapida"
                             styleClass="botoes nvoBt btSec tooltipster"
                             title="Imprimir todos os boletos"
                             rendered="#{not empty InclusaoVendaRapidaControle.listaBoletosOnlineGerados}"
                             action="#{InclusaoVendaRapidaControle.imprimirBoletos}"
                             oncomplete="#{InclusaoVendaRapidaControle.msgAlert}">
                <i class="fa-icon-print" style="margin-right: 7px;"></i> Imprimir
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup styleClass="pure-form" style="margin-left: 10px">
            <a4j:commandLink id="btnEnviarTodosBoletoEmailVendaRapida"
                             styleClass="botoes nvoBt btSec tooltipster"
                             title="Enviar todos os boletos para o e-mail do cliente"
                             rendered="#{not empty InclusaoVendaRapidaControle.listaBoletosOnlineGerados}"
                             oncomplete="#{InclusaoVendaRapidaControle.mensagemNotificar}"
                             actionListener="#{InclusaoVendaRapidaControle.enviarEmailBoleto}">
                <f:attribute name="tipoEnvioBoleto" value="todos"/>
                <i class="fa-icon-envelope" style="margin-right: 7px;"></i> Enviar todos por e-mail
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup styleClass="pure-form" style="float: right">
            <a4j:commandLink id="btnNovaVendaBoletoVendaRapida"
                             styleClass="botoes nvoBt tooltipster"
                             title="Acessar venda r�pida"
                             value="Realizar nova venda"
                             action="#{InclusaoVendaRapidaControle.irParaTelaVendaRapida}"/>
        </h:panelGroup>

        <h:panelGroup styleClass="pure-form" style="float: right; margin-right: 10px">
            <a4j:commandLink id="btnNovaVendaBoletoVendaRapidaCliente"
                             styleClass="botoes nvoBt btSec tooltipster"
                             title="Acessar a tela do cliente"
                             value="Tela do aluno"
                             action="#{InclusaoVendaRapidaControle.irParaTelaCliente}"/>
        </h:panelGroup>
    </div>

    <div class="col-md-12 title-space mtop20">
        <rich:dataTable id="tblBoletosVendaRapida"
                        rowClasses="linhaPar,linhaImpar" rowKeyVar="status"
                        styleClass="tabelaDados font-size-Em"
                        onRowMouseOver="this.addClassName('background-color-tabelaDados-over')"
                        onRowMouseOut="this.removeClassName('background-color-tabelaDados-over')"
                        value="#{InclusaoVendaRapidaControle.listaBoletosOnlineGerados}"
                        rendered="#{not empty InclusaoVendaRapidaControle.listaBoletosOnlineGerados}"
                        var="boleto">

            <rich:column styleClass="colunaEsquerda" sortBy="#{boleto.linhaDigitavel}">
                <f:facet name="header">
                    <h:outputText value="Linha Digit�vel"/>
                </f:facet>
                <h:outputText value="#{boleto.linhaDigitavel}"
                              styleClass="tooltipster"
                              title="#{boleto.informacoesTitleTelaCliente}"/>
            </rich:column>

            <rich:column styleClass="centro" headerClass="centro">
                <f:facet name="header">
                    <h:outputText value="Parcelas"/>
                </f:facet>

                <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status"
                                styleClass="tabelaDados font-size-Em"
                                style="margin: 0;"
                                value="#{boleto.listaBoletoMovParcela}"
                                var="boletomovparcela">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Parcela"
                                          style="padding-inline-end: 8px;margin-right: 10px; display: block;"/>
                        </f:facet>
                        <h:outputText styleClass="tooltipster"
                                      value="#{boletomovparcela.movParcelaVO.codigo}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Descri��o"
                                          style="padding-inline-end: 8px; margin-right: 10px; display: block;"/>
                        </f:facet>
                        <h:outputText styleClass="tooltipster"
                                      title="#{boletomovparcela.movParcelaVO.pessoa_Apresentar_UpperCase}"
                                      value="#{boletomovparcela.movParcelaVO.descricao}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor" style="padding-inline-end: 50px;"/>
                        </f:facet>
                        <h:outputText
                                value="#{boleto.pago ? boletomovparcela.valorParcelaApresentar : boletomovparcela.valorTotalApresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </rich:column>

            <rich:column sortBy="#{boleto.dataVencimento}">
                <f:facet name="header">
                    <h:outputText value="Vencimento"
                                  styleClass="tooltipster"
                                  title="Data de vencimento do boleto"/>
                </f:facet>
                <h:outputText value="#{boleto.dataVencimentoApresentar}"
                              styleClass="tooltipster"
                              title="#{boleto.informacoesTitleTelaCliente}"/>
            </rich:column>

            <rich:column styleClass="colunaEsquerda" sortBy="#{boleto.valor}">
                <f:facet name="header">
                    <h:outputText value="Valor"/>
                </f:facet>
                <h:outputText value="#{boleto.valorApresentar}"
                              styleClass="tooltipster"
                              title="#{boleto.informacoesTitleTelaCliente}"/>
            </rich:column>

            <rich:column styleClass="colunaEsquerda">
                <f:facet name="header">
                    <h:outputText value=""/>
                </f:facet>
                <h:panelGroup style="display: inline-flex">
                    <h:outputLink value="#{boleto.linkBoleto}"
                                  rendered="#{boleto.apresentarImprimirBoleto}"
                                  target="_blank" id="linkBoletoVendaRapida"
                                  title="Imprimir boleto"
                                  style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                                  styleClass="tooltipster">
                        <i class="fa-icon-print"></i>
                    </h:outputLink>

                    <a4j:commandLink id="enviarEmailBoletoGeralVendaRapida"
                                     rendered="#{boleto.podeEnviarEmail}"
                                     actionListener="#{InclusaoVendaRapidaControle.enviarEmailBoleto}"
                                     oncomplete="#{InclusaoVendaRapidaControle.mensagemNotificar}"
                                     style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                                     title="Enviar boleto por e-mail"
                                     styleClass="tooltipster">
                        <f:attribute name="boletoDetalhe" value="#{boleto}"/>
                        <f:attribute name="tipoEnvioBoleto" value="individual"/>
                        <i class="fa-icon-envelope"></i>
                    </a4j:commandLink>
                </h:panelGroup>
            </rich:column>
        </rich:dataTable>
    </div>
</h:panelGroup>

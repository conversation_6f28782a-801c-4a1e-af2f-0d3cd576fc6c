<%-- 
    Document   : include_modal_capfoto
    Created on : 26/11/2015, 16:06:59
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="/include_imports.jsp"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<head>
    <script type="text/javascript" language="javascript" src="${root}/script/swfobject.js"></script>
</head>

<script>
    var swf = "index20151127";
    var swfVersionStr = "0";
    var xiSwfUrlStr = "";
    var flashvars = {};
    var params = {};
    params.quality = "high";
    params.bgcolor = "#ffffff";
    params.allowscriptaccess = "sameDomain";

    var attr = {};
    attr.id = swf;
    attr.name = "index";
    attr.align = "middle";

    function initFlex(key, obj, context, tipoDoc) {        
        flashvars.key = key;
        flashvars.obj = obj;
        flashvars.contextRoot = context;        
        flashvars.tipoDoc = tipoDoc;
        swfobject.embedSWF(
                "${root}/flex/" + swf + ".swf", "formCapFoto:flashDiv",
                "730", "540",
                swfVersionStr, xiSwfUrlStr,
                flashvars, params, attr);

    }

    function onTerminateCapFoto() {
        try {
            atualizarPagina();
        }catch(err){
        }
        updateFoto();
        Richfaces.hideModalPanel('modalCapFoto');
    }

</script>

<rich:modalPanel id="modalCapFoto" autosized="true" styleClass="novaModal" shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Captura de Foto"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="closeModalCapFoto"/>
            <rich:componentControl for="modalCapFoto" attachTo="closeModalCapFoto" operation="hide"  event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCapFoto">

        <h:panelGroup layout="block" id="flashDiv">

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>



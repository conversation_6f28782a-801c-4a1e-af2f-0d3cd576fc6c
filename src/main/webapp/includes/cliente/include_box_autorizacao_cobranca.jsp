<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@include file="../../include_imports.jsp" %>
<link href="../../css/cobrancaCliente.2.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/script.js"></script>
<script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript"></script>
<script>
    carregarTooltipster();

</script>
<style>
    .cartao {
        border: 1px solid #D3D5D7;
        box-sizing: border-box;
        border-radius: 10px;
        width: 240px;
        margin: 24px 0px 0px 14px;
    }

    .cartao .titleWhite {
        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 18px;
        color: #E7E7E7;
    }

    .cartao .titleGrey {
        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 18px;
        color: #6F747B;
    }

    .white {
        background: #FFFFFF;
        border-radius: 10px;
    }

    .white .rodape {
        background: #FFFFFF; border-radius: 0px 0px 10px 10px; width: 100%; height: 8px; margin-top: 31px;
    }

    .gradient {
        background: radial-gradient(98.33% 251.73% at 96.88% 64%, #4AB5E3 2.89%, #1B9FFD 45.13%, #0078D0 72.63%, #005A93 100%);
    }

    .gradient .rodape {
        background: #013E6F; border-radius: 0px 0px 10px 10px; width: 100%; height: 8px; margin-top: 31px;
    }

    .cartaoRodaPe {
        background: #013E6F !important;
        border-radius: 0px 0px 10px 10px;
    }

    .group {
        display: flex;
    }

    .sub-group {
        float: left;
        margin: 11px;
    }

    .title {
        font-family: Arial;
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
        line-height: 16px;
        color: #6F747B;
    }

    .circleQuestion {
        width: 21px;
        height: 21px;
        border-radius: 100%;
        background: #D3D5D7;
        border: 1px solid #D3D5D7;
        color: #0078D0;
        display: flex;
        margin: 8px;
    }

    .button-produtos {
        position: absolute;
        height: 32px;
        width: 38.5%;
        border: 1px solid #E5E5E5;
        background-color: #FFFFFF;
        box-sizing: border-box;
        border-radius: 3px;
        padding-top: 6px;
        padding-left: 10px;
        color: #29abe2;
        font-size: 14px;
        text-decoration: none;
    }

    .button-produtos:hover {
        color: #29abe2;
        text-decoration: none;
    }

    .button-produtos::after {
        font-family: 'FontAwesome';
        font-size: calc((100vw / 100) * 1.1);
        position: absolute;
        content: "\f0d7";
        right: 0;
        top: 0;
        line-height: 2.0em;
        z-index: 0;
        padding-right: 10px;
        color: #777;
    }

    .table {
        margin-top: 12px;
        background-color: transparent;
        border: none;
    }

    .table thead {
        border-bottom: none;
    }

    .table thead tr {
        background-color: transparent;
    }

    .table thead tr th {
        text-align: left;
        border: none;
        font-style: normal;
        font-weight: bold;
        font-size: 14px;
        line-height: 14px;
        align-items: center;
        color: #383B3E;
    }

    .table tbody tr td {
        border-right: none;
        border-bottom: 1px dashed #DCDDDF;
        height: 54px;
    }

</style>
<a4j:outputPanel>
    <h:panelGroup layout="block" id="panelAutorizacaoCobrancaCliente" style="background: #FAFAFA; padding: 20px 20px 0px 20px">
            <h:outputText value="Dados de Cobrança" style="font-weight: bold; font-size: 18px; line-height: 20px; color: #383B3E;"/>
        <h:panelGrid cellpadding="0" cellspacing="2" columnClasses="colsL,colsR" columns="1" width="100%">
            <rich:dataTable styleClass="table" id="tabAutorizacao" width="100%"
                            value="#{AutorizacaoCobrancaControle.listaAutorizacoes}"
                            rowKeyVar="status"
                            rendered="#{fn:length(AutorizacaoCobrancaControle.listaAutorizacoes) > 0}"
                            var="autorizacao">

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Forma de cobrança"/>
                    </f:facet>

                    <h:panelGroup layout="block" id="formaCobranca">

                        <%-- CARTAO DE CREDITO --%>
                        <h:panelGroup layout="block" id="iconeCartaoCredito"
                                      rendered="#{autorizacao.tipoCartao && !autorizacao.autorizacaoUtilizandoIdVindiPessoa && !autorizacao.autorizacaoUtilizandotokenCielo}"
                                      style="text-align: center; display: inline-flex">
                            <h:panelGroup id="panelAutorizacaoDadosImagemBandeira" style="display: inline-flex">
                                <h:graphicImage title="Bandeira do cartão"
                                                styleClass="tooltipster"
                                                style="vertical-align:middle;margin-right:5px; width: 40px; height: 25px; border-radius: 5px; align-self: center;"
                                                url="imagens/bandeiras/#{autorizacao.operadoraCartao.imagem}.png"/>
                                <h:outputText styleClass="title tooltipster"
                                              style="vertical-align:middle; align-self: center;"
                                              value="Cartão de crédito"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <%-- CARTAO DE CREDITO | ID VINDI --%>
                        <h:panelGroup layout="block" id="iconeCartaoVindi"
                                      rendered="#{autorizacao.tipoCartao && autorizacao.autorizacaoUtilizandoIdVindiPessoa && !autorizacao.autorizacaoUtilizandotokenCielo}"
                                      style="text-align: center; display: inline-flex">
                            <h:panelGroup id="panelAutorizacaoDadosImagemLogoVindi" style="display: inline-flex">
                                <h:graphicImage
                                        style="vertical-align:-2px; margin-right:5px; width: 40px; height: 15px;border:none;align-self: center;"
                                        url="imagens/externo/logo-vindi.png"/>
                                <h:outputText styleClass="title tooltipster"
                                              style="vertical-align:middle; align-self: center;"
                                              value="Cartão de crédito"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <%-- CARTAO DE CREDITO | TOKEN CIELO --%>
                        <h:panelGroup layout="block" id="iconeCartaoCielo"
                                      rendered="#{autorizacao.tipoCartao && !autorizacao.autorizacaoUtilizandoIdVindiPessoa && autorizacao.autorizacaoUtilizandotokenCielo}"
                                      style="text-align: center; display: inline-flex">
                            <h:panelGroup id="panelAutorizacaoDadosImagemLogoCielo" style="display: inline-flex">
                                <h:graphicImage
                                        style="margin-right: 5px; width: 40px; height: 15px; size: 18px; margin-top: -2px;"
                                        url="imagens/externo/cielo-logo-new.png"/>
                                <h:outputText styleClass="title tooltipster"
                                              style="vertical-align:middle; align-self: center;"
                                              value="Cartão de crédito"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <%-- DEBITO EM CONTA CORRENTE --%>
                        <h:panelGroup layout="block" id="iconeDebitoEmConta"
                                      rendered="#{autorizacao.tipoContaCorrente}"
                                      style="text-align: center; display: inline-flex;">
                            <h:panelGroup id="panelAutorizacaoImgBanco" style="display: inline-flex">
                                <h:graphicImage title="Banco"
                                                styleClass="tooltipster"
                                                style="vertical-align:middle; margin-right:5px; width: 40px; height: 25px; border:none; border-radius: 5px; align-self: center;"
                                                url="#{autorizacao.convenio.tipo.logomarca}"/>
                                <h:outputText styleClass="title tooltipster"
                                              style="vertical-align:middle; align-self: center;"
                                              value="Débito em conta"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <%-- BOLETO BANCARIO --%>
                        <h:panelGroup layout="block" id="IconeBoleto"
                                      rendered="#{autorizacao.tipoBoleto}"
                                      style="text-align: center; display: inline-flex;">
                            <h:panelGroup id="panelAutorizacaoImgBoleto" style="display: inline-flex">
                                <h:graphicImage title="Boleto Bancário"
                                                styleClass="tooltipster"
                                                style="vertical-align:middle;margin-right:5px; width: 41px; height: 33px; border:none; align-self: center;"
                                                url="imagens/bandeiras/boleto.png"/>
                                <h:outputText styleClass="title tooltipster"
                                              style="vertical-align:middle; align-self: center;"
                                              value="Boleto bancário"/>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </rich:column>


                <rich:column rendered="#{AutorizacaoCobrancaControle.apresentarDadosCobranca}">
                    <f:facet name="header">
                        <h:outputText value="Dados de cobrança"/>
                    </f:facet>

                    <h:panelGroup layout="block" id="panelDadosCobrancaAutoaa">

                        <%-- DADOS CARTAO DE CREDITO --%>
                        <h:panelGroup layout="block" id="panelAutorizacaoDadosCartaoPanel"
                                      rendered="#{autorizacao.tipoCartao && !autorizacao.autorizacaoUtilizandoIdVindiPessoa && !autorizacao.autorizacaoUtilizandotokenCielo}"
                                      style="text-align: center; display: inline-flex; padding: 10px;">
                            <h:panelGroup layout="block" id="panelAutorizacaoDadosCartao"
                                          style="text-align: left">
                                <h:outputText title="Número do cartão"
                                              styleClass="title tooltipster"
                                              value="#{autorizacao.cartaoMascarado}"/>

                                <h:outputText rendered="#{autorizacao.clienteTitularCartao}"
                                              styleClass="title fa-icon-user tooltipster"
                                              title="Cliente é o titular do cartão"
                                              style="margin-left: 5px">
                                </h:outputText>

                                <br/>
                                <h:outputText title="Validade do cartão"
                                              rendered="#{not empty autorizacao.validadeCartao}"
                                              styleClass="title tooltipster"
                                              value="Venc. (#{autorizacao.validadeCartao})"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <%-- DADOS CARTAO DE CREDITO | ID VINDI --%>
                        <h:panelGroup layout="block" id="panelAutorizacaoDadosCartaoVindi"
                                      rendered="#{autorizacao.tipoCartao && autorizacao.autorizacaoUtilizandoIdVindiPessoa && !autorizacao.autorizacaoUtilizandotokenCielo}"
                                      style="text-align: center; display: inline-flex; padding: 10px;">
                            <h:panelGroup layout="block" id="panelAutorizacaoDadosCartaoVindii"
                                          style="text-align: left">
                                <h:outputText title="A transação será enviada utilizando somente o IdVindi do cliente.<br/>
                                A Vindi irá realizar a tentativa de cobrança utilizando o cartão que o cliente tiver cadastrado na Vindi."
                                              styleClass="title tooltipster"
                                              value="Autorização utilizando IdVindi"/>
                                <br/>
                                <h:outputText title="A transação será enviada utilizando somente o IdVindi do cliente.<br/>
                                A Vindi irá realizar a tentativa de cobrança utilizando o cartão que o cliente tiver cadastrado na Vindi."
                                              styleClass="title tooltipster"
                                              value="IdVindi: #{AutorizacaoCobrancaControle.cliente.pessoa.idVindi}"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <%-- DADOS CARTAO DE CREDITO | TOKEN CIELO --%>
                        <h:panelGroup layout="block" id="panelAutorizacaoDadosCartaoTokenCielo"
                                      rendered="#{autorizacao.tipoCartao && !autorizacao.autorizacaoUtilizandoIdVindiPessoa && autorizacao.autorizacaoUtilizandotokenCielo}"
                                      style="text-align: center; display: inline-flex">
                            <h:panelGroup layout="block" id="panelAutDadosCartaoTokenCielo"
                                          style="text-align: left">
                                <h:outputText title="A transação será enviada utilizando somente o token do cartão do cliente.<br/>
                                A Cielo irá realizar a tentativa de cobrança utilizando o cartão que o cliente tiver cadastrado com esse token lá."
                                              styleClass="title tooltipster"
                                              value="Autorização utilizando Token Cielo"/>
                                <br/>
                                <h:outputText title="A transação será enviada utilizando somente o token do cartão do cliente.<br/>
                                A Cielo irá realizar a tentativa de cobrança utilizando o cartão que o cliente tiver cadastrado com esse token lá."
                                              styleClass="title tooltipster"
                                              style="font-weight: bold;"
                                              value="tokenCielo:"/>
                                <h:outputText title="A transação será enviada utilizando somente o token do cartão do cliente.<br/>
                                A Cielo irá realizar a tentativa de cobrança utilizando o cartão que o cliente tiver cadastrado com esse token lá."
                                              styleClass="title tooltipster"
                                              style="margin-left: 5px;"
                                              value="#{autorizacao.tokenCielo}"/>
                            </h:panelGroup>

                            <%-- se tiver dados do cartão então mostrar--%>
                            <h:panelGroup layout="block"
                                          style="margin-left: 18px;">
                                <h:panelGroup layout="block"
                                              style="text-align: left">
                                    <h:outputText title="Número do cartão que está vinculado ao token Vindi"
                                                  rendered="#{not empty autorizacao.cartaoMascarado}"
                                                  styleClass="title tooltipster"
                                                  value="#{autorizacao.cartaoMascarado}"/>

                                    <h:outputText rendered="#{autorizacao.clienteTitularCartao}"
                                                  styleClass="title fa-icon-user tooltipster"
                                                  title="Cliente é o titular do cartão"
                                                  style="margin-left: 5px">
                                    </h:outputText>

                                    <br/>
                                    <h:outputText title="Validade do cartão que está vinculado ao token Vindi"
                                                  rendered="#{not empty autorizacao.validadeCartao}"
                                                  styleClass="title tooltipster"
                                                  value="Venc. (#{autorizacao.validadeCartao})"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>


                        <%-- DADOS DEBITO EM CONTA CORRENTE --%>
                        <h:panelGroup layout="block" id="panelAutorizacaoDadosDebitoConta"
                                      rendered="#{autorizacao.tipoContaCorrente}"
                                      style="text-align: center; display: inline-flex; padding: 10px;">
                            <h:panelGroup layout="block" id="panelAutorizacaoDebitoContaaa"
                                          style="text-align: left">
                                <h:outputText title="Agência"
                                              styleClass="title tooltipster"
                                              value="Ag: #{autorizacao.agencia}-#{autorizacao.agenciaDV}"/>

                                <h:outputText title="Conta Corrente"
                                              styleClass="title tooltipster"
                                              value=" | C/C: #{autorizacao.contaCorrente}-#{autorizacao.contaCorrenteDV}"/>
                                <br/>
                                <h:outputText title="CPF/CNPJ do titular"
                                              styleClass="title tooltipster"
                                              value="CPF/CNPJ: #{autorizacao.cpfTitular}"/>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </rich:column>


                <rich:column id="comboTipoACobrar"
                             rendered="#{AutorizacaoCobrancaControle.apresentarDadosCobranca}">
                    <f:facet name="header">
                        <h:outputText styleClass="tooltipster"
                                      title="Tipos de Parcelas a cobrar"
                                      value="Parcelas a Cobrar"/>
                    </f:facet>
                    <h:panelGroup layout="block" rendered="#{autorizacao.tipoAutorizacao.id != 3}">
                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="listaSuspensa tooltipster"
                                         style="margin-top: 17px"
                                         title="#{autorizacao.tipoACobrar_Title}"
                                         value="#{autorizacao.tipoACobrar}"
                                         valueChangeListener="#{AutorizacaoCobrancaControle.tipoACobrarListener}">
                            <f:selectItems value="#{AutorizacaoCobrancaControle.tiposACobrar}"/>
                            <a4j:support event="onchange" reRender="comboTipoACobrar"/>
                        </h:selectOneMenu>
                        <h:panelGroup layout="block" style="margin-left: 60px; padding-top: 5px">
                            <a4j:commandLink id="editarTipoACobrarNovo"
                                             value="SALVAR"
                                             styleClass="tooltipster"
                                             title="Salvar o tipo a cobrar caso tenha alterado, sem a necessidade de editar o cartão apenas para fazer esta operação."
                                             reRender="comboTipoACobrar, mdlMensagemGenerica"
                                             action="#{AutorizacaoCobrancaControle.editarTipoACobrar}"
                                             oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};#{AutorizacaoCobrancaControle.onComplete}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block"
                                      rendered="#{autorizacao.tipoACobrar.id == 3}"
                                      style="padding-top: 10px">

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-bold"
                                          style="font-size: 11px !important; display: block"
                                          value="Tipos de produtos:"/>

                            <%-- NÃO PERMITE EDITAR TIPOS DE PRODUTO --%>
                            <h:outputText rendered="#{!AutorizacaoCobrancaControle.permiteEditarProdutos}"
                                          styleClass="texto-font texto-cor-cinza"
                                          style="font-size: 9px !important; display: block"
                                          value="#{autorizacao.listaObjetosACobrar}"/>

                            <%-- PERMITE EDITAR TIPOS DE PRODUTO --%>
                            <a4j:commandLink rendered="#{AutorizacaoCobrancaControle.permiteEditarProdutos}"
                                             value="#{empty autorizacao.listaObjetosACobrar ? 'CLIQUE PARA ADICIONAR' : autorizacao.listaObjetosACobrar}"
                                             action="#{AutorizacaoCobrancaControle.abrirModalTiposProdutosLista}"
                                             oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};#{AutorizacaoCobrancaControle.onComplete}"
                                             reRender="painelConfigTipoProdutoAuto"
                                             title="#{autorizacao.listaObjetosACobrar_Title}"
                                             styleClass="form tooltipster"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Convênio"/>
                    </f:facet>
                    <h:panelGroup layout="block">
                        <h:outputText value="#{autorizacao.convenio.descricao}" styleClass="title"/>
                    </h:panelGroup>
                    </br>
                    <h:panelGroup layout="block">
                        <h:outputText value="Id: #{autorizacao.idPinBank}" rendered="#{autorizacao.convenio.pinBank and AutorizacaoCobrancaControle.usuarioLogado.usuarioPactoSolucoes}" styleClass="title tooltipster" title="IdPinBank"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Ordem"/>
                    </f:facet>
                    <h:panelGroup layout="block">
                        <h:outputText value="#{autorizacao.ordemApresentar}" styleClass="title"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <h:panelGroup layout="block" style="justify-content: end; display: flex">
                        <a4j:commandLink id="upAutorizacao"
                                         style="background-color: transparent; border: none; font-size: 20px; text-decoration: none"
                                         styleClass="tooltipster"
                                         title="Alterar ordem do cartão"
                                         reRender="form:tabAutorizacao"
                                         rendered="#{autorizacao.tipoAutorizacao.id == 1}"
                                         oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar}"
                                         action="#{AutorizacaoCobrancaControle.subirAutorizacao}">
                            <i class="fa-icon-angle-up"></i>
                        </a4j:commandLink>
                        <a4j:commandLink id="downAutorizacao"
                                         style="background-color: transparent; border: none; font-size: 20px; text-decoration: none; padding-left: 10px;"
                                         styleClass="tooltipster"
                                         title="Alterar ordem do cartão"
                                         reRender="form:tabAutorizacao"
                                         rendered="#{autorizacao.tipoAutorizacao.id == 1}"
                                         oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar}"
                                         action="#{AutorizacaoCobrancaControle.descerAutorizacao}">
                            <i class="fa-icon-angle-down"></i>
                        </a4j:commandLink>
                        <a4j:commandLink id="editarAutorizacao"
                                           style="background-color: transparent; border: none; font-size: 18px; padding-left: 10px;"
                                            styleClass="tooltipster"
                                            title="Editar autorização de cobrança"
                                           rendered="#{!autorizacao.autorizacaoUtilizandoIdVindiPessoa}"
                                           reRender="panelAutorizacaoCobrancaCliente, mdlMensagemGenerica"
                                           oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar}"
                                           action="#{AutorizacaoCobrancaControle.selecionaAutorizacao}">
                            <i class="fa-icon-edit"></i>
                        </a4j:commandLink>
                        <a4j:commandLink id="removerAutorizacao"
                                           style="background-color: transparent; border: none; font-size: 18px; padding-left: 10px;"
                                           styleClass="tooltipster"
                                           title="Excluir autorização de cobrança"
                                           reRender="panelAutorizacaoCobrancaCliente, dados, mdlMensagemGenerica"
                                           action="#{AutorizacaoCobrancaControle.confirmarRemoverAutorizacaoCobranca}"
                                           oncomplete="#{AutorizacaoCobrancaControle.msgAlert}">
                            <i class="fa-icon-trash"></i>
                        </a4j:commandLink>
                    </h:panelGroup>
                </rich:column>
            </rich:dataTable>
            <rich:spacer height="10"/>

            <h:outputText value="Adicionar cobrança"
                          rendered="#{AutorizacaoCobrancaControle.autorizacao.novoObj}"
                          style="font-weight: bold;font-size: 14px; margin-top: 10px;"/>
            <h:outputText value="Editar cobrança"
                          rendered="#{!AutorizacaoCobrancaControle.autorizacao.novoObj}"
                          style="font-weight: bold;font-size: 14px; margin-top: 10px;"/>

            <%--BOTOES ADD COBRANÇA--%>
            <h:panelGroup layout="block" styleClass="group">

                <%--TIPO DE COBRANÇA--%>
                <h:panelGroup layout="block" id="panelTipoAutorizacao" styleClass="sub-group">
                    <h:outputText styleClass="title"
                                  value="Tipo de cobrança"/>
                    </br>
                    <h:panelGroup layout="block" styleClass="cb-container" style="margin-top: 5px;">
                        <h:selectOneMenu id="tpAutorizacao"
                                         disabled="#{not AutorizacaoCobrancaControle.permiteAlterarConvenio}"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao}">
                            <f:selectItems value="#{AutorizacaoCobrancaControle.tiposAutorizacao}"/>
                            <a4j:support event="onchange"
                                         action="#{AutorizacaoCobrancaControle.carregarConveniosTela}"
                                         reRender="panelAutorizacaoCobrancaCliente"
                                         oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};adicionarPlaceHolderAutorizacaoCobranca()"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>

                <%--CONVENIO DE COBRANÇA--%>
                <h:panelGroup layout="block" id="panelConvenioCobranca" styleClass="sub-group">
                    <h:outputText styleClass="title"
                                  value="Convênio de cobrança"/>
                    </br>
                    <h:panelGroup layout="block" styleClass="cb-container" style="margin-top: 5px;">
                        <h:selectOneMenu id="convAutorizacao"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.convenio.codigo}">
                            <f:selectItems value="#{AutorizacaoCobrancaControle.convenios}"/>
                            <a4j:support event="onchange" action="#{AutorizacaoCobrancaControle.setarConvenio}"
                                         reRender="panelAutorizacaoCobrancaCliente,dadosContaCorrente,panelScriptVindi,cardCartao"
                                         oncomplete="adicionarPlaceHolderAutorizacaoCobranca()"/>
                            <f:param name="negociacao" value="false"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>

                <%--PARCELAS A COBRAR--%>
                <h:panelGroup layout="block" styleClass="sub-group" rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id != 3}">
                    <h:outputText styleClass="title"
                                  value="Parcelas a cobrar"/>
                    </br>
                    <h:panelGroup layout="block"
                                  id="panelParACobrar"
                                  style="display: flex; align-items: center; display: flex;">
                        <h:panelGroup layout="block" styleClass="cb-container">
                            <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                             id="tpParcelasCobrar"
                                             value="#{AutorizacaoCobrancaControle.autorizacao.tipoACobrar}"
                                             valueChangeListener="#{AutorizacaoCobrancaControle.tipoACobrarListener}">
                                <f:selectItems value="#{AutorizacaoCobrancaControle.tiposACobrar}"/>
                                <a4j:support event="onchange" reRender="panelParACobrar,tipProd"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <h:panelGroup id="botaoHelp"
                                      layout="block"
                                      styleClass="circleQuestion">
                            <h:outputText style="margin: auto;"
                                          styleClass="tooltipParcelas tooltipster"
                                          title="#{AutorizacaoCobrancaControle.autorizacao.tipoACobrar_Title}"
                                          value="?"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

            </h:panelGroup>

            <%--COBRANÇA VINDI--%>
            <h:panelGroup layout="block" id="panelBtnIncluirAutorizacaoIdVindi" styleClass="sub-group"
                          rendered="#{AutorizacaoCobrancaControle.permiteUsarIdVindiCliente}">
                <a4j:commandLink id="incluirAutorizacaoIdVindi" value="Usar IdVindi do Cliente"
                                 title="A transação será enviada utilizando somente o IdVindi do cliente.<br/>
                                A Vindi irá realizar a tentativa de cobrança utilizando o cartão que o cliente tiver cadastrado lá na Vindi."
                                 action="#{AutorizacaoCobrancaControle.incluirAutorizacaoIdVindi}"
                                 styleClass="tooltipster botoes nvoBt btSec"
                                 oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};#{AutorizacaoCobrancaControle.msgAlert}"
                                 reRender="panelAutorizacaoCobrancaCliente, dados"/>
            </h:panelGroup>

            <h:panelGroup layout="block" id="tipProd">
                <c:if test="${AutorizacaoCobrancaControle.autorizacao.tipoACobrar.id == 3}">
                    <h:panelGroup style="margin-bottom: 3%; margin-left: 1%;" layout="block" id="produto">
                        <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                      value="Tipos de produtos"/>
                        <br/>

                        <%-- NÃO PERMITE EDITAR TIPOS DE PRODUTO --%>
                        <h:outputText rendered="#{!AutorizacaoCobrancaControle.permiteEditarProdutos}"
                                      styleClass="texto-font texto-cor-cinza tooltipster"
                                      title="Não é possível editar os tipos de produtos para o tipo de convênio de cobrança selecionado"
                                      style="font-size: 12px !important"
                                      value="#{AutorizacaoCobrancaControle.autorizacao.listaObjetosACobrar}"/>

                        <%-- PERMITE EDITAR TIPOS DE PRODUTO --%>
                        <a4j:commandLink rendered="#{AutorizacaoCobrancaControle.permiteEditarProdutos}"
                                         value="#{empty AutorizacaoCobrancaControle.autorizacao.listaObjetosACobrar ? 'CLIQUE PARA ADICIONAR' : AutorizacaoCobrancaControle.autorizacao.listaObjetosACobrar}"
                                         action="#{AutorizacaoCobrancaControle.abrirModalTiposProdutosNovo}"
                                         oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};#{AutorizacaoCobrancaControle.onComplete}"
                                         reRender="painelConfigTipoProdutoAuto"
                                         style="margin-top: 5px;"
                                         title="#{AutorizacaoCobrancaControle.autorizacao.listaObjetosACobrar_Title}"
                                         styleClass="form tooltipster"/>
                    </h:panelGroup>
                </c:if>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          id="divCartaoCredInclude"
                          rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 1 and AutorizacaoCobrancaControle.autorizacao.convenio.codigo != 0 and not AutorizacaoCobrancaControle.autorizacao.convenio.boleto}">

                <h:panelGroup rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}">

                    <h:panelGroup layout="block"
                                  style="display: flex;">
                        <h:panelGroup layout="block" id="cardCartao"
                                      styleClass="cartao #{AutorizacaoCobrancaControle.styleClassCartao}">
                            <div style="padding: 10px">
                                <h:panelGroup layout="block" id="chipcard" style="margin: 16px;">
                                    <h:graphicImage value="imagens_flat/icon-chip.svg" width="25" height="18"/>
                                </h:panelGroup>

                            <h:panelGroup layout="block" id="nomeTitularCartao" style="text-transform: uppercase">
                                <h:outputText styleClass="#{AutorizacaoCobrancaControle.styleClassCartaoTitles}"
                                              value="#{AutorizacaoCobrancaControle.autorizacao.nomeTitularCartao == '' ? 'Nome Titular do cartão' : AutorizacaoCobrancaControle.autorizacao.nomeTitularCartao}"/>
                            </h:panelGroup>

                                <div style="display: flex; justify-content: space-between; margin: 3% 0 3% 0;">
                                    <h:panelGroup id="numeroCartaoApresentar">
                                        <h:outputText
                                                styleClass="#{AutorizacaoCobrancaControle.styleClassCartaoTitles}"
                                                value="#{AutorizacaoCobrancaControle.autorizacao.numeroCartao == '' || AutorizacaoCobrancaControle.autorizacao.numeroCartao == null ? '**** **** **** ****' : AutorizacaoCobrancaControle.autorizacao.numeroCartao}"/>
                                    </h:panelGroup>
                                    <h:panelGroup id="datesCartao">
                                        <h:outputText
                                                styleClass="#{AutorizacaoCobrancaControle.styleClassCartaoTitles}"
                                                value="#{AutorizacaoCobrancaControle.autorizacao.validadeCartao == '' ? '00/00' : AutorizacaoCobrancaControle.autorizacao.validadeCartao}"/>
                                    </h:panelGroup>
                                </div>
                            </div>
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" id="imagemCartao"
                                              style="float: right; margin-right: 10px;">
                                    <h:graphicImage
                                            rendered="#{AutorizacaoCobrancaControle.imagemCartao != 'semCartao'}"
                                            url="/imagens_flat/#{AutorizacaoCobrancaControle.imagemCartao}-icon.svg"
                                            width="30" height="30"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="margin-left: 2%; margin-block-start: auto;">
                            <a4j:commandLink action="#{AutorizacaoCobrancaControle.limparDadosDoCartao}"
                                             reRender="divCartaoCredInclude">
                                <i class="fa-icon-eraser texto-size-18"></i>
                                <h:outputText styleClass="title" value="Limpar dados"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="group" style="padding-top: 10px;" rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}">

                    <%--NOME TITULAR--%>
                    <h:panelGroup layout="block" styleClass="sub-group">
                        <h:outputText styleClass="title tooltipster"
                                      value="Nome titular do cartão"
                                      title="Informe o Nome do Titular exatamente como está impresso no cartão"/>
                        </br>
                        <h:inputText id="titularCartao" rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}"
                                     value="#{AutorizacaoCobrancaControle.autorizacao.nomeTitularCartao}"
                                     tabindex="1"
                                     size="30" style="text-transform: uppercase; margin-top: 5px; width: 180px; height: 32px !important;"
                                     styleClass="inputCobranca"
                                     onfocus="focusinput(this);"
                                     onkeypress="return permiteSomenteLetra(this.form, this.id, event);"
                                     onblur="blurinput(this);" maxlength="80">
                            <a4j:support event="onchange" reRender="nomeTitularCartao, cardCartao"
                                         action="#{AutorizacaoCobrancaControle.inicializarCard}"
                                         oncomplete="document.getElementById('form:cpfTitularAutorizacaoCobranca').focus()"/>
                        </h:inputText>
                    </h:panelGroup>

                        <%--CPF DO TITULAR--%>
                        <h:panelGroup layout="block"
                                      styleClass="sub-group">
                            <h:outputText styleClass="title"
                                          value="CPF do titular"
                                          rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}"/>
                            </br>
                            <h:inputText id="cpfTitularAutorizacaoCobranca"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.cpfTitular}"
                                         style="margin-top: 5px; width: 110px; height: 32px !important;"
                                         tabindex="2" rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}"
                                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                         size="15" styleClass="inputCobranca"
                                         onfocus="focusinput(this);"
                                         onblur="blurinput(this);"
                                         maxlength="14"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="group" rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}">

                    <%--NUMERO CARTAO--%>
                    <h:panelGroup layout="block" styleClass="sub-group">
                        <h:outputText styleClass="title"
                                      value="Número do cartão"/>
                            </br>
                            <h:inputText id="nrCartao" value="#{AutorizacaoCobrancaControle.autorizacao.numeroCartao}"
                                         style="margin-top: 5px; width: 180px; height: 32px !important;"
                                         tabindex="3"
                                         onkeypress="mascaraCartaoCreditoAutorizacaoCobranca()"
                                         size="30" rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}"
                                         styleClass="inputCobranca" onfocus="focusinput(this);"
                                         onblur="blurinput(this);" maxlength="19">
                                <a4j:support event="onchange"
                                             action="#{AutorizacaoCobrancaControle.buscaBandeiraCartaoOperadora}"
                                             reRender="cardCartao, panelScriptVindi, selectBandeira"
                                             oncomplete="document.getElementById('form:validade').focus();mascaraCartaoCreditoAutorizacaoCobranca()"/>
                            </h:inputText>
                        </h:panelGroup>

                        <%--BANDEIRA--%>
                        <h:panelGroup styleClass="sub-group">
                            <h:outputText styleClass="title" value="Bandeira"/>
                            </br>
                            <h:panelGroup layout="block" styleClass="block cb-container" style="margin-top: 5px;width: 116%;" id="selectBandeira">
                                <h:selectOneMenu
                                        value="#{AutorizacaoCobrancaControle.bandeiraCartao}"
                                        onblur="blurinput(this);" onfocus="focusinput(this);"
                                        style="height: 32px !important;"
                                        styleClass="form"
                                        tabindex="4">
                                    <f:selectItems value="#{AutorizacaoCobrancaControle.bandeirasAprovaFacil}"/>
                                    <a4j:support event="onchange" reRender="cardCartao"
                                                 action="#{AutorizacaoCobrancaControle.setBandeiraCartaoOperadora}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="group" rendered="#{!empty AutorizacaoCobrancaControle.bandeirasCartao}">

                    <%--VENCIMENTO--%>
                    <h:panelGroup layout="block" styleClass="sub-group">
                        <h:outputText styleClass="title tooltipster"
                                      value="Data de vencimento"
                                      title="Informe a Validade do cartão de crédito"/>
                        </br>
                        <div style="margin-top: 5px; display: flex;">
                            <h:inputText id="validade" value="#{AutorizacaoCobrancaControle.autorizacao.validadeCartao}"
                                         tabindex="5"
                                         style="width: 80px; height: 32px !important;"
                                         onkeypress="return mascara(this.form, this.id, '99/99', event);"
                                         styleClass="inputCobranca" onfocus="focusinput(this);"
                                         onblur="blurinput(this);">
                                <a4j:support event="onchange" reRender="cardCartao"
                                             oncomplete="document.getElementById('form:cvv').focus()"/>
                            </h:inputText>
                        </div>
                    </h:panelGroup>

                    <%--CODIGO SEGURANÇA CVV--%>
                    <h:panelGroup layout="block" styleClass="sub-group" style="  margin-left: 68px;">
                            <h:outputText styleClass="title"
                                          value="CVV"/>
                            </br>
                            <h:panelGroup layout="block" style="display: flex; align-items: center; display: flex;">
                                <h:inputSecret id="cvv" value="#{AutorizacaoCobrancaControle.autorizacao.cvv}"
                                               tabindex="6"
                                               style="width: 68px; height: 32px !important;"
                                               onblur="blurinput(this);" onfocus="focusinput(this);"
                                               onkeypress="return mascara(this.form, this.id, '9999', event);"
                                               onkeyup="somenteNumeros(this);"
                                               size="5" styleClass="inputCobranca"
                                               maxlength="4">
                                </h:inputSecret>
                                <h:panelGroup id="botaoCvv"
                                              layout="block"
                                              styleClass="circleQuestion">
                                    <h:outputText id="hintCVV" style="margin: auto;"
                                                  styleClass="tooltipParcelas tooltipster"
                                                  title="CVV é um código de segurança de 3 dígitos impresso no verso de cartões de crédito"
                                                  value="?"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                <h:panelGroup layout="block"
                              style="margin-top: 10px; display: flex; margin-left: 12px;"
                              rendered="#{AutorizacaoCobrancaControle.apresentarClienteTitularCartao && !empty AutorizacaoCobrancaControle.bandeirasCartao}">
                    <h:outputText styleClass="title tooltipster"
                                  title="Esta opção deve ser utilizada caso este mesmo cartão seja utilizado para outro(s) alunos(s).<br/>
                                 Isso ira influenciar na geração de notas fiscais e na informação do pagador principal.<br/>
                                 As notas fiscais e o pagador de todas as parcelas que forem transacionadas neste mesmo cartão ficarão no nome do aluno que possuir essa configuração marcada."
                                  value="Cliente é o titular do cartão: "
                                  style="margin-right: 5px"/>
                    <h:selectBooleanCheckbox id="clienteTitularCartao"
                                             styleClass="checkAutorizarCobranca"
                                             style="align-self: center"
                                             value="#{AutorizacaoCobrancaControle.autorizacao.clienteTitularCartao}" tabindex="7"/>
                </h:panelGroup>

                </h:panelGroup>

                <%--Se não tiver operadoras cadastradas não irá exibir os campos para preenchimento do cartão - exibir mensagem informando...--%>
                <h:panelGroup layout="block"
                              id="msgSemOperadora"
                              rendered="#{empty AutorizacaoCobrancaControle.bandeirasCartao}">
                    <h:outputText styleClass="title tooltipster"
                                  title="Não encontramos nenhuma operadora configurada para este convênio de cobrança. Vá nas configurações do convênio de cobrança selecioado e configure as operadoras."
                                  value="Não foi encontrado nenhuma operadora configurada para este convênio de cobrança."
                                  style="margin-right: 5px"/>
                </h:panelGroup>

            </h:panelGroup>

            <h:panelGroup layout="block" id="dadosContaCorrente"
                          rendered="#{AutorizacaoCobrancaControle.autorizacao.tipoAutorizacao.id == 2 and not AutorizacaoCobrancaControle.autorizacao.convenio.boleto and AutorizacaoCobrancaControle.autorizacao.convenio.codigo != 0}">

                <h:panelGroup layout="block" styleClass="group">

                    <h:panelGroup id="panelIdentificadorDoc" styleClass="sub-group">
                        <h:outputText styleClass="title tooltipster" value="Nome do titular"
                                      title="Informe o nome do titular da conta corrente"/>
                        <h:inputText value="#{AutorizacaoCobrancaControle.autorizacao.nomeTitularCartao}"
                                     id="nomeCobran"
                                     styleClass="inputCobranca"
                                     style="margin-top: 5px; width: 180px; height: 32px !important;"
                                     onkeypress="return permiteSomenteLetra(this.form, this.id, event);"
                                     tabindex="1"/>
                        <a4j:support event="onchange" reRender="nomeTitularCartao, cardCartao, panelConteudo"
                                     action="#{PagamentoCartaoCreditoControle.inicializarCard}"
                                     oncomplete="document.getElementById('form:nrCartao').focus()"/>
                    </h:panelGroup>

                    <h:panelGroup styleClass="sub-group">
                        <h:outputText styleClass="title tooltipster"
                                      value="CPF ou CNPJ"
                                      title="#{msg.title_cpf_titular}"/>
                        </br>
                        <h:inputText id="cpfTitular"
                                     tabindex="2"
                                     styleClass="inputCobranca"
                                     style="margin-top: 5px; width: 140px; height: 32px !important;"
                                     value="#{AutorizacaoCobrancaControle.autorizacao.cpfTitular}"
                                     maxlength="14"/>
                    </h:panelGroup>

                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="group">
                    <h:panelGroup styleClass="sub-group">
                        <h:outputText styleClass="title" value=""/>
                        <%--<h:inputHidden id="bancoCodigo" value="#{AutorizacaoCobrancaControle.autorizacao.banco.codigoBanco}"/>
                        <h:inputHidden id="contaValida2" value="#{AutorizacaoCobrancaControle.contaValida}"/>--%>
                        <h:outputText value="#{AutorizacaoCobrancaControle.autorizacao.banco.codigoBanco}"
                                      styleClass="title"
                                      rendered="#{AutorizacaoCobrancaControle.autorizacao.banco.codigoBanco != 0}">
                            <f:convertNumber pattern="000"/>
                        </h:outputText>
                        <h:outputText value=" - #{AutorizacaoCobrancaControle.autorizacao.banco.nome}"
                                      styleClass="title"
                                      rendered="#{AutorizacaoCobrancaControle.autorizacao.banco.codigoBanco != 0}"/>
                        <div id="erroValidacaoDcoConta" style="color: orangered"></div>
                    </h:panelGroup>
                </h:panelGroup>

            <h:panelGroup layout="block" styleClass="group" rendered="#{AutorizacaoCobrancaControle.autorizacao.apresentarCodigoOperacao}">
                <h:panelGroup styleClass="sub-group">
                    <h:outputText styleClass="title" value="Operação"/>
                    <h:inputText id="codOperacao" value="#{AutorizacaoCobrancaControle.autorizacao.codigoOperacao}"
                                 tabindex="3"
                                 style="margin-top: 5px; width: 180px; height: 32px !important;"
                                 size="3"
                                 title="Código Operação"
                                 styleClass="inputCobranca" onfocus="focusinput(this);"
                                 onblur="blurinput(this);" maxlength="3"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="group">

                <h:panelGroup styleClass="sub-group">
                    <h:outputText styleClass="title tooltipster" value="Agência"/>
                    <%--<h:inputHidden id="contaValida" value="#{AutorizacaoCobrancaControle.contaValida}"/>--%>

                        <h:panelGroup style="display: block">
                            <h:inputText id="agenciaCobranca" value="#{AutorizacaoCobrancaControle.autorizacao.agencia}"
                                         tabindex="4"
                                         style="margin-top: 5px; width: 140px; height: 32px !important;"
                                         styleClass="inputCobranca inputCobrancaR tooltipster"
                                         maxlength="12"/>

                        <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza texto-bold" value=" - "/>
                        <h:inputText id="agenciaCobrancaDV" value="#{AutorizacaoCobrancaControle.autorizacao.agenciaDV}"
                                     onchange="validarConta()"
                                     tabindex="5"
                                     style="margin-top: 5px; width: 30px; height: 32px !important;"
                                     styleClass="inputCobranca  inputCobrancaR tooltipster"
                                     title="Informe o dígito da agência"
                                     onfocus="focusinput(this);"
                                     onblur="blurinput(this);" maxlength="2"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup styleClass="sub-group">
                    <h:outputText styleClass="title tooltipster"
                                  value="Nº conta corrente"/>

                        <h:panelGroup style="display: block">
                            <h:inputText id="contaCorrenteCobranca"
                                         value="#{AutorizacaoCobrancaControle.autorizacao.contaCorrente}"
                                         style="margin-top: 5px; width: 100px; height: 32px !important;"
                                         tabindex="6"
                                         styleClass="inputCobranca inputCobrancaR"
                                         maxlength="12"/>

                        <h:outputText styleClass="texto-font texto-size-12-real texto-cor-cinza texto-bold" value=" - "/>
                        <h:inputText id="contaCorrenteCobrancaDV"
                                     value="#{AutorizacaoCobrancaControle.autorizacao.contaCorrenteDV}"
                                     onchange="validarConta()"
                                     tabindex="7"
                                     style="margin-top: 5px; width: 30px; height: 32px !important;"
                                     title="Informe o dígito da conta corrente"
                                     styleClass="inputCobranca inputCobrancaR tooltipster"
                                     onfocus="focusinput(this);"
                                     onblur="blurinput(this);" maxlength="2"/>
                    </h:panelGroup>
                </h:panelGroup>

                </h:panelGroup>

            <h:panelGroup styleClass="group">
                <h:panelGroup layout="block" style="margin-top: 10px; display: flex;">
                    <h:outputText styleClass="title"
                                  value="Autorizar débito cliente no banco:" style="margin-right: 5px"/>
                    <h:selectBooleanCheckbox id="autorizarDebitoCliente"
                                             styleClass="checkAutorizarCobranca"
                                             value="#{AutorizacaoCobrancaControle.autorizacao.autorizarClienteDebito}" tabindex="7"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        </h:panelGrid>

        <h:panelGrid id="botoesAcao" columnClasses="colunaCentralizada" width="100%" cellpadding="0" cellspacing="0" columns="1">
            <h:panelGroup  layout="block" style="float: left; line-height: 50px;height: 50px;">

                <a4j:commandLink id="confirmarAutorizacaoVindi" value="Salvar"
                                 status="false"
                                 onclick="Richfaces.showModalPanel('panelStatus1');"
                                 rendered="#{AutorizacaoCobrancaControle.cadastrarUsandoVindiPublica and AutorizacaoCobrancaControle.autorizacao.convenio.codigo != 0}"
                                 action="#{AutorizacaoCobrancaControle.verificarCadastrarVindiPublica}"
                                 tabindex="9" styleClass="botoes nvoBt"
                                 style="color: #FFFFFF; background: #28AB45; height: 36px"
                                 oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};#{AutorizacaoCobrancaControle.onComplete}"
                                 reRender="panelAutorizacaoCobrancaCliente, dados, modalSalvarCartaoMesmoAssim,panelStatus1"/>

                <a4j:commandLink id="confirmarAutorizacao" value="Salvar"
                                 rendered="#{!AutorizacaoCobrancaControle.cadastrarUsandoVindiPublica and AutorizacaoCobrancaControle.autorizacao.convenio.codigo != 0}"
                                 action="#{AutorizacaoCobrancaControle.confirmarOrigemAluno}"
                                 tabindex="9" styleClass="botoes nvoBt"
                                 style="color: #FFFFFF; background: #28AB45; height: 36px"
                                 oncomplete="#{AutorizacaoCobrancaControle.msgAlert}"
                                 reRender="#{AutorizacaoCobrancaControle.reRender}"/>

                <a4j:commandLink rendered="#{AutorizacaoCobrancaControle.autorizacao.convenio.codigo != 0}"
                                 id="novaAutorizacao" value="Cancelar" action="#{AutorizacaoCobrancaControle.novo}"
                                 tabindex="10" styleClass="botoes nvoBt"
                                 style="color: #6F747B; background: #E5E5E5; height: 36px"
                                 reRender="panelAutorizacaoCobrancaCliente, dados, mdlMensagemGenerica"/>

                <a4j:commandLink action="#{AutorizacaoCobrancaControle.realizarConsultaLogObjetoSelecionado}"
                                 style="color: #6F747B; background: #E5E5E5; height: 36px"
                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                 title="Visualizar Log da autorização de cobrança" styleClass="botoes nvoBt btSec tooltipster">
                    <i class="fa-icon-list"></i>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGrid columns="1" width="100%" style="margin-top: 10px">
            <h:outputText id="outputTextMensagem" styleClass="mensagem" value="#{AutorizacaoCobrancaControle.mensagem}"/>
            <h:outputText id="outputTextMensagemDetalhada" styleClass="mensagemDetalhada" value="#{AutorizacaoCobrancaControle.mensagemDetalhada}"/>
        </h:panelGrid>
    </h:panelGroup>

    <h:inputHidden id="gatewayTokenVindi"
                   value="#{AutorizacaoCobrancaControle.gatewayTokenVindi}"/>

    <a4j:jsFunction name="confirmarCadastrarVindiPublica"
                    action="#{AutorizacaoCobrancaControle.confirmarCadastrarVindiPublica}"
                    oncomplete="#{AutorizacaoCobrancaControle.mensagemNotificar};Richfaces.hideModalPanel('panelStatus1');"
                    reRender="panelAutorizacaoCobrancaCliente, dados"/>

    <h:panelGroup layout="block" id="panelScriptVindi">
        <script>
            function cadastrarCardVindiPublicaAuto() {
                try {

                    console.log('Entrei cadastrarCardVindiPublicaAuto');

                    jQuery("[id='form:gatewayTokenVindi']").val('');

                    var nrCartao = jQuery("[id='form:nrCartao']").val();

                    if (nrCartao.includes("***")) {
                        console.log('Cartão ignorar...');
                        confirmarCadastrarVindiPublica();
                        return;
                    }

                    var nomeTitular = jQuery("[id='form:titularCartao']").val();
                    var validade = jQuery("[id='form:validade']").val();
                    var codSeguranca = jQuery("[id='form:cvv']").val();
                    var band = '${AutorizacaoCobrancaControle.autorizacao.operadoraCartao.descricaoParaVindi}';


                    var profile = {
                        "holder_name": nomeTitular,
                        "card_expiration": validade,
                        "card_number": nrCartao,
                        "card_cvv": codSeguranca,
                        "payment_method_code": "credit_card",
                        "payment_company_code": band
                    };

                    var authorization = ("Basic " + '${AutorizacaoCobrancaControle.chavePublicaGatewayTokenVindi}');

                    jQuery.ajax({
                        type: "POST",
                        url: '${AutorizacaoCobrancaControle.urlGatewayTokenVindi}',
                        dataType: "json",
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', authorization);
                        },
                        data: profile,
                        success: function (data) {
                            console.log(data);
                            jQuery("[id='form:gatewayTokenVindi']").val(data.payment_profile.gateway_token);
                            confirmarCadastrarVindiPublica();
                            console.log('fim sucesso');
                        },
                        error: function (request, status, error) {
                            console.log(error);
                            console.log(request.responseText);
                            var mensagem = request.responseText;
                            try {
                                var obj = JSON.parse(request.responseText);
                                mensagem = "Campo: " +obj.errors[0].parameter + " - " + obj.errors[0].message;
                            } catch (ex) {
                                console.log('Erro VINDI [converterJSON] ' + ex);
                            }
                            Notifier.error(mensagem, "Erro cadastrar cartão Vindi");
                            Richfaces.hideModalPanel('panelStatus1');
                        },
                        async: false
                    });
                } catch (ex) {
                    console.log(ex);
                }
            }
        </script>
    </h:panelGroup>
</a4j:outputPanel>

<jsp:include page="/include_modal/modalVerificacaoCartao.jsp" flush="true" />

<script>
    function validarConta() {
        var selectorInputCodigoBanco = "form:bancoCodigo";
        var selectorInputAgencia = "form:agenciaCobranca";
        var selectorInputAgenciaDV = "form:agenciaCobrancaDV";
        var selectorInputConta = "form:contaCorrenteCobranca";
        var selectorInputContaDV = "form:contaCorrenteCobrancaDV";
        var selectorInputContaValida = "form:contaValida";
        var selectorOutputMensagemErro = "erroValidacaoDcoConta";
        var selectorInputCodOperacao = "form:codOperacao";

        validarContaBancaria(selectorInputCodigoBanco,
            selectorInputAgencia,
            selectorInputAgenciaDV,
            selectorInputConta,
            selectorInputContaDV,
            selectorInputContaValida,
            selectorOutputMensagemErro,
            selectorInputCodOperacao
        );
    }

    function adicionarPlaceHolderAutorizacaoCobranca() {
        try {
            if (document.getElementById("form:validade") != null) {
                document.getElementById("form:validade").setAttribute("placeholder", "MM/AA");
            }
        } catch (e) {
            console.log("ERRO adicionarPlaceHolderAutorizacaoCobranca: " + e);
        }
    }

    function mascaraCartaoCreditoAutorizacaoCobranca() {
        try {
            var v = document.getElementById('form:nrCartao').value;
            v = v.replace(/^(\d{4})(\d)/g, "$1 $2");
            v = v.replace(/^(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3");
            v = v.replace(/^(\d{4})\s(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3 $4");
            document.getElementById('form:nrCartao').value = v;
        } catch (e) {
            console.log("ERRO mascaraCartaoCredito: " + e);
        }
    }
</script>

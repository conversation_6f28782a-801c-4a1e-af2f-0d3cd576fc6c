<%@page pageEncoding="iso-8859-1" %>
<%@include file="../imports.jsp" %>

<h:panelGroup rendered="#{TelaClienteControle.contratoSelecionado != null}" id="panelOpracoesContrato">
    <c:if test="${not param.readOnly}">
        <div style="width: 100%; text-align: right;">

            <a4j:commandLink id="linkTransferirDireitoUso"
                             rendered="#{TelaClienteControle.mapaMostrar['TRANSFERENCIA_DIREITO_USO']}"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             oncomplete="#{TransferirDireitoUsoContratoControle.msgAlert}#{rich:component('panelStatus1')}.hide();#{TransferenciaContratoEVOControle.mensagemNotificar}"
                             action="#{TransferirDireitoUsoContratoControle.novoTransferirDireitoUso}"
                             reRender="pnlTransferirDireitoUsoContrato">
                <i id="iconTransfer" class="fa-icon-signout" style="color: #FF5555;"></i>
                <h:outputText value="Transferir este Contrato" style="color: #FF5555;"/>
            </a4j:commandLink>

            <a4j:commandLink id="linkRecuperarDireitoUso"
                             rendered="#{TelaClienteControle.mapaMostrar['RECUPERAR_DIREITO_USO']}"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             oncomplete="#{TransferirDireitoUsoContratoControle.msgAlert}#{rich:component('panelStatus1')}.hide();#{TransferenciaContratoEVOControle.mensagemNotificar}"
                             action="#{TransferirDireitoUsoContratoControle.novoRecuperarDireitoUso}"
                             reRender="pnlRecuperarDireitoUsoContrato">
                <i id="iconTransfer" class="fa-icon-signin"></i>
                <h:outputText value="Recuperar este Contrato"/>
            </a4j:commandLink>

            <a4j:commandLink id="linkEstornar"
                             rendered="#{TelaClienteControle.exibirEstornoContrato}"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             oncomplete="#{EstornoContratoControle.mensagemNotificar}#{rich:component('panelStatus1')}.hide();abrirPopup('estornoContratoForm.jsp', 'EstornoContratoControle', 900, 560);"
                             action="#{EstornoContratoControle.novo}"
                             reRender="panelParcelasRemessas">
                <i id="linkEstornarIcon" class="fa-icon-minus-sign" style="color: #FF5555;"></i>
                <h:outputText value="Estornar" style="color: #FF5555;"/>
                <f:setPropertyActionListener value="O Estorno só poderá ser feita após o retorno da remessa da(s) parcela(s) acima!" target="#{ClienteControle.tituloModalParcelaRemessa}"/>
            </a4j:commandLink>

            <a4j:commandLink styleClass="linkAzul texto-size-14"
                             id="linkRetornoAtesMed"
                             style="margin-right: 20px;"
                             rendered="#{TelaClienteControle.mapaMostrar['RETORNO_ATESTADO']}"
                             oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('retornoAtestadoContrato.jsp', 'RetornoAtestadoContratoControle', 750, 550);"
                             action="#{RetornoAtestadoContratoControle.novo}">
                <i id="linkRetornoAtesMedIcon" class="fa-icon-ambulance"></i>
                <h:outputText value="Retorno Atestado"/>
            </a4j:commandLink>

            <a4j:commandLink id="linkRetornoTrancamento"
                             oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('retornoTrancamentoContratoForm.jsp', 'RetornoTrancamentoContratoControle', 900, 690);"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             actionListener="#{RetornoTrancamentoContratoControle.novoRetornoEvent}"
                             rendered="#{TelaClienteControle.mapaMostrar['RETORNO_TRANCAMENTO']}">
                <i id="linkRetornoTrancamentoIcon" class="fa-icon-unlock"></i>
                <h:outputText value="Retorno Trancamento"/>
            </a4j:commandLink>

            <a4j:commandLink id="linkRetornoCarencia"
                             style="margin-right: 20px;"
                             styleClass="linkAzul texto-size-14"
                             rendered="#{TelaClienteControle.mapaMostrar['RETORNO_CARENCIA']}"
                             oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('retornoCarenciaContrato.jsp', 'RetornoCarenciaContratoControle', 830, 690);"
                             action="#{RetornoCarenciaContratoControle.novo}">
                <i id="linkRetornoCarenciaIcon" class="fa-icon-ship"></i>
                <h:outputText value="Retorno Férias"/>
            </a4j:commandLink>


            <a4j:commandLink styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             rendered="#{TelaClienteControle.mapaMostrar['ALTERACAO_CONTRATO']}"
                             action="#{TelaClienteControle.validarUsuarioELiberarVaga}"
                             reRender="panelAutorizacaoFuncionalidade">
                <i class="fa-icon-signout"></i>
                <h:outputText value="Liberar Vaga na Turma"/>
            </a4j:commandLink>

            <a4j:commandLink id="linkAfastamento"
                             rendered="#{TelaClienteControle.mapaMostrar['CARENCIA'] || TelaClienteControle.mapaMostrar['ATESTADO'] ||
                                     TelaClienteControle.mapaMostrar['TRANCAMENTO'] || TelaClienteControle.mapaMostrar['CANCELAMENTO']}"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             oncomplete="abrirPopupTopoPagina('afastamentoContratoForm.jsp', 'AfastamentoContratoControle', 830, 650);"
                             action="#{AfastamentoContratoControle.novo}">
                <i id="linkAfastamentoIcon" class="fa-icon-hand-stop-o"></i>
                <h:outputText value="Afastamento" />
            </a4j:commandLink>

            <a4j:commandLink id="linkBonus"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             rendered="#{TelaClienteControle.mapaMostrar['BONUS']}"
                             oncomplete="abrirPopup('bonusForm.jsp', 'BonusContratoControle', 830, 690);"
                             action="#{BonusContratoControle.novo}">
                <i id="linkBonusIcon" class="fa-icon-certificate"></i>
                <h:outputText value="Bônus" />
            </a4j:commandLink>

            <a4j:commandLink id="linkAlterarHorario"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             rendered="#{TelaClienteControle.mapaMostrar['ALTERAR_HORARIO']}"
                             oncomplete="#{AlterarHorarioContratoControle.msgAlert}"
                             action="#{AlterarHorarioContratoControle.novo}"
                             reRender="panelParcelasRemessas, mdlMensagemGenerica">
                <i id="linkAlterarHorarioIcon" class="fa-icon-clock-o"></i>
                <h:outputText value="Alterar horário" />
                <f:setPropertyActionListener value="Manutenção só poderá ser feita após o retorno da remessa da(s) parcela(s) acima!" target="#{ClienteControle.tituloModalParcelaRemessa}"/>
            </a4j:commandLink>
        </div>
    </c:if>

    <h:panelGrid id="dadosContrato"
                 columns="3" style="width: calc(100% - 40px); margin: 0 20px 20px 20px;"
                 columnClasses="cinza texto-size-14 w48,w4 semBorda, cinza texto-size-14 w48"
                 styleClass="dadosContrato">

        <%-- Valor Contrato e Valor Base do Contrato --%>
        <h:panelGroup>
            <h:outputText styleClass="upper negrito" value="Valor Contrato: "/>
            <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} "/>
            <h:outputText id="valorFinal" value="#{TelaClienteControle.contratoSelecionado.valorFinal}">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
        <h:panelGroup>
            <h:outputText  styleClass="upper negrito" value="Valor Base do Contrato: "/>
            <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} "/>
            <h:outputText id="valorBaseCalculo" value="#{TelaClienteControle.contratoSelecionado.valorBaseCalculo}">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
        </h:panelGroup>

        <%-- Valor Pago e Valor Base do Contrato --%>
        <h:panelGroup>
            <h:outputText styleClass="upper negrito" value="Valor Pago: "/>
            <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda}"/>
            <h:outputText id="valorPago" value="#{TelaClienteControle.valorPago}">
                <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
        <h:panelGroup>
            <h:outputText styleClass="upper negrito" value="Condição de Pagamento: "/>
            <h:outputText value="#{TelaClienteControle.contratoSelecionado.contratoCondicaoPagamento.condicaoPagamento.descricao}"/>
            <%--            <a4j:commandLink rendered="#{TelaClienteControle.contratoSelecionado.regimeRecorrencia}"--%>
            <%--                             style="margin-left: 10px;"--%>
            <%--                             styleClass="texto-cor-azul texto-size-14 linkPadrao"--%>
            <%--                             reRender="abaContrato,modalTrocarCartaoContratoCliente,panelConteudo2"--%>
            <%--                             id="btnTrocarCartaoRecorrencia"--%>
            <%--                             title="Trocar o cartão de crédito associado a este Contrato de Recorrência"--%>
            <%--                             oncomplete="#{rich:component('modalTrocarCartaoContratoCliente')}.show();"--%>
            <%--                             actionListener="#{TrocarCartaoRecorrenciaControle.listenerCodigoContrato}">--%>
            <%--                <i class="fa-icon-exchange texto-size-18"></i> Trocar cartão--%>
            <%--                <f:attribute name="codigoContrato" value="#{TelaClienteControle.contratoSelecionado.codigo}"/>--%>
            <%--            </a4j:commandLink>--%>
        </h:panelGroup>

        <%-- Horário e Duração --%>
        <h:panelGroup>
            <h:outputText styleClass="upper negrito" value="Horário: "/>
            <h:outputText id="hrContrato" value="#{TelaClienteControle.contratoSelecionado.descricaoHorarioContrato_Apresentar}"/>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
        <h:panelGroup>
            <h:outputText styleClass="upper negrito" value="Duração: "/>
            <h:outputText  id="plnDuracao" value="#{TelaClienteControle.contratoSelecionado.contratoDuracao.descricaoDuracao}"/>
        </h:panelGroup>

        <%-- Plano e Tipo Contrato --%>
        <h:panelGroup>
            <h:outputText styleClass="upper negrito" value="Plano: "/>
            <h:outputText id="descPlano" value="#{TelaClienteControle.contratoSelecionado.plano.descricao}"/>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
        <h:panelGroup id="agendadoEspontaneo">
            <h:outputText styleClass="upper negrito"  value="Tipo Contrato: "/>
            <a4j:commandLink id="linkContratoEspontaneo" styleClass="linkAzul icon texto-size-14 tooltipster"
                             rendered="#{!TelaClienteControle.contratoSelecionado.contratoAgendado}"
                             value="#{msg_aplic.prt_Contrato_espontaneo}"
                             actionListener="#{ContratoControle.prepararAlteracaoTipoContrato}"
                             oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaTipoContrato');"
                             reRender="panelUsuarioSenhaTipoContrato"
                             title="#{msg_aplic.prt_Contrato_Tipodica}">
                <f:attribute value="#{TelaClienteControle.contratoSelecionado}" name="contratoAlteracaoTipo"/>
            </a4j:commandLink>

            <a4j:commandLink id="linkContratoAgendamento" styleClass="linkAzul icon texto-size-14 tooltipster"
                             rendered="#{TelaClienteControle.contratoSelecionado.contratoAgendado}"
                             value="#{msg_aplic.prt_Contrato_agendado}"
                             title="#{msg_aplic.prt_Contrato_Tipodica}"
                             actionListener="#{ContratoControle.prepararAlteracaoTipoContrato}"
                             oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaTipoContrato');"
                             reRender="panelUsuarioSenhaTipoContrato">
                <f:attribute value="#{TelaClienteControle.contratoSelecionado}" name="contratoAlteracaoTipo"/>
            </a4j:commandLink>
        </h:panelGroup>

        <%-- Resp. Lançamento Contrato e Condição de Pagamento --%>
        <h:panelGroup>
            <h:outputText styleClass="upper negrito" value="#{msg_aplic.prt_Contrato_responsavelPeloContrato}: "/>
            <h:outputText value="#{TelaClienteControle.contratoSelecionado.responsavelContrato.nome}"/>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
        <h:panelGroup id="consultorContrato">
            <h:outputText styleClass="upper negrito" value="Consultor Responsável: "/>
            <h:outputText rendered="#{!ClienteControle.alterandoConsultor}"
                          value="#{TelaClienteControle.contratoSelecionado.consultor.pessoa.nome}"/>

            <h:panelGroup rendered="#{ClienteControle.permiteAlterarConsultorContrato}">
                <h:panelGroup rendered="#{ClienteControle.alterandoConsultor}" layout="block" styleClass="cb-container">
                    <rich:spacer width="5px"/>
                    <h:selectOneMenu value="#{TelaClienteControle.contratoSelecionado.consultor.codigo}">
                        <f:selectItems value="#{ClienteControle.listaSelectConsultor}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <c:if test="${!param.readOnly}">
                    <h:panelGroup>
                        <rich:spacer width="5px"/>
                        <a4j:commandLink rendered="#{!ClienteControle.alterandoConsultor}" styleClass="texto-size-14" id="alterarConsultorContrato"
                                         action="#{TelaClienteControle.permitirAlterarConsultorContrato}"
                                         reRender="consultorContrato" value="Alterar"/>
                        <a4j:commandLink rendered="#{ClienteControle.alterandoConsultor}"  styleClass="texto-size-14" id="salvarAlteracaoConsultorContrato"
                                         action="#{TelaClienteControle.alterarConsultorContrato}"
                                         reRender="consultorContrato" value="Salvar"/>
                    </h:panelGroup>
                </c:if>
            </h:panelGroup>
        </h:panelGroup>

        <%-- Lançamento e Condição de Renovável Automaticamente --%>
        <h:panelGroup>
            <h:outputText styleClass="upper negrito" value="Lançamento: "/>
            <h:outputText id="lancamentodata" value="#{TelaClienteControle.contratoSelecionado.dataLancamento_Apresentar}"/>
            <h:outputText style="font-weight: bold;" styleClass="red" value=" CONTRATO IMPORTADO"
                          rendered="#{TelaClienteControle.contratoSelecionado.importacao}"/>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
        <h:panelGroup id="containerAlterarContratoRenovacao" rendered="#{TelaClienteControle.apresentarRenovavelAutomaticamente}">
            <h:outputText styleClass="upper negrito" value="Renovável Automaticamente:"/>
            <h:panelGroup layout="block" styleClass="cb-container" style="margin: 0 5px;">
                <h:selectOneMenu id="comboRenovarAutomatico" value="#{TelaClienteControle.contratoSelecionado.permiteRenovacaoAutomatica}">
                    <f:selectItem itemValue="#{true}" itemLabel="SIM"/>
                    <f:selectItem itemValue="#{false}" itemLabel="NÃO"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <a4j:commandLink styleClass="texto-size-14" id="alterarRenovavelAltomaticamenteContrato"
                             action="#{TelaClienteControle.confirmarAlterarContatroRenovarAutomaticamente}"
                             reRender="panelAutorizacaoFuncionalidade" value="Alterar"/>
        </h:panelGroup>
        <h:panelGroup rendered="#{!TelaClienteControle.apresentarRenovavelAutomaticamente}"></h:panelGroup>

        <%-- Data Término Original --%>
        <c:choose>
            <c:when test="${TelaClienteControle.contratoSelecionado.vigenciaAte_Apresentar ne TelaClienteControle.contratoSelecionado.vigenciaAteAjustada_Apresentar}">
                <h:panelGroup>
                    <h:outputText styleClass="upper negrito" value="Data Término Original: "/>
                    <h:outputText id="dtFimAutoOrig" value="#{TelaClienteControle.contratoSelecionado.vigenciaAte_Apresentar}"/>
                </h:panelGroup>
                <h:panelGroup></h:panelGroup>
                <h:panelGroup></h:panelGroup>
            </c:when>
        </c:choose>

        <%-- Empresa --%>
        <c:choose>
            <c:when test="${TelaClienteControle.apresentarEmpresa}">
                <h:panelGroup>
                    <h:outputText styleClass="upper negrito" value="Empresa: "/>
                    <h:outputText value="#{TelaClienteControle.contratoSelecionado.empresa.nome}"/>
                </h:panelGroup>
                <h:panelGroup></h:panelGroup>
                <h:panelGroup></h:panelGroup>
            </c:when>
        </c:choose>

        <%-- Bolsa --%>
        <c:choose>
            <c:when test="${TelaClienteControle.contratoSelecionado.bolsa_Apresentar eq 'Sim'}">
                <h:panelGroup>
                    <h:outputText styleClass="upper negrito" value="Bolsa: "/>
                    <h:outputText id="bolsa_Apresentar" value="#{TelaClienteControle.contratoSelecionado.bolsa_Apresentar}"/>
                </h:panelGroup>
                <h:panelGroup></h:panelGroup>
                <h:panelGroup></h:panelGroup>
            </c:when>
        </c:choose>

        <%-- Convênio de desconto --%>
        <c:choose>
            <c:when test="${!empty TelaClienteControle.contratoSelecionado.convenioDesconto.descricao}">
                <h:panelGroup>
                    <h:outputText styleClass="upper negrito" value="Convênio de desconto: "/>
                    <h:outputText value="#{TelaClienteControle.contratoSelecionado.convenioDesconto.descricao}"/>
                </h:panelGroup>
                <h:panelGroup></h:panelGroup>
                <h:panelGroup></h:panelGroup>
            </c:when>
        </c:choose>

        <%-- Observação do Contrato --%>
        <c:choose>
            <c:when test="${!empty TelaClienteControle.contratoSelecionado.observacao}">
                <h:panelGroup>
                    <h:outputText styleClass="upper negrito" value="Observação do Contrato: "/>
                    <h:outputText escape="false"
                                  value="#{TelaClienteControle.contratoSelecionado.observacao}"/>
                </h:panelGroup>
                <h:panelGroup></h:panelGroup>
                <h:panelGroup></h:panelGroup>
            </c:when>
        </c:choose>

        <c:if test="${TelaClienteControle.contratoSelecionado.pessoaOriginal.codigo > 0}">
            <h:panelGroup>
                <h:outputText styleClass="upper negrito" value="Direito de uso concedido "/>
                <h:outputText escape="false" rendered="#{TelaClienteControle.mapaMostrar['RECUPERAR_DIREITO_USO']}"
                              value="<span class='upper negrito'>para:</span> #{TelaClienteControle.contratoSelecionado.pessoa.nome}" />
                <h:outputText escape="false" rendered="#{!TelaClienteControle.mapaMostrar['RECUPERAR_DIREITO_USO']}"
                              value="<span class='upper negrito'>de:</span> #{TelaClienteControle.contratoSelecionado.pessoaOriginal.nome} <span class='upper negrito'>para:</span> #{TelaClienteControle.contratoSelecionado.pessoa.nome}" />
        </h:panelGroup>

            <h:panelGroup></h:panelGroup>
            <h:panelGroup></h:panelGroup>
        </c:if>

        <%-- Origem --%>
        <c:choose>
            <c:when test="${TelaClienteControle.contratoSelecionado.origemSistema.descricao ne 'ZillyonWeb'}">
                <h:panelGroup>
                    <h:outputText styleClass="upper negrito" value="Origem: "/>
                    <h:outputText  id="origemSistema" value="#{TelaClienteControle.contratoSelecionado.origemSistema.descricao}"/>
                </h:panelGroup>
                <h:panelGroup></h:panelGroup>
                <h:panelGroup></h:panelGroup>
            </c:when>
        </c:choose>

        <c:if test="${TelaClienteControle.apresentarDataAlteracaoManual}">
            <h:panelGroup>
                <h:outputText styleClass="upper negrito" value="Alteração Data Base: "/>
                <h:outputText id="dtBaseContrato"
                              value="#{TelaClienteControle.contratoSelecionado.dataAlteracaoManual_Apresentar}"/>
            </h:panelGroup>
            <h:panelGroup></h:panelGroup>
            <h:panelGroup>
            </h:panelGroup>
        </c:if>

        <%-- XNUMPRO --%>
        <c:choose>
            <c:when test="${ConfiguracaoSistemaControle.configuracaoSistemaVO.sesiCe}">
                <h:panelGroup>
                    <h:outputText styleClass="upper negrito" value="XNUMPRO: "/>
                    <h:outputText id="campo_xnumpro_sesiCe" value="#{TelaClienteControle.contratoSelecionado.xnumpro}"/>
                </h:panelGroup>
            </c:when>
        </c:choose>

        <h:panelGroup rendered="#{TelaClienteControle.contratoSelecionado.possuiGrupoDesconto}">
            <h:outputText styleClass="upper negrito" value="Grupo com Desconto: "/>
            <h:outputText value="#{TelaClienteControle.contratoSelecionado.grupo.descricao}"/>
        </h:panelGroup>
        <h:panelGroup rendered="#{TelaClienteControle.contratoSelecionado.possuiGrupoDesconto}"></h:panelGroup>

        <h:panelGroup id="pgInfoCreditoTreinoContrato">
            <h:panelGroup rendered="#{TelaClienteControle.contratoSelecionado.vendaCreditoTreino}">
                <h:outputText style="font-weight: bold"  value="Quantidade #{ClienteControle.configNomenclaturaVendaCredito} Compra: "/>
                <h:outputText value="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.quantidadeCreditoCompra}"/>
                <h:outputText rendered="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.creditoTreinoNaoCumulativo}"
                              style="font-weight: bold; padding-left: 10px" value="Quantidade Mensal: " />
                <h:outputText rendered="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.creditoTreinoNaoCumulativo}"
                              value="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.quantidadeCreditoMensal}" />
                <h:outputText style="font-weight: bold; padding-left: 10px"  value="Saldo: "/>
                <h:outputText value="#{TelaClienteControle.saldoCreditosProcessado}"/>

                <h:outputText rendered="#{TelaClienteControle.marcacoesFuturas != null && TelaClienteControle.marcacoesFuturas == 1}" value=" (-#{TelaClienteControle.marcacoesFuturas} marcação futura)"/>
                <h:outputText rendered="#{TelaClienteControle.marcacoesFuturas != null && TelaClienteControle.marcacoesFuturas > 1}" value=" (-#{TelaClienteControle.marcacoesFuturas} marcações futuras)"/>

                <h:outputText style="font-weight: bold; padding-left: 10px" value="Vezes por semana: "
                              rendered="#{!TelaClienteControle.contratoSelecionado.vendaCreditoSessao}"/>
                <h:outputText value="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.numeroVezesSemana}"
                              rendered="#{!TelaClienteControle.contratoSelecionado.vendaCreditoSessao}"/>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGrid>

    <div class="caixaDados" style="border: none !important;">
        <h:outputText value="MODALIDADES" styleClass="texto-size-14 negrito cinza pl10"/>
       <c:if test="${!param.readOnly}">
        <a4j:commandLink id="linkManutencaoModalidade"
                         styleClass="linkAzul texto-size-14"
                         style="float: right; margin-left: 10px"
                         rendered="#{TelaClienteControle.mapaMostrar['MANUTENCAO_MODALIDADE'] && TelaClienteControle.contratoSelecionado.empresa.codigo == TelaClienteControle.empresaLogado.codigo}"
                         oncomplete="#{ClienteControle.urlPopup}#{ClienteControle.mensagemNotificar}"
                         action="#{ManutencaoModalidadeControle.novo}">
            <i class="fa-icon-cogs"></i>
            <h:outputText value="Manutenção de modalidade" />
            <f:setPropertyActionListener value="Manutenção só poderá ser feita após o retorno da remessa da(s) parcela(s) acima!" target="#{ClienteControle.tituloModalParcelaRemessa}"/>
        </a4j:commandLink>
       </c:if>

        <a4j:commandLink id="linkFaltas"
                         styleClass="linkAzul texto-size-14 tooltipster"
                         style="float: right;"
                         title="Vejas as faltas das turmas removidas via manutenção de modalidade. "
                         reRender="mdlHistoricoTurmas"
                         action="#{TelaClienteControle.registrarClickEquipeControle}"
                         oncomplete="Richfaces.showModalPanel('mdlHistoricoTurmas')"
                         rendered="#{!TelaClienteControle.contratoSelecionado.vendaCreditoTreino and TelaClienteControle.contratoSelecionado.historicoTurmasContrato.qtd > 0}">
            <i class="fa-icon-history"></i>
            <h:outputText value="Faltas de turmas removidas (#{TelaClienteControle.contratoSelecionado.historicoTurmasContrato.qtd}) " />
        </a4j:commandLink>
    </div>

    <h:panelGroup layout="block" id="tabelaContratoModalidade">
        <a4j:repeat id="listaModalidade"
                    rowKeyVar="index"
                    value="#{TelaClienteControle.contratoSelecionado.contratoModalidadeVOs}"
                    var="contratoModalidade">
            <h:panelGroup id="modalidadeContratada" layout="block" styleClass="caixaDados" >
                <h:outputText id="nomeModalidade" styleClass="cinza texto-size-14" value="#{contratoModalidade.modalidade.nome}"/>
                <h:outputText id="nrVezesSemana" styleClass="cinza negrito texto-size-14" value="#{contratoModalidade.nrVezesSemana}X/SEMANA"
                              style="float: right;"/>
            </h:panelGroup>
            <h:panelGroup rendered="#{not empty contratoModalidade.contratoModalidadeTurmaVOs}">

                <a4j:repeat rowKeyVar="index"
                            id="turmasdetalhes"
                            value="#{contratoModalidade.contratoModalidadeTurmaVOs}"
                            var="modalidadeTurma">

                    <table class="turmasTl">
                        <tr style="vertical-align: top;">
                            <td style="width: 75%;">
                                <h:outputText styleClass="cinza negrito texto-size-14" value="TURMA: #{modalidadeTurma.turma.descricao}"/>
                                <h:dataTable id="turmaHorario" styleClass="tabelaDados semZebra"
                                             value="#{modalidadeTurma.contratoModalidadeHorarioTurmaVOs}"
                                             var="horario">
                                    <h:column>
                                        <h:outputText value="#{horario.horarioTurma.diaSemana_Apresentar}"/>
                                    </h:column>
                                    <h:column>
                                        <h:outputText value="#{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal}"/>
                                    </h:column>
                                    <h:column>
                                        <h:outputText value="#{horario.horarioTurma.professor.pessoa.nome}"/>
                                    </h:column>
                                    <h:column>
                                        <h:outputText value="#{horario.horarioTurma.ambiente.descricao}"/>
                                    </h:column>
                                    <h:column>
                                        <h:outputText value="#{horario.horarioTurma.nivelTurma.descricao}"/>
                                    </h:column>

                                    <h:column>
                                        <a4j:commandLink ajaxSingle="true" value="Repor" styleClass="linkAzul" id="reporAulaHorarioTurma"
                                                         title="Agendar Reposição para substituir o horário de: #{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal}"
                                                         actionListener="#{ClienteControle.prepararConsultaTurmaListener}"
                                                         oncomplete="#{ClienteControle.onCompleteReposicao}" reRender="panelAutorizacaoFuncionalidade">
                                            <f:attribute name="horarioTurma" value="#{horario.horarioTurma}"/>
                                        </a4j:commandLink>
                                    </h:column>
                                    <h:column>
                                        <a4j:commandLink value="Desmarcar" styleClass="linkAzul"
                                                         ajaxSingle="true"
                                                         id="demasmarAula"
                                                         title="Desmarcar o horário de: #{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal}"
                                                         actionListener="#{ClienteControle.prepararConsultaTurmaListenerDesmarcar}"
                                                         reRender="formAulaDesmarcada, tituloModal"
                                                         oncomplete="Richfaces.showModalPanel('modalPanelAulaDesmarcada');">
                                            <f:attribute name="horarioTurma"
                                                         value="#{horario.horarioTurma}"/>
                                        </a4j:commandLink>
                                    </h:column>
                                </h:dataTable>
                            </td>
                            <td style="width: 25%; padding: 0 !important; text-align: center; ">
                                <div style="background-color: #E5E5E5;padding: 10px;">
                                    <h:outputText styleClass="cinza negrito texto-size-14" value="AULAS"/>
                                </div>


                                <h:panelGroup style="display: block;margin-top: 14px;margin-bottom: 5px;"
                                              rendered="#{!TelaClienteControle.contratoSelecionado.vendaCreditoTreino || TelaClienteControle.contratoSelecionado.vendaCreditoSessao}">
                                    <h:outputText styleClass="cinza texto-size-60 tooltipster"
                                                  title="Presenças + Faltas + Aulas desmarcadas (exceto aulas desmarcadas futuras)"
                                                  value="#{modalidadeTurma.turma.totalAulasAteHoje}/"/>
                                    <h:outputText styleClass="cinza texto-size-60 tooltipster"
                                                  title="Total de aulas previstas"
                                                  value="#{modalidadeTurma.turma.totalAulas}"/>
                                </h:panelGroup>

                                <h:panelGroup style="display: block;margin-top: 5px;margin-bottom: 20px;"
                                              rendered="#{!TelaClienteControle.contratoSelecionado.vendaCreditoTreino || TelaClienteControle.contratoSelecionado.vendaCreditoSessao}">
                                    <h:outputText styleClass="cinza texto-size-14 w50, cinza texto-size-14 w50 tooltipster"
                                                  title="(Total de aulas previstas) -  (Presenças + Faltas + Aulas desmarcadas (exceto aulas desmarcadas futuras))"
                                                  value="Aulas restantes: #{modalidadeTurma.turma.totalAulas - modalidadeTurma.turma.totalAulasAteHoje}"> </h:outputText>
                                </h:panelGroup>

                                <h:panelGroup style="display: block;margin-top: 14px;margin-bottom: 20px;" id="pgSaldoCreditoTurma"
                                              rendered="#{TelaClienteControle.contratoSelecionado.vendaCreditoTreino && !TelaClienteControle.contratoSelecionado.vendaCreditoSessao}">
                                    <h:outputText styleClass="cinza texto-size-60 tooltipster"
                                                  title="Saldo de crédito. <br> Obs.: O saldo de créditos pode ser maior que a quantidade de compra, <br> pois pode haver transferência de saldo do contrato anterior ou ajuste manual de crédito para maior."
                                                  value="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.quantidadeCreditoDisponivel}/"/>

                                    <h:outputText styleClass="cinza texto-size-60 tooltipster"
                                                  title="Quantidade de créditos que o cliente comprou"
                                                  value="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.quantidadeCreditoCompra}"/>
                                </h:panelGroup>


                                <h:panelGrid columns="2" styleClass="semBorda" columnClasses="cinza texto-size-14 w50, cinza texto-size-14 w50"
                                             width="100%" id="gridAulas">
                                    <h:panelGroup rendered="#{!TelaClienteControle.contratoSelecionado.vendaCreditoTreino || TelaClienteControle.contratoSelecionado.vendaCreditoSessao}">
                                        <h:outputText style="margin: 10px;" styleClass="negrito" value="#{modalidadeTurma.turma.totalPresencas}"/>
                                        <h:outputText value="Presenças"/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{TelaClienteControle.contratoSelecionado.vendaCreditoTreino && !TelaClienteControle.contratoSelecionado.vendaCreditoSessao}">
                                        <h:outputText style="margin: 10px;" styleClass="negrito" value="#{TelaClienteControle.totalPresencaContratoCredito}"/>
                                        <h:outputText value="Presenças"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{!TelaClienteControle.empresaLogado.habilitarSomaDeAulaNaoVigente}">
                                        <a4j:commandLink value="#{modalidadeTurma.turma.totalReposicoes}" id="reposicoesAulasContrato"
                                                         oncomplete="#{ReposicaoControle.onComplete}"
                                                         reRender="modalReposicoes" style="margin: 10px;"
                                                         styleClass="linkAzul negrito"
                                                         actionListener="#{ReposicaoControle.exibirReposicoes}">
                                            <f:attribute name="turmaReposicao"
                                                         value="#{modalidadeTurma.turma.codigo}"/>
                                            <f:attribute name="contrato"
                                                         value="#{TelaClienteControle.contratoSelecionado.codigo}"/>
                                        </a4j:commandLink>
                                        <h:outputText value="Reposições"/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{TelaClienteControle.empresaLogado.habilitarSomaDeAulaNaoVigente}">
                                        <a4j:commandLink value="#{modalidadeTurma.turma.totalReposicoes}"
                                                         oncomplete="#{ReposicaoControle.onComplete}"
                                                         reRender="modalReposicoes" style="margin: 10px;"
                                                         styleClass="linkAzul negrito tooltipster"
                                                         title="Estão sendo consideradas as aulas passadas e vigentes."
                                                         actionListener="#{ReposicaoControle.exibirReposicoes}">
                                            <f:attribute name="turmaReposicao"
                                                         value="#{modalidadeTurma.turma.codigo}"/>
                                            <f:attribute name="contrato"
                                                         value="#{TelaClienteControle.contratoSelecionado.codigo}"/>
                                        </a4j:commandLink>
                                        <h:outputText value="Reposições"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{(!TelaClienteControle.contratoSelecionado.vendaCreditoTreino)}">
                                        <a4j:commandLink value="#{modalidadeTurma.turma.totalFaltas}"
                                                         id="totalFaltas"
                                                         oncomplete="#{ClienteControle.mostrarListaAulasComFalta}"
                                                         reRender="modalFaltasAluno"
                                                         styleClass="linkAzul negrito" style="margin: 10px;"
                                                         actionListener="#{ClienteControle.exibirListaAulasFaltas}">
                                            <f:attribute name="listaFaltas" value="#{modalidadeTurma.turma.listaDeFaltas}"/>
                                        </a4j:commandLink>
                                        <h:outputText value="Faltas"/>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{(TelaClienteControle.contratoSelecionado.vendaCreditoTreino)}">
                                        <h:outputText styleClass="negrito" style="margin: 10px;"
                                                      value="#{TelaClienteControle.totalFaltaContratoCredito}"/>
                                        <h:outputText value="Faltas"/>
                                    </h:panelGroup>


                                    <h:panelGroup rendered="#{!TelaClienteControle.empresaLogado.habilitarSomaDeAulaNaoVigente}">                                      
                                        <a4j:commandLink value="#{modalidadeTurma.turma.totalAulasDesmarcadas}"
                                                         id="qtdeDesmarcadas"
                                                         oncomplete="#{ClienteControle.mostrarModalAulasDesmarcadas}"
                                                         reRender="formListaAulasDesmarcadas"
                                                         styleClass="linkAzul negrito" style="margin: 10px;"
                                                         actionListener="#{ClienteControle.exibirListaAulasDesmarcadas}">
                                            <f:attribute name="turmaReposicao"
                                                         value="#{modalidadeTurma.turma.codigo}"/>
                                            <f:attribute name="contrato"
                                                         value="#{TelaClienteControle.contratoSelecionado.codigo}"/>
                                        </a4j:commandLink>
                                        <h:outputText value="Desmarcadas"/>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{TelaClienteControle.empresaLogado.habilitarSomaDeAulaNaoVigente}">
                                        <a4j:commandLink value="#{modalidadeTurma.turma.totalAulasDesmarcadas}"
                                                         id="qtdeDesmarcadasComAulaPassada"
                                                         oncomplete="#{ClienteControle.mostrarModalAulasDesmarcadas}"
                                                         reRender="formListaAulasDesmarcadas"
                                                         styleClass="linkAzul negrito tooltipster" style="margin: 10px;"
                                                         title="Estão sendo consideradas as aulas passadas e vigentes."
                                                         actionListener="#{ClienteControle.exibirListaAulasDesmarcadas}">
                                            <f:attribute name="turmaReposicao"
                                                         value="#{modalidadeTurma.turma.codigo}"/>
                                            <f:attribute name="contrato"
                                                         value="#{TelaClienteControle.contratoSelecionado.codigo}"/>
                                        </a4j:commandLink>
                                        <h:outputText value="Desmarcadas"/>
                                    </h:panelGroup>
                                </h:panelGrid>
                                <h:panelGrid columns="2" styleClass="semBorda" columnClasses="cinza texto-size-14 w50, cinza texto-size-14 w50"
                                             width="100%" rendered="#{TelaClienteControle.empresaLogado.adicionarAulasDesmarcadasContratoAnterior and modalidadeTurma.turma.totalAulasDesmarcadasContratoPassado > 0}">
                                    <h:panelGroup>
                                        <a4j:commandLink value="#{modalidadeTurma.turma.totalAulasDesmarcadasContratoPassado}"
                                                         oncomplete="#{ClienteControle.mostrarModalAulasDesmarcadas}"
                                                         reRender="formListaAulasDesmarcadas"
                                                         styleClass="linkAzul negrito" style="margin: 10px;"
                                                         actionListener="#{ClienteControle.exibirListaAulasDesmarcadasContratoPassado}">
                                            <f:attribute name="contratoBaseadoRematricula"
                                                         value="#{TelaClienteControle.contratoSelecionado.contratoBaseadoRematricula}"/>
                                            <f:attribute name="contratoBaseadoRenovacao"
                                                         value="#{TelaClienteControle.contratoSelecionado.contratoBaseadoRenovacao}"/>
                                            <f:attribute name="contrato"
                                                         value="#{TelaClienteControle.contratoSelecionado.codigo}"/>
                                        </a4j:commandLink>
                                        <h:outputText value="Desmarcadas de Contrato Anterior"/>
                                    </h:panelGroup>     
                                </h:panelGrid>
                            </td>
                        </tr>
                    </table>

                </a4j:repeat>
            </h:panelGroup>

        </a4j:repeat>
        <SCRIPT>
            carregarTooltipsterContrato();
        </SCRIPT>
    </h:panelGroup>   

    <h:panelGroup layout="block" styleClass="rodapePainelAluno" 
                  id="separatormodalidade"
                  style="text-align: right;border-bottom: #E5E5E5 1px solid;border-top: 0; display: block !important; margin-top: 20px;">
    </h:panelGroup>



    <h:panelGroup layout="block" id="logControleCreditoTreino" rendered="#{TelaClienteControle.contratoSelecionado.vendaCreditoTreino}" >
        <h:panelGroup layout="block" id="gridInfoContCredTreino" rendered="#{TelaClienteControle.contratoSelecionado.listaControleCreditoTreino != null}" >
            <h:outputText value="Extrato #{TelaClienteControle.configNomenclaturaVendaCredito}"
                          style=" display: block;margin: 20px 0px"
                          styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
            <h:panelGrid id="pgCreditoTreino" columns="1" width="100%">
                <h:panelGroup layout="block" styleClass="container-botoes" style="text-align: right;margin: 0px 20px;width: calc(100% - 40px)">
                    <a4j:commandLink id="novoControleCredito"
                                     styleClass="botaoPrimarioSmall texto-size-14 pull-left"
                                     action="#{TelaClienteControle.novoAjusteManualControleCreditoTreino}"
                                     rendered="#{TelaClienteControle.contratoSelecionado.listaControleCreditoTreino != null}"
                                     reRender="listaHistoricoCreditoTreino, formControleCreditoTreino"
                                     oncomplete="#{TelaClienteControle.msgAlert}"
                                     style="line-height: normal;margin-top: 12px;vertical-align: middle;"
                                     accesskey="2" >
                        <i class="fa-icon-plus" ></i> &nbsp Novo
                    </a4j:commandLink>

                    <a4j:commandLink styleClass="pull-left lineHeight-10em" style="padding-left: 20px;vertical-align: middle;font-size: 20px"
                                     reRender="modalFiltroCredito"
                                     oncomplete="Richfaces.showModalPanel('modalFiltroCredito')">
                        <i title="Filtro" class="tooltipster fa-icon-filter bi-btn-refresh bi-link lineHeight-3em tooltipstered"></i>
                    </a4j:commandLink>

                    <a4j:commandLink id="btnExcelControleCredito"
                                     styleClass="linkPadrao"
                                     rendered="#{TelaClienteControle.contratoSelecionado.listaControleCreditoTreino != null}"
                                     actionListener="#{TelaClienteControle.exportarControleCreditoTreino}"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','controleCredito', 800,200);#{ExportadorListaControle.msgAlert}"
                                     accesskey="3">
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="lista" value="#{TelaClienteControle.contratoSelecionado.listaControleCreditoTreino}"/>
                        <f:attribute name="atributos" value="#{TelaClienteControle.atributosImprimirExtratoCreditoTreino}"/>
                        <f:attribute name="prefixo" value="ControleCreditoTreino#{TelaClienteControle.contratoSelecionado.pessoa.nome}"/>
                        <f:attribute name="titulo" value="Controle #{TelaClienteControle.configNomenclaturaVendaCredito}"/>
                        <f:attribute name="subTitulo" value="Aluno: #{TelaClienteControle.clienteVO.nome_Apresentar}"/>
                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="btnPDFControleCredito"
                                     styleClass="linkPadrao"
                                     actionListener="#{TelaClienteControle.exportarControleCreditoTreino}"
                                     rendered="#{TelaClienteControle.contratoSelecionado.listaControleCreditoTreino != null}"
                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','controleCredito', 800,200);#{ExportadorListaControle.msgAlert}"
                                     accesskey="4">
                        <f:attribute name="tipo" value="pdf"/>
                        <f:attribute name="lista" value="#{TelaClienteControle.contratoSelecionado.listaControleCreditoTreino}"/>
                        <f:attribute name="atributos" value="#{TelaClienteControle.atributosImprimirExtratoCreditoTreino}"/>
                        <f:attribute name="prefixo" value="ControleCreditoTreino"/>
                        <f:attribute name="titulo" value="Controle #{TelaClienteControle.configNomenclaturaVendaCredito}"/>
                        <f:attribute name="subTitulo" value="Aluno: #{TelaClienteControle.clienteVO.nome_Apresentar}"/>
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGroup id="pgTableCreditoTreino" >
                <rich:dataTable
                        id="listaHistoricoCreditoTreino" width="100%" border="0"
                        cellspacing="0" cellpadding="0" styleClass="tabelaDados tabelaZebrada"
                        style="margin: 0px 20px;"
                        rows="5"
                        rendered="#{TelaClienteControle.contratoSelecionado.listaControleCreditoTreino != null}"
                        value="#{TelaClienteControle.contratoSelecionado.listaControleCreditoTreino}"
                        var="controleCreditoTreino">
                    <rich:column >
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"  value="Data"/>
                        </f:facet>
                        <a4j:commandLink action="#{TelaClienteControle.visualizarControleCreditoTreino}" oncomplete="#{TelaClienteControle.msgAlert}" reRender="formControleCreditoTreino">
                            <h:outputText value="#{controleCreditoTreino.dataOperacao_Apresentar}"/>
                            <f:setPropertyActionListener value="#{controleCreditoTreino}" target="#{TelaClienteControle.controleCreditoTreinoVO}"/>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Operação"/>
                        </f:facet>
                        <a4j:commandLink action="#{TelaClienteControle.visualizarControleCreditoTreino}" oncomplete="#{TelaClienteControle.msgAlert}" reRender="formControleCreditoTreino">
                            <h:outputText value="#{controleCreditoTreino.tipoOperacaoCreditoTreinoEnum.descricao}"/>
                            <f:setPropertyActionListener value="#{controleCreditoTreino}" target="#{TelaClienteControle.controleCreditoTreinoVO}"/>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column >
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Usuário"/>
                        </f:facet>
                        <h:outputText value="#{controleCreditoTreino.nomeUsuarioCurto}"/>
                    </rich:column>
                    <rich:column style="padding: 4px">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Observação"/>
                        </f:facet>
                        <h:outputText value="#{controleCreditoTreino.observacaoCurta}" title="#{controleCreditoTreino.observacao}"/>
                    </rich:column>
                    <rich:column headerClass="col-text-align-center" styleClass="col-text-align-center">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Aula Desmarcada"/>
                        </f:facet>
                        <h:outputText value="#{controleCreditoTreino.aulaDesmarcadaCurta}"
                                      title="#{controleCreditoTreino.aulaDesmarcada_Apresentar}"
                                      rendered="#{controleCreditoTreino.tipoOperacaoCreditoTreinoEnum != 'MARCOU_AULA'}"/>
                    </rich:column>
                    <rich:column headerClass="col-text-align-center" styleClass="col-text-align-center">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Aula Marcada"/>
                        </f:facet>
                        <h:outputText  value="#{controleCreditoTreino.aulaMarcadaCurta}" title="#{controleCreditoTreino.aulaMarcada_Apresentar}"/>
                    </rich:column>
                    <rich:column headerClass="col-text-align-center" styleClass="col-text-align-center">
                        <f:facet name="header">
                            <h:outputText value="QTD."/>
                        </f:facet>
                        <h:outputText value="#{controleCreditoTreino.quantidade}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-center"
                                 rendered="#{(!TelaClienteControle.aplicouFiltroExtratoCreditoTreino)}"
                                 headerClass="col-text-align-center">
                        <f:facet name="header">
                            <h:outputText value="Saldo"/>
                        </f:facet>
                        <h:outputText value="#{controleCreditoTreino.saldo}"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" renderIfSinglePage="false" styleClass="scrollPureCustom" for="listaHistoricoCreditoTreino" maxPages="5"  id="scControleCreditoTreino"/>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="rodapePainelAluno"
                      id="separatorlistaControleCreditoTreino"
                      style="text-align: right;border-bottom: #E5E5E5 1px solid;border-top: 0; display: block;"></h:panelGroup>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="rodapePainelAluno"
                  id="separatorlistaoperacoescontrato"
                  style="text-align: right;border-bottom: #E5E5E5 1px solid;border-top: 0; display: block;">
        <c:if test="${not TelaClienteControle.financeiroContrato}">
            <a4j:commandLink id="linkVerMaisFinanceiroContrato"
                             reRender="separatorlistaoperacoescontrato, panelOpracoesContrato"
                             action="#{TelaClienteControle.verMaisDadosContrato}"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;">
                <i class="fa-icon-eye-open"></i>
                <h:outputText value="Mostrar detalhes do contrato" />


            </a4j:commandLink>
        </c:if>
        <c:if test="${TelaClienteControle.financeiroContrato}">
            <a4j:commandLink id="linkVerMenosFinanceiroContrato"
                             reRender="separatorlistaoperacoescontrato, panelOpracoesContrato"
                             action="#{TelaClienteControle.verMenosDadosContrato}"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;">
                <i class="fa-icon-eye-close"></i>
                <h:outputText value="Esconder detalhes do contrato" />

            </a4j:commandLink>
        </c:if>
    </h:panelGroup>
    <c:if test="${TelaClienteControle.financeiroContrato}">
        <h:outputText value="Histórico de Contrato"
                      style="margin-top: 20px; display: block;"
                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>

        <h:dataTable
                id="listaHistoricoContrato" width="100%" border="0"
                cellspacing="0" cellpadding="2"
                styleClass="tabelaDados semZebra"
                value="#{TelaClienteControle.listaHistoricoContrato}"
                var="historicoContrato">

            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_HistoricoContrato_descricao}"/>
                </f:facet>
                <h:outputText value="#{historicoContrato.descricaoMin}" style="text-transform: capitalize;"/>
            </h:column>

            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_HistoricoContrato_dataLancamento}"/>
                </f:facet>
                <h:outputText value="#{historicoContrato.dataRegistro_Apresentar}">
                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                </h:outputText>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_HistoricoContrato_dataInicioSituacao}"/>
                </f:facet>
                <h:outputText value="#{historicoContrato.dataInicioSituacao_Apresentar}">
                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                </h:outputText>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_HistoricoContrato_dataFinalSituacao}"/>
                </f:facet>
                <h:outputText value="#{historicoContrato.dataFinalSituacao_Apresentar}">
                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                </h:outputText>
            </h:column>
            <h:column>
                <a4j:commandLink action="#{ClienteControle.selecionarDadosHistoricoContrato}"
                                 reRender="formHistoricoContrato,form:panelMensagemSuperior, form:panelMensagemInferior"
                                 oncomplete="Richfaces.showModalPanel('panelHistoricoContrato')"
                                 title="Visualizar Detalhes Histórico"
                                 styleClass="linkAzul icon tooltipster">
                    <i id="visualizarDetalhesHistoricoContrato" class="fa-icon-search"></i>
                </a4j:commandLink>
            </h:column>
        </h:dataTable>

        <h:panelGroup layout="block" styleClass="rodapePainelAluno"
                      id="separatorlistahistoricocontrato"
                      style="text-align: right;border-bottom: #E5E5E5 1px solid;border-top: 0; display: block !important;">
        </h:panelGroup>

        <h:outputText value="Operações de Contrato"
                      style="margin-top: 20px; display: block;"
                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>

        <h:outputText value="Não existem operações para este contrato."
                      style="margin-bottom: 20px; margin-top: 20px; display: block;"
                      rendered="#{empty TelaClienteControle.listaContratoOperacao}"
                      styleClass="texto-size-14 cinza pl20"/>

    <h:dataTable id="listaContratoOperacao" width="100%" border="0"
                 cellspacing="0" cellpadding="2" styleClass="tabelaDados semZebra"
                 value="#{TelaClienteControle.listaContratoOperacao}"
                 rendered="#{not empty TelaClienteControle.listaContratoOperacao}"
                 var="contratoOperacao">
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_ContratoOperacao_tipoOperacao}"/>
            </f:facet>
            <h:outputText value="#{contratoOperacao.tipoOperacao_Apresentar}"/>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoContrato_dataLancamento}"/>
            </f:facet>
            <h:outputText value="#{contratoOperacao.dataOperacao_Apresentar}"></h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoContrato_dataInicioSituacao}"/>
            </f:facet>
            <h:outputText value="#{contratoOperacao.dataInicioEfetivacaoOperacao_Apresentar}">
                <f:convertDateTime pattern="dd/MM/yyyy"/>
            </h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_HistoricoContrato_dataFinalSituacao}"/>
            </f:facet>
            <h:outputText value="#{contratoOperacao.dataFimEfetivacaoOperacao_Apresentar}">
                <f:convertDateTime pattern="dd/MM/yyyy"/>
            </h:outputText>
        </h:column>

        <h:column>
            <f:facet name="header">
                <h:outputText value="#{msg_aplic.prt_Qtde_dias_Operacao}"/>
            </f:facet>
            <h:outputText value="#{contratoOperacao.diasOperacao}"/>
        </h:column>

        <h:column>


                <a4j:commandLink id="btnVisualizarOper" style="text-align: right"
                                 action="#{ClienteControle.selecionarDadosContratoOperacao}"
                                 reRender="formContratoOperacao"
                                 oncomplete="Richfaces.showModalPanel('panelContratoOperacao')"
                                 title="Visualizar Detalhes Operação"
                                 styleClass="linkAzul icon tooltipster">
                    <i id="btnVisualizarOperIcon" class="fa-icon-search"></i>
                </a4j:commandLink>



                <a4j:commandLink id="btnImprimirComprovanteOp"
                                 title="Comprovante de Operação"
                                 action="#{ClienteControle.imprimirComprovanteOperacao}"
                                 style="margin-left: 10px;"
                                 oncomplete="abrirPopupPDFImpressao('relatorio/#{ClienteControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                 styleClass="linkAzul icon tooltipster">
                    <i id="btnImprimirComprovanteOpIcon" class="fa-icon-print"></i>
                </a4j:commandLink>

                <a4j:commandLink id="btnEnviarCancelamentoEmail"
                                 rendered="#{contratoOperacao.tipoOperacao == 'CA'}"
                                 title="Enviar e-mail com informações do cancelamento"
                                 action="#{ClienteControle.enviarEmailCancelamentoContrato}"
                                 style="margin-left: 10px;"
                                 oncomplete="#{ClienteControle.mensagemNotificar}"
                                 styleClass="linkAzul icon tooltipster">
                    <i class="fa-icon-paper-plane"></i>
                </a4j:commandLink>

                <h:outputLink styleClass="linkAzul icon tooltipster" style="margin-left: 10px;" title="Download Atestado" rendered="#{contratoOperacao.urlCompletaEmpty}"
                              target="_blank"
                              value="#{contratoOperacao.urlCompletaArquivoAtestado}">
                    <i class="fa-icon-download-alt"/>
                </h:outputLink>

                <a4j:commandLink id="estornarOperacao" reRender="panelAutorizacaoFuncionalidade"
                                 action="#{ClienteControle.estornarAtestadoCarencia}"
                                 rendered="#{contratoOperacao.permiteEstorno}"
                                 oncomplete="#{ClienteControle.msgAlert}"
                                 style="margin-left: 10px;"
                                 styleClass="linkAzul icon tooltipster"
                                 title="Estornar operação">
                    <i id="estornarOperacao" class="fa-icon-remove"></i>
                </a4j:commandLink>

                <a4j:commandLink id="btnConfirmarDesfazerCancelamentoProporcional"
                                 title="Desfazer Cancelamento"
                                 rendered="#{TelaClienteControle.apresentarEstornoCancelamento}"
                                 action="#{TelaClienteControle.confirmarDesfazerCancelamentoProporcional}"
                                 reRender="formModalDesfazerCancelamento"
                                 oncomplete="#{TelaClienteControle.msgAlert}"
                                 style="margin-left: 10px;"
                                 styleClass="linkAzul icon tooltipster">
                    <i id="btnConfirmarDesfazerCancelamentoProporcionalIcon" class="fa-icon-remove"></i>
                </a4j:commandLink>

            </h:column>
        </h:dataTable>
        <h:panelGroup layout="block" styleClass="rodapePainelAluno"
                      id="separatorlistaoperacoescontrato1"
                      style="text-align: right;border-bottom: #E5E5E5 1px solid;border-top: 0; display: block;">

        </h:panelGroup>

        <h:outputText value="Histórico de compras"
                      style="margin-top: 20px; display: block;"
                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
        <h:panelGroup id="containerListaCompras">
            <h:dataTable id="listaHistoricoComprasContrato"
                         styleClass="tabelaDados semZebra"
                         value="#{TelaClienteControle.listaHistoricoProduto}"
                         var="historicoCompras">
                <h:column>
                    <f:facet name="header">
                        <h:outputText  value="Código" />
                    </f:facet>
                    <h:outputText id="historicoComprasCodigo" value="#{historicoCompras.codigo}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText  value="#{msg_aplic.prt_HistoricoComprasCliente_contrato}" />
                    </f:facet>
                    <h:outputText id="historicoComprasContrato" value="#{historicoCompras.contrato.codigo}" />
                </h:column>
                <h:column rendered="#{TelaClienteControle.apresentarEmpresa}">
                    <f:facet name="header">
                        <h:outputText value="Empresa" />
                    </f:facet>
                    <h:outputText value="#{historicoCompras.empresa_Apresentar}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_descricao}" />
                    </f:facet>
                    <h:outputText id="historicoComprasDescricao" value="#{historicoCompras.descricao}" />
                </h:column>

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                    </f:facet>
                    <h:outputText id="historicoComprasDataLancamento" value="#{historicoCompras.dataLancamentoComHora_Apresentar}" />
                </h:column>

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_quantidade}" />
                    </f:facet>
                    <h:outputText id="historicoComprasQuantidade" value="#{historicoCompras.quantidade}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_unitario}" />
                    </f:facet>
                    <h:outputText id="historicoComprasValorUnitario"
                                  style="float: right; margin-right: 10px;"
                                  value="#{historicoCompras.precoUnitario}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>
                <h:column>

                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_desconto}" />
                    </f:facet>
                    <h:outputText id="historicoComprasValorDesconto"
                                  style="float: right; margin-right: 10px;"
                                  value="#{historicoCompras.valorDesconto}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_totalFinal}" />
                    </f:facet>
                    <h:outputText id="historicoComprasValorTotal"
                                  style="float: right; margin-right: 10px;"
                                  value="#{historicoCompras.totalFinal}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>

                <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center" rendered="#{TelaClienteControle.colunaCupomDesconto}">
                    <f:facet name="header">
                        <h:outputText value="Cupom Desconto"/>
                    </f:facet>
                    <h:outputText styleClass="col-text-align-center" value="#{historicoCompras.numeroCupomDesconto}">
                    </h:outputText>
                </rich:column>

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_situacao}" />
                    </f:facet>
                    <h:outputText id="historicoComprasSituacao" value="#{historicoCompras.situacao_Apresentar}" styleClass="tooltipster" title="#{historicoCompras.situacao eq 'EA' ? 'Aguardando Pagamento' : historicoCompras.situacao eq 'CA' ? historicoCompras.dataCancelamento_Hint : historicoCompras.dataPagamento_Hint}" />
                </h:column>
                <h:column
                        rendered="#{TelaClienteControle.apresentarValorParcialmentePago}">
                    <f:facet name="header">
                        <h:outputText value="Parc. Pago" />
                    </f:facet>
                    <h:outputText value="#{historicoCompras.valorParcialmentePagoApresentar}"/>
                </h:column>
            </h:dataTable>
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" rendered="#{TelaClienteControle.listaCompras.count > 0}">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="center" valign="middle">
                            <h:panelGroup layout="block"
                                          styleClass="paginador-container">
                                <h:panelGroup styleClass="pull-left"
                                              layout="block">
                                    <h:outputText
                                            styleClass="texto-size-14 cinza"
                                            value="Total #{TelaClienteControle.listaCompras.count} itens"></h:outputText>
                                </h:panelGroup>
                                <h:panelGroup layout="block"
                                              style="align-items: center">
                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="containerListaCompras"
                                                     actionListener="#{TelaClienteControle.primeiraPagina}">
                                        <i class="fa-icon-double-angle-left"></i>
                                        <f:attribute name="tipo" value="LISTA_COMPRAS" />
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaCompras"
                                                     actionListener="#{TelaClienteControle.paginaAnterior}">
                                        <i class="fa-icon-angle-left"></i>
                                        <f:attribute name="tipo" value="LISTA_COMPRAS" />
                                    </a4j:commandLink>

                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                  value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaCompras.paginaAtualApresentar}" rendered="true"/>
                                    <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="containerListaCompras"
                                                     actionListener="#{TelaClienteControle.proximaPagina}">
                                        <i class="fa-icon-angle-right"></i>
                                        <f:attribute name="tipo" value="LISTA_COMPRAS" />
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaCompras"
                                                     actionListener="#{TelaClienteControle.ultimaPagina}">
                                        <i class="fa-icon-double-angle-right"></i>
                                        <f:attribute name="tipo" value="LISTA_COMPRAS" />
                                    </a4j:commandLink>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                    <h:panelGroup styleClass="pull-right" layout="block">
                                        <h:outputText
                                                styleClass="texto-size-14 cinza "
                                                value="Itens por página "></h:outputText>
                                    </h:panelGroup>
                                    <h:panelGroup styleClass="cb-container pl20" layout="block">
                                        <h:selectOneMenu value="#{TelaClienteControle.listaCompras.limit}">
                                            <f:selectItem itemValue="#{6}"></f:selectItem>
                                            <f:selectItem itemValue="#{10}"></f:selectItem>
                                            <f:selectItem itemValue="#{20}"></f:selectItem>
                                            <f:selectItem itemValue="#{50}"></f:selectItem>
                                            <f:selectItem itemValue="#{100}"></f:selectItem>
                                            <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="containerListaCompras">
                                                <f:attribute name="tipo" value="LISTA_COMPRAS" />
                                            </a4j:support>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                </h:panelGroup>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </h:panelGrid>
        </h:panelGroup>


        <h:outputText value="Histórico de parcelas geradas"
                      style="margin-top: 20px; display: block;"
                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
        <h:panelGroup id="containerListaParcelas">
            <rich:dataTable id="listaHistoricoParcelasContrato"
                            styleClass="tabelaDados semZebra"
                            value="#{TelaClienteControle.listaHistoricoParcelas}"
                            var="historicoParcela">

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_codigo}" />
                    </f:facet>
                    <h:outputText id="historicoParcelasContrato" value="#{historicoParcela.contrato.codigo}" />
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Código" />
                    </f:facet>
                    <h:outputText id="historicoParcelasCodigo" value="#{historicoParcela.codigo}" />
                </rich:column>
                <rich:column rendered="#{TelaClienteControle.apresentarEmpresa}">
                    <f:facet name="header">
                        <h:outputText value="Empresa" />
                    </f:facet>
                    <h:outputText value="#{historicoParcela.empresa_Apresentar}" />
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}" />
                    </f:facet>
                    <h:outputText id="historicoParcelasDescricao" value="#{historicoParcela.descricao}" />
                    <h:outputText rendered="#{not empty historicoParcela.cupomDesconto}" value=" - CUPOM #{historicoParcela.cupomDesconto}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_DataLancada}" />
                    </f:facet>
                    <h:outputText id="historicoParcelasDataLancamento" value="#{historicoParcela.dataRegistro_Apresentar}">

                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <a4j:commandLink reRender="containerListaParcelas"
                                         actionListener="#{TelaClienteControle.ordenarLista}"
                                         style="padding: 7px 0;font-size: 1em; color: #777; font-weight: bold; border: 0; text-transform: uppercase; background-color: white;">
                            <f:attribute name="tipo"
                                         value="LISTA_PARCELAS"/>
                            <f:attribute name="orderBy"
                                         value="dataVencimento"/>
                            <h:outputText
                                    value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}"/>
                            <i class="${TelaClienteControle.listaParcelas.icon}" style="transform: rotate(90deg);"></i>
                        </a4j:commandLink>
                    </f:facet>
                    <h:outputText id="historicoParcelasDataVencimento" value="#{historicoParcela.dataVencimento_Apresentar}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}" />
                    </f:facet>
                    <h:outputText id="historicoParcelasValorParcela"
                                  style="float: right; margin-right: 10px;"
                                  value="#{historicoParcela.valorParcela}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}" />
                    </f:facet>
                    <h:outputText id="historicoParcelasSituacao" value="#{historicoParcela.situacao_Apresentar}" styleClass="tooltipster" title="#{historicoParcela.situacao eq 'EA' ? 'Aguardando Pagamento' : historicoParcela.situacao eq 'CA' ? historicoParcela.dataCancelamento_Hint : historicoParcela.dataPagamento_Hint}"/>
                </rich:column>
            </rich:dataTable>
            <h:panelGrid columns="1" width="100%" rendered="#{TelaClienteControle.listaParcelas.count > 0}" columnClasses="colunaCentralizada">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="center" valign="middle">
                            <h:panelGroup layout="block"
                                          styleClass="paginador-container">
                                <h:panelGroup styleClass="pull-left"
                                              layout="block">
                                    <h:outputText
                                            styleClass="texto-size-14 cinza"
                                            value="Total #{TelaClienteControle.listaParcelas.count} itens"></h:outputText>
                                </h:panelGroup>
                                <h:panelGroup layout="block"
                                              style="align-items: center">
                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="containerListaParcelas"
                                                     actionListener="#{TelaClienteControle.primeiraPagina}">
                                        <i class="fa-icon-double-angle-left"></i>
                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaParcelas"
                                                     actionListener="#{TelaClienteControle.paginaAnterior}">
                                        <i class="fa-icon-angle-left"></i>
                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                    </a4j:commandLink>

                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                  value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaParcelas.paginaAtualApresentar}" rendered="true"/>
                                    <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="containerListaParcelas"
                                                     actionListener="#{TelaClienteControle.proximaPagina}">
                                        <i class="fa-icon-angle-right"></i>
                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaParcelas"
                                                     actionListener="#{TelaClienteControle.ultimaPagina}">
                                        <i class="fa-icon-double-angle-right"></i>
                                        <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                    </a4j:commandLink>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                    <h:panelGroup styleClass="pull-right" layout="block">
                                        <h:outputText
                                                styleClass="texto-size-14 cinza "
                                                value="Itens por página "></h:outputText>
                                    </h:panelGroup>
                                    <h:panelGroup styleClass="cb-container pl20" layout="block">
                                        <h:selectOneMenu value="#{TelaClienteControle.listaParcelas.limit}">
                                            <f:selectItem itemValue="#{6}"></f:selectItem>
                                            <f:selectItem itemValue="#{10}"></f:selectItem>
                                            <f:selectItem itemValue="#{20}"></f:selectItem>
                                            <f:selectItem itemValue="#{50}"></f:selectItem>
                                            <f:selectItem itemValue="#{100}"></f:selectItem>
                                            <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="containerListaParcelas">
                                                <f:attribute name="tipo" value="LISTA_PARCELAS" />
                                            </a4j:support>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                </h:panelGroup>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </h:panelGrid>
        </h:panelGroup>


        <h:outputText value="Histórico de pagamentos efetuados"
                      style="margin-top: 20px; display: block;"
                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
        <h:outputText value="Não existem pagamentos para este contrato."
                      style="margin-bottom: 20px; margin-top: 20px; display: block;"
                      rendered="#{empty TelaClienteControle.listaHistoricoPagamentos}"
                      styleClass="texto-size-14 cinza pl20"/>
        <h:panelGroup id="containerListaPagamento">
            <h:dataTable id="listaHistoricoPagamentosContrato"
                         styleClass="tabelaDados semZebra"
                         rendered="#{not empty TelaClienteControle.listaHistoricoPagamentos}"
                         value="#{TelaClienteControle.listaHistoricoPagamentos}"
                         var="historicoPagamentos">
                <h:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="#{msg_aplic.prt_HistoricoComprasCliente_codigo}" />
                    </f:facet>
                    <h:outputText id="listaHistoricoPagamentosCodigo" value="#{historicoPagamentos.codigo}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold"
                                      value="#{msg_aplic.prt_HistoricoComprasCliente_nrRecibo}" />
                    </f:facet>
                    <h:outputText id="listaHistoricoPagamentosRecibo" value="#{historicoPagamentos.reciboPagamento.codigo}" />
                </h:column>
                <h:column rendered="#{TelaClienteControle.apresentarEmpresa}">
                    <f:facet name="header">
                        <h:outputText value="Empresa" />
                    </f:facet>
                    <h:outputText value="#{historicoPagamentos.empresa_Apresentar}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}" />
                    </f:facet>
                    <h:outputText id="listaHistoricoPagamentosNomePagador" value="#{historicoPagamentos.nomePagador}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                    </f:facet>
                    <h:outputText id="listaHistoricoPagamentosDataLancamento" value="#{historicoPagamentos.dataLancamento_Apresentar}">
                    </h:outputText>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}" />
                    </f:facet>
                    <h:outputText id="listaHistoricoPagamentosFormaPagamento" value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar} #{historicoPagamentos.creditoApresentar}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_valor}" />
                    </f:facet>
                    <h:outputText id="listaHistoricoPagamentosValor"
                                  style="float: right; margin-right: 10px;"
                                  value="#{historicoPagamentos.valorTotal}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_HistoricoComprasCliente_recibo}" style="margin-left: 10px;"/>
                    </f:facet>
                    <a4j:commandLink title="Imprimir recibo"  styleClass="linkAzul icon"
                                     actionListener="#{ReciboControle.prepareRecibo}"
                                     style="margin-left: 10px;"
                                     action="#{ReciboControle.imprimirReciboPDF}"
                                     oncomplete="abrirPopupPDFImpressao('relatorio/#{ReciboControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}" />
                        <i class="fa-icon-print texto-size-18 linkAzul"></i>
                    </a4j:commandLink>
                    &nbsp;
                    <a4j:commandLink  styleClass="linkAzul icon"
                                      reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, modalEnviarContratoEmail"
                                      title="Enviar recibo"
                                      oncomplete="#{ReciboControle.msgAlert}#{ReciboControle.mensagemNotificar}"
                                      actionListener="#{ReciboControle.prepararModalEnvioReciboPorEmail}">
                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                        <f:attribute name="pessoaVO" value="#{TelaClienteControle.cliente.pessoa}"/>
                        <i class="fa-icon-paper-plane texto-size-18 linkAzul"></i>
                    </a4j:commandLink>
                    &nbsp;
                    <c:if test="${not param.readOnly}">
                        <a4j:commandLink id="editarRecibo" styleClass="linkAzul icon"
                                         actionListener="#{EdicaoPagamentoControle.prepareRecibo}"
                                         title="Editar Recibo"
                                         reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao"
                                         oncomplete="#{EdicaoPagamentoControle.msgAlert}#{EdicaoPagamentoControle.mensagemNotificar}">
                            <f:attribute name="pagamentoVO" value="#{historicoPagamentos}"/>
                            <f:attribute name="tipoEdicao"
                                         value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento}"/>
                            <i class="fa-icon-edit texto-size-18 linkAzul"></i>
                        </a4j:commandLink>
                        &nbsp;
                    </c:if>

                    <a4j:commandLink id="visualizarRecibo"
                                     styleClass="linkAzul icon"
                                     title="Visualizar Recibo"
                                     actionListener="#{EstornoReciboControle.preparaRecibo}"
                                     action="#{EstornoReciboControle.preencherRecibo}"
                                     oncomplete="abrirPopup('estornoReciboForm.jsp', 'EstornoRecibo', 1000, 650);">
                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                        <i class="fa-icon-search texto-size-18 linkAzul"></i>
                    </a4j:commandLink>
                    &nbsp;
                    <a4j:commandLink id="gerarNotaFiscal2" reRender="panelAutorizacaoFuncionalidade"
                                     value="NFSe"
                                     styleClass="linkAzul icon"
                                     onclick="#{historicoPagamentos.reciboPagamento.onclickNFSe}"
                                     actionListener="#{ReciboControle.prepareRecibo}"
                                     action="#{ReciboControle.emitirNFSe}"
                                     rendered="#{ClienteControle.clienteVO.empresa.apresentarBotoesFaturamentoNFSe && LoginControle.permissaoAcessoMenuVO.apresentarNfseRecibo}"
                                     oncomplete="#{ReciboControle.onComplete}">
                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                        <f:attribute name="maisDetalhes" value="false"/>
                    </a4j:commandLink>
                    &nbsp;
                    <a4j:commandLink id="gerarNFCe2"
                                     value="NFC-e"
                                     reRender="mdlMensagemGenerica"
                                     style="text-transform: none;"
                                     styleClass="linkAzul icon"
                                     rendered="#{ClienteControle.clienteVO.empresa.usarNFCe && LoginControle.permissaoAcessoMenuVO.gestaoNFCe}"
                                     actionListener="#{ReciboControle.prepareRecibo}"
                                     action="#{ReciboControle.confirmarEmitirNFCe}"
                                     oncomplete="#{ReciboControle.modalMensagemGenerica}">
                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                    </a4j:commandLink>

                </h:column>

                <rich:column style="text-align: center"
                             rendered="#{TelaClienteControle.apresentarStatusConciliadora}">
                    <f:facet name="header">
                        <h:outputText value="STATUS"
                                      title="STATUS DA CONCILIADORA"
                                      styleClass="tooltipster"
                                      style="width: 100%; text-align: center; display: block"/>
                    </f:facet>

                    <h:panelGroup layout="block" style="text-align: center;" rendered="#{!historicoPagamentos.credito}">
                        <a4j:commandLink
                                action="#{TelaClienteControle.obterStatusConciliadora}"
                                reRender="panelStatusConciliadora"
                                styleClass="tooltipster"
                                title="#{historicoPagamentos.statusConciliadora.descricao}"
                                oncomplete="#{TelaClienteControle.msgAlert}"
                                style="display: inline; font-size: 14px; float: right">

                            <h:outputText
                                    styleClass="#{historicoPagamentos.statusConciliadora.styleClass}"
                                    style="#{historicoPagamentos.statusConciliadora.style}"/>
                        </a4j:commandLink>
                    </h:panelGroup>

                </rich:column>
            </h:dataTable>
            <h:panelGrid columns="1" rendered="#{TelaClienteControle.listaPagamentos.count > 0}" width="100%" columnClasses="colunaCentralizada">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="center" valign="middle">
                            <h:panelGroup layout="block"
                                          styleClass="paginador-container">
                                <h:panelGroup styleClass="pull-left"
                                              layout="block">
                                    <h:outputText
                                            styleClass="texto-size-14 cinza"
                                            value="Total #{TelaClienteControle.listaPagamentos.count} itens"></h:outputText>
                                </h:panelGroup>
                                <h:panelGroup layout="block"
                                              style="align-items: center">
                                    <a4j:commandLink  styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="containerListaPagamento"
                                                      actionListener="#{TelaClienteControle.primeiraPagina}">
                                        <i class="fa-icon-double-angle-left"></i>
                                        <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaPagamento"
                                                     actionListener="#{TelaClienteControle.paginaAnterior}">
                                        <i class="fa-icon-angle-left"></i>
                                        <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                    </a4j:commandLink>

                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                  value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaPagamentos.paginaAtualApresentar}" rendered="true"/>
                                    <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="containerListaPagamento"
                                                     actionListener="#{TelaClienteControle.proximaPagina}">
                                        <i class="fa-icon-angle-right"></i>
                                        <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaPagamento"
                                                     actionListener="#{TelaClienteControle.ultimaPagina}">
                                        <i class="fa-icon-double-angle-right"></i>
                                        <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                    </a4j:commandLink>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                    <h:panelGroup styleClass="pull-right" layout="block">
                                        <h:outputText
                                                styleClass="texto-size-14 cinza "
                                                value="Itens por página "></h:outputText>
                                    </h:panelGroup>
                                    <h:panelGroup styleClass="cb-container pl20" layout="block">
                                        <h:selectOneMenu value="#{TelaClienteControle.listaPagamentos.limit}">
                                            <f:selectItem itemValue="#{6}"></f:selectItem>
                                            <f:selectItem itemValue="#{10}"></f:selectItem>
                                            <f:selectItem itemValue="#{20}"></f:selectItem>
                                            <f:selectItem itemValue="#{50}"></f:selectItem>
                                            <f:selectItem itemValue="#{100}"></f:selectItem>
                                            <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="containerListaPagamento">
                                                <f:attribute name="tipo" value="LISTA_PAGAMENTOS" />
                                            </a4j:support>
                                        </h:selectOneMenu>
                                    </h:panelGroup>

                                </h:panelGroup>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </h:panelGrid>
        </h:panelGroup>


        <h:outputText value="Histórico de cheques"
                      style="margin-top: 20px; display: block;"
                      styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
        <h:outputText value="Não foram recebidos cheques neste contrato."
                      rendered="#{empty TelaClienteControle.cheques}"
                      style="margin-bottom: 20px; margin-top: 20px; display: block;"
                      styleClass="texto-size-14 cinza pl20"/>

        <h:dataTable styleClass="tabelaDados"
                     id="listaHistoricoCheques"
                     rendered="#{not empty TelaClienteControle.cheques}"
                     value="#{TelaClienteControle.cheques}"
                     var="historicoCheques">
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_codigo}"/>
                </f:facet>
                <h:outputText value="#{historicoCheques.codigo}"/>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_banco}"/>
                </f:facet>
                <h:outputText value="#{historicoCheques.banco.nome}"/>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_agencia}"/>
                </f:facet>
                <h:outputText value="#{historicoCheques.agencia}"/>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_conta}"/>
                </f:facet>
                <h:outputText value="#{historicoCheques.conta}"/>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_numeroCheque}"/>
                </f:facet>
                <h:outputText value="#{historicoCheques.numero}"/>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_nomeNoCheque}"/>
                </f:facet>
                <h:outputText value="#{historicoCheques.nomeNoCheque}"/>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_data}"/>
                </f:facet>
                <a4j:outputPanel layout="none"
                                 title="Data de compensação original: #{historicoCheques.dataOriginalApresentar}">
                    <h:outputText value="#{historicoCheques.dataCompensacao}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </a4j:outputPanel>

            </h:column>

            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_situacao}"/>
                </f:facet>
                <h:outputText value="#{historicoCheques.situacao_Apresentar}">
                </h:outputText>
            </h:column>

            <h:column>
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cheque_valor}"/>
                </f:facet>
                <h:outputText style="float: right; margin-right: 10px;"
                              value="#{historicoCheques.valor}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </h:column>
        </h:dataTable>
    </c:if>
    <script>carregarTooltipster();</script>
</h:panelGroup>

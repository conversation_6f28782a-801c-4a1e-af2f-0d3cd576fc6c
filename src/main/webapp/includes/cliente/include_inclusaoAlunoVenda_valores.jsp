<%@include file="../../include_imports.jsp" %>

<h:panelGroup id="valores" styleClass="col-md-12" style="margin-top: 20px">

    <div class="col-md-3 title-space mtop20">
        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Matr&iacute;cula (inicial)</span>
       <h:outputText styleClass="col-md-12 texto-size-18 texto-cor-cinza-2 mtop20 texto-font texto-bold"
                     value="#{MovPagamentoControle.empresaLogado.moeda} #{InclusaoVendaRapidaControle.valorMatriculaApresentar}"/>
    </div>

    <div class="col-md-3 title-space mtop20">
        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Manuten&ccedil;&atilde;o (anual)</span>
        <h:outputText styleClass="col-md-12 texto-size-18 texto-cor-cinza-2 mtop20 texto-font texto-bold"
                      value="#{InclusaoVendaRapidaControle.valorManutencaoApresentar}"/>

    </div>

    <div class="col-md-3 title-space mtop20">
        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Mensalidade</span>
            <h:outputText id="idValorMensalidade" styleClass="col-md-12 texto-size-18 texto-cor-cinza-2 mtop20 texto-font texto-bold"
           value="#{InclusaoVendaRapidaControle.valorMensalApresentar}" />
    </div>

    <h:panelGroup layout="block" styleClass="col-md-12 title-space mtop20"
        rendered="#{InclusaoVendaRapidaControle.parcelarAdesao}">
        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Parcelamento da matr&iacute;cula</span>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="col-md-3 cb-container margenVertical"
        rendered="#{InclusaoVendaRapidaControle.parcelarAdesao}">
        <h:selectOneMenu value="#{InclusaoVendaRapidaControle.nrVezesParcelarAdesao}">
            <f:selectItems value="#{InclusaoVendaRapidaControle.parcelasAdesao}" />
        </h:selectOneMenu>
    </h:panelGroup>


    <h:panelGroup layout="block" styleClass="col-md-12 title-space mtop20">
        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Dia de cobran&ccedil;a das parcelas:</span>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="col-md-2 cb-container margenVertical">
        <h:selectOneMenu id="diaCobranca" value="#{InclusaoVendaRapidaControle.diaVencimento}">
            <f:selectItems value="#{InclusaoVendaRapidaControle.listaDiaVencimento}" />
        </h:selectOneMenu>
    </h:panelGroup>


    <h:panelGroup layout="block" styleClass="col-md-12 title-space mtop20"
        rendered="#{InclusaoVendaRapidaControle.existeCampanhaCupomDescontoVigente}">
        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Cupom de desconto:</span>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="col-md-6 margenVertical" id="panelCupomDesconto"
        rendered="#{!InclusaoVendaRapidaControle.plano.planoPersonal}">
        <a4j:commandLink rendered="#{InclusaoVendaRapidaControle.existeCampanhaCupomDescontoVigente}"
            id="botaoAdicionarCupomDesconto" reRender="panelCupomDesconto,modalCupomDesconto"
            action="#{InclusaoVendaRapidaControle.abrirModalCupomDesconto}"
            oncomplete="Richfaces.showModalPanel('modalCupomDesconto');document.getElementById('formCupomDesc:nrCupom').focus()"
            styleClass="linkPadrao texto-cor-azul texto-size-16">
            <i class="fa-icon-tags"></i> Adicionar Cupom
        </a4j:commandLink>
        <span style="color: #6666"
            class="texto-size-14 texto-cor-cinza texto-font texto-bold">${InclusaoVendaRapidaControle.numeroCupomAplicado}</span>
        <a4j:commandLink rendered="#{not empty InclusaoVendaRapidaControle.numeroCupomAplicado}"
            id="botaoRetirarCupomDesconto" reRender="panelCupomDesconto,modalCupomDesconto,form:idValorMensalidade"
            action="#{InclusaoVendaRapidaControle.retirarCupomDesconto}"
            oncomplete="#{InclusaoVendaRapidaControle.mensagemNotificar}" style="margin-left: 10px"
            styleClass="linkPadrao texto-cor-vermelho texto-size-16">
            <i class="fa-icon-tags"></i> Remover Cupom
        </a4j:commandLink>
    </h:panelGroup>


</h:panelGroup>

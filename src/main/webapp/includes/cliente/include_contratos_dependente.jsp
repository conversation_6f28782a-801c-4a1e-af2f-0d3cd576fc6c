<%@include file="../imports.jsp" %>


<h:panelGroup id="lstContratoDependentes" style="width: 96%; height: auto;margin: 0 0 0 1.5%;min-width: 700px;"
              styleClass="painelDadosAluno contratos blocoContratos blocosPrincipais visivel tudo step5 googleAnalytics"
              rendered="#{not empty TelaClienteControle.listaContratosDependentes}">

    <h:panelGroup layout="block" rendered="#{TelaClienteControle.contratoSelecionado == null}" styleClass="tituloPainelAluno">
        <h:outputText value="Contratos Compartilhados" styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
        <h:outputLink styleClass="pl5" value="#{SuperControle.urlBaseConhecimento}como-trabalhar-com-plano-com-dependentes/" title="Clique e saiba mais: Contratos" target="_blank">
            <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
        </h:outputLink>

    </h:panelGroup>

    <jsp:include page="include_lista_contratos_dependente.jsp"/>

    <div class="rodapePainelAluno" style="text-align: right;">
        <c:if test="${fn:length(TelaClienteControle.listaContratosDependentes) eq 3}">
            <a4j:commandLink reRender="lstContratoDependentes"
                             id="listarTodosContratosDependentes" styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             action="#{TelaClienteControle.verMaisContratos}">
                <h:outputText value="Ver mais"/>
                <i class="fa-icon-angle-right"></i>
            </a4j:commandLink>
        </c:if>

        <c:if test="${fn:length(TelaClienteControle.listaContratosDependentes) gt 3}">
            <a4j:commandLink reRender="lstContratoDependentes"
                             styleClass="linkAzul texto-size-14"
                             style="margin-right: 20px;"
                             action="#{TelaClienteControle.verMenosContratos}">
                <h:outputText value="Ver menos"/>
                <i class="fa-icon-angle-left"></i>
            </a4j:commandLink>
        </c:if>
    </div>
</h:panelGroup>

<%@include file="../imports.jsp" %>
<h:panelGrid width="100%" cellspacing="5" rendered="#{!empty ClienteControle.clienteVO.listaMensagemAvisoCliente}"
             styleClass="colunaCentralizada">
    <h:panelGroup>
        <table width="100%" border="0" align="left" cellpadding="0"
               cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
            <tr>
                <td align="left" valign="top" style="padding-bottom: 5px;">
                    <div style="clear: both;" class="text">
                        <p style="margin-bottom: 6px;"><img
                                src="images/arrow2.gif" width="16" height="16"
                                style="vertical-align: middle; margin-right: 6px;">
                            <h:outputText id="ttAvisos" style="font-weight: bold"
                                          value="Mensagens / Avisos" /></p>
                        <div class="sep"><img src="images/shim.gif"></div>
                    </div>
                </td>
            </tr>
            <tr>
                <td align="left" valign="top">
                    <h:dataTable
                        id="avisosCliente" width="100%" columnClasses="textsmall"
                        value="#{ClienteControle.clienteVO.listaMensagemAvisoCliente}"
                        styleClass="tabFormSubordinada" var="avisoCliente">
                        <h:column id="teste1">
                            <%-- <img style="vertical-align: middle; margin-right: 4px;"
                                  src="images/arrow.gif">--%>
                            <img style="vertical-align: middle; margin-right: 4px;" src="imagens/ico_ani_telefone.gif">
                            <%--<rich:spacer width="10px" />--%>
                            <h:panelGroup id="teste2"
                                          rendered="#{avisoCliente.navegacaoFrame}">
                                <a4j:commandLink id="linkMsgCliente" styleClass="link_red"
                                                 action="#{ClienteControle.abreTela}"
                                                 actionListener="#{ClienteControle.selecionarClienteMensagemListener}">
                                    <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                    <h:outputText id="textoMsgCliente" value="#{avisoCliente.mensagem}" />
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup
                                rendered="#{avisoCliente.navegacaoPopUp}">
                                <a4j:commandLink id="linkMsgCliente1" styleClass="link_red"
                                                 action="#{ClienteControle.abreTela}"
                                                 actionListener="#{ClienteControle.selecionarClienteMensagemListener}"
                                                 oncomplete="abrirPopup('#{avisoCliente.tipomensagem.navegacao}', '#{avisoCliente.tipomensagem}', 780, 595);">
                                    <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                    <h:outputText id="textoMsgCliente1"  value="#{avisoCliente.mensagem}" />
                                </a4j:commandLink>
                            </h:panelGroup>

                        </h:column>
                    </h:dataTable>
                </td>
            </tr>
        </table>
    </h:panelGroup>
</h:panelGrid>
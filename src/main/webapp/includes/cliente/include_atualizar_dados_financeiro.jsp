<%@include file="../imports.jsp" %>
<head>
    <script language="javascript" src="${root}/script/required.js" type="text/javascript"></script>
    <link href="${root}/css/required.css" rel="stylesheet" type="text/css"/>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<style>
    .btModalAtualizarFinanceiro {
        font-size: 14px !important;
        background: #094771 !important;
        color: #ffffff !important;
        padding: 10px !important;
        border-radius: 4px;
        margin: 25px 25px 0 0px !important;
        width: 100px;
        /*float: right;*/
    }

    .btModalAtualizarFinanceiro:hover {
        background: #2B4671 !important;
        color: #ffffff !important;
    }

    .placeholderInput::placeholder {
        color: #8F8F8F !important;
        font-size: 15px;
    }

    .listaSelectOne {
        margin-top: 10px !important;
        color: #0a6aa1;
        border-radius: 2px;
        width: 250px;
    }
</style>
<rich:modalPanel id="modalAtualizarFinanceiros" onshow="addPlaceModalHoderAtualizarFinan()"
                 showWhenRendered="#{AtualizarDadosFinanceiroControle.mostrarModalAtualizaDados}" moveable="true" resizeable="false"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="656"  height="580" style=" max-height: 580px; overflow: hidden auto;">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Atualiza��o Cadastral Financeira"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup rendered="#{!AtualizarDadosFinanceiroControle.preenchimentoObrigatorio}">
            <a4j:commandLink
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    style="color: #fff"
                    status="false"
                    action="#{AtualizarDadosFinanceiroControle.naoApresentarModalAtualizarFinanceiros}"
                    id="hideModalVerificarAtualizarFinanceiro">
                <rich:componentControl for="modalAtualizarFinanceiros" operation="hide" event="onclick"/>
            </a4j:commandLink>

        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" style="padding: 10px 10px 10px 17px;">
        <span style="font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 15px; line-height: 1.5; !important;">
            Ol� <b>${AtualizarDadosFinanceiroControle.pessoaVO.nome} ${AtualizarDadosFinanceiroControle.pessoaVO.sobreNome},</b>
            antes de continuar, por favor, atualize os dados financeiro.
        </span></br>
        <hr style="border: solid 1px #DDD;"/>
    </h:panelGroup>
    <a4j:form id="formAtualizarFinanceiroModal" styleClass="pure-form">
        <h:panelGroup id="panelAtualizarFinanceiroModal" layout="block" style="padding: 10px 0px 10px 10px;">

            <h:panelGrid columns="1">
                <h:panelGrid columns="1">
                    <h:outputText value="*Dono da academia" style="color: #7A7A7A; font-size: 16px;"/>
                    <h:inputText value="#{AtualizarDadosFinanceiroControle.atualizacaoFinanceiroDTO.nomeDono}" id="mdlfDono"
                                 maxlength="255" styleClass="placeholderInput required nomeDonoModalCadastro"
                                 style="width: 286px; height: 37px; border-radius: 2px; margin: 5px 25px 0 0; font-size: 14px!important;color:#0a6aa1!important; text-transform: uppercase;" />
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid columns="2">
                <h:panelGrid columns="1" style="margin-top: 20px">
                    <h:outputText value="*Respons�vel geral" style="color: #7A7A7A; font-size: 16px;"/>
                    <h:inputText value="#{AtualizarDadosFinanceiroControle.atualizacaoFinanceiroDTO.responsavelGeral}" id="mdlfResponsavelGeral"
                                 maxlength="255" styleClass="placeholderInput required nomeResponsavelModalCadastro"
                                 style="width: 286px; height: 37px; border-radius: 2px; margin: 5px 25px 0 0; font-size: 14px!important;color:#0a6aa1!important; text-transform: uppercase;" />
                </h:panelGrid>

                <h:panelGrid columns="1" style="margin-top: 20px">
                    <h:outputText value="*Celular/WhatsApp" style="color: #7A7A7A; font-size: 16px;"/>
                    <h:inputText value="#{AtualizarDadosFinanceiroControle.atualizacaoFinanceiroDTO.responsavelTelefone}" id="mdlfFone"
                                 styleClass="placeholderInput required telefoneModalCadastro"
                                 style="width: 286px; height: 37px; border-radius: 2px; margin: 5px 25px 0 0; font-size: 14px!important;color:#0a6aa1!important; text-transform: uppercase;"
                                 onkeyup="mascaraFinan(this, mtelFinan);" maxlength="15"/>
                </h:panelGrid>

                <h:panelGrid columns="1">
                    <h:outputText value="*E-mail" style="color: #7A7A7A; font-size: 16px;"/>
                    <h:inputText value="#{AtualizarDadosFinanceiroControle.atualizacaoFinanceiroDTO.responsavelEmail}" id="mdlfEmail"
                                 styleClass="placeholderInput required emailModalCadastro"
                                 style="width: 286px; height: 37px; border-radius: 2px; margin: 5px 0 0 0; font-size: 14px!important;color:#0a6aa1!important; text-transform: uppercase;"
                                 onkeydown="validarModalEmailFinan(this)" maxlength="60" size='65'/>
                </h:panelGrid>

                <h:panelGrid columns="1">
                    <h:outputLabel value="Nicho: " style="color: #7A7A7A; font-size: 16px;"/>
                    <h:selectOneMenu id="mdlfCargo" value="#{AtualizarDadosFinanceiroControle.nichoEnum}"
                                     style="width: 286px;"
                                     styleClass="listaSelectOne nichoModalCadastro">
                        <f:selectItems value="#{AtualizarDadosFinanceiroControle.listaNichos}"/>
                    </h:selectOneMenu>
                </h:panelGrid>

                <h:panelGrid columns="1">
                    <h:outputText value="*Metragem" style="color: #7A7A7A; font-size: 16px;"/>
                    <h:inputText value="#{AtualizarDadosFinanceiroControle.atualizacaoFinanceiroDTO.metragem}" id="mdlfMetragem"
                                 styleClass="placeholderInput required metragemModalCadastro"
                                 onkeypress="return mascara(this.form, this.id , '9999', event);"
                                 style="width: 286px; height: 37px; border-radius: 2px; margin: 5px 25px 0 0; font-size: 14px!important;color:#0a6aa1!important; text-transform: uppercase;"/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGroup layout="block" style="text-align: center; padding: 30px 0px 20px 0px">
                <a4j:commandLink id="btnSalvarModalAtualizarFinanceiro" title="Atualizar" value="Atualizar"
                                 action="#{AtualizarDadosFinanceiroControle.salvarAtualizacaoCadastral}"
                                 oncomplete="#{AtualizarDadosFinanceiroControle.onComplete};esconderTelaAtualizacaoCadastralFinanceira(#{!AtualizarDadosFinanceiroControle.mostrarModalAtualizaDados});"
                                 styleClass="btModalAtualizarFinanceiro" reRender="panelAtualizarFinanceiroModal,modalAtualizarFinanceiros"
                                 style="padding-right: 24px !important; padding-left: 24px !important;"/>

            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<script type="text/javascript" language="JavaScript">
    function addPlaceModalHoderAtualizarFinan() {
        if (document.getElementById("formAtualizarFinanceiroModal:mdlfDono") != null) {
            document.getElementById("formAtualizarFinanceiroModal:mdlfDono").setAttribute("placeholder", "Digite o nome do dono da unidade");
        }
        if (document.getElementById("formAtualizarFinanceiroModal:mdlfResponsavelGeral") != null) {
            document.getElementById("formAtualizarFinanceiroModal:mdlfResponsavelGeral").setAttribute("placeholder", "Digite o nome do respons�vel geral da unidade");
        }
        if (document.getElementById("formAtualizarFinanceiroModal:mdlfFone") != null) {
            document.getElementById("formAtualizarFinanceiroModal:mdlfFone").setAttribute("placeholder", "Digite o n�mero do responsavel com DDD");
        }
        if (document.getElementById("formAtualizarFinanceiroModal:mdlfEmail") != null) {
            document.getElementById("formAtualizarFinanceiroModal:mdlfEmail").setAttribute("placeholder", "Digite o email do responsavel");
        }
        if (document.getElementById("formAtualizarFinanceiroModal:mdlfMetragem") != null) {
            document.getElementById("formAtualizarFinanceiroModal:mdlfMetragem").setAttribute("placeholder", "Digite a metragem");
        }
    }

    function validarModalEmailFinan(field) {
        usuario = field.value.substring(0, field.value.indexOf("@"));
        dominio = field.value.substring(field.value.indexOf("@") + 1, field.value.length);

        if ((usuario.length >= 1) &&
            (dominio.length >= 3) &&
            (usuario.search("@") == -1) &&
            (dominio.search("@") == -1) &&
            (usuario.search(" ") == -1) &&
            (dominio.search(" ") == -1) &&
            (dominio.search(".") != -1) &&
            (dominio.indexOf(".") >= 1) &&
            (dominio.lastIndexOf(".") < dominio.length - 1)) {
        }
    }

    function mascaraFinan(o, f) {
        v_obj = o;
        v_fun = f;
        setTimeout("execmascaraFinan()", 1)
    }

    function execmascaraFinan() {
        v_obj.value = v_fun(v_obj.value)
    }

    function mtelFinan(v) {
        v = v.replace(/\D/g, ""); //Remove tudo o que n�o � d�gito
        v = v.replace(/^(\d{2})(\d)/g, "($1) $2"); //Coloca par�nteses em volta dos dois primeiros d�gitos
        return v;
    }

    function esconderTelaAtualizacaoCadastralFinanceira(isEsconder){
        if(isEsconder) {
            Richfaces.hideModalPanel('modalAtualizarFinanceiros');
        }
    }
</script>




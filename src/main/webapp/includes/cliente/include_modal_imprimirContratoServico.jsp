<%-- 
    Document   : include_modal_imprimirContratoServico
    Created on : 29/10/2013, 13:29:08
    Author     : marcosand<PERSON>
--%>

<%@include file="../imports.jsp" %>

<rich:modalPanel id="panelContratoPrestacaoServico" domElementAttachment="parent"
                 rendered="#{ClienteControle.apresentarPanelContratoPrestacaoServico}"
                 autosized="true" shadowOpacity="false" width="500"  height="100">

    <f:facet name="header">
        <h:outputText value="Imprimir Contrato  de Presta��o de Servi�os" styleClass="titulo2"/>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <a4j:form>
                <a4j:commandButton image="/imagens/close.png" style="cursor:pointer;"
                                   action="#{ClienteControle.fecharPanelContratoPrestacaoServico}"
                                   oncomplete="#{rich:component('panelContratoPrestacaoServico')}.hide();"
                                   id="hidelink12"/>
            </a4j:form>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formContratoPrestacaoServico"  ajaxSubmit="false">
        <h:outputText rendered="#{!empty ClienteControle.listaSelectModelosContratoPrestacao}" value="Escolha o modelo de contrato: " style="font-weight:bold;"/>
        <h:selectOneMenu rendered="#{!empty ClienteControle.listaSelectModelosContratoPrestacao}"  id="modeloContratoPrestacao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.textoContratoPrestacao.codigo}" >
            <f:selectItems  value="#{ClienteControle.listaSelectModelosContratoPrestacao}" />
        </h:selectOneMenu>
        <rich:spacer width="5px"/>
        <a4j:commandButton style="vertical-align:middle" id="btnImprimirContratoPrestacao" rendered="#{!empty ClienteControle.listaSelectModelosContratoPrestacao}"
                                           value="Imprimir Contrato" image="./imagens/imprimir.png"
                                           action="#{ClienteControle.imprimirContratoServico}" reRender="form:panelMensagemSuperior,form:panelMensagemInferior"
                                           oncomplete="abrirPopup('VisualizarContrato', 'RelatorioContrato', 730, 545);#{rich:component('panelContratoPrestacaoServico')}.hide();" />
        <h:panelGrid rendered="#{empty ClienteControle.listaSelectModelosContratoPrestacao}">
            <br>
             <h:outputText value="N�o existe Modelo de Contrato de Presta��o de Servi�o ativo. Cadastre um novo modelo ou Ative um existente para realizar essa impress�o" style="color:red;"/>

        </h:panelGrid>

    </a4j:form>
</rich:modalPanel>


<%@page pageEncoding="ISO-8859-1"%>
<%@include file="../../include_imports.jsp" %>


<div style="margin: 1.5vw; background-color: white;padding: 20px; position: relative; width: calc(100% - 6vw); display: inline-block; font-size: 14px; border-radius: 10px"
     class="container-box zw_ui col-md-12">

    <div class="tituloPainelPesquisa notop col-md-12" style="font-size: 16px">
        <c:if test="${InclusaoVendaRapidaControle.telaBoletos}">
            <h:outputText value="Boletos Gerados"/>
        </c:if>
        <c:if test="${!InclusaoVendaRapidaControle.telaBoletos}">
            <h:outputText value="Venda rápida de plano recorrente"
                          rendered="#{not InclusaoVendaRapidaControle.cadastrandoParaConvite}"/>
            <h:outputText value="Adicionando convidado de #{InclusaoVendaRapidaControle.indicadoPor.pessoa.nome}"
                          rendered="#{InclusaoVendaRapidaControle.cadastrandoParaConvite}"/>
            <h:outputLink styleClass="linkWiki cinza" value="#{SuperControle.urlBaseConhecimento}como-funciona-o-venda-rapida/"
                          title="Clique e saiba mais: Venda rápida" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </c:if>
    </div>

    <div style="text-align: left">
        <c:if test="${InclusaoVendaRapidaControle.telaBoletos}">
            <jsp:include page="include_inclusaoAlunoVenda_boletos.jsp" flush="true"/>
        </c:if>

        <c:if test="${!InclusaoVendaRapidaControle.telaBoletos}">
            <h:panelGroup rendered="#{not InclusaoVendaRapidaControle.cadastrandoParaConvite}">
                    <div class="col-md-6">
                        <div class="title-space mtop20">
                            <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Unidade</span>
                        </div>
                        <h:panelGroup layout="block" styleClass="col-md-11 cb-container margenVertical">
                            <h:selectOneMenu value="#{InclusaoVendaRapidaControle.empresa.codigo}">
                                <f:selectItems value="#{InclusaoVendaRapidaControle.listaEmpresas}"/>
                                <a4j:support event="onchange"
                                             reRender="comboplano, form:formaspagamento"
                                             action="#{InclusaoVendaRapidaControle.alterarEmpresa}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>

                    <div class="col-md-6">
                        <div class="title-space mtop20">
                            <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Plano</span>
                        </div>
                        <h:panelGroup layout="block" id="comboplano" styleClass="col-md-11 cb-container margenVertical">
                            <h:selectOneMenu value="#{InclusaoVendaRapidaControle.planoSelecionado}"
                                             onfocus="ativarVariaveisVendaRapida()"
                                             styleClass="required planoid" id="planoid">
                                <f:selectItems value="#{InclusaoVendaRapidaControle.planos}"/>
                                <a4j:support event="onchange" ajaxSingle="true"
                                             reRender="valores,pnlDependente,comboplano,formaspagamento,dadosCliente"
                                             action="#{InclusaoVendaRapidaControle.montarPlanoSelecionado}"/>
                            </h:selectOneMenu>

                            <h:panelGroup layout="block" style="padding-top: 10px">
                                <h:outputText styleClass="texto-size-16 texto-cor-vermelho"
                                              rendered="#{not empty InclusaoVendaRapidaControle.planoVOSelecionado.inicioMinimoContratoFomatado_VendaRapida}"
                                              value="#{InclusaoVendaRapidaControle.planoVOSelecionado.inicioMinimoContratoFomatado_VendaRapida}"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" style="padding-top: 10px"
                                          rendered="#{not empty InclusaoVendaRapidaControle.situacaoContratoBase}">
                                <h:outputText styleClass="texto-size-16 texto-cor-vermelho"
                                              value="#{InclusaoVendaRapidaControle.mensagemRenovacaoRematricula}"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </div>
                    <div class="col-md-12 mtop20">
                        <hr class="dividerFundoEscuro">
                    </div>
            </h:panelGroup>

            <jsp:include page="include_inclusaoAlunoVenda_cliente.jsp" flush="true"/>

            <h:panelGroup rendered="#{not InclusaoVendaRapidaControle.cadastrandoParaConvite}">
                <jsp:include page="include_inclusaoAlunoVenda_valores.jsp" flush="true"/>

                <div class="col-md-12 mtop20">
                    <hr class="dividerFundoEscuro">
                </div>

                <jsp:include page="include_inclusaoAlunoVenda_autorizacao.jsp" flush="true"/>


                <div class="col-md-12 mtop20">
                    <hr class="dividerFundoEscuro">
                </div>

                <div class="col-md-12 mtop20">
                    <h:panelGroup id="pnlBotoes" layout="block">
                        <a4j:commandButton id="salvar"
                                           onclick="limparMsgCarregando();ativarPoll()"
                                           action="#{InclusaoVendaRapidaControle.validarContratoRenovando}"
                                           value="#{InclusaoVendaRapidaControle.labelBtnSalvar}"
                                           alt="#{InclusaoVendaRapidaControle.labelBtnSalvar}"
                                           styleClass="botoes nvoBt"
                                           style="margin: 20px 0 0;"
                                           oncomplete="limparMsgCarregando();desativarPoll();#{InclusaoVendaRapidaControle.msgAlert}"
                                           reRender="fmRenovacaoContrato"/>
                    </h:panelGroup>
                </div>
            </h:panelGroup>

            <h:panelGroup rendered="#{InclusaoVendaRapidaControle.cadastrandoParaConvite}">

                <div class="col-md-12 mtop20">
                    <a4j:commandButton id="salvarConvidado"
                                       value="Lançar convidado" alt="Lançar convidado" styleClass="botoes nvoBt"
                                       style="margin: 0; margin-top: 20px;"
                                       action="#{InclusaoVendaRapidaControle.gravarConvidado}"
                                       oncomplete="#{InclusaoVendaRapidaControle.msgAlert}"/>
                </div>
            </h:panelGroup>
        </c:if>
    </div>
</div>

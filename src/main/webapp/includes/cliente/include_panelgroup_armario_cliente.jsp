<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 23/10/2015
  Time: 08:30
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../imports.jsp" %>

<h:panelGroup id="panelArmarioCliente" layout="block"  rendered="#{not empty ClienteControle.clienteVO.armariosAlugados}">
  <table width="100%" border="0" align="left" cellpadding="0"
         cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
    <tr>
      <td align="left" valign="top" style="padding-bottom: 5px;">
        <div style="clear: both;" class="text">
          <p style="margin-bottom: 6px;"><img
                  src="images/arrow2.gif" width="16" height="16"
                  style="vertical-align: middle; margin-right: 6px;">
            <h:outputText style="font-weight: bold"
                          value="Arm�rios Alugados" /></p>
          <div class="sep" style="margin: 4px 0 5px 0;"><img
                  src="images/shim.gif"></div>
        </div>
      </td>
    </tr>
    <tr>
      <td align="left" valign="top">
        <rich:dataTable id="listaArmarioAlugado" width="100%" border="0"
                        rows="#{ClienteControle.nrPaginaArmarioAlugado}"
                        cellspacing="0" cellpadding="10" styleClass="textsmall"
                        columnClasses="centralizado"
                        value="#{ClienteControle.clienteVO.armariosAlugados}"
                        var="aluguel">
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold" value="Arm�rio" />
            </f:facet>
            <h:outputText style="font-weight: bold"
                          styleClass="blue"
                          value="#{aluguel.armario.descricao}" />
          </rich:column>
          <rich:column>
                <f:facet name="header">
                    <h:outputText style="font-weight: bold"
                                  value="Lan�amento Loca��o" />
                </f:facet>
                <h:outputText style="font-weight: bold"
                              styleClass="blue"
                              value="#{aluguel.dataCadastro_Apresentar}" />
          </rich:column>
          <rich:column>
                <f:facet name="header">
                    <h:outputText style="font-weight: bold"
                                  value="In�cio Loca��o" />
                </f:facet>
                <h:outputText style="font-weight: bold"
                              styleClass="blue"
                              value="#{aluguel.dataInicioAluguelApresentar}" />
          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold"
                            value="Vencimento Loca��o" />
            </f:facet>
            <h:outputText style="font-weight: bold"
                          styleClass="blue"
                          value="#{aluguel.dataFimOriginalApresentar}" />
          </rich:column>
          <rich:column  rendered="#{ArmarioControleRel.habilitadoGestaoArmarios}">
            <f:facet name="header">
              <h:outputText style="font-weight: bold"
                            value="Contrato Assinado" ></h:outputText>
            </f:facet>
              <a4j:commandLink  styleClass="botaoAssinarCotrato" action="#{ClienteControle.selecionarArmarioContratoAssinado}"  oncomplete="#{rich:component('modalPanelContratoArmario')}.show();" reRender="modalPanelContratoArmario">
                <h:outputText style="font-weight: bold" styleClass="contratoarmario #{aluguel.contratoAssinadoApresentar}" />
              </a4j:commandLink>

            <rich:spacer width="10px"/>
          </rich:column>
        </rich:dataTable>
          <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
              <h:panelGroup>
                  <h:panelGrid columns="2">
                      <rich:datascroller align="center"
                                         for="listaArmarioAlugado" maxPages="100"
                                         id="scResultadoListaArmarioAlugado" />
                      <rich:inputNumberSpinner inputSize="4"
                                               styleClass="form" enableManualInput="true"
                                               minValue="1" maxValue="100"
                                               value="#{ClienteControle.nrPaginaArmarioAlugado}">
                          <a4j:support event="onchange"
                                       focus="scResultadoListaArmarioAlugado"
                                       reRender="listaArmarioAlugado,scResultadoListaArmarioAlugado" />
                      </rich:inputNumberSpinner>
                  </h:panelGrid>
              </h:panelGroup>
          </h:panelGrid>
      </td>
    </tr>
  </table>
</h:panelGroup>
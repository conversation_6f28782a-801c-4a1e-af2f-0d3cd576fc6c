<%@include file="../imports.jsp" %>
<head>
    <script language="javascript" src="${root}/script/required.js" type="text/javascript"></script>
    <link href="${root}/css/required.css" rel="stylesheet" type="text/css"/>
</head>

<style>
    .btModalAtualizarUsers {
        font-size: 14px !important;
        background: #094771 !important;
        color: #ffffff !important;
        padding: 10px !important;
        border-radius: 4px;
        margin: 25px 25px 0 0px !important;
        width: 100px;
        /*float: right;*/
    }

    .btModalAtualizarUsers:hover {
        background: #2B4671 !important;
        color: #ffffff !important;
    }

    .btModalVoltalModalAtualizarUsers {
        font-size: 14px !important;
        /*background: #094771 !important;*/
        /*color: #ffffff !important;*/
        color: #555 !important;
        background-color: #d5d2d2 !important;
        padding: 10px !important;
        border-radius: 4px;
        margin: 25px 25px 0 15px !important;
        width: 100px;
        /*float: left;*/
    }

    .btModalVoltalModalAtualizarUsers:hover {
        background: #2B4671 !important;
        color: #ffffff !important;
    }

    .placeholderInput::placeholder {
        color: #8F8F8F !important;
        font-size: 15px;
    }

    .listaSelectOne {
        margin-top: 10px !important;
        color: #0a6aa1;
        border-radius: 2px;
        width: 250px;
    }

    .listaSelectOneAdmin {
        margin-top: 7px !important;
        color: #0a6aa1;
        border-radius: 2px;
        width: 180px;
    }

    .fontTermos {
        font-size: 16px;
        line-height: 1.5;
        font-weight: bold;
    }

    .fontTermosAdmin {
        font-size: 13px;
        line-height: 1.3;
        font-weight: bold;
    }

    .fontAutorizar {
        font-size: 15px;
        line-height: 1.5;
    }

    .fontAutorizarAdmin {
        font-size: 13px;
        line-height: 1.3;
    }

    .outputs {
        color: #7A7A7A; font-weight: bold; font-size: 16px;
    }
</style>
<rich:modalPanel id="modalAtualizarUsuarios" onshow="addPlaceModalHoderAtualizarUser()"
                 showWhenRendered="#{AtualizarDadosControle.mostrarModalAtualizaDados}" moveable="true" resizeable="false"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="656"
                 style="max-height: 640px; overflow: hidden auto;">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Atualiza��o Cadastral"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" style="padding: 10px 10px 10px 17px;">
        <span style="font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 15px; line-height: 1.5; !important;">
            Ol� <b>${AtualizarDadosControle.pessoaVO.nome} ${AtualizarDadosControle.pessoaVO.sobreNome},</b>
            antes de continuar, por favor, ${AtualizarDadosControle.mensagemTitulo}.
        </span></br>
        <hr style="border: solid 1px #DDD;"/>
    </h:panelGroup>
    <a4j:form id="formAtualizarUsuarioModal" styleClass="pure-form">
        <h:panelGroup id="panelAtualizarUsuarioModal" layout="block" style="padding: 10px 0px 10px 10px;"
                      rendered="#{AtualizarDadosControle.modalDadosPessoa}">

            <h:panelGrid columns="2">

                <h:panelGrid columns="1">
                    <h:outputText value="Nome" style="color: #7A7A7A; font-size: 16px;"/>
                    <h:inputText value="#{AtualizarDadosControle.pessoaVO.nome}" id="mdlNome"
                                 styleClass="placeholderInput required nomeModalCadastro" style="width: 286px; height: 37px; border-radius: 2px; margin: 5px 25px 0 0; font-size: 14px!important;color:#0a6aa1!important; text-transform: uppercase;" />
                </h:panelGrid>

                <h:panelGrid columns="1">
                    <h:outputText value="Sobrenome" style="color: #7A7A7A; font-size: 16px;"/>
                    <h:inputText value="#{AtualizarDadosControle.pessoaVO.sobreNome}" id="mdlSobrenome"
                                 styleClass="placeholderInput required sobrenomeModalCadastro" style="width: 286px; height: 37px; border-radius: 2px; margin: 5px 0 0 0; font-size: 14px!important;color:#0a6aa1!important; text-transform: uppercase;"/>
                </h:panelGrid>

                <h:panelGrid columns="1" style="margin-top: 20px">
                    <h:outputLabel value="Seu Cargo: " style="color: #7A7A7A; font-size: 16px;"/>
                    <h:selectOneMenu id="mdlCargo" value="#{AtualizarDadosControle.pessoaVO.cargoEnum}"
                                     style="width: 286px;"
                                     styleClass="listaSelectOne required cargoModalCadastro">
                        <f:selectItems value="#{AtualizarDadosControle.listaCargos}"/>
                    </h:selectOneMenu>
                </h:panelGrid>

                <h:panelGrid columns="1" style="margin-top: 20px">
                    <h:outputLabel value="Sua Fun��o: " style="color: #7A7A7A; font-size: 16px;"/>
                    <h:selectOneMenu id="mdlFuncao" value="#{AtualizarDadosControle.pessoaVO.funcaoEnum}"
                                     style="width: 286px;"
                                     styleClass="listaSelectOne required funcaoModalCadastro">
                        <f:selectItems value="#{AtualizarDadosControle.listaFuncao}"/>
                    </h:selectOneMenu>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGroup layout="block" style="text-align: center; padding: 30px 0px 20px 0px">
                <a4j:commandLink id="btnProximoModalAtualizarUsers" title="Pr�xima p�gina" value="Continuar"
                                 action="#{AtualizarDadosControle.avancarModal}"
                                 oncomplete="#{AtualizarDadosControle.onComplete}"
                                 styleClass="btModalAtualizarUsers" reRender="modalAtualizarUsuarios"
                                 style="padding-right: 24px !important; padding-left: 24px !important;"/>

            </h:panelGroup>
        </h:panelGroup>




        <h:panelGroup style="margin-top: 10px!important;" rendered="#{AtualizarDadosControle.modalDadosEmpresa and AtualizarDadosControle.atualizarDadosEmpresa}">
            <c:if test="${AtualizarDadosControle.atualizarDadosEmpresa}">

                <h:panelGrid columns="1" style="margin-left: 15px !important;">
                    <h:outputLabel value="Quem � o respons�vel financeiro?" style="color: #7A7A7A; font-size: 16px;"/>
                    <h:inputText
                            styleClass="required inputResponsavelFinanCadastro"
                            style="width: 610px; font-size: 14px!important;color:#0a6aa1!important; margin-top: 8px;"
                            id="mdlRespFinanceiro" onblur="blurinput(this);" onfocus="focusinput(this);"
                            value="#{AtualizarDadosControle.empresaVO.responsavelFinanceiro}"/>
                    <rich:suggestionbox height="200" width="610"
                                        for="mdlRespFinanceiro"
                                        status="statusInComponent"
                                        fetchValue="#{result.nome}"
                                        immediate="true"
                                        suggestionAction="#{AtualizarDadosControle.executarAutocompleteUsuarios}"
                                        minChars="1" rowClasses="20"
                                        nothingLabel="Nenhum Respons�vel encontrado !"
                                        var="result" id="suggestionResponsavel">
                        <h:column>
                            <h:outputText value="#{result.nome}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGrid>

                <h:panelGrid columns="2" style="margin-top: 10px!important; margin-left: 15px !important;">
                    <h:panelGrid columns="1">
                        <h:outputLabel value="Classifica��o do neg�cio:" style="color: #7A7A7A; font-size: 16px;"/>
                        <h:selectOneMenu id="mdlClassificacao"
                                         style="width: 286px;"
                                         value="#{AtualizarDadosControle.empresaVO.classificacaoNegocioEnum}"
                                         styleClass="listaSelectOneAdmin required classificacaoNegocioEnum">
                            <f:selectItems value="#{AtualizarDadosControle.listaClassificacaoNegocio}"/>
                        </h:selectOneMenu>
                    </h:panelGrid>

                    <h:panelGrid columns="1" style="margin-top: 10px; margin-left: 30px;">
                        <h:outputLabel value="Quem � o gestor da academia? " style="color: #7A7A7A; font-size: 16px;"/>
                        <h:inputText
                                styleClass="required inputGestorAcademiaCadastro"
                                style="width: 286px; font-size: 14px!important;color:#0a6aa1!important;"
                                id="mdlGestor" onblur="blurinput(this);" onfocus="focusinput(this);"
                                value="#{AtualizarDadosControle.empresaVO.gestor}"/>
                        <rich:suggestionbox height="200" width="286"
                                            for="mdlGestor"
                                            status="statusInComponent"
                                            fetchValue="#{result.nome}"
                                            immediate="true"
                                            suggestionAction="#{AtualizarDadosControle.executarAutocompleteUsuarios}"
                                            minChars="1" rowClasses="20"
                                            nothingLabel="Nenhum usu�rio encontrado !"
                                            var="result" id="suggestionResponsavell">
                            <h:column>
                                <h:outputText value="#{result.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGroup layout="block" style="text-align: center; padding: 30px 0px 20px 0px; width: 95%;">
                    <a4j:commandLink title="Voltar" value="Voltar"
                                     action="#{AtualizarDadosControle.voltarModal}"
                                     oncomplete="#{AtualizarDadosControle.onComplete}"
                                     reRender="modalAtualizarUsuarios" styleClass="btModalVoltalModalAtualizarUsers"
                                     style="padding-right: 24px !important; padding-left: 24px !important;"/>

                    <a4j:commandLink title="Pr�xima p�gina" value="Continuar"
                                     action="#{AtualizarDadosControle.avancarModal}"
                                     oncomplete="#{AtualizarDadosControle.onComplete}"
                                     styleClass="btModalAtualizarUsers" reRender="modalAtualizarUsuarios"
                                     style="padding-right: 24px !important; padding-left: 24px !important;"/>

                </h:panelGroup>
            </c:if>
        </h:panelGroup>

        <h:panelGroup style="margin-top: 10px!important;"
                      rendered="#{AtualizarDadosControle.modalTermosPoliticas}">

            <rich:panel id="pngTermosEAceiteModalAtualizarUser" style="border: none">
                <h:panelGrid columns="1">
                    <h:panelGrid columns="1">
                        <h:outputLabel value="Crie um PIN com quatro n�meros para utilizar durante as valida��es de algumas fun��es do sistema. Tamb�m lembramos que o PIN n�o poder� ser utilizado para acessar o sistema."
                                       style="color: #7A7A7A; font-size: 16px;"/>
                        <br />
                        <h:outputLabel value="PIN:* " style="color: #7A7A7A; font-size: 16px;" />
                        <h:inputSecret value="#{AtualizarDadosControle.pin}" id="mdlPin" maxlength="4"
                                       styleClass="novoPin required"
                                       redisplay="true"
                                       onkeypress="if(event.which < 48 || event.which > 57) return false;"
                                       style="width: 610px; height: 37px; border-radius: 2px; margin: 5px 0 0 0; font-size: 14px!important;color:#0a6aa1!important;">
                        </h:inputSecret>
                        <h:outputLabel value="* Seu PIN deve ser num�rico e conter quatro d�gitos n�o sequenciais."
                                       style="color: #7A7A7A; font-size: 16px;"/>
                    </h:panelGrid>
                </h:panelGrid>

                <div id="panelAceiteTermos">
                    <div class="required termosModalCadastro"
                         style="border-radius: 10px; padding: 3px; margin-top: 10px;">
                        <h:selectBooleanCheckbox style="margin-right: 5px;"
                                                 value="#{AtualizarDadosControle.pessoaVO.aceiteTermosPacto}"
                                                 id="checkBoxAceiteTermosAtualizarUsuarioModal"/>
                        <span style="font-size: 16px;">Li e aceito os </span>
                        <a href="https://sistemapacto.com.br/politica-de-privacidade/" target="_blank"
                           style="font-size: 16px; text-decoration: underline"> termos de uso e privacidade</a>
                        <span style="font-size: 16px;"> do sistema Pacto, seus m�dulos e recursos. </span>
                    </div>

                    <div style="border-radius: 10px; padding: 3px; padding-top: 10px"
                         class="required termosModalCadastro2">
                    <h:selectBooleanCheckbox style="margin-right: 5px"
                                             value="#{AtualizarDadosControle.emailVO.receberEmailNovidades}"
                                             id="checkBoxAutorizaEmailUsuarioModal"/>
                    <span class="fontAutorizarAdmin" style="font-size: 16px;">Autorizo o envio de novidades e atualiza��es do sistema e da Pacto Solu��es atrav�s do e-mail informado.</span>
                </div>

                <div style="border-radius: 10px; padding: 3px; padding-top: 15px">
                    <h:selectBooleanCheckbox style="margin-right: 5px"
                                             value="#{AtualizarDadosControle.telefoneVO.receberSMS}"
                                             id="checkBoxAutorizaSMSUsuarioModal"/>
                    <span class="fontAutorizarAdmin" style="font-size: 16px;">Desejo receber novidades e atualiza��es do sistema e da Pacto Solu��es via SMS e autorizo o recebimento atrav�s do celular informado.</span>
                </div>
                </div>
            </rich:panel>

            <h:panelGroup layout="block" style="text-align: center; padding: 30px 0px 20px 0px; width: 95%;">
                <a4j:commandLink id="btnVoltarModalAtualizarUsers" title="Voltar" value="Voltar"
                                 action="#{AtualizarDadosControle.voltarModal}"
                                 oncomplete="#{AtualizarDadosControle.onComplete}"
                                 reRender="modalAtualizarUsuarios" styleClass="btModalVoltalModalAtualizarUsers"
                                 style="padding-right: 24px !important; padding-left: 24px !important;"/>

                <a4j:commandLink id="btnSalvarModalAtualizarUsers" title="Atualizar" value="Atualizar"
                                 action="#{AtualizarDadosControle.salvarAtualizacaoCadastral}"
                                 oncomplete="#{AtualizarDadosControle.onComplete}"
                                 styleClass="btModalAtualizarUsers" reRender="pngTermosEAceiteModalAtualizarUser, modalAtualizarUsuarios"
                                 style="padding-right: 24px !important; padding-left: 24px !important;"/>

            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<script type="text/javascript" language="JavaScript">
    function addPlaceModalHoderAtualizarUser() {
        if (document.getElementById("formAtualizarUsuarioModal:mdlNome") != null) {
            document.getElementById("formAtualizarUsuarioModal:mdlNome").setAttribute("placeholder", "Digite seu primeiro nome");
        }
        if (document.getElementById("formAtualizarUsuarioModal:mdlSobrenome") != null) {
            document.getElementById("formAtualizarUsuarioModal:mdlSobrenome").setAttribute("placeholder", "Digite seu sobrenome");
        }
    }

    function validarModalEmail(field) {
        usuario = field.value.substring(0, field.value.indexOf("@"));
        dominio = field.value.substring(field.value.indexOf("@") + 1, field.value.length);

        if ((usuario.length >= 1) &&
            (dominio.length >= 3) &&
            (usuario.search("@") == -1) &&
            (dominio.search("@") == -1) &&
            (usuario.search(" ") == -1) &&
            (dominio.search(" ") == -1) &&
            (dominio.search(".") != -1) &&
            (dominio.indexOf(".") >= 1) &&
            (dominio.lastIndexOf(".") < dominio.length - 1)) {
            // document.getElementById("msgemail").innerHTML = "";
            //alert("E-mail valido");
        } else {
            // document.getElementById("msgemail").innerHTML = "<font color='red' size='2px'>E-mail inv�lido </font>";
            //alert("E-mail invalido");
        }
    }

    function mascara(o, f) {
        v_obj = o;
        v_fun = f;
        setTimeout("execmascara()", 1)
    }

    function execmascara() {
        v_obj.value = v_fun(v_obj.value)
    }

    function mtel(v) {
        v = v.replace(/\D/g, ""); //Remove tudo o que n�o � d�gito
        v = v.replace(/^(\d{2})(\d)/g, "($1) $2"); //Coloca par�nteses em volta dos dois primeiros d�gitos
        // v = v.replace(/(\d)(\d{4})$/, "$1-$2"); //Coloca h�fen entre o quarto e o quinto d�gitos
        // if (v.length < 14) {
        //     document.getElementById("msgefone").innerHTML = "<font color='red' size='2px'>Telefone inv�lido </font>";
        // } else {
        //     document.getElementById("msgefone").innerHTML = " ";
        // }
        return v;
    }
</script>




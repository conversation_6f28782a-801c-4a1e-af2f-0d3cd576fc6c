<%-- 
    Document   : include_panelgrid_lista_contratos
    Created on : 30/04/2012, 15:47:14
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>
<h:panelGrid width="100%" cellspacing="5">
    <table width="100%" border="0" align="left" cellpadding="0"
           cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
        <h:panelGrid width="100%">
            <h:panelGrid columns="2" width="100%">
                <h:panelGroup>
                    <img src="images/arrow2.gif" width="16" height="16"
                         style="vertical-align: middle; margin-right: 6px;"/>
                    <h:outputText style="font-weight: bold" styleClass="text"
                                  value="Contrato"/>
                </h:panelGroup>
                <h:panelGroup>
                    <a4j:commandButton reRender="listaContrato,form:panelMensagemInferior,form:panelMensagemSuperior"
                                       image="./imagens/botaoListarTodosContratos.png"
                                       alt="Listar Todos Contratos"
                                       action="#{ClienteControle.listarTodoContratos}"/>
                </h:panelGroup>
            </h:panelGrid>
            <div class="sep" style="margin: 4px 0 5px 0;">
                <img src="images/shim.gif"/>
            </div>
        </h:panelGrid>
        <tr>
            <td align="left" valign="top">
                <rich:dataTable
                        id="listaContrato" width="100%" border="0"
                        rows="#{ClienteControle.tamanhoListaContrato}"
                        cellspacing="0" cellpadding="10" styleClass="textsmall"
                        columnClasses="centralizado, centralizado, centralizado,
                    centralizado, centralizado,centralizado, colunaEsquerda, centralizado, colunaEsquerda"
                        rendered="#{!empty ClienteControle.listaContratos}"
                        value="#{ClienteControle.listaContratos}"
                        var="contratoTbl">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Situa��o"/>
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                                      value="#{contratoTbl.situacaoContrato}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Contrato"/>
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                                      value="#{contratoTbl.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Data Lan�amento"/>
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                                      value="#{contratoTbl.dataLancamento_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Data In�cio"/>
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                                      value="#{contratoTbl.vigenciaDe}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Data T�rmino"/>
                        </f:facet>

                        <h:outputText style="font-weight: bold"
                                      styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigenteDataTermino}"
                                      value="#{contratoTbl.vigenciaAteAjustada}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column rendered="#{ClienteControle.mostrarLabelPrevisaoRenovacaoAntecipada}">
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Previs�o Renova��o Original"/>
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigenteDataTermino}"
                                      value="#{contratoTbl.dataPrevistaRenovar}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Respons�vel"/>
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                                      value="#{contratoTbl.responsavelContrato.nomeAbreviado}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight:bold" value="S."/>

                        </f:facet>
                        <h:graphicImage id="contAtivo" value="./imagens/botaoAtivo.png"
                                        rendered="#{contratoTbl.contratoAtivo}" width="25"
                                        height="24"/>
                        <h:graphicImage id="contCancelado" value="./imagens/botaoCancelamento.png"
                                        rendered="#{contratoTbl.contratoCancelado}" width="25"
                                        height="24"/>
                        <h:graphicImage id="contTrancado" value="./imagens/botaoTrancamento.png"
                                        rendered="#{contratoTbl.contratoTrancado}" width="25"
                                        height="24"/>
                        <h:graphicImage id="contInativo" value="./imagens/botaoInativo.png"
                                        rendered="#{contratoTbl.contratoInativo}" width="25"
                                        height="24"/>
                        <h:graphicImage id="contRenovado" value="./imagens/botaoRenovado.png"
                                        rendered="#{contratoTbl.apresentarBotaoRenovarContrato}"
                                        width="25" height="24"/>
                        <rich:spacer width="5px"/>
                    </rich:column>
                    <%--
        <rich:column>
        <f:facet name="header">
        <h:outputText  style="font-weight: bold" value="Dura��o"/>
        </f:facet>
        <h:outputText style="font-weight: bold" styleClass="blue" value="#{contratoTbl.contratoDuracao.numeroMeses}"/>
        </rich:column>--%>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Op��es"/>
                        </f:facet>

                        <a4j:commandButton id="renovarContrato"
                                           rendered="#{contratoTbl.renovarContrato}"
                                           alt="Renovar Contrato"
                                           title="Renovar Contrato"
                                           image="/imagens/botaoRenovar.png"
                                           oncomplete="#{ClienteControle.mensagemNotificar}"
                                           action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}"/>
                        <a4j:commandButton id="rematricularContrato"
                                           rendered="#{contratoTbl.rematricularContrato}"
                                           alt="Rematricular Contrato"
                                           title="Rematricular Contrato"
                                           image="/imagens/botaoRematricular.png"
                                           oncomplete="#{ClienteControle.mensagemNotificar}"
                                           action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}"/>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="imprimir"
                                           value="Imprimir Contrato" image="./imagens/imprimir.png"
                                           action="#{ClienteControle.imprimirContrato}"
                                           reRender="form:panelMensagemSuperior,form:panelMensagemInferior"
                                           oncomplete="abrirPopup('VisualizarContrato', 'RelatorioContrato', 730, 545);"/>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="enviar" image="./imagens/email.png"
                                           action="#{ClienteControle.prepararEnvioContratoPorEmail}"
                                           oncomplete="#{ClienteControle.mensagemNotificar}#{ClienteControle.msgAlert}"
                                           reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, modalEnviarContratoEmail"
                                           value="E-mail"/>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton style="font-weight:bold;"
                                           reRender="modalAlterarVencimento"
                                           id="btnAbrirModalAlterarVencimentoCont" title="Alterar os vencimentos de parcelas deste contrato"
                                           image="./imagens/date_edit.png"
                                           oncomplete="#{rich:component('modalAlterarVencimento')}.show();"
                                           actionListener="#{AlterarVencimentoParcelasControle.preparar}">
                            <f:attribute name="ehRecorrencia" value="#{contratoTbl.regimeRecorrencia}"/>
                            <f:attribute name="contrato" value="#{contratoTbl}"/>
                            <f:attribute name="cliente" value="#{ClienteControle.clienteVO}"/>
                        </a4j:commandButton>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="visualisarDadosCompletosContrato"
                                           image="./imagens/botaoVisualizar.png"
                                           alt="Ver Detalhes do Contrato"
                                           action="#{ClienteControle.selecionarDadosContrato}"
                                           reRender="richPanel,panelOperacoesContrato,form:panelMensagemSuperior,form:panelMensagemInferior"/>
                        <rich:spacer width="5px"/>
                    </rich:column>
                </rich:dataTable>
            </td>
        </tr>
    </table>
</h:panelGrid>

<%-- 
    Document   : include_modalPanelEmailEnviadoSucesso
    Created on : 12/05/2011, 13:47:54
    Author     : Waller
--%>
<%@include file="./../imports.jsp" %>
<h:panelGroup id="panelConfirmacao">
    <rich:modalPanel domElementAttachment="parent" id="panelMsgSucesso"
                     showWhenRendered="#{SuperControle.apresentarSucessoEmail}"
                     autosized="true" shadowOpacity="true" width="150" height="30">

        <h:panelGrid columns="1" width="100%" style="text-align: center;">
            <h:outputText styleClass="textsmall" value="E-mail enviado com sucesso!"/>
            <rich:spacer height="5px"/>
            <h:form>
                <a4j:commandButton value="OK"
                                   image="./imagens/botoesCE/OK.png"
                                   oncomplete="Richfaces.hideModalPanel('panelMsgSucesso');"
                                   action="#{SuperControle.fecharConfirmacao}">
                </a4j:commandButton>
            </h:form>
        </h:panelGrid>
    </rich:modalPanel>
</h:panelGroup>
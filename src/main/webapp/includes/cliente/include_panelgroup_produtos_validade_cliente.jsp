<%-- 
    Document   : include_panelgroup_produtos_validade_cliente
    Created on : 30/04/2012, 16:22:59
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup id="panelProdutosValidadeCliente" layout="block">
    <table width="100%" border="0" align="left" cellpadding="0"
           cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
        <tr>
            <td align="left" valign="top" style="padding-bottom: 5px;">
                <div style="clear: both;" class="text">
                    <p style="margin-bottom: 6px;"><img
                            src="images/arrow2.gif" width="16" height="16"
                            style="vertical-align: middle; margin-right: 6px;">
                        <h:outputText style="font-weight: bold"
                                      value="Produtos com Validade" /></p>
                    <div class="sep" style="margin: 4px 0 5px 0;"><img
                            src="images/shim.gif"></div>
                </div>
            </td>
        </tr>
        <tr>
            <td align="left" valign="top">
                <rich:dataTable id="listaProdutoComValidade" width="100%" border="0"
                                rows="#{ClienteControle.nrPaginaMovProdutoValidade}"
                                cellspacing="0" cellpadding="10" styleClass="textsmall"
                                columnClasses="centralizado, centralizado, centralizado "
                                value="#{ClienteControle.clienteVO.listaProdutosComValidade}"
                                var="movProduto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Produto" />
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="blue"
                                      value="#{movProduto.produto.descricao}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Data Compra" />
                        </f:facet>
                        <h:outputText style="font-weight: bold"
                                      styleClass="blue"
                                      value="#{movProduto.dataLancamento_Apresentar}" />
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          value="Data Final Vig�ncia" />
                        </f:facet>
                        <h:outputText style="font-weight: bold" styleClass="red"
                                      value="#{movProduto.dataFinalVigencia_Apresentar}" />
                        <rich:spacer width="10px"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold" value="Op��es" />
                        </f:facet>

                        <a4j:commandButton id="botaoValidarEditarData" style="margin-left: 8px;" title="Alterar a data final de vig�ncia"
                                           rendered="#{movProduto.produto.tipoVigencia != 'VV' && movProduto.produto.tipoProduto != 'TR'}"
                                           action="#{MovProdutoControle.confirmarAlteracaoDataValidade}"
                                           actionListener="#{MovProdutoControle.selecionarMovProdutoListener}"
                                           reRender="panelAutorizacaoFuncionalidade, form:panelRecarregar, modalEditarVigenciaFinalProdutoCliente"
                                           image="/imagens/bt_editar_16.png">
                            <f:attribute name="objMovProdutoEdicao" value="#{movProduto}"/>
                        </a4j:commandButton>

                        <a4j:commandButton id="btnRenovarProduto" style="margin-left: 8px;" title="Renovar um produto com Validade"
                                           rendered="#{movProduto.produto.apresentarRenovar}"
                                           action="#{RenovarProdutoControle.confirmarLancarProduto}"
                                           actionListener="#{RenovarProdutoControle.renovarProduto}"
                                           reRender="panelAutorizacaoFuncionalidade, modalEscolherProdutoRenovar"
                                           image="/imagens/bt_parcelas.png">
                            <f:attribute name="movProdutoVO" value="#{movProduto}"/>
                            <f:attribute name="clienteVO" value="#{ClienteControle.clienteVO}"/>
                        </a4j:commandButton>

                        <a4j:commandButton id="btnEditarAtestado" style="margin-left: 8px;" title="Visualizar Atestado"
                                           rendered="#{movProduto.produto.tipoVigencia == 'VV'}"
                                           action="#{AtestadoControle.editarAtestado}"
                                           actionListener="#{AtestadoControle.prepararEdicaoAtestado}"
                                           oncomplete="abrirPopup('atestadoAptidaoFisica.jsp', 'AptidaoFisica', 880, 650); return false;"
                                           reRender="panelAutorizacaoFuncionalidade, modalEscolherProdutoRenovar"
                                           image="/imagens/icon_lupa.png">
                            <f:attribute name="movProdutoVO" value="#{movProduto}"/>
                            <f:attribute name="clienteVO" value="#{ClienteControle.clienteVO}"/>
                        </a4j:commandButton>

                        <h:outputText value="#{movProduto.produto.categoriaProduto.avaliacaoFisica}"/>

                        <a4j:commandButton id="btnVisualizarUtilizacao" style="margin-left: 8px;" title="Visualizar utiliza��es"
                                           rendered="#{movProduto.produto.categoriaProduto.avaliacaoFisica}"
                                           actionListener="#{TelaClienteControle.prepararVisualizacaoUtilizacaoAvaliacao}"
                                           oncomplete="Richfaces.showModalPanel('modalUtilizacoesAvaliacaoFisica')"
                                           reRender="modalUtilizacoesAvaliacaoFisica"
                                           image="/imagens/icon_lupa.png">
                            <f:attribute name="movProdutoVO" value="#{movProduto}"/>
                        </a4j:commandButton>

                        <a4j:commandButton id="btnExcluirAtestado" style="margin-left: 8px;" title="Excluir Atestado"
                                           rendered="#{movProduto.produto.tipoVigencia == 'VV'}"
                                           action="#{AtestadoControle.prepararExcluirAtestado}"
                                           image="/imagens/botaoRemover.png"
                                           reRender="panelAutorizacaoFuncionalidade">
                            
                        </a4j:commandButton>
                    </rich:column>
                </rich:dataTable>
                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                    <h:panelGroup>
                        <h:panelGrid columns="2">
                            <rich:datascroller align="center"
                                               for="listaProdutoComValidade" maxPages="100"
                                               id="scResultadoListaProdutoComValidade" />
                            <rich:inputNumberSpinner inputSize="4"
                                                     styleClass="form" enableManualInput="true"
                                                     minValue="1" maxValue="100"
                                                     value="#{ClienteControle.nrPaginaMovProdutoValidade}">
                                <a4j:support event="onchange"
                                             focus="scResultadoListaProdutoComValidade"
                                             reRender="listaProdutoComValidade,scResultadoListaProdutoComValidade" />
                            </rich:inputNumberSpinner>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>
            </td>
        </tr>
    </table>
</h:panelGroup>

<%-- 
    Document   : include_panelgrid_dados_pessoais_cliente
    Created on : 30/04/2012, 15:41:02
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup>
    <table width="100%" border="0" align="left" cellpadding="0"
           cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
        <tr>
            <td align="left" valign="top" style="padding-bottom: 5px;">
                <div style="clear: both;" class="text">
                    <p style="margin-bottom: 6px;"><img
                            src="images/arrow2.gif" width="16" height="16"
                            style="vertical-align: middle; margin-right: 6px;"><h:outputText
                        style="font-weight: bold" value="Dados Pessoais" /></p>
                    <div class="sep" style="margin: 4px 0 5px 0;"><img
                            src="images/shim.gif"></div>
                </div>
            </td>
        </tr>
        <tr>
            <td align="left" valign="top"><h:panelGrid
            id="pendencias" columns="1" columnClasses="left"
            width="100%" border="0" cellspacing="0" cellpadding="0"
            styleClass="textsmall">
            <h:panelGroup>
                <h:outputText style="font-weight: bold"
                              value="Data Cadastramento:" />
                <f:verbatim>
                    <h:outputText value="        " />
                </f:verbatim>
                <h:outputText id="cliDataCadatro" style="font-weight: bold"
                              styleClass="blue"
                              value="#{ClienteControle.clienteVO.pessoa.dataCadastro_Apresentar}" />
            </h:panelGroup>
            <h:panelGroup>
                <h:outputText style="font-weight: bold"
                              value="Categoria:" />
                <f:verbatim>
                    <h:outputText value="        " />
                </f:verbatim>
                <h:outputText id="cliDataCategoria" style="font-weight: bold"
                              styleClass="blue"
                              value="#{ClienteControle.clienteVO.categoria.nome}" />
            </h:panelGroup>
            <h:panelGroup>
                <h:outputText style="font-weight: bold"
                              value="Data Matr�cula: " />
                <f:verbatim>
                    <h:outputText id="cliDataMatricula" style="font-weight: bold" styleClass="blue"
                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataMatricula}">
                        <f:convertDateTime type="date" dateStyle="short"
                                           locale="pt" timeZone="America/Sao_Paulo"
                                           pattern="dd/MM/yyyy" />
                    </h:outputText>
                    -
                    <h:outputText
                        style="font-weight: bold" styleClass="blue"
                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.nomeDiaSemanaDataMatricula}" />
                    <h:outputText
                        style="font-weight: bold" styleClass="blue"
                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataMatriculaAteHojePorExtenso}" />
                </f:verbatim>
                <h:outputText style="font-weight: bold"
                              styleClass="blue" />
            </h:panelGroup>
            <h:panelGroup>
                <h:outputText style="font-weight: bold"
                              value="Data �ltima Rematr�cula: " />
                <f:verbatim>
                    <h:outputText id="cliDataRematricula" style="font-weight: bold" styleClass="blue"
                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimaRematricula}">
                        <f:convertDateTime type="date" dateStyle="short"
                                           locale="pt" timeZone="America/Sao_Paulo"
                                           pattern="dd/MM/yyyy" />
                    </h:outputText>
                    -
                    <h:outputText
                        style="font-weight: bold" styleClass="blue"
                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.nomeDiaSemanaDataUltimaRematricula}" />
                    <h:outputText
                        style="font-weight: bold" styleClass="blue"
                        value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataRematriculaAteHojePorExtenso}" />
                </f:verbatim>
                <h:outputText style="font-weight: bold"
                              styleClass="blue" />
            </h:panelGroup>
            <h:panelGroup>
                <h:outputText style="font-weight: bold"
                              value="Situa��o:" />
                <f:verbatim>
                    <h:outputText value="        " />
                </f:verbatim>
                <h:outputText id="situacaoClienteAuto" style="font-weight: bold"
                              styleClass="blue"
                              value="#{ClienteControle.clienteVO.situacao_Apresentar}" />
            </h:panelGroup>


            <h:panelGroup>
                <f:verbatim>
                    <h:outputText value="        " />
                </f:verbatim>
                <h:dataTable id="emailVO" width="100%"
                             rowClasses="textsmall"
                             columnClasses="centralizado,centralizado"
                             value="#{ClienteControle.pessoaVO.emailVOs}"
                             var="email">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          styleClass="textsmall" value="Email:" />
                        </f:facet>
                        <h:outputText id="cliEmail" styleClass="blue" value="#{email.email}" />
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText style="font-weight: bold"
                                          styleClass="textsmall"
                                          value="Email para Correspond�ncia:" />
                        </f:facet>
                        <h:outputText styleClass="blue"
                                      value="#{email.emailCorrespondencia_Apresentar}" />
                    </h:column>
                </h:dataTable>
            </h:panelGroup>
        </h:panelGrid></td>
        </tr>
    </table>
</h:panelGroup>

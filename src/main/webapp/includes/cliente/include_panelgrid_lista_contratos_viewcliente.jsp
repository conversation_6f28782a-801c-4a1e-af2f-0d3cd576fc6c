<%--
    Document   : include_panelgrid_lista_contratos
    Created on : 30/04/2012, 15:47:14
    Author     : Waller
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@include file="../imports.jsp" %>
<h:panelGrid width="100%" cellspacing="5">
  <table width="100%" border="0" align="left" cellpadding="0"
         cellspacing="0" bgcolor="#e6e6e6" style="padding: 10px;">
    <h:panelGrid width="100%">
      <h:panelGrid columns="2" width="100%">
        <h:panelGroup>
          <img src="images/arrow2.gif" width="16" height="16"
               style="vertical-align: middle; margin-right: 6px;"/>
          <h:outputText style="font-weight: bold" styleClass="text"
                        value="Contrato"/>
        </h:panelGroup>
        <h:panelGroup>
          <a4j:commandButton reRender="listaContrato,form:panelMensagemInferior,form:panelMensagemSuperior"
                             id="listarTodoContratos"
                             image="./imagens/botaoListarTodosContratos.png"
                             alt="Listar Todos Contratos"
                             action="#{ClienteControle.listarTodoContratos}"/>
        </h:panelGroup>
      </h:panelGrid>
      <div class="sep" style="margin: 4px 0 5px 0;">
        <img src="images/shim.gif"/>
      </div>
    </h:panelGrid>
    <tr>
      <td align="left" valign="top">
        <rich:dataTable
                id="listaContrato" width="100%" border="0"
                rows="#{ClienteControle.tamanhoListaContrato}"
                cellspacing="0" cellpadding="10" styleClass="textsmall"
                columnClasses="centralizado, centralizado, centralizado,
                    centralizado, centralizado, colunaEsquerda, centralizado, colunaEsquerda"
                rendered="#{!empty ClienteControle.listaContratos}"
                value="#{ClienteControle.listaContratos}"
                var="contratoTbl">
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold" value="Situação"/>
            </f:facet>
            <h:outputText style="font-weight: bold"
                          styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                          value="#{contratoTbl.situacaoContrato}"/>
          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold" value="Contrato"/>
            </f:facet>
            <h:outputText style="font-weight: bold"
                          styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                          value="#{contratoTbl.codigo}"/>
          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold"
                            value="Data Lançamento"/>
            </f:facet>
            <h:outputText style="font-weight: bold"
                          styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                          value="#{contratoTbl.dataLancamento_Apresentar}"/>
          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold"
                            value="Data Início"/>
            </f:facet>
            <h:outputText style="font-weight: bold"
                          styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                          value="#{contratoTbl.vigenciaDe}">
              <f:convertDateTime pattern="dd/MM/yyyy"/>
            </h:outputText>
          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold"
                            value="Data Término"/>
            </f:facet>

            <h:outputText style="font-weight: bold"
                          styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigenteDataTermino}"
                          value="#{contratoTbl.vigenciaAteAjustada}">
              <f:convertDateTime pattern="dd/MM/yyyy"/>
            </h:outputText>

          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold"
                            value="Responsável"/>
            </f:facet>
            <h:outputText style="font-weight: bold"
                          styleClass="#{contratoTbl.styleContratoVigenteOuNaoVigente}"
                          value="#{contratoTbl.responsavelContrato.nomeAbreviado}"/>
          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight:bold" value="S."/>

            </f:facet>
            <h:graphicImage id="contAtivo" value="./imagens/botaoAtivo.png"
                            rendered="#{contratoTbl.contratoAtivo}" width="25"
                            height="24"/>
            <h:graphicImage id="contCancelado" value="./imagens/botaoCancelamento.png"
                            rendered="#{contratoTbl.contratoCancelado}" width="25"
                            height="24"/>
            <h:graphicImage id="contTrancado" value="./imagens/botaoTrancamento.png"
                            rendered="#{contratoTbl.contratoTrancado}" width="25"
                            height="24"/>
            <h:graphicImage id="contInativo" value="./imagens/botaoInativo.png"
                            rendered="#{contratoTbl.contratoInativo}" width="25"
                            height="24"/>
            <h:graphicImage id="contRenovado" value="./imagens/botaoRenovado.png"
                            rendered="#{contratoTbl.apresentarBotaoRenovarContrato}"
                            width="25" height="24"/>
            <rich:spacer width="5px"/>
          </rich:column>
          <%--
<rich:column>
<f:facet name="header">
<h:outputText  style="font-weight: bold" value="Duração"/>
</f:facet>
<h:outputText style="font-weight: bold" styleClass="blue" value="#{contratoTbl.contratoDuracao.numeroMeses}"/>
</rich:column>--%>
          <rich:column>
            <f:facet name="header">
              <h:outputText style="font-weight: bold" value="Opções"/>
            </f:facet>

            <a4j:commandButton id="imprimir"
                               value="Imprimir Contrato" image="./imagens/imprimir.png"
                               action="#{ClienteControle.imprimirContrato}"
                               reRender="form:panelMensagemSuperior,form:panelMensagemInferior"
                                oncomplete="#{ClienteControle.mensagemNotificar}#{ClienteControle.msgAlert}" />
            <rich:spacer width="5px"/>
            <%--<a4j:commandButton id="enviar" image="./imagens/email.png"--%>
                               <%--onclick="if(!confirm('Deseja enviar o e-mail?')) {return false;}"--%>
                               <%--action="#{ClienteControle.enviarContrato}"--%>
                               <%--reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao"--%>
                               <%--value="E-mail">--%>
                <a4j:commandButton id="enviar" image="./imagens/email.png"
                                   action="#{ClienteControle.prepararEnvioContratoPorEmail}"
                                   oncomplete="#{ClienteControle.mensagemNotificar}#{ClienteControle.msgAlert}"
                                   reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, modalEnviarContratoEmail"
                                   value="E-mail"/>
            <rich:spacer width="5px"/>
            <a4j:commandButton id="visualisarDadosCompletosContrato"
                               image="./imagens/botaoVisualizar.png"
                               alt="Ver Detalhes do Contrato"
                               action="#{ClienteControle.selecionarDadosContrato}"
                               reRender="richPanel,panelOperacoesContrato,form:panelMensagemSuperior,form:panelMensagemInferior"/>
            <rich:spacer width="5px"/>
          </rich:column>
        </rich:dataTable>
      </td>
    </tr>
  </table>
</h:panelGrid>

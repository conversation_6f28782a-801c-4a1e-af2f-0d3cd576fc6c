<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@include file="imports.jsp" %>

<style>
    .textoDetalheNota {
        font-size: .9em;
        color: #777;
        border: 0;
        text-transform: uppercase;
    }

    .textoBold {
        font-weight: bold;
    }

    .textoNenhumaNota {
        color: darkred;
        text-transform: uppercase;
        font-weight: bold;
    }

</style>

<rich:modalPanel domElementAttachment="parent" id="modalReenviar" styleClass="novaModal" width="500" autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Reenviar"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalReenviar"/>
            <rich:componentControl for="modalReenviar" attachTo="hideLinkModalReenviar" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formReen">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup id="panelReenviarNota">
                <h:panelGroup layout="block" style="text-align: center;">
                    <h:outputText value="* Ajuste os dados no cadastro do aluno antes de realizar o reenvio."
                                  style="width: 100%; color: red"/>
                </h:panelGroup>

                <h:panelGroup layout="block" style="text-align: center; padding-top: 10px"
                              rendered="#{NotaFiscalControle.notaFiscalVOReenviar.permiteAlterarDataReenvio}">
                    <h:outputText value="Data Emissão:"/>
                    <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important; width: 50%;">
                        <rich:calendar id="dataEmissaoInicio"
                                       value="#{NotaFiscalControle.dataEmissaoReenvio}"
                                       inputSize="6"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box">
                    <a4j:commandLink id="botaoReenviarNota"
                                     value="Reenviar"
                                     styleClass="botoes nvoBt"
                                     action="#{NotaFiscalControle.reenviarNotaFiscal}"
                                     oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"
                                     reRender="form:panelENotas"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalCancelar" styleClass="novaModal" width="500" autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Justificativa - Cancelar"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalCancelar"/>
            <rich:componentControl for="modalCancelar" attachTo="hideLinkModalCancelar" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formCan">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup id="panelCancelarNota">
                <div>
                    <h:inputText id="justificativa" maxlength="50"
                                 value="#{NotaFiscalControle.justificativa}"
                                 style="width: 100%"/>
                </div>

                <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box">
                    <a4j:commandLink id="botaoCancelarNota"
                                     value="Solicitar cancelamento"
                                     styleClass="botoes nvoBt"
                                     action="#{NotaFiscalControle.cancelarNota}"
                                     oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"
                                     reRender="form:panelENotas"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalInutilizar" styleClass="novaModal" width="500" autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Justificativa - Inutilizar"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalInutilizar"/>
            <rich:componentControl for="modalInutilizar" attachTo="hideLinkModalInutilizar" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formInuti">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup id="panelModalInutilizar">
                <div>
                    <h:inputText id="justificativaInutilizar" maxlength="50"
                                 value="#{NotaFiscalControle.justificativa}"
                                 style="width: 100%"/>
                </div>

                <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box">
                    <a4j:commandLink id="btnInutilizarNota"
                                     value="Solicitar"
                                     styleClass="botoes nvoBt"
                                     action="#{NotaFiscalControle.inutilizarNota}"
                                     oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"
                                     reRender="form:panelENotas"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalEnviarEmailNotaFiscal" styleClass="novaModal" width="500"
                 autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Enviar E-mail"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalEnviarEmailNotaFiscal"/>
            <rich:componentControl for="modalEnviarEmailNotaFiscal" attachTo="hideLinkModalEnviarEmailNotaFiscal"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formEmail">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup id="panelEnviarEmailNotaFiscal">
                <div>
                    <h:inputText id="emailEnviar" maxlength="50"
                                 value="#{NotaFiscalControle.emailEnviar}"
                                 style="width: 100%"/>
                </div>

                <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box">
                    <a4j:commandLink id="btnEnviarEmail"
                                     value="Enviar"
                                     styleClass="botoes nvoBt"
                                     action="#{NotaFiscalControle.enviarEmail}"
                                     oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"
                                     reRender="form:panelENotas"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalParametrosNota"
                 width="500"
                 styleClass="novaModal"
                 autosized="true"
                 shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Razão Social: #{NotaFiscalControle.notaSelecionada.razaoSocial}"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalParametrosNota"/>
            <rich:componentControl for="modalParametrosNota" attachTo="hideLinkModalParametrosNota" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:panelGroup id="panelParametrosNota">
        <c:if test="${empty NotaFiscalControle.listaParametrosSelecionado}">
            <h:panelGrid columns="2">
                <h:graphicImage value="images/warning.png"/>
                <h:outputText styleClass="mensagemDetalhada"
                              value="Não há parâmetros."/>
            </h:panelGrid>
        </c:if>
        <h:panelGroup layout="block" style="height: 400px; overflow-y: scroll; overflow-x: hidden;">
            <rich:dataTable width="100%" value="#{NotaFiscalControle.listaParametrosSelecionado}" var="obj">
                <f:facet name="header">
                    <c:if test="${!empty NotaFiscalControle.listaParametrosSelecionado}">
                        <h:outputText value="Parâmetros utilizados"/>
                    </c:if>
                </f:facet>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Atributo"/>
                    </f:facet>
                    <h:outputText value="#{obj.atributo}"/>
                </rich:column>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor"/>
                    </f:facet>
                    <h:outputText style="font-weight:bold;" value="#{obj.valor}"/>
                </rich:column>
            </rich:dataTable>
        </h:panelGroup>

    </h:panelGroup>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalHistoricoNotaFiscal" styleClass="novaModal" width="800"
                 autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Histórico"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalHistoricoNotaFiscal"/>
            <rich:componentControl for="modalHistoricoNotaFiscal" attachTo="hideLinkModalHistoricoNotaFiscal"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form>
        <h:panelGroup id="panelHistoricoNotaFiscal">
            <h:panelGroup layout="block" style="text-align: center;">

                <rich:dataTable styleClass="tabelaDados semZebra"
                                id="tblHistoricoNotas"
                                value="#{NotaFiscalControle.listaNotaFiscalHistorico}"
                                var="hist" rows="20" rowKeyVar="status">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="COD."/>
                        </f:facet>
                        <h:outputText value="#{hist.codigo}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="DT. REGISTRO"/>
                        </f:facet>
                        <h:outputText value="#{hist.dataRegistro}">
                            <f:convertDateTime type="both" dateStyle="full"
                                               pattern="dd/MM/yyyy - HH:mm:ss"
                                               locale="pt"
                                               timeZone="America/Sao_Paulo"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="DESCRIÇÃO"/>
                        </f:facet>
                        <h:outputText style="text-transform: uppercase"
                                      value="#{hist.descricaoApresentar}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="OBSERVAÇÃO"/>
                        </f:facet>
                        <h:outputText value="#{hist.observacao}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="USUÁRIO"/>
                        </f:facet>
                        <h:outputText value="#{hist.usuarioVO.nome}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalVisualizarNotaFiscal" styleClass="novaModal"
                 width="800" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Nota Fiscal - Detalhes"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalVisualizarNotaFiscal"/>
            <rich:componentControl for="modalVisualizarNotaFiscal" attachTo="hideLinkModalVisualizarNotaFiscal"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form>
        <h:panelGroup layout="block" id="panelVisualizarNotaFiscal" style="max-height: 500px; overflow-y: auto">

            <h:panelGroup layout="block" style="text-align: center; display: inline-flex">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             style="border-right: 1px solid #ccc; margin-right: 10px; padding-right: 10px;"
                             columnClasses="colunaDireita,colunaEsquerda" width="50%"
                             headerClass="subordinado">

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="RAZÃO SOCIAL:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.razaoSocial}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="CPF/CNPJ:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.cpfCnpjApresentar}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="NOME ALUNO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.nomeCliente}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="TELEFONE:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteTelefone}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="E-MAIL:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteEmail}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="ENVIAR E-MAIL:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.enviarEmail}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="PAÍS:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clientePais}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="ESTADO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteEstado}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="ESTADO IBGE:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteEstadoIBGE}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="CIDADE:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteCidade}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="CIDADE IBGE:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteCidadeIBGE}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="CEP:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteEndCEP}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="LOGRADOURO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteEndLogradouro}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="NÚMERO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteEndNumero}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="COMPLEMENTO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteEndComplemento}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="BAIRRO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.clienteEndBairro}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  rendered="#{not empty NotaFiscalControle.notaSelecionada.chaveAcesso}"
                                  value="CHAVE ACESSO:"/>
                    <h:outputText value="#{NotaFiscalControle.notaSelecionada.chaveAcesso}"
                                  styleClass="textoDetalheNota"
                                  rendered="#{not empty NotaFiscalControle.notaSelecionada.chaveAcesso}"/>

                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="colunaDireita,colunaEsquerda" width="50%"
                             headerClass="subordinado">

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                  value="ID_ENOTAS:"/>
                    <a4j:commandLink id="ID_ENOTAS" onclick="copiar('#{NotaFiscalControle.notaSelecionada.idExterno}','Id_Enotas')" styleClass="textoDetalheNota" style="font-size: 0.8em;color: #777;"
                                     rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                     value="#{NotaFiscalControle.notaSelecionada.idExterno}">
                    </a4j:commandLink>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                  value="ID_PACTO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                  value="#{NotaFiscalControle.notaSelecionada.idPacto}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                  value="CÓDIGO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                                  value="#{NotaFiscalControle.notaSelecionada.codigo}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="TIPO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.tipo.descricao}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="SÉRIE:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.serie}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="RPS:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.rps}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="NR. NOTA:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.numeroNota}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="STATUS:"/>
                    <h:outputText styleClass="textoDetalheNota tooltipster"
                                  title="#{NotaFiscalControle.notaSelecionada.statusNotaHint}"
                                  value="#{NotaFiscalControle.notaSelecionada.statusNotaApresentar}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="DT. EMISSÃO:"/>
                    <h:outputText styleClass="textoDetalheNota tooltipster"
                                  title="#{NotaFiscalControle.notaSelecionada.hintNotaRetroativa}"
                                  value="#{NotaFiscalControle.notaSelecionada.dataEmissaoApresentar}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="DT. AUTORIZAÇÃO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.dataAutorizacaoApresentar}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="ITEM LISTA SERVIÇO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.itemListaServico}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="COD. TRIBUTAÇÃO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.codigoTributacaoMunicipio}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="CNAE:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.codigoCNAE}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="ALÍQUOTA:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaSelecionada.aliquotaISS}"/>

                </h:panelGrid>

            </h:panelGroup>

            <h:panelGrid columns="1" width="100%"
                         style="margin-top: 20px">

                <f:facet name="header">
                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="Produtos"/>
                </f:facet>

                <rich:dataTable styleClass="tabelaDados semZebra"
                                id="tblnNotaProdutos"
                                style="margin-left: 10px; margin-right: 10px; margin-top: 0px;"
                                value="#{NotaFiscalControle.notaSelecionada.notaProdutos}"
                                var="prod" rows="20" rowKeyVar="status">

                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="DESCRIÇÃO"/>
                        </f:facet>
                        <h:outputText value="#{prod.descricao}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="VALOR UNITÁRIO"/>
                        </f:facet>
                        <h:outputText value="#{prod.valorUnitarioApresentar}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="QUANTIDADE"/>
                        </f:facet>
                        <h:outputText value="#{prod.quantidade}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="DESCONTO"/>
                        </f:facet>
                        <h:outputText value="#{prod.valorDescontoApresentar}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="TOTAL"/>
                        </f:facet>
                        <h:outputText value="#{prod.totalApresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>

            <h:panelGrid columns="1" width="100%">

                <f:facet name="header">
                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="Forma de Pagamento"/>
                </f:facet>

                <rich:dataTable styleClass="tabelaDados semZebra"
                                id="tblnNotaPagamentos"
                                style="margin-left: 10px; margin-right: 10px; margin-top: 0px;"
                                value="#{NotaFiscalControle.notaSelecionada.notaPagamentos}"
                                var="pag" rows="20" rowKeyVar="status">

                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="FORMA DE PAGAMENTO"/>
                        </f:facet>
                        <h:outputText style="text-transform: uppercase" value="#{pag.descricaoFormaPagamento}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="VALOR"/>
                        </f:facet>
                        <h:outputText value="#{pag.valorApresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalCancelarNotas" styleClass="novaModal" width="900"
                 autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Solicitar cancelamento"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalCancelarNotas"/>
            <rich:componentControl for="modalCancelarNotas" attachTo="hideLinkModalCancelarNotas"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formCance">
        <h:panelGroup layout="block" id="panelCancelarNotas"
                      style="max-height: 500px; overflow-y: auto">
            <h:panelGroup layout="block" style="text-align: center;">

                <h:panelGrid columns="1" width="100%"
                             style="margin-top: 10px"
                             rendered="#{not empty NotaFiscalControle.listaNotasCancelar}">

                    <f:facet name="header">
                        <h:outputText styleClass="textoDetalheNota textoBold"
                                      value="Notas Solicitar Cancelamento"/>
                    </f:facet>

                    <h:panelGroup layout="block"
                                  style="text-align: center; padding: 10px"
                                  rendered="#{empty NotaFiscalControle.listaNotasCancelar}">
                        <h:outputText value="Nenhuma nota para solicitar cancelamento"
                                      styleClass="textoNenhumaNota"/>
                    </h:panelGroup>

                    <rich:dataTable id="tblListaNotasCancelar"
                                    styleClass="tabelaDados semZebra"
                                    rendered="#{not empty NotaFiscalControle.listaNotasCancelar}"
                                    value="#{NotaFiscalControle.listaNotasCancelar}"
                                    var="nota" rows="10" rowKeyVar="status">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="SERIE"/>
                            </f:facet>
                            <h:outputText value="#{nota.serie}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="RPS"/>
                            </f:facet>
                            <h:outputText value="#{nota.rps}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="NR. NOTA"/>
                            </f:facet>
                            <h:outputText value="#{nota.numeroNota}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="RAZÃO SOCIAL"/>
                            </f:facet>
                            <h:outputText value="#{nota.razaoSocial}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="DT. REGISTRO"/>
                            </f:facet>
                            <h:outputText value="#{nota.dataRegistro}">
                                <f:convertDateTime type="both" dateStyle="full"
                                                   pattern="dd/MM/yyyy - HH:mm:ss"
                                                   locale="pt"
                                                   timeZone="America/Sao_Paulo"/>
                            </h:outputText>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller rendered="#{NotaFiscalControle.qtdNotasCancelar > 10}"
                                       for="tblListaNotasCancelar" maxPages="10"
                                       page="#{NotaFiscalControle.scrollerPage}"/>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%"
                             rendered="#{not empty NotaFiscalControle.listaNotasIgnoradas}">

                    <f:facet name="header">
                        <h:outputText styleClass="textoDetalheNota textoBold"
                                      value="Notas Ignoradas"/>
                    </f:facet>

                    <rich:dataTable styleClass="tabelaDados semZebra"
                                    id="tblListaNotasIgnoradas"
                                    value="#{NotaFiscalControle.listaNotasIgnoradas}"
                                    var="igno" rows="10" rowKeyVar="status">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="SÉRIE"/>
                            </f:facet>
                            <h:outputText value="#{igno.serie}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="RPS"/>
                            </f:facet>
                            <h:outputText value="#{igno.rps}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="NR. NOTA"/>
                            </f:facet>
                            <h:outputText value="#{igno.numeroNota}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="RAZÃO SOCIAL"/>
                            </f:facet>
                            <h:outputText value="#{igno.razaoSocial}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="DT. REGISTRO"/>
                            </f:facet>
                            <h:outputText value="#{igno.dataRegistro}">
                                <f:convertDateTime type="both" dateStyle="full"
                                                   pattern="dd/MM/yyyy - HH:mm:ss"
                                                   locale="pt"
                                                   timeZone="America/Sao_Paulo"/>
                            </h:outputText>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller rendered="#{NotaFiscalControle.qtdNotasIgnoradas > 10}"
                                       for="tblListaNotasIgnoradas" maxPages="10"
                                       page="#{NotaFiscalControle.scrollerPage}"/>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" style="text-align: center; padding-top: 20px">

                    <f:facet name="header">
                        <h:outputText styleClass="textoDetalheNota textoBold"
                                      value="Totais"/>
                    </f:facet>

                    <br/>
                    <h:outputText value="#{NotaFiscalControle.qtdNotasCancelar} - Notas solicitar cancelamento"
                                  styleClass="textoDetalheNota textoBold"/>
                    <h:outputText value="#{NotaFiscalControle.qtdNotasIgnoradas} - Notas ignoradas"
                                  styleClass="textoDetalheNota textoBold"/>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" style="text-align: center; padding: 30px;">

                    <div>
                        <h:inputText maxlength="50"
                                     id="justificativaCancelarSel"
                                     value="#{NotaFiscalControle.justificativa}"
                                     style="width: 100%"/>
                    </div>

                    <h:panelGroup layout="block" style="padding-top: 20px;">
                        <a4j:commandLink id="btnCancelarNotasSelecionadas"
                                         value="Solicitar cancelamento"
                                         reRender="form:panelENotas"
                                         styleClass="botoes nvoBt"
                                         action="#{NotaFiscalControle.cancelarNotasSelecionadas}"
                                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalInutilizarNotas" styleClass="novaModal" width="700"
                 autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Solicitar inutilização"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalInutilizarNotas"/>
            <rich:componentControl for="modalInutilizarNotas" attachTo="hideLinkModalInutilizarNotas"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formInu">
        <h:panelGroup layout="block" id="panelInutilizarNotas"
                      style="max-height: 500px; overflow-y: auto"
                      rendered="#{!NotaFiscalControle.apresentarInutilizarPorFaixa}">
            <h:panelGroup layout="block" style="text-align: center;">

                <h:panelGrid columns="1" width="100%"
                             style="margin-top: 10px"
                             rendered="#{not empty NotaFiscalControle.listaNotasInutilizar}">

                    <f:facet name="header">
                        <h:outputText styleClass="textoDetalheNota textoBold"
                                      value="Notas Solicitar Inutilização"/>
                    </f:facet>

                    <h:panelGroup layout="block"
                                  style="text-align: center; padding: 10px"
                                  rendered="#{empty NotaFiscalControle.listaNotasInutilizar}">
                        <h:outputText value="Nenhuma nota para solicitar inutilização"
                                      styleClass="textoNenhumaNota"/>
                    </h:panelGroup>

                    <rich:dataTable id="tblListaNotasInutilizar"
                                    styleClass="tabelaDados semZebra"
                                    rendered="#{not empty NotaFiscalControle.listaNotasInutilizar}"
                                    value="#{NotaFiscalControle.listaNotasInutilizar}"
                                    var="nota" rows="20" rowKeyVar="status">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="SERIE"/>
                            </f:facet>
                            <h:outputText value="#{nota.serie}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="STATUS"/>
                            </f:facet>
                            <h:outputText value="#{nota.statusNotaApresentar}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="RPS"/>
                            </f:facet>
                            <h:outputText value="#{nota.rps}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="NR. NOTA"/>
                            </f:facet>
                            <h:outputText value="#{nota.numeroNota}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="RAZÃO SOCIAL"/>
                            </f:facet>
                            <h:outputText value="#{nota.razaoSocial}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="DT. REGISTRO"/>
                            </f:facet>
                            <h:outputText value="#{nota.dataRegistro}">
                                <f:convertDateTime type="both" dateStyle="full"
                                                   pattern="dd/MM/yyyy - HH:mm:ss"
                                                   locale="pt"
                                                   timeZone="America/Sao_Paulo"/>
                            </h:outputText>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%"
                             rendered="#{not empty NotaFiscalControle.listaNotasIgnoradas}">

                    <f:facet name="header">
                        <h:outputText styleClass="textoDetalheNota textoBold"
                                      value="Notas com Status que não permitem inutilização"/>
                    </f:facet>

                    <rich:dataTable styleClass="tabelaDados semZebra"
                                    id="tblListaNotasIgnoradas"
                                    value="#{NotaFiscalControle.listaNotasIgnoradas}"
                                    var="igno" rows="20" rowKeyVar="status">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="SÉRIE"/>
                            </f:facet>
                            <h:outputText value="#{igno.serie}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="STATUS"/>
                            </f:facet>
                            <h:outputText value="#{igno.statusNotaApresentar}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="RPS"/>
                            </f:facet>
                            <h:outputText value="#{igno.rps}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="NR. NOTA"/>
                            </f:facet>
                            <h:outputText value="#{igno.numeroNota}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="RAZÃO SOCIAL"/>
                            </f:facet>
                            <h:outputText value="#{igno.razaoSocial}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="DT. REGISTRO"/>
                            </f:facet>
                            <h:outputText value="#{igno.dataRegistro}">
                                <f:convertDateTime type="both" dateStyle="full"
                                                   pattern="dd/MM/yyyy - HH:mm:ss"
                                                   locale="pt"
                                                   timeZone="America/Sao_Paulo"/>
                            </h:outputText>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" style="text-align: center; padding-top: 20px">

                    <f:facet name="header">
                        <h:outputText styleClass="textoDetalheNota textoBold"
                                      value="Totais"/>
                    </f:facet>

                    <br/>
                    <h:outputText value="#{NotaFiscalControle.qtdNotasInutilizar} - Notas inutilizar"
                                  styleClass="textoDetalheNota textoBold"/>
                    <h:outputText value="#{NotaFiscalControle.qtdNotasIgnoradas} - Notas ignoradas"
                                  styleClass="textoDetalheNota textoBold"/>
                </h:panelGrid>

                <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box"
                              rendered="#{not empty NotaFiscalControle.listaNotasInutilizar}">

                    <div>
                        <h:inputText maxlength="50"
                                     id="justificativaInutilizarSel"
                                     value="#{NotaFiscalControle.justificativa}"
                                     style="width: 100%"/>
                    </div>

                    <h:panelGroup layout="block" style="padding-top: 20px;">
                        <a4j:commandLink id="btnInutilizarNotasSelecionadas"
                                         value="Solicitar inutilização"
                                         reRender="form:panelENotas"
                                         styleClass="botoes nvoBt"
                                         action="#{NotaFiscalControle.inutilizarNotasSelecionadas}"
                                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGroup layout="block" id="panelInutilizarNotasFaixa"
                      rendered="#{NotaFiscalControle.apresentarInutilizarPorFaixa}"
                      style="max-height: 500px; overflow-y: auto">
            <h:panelGroup layout="block" style="text-align: center;">

                <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box">

                    <div style="width: 100%;text-align: left">
                        <h:outputText styleClass="labelFiltros" value="Empresa Inutilizar"/>
                        <h:selectOneMenu id="idEmpresaEnotasInutilizarFaixa"
                                         style="width: 100%;height: 36px;font-size: 14px;"
                                         styleClass="form"
                                         value="#{NotaFiscalControle.codigoConfigNota}">
                            <f:selectItems value="#{NotaFiscalControle.listaEmpresasPorIdEnotas}"/>
                        </h:selectOneMenu>
                    </div>

                    <div style="width: 100%;display: flex;margin-top: 16px">
                        <h:inputText maxlength="10"
                                     id="numInicialFaixa"
                                     onkeypress="return mascara(this.form, this.id, '9999999999', event);"
                                     value="#{NotaFiscalControle.numInicialInutilizar}"
                                     style="width: 100%;margin-right: 8px"/>

                        <h:inputText maxlength="10"
                                     id="numFinalFaixa"
                                     onkeypress="return mascara(this.form, this.id, '9999999999', event);"
                                     value="#{NotaFiscalControle.numFinalInutilizar}"
                                     style="width: 100%;margin-left: 8px"/>
                    </div>

                    <div style="margin-top: 16px">
                        <h:inputText maxlength="50"
                                     id="justificativaInutilizarSelFaixa"
                                     value="#{NotaFiscalControle.justificativa}"
                                     style="width: 100%"/>
                    </div>

                    <h:panelGroup layout="block" style="padding-top: 20px;">
                        <a4j:commandLink id="btnHistoricoInutilizacaoFaixa"
                                         value="Histórico de inutilização"
                                         reRender="form:panelENotas, modalHistoricoInutilizacao"
                                         styleClass="botoes nvoBt btSec"
                                         action="#{NotaFiscalControle.consultarHistoricoInutilizacao}"
                                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"/>

                        <a4j:commandLink id="btnInutilizarNotasFaixa"
                                         value="Solicitar inutilização"
                                         reRender="form:panelENotas"
                                         styleClass="botoes nvoBt"
                                         action="#{NotaFiscalControle.inutilizarNotasSelecionadas}"
                                         oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalHistoricoInutilizacao" styleClass="novaModal" width="1000"
                 autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Histórico de inutilização"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                          id="hideLinkModalHistoricoInutilizacao"/>
            <rich:componentControl for="modalHistoricoInutilizacao" attachTo="hideLinkModalHistoricoInutilizacao"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formHistInu">
        <h:panelGroup layout="block" id="panelHistoricoInutilizacao" style="max-height: 500px; overflow-y: auto">
            <h:panelGroup layout="block" style="text-align: center;">

                <h:panelGrid columns="1" width="100%" style="margin-top: 10px">

                    <f:facet name="header">
                        <h:outputText styleClass="textoDetalheNota textoBold"
                                      value="Faixas solicitadas para Inutilizar"/>
                    </f:facet>

                    <h:panelGroup layout="block" style="text-align: center; padding: 10px"
                                  rendered="#{empty NotaFiscalControle.faixasInutilizadas}">
                        <h:outputText value="Nenhuma faixa inutilizada" styleClass="textoNenhumaNota"/>
                    </h:panelGroup>

                    <rich:dataTable id="tblFaixasInutilizadas" styleClass="tabelaDados semZebra"
                                    rendered="#{not empty NotaFiscalControle.inutilizacaoNotaFiscalTO}"
                                    value="#{NotaFiscalControle.inutilizacaoNotaFiscalTO}"
                                    var="faixa" rows="20" rowKeyVar="status"
                                    columnClasses="esquerda,centralizado,centralizado,esquerda,centralizado,esquerda">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="DT. REGISTRO"/>
                            </f:facet>
                            <h:outputText value="#{faixa.dataRegistro}">
                                <f:convertDateTime type="both" dateStyle="full"
                                                   pattern="dd/MM/yyyy - HH:mm:ss"
                                                   locale="pt"
                                                   timeZone="America/Sao_Paulo"/>
                            </h:outputText>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Núm. Inicial"/>
                            </f:facet>
                            <h:outputText value="#{faixa.numeroInicial}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Núm. Final"/>
                            </f:facet>
                            <h:outputText value="#{faixa.numeroFinal}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Justificativa"/>
                            </f:facet>
                            <h:outputText value="#{faixa.justificativa}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Status"/>
                            </f:facet>
                            <h:outputText value="#{faixa.status}"/>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Motivo"/>
                            </f:facet>
                            <h:outputText value="#{faixa.motivoStatus}"/>
                        </rich:column>

                    </rich:dataTable>
                </h:panelGrid>

                <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box">

                    <h:panelGroup layout="block" style="padding-top: 20px;">
                        <a4j:commandLink id="btnFecharListaOperacaoInutilizar"
                                         value="Fechar"
                                         reRender="form:panelENotas"
                                         styleClass="botoes nvoBt"
                                         oncomplete="Richfaces.hideModalPanel('modalHistoricoInutilizacao');"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalNotaEnotas" styleClass="novaModal" width="600"
                 autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Dados eNotas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalNotaEnotas"/>
            <rich:componentControl for="modalNotaEnotas" attachTo="hideLinkModalNotaEnotas"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form>
        <h:panelGroup id="panelNotaEnotas">
            <h:panelGroup layout="block" style="text-align: center;">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="colunaDireita,colunaEsquerda" width="100%"
                             headerClass="subordinado">

                    <h:outputText rendered="#{NotaFiscalControle.apresentarDadosInutilizacao}"
                                  styleClass="textoDetalheNota textoBold"
                                  value="ID INUTILIZAÇÃO:"/>
                    <h:outputText rendered="#{NotaFiscalControle.apresentarDadosInutilizacao}"
                                  styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.inutilizacaoNotaFiscalTO.id}"/>

                    <h:outputText rendered="#{NotaFiscalControle.apresentarDadosInutilizacao}"
                                  styleClass="textoDetalheNota textoBold"
                                  value="STATUS INUTILIZAÇÃO:"/>
                    <h:outputText rendered="#{NotaFiscalControle.apresentarDadosInutilizacao}"
                                  styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.inutilizacaoNotaFiscalTO.status}"/>

                    <h:outputText rendered="#{NotaFiscalControle.apresentarDadosInutilizacao}"
                                  styleClass="textoDetalheNota textoBold"
                                  value="MOTIVO STATUS INUTILIZAÇÃO:"/>
                    <h:outputText rendered="#{NotaFiscalControle.apresentarDadosInutilizacao}"
                                  styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.inutilizacaoNotaFiscalTO.motivoStatus}"/>

                    <h:outputText rendered="#{NotaFiscalControle.apresentarDadosInutilizacao}"
                                  styleClass="textoDetalheNota textoBold"
                                  value="JUSTIFICATIVA INUTILIZAÇÃO:"/>
                    <h:outputText rendered="#{NotaFiscalControle.apresentarDadosInutilizacao}"
                                  styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.inutilizacaoNotaFiscalTO.justificativa}"/>


                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="ID_ENOTAS (idExterno):"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.idExterno}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="ID_PACTO (idReferencia):"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.idReferencia}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="TIPO NOTA:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.tipo}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="AMBIENTE EMISSÃO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.ambienteEmissao}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="STATUS:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.status}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="MOTIVO STATUS:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  escape="false"
                                  value="#{NotaFiscalControle.notaEnotasTO.motivoStatus}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="Enviada por e-mail:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.enviadaPorEmailApresentar}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="SÉRIE:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.serieRps}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="NR. RPS:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.numeroRps}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="NÚMERO NOTA:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.numero}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="CÓDIGO VERIFICAÇÃO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.codigoVerificacao}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="CHAVE ACESSO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.chaveAcesso}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="DT. COMPETÊNCIA:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.dataCompetenciaRpsApresentar}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="DT. CRIAÇÃO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.dataCriacaoApresentar}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="DT. AUTORIZAÇÃO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.dataAutorizacaoApresentar}"/>

                    <h:outputText styleClass="textoDetalheNota textoBold"
                                  value="DT. ÚLTIMA ALTERAÇÃO:"/>
                    <h:outputText styleClass="textoDetalheNota"
                                  value="#{NotaFiscalControle.notaEnotasTO.dataUltimaAlteracaoApresentar}"/>

                </h:panelGrid>

                <h:panelGroup layout="block" style="text-align: center;" styleClass="margin-box">
                    <a4j:commandLink id="btnAtualizarInformacoesComDadosEnotas"
                                     value="Atualizar"
                                     reRender="form:panelENotas"
                                     title="Atualizar a nota fiscal com as informações obtidas no eNotas"
                                     styleClass="botoes nvoBt tooltipster"
                                     action="#{NotaFiscalControle.atualizarInformacoesComDadosEnotas}"
                                     oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalJsonEnvio" autosized="true"
                 shadowOpacity="true" width="900" height="400"
                 styleClass="novaModal" onbeforeshow="formatJSONEnvio()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="JSON Envio"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkModalJsonEnvio"/>
            <rich:componentControl for="modalJsonEnvio"
                                   attachTo="hidelinkModalJsonEnvio" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formJsonEnvio" ajaxSubmit="true">
        <h:panelGroup layout="block" id="panelModalJsonEnvio" >
            <h:inputHidden id="jsonEnvioJSON" value="#{NotaFiscalControle.jsonEnvio}"/>
                <pre id="jsonEnvio" style="white-space: pre-wrap" onclick="copiaColaJson()"></pre>
            <script type="text/javascript">
                function formatJSONEnvio() {
                    document.getElementById("jsonEnvio").innerHTML = JSON.stringify(JSON.parse(document.getElementById("formJsonEnvio:jsonEnvioJSON").value), null, 3);
                }
                function copiaColaJson() {
                    debugger
                    var el = document.createElement('textarea');
                    el.value = document.getElementById("jsonEnvio").innerHTML;
                    document.body.appendChild(el);
                    el.select();
                    document.execCommand('copy');
                    document.body.removeChild(el);
                    Notifier.info('Json Copiada para a área de transferência.');
                }
            </script>
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalMotivoNota" autosized="true"
                 shadowOpacity="true" width="400"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Status - Motivo"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkModalMotivoNota"/>
            <rich:componentControl for="modalMotivoNota"
                                   attachTo="hidelinkModalMotivoNota" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formMotivo" ajaxSubmit="true">
        <h:panelGroup layout="block" id="panelModalMotivoNota" style="padding: 10px">
            <h:panelGroup layout="block" style="text-align: center">
                <h:outputText style="font-weight: bold; text-transform: uppercase; text-decoration: none;"
                              styleClass="tooltipster fontListaNotaFiscal #{NotaFiscalControle.notaSelecionada.statusNotaEnum.css}"
                              value="#{NotaFiscalControle.notaSelecionada.statusNotaApresentar}"/>
            </h:panelGroup>
            <br/>
            <br/>
            <h:outputText styleClass="fontListaNotaFiscal"
                          escape="false"
                          value="#{NotaFiscalControle.notaSelecionada.statusNotaHint}"/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>




<rich:modalPanel domElementAttachment="parent" id="modalAlterarStatusNotas" styleClass="novaModal" width="900"
                 autosized="true"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Alterar Status Notas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkModalAlterarStatusNotas"/>
            <rich:componentControl for="modalAlterarStatusNotas" attachTo="hideLinkModalAlterarStatusNotas"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formAlterarStatusNotas">
        <h:panelGroup layout="block" id="panelAlterarStatusNotas">
            <h:panelGroup layout="block" style="text-align: justify;">

                <h:panelGrid columns="1" width="100%"
                             style="margin-top: 10px">
                    <f:facet name="header">
                        <h:outputText styleClass="textoBold"
                                      value="A mudança de status é realizada exclusivamente dentro do sistema, com o objetivo de ajustar as informações junto à prefeitura,
                                       uma vez que algumas prefeituras não oferecem a opção de cancelamento via web service.
                                       Como essa alteração ocorre apenas no sistema, ao solicitar a sincronização das notas com o Enotas,
                                       o status será revertido para a situação original que está registrada no Enotas."/>
                    </f:facet>
                </h:panelGrid>

                <h:panelGrid columns="2" width="100%" style="padding: 30px;">
                    <h:panelGroup layout="block" styleClass="panelFiltros">
                        <h:outputText styleClass="labelFiltros"
                                      style="vertical-align:middle;" value="Status da Alteração"/>
                        <h:selectOneMenu id="filtroStatusNota"
                                         style="width: 100%;height: 30px;"
                                         styleClass="form"
                                         value="#{NotaFiscalControle.statusAlteracaoNotaFiscal}">
                            <f:selectItems value="#{NotaFiscalControle.listaStatusAlterarNotaFiscal}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="panelFiltros">
                        <h:outputText styleClass="labelFiltros"
                                      style="vertical-align:middle;" value="Motivo da Alteração"/>
                        <h:inputText maxlength="50"
                                     id="motivoAlteracaoStatus"
                                     value="#{NotaFiscalControle.motivoAlteracaoStatus}"
                                     style="width: 100%"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%" style="text-align: center;margin: 16px 0;">
                <a4j:commandLink id="btnAlterarStatusNotasSelecionadas"
                                 value="Alterar Status"
                                 reRender="form:panelENotas"
                                 styleClass="botoes nvoBt"
                                 action="#{NotaFiscalControle.alterarStatusNotasSelecionadas}"
                                 oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete}"/>
            </h:panelGrid>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<%--
    Document   : include_top_datahora
    Created on : 03/11/2011, 18:55:00
    Author     : Waller
--%>

<%@page import="negocio.comuns.utilitarias.Calendario"%>
<%@page import="java.util.Date"%>
<%@page import="java.util.Calendar" %>
<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="imports.jsp" %>

<c:set scope="page" var="diffTime" value="<%=(session.getLastAccessedTime() + session.getMaxInactiveInterval() * 1000) - Calendar.getInstance().getTimeInMillis()%>"/>
<c:set scope="page" var="urlAfterLogout" value="${LogoutControle.URL}"/>

<style type="text/css">
    #minhaTabela td {
        background-color: transparent;
    }
</style>

<jsp:include page="websocket/include_websocket.jsp" flush="true"/>
<jsp:include page="notificacao/include_modais_notificacao.jsp" flush="true"/>

<tr>
    <td height="32" colspan="3" align="right" valign="top" class="text" style="padding-top:13px;">
        <h:panelGroup id="horaSistema" layout="block">

            <rich:modalPanel domElementAttachment="parent" id="panelNotificacao"
                             showWhenRendered="#{NotificadorServiceControle.apresentarNotificacao}"
                             autosized="true" shadowOpacity="true" width="300" height="30">
                <h:panelGroup layout="block" styleClass="headerIndex">

                    <h:panelGrid id="minhaTabela" columns="1" width="100%" style="text-align: center;">

                        <h:panelGrid>
                            <h:outputText styleClass="titulo2"  style="color:red;" value="Atenção!"/>
                            <h:outputText styleClass="text" value="#{NotificadorServiceControle.notificacaoAtual.descricao}"/>
                            <h:outputText styleClass="text" style="font-weight:bold;"
                                          value="Tempo restante: ~ #{NotificadorServiceControle.notificacaoAtual.minutosRestante} minutos."/>
                            <rich:spacer height="5px"/>
                            <a4j:commandButton value="OK"
                                               status="statusHora"
                                               image="/imagens/botoesCE/OK.png"
                                               onclick="Richfaces.hideModalPanel('panelNotificacao');">
                            </a4j:commandButton>

                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>
            </rich:modalPanel>

            <h:commandLink rendered="#{SuporteControle.desv}" onclick="abrirPopup('robocontrol.jsp', 'RoboControl', 654, 300);"
                           style="margin-left:5px;cursor:pointer;">
                <i class="fa-icon-calendar" style="color:grey"></i>
            </h:commandLink>
            <h:commandLink rendered="#{!SuporteControle.desv}"
                           style="margin-left:5px;">
                <i class="fa-icon-calendar" style="color:grey"></i>
            </h:commandLink>
            <c:if test="${!SuperControle.menuZwUi}">
                <h:outputText id="dataSistema" style="font-weight:bold; margin-top: 2px"
                              value="#{SuporteControle.dataAtual}"/>
            </c:if>
            <c:if test="${!SuperControle.menuZwUi}">
                <h:outputText id="tempo"
                              title="Tempo Restante de sua Sessão. Para sua segurança, depois deste tempo, você será desconectado automaticamente."
                              style="font-weight:bold;color:red;"/>
            </c:if>
            <h:graphicImage rendered="#{LoginControle.apresentarIconeExecucaoRoboBackground}"
                            title="Estatísticas estão sendo atualizadas..."
                            style="margin-left:5px;vertical-align:middle;" url="/images/system-software-update.png"/>
        </h:panelGroup>

        <a4j:poll id="updateHora" interval="60000" status="statusHora"
          enabled="#{fn:contains(pagina, 'gestaoRemessas') ? false : true}"
          reRender="horaSistema"
          process="@this"
          action="#{SuperControle.poll}" limitToList="true"/>

        <a4j:poll id="updateCompleto" interval="323000" status="statusHora"
          enabled="#{fn:contains(pagina, 'gestaoRemessas') ? false : true}"
          reRender="nrMsgLidas"
          process="@this"
          action="#{SuperControle.pollCompleto}" limitToList="true"/>
        
        <rich:modalPanel domElementAttachment="parent" id="panelRenovarSessao"
                         width="350"                         
                         autosized="true" shadowOpacity="true">
            <h:panelGroup layout="block" styleClass="headerIndex">
                <h:panelGrid id="tblRenovarSessao" columns="1" width="100%" style="text-align: center;">
                    <h:panelGrid columnClasses="colunaCentralizada" width="100%">                        
                        <h:graphicImage url="/imagens/clock_blue.png"/>
                        <h:outputText style="font-weight:bold;" styleClass="text" value="Para sua segurança, sua sessão finalizará em: "/>
                        <h:outputText style="font-weight:bold;font-size:24px;color:red;" id="regressiveTime" styleClass="text" value=""/>
                        <rich:spacer height="5px"/>
                        <a4j:commandButton value="Renovar"
                                           status="statusHora"
                                           title="Clique aqui apra renovar sua sessão"
                                           reRender="horaSistema"
                                           action="#{SuporteControle.poll}"
                                           oncomplete="resetTime(tempoEmMillis);#{SuperControle.enableSetLastActionTime ? 'setLastActionTime();' : ''}Richfaces.hideModalPanel('panelRenovarSessao');"
                                           image="/imagens/refresh.png">
                        </a4j:commandButton>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGroup>
        </rich:modalPanel>

        <c:if test="${!SuperControle.desv and SuperControle.enableCountDown}">
            <script type="text/javascript"  language=JavaScript>
                diferenca = ${diffTime};
                dataFinal = new Date(agora.getTime() + diferenca);
                urlLogoutRedirect = "${urlAfterLogout}";
                startCountdown();
            </script>
        </c:if>

    </td>
</tr>

<%
        String ipaddress = request.getHeader("HTTP_CLIENT_IP");

        if (ipaddress == null) {
            ipaddress = request.getRemoteAddr();
        }

        request.getSession().setAttribute("RemoteAddr", ipaddress);

    %>
    <a4j:jsFunction status="statusHora" name="enviarIp" action="#{SuperControle.enviar}">
        <a4j:actionparam name="tmpIP" assignTo="#{SuperControle.ip}"/>
    </a4j:jsFunction>

    <a4j:jsFunction status="statusHora" name="enviarBrowser" action="#{SuperControle.enviarBrowser}">
        <a4j:actionparam name="tmpBrowser" assignTo="#{SuperControle.browser}"/>
        <a4j:actionparam name="tmpW" assignTo="#{SuperControle.widthScreenClient}"/>
        <a4j:actionparam name="tmpH" assignTo="#{SuperControle.heightScreenClient}"/>
        <a4j:actionparam name="protocol" assignTo="#{SuperControle.protocol}"/>        
    </a4j:jsFunction>
    
    <script type="text/javascript">
        function load() {
            enviarBrowser(navigator.userAgent, screen.availWidth,
            screen.availHeight, document.location.protocol, document.location.origin);
            submitIp();
        }
        function submitIp() {
            urlGetIp = "https://app.pactosolucoes.com.br/ip/v2.php";
            rtype = "text";            
            var protocolo = document.location.protocol.toString();            
            if (protocolo.indexOf("https", 0) != -1){
                urlGetIp = "https://app.pactosolucoes.com.br/ip/v2.php";
            }           
            jQuery.ajax({
                type: "GET",
                url: urlGetIp,
                dataType: rtype,
                success: function (valor) {
                    if (rtype == "jsonp") {
                        enviarIp(valor.ip);
                    } else {
                        enviarIp(valor);
                    }
                }
            });
        }
    </script>

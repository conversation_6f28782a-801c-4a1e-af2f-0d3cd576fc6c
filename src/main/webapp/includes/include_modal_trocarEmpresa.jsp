<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 30/06/2015
  Time: 14:43
  To change this template use File | Settings | File Templates.
--%>
<style type="text/css">
     .sink {
        display: inline-block;
        -webkit-transition: .3s ease-out;
        -moz-transition: all .3s ease-out;
        -ms-transition: all .3s ease-out;
        -o-transition: all .3s ease-out;

    }

    .sink:hover {
        -webkit-transform: translateX(5px);
        -moz-transform: translateX(5px);
        -ms-transform: translateX(5px);
        -o-transform: translateX(5px);
        transform: translateX(5px);
    }

</style>

<%--INICIO TROCA EMPRESA--%>
<c:if test="${InicioControle.usuarioLogado.senha != '88f2f8d383d38e72184852b185825965a566b30c8a167e5bf13774bdeaaa0718'&& InicioControle.usuarioMultiEmpresa }">
    <li>
        <rich:dropDownMenu  popupWidth="10">
            <f:facet name="label">
                <h:panelGroup>
                    <i class="fa-icon-exchange fa-icon-2x"></i>
                </h:panelGroup>
            </f:facet>
            <rich:menuItem submitMode="none" icon="/faces/imagens/trocaDeEmpresa-circ-ativ.svg"  >
                <h:outputText
                        style="cursor:auto;color:rgba(94, 100, 105, 0.89);  margin-left: 0px;vertical-align: top;"
                        value="#{InicioControle.empresaLogado.nome}"
                        >

                </h:outputText>
            </rich:menuItem>
            <c:forEach varStatus="index" items="#{InicioControle.usuarioLogado.usuarioPerfilAcessoVOs}" var="perfil">
                <c:if test="${perfil.empresa.codigo != InicioControle.empresaLogado.codigo}">
                    <rich:menuItem submitMode="none" icon="/faces/imagens/trocaDeEmpresa-circ-inativ.svg" iconStyle="width:6px;height:6px;" >
                        <a4j:commandLink
                                style="color:#2D4C6B;  margin-left: 0px;vertical-align: top;"
                                value="#{  perfil.empresa.nome}"
                                action="#{InicioControle.confirmarTrocaEmpresa}"
                                styleClass="sink">
                            <f:setPropertyActionListener value="#{perfil.empresa.codigo}"
                                                         target="#{InicioControle.codigoEmpresa}"/>
                        </a4j:commandLink>
                    </rich:menuItem>

                </c:if>
            </c:forEach>
        </rich:dropDownMenu>
    </li>
</c:if>
<%--FIM TROCA EMPRESA--%>
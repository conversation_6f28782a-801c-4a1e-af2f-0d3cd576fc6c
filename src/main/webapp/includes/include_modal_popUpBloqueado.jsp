<%-- 
    Document   : include_modal_popUpBloqueado
    Created on : 20/07/2017, 17:45:11
    Author     : <PERSON><PERSON><PERSON><PERSON>uza
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="imports.jsp" %>


<rich:modalPanel id="modalPopUpBloqueado" autosized="true" styleClass="novaModal noMargin" 
                 shadowOpacity="true" top="20"> <%--showWhenRendered="true"  width="800" height="600"--%>
    <f:facet name="header">
        <h:panelGroup id="pnlAtencaoModalBloqueado">
            <h:graphicImage style="margin: 0 0 -5px 0" value="/imagens/atencao.png"/>
            <rich:spacer width="5" />
            <h:outputText value="Atenção! Pop-up bloqueado."/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkmodalPopUpBloqueado"/>
            <rich:componentControl for="modalPopUpBloqueado" attachTo="hidelinkmodalPopUpBloqueado" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <div id="imagem_divInterno_modalPopUpBloqueado"></div>
</rich:modalPanel>

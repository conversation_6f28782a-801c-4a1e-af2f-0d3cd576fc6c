<%-- 
    Document   : include_menubotoesfixos
    Created on : 20/07/2011, 18:13:41
    Author     : waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<link href="css_pacto.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="includes/menus/menubotoes.css" type="text/css" />
<%@include file="../imports.jsp" %>

<%-- LEMBRE-SE DE QUE OS CONTROLES DEVEM FUNCIONAR EM INTERNET EXPLORER E OUTROS NAVEGADORES--%>
<c:choose>
    <c:when test="${SuperControle.internetExplorer}">

        <h:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber}" image="images/btn_voltar.gif"
                         action="#{ContratoControle.voltar}"/>

        <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber && !ContratoControle.temDadosObrigatoriosPendentes}" image="images/btn_fecharnegociacao2.gif"
                           id="botaoConfirmarcao" action="#{ContratoControle.validarDadosContrato}"
                           reRender="form:panelProdutoParcela, form:panelMesangem, form:planoCondicaoPagamentoVO,form:contratoVOPlanoProdutoSugerido ,form:contratoVO6 , form:movProduto , form:turma, form:modalidade"
                           oncomplete="#{ContratoControle.abriPanelUsuarioSenha}"/>



        <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber && !ContratoControle.temDadosObrigatoriosPendentes}" image="images/btn_fecharereceber.gif"
                           id="botaoPagar"
                           action="#{ContratoControle.validarDadosContrato}"
                           reRender="form:panelProdutoParcela, form:panelMesangem,
                           form:planoCondicaoPagamentoVO,form:contratoVOPlanoProdutoSugerido,
                           form:contratoVO6 , form:movProduto , form:turma, form:modalidade"
                           oncomplete="#{ContratoControle.abriRichModalPanelConfimacaoPagamento}"/>

        <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber && ContratoControle.temDadosObrigatoriosPendentes}"
                         id="botaoConcluirCadastro"
                         image="images/btn_concluir_cadastro.jpg"
                         action="#{ContratoControle.validarDadosContratoIrCadastroCliente}"/>
    </c:when>
    <c:otherwise>
        <div id="ToolbarZillyonWeb" style="display: block; margin-bottom: 0px; ">
            <div id="globalZillyonWeb">
                <div id="logoZillyonWeb" style="display: block; "></div>

                <div class="menubotoesfixo">
                    <ul  id="css3menu1" class="topmenu">
                        <li class="topfirst">

                            <a4j:commandLink id="voltar" rendered="#{ContratoControle.apresentarBotoesFecharEReceber}"  style="height:36px;line-height:36px;"
                                           action="#{ContratoControle.voltar}">
                                <img src="includes/menus/back.png"/>
                                Voltar
                            </a4j:commandLink>

                        </li>
                        <li>
                            <a4j:commandLink rendered="#{ContratoControle.apresentarBotoesFecharEReceber && !ContratoControle.temDadosObrigatoriosPendentes}"  
                                             style="height:36px;line-height:36px;"
                                             id="botaoConfirmarcao" action="#{ContratoControle.validarDadosContrato}"
                                             reRender="form:panelProdutoParcela, form:panelMesangem, form:planoCondicaoPagamentoVO,form:contratoVOPlanoProdutoSugerido ,form:contratoVO6 , form:movProduto , form:turma, form:modalidade"
                                             oncomplete="#{ContratoControle.abriPanelUsuarioSenha}">
                                <img src="includes/menus/favour.png"/>
                                Fechar Negociação
                            </a4j:commandLink>

                        </li>
                        <li class="toplast">
                            <a4j:commandLink rendered="#{ContratoControle.apresentarBotoesFecharEReceber && !ContratoControle.temDadosObrigatoriosPendentes}"  style="height:36px;line-height:36px;"
                                             id="botaoPagar"
                                             action="#{ContratoControle.validarDadosContrato}"
                                             reRender="form:panelProdutoParcela, form:panelMesangem,
                                             form:planoCondicaoPagamentoVO,form:contratoVOPlanoProdutoSugerido,
                                             form:contratoVO6 , form:movProduto , form:turma, form:modalidade"
                                             oncomplete="#{ContratoControle.abriRichModalPanelConfimacaoPagamento}">
                                <img src="images/money.png" border="0"/>
                                Fechar e Receber
                            </a4j:commandLink>
                            <a4j:commandLink rendered="#{ContratoControle.apresentarBotoesFecharEReceber && ContratoControle.temDadosObrigatoriosPendentes}"  style="height:36px;line-height:36px;"
                                             id="botaoConcluirCadastro"
                                             action="#{ContratoControle.validarDadosContratoIrCadastroCliente}">
                                <img src="images/cadastro_cliente.png" border="0"/>
                                Concluir cadastro cliente
                            </a4j:commandLink>
                        </li>
                    </ul>
                </div>


            </div>

        </div>
    </c:otherwise>

</c:choose>
<%-- 
    Document   : include_menubotoesfixos
    Created on : 20/07/2011, 18:13:41
    Author     : waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<link href="css_pacto.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="includes/menus/menubotoes.css" type="text/css" />
<%@include file="../imports.jsp" %>

<%-- LEMBRE-SE DE QUE OS CONTROLES DEVEM FUNCIONAR EM INTERNET EXPLORER E OUTROS NAVEGADORES--%>
<c:choose>

    <c:when test="${SuperControle.internetExplorer}">

        <h:panelGroup id="panelBotoesControle"
                      layout="block">
            <c:if test="${MovPagamentoControle.apresentarBotaoRecibo}">
                <a4j:commandButton
                        actionListener="#{ReciboControle.prepareRecibo}"
                    action="#{ReciboControle.imprimirReciboPDF}"
                    image="imagens/imprimir_recibo.png"
                    oncomplete="abrirPopupPDFImpressao('relatorio/#{MovPagamentoControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);"/>

            </c:if>


            <a4j:commandButton rendered="#{MovPagamentoControle.apresentarBotaoRecibo && MovPagamentoControle.movPagamentoVO.pessoa.codigo > 0}"
                               image="imagens/enviar_recibo.png"
                               action="#{ReciboControle.enviarReciboPagamento}"
                               onclick="if(!confirm('Deseja enviar o e-mail?')) {return false;}"
                               id="enviarRecibo"
                               reRender="mensagemSup, mensagemInf, panelConfirmacao"
                               actionListener="#{ReciboControle.prepareReciboEmail}">
                <f:attribute name="pessoaVO"
                                                 value="#{MovPagamentoControle.movPagamentoVO.pessoa}" />

            </a4j:commandButton>

            <c:if test="${MovPagamentoControle.apresentarBotoesControle}">
                <c:if test="${modulo eq 'zillyonWeb'}">
                    <h:commandButton
                        action="#{MovPagamentoControle.voltar}"
                        image="images/btn_cancelar2.gif"/>

                </c:if>
                <c:if test="${modulo eq 'centralEventos'}">
                    <h:commandButton
                        image="images/btn_cancelar2.gif"
                        actionListener="#{CadastroInicialControle.selCancelarPagamento}"
                        action="#{CadastroInicialControle.abrirDetalhamento}">
                        <!-- Entidade.AMBIENTE -->
                        <f:attribute name="codigoContrato"
                                     value="#{MovPagamentoControle.codigoContratoEvento}" />
                    </h:commandButton>



                </c:if>
            </c:if>

            <a4j:commandButton id="btnConfirmar"
                               rendered="#{MovPagamentoControle.apresentarBotoesControle &&
                                           !MovPagamentoControle.movPagamentoVO.usarPagamentoAprovaFacil}"
                               image="images/btn_confirmar2.gif"
                               reRender="escolhaFormaPagamento, totalLancado, residuo, mensagemSup, mensagemInf,mensagem1, mensagem2, geralnovopagamento"
                               action="#{MovPagamentoControle.validarDadosPagamento}"
                               value="Confirmar"
                               oncomplete="#{MovPagamentoControle.abrirRichModalConfirmaPagamento}"/>



            <a4j:commandButton id="botaoConfirmacaoAPF"
                               image="images/btn_confirmar2.gif"
                               value="Confirmar"
                               reRender="form, panelUsuarioSenha"
                               rendered="#{MovPagamentoControle.apresentarBotoesControle && MovPagamentoControle.movPagamentoVO.usarPagamentoAprovaFacil}"
                               oncomplete="#{PagamentoCartaoCreditoControle.mensagemNotificar};#{PagamentoCartaoCreditoControle.onComplete}"
                               action="#{PagamentoCartaoCreditoControle.confirmar}"
                               tabindex="13"/>


            <a4j:commandButton rendered="#{MovPagamentoControle.apresentarBotaoImprimirContrato}"
                               image="imagens/imprimir_contrato.png"
                               action="#{MovPagamentoControle.gerarImpressaoContrato}"
                               oncomplete="abrirPopup('faces/VisualizarContrato', 'RelatorioContrato', 730, 545);"/>


            <a4j:commandButton id="enviarContrato"
                               rendered="#{MovPagamentoControle.apresentarBotaoImprimirContrato}"
                               image="imagens/enviar_contrato.png"
                               action="#{MovPagamentoControle.prepararEnvioContratoPorEmail}"
                               oncomplete="#{MovPagamentoControle.mensagemNotificar}#{MovPagamentoControle.msgAlert}"
                               reRender="mensagemSup, mensagemInf, panelConfirmacao, modalEnviarContratoEmail"
                               value="E-mail"/>

        </h:panelGroup>
    </c:when>
    <c:otherwise>
        <div  style="display: block; margin-bottom: 0px; " class="barraFixaClean">

            <div id="globalZillyonWeb">
                <h:panelGroup id="panelBotoesControle"
                              layout="block">
                    <div class="menubotoesfixo">
                        <ul id="css3menu1" class="topmenu" style="background-color: #fff">
                            <c:if test="${!MovPagamentoControle.processandoOperacao && !PagamentoCartaoDebitoOnlineControle.processandoOperacao  && !PagamentoCartaoCreditoControle.processandoOperacao}">
                                <li class="topfirst">
                                        <c:if test="${modulo eq 'centralEventos'}">
                                           <h:commandLink style="height:36px;line-height:36px;" rendered="#{MovPagamentoControle.apresentarBotoesControle}"
                                                   action="#{NavegacaoControle.abrirTelaCaixaEmAberto}">
                                               <i class="fa-icon-arrow-left fa-icon-2x" style=" vertical-align: middle;"></i>
                                                    Voltar
                                                    </h:commandLink>

                                        </c:if>



                                </li>
                                <li>

                                    <c:if test="${MovPagamentoControle.apresentarBotaoRecibo}">
                                        <a4j:commandLink
                                                actionListener="#{ReciboControle.prepareRecibo}"
                                                action="#{ReciboControle.imprimirReciboPDF}"
                                                style="height:36px;line-height:36px;"
                                                oncomplete="abrirPopupPDFImpressao('relatorio/#{MovPagamentoControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);" >
                                        <i class="fa-icon-print fa-icon-2x" style=" vertical-align: middle;"></i>Imprimir Recibo
                                        </a4j:commandLink>
                                    </c:if>

                                <li>
                                <a4j:commandLink rendered="#{MovPagamentoControle.apresentarBotaoRecibo && MovPagamentoControle.movPagamentoVO.pessoa.codigo > 0}"
                                                 style="height:36px;line-height:36px;"
                                                 oncomplete="#{ReciboControle.mensagemNotificar}#{ReciboControle.msgAlert}"
                                                 id="enviarRecibo"
                                                 reRender="mensagemSup, mensagemInf, panelConfirmacao, modalEnviarContratoEmail"
                                                 actionListener="#{ReciboControle.prepararModalEnvioReciboPorEmailPagamento}">
                                    <f:attribute name="pessoaVO" value="#{MovPagamentoControle.movPagamentoVO.pessoa}" />
                                    <i class="fa-icon-paper-plane fa-icon-2x" style=" vertical-align: middle;"></i>Enviar Recibo
                                </a4j:commandLink>
                                </li>
                                <c:if test="${not empty MovPagamentoControle.pinpad.retorno}">
                                    <li>
                                        <a style="height:36px;line-height:36px;" onclick="openWin('${MovPagamentoControle.pinpad.retorno}')">
                                            <i class="fa-icon-print fa-icon-2x" style=" vertical-align: middle;"></i>Comprovante Pinpad
                                        </a>
                                    </li>
                                </c:if>

                                <li>

                                    <a4j:commandLink id="btnImprimirContrato" rendered="#{MovPagamentoControle.apresentarBotaoImprimirContrato}" style="height:36px;line-height:36px;"
                                                     action="#{MovPagamentoControle.gerarImpressaoContrato}"
                                                     oncomplete="abrirPopup('faces/VisualizarContrato', 'RelatorioContrato', 730, 545);">
                                        <i class="fa-icon-print fa-icon-2x" style=" vertical-align: middle;"></i>
                                        Imprimir Contrato
                                    </a4j:commandLink>
                                </li>
                                 <li>

                                    <a4j:commandLink rendered="#{MovPagamentoControle.apresentarBotaoImprimirContratoServico}" style="height:36px;line-height:36px;"
                                                     action="#{MovPagamentoControle.prepararVendaAvulsa}"
                                                     reRender="panelIncludeMensagem"
                                                oncomplete="#{MovPagamentoControle.mostrarRichModalPanelContratoPrestacaoServico}">
                                        <i class="fa-icon-print fa-icon-2x" style=" vertical-align: middle;"></i>
                                        Imprimir Contrato
                                    </a4j:commandLink>
                                </li>
                                <li>

                                    <a4j:commandLink rendered="#{MovPagamentoControle.apresentarBotaoImprimirContrato}" style="height:36px;line-height:36px;"
                                                     action="#{MovPagamentoControle.prepararEnvioContratoPorEmail}"
                                                     oncomplete="#{MovPagamentoControle.mensagemNotificar}#{MovPagamentoControle.msgAlert}"
                                                     id="btnEnviarContrato"
                                                     reRender="mensagemSup, mensagemInf, panelConfirmacao, modalEnviarContratoEmail">
                                        <i class="fa-icon-paper-plane fa-icon-2x" style=" vertical-align: middle;"></i>
                                        Enviar Contrato
                                    </a4j:commandLink>

                                </li>

                                <li>
                                    <c:if test="${MovPagamentoControle.apresentarBotoesControle}">
                                        <c:if test="${modulo eq 'zillyonWeb'}">
                                            <a4j:commandLink id="btnCancelar" action="#{MovPagamentoControle.voltar}" style="height:36px;line-height:36px;" >
                                                <i class="fa-icon-remove-sign fa-icon-2x" style=" vertical-align: middle;"></i>
                                                Cancelar
                                            </a4j:commandLink>
                                        </c:if>
                                        <c:if test="${modulo eq 'centralEventos'}">
                                            <h:commandLink style="height:36px;line-height:36px;"
                                                           actionListener="#{CadastroInicialControle.selCancelarPagamento}"
                                                           action="#{CadastroInicialControle.abrirDetalhamento}">
                                                <!-- Entidade.AMBIENTE -->
                                                <f:attribute name="codigoContrato"
                                                             value="#{MovPagamentoControle.codigoContratoEvento}" />
                                                <i class="fa-icon-remove-sign fa-icon-2x" style=" vertical-align: middle;"></i>
                                                Cancelar

                                            </h:commandLink>

                                        </c:if>
                                    </c:if>
                                </li>


                                <li>

                                    <a4j:commandLink id="btnConfirmar"
                                                     rendered="#{MovPagamentoControle.apresentarBotoesControle && !MovPagamentoControle.movPagamentoVO.usarPagamentoAprovaFacil && !MovPagamentoControle.movPagamentoVO.usarPagamentoDebitoOnline}"
                                                     style="height:36px;line-height:36px;"
                                                     reRender="panelAutorizacaoFuncionalidade, escolhaFormaPagamento, totalLancado, residuo, mensagemSup,
                                                                mensagemInf,mensagem1, mensagem2, form:containerTotalizadores"
                                                     action="#{MovPagamentoControle.confirmarPermissaoUsuario}"
                                                     styleClass="botaoPrimario texto-cor-branco"
                                                     oncomplete="#{MovPagamentoControle.mensagemNotificar};#{MovPagamentoControle.apresentarMensagem};">
                                        <i class="fa-icon-ok-sign fa-icon-2x" style=" vertical-align: middle;"></i>Confirmar
                                    </a4j:commandLink>


                                    <!-- ------------------------ BOTOES -------------------------------- -->
                                    <a4j:commandLink id="botaoConfirmacaoAPF"
                                                     style="height:36px;line-height:36px;"
                                                     reRender="form:geralnovopagamento, formUsuarioSenha"
                                                     rendered="#{MovPagamentoControle.apresentarBotoesControle && MovPagamentoControle.movPagamentoVO.usarPagamentoAprovaFacil}"
                                                     oncomplete="#{PagamentoCartaoCreditoControle.mensagemNotificar};#{PagamentoCartaoCreditoControle.onComplete}"
                                                     action="#{PagamentoCartaoCreditoControle.confirmar}"
                                                     styleClass="botaoPrimario texto-cor-branco"
                                                     tabindex="13">
                                        <i class="fa-icon-ok-sign fa-icon-2x" style=" vertical-align: middle;"></i>Confirmar
                                    </a4j:commandLink>

                                    <a4j:commandLink id="botaoConfirmacaoDebitoCielo"
                                                     style="height:36px;line-height:36px;"
                                                     reRender="form, panelUsuarioSenhaDebito"
                                                     rendered="#{MovPagamentoControle.apresentarBotoesControle && MovPagamentoControle.movPagamentoVO.usarPagamentoDebitoOnline}"
                                                     oncomplete="#{PagamentoCartaoDebitoOnlineControle.mostrarConfirmacaoUsuario}"
                                                     action="#{PagamentoCartaoDebitoOnlineControle.confirmar}"
                                                     styleClass="botaoPrimario texto-cor-branco"
                                                     tabindex="13">
                                        <i class="fa-icon-ok-sign fa-icon-2x" style=" vertical-align: middle;"></i>Confirmar
                                    </a4j:commandLink>


                                </li>

                            </c:if>
                            <c:if test="${MovPagamentoControle.processandoOperacao || PagamentoCartaoDebitoOnlineControle.processandoOperacao || PagamentoCartaoCreditoControle.processandoOperacao}">
                                <li>
                                    <label>
                                            <span style="font-weight: bold; font-size: 9pt; margin-top: 10; margin-right: 5; color: #d14 ;">
                                                Operação já está sendo processada. Dentro de alguns instantes, atualize a página para verificar se operação já foi concluída.  
                                            </span>
                                        </label>
                                </li>     
                                <li>
                                    <a4j:commandLink onclick="window.location.reload();" id="atualizar" title="Atulaizar"  styleClass="botaoPrimario texto-cor-branco" >
                                                <i class="fa-icon-refresh fa-icon-2x" style=" vertical-align: middle;"></i>
                                                Atualizar
                                            </a4j:commandLink>
                                </li>        
                            </c:if>
                       

                        </ul>
                    </div>
                </h:panelGroup>
            </div>
        </div>
    </c:otherwise>

</c:choose>


<%-- 
    Document   : include_menubotoesfixos
    Created on : 20/07/2011, 18:13:41
    Author     : waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<link rel="stylesheet" href="${root}/includes/menus/menubotoes.css" type="text/css" />
<%@include file="../imports.jsp" %>


<%-- LEMBRE-SE DE QUE OS CONTROLES DEVEM FUNCIONAR EM INTERNET EXPLORER E OUTROS NAVEGADORES--%>
<c:choose>
    <c:when test="${SuperControle.internetExplorer}">

        <c:if test="${modulo eq 'zillyonWeb'}">
            <a4j:commandLink id="btnCancelarParcelasIESuperior" title="Cancelar Parcelas" style="height:36px;line-height:36px; "
                             action="#{MovParcelaControle.validarCancelarParcelas}"
                             oncomplete="#{MovParcelaControle.mensagemNotificar};Richfaces.showModalPanel(#{MovParcelaControle.abrirRichConfirmacaoCancelamento ? 'mdlJustificativa' : ''})"
                             reRender="mensagem, mdlJustificativa, formBoletoPendente">
                <img src="${root}/images/btn_cancelarParcelaG.png" class="imgalpha" title="Cancelar Parcelas">
            </a4j:commandLink>
            <a4j:commandLink id="btnReceberParcelasIESuperior"
                             action="#{MovParcelaControle.validarListaParcelasPagarView}"
                             reRender="formBoletoPendente">
                <img src="${root}/images/btn_receber.gif" class="imgalpha" title="Receber">
            </a4j:commandLink>
        </c:if>
        <c:if test="${modulo eq 'centralEventos'}">
            <h:commandLink action="#{MovParcelaControle.validarListaParcelasPagarCE}">
                <img src="${root}/imagens/botoesCE/receber.png" border="0" class="imgalpha" title="Receber">
            </h:commandLink>
        </c:if>

    </c:when>
    <c:otherwise>
        <div style="display: block; margin-bottom: 0px; " class="barraFixaClean">
            <div id="globalZillyonWeb">
                <div class="menubotoesfixo">
                   <ul id="css3menu1" class="topmenu">
                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <li class="toplast">


                                <a4j:commandLink id="btnCancelarCaixaAbertos" title="Cancelar Parcelas" style="height:36px;line-height:36px; "
                                                 action="#{MovParcelaControle.validarCancelarParcelas}"
                                                 oncomplete="#{MovParcelaControle.msgAlert};#{MovParcelaControle.mensagemNotificar};Richfaces.showModalPanel(#{MovParcelaControle.abrirRichConfirmacaoCancelamento ? 'mdlJustificativa' : ''})"
                                                 reRender="mensagem, mdlJustificativa, formBoletoPendente">
                                    <i class="fa-icon-remove fa-icon-2x" style=" vertical-align: middle;"></i>
                                    Cancelar Parcelas
                                </a4j:commandLink>


                            </li>
                        </c:if>
                        <li class="topfirst" style="height:40px;">

                            <table style="border: none;display: table" class="font-size-Em"
                                   border="0" cellspacing="0" cellpadding="0" height="100%">
                                <tr style="background-color: #DDE7E7;;">
                                    <td height="100%" align="right" valign="middle" style="padding:0px 5px;">
                                        <label>
                                            <span style="font-weight: bold; font-size: 9pt;">
                                                Responsável Pagamento:
                                            </span>
                                        </label>
                                    </td>
                                    <td height="100%" align="left" valign="middle">
                                        <h:panelGroup layout="block" styleClass="cb-container h30px" style="line-height: 30px;height:30px;">
                                            <h:selectOneMenu id="responsavel"  style="line-height: 30px;height:30px;" value="#{MovParcelaControle.numeroContratoResponsavel}" styleClass="form" onfocus="focusinput(this);" onblur="blurinput(this);">
                                                <a4j:support event="onchange" ajaxSingle="true"
                                                         action="#{MovParcelaControle.selecionarResponsavelPagamento}"
                                                         reRender="responsavel, responsavelAbaixo, gerarBoleto, renegociarParcelas"/>
                                             <f:selectItems value="#{MovParcelaControle.listaResponsavelPagamento}"/>
                                            </h:selectOneMenu>
                                        </h:panelGroup>
                                    </td>
                                </tr>
                            </table>

                        </li>

                        <li>                           

                            <a4j:commandLink style="height:36px;line-height:36px;">
                                <h:outputText id="valorTotalParcela" styleClass="verde" value="Total #{MovParcelaControle.empresaLogado.moeda} #{MovParcelaControle.valorTotalParcela_Apresentar}"/>
                            </a4j:commandLink>


                        </li>

                        <li>
                            <h:panelGroup layout="block" id="gerarBoleto">
                                <a4j:commandLink style="height:36px;line-height:36px;"
                                                 id="btnGerarBoletoCaixa"
                                                 rendered="#{MovParcelaControle.existeAutorizacaoBoleto || MovParcelaControle.existeConvenioCobrancaBoleto}"
                                                 oncomplete="#{BoletoBancarioControle.mensagemNotificar}"
                                                 action="#{BoletoBancarioControle.inicializarTela}"
                                                 reRender="form:panelMensagemErro">
                                    <i class="fa-icon-shopping-cart fa-icon-2x" style="vertical-align: middle;"></i>
                                    <h:outputText value="Gerar Boleto"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </li>

                        <li class="toplast">


                            <c:if test="${modulo eq 'zillyonWeb'}">
                                <a4j:commandLink id="btnReceberCaixaAberto" title="Receber" style="height:36px;line-height:36px;"
                                                 action="#{MovParcelaControle.validarListaParcelasPagarView}"
                                                 reRender="formBoletoPendente"
                                                 oncomplete="#{MovParcelaControle.msgAlert eq '' ? MovParcelaControle.mensagemNotificar : MovParcelaControle.msgAlert}">
                                    <i class="fa-icon-money fa-icon-2x" style="vertical-align: middle;"></i>
                                    Receber
                                </a4j:commandLink>
                            </c:if>
                            <c:if test="${modulo eq 'centralEventos'}">
                                <a4j:commandLink id="btnReceberCaixaAberto" title="Receber" style="height:36px;line-height:36px;" oncomplete="#{MovParcelaControle.mensagemNotificar}"
                                               action="#{MovParcelaControle.validarListaParcelasPagarCE}">
                                    <i class="fa-icon-money fa-icon-2x" style="vertical-align: middle;"></i>
                                    Receber
                                </a4j:commandLink>
                            </c:if>

                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </c:otherwise>

</c:choose>

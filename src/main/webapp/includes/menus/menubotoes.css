/* 
    Document   : menubotoes
    Created on : 15/07/2011, 15:51:51
    Author     : waller
    Description:
        Purpose of the stylesheet follows.
*/

/* 
   TODO customize this sample style
   Syntax recommendation http://www.w3.org/TR/REC-CSS2/
*/

div.menubotoesfixo {
    margin: 4px;
    font-size: 80% /*smaller*/;
    font-weight: bold;
    line-height: 1.1;
    text-align: right;
    vertical-align: middle ;
    position: fixed;
    /*bottom: 0;*/
    right: 4.25em;
    /*width: 15em;
    right: 5em;*/
}

div.menubotoesrelativo {
    margin: 0 0 0 0;
    font-size: 80% /*smaller*/;
    font-weight: bold;
    line-height: 1.1;
    text-align: right;
    vertical-align: middle ;
    position: relative;    
}

ul#css3menu1,ul#css3menu1 ul{
    margin:0;list-style:none;padding:0;border-width:1px;border-style:solid;border-color:#5f5f5f;-moz-border-radius:5px;-webkit-border-radius:5px;border-radius:5px;}
ul#css3menu1 ul{
    display:none;position:absolute;left:0;top:100%;-moz-box-shadow:3.5px 3.5px 5px #000000;-webkit-box-shadow:3.5px 3.5px 5px #000000;box-shadow:3.5px 3.5px 5px #000000;padding:0 10px 10px;background-color:#ffffff;border-radius:6px;-moz-border-radius:6px;-webkit-border-radius:6px;border-color:#d4d4d4;}
ul#css3menu1 li:hover>*{
    display:block;}
ul#css3menu1 li:hover{
    position:relative;}
ul#css3menu1 ul ul{
    position:absolute;left:100%;top:0;}
ul#css3menu1{
    display:block;font-size:0;float:left;}
ul#css3menu1 li{
    display:block;white-space:nowrap;font-size:0;float:left;}
ul#css3menu1>li,ul#css3menu1 li{
    margin:0;}
ul#css3menu1 a:active, ul#css3menu1 a:focus{
    outline-style:none;}
ul#css3menu1 a,ul#css3menu1 a.pressed{
    display:block;vertical-align:middle;text-align:left;text-decoration:none;font:bold 14px Trebuchet MS;color:#000000;text-shadow:#FFF 0 0 1px;cursor:pointer;}
ul#css3menu1 ul li{
    float:none;margin:10px 0 0;}
ul#css3menu1 ul a{
    text-align:left;padding:4px;background-color:#ffffff;background-image:none;border-width:0;border-radius:0px;-moz-border-radius:0px;-webkit-border-radius:0px;font:14px Tahoma;color:#000000;text-decoration:none;}
ul#css3menu1 li:hover>a{
    border-color:#C0C0C0;border-style:solid;font:bold 14px Trebuchet MS;color:#000000;text-decoration:none;text-shadow:#FFF 0 0 1px;background-image:url("mainbk.png");background-position:0 100px;}
ul#css3menu1 img{
    border:none;vertical-align:middle;margin-right:10px;}
ul#css3menu1 img.over{
    display:none;}
ul#css3menu1 li:hover > a img.def{
    display:none;}
ul#css3menu1 li:hover > a img.over{
    display:inline;}
ul#css3menu1 li a.pressed img.over{
    display:inline;}
ul#css3menu1 li a.pressed img.def{
    display:none;}
ul#css3menu1 span{
    display:block;overflow:visible;background-position:right center;background-repeat:no-repeat;padding-right:0px;}
ul#css3menu1 a{
    padding:2px;background-image:url("mainbk.png");background-repeat:repeat;background-position:0 0;border-width:0 0 0 1px;border-style:solid;border-color:#C0C0C0;color:#000000;text-decoration:none;text-shadow:#FFF 0 0 1px;}
ul#css3menu1 li:hover>a,ul#css3menu1 li:hover>div,ul#css3menu1 li>a.pressed{
    background-color:#f8ac00;background-image:url("mainbk.png");background-position:0 100px;color:#000000;text-decoration:none;text-shadow:#FFF 0 0 1px;}
ul#css3menu1 ul li:hover>a,ul#css3menu1 ul li>a.pressed{
    background-color:#ffffff;background-image:none;font:14px Tahoma;color:#0978b3;text-decoration:none;}
ul#css3menu1 li.topfirst>a{
    height:18px;line-height:18px;border-radius:5px 0 0 5px;-moz-border-radius:5px 0 0 5px;-webkit-border-radius:5px;-webkit-border-top-right-radius:0;-webkit-border-bottom-right-radius:0;}
ul#css3menu1 li.topfirst:hover>a,ul#css3menu1 li.topfirst>a.pressed{
    line-height:18px;}
ul#css3menu1 li.toplast>a{
    height:18px;line-height:18px;border-radius:0 5px 5px 0;-moz-border-radius:0 5px 5px 0;-webkit-border-radius:0;-webkit-border-top-right-radius:5px;-webkit-border-bottom-right-radius:5px;}
ul#css3menu1 li.toplast:hover>a,ul#css3menu1 li.toplast>a.pressed{
    line-height:18px;}

/*TOOLBAR*/

#ToolbarZillyonWeb{ padding:0; margin:0; left:0; right:0; font-family:Arial;}
#ToolbarZillyonWeb{
    background:transparent url(../../images/bg_menubotoes.png) repeat-x;
    /*background:transparent url(../../images/bg_menu.gif) repeat-x;*/
    display:none;
    height:50px;
    width:100%;
    background-position: 0 50%;
    position:fixed;
    bottom: 0;    
    z-index:99;
}


#ToolbarZillyonWeb #logoZillyonWeb{
    background:url(../../images/zw.png) no-repeat;
    width:100%;
    height:100%;
    margin:0px;
}
#formSearchToolbarZillyonWeb{
    width:70%; margin:0pt auto;
}
#ToolbarZillyonWeb #showHideZillyonWeb, #ToolbarZillyonWeb #logoZillyonWeb{
    float:left;
    /*cursor:pointer;*/
}
#ToolbarZillyonWeb #searchZillyonWeb label{
    padding:0 2px;
    color:#FFFFFF;
    font-size:11px;
}
#ToolbarZillyonWeb #searchZillyonWeb{
    display:block;
}
#ToolbarZillyonWeb #searchZillyonWeb #busca{
    width:99%;
    color:#666666;
    font-size:12px;
}

#ToolbarZillyonWeb #searchZillyonWeb{
    margin-top:2px;
}
#ToolbarZillyonWeb #btMaisVendidosZillyonWeb, #ToolbarZillyonWeb #btUltimosVisitadosZillyonWeb{
    width:106px;
    height:19px;
    display:none;
    cursor:pointer;
}

#layerCartZillyonWeb{
    position:absolute;
    right:1px;
    width:310px;
    top:36px;
    z-index:10000;
}


.contentCartZillyonWeb h1{
    color:#037FD0;
    font-size:11px;
    font-weight:bold;
    margin:8px 12px;
    font-family:Arial;
    text-align:left;
}
#box13B h1{
    color:#037FD0;
    font-size:12px;
    font-weight:bold;
    padding:4px 0;
    font-family:Arial;
    text-align:left;
}

.previewCartZillyonWeb{
    background-color:#e4e4e4;
    border-top:1px solid #e2e2e2;
    margin:0 6px;
    width:299px;
}
.previewCartZillyonWeb img{
    padding-top:5px;
    padding-bottom:3px;
}
.previewCartZillyonWeb a{
    outline:none;
}
#infoCartZillyonWeb{
    width:80px;
    padding:5px 43px;
}
#qtdCartZillyonWeb{
    font-size:12px;
}
#infoCartZillyonWeb span{
    color:#FFFFFF;
    font-size:11px;
}
#infoCartZillyonWeb strong{
    font-size:12px;
    color:#FFFFFF;
}
.containerCartZillyonWeb{
    display:none;
    margin:36px 0px;
    top:0;
    right:0;
    position:fixed !important;
    z-index:9999 !important;
}
.produtosZillyonWeb .text{
    text-decoration:none;
}
.produtosZillyonWeb a{
    text-decoration:none;
    color:#666666;
    font-size:11px;
}
.produtosZillyonWeb a:hover{
    text-decoration:underline;
}
.produtosZillyonWeb{
    width:292px;
    margin:0 13px;
    text-align:left;
}
.produtosZillyonWeb img{
    width:50px;
}
.produtosZillyonWeb .price strong, .produtosZillyonWeb tfoot{
    font-size:13px;
}
#box13B .addProductsToolbarZillyonWeb table{
    margin:0 -7px;
}
.produtosZillyonWeb .addProductsToolbarZillyonWeb table{
    width:93%;
}
.produtosZillyonWeb .price strong, .produtosZillyonWeb .price, .addProductsToolbarZillyonWeb strong{
    color:#0082D6;
}
.produtosZillyonWeb .price{
    text-align:right;
}
.price_{
    color:#0082D6;
    font-weight:bold;
}
.produtosZillyonWeb table{
    margin-left:-3px;
}
.addProductsToolbarZillyonWeb{
    overflow-y:auto;
    overflow-x:hidden;
}
.checkedProductZillyonWeb{
    margin-left:-18px;
    margin-top:-104px;
    position:absolute;
}
.productsAddCart_{
    width:100%;
}
.checkedProdZillyonWeb{
    margin-top:-28px;
    position:absolute;
}
.preview .listVitrine.selected a, .preview .gridVitrine.selected a, .preview .extendedVitrine.selected a {color:#666666;}


.msgToolbar{
    margin-left:-7px;
    width:299px;
    color:#FFF;
    background-color:#666;
    font-size:11px;
}
.msgToolbar div{
    margin-left:5px;
    text-align:center;
    padding:2px;
}
#boxToolbar{
    margin-top:103px;
    width:95%;
}
#boxToolbar1{
    width:100%;
    position:fixed;
}
.layerAdd{
    position:absolute;
    width:19%;
    z-index:21;
    margin-top:-17px;
}
#helperToolbar{
    position:relative;z-index:100000;
}
#helpToolbar{
    margin-left:15px;
    cursor:pointer;
}
#layerHelpToolbar{
    position:absolute;
    margin:-4px -113px;
    display:none;
}

#layerHelpToolbar #contentHelpToolbar a{
    font-size:10px;
    color:#0082d6;
    text-decoration:underline;
}
#moreHelpToolbar{
    margin:68px 112px;
    float: left;
    width:60px;
}

.produtosZillyonWeb #warringCart{
    margin:-10px -48px;
    position:absolute;
}
#seloToolbar #conteudo span{
    font-size:11px;
    color:#666666;
}
#seloToolbar #conteudo span a{
    color:#666666;
    outline:none;
    text-decoration:underline;
}
#seloToolbar #conteudo div{
    float:left;
}
#seloToolbar #conteudo #info1{
    padding:4px 0 0 12px;
}
#seloToolbar #conteudo #info2{
    padding:7px 4px;
}
#cupomToolbar{
    display:none;
}
#menulateral{
    z-index:0;
}


.contextmenu{
    list-style-type:none;
    font-size:11px;
    color:#666;
    font-weight:normal;
    font-family:Arial;
    padding:1px 6px;
}
.contextmenu li{
    cursor:pointer;
    padding-top:10px !important;
    padding-left:10px !important;
    padding-right:0px !important;
    padding-bottom:0px !important;
    width:auto !important;
    display:block !important;
    float:none !important;
    text-align:left !important;
    color:#000000 !important;
    margin:0 !important;
    background-image:none;
    height:23px;
}

.contextmenu .buy1-click.hover, .contextmenu .addProduto.hover{
    background-position:center 8px;
}
.contextmenu .wishList.hover{
    background-position:center 6px;
}
.twitter, .facebook{
    float:left;
}
.open-social div{
    margin-top:1px;
    font-size:11px;
}
.contextmenu li div{
    color:#666666;
}
.moreProd img, .wishList img{
    margin-left:3px;
}

/*Layer2*/
#layerCart2{position:fixed;right:2px;top:123px;z-index:22;display:none;}
/*End*/

div.boxShipping .type1, div.boxShipping .type2{display:block !important;}
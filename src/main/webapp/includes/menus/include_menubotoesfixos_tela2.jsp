

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<link href="css_pacto.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="includes/menus/menubotoes.css" type="text/css" />
<%@include file="../imports.jsp" %>

<%-- LEMBRE-SE DE QUE OS CONTROLES DEVEM FUNCIONAR EM INTERNET EXPLORER E OUTROS NAVEGADORES--%>
<c:choose>
    <c:when test="${SuperControle.internetExplorer}">

        <h:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber}" image="images/btn_voltar.gif"
                         action="voltar"/>

        <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber}" image="images/btn_fecharnegociacao2.gif"
                           id="botaoConfirmarcao" action="#{ContratoControle.validarDadosContrato}"
                           reRender="form:msgCima, form:msgBaixo"
                           oncomplete="#{ContratoControle.abriPanelUsuarioSenha}window.location.href = '#anchorMsg';"
                           />
        <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber}" image="images/btn_fecharereceber.gif"
                           id="botaoPagar"
                           action="#{ContratoControle.validarDadosContrato}"
                           reRender="form:msgCima, form:msgBaixo"
                           oncomplete="#{ContratoControle.abriRichModalPanelConfimacaoPagamento}window.location.href = '#anchorMsg';"/>
    </c:when>
    <c:otherwise>
        <div id="ToolbarZillyonWeb" style="display: block; margin-bottom: 0px; ">
            <div id="globalZillyonWeb">
                <div id="logoZillyonWeb" style="display: block; "></div>

                <div class="menubotoesfixo">
                    <ul  id="css3menu1" class="topmenu">
                        <li class="topfirst">

                            <a4j:commandLink rendered="#{ContratoControle.apresentarBotoesFecharEReceber}"  style="height:36px;line-height:36px;"
                                           action="voltar">
                                <img src="includes/menus/back.png"/>
                                Voltar
                            </a4j:commandLink>

                        </li>
                        <li>
                            <a4j:commandLink rendered="#{ContratoControle.apresentarBotoesFecharEReceber}"  
                                             style="height:36px;line-height:36px;" reRender="form:msgCima, form:msgBaixo,panelExistePessoa, msgBaixo, msgCima,modalValidacaoCPF"
                                             title="Gravar dados do cliente e fechar a negociação"
                                             id="botaoConfirmarcao" 
                                             action="#{ClienteControle.gravarDadosClienteAbrirModalFecharNegociacao}"
                                             oncomplete="#{ClienteControle.msgAlert}">
                                <img src="includes/menus/favour.png"/>
                                Fechar Negociação
                            </a4j:commandLink>

                        </li>
                        <li class="toplast">
                            <a4j:commandLink rendered="#{ContratoControle.apresentarBotoesFecharEReceber}"  style="height:36px;line-height:36px;"
                                             id="botaoPagar" reRender="form:msgCima, form:msgBaixo,panelExistePessoa, msgBaixo, msgCima,modalValidacaoCPF"
                                             title="Gravar dados do cliente e fechar a negociação"
                                             action="#{ClienteControle.gravarDadosClienteAbrirModalFecharNegociacaoReceber}"
                                             oncomplete="#{ClienteControle.msgAlert}"
                                             >
                                <img src="images/money.png" border="0"/>
                                Fechar e Receber
                            </a4j:commandLink>
                            
                        </li>
                    </ul>
                </div>


            </div>

        </div>
    </c:otherwise>

</c:choose>
<%-- 
    Document   : include_panelMensagem_goBackBlock
    Created on : 27/01/2017, 16:47:37
    Author     : marcosand<PERSON>
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="imports.jsp" %>
<rich:modalPanel id="panelGoBack" styleClass="novaModal" autosized="true" shadowOpacity="true"
                 width="450" height="200">
    <f:facet name="header">
        <h:panelGroup id="pnlTituloGoBack">
            <h:graphicImage style="margin: 0 0 -5px 0" value="/imagens/atencao.png"/>
            <rich:spacer width="5"/>
            <h:outputText value="Atenção!"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup id="pnlAcoesGoBack">
            <h:form>
                <a4j:commandLink
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        action="#{SuperControle.notificarEmpresaNoBack}"
                        id="hidelinkPanelGoBack"/>
            </h:form>
            <rich:componentControl for="panelGoBack" attachTo="hidelinkPanelGoBack" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" style="width:100%;margin: 10 20 10 20;">
        <h:panelGrid id="panelResponsavelPagamento" columns="1" width="100%" cellpadding="10"
                     columnClasses="colunaEsquerda" styleClass="font-size-Em-max">
            <h:panelGroup>
                <h:outputText id="msgBloqueioAdverte"
                              style="font: 14px Arial;color: #777;text-align: center;vertical-align: middle;"
                              value="Não é permitido a utilização do botão Voltar do navegador. Utilize os botões presentes na tela para navegar."/>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGroup layout="block" styleClass="container-botoes">
            <h:form>
                <a4j:commandLink id="hideMsgBlock" onclick="Richfaces.hideModalPanel('panelGoBack')"
                                 oncomplete="Richfaces.hideModalPanel('panelProdutoSugerido')"
                                 action="#{SuperControle.notificarEmpresaNoBack}"
                                 value="OK" accesskey="2"
                                 styleClass="botaoPrimario texto-font texto-size-16"/>
            </h:form>
        </h:panelGroup>

    </h:panelGroup>
</rich:modalPanel>

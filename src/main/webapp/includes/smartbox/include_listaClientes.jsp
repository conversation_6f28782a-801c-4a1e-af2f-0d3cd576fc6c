<%-- 
    Document   : include_smartbox_conteudo
    Created on : 20/01/2012, 08:30:11
    Author     : <PERSON><PERSON>
--%>


<%@include file="../imports.jsp" %>

<rich:modalPanel id="modalListaClientes" autosized="true"
                 shadowOpacity="true" width="670" minHeight="350"
                 top="100">

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hideListaClientes" />
            <rich:componentControl for="modalListaClientes"
                                   attachTo="hideListaClientes" operation="hide"
                                   event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formListaClientes" ajaxSubmit="true">
        <a4j:outputPanel id="panelLista" rendered="#{!ListaClientesControle.detalharCliente}">

            <script type="text/javascript">
                tabelaSmartBox('formListaClientes:listaClientes');
                var modal = document.getElementById('modalListaClientesCDiv');
                modal.style.top = '100px';
                modal.style.left = (screen.availWidth - 670) / 2;
            </script>

            <table width="100%">
                <tr>
                    <td valign="top" width="30%">
                        <h:graphicImage rendered="#{!empty ListaClientesControle.imagem}" style="padding: 6 0 0 6;" url="#{ListaClientesControle.imagem}"/>
                    </td>
                    <td valign="middle" align="right" width="70%">
                        <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_ListaClientes_buscarAluno}"></h:outputText>
                        <h:inputText onfocus="focusinput(this);" styleClass="form" id="listaConsultaPorNome" value="#{ListaClientesControle.nomeConsulta}" ></h:inputText>
                        <rich:hotKey selector="#listaConsultaPorNome" key="return"
                                     handler="#{rich:element('consultarAlunosNaLista')}.onclick();return false;"/>
                    </td>
                    <td valign="middle" width="5%">
                        <a4j:commandButton style="width:25px; height:20px;" image="/imagens/pesquisar_smartbox.png"
                                           action="#{ListaClientesControle.consultarAluno}"
                                           reRender="panelLista"
                                           id="consultarAlunosNaLista"
                                           oncomplete="tabelaSmartBox('formListaClientes:listaClientes');"/>
                    </td>
                </tr>
            </table>
            <br/>
            <!-- LISTA -->
            <rich:dataTable rendered="#{ListaClientesControle.apresentarLista}"
                            id="listaClientes" var="cliente"
                            value="#{ListaClientesControle.listaClientes}" rows="10"
                            onRowMouseOver="this.style.backgroundColor='#F1F1F1'"
                            onRowMouseOut="this.style.backgroundColor='#{a4jSkin.tableBackgroundColor}'"
                            width="650px">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_matricula}"/>
                    </f:facet>
                    <a4j:commandLink styleClass="linkListaClientes"
                                     reRender="formListaClientes"
                                     action="#{ListaClientesControle.detalharAluno}">
                        <h:outputText value="#{cliente.matricula}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column sortBy="#{cliente.pessoa.nome}" sortable="true">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_nome}"/>
                    </f:facet>
                    <a4j:commandLink styleClass="linkListaClientes"
                                     reRender="formListaClientes"
                                     action="#{ListaClientesControle.detalharAluno}">
                        <h:outputText value="#{cliente.pessoa.nome}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Plano_tituloForm}"/>
                    </f:facet>
                    <a4j:commandLink styleClass="linkListaClientes"
                                     reRender="formListaClientes"
                                     action="#{ListaClientesControle.detalharAluno}">
                        <h:outputText value="#{cliente.situacaoClienteSinteticoVO.nomePlano}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_situacao}"/>
                    </f:facet>
                    <a4j:commandLink styleClass="linkListaClientes"
                                     reRender="formListaClientes"
                                     action="#{ListaClientesControle.detalharAluno}">
                        <h:outputText value="#{cliente.situacaoClienteSinteticoVO.situacao}"/>
                    </a4j:commandLink>
                </rich:column>

                <rich:column sortBy="#{cliente.situacaoClienteSinteticoVO.duracaoContratoMeses}" sortable="true" rendered="#{ListaClientesControle.apresentarColunaDuracao}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Duracao_tituloForm} "/>
                    </f:facet>
                    <h:outputText value="#{cliente.situacaoClienteSinteticoVO.duracaoContratoMeses}"/>
                </rich:column>

                <rich:column sortBy="#{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustada}">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Agenda_vencimentoContrato}"/>
                    </f:facet>
                    <a4j:commandLink styleClass="linkListaClientes"
                                     reRender="formListaClientes"
                                     action="#{ListaClientesControle.detalharAluno}">
                        <h:outputText value="#{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustada}">
                            <f:convertDateTime type="date" dateStyle="short"
                                               locale="pt" timeZone="America/Sao_Paulo"
                                               pattern="dd/MM/yy" />
                        </h:outputText>
                    </a4j:commandLink>
                </rich:column>

            </rich:dataTable>
            <rich:datascroller oncomplete="tabelaSmartBox('formListaClientes:listaClientes');"
                               rendered="#{ListaClientesControle.apresentarLista}" align="center"
                               for="formListaClientes:listaClientes" maxPages="10"
                               id="scListaClientes" status="statusInComponent" />

            <h:panelGrid width="100%" rendered="#{!ListaClientesControle.apresentarLista}" columnClasses="colunaCentralizada">
                <rich:spacer height="40px"></rich:spacer>
                <img src="imagens/lista_vazia.png" width="48px" height="48px">
                <h:outputText styleClass="tituloDemonstrativo"
                              value="#{msg.msg_listaClientes_vazia}"></h:outputText>
            </h:panelGrid>
        </a4j:outputPanel>
        <!-- ---------------DETALHAMENTO DE CLIENTE -------------------------------  -->
        <a4j:outputPanel id="panelDetalhamento" rendered="#{ListaClientesControle.detalharCliente}">
            <script type="text/javascript">
                tabelaSmartBox('formListaClientes:detalhaCliente');
                var modal = document.getElementById('modalListaClientesCDiv');
                modal.style.top = '5px';
                modal.style.left = (screen.availWidth - 870) / 2;
            </script>
            <br/>
            <rich:dataTable value="#{ListaClientesControle.listaClientes}"
                            id="detalhaCliente" var="cliente"
                            rows="1"
                            width="850px">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_matricula} "/>
                    </f:facet>
                    <h:outputText value="#{cliente.matricula}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_nome} "/>
                    </f:facet>
                    <h:outputText value="#{cliente.pessoa.nome}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Plano_tituloForm} "/>
                    </f:facet>
                    <h:outputText value="#{cliente.situacaoClienteSinteticoVO.nomePlano}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Duracao_tituloForm} "/>
                    </f:facet>
                    <h:outputText value="#{cliente.situacaoClienteSinteticoVO.duracaoContratoMeses}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ListaClientes_situacao} "/>
                    </f:facet>
                    <h:dataTable id="clienteSituacao" width="100%"
                                 columnClasses="colunaCentralizada"
                                 value="#{cliente.clienteSituacaoVOs}"
                                 var="clienteSituacao">
                        <h:column>
                            <h:graphicImage id="sitFreePass" value="./imagens/botaoFreePass.png"
                                            rendered="#{clienteSituacao.visitanteFreePass}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitAtivo" value="./imagens/botaoAtivo.png"
                                            rendered="#{clienteSituacao.ativo}" width="20" height="20" />
                            <h:graphicImage id="sitInativo" value="./imagens/botaoInativo.png"
                                            rendered="#{clienteSituacao.inativo}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitVisitante" value="./imagens/botaoVisitante.png"
                                            rendered="#{clienteSituacao.visitante}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitTrancado" value="./imagens/botaoTrancamento.png"
                                            rendered="#{clienteSituacao.trancado}" width="20"
                                            height="20" />
                            <rich:spacer width="5px" />
                            <h:graphicImage id="sitAtNormal" value="./imagens/botaoNormal.png"
                                            rendered="#{clienteSituacao.ativoNormal}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitTraVencido" value="./imagens/botaoTrancadoVencido.png"
                                            rendered="#{clienteSituacao.trancadoVencido}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitAulaAvulsa" value="./imagens/botaoAulaAvulsa.png"
                                            rendered="#{clienteSituacao.visitanteAulaAvulsa}"
                                            width="20" height="20" />
                            <h:graphicImage id="sitDiaria" value="./imagens/botaoDiaria.png"
                                            rendered="#{clienteSituacao.visitanteDiaria}" width="20"
                                            height="20" />
                            <rich:spacer width="5px" />
                            <h:graphicImage id="sitCancelamento" value="./imagens/botaoCancelamento.png"
                                            rendered="#{clienteSituacao.inativoCancelamento}"
                                            width="20" height="20" />
                            <h:graphicImage id="sitDesistente" value="./imagens/botaoDesistente.png"
                                            rendered="#{clienteSituacao.inativoDesistente}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitAVencer" value="./imagens/botaoAvencer.png"
                                            rendered="#{clienteSituacao.ativoAvencer}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitVencido" value="./imagens/botaoVencido.png"
                                            rendered="#{clienteSituacao.inativoVencido}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitCarencia" value="./imagens/botaoCarencia.png"
                                            rendered="#{clienteSituacao.ativoCarencia}" width="20"
                                            height="20" />
                            <h:graphicImage id="sitAtestado" value="./imagens/botaoAtestado.png"
                                            rendered="#{clienteSituacao.ativoAtestado}" width="20"
                                            height="20" />
                        </h:column>
                    </h:dataTable>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Agenda_vencimentoContrato}"/>
                    </f:facet>
                    <h:outputText value="#{cliente.situacaoClienteSinteticoVO.dataVigenciaAteAjustada}">
                        <f:convertDateTime type="date" dateStyle="short"
                                           locale="pt" timeZone="America/Sao_Paulo"
                                           pattern="dd/MM/yy" />
                    </h:outputText>
                </rich:column>
            </rich:dataTable>

            <table width="100%">
                <tr>
                    <!-- DADOS DO ALUNO -->
                    <td width="30%" valign="top">
                        <table width="100%">
                            <tr>
                                <td width="35%" valign="top">
                                    <a4j:outputPanel id="panelFoto">                                        
                                        <a4j:mediaOutput element="img" id="imagem1"  style="width:120px;height:150px "  cacheable="false" session="true"
                                                         rendered="#{!SuperControle.fotosNaNuvem}" 
                                                         createContent="#{ClienteControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                            <f:param name="largura" value="120"/>
                                            <f:param name="altura" value="150"/>
                                        </a4j:mediaOutput>
                                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                                        width="120" height="150"                                        
                                                        style="width:120px;height:150px"
                                                        url="#{ClienteControle.paintFotoDaNuvem}"/>
                                    </a4j:outputPanel>
                                </td>
                                <td width="70%" valign="top">
                                    <img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente" value="#{msg_aplic.prt_DadosPessoais_tituloForm} "/>
                                    <br/><rich:spacer height="20px"></rich:spacer>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_dataCadastramento} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.clienteVO.pessoa.dataCadastro}">
                                        <f:convertDateTime type="date" dateStyle="short"
                                                           locale="pt" timeZone="America/Sao_Paulo"
                                                           pattern="dd/MM/yy" />
                                    </h:outputText>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_Cliente_categoria} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.clienteVO.categoria.nome}"/>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_dataMatricula} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataMatricula}">
                                        <f:convertDateTime type="date" dateStyle="short"
                                                           locale="pt" timeZone="America/Sao_Paulo"
                                                           pattern="dd/MM/yy" />
                                    </h:outputText>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_dataRematricula} "/>
                                    <h:outputText rendered="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimaRematricula != null}"
                                                  styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimaRematricula}">
                                        <f:convertDateTime type="date" dateStyle="short"
                                                           locale="pt" timeZone="America/Sao_Paulo"
                                                           pattern="dd/MM/yy" />
                                    </h:outputText>
                                    <h:outputText rendered="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimaRematricula == null}" styleClass="valoresDetalhamentoCliente" value="...">
                                    </h:outputText>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_Cliente_situacao} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.clienteVO.situacao_Apresentar}"/>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_HistoricoContato_aniversario}: "/>
                                    <h:outputText rendered="#{ClienteControle.clienteVO.pessoa.dataNasc != null}" styleClass="valoresDetalhamentoCliente"
                                                  value="#{ClienteControle.clienteVO.pessoa.dataNasc}">
                                        <f:convertDateTime type="date" dateStyle="short"
                                                           locale="pt" timeZone="America/Sao_Paulo"
                                                           pattern="dd/MM/yy" />
                                    </h:outputText>
                                    <h:outputText rendered="#{ClienteControle.clienteVO.pessoa.dataNasc == null}" styleClass="valoresDetalhamentoCliente" value="...">
                                    </h:outputText>
                                </td>
                            </tr>
                        </table>
                        <table>
                            <tr>
                                <td>
                                    <img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_dadosAluno}"/>
                                    <br/><rich:spacer height="20px"></rich:spacer>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_contaAcademia} "/>
                                    <h:outputText styleClass="#{ClienteControle.corVermelhaSaldoNegativoCorVerdeSaldoPositivo}"
                                                  value="#{ClienteControle.clienteVO.saldoContaCorrente}">
                                        <f:converter converterId="FormatadorNumerico" />
                                    </h:outputText>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_Idade} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.idadeCliente}"/>
                                    <br/>
                                    <h:outputText styleClass="labelDetalhamentoCliente" value="#{msg_aplic.prt_Pessoa_sexo} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.clienteVO.pessoa.sexo_Apresentar}"/>
                                    <br/>
                                    <a4j:repeat value="#{ClienteControle.clienteVO.vinculoVOs}" var="vinculo">
                                        <h:outputText styleClass="labelDetalhamentoCliente" value="#{vinculo.tipoVinculo_Apresentar}: "/>
                                        <h:outputText styleClass="valoresDetalhamentoCliente" value="#{vinculo.colaborador.pessoa.nome}"/>
                                        <br/>
                                    </a4j:repeat>
                                </td>
                            </tr>

                            <tr>
                                <td  width="100%">
                                    <img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_mensagensAvisos}"/>

                                    <h:dataTable
                                        id="avisosCliente" width="100%" columnClasses="textsmall"
                                        value="#{ClienteControle.clienteVO.listaMensagemAvisoCliente}"
                                        styleClass="tabFormSubordinada" var="avisoCliente">
                                        <h:column id="teste1">
                                            <%-- <img style="vertical-align: middle; margin-right: 4px;"
                                                  src="images/arrow.gif">--%>
                                            <img style="vertical-align: middle; margin-right: 4px;" src="imagens/ico_ani_telefone.gif">
                                            <%--<rich:spacer width="10px" />--%>
                                            <h:panelGroup id="teste2"
                                                          rendered="#{avisoCliente.navegacaoFrame}">
                                                <a4j:commandLink id="linkMsgCliente" styleClass="link_red"
                                                                 action="#{ClienteControle.abreTela}"
                                                                 actionListener="#{ClienteControle.selecionarClienteMensagemListener}">
                                                    <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                                    <h:outputText id="textoMsgCliente" value="#{avisoCliente.mensagem}" />
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                            <h:panelGroup
                                                rendered="#{avisoCliente.navegacaoPopUp}">
                                                <a4j:commandLink id="linkMsgCliente1" styleClass="link_red"
                                                                 action="#{ClienteControle.abreTela}"
                                                                 actionListener="#{ClienteControle.selecionarClienteMensagemListener}"
                                                                 oncomplete="abrirPopup('#{avisoCliente.tipomensagem.navegacao}', '#{avisoCliente.tipomensagem}', 780, 595);">
                                                    <f:attribute name="objClienteMensagem" value="#{avisoCliente}"/>
                                                    <h:outputText id="textoMsgCliente1"  value="#{avisoCliente.mensagem}" />
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                        </h:column>
                                    </h:dataTable>

                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <img src="images/smile_smartbox.png" width="20" height="20" style="vertical-align:middle;margin-right:3px;">
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_grauSatisfacao} "/>
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.grauSatisfacao}"/>
                                    <br/>
                                    <img src="imagensCRM/grupo_de_risco.png" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{msg_aplic.prt_HistoricoContato_risco}: "/>
                                    <h:outputText rendered="#{ClienteControle.risco.peso != null}" styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.risco.peso}"/>
                                    <h:outputText rendered="#{ClienteControle.risco.peso == null}" styleClass="valoresDetalhamentoCliente" value="..."/>
                                    <br/>
                                    <img src="imagensCRM/vencidos.png" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="valoresDetalhamentoCliente" value="#{msg_aplic.prt_ListaClientes_nrDiasUltimoContato} "/>
                                    <h:outputText rendered="#{!(ClienteControle.diasUltimoContato == '0')}" styleClass="valoresDetalhamentoCliente" value="#{ClienteControle.diasUltimoContato}"/>
                                    <h:outputText rendered="#{ClienteControle.diasUltimoContato == '0'}" styleClass="valoresDetalhamentoCliente" value="#{msg_aplic.prt_hoje}"/>
                                    <br/>
                                    <br/>
                                    <a4j:commandLink action="#{ListaClientesControle.voltarListaClientes}"
                                                     value="<< #{msg_aplic.prt_voltar}"
                                                     reRender="formListaClientes"></a4j:commandLink>
                                </td>

                            </tr>
                        </table>

                    </td>
                    <!-- ESTAT�STICAS -->
                    <td width="60%" valign="top">

                        <!-- -------------CONTRATOS---------------------- -->

                        <table width="100%">
                            <tr>
                                <td align="left">
                                    <img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente" value="Contratos"/>
                                </td>
                                <td align="right">
                                </td>
                            </tr>
                        </table>
                        <rich:dataTable id="listaContrato" width="100%" border="0"
                                        rows="3"
                                        cellspacing="0" cellpadding="10" styleClass="textsmall"
                                        rendered="#{!empty ClienteControle.listaContratos}"
                                        value="#{ClienteControle.listaContratos}"
                                        var="contrato">
                            <rich:column style="border: 1px solid silver; padding: 2px;" 
                                         headerClass="tabelaFichaCliente" styleClass="tabelaFichaCliente"
                                         >
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Contrato" />
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.codigo}" />
                            </rich:column>
                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente" styleClass="tabelaFichaCliente" >
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="Lan�amento" />
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.dataLancamento_Apresentar2}" />
                            </rich:column>
                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente"  styleClass="tabelaFichaCliente" >
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="In�cio" />
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.vigenciaDe}">
                                    <f:convertDateTime pattern="dd/MM/yy" />
                                </h:outputText>
                            </rich:column>
                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente" styleClass="tabelaFichaCliente" >
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold"
                                                  value="Valor" />
                                </f:facet>
                                <h:outputText style="font-weight: bold" value="#{contrato.valorFinal}"  styleClass="#{contrato.styleContratoVigenteOuNaoVigente}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </rich:column>
                            <rich:column style="border: 1px solid silver;padding: 2px;"  headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" 
                                                  value="T�rmino" />
                                </f:facet>

                                <h:outputText style="font-weight: bold" styleClass="#{contrato.styleContratoVigenteOuNaoVigenteDataTermino}"
                                              value="#{contrato.vigenciaAteAjustada}">
                                    <f:convertDateTime pattern="dd/MM/yy" />
                                </h:outputText>

                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"  headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" 
                                                  value="Plano" />
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.plano.descricao}">
                                </h:outputText>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;"  headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" 
                                                  value="Dur." />
                                </f:facet>
                                <h:outputText style="font-weight: bold" styleClass="#{contrato.styleContratoVigenteOuNaoVigente}"
                                              value="#{contrato.contratoDuracao.numeroMeses}">
                                </h:outputText>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente">
                                <f:facet name="header">
                                    <h:outputText style="font-weight:bold" value=" S."/>

                                </f:facet>
                                <h:graphicImage id="contAtivo" value="./imagens/botaoAtivo.png"
                                                rendered="#{contrato.contratoAtivo}" width="15"
                                                height="15" />
                                <h:graphicImage id="contCancelado"  value="./imagens/botaoCancelamento.png"
                                                rendered="#{contrato.contratoCancelado}" width="15"
                                                height="15" />
                                <h:graphicImage id="contTrancado"  value="./imagens/botaoTrancamento.png"
                                                rendered="#{contrato.contratoTrancado}" width="15"
                                                height="15" />
                                <h:graphicImage id="contInativo" value="./imagens/botaoInativo.png"
                                                rendered="#{contrato.contratoInativo}" width="15"
                                                height="15" />
                                <h:graphicImage id="contRenovado"  value="./imagens/botaoRenovado.png"
                                                rendered="#{contrato.apresentarBotaoRenovarContrato}"
                                                width="15" height="15" />
                            </rich:column>
                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente">

                                <f:facet name="header">
                                    <h:outputText style="font-weight:bold" value="Op��es"/>

                                </f:facet>

                                <a4j:commandButton id="imprimir"
                                                   value="Imprimir Contrato" image="./imagens/imprimir.png"
                                                   style="width: 18px; height : 18px;"
                                                   action="#{ClienteControle.imprimirContrato}" reRender="form:panelMensagemSuperior,form:panelMensagemInferior"
                                                   oncomplete="abrirPopup('VisualizarContrato', 'RelatorioContrato', 730, 545);" />
                                <%--<rich:spacer width="5px" />--%>
                                <%--<a4j:commandButton id="enviar" image="./imagens/email.png"--%>
                                                   <%--style="width: 18px; height : 18px;"--%>
                                                   <%--onclick="if(!confirm('Deseja enviar o e-mail?')) {return false;}"--%>
                                                   <%--action="#{ClienteControle.enviarContrato}" reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao"--%>
                                                   <%--value="E-mail">--%>
                                <%--</a4j:commandButton>--%>
                                <rich:spacer width="5px" />
                            </rich:column>
                        </rich:dataTable>

                        <!-- -------------produtos com validade---------------------- -->

                        <h:panelGroup rendered="#{not empty ClienteControle.clienteVO.listaProdutosComValidade}">
                            <rich:spacer height="10px"></rich:spacer>
                            <table width="100%">
                                <tr>
                                    <td align="left">
                                        <img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                        <h:outputText styleClass="tituloDetalhamentoCliente" value="Produtos com Validade"/>
                                    </td>
                                    <td align="right">
                                    </td>
                                </tr>
                            </table>
                            <rich:dataTable    id="listaProdutoComValidade" width="100%" border="0"
                                               rows="3"
                                               cellspacing="0" cellpadding="10" styleClass="textsmall"
                                               columnClasses="centralizado, centralizado, centralizado "
                                               value="#{ClienteControle.clienteVO.listaProdutosComValidade}"
                                               var="movProduto">
                                <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente" >
                                    <f:facet name="header">
                                        <h:outputText style="font-weight: bold" value="Produto" />
                                    </f:facet>
                                    <h:outputText value="#{movProduto.produto.descricao}" />
                                </rich:column>
                                <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente"  >
                                    <f:facet name="header">
                                        <h:outputText style="font-weight: bold"
                                                      value="Data Compra" />
                                    </f:facet>
                                    <h:outputText value="#{movProduto.dataLancamento_Apresentar}" />
                                </rich:column>
                                <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente" >
                                    <f:facet name="header">
                                        <h:outputText style="font-weight: bold"
                                                      value="Data Final Vig�ncia" />
                                    </f:facet>
                                    <h:outputText style="font-weight: bold" styleClass="red"
                                                  value="#{movProduto.dataFinalVigencia_Apresentar}" />
                                </rich:column>
                            </rich:dataTable>
                        </h:panelGroup>
                        <!-- -------------acessos---------------------- -->

                        <rich:spacer height="10px"></rich:spacer>
                        <table width="100%">
                            <tr>
                                <td align="left">
                                    <img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente" value="Acessos"/>
                                </td>
                                <td align="right">
                                </td>
                            </tr>
                        </table>

                        <jsp:include page="/includes/cliente/include_panelgroup_dados_acesso_cliente_branco.jsp" flush="true"/>

                        <!-- -------------contatos---------------------- -->
                        <rich:spacer height="10px"></rich:spacer>
                        <table width="100%">
                            <tr>
                                <td align="left">
                                    <img src="images/btn_historico_ligacoes.png" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
                                    <h:outputText styleClass="tituloDetalhamentoCliente" value="Hist�rico de liga��es"/>
                                </td>
                                <td align="right">
                                    <a4j:commandButton image="images/btn_fazer_ligacao.png"
                                                       action="#{HistoricoContatoControle.inicializarNovaLigacao}"
                                                       oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}">
                                    </a4j:commandButton>
                                </td>
                            </tr>
                        </table>

                        <rich:dataTable  id="dadosLigacoes" width="100%" border="0"
                                         cellspacing="0" cellpadding="10" styleClass="textsmall"
                                         value="#{ClienteControle.contatos}"
                                         rows="3"
                                         var="historicoContato">
                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente" >
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Data" />
                                </f:facet>
                                <f:verbatim>
                                    <h:outputText value="#{historicoContato.dia}">
                                        <f:convertDateTime pattern="dd/MM/yy HH:mm" />
                                    </h:outputText>
                                </f:verbatim>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente" >
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Consultor" />
                                </f:facet>
                                <f:verbatim>
                                    <h:outputText value="#{historicoContato.responsavelApresentar}">
                                    </h:outputText>
                                </f:verbatim>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente" >
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Motivo" />
                                </f:facet>
                                <f:verbatim>
                                    <h:outputText value="#{historicoContato.fase_Apresentar}">
                                    </h:outputText>
                                </f:verbatim>
                            </rich:column>

                            <rich:column style="border: 1px solid silver;padding: 2px;" headerClass="tabelaFichaCliente" >
                                <f:facet name="header">
                                    <h:outputText style="font-weight: bold" value="Descri��o" />
                                </f:facet>
                                <f:verbatim>
                                    <h:outputText escape="false" value="#{historicoContato.resumoObservacao}">
                                    </h:outputText>
                                    <a4j:commandLink value="[...]"
                                                     action="#{HistoricoContatoControle.abrirPopUpHistorico}"
                                                     oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}">
                                    </a4j:commandLink>
                                </f:verbatim>
                            </rich:column>

                        </rich:dataTable>
                        <table width="100%">
                            <tr>
                                <td align="right">
                                    <a4j:commandLink value="Ver hist�rico completo"
                                                     action="#{HistoricoContatoControle.abrirPopUpHistorico}"
                                                     oncomplete="#{HistoricoContatoControle.abrirPopUpAdequada}">
                                    </a4j:commandLink>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </a4j:outputPanel>
    </a4j:form>

</rich:modalPanel>

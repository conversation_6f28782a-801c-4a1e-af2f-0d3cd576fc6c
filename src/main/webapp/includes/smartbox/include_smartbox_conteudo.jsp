<%--
    Document   : include_smartbox_conteudo
    Created on : 09/01/2012, 12:15:43
    Author     : Waller
--%>

<%@page contentType="text/html;charset=UTF-8" %>
<%@include file="../imports.jsp" %>

<style type="text/css" >
    .rich-table-cell{
        border: none;
    }
</style>

<a4j:outputPanel id="outputCaixas">
    <h:outputText value="O que é o SmartBox?" style="padding: 5 5 5 5;" styleClass="tituloSmart"/>
    <h:outputLink value="#{SuperControle.urlWiki}Smartbox"
                  title="Clique e saiba mais: SmartBox" target="_blank">
        <h:graphicImage styleClass="linkWiki" url="imagens/wiki_link2.gif" style="margin-left:5px;"/>
    </h:outputLink>
    <h:panelGrid id="tabelaNColunasSmart" width="100%" style="padding-right:10px;"
                 cellpadding="0" cellspacing="0"
                 columns="#{fn:length(SmartBoxControle.listaCaixas)}">
        <a4j:repeat value="#{SmartBoxControle.listaCaixas}" var="caixa" rowKeyVar="ind">
            <rich:column styleClass="tituloCamposTop">
                <h:panelGroup layout="block">
                    <div class="cantos_t">
                        <div class="cantos_l">
                            <div class="cantos_r">
                                <div class="cantos_b">
                                    <div class="cantos_tl">
                                        <div class="cantos_tr">
                                            <div class="cantos_bl">
                                                <div class="cantos_br">
                                                    <div class="cantos">
                                                        <h:panelGrid columns="1" width="100%">
                                                            <h:panelGrid cellpadding="0" cellspacing="0" width="100%" columns="3">
                                                                <rich:column styleClass="colunaEsquerda">
                                                                    <h:graphicImage style="padding: 6 0 0 6;" url="#{caixa.tipo.imagem}"/>
                                                                </rich:column>
                                                                <rich:column style="vertical-align:middle;" styleClass="colunaDireita">
                                                                    <h:panelGrid cellpadding="0" cellspacing="0" columns="1" width="100%" columnClasses="colunaCentralizada">
                                                                        <h:outputText style="font-size:15px;" styleClass="titulo" value="#{caixa.porcentagemContratosAtivosDesteTipoEmRelacaoAoTodo}"/>
                                                                        <hr style="margin:0 0 0 0;padding:0 0 0 0;">
                                                                        <h:outputText style="font-size:15px;" styleClass="titulo" value="#{caixa.totalContratosAtivosDesteTipo}"/>
                                                                    </h:panelGrid>
                                                                </rich:column>
                                                                <rich:column styleClass="colunaDireita,colunaEsquerda" style="padding: 0 0 0 0;margin: 0 0 0 0;">
                                                                    <h:graphicImage id="tendencia" width="24" height="24" style="padding: 0 0 0 0;margin: 0 0 0 0;" url="#{caixa.tendencia.imagem}"/>
                                                                    <rich:toolTip direction="bottom-left" showDelay="500" style="width:180px;height:42px;background-color:#EBF2F5;border-color:A6CEDE;"
                                                                                  for="tendencia" value="#{caixa.descricaoTendencia}"/>
                                                                </rich:column>
                                                            </h:panelGrid>

                                                            <a4j:repeat value="#{caixa.listaIndicadores}" var="totalPorIndicador">

                                                                <h:panelGrid columns="2" width="100%">
                                                                    <h:panelGroup style="padding-left:5px;" rendered="#{totalPorIndicador.indicador.formatoMidia == 'totalizador'}">
                                                                        <a4j:commandLink style="font-size:15px;" reRender="modalListaClientes,mensagem" oncomplete="Richfaces.showModalPanel('modalListaClientes');"
                                                                                         styleClass="titulo" value="#{totalPorIndicador.totalLista}"
                                                                                         actionListener="#{SmartBoxControle.carregarClientesIndicador}">
                                                                            <f:attribute name="totalPorIndicador" value="#{totalPorIndicador}"/>
                                                                        </a4j:commandLink>
                                                                        <h:outputText styleClass="text" style="padding-left:5px;padding-top:0px;padding-bottom:0px;margin:0 0 0 0;" value="#{totalPorIndicador.indicador.descricao}"/>
                                                                    </h:panelGroup>
                                                                </h:panelGrid>

                                                                <h:panelGroup rendered="#{totalPorIndicador.indicador.formatoMidia == 'grafico'}">
                                                                    <a4j:commandButton style="vertical-align:middle;" status="statusInComponent"
                                                                                       id="btnRetrairGraficos" image="images/chevron-small.png"
                                                                                       onclick="mostraEscondePorSimilaridade('btnRetrairGraficos', 'panelGraficos', this.id, 'btnRetrairGraficos');"/>
                                                                    <a4j:commandLink status="statusInComponent" id="labelRetrairGraficos" styleClass="titulo"
                                                                                     style="margin:0 0 0 0;font-size:13px;vertical-align:middle;"
                                                                                     onclick="mostraEscondePorSimilaridade('labelRetrairGraficos', 'panelGraficos', this.id, 'btnRetrairGraficos');"
                                                                                     value="#{totalPorIndicador.indicador.descricao}"/>
                                                                    <h:panelGroup id="panelGraficos">

                                                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">

                                                                            <%-- O parâmetro timestamp só existe em função do Internet Explorer não renderizar uma imagem através de uma URI se a sua URL não fora alterada. --%>
                                                                            <h:graphicImage id="grafico" style="align:center;"
                                                                                            url="acesso?box=#{totalPorIndicador.tipoBox}&indicador=#{totalPorIndicador.indicador}&timestamp=#{SuperControle.timeStamp}"/>

                                                                        </h:panelGrid>
                                                                        <rich:toolTip direction="top-left" showDelay="500" style="width:225px;height:182px;background-color:#EBF2F5;border-color:A6CEDE;"
                                                                                      for="grafico" value="#{totalPorIndicador.memoriaCalculoGrafico}"/>
                                                                    </h:panelGroup>

                                                                </h:panelGroup>



                                                            </a4j:repeat>

                                                            <h:panelGroup style="width:100px; align: center;padding:5 5 5 5;">
                                                                <a4j:commandLink actionListener="#{SmartBoxControle.montarComparativo}"
                                                                                 reRender="formComparativo"
                                                                                 oncomplete="#{SmartBoxControle.abrirComparativo}">
                                                                    <f:attribute name="caixa" value="#{caixa}"/>
                                                                    <h:graphicImage url="images/btn_comparativo.png" style="border:none;"/>
                                                                </a4j:commandLink>
                                                            </h:panelGroup>
                                                        </h:panelGrid>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </h:panelGroup>
            </rich:column>
        </a4j:repeat>

    </h:panelGrid>
</a4j:outputPanel>
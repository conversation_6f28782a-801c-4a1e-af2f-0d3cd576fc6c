<%-- 
    Document   : include_smartbox_conteudo
    Created on : 20/01/2012, 08:30:11
    Author     : <PERSON><PERSON>
--%>


<%@include file="../imports.jsp" %>
<%@taglib prefix="fiji" uri="http://exadel.com/fiji"%>
<rich:modalPanel id="modalComparativo" autosized="true"
                 shadowOpacity="true" width="680" height="455"
                 top="20">

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hideComparativo" />
            <rich:componentControl for="modalComparativo"
                                   attachTo="hideComparativo" operation="hide"
                                   event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formComparativo" ajaxSubmit="true">
        <table width="100%">
            <tr>
                <td width="40%">
                    <h:graphicImage style="padding: 6 0 0 6;" url="#{SmartBoxControle.caixaComparativo.tipo.imagem}"/>
                </td>
                <td width="60%" align="right">
                    <h:graphicImage url="images/comparativo.png"/>
                    <h:outputText id="labelComparativo" styleClass="text" value="#{SmartBoxControle.indicadorComparativo.totalLista} #{SmartBoxControle.indicadorComparativo.indicador.descricao}"></h:outputText>
                    <a4j:status forceId="true" id="statusComparativo"
                                onstart="document.getElementById('formComparativo:imageLoadingComparativo').style.visibility = '';"
                                onstop="document.getElementById('formComparativo:imageLoadingComparativo').style.visibility = 'hidden';"/>                    
                </td>
            </tr>
            <tr>
                <td width="40%">
                <h:panelGroup id="botoes">
                    <a4j:repeat value="#{SmartBoxControle.caixaComparativo.listaIndicadores}" var="totalPorIndicador">
                        <a4j:commandButton image="#{totalPorIndicador.indicador.imagem}"
                                           reRender="botoes,panelChart,labelComparativo"
                                           status="statusComparativo"
                                           rendered="#{totalPorIndicador.indicador.formatoMidia == 'totalizador'}"
                                           actionListener="#{SmartBoxControle.montarComparativo}"
                                           style="width:200;">
                            <f:attribute name="totalPorIndicador" value="#{totalPorIndicador}"/>
                            <f:attribute name="caixa" value="#{SmartBoxControle.caixaComparativo}"/>
                        </a4j:commandButton><br/>
                        <rich:spacer height="5px" ></rich:spacer>
                    </a4j:repeat>

				</h:panelGroup>
                </td>
                <td width="60%">

                    <h:panelGrid width="100%" columns="2" id="panelChart">

                        <h:panelGroup rendered="#{SuperControle.suportaFlash}">
                            <h:panelGroup>
                                <h:selectBooleanCheckbox value="#{SmartBoxControle.percentagem}">
                                    <a4j:support reRender="panelChart" event="onclick" status="statusComparativo" action="#{SmartBoxControle.montarGrafico}"/>
                                </h:selectBooleanCheckbox>
                                <h:outputText value="#{msg_aplic.prt_percentual}" styleClass="text"></h:outputText>
                            </h:panelGroup>
                            <h:panelGrid columns="2">
                                <h:graphicImage id="imageLoadingComparativo" style="visibility: hidden;vertical-align: middle;align:center" url="/images/loading.gif"/>
                                <fiji:columnChart value="#{SmartBoxControle.data}" title="#{SmartBoxControle.tituloComparativo}"
                                                  legendPosition="bottom" width="450" height="350"
                                                  rulersValuesHighlight="none" barColors="#{SmartBoxControle.colors}"
                                                  displayLegend="false"
                                                  displayTooltip="true"
                                                  toolTipValue="">
                                    <fiji:chartData type="name" value="{x}"></fiji:chartData>                                    
                                </fiji:columnChart>
                            </h:panelGrid>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{!SuperControle.suportaFlash}">
                            <jsfChart:chart id="chartICV" datasource="#{SmartBoxControle.comparativoBarra}"
                                            type="bar" is3d="true" background="#FFFFFF" foreground="white" depth="24"
                                            colors="#6CA6CD, #563857,#7CFC00, #D34C31, #FFD700, #EE9A00, #7D26CD, #00C5CD" antialias="true"
                                            legend="true" height="350" width="450" legendBorder="true" legendFontSize="10"
                                            rangeGridLines="true" outline="true">
                            </jsfChart:chart>
                        </h:panelGroup>
                    </h:panelGrid>
                </td>
            </tr>
        </table>
    </a4j:form>
</rich:modalPanel>
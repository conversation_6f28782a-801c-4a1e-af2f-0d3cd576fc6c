<%-- 
    Document   : include_modal_notificacao_indiceFinanceiro
    Created on : 11/08/2017, 16:11:46
    Author     : arthur
--%>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<a4j:jsFunction name="gatilhoVerificouModalIndiceFinanceiro" 
                action="#{NotificacaoControle.toogleVerificado}"/>
                
<rich:modalPanel id="modalNotificacaoIndiceFinanceiro"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="600" showWhenRendered="#{NotificacaoControle.modalIndiceFinanceiro && NotificacaoControle.verificouModal eq false}" onshow="gatilhoVerificouModalIndiceFinanceiro();">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Notifica��o do Indice Financeiro"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                id="hidelinkInfoPush1"/>
            <rich:componentControl for="modalNotificacaoIndiceFinanceiro" attachTo="hidelinkInfoPush1" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup rendered="#{NotificacaoControle.mostrarIndiceFinanceiroMesAtual}" layout="block" styleClass="paginaFontResponsiva" style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 20px; font-weight: bold;">
        <h:outputText value="M�s Atual"/>
    </h:panelGroup>

    <h:panelGroup layout="block" style="width: 100%; text-align: left; padding-bottom: 10px; padding-top: 10px;" rendered="#{NotificacaoControle.mostrarIndiceFinanceiroMesAtual}">
        <h:outputText value="Indice Financeiro referente ao " style="font-size: 120%;color: #777777;"/>
        <h:outputText value="m�s #{NotificacaoControle.contratosMensagem}" style="font-size: 120%;font-weight: bold; "/>
        <h:outputText value=" do(s) tipo(s) #{NotificacaoControle.tiposIGPMMesCorrente}" style="font-size: 120%;color: #777777;" rendered="#{NotificacaoControle.tiposIGPMMesCorrente != ''}"/>
        <h:outputText value=" n�o foi cadastrado e isso pode fazer" style="font-size: 120%;color: #777777;"/>
        <h:outputText value="com que " style="font-size: 120%;color: #777777;"/>
        <h:outputText value="#{NotificacaoControle.contratosNaoRenovadosMesCorrente} contratos " style="font-size: 120%;font-weight: bold;"/>
        <h:outputText value="deixem de ser renovados automaticamente." style="font-size: 120%;color: #777777;"/>
    </h:panelGroup>


    <h:panelGroup layout="block" style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 25px;" rendered="#{NotificacaoControle.mostrarIndiceFinanceiroMesAtual and NotificacaoControle.mostrarIndiceFinancerioProximoMes}">
        <rich:separator lineType="solid" height="1" style="background-color: #d5d2d2;"/>
    </h:panelGroup>
    
    <h:panelGroup rendered="#{NotificacaoControle.mostrarIndiceFinancerioProximoMes}" layout="block" styleClass="paginaFontResponsiva" style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 15px; font-weight: bold;">
        <h:outputText value="Proximo M�s"/>
    </h:panelGroup>

    <h:panelGroup layout="block" style="width: 100%; text-align: left; padding-bottom: 10px; padding-top: 10px;margin-left: 10px;margin-right: 10px" rendered="#{NotificacaoControle.mostrarIndiceFinancerioProximoMes}">
        <h:outputText value="Indice Financeiro referente ao " style="font-size: 120%;color: #777777;"/>
        <h:outputText value="m�s #{NotificacaoControle.mensagem}" style="font-size: 120%;font-weight: bold;"/>
        <h:outputText value=" do(s) tipo(s) #{NotificacaoControle.tiposIGPM}" style="font-size: 120%;color: #777777;" rendered="#{NotificacaoControle.tiposIGPM != ''}"/>
        <h:outputText value=" n�o foi cadastrado e isso pode fazer com que " style="font-size: 120%;color: #777777;"/>
        <h:outputText value="#{NotificacaoControle.contratosNaoRenovados} contratos " style="font-size: 120%;font-weight: bold;"/>
        <h:outputText value="deixem de ser renovados automaticamente." style="font-size: 120%;color: #777777;padding-right: 3px"/>
    </h:panelGroup>

    </br>
    </br>
    <h:outputText id="mensagemErro" value="#{NotificacaoControle.mensagemDetalhada}" style="font-size: medium; text-align: center; display: block; margin-bottom: 50px;" rendered="#{NotificacaoControle.mostrarBotoes}"/>
    <h:panelGroup layout="block" style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 10px; font-weight: bold;" >
        <a4j:commandLink title="Resolver agora" styleClass="botoes nvoBt" oncomplete="abrirPopup('indiceFinanceiroReajustePrecoCons.jsp', 'Campanha Cupom Desconto', 1000, 595);" rendered="#{NotificacaoControle.mostrarBotaoIndiceFinanceiro}">
            <i class="fa-icon-ok"></i> Resolver agora    
        </a4j:commandLink>
        
        <a4j:commandLink title="Lembre-me Depois"  styleClass="botoes nvoBt btSec"  oncomplete="Richfaces.hideModalPanel('modalNotificacaoIndiceFinanceiro');" >
            <h:outputText value="Lembre-me Depois"/>      
        </a4j:commandLink>
				
    </h:panelGroup>
</rich:modalPanel>
<%-- 
    Document   : include_bi_indiceconversaofiltrotipo
    Created on : 09/08/2012, 13:54:51
    Author     : carla
--%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<rich:modalPanel id="panelFiltroTM" styleClass="novaModal" width="380" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtro"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkTM"/>
            <rich:componentControl for="panelFiltroTM" attachTo="hidelinkTM" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formPanelFiltroTM">
        <h:dataTable id="tabelaFiltroTicket" value="#{BITicketMedioControle.configuracoes}" var="config" width="100%">
            <h:column>
                <h:outputText value="#{config.nome}:" styleClass="tituloCampos"/>
            </h:column>
            <h:column>
                <!-- CAMPO CHECKBOX -->
                <h:selectBooleanCheckbox id="filtroTMCheckBox" value="#{config.valorAsBoolean}" rendered="#{config.tpBoolean}">
                    <a4j:support event="onclick"
                                 action="#{BITicketMedioControle.gravarConfiguracoes}"
                                 reRender="tabelaFiltroTicket"/>
                </h:selectBooleanCheckbox>

                <!-- CAMPO INPUT DOUBLE -->
                <h:inputText value="#{config.valorAsDouble}" rendered="#{config.tpDouble}">
                    <a4j:support event="onchange"
                                 action="#{BITicketMedioControle.gravarConfiguracoes}"/>
                </h:inputText>

                <!-- CAMPO INPUT STRING -->
                <h:inputText value="#{config.valor}" rendered="#{config.tpString}">
                    <a4j:support event="onchange"
                                 action="#{BITicketMedioControle.gravarConfiguracoes}"/>
                </h:inputText>

                <!-- CAMPO INPUT INTEGER -->
                <h:inputText value="#{config.valorAsInteger}" rendered="#{config.tpInteger}">
                    <a4j:support event="onchange"
                                 action="#{BITicketMedioControle.gravarConfiguracoes}"/>
                </h:inputText>

                <!-- COMBO SELECT -->
                <h:panelGroup rendered="#{config.tpCombo}" layout="block" styleClass="cb-container">
                    <h:selectOneMenu value="#{config.valorAsInteger}" styleClass="form">
                        <f:selectItems value="#{config.listaItens}"/>
                        <a4j:support event="onchange"
                                     action="#{BITicketMedioControle.gravarConfiguracoes}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:column>
        </h:dataTable>

        <!-- BOTAO ATUALIZAR -->
        <br>
        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink
                               id="atualizarTM"
                               action="#{BITicketMedioControle.atualizarData}"
                               actionListener="#{BITicketMedioControle.atualizarData}"
                               oncomplete="Richfaces.hideModalPanel('panelFiltroTM')"
                               reRender="containerTicketMedio, panelFiltroTM"
                               style="vertical-align:middle;"
                               styleClass="botaoPrimario texto-size-16 texto-font"
                               >
                <span class="texto-size-16 texto-font">Atualizar </span>
                <i style="vertical-align: inherit" class="fa-icon-refresh texto-size-20 texto-cor-branco">

            </a4j:commandLink>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

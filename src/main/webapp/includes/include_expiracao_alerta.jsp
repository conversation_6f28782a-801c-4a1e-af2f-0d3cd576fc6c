<%-- 
    Document   : include_expiracao_alerta
    Created on : 20/04/2020
    Author     : <PERSON><PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<head>
    <style type="text/css">
        html, body {
            width: 100%;
            height: 100%;
            margin: 0 0 0 0;
            padding: 0 0 0 0;
        }

        .colunaCentralizada:hover > a {
            text-decoration: none;
        }

        .textoPadraoLabel {
            font-family: Arial;
            font-size: 12px;
            font-weight: normal;
            font-style: normal;
            font-stretch: normal;
            line-height: 1.43;
            letter-spacing: normal; /*text-align: center;*/
            color: #777777;
        }
    </style>
</head>

<c:set var="apresentarModal" value="${param.apresentarModal}" scope="request"/>
<h:panelGroup>
    <rich:modalPanel id="panelDiasParaDesbloqueioTopMod" styleClass="novaModal bg-amarelo" autosized="true" shadowOpacity="true"
                     width="800"
                     height="250"
                     showWhenRendered="#{apresentarModal}">
        <f:facet name="header">
            <h:panelGroup layout="block" style="text-align: center; margin: 20px 0;">
                <img src="../images/bloqueio.png"
                     alt="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                     style="width: 60px; vertical-align: bottom;">
                <div style="text-align: center;">
                    <h:outputText value="Alerta de recurso desativado!" escape="false"
                                  style="font-size: 18pt; vertical-align: middle; font-weight: lighter"/>
                </div>
            </h:panelGroup>
        </f:facet>

        <h:form id="formDiasParaDesbloqueioMod">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" style="margin-top: 10px">
                <h:panelGroup layout="block" style="padding: 10px 20px;">
                    <h:outputText id="tituloBloqueio" escape="false"
                                  value="#{LoginControle.mensagemRecursoDesativado}"
                                  style="font-size: 16pt; font-weight: bold"/>
                </h:panelGroup>

                <h:panelGroup layout="block" style="padding: 10px 10px;">
                    <h:outputText escape="false"
                                  value="#{LoginControle.infoMensagemRecursoDesativado}"
                                  style="font-size: 16pt;"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGroup layout="block"
                          style="padding-top: 30px; text-align: center; width: 100%; padding-bottom: 20px"
                          styleClass="colunaCentralizada">
                <a4j:commandLink
                        style="margin-left: 12px; width: 150px; height: 45px; border-radius: 5px; background-color: #094771;"
                        styleClass="botaoPrimario texto-size-16-real"
                        value="Ver valor(es) em aberto"
                        oncomplete="#{CanalPactoControle.mensagemNotificar}"
                        action="#{CanalPactoControle.abrirTelaMinhaContaFaturas}" />
                <a href="https://api.whatsapp.com/send?phone=${LoginControle.whatsFinanceiro}" target="_blank"
                   class="botaoPrimarioSmall texto-size-16-real botoesLink"
                   style="background-color: #39b54a; padding: 8px 22px 8px 22px; margin-left: 12px;">
                    <img src="${SuperControle.url}/faces/imagens/wpp.png" style="width: 20px; vertical-align: middle;"/> Negociar pelo
                    WhatsApp
                </a>
                <a4j:commandLink rendered="true"
                                 style="margin-left: 12px; width: 150px; height: 45px; border-radius: 5px; background-color: #ed1c24 !important;"
                                 styleClass="botaoPrimario texto-size-16-real" value="OK"
                                 action="#{LoginControle.abrirZillyonWeb}" />
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>
</h:panelGroup>

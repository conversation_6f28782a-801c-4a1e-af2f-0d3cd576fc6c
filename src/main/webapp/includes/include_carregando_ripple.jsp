<%@include file="imports.jsp" %>
<head>
    <script type="text/javascript" language="javascript" src="${root}/script/cubomagico.min.js"></script>
</head>
<style>
    .se-pre-con {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        background: url(${root}/images/loader-small.gif) center no-repeat #fff;
    }

    .fadeout {
        color: #FFFFFF;
        text-align: center;
        -webkit-transition: opacity 3s ease-in-out;
        -moz-transition: opacity 3s ease-in-out;
        -ms-transition: opacity 3s ease-in-out;
        -o-transition: opacity 3s ease-in-out;
        opacity: 1;        
    }

</style>

<script>

    function executarFuncoesJs(){
        hideCuboMagico();
        carregarTooltipster();
        if (typeof isRenderedModuloMenu !== "undefined" && typeof isRenderedModuloMenu === "function") {
            if (!isRenderedModuloMenu()) {
                montaModulomenu();
            }
        }
    }

    function carregarTooltipster(){
        var tol = jQuery('.tooltipster');
        if (tol != null && tol.length > 0){
            tol.tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        }

        var tolTop = jQuery('.tooltipster-top');
        if (tolTop != null && tol.length > 0){
            tolTop.tooltipster({
                theme: 'tooltipster-light',
                position: 'top',
                animation: 'grow',
                contentAsHTML: true
            });
        }

    }
</script>

<div id="cuboMagico" class="se-pre-con"></div>
<script>
    listen("load", window, executarFuncoesJs);
</script>

<rich:modalPanel id="panelStatus1" autosized="true" styleClass="modal-carregando-ripple">
    <%--    <div class="spinnerCarregando">--%>
    <%--        <div class="bounce1"></div>--%>
    <%--        <div class="bounce2"></div>--%>
    <%--        <div class="bounce3"></div>--%>
    <%--    </div>--%>
    <h:graphicImage url="/images/loader-small.gif"/>
    <div class="textoCarregando" id="idtextoCarregando" style="font-size: 20px; background-color: #fff">
    </div>
</rich:modalPanel>

<a4j:status id="status" onstart="Richfaces.showModalPanel('panelStatus1');resetTime(tempoEmMillis);#{SuperControle.enableSetLastActionTime ? 'setLastActionTime();' : ''}jQuery('body').css({'overflow':'hidden'});"
            onstop="#{rich:component('panelStatus1')}.hide();jQuery('body').css({'overflow':'auto'});executarFuncoesJs();"/>

<a4j:status forceId="true" id="statusHora" onstart="" onstop=""/>

<a4j:status forceId="true" id="statusInComponent"
            onstart="document.getElementById('form:imageLoading').style.visibility = '';"
            onstop="document.getElementById('form:imageLoading').style.visibility = 'hidden';"/>

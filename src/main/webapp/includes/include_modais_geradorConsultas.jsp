<%@include file="/includes/imports.jsp" %>

<rich:modalPanel id="modalGeradorConsultas" width="480" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Consulta: "/>
            <h:outputText value="#{GeradorConsultasControle.consultaSelecionada.nomeConsulta}"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="lnkHideModalGeradorConsultas"/>
            <rich:componentControl for="modalGeradorConsultas" attachTo="lnkHideModalGeradorConsultas"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModal">
        <h:panelGroup id="panelConteudo">

            <h:panelGroup layout="block" id="divMsgAtencao"
                          style="padding: 10px 5px 10px 5px; border: 1px #000 solid; border-style: dotted;"
                          rendered="#{not empty GeradorConsultasControle.consultaSelecionada.msgAtencao}">
                <h:panelGroup layout="block"
                              style="font-size: 16px; color: red; font-weight: bold">
                    <h:outputText value="ATEN��O"/>
                </h:panelGroup>
                <h:outputText id="msgAtencao" escape="false"
                              style="font-size: 14px;"
                              value="#{GeradorConsultasControle.consultaSelecionada.msgAtencao}"/>
            </h:panelGroup>

            <rich:panel rendered="#{GeradorConsultasControle.servidorUsuarioPermiteSelectALL}">
                <h:panelGroup style="margin-botton: 5px; margin-top: 5px;">
                    <h:selectBooleanCheckbox id="ehSelectAll" value="#{GeradorConsultasControle.selectAll}"/>
                    <h:outputText style="font-weight:bold;" value="Consultar em todos os bancos desse servidor"/>
                </h:panelGroup>
            </rich:panel>

            <h:panelGroup rendered="#{GeradorConsultasControle.testarSQL}">
                <fieldset>
                    <legend>SQL:</legend>
                    <h:inputTextarea cols="120" rows="2" value="#{GeradorConsultasControle.consulta}"/>
                </fieldset>
            </h:panelGroup>

            <h:panelGroup rendered="#{GeradorConsultasControle.consultaSelecionada.permiteWhere}">
                <fieldset>
                    <legend>Where:</legend>
                    <h:inputTextarea cols="120" rows="#{GeradorConsultasControle.numberRowsWhere}" value="#{GeradorConsultasControle.whereDaConsulta}"/>
                </fieldset>
            </h:panelGroup>

            <h:panelGroup id="botoes">
                <a4j:commandButton id="btnConsultar" value="Gerar Relat�rio"
                                   action="#{GeradorConsultasControle.prepararParametrosRelatorio}"
                                   oncomplete="#{GeradorConsultasControle.msgAlertAuxiliar}"
                                   reRender="modalConfirmar"/>
            </h:panelGroup>

            <h:panelGrid id="msgMdlGeradorConsultas" columns="1" width="100%" styleClass="tabMensagens"
                         style="margin:0;">
                <h:outputText styleClass="mensagem" value="#{GeradorConsultasControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{GeradorConsultasControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalConfirmar" width="480" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Confirmar"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="lnkHideModalGonfirmar"/>
            <rich:componentControl for="modalConfirmar" attachTo="lnkHideModalGonfirmar" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalConfirmar">
        <h:panelGroup id="panelConteudoConfirmar">
            <rich:panel>
                <h:outputText styleClass="text" value="#{GeradorConsultasControle.informacaoModalConfirmar}"/>
            </rich:panel>

            <h:panelGroup id="botoes">
                <h:panelGroup style="float: right" rendered="#{GeradorConsultasControle.qtdConsulta == 0}">
                    <a4j:commandButton style="float: right" value="Cancelar"
                                       oncomplete="#{rich:component('modalConfirmar')}.hide();"
                                       reRender="modalConfirmar, modalGeradorConsultas"/>
                </h:panelGroup>
                <h:panelGroup style="float: right;" rendered="#{GeradorConsultasControle.qtdConsulta != 0}">
                    <a4j:commandButton value="Continuar" action="#{GeradorConsultasControle.executarConsulta}"
                                       id="btnConfirmarConsultaGerador"
                                       oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{GeradorConsultasControle.fileName}&mimetype=#{GeradorConsultasControle.mimeType}','GeradorConsultas', 800,200);
                                       #{ExportadorListaControle.msgAlert}; #{rich:component('modalConfirmar')}.hide();"
                                       reRender="modalConfirmar, modalGeradorConsultas"/>
                    <a4j:commandButton value="Cancelar" oncomplete="#{rich:component('modalConfirmar')}.hide();"
                                       reRender="modalConfirmar, modalGeradorConsultas"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="modalConfirmarSelectALL" width="480" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Autoriza��o para Consultar Todos"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="lnkHideModalGonfirmarSelectALL"/>
            <rich:componentControl for="modalConfirmarSelectALL" attachTo="lnkHideModalGonfirmarSelectALL" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalSelectALL">
        <h:panelGroup id="panelConteudoConfirmarSelectALL">
            <rich:panel>
                <h:outputText styleClass="text" value="A consulta em todos os bancos exije uma senha especial. Informe-a:"/>
                <center><h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{GeradorConsultasControle.senhaSelectALL}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('autorizar')}.onclick();return false;"/></center>
                        
                        <h:outputText id="mensagem" styleClass="mensagemDetalhada" value="#{GeradorConsultasControle.mensagemDetalhada}"/>
            </rich:panel>
           
            <h:panelGroup id="botoes">
                <h:panelGroup style="float: right;">
                     <a4j:commandButton id="autorizar" value="#{msg_bt.btn_confirmar}" reRender="modalConfirmarSelectALL,modalConfirmar"
                                         alt="#{msg.msg_gravar_dados}" action="#{GeradorConsultasControle.validarSenhaSelectALL}" oncomplete="#{GeradorConsultasControle.msgAlertAuxiliar}"/>
                     <rich:spacer height="5"/>
                    <a4j:commandButton value="Gerar Apenas para Esse banco" action="#{GeradorConsultasControle.gerarSemSelectAll}" oncomplete="#{rich:component('modalConfirmarSelectALL')}.hide();#{GeradorConsultasControle.msgAlertAuxiliar}"
                                       reRender="modalConfirmar, modalGeradorConsultas"/>
                </h:panelGroup>
                
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

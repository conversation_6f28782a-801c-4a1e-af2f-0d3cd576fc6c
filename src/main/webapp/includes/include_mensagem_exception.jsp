<%--
  Created by IntelliJ IDEA.
  User: rafaelc
  Date: 16/06/2017
  Time: 10:15
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<style>
    .container-mensagem-exception {
        padding: 10px 0 0 0;
    }
</style>
<h:panelGroup layout="block" styleClass="container-mensagem-exception font-size-Em-max texto-font text-center" id="containerMensagemException" rendered="#{MensagemExceptionControle.show}">
    <h:panelGroup layout="block" styleClass="col-text-align-left container-mensagem-exception">
        <h:panelGroup rendered="#{MensagemExceptionControle.exception != null}">
            <i class="fa-icon-warning-sign texto-cor-vermelho texto-size-20"></i>
        </h:panelGroup>

        <h:panelGroup rendered="#{MensagemExceptionControle.exception == null and not empty MensagemExceptionControle.mensagem}">
            <i class="fa-icon-ok-sign texto-cor-verde texto-size-20"></i>
        </h:panelGroup>
        <h:outputText styleClass="texto-cor-cinza" value="#{MensagemExceptionControle.mensagem}"/>
        <h:panelGroup rendered="#{MensagemExceptionControle.exception != null}">
            <a styleClass="linkPadrao texto-size-12 texto-cor-azul" value="Exibir Detalhes" style="cursor: pointer"
               onclick="jQuery(this).parent().parent().find('.container-mensagem-exception-cause').toggleClass('hidden');">
                Detalhes
            </a>
        </h:panelGroup>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="container-mensagem-exception-cause hidden col-text-align-left">
        <h:outputText  styleClass="texto-cor-vermelho" value="#{MensagemExceptionControle.exception.message}"/>
    </h:panelGroup>
</h:panelGroup>

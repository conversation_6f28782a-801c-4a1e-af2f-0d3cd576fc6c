<%-- 
    Document   : include_expiracao
    Created on : 03/11/2011, 18:26:47
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>

<h:panelGroup layout="block" style="display: block" rendered="#{SuporteControle.apresentarMensagemExpiracao and LoginControle.exibirNotificacaoExpiracaoSistema}">

    <h:panelGroup layout="block" style="display: inline-block; vertical-align: top;">
        <div style="display: flex; align-items: center">
            <div style="height: 30px; margin-right: 5px;">
                <h:graphicImage url="#{SuperControle.urlImagemAlertaTodosOsModulos}"
                     alt="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                     style="width: 32.3px; height: 30px; object-fit: contain;" />
            </div>

            <div style="height: 30px">
                <h:panelGroup style="width: 240px;">
                    <h:outputText
                            style="font-family: Arial; font-size: 14px; font-weight: bold; font-style: normal; font-stretch: normal; line-height: 1.18; letter-spacing: normal; text-align: center; color: #333333;"
                            title="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                            id="dataExpiracaoNoLink"
                            value="#{SuporteControle.mensagemExpiracaoTopo}"/>
                </h:panelGroup>
            </div>
        </div>
    </h:panelGroup>

    <%--</h:panelGroup>--%>
    <h:panelGroup style="display: inline-block;vertical-align: middle;">

        <c:if test="${SuporteControle.diasParaExpirarInteger > 0}">
            <h:outputText
                    style="font-size: 12px" styleClass="texto-font texto-cor-cinza"
                    title="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                    value="Para evitar o bloqueio, "/>

            <a4j:commandLink style="font-size: 12px; color: #2f96d2;" styleClass="texto-font texto-cor-cinza"
                             title="Clique para acessar os boletos disponíveis"
                             value="clique aqui "
                             oncomplete="#{CanalPactoControle.mensagemNotificar}"
                             action="#{CanalPactoControle.abrirTelaMinhaContaFaturas}"/>

            <h:outputText
                    style="font-size: 12px" styleClass="texto-font texto-cor-cinza"
                    title="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                    value="para visualizar os boletos."/>
        </c:if>

        <c:if test="${SuporteControle.diasParaExpirarInteger <= 0}">
            <h:outputText
                    rendered="#{LoginControle.usuarioLogado.possuiPerfilAcessoAdministrador}"
                    style="font-size: 12px" styleClass="texto-font texto-cor-cinza"
                    title="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                    value="Verifique seus boletos vencidos "/>

            <a4j:commandLink rendered="#{LoginControle.usuarioLogado.possuiPerfilAcessoAdministrador}"
                             style="font-size: 12px" styleClass="texto-font texto-cor-cinza"
                             title="Clique para acessar os boletos disponíveis"
                             value="clicando aqui."
                             oncomplete="#{CanalPactoControle.mensagemNotificar}"
                             action="#{CanalPactoControle.abrirTelaMinhaContaFaturas}"/>

            <h:outputText
                    rendered="#{!LoginControle.usuarioLogado.possuiPerfilAcessoAdministrador}"
                    style="font-size: 12px" styleClass="texto-font texto-cor-cinza"
                    title="Entre em contato com a Pacto Soluções: (62) 3251-5820"
                    value="Entre em contato com pelo telefone (62) 3251-5820"/>
        </c:if>

    </h:panelGroup>

</h:panelGroup>

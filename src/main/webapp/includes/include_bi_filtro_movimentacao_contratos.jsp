<%--
  Created by IntelliJ IDEA.
  User: athos
  Date: 29/04/2024
  Time: 12:21
--%>
<%@include file="imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>
<rich:modalPanel id="panelFiltroMovimentacaoContratos" width="900" styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtro"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hideLinkMovimentacaoContratos"/>
            <rich:componentControl for="panelFiltroMovimentacaoContratos" attachTo="hideLinkMovimentacaoContratos" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formFiltroMovContrato">
        <div style="display: flex; flex-direction: column; gap: 5px;">
            <h:outputText style="color: #505050; font-weight: 700; font-size: 12px;"
                          title="Os filtros abaixo são aplicados no indicador de movimentação de contratos cancelados."
                          value="Cancelados "/>
            <div style="display: flex; gap: 5px; align-items: center;">
                <h:selectBooleanCheckbox
                        value="#{RotatividadeAnaliticoDWControle.desconsiderarCancelamentoMudancaPlano}"/>
                <span>Desconsiderar cancelamentos por mudança de plano</span>
            </div>
        </div>
        <br>

        <h:dataTable id="tabelaFiltroMovimentacaoContrato" rendered="#{RotatividadeAnaliticoDWControle.exibirOpcoesAgregadores}"
                     value="#{RotatividadeAnaliticoDWControle.configuracoes}" var="config" width="100%">
            <h:column>
                <h:outputText value="#{config.nome}:" styleClass="tituloCampos"/>
            </h:column>
            <h:column>
                <!-- CAMPO CHECKBOX -->
                <h:selectBooleanCheckbox id="filtroTMCheckBox" value="#{config.valorAsBoolean}" rendered="#{config.tpBoolean}">
                    <a4j:support event="onclick"
                                 action="#{RotatividadeAnaliticoDWControle.gravarConfiguracoes}"
                                 reRender="tabelaFiltroMovimentacaoContrato"/>
                </h:selectBooleanCheckbox>

                <!-- COMBO SELECT -->
                <h:panelGroup rendered="#{config.tpCombo}" layout="block" styleClass="cb-container">
                    <h:selectOneMenu value="#{config.valorAsInteger}" styleClass="form">
                        <f:selectItems value="#{config.listaItens}"/>
                        <a4j:support event="onchange"
                                     action="#{RotatividadeAnaliticoDWControle.gravarConfiguracoes}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:column>
        </h:dataTable>

        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink
                    id="salvarBtnFiltroMovContrato"
                    oncomplete="Richfaces.hideModalPanel('panelFiltroMovimentacaoContratos')"
                    title="Salvar filtros e atualizar tela"
                    reRender="idContainerRotatividade"
                    action="#{RotatividadeAnaliticoDWControle.atualizar}"
                    style="vertical-align:middle;line-height: 50px"
                    styleClass="botaoPrimario texto-size-16"
            >
                <span class="texto-size-16 texto-font">Atualizar </span>
                <i style="vertical-align: inherit" class="fa-icon-refresh texto-size-20 texto-cor-branco">
            </a4j:commandLink>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

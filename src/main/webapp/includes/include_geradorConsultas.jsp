<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>

<h:panelGroup rendered="#{LoginControle.usuarioLogado.administrador}">

    <a4j:commandButton id="btnTestar" value="Testar Consultas"
                       actionListener="#{GeradorConsultasControle.selecionarConsultaTeste}"
                       oncomplete="#{GeradorConsultasControle.modalGeradorConsultas}"
                       reRender="modalGeradorConsultas"/>
    <rich:toolTip value="Administrador, teste aqui alguma consulta" for="btnTestar"/>
</h:panelGroup>

<rich:dataTable value="#{GeradorConsultasControle.gruposConsultas}" width="100%" var="grupo" id="geradorconsultasgeral">
    <rich:column>
        <rich:simpleTogglePanel id="categoria" width="100%" switchType="client" headerClass="headerSanfona"
                                rendered="#{!grupo.somenteUsuarioPactoSolucoes || (grupo.somenteUsuarioPactoSolucoes && LoginControle.usuarioLogado.usuarioPactoSolucoes)}"
                                opened="#{grupo.categoriaAberta}">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText styleClass="nomeCol" style="font-weight: bold" value="#{grupo.nomeGrupo}" id="safonageradorcons"/>
                    <h:outputText styleClass="vinc" value=""/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="openMarker">
                <h:panelGroup>
                    <i class="fa-icon-chevron-right"></i>
                </h:panelGroup>
            </f:facet>
            <f:facet name="closeMarker">
                <h:panelGroup>
                    <i class="fa-icon-chevron-down"></i>
                </h:panelGroup>
            </f:facet>
            <a4j:repeat value="#{grupo.consultas}" var="consulta" id="grupoConsultas">
                <h:panelGroup>
                    <a4j:commandLink id="consulta" styleClass="texto" value="#{consulta.nomeConsulta}"
                                     style="margin-left: 20px"
                                     actionListener="#{GeradorConsultasControle.selecionarConsulta}"
                                     oncomplete="#{GeradorConsultasControle.modalGeradorConsultas}"
                                     reRender="modalGeradorConsultas"/>
                    <rich:toolTip value="#{consulta.hintConsulta}" for="consulta"/>
                </h:panelGroup>
                <h:panelGroup style="float: right">
                    <a4j:commandLink id="sql" styleClass="texto" value="?"/>
                    <rich:toolTip followMouse="true" value="#{consulta.SQLMontadaApresentar}" for="sql"/>
                </h:panelGroup>
                <br/>
            </a4j:repeat>
        </rich:simpleTogglePanel>
    </rich:column>
</rich:dataTable>

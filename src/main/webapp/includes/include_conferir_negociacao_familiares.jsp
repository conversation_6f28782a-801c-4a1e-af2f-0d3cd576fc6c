<%@include file="imports.jsp" %>

<style>
    .familiar {
        display: block;
        height: 50px;
        padding-left: 10px;
        margin-top: 10px;
    }

    .familiar .fotopessoa {
        border: 2px solid #094771;
    }

    .nomeFamiliar {
        text-align: left;
        vertical-align: top;
        text-transform: capitalize;
        color: #094771;
        font-weight: bold;
        display: block;
        margin-top: 5px;
        margin-left: 5px;
    }

    .dadosFamiliar {
        margin-top: 5px;
        margin-left: 5px;
        display: block;
        color: #777777;
        text-transform: capitalize;
    }

    .btnsituacao {
        margin-right: 10px;
        height: 20px !important;
    }

    .cfoto {
        border-right: #777 solid;
        text-align: center;
    }

    .cfamilia {
        vertical-align: top;
        padding-left: 20px;
    }
</style>

<h:panelGrid columns="2" width="100%" columnClasses="w40 cfoto, w60 cfamilia" id="negociacaofamiliar">

    <h:panelGroup>
        <h:panelGroup layout="block" styleClass="containerFotoCliente">
            <a4j:mediaOutput element="img" cacheable="false"
                             rendered="#{!SuperControle.fotosNaNuvem}"
                             createContent="#{ContratoControle.paintFoto}"
                             value="#{ImagemData}" mimeType="image/jpeg">
            </a4j:mediaOutput>
            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                            url="#{ContratoControle.paintFotoDaNuvem}"/>
        </h:panelGroup>

        <h:panelGroup layout="block">
            <h:outputText styleClass="texto-size-20 texto-font texto-cor-cinza-2 texto-bold"
                          value="#{ContratoControle.contratoVO.pessoa.nome}"/>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup>
        <div style="font-size: 16px; color: #094771; font-weight: bold">Familiares
        </div>

        <a4j:repeat value="#{NegociacaoFamiliaresControle.familiares}" var="f">
            <h:panelGroup layout="block" styleClass="familiar" rendered="#{f.codigoPessoa != ContratoControle.contratoVO.pessoa.codigo and f.selecionado}">


                <h:panelGroup layout="block" style="position: relative; display: inline-block">
                    <h:graphicImage styleClass="fotopessoa" style="width:43px;height:43px; border-radius: 50%;"
                                    url="#{f.urlFoto}">
                    </h:graphicImage>
                </h:panelGroup>


                <h:panelGroup style="display: inline-block;vertical-align: top;">
                    <span class="nomeFamiliar">
                        <h:outputText value="#{f.primeiroNome} "/>
                    </span>
                    <span class="dadosFamiliar">
                        <h:outputText rendered="#{f.vencimento ne null}" value="Venc. #{f.vencimentoApresentar}"/>
                    </span>


                </h:panelGroup>


            </h:panelGroup>
        </a4j:repeat>
    </h:panelGroup>

</h:panelGrid>

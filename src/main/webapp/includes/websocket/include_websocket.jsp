<%-- 
    Document   : include_websocket
    Created on : 13/06/2017, 10:57:47
    Author     : waller
--%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<script type="text/javascript" src="${root}/script/atmosphere.js"></script>
<script type="text/javascript" src="${root}/script/script.js"></script>
<%@include file="/includes/imports.jsp" %>

<rich:modalPanel id="modalValorNFSeEmitido"
                 showWhenRendered="#{LoginControle.apresentarModalValorNFSeEmitido}"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="700" height="275">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Total Notas Emitidas"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <a4j:commandLink
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    style="color: #fff"
                    status="false"
                    action="#{LoginControle.naoApresentarModalValorNFSeEmitido}"
                    id="hideModalValorNFSeEmitido"/>
            <rich:componentControl for="modalValorNFSeEmitido" attachTo="hideModalValorNFSeEmitido"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                  style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 10px">
        <h:outputText value="Aten��o"
                      style="font-weight: bold; color: red; font-size: 22px"/>
        <br/>
        <br/>
        <h:outputText value="#{LoginControle.msgModalValorNFSeEmitido}"
                      escape="false"
                      style="font-weight: bold; font-size: 16px"/>
    </h:panelGroup>

</rich:modalPanel>
<rich:modalPanel id="modalNotficaGestaoRemessas"
                 showWhenRendered="#{LoginControle.apresentarModalNotificaRemessas}"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Notifica��o"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <a4j:commandLink
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    style="color: #fff"
                    status="false"
                    action="#{LoginControle.naoApresentarModalNotificaRemessas}"
                    id="hideModalNotificaRemessas"/>
            <rich:componentControl for="modalNotficaGestaoRemessas" attachTo="hideModalNotificaRemessas"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                  style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 10px">
        <br/>
        <h:outputText value="#{LoginControle.msgModalNotificaRemessas}"
                      escape="false"
                      style="font-weight: bold; font-size: 16px"/>
    </h:panelGroup>

</rich:modalPanel>

<rich:modalPanel id="modalNotficaCertificado"
                 showWhenRendered="#{LoginControle.apresentarModalNotificaCertificado}"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="250">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Notifica��o"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <a4j:commandLink
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    style="color: #fff"
                    status="false"
                    action="#{LoginControle.naoApresentarModalNotificaCertificado}"
                    id="hideModalNotificaoCertificado"/>
            <rich:componentControl for="modalNotficaCertificado" attachTo="hideModalNotificaoCertificado"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                  style="width: 100%; text-align: left; padding-bottom: 10px; padding-top: 10px">
        <br/>
        <h:outputText value="Certificado(s) A1 de Nota Fiscal #{LoginControle.labelVencimentoCertificado}:<br/><br/>" style="font-weight: bold; font-size: 16px;" escape="false"/>
        <h:outputText value="#{LoginControle.msgModalNotificaCertificado}" escape="false"/>
    </h:panelGroup>

</rich:modalPanel>

<rich:modalPanel id="modalPush"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="380" height="150">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Notifica��o"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkModalPush"/>
            <rich:componentControl for="modalPush" attachTo="hidelinkModalPush" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                  style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 10px">
        <a4j:commandLink action="#{PushUsuarioControle.abrirSocialMailingConversa}"
                         title="Clique para visualizar a mensagem."
                         reRender="form"
                         style="text-decoration: none; text-align: left; font-size: 16px; color: #29AAE2;"
                         oncomplete="#{PushUsuarioControle.msgAlert}">
            <i class="fa-icon-comments-o"></i>
            <h:outputText value="Voc� tem uma nova mensagem!"/>
        </a4j:commandLink>
    </h:panelGroup>
</rich:modalPanel>

<rich:modalPanel id="modalRespostasNaoCompreendidas"
                 showWhenRendered="#{UsuarioControle.usuario.apresentarModalSolicitacoesPendentes}"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="700">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Solicita��es Pendentes"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <a4j:commandLink
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    style="color: #fff"
                    status="false"
                    action="#{UsuarioControle.naoApresentarModalSolicitacoes}"
                    id="hidelinkRespostasNaoCompreendidas"/>
            <rich:componentControl for="modalRespostasNaoCompreendidas" attachTo="hidelinkRespostasNaoCompreendidas"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" id="panelGeralSolicitacoesPendentes" styleClass="panelGeralSolicitacoesPendentes">

        <h:panelGroup layout="block" id="panelMensagemSolicitacoes"
                      style="text-align: center; margin-top: 15px; color: #777">

            <h:outputText value="Aten��o! Verifique suas solicita��es pendentes."
                          style="font-weight: bold; font-size: 16px; color: #474747"/>
            <br/>
            <br/>
            <br/>
            <h:outputText
                    value="O seu pedido foi atendido e as respostas para suas d�vidas j� est�o dispon�veis no sistema."
                    style="font-size: 14px"/>
            <br/>
            <h:outputText value=" Por favor, verifique o status da resposta e confirme que entendeu a resposta."
                          style="font-size: 14px"/>
            <br/>
            <br/>
            <h:outputText value="*Esta mensagem aparecer� sempre que houverem solicita��es n�o-confirmadas."/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="width: 100%; text-align: center; max-height: 200px; overflow-y: auto">

            <table class="tblHeaderLeft semZebra" style="margin-bottom: 5px;">
                <thead>
                <tr>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="C�DIGO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="DESCRI��O"/>
                    </th>
                </tr>
                </thead>

                <tbody>
                <a4j:repeat var="grupo" value="#{UsuarioControle.usuario.listaSolicitacoesRespostasNaoCompreendidas}">

                    <tr>

                        <td>
                            <a4j:commandLink id="linkCodigoSolicitacao"
                                             value="#{grupo.solicitacao_id}"
                                             action="#{LoginControle.abrirSocialMailingSolicitacao}"
                                             oncomplete="#{LoginControle.msgAlert}"
                                             title="Clique para visualizar a mensagem completa."
                                             style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <a4j:commandLink id="linkDescricaoSolicitacao"
                                             value="#{grupo.nomeApresentarSolicitacao}"
                                             action="#{LoginControle.abrirSocialMailingSolicitacao}"
                                             oncomplete="#{LoginControle.msgAlert}"
                                             title="Clique para visualizar a mensagem completa."
                                             style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>

                    </tr>
                </a4j:repeat>
                </tbody>


            </table>


        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 10px; border-top: 1px #E5E5E5 solid">
            <a4j:commandLink
                    id="abrirPanelInformacaoSolicitacao"
                    status="false"
                    style="font-size: 12px; font-weight: bold; color: #29AAE2;"
                    ajaxSingle="true"
                    onclick="abrirPanelInformacaoSolicitacao()">
                <i class="fa-icon-double-angle-up"></i> Mostrar como resolver
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="panelEsconderInformacaoSolicitacoes"
                  id="panelEsconderInformacaoSolicitacoes"
                  style="width: 100%; text-align: center; padding-bottom: 10px; display: none">


        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 10px; border-bottom: 1px #E5E5E5 solid; ">
            <a4j:commandLink
                    id="fecharPanelInformacaoSolicitacao"
                    status="false"
                    style="font-size: 12px; font-weight: bold; color: #29AAE2;"
                    ajaxSingle="true"
                    onclick="fecharPanelInformacaoSolicitacao()">
                <i class="fa-icon-double-angle-down"></i> Ocultar como resolver
            </a4j:commandLink>
        </h:panelGroup>


        <h:panelGroup layout="block"
                      style="text-align: center; margin-top: 15px; color: #777">

            <h:outputText value="Como fa�o para resolver?"
                          style="font-weight: bold; font-size: 16px; color: #474747"/>
            <br/>
            <br/>
            <br/>
            <h:outputText
                    value="Clique na"
                    style="font-size: 14px"/>
            <h:outputText
                    value=" solicita��o "
                    style="font-size: 14px; font-weight: bold"/>
            <h:outputText
                    value="que deseja ver a resposta e na janela que se abrir marque a op��o:"
                    style="font-size: 14px;"/>
            <br/>
            <h:outputText
                    value="\"Entendi a resposta\""
                    style="font-size: 14px;"/>

            <img border="none" src="${root}/images/info_push_social.jpg"/>

        </h:panelGroup>

    </h:panelGroup>

</rich:modalPanel>

<rich:modalPanel id="modalInfoPush"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="600" height="400">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Informa��es"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkInfoPush"/>
            <rich:componentControl for="modalInfoPush" attachTo="hidelinkInfoPush" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <img border="none" src="${root}/images/info_push_social.jpg"/>

</rich:modalPanel>


<script type="text/javascript">

    function abrirPanelInformacaoSolicitacao() {
        jQuery('.panelEsconderInformacaoSolicitacoes').slideDown();
        jQuery('.panelGeralSolicitacoesPendentes').slideUp();
    }

    function fecharPanelInformacaoSolicitacao() {
        jQuery('.panelEsconderInformacaoSolicitacoes').slideUp();
        jQuery('.panelGeralSolicitacoesPendentes').slideDown();
    }


    var wsApi = {
        connectedEndpoint: null,
        callbackAdded: false,
        incompleteMessage: "",
        subscribe: function () {
            function callback(response) {
                if (response.transport != 'polling' && response.state == 'messageReceived') {
                    if (response.status == 200) {
                        var data = response.responseBody;
                        try {
                            if (data.includes("##${SuperControle.key}##")) {
                                chatApi.update(data);
                            }
                        } catch (err) {
                            console.log(err);
                        }
                    }
                }
            }

//            this.connectedEndpoint = atmosphere.subscribe('https://push.pactosolucoes.com.br:8443/oamd-ws/websockets',
//                    !this.callbackAdded ? callback : null,
//                    atmosphere.request = {transport: 'websocket', logLevel: 'none'});
//            callbackAdded = true;
        },
        send: function (message) {
            console.log("Sending message");
            console.log(message);
            this.connectedEndpoint.push(JSON.stringify(message))
        },
        unsubscribe: function () {
            atmosphere.unsubscribe();
        }
    };

    var chatApi = {
        update: function (data) {
            var inputMensagem = document.getElementById('form:mensagemPushInput');
            inputMensagem.value = data;
            document.getElementById('form:processarMensagemPush').click();
        }
    };

    var currentChannel = null;
    wsApi.subscribe();

    function join() {
        if (currentChannel !== null) {
            wsApi.send({"type": "unsubscribe", "channel": currentChannel});
        }
        var channel = "${SuperControle.key}";
        wsApi.send({"type": "subscribe", "channel": channel});
        currentChannel = channel;
    }

    function leave() {
        wsApi.send({"type": "unsubscribe", "channel": currentChannel});
        currentChannel = null;
    }

    //    listen("load", window, join);

</script>
<style>
    .contratosMensagem {

    }
</style>
<h:inputHidden id="mensagemPushInput" value="#{PushUsuarioControle.mensagem}"/>

<a4j:commandLink id="processarMensagemPush"
                 style="visibility: hidden;"
                 action="#{PushUsuarioControle.processarMensagemPush}"
                 oncomplete="#{PushUsuarioControle.onComplete}"
                 status="false"
                 reRender="modalPush"/>
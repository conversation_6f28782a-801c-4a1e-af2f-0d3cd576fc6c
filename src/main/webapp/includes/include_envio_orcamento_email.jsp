<%@include file="imports.jsp" %>
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<rich:modalPanel domElementAttachment="parent" id="modalEnviarOrcamentoEmail"
                 styleClass="novaModal"
                 autosized="true" width="400"
                 height="150" shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup id="pnlTituloEmailOrcamento">
            <h:outputText value="Solicitar envio de Or�amento por Email" rendered="#{EnvioEmailContratoReciboControle.envioDeContrato}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup id="pnlFuncoesEmailOrcamento">
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkEmailOrcamento"/>
                <rich:componentControl for="modalEnviarOrcamentoEmail" attachTo="hidelinkEmailOrcamento" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formModalEnviarOrcamentoEmail">
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

            <h:panelGrid id="panelEnvioOrcamentoEmail"
                         rendered="#{!EnvioEmailContratoReciboControle.sucesso}"
                         columns="3" width="100%">
                <h:outputText styleClass="tituloCampos" value="Email:"/>
                <h:inputText id="novoEmailEnviarOrcamento" size="50" maxlength="50" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{EnvioEmailContratoReciboControle.novoEmailEnviar}"/>
                <a4j:commandLink id="adicionarEmailEnviarOrcamento"
                                 styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                 action="#{EnvioEmailContratoReciboControle.adicionarEmailEnviar}"
                                 reRender="panelEmailEnviarOrcamento, novoEmailEnviarOrcamento">
                    <i class="fa-icon-plus-sign" style="font-size: large;  color: #000"> </i>
                </a4j:commandLink>
            </h:panelGrid>

            <h:panelGroup id="panelEmailEnviarOrcamento">
                <h:dataTable width="100%" headerClass="subordinado"
                             rendered="#{!EnvioEmailContratoReciboControle.sucesso}"
                             styleClass="tabelaDados semZebra"
                             style="margin: 0;"
                             id="emailsselecionadosenviarorcamento"
                             value="#{EnvioEmailContratoReciboControle.listaEmailsEnviar}"
                             var="emailsEnviar">
                    <h:column>
                        <h:outputText style="text-transform: none;" value="#{emailsEnviar.email}"/>
                    </h:column>

                    <h:column>
                        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                            <a4j:commandLink id="removerEmailEnviarOrcamento"
                                             styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                             action="#{EnvioEmailContratoReciboControle.removerEmailEnviar}"
                                             reRender="panelEmailEnviarOrcamento">
                                <i class="fa-icon-minus-sign" style="font-size: large; "> </i>
                            </a4j:commandLink>
                        </h:panelGrid>
                    </h:column>
                </h:dataTable>

                <h:panelGroup layout="block" styleClass="tituloDemonstrativo" style="padding-top: 7px; padding-bottom: 7px;"
                              rendered="#{EnvioEmailContratoReciboControle.sucesso}">
                    <h:outputText styleClass="tituloCampos" value="#{EnvioEmailContratoReciboControle.mensagemDetalhada}"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink id="enviarOrcamento"
                                     rendered="#{EnvioEmailContratoReciboControle.envioDeContrato && !EnvioEmailContratoReciboControle.sucesso}"
                                     value="Enviar"
                                     styleClass="pure-button pure-button-small pure-button-primary"
                                     action="#{EnvioEmailContratoReciboControle.enviarOrcamento}"
                                     reRender="formModalEnviarOrcamentoEmail"/>

                    <a4j:commandLink id="btnFecharEmailOrcamento"
                                     style="margin-left: 8px"
                                     styleClass="pure-button pure-button-small"
                                     value="Fechar"
                                     onclick="#{rich:component('modalEnviarOrcamentoEmail')}.hide();"/>
                </h:panelGroup>

                <h:panelGrid columns="1" width="100%" rendered="#{EnvioEmailContratoReciboControle.erro}">
                    <h:outputText id="msgRealizarContatoOrcamento" styleClass="mensagem"
                                  value="#{EnvioEmailContratoReciboControle.mensagem}"/>
                    <h:outputText id="msgRealizarContatoDetOrcamento" styleClass="mensagemDetalhada"
                                  value="#{EnvioEmailContratoReciboControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

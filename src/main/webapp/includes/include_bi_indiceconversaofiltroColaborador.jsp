<%--
    Document   : include_bi_indiceconversaofiltroColaborador
    Created on : 11/06/2019
    Author     : <PERSON><PERSON>
--%>
<%@include file="imports.jsp" %>
<style>
    .rich-tree-node-icon {
        display: none;
    }

    .rich-tree-node-icon-leaf {
        display: none;

    }
</style>
<rich:modalPanel id="filtroConversaoColaborador" width="800" height="600" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup id="tituloFiltro">
            <h:outputText value="Filtrar #{BIControle.nomeBi} Por Colaborador"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkCol"/>
            <rich:componentControl for="filtroConversaoColaborador" attachTo="hidelinkCol" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formPanelFiltroCol">
        <h:panelGroup id="filtro-colaborador" style="margin-top: 0px;">

            <h:panelGroup layout="block" id="containerFiltroColaborador"
                          style="height: 433px; overflow-y: auto; margin-top: 5px; margin-left: 5px;">
                <a4j:repeat value="#{BIControle.listaGrupoColaboradorIndividual}" var="grupoColaborador" id="repeatgrupos">
                    <h:panelGroup layout="block" styleClass="filtro-container-grupo2"
                                  rendered="#{not empty grupoColaborador.grupoColaboradorParticipanteVOs}">
                        <h:panelGroup layout="block" styleClass="filtro-colaborador-grupo-header" id="grupoMarcadoCss">
                            <a4j:commandLink id="selecionargrupo"
                                             reRender="colCheck, grupoMarcadoCss"
                                             styleClass="btn-toggle-grupo texto-size-14 filtro-col-icon-check2 #{grupoColaborador.todosParticipantesSelecionados ? 'fa-icon-check' : 'fa-icon-check-empty'}"
                                             action="#{BIControle.marcarGrupo}">
                                <a4j:actionparam name="grupoColaborador.codigo"
                                                 value="#{grupoColaborador.codigo}"></a4j:actionparam>
                            </a4j:commandLink>
                            <h:outputText value="#{grupoColaborador.descricaoComQuantidade}"/>
                        </h:panelGroup>
                        <a4j:repeat id="repeatcolaboradores" value="#{grupoColaborador.grupoColaboradorParticipanteVOs}" var="participante">

                                <h:panelGroup layout="block" id="colCheck"
                                              styleClass="filtro-colaborador-grupo-item marcar-item2 hvr-sweep-to-right2"
                                              style="width: 20%; float: none; #{participante.grupoColaboradorParticipanteEscolhido ? 'background: rgba(41, 171, 226, 0.4)' : ''}">


                                        <h:panelGroup layout="block"
                                                      styleClass="filtro-colaborador-img paginaFontResponsiva">
                                            <a4j:mediaOutput element="img"
                                                             rendered="#{!SuperControle.fotosNaNuvem}"
                                                             align="left"
                                                             onclick="jQuery('.cod#{participante.colaboradorParticipante.codigo}').click()"
                                                             cacheable="false" session="false"
                                                             createContent="#{BIControle.paintFoto}"
                                                             styleClass="shadow"
                                                             value="#{ImagemData}" mimeType="image/jpeg">
                                                <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                                <f:param name="largura" value="32"/>
                                                <f:param name="altura" value="32"/>
                                                <f:param name="pessoa"
                                                         value="#{participante.colaboradorParticipante.codigo}"></f:param>
                                            </a4j:mediaOutput>
                                            <a4j:commandLink status="false"
                                                             onclick="jQuery('.cod#{participante.colaboradorParticipante.codigo}').click();return false;"
                                                             styleClass="fa-icon-ok texto-size-16 filtro-col-icon-check"/>
                                            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                            width="150" height="180"
                                                            onclick="jQuery('.cod#{participante.colaboradorParticipante.codigo}').click()"
                                                            url="#{BIControle.urlFotoNuvem}"/>
                                            <h:selectBooleanCheckbox
                                                    styleClass="checkBox-filtro-col"
                                                    value="#{participante.grupoColaboradorParticipanteEscolhido}">
                                            </h:selectBooleanCheckbox>
                                        </h:panelGroup>
                                    <a4j:commandLink id="acaoselecionarcolaborador"
                                                     value="#{participante.colaboradorParticipante.pessoa.nomeAbreviado}"
                                                     title="C�digo: #{participante.colaboradorParticipante.codigo}"
                                                     styleClass="cod#{participante.colaboradorParticipante.codigo} filtro-colaborador-grupo-item-nome tooltipster #{participante.colaboradorParticipante.inativo ? 'texto-cor-vermelho' : '' }"
                                                     action="#{BIControle.marcarColaborador}" reRender="grupoMarcadoCss, colCheck, containerFiltroColaborador">
                                        <a4j:actionparam name="participante.codigo"
                                                         value="#{participante.colaboradorParticipante.codigo}"></a4j:actionparam>
                                    </a4j:commandLink>

                                </h:panelGroup>

                        </a4j:repeat>
                    </h:panelGroup>

                </a4j:repeat>
            </h:panelGroup>
        </h:panelGroup>
        <hr/>
        <h:panelGroup layout="block" style="width: 100%; margin-top: 20px;" styleClass="col-text-align-right">
            <a4j:commandLink style="margin-right: 5px; font-size: 14px;" action="#{BIControle.marcarTodosBIs}"
                             reRender="grupoMarcadoCss, colCheck"
                             value="Marcar Todos "
                             styleClass="botaoSecundario marcarTodosItemFiltro">
                <h:outputText style="vertical-align: inherit" styleClass="fa-icon-check texto-size-14 texto-cor-azul"/>
            </a4j:commandLink>
            <a4j:commandLink style="margin-right: 5px; font-size: 14px;" action="#{BIControle.desmarcarTodosBIs}"
                             reRender="grupoMarcadoCss, colCheck"
                             styleClass="botaoSecundario desmarcarTodosFiltro" value="Desmarcar Todos ">
                <h:outputText style="vertical-align: inherit" styleClass="fa-icon-check-empty texto-size-14 texto-cor-azul"/>
            </a4j:commandLink>
            <a4j:commandLink id="atualizarFiltroIndiceRenovacaoColaborador"
                    reRender="colCheck, grupoMarcadoCss, #{BIControle.reRenderBi}"
                    oncomplete="Richfaces.hideModalPanel('filtroConversaoColaborador'); carregarBIPorDemanda();"
                    title="Aplicar filtro"
                    action="#{BIControle.biAtualizarParam}"
                    style="font-size: 14px;"
                    styleClass="botaoPrimario" value="Atualizar ">
                <h:outputText style="vertical-align: inherit" styleClass="fa-icon-refresh texto-size-14 texto-cor-branco"/>
                <a4j:actionparam name="biAtualizarAbrirConsulta" value="carregar"></a4j:actionparam>
                <a4j:actionparam name="biAtualizar" ></a4j:actionparam>
            </a4j:commandLink>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

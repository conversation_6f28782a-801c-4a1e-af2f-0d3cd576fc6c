<%@page pageEncoding="ISO-8859-1" %>
<head>
    <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
    <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
</head>

<%@include file="imports.jsp" %>

<h:panelGroup>

    <rich:modalPanel id="modalSincronizarBoleto" domElementAttachment="parent"
                     autosized="true" shadowOpacity="false" width="500"
                     styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Sincronizar boleto"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelinkModalSincronizarBoleto"/>
                <rich:componentControl for="modalSincronizarBoleto" attachTo="hidelinkModalSincronizarBoleto"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <h:form id="formModalSincronizarBoleto">
            <h:panelGroup id="panelModalSincronizarBoleto"
                          layout="block" style="padding: 20px">

                <h:panelGroup layout="block" id="panelTitleSincronizarBol"
                              style="padding-top: 10px;padding-bottom: 10px;">
                    <h:outputText value="Verificar se houveram alterações no boleto."
                                  styleClass="texto-size-20 cinza"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnModalSincronizarBol"
                              style="text-align: center; padding: 25px 0 20px 0;">
                    <a4j:commandLink id="confirmarSincronizarBoletoGeral"
                                     reRender="form:tblTransacoesBoletoGeral"
                                     value="Confirmar"
                                     action="#{TelaClienteControle.sincronizarBoleto}"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalCancelarBoleto" domElementAttachment="parent"
                     autosized="true" shadowOpacity="false" width="500"
                     styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cancelar boleto"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelinkModalCancelarBoleto"/>
                <rich:componentControl for="modalCancelarBoleto" attachTo="hidelinkModalCancelarBoleto"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <h:form id="formModalCancelarBoleto">
            <h:panelGroup id="panelModalCancelarBoleto"
                          layout="block" style="padding: 20px">

                <h:panelGroup layout="block" id="panelTitleCancelarBol"
                              style="padding-top: 10px;padding-bottom: 10px;">
                    <h:outputText value="#{TelaClienteControle.mensagemCancelarBoleto}"
                                  styleClass="texto-size-20 cinza"/>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnModalCancelarBol"
                              style="text-align: center; padding: 25px 0 20px 0;">
                    <a4j:commandLink id="confirmarCancelarBoletoBol"
                                     reRender="form:tblTransacoesBoletoGeral"
                                     value="Confirmar"
                                     rendered="#{!TelaClienteControle.boletoCancelarSelecionados}"
                                     action="#{TelaClienteControle.cancelarBoleto}"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}"
                                     styleClass="botaoPrimario texto-size-16"/>

                    <a4j:commandLink id="btnAcaoCancelarBoletosSelecionados"
                                     reRender="form:tblTransacoesBoletoGeral"
                                     value="Confirmar"
                                     rendered="#{TelaClienteControle.boletoCancelarSelecionados}"
                                     action="#{TelaClienteControle.acaoCancelarBoletosSelecionados}"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>
</h:panelGroup>

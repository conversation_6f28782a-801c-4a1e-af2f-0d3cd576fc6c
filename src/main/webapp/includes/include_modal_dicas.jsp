<%@include file="imports.jsp" %>
<link href="${root}/dicas/css/jquery.stepy.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="${root}/dicas/js/jquery.stepy.min.js"></script>
<script type="text/javascript">
    var $j = jQuery.noConflict();
    $j(document).ready(function () {
        $j('.default').stepy({
            description: false,
            legend: false,
            titleClick: true,
            titleTarget: '#title-target'
        });
    });
</script>
<rich:modalPanel showWhenRendered="#{DicasControle.exibirDica}"
                 autosized="false" id="tip" width="680" styleClass="modalDicNovos" minHeight="600">
    <f:facet name="controls">
        <h:panelGroup>
            <i class="fa-icon-remove-circle fecharModal" style="cursor:pointer" id="hideTip"> </i>
            <rich:componentControl for="tip"
                                   attachTo="hideTip" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" styleClass="default margin-v-10">
        <c:forEach items="${DicasControle.montarLista}" var="var">
            <fieldset title="${var.titulo}" style="width: 600px">
                <legend>${var.titulo}</legend>
                <h1 style="font-weight: bold !important; ">${var.titulo}</h1>
                <p class="descr" style="margin-bottom: 10px !important;margin-top: 5px !important;">${var.subTitulo}</p>
                <c:if test="${var.exibirImagem}">
                    <img src="dicas/imagens/${var.imagem}" alt="dicas"/>
                </c:if>
                <c:if test="${var.exibirVideo}">
                    ${var.video}
                </c:if>
                <c:if test="${not var.exibirImagem && not var.exibirVideo}">
                    <c:out value="${var.texto}"/>
                </c:if>

            </fieldset>
        </c:forEach>
        <a href="#" class="finish pure-button pure-button-primary" onclick="Richfaces.hideModalPanel('tip')">Fim!</a>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="pure-u-1 text-left margin-v-10">
        <h:form>
            <h:selectBooleanCheckbox value="#{DicasControle.naoMostrarMais}" style="vertical-align: middle">
                <a4j:support action="#{DicasControle.marcarEsconder}" event="onclick" status="false"/>
            </h:selectBooleanCheckbox>
            <h:outputText value="  N�o mostrar este aviso novamente"/>
        </h:form>
    </h:panelGroup>


</rich:modalPanel>
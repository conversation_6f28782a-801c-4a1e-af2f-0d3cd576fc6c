<%@page pageEncoding="ISO-8859-1" %>
<%@include file="/includes/imports.jsp" %>
<h:panelGroup id="containerListaBoleto">
    <%@include file="/includes/imports.jsp" %>

    <h:panelGroup rendered="#{TelaClienteControle.listaBoleto.count > 0}" layout="block" styleClass="pure-g-r margin-0-auto margin-v-10" style="float:right;">
        <a4j:commandLink id="botaoExcel"
                         styleClass="pure-button pure-button-small"
                         actionListener="#{ExportadorListaControle.exportar}"
                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','BoletosGerados', 800,200);#{ExportadorListaControle.msgAlert}"
                         accesskey="3">
            <f:attribute name="lista" value="#{GestaoRemessasControle.itensRemessaEstorno}"/>
            <f:attribute name="tipo" value="xls"/>
            <f:attribute name="atributos" value="codigo=Código,codigoRemessa=Remessa,codigoStatus=Status,dataVencimentoBoleto=Venc.,movPagamentoBoleto=Mpg,valorBoletoApresentar=V. Tít(R$)"/>
            <f:attribute name="prefixo" value="BoletosGerados"/>
            <f:attribute name="titulo" value="BoletosGerados"/>
            <i class="fa-icon-excel" ></i> &nbsp Excel
        </a4j:commandLink>



        <a4j:commandLink id="botaoPDF"
                         styleClass="pure-button pure-button-small"
                         style="margin-right: 20px;"
                         actionListener="#{ExportadorListaControle.exportar}"
                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','BoletosGerados', 800,200);#{ExportadorListaControle.msgAlert}"
                         accesskey="4">
            <f:attribute name="tipo" value="pdf"/>
            <f:attribute name="lista" value="#{GestaoRemessasControle.itensRemessaBoleto}"/>
            <f:attribute name="atributos" value="codigo=Código,codigoRemessa=Remessa,codigoStatus=Status,dataVencimentoBoleto=Venc.,movPagamentoBoleto=Mpg,valorBoletoApresentar=V. Tít(R$)"/>
            <f:attribute name="prefixo" value="BoletosGerados"/>
            <f:attribute name="titulo" value="BoletosGerados"/>
            <i class="fa-icon-pdf" ></i> &nbsp PDF
        </a4j:commandLink>
    </h:panelGroup>

    <h:outputText value="Boletos Gerados - Remessa"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 negrito cinzaEscuro pl20"/>

    <h:outputText value="Nenhum item de boleto."
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 cinza pl20"
                  rendered="#{TelaClienteControle.listaBoleto.count <= 0}"/>

    <rich:dataTable style="margin-top: 12px" rowClasses="linhaImpar,linhaPar" width="100%" id="tblItensRemessaBoleto"
                    rendered="#{TelaClienteControle.listaBoleto.count > 0}"
                    styleClass="tabelaDados"
                    rowKeyVar="status" value="#{GestaoRemessasControle.itensRemessaBoleto}" var="item">
        <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>
        <rich:column sortBy="#{item.codigo}">
            <f:facet name="header">
                <h:outputText value="Remessa" styleClass="tooltipster" title="Código da Remessa"/>
            </f:facet>
            <h:outputText styleClass="tooltipster" title="Código Item Remessa: #{item.codigo}" value="#{item.remessa.codigo}"/>
        </rich:column>

        <rich:column width="30" sortBy="#{item.codigoStatus}">
            <f:facet name="header">
                <h:outputText value="Status" styleClass="tooltipster" title="Código de retorno enviado pelo banco"/>
            </f:facet>
            <h:outputText style="cursor:pointer;" value="#{item.codigoStatus}" styleClass="tooltipster" title="#{item.descricaoStatus}"/>
        </rich:column>

        <rich:column sortBy="#{item.movParcela.dataVencimento}">
            <f:facet name="header">
                <h:outputText value="Vencimento" styleClass="tooltipster" title="Vencimento da Parcela"/>
            </f:facet>
            <h:outputText value="#{item.dataVencimentoBoleto}">
                <f:convertDateTime pattern="dd/MM/yyyy"/>
            </h:outputText>
        </rich:column>

        <rich:column sortBy="#{item.valorBoleto}">
            <f:facet name="header">
                <h:outputText title="Valor do Título" styleClass="tooltipster" value="Valor"/>
            </f:facet>
            <h:outputText value="#{item.valorBoletoApresentar}"/>
        </rich:column>

        <rich:column sortBy="#{item.movParcela.codigo}">
            <f:facet name="header">
                <h:outputText value="Parcelas do Boleto"/>
            </f:facet>

            <rich:dataTable rowClasses="linhaImpar,linhaPar" width="98%" id="tblParcelasBoleto" rowKeyVar="statusParc"
                            value="#{item.movParcelas}" var="parcela">

                <rich:column rendered="#{SuperControle.widthScreenClient > 1024}"
                             sortBy="#{parcela.movParcelaVO.contrato.codigo}" style="#{parcela.background}">
                    <f:facet name="header">
                        <h:outputText value="Parcela"/>
                    </f:facet>
                    <h:outputText  styleClass="tooltipster" title="Cód. Parcela #{parcela.movParcelaVO.codigo}"
                                   value="#{parcela.movParcelaVO.codigo}"/>
                </rich:column>

                <rich:column sortBy="#{parcela.movParcelaVO.descricao}" style="#{parcela.background}">
                    <f:facet name="header">
                        <h:outputText value="Descrição"/>
                    </f:facet>
                    <h:outputText rendered="#{!empty parcela.movParcelaVO.descricao}"
                                  style="vertical-align:middle;"
                                  title="#{parcela.movParcelaVO.descricao}"
                                  value="#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : '***Estornada***'}"/>
                    <a4j:commandLink rendered="#{empty parcela.movParcelaVO.descricao}"
                                     value="Log Estorno"
                                     onclick="alert('#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : item.logEstorno}');"/>
                </rich:column>

                <rich:column sortBy="#{parcela.movParcelaVO.situacao_Apresentar}" style="#{parcela.background}">
                    <f:facet name="header">
                        <h:outputText title="Situação Atual da Parcela"  styleClass="tooltipster" value="Situação"/>
                    </f:facet>
                    <h:outputText  styleClass="#{item.duplicidade ? 'tooltipster' : ''}"
                                   title="#{item.duplicidade ? 'Significa que foi cobrado do Cartão de Crédito e aprovada, porém a parcela já se encontrava quitada.' : ''}"
                                   value="#{item.duplicidade ? '***Duplicidade***' : parcela.movParcelaVO.situacao_Apresentar}"/>

                </rich:column>


                <rich:column sortBy="#{parcela.movParcelaVO.valorParcela}" style="#{parcela.background}">
                    <f:facet name="header">
                        <h:outputText value="Valor" styleClass="tooltipster" title="Valor da Parcela Cobrada"/>
                    </f:facet>
                    <h:outputText rendered="#{!parcela.valorAlterado}"  value="R$ #{parcela.movParcelaVO.valorParcelaNumerico}"/>

                    <h:outputText rendered="#{parcela.valorAlterado}" styleClass="tooltipster"
                                  title="#{parcela.infoAlteracaoValor}"
                                  value="R$ #{parcela.movParcelaVO.valorParcelaNumerico}">
                        <i class="fa-icon-info-circle"></i>
                    </h:outputText>
                </rich:column>
            </rich:dataTable>

        </rich:column>

        <rich:column styleClass="colunaCentralizada">
            <f:facet name="header">
                <h:outputText title="Opções"/>
            </f:facet>
            <h:panelGroup layout="block" style="display: inline-flex">
                <a4j:commandLink id="imprimirBoletoCliente"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 action="#{BoletoBancarioControle.imprimirBoletoCliente}"
                                 rendered="#{item.mostrarBotaoImprimir}"
                                 styleClass="tooltipster"
                                 title="Imprimir"
                                 oncomplete="abrirPopupPDFImpressao('relatorio/#{BoletoBancarioControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595); #{BoletoBancarioControle.mensagemNotificar}"
                                 reRender="panelMensagem, panelBotoes">
                    <i class="fa-icon-print"></i>
                    <f:setPropertyActionListener value="#{item}" target="#{BoletoBancarioControle.itemReimpressao}"/>
                </a4j:commandLink>

                <a4j:commandLink id="btnEnviarEmail"
                                 rendered="#{item.mostrarBotaoImprimir}"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="tooltipster"
                                 title="Enviar E-mail"
                                 action="#{BoletoBancarioControle.enviarEmailCliente}"
                                 reRender="panelMensagem, panelBotoes, panelConfirmacao">
                    <i class="fa-icon-paper-plane"></i>
                    <f:setPropertyActionListener value="#{item}" target="#{BoletoBancarioControle.itemReimpressao}"/>
                </a4j:commandLink>

                <a4j:commandLink id="btnRemoverParcelaBoleto"
                                 rendered="#{item.permiteRemoverParcelaBoleto}"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="tooltipster"
                                 title="Remover parcela da remessa"
                                 action="#{TelaClienteControle.abrirModalRemoverParcelaBoleto}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteGenerico}"
                                 reRender="formModalRemoverParcelaBoleto">
                    <i class="fa-icon-ban-circle"></i>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>

    </rich:dataTable>

    <h:panelGrid columns="1" rendered="#{TelaClienteControle.listaBoleto.count > 0 }" width="100%" columnClasses="colunaCentralizada">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">
                            <h:outputText
                                    styleClass="texto-size-14 cinza"
                                    value="Total #{TelaClienteControle.listaBoleto.count} itens"></h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink  styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="containerListaBoleto"
                                              actionListener="#{TelaClienteControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_BOLETOS" />
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaBoleto"
                                             actionListener="#{TelaClienteControle.paginaAnterior}">
                                <i class="fa-icon-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_BOLETOS" />
                            </a4j:commandLink>

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                          value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaBoleto.paginaAtualApresentar}" rendered="true"/>
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="containerListaBoleto"
                                             actionListener="#{TelaClienteControle.proximaPagina}">
                                <i class="fa-icon-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_BOLETOS" />
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaBoleto"
                                             actionListener="#{TelaClienteControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_BOLETOS" />
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText
                                        styleClass="texto-size-14 cinza "
                                        value="Itens por página "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{TelaClienteControle.listaBoleto.limit}">
                                    <f:selectItem itemValue="#{6}"></f:selectItem>
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{20}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="containerListaBoleto">
                                        <f:attribute name="tipo" value="LISTA_BOLETOS" />
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>
</h:panelGroup>


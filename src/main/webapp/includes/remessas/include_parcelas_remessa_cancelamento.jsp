<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<a4j:outputPanel>

    <style>
        .rich-fileupload-list-decor {
            border: none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-toolbar-decor {
            background-color: transparent;
            border: none;
        }

        .rich-fileupload-table-td {
            border: none;
        }

        .rich-panel {
            background-color: transparent;
            border: none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-panel-body {
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-ico-add {
            background-image: url(images/drive-upload.png);
        }

        .rich-fileupload-button {
            background-color: transparent;
            background-image: none;
        }

        .rich-fileupload-button-border {
            border: none;
        }

        .rich-fileupload-button-light, .rich-fileupload-button-press {
            background-image: none;
            background-color: transparent;
        }

        .alinhar {
            vertical-align: top !important;
        }
    </style>

    <h:panelGrid columns="1" columnClasses="w50 alinhar,w50 alinhar" width="100%" rowClasses="margin-v-10"
                 id="abaCancelamentoRemessa"
                 styleClass="forcar-v-margin">

        <h:panelGroup layout="block" id="panelGeralFiltrosCancelamento" style="display: inline-flex">
            <h:panelGroup layout="block" id="panelInputNomeAluno" style="margin-top: 2px;">
                <h:outputText styleClass="tituloCampos" style="vertical-align: middle;" value="Cliente:"/>
                <h:inputText id="nomeClienteCancelamento" size="50"
                             maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);"/>

                <rich:suggestionbox height="200" width="200"
                                    for="nomeClienteCancelamento"
                                    fetchValue="#{result.pessoa.nome}"
                                    suggestionAction="#{GestaoRemessasControle.executarAutocompleteConsultaCliente}"
                                    minChars="1" rowClasses="20"
                                    status="statusHora"
                                    nothingLabel="Nenhum Cliente encontrado !"
                                    var="result" id="suggestionBomeClienteCancelamento" reRender="mensagem">
                    <a4j:support event="onselect"
                                 reRender="abaCancelamentoRemessa"
                                 action="#{GestaoRemessasControle.selecionarClienteSuggestionBox}"/>
                    <h:column>
                        <h:outputText value="#{result.pessoa.nome}"/>
                    </h:column>
                </rich:suggestionbox>
                <a4j:commandButton id="limparAluno"
                                   style="margin-left: 5px;vertical-align: middle;"
                                   onclick="document.getElementById('form:nomeClienteCancelamento').value = null;"
                                   image="/images/limpar.gif" title="Limpar aluno."
                                   reRender="abaCancelamentoRemessa"
                                   action="#{GestaoRemessasControle.limparAluno}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelDescricaoParcela" style="margin-left: 20px">
                <h:outputText styleClass="tituloCampos" style="vertical-align: middle;" value="Descrição da Parcela:"/>
                <h:selectOneMenu id="descricaoParcela" value="#{GestaoRemessasControle.filtroParcelaAutorizada}">
                    <f:selectItems value="#{GestaoRemessasControle.selectItemDescricaoParcela}"/>
                    <a4j:support event="onchange" action="#{GestaoRemessasControle.filtrarRemessasParaCancelar}"
                                 reRender="abaCancelamentoRemessa"/>
                </h:selectOneMenu>
            </h:panelGroup>
        </h:panelGroup>

        <rich:panel rendered="#{not empty GestaoRemessasControle.itensAutorizadosFiltrada}">
            <f:facet name="header">
                <h:panelGrid columns="3" columnClasses="esquerda, direita, direita" width="100%">
                    <h:outputText value="#{msg['REALIZAR_REENVIO_CANCELAMENTO']}" style="font-weight:bold" styleClass="text"/>
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                            <a4j:commandLink title="Gerar excel"
                                             styleClass="pure-button pure-button-xsmall"
                                             actionListener="#{GestaoRemessasControle.exportarItensCancelar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="pessoa_Apresentar=Cliente,dataRemessa=Data Remessa,codRemessa=Remessa,descricaoParcela=Parcela,valorParcela=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <i class="fa-icon-table"></i>
                            </a4j:commandLink>

                            <a4j:commandLink title="Imprimir PDF"
                                             styleClass="pure-button pure-button-xsmall margin-h-10"
                                             actionListener="#{GestaoRemessasControle.exportarItensCancelar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="pessoa_Apresentar=Cliente,dataRemessa=Data Remessa,codRemessa=Remessa,descricaoParcela=Parcela,valorParcela=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <f:attribute name="titulo" value="Parcelas"/>
                                <i class="fa-icon-print"></i>
                            </a4j:commandLink>


                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
            <rich:dataTable id="tblItensAutorizados" rowKeyVar="status" width="100%" styleClass="pure-table-striped"
                            columnClasses="colunaCentralizada" value="#{GestaoRemessasControle.itensAutorizadosFiltrada}"
                            rows="#{DataScrollerControle.nrRows}" var="itemAutorizado">
                <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText title="Nome do Cliente" value="Cliente"/>
                    </f:facet>
                    <h:outputText value="#{itemAutorizado.itemRemessaCancelar.movParcela.pessoa_Apresentar}"/>
                </rich:column>


                <rich:column>
                    <f:facet name="header">
                        <h:outputText title="Data de geração da Remessa" value="Dt. Remessa"/>
                    </f:facet>
                    <h:outputText value="#{itemAutorizado.itemRemessaCancelar.remessa.dataRegistro}" title="Data de geração da Remessa">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText title="Código da Remessa" value="Remessa"/>
                    </f:facet>
                    <h:outputText value="#{itemAutorizado.itemRemessaCancelar.remessa.codigo}" title="Código da Remessa"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText title="Descrição da Parcela" value="Descrição"/>
                    </f:facet>
                    <h:outputText value="#{itemAutorizado.itemRemessaCancelar.movParcela.descricao}" title="Descrição da Parcela"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText title="Valor da Parcela" value="Vl. Parc(R$)"/>
                    </f:facet>
                    <h:outputText value="#{itemAutorizado.itemRemessaCancelar.movParcela.valorParcela}" title="Valor da Parcela">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <%--<h:selectBooleanCheckbox title="Marcar/desmarcar todas as parcelas"--%>
                        <%--value="#{GestaoRemessasControle.marcarTodosReenvio}"/>--%>
                        <h:outputText value="Ações"/>
                    </f:facet>
                    <h:selectBooleanCheckbox title="Marcar/desmarcar esta parcela"
                                             value="#{itemAutorizado.selecionado}">
                        <a4j:support event="onchange"
                                     action="#{GestaoRemessasControle.selecionarParcela}"
                                     reRender="tblItensAutorizados"
                                     oncomplete="#{GestaoRemessasControle.mensagemNotificar}"
                                     status="false"/>
                    </h:selectBooleanCheckbox>
                </rich:column>

            </rich:dataTable>
            <rich:datascroller for="tblItensAutorizados" status="false" renderIfSinglePage="false"/>

            <a4j:commandLink styleClass="pure-button margin-v-10 margin-h-10"
                             onclick="if (!confirm('Confirma a geração de Remessa de Cancelamento?')){return false;}"
                             action="#{GestaoRemessasControle.criarRemessaCancelamento}"
                             oncomplete="#{GestaoRemessasControle.msgAlert}"
                             reRender="panelConteudo"
                             style="right:20px;" value="Nova Remessa"/>
        </rich:panel>
    </h:panelGrid>
</a4j:outputPanel>

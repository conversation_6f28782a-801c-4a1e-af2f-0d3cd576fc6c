<%-- 
    Document   : include_itens_remessa
    Created on : 13/12/2012, 19:11:15
    Author     : waller
--%>
<%@include file="/includes/imports.jsp" %>
<%@page pageEncoding="ISO-8859-1"%>
<rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%"
                id="tblItensRemessaBoleto"
                rows="#{fn:contains(pagina, 'impressao') ? 5000 : 10}"
                rowKeyVar="status"
                value="#{GestaoRemessasControle.remessaVO.listaItens}"
                var="itemRemessa">
    <f:facet name="header">
        <h:outputText value="Itens de Remessa de Cobrança"/>
    </f:facet>
    <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>

    <rich:column sortBy="#{itemRemessa.codigo}">
        <f:facet name="header">
            <h:outputText value="Cod."/>
        </f:facet>
        <h:outputText title="Código Item Remessa" value="#{itemRemessa.codigo}"/>
    </rich:column>

    <rich:column sortBy="#{itemRemessa.identificador}">
        <f:facet name="header">
            <h:outputText value="Id."/>
        </f:facet>
        <h:outputText title="Nosso Número" value="#{itemRemessa.identificador}"/>
    </rich:column>


    <rich:column rendered="#{!(fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6'))}"
                 sortBy="#{itemRemessa.pessoa.nome}">
        <f:facet name="header">
            <h:outputText value="Pessoa"/>
        </f:facet>
        <div title="Clique para abrir o Cadastro de Cliente" style="overflow:hidden;width:250px">
            <% if (request.getRequestURI().contains("impressao")) {%>
            <a4j:commandLink value="#{itemRemessa.pessoa.nome}"
                             oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'ClienteGestaoRemessa', 1024, 700);"
                             actionListener="#{ClienteControle.atualizarCliente}" action="#{ClienteControle.acaoAjax}">
                <f:attribute name="pessoa" value="#{itemRemessa.pessoa}"/>
            </a4j:commandLink>
            <%} else {%>
            <a4j:commandLink value="#{itemRemessa.pessoa.nome}"
                             oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'ClienteGestaoRemessa', 1024, 700);"
                             actionListener="#{ClienteControle.atualizarCliente}" action="#{ClienteControle.acaoAjax}">
                <f:attribute name="pessoa" value="#{itemRemessa.pessoa}"/>
            </a4j:commandLink>
            <%}%>

        </div>
    </rich:column>

    <rich:column width="30" sortBy="#{itemRemessa.codigoStatus}">
        <f:facet name="header">
            <h:outputText value="Status"/>
        </f:facet>
        <h:outputText style="cursor:pointer;" value="#{itemRemessa.codigoStatus}" title="\"#{itemRemessa.descricaoStatus}\""/>
    </rich:column>

    <rich:column sortBy="#{itemRemessa.dataVencimentoBoleto}">
        <f:facet name="header">
            <h:outputText value="Venc."/>
        </f:facet>

        <h:outputText title="Data de Vencimento" value="#{itemRemessa.dataVencimentoBoleto}">
            <f:convertDateTime pattern="dd/MM/yyyy"/>
        </h:outputText>
    </rich:column>


    <rich:column sortBy="#{itemRemessa.valorItemRemessa}" styleClass="direita">
        <f:facet name="header">
            <h:outputText value="Valor Título"/>
        </f:facet>

        <h:outputText title="Valor do Título (já inclui multa e juros)" value="#{itemRemessa.valorItemRemessa}">
            <f:converter converterId="FormatadorNumerico"/>
        </h:outputText>
    </rich:column>

    <rich:column sortBy="#{itemRemessa.movPagamento.codigo}">
        <f:facet name="header">
            <h:outputText title="MovPagamento" value="MPg"/>
        </f:facet>
        <h:outputText style="color:blue;font-weight:bold;" title="Código do Movimento de Pagamento"
                      rendered="#{itemRemessa.movPagamento.codigo != 0}"
                      value="#{itemRemessa.movPagamentoBoleto}"/>

        <h:outputText style="color:orange;font-weight:bold;"
                      title="Outra Remessa ou Outra Forma de Pagamento pagou a Parcela"
                      rendered="#{(itemRemessa.movPagamento.codigo == 0) && (itemRemessa.todasParcelasPagas)}"
                      value="#{itemRemessa.movPagamentoBoleto}"/>

        <h:outputText style="color:red;font-weight:bold;"
                      title="O Contrato ou a parcela foi estornada"
                      rendered="#{fn:length(itemRemessa.movParcelas) == 0}"
                      value="#{itemRemessa.movPagamentoBoleto}"/>



    </rich:column>

    <rich:column sortBy="#{itemRemessa.movParcela.codigo}">
        <f:facet name="header">
            <h:outputText title="MovParcela" value="Parcelas do Boleto"/>
        </f:facet>

        <rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%" id="tblParcelasBoleto" rowKeyVar="statusParc"
                        value="#{itemRemessa.movParcelas}" var="parcela">

            <rich:column rendered="#{SuperControle.widthScreenClient > 1024}"
                         sortBy="#{parcela.movParcelaVO.contrato.codigo}">
                <f:facet name="header">
                    <h:outputText title="Contrato" value="Cont."/>
                </f:facet>
                <h:outputText title="Contrato #{parcela.movParcelaVO.contrato.codigo}"
                              value="#{parcela.movParcelaVO.contrato.codigo}"/>
            </rich:column>

            <rich:column sortBy="#{parcela.movParcelaVO.descricao}">
                <f:facet name="header">
                    <h:outputText value="Descrição"/>
                </f:facet>
                <h:outputText rendered="#{!empty parcela.movParcelaVO.descricao}"
                              style="vertical-align:middle;"
                              title="#{parcela.movParcelaVO.descricao}"
                              value="#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : '***Estornada***'}"/>
                <a4j:commandLink rendered="#{empty parcela.movParcelaVO.descricao}"
                                 value="Log Estorno"
                                 onclick="alert('#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : itemRemessa.logEstorno}');"/>
            </rich:column>

            <rich:column sortBy="#{parcela.movParcelaVO.situacao_Apresentar}">
                <f:facet name="header">
                    <h:outputText title="Situação Atual da Parcela" value="Sit."/>
                </f:facet>
                <h:outputText
                        title="#{itemRemessa.duplicidade ? 'Significa que foi cobrado do Cartão de Crédito e aprovada, porém a parcela já se encontrava quitada.' : ''}"
                        value="#{itemRemessa.duplicidade ? '***Duplicidade***' : parcela.movParcelaVO.situacao_Apresentar}"/>

            </rich:column>


            <rich:column sortBy="#{parcela.movParcelaVO.valorParcela}" styleClass="direita">
                <f:facet name="header">
                    <h:outputText value="Valor"/>
                </f:facet>
                <h:outputText title="Valor da Parcela Cobrada" value="R$ #{parcela.movParcelaVO.valorParcelaNumerico}"/>
            </rich:column>

        </rich:dataTable>

    </rich:column>


</rich:dataTable>
<rich:datascroller id="scItensRemessaBoleto" reRender="tblItensRemessaBoleto" maxPages="30"
                   for="tblItensRemessaBoleto"/>

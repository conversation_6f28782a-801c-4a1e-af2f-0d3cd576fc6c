<%-- 
    Document   : include_totais_remessa
    Created on : 13/12/2012, 19:10:10
    Author     : waller
--%>

<%@include file="/includes/imports.jsp" %>

<rich:panel>
    <h:panelGrid columns="5" cellpadding="1" cellspacing="1" columnClasses="text,text"
                 style="padding-top: 15px">

        <rich:column>
            <h:outputText style="font-weight:bold"
                          value="Remessa Id: "/>
            <h:outputText value="#{GestaoRemessasControle.remessaVO.identificador}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold"
                          value="Criada por: "/>
            <h:outputText value="#{GestaoRemessasControle.remessaVO.usuario.nome}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold"
                          value="Data Remessa: "/>
            <h:outputText value="#{GestaoRemessasControle.remessaVO.dataRegistro}">
                <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy HH:mm:ss"/>
            </h:outputText>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold"
                          value="Retorno por: "/>
            <h:outputText value="#{GestaoRemessasControle.remessaVO.usuarioRetorno}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold"
                          value="Data Retorno: "/>
            <h:outputText value="#{GestaoRemessasControle.remessaVO.dataRetorno}">
                <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy HH:mm:ss"/>
            </h:outputText>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold"
                          value="Qtd. Bruto: "/>
            <h:outputText rendered="#{!GestaoRemessasControle.remessaVO.cancelamento}" value="#{fn:length(GestaoRemessasControle.remessaVO.listaItens)}"/>
            <h:outputText rendered="#{GestaoRemessasControle.remessaVO.cancelamento}" value="#{fn:length(GestaoRemessasControle.remessaVO.listaItensCancelamento)}"/>
            <rich:spacer width="10"/>
            <h:outputText style="font-weight:bold"
                          value="Valor Bruto: "/>
            <h:outputText title="Quantidade total de itens enviados nesta remessa"
                          value="R$ #{GestaoRemessasControle.remessaVO.valorBruto_Apresentar}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold"
                          value="Qtd. Aceita: "/>
            <h:outputText title="Quantidade de itens que foram aceitos pela Administradora"
                          value="#{GestaoRemessasControle.remessaVO.qtdAceito}"/>
            <rich:spacer width="10"/>
            <h:outputText style="font-weight:bold" value="Valor Aceito: "/>
            <h:outputText title="Soma dos Itens Aceitos pela Administradora" value="R$ #{GestaoRemessasControle.remessaVO.valorAceito_Apresentar}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold"
                          value="Valor L�quido: "/>
            <h:outputText title="Soma dos Itens Aceitos deduzidos Taxas de Administra��o" value="R$ #{GestaoRemessasControle.remessaVO.valorLiquido_Apresentar}"/>
        </rich:column>

        <rich:column style="border: 0">

        </rich:column>

        <rich:column style="border: 0">

        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold" value="Primeira Tentativa: "/>
            <h:outputText title="Qtd de itens em primeira tentativa" value="#{GestaoRemessasControle.remessaVO.qtdItensPrimeiraTentativa}"/>
            <rich:spacer width="10"/>
            <h:outputText title="Soma dos itens em primeira tentativa" value="R$ #{GestaoRemessasControle.remessaVO.valorItensPrimeiraTentativa_Apresentar}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold" value="Aceito: "/>
            <h:outputText title="Qtd de itens aceitos em primeira tentativa" value="#{GestaoRemessasControle.remessaVO.qtdItensAceitosPrimeiraTentativa}"/>
            <rich:spacer width="10"/>
            <h:outputText title="Soma dos itens aceitos em primeira tentativa" value="R$ #{GestaoRemessasControle.remessaVO.valorItensAceitosPrimeiraTentativa_Apresentar}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold" value="N�o Aceito: "/>
            <h:outputText title="Qtd de itens n�o aceitos em primeira tentativa" value="#{GestaoRemessasControle.remessaVO.qtdItensNaoAceitosPrimeiraTentativa}"/>
            <rich:spacer width="10"/>
            <h:outputText title="Soma dos itens n�o aceitos em primeira tentativa" value="R$ #{GestaoRemessasControle.remessaVO.valorBruto - GestaoRemessasControle.remessaVO.valorAceito}"/>
        </rich:column>

        <rich:column>
            <h:outputText title="Efici�ncia do primeiro envio" value="#{GestaoRemessasControle.remessaVO.eficienciaPrimeiraTentativa}%"/>
        </rich:column>

        <rich:column style="border: 0">

        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold" value="Repescagem: "/>
            <h:outputText title="Qtd de itens em repescagem" value="#{GestaoRemessasControle.remessaVO.qtdItensRepescagem}"/>
            <rich:spacer width="10"/>
            <h:outputText title="Soma dos itens em repescagem" value="R$ #{GestaoRemessasControle.remessaVO.valorItensRepescagem_Apresentar}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold" value="Aceito: "/>
            <h:outputText title="Qtd de itens aceitos em repescagem" value="#{GestaoRemessasControle.remessaVO.qtdItensAceitosRepescagem}"/>
            <rich:spacer width="10"/>
            <h:outputText title="Soma dos itens aceitos em repescagem" value="R$ #{GestaoRemessasControle.remessaVO.valorItensAceitosRepescagem_Apresentar}"/>
        </rich:column>

        <rich:column>
            <h:outputText style="font-weight:bold" value="N�o Aceito: "/>
            <h:outputText title="Qtd de itens n�o aceitos em repescagem" value="#{GestaoRemessasControle.remessaVO.qtdItensNaoAceitosRepescagem}"/>
            <rich:spacer width="10"/>
            <h:outputText title="Soma dos itens n�o aceitos em repescagem" value="R$ #{GestaoRemessasControle.remessaVO.valorItensNaoAceitosRepescagem_Apresentar}"/>
        </rich:column>

        <rich:column>
            <h:outputText title="Efici�ncia da repescagem" value="#{GestaoRemessasControle.remessaVO.eficienciaRepescagem}%"/>
        </rich:column>


    </h:panelGrid>
    
</rich:panel>

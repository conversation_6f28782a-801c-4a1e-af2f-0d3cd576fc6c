<%@include file="/includes/imports.jsp" %>
<rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%" id="tblItensRemessaCancelamento"
                rows="#{fn:contains(pagina, 'impressao') ? 5000 : 10}" rowKeyVar="status"
                value="#{GestaoRemessasControle.remessaVO.listaItensCancelamento}" var="itemCancelamento">
    <f:facet name="header">
        <h:outputText value="Itens de Remessa de Cancelamento"/>
    </f:facet>
    <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>
    <rich:column sortBy="#{itemCancelamento.codigo}">
        <f:facet name="header">
            <h:outputText value="Cod."/>
        </f:facet>
        <h:outputText title="C�digo Item Remessa" value="#{itemCancelamento.codigo}"/>
    </rich:column>

    <rich:column rendered="#{fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6')}"
                 sortBy="#{itemCancelamento.remessa.identificador}">
        <f:facet name="header">
            <h:outputText value="Remessa"/>
        </f:facet>
        <h:outputText title="Remessa" value="#{itemCancelamento.remessa.identificador}"/>
    </rich:column>


    <rich:column rendered="#{!(fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6'))}"
                 sortBy="#{itemCancelamento.itemRemessaCancelar.pessoa.nome}">
        <f:facet name="header">
            <h:outputText value="Pessoa"/>
        </f:facet>
        <div title="Clique para abrir o Cadastro de Cliente" style="overflow:hidden;width:250px">

            <a4j:commandLink value="#{itemCancelamento.itemRemessaCancelar.pessoa.nome}"
                             oncomplete="#{GestaoRemessasControle.onCompleteDetalhes}"
                             actionListener="#{GestaoRemessasControle.abrirTelaClienteColaborador}">
                <f:attribute name="pessoa" value="#{itemCancelamento.itemRemessaCancelar.pessoa}"/>
                <f:attribute name="impressao" value="false"/>
            </a4j:commandLink>

        </div>
    </rich:column>

    <rich:column width="30" sortBy="#{itemCancelamento.codigoStatus}">
        <f:facet name="header">
            <h:outputText value="Status"/>
        </f:facet>
        <h:outputText style="cursor:pointer;" value="#{itemCancelamento.codigoStatus}"
                      title="\"#{itemCancelamento.descricaoStatus}\""/>
    </rich:column>

    <rich:column sortBy="#{itemCancelamento.itemRemessaCancelar.movParcela}">
        <f:facet name="header">
            <h:outputText title="MovParcela" value="MPc"/>
        </f:facet>
        <h:outputText title="Parcela: #{itemCancelamento.itemRemessaCancelar.movParcela.codigo}"
                      value="#{itemCancelamento.itemRemessaCancelar.movParcela.codigo}"/>
    </rich:column>

    <rich:column sortBy="#{itemCancelamento.itemRemessaCancelar.movParcela.descricao}">
        <f:facet name="header">
            <h:outputText value="Descri��o"/>
        </f:facet>
        <h:outputText rendered="#{!empty itemCancelamento.itemRemessaCancelar.movParcela.descricao}"
                      style="vertical-align:middle;"
                      title="#{itemCancelamento.itemRemessaCancelar.movParcela.descricao}"
                      value="#{!empty itemCancelamento.itemRemessaCancelar.movParcela.descricao ? itemCancelamento.itemRemessaCancelar.movParcela.descricao : '***Estornada***'}"/>
        <a4j:commandLink rendered="#{empty itemCancelamento.itemRemessaCancelar.movParcela.descricao}"
                         value="Log Estorno"
                         onclick="alert('#{!empty itemCancelamento.itemRemessaCancelar.movParcela.descricao ? itemCancelamento.itemRemessaCancelar.movParcela.descricao : itemCancelamento.itemRemessaCancelar.logEstorno}');"/>
    </rich:column>

    <rich:column sortBy="#{itemCancelamento.itemRemessaCancelar.movPagamento.codigo}">
        <f:facet name="header">
            <h:outputText title="MovPagamento" value="MPg"/>
        </f:facet>
        <h:outputText style="#{itemCancelamento.itemRemessaCancelar.styleMovPagamento};font-weight:bold"
                      title="#{itemCancelamento.itemRemessaCancelar.titleMovPagamento}"
                      value="#{itemCancelamento.itemRemessaCancelar.movPagamento_Apresentar}"/>
    </rich:column>

<%--    <rich:column sortBy="#{itemCancelamento.itemRemessaCancelar.movParcela.situacao_Apresentar}">
        <f:facet name="header">
            <h:outputText title="Situa��o Atual da Parcela" value="Sit."/>
        </f:facet>
        <h:outputText
                title="#{itemCancelamento.itemRemessaCancelar.duplicidade ? 'Significa que foi cobrado do Cart�o de Cr�dito e aprovada, por�m a parcela j� se encontrava quitada.' : ''}"
                value="#{itemCancelamento.itemRemessaCancelar.duplicidade ? '***Duplicidade***' : itemCancelamento.itemRemessaCancelar.movParcela.situacao_Apresentar}"/>
    </rich:column>

    <rich:column sortBy="#{itemCancelamento.itemRemessaCancelar.movParcela.valorParcela}">
        <f:facet name="header">
            <h:outputText value="Valor"/>
        </f:facet>
        <h:outputText title="Valor da Parcela Cobrada"
                      value="R$ #{itemCancelamento.itemRemessaCancelar.movParcela.valorParcelaNumerico}"/>
    </rich:column>--%>

    <rich:column>
        <f:facet name="header">
            <h:outputText value="Parcelas"/>
        </f:facet>

        <rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%" id="tblParcelasBoleto" rowKeyVar="statusParc"
                        value="#{itemCancelamento.itemRemessaCancelar.movParcelas}" var="parcela">

            <rich:column rendered="#{SuperControle.widthScreenClient > 1024}" style="#{parcela.background}"
                         sortBy="#{parcela.movParcelaVO.contrato.codigo}">
                <f:facet name="header">
                    <h:outputText value="Parcela"/>
                </f:facet>
                <h:outputText  styleClass="tooltipster" title="Contrato #{parcela.movParcelaVO.codigo}"
                               value="#{parcela.movParcelaVO.codigo}"/>
            </rich:column>

            <rich:column sortBy="#{parcela.movParcelaVO.descricao}" style="#{parcela.background}">
                <f:facet name="header">
                    <h:outputText value="Descri��o"/>
                </f:facet>
                <h:outputText rendered="#{!empty parcela.movParcelaVO.descricao}"
                              style="vertical-align:middle;"
                              title="#{parcela.movParcelaVO.descricao}"
                              value="#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : '***Estornada***'}"/>
                <a4j:commandLink rendered="#{empty parcela.movParcelaVO.descricao}"
                                 value="Log Estorno"
                                 onclick="alert('#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : item.logEstorno}');"/>
            </rich:column>

            <rich:column sortBy="#{parcela.movParcelaVO.situacao_Apresentar}" style="#{parcela.background}">
                <f:facet name="header">
                    <h:outputText title="Situa��o Atual da Parcela"  styleClass="tooltipster" value="Situa��o"/>
                </f:facet>
                <h:outputText  styleClass="#{item.duplicidade ? 'tooltipster' : ''}"
                               title="#{item.duplicidade ? 'Significa que foi cobrado do Cart�o de Cr�dito e aprovada, por�m a parcela j� se encontrava quitada.' : ''}"
                               value="#{item.duplicidade ? '***Duplicidade***' : parcela.movParcelaVO.situacao_Apresentar}"/>

            </rich:column>


            <rich:column sortBy="#{parcela.movParcelaVO.valorParcela}" style="#{parcela.background}">
                <f:facet name="header">
                    <h:outputText value="Valor" styleClass="tooltipster" title="Valor da Parcela Cobrada"/>
                </f:facet>
                <h:outputText rendered="#{!parcela.valorAlterado}"  value="R$ #{parcela.movParcelaVO.valorParcelaNumerico}"/>

                <h:outputText rendered="#{parcela.valorAlterado}" styleClass="tooltipster"
                              title="#{parcela.infoAlteracaoValor}"
                              value="R$ #{parcela.movParcelaVO.valorParcelaNumerico}">
                    <i class="fa-icon-info-circle"></i>
                </h:outputText>
            </rich:column>
        </rich:dataTable>

    </rich:column>


</rich:dataTable>
<rich:datascroller id="scItensRemessaCancelamento" reRender="tblItensRemessaCancelamento" maxPages="30"
                   for="tblItensRemessaCancelamento"/>

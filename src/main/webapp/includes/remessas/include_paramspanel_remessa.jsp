<%-- 
    Document   : include_paramspanel_remessa
    Created on : 22/11/2012, 18:05:03
    Author     : waller
--%>
<%@page pageEncoding="ISO-8859-1"%>
<%@include file="/includes/imports.jsp" %>
<link href="../../css/font-awesome-4/css/font-awesome.css" type="text/css" rel="stylesheet"/>
<head>
    <style>
        .export {
            font-family: FontAwesome;
            font-size: 44px;
            color: #29ABE2;
            border-radius: 2px;
            padding: 2px;
            cursor: pointer;
            background-color: white;
        }
        .export.excel:before {
            content: "\f1c3";
        }
        .export.pdf:before {
            content: "\f1c1";
        }
    </style>
</head>

<a4j:outputPanel>
    <a4j:form id="formParamsRemessa">
        <%-- PARÂMETROS UTILIZADOS NA TRANSMISSÃO DA remessa--%>
        <rich:modalPanel id="panelDadosParametros" showWhenRendered="#{GestaoRemessasControle.exibirModalParametros}"
                         width="300"
                         autosized="true"
                         shadowOpacity="true">

            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Remessa: #{GestaoRemessasControle.remessaVO.dataRegistro}"/>
                </h:panelGroup>
            </f:facet>

            <f:facet name="controls">
                <h:panelGroup>
                    <a4j:commandButton id="btnFecharEnvio" image="imagens/close.png" style="cursor:pointer"
                                       action="#{GestaoRemessasControle.fecharPanelDadosParametros}"
                                       oncomplete="#{rich:component('panelDadosParametros')}.hide();"/>
                </h:panelGroup>
            </f:facet>
            <h:panelGroup>
                <c:if test="${empty GestaoRemessasControle.listaParametrosSelecionado}">
                    <h:panelGrid columns="2">
                        <h:graphicImage value="images/warning.png"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="Não há parâmetros para a situação atual desta remessa."/>
                    </h:panelGrid>
                </c:if>
                <rich:scrollableDataTable width="100%" value="#{GestaoRemessasControle.listaParametrosSelecionado}"
                                          var="obj"
                                          columnClasses="w50,w50">
                    <f:facet name="header">
                        <c:if test="${!empty GestaoRemessasControle.listaParametrosSelecionado}">
                            <h:outputText value="Parâmetros utilizados"/>
                        </c:if>
                    </f:facet>

                    <rich:column>
                        <h:outputText value="#{obj.atributo}"/>
                    </rich:column>
                    <rich:column>
                        <h:outputText style="font-weight:bold;" value="#{obj.valor}"/>
                    </rich:column>

                </rich:scrollableDataTable>
            </h:panelGroup>
        </rich:modalPanel>

        <%-- DADOS DETALHADOS DA remessa--%>

        <rich:modalPanel id="panelDadosRemessa" showWhenRendered="#{GestaoRemessasControle.exibirRemessa}"
                         width="#{SuperControle.widthScreenClient - (SuperControle.widthScreenClient*20/100)}"
                         autosized="true"
                         shadowOpacity="true">

            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Remessa: #{GestaoRemessasControle.remessaVO.identificador}"/>
                </h:panelGroup>
            </f:facet>

            <f:facet name="controls">
                <h:panelGroup>

                    <a4j:commandButton id="btnFechar" image="imagens/close.png" style="cursor:pointer"
                                       action="#{GestaoRemessasControle.fecharPanelDadosParametros}"
                                       oncomplete="#{rich:component('panelDadosRemessa')}.hide();"/>
                </h:panelGroup>
            </f:facet>

            <div style="overflow: auto; width:${SuperControle.widthScreenClient - (SuperControle.widthScreenClient*20/100)}px;height: 350px;">
                <h:panelGroup id="panelBasico" rendered="#{!GestaoRemessasControle.remessaVO.cancelamento}">
                    <%@include file="include_itens_remessa.jsp" %>
                </h:panelGroup>

                <h:panelGroup id="panelBasicoCancelamento" rendered="#{GestaoRemessasControle.remessaVO.cancelamento}">
                    <%@include file="include_itens_remessa_cancelamento.jsp" %>
                </h:panelGroup>

                <h:panelGrid id="mensagemModal" columns="1" width="100%" styleClass="tabMensagens"
                             style="margin:0 0 0 0;">
                    <h:outputText id="msgOperacoes" styleClass="mensagem" value="#{GestaoRemessasControle.mensagem}"/>
                    <h:outputText id="msgOperacoesDet" styleClass="mensagemDetalhada"
                                  value="#{GestaoRemessasControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </div>

            <%@include file="include_totais_remessa.jsp" %>

            <div style="width: 100%; padding-top: 5px; padding-left: 5px;">
                <a4j:commandButton title="Versão para Impressão"
                                   rendered="#{!GestaoRemessasControle.remessaVO.cancelamento}"
                                   id="btnImpressao"
                                   style="text-align:right;"
                                   image="images/printer_basic_blue.png"
                                   oncomplete="abrirPopup('includes/remessas/impressao_lista_itens_remessa.jsp', 'Detalhes da Remessa', 1024, 700);"
                                   action="#{GestaoRemessasControle.imprimirDetalhe}"/>

                <a4j:commandButton title="Versão para Impressão"
                                   rendered="#{GestaoRemessasControle.remessaVO.cancelamento}"
                                   id="btnImpressaoRemessaCancelamento"
                                   style="text-align:right;"
                                   image="images/printer_basic_blue.png"
                                   oncomplete="abrirPopup('includes/remessas/impressao_lista_itens_remessa_cancelamento.jsp', 'Detalhes da Remessa', 1024, 700);"
                                   action="#{GestaoRemessasControle.imprimirDetalhe}"/>

                <a4j:commandLink id="btnExcel" styleClass="exportadores" style="position: relative; left: 10px; top: -7px;"
                                 actionListener="#{GestaoRemessasControle.exportarItensRemessa}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}#{GestaoRemessasControle.mensagemNotificar}"
                                 accesskey="3">

                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="codigo=Código,movParcela.pessoa.nome=Pessoa,valorCartaoMascaradoOuAgenciaConta=Cartão/Ag/CC,
                                        codigoStatus=Status,dataPrevistaPagamento=Previsão Pg,autorizacao=Autorização,identificador=Id.Cob.,
                                        movParcela.codigo=Cod. Parcela,movParcela.descricao=Descrição,movParcela.dataVencimento=Vencimento,movParcela.dataCobranca=Dt. Cobrança,
                                        nrTentativaParcela=Tentativas,movPagamento.codigo=Cod. Pgto,movParcela.situacao_Apresentar=Situação,
                                        movParcela.valorParcela=Valor Parcela,movParcela.valorMulta=Valor Multa,movParcela.valorJuros=Juros,
                                        valorItemRemessa=Valor Total,movParcela.empresa_Apresentar=Empresa"/>
                    <f:attribute name="prefixo" value="Remessa"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="export excel"/>
                </a4j:commandLink>

                <a4j:commandLink id="btnPdf" styleClass="exportadores" style="position: relative; left: 15px; top: -7px;"
                                 actionListener="#{GestaoRemessasControle.exportarItensRemessa}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}#{GestaoRemessasControle.mensagemNotificar}"
                                 accesskey="3">

                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                 value="codigo=Código,movParcela.pessoa.nome=Pessoa,valorCartaoMascaradoOuAgenciaConta=Cartão/Ag/CC,
                                        codigoStatus=Status,dataPrevistaPagamento=Previsão Pg,autorizacao=Autorização,identificador=Id.Cob.,
                                        movParcela.codigo=Cod. Parcela,movParcela.descricao=Descrição,movParcela.dataVencimento=Vencimento,movParcela.dataCobranca=Dt. Cobrança,
                                        nrTentativaParcela=Tentativas,movPagamento.codigo=Cod. Pgto,movParcela.situacao_Apresentar=Situação,
                                        movParcela.valorParcela=Valor Parcela,movParcela.valorMulta=Valor Multa,movParcela.valorJuros=Juros,
                                        valorItemRemessa=Valor Total,movParcela.empresa_Apresentar=Empresa"/>
                    <f:attribute name="prefixo" value="Remessa"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="export pdf"/>
                </a4j:commandLink>
            </div>
        </rich:modalPanel>


        <rich:modalPanel id="panelDadosRemessaNovo" showWhenRendered="#{GestaoRemessasControle.exibirRemessaNovo}"
                         width="#{SuperControle.widthScreenClient - (SuperControle.widthScreenClient*20/100)}"
                         autosized="true"
                         shadowOpacity="true">

            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Remessa: #{GestaoRemessasControle.remessaVO.identificador}"/>
                </h:panelGroup>
            </f:facet>

            <f:facet name="controls">
                <h:panelGroup>
                    <a4j:commandButton id="btnFecharNovo" image="imagens/close.png" style="cursor:pointer"
                                       action="#{GestaoRemessasControle.fecharPanelDadosParametros}"
                                       oncomplete="#{rich:component('panelDadosRemessaNovo')}.hide();"/>
                </h:panelGroup>
            </f:facet>

            <div style="overflow: auto; width:${SuperControle.widthScreenClient - (SuperControle.widthScreenClient*10/100)}px;height: 500px;">
                <h:panelGroup id="panelBasicoNovo">
                    <%@include file="include_itens_remessa_novo.jsp" %>
                </h:panelGroup>

                <h:panelGrid id="mensagemModalNovo" columns="1" width="100%" styleClass="tabMensagens"
                             style="margin:0 0 0 0;">
                    <h:outputText id="msgOperacoesNovo" styleClass="mensagem" value="#{GestaoRemessasControle.mensagem}"/>
                    <h:outputText id="msgOperacoesDetNovo" styleClass="mensagemDetalhada"
                                  value="#{GestaoRemessasControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </div>

            <%@include file="include_totais_remessa.jsp" %>

            <div style="width: 100%; padding-top: 5px; padding-left: 5px; display: flex; align-items: center; gap: 15px;">
                <a4j:commandButton title="Versão para Impressão"
                                   style="text-align:right;"
                                   image="images/printer_basic_blue.png"
                                   oncomplete="abrirPopup('includes/remessas/impressao_lista_itens_remessa.jsp', 'Detalhes da Remessa', 1024, 700);"
                                   action="#{GestaoRemessasControle.imprimirDetalhe}"/>

                <a4j:commandLink id="btnExcelNovo" styleClass="exportadores"
                                 actionListener="#{GestaoRemessasControle.exportarItensRemessa}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}#{GestaoRemessasControle.mensagemNotificar}"
                                 accesskey="3">

                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="codigo=Código,pessoa.nome=Pessoa,valorCartaoMascaradoOuAgenciaConta=Cartão/Ag/CC,
                                        codigoStatus=Status,dataPrevistaPagamento=Previsão Pg,autorizacao=Autorização,identificador=Id.Cob.,
                                        movParcela.codigo=Cod. Parcela,movParcela.descricao=Descrição,movParcela.dataVencimento=Vencimento,movParcela.dataCobranca=Dt. Cobrança,
                                        nrTentativaParcela=Tentativas,movPagamento.codigo=Cod. Pgto,movParcela.situacao_Apresentar=Situação,
                                        movParcela.valorParcela=Valor Parcela,movParcela.valorMulta=Valor Multa,movParcela.valorJuros=Juros,
                                        valorItemRemessa=Valor Total,movParcela.empresa_Apresentar=Empresa"/>
                    <f:attribute name="prefixo" value="Remessa"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="export excel"/>
                </a4j:commandLink>

                <a4j:commandLink id="btnPdfNovo" styleClass="exportadores"
                                 actionListener="#{GestaoRemessasControle.exportarItensRemessa}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}#{GestaoRemessasControle.mensagemNotificar}"
                                 accesskey="3">

                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                 value="codigo=Código,pessoa.nome=Pessoa,valorCartaoMascaradoOuAgenciaConta=Cartão/Ag/CC,
                                        codigoStatus=Status,dataPrevistaPagamento=Previsão Pg,autorizacao=Autorização,identificador=Id.Cob.,
                                        movParcela.codigo=Cod. Parcela,movParcela.descricao=Descrição,movParcela.dataVencimento=Vencimento,movParcela.dataCobranca=Dt. Cobrança,
                                        nrTentativaParcela=Tentativas,movPagamento.codigo=Cod. Pgto,movParcela.situacao_Apresentar=Situação,
                                        movParcela.valorParcela=Valor Parcela,movParcela.valorMulta=Valor Multa,movParcela.valorJuros=Juros,
                                        valorItemRemessa=Valor Total,movParcela.empresa_Apresentar=Empresa"/>
                    <f:attribute name="prefixo" value="Remessa"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="export pdf"/>
                </a4j:commandLink>
            </div>
        </rich:modalPanel>

        <rich:modalPanel id="panelDadosRemessaBoleto" showWhenRendered="#{GestaoRemessasControle.exibirRemessaBoleto}"
                         width="#{SuperControle.widthScreenClient - (SuperControle.widthScreenClient*20/100)}"
                         autosized="true" shadowOpacity="true">
            <f:facet name="header">
                <h:outputText value="Remessa: #{GestaoRemessasControle.remessaVO.identificador}"/>
            </f:facet>

            <f:facet name="controls">
                <a4j:commandButton image="imagens/close.png" style="cursor:pointer"
                                   action="#{GestaoRemessasControle.fecharPanelDadosParametros}"
                                   oncomplete="#{rich:component('panelDadosRemessaBoleto')}.hide();"/>
            </f:facet>
            <div style="overflow: auto; width:${SuperControle.widthScreenClient - (SuperControle.widthScreenClient*20/100)}px;height: 350px;">
                <h:panelGroup id="panelBasicoBoleto">
                    <%@include file="include_itens_boleto.jsp" %>
                </h:panelGroup>

                <h:panelGrid id="mensagemModalBoleto" columns="1" width="100%" styleClass="tabMensagens"
                             style="margin:0 0 0 0;">
                    <h:outputText styleClass="mensagem" value="#{GestaoRemessasControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{GestaoRemessasControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </div>

            <jsp:include page="/includes/remessas/include_totais_remessa.jsp" flush="true"/>

            <div style="width: 100%; padding-top: 5px; padding-left: 5px; display: flex; align-items: center; gap: 15px;">
                <a4j:commandButton title="Versão para Impressão"
                                   style="text-align:right;"
                                   image="images/printer_basic_blue.png"
                                   oncomplete="abrirPopup('includes/remessas/impressao_lista_itens_remessa.jsp', 'Detalhes da Remessa', 1024, 700);"
                                   action="#{GestaoRemessasControle.imprimirDetalhe}"/>

                <a4j:commandLink id="btnExcelBoleto" styleClass="exportadores"
                                 actionListener="#{GestaoRemessasControle.exportarItensRemessaBoleto}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="3">

                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos"
                                 value="sequencia=S.,codigo=Código,identificador=Id.,pessoa=Pessoa,
                                        status=Status,dataVencimento=Venc.,valorTitulo=Valor Título,
                                        codigoPagamento=Cod. Pgto,codigoParcela=Cont.,
                                        descricaoParcela=Descrição,situacaoParcela=Situação,valorParcela=Valor"/>
                    <f:attribute name="prefixo" value="Remessa"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="export excel"/>
                </a4j:commandLink>

                <a4j:commandLink id="btnPdfBoleto" styleClass="exportadores"
                                 actionListener="#{GestaoRemessasControle.exportarItensRemessaBoleto}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="3">

                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos"
                                 value="sequencia=S.,codigo=Código,identificador=Id.,pessoa=Pessoa,
                                        status=Status,dataVencimento=Venc.,valorTitulo=Valor Título,
                                        codigoPagamento=Cod. Pgto,codigoParcela=Cont.,
                                        descricaoParcela=Descrição,situacaoParcela=Situação,valorParcela=Valor"/>
                    <f:attribute name="prefixo" value="Remessa"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="export pdf"/>
                </a4j:commandLink>
            </div>
        </rich:modalPanel>
    </a4j:form>

    <rich:modalPanel autosized="true" id="panelDadosAutorizacoes" width="970"
                     showWhenRendered="#{GestaoRemessasControle.exibirAutorizacoes}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Autorizações de Cobrança Cadastradas"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <a4j:form ajaxSingle="true" ajaxSubmit="true">
                <h:panelGroup>
                    <a4j:commandButton id="btnFecharAutorizacoes" image="../imagens/close.png" style="cursor:pointer"
                                       action="#{GestaoRemessasControle.fecharModalAutorizacoes}"
                                       oncomplete="#{rich:component('panelDadosAutorizacoes')}.hide();"/>
                </h:panelGroup>
            </a4j:form>
        </f:facet>
        <a4j:form id="formAutorizacoes">
            <h:panelGroup style="position: absolute; right: 12px;">

                <a4j:commandLink id="btnRealizarContato" styleClass="pure-button pure-button-success"
                                 value="Realizar contato"
                                 action="#{AutorizacaoCobrancaControle.abrirContatoCliente}" ajaxSingle="true"
                                 oncomplete="#{AutorizacaoCobrancaControle.msgAlert}"/>

            </h:panelGroup>

            <%@include file="../../includes/cliente/include_box_autorizacao_cobranca.jsp" %>

            <a4j:commandLink styleClass="pure-button"
                             reRender="panelDadosAutorizacoes" value="Gravar"
                             action="#{AutorizacaoCobrancaControle.gravar}"/>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelDadosRemessaContatoCielo"
                     width="300"
                     autosized="false"
                     height="150"
                     shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Entrar em contato com a Cielo"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkConfigAcoesCielo"/>
                <rich:componentControl for="panelDadosRemessaContatoCielo" attachTo="hidelinkConfigAcoesCielo"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:panelGrid columns="2" width="100%" styleClass="tituloCampos"
                     columnClasses="esquerda,direita">
            <h:outputText value="Número Cielo:" style="font-weight:bold;"/>
            <h:outputText value="#{GestaoRemessasControle.numeroCielo}"/>

            <h:outputText value="Número Estabelecimento:" style="font-weight:bold;"/>
            <h:outputText value="#{GestaoRemessasControle.numeroEstabelecimento}"/>

            <h:outputText value="Data Envio:" style="font-weight:bold;"/>
            <h:outputText value="#{GestaoRemessasControle.dataEnvioApresentar}"/>

            <h:outputText value="Data Retorno:" style="font-weight:bold;"/>
            <h:outputText value="#{GestaoRemessasControle.dataRetornoApresentar}"/>
        </h:panelGrid>
    </rich:modalPanel>

</a4j:outputPanel>

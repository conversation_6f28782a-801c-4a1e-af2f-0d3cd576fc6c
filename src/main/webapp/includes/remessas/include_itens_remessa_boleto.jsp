<%@page pageEncoding="ISO-8859-1"%>
<%@include file="/includes/imports.jsp" %>

<h:panelGroup rendered="#{not empty GestaoRemessasControle.itensRemessaBoleto}" layout="block" styleClass="pure-g-r margin-0-auto margin-v-10" style="float:right;">
        <a4j:commandLink id="botaoExcel"
                         styleClass="pure-button pure-button-small"
                         actionListener="#{ExportadorListaControle.exportar}"
                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','BoletosGerados', 800,200);#{ExportadorListaControle.msgAlert}"
                         accesskey="3">
            <f:attribute name="lista" value="#{GestaoRemessasControle.itensRemessaEstorno}"/>
            <f:attribute name="tipo" value="xls"/>
            <f:attribute name="atributos" value="codigo=Código,codigoRemessa=Remessa,codigoStatus=Status,dataVencimentoBoleto=Venc.,movPagamentoBoleto=Mpg,valorBoletoApresentar=V. Tít(R$)"/>
            <f:attribute name="prefixo" value="BoletosGerados"/>
            <f:attribute name="titulo" value="BoletosGerados"/>
            <i class="fa-icon-excel" ></i> &nbsp Excel
        </a4j:commandLink>



        <a4j:commandLink id="botaoPDF"
                         styleClass="pure-button pure-button-small"
                         style="margin-right: 20px;"
                         actionListener="#{ExportadorListaControle.exportar}"
                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','BoletosGerados', 800,200);#{ExportadorListaControle.msgAlert}"
                         accesskey="4">
            <f:attribute name="tipo" value="pdf"/>
            <f:attribute name="lista" value="#{GestaoRemessasControle.itensRemessaBoleto}"/>
            <f:attribute name="atributos" value="codigo=Código,codigoRemessa=Remessa,codigoStatus=Status,dataVencimentoBoleto=Venc.,movPagamentoBoleto=Mpg,valorBoletoApresentar=V. Tít(R$)"/>
            <f:attribute name="prefixo" value="BoletosGerados"/>
            <f:attribute name="titulo" value="BoletosGerados"/>
            <i class="fa-icon-pdf" ></i> &nbsp PDF
        </a4j:commandLink>
</h:panelGroup>

<h:outputText value="Boletos Gerados" 
              style="margin-top: 20px; display: block;"
              styleClass="texto-size-16 negrito cinzaEscuro pl20"/>

<h:outputText value="Nenhum item de boleto."
              style="margin-top: 20px; display: block;"
              styleClass="texto-size-16 cinza pl20"
              rendered="#{fn:length(GestaoRemessasControle.itensRemessaBoleto) eq 0}"/>

<rich:dataTable style="margin-top: 12px" rowClasses="linhaImpar,linhaPar" width="100%" id="tblItensRemessaBoleto"
                rendered="#{fn:length(GestaoRemessasControle.itensRemessaBoleto) gt 0}"
                rows="#{GestaoRemessasControle.nrPaginasItensRemessaBoleto}" styleClass="tabelaDados"
                rowKeyVar="status" value="#{GestaoRemessasControle.itensRemessaBoleto}" var="item">
    <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>
    <rich:column sortBy="#{item.codigo}">
        <f:facet name="header">
            <h:outputText value="Remessa" styleClass="tooltipster" title="Código da Remessa"/>
        </f:facet>
        <h:outputText styleClass="tooltipster" title="Código Item Remessa: #{item.codigo}" value="#{item.remessa.codigo}"/>
    </rich:column>

    <rich:column width="30" sortBy="#{item.codigoStatus}">
        <f:facet name="header">
            <h:outputText value="Status" styleClass="tooltipster" title="Código de retorno enviado pelo banco"/>
        </f:facet>
        <h:outputText style="cursor:pointer;" value="#{item.codigoStatus}" styleClass="#{(item.duplicidade || !empty item.movPagamento_Apresentar) ? 'tooltipster' : ''}" title="#{item.duplicidade ? 'Significa que foi cobrado do Cartão de Crédito e aprovada, porém a parcela já se encontrava quitada.' : item.movPagamento_Apresentar}"/>
    </rich:column>

    <rich:column sortBy="#{item.movParcela.dataVencimento}">
        <f:facet name="header">
            <h:outputText value="Vencimento" styleClass="tooltipster" title="Vencimento da Parcela"/>
        </f:facet>
        <h:outputText value="#{item.dataVencimentoBoleto}">
            <f:convertDateTime pattern="dd/MM/yyyy"/>
        </h:outputText>
    </rich:column>

    <rich:column sortBy="#{item.valorItemRemessa}">
        <f:facet name="header">
            <h:outputText title="Valor do Título" styleClass="tooltipster" value="Valor"/>
        </f:facet>
        <h:outputText value="#{item.valorItemRemessa}">
            <f:converter converterId="FormatadorNumerico"/>
        </h:outputText>
    </rich:column>

    <rich:column sortBy="#{item.movParcela.codigo}">
        <f:facet name="header">
            <h:outputText value="Parcelas do Boleto"/>
        </f:facet>

        <rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%" id="tblParcelasBoleto" rowKeyVar="statusParc"
                        value="#{item.movParcelas}" var="parcela">

            <rich:column rendered="#{SuperControle.widthScreenClient > 1024}"
                         sortBy="#{parcela.movParcelaVO.contrato.codigo}" style="#{parcela.background}">
                <f:facet name="header">
                    <h:outputText value="Parcela"/>
                </f:facet>
                <h:outputText  styleClass="tooltipster" title="Contrato #{parcela.movParcelaVO.codigo}"
                              value="#{parcela.movParcelaVO.codigo}"/>
            </rich:column>

            <rich:column sortBy="#{parcela.movParcelaVO.descricao}" style="#{parcela.background}">
                <f:facet name="header">
                    <h:outputText value="Descrição"/>
                </f:facet>
                <h:outputText rendered="#{!empty parcela.movParcelaVO.descricao}"
                              style="vertical-align:middle;"
                              title="#{parcela.movParcelaVO.descricao}"
                              value="#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : '***Estornada***'}"/>
                <a4j:commandLink rendered="#{empty parcela.movParcelaVO.descricao}"
                                 value="Log Estorno"
                                 onclick="alert('#{!empty parcela.movParcelaVO.descricao ? parcela.movParcelaVO.descricao : item.logEstorno}');"/>
            </rich:column>

            <rich:column sortBy="#{parcela.movParcelaVO.situacao_Apresentar}" style="#{parcela.background}">
                <f:facet name="header">
                    <h:outputText title="Situação Atual da Parcela"  styleClass="tooltipster" value="Situação"/>
                </f:facet>
                <h:outputText  styleClass="#{item.duplicidade ? 'tooltipster' : ''}"
                    title="#{item.duplicidade ? 'Significa que foi cobrado do Cartão de Crédito e aprovada, porém a parcela já se encontrava quitada.' : ''}"
                    value="#{item.duplicidade ? '***Duplicidade***' : parcela.movParcelaVO.situacao_Apresentar}"/>

            </rich:column>


            <rich:column sortBy="#{parcela.movParcelaVO.valorParcela}" style="#{parcela.background}">
                <f:facet name="header">
                    <h:outputText value="Valor" styleClass="tooltipster" title="Valor da Parcela Cobrada"/>
                </f:facet>
                <h:outputText rendered="#{!parcela.valorAlterado}"  value="R$ #{parcela.movParcelaVO.valorParcelaNumerico}"/>

                <h:outputText rendered="#{parcela.valorAlterado}" styleClass="tooltipster"
                              title="#{parcela.infoAlteracaoValor}"
                              value="R$ #{parcela.movParcelaVO.valorParcelaNumerico}">
                    <i class="fa-icon-info-circle"></i>
                </h:outputText>
            </rich:column>
        </rich:dataTable>

    </rich:column>

    <rich:column styleClass="colunaCentralizada">
        <f:facet name="header">
            <h:outputText title="Opções"/>
        </f:facet>

        <a4j:commandLink styleClass="pure-button"
                         style="margin-bottom: 8px; margin-right: 4px; margin-left: 4px; width: 80%;"
                         action="#{BoletoBancarioControle.imprimirBoletoCliente}"
                         rendered="#{item.mostrarBotaoImprimir}"
                         oncomplete="abrirPopupPDFImpressao('relatorio/#{BoletoBancarioControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);"
                         reRender="panelMensagem, panelBotoes">
            <i class="fa-icon-print"></i> Imprimir
            <f:setPropertyActionListener value="#{item}" target="#{BoletoBancarioControle.itemReimpressao}"/>
        </a4j:commandLink>

        <br/>

        <a4j:commandLink id="btnEnviarEmail" styleClass="pure-button pure-button"
                         rendered="#{item.mostrarBotaoImprimir}"
                         style="margin-right: 4px; margin-left: 4px; width: 80%;"
                         action="#{BoletoBancarioControle.enviarEmailCliente}"
                         reRender="panelMensagem, panelBotoes, panelConfirmacao">
            <i class="fa-icon-share-alt"></i> Enviar E-mail
            <f:setPropertyActionListener value="#{item}" target="#{BoletoBancarioControle.itemReimpressao}"/>
        </a4j:commandLink>

    </rich:column>

</rich:dataTable>
<h:panelGroup styleClass="semSpinner">
    <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
        <rich:datascroller align="center"
                           for="tblItensRemessaBoleto" maxPages="30"
                           id="scItensRemessaBoleto"  reRender="tblItensRemessaBoleto"/>
        <rich:inputNumberSpinner inputSize="5" styleClass="form"
                                 enableManualInput="true" minValue="1" maxValue="100"
                                 value="#{GestaoRemessasControle.nrPaginasItensRemessaBoleto}">
            <a4j:support event="onchange"
                         focus="scItensRemessaBoleto"
                         reRender="scItensRemessaBoleto,tblItensRemessaBoleto" />
        </rich:inputNumberSpinner>
    </h:panelGrid>
</h:panelGroup>

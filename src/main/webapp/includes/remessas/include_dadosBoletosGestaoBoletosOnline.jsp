<%@page pageEncoding="ISO-8859-1"%>

<%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

<rich:column sortBy="#{GestaoBoletosOnlineControle.getMatriculaAluno(boleto.pessoaVO.codigo)}">
    <f:facet name="header">
        <h:outputText title="Matrícula" value="Matr<PERSON>cula"/>
    </f:facet>
    <h:outputText value="#{GestaoBoletosOnlineControle.getMatriculaAluno(boleto.pessoaVO.codigo)}"/>
</rich:column>

<rich:column sortBy="#{boleto.pessoaVO.nome}">
    <f:facet name="header">
        <h:outputText title="Aluno" value="Aluno"/>
    </f:facet>
    <h:outputText value="#{boleto.pessoaVO.nome}"/>
</rich:column>

<rich:column sortBy="#{boleto.codigo}">
    <f:facet name="header">
        <h:outputText title="Cód. Boleto" value="Cód. Boleto"/>
    </f:facet>
    <h:outputText value="#{boleto.codigo}"/>
</rich:column>

<rich:column sortBy="#{boleto.dataRegistroApresentar}">
    <f:facet name="header">
        <h:outputText title="Data Registro" value="Data Registro"/>
    </f:facet>
    <h:outputText value="#{boleto.dataRegistroApresentar}"/>
</rich:column>

<rich:column sortBy="#{boleto.linhaDigitavel}">
    <f:facet name="header">
        <h:outputText title="Linha Digitável" value="Linha Digitável"/>
    </f:facet>
    <h:outputText value="#{boleto.linhaDigitavel}"/>
</rich:column>

<rich:column styleClass="centro" headerClass="centro">
    <f:facet name="header">
        <h:outputText value="Parcelas"/>
    </f:facet>

    <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status"
                    styleClass="tabelaDados font-size-Em"
                    value="#{boleto.listaBoletoMovParcela}"
                    var="boletomovparcela">

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Parcela" style="padding-inline-end: 8px;margin-right: 10px; display: block;"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          value="#{boletomovparcela.movParcelaVO.codigo}"/>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Descrição" style="padding-inline-end: 8px; margin-right: 10px; display: block;"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          title="#{boletomovparcela.movParcelaVO.pessoa_Apresentar_UpperCase}"
                          value="#{boletomovparcela.movParcelaVO.descricao}"/>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Valor" style="padding-inline-end: 50px;"/>
            </f:facet>
            <h:outputText value="#{boleto.pago ? boletomovparcela.valorParcelaApresentar : boletomovparcela.valorTotalApresentar}"/>
        </rich:column>
    </rich:dataTable>
</rich:column>

<rich:column styleClass="colunaEsquerda" sortBy="#{boleto.valor}">
    <f:facet name="header">
        <h:outputText value="Valor"/>
    </f:facet>
    <h:outputText value="#{boleto.valorApresentar}"
                  styleClass="tooltipster"/>
</rich:column>

<rich:column sortBy="#{boleto.dataVencimentoApresentar}">
    <f:facet name="header">
        <h:outputText title="Data Vencimento" value="Data Vencimento"/>
    </f:facet>
    <h:outputText value="#{boleto.dataVencimentoApresentar}"/>
</rich:column>

<rich:column sortBy="#{boleto.situacao.descricao}">
    <f:facet name="header">
        <h:outputText value="Situação"/>
    </f:facet>
    <h:outputText value="#{boleto.situacao.descricao}"
                  styleClass="tooltipster"/>
</rich:column>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>

<a4j:outputPanel>

    <rich:modalPanel  id="panelConfigAcoes" width="500"
                      autosized="true"
                      shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_acoes_remessas_config}"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkConfigAcoes" />
                <rich:componentControl for="panelConfigAcoes" attachTo="hidelinkConfigAcoes" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfigAcoes" styleClass="pure-form">
            <h:panelGrid width="100%" columns="2" columnClasses="w60,w40">
                <h:outputText value="#{msg_aplic.prt_acoes_remessas_config_codigo_status}" styleClass="text" style="line-height: 200%;"/>
                <h:inputText value="#{GestaoRemessasControle.acao.codigoStatus}" id="idcodigostatus"/>

                <h:outputText value="#{msg_aplic.prt_acoes_remessas_config_acao}"  styleClass="text" style="line-height: 200%;"/>
                <h:selectOneMenu value="#{GestaoRemessasControle.acao.codigoAcao}">
                    <f:selectItems value="#{GestaoRemessasControle.acoes}"/>
                    <a4j:support event="onchange" reRender="formConfigAcoes"/>
                </h:selectOneMenu>

                <c:if test="${GestaoRemessasControle.acao.acao == 'REALIZAR_REENVIO'}">

                    <h:outputText styleClass="text" style="line-height: 200%;"
                                  value="#{msg_aplic.prt_acoes_remessas_reagendarParcelasAutomaticamente}"/>
                    <h:selectBooleanCheckbox label="#{msg_aplic.prt_acoes_remessas_reagendarParcelasAutomaticamente}"
                                             styleClass="form"
                                             value="#{GestaoRemessasControle.acao.reagendarAutomaticamente}">
                        <a4j:support event="onchange" reRender="formConfigAcoes"/>
                    </h:selectBooleanCheckbox>

                    <h:outputText styleClass="text" style="line-height: 200%;"
                                  rendered="#{GestaoRemessasControle.acao.reagendarAutomaticamente}"
                                  value="#{msg_aplic.prt_acoes_remessas_qtdDiasParaReagendamentoAutomatico}"/>
                    <rich:inputNumberSpinner rendered="#{GestaoRemessasControle.acao.reagendarAutomaticamente}"
                                             minValue="0" maxValue="999"
                                             label="#{msg_aplic.prt_acoes_remessas_qtdDiasParaReagendamentoAutomatico}"
                                             value="#{GestaoRemessasControle.acao.qtdDiasParaReagendamentoAutomatico}"/>

                    <h:outputText styleClass="text" style="line-height: 200%;"
                                  rendered="#{GestaoRemessasControle.acao.reagendarAutomaticamente}"
                                  value="#{msg_aplic.prt_acoes_remessas_qtdTentativasParaReagendamentoAutomatico}"/>
                    <rich:inputNumberSpinner rendered="#{GestaoRemessasControle.acao.reagendarAutomaticamente}"
                                             minValue="0" maxValue="999"
                                             label="#{msg_aplic.prt_acoes_remessas_qtdTentativasParaReagendamentoAutomatico}"
                                             value="#{GestaoRemessasControle.acao.qtdTentativasParaReagendamentoAutomatico}"/>
                </c:if>
            </h:panelGrid>
            <div style="text-align: center;">
                <a4j:commandLink value="Gravar" action="#{GestaoRemessasControle.adicionarAcao}"
                                   reRender="formConfigAcoes" styleClass="pure-button pure-button-primary"
                                   oncomplete="#{GestaoRemessasControle.msgAlert}"/>
                <a4j:commandLink value="Cancelar" action="#{GestaoRemessasControle.abrirConfigAcao}"
                                   reRender="formConfigAcoes" styleClass="pure-button margin-h-10 margin-v-10"/>
                <a4j:commandLink value="Log" action="#{GestaoRemessasControle.realizarConsultaLogObjetoSelecionado}"
                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                 reRender="formConfigAcoes" styleClass="pure-button margin-v-10">
                    <i style="text-decoration: none" class="fa-icon-search"></i>
                </a4j:commandLink><br/>
            </div>
                <rich:dataTable value="#{GestaoRemessasControle.listaAcoes}" styleClass="pure-table-striped"
                                rendered="#{GestaoRemessasControle.apresentarLista}"
                                var="acaoLinha"
                                width="100%" columnClasses="centralizado"
                                id="tableListaAcoes"
                                rows="10">
                    <rich:column sortBy="#{acaoLinha.codigoStatus_Ordenacao}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_acoes_remessas_config_codigo_status}"/>
                        </f:facet>
                         <h:outputText value="#{acaoLinha.codigoStatus}"/>
                    </rich:column>

                    <rich:column sortBy="#{acaoLinha.acaoAssociada_Ordenacao}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_acoes_remessas_config_acao}"/>
                        </f:facet>
                            <h:outputText value="#{msg[acaoLinha.acaoName]}"/>
                    </rich:column>

                    <rich:column sortBy="#{acaoLinha.reagendarAutomaticamente}">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_acoes_remessas_reagendarParcelasAutomaticamente}"/>
                        </f:facet>
                        <h:selectBooleanCheckbox disabled="true" value="#{acaoLinha.reagendarAutomaticamente}"/>
                    </rich:column>

                    <rich:column sortBy="#{acaoLinha.qtdDiasParaReagendamentoAutomatico}">
                        <f:facet name="header">
                            <h:outputText title="Quantidade de dias, após o retorno, para acontecer a respecagem"  value="#{msg_aplic.prt_acoes_remessas_dias_repescagem_automatica}"/>
                        </f:facet>
                        <h:outputText title="Quantidade de dias, após o retorno, para acontecer a respecagem" value="#{acaoLinha.qtdDiasParaReagendamentoAutomatico}"/>
                    </rich:column>

                    <rich:column sortBy="#{acaoLinha.qtdTentativasParaReagendamentoAutomatico}">
                        <f:facet name="header">
                            <h:outputText title="Quantidade máxima de tentativas para tentar realizar repescagem" value="#{msg_aplic.prt_acoes_remessas_tentativas_repescagem_automatica}"/>
                        </f:facet>
                        <h:outputText title="Quantidade máxima de tentativas para tentar realizar repescagem" value="#{acaoLinha.qtdTentativasParaReagendamentoAutomatico}"/>
                    </rich:column>

                    <rich:column>
                        <a4j:commandButton image="/imagens/botaoEditar.png"
                                           action="#{GestaoRemessasControle.editar}"
                                           reRender="formConfigAcoes"/>

                        <a4j:commandButton image="/imagens/botaoRemover.png"
                                           action="#{GestaoRemessasControle.excluir}"
                                           reRender="formConfigAcoes"/>

                    </rich:column>

                    <f:facet name="footer">
                        <h:outputText style="float: right" value="#{GestaoRemessasControle.totalizadorAcoes}"/>
                    </f:facet>
                </rich:dataTable>
                <rich:datascroller for="tableListaAcoes" rendered="#{GestaoRemessasControle.apresentarLista}"/>
        </a4j:form>
    </rich:modalPanel>

</a4j:outputPanel>
<%-- 
    Document   : include_itens_remessa
    Created on : 13/12/2012, 19:11:15
    Author     : waller
--%>
<%@include file="/includes/imports.jsp" %>
<rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%"
                id="tblItensRemessa"
                rows="#{fn:contains(pagina, 'impressao') ? 5000 : 10}"
                rowKeyVar="status"
                value="#{GestaoRemessasControle.remessaVO.listaItens}"
                var="item">
    <f:facet name="header">
        <h:outputText value="Itens de Remessa de Cobran�a"/>
    </f:facet>
    <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>
    <rich:column sortBy="#{item.codigo}" >
        <f:facet name="header">
            <h:outputText value="Cod."/>
        </f:facet>
        <h:outputText title="C�digo Item Remessa" value="#{item.codigo}"/>
    </rich:column>

    <rich:column rendered="#{fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6')}" sortBy="#{item.remessa.identificador}" >
        <f:facet name="header">
            <h:outputText value="Remessa"/>
        </f:facet>
        <h:outputText title="Remessa" value="#{item.remessa.identificador}"/>
    </rich:column>


    <rich:column rendered="#{!(fn:contains(pagina, 'estorno') || fn:contains(pagina, 'tela6'))}" sortBy="#{item.pessoa.nome}">
        <f:facet name="header">
            <h:outputText value="Pessoa"/>
        </f:facet>
        <div title="Clique para abrir o Cadastro de Cliente" style="overflow:hidden;width:250px">
            <% if (request.getRequestURI().contains("impressao")) {%>
            <a4j:commandLink value="#{item.pessoa.nome}"
                             oncomplete="#{GestaoRemessasControle.onCompleteDetalhes}"
                             actionListener="#{GestaoRemessasControle.abrirTelaClienteColaborador}">
                <f:attribute name="pessoa" value="#{item.pessoa}"/>
                <f:attribute name="impressao" value="true"/>
            </a4j:commandLink>
            <%} else {%>
            <a4j:commandLink value="#{item.pessoa.nome}"
                             oncomplete="#{GestaoRemessasControle.onCompleteDetalhes}"
                             actionListener="#{GestaoRemessasControle.abrirTelaClienteColaborador}">
                <f:attribute name="pessoa" value="#{item.pessoa}"/>
                <f:attribute name="impressao" value="false"/>
            </a4j:commandLink>
            <%}%>

        </div>
    </rich:column>

    <rich:column width="30" sortBy="#{item.valorCartaoMascaradoOuAgenciaConta}">
        <f:facet name="header">
            <h:outputText value="Cart�o/Ag/CC"/>
        </f:facet>
        <h:outputText style="cursor:pointer;" value="#{item.valorCartaoMascaradoOuAgenciaConta}"/>
    </rich:column>

    <rich:column width="30" sortBy="#{item.codigoStatus}" styleClass="col-text-align-center" headerClass="col-text-align-center">
        <f:facet name="header">
            <h:outputText value="Status"/>
        </f:facet>
        <h:outputText style="cursor:help;"
                      styleClass="tooltipster"
                      value="#{item.codigoStatus}" title="#{item.descricaoStatus}"/>
    </rich:column>

    <rich:column width="30" sortBy="#{item.dataPrevistaPagamento}" rendered="#{GestaoRemessasControle.remessaVO.DCO}" styleClass="col-text-align-center" headerClass="col-text-align-center">
        <f:facet name="header">
            <h:outputText value="Prev. Pg" title="Data prevista de pagamento"/>
        </f:facet>
        <h:outputText title="Data prevista de pagamento" id="dataPag" rendered="#{item.codigoStatus eq 'AT'}" value="#{item.dataPrevistaPagamento}">
            <f:convertDateTime pattern="dd/MM/yyyy"/>
        </h:outputText>
    </rich:column>

    <rich:column sortBy="#{item.autorizacao}">
        <f:facet name="header">
            <h:outputText title="Autoriza��o" value="Aut."/>
        </f:facet>
        <h:outputText title="C�digo Autoriza��o Administradora" value="#{item.autorizacao}"/>
    </rich:column>

    <rich:column sortBy="#{item.identificador}">
        <f:facet name="header">
            <h:outputText title="Identificador de d�bito autom�tico no banco" value="Id.Cob."/>
        </f:facet>
        <h:outputText value="#{item.identificador}"/>
    </rich:column>

    <rich:column sortBy="#{item.movParcela.codigo}">
        <f:facet name="header">
            <h:outputText title="MovParcela" value="MPc"/>
        </f:facet>
        <h:outputText title="Parcela: #{item.movParcela.codigo}" value="#{item.movParcela.codigo}"/>
    </rich:column>


    <rich:column sortBy="#{item.movParcela.descricao}">
        <f:facet name="header">
            <h:outputText value="Descri��o"/>
        </f:facet>
        <h:outputText rendered="#{!empty item.movParcela.descricao}"
                      style="vertical-align:middle;"
                      title="#{item.movParcela.descricao}"
                      value="#{!empty item.movParcela.descricao ? item.movParcela.descricao : '***Estornada***'}"/>
        <a4j:commandLink rendered="#{empty item.movParcela.descricao}"
                         value="Log Estorno"
                         onclick="alert('#{!empty item.movParcela.descricao ? item.movParcela.descricao : item.logEstorno}');"/>
    </rich:column>

    <rich:column sortBy="#{item.movParcela.dataVencimento}">
        <f:facet name="header">
            <h:outputText value="Venc."/>
        </f:facet>

        <h:outputText  rendered="#{!empty item.movParcela.descricao}" title="Data de Vencimento" id="dataVenc" value="#{item.movParcela.dataVencimento}">
            <f:convertDateTime pattern="dd/MM/yyyy"/>
        </h:outputText>
    </rich:column>

    <rich:column sortBy="#{item.dataCobrancaDCO}" rendered="#{GestaoRemessasControle.remessaVO.DCO}">
        <f:facet name="header">
            <h:outputText value="Dt. Cobran�a"/>
        </f:facet>
        <h:outputText  rendered="#{!empty item.dataCobrancaDCO}" title="Data de cobran�a" id="dataCobrancaDCO" value="#{item.dataCobrancaDCO}">
            <f:convertDateTime pattern="dd/MM/yyyy"/>
        </h:outputText>
    </rich:column>

    <rich:column rendered="#{SuperControle.widthScreenClient > 1024}" sortBy="#{item.nrTentativaParcela}">
        <f:facet name="header">
            <h:outputText title="Tentativa da Parcela na Remessa" value="TR"/>
        </f:facet>
        <h:outputText title="Tentativa da Parcela na Remessa" value="#{item.nrTentativaParcela}"/>
    </rich:column>


    <rich:column sortBy="#{item.movPagamento.codigo}" >
        <f:facet name="header">
            <h:outputText title="MovPagamento" value="MPg"/>
        </f:facet>
        <h:outputText style="color:blue;font-weight:bold;" title="C�digo do Movimento de Pagamento"
                      rendered="#{item.movPagamento.codigo != 0}" value="Pgt. #{item.movPagamento.codigo}"/>

        <h:outputText style="color:red;font-weight:bold;" title="Outra Remessa ou Outra Forma de Pagamento pagou a Parcela"
                      rendered="#{(item.movPagamento.codigo == 0) && (item.movParcela.situacao == 'PG')}" value="Pgt. OUTRO"/>

    </rich:column>

    <rich:column sortBy="#{item.movParcela.situacao_Apresentar}" >
        <f:facet name="header">
            <h:outputText title="Situa��o Atual da Parcela" value="Sit."/>
        </f:facet>
        <h:outputText title="#{item.duplicidade ? 'Significa que foi cobrado do Cart�o de Cr�dito e aprovada, por�m a parcela j� se encontrava quitada.' : ''}"
                      value="#{item.duplicidade ? '***Duplicidade***' : item.movParcela.situacao_Apresentar}"/>

    </rich:column>

    <rich:column sortBy="#{item.valorParcela}" >
        <f:facet name="header">
            <h:outputText value="Valor Parcela"/>
        </f:facet>
        <h:outputText title="Valor da Parcela Cobrada" value="R$ #{item.movParcela.valorParcelaNumerico}"/>
    </rich:column>

    <rich:column sortBy="#{item.valorMulta}" >
        <f:facet name="header">
            <h:outputText value="Multa"/>
        </f:facet>
        <h:outputText title="Valor da multa" value="R$ #{item.valorMultaNumerico}"/>
    </rich:column>

    <rich:column sortBy="#{item.valorJuros}" >
        <f:facet name="header">
            <h:outputText value="Juros"/>
        </f:facet>
        <h:outputText title="Valor dos juros" value="R$ #{item.valorJurosNumerico}"/>
    </rich:column>

    <rich:column sortBy="#{item.valorItemRemessa}" >
        <f:facet name="header">
            <h:outputText value="Valor Cobran�a"/>
        </f:facet>
        <h:outputText title="Valor da total da cobran�a" value="R$ #{item.valorItemRemessaNumerico}"/>
    </rich:column>

    <rich:column sortBy="#{item.movParcela.empresa_Apresentar}">
        <f:facet name="header">
            <h:outputText value="Empresa"/>
        </f:facet>
        <h:outputText title="Empresa" value="#{item.movParcela.empresa_Apresentar}"/>
    </rich:column>

</rich:dataTable>
<rich:datascroller id="scItensRemessa" reRender="tblItensRemessa"
                   maxPages="30" for="tblItensRemessa"/>

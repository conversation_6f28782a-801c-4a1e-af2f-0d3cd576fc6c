<%--
    Document   : include_itens_remessaestorno
    Created on : 05/02/2014, 12:38:50
    Author     : marcosandre
--%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="/includes/imports.jsp" %>

<h:panelGroup id="containerListaRemessaEstorno">
<%--    <h:panelGroup  rendered="#{TelaClienteControle.listaRemessa.count > 0 }"--%>
<%--                   layout="block"--%>
<%--                   styleClass="pure-g-r margin-0-auto margin-v-10" style="float:right;">--%>
<%--        <a4j:commandLink id="botaoCartaasasoExcelremessas"--%>
<%--                         styleClass="pure-button pure-button-small"--%>
<%--                         actionListener="#{ExportadorListaControle.exportar}"--%>
<%--                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','TransacaoCartaoCredito', 800,200);#{ExportadorListaControle.msgAlert}"--%>
<%--                         accesskey="3">--%>
<%--            <f:attribute name="tipo" value="xls"/>--%>
<%--            <f:attribute name="lista" value="#{GestaoRemessasControle.itensRemessaEstorno}"/>--%>
<%--            <f:attribute name="atributos" value="codigo=Código,identificadorRemessa=Remessa,nomePessoaApresentar=Pessoa,valorCartaoMascaradoOuAgenciaConta=Cartão/Ag/CC,codigoStatus=Status,identificador=Id.Cob.,codigoMovParcela=MPc,codigoContrato=Cont.,descricao=Descrição,vencimento=Venc.,movPagamento_Apresentar=Mpg,situacaoParcela=Sit.,valorParcelaNumerico=Valor"/>--%>
<%--            <f:attribute name="prefixo" value="RemessaCobranca"/>--%>
<%--            <f:attribute name="titulo" value="RemessaCobranca"/>--%>
<%--            <i class="fa-icon-excel" ></i> &nbsp Excel--%>
<%--        </a4j:commandLink>--%>



<%--        <a4j:commandLink id="btnPDF" style="margin-right: 20px;"--%>
<%--                         styleClass="pure-button pure-button-small"--%>
<%--                         actionListener="#{ExportadorListaControle.exportar}"--%>
<%--                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','RemessaCobrança', 800,200);#{ExportadorListaControle.msgAlert}"--%>
<%--                         accesskey="4">--%>
<%--            <f:attribute name="tipo" value="pdf" />--%>
<%--            <f:attribute name="lista" value="#{GestaoRemessasControle.itensRemessaEstorno}"/>--%>
<%--            <f:attribute name="atributos" value="codigo=Código,identificadorRemessa=Remessa,nomePessoaApresentar=Pessoa,valorCartaoMascaradoOuAgenciaConta=Cartão/Ag/CC,codigoStatus=Status,identificador=Id.Cob.,codigoMovParcela=MPc,codigoContrato=Cont.,descricao=Descrição,vencimento=Venc.,movPagamento_Apresentar=Mpg,situacaoParcela=Sit.,valorParcelaNumerico=Valor"/>--%>
<%--            <f:attribute name="prefixo" value="RemessaCobranca"/>--%>
<%--            <f:attribute name="titulo" value="RemessaCobranca"/>--%>
<%--            <i class="fa-icon-pdf" ></i> &nbsp PDF--%>
<%--        </a4j:commandLink>--%>
<%--    </h:panelGroup>--%>

    <h:outputText value="Itens de Remessa de Cobrança" id="idcodigositenscobranca"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 negrito cinzaEscuro pl20"/>
    <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-configurar-retentativa-de-cobranca-para-retornos-especificos-de-remessas/"
                  style="margin-left: 20px;" id="idcodigoserros"
                  title="Clique e saiba mais: Códigos de Erros de Remessas" target="_blank">
        <h:graphicImage styleClass="linkWiki" style="margin-left:5px;margin-right:2px;" url="/imagens/wiki_link2.gif"/>
        <h:outputText value="Lista de Códigos de Erros de Remessas" id="idlistacodigoserrosremessas"/>
    </h:outputLink>
    <h:outputText value="Nenhum item de remessa." id="idnenhumitem"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 cinza pl20"
                  rendered="#{TelaClienteControle.listaRemessa.count <= 0 }"/>

    <rich:dataTable width="100%"
                    rendered="#{TelaClienteControle.listaRemessa.count > 0 }"
                    id="tblItensRemessa" styleClass="tabelaDados"
                    rowKeyVar="status"
                    value="#{GestaoRemessasControle.itensRemessaEstorno}"
                    var="item">

        <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>

        <rich:column sortBy="#{item.remessa.codigo}"  styleClass="centro" headerClass="centro">
            <f:facet name="header">
                <h:outputText value="Cod. Rem." styleClass="tooltipster" title="Código da remessa que a parcela se encontra"/>
            </f:facet>
            <h:outputText value="#{item.remessa.codigo}" styleClass="tooltipster"
                          title="<b>Cod. Item:</b> #{item.codigo}<br/><b>Convênio:</b> #{item.remessa.convenioCobranca.descricao}"/>
        </rich:column>

        <rich:column sortBy="#{item.remessa.dataRegistro}">
            <f:facet name="header">
                <h:outputText value="Dt Remessa" styleClass="tooltipster" title="Data em que a remessa foi criada no sistema"/>
            </f:facet>
            <h:outputText value="#{item.remessa.dataRegistro}" styleClass="tooltipster"
                          title="<b>Convênio:</b> #{item.remessa.convenioCobranca.descricao}">
                <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
            </h:outputText>
        </rich:column>

        <rich:column sortBy="#{item.codigoStatus}" styleClass="esquerda" headerClass="esquerda">
            <f:attribute name="codigoStatus" value="#{item.codigoStatus}"/>
            <f:attribute name="identificador" value="#{item.identificador}"/>
            <f:facet name="header">
                <h:outputText value="Cod. Ret"
                              styleClass="tooltipster"
                              title="Código de retorno enviado pelo adquirente"/>
            </f:facet>
            <h:panelGrid columns="3">
                <h:outputText style="cursor:help;"
                              styleClass="tooltipster"
                              value="#{item.codigoStatus}"
                              title="#{item.descricaoStatus}"/>

                <a4j:commandLink id="btnErro99" rendered="#{GestaoRemessasControle.perfilAcessoBotaoErro99 && item.codigoStatus eq '99'
                && item.tipo.identificador eq 'CIELOEDIDCC' && item.movPagamento.codigo == 0 && item.itemRemessaTemParcelaEmAberto}"
                                 style="padding-left: 5px"
                                 title="Criação de recibo remessa erro 99 (CIELO)" reRender="formPanelGerarRecibo"
                                 oncomplete="Richfaces.showModalPanel('panelGerarRecibo')" styleClass="tooltipster"
                                 actionListener="#{GestaoRemessasControle.remessa}">
                    <h:graphicImage id="paramEnvio" value="imagens/cielo.png"/>
                    <f:attribute name="codigoItem" value="#{item.codigo}"/>
                </a4j:commandLink>
            </h:panelGrid>
        </rich:column>

        <rich:column sortBy="#{item.autorizacao}"  styleClass="centro" headerClass="centro">
            <f:facet name="header">
                <h:outputText title="Código de autorização de pagamento emitido pelo banco" styleClass="tooltipster" value="Aut."/>
            </f:facet>
            <h:outputText value="#{item.autorizacao}"
            styleClass="tooltipster"
            title="<b>Autorização:</b> #{item.autorizacao}"/>
        </rich:column>

        <rich:column width="30"  styleClass="centro" headerClass="centro" sortBy="#{item.valorCartaoMascaradoOuAgenciaConta}">
            <f:facet name="header">
                <h:outputText value="Cartão/Ag/CC"/>
            </f:facet>
            <h:outputText styleClass="#{!empty item.identificadorClienteEmpresa ? 'tooltipster' : ''}" title="#{!empty item.identificadorClienteEmpresa ? 'Identificador debito banco:' : ''} #{item.identificadorClienteEmpresa}" value="#{item.valorCartaoMascaradoOuAgenciaConta}"></h:outputText>
        </rich:column>

        <rich:column sortBy="#{item.valorItemRemessa}" style="margin-left: 10px;" headerClass="direita" styleClass="direita">
            <f:facet name="header">
                <h:outputText value="Valor" styleClass="tooltipster" title="Valor da cobrança"/>
            </f:facet>
            <h:outputText style="margin-left: 10px; margin-right: 4px;" value="R$ #{item.valorItemRemessaNumerico}"/>
        </rich:column>

        <rich:column style="margin-left: 10px;" headerClass="centro" styleClass="centro">
            <f:facet name="header">
                <h:outputText value="Obs." title="Observação" styleClass="tooltipster"/>
            </f:facet>
            <h:outputText rendered="#{not empty item.logEstorno}"
                          styleClass="tooltipster"
                          value="***Estornada***"
                          title="#{item.logEstorno}"/>
        </rich:column>

        <rich:column styleClass="centro" headerClass="centro">
            <f:facet name="header">
                <h:outputText value="Parcelas"/>
            </f:facet>

            <rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%" rowKeyVar="statusParc"
                            value="#{item.movParcelas}" var="parcela">

                <rich:column sortBy="#{parcela.movParcelaVO.codigo}">
                    <f:facet name="header">
                        <h:outputText value="Parcela"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  rendered="#{parcela.movParcelaVO.codigo > 0}"
                                  title="<b>Contrato:</b> #{parcela.movParcelaVO.contrato_Apresentar}<br/><b>Pessoa:</b> #{parcela.movParcelaVO.pessoa.nome}"
                                  value="#{parcela.movParcelaVO.codigo}"/>
                    <h:outputText styleClass="tooltipster"
                                  rendered="#{not empty parcela.logEstornoApresentar}"
                                  title="#{parcela.logEstornoApresentar}"
                                  value="***Estornada***"/>
                </rich:column>

                <rich:column sortBy="#{parcela.movParcelaVO.descricao}">
                    <f:facet name="header">
                        <h:outputText value="Descrição"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  rendered="#{parcela.movParcelaVO.codigo > 0}"
                                  title="#{parcela.movParcelaVO.pessoa_Apresentar}"
                                  value="#{parcela.movParcelaVO.descricao}"/>
                    <h:outputText styleClass="tooltipster"
                                  rendered="#{parcela.movParcelaVO.codigo == 0}"
                                  title="#{parcela.logEstornoApresentar}"
                                  value="***Estornada***"/>
                </rich:column>

                <rich:column sortBy="#{parcela.movParcelaVO.situacao_Apresentar}">
                    <f:facet name="header">
                        <h:outputText title="Situação Atual da Parcela"
                                      styleClass="tooltipster"
                                      value="Situação"/>
                    </f:facet>
                    <h:outputText value="#{parcela.movParcelaVO.situacao_Apresentar}"/>
                </rich:column>

                <rich:column sortBy="#{parcela.valorParcela}">
                    <f:facet name="header">
                        <h:outputText value="Valor"
                                      styleClass="tooltipster"
                                      title="Valor da Parcela"/>
                    </f:facet>
                    <h:outputText value="#{parcela.valorOriginal_Apresentar}"/>
                </rich:column>

                <rich:column sortBy="#{parcela.valorMulta}">
                    <f:facet name="header">
                        <h:outputText value="Multa"
                                      styleClass="tooltipster"
                                      title="Valor da Multa"/>
                    </f:facet>
                    <h:outputText value="#{parcela.valorMulta_Apresentar}"/>
                </rich:column>

                <rich:column sortBy="#{parcela.valorJuros}">
                    <f:facet name="header">
                        <h:outputText value="Juros"
                                      styleClass="tooltipster"
                                      title="Valor dos Juros"/>
                    </f:facet>
                    <h:outputText value="#{parcela.valorJuros_Apresentar}"/>
                </rich:column>
            </rich:dataTable>
        </rich:column>

        <rich:column rendered="#{GestaoRemessasControle.apresentarColunaRetornoManual}" >
            <f:facet name="header">
                <h:outputText value="Operações"/>
            </f:facet>
            <a4j:commandLink rendered="#{item.apresentarOperacaoRetornoManual}" title="Retornar Manualmente a Parcela"
                             actionListener="#{GestaoRemessasControle.prepararRetornoManual}"
                             styleClass="linkPadrao texto-cor-azul"
                             style="color:#29abe2;"
                             action="#{GestaoRemessasControle.retornarParcelaManualmente}" value="Retorno Manual"
                             reRender="panelAutorizacaoFuncionalidade"/>
        </rich:column>
    </rich:dataTable>

    <h:panelGrid columns="1" rendered="#{TelaClienteControle.listaRemessa.count > 0 }" width="100%" columnClasses="colunaCentralizada">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">
                            <h:outputText
                                    styleClass="texto-size-14 cinza"
                                    value="Total #{TelaClienteControle.listaRemessa.count} itens"></h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink  styleClass="linkPadrao texto-cor-azul texto-size-20-real"  reRender="containerListaRemessaEstorno"
                                              actionListener="#{TelaClienteControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_REMESSA" />
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaRemessaEstorno"
                                             actionListener="#{TelaClienteControle.paginaAnterior}">
                                <i class="fa-icon-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_REMESSA" />
                            </a4j:commandLink>

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                          value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaRemessa.paginaAtualApresentar}" rendered="true"/>
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real" reRender="containerListaRemessaEstorno"
                                             actionListener="#{TelaClienteControle.proximaPagina}">
                                <i class="fa-icon-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_REMESSA" />
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real" reRender="containerListaRemessaEstorno"
                                             actionListener="#{TelaClienteControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_REMESSA" />
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText
                                        styleClass="texto-size-14 cinza "
                                        value="Itens por página "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{TelaClienteControle.listaRemessa.limit}">
                                    <f:selectItem itemValue="#{6}"></f:selectItem>
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{20}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange" actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}" reRender="containerListaRemessaEstorno">
                                        <f:attribute name="tipo" value="LISTA_REMESSA" />
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>
</h:panelGroup>

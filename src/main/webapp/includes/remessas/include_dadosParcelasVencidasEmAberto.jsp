<%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

<rich:column sortBy="#{parc.nome}" width="200px">
    <f:facet name="header">
        <h:outputText title="Nome Cliente" value="Nome Cliente" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.nome}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>

<rich:column sortBy="#{parc.empresaCodigo}">
    <f:facet name="header">
        <h:outputText title="Cod. Empresa" value="Cod. Empresa." styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.empresaCodigoApresentar}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>

<rich:column sortBy="#{parc.contrato}">
    <f:facet name="header">
        <h:outputText title="Contrato" value="Contrato." styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.contratoApresentar}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>

<rich:column sortBy="#{parc.parcelaCodigo}">
    <f:facet name="header">
        <h:outputText title="Codigo Parcela" value="Codigo Parcela" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.parcelaCodigoApresentar}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>

<rich:column>
    <f:facet name="header">
        <h:outputText title="Descricao parcela" value="Parcela"
                      styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.descricao}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>

<rich:column sortBy="#{parc.dataVencimentoParcela}">
    <f:facet name="header">
        <h:outputText title="Data de vencimento" value="Dt. Vencimento Parcela"
                      styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.dataVencimentoApresentar}" styleClass="texto-font texto-size-14-real texto-cor-cinza">
        <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy"/>
    </h:outputText>
</rich:column>

<rich:column sortBy="#{parc.valorParcela}">
    <f:facet name="header">
        <h:outputText title="Valor da Parcela" value="Vlr. Parcela"
                      styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.valorParcelaApresentar}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>

<rich:column sortBy="#{parc.nomeConvenioCobranca}">
    <f:facet name="header">
        <h:outputText title="Conv�nio Cobran�a" value="Conv�nio Cobran�a"
                      styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.nomeConvenioCobranca}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>


<rich:column sortBy="#{parc.nomeEmpresa}">
    <f:facet name="header">
        <h:outputText title="Empresa" value="Empresa" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.nomeEmpresa}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
    <%--<h:outputText style="#{parc.corRemessa}" value="#{parc.empresa_Apresentar}"/>--%>
</rich:column>

<rich:column sortBy="#{parc.nomeConsultor}">
    <f:facet name="header">
        <h:outputText title="Nome do Consultor do Contrato" value="Consultor" styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.nomeConsultor}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>

<rich:column sortBy="#{parc.emRemessa}">
    <f:facet name="header">
        <h:outputText title="Em Remessa" value="Em Remessa"
                      styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"/>
    </f:facet>
    <h:outputText value="#{parc.emRemessa}" styleClass="texto-font texto-size-14-real texto-cor-cinza"/>
</rich:column>
<rich:column styleClass="col-text-align-center" width="5%">
    <a4j:commandLink id="visualizarAluno"
                     styleClass="linkPadrao texto-size-16-real"
                     action="#{GestaoRemessasControle.irParaTelaCliente}"
                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
        <f:param name="state" value="AC"/>
        <i class="fa-icon-search"></i>
    </a4j:commandLink>
</rich:column>
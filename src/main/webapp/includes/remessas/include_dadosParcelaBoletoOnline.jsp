<%@page pageEncoding="ISO-8859-1"%>

<%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

<rich:column sortBy="#{parc.empresa.codigo}">
    <f:facet name="header">
        <h:outputText title="Cód. Empresa" value="Cód. Empresa"/>
    </f:facet>
    <h:outputText value="#{parc.empresa.codigo}"/>
</rich:column>

<rich:column sortBy="#{parc.empresa_Apresentar}">
    <f:facet name="header">
        <h:outputText title="Empresa" value="Empresa"/>
    </f:facet>
    <h:outputText value="#{parc.empresa_Apresentar}"/>
</rich:column>

<rich:column sortBy="#{parc.pessoa.nome}">
    <f:facet name="header">
        <h:outputText title="Nome da Pessoa" value="Pessoa"/>
    </f:facet>
    <h:outputText value="#{parc.pessoa.nome}"/>
</rich:column>

<rich:column sortBy="#{parc.codigo}">
    <f:facet name="header">
        <h:outputText title="Código Parcela" value="Cód. Parcela"/>
    </f:facet>
    <h:outputText value="#{parc.codigo}"/>
</rich:column>

<rich:column sortBy="#{parc.descricao}">
    <f:facet name="header">
        <h:outputText title="Descrição Parcela" value="Descrição Parcela"/>
    </f:facet>
    <h:outputText value="#{parc.descricao}"/>
</rich:column>

<rich:column sortBy="#{parc.dataVencimento}">
    <f:facet name="header">
        <h:outputText title="Data Vencimento Parcela" value="Data Vencimento Parcela"/>
    </f:facet>
    <h:outputText style="font-size: 9px;" value="#{parc.dataCobranca}">
        <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy"/>
    </h:outputText>
</rich:column>

<rich:column sortBy="#{parc.valorParcela}">
    <f:facet name="header">
        <h:outputText title="Valor da Parcela" value="Valor Parcela"/>
    </f:facet>
    <h:outputText value="#{parc.valorParcelaNumerico}"/>
</rich:column>

<rich:column sortBy="#{parc.valorMultaJuros}">
    <f:facet name="header">
        <h:outputText title="Valor Multa/Juros" value="Multa/Juros"/>
    </f:facet>
    <h:outputText value="#{parc.valorMultaJuros_Apresentar}"/>
</rich:column>

<rich:column sortBy="#{parc.valorCobranca}">
    <f:facet name="header">
        <h:outputText title="Valor Cobrança" value="Valor Cobrança"/>
    </f:facet>
    <h:outputText value="#{parc.valorCobranca_Apresentar}"/>
</rich:column>

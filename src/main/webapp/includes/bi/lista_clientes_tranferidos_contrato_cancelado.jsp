<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="../include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../../hoverform.js"></script>
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <title>
        <h:outputText value="Resumo de Colaborador(es) Com Pendência(s)"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Tranferidos com Contrato Cancelado Total:${fn:length(PendenciaControleRel.pendenciaRelVO.listaPendenciaResumoPessoaRelVOs)} "/>
        <c:set var="urlWiki" scope="session" value="semWiki"/>
        <f:facet name="header">
            <jsp:include page="../../topo_reduzido_popUp.jsp"/>
        </f:facet>
        <html>
        <body onload="fireElement('form:botaoAtualizarPagina')"/>
        <h:form id="form" styleClass="font-size-Em-max">
            <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
            <h:panelGrid columns="1" width="100%" styleClass="font-size-Em-max" >
                <rich:dataTable width="100%" styleClass="tabelaSimplesCustom font-size-Em-max" id="tabelaRes"
                                value="#{RelControleOperacoesControle.controleOperacoesRelVO.listaPendenciaResumoPessoaRelVOs}" rows="50" var="resumoPessoa" rowKeyVar="status">
                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Mensagem" />
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.conteudoMensagem}" />
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
            </h:panelGrid>
        </h:form>
        </body>
        </html>
    </h:panelGrid>
</f:view>
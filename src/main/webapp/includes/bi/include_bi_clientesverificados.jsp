<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-clientesverificados" styleClass="container-bi"
              rendered="#{LoginControle.permissaoAcessoMenuVO.ativarVerificacaoClientesAtivos or LoginControle.permissaoAcessoMenuVO.verificarClientesAtivos or
              LoginControle.permissaoAcessoMenuVO.biVerificarcaoClientes}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.ativarVerificacaoClientesAtivos || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.clientesVerificados.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarClientesVerificados}" reRender="containerClientesVerificados"
                         styleClass="btn-atualizar-bi" status="nenhumStatus" onclick="abrirCarregandoPoBI(this)"
                         oncomplete="hideCarregandoPorBI(this)"/>

        <h:panelGroup layout="block" id="containerClientesVerificados">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.clientesVerificados.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Verifica��o de Clientes" styleClass="bi-titulo pull-left"
                                      id="tituloBIImp"/>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="ghost">

                    <div class="loading" style="height: 10px; margin-bottom: 10px;"></div>
                    <div class="loading" style="height: 20px; margin-bottom: 10px;"></div>
                    <div class="loading" style="height: 50px; margin-bottom: 10px;"></div>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.clientesVerificados.apresentarBoxCarregar || BIControle.biCarregado}">

                <h:panelGroup id="bi-header-clientesverificados" styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Verifica��o de Clientes" styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="tooltipster linkWiki bi-cor-cinza bi-left-align"
                                      style="float: left;margin-left: 2%"
                                      value="#{SuperControle.urlBaseConhecimento}bi-verificacao-de-clientes/"
                                      title="Clique e saiba mais: Verifica��o de Clientes" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarClientesVerificados"
                                         reRender="bi-header-clientesverificados,clienteverificados-container-numeros,clientesverificados-container-resultado,panelGraficoClientesVerificados"
                                         action="#{ClientesVerificadosRelControle.atualizarIndiceVerificacao}"
                                         oncomplete="carregarGraficoClientesVerificados();montarTips();">
                            <i title="Consultar..."
                               class="lineHeight-3em tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right"></i>
                        </a4j:commandLink>

                        <%--                    Este ainda n�o filtra por colaborador--%>
                        <%--                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')" action="#{BIControle.biAtualizarParam}"  reRender="colCheck, grupoMarcadoCss, tituloFiltro">--%>
                        <%--                            <i title="Filtrar Por Colaborador" class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">--%>
                        <%--                                <span class="badgeItem1Icon" data-bagde="${BIControle.qtdColClientesVerif}"></span>--%>
                        <%--                            </i>--%>
                        <%--                            <a4j:actionparam name="biAtualizar" value="CLIENTES_VERIFICADOS"></a4j:actionparam>--%>
                        <%--                            <a4j:actionparam name="biAtualizarAbrirConsulta" value="biAtualizarAbrirConsulta"></a4j:actionparam>--%>
                        <%--                            <a4j:actionparam name="reRenderBi" value="containerClientesVerificados"></a4j:actionparam>--%>
                        <%--                        </a4j:commandLink>--%>

                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" id="clienteverificados-container-numeros" style="margin-top: 4%"
                              styleClass="icv-container-numeros">

                    <h:panelGroup layout="block" style="margin-left: 60%;text-align: center"
                                  styleClass="icv-numeros-col">
                        <h:outputText styleClass="icv-periodo-text" value=""/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <h:outputText styleClass="icv-periodo-text" value=""/>
                    </h:panelGroup>

                    <%--Total de clientes para verificar--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor-cinza fa-icon-circle"/>
                        <h:outputText style="margin-left: 1px;" styleClass="bi-table-text bi-font-family" value=" Total"
                                      id="totalBIImp"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         id="qtdeTotalBIImp"
                                         action="#{ClientesVerificadosRelControle.mostrarTotalVerificar}"
                                         oncomplete="abrirPopup('clientesVerificadosForm.jsp', 'Total',980 ,700);"
                                         value="#{ClientesVerificadosRelControle.totalVerificar}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink>
                            <i title="${ClientesVerificadosRelControle.primeiraVerificacao_HintApresentar}"
                               class="tooltipster">
                                <h:outputText styleClass="linkPadrao texto-cor-azul texto-size-16"
                                              value="#{ClientesVerificadosRelControle.primeiraVerificacao_Apresentar}"/>
                            </i>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <%--Total Clientes Verificados--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor-verde fa-icon-circle texto-cor-cinza-3"/>
                        <h:outputText styleClass="bi-table-text bi-font-family" value=" Verificados"
                                      id="verificadosBI"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         id="qtdeVerificadosBI"
                                         action="#{ClientesVerificadosRelControle.mostrarVerificados}"
                                         oncomplete="abrirPopup('clientesVerificadosForm.jsp', 'Verificados',980 ,700);"
                                         value="#{ClientesVerificadosRelControle.qtdVerificado}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink>
                            <i title="${ClientesVerificadosRelControle.ultimaVerificacao_HintApresentar}"
                               class="tooltipster">
                                <h:outputText styleClass="linkPadrao texto-cor-azul texto-size-16"
                                              value="#{ClientesVerificadosRelControle.ultimaVerificacao_Apresentar}"/>
                            </i>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <%--Matriculas I.C.V--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor-vermelho fa-icon-circle"
                                      style="background-color: transparent !important;"/>
                        <h:outputText style="margin-left: 1px;" styleClass="bi-table-text bi-font-family"
                                      value=" N�o checados" id="naoChecadosBI"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         id="qtdeNaoChecadosBI"
                                         action="#{ClientesVerificadosRelControle.mostrarNaoVerificados}"
                                         oncomplete="abrirPopup('clientesVerificadosForm.jsp', 'N�o Verificados',980 ,700);"
                                         value="#{ClientesVerificadosRelControle.qtdNaoVerificado}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <h:outputText styleClass="linkPadrao texto-cor-azul texto-size-16" value=" - "/>
                    </h:panelGroup>


                </h:panelGroup>
                <h:panelGroup layout="block" id="clientesverificados-container-resultado" style="margin-top: 4%"
                              styleClass="icv-container-resultado">
                    <h:outputText style="display: block;" styleClass="bi-table-text" value="�ndice de Verifica��o"
                                  id="IVBI"/>
                    <h:outputText styleClass="tooltipster gr-totalizador-text bi-font-family"
                                  title="#{ClientesVerificadosRelControle.mediaVerificacao_HintApresentar}"
                                  value="#{ClientesVerificadosRelControle.indiceVerificacao}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                    <h:outputText styleClass="tooltipster gr-totalizador-text bi-font-family"
                                  id="percIVBI"
                                  title="#{ClientesVerificadosRelControle.mediaVerificacao_HintApresentar}"
                                  value="%"/>
                </h:panelGroup>
                <h:panelGroup layout="block" id="panelGraficoClientesVerificados">
                    <jsp:include flush="true" page="include_bi_clientesverificados_grafico.jsp"/>
                </h:panelGroup>

            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>

<%-- 
    Document   : include_bi_panel_metacrescimento
    Created on : 23/08/2010, 15:35:14
    Author     : <PERSON><PERSON><PERSON>
--%>
<%@include file="../imports.jsp" %>

<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
    <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
        <h:outputText styleClass="tituloFormulario" value="Meta de Crescimento"/>
    </h:panelGrid>
    <rich:spacer style="display:block;" height="10"/>

    <h:panelGrid columns="3" width="90%" rowClasses="linhaPar, linhaImpar" columnClasses="colunaDireita" style="margin-left:20px;">
        <h:outputText value=""/>
        <h:outputText styleClass="textsmall" value="Novo"/>
        <h:outputText styleClass="textsmall" value="Anterior"/>

        <h:outputText rendered="#{MetaCrescimentoControle.usuario.administrador}" styleClass="text" value="Empresa:" />
        
        <h:selectOneMenu id="empresaMetaPanel" styleClass="form" onblur="blurinput(this);" rendered="#{MetaCrescimentoControle.usuario.administrador}"
                         onfocus="focusinput(this);" value="#{MetaCrescimentoControle.meta.empresa.codigo}">
            <f:selectItems value="#{MetaCrescimentoControle.listaSelectItemEmpresa}"/>
        </h:selectOneMenu>
        
        <h:outputText value="" rendered="#{MetaCrescimentoControle.usuario.administrador}"/>

        <h:outputText styleClass="text" value="Defina a Meta do Final do M�s:" />
        <h:inputText size="3" maxlength="5" style="margin-left:5px;text-align:right;" value="#{MetaCrescimentoControle.meta.metaCrescimento}"/>
        <h:outputText value=""/>

        <h:outputText styleClass="text" value="Mude seu IR se Desejar:" />
        <h:inputText size="3" maxlength="5" style="margin-left:5px;text-align:right;" value="#{MetaCrescimentoControle.meta.IRInformado}"/>
        <h:outputText styleClass="text" value="#{MetaCrescimentoControle.meta.IRAnterior}%"/>

        <h:outputText styleClass="text" value="Mude seu ICV se Desejar:" />
        <h:inputText size="3" maxlength="5" style="margin-left:5px;text-align:right;" value="#{MetaCrescimentoControle.meta.ICVInformado}"/>
        <h:outputText styleClass="text" value="#{MetaCrescimentoControle.meta.ICVAnterior}%"/>

        <h:outputText styleClass="text" value="Mude seu �ndice de Cancelamento se Desejar:" />
        <h:inputText size="3" maxlength="5" style="margin-left:5px;text-align:right;" value="#{MetaCrescimentoControle.meta.ICanceladosInformado}"/>
        <h:outputText styleClass="text" value="#{MetaCrescimentoControle.meta.ICanceladosAnterior}%"/>

        <h:outputText styleClass="text" value="Mude seu �ndice de Trancamento se Desejar:" />
        <h:inputText size="3" maxlength="5" style="margin-left:5px;text-align:right;" value="#{MetaCrescimentoControle.meta.ITrancadosInformado}"/>
        <h:outputText styleClass="text" value="#{MetaCrescimentoControle.meta.ITrancadosAnterior}%"/>
    </h:panelGrid>
    
    <rich:spacer style="display:block;" height="10"/>
    <h:outputText id="mensagem" styleClass="mensagemDetalhada" value="#{MetaCrescimentoControle.mensagemDetalhada}"/>
    <h:panelGroup>
        <a4j:commandButton id="gravar" value="#{msg_bt.btn_gravar}" reRender="mensagem, panelMesangem, form:panelMeta"
                           oncomplete="Richfaces.hideModalPanel('panelMetaCrescimento')" image="./imagens/botaoGravar.png"
                           alt="#{msg.msg_gravar_dados}" action="#{MetaCrescimentoControle.salvar}"/>
    </h:panelGroup>
</h:panelGrid>

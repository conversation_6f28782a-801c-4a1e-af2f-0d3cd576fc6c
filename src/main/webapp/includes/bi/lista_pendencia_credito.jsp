<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="../../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="../../beta/js/jquery.js" type="text/javascript"></script>
<script src="../../beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="../../beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="../../beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style>
    .tabelaCreditoContaCorrente tbody tr td:nth-child(6),.tabelaCreditoContaCorrente tbody tr td:nth-child(7){
        text-align: right;
    }
    .tabelaCreditoContaCorrente tbody tr td:nth-child(2),.tabelaCreditoContaCorrente tbody tr td:nth-child(5){
        text-align: left;
    }
    
    .margin-v-10{
        margin-top: 0px;
    }
    .tabelaCreditoContaCorrente tbody tr td:nth-child(1){
        display: none; 
    }
    .tabelaCreditoContaCorrente #codigoCliente{
        display: none; 
    }
    .tabelaCreditoContaCorrente tbody tr td:nth-child(4),.tabelaCreditoContaCorrente tbody tr td:nth-child(8),.tabelaCreditoContaCorrente tbody tr td:nth-child(11),.tabelaCreditoContaCorrente tbody tr td:nth-child(10){
        text-align: center;
    }
</style>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <title><h:outputText value="#{PendenciaControleRel.pendenciaSelecionada.descricao}"/></title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${PendenciaControleRel.pendenciaSelecionada.descricao}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-pendencias-de-clientes-adm/"/>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="../../topo_reduzido_popUp.jsp"/>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto margin-v-10 titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1-3 text-right">
                      <h:panelGroup layout="block" styleClass="controles">
                        <a4j:commandLink id="btnExcel"
                                         styleClass="linkPadrao"
                                         actionListener="#{PendenciaControleRel.exportar}"
                                         oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="3">
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,cpf_Apresentar=CPF,situacao_Apresentar=Situação,valorCC_Apresentar=Valor Crédito,dataRegistro_Apresentar=Data Lançamento,planoApresentar=Plano,duracaoApresentar=Duração,telefone_Apresentar=Telefone"/>
                            <f:attribute name="prefixo" value="ClientesComCredito"/>
                            <f:attribute name="titulo" value="#{PendenciaControleRel.pendenciaSelecionada.descricao}"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="btnPDF"
                                         styleClass="linkPadrao"
                                         style="margin-left: 8px"
                                         actionListener="#{PendenciaControleRel.exportar}"
                                         oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="4">
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,cpf_Apresentar=CPF,situacao_Apresentar=Situação,valorCC_Apresentar=Valor Crédito,dataRegistro_Apresentar=Data Lançamento,planoApresentar=Plano,duracaoApresentar=Duração,telefone_Apresentar=Telefone"/>
                            <f:attribute name="prefixo" value="ClientesComCredito"/>
                            <f:attribute name="titulo" value="#{PendenciaControleRel.pendenciaSelecionada.descricao}"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>
                      </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblCreditoContaCorrente"
                       class="tabelaCreditoContaCorrente pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <tr>
                        <th id="codigoCliente">Codigo</th>
                        <th>Matrícula</th>
                        <th style="text-align: center">Nome</th>
                        <th style="text-align: center">CPF</th>
                        <th>Situação</th>
                        <th style="text-align: center">Valor Crédito</th>
                        <th style="text-align: center">Data de Lançamento</th>
                        <th>Plano</th>
                        <th style="text-align: center">Duração Plano</th>
                        <th style="text-align: center">Telefone</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>


                <a4j:jsFunction name="jsEditar" action="#{PendenciaControleRel.irParaTelaClienteDatatables}"
                                oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700)"
                                reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{PendenciaControleRel.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{PendenciaControleRel.erro}" value="./imagens/erro.png"/>
                <h:outputText styleClass="mensagem" rendered="#{not empty PendenciaControleRel.mensagem}"
                              value=" #{PendenciaControleRel.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty PendenciaControleRel.mensagemDetalhada}"
                              value=" #{PendenciaControleRel.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

    <jsp:include page="../include_carregando_ripple.jsp" flush="true"/>
    </h:panelGroup>

    <script src="../../beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaCreditoContaCorrente", "${contexto}/prest/relatorio/pendencia", 1, "asc", 6, true, null, null, null, true, [10], null, [[10, 5]]);
        });
    </script>
</f:view>
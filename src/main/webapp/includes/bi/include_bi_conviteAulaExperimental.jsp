<%@include file="../imports.jsp" %>
<%--<PERSON><PERSON>o ainda em adamento deixado em standBy
    rendered="#{LoginControle.permissaoAcessoMenuVO.conviteBI}"--%>
<h:panelGroup id="biConvite" layout="block"
              styleClass="container-bi" rendered="#{false}">
    <a4j:commandLink action="#{BIControle.carregarConviteAulaExperimental}" reRender="containerConviteBI"
                     onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                     status="nenhumStatus"
                     styleClass="btn-atualizar-bi"/>

    <h:panelGroup layout="block" id="containerConviteBI" >
        <h:panelGroup layout="block" rendered="#{(BIControle.configuracaoBI.conviteBI.apresentarBoxCarregar) && !BIControle.biCarregado}" styleClass="bi-unloaded">
            <h:panelGroup styleClass="bi-header" layout="block" style="border: none">
                <h:panelGroup layout="block" styleClass="bi-panel" >
                    <h:outputText value="Convites de Aula Experimental" styleClass="bi-titulo pull-left"/>
                </h:panelGroup>
            </h:panelGroup>

            <div class="ghost">
                <table>
                    <tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr><tr>
                    <td><h3 class="card-title loading"></h3></td>
                    <td><h3 class="card-title loading"></h3></td>
                </tr>
                </table>
            </div>
        </h:panelGroup>

        <h:panelGroup layout="block" rendered="#{!BIControle.configuracaoBI.conviteBI.apresentarBoxCarregar || BIControle.biCarregado}">
            <h:panelGroup styleClass="bi-header" layout="block">
                <h:panelGroup layout="block" styleClass="bi-panel">
                    <h:outputText value="BI - Convites de Aula Experimental" styleClass="bi-titulo pull-left"/>

                    <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                  style="float: left;margin-top: 4.4%;margin-left: 2%"
                                  value="#{SuperControle.urlBaseConhecimento}bi-ticket-medio-de-planos-adm/"
                                  title="Clique e saiba mais: BI - Ticket M�dio" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                    </h:outputLink>

                    <a4j:commandLink 
                        style="margin-left: 7px"
                        reRender="containerTicketMedio"
                        oncomplete="montarTips();"
                        action="#{BIConviteAulaExperimentalControle.atualizar}">
                        <i title="Consultar dados de Convites de Aula Experimental" 
                           class="fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em tooltipster"></i>
                    </a4j:commandLink>


                </h:panelGroup>

            </h:panelGroup>
            <h:panelGroup layout="block"
                          styleClass="bi-row bi-row-center"
                          style="margin-left: 0; width: 100%;">
                <h:outputText value="Alunos sem consultor" style="margin-left: 4.5%;"
                              styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>
                <a4j:commandLink  styleClass="texto-font texto-size-16 texto-cor-azul tooltipster" title="Ver Detalhes"
                                  style="margin-right: 4.5%;"
                                  oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 780, 595);"
                                  value="#{BIConviteAulaExperimentalControle.bi.alunosSemConsultor}"/>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          styleClass="bi-row bi-row-center"
                          style="border-top: 1px solid #dde7e7; margin-left: 0; width: 100%;">
                <h:outputText value="Convites enviados" style="margin-left: 4.5%;"
                              styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>
                <a4j:commandLink  styleClass="texto-font texto-size-16 texto-cor-azul tooltipster" title="Ver Detalhes"
                                  style="margin-right: 4.5%;"
                                  oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 780, 595);"
                                  value="#{BIConviteAulaExperimentalControle.bi.convitesEnviados}"/>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          styleClass="bi-row bi-row-center"
                          style="border-top: 1px solid #dde7e7; margin-left: 0; width: 100%;">
                <h:outputText value="Valida��es de convites" style="margin-left: 4.5%;"
                              styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>
                <a4j:commandLink  styleClass="texto-font texto-size-16 texto-cor-azul tooltipster" title="Ver Detalhes" 
                                  style="margin-right: 4.5%;"
                                  oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 780, 595);"
                                  value="#{BIConviteAulaExperimentalControle.bi.convitesValidados}"/>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          styleClass="bi-row bi-row-center"
                          style="border-top: 1px solid #dde7e7; margin-left: 0; width: 100%;">
                <h:outputText value="Aulas agendadas" style="margin-left: 4.5%;"
                              styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>
                <a4j:commandLink  styleClass="texto-font texto-size-16 texto-cor-azul tooltipster" title="Ver Detalhes"
                                  style="margin-right: 4.5%;"
                                  oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 780, 595);"
                                  value="#{BIConviteAulaExperimentalControle.bi.agendaramAula}"/>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          styleClass="bi-row bi-row-center"
                          style="border-top: 1px solid #dde7e7; margin-left: 0; width: 100%;">
                <h:outputText value="Convidados que compareceram � aula" style="margin-left: 4.5%;"
                              styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>
                <a4j:commandLink  styleClass="texto-font texto-size-16 texto-cor-azul tooltipster" title="Ver Detalhes"
                                  style="margin-right: 4.5%;"
                                  oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 780, 595);"
                                  value="#{BIConviteAulaExperimentalControle.bi.agendaramCompareceram}"/>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          styleClass="bi-row bi-row-center"
                          style="border-top: 1px solid #dde7e7; margin-left: 0; width: 100%;">
                <h:outputText value="Convers�o de vendas" style="margin-left: 4.5%;"
                              styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>
                <a4j:commandLink  styleClass="texto-font texto-size-16 texto-cor-azul tooltipster" title="Ver Detalhes"
                                  style="margin-right: 4.5%;"
                                  oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 780, 595);"
                                  value="#{BIConviteAulaExperimentalControle.bi.conversaoVendas}%"/>
            </h:panelGroup>
                        
            <h:panelGroup layout="block" styleClass="bi-panel" 
                          style="text-align: right; border-top: 1px solid #dde7e7; margin-left: 0; width: 100%;">
                <a4j:commandLink value="Ver gr�fico de evolu��o dos convites"
                                 action="#{BIConviteAulaExperimentalControle.abrirGrafico}"
                                 oncomplete="montarTips();Richfaces.showModalPanel('modalGraficoConvite')" 
                                 style="margin-right: 4.5%;"
                                 styleClass="linkPadrao texto-font texto-size-16 texto-cor-azul tooltipster"
                                 title="Ver gr�fico de evolu��o dos convites"
                                 reRender="painelmodalGraficoConvite">
                    <h:outputText style="margin-left: 5px;vertical-align: middle;"
                                  styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-bar-chart"/>
                </a4j:commandLink>
            </h:panelGroup>
           

        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
                        


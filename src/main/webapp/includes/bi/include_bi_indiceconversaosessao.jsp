<%--
    Document   : include_bi_pendencias
    Created on : 20/02/2016
    Author     : <PERSON>-%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-idcsessao" styleClass="container-bi"
              rendered="#{LoginControle.apresentarLinkEstudio && LoginControle.permissaoAcessoMenuVO.indiceConversao}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.indiceConversao || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.conversaoVendaSS.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarConversaoSessao}" reRender="containerConversaoSessao"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         status="nenhumStatus"
                         styleClass="btn-atualizar-bi"/>
        <h:panelGroup layout="block" id="containerConversaoSessao" styleClass="bordaEST">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.conversaoVendaSS.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Convers�o de vendas por Sess�o" styleClass="bi-titulo pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.conversaoVendaSS.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup id="bi-header-icvss" styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Convers�o de vendas por Sess�o" styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="tooltipster linkWiki bi-cor-cinza bi-left-align"
                                      style="float: left;margin-top: 4.4%;margin-left: 2%"
                                      value="#{SuperControle.urlWiki}Relat�rios:BI_-_�ndice_de_Convers�o_de_Vendas_Sessoes"
                                      title="Clique e saiba mais: BI - �ndice de Convers�o de Vendas(Sess�es)" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarICVSS"
                                         reRender="containerConversaoSessao"
                                         onclick="carregarGragicoICVSS();montarTips();"
                                         action="#{ICVSessaoRelControle.consultarICVS}">
                            <i title="Consultar Dados Indice de Convers�o de Vendas por Sess�o"
                               class="tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>

                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelFiltroSessao')">
                            <i title="Filtro"
                               class="tooltipster fa-icon-filter bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>

                        <h:panelGroup layout="block" styleClass="col-text-align-right pull-right calendarSemInputBI">
                            <div title="${ICVSessaoRelControle.dataBase_ApresentarMesDia}"
                                 style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                                 class="tooltipster dateTimeCustom alignToRight">
                                <rich:calendar id="dataInicioICVSS"
                                               value="#{ICVSessaoRelControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{ICVSessaoRelControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{ICVSessaoRelControle.consultarIndiceConversaoVendaPorColaborador}"
                                                 oncomplete="carregarGragicoICVSS();montarTips();"
                                                 reRender="panelDataBaseICVSS,bi-header-icvss,panelListaColaboradorIndiceConversaoSessao,icvss-container-numeros,icvss-container-resultado,panelGraficoICVSS"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      styleClass="pull-right calendarSemInputBI" style="margin-right: 10px;">
                            <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')"
                                             action="#{BIControle.biAtualizarParam}"
                                             reRender="formPanelFiltroCol, tituloFiltro">
                                <i title="Filtrar Por Colaborador"
                                   class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">
                                    <span class="badgeItem3Icon" data-bagde="${BIControle.qtdColConvVendasSs}"></span>
                                </i>
                                <a4j:actionparam name="biAtualizar" value="CONVERSAO_VENDAS_SS"></a4j:actionparam>
                                <a4j:actionparam name="biAtualizarAbrirConsulta"
                                                 value="biAtualizarAbrirConsulta"></a4j:actionparam>
                                <a4j:actionparam name="reRenderBi" value="containerConversaoSessao"></a4j:actionparam>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup id="icvss-container-numeros" layout="block" style="margin-top: 4%"
                              styleClass="icv-container-numeros">

                    <h:panelGroup layout="block" style="margin-left: 60%;text-align: center"
                                  styleClass="icv-numeros-col">
                        <h:outputText styleClass="icv-periodo-text" value="DIA"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <h:outputText styleClass="icv-periodo-text" value="M�S"/>
                    </h:panelGroup>

                    <%--Resultado B.V.--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor- fa-icon-circle texto-cor-cinza-3"/>
                        <h:outputText styleClass="bi-table-text bi-font-family" value=" BV"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{ICVSessaoRelControle.mostrarListaClientesBVHoje}"
                                         oncomplete="abrirPopup('indiceConversaoVendaSessao.jsp', 'Bv', 850, 595);"
                                         value="#{ICVSessaoRelControle.indiceConversaoVendaVO.qtdBVSessaoDia}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{ICVSessaoRelControle.mostrarListaClientesBVMes}"
                                         oncomplete="abrirPopup('indiceConversaoVendaSessao.jsp', 'Bv', 850, 595);"
                                         value="#{ICVSessaoRelControle.indiceConversaoVendaVO.qtdBVSessaoMes}"/>
                    </h:panelGroup>

                    <%--Matriculas I.C.V--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor-azul fa-icon-circle"/>
                        <h:outputText style="margin-left: 1px;" styleClass="bi-table-text bi-font-family"
                                      value=" Primeira sess�o"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{ICVSessaoRelControle.mostrarListaSessaoHoje}"
                                         oncomplete="abrirPopup('indiceConversaoVendaSessao.jsp', 'Sessao', 850, 595);"
                                         value="#{ICVSessaoRelControle.indiceConversaoVendaVO.qtdBVSessaoPrimeiraCompraDia}">
                            <a4j:actionparam name="tipo" value="SS-P"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{ICVSessaoRelControle.mostrarListaSessaoMes}"
                                         oncomplete="abrirPopup('indiceConversaoVendaSessao.jsp', 'Sessao', 850, 595);"
                                         value="#{ICVSessaoRelControle.indiceConversaoVendaVO.qtdBVSessaoPrimeiraCompraMes}">
                            <a4j:actionparam name="tipo" value="SS-P"/>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <%--Renovados I.C.V--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor-verde fa-icon-circle"/>
                        <h:outputText style="margin-left: 1px;" styleClass="bi-table-text bi-font-family"
                                      value=" Retornos"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{ICVSessaoRelControle.mostrarListaSessaoHoje}"
                                         oncomplete="abrirPopup('indiceConversaoVendaSessao.jsp', 'Sessao', 850, 595);"
                                         value="#{ICVSessaoRelControle.indiceConversaoVendaVO.qtdBVSessaoRetornoCompraDia}">
                            <a4j:actionparam name="tipo" value="SS-R"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{ICVSessaoRelControle.mostrarListaSessaoMes}"
                                         oncomplete="abrirPopup('indiceConversaoVendaSessao.jsp', 'Sessao', 850, 595);"
                                         value="#{ICVSessaoRelControle.indiceConversaoVendaVO.qtdBVSessaoRetornoCompraMes}">
                            <a4j:actionparam name="tipo" value="SS-R"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" id="icvss-container-resultado" style="margin-top: 4%"
                              styleClass="icv-container-resultado">
                    <h:outputText style="display: block;" styleClass="bi-table-text"
                                  value="ICV"/>
                    <h:outputText id="bvIndiceConversaoSS" styleClass="gr-totalizador-text bi-font-family"
                                  value="#{ICVSessaoRelControle.indiceConversaoVendaVO.totalICVS}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                    <h:outputText styleClass="gr-totalizador-text bi-font-family" value="%"/>
                </h:panelGroup>
                <h:panelGroup layout="block" id="panelGraficoICVSS">
                    <jsp:include flush="true" page="include_bi_icvss_grafico.jsp"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>

</h:panelGroup>

<style>
    .titulobeta {
        font-family: Arial;
        background: #243570;
        border-radius: 8px;
        /* display: block; */
        color: white;
        line-height: 40px;
        padding: 2px 6px;
        margin-left: 10px;
        font-weight: bold;
        font-size: 50px;
    }

    .probabilidadeEvasao {
        margin-top: 6px;
        font-family: Arial;
        font-size: 16px;
        line-height: 24px;
        color: grey;
    }

    .dataTexto {
        font-family: Arial;
        font-weight: normal;
        font-size: 12px;
        line-height: 24px;
        /*margin-left: 22px;*/
        color: grey;
    }

    .quantidadedAlunos {
        font-family: Arial;
        display: flex;
        text-align: center;
        flex-direction: column;
        align-items: center;
        font-weight: bold;
        font-size: 48px;
        margin-left: 16px;
    }
    /*TODO Impactando nos hiperlinks do navbar do detalheBI.jsp*/
    /*a:visited {*/
    /*    color: black;*/
    /*}*/

    .alunos {
        padding-bottom: 20px;
        font-family: Arial;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-weight: bold;
        font-size: 12px;
        color: grey;
        margin-left: 14px;
    }

    .rich-inslider-track-decor-1 {
        border: none;
    }

    .rich-inslider-track-decor-2 {
        background-image: linear-gradient(to right, #1998FC, #F0F0F0);
        height: 12px;
        border-radius: 8px;
        border: none;
    }

    .rich-inslider-track {
        background-image: none !important;
    }

    .rich-inslider-handler {
        top: -26px;
        /*left: 100px;*/
        background-image: url("../images/pin.png");
        background-repeat: no-repeat;
        background-position: center;
        background-position-y: 2px;
        background-size: contain;
        height: 47px;
        width: 24px;
        margin: 0px;
        /*-webkit-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);*/
        /*-moz-box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);*/
        /*box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.35);*/
    }

    .rich-inslider-handler-selected {
        top: -28px;
        background-image: url("../images/pin.png") !important;
        background-repeat: no-repeat;
        background-position: center;
        background-position-y: 2px;
        background-size: contain;
        height: 47px;
        width: 24px;
        margin: 0px;
        padding: 0px;
    }

    .right-component{
        width:37%;
        background-image: url("../images/churn-rate-icon.png");
        background-repeat: no-repeat;
        float: right;
        width: 150px;
        height: 130px;
        margin-right: 30px;
    }

    .container div {

    }

    .container {
        width: 80%;
        height: 200px;
        background: aqua;
        margin: auto;
        padding: 10px;
    }

    .one {
        width: 15%;
        height: 200px;
        background: red;
        float: left;
    }

    .two {
        margin-left: 15%;
        height: 200px;
        background: black;
    }

    .rich-inslider-left-num {
        border: none;
        color: white;
    }

    .rich-inslider-right-num {
        border: none;
        color: white;
    }

    .rich-inslider-size {
        border: none;
        background-color: white;
    }

    .badgeItem1Icon:after { /*Utilizado para filtro de colaborador por BI quando tem apenas um �cone � direita*/
        right: 4%;
        content: attr(data-bagde);
        position: absolute;
        top: -7px;
        text-align: center;
        font-size: 12px;
        line-height: normal;
        color: #fff;
        padding: 0px 9px 0px 9px;
        background-color: #29ABE2;
        border-radius: .5em
    }
    .barra-principal{
        display: flex;
    }
    .barra-item1{
        flex-basis: auto;
        border-top-width: 1px;
        border-left-width: 1px;
        border-top-color: #0A3858;
        border-left-color: #0A3858;
        height: 1px;
    }
    .barra-item2{
        flex-basis: auto;
        border-top-width: 1px;
        border-left-width: 1px;
        border-right-width: 1px;
        border-top-color: #0A3858;
        border-left-color: #0A3858;
        border-right-color: #0A3858;
        height: 1px;
    }
</style>

<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="biProbEvas" rendered="#{RelControleProbEvas.mostraCardIA}" styleClass="container-bi">
    <a4j:commandLink action="#{BIControle.carregarProbEvas}" reRender="containerProbEvas"
                     onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                     status="nenhumStatus"
                     styleClass="btn-atualizar-bi"/>
    <h:panelGroup layout="block" id="containerProbEvas">
        <h:panelGroup layout="block"
                      rendered="#{(BIControle.configuracaoBI.probEvas.apresentarBoxCarregar) && !BIControle.biCarregado}"
                      styleClass="bi-unloaded">
            <h:panelGroup styleClass="bi-header" layout="block">
                <h:panelGroup layout="block" styleClass="bi-panel">
                    <h:outputText value="Pacto IA" styleClass="bi-titulo pull-left"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block"
                      rendered="#{!BIControle.configuracaoBI.probEvas.apresentarBoxCarregar || BIControle.biCarregado}">
            <h:panelGroup styleClass="bi-header" layout="block">
                <h:panelGroup layout="block" styleClass="bi-panel">
                    <h:outputText value="Pacto IA - Churn Rate" styleClass="bi-titulo pull-left"/>
                    <h:outputText value="BETA" styleClass="titulobeta"/>

                    <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                  style="float: left;margin-top: 4.4%;margin-left: 1%;"
                                  value="#{SuperControle.urlWiki}Relat%C3%B3rios:IA_-_Churn_Rate"
                                  title="Clique e saiba mais: BI - Pacto IA - Churn Rate" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                    <h:panelGroup layout="block"
                                  styleClass="pull-right calendarSemInputBI" style="margin-right: 4px;">
                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')"
                                         action="#{BIControle.biAtualizarParam}"
                                         reRender="formPanelFiltroCol, tituloFiltro">
                            <i title="Filtrar Por Colaborador"
                               class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">
                                <span class="badgeItem1Icon" style="margin-right: 4px;"
                                      data-bagde="${BIControle.qtdColContrOperacoes}"></span>
                            </i>
                            <a4j:actionparam name="biAtualizar" value="PROBABILIDADE_EVASAO"></a4j:actionparam>
                            <a4j:actionparam name="biAtualizarAbrirConsulta"
                                             value="biAtualizarAbrirConsulta"></a4j:actionparam>
                            <a4j:actionparam name="reRenderBi" value="containerProbEvas"></a4j:actionparam>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <div style="display: inline-block; width: 73%">
                <h:panelGrid style="margin-left:2%; width: 98%">
                    <h:panelGrid id="grupoSlider" columns="1" width="100%" style="padding-top: 20px">
                        <h:panelGroup id="qtdAlunos">
                            <a4j:commandLink styleClass="quantidadedAlunos"
                                             oncomplete="abrirPopup('probabilidadeEvasao.jsp', 'probabilidadeEvasao', 1024, 595);"
                                             value="#{RelControleProbEvas.qtdAlunos}"/>
                        </h:panelGroup>
                        <h:outputLabel styleClass="alunos" value="alunos"/>
                        <rich:inputNumberSlider minValue="0" maxValue="90" width="100%" showInput="false" step="10"
                                                showToolTip="false"
                                                enableManualInput="false"
                                                value="#{RelControleProbEvas.valorSlider}">
                            <a4j:support event="onchange" action="#{RelControleProbEvas.consultaQtdAlunos}" reRender="qtdAlunos, valorSliderPerc"/>
                        </rich:inputNumberSlider>
                    </h:panelGrid>
                </h:panelGrid>

                <div style="text-align: center; margin-left:27px; width: 92%;padding-top: 12px">
                    <h:outputLabel value="0%" styleClass="texto-cor-cinza texto-font texto-size-16-real"
                                   style="margin-left: 3px;float: left"/>
                    <h:outputLabel id="valorSliderPerc" value="#{RelControleProbEvas.valorSlider} � 100%"
                                   styleClass="texto-font texto-size-18 texto-bold"
                                   style="display: inline-flex;margin-left: 50px"/>
                    <h:outputLabel value="100%" styleClass="texto-cor-cinza texto-font texto-size-16-real"
                                   style="margin-right: 3px;float: right"/>
                </div>
            </div>

            <div style="display: inline-block; width: 25%; vertical-align: top; text-align: right">
                <h:graphicImage style="margin-top: 40px; margin-right: 40px;" url="../images/churn-rate-icon.png"/>
            </div>
            <h:panelGroup layout="block" styleClass="bi-panel" style="text-align: right;padding-top: 8px">
                <a4j:commandLink action="#{RelControleProbEvas.consultarAlunos}"
                                 styleClass="bi-btn-refresh bi-link pull-right"
                                 oncomplete="abrirPopup('probabilidadeEvasao.jsp', 'probabilidadeEvasao', 1024, 595);">
                    Ver Alunos &nbsp<i title="Visualizar os alunos" class="tooltipster fa-icon-plus-sign "></i>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
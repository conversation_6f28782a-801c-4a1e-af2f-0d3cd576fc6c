<%-- 
    Document   : include_bi_rotatividade
    Created on : 03/02/2011, 16:39:30
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block"
              rendered="#{LoginControle.permissaoAcessoMenuVO.rotatividade}"
              id="bi-movimentacao-contrato" styleClass="container-bi">
    <a4j:commandLink action="#{BIControle.carregarRotatividade}" reRender="containerRotatividade"
                     onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                     status="nenhumStatus"
                       styleClass="btn-atualizar-bi"/>


    <h:panelGroup layout="block" id="containerRotatividade" >
        <h:panelGroup layout="block" rendered="#{(BIControle.configuracaoBI.movContrato.apresentarBoxCarregar) && !BIControle.biCarregado}" styleClass="bi-unloaded">
            <h:panelGroup styleClass="bi-header" layout="block">
                <h:panelGroup layout="block" styleClass="bi-panel" >
                    <h:outputText value="Movimenta��o de Contratos" styleClass="bi-titulo pull-left"/>
                </h:panelGroup>
            </h:panelGroup>

            <div class="ghost">
                <table>
                    <tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr><tr>
                    <td><h3 class="card-title loading"></h3></td>
                    <td><h3 class="card-title loading"></h3></td>
                </tr><tr>
                    <td><h3 class="card-title loading"></h3></td>
                    <td><h3 class="card-title loading"></h3></td>
                </tr><tr>
                    <td><h3 class="card-title loading"></h3></td>
                    <td><h3 class="card-title loading"></h3></td>
                </tr><tr>
                    <td><h3 class="card-title loading"></h3></td>
                    <td><h3 class="card-title loading"></h3></td>
                </tr>
                </table>
            </div>
        </h:panelGroup>
        <h:panelGroup layout="block" rendered="#{!BIControle.configuracaoBI.movContrato.apresentarBoxCarregar || BIControle.biCarregado}">
            <h:panelGroup layout="block" styleClass="bi-header">

                <h:panelGroup styleClass="bi-panel" layout="block">
                    <h:outputText value="Movimenta��o de contratos" styleClass="bi-titulo pull-left"/>

                    <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                  style="float: left;margin-top: 4.4%;margin-left: 2%"
                                  value="#{SuperControle.urlBaseConhecimento}bi-movimentacao-de-contratos-adm/"
                                  title="Clique e saiba mais: Movimenta��o de Contratos" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>

                    <a4j:commandLink id="consultarRotatividade"
                                     style="margin-left: 7px"
                                     reRender="containerRotatividade"
                                     oncomplete="montarTips();"
                                     action="#{RotatividadeAnaliticoDWControle.atualizar}">
                        <i  title="${RotatividadeAnaliticoDWControle.dataAtualizacao_Apresentar}" class="tooltipster lineHeight-3em fa-icon-refresh bi-btn-refresh bi-link pull-right tooltipster"></i>
                    </a4j:commandLink>
                    <h:panelGroup layout="block" styleClass="pull-right calendarSemInputBI col-text-align-right" >
                        <div title="${RotatividadeAnaliticoDWControle.dataBase_ApresentarMesDia}"
                             style="display: inline-flex;margin-top: 1em;vertical-align: top;" class="tooltipster dateTimeCustom alignToRight">
                            <rich:calendar id="dataInicioRotatividade"
                                           value="#{RotatividadeAnaliticoDWControle.dataBaseFiltro}"
                                           inputSize="8"
                                           showInput="false"
                                           inputClass="forcarSemBorda"
                                           buttonIcon="#{RotatividadeAnaliticoDWControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false">
                                <a4j:support event="onchanged" action="#{RotatividadeAnaliticoDWControle.atualizar}" oncomplete="montarTips();"   reRender="containerRotatividade"/>
                            </rich:calendar>
                        </div>
                    </h:panelGroup>
<%--                    <h:panelGroup layout="block"--%>
<%--                                  styleClass="pull-right calendarSemInputBI" style="margin-right: 12px;">--%>
<%--                        Este ainda n�o filtra por colaborador--%>

<%--                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')" action="#{BIControle.biAtualizarParam}"  reRender="colCheck, grupoMarcadoCss, tituloFiltro">--%>
<%--                            <i title="Filtrar Por Colaborador" class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">--%>
<%--                                <span class="badgeItem2Icon" data-bagde="${BIControle.qtdColRotContrato}"></span>--%>
<%--                            </i>--%>
<%--                            <a4j:actionparam name="biAtualizar" value="ROTATIVIDADE_CONTRATO"></a4j:actionparam>--%>
<%--                            <a4j:actionparam name="biAtualizarAbrirConsulta" value="biAtualizarAbrirConsulta"></a4j:actionparam>--%>
<%--                            <a4j:actionparam name="reRenderBi" value="containerRotatividade"></a4j:actionparam>--%>
<%--                        </a4j:commandLink>--%>
<%--                    </h:panelGroup>--%>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="container-row">
                <h:panelGroup styleClass="bi-row" layout="block">
                    <h:outputText styleClass="bi-font-bold bi-table-text bi-left-align" value="IN�CIO DO M�S"/>
                    <h:outputText styleClass="bi-table-text bi-right-align"
                                  value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.periodoDiasDataBase}"/>
                </h:panelGroup>

                <h:panelGroup styleClass="bi-row" layout="block">
                    <h:outputText styleClass="bi-table-text bi-left-align" value="Ativos"/>
                    <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster" id="mcAtivoInicio"
                                     title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVigenteInicioMes}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorVigenteMes}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdeVigenteMesAtual}"/>
                </h:panelGroup>

                <h:panelGroup styleClass="bi-row" layout="block">
                    <h:outputText styleClass="bi-table-text bi-left-align" value="Vencidos"/>
                    <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-right-align tooltipster" id="mcVencidosInicio"
                                     title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVencidoAnterior}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorVencido}"
                                     value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdVencido}"/>
                </h:panelGroup>

            </h:panelGroup>


            <h:panelGroup styleClass="bi-row bi-bg-verde bi-row-center" layout="block">
                <h:outputText styleClass="texto-size-16  bi-text-verde bi-font-bold bi-left-align"
                              value="Total de ativos + Vencidos"/>
                <a4j:commandLink styleClass="texto-size-16  bi-text-verde tooltipster" id="mcTotalInicio" title="Ver Detalhes"
                                 action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVirgentesMesAnterior}"
                                 oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorVigenteMes}"
                                 value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdVigentesMesAnterior}"/>
            </h:panelGroup>
            <h:panelGroup styleClass="bi-separador" layout="block"/>
            <h:panelGroup layout="block" styleClass="container-row">
                <h:panelGrid columns="3" width="100%" styleClass="panel-grid-icv"
                             columnClasses="col-text-align-left,col-text-align-right,col-text-align-right">
                    <h:outputText value="RESULTADOS" styleClass="icv-periodo-text"/>
                    <h:outputText value="HOJE" styleClass="icv-periodo-text"/>
                    <h:outputText value="M�S" styleClass="icv-periodo-text"/>

                    <h:outputText value="Matriculados" styleClass="texto-size-16 bi-cor-cinza "/>

                    <a4j:commandLink id="mcMatriculadosHoje" styleClass="texto-size-16  bi-cor-azul tooltipster" title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoMatriculadosHoje}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorMatriculaHoje}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdMatriculadoHoje}"/>

                    <a4j:commandLink id="mcMatriculadosMes" title="Ver Detalhes"
                                     styleClass="texto-size-16  bi-cor-azul tooltipster"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoMatriculados}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorMatricula}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdMatriculado}"/>


                    <h:outputText value="Rematriculados" styleClass="texto-size-16 bi-cor-cinza "/>


                    <a4j:commandLink id="mcRematriculadosHoje" styleClass="texto-size-16  bi-cor-azul tooltipster" title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoRematriculaHoje}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorRematriculaHoje}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdRematriculadoHoje}"/>

                    <a4j:commandLink id="mcRematriculadosMes" title="Ver Detalhes" styleClass="texto-size-16  bi-cor-azul tooltipster"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoRematricula}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorRematricula}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdRematriculado}"/>

                    <h:outputText value="Cancelados" styleClass="gr-text-warning bi-font-family"/>

                    <a4j:commandLink id="mcCanceladosHoje" styleClass="texto-size-16  texto-cor-vermelho tooltipster" title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoCanceladoHoje}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorCanceladoHoje}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdCanceladoHoje}"/>

                    <a4j:commandLink id="mcCanceladosMes" title="Ver Detalhes" styleClass="texto-size-16 texto-cor-vermelho tooltipster"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoCancelado}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorCancelado}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdCancelado}"/>


                    <h:outputText value="Trancados" styleClass="gr-text-warning bi-font-family"/>


                    <a4j:commandLink id="mcTrancadoHoje" styleClass="texto-size-16  texto-cor-vermelho tooltipster" title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoTrancadoHoje}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorTrancadoHoje}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdTrancamentoHoje}"/>

                    <a4j:commandLink id="mcTrancadoMes" title="Ver Detalhes" styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoTrancado}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorTrancado}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdTrancamento}"/>

                    <h:outputText value="Desist�ncias" styleClass="gr-text-warning bi-font-family"/>


                    <a4j:commandLink id="mcDesistesHoje" styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                     title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoDesistenteHoje}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDesistenteHoje}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdDesistenteHoje}"/>

                    <a4j:commandLink id="mcDesistesMes" title="Ver Detalhes" styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoDesistente}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDesistente}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdDesistente}"/>

                    <h:outputText value="Retorno de trancamento" styleClass="texto-size-16 bi-cor-cinza"/>

                    <a4j:commandLink id="mcReTrancamentoHoje" styleClass="texto-size-16  bi-cor-azul tooltipster" title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoRetornoTrancamentoHoje}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorRetornoTrancamentoHoje}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdRetornoTrancamentoHoje}"/>

                    <a4j:commandLink id="mcReTrancamentoMes" title="Ver Detalhes" styleClass="texto-size-16  bi-cor-azul tooltipster"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoRetornoTrancamento}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorRetornoTrancamento}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdRetornoTrancamento}"/>

                    <h:outputText value="Contratos Transferidos" styleClass="texto-size-16 bi-cor-cinza"/>

                    <a4j:commandLink id="mcContratoTransferidoHoje" styleClass="texto-size-16  bi-cor-azul tooltipster" title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoContratoTransferidoHoje}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorContratoTransferidoHoje}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdContratoTransferidoHoje}"/>

                    <a4j:commandLink id="mcContratoTransferidoMes" title="Ver Detalhes" styleClass="texto-size-16  bi-cor-azul tooltipster"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoContratoTransferido}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorContratoTransferido}"
                                     value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdContratoTransferido}"/>

                </h:panelGrid>
            </h:panelGroup>

            <h:panelGroup
                    styleClass="bi-row bi-row-center #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoMes}"
                    layout="block">

                <h:outputText
                        styleClass="texto-size-16  bi-font-bold bi-left-align #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoMes}"
                        value="Saldo do m�s"/>

                <h:outputText id="mcSaldoMes"
                              styleClass="texto-size-16  #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoMes}"
                              value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdSaldo}"/>

            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-separador"/>
            <h:panelGroup layout="block" styleClass="container-row">
                <h:panelGroup styleClass="bi-row" layout="block">

                    <h:outputText styleClass="icv-periodo-text bi-left-align tooltipster" value="FIM DO M�S" title="Para este c�lculo � considerada a data de in�cio do contrato"/>

                </h:panelGroup>

                <h:panelGroup styleClass="bi-row" layout="block">

                    <h:outputText styleClass="texto-size-14 bi-cor-cinza  bi-left-align " value="Ativos"/>

                    <a4j:commandLink id="mcAtivosFim" title="Ver Detalhes"
                                     styleClass="texto-size-16  bi-right-align bi-cor-azul tooltipster"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVigenteMes}"
                                     oncomplete="abrirPopup('rotatividadeClienteForm.jsp', 'RotatividadeCliente', 850, 650);"
                                     value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdeFinalMesAtual}"/>
                </h:panelGroup>

                <h:panelGroup styleClass="bi-row" layout="block">

                    <h:outputText styleClass="texto-size-14 bi-cor-cinza  bi-left-align " value="Vencidos"/>

                    <a4j:commandLink id="mcVencidosFim"
                                     styleClass="texto-size-16  bi-right-align bi-cor-azul tooltipster"
                                     title="Ver Detalhes"
                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVencidoMes}"
                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorVencidoMes}"
                                     value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdVencidoMes}"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup styleClass="bi-row bi-bg-verde bi-row-center" layout="block">

                <h:outputText styleClass="texto-size-14 bi-cor-verde bi-font-bold  bi-left-align"
                              value="Total de ativos + Vencidos"/>

                <a4j:commandLink id="mcTotalFim" title="Ver Detalhes" styleClass="texto-size-16  bi-cor-verde tooltipster"
                                 action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoTodos}"
                                 oncomplete="abrirPopup('rotatividadeClienteForm.jsp', 'RotatividadeCliente', 850, 650);"
                                 value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdTotal}"/>
            </h:panelGroup>
            <h:panelGroup styleClass="bi-panel-margin-bottom" layout="block"/>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

<%--
    Document   : include_bi_intervencaomanual
    Created on : 13/06/2011
    Author     : <PERSON><PERSON>
--%>
<%@include file="../imports.jsp" %>
<h:panelGrid id="panelAMD" columns="1" width="98%" rendered="#{LoginControle.permissaoAcessoMenuVO.relatorioAlteracaoDataBaseContrato}" style="clear:both;" cellpadding="0" cellspacing="0">
    <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:10px;margin-bottom:5px;">
        <tr>
            <td width="100%">
                <table width="98%"  border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:10px;margin-bottom:5px;">
                    <tr>
                        <td>
                            <h:panelGroup  styleClass="alinhamentoSuperior, tituloboxcentro2" style="margin-right:10px;margin-bottom:5px;">
                                <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" >
                                    <tr>
                                        <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"/></td>
                                        <td align="left" width="100%" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding:10px 0 0 0;">
                                            BI - Altera��o Manual de Data Base de Contrato
                                            <a href="${SuperControle.urlWiki}Relat�rios:BI_-_Altera��o_Manual_de_Data_Base_de_Contrato" class="linkWiki"  title="Clique e saiba mais: BI - Altera��o Manual de Data Base de Contrato" target="_blank" >
                                                <img src="imagens/wiki_link2.gif" alt="Clique e saiba mais: BI - Altera��o Manual de Data Base de Contrato" class="linkWiki"/>
                                            </a>

                                        </td>
                                        <td class="tituloboxcentro2">
                                            <rich:calendar id="dataFimAMD"
                                                           value="#{RelAlteracaoDataManualControle.fim}"
                                                           direction="bottom-left"
                                                           inputSize="10"
                                                           styleClass="tituloboxcentro2"
                                                           showInput="false"
                                                           inputClass="form"
                                                           datePattern="dd/MM/yyyy"
                                                           zindex="2"
                                                           showWeeksBar="false">
                                                <a4j:support event="onchanged" reRender="panelAMD"
                                                             action="#{RelAlteracaoDataManualControle.atualizar}"/>
                                            </rich:calendar>
                                        </td>
                                        <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"/></td>
                                    </tr>
                                    <tr>
                                        <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif"></td>
                                        <td align="right" colspan="2" valign="top" bgcolor="#ffffff" style="padding:-5px 0px 0px 5px;">
                                            <h:panelGroup id="panelDataSelecionadaAMD">
                                                <h:outputText styleClass="textverysmall" value="Data base: " style="text-align:right;color:#0f4c6b;"/>
                                                <h:outputText styleClass="textverysmall" value="#{RelAlteracaoDataManualControle.fim}"
                                                              style="text-align:right;color:#0f4c6b;">
                                                    <f:convertDateTime type="date" dateStyle="short"
                                                                       locale="pt" timeZone="America/Sao_Paulo"
                                                                       pattern="dd/MM/yyyy" />
                                                </h:outputText>
                                            </h:panelGroup>
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom:5px;">
                                                <tr><td style="padding:0px 0px 0px 0px;">
                                                        <h:panelGroup id="panelEmpresaAMD" rendered="#{RelAlteracaoDataManualControle.usuario.administrador}" >
                                                            <h:outputText styleClass="titulo3" value="Empresa:" />
                                                            <rich:spacer width="10px" />
                                                            <h:selectOneMenu  id="empresaAMD" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{RelAlteracaoDataManualControle.codigoEmpresa}" >
                                                                <f:selectItems  value="#{RelAlteracaoDataManualControle.listaEmpresas}" />
                                                            </h:selectOneMenu>
                                                            <rich:spacer width="5px"/>
                                                            <a4j:commandButton id="consultarAMD" reRender="panelAMD"
                                                                               title="Consultar contratos com altera��o manual no per�odo"
                                                                               action="#{RelAlteracaoDataManualControle.atualizar}"
                                                                               value="Atualizar" />
                                                            <h:message for="dataFimAMD" styleClass="mensagemDetalhada"/>
                                                        </h:panelGroup>

                                                        <h:panelGrid width="100%" columns="1" id="panelDataAMD" columnClasses="colunaCentralizada">
                                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" >
                                                                <tr>
                                                                    <td>
                                                                        <br/>
                                                                        <h:panelGrid width="100%" columns="3" columnClasses="colunaDireita,colunaEsquerda">
                                                                            <h:panelGroup>
                                                                                &nbsp;&nbsp;
                                                                            </h:panelGroup>
                                                                            <h:panelGroup>
                                                                                <h:outputText styleClass="tituloCamposAzulGrandeNegrito" value=" Total ="/>
                                                                            </h:panelGroup>
                                                                            
                                                                        </h:panelGrid>
                                                                        <div class="sep" style=""><img src="images/shim.gif"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </h:panelGrid>
                                                        <h:panelGrid columns="1" width="100%">
                                                            <h:outputText styleClass="textverysmall"
                                                                          value="*Para este c�lculo � considerada a data de altera��o manual da data base do contrato"
                                                                          style="text-align:left;color:#0f4c6b;"/>
                                                            <h:outputText styleClass="mensagem"  value="#{RelAlteracaoDataManualControle.mensagem}"/>
                                                            <h:outputText styleClass="mensagemDetalhada" value="#{RelAlteracaoDataManualControle.mensagemDetalhada}"/>
                                                        </h:panelGrid>

                                                    </td></tr>
                                            </table>


                                        </td>

                                        <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>
                                    </tr>
                                    <tr>
                                        <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                        <td align="left" colspan="2" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                        <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                    </tr>
                                </table>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</h:panelGrid>

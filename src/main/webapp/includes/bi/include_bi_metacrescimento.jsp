<%-- 
    Document   : include_bi_metacrescimento
    Created on : 03/02/2011, 16:38:37
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>

    <rich:modalPanel id="panelMetaCrescimento" autosized="true"
                 shadowOpacity="true" width="500" height="300" onshow="document.getElementById('formMetaCrescimento:empresaMetaPanel').focus()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Configura��o da Meta de Crescimento"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkMeta"/>
            <rich:componentControl for="panelMetaCrescimento" attachTo="hidelinkMeta" operation="hide" event="onclick">
                <a4j:support reRender="panelMeta, mensagem, panelMesangem" oncomplete="Richfaces.hideModalPanel('panelMetaCrescimento')"/>
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:include viewId="include_bi_panel_metacrescimento.jsp"/>    
</rich:modalPanel>

<h:panelGrid columns="3" width="98%" style="clear:both;" cellpadding="0" cellspacing="0">
    <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:10px;margin-bottom:5px;">
        <tr>
            <td width="100%">
                <table width="98%"  border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:10px;margin-bottom:5px;">
                    <tr>
                        <td>

                    <h:panelGrid id="panelMeta" rendered="#{LoginControle.usuarioLogado.administrador}" width="100%" cellpadding="0" cellspacing="0" columns="3" columnClasses="alinhamentoSuperior,tituloboxcentro2, tituloboxcentro2" style="margin-right:10px;margin-bottom:5px;">
                        <table width="100%" height="420" border="0" align="left" cellpadding="0" cellspacing="0">
                            <tr>
                                <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"/></td>
                                <td width="100%" align="left" colspan="7" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">
                                    BI - Meta de Crescimento
                                    <a href="${SuperControle.urlWiki}Relat�rios:BI_-_C�lculo_de_Meta_de_Crescimento"
                                       class="linkWiki"  title="Clique e saiba mais: BI - C�lculo de Meta de Crescimento" target="_blank" >
                                        <img src="imagens/wiki_link2.gif" alt="Clique e saiba mais: BI - C�lculo de Meta de Crescimento" class="linkWiki"/>
                                    </a>
                                </td>
                                <td class="tituloboxcentro2">
                            <rich:calendar  id="dataBaseMeta"
                                            value="#{MetaCrescimentoControle.dataMeta}"
                                            showInput="false"
                                            direction="bottom-left"
                                            inputSize="10"
                                            datePattern="dd/MM/yyyy"
                                            zindex="1000"
                                            showWeeksBar="false">
                                <a4j:support event="onchanged"
                                             action="#{MetaCrescimentoControle.consultarMetaPanel}"
                                             reRender="panelMeta"/>
                            </rich:calendar>
                            </td>
                            <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"/></td>
                            </tr>
                            <tr>
                                <td align="left" valign="top" background="images/box_centro_left.gif"><img src="images/shim.gif"/></td>
                                <td align="left" colspan="10" valign="top" bgcolor="#ffffff">
                                    <table width="100%" border="0"  cellspacing="0" cellpadding="0" >
                                        <tr>
                                            <td align="left" valign="top">
                                        <h:panelGrid columns="1" width="100%" style="background-color:#ffffff;">
                                            <h:panelGrid columns="2" width="100%" columnClasses="colunaEsquerda, colunaDireita">
                                                <a4j:commandLink styleClass="text" style="color:#0f4c6b;" value="Editar Meta" oncomplete="Richfaces.showModalPanel('panelMetaCrescimento')"/>
                                                <h:panelGroup  layout="block" style="text-align:right;" id="panelDataBaseMeta">
                                                    <h:outputText styleClass="textverysmall" value="M�s base: " style="color:#0f4c6b;" />
                                                    <h:outputText styleClass="textverysmall" value="#{MetaCrescimentoControle.dataMeta}"
                                                                  style="color:#0f4c6b;">
                                                        <f:convertDateTime type="date" dateStyle="short"
                                                                           locale="pt" timeZone="America/Sao_Paulo"
                                                                           pattern="MM/yyyy" />
                                                    </h:outputText>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <h:panelGroup id="panelFiltrosMeta"
                                                          style="background-color:#ffffff;"
                                                          rendered="#{MetaCrescimentoControle.usuario.administrador}">
                                                <h:outputText styleClass="titulo3" value="Empresa:" />
                                                <rich:spacer width="10px" />
                                                <h:selectOneMenu  id="empresaMeta" styleClass="form"  onblur="blurinput(this);"  
                                                                  onfocus="focusinput(this);"
                                                                  value="#{MetaCrescimentoControle.empresaVO.codigo}" >
                                                    <f:selectItems  value="#{MetaCrescimentoControle.listaSelectItemEmpresa}" />
                                                </h:selectOneMenu>
                                                <rich:spacer width="5px"/>
                                                <a4j:commandButton status="statusHora" id="consultarMeta" reRender="panelMeta"
                                                                   title="Consultar Dados Meta"
                                                                   action="#{MetaCrescimentoControle.consultarMetaPanel}"
                                                                   value="Atualizar" />
                                            </h:panelGroup>

                                            <h:panelGrid columns="1" width="100%" style="background-color:#ffffff;">
                                                <h:outputText styleClass="mensagem"  value="#{MetaCrescimentoControle.mensagem}"/>
                                                <h:outputText styleClass="mensagemDetalhada" value="#{MetaCrescimentoControle.mensagemDetalhada}"/>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                            
                                </td>
                            </tr>
                        </table>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-left:5px; margin-bottom:5px;">
                            <tr>
                                <td>
                            <h:panelGrid width="100%" columns="2" style="background-color:#ffffff;">
                                <img src="images/meta.gif" alt="" width="25" height="90" style="padding:0 0 0 0;">
                                <h:panelGrid width="100%" columns="4" columnClasses="colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">
                                    <h:outputText styleClass="tituloCamposAzulGrande" value=""/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value=""/>
                                    <h:outputText styleClass="textsmall" value="Realizados"/>
                                    <h:outputText styleClass="textsmall" value="Faltam"/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" Ativos In�cio do M�s"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.totalInicial}"/>
                                    <h:outputText value=""/>
                                    <h:outputText value=""/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" Metas - Crescimento de #{MetaCrescimentoControle.meta.IMeta}%"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.metaCrescimento}"/>
                                    <h:outputText value=""/>
                                    <h:outputText value=""/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" Total a Alcan�ar"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.totalFinalDaMeta}"/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.totalFinal}"/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.totalFinalFaltam}"/>

                                    <rich:spacer width="100"/>
                                    <rich:spacer width="40"/>
                                    <rich:spacer width="50"/>
                                    <rich:spacer width="40"/>
                                </h:panelGrid>
                            </h:panelGrid>
                            </td>
                            </tr>
                        </table>
                        <div class="sep"><img src="images/shim.gif"/></div>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-top:10px; margin-left:5px; margin-bottom:5px;">
                            <tr>
                                <td>
                            <h:panelGrid width="100%" columns="2" style="background-color:#ffffff;">
                                <img src="images/meta_retencao.gif" alt="" width="25" height="110" style="padding:0 0 0 0;">
                                <h:panelGrid width="100%" columns="4" columnClasses="colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">
                                    <rich:spacer width="100"/>
                                    <rich:spacer width="40"/>
                                    <rich:spacer width="50"/>
                                    <rich:spacer width="40"/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" Previs�o de Renova��es"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.qtdePrevistosRenovar}"/>
                                    <h:outputText value=""/>
                                    <h:outputText value=""/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" IR%"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.IRInformado}%"/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.IRRealizado}%"/>
                                    <h:outputText value=""/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" Renova��es Estimadas"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulClaroGrande" value="#{MetaCrescimentoControle.meta.totalRenovacoesEstimada}"/>
                                    <h:outputText styleClass="tituloCamposAzulComanddLinkMesVerde" value="#{MetaCrescimentoControle.meta.totalRenovacoes}"/>
                                    <h:outputText styleClass="tituloCamposAzulComanddLinkMesVermelho" rendered="#{!MetaCrescimentoControle.checkRFalta}" value="#{MetaCrescimentoControle.meta.totalRenovacoesFaltam}"/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" rendered="#{MetaCrescimentoControle.checkRFalta}" value="#{MetaCrescimentoControle.meta.totalRenovacoesFaltam}"/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" Renova��es M�s Anterior"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulClaroGrande" value="#{MetaCrescimentoControle.meta.totalRenovacoesAtrasadas}"/>
                                    <h:outputText value=""/>
                                    <h:outputText value=""/>
                                </h:panelGrid>
                            </h:panelGrid>
                            </td>
                            </tr>
                        </table>
                        <div class="sep"><img src="images/shim.gif"/></div>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-top:10px; margin-left:5px; margin-bottom:5px;">
                            <tr>
                                <td>
                            <h:panelGrid width="100%" columns="4" style="background-color:#ffffff;">
                                <img src="images/meta_vendas.gif" alt="" width="25" height="90" style="padding:0 0 0 0;">
                                <h:panelGrid width="100%" columns="4" columnClasses="colunaEsquerda, colunaDireita, colunaDireita, colunaDireita">

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" ICV%"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.ICVInformado}%"/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.ICVRealizado}%"/>
                                    <h:outputText value=""/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" Mat. e Remat. Necess�rias"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulClaroGrande" value="#{MetaCrescimentoControle.meta.totalVendasNecessarias}"
                                                  title="= N�o Renovados + Cancelados + Trancados + Total de Crescimento"/>
                                    <h:outputText styleClass="tituloCamposAzulComanddLinkMesVerde" value="#{MetaCrescimentoControle.meta.totalVendas}"/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" rendered="#{MetaCrescimentoControle.checkMFalta}" value="#{MetaCrescimentoControle.meta.totalVendasFaltam}"/>
                                    <h:outputText styleClass="tituloCamposAzulComanddLinkMesVermelho" rendered="#{!MetaCrescimentoControle.checkMFalta}" value="#{MetaCrescimentoControle.meta.totalVendasFaltam}"/>

                                    <h:panelGroup>
                                        <h:outputText styleClass="tituloCamposAzulGrande" value=" Visitas Necess�rias"/>
                                    </h:panelGroup>
                                    <h:outputText styleClass="tituloCamposAzulClaroGrande" value="#{MetaCrescimentoControle.meta.totalVisitasNecessarias}"/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.totalVisitas}"/>
                                    <h:outputText styleClass="tituloCamposAzulGrande" value="#{MetaCrescimentoControle.meta.totalVisitasFaltam}"/>

                                    <rich:spacer width="100"/>
                                    <rich:spacer width="40"/>
                                    <rich:spacer width="50"/>
                                    <rich:spacer width="40"/>
                                </h:panelGrid>
                            </h:panelGrid>
                            </td>
                            </tr>
                        </table>
                        </td>
                        <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"/></td>
                        </tr>
                        <tr>
                            <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                            <td align="left" colspan="10" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"/></td>
                            <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"/></td>
                        </tr>
                </table>
                </h:panelGrid>
            </td>
        </tr>
    </table>
</td>
</tr>
</table>
</h:panelGrid>

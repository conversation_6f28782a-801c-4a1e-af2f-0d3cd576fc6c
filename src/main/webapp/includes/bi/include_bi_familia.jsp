<%@include file="../imports.jsp" %>
<h:panelGroup layout="block"
              styleClass="container-bi" id="bi-familia" rendered="#{LoginControle.permissaoAcessoMenuVO.business}">
    <a4j:commandLink action="#{BIControle.carregarGrupoRisco}" reRender="containerFamilia"
                     onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                     status="nenhumStatus"
                       styleClass="btn-atualizar-bi"/>

    <h:panelGroup layout="block" id="containerFamilia" >
        <h:panelGroup layout="block" rendered="#{false}" styleClass="bi-unloaded">
            <h:panelGroup styleClass="bi-header" layout="block" >
                <h:panelGroup layout="block" styleClass="bi-panel" >
                    <h:outputText value="BI - Fam�lia" styleClass="bi-titulo pull-left"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" rendered="#{true}">
            <h:panelGroup styleClass="bi-header" layout="block">
                <h:panelGroup layout="block" styleClass="bi-panel">
                    <h:outputText value="BI - Fam�lia" styleClass="bi-titulo pull-left"/>

                    <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                  style="float: left;margin-top: 4.4%;margin-left: 2%"
                                  value="#{SuperControle.urlWiki}Relat�rios:BI_(Business_Intelligence)_-_Familia"
                                  title="Clique e saiba mais: Business Intelligence Fam�lia" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>

                    <a4j:commandLink id="consultarFamilia"
                                     reRender="containerFamilia"

                                     oncomplete="montarTips();"
                                     action="#{RiscoControle.atualizar}">
                        <i title="Atualizar dados de Fam�lia" class="fa-icon-refresh bi-btn-refresh bi-link pull-right tooltipster lineHeight-3em"></i>
                    </a4j:commandLink>


                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="gr-container-totalizador">
                <h:panelGroup layout="block" styleClass="gr-totalizador">
                    <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text" value="Cadastradas"/>
                    <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                     id="btnfamiliatodas"
                                     action="#{RiscoControle.mostrarListaClientesPeso6}"
                                     oncomplete="abrirPopup('riscoForm.jsp', 'Risco', 1024, 595);"
                                     value="#{RiscoControle.totalRisco6} "/>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="gr-totalizador">
                    <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text" value="Ativas"/>
                    <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                     id="btfamiliaativas"
                                     action="#{RiscoControle.mostrarListaClientesPeso7}"
                                     oncomplete="abrirPopup('riscoForm.jsp', 'Risco', 1024, 595);"
                                     value="#{RiscoControle.totalRisco7}"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="gr-totalizador">
                    <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text" value="Ativas com integrante inativo"/>
                    <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                     action="#{RiscoControle.mostrarListaClientesPeso8}"
                                     oncomplete="abrirPopup('riscoForm.jsp', 'Risco', 1024, 595);"
                                     value="#{RiscoControle.totalRisco8} "/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-panel">
                <a4j:commandLink id="verMaisFamilia"
                                 action="#{BIFamiliaControle.abrirSugestoes}"
                                 styleClass="bi-btn-refresh bi-link pull-right"
                                 reRender="mdlFamiliaSug"
                                 oncomplete="#{BIFamiliaControle.msgAlert}">
                    Temos 5 sugest�es de fam�lias para voc� &nbsp<i title="Ver sugest�es de fam�lias para voc�" class="tooltipster fa-icon-group "></i>
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

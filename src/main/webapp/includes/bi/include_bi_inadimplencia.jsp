<%@include file="../imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>
<style>
    .rich-calendar-exterior.rich-calendar-popup.dataMatricula {
        top: 37px !important;
        left: 10px !important;
    }

    .bi-separador {
        margin: 10px 0px 10px 0px;
        width: 100%;
        height: 1px;
        border-bottom: 1px solid #E5E5E5;
    }
</style>

<h:panelGroup layout="block" id="bi-inadimplencia" styleClass="container-bi"
              rendered="#{LoginControle.permissaoAcessoMenuVO.biInadimplencia}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.biInadimplencia || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.inadimplencia.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarBIInadimplencia}" reRender="bi-container-inadimplencia"
                         status="nenhumStatus"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         styleClass="btn-atualizar-bi"/>
        <h:panelGroup layout="block" id="bi-container-inadimplencia">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.inadimplencia.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block"
                              style="border-bottom: none; height: 4em !important;">
                    <h:panelGroup layout="block" styleClass="bi-panel" style="line-height: 5.2em !important;">
                        <h:outputText value="Inadimplência" styleClass="bi-titulo-mc pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup styleClass="bi-separador-mc" layout="block"/>
                <div class="ghost">
                    <table>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                    </table>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="bi-inadimplencia"
                          rendered="#{!BIControle.configuracaoBI.inadimplencia.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup styleClass="bi-header" layout="block"
                              style="border-bottom: none; height: 4em !important;">
                    <h:panelGroup layout="block" styleClass="bi-panel" style="line-height: 5.2em !important;">
                        <h:outputText id="tituloInadimplencia" value="Inadimplência"
                                      styleClass="bi-titulo-mc pull-left tooltipster"/>
                        <h:outputText styleClass="pull-left tooltipster"
                                      style="color: dodgerblue; cursor: help; font-size: 0.6vw; padding-left: 3px; font-weight: bold;"
                                      title="
<div style='text-align: left !important'>
<h3><b>Inadimplência</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li> O b.i de Inadimplência trabalha com um indicador de <b>parcelas</b> de acordo com <br>
       a sua data de vencimento. São apresentadas todas as <b>parcelas</b> existentes no sistema, <br>
       seja ela de plano, produto etc...</li><br>
      <li> Algumas operações feitas nas <b>parcelas</b> previstas do mês, pode alterar o resultado <br>
       do b.i (Cancelamentos, Estornos, Novas Vendas etc...)</li>
     </ul>
</div>"
                                      value="?">
                        </h:outputText>
                        <h:panelGroup layout="block" styleClass="pull-left">
                            <div
                                 style="margin-left:0.8em; margin-top: 0.6em !important;">
                                <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                              value="#{SuperControle.urlBaseConhecimento}o-sistema-possui-alguma-ferramenta-que-permita-visualizar-as-inadimplencias-da-minha-empresa/"
                                              title="Clique e saiba mais: Inadimplência" target="_blank">
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                            </div>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div class="divBotoesBi">
                                <a4j:commandLink id="atualizarInadimplencia"
                                                 reRender="bi-container-inadimplencia"
                                                 action="#{BIInadimplenciaControle.atualizarInadimplencia}">
                                    <i title="Atualizar Inadimplência"
                                       style="line-height: normal !important;"
                                       class="tooltipster fa-icon-refresh bi-link"></i>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div class="divBotoesBi">
                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelFiltroInadimplencia')">
                                    <i title="Filtro"
                                       style="line-height: normal !important;"
                                       class="tooltipster fa-icon-filter bi-link"></i>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div title="${BIInadimplenciaControle.dataBase_ApresentarMesDia}"
                                 class="tooltipster dateTimeCustom alignToRight divBotoesBi">
                                <rich:calendar id="dataInicioBIIna"
                                               value="#{BIInadimplenciaControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{BIInadimplenciaControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{BIInadimplenciaControle.atualizarInadimplencia}"
                                                 oncomplete="montarTips();"
                                                 reRender="bi-container-inadimplencia"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div class="divBotoesBi">
                                <a4j:commandLink
                                        id="abrirModalExcelPlanilhaAgrupadaBiInadimp"
                                        reRender="formExportaBiInaNomd"
                                        action="#{BIInadimplenciaControle.abrirModalExcelPlanilhaAgrupada}"
                                        oncomplete="#{BIInadimplenciaControle.mensagemNotificar};#{BIInadimplenciaControle.onComplete}">
                                    <i style="line-height: normal !important;"
                                       class="fa-icon-file-alt  bi-link tooltipster"
                                       title="Exportar relatório de inadimplência detalhado para excel agrupado pelo resultado da última tentativa de cobrança"></i>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup styleClass="bi-separador-mc" layout="block"/>

                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGroup styleClass="bi-row-mc" layout="block">
                        <h:outputText styleClass="bi-left-align bi-indicador-mes-referencia-mc" value="Deste Mês"/>
                        <h:outputText styleClass="bi-right-align bi-mes-referencia-mc"
                                      value="#{BIInadimplenciaControle.mesAtualFormatado}"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="container-row" style="margin-left: 0; width: 100%">
                        <h:panelGrid columns="5" styleClass="bi-mc-caixa-conteudo-panelgrid">
                            <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                             style="height: 100%;">
                                    <h:outputText value=""/>
                                    <h:outputText value=""/>
                                    <a4j:commandLink id="previsao"
                                                     styleClass="quantidade-caixa-bi tooltipster"
                                                     style="color: #29AAE2 !important;"
                                                     oncomplete="abrirPopupMaximizada('faces/relatorio/parcelaEmAbertoRel20.jsp','ParcelaEmAberto');"
                                                     actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                     value="#{MovPagamentoControle.empresaLogado.moeda} #{BIInadimplenciaControle.previsaoBi.valorApresentar}">
                                        <f:attribute name="totalizador"
                                                     value="#{BIInadimplenciaControle.previsaoBi}"/>
                                    </a4j:commandLink>
                                    <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                                  title="
<div style='text-align: left !important'>
<h3><b>Previsão</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li> São apresentadas todas as <b>parcelas</b> que vencem dentro do mês de análise mesmo estando como <br>
       (<b>Paga</b>, <b>Em aberto</b>, <b>Em Aberto Vencida</b> e <b>Canceladas</b>).</li><br>
       <li>As parcelas que foram <b>canceladas</b> após a sua data de <b>vencimento</b> não saem da previsão e <br>
          continuam sendo uma <b>inadimplência</b>.</li>
    </ul>
</div>"
                                                  value="#{BIInadimplenciaControle.previsaoBi.label}"/>
                                    <a4j:commandLink id="qtdAlunosPrevisao"
                                                     style="font-size: 0.8vw"
                                                     styleClass="linkPadrao tooltipster"
                                                     title="Quantidade de alunos que possuem <b>parcelas</b> na <b>previsão</b> do <b>mês</b>."
                                                     oncomplete="abrirPopup('./listaBIInadimplenciaAlunos.jsp', 'ListaParcelas', 800, 600);"
                                                     actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                     value="#{BIInadimplenciaControle.previsaoBi.quantidadeAlunos}">
                                        <f:attribute name="totalizadorAlunos"
                                                     value="#{BIInadimplenciaControle.previsaoBi}"/>
                                    </a4j:commandLink>
                                    <h:outputText value=""/>
                                </h:panelGrid>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="bi-separador-vertical-inad"/>

                            <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                             style="height: 100%;">
                                    <h:outputText value=""/>
                                    <h:outputText value=""/>
                                    <a4j:commandLink id="recebidos"
                                                     styleClass="quantidade-caixa-bi tooltipster"
                                                     style="color: #64AF45 !important;"
                                                     oncomplete="abrirPopupMaximizada('faces/relatorio/parcelaEmAbertoRel20.jsp','ParcelaEmAberto');"
                                                     actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                     value="#{MovPagamentoControle.empresaLogado.moeda} #{BIInadimplenciaControle.pagasBi.valorApresentar}">
                                        <f:attribute name="totalizador"
                                                     value="#{BIInadimplenciaControle.pagasBi}"/>
                                    </a4j:commandLink>
                                    <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                                  value="#{BIInadimplenciaControle.pagasBi.label}"
                                                  title="
<div style='text-align: left !important'>
<h3><b>Recebidos</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li> Das <b>parcelas</b> previstas para o mês de análise, aqui constam as que foram <b>recebidas</b>.</li><br>
      <li> Este valor necessariamente não vai bater com outros relatórios financeiros devido a sua data de <b>pagamento</b>.</li>
    </ul>
</div>"/>
                                    <a4j:commandLink id="qtdAlunosRecebidos"
                                                     style="font-size: 0.8vw"
                                                     styleClass="linkPadrao tooltipster"
                                                     title="Quantidade de alunos que possuem <b>parcelas</b> na <b>previsão</b> e que estão <b>pagas</b>. <br>
                                                 Pode ocorrer dela ter sido paga em outros meses."
                                                     oncomplete="abrirPopup('./listaBIInadimplenciaAlunos.jsp', 'ListaParcelas', 800, 600);"
                                                     actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                     value="#{BIInadimplenciaControle.pagasBi.quantidadeAlunos}">
                                        <f:attribute name="totalizadorAlunos"
                                                     value="#{BIInadimplenciaControle.pagasBi}"/>
                                    </a4j:commandLink>
                                    <h:outputText value=""/>
                                </h:panelGrid>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="bi-separador-vertical-inad"/>

                            <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                             style="height: 100%;">
                                    <h:outputText value=""/>
                                    <h:outputText value=""/>
                                    <a4j:commandLink id="inadimplencia"
                                                     styleClass="quantidade-caixa-bi tooltipster"
                                                     style="color: #F15858 !important;"
                                                     oncomplete="abrirPopupMaximizada('faces/relatorio/parcelaEmAbertoRel20.jsp','ParcelaEmAberto');"
                                                     actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                     value="#{MovPagamentoControle.empresaLogado.moeda} #{BIInadimplenciaControle.inadimplenciaBi.valorApresentar}">
                                        <f:attribute name="totalizador"
                                                     value="#{BIInadimplenciaControle.inadimplenciaBi}"/>
                                    </a4j:commandLink>
                                    <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                                  value="#{BIInadimplenciaControle.inadimplenciaBi.label}"
                                                  title="
<div style='text-align: left !important'>
<h3><b>Inadimplência</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li> Das <b>parcelas</b> previstas para o mês de análise, aqui constam as que não foram <b>recebidas</b> e estão <b>em aberto</b>.</li><br>
      <li>As <b>parcelas</b> que estavam <b>em aberto</b> e foram <b>canceladas</b> após a sua data de <b>vencimento</b>, continuaram sendo uma <b>inadimplência</b>.</li>
    </ul>
</div>"/>
                                    <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                                  style="font-size: 0.8vw"
                                                  title="Percentual da <b>Inadimplência</b>."
                                                  value="#{BIInadimplenciaControle.inadimplenciaBi.porcentagem_Apresentar} %"/>
                                    <h:outputText value=""/>
                                </h:panelGrid>
                            </h:panelGroup>

                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGroup>
                <style>

                    .btn-graphic-toggle-view {
                        color: #29ABE2;
                    }

                </style>
                <h:panelGroup styleClass="bi-separador-mc" layout="block" style="margin-top: 2vw;"/>

                <h:panelGroup layout="block" styleClass="bi-totalizador-header"
                              style="text-align: left; background-color: inherit! important; margin-left: 4.5%; padding-bottom: 1vw; padding-top: 1vw; width: 91%;">

                    <h:outputText styleClass="bi-left-align bi-indicador-mes-referencia-mc" value="Últimos 4 Meses"
                                  style="float: left !important;"/>

                    <h:panelGroup layout="block" style="display: inline-block; float: right;"
                                  styleClass="col-text-align-right">

                        <%--BOTÃO SELECIONAR LISTA--%>
                        <a4j:commandLink
                                id="btnSelecionarLista"
                                status="false"
                                styleClass="linkPadrao btnSelecionarLista padding-5"
                                style="font-size: 22px !important; color: #51555A; display: none;"
                                onclick="modoTabelaBIInadimplencia()">
                            <h:outputText styleClass="fa-icon-list-alt linkPadrao"/>
                        </a4j:commandLink>

                        <%--ICONE DE LISTA SELECIONADO--%>
                        <h:outputText styleClass="fa-icon-list-alt linkPadrao btn-graphic-toggle-view padding-5"
                                      style="font-size: 22px !important;"
                                      id="iconeListaSelecionado"/>

                        <%--ICONE DE GRAFICO SELECIONADO--%>
                        <h:outputText
                                styleClass="fa-icon-bar-chart linkPadrao btn-graphic-toggle-view iconeGraficoSelecionado padding-5"
                                style="font-size: 22px !important; margin-left: 5px; display: none;"
                                id="iconeGraficoSelecionado"/>

                        <%--BOTÃO SELECIONAR GRAFICO--%>
                        <a4j:commandLink
                                id="btnSelecionarGrafico"
                                status="false"
                                style="font-size: 22px !important; margin-left: 5px; color: #51555A;"
                                styleClass="linkPadrao btnSelecionarGrafico padding-5"
                                onclick="modoGraficoBIInadimplencia()">
                            <h:outputText styleClass="fa-icon-bar-chart linkPadrao"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>


                <h:panelGroup layout="block"
                              styleClass="container-row"
                              id="panelModoListaInadimplencia"
                              style="padding-bottom: 3%;">
                    <h:panelGroup layout="block" id="panelGeralTabelaInadimplencia"
                                  style="display: inline-flex; width: 100%; overflow-x: auto;">

                        <h:panelGroup layout="block" id="testeTabIna11"
                                      style="width: 100%; text-align: left"
                                      styleClass="texto-size-14 bi-font-family">

                            <h:panelGroup layout="block"
                                          style="white-space: nowrap; padding: 0px 0px 20px 0px; color: black; font-size: 0.8vw !important;">
                                <h:outputText value="Mês Referência"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block"
                                          style="white-space: nowrap; color: #9D9D9D;">
                                <h:outputText value="Previsão (#{MovPagamentoControle.empresaLogado.moeda})"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="padding-top: 0.4vw;">
                                <h:outputText value="&nbsp;" escape="false"/>
                            </h:panelGroup>
                            <h:panelGroup styleClass="bi-separador" layout="block"/>
                            <h:panelGroup layout="block"
                                          style="white-space: nowrap; color: #9D9D9D;">
                                <h:outputText value="Recebido (#{MovPagamentoControle.empresaLogado.moeda})"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="padding-top: 0.4vw;">
                                <h:outputText value="&nbsp;" escape="false"/>
                            </h:panelGroup>
                            <h:panelGroup styleClass="bi-separador" layout="block"/>
                            <h:panelGroup layout="block"
                                          style="white-space: nowrap; color: #FF5555; padding: 5px 0px 5px 5px; background-color: #F4C9D1">
                                <h:outputText value="Inadimplência (#{MovPagamentoControle.empresaLogado.moeda})"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="padding-top: 0.4vw;">
                                <h:outputText value="&nbsp;" escape="false"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="padding-top: 0.4vw;">
                                <h:outputText value="&nbsp;" escape="false"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <a4j:repeat value="#{BIInadimplenciaControle.totalizador}" var="tab">
                            <h:panelGroup layout="block" id="testeTabIna1"
                                          style="width: 100%; text-align: right;"
                                          styleClass="texto-size-14 bi-font-family">

                                <h:panelGroup layout="block" id="mesInad"
                                              style="white-space: nowrap; padding: 0px 0px 20px 0px; color: black; font-size: 0.8vw !important;">
                                    <h:outputText value="#{tab.previsao.mesReferencia_Apresentar}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block" id="valorTotalInad"
                                              style="white-space: nowrap; color: #9D9D9D;">
                                    <h:outputText
                                            value="#{tab.previsao.valorApresentar}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block" id="qtdeAlunosPrevisao"
                                              style="white-space: nowrap; color: #9D9D9D; padding-top: 0.4vw;">
                                    <a4j:commandLink id="qtdAlunosPrevisaoTotalMes"
                                                     style="font-size:inherit"
                                                     styleClass="linkPadrao tooltipster"
                                                     title="Quantidade de alunos que possuem <b>parcelas</b> na <b>previsão</b> do <b>mês</b>."
                                                     rendered="#{!tab.media}"
                                                     oncomplete="abrirPopup('./listaBIInadimplenciaAlunos.jsp', 'ListaParcelas', 800, 600);"
                                                     actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                     value="#{tab.previsao.quantidadeAlunos}">
                                        <f:attribute name="totalizadorAlunos"
                                                     value="#{tab.previsao}"/>
                                    </a4j:commandLink>
                                    <h:outputText value="#{tab.previsao.quantidadeAlunos}" rendered="#{tab.media}"/>
                                </h:panelGroup>
                                <h:panelGroup styleClass="bi-separador" layout="block"/>
                                <h:panelGroup layout="block" id="valorPagoInad"
                                              style="white-space: nowrap; color: #9D9D9D;">
                                    <h:outputText
                                            value="#{tab.pagas.valorApresentar}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block" id="qtdeAlunosPago"
                                              style="white-space: nowrap; color: #9D9D9D; padding-top: 0.4vw;">
                                    <a4j:commandLink id="qtdAlunosPagasTotalMes"
                                                     style="font-size:inherit"
                                                     styleClass="linkPadrao tooltipster"
                                                     title="Quantidade de alunos que possuem <b>parcelas</b> na <b>previsão</b> e que estão <b>pagas</b>. <br>
                                                 Pode ocorrer dela ter sido paga em outros meses."
                                                     rendered="#{!tab.media}"
                                                     oncomplete="abrirPopup('./listaBIInadimplenciaAlunos.jsp', 'ListaParcelas', 800, 600);"
                                                     actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                     value="#{tab.pagas.quantidadeAlunos}">
                                        <f:attribute name="totalizadorAlunos"
                                                     value="#{tab.pagas}"/>
                                    </a4j:commandLink>
                                    <h:outputText value="#{tab.pagas.quantidadeAlunos}" rendered="#{tab.media}"/>
                                </h:panelGroup>
                                <h:panelGroup styleClass="bi-separador" layout="block"/>
                                <h:panelGroup layout="block" id="valorEmAbertoInad"
                                              style="white-space: nowrap; color: #FF5555; padding: 5px 0px 5px 5px; background-color: #F4C9D1">
                                    <h:outputText
                                            value="#{tab.inadimplencia.valorApresentar}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block" id="qtdeAlunosInadimplencia1"
                                              style="white-space: nowrap; color: #9D9D9D; padding-top: 0.4vw;">
                                    <a4j:commandLink id="qtdAlunosInadimplenciaTotalMes"
                                                     style="font-size:inherit"
                                                     styleClass="linkPadrao tooltipster"
                                                     title="Quantidade de alunos que possuem <b>parcelas</b> na <b>previsão</b> e que ainda estão <b>em aberto</b>."
                                                     rendered="#{!tab.media}"
                                                     oncomplete="abrirPopup('./listaBIInadimplenciaAlunos.jsp', 'ListaParcelas', 800, 600);"
                                                     actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                     value="#{tab.inadimplencia.quantidadeAlunos}">
                                        <f:attribute name="totalizadorAlunos"
                                                     value="#{tab.inadimplencia}"/>
                                    </a4j:commandLink>
                                    <h:outputText value="#{tab.inadimplencia.quantidadeAlunos}"
                                                  rendered="#{tab.media}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block" id="inadimplenciaInad"
                                              style="white-space: nowrap; color: #9D9D9D; padding-top: 0.4vw;">
                                    <h:outputText title="Percentual da <b>Inadimplência</b>."
                                                  value="#{tab.inadimplencia.porcentagem_Apresentar}%"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </a4j:repeat>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              id="panelGraficoBIInadimplencia"
                              styleClass="container-row"
                              style="display: block; display: none;">
                    <script type="text/javascript">
                        function carregarGraficoBIInadimplencia() {
                            chartInadimplencia = AmCharts.makeChart("chartBIInadimplencia", {
                                "type": "serial",
                                "theme": "light",
                                "dataProvider": ${BIInadimplenciaControle.dadosInadimplenciaJSON},
                                "valueAxes": [{
                                    "id": "valorTotalAxis",
                                    "axisAlpha": 0,
                                    "gridAlpha": 0.1,
                                    "dashLength": 5,
                                    "precision": 0,
                                    "position": "right",
                                    "title": "Valor (${MovPagamentoControle.empresaLogado.moeda})"
                                },
                                    {
                                        "id": "inadimplenciaAxis",
                                        "axisAlpha": 0,
                                        "gridAlpha": 0.1,
                                        "dashLength": 5,
                                        "precision": 0,
                                        "position": "left",
                                        "maximum": 100,
                                        "title": "Eficiência / Inadimplência %"
                                    }],
                                "startDuration": 1,
                                "legend": {
                                    "autoMargins": false,
                                    "maxColumns": 3,
                                    "align": "left",
                                    "equalWidths": true,
                                    "useGraphSettings": false,
                                    "valueAlign": "center",
                                    "valueWidth": 0
                                },
                                "numberFormatter": {
                                    "precision": 2,
                                    "decimalSeparator": ",",
                                    "thousandsSeparator": "."
                                },
                                "graphs": ${BIInadimplenciaControle.legendaGrafico},
                                "chartCursor": {
                                    "categoryBalloonEnabled": false,
                                    "cursorAlpha": 0,
                                    "zoomable": false
                                },
                                "categoryField": "mes",
                                "categoryAxis": {
                                    "gridAlpha": 0,
                                    "axisAlpha": 0,
                                    "position": "top",
                                    "tickLength": 0,
                                    "labelRotation": 0
                                },
                                "export": {
                                    "enabled": true
                                }

                            });
                        }


                    </script>
                    <div id="chartBIInadimplencia" style="height: 320px; padding-bottom: 10px;"></div>
                    <script>
                        carregarGraficoBIInadimplencia();
                    </script>
                </h:panelGroup>

                <h:panelGroup id="totalizador4Meses" layout="block" styleClass="container-row"
                              style="margin-left: 0; width: 100%; padding-bottom: 1vw;">
                    <h:panelGrid columns="5" styleClass="bi-mc-caixa-conteudo-panelgrid">

                        <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                         style="height: 100%;">
                                <h:outputText value=""/>
                                <h:outputText value=""/>
                                <a4j:commandLink id="totalPrevisao"
                                                 styleClass="quantidade-caixa-bi tooltipster"
                                                 style="color: #29AAE2 !important;"
                                                 oncomplete="abrirPopupMaximizada('faces/relatorio/parcelaEmAbertoRel20.jsp','ParcelaEmAberto');"
                                                 actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                 value="#{MovPagamentoControle.empresaLogado.moeda} #{BIInadimplenciaControle.previsaoTotalBi.valorApresentar}">
                                    <f:attribute name="totalizador"
                                                 value="#{BIInadimplenciaControle.previsaoTotalBi}"/>
                                </a4j:commandLink>
                                <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                              value="Previsão"
                                              title="
<div style='text-align: left !important'>
<h3><b>Previsão</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li>Essa é a previsão total dos últimos 4 meses, são apresentadas todas as <b>parcelas</b> que <br>
          vencem dentro do período visualizado no B.i mesmo estando como (<b>Paga</b>, <b>Em aberto</b>, <b>Em Aberto Vencida</b> e <b>Canceladas</b>).</li><br>
      <li>As <b>parcelas</b> que foram <b>canceladas</b> após a sua data de <b>vencimento</b> não saem da previsão e <br>
          continuam sendo uma <b>inadimplência</b>.</li>
    </ul>
</div>"/>
                                <a4j:commandLink id="qtdAlunosPrevisaoTotal"
                                                 style="font-size: 0.8vw"
                                                 styleClass="linkPadrao tooltipster"
                                                 title="Total de alunos que possuem parcelas na previsão dos últimos 4 meses."
                                                 oncomplete="abrirPopup('./listaBIInadimplenciaAlunos.jsp', 'ListaParcelas', 800, 600);"
                                                 actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                 value="#{BIInadimplenciaControle.previsaoTotalBi.quantidadeAlunos}">
                                    <f:attribute name="totalizadorAlunos"
                                                 value="#{BIInadimplenciaControle.previsaoTotalBi}"/>
                                </a4j:commandLink>
                                <h:outputText value=""/>
                            </h:panelGrid>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="bi-separador-vertical-inad"/>

                        <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                         style="height: 100%;">
                                <h:outputText value=""/>
                                <h:outputText value=""/>
                                <a4j:commandLink id="totalPagas"
                                                 styleClass="quantidade-caixa-bi tooltipster"
                                                 style="color: #64AF45 !important;"
                                                 oncomplete="abrirPopupMaximizada('faces/relatorio/parcelaEmAbertoRel20.jsp','ParcelaEmAberto');"
                                                 actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                 value="#{MovPagamentoControle.empresaLogado.moeda} #{BIInadimplenciaControle.pagasTotalBi.valorApresentar}">
                                    <f:attribute name="totalizador"
                                                 value="#{BIInadimplenciaControle.pagasTotalBi}"/>
                                </a4j:commandLink>
                                <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                              value="Recebidos"
                                              title="
<div style='text-align: left !important'>
<h3><b>Recebidos</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li> Das <b>parcelas</b> previstas dos últimos 4 meses, aqui constam as que foram <b>recebidas</b>.</li><br>
      <li> Este valor necessariamente não vai bater com outros relatórios financeiros devido a sua data de <b>pagamento</b>.</li>
    </ul>
</div>"/>
                                <a4j:commandLink id="qtdAlunosPagasTotal"
                                                 style="font-size: 0.8vw"
                                                 styleClass="linkPadrao tooltipster"
                                                 title="Total de alunos que possuem parcelas <b>Pagas</b> da <b>previsão</b> dos últimos 4 meses. <br>Pode ocorrer dela ter sido paga em outros meses."
                                                 oncomplete="abrirPopup('./listaBIInadimplenciaAlunos.jsp', 'ListaParcelas', 800, 600);"
                                                 actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                 value="#{BIInadimplenciaControle.pagasTotalBi.quantidadeAlunos}">
                                    <f:attribute name="totalizadorAlunos"
                                                 value="#{BIInadimplenciaControle.pagasTotalBi}"/>
                                </a4j:commandLink>
                                <h:outputText value=""/>
                            </h:panelGrid>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="bi-separador-vertical-inad"/>

                        <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                         style="height: 100%;">
                                <h:outputText value=""/>
                                <h:outputText value=""/>
                                <a4j:commandLink id="totalInadimplencia"
                                                 styleClass="quantidade-caixa-bi tooltipster"
                                                 style="color: #F15858 !important;"
                                                 oncomplete="abrirPopupMaximizada('faces/relatorio/parcelaEmAbertoRel20.jsp','ParcelaEmAberto');"
                                                 actionListener="#{BIInadimplenciaControle.abrirListaParcelas}"
                                                 value="#{MovPagamentoControle.empresaLogado.moeda} #{BIInadimplenciaControle.inadimplenciaTotalBi.valorApresentar}">
                                    <f:attribute name="totalizador"
                                                 value="#{BIInadimplenciaControle.inadimplenciaTotalBi}"/>
                                </a4j:commandLink>
                                <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                              value="Inadimplência"
                                              title="
<div style='text-align: left !important'>
<h3><b>Inadimplência</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li> Das <b>parcelas</b> previstas dos últimos 4 meses, aqui constam as que não foram <b>recebidas</b> e estão <b>em aberto</b>.</li><br>
      <li>As <b>parcelas</b> que estavam <b>em aberto</b> e foram <b>canceladas</b> após a sua data de <b>vencimento</b>, continuaram sendo uma <b>inadimplência</b>.</li>
    </ul>
</div>"/>
                                <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                              style="font-size: 0.8vw"
                                              title="Percentual da <b>Inadimplência</b> dos últimos 4 meses."
                                              value="#{BIInadimplenciaControle.inadimplenciaTotalBi.porcentagem_Apresentar} %"/>
                                <h:outputText value=""/>
                            </h:panelGrid>
                        </h:panelGroup>

                    </h:panelGrid>

                </h:panelGroup>

            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>

<script>
    function modoTabelaBIInadimplencia() {
        document.getElementById('form:panelModoListaInadimplencia').style.display = '';
        document.getElementById('form:panelGraficoBIInadimplencia').style.display = 'none';
        document.getElementById('form:iconeGraficoSelecionado').style.display = 'none';
        document.getElementById('form:btnSelecionarLista').style.display = 'none';
        document.getElementById('form:btnSelecionarGrafico').style.display = '';
        document.getElementById('form:iconeListaSelecionado').style.display = '';

    }

    function modoGraficoBIInadimplencia() {
        document.getElementById('form:panelModoListaInadimplencia').style.display = 'none';
        document.getElementById('form:panelGraficoBIInadimplencia').style.display = '';
        document.getElementById('form:iconeGraficoSelecionado').style.display = '';
        document.getElementById('form:btnSelecionarLista').style.display = '';
        document.getElementById('form:btnSelecionarGrafico').style.display = 'none';
        document.getElementById('form:iconeListaSelecionado').style.display = 'none';
    }
</script>

<%@page contentType="text/html;charset=UTF-8" %>
<head>
  <script type="text/javascript" language="javascript" src="../../script/script.js"></script>
</head>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="../../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="../../beta/js/jquery.js" type="text/javascript"></script>
<script src="../../beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="../../beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="../../beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
  jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<f:view>
  <title>Life Time</title>

  <%-- INICIO HEADER --%>
  <h:panelGroup layout="block" styleClass="pure-g-r">
    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="Life Time"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}o-que-e-o-ltv-e-como-realizar-o-calculo/"/>
    <h:panelGroup layout="block" styleClass="pure-g-r">
      <f:facet name="header">
        <jsp:include page="../../topoReduzido_material.jsp"/>
      </f:facet>
    </h:panelGroup>
  </h:panelGroup>
  <%-- FIM HEADER --%>

  <h:panelGroup layout="block" styleClass="pure-g-r">
    <h:form id="form" styleClass="pure-form pure-u-1">
      <a4j:keepAlive beanName="ExportadorListaControle"/>
      <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

      <%-- INICIO CONTENT --%>
      <h:panelGroup>

        <%-- INICIO COMANDOS --%>
        <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto margin-v-10 titulo-topo">
          <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
          <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1-3 text-right">
            <h:panelGroup layout="block" styleClass="controles">
              <a4j:commandLink id="btnExcel"
                               styleClass="linkPadrao"
                               actionListener="#{LtvControle.exportarListas}"
                               oncomplete="#{LtvControle.mensagemNotificar}#{LtvControle.msgAlert}"
                               accesskey="3">
                <f:attribute name="tipo" value="xls"/>
                <f:attribute name="atributos"
                             value="matriculaCliente=Matricula,nomeApresentar=Nome,codigoContrato=Contrato,planoApresentar=Plano,
                                         duracao=Duração,lifeTime=Life Time(meses),lifeTimeValue= Life Time Value,
                                         telefonesCliente=Telefone,emailCliente=Email"/>
                <f:attribute name="prefixo" value="LifeTime"/>
                <f:attribute name="titulo" value="Life Time"/>
                <f:attribute name="itemExportacao" value="biLtvLT"/>
                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
              </a4j:commandLink>

              <a4j:commandLink id="btnPDF"
                               styleClass="linkPadrao"
                               style="margin-left: 8px"
                               actionListener="#{LtvControle.exportarListas}"
                               oncomplete="#{LtvControle.mensagemNotificar}#{LtvControle.msgAlert}"
                               accesskey="4">
                <f:attribute name="tipo" value="pdf"/>
                <f:attribute name="atributos"
                             value="matriculaCliente=Matricula,nomeApresentar=Nome,codigoContrato=Contrato,planoApresentar=Plano,
                                         duracao=Duração,lifeTime=Life Time(meses),lifeTimeValue=Life Time Value,
                                         telefonesCliente=Telefone,emailCliente=Email"/>
                <f:attribute name="prefixo" value="Lifetime"/>
                <f:attribute name="itemExportacao" value="biLtvLT"/>
                <f:attribute name="titulo" value="Life"/>
                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
              </a4j:commandLink>
            </h:panelGroup>
          </h:panelGroup>
        </h:panelGroup>
        <%-- FIM COMANDOS --%>

        <table id="tblLifeTime" class="tabelaLifeTime pure-g-r pure-u-11-12 margin-0-auto">
          <thead>
          <tr>
            <th>Matrícula</th>
            <th>Nome</th>
            <th>Contrato</th>
            <th>Plano</th>
            <th>Duração</th>
            <th>Life Time(meses)</th>
            <th>Life Time Value</th>
            <th>Telefone</th>
            <th>Email</th>
          </tr>
          </thead>
          <tbody></tbody>
        </table>

        <a4j:jsFunction name="jsEditar" action="#{LtvControle.irParaTelaClienteDatatables}"
                        oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700)"
                        reRender="mensagem"/>

      </h:panelGroup>
    </h:form>

    <jsp:include page="../include_carregando_ripple.jsp" flush="true"/>
  </h:panelGroup>

  <script src="../../beta/js/ext-funcs.js" type="text/javascript"></script>

  <script>
    jQuery(window).on("load", function () {
      iniciarTabela("tabelaLifeTime", "${contexto}/prest/ltv/lifetime", 1, "asc", "", true);
    });
  </script>
</f:view>

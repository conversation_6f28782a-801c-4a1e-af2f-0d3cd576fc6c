
<%@include file="../imports.jsp" %>
<h:panelGroup id="biTicket" layout="block"
              styleClass="container-bi" rendered="#{LoginControle.permissaoAcessoMenuVO.ticketMedio}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.ticketMedio || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.ticketMedio.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarTicketMedio}" reRender="containerTicketMedio"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         status="nenhumStatus"
                         styleClass="btn-atualizar-bi"/>

        <h:panelGroup layout="block" id="containerTicketMedio">
            <h:panelGroup layout="block"
                          rendered="#{BIControle.configuracaoBI.ticketMedio.apresentarBoxCarregar && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Ticket M�dio de Planos" styleClass="bi-titulo pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="ghost">
                    <div class="loading" style="height: 50px; margin-bottom: 30px;"></div>
                    <div class="loading" style="height: 100px; margin-bottom: 10px;"></div>
                </div>

            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.ticketMedio.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Ticket M�dio de Planos" styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                      style="float: left;margin-top: 4.4%;margin-left: 2%"
                                      value="#{SuperControle.urlBaseConhecimento}bi-ticket-medio-de-planos-adm/"
                                      title="Clique e saiba mais: BI - Ticket M�dio" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarTicket"
                                         style="margin-left: 7px"
                                         reRender="containerTicketMedio"
                                         oncomplete="carregarGraficoTM();montarTips();"
                                         action="#{BITicketMedioControle.atualizarData}">
                            <i title="Consultar dados do Ticket M�dio"
                               class="fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em tooltipster"></i>
                        </a4j:commandLink>

                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelFiltroTM')"
                                         reRender="formPanelFiltroTM">
                            <i class="fa-icon-filter bi-btn-refresh bi-link pull-right lineHeight-3em tooltipster"
                               title="Filtrar" id="filtroTicketMedio"></i>
                        </a4j:commandLink>

                        <h:panelGroup layout="block" styleClass="col-text-align-right pull-right calendarSemInputBI">
                            <div title="${BITicketMedioControle.dataBase_ApresentarMesDia}"
                                 style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                                 class="tooltipster dateTimeCustom alignToRight">
                                <rich:calendar id="dataInicioTicketMedio"
                                               value="#{BITicketMedioControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{BITicketMedioControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged" action="#{BITicketMedioControle.atualizarData}"
                                                 reRender="containerTicketMedio"
                                                 oncomplete="carregarGraficoTM();montarTips();"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                        <%--                    <h:panelGroup layout="block"--%>
                        <%--                                  styleClass="pull-right calendarSemInputBI" style="margin-right: 10px;">--%>
                        <%--                        Este ainda n�o filtra por colaborador--%>
                        <%--                        --%>
                        <%--                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')" action="#{BIControle.biAtualizarParam}"  reRender="colCheck, grupoMarcadoCss, tituloFiltro">--%>
                        <%--                            <i title="Filtrar Por Colaborador" class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">--%>
                        <%--                                <span class="badgeItem3Icon" data-bagde="${BIControle.qtdColTicketMed}"></span>--%>
                        <%--                            </i>--%>
                        <%--                            <a4j:actionparam name="biAtualizar" value="TICKET_MEDIO"></a4j:actionparam>--%>
                        <%--                            <a4j:actionparam name="biAtualizarAbrirConsulta" value="biAtualizarAbrirConsulta"></a4j:actionparam>--%>
                        <%--                            <a4j:actionparam name="reRenderBi" value="containerTicketMedio"></a4j:actionparam>--%>
                        <%--                        </a4j:commandLink>--%>
                        <%--                    </h:panelGroup>--%>

                    </h:panelGroup>

                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGrid columns="2" width="100%" styleClass="panel-grid-icv"
                                 style="border-spacing: 0; margin-bottom: 1.0em;"
                                 columnClasses="col-text-align-left,col-text-align-right">

                        <h:outputText rendered="#{BITicketMedioControle.mediaAtivos}"
                                      value="M�dia de ativos + Vencidos em #{BITicketMedioControle.mesSelecionado}"
                                      styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>


                        <h:outputText rendered="#{BITicketMedioControle.mediaAtivos}"
                                      styleClass="tooltipster bi-cor-cinza texto-size-16 bi-font-family"
                                      value="#{BITicketMedioControle.ticket.mediaAtivosVencidos}"
                                      title="<div class='esquerda'><span>Ativos + Vencidos no in�cio do m�s : #{BITicketMedioControle.ticket.ativosVencidosInicioMes}</span></br>
                                   <span>No fim do m�s: #{BITicketMedioControle.ticket.ativosVencidosFimMes}</span></div>"
                                      id="mediaAtivos">
                        </h:outputText>


                        <h:outputText rendered="#{!BITicketMedioControle.mediaAtivos}"
                                      styleClass="texto-cor-cinza texto-size-16 bi-font-family"
                                      value="Ativos em #{BITicketMedioControle.mesSelecionado}"/>

                        <h:outputText rendered="#{!BITicketMedioControle.mediaAtivos}"
                                      styleClass="texto-cor-cinza texto-size-16 bi-font-family"
                                      value="#{BITicketMedioControle.ticket.ativos}">
                        </h:outputText>

                        <h:outputText rendered="#{!BITicketMedioControle.incluirBolsas}"
                                      value="Bolsas no per�odo" styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>

                        <h:outputText rendered="#{!BITicketMedioControle.incluirBolsas}"
                                      value="#{BITicketMedioControle.ticket.bolsas}" id="bolsasMes"
                                      title="<div class='esquerda'><span>Contratos com bolsa no m�s de #{BITicketMedioControle.mesSelecionado}</span></div>"
                                      styleClass="tooltipster texto-cor-cinza texto-size-16 bi-font-family"/>

                        <h:outputText rendered="#{BITicketMedioControle.considerarDependentesComoPagantes}"
                                      value="Dependentes no per�odo" styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>

                        <h:outputText rendered="#{BITicketMedioControle.considerarDependentesComoPagantes}"
                                      value="#{BITicketMedioControle.ticket.dependentesFimMes}"
                                      styleClass="tooltipster  texto-cor-cinza texto-size-16 bi-font-family"
                                      id="dependentes" title="Dependentes no fim do m�s de #{BITicketMedioControle.mesSelecionado}"/>

                        <h:outputText rendered="#{!BITicketMedioControle.incluirBolsas}"
                                      value="M�dia de Pagantes" styleClass="bi-cor-cinza texto-size-16 bi-font-family"/>

                        <h:outputText rendered="#{!BITicketMedioControle.incluirBolsas}"
                                      value="#{BITicketMedioControle.ticket.pagantes}"
                                      styleClass="tooltipster  texto-cor-cinza texto-size-16 bi-font-family"
                                      id="pagantes" title="(M�dia de Ativos + Vencidos) - Bolsas"/>

                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="bi-separador" style="margin: 0"/>

                <h:panelGroup layout="block" styleClass="gr-container-totalizador"
                              style=" border-bottom:none;padding-top: 20px">
                    <h:panelGroup layout="block" styleClass="gr-totalizador" style="height: auto;vertical-align: top;">
                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-14">
                            <h:outputText style="display: block;" styleClass="bi-font-family texto-size-14 bi-cor-cinza"
                                          value="Caixa por Compet�ncia Mensal (planos)"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-14-middle">
                            <h:outputText value="#{BITicketMedioControle.ticket.caixaCompetencia}"
                                          title="<div class='esquerda'>Compet�ncias atualizadas at� #{BITicketMedioControle.ticket.ultimaAtualizacaoApresentar}<br/>A compet�ncia deste BI equivale ao Relat�rio de Compet�ncia Mensal, marcando apenas a op��o 'M�s de Refer�ncia Plano'.</div>"
                                          styleClass="tooltipster bi-font-family texto-size-14 texto-cor-verde">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-16">
                            <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text"
                                          value="Ticket M�dio"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40">
                            <h:outputText style="color:#2BAF50;" value="#{BITicketMedioControle.empresaLogado.moeda}"/>
                            <h:outputText value=" #{BITicketMedioControle.ticket.ticketCompetenciaFormatado}"
                                          styleClass="bi-cor-cinza bi-font-family"
                                          style="font-size: 2vw !important;">
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGroup layout="block" styleClass="gr-totalizador" style="height: auto;vertical-align: top;">

                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-14">
                            <h:outputText styleClass="bi-font-family texto-size-14 bi-cor-cinza"
                                          value="Caixa por Receita (total)"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-14-middle">
                            <h:outputText value="#{BITicketMedioControle.ticket.caixaReceita}"
                                          title="<div class='esquerda'>Receitas atualizadas at� #{BITicketMedioControle.ticket.ultimaAtualizacaoApresentar}
                                          <br/>A receita deste BI equivale ao #{BITicketMedioControle.labelFonteDadosFinanceiros} por receita\despesa com todas as fontes de dados.</div>"
                                          styleClass="tooltipster bi-font-family texto-size-14 texto-cor-verde">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-16">
                            <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text"
                                          value="Ticket M�dio"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40">
                            <h:outputText style="color:#2BAF50;" value="#{BITicketMedioControle.empresaLogado.moeda}"/>
                            <h:outputText value=" #{BITicketMedioControle.ticket.ticketReceitaFormatado}"
                                          styleClass="bi-cor-cinza bi-font-family"
                                          style="font-size: 2vw !important;">
                            </h:outputText>
                        </h:panelGroup>

                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="gr-totalizador" style="height: auto;vertical-align: top;">
                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-14">
                            <h:outputText
                                    style="display: block;text-align: center;margin-right: 14px;;margin-left: 14px;padding-bottom: 2px;"
                                    styleClass="bi-font-family texto-size-14 bi-cor-cinza"
                                    value="Despesa do m�s (total)"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-14-middle">
                            <h:outputText style="display: block;padding-bottom: 0px;"
                                          value="#{BITicketMedioControle.ticket.despesaTotalMes}" id="despesaPorAluno"
                                          styleClass="tooltipster bi-font-family texto-size-14 texto-cor-verde"
                                          title="<div class='esquerda'>Despesas atualizadas at� #{BITicketMedioControle.ticket.ultimaAtualizacaoApresentar}
                                          <br/>A despesa deste BI  equivale ao total das sa�das do #{BITicketMedioControle.labelFonteDadosFinanceiros} por receita\despesa com todas as fontes de dados.</div>">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-16">
                            <h:outputText styleClass="tooltipster bi-font-family bi-table-text"
                                          value="Despesa por contrato" id="ticketMedioDespesaLabel"
                                          title="<div class='esquerda' style='width: 250px'>
                                    <div class='display:block'><span class='text' style='font-weight: bold;'>Legenda da despesa por aluno: </span></div>
                                    <div class='display:block'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#002C74;display:inline-block;'></div><span class='text'>At� 80% da receita</span></div>
                                    <div class='display:block'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#FF6E00;display:inline-block;'></div><span class='text'>Entre 80 e 100% da receita</span></div>
                                    <div class='display:block'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#FF322C;display:inline-block;'></div><span class='text'>Despesa maior do que receita</span></div>
                                  </div>"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40">
                            <h:outputText style="color:#2BAF50;" value="#{BITicketMedioControle.empresaLogado.moeda}"/>
                            <h:outputText
                                    style="font-size: 2vw !important;padding-bottom: 1px;color:#{BITicketMedioControle.ticket.corDespesa}"
                                    value=" #{BITicketMedioControle.ticket.despesaPorAlunoFormatado}"
                                    title="<div class='esquerda' style='width: 250px'>
                                    <div class='display:block'><span class='text' style='font-weight: bold;'>Legenda da despesa por aluno: </span></div>
                                    <div class='display:block'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#002C74;display:inline-block;'></div><span class='text'>At� 80% da receita</span></div>
                                    <div class='display:block'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#FF6E00;display:inline-block;'></div><span class='text'>Entre 80 e 100% da receita</span></div>
                                    <div class='display:block'><div style='margin-right:3px;border-radius:2px;border:none; width: 8px; height: 8px; background-color:#FF322C;display:inline-block;'></div><span class='text'>Despesa maior do que receita</span></div>
                                  </div>" styleClass="tooltipster bi-cor-azul bi-font-family">
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <script type="text/javascript">
                    function carregarGraficoTM() {
                        chart = AmCharts.makeChart("chartdiv", {
                            "type": "serial",
                            "theme": "light",
                            "dataProvider": ${BITicketMedioControle.dadosGrafico},
                            "valueAxes": [{
                                "dashLength": 0
                            }],
                            "startDuration": 1,
                            "legend": {
                                "autoMargins": true,
                                "equalWidths": false,
                                "horizontalGap": 10,
                                "markerSize": 10,
                                "useGraphSettings": true,
                                "valueAlign": "left",
                                "valueWidth": 0
                            },
                            "graphs": [{
                                "balloonText": "[[title]]: <b>[[value]]</b>",
                                "lineAlpha": 1,
                                "lineThickness": 1,
                                "bullet": "round",
                                "type": "line",
                                "lineColor": "#008000",
                                "valueField": "tmReceita",
                                "title": "${msg_aplic.prt_Bi_ticketmedio_receita}"
                            }, {
                                "balloonText": "[[title]]: <b>[[value]]</b>",
                                "lineAlpha": 1,
                                "lineThickness": 1,
                                "bullet": "round",
                                "type": "line",
                                "lineColor": "#3E35BB",
                                "valueField": "tmCompetencia",
                                "title": "${msg_aplic.prt_Bi_ticketmedio_competencia}"
                            }, {
                                "balloonText": "[[title]]: <b>[[value]]</b>",
                                "lineAlpha": 1,
                                "lineThickness": 1,
                                "bullet": "round",
                                "type": "line",
                                "lineColor": "#E23904",
                                "valueField": "tmDespesa",
                                "title": "${msg_aplic.prt_Bi_ticketmedio_despesas}"
                            }],
                            "chartCursor": {
                                "categoryBalloonEnabled": false,
                                "cursorAlpha": 0,
                                "zoomable": false
                            },
                            "categoryField": "mesAno",
                            "categoryAxis": {
                                "gridAlpha": 0,
                                "axisAlpha": 0,
                                "tickLength": 0
                            },
                            "export": {
                                "enabled": true
                            }

                        });
                        chart.validateData();
                        chart.animateAgain();
                    }
                </script>

                <div id="chartdiv" style="width: 97%; height: 300px; margin-left: 8px;margin-top: 20px"></div>
                <script>
                    carregarGraficoTM();
                </script>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>

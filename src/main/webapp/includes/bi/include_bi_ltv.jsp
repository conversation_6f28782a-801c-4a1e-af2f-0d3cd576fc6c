<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-ltv" styleClass="container-bi"
              rendered="#{LtvControle.exibirBi && !BIControle.configuracaoBI.ltv.naLixeira}">
    <c:if test="${LoginControle.permissaoAcessoMenuVO.biCicloDeVida && (LtvControle.exibirBi || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.ltv.naLixeira)}">
        <a4j:commandLink reRender="containerLtv"
                         styleClass="ba4j:commanar-bi" status="nenhumStatus" onclick="abrirCarregandoPoBI(this)"
                         oncomplete="hideCarregandoPorBI(this)"/>
        <h:panelGroup layout="block" id="containerLtv" style="">
            <h:panelGroup layout="block"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Ciclo de vida do seu cliente" styleClass="bi-titulo pull-left"
                                      id="tituloBILtv"/>
                        <h:outputLink styleClass="tooltipster linkWiki bi-cor-cinza bi-left-align"
                                      style="float: left;margin-top: 2%;margin-left: 2%"
                                      value="#{SuperControle.urlBaseConhecimento}como-habilitar-o-bi-ciclo-de-vida-do-seu-cliente/"
                                      title="Clique e saiba mais: LTV" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>
                        <a4j:commandLink id="recarregarBiLtv" style="float: right;"
                                         reRender="containerLtv, formPanelConfigLtv"
                                         oncomplete="montarTips();"
                                         action="#{LtvControle.recalcularMetricas}">
                            <i class="tooltipster fa-icon-refresh bi-btn-refresh bi-link "
                               style="margin-top: -5px;"></i>
                        </a4j:commandLink>
                        <div style="display: inline-flex; vertical-align: top; float: right; margin-right: 10px; margin-top: 12px;"
                             title="Filtrar os registros a partir desta configura��o"
                             class="tooltipster dateTimeCustom alignToRight">
                            <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelConfigBiLtv')" id="configLtv"
                                             style="float: right;">
                                <img src="../imagens_flat/icon-config-blue.png" height="16px;">
                            </a4j:commandLink>
                        </div>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup rendered="#{LtvControle.configLtvRealizado}">
                    <p style="font-size: 14px; color: #0B4B68; background-color: #E5F6FD; padding: 12px; border: 1px solid #bed6e0; border-radius: 6px; margin: 10px;">
                        Filtro <b>LTV realizado</b> est� ativado.
                    </p>
                </h:panelGroup>
                <h:panelGroup layout="block"
                              rendered="#{(BIControle.configuracaoBI.ltv.apresentarBoxCarregar) && !BIControle.biCarregado}">
                    <div class="ghost">
                        <h3 class="loading linha"></h3>
                        <div class="loading bloco"></div>
                        <div class="loading bloco"></div>
                        <div class="loading bloco" style="margin-right: 0"></div>
                        <h3 class="loading linha" style="width: 30%;"></h3>
                        <h3 class="loading linha"></h3>
                        <div class="loading bloco" style="height: 30px;"></div>
                        <div class="loading bloco" style="height: 30px;"></div>
                        <div class="loading bloco" style="margin-right: 0; height: 30px;"></div>
                        <h3 class="loading linha" style="height: 60px;"></h3>
                    </div>
                </h:panelGroup>
                <h:panelGroup layout="block" style="margin-top: 20px;"
                              rendered="#{!BIControle.configuracaoBI.ltv.apresentarBoxCarregar || BIControle.biCarregado}">
                    <h:panelGroup id="bi-header-ltv" styleClass="" layout="block" style="padding-bottom: 10px;">
                        <h:panelGroup layout="block" styleClass="gr-container-totalizador"
                                      style="padding-bottom: 30px;">
                            <h:panelGroup layout="block" styleClass="gr-totalizador">
                                <h:outputText style="display: block;"
                                              styleClass="bi-font-family bi-table-text tituloLtv tooltipster"
                                              value="CAC" title="
<div style='text-align: left !important'>
<h3><b>Custo de Aquisi��o de Clientes (CAC):</b></h3>
   <span style='list-style-type: lower-alpha;'>
      Como o pr�prio nome sugere, � o quanto sua empresa investe para conquistar novos clientes.<br><br><br>
     </span>
</div>
"/>
                                <div style="height: 30px;">
                                    <h:outputText style="display: block;"
                                                  styleClass="bi-font-family bi-table-text subtituloLtv"
                                                  value="Custo de Aquisi��o do Cliente"/>
                                </div>
                                <h:outputText styleClass="bi-font-family bi-table-text bi-font-bold"
                                              id="ltvCac2" style="font-size: 1.5em;"
                                              value="R$ "/>
                                <h:outputText styleClass="bi-font-family bi-table-text bi-font-bold"
                                              id="ltvCac" style="font-size: 2em;"
                                              value="#{LtvControle.cac}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="gr-totalizador">
                                <h:outputText rendered="#{LtvControle.configLtvRealizado}"
                                              style="display: block;"
                                              styleClass="bi-font-family bi-table-text tituloLtv tooltipster"
                                              value="LTV" title="#{LtvControle.titleFiltroLtvRealizadoAtivado}"/>
                                <h:outputText rendered="#{!LtvControle.configLtvRealizado}"
                                              style="display: block;"
                                              styleClass="bi-font-family bi-table-text tituloLtv tooltipster"
                                              value="LTV" title="#{LtvControle.titleFiltroLtvRealizadoDesativado}"/>
                                <div style="height: 30px;">
                                    <a4j:commandLink id="abrirLT" style="display: block;"  styleClass="bi-font-family bi-table-text subtituloLtv tooltipster"
                                                     reRender="bi-container-pendencia"
                                                     actionListener="#{LtvControle.prepararLista}"
                                                     oncomplete="abrirPopup('./includes/bi/lista_clientes_life_time.jsp', 'ClientesLT', 780, 595);"
                                                     value="Tempo m�dio da vida dos clientes: #{LtvControle.tempoDuracaoMedioDosContratos} meses">
                                        <f:attribute name="dado" value="LT"/>
                                    </a4j:commandLink>
                                    <a4j:commandLink id="abrirobj" styleClass="texto-size-16 linkPadrao texto-cor-azul tooltipster"
                                                     reRender="bi-container-obj"
                                                     title="#{obj.tipo.hint}"
                                                     actionListener="#{RelContratosRecorrenciaControle.prepararLista}"
                                                     oncomplete="#{obj.abrirPopUp}"
                                                     value="#{obj.qtd}">
                                        <f:attribute name="obj" value="#{obj}"/>
                                    </a4j:commandLink>

                                </div>
                                <h:outputText styleClass="bi-font-family tooltipster bi-table-text bi-font-bold"
                                              id="ltvLtv2" style="font-size: 1.5em;"
                                              value="R$ "
                                              rendered="#{!LtvControle.configLtvRealizado}"
                                              title="#{LtvControle.titleFiltroLtvRealizadoDesativado}"
                                />
                                <h:outputText styleClass="bi-font-family tooltipster bi-table-text bi-font-bold"
                                              id="ltvLtv2-filtroAtivado" style="font-size: 1.5em;"
                                              value="R$ "
                                              rendered="#{LtvControle.configLtvRealizado}"
                                              title="#{LtvControle.titleFiltroLtvRealizadoAtivado}"
                                />
                                <h:outputText styleClass="bi-table-text bi-font-bold bi-font-family tooltipster"
                                              rendered="#{!LtvControle.configLtvRealizado}"
                                              title="#{LtvControle.titleFiltroLtvRealizadoDesativado}"
                                              id="ltvLtv" style="font-size: 2em;"
                                              value="#{LtvControle.ltv}">

                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText styleClass="bi-table-text bi-font-bold bi-font-family tooltipster"
                                              rendered="#{LtvControle.configLtvRealizado}"
                                              title="#{LtvControle.titleFiltroLtvRealizadoAtivado}"
                                              id="ltvLtv-filtroAtivado" style="font-size: 2em;"
                                              value="#{LtvControle.ltv}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="gr-totalizador">
                                <h:outputText style="display: block;"
                                              styleClass="bi-font-family bi-table-text tituloLtv tooltipster"
                                              value="Churn Rate"
                                              title="
<div style='text-align: left !important'>
<h3><b>Churn Rate:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      � uma m�trica que indica o quanto sua empresa perdeu de clientes no m�s atual.<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      O valor apresentado aqui � referente ao m�s atual de todas as suas empresas.<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Para ver detalhamento de todas as empresas dos meses anteriores, clique em \"Ver detalhes\" no link abaixo.<br /><br /><br />
     </span>
</div>
"/>
                                <div style="height: 30px;">
                                    <h:outputText style="display: block;"
                                                  styleClass="bi-font-family bi-table-text subtituloLtv"
                                                  value="Taxa de cancelamento"/>
                                </div>
                                <h:outputText styleClass="bi-font-family bi-table-text bi-font-bold tooltipster"
                                              id="ltvChurn" style="font-size: 2em; color: #{LtvControle.corChurn};"
                                              title="
<div style='text-align: left !important'>
<h3><b>Churn Rate:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      � uma m�trica que indica o quanto sua empresa perdeu de clientes no m�s atual.<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      O valor apresentado aqui � referente ao m�s atual de todas as suas empresas.<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Para ver detalhamento de todas as empresas dos meses anteriores, clique em \"Ver detalhes\" no link abaixo.<br /><br /><br />
     </span>
</div>
"
                                              value="#{LtvControle.churn}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText styleClass="bi-font-family bi-table-text bi-font-bold tooltipster"
                                              title="
<div style='text-align: left !important'>
<h3><b>Churn Rate:</b></h3>
    <span style='list-style-type: lower-alpha;'>
      � uma m�trica que indica o quanto sua empresa perdeu de clientes no m�s atual.<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      O valor apresentado aqui � referente ao m�s atual de todas as suas empresas.<br />
     </span>
     <span style='list-style-type: lower-alpha;'>
      Para ver detalhamento de todas as empresas dos meses anteriores, clique em \"Ver detalhes\" no link abaixo.<br /><br /><br />
     </span>
</div>
"
                                              id="ltvChurn2" style="font-size: 1.5em; color: #{LtvControle.corChurn};"
                                              value="%"/>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup id="bi-bottom-ltv" styleClass="" layout="block"
                                      style="padding: 20px; height: 230px; margin-top: 20px;">
                            <h:outputText
                                    style="display: block; text-transform: none !important; font-size: 16px; float: left;"
                                    styleClass="bi-font-family bi-table-text tituloLtv"
                                    value="Como calcular o LTV?"/>
                            <i class="fa-icon-info-circle lineHeight-3em tooltipster"
                               title="LTV = Valor do tempo de vida do cliente."
                               style="height: 24px; color: #29AAE2; float: left; margin-left: 10px; margin-top: -8px;"></i>
                            <br/><br/>
                            <h:outputText style="font-size: 12px;"
                                          styleClass="bi-font-family bi-table-text"
                                          rendered="#{!LtvControle.configLtvRealizado}"
                                          value="LTV = [(Valor da soma de todos os contratos / pela dura��o dos mesmos) * Tempo m�dio de vida dos clientes]"/>
                            <h:outputText style="font-size: 12px;"
                                          styleClass="bi-font-family bi-table-text"
                                          rendered="#{LtvControle.configLtvRealizado}"
                                          value="LTV = [(Valor da soma equivalente aos meses utilizados de todos os contratos / soma dos meses utilizados dos contratos) * Tempo m�dio de vida dos clientes]"/>
                            <br/><br/><br/>
                            <div class="tabs-container">

                                <input type="radio" name="tabs" class="tabs" id="tab1" checked>
                                <label for="tab1">CAC</label>
                                <div>
                                    <p class="textoRodapeLtv">� o quanto sua empresa investe para conquistar novos
                                        clientes.</p>
                                </div>

                                <input type="radio" name="tabs" class="tabs" id="tab2">
                                <label for="tab2">VALOR LTV</label>
                                <div>
                                    <c:choose>
                                    <c:when test="${LtvControle.configLtvRealizado}">
                                        <p class="textoRodapeLtv">
                                            O <b>Valor LTV</b> � calculado utilizando a compet�ncia mensal dos contratos ativos no m�s analisado (desconsiderando clientes inadimplentes). Esse valor � dividido pela soma dos meses utilizados at� o momento e, em seguida, multiplicado pelo tempo m�dio de vida dos clientes. Em outras palavras, o LTV � a previs�o de quanto, em m�dia, cada cliente pode gerar de receita durante seu v�nculo com a empresa.
                                        </p>
                                    </c:when>
                                    <c:otherwise>
                                    <p class="textoRodapeLtv">
                                        O <b>Valor LTV</b> � a compet�ncia m�dia mensal gerada por cada cliente, multiplicado pelo tempo m�dio de vida dos clientes. Em outras palavras, � a previs�o de quanto, em m�dia, cada cliente pode gerar de receita durante seu v�nculo com a empresa.
                                        </p>
                                    </c:otherwise>
                                </c:choose>
                                </div>


                                <input type="radio" name="tabs" class="tabs" id="tab3">
                                <label for="tab3">CHURN RATE</label>
                                <div>
                                    <p class="textoRodapeLtv">� quantidade de contratos cancelados e finalizados (data final do contrato) dentro do per�odo pesquisado (Exceto os renovados), dividida pelos contratos ativos no in�cio do m�s + as matr�culas, rematr�culas e contratos transferidos do per�odo. Os
                                        n�veis do Churn Rate est�o em:<br/><b>
                                            <span style="color: #28A24E;">AT� 3% = NORMAL</span> |
                                            <span style="color: #F37021;">AT� 8% = PERIGO</span> |
                                            <span style="color: #EF3C34;">ACIMA DE 8% = ALERTA M�XIMO.</span>
                                        </b></p>
                                </div>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="bi-panel" style="text-align: right">
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-size-16 texto-cor-azul tooltipster"
                                             oncomplete="Richfaces.showModalPanel('panelGraficoBiLtv')"
                                             value="Ver Detalhes"
                                             id="verDetalhesChurnRate"
                                             title="Mostrar detalhamento Churn Rate"
                                             reRender="bi-container-pendencia">
                                <h:outputText style="margin-left: 5px"
                                              styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-plus-sign"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>

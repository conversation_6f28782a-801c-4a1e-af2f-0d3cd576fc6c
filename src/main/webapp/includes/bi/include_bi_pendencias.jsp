<%--
    Document   : include_bi_pendencias
    Created on : 19/02/2016
    Author     : <PERSON>
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-pendencia" styleClass="container-bi">
    <a4j:commandLink  action="#{BIControle.carregarPendencia}" reRender="bi-container-pendencia"
                      status="nenhumStatus"
                      onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                       styleClass="btn-atualizar-bi"/>
    <c:if test="${LoginControle.permissaoAcessoMenuVO.pendenciaCliente || LoginControle.usuarioLogado.administrador}">
    <h:panelGroup layout="block" id="bi-container-pendencia">
        <h:panelGroup layout="block" rendered="#{(BIControle.configuracaoBI.pendencia.apresentarBoxCarregar) && !BIControle.biCarregado}" styleClass="bi-unloaded">
            <h:panelGroup styleClass="bi-header" layout="block" >
                <h:panelGroup layout="block" styleClass="bi-panel" >
                    <h:outputText value="Pend�ncias de Clientes" styleClass="bi-titulo pull-left"/>
                </h:panelGroup>
            </h:panelGroup>
            <div class="ghost">
                <table>
                    <tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr><tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr><tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr><tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr><tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr>
                </table>
            </div>
        </h:panelGroup>
        <h:panelGroup layout="block"  styleClass="bi-pendencia" rendered="#{!BIControle.configuracaoBI.pendencia.apresentarBoxCarregar || BIControle.biCarregado}">
        <h:panelGroup styleClass="bi-header" layout="block"   >
            <h:panelGroup layout="block" styleClass="bi-panel" >
                <h:outputText value="Pend�ncias de Clientes" styleClass="bi-titulo pull-left"/>

                <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                              style="float: left;margin-top: 4.4%;margin-left: 2%"
                              value="#{SuperControle.urlBaseConhecimento}bi-pendencias-de-clientes-adm/"
                              title="Clique e saiba mais: Pend�ncias de Clientes por Carteira" target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>

                <a4j:commandLink id="consultarPendencia"
                reRender="bi-container-pendencia"
                                 styleClass="font-size-Em tooltipster"
                                 action="#{PendenciaControleRel.atualizarConsulta}">
                    <i title="Consultar Dados Pend�ncia Clientes" class="tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                </a4j:commandLink>
                <h:panelGroup layout="block" styleClass="pull-right calendarSemInputBI" >
                    <div  title="${PendenciaControleRel.dataBase_ApresentarMesDia}"  style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                          class="tooltipster dateTimeCustom alignToRight">
                        <rich:calendar id="dataInicioPendencia"
                                       value="#{PendenciaControleRel.dataBaseFiltro}"
                                       inputSize="8"
                                       showInput="false"
                                       inputClass="forcarSemBorda"
                                       buttonIcon="#{PendenciaControleRel.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                       buttonClass="tooltipster"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false">
                            <a4j:support event="onchanged"  action="#{PendenciaControleRel.filtrarPorPendenciaPorEmpresa}" oncomplete="montarTips();"  reRender="bi-container-pendencia"/>
                        </rich:calendar>
                    </div>
                </h:panelGroup>
                <h:panelGroup layout="block"
                              styleClass="pull-right calendarSemInputBI" style="margin-right: 0.6em;">
                    <div  style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                          title="Filtra os registros a partir desta data"
                          class="tooltipster dateTimeCustom alignToRight">
                        <rich:calendar id="dataLimiteItensBI"
                                       value="#{PendenciaControleRel.dataBaseInicialFiltro}"
                                       inputSize="8"
                                       showInput="false"
                                       inputClass="forcarSemBorda"
                                       buttonIcon="/imagens_flat/icon-config-blue.png"
                                       buttonClass="tooltipster"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       enableManualInput="true"
                                       zindex="2"
                                       rendered="#{empty PendenciaControleRel.dataBaseInicialFiltro}"
                                       showWeeksBar="false">
                            <a4j:support event="onchanged"  action="#{PendenciaControleRel.atualizarDataBaseLimiteFiltroBI}" oncomplete="montarTips();"  reRender="panelAutorizacaoFuncionalidade"/>
                        </rich:calendar>
                    </div>
                    <div title="${PendenciaControleRel.dataBaseInicialFiltroDescricaoMesAno}"
                         style="display: inline-flex;margin-top: 1em;vertical-align: top;">
                        <a4j:commandLink id="botaoRemoverDataLimite"
                                         reRender="bi-container-pendencia, panelAutorizacaoFuncionalidade"
                                         styleClass="font-size-Em tooltipster"
                                         rendered="#{not empty PendenciaControleRel.dataBaseInicialFiltro}"
                                         action="#{PendenciaControleRel.removerDataBaseLimiteFiltroBI}"
                                         oncomplete="montarTips();">
                            <h:graphicImage url="/imagens_flat/icon-config-red.png"
                                            style="width: 1.1em;"
                                            styleClass="tooltipster bi-link pull-right lineHeight-3em" />
                        </a4j:commandLink>
                    </div>
                </h:panelGroup>
                <h:panelGroup layout="block"
                              styleClass="pull-right calendarSemInputBI" style="margin-right: 10px;">
                <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')" action="#{BIControle.biAtualizarParam}"
                                 reRender="formPanelFiltroCol, tituloFiltro,filtroConversaoColaborador">
                    <i title="Filtrar Por Colaborador" class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">
                        <span class="badgeItem3Icon" data-bagde="${BIControle.qtdColPendencia}"></span>
                    </i>
                    <a4j:actionparam name="biAtualizar" value="PENDENCIA"></a4j:actionparam>
                    <a4j:actionparam name="biAtualizarAbrirConsulta" value="biAtualizarAbrirConsulta"></a4j:actionparam>
                    <a4j:actionparam name="reRenderBi" value="bi-container-pendencia"></a4j:actionparam>
                </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <%--<h:panelGroup styleClass="bi-header bg-cinza" layout="block">--%>
            <%--<h:panelGroup layout="block" styleClass="bi-panel col-text-align-right" >--%>
               <%----%>
            <%--</h:panelGroup>--%>
        <%--</h:panelGroup>--%>
        <rich:dataTable id="listaPendencia" width="100%"
                        headerClass="consulta"
                        columnClasses="semBorda" styleClass="tabelaSimplesCustom showCellEmpty"
                        rows="100"
                        value="#{PendenciaControleRel.listaPendenciaRelVOs}" var="pendencia">
            <rich:column width="60%" rendered="#{pendencia.qtd > 0 || PendenciaControleRel.exibirTodos}">
                <h:panelGroup>
                    <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza tooltipster" value="#{pendencia.tipo.descricao}" title="#{pendencia.tipo.hint}"/>
                </h:panelGroup>
            </rich:column>
            <rich:column rendered="#{pendencia.qtd > 0 || PendenciaControleRel.exibirTodos}" styleClass="col-text-align-right tooltipster" title="#{pendencia.tipo.hint}">
                <a4j:commandLink id="abrirPendencia" styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                 reRender="bi-container-pendencia"
                                 actionListener="#{PendenciaControleRel.selecionarPendenciaListener}"
                                 oncomplete="#{pendencia.onComplete}"
                                 value="#{pendencia.qtd}">
                    <f:attribute name="pendencia" value="#{pendencia}"/>
                </a4j:commandLink>
            </rich:column>
            <rich:column style="text-align: right;margin-right: 4%;" rendered="#{pendencia.qtd > 0 || PendenciaControleRel.exibirTodos}">
                <h:panelGroup rendered="#{pendencia.valor != 0.0}">
                    <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza" style="font-weight:bold;" value=" #{MovPagamentoControle.empresaLogado.moeda} "/>
                    <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza" style="font-weight:bold;margin-right: 13%;"
                                  value="#{pendencia.valor}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </h:panelGroup>
            </rich:column>
        </rich:dataTable>
        <h:panelGroup layout="block" styleClass="bi-panel" style="text-align: right">
            <a4j:commandLink value="#{!PendenciaControleRel.exibirTodos ? 'Ver mais' : 'Ver menos'}" oncomplete="montarTips();" styleClass="linkPadrao texto-font texto-size-16 texto-cor-azul tooltipster"
                             id="verMaisMenos"
                             title="Mostrar/Esconder indicadores sem resultado"
                             reRender="bi-container-pendencia">
                <h:outputText style="margin-left: 5px" styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-minus-sign" rendered="#{PendenciaControleRel.exibirTodos}" />
                <h:outputText style="margin-left: 5px" styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-plus-sign" rendered="#{!PendenciaControleRel.exibirTodos}"/>
                <f:setPropertyActionListener value="#{!PendenciaControleRel.exibirTodos}"
                                             target="#{PendenciaControleRel.exibirTodos}"/>
            </a4j:commandLink>
        </h:panelGroup>
</h:panelGroup>

</h:panelGroup>
    </c:if>
</h:panelGroup>

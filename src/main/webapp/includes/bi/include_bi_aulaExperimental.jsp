<%--
  Created by IntelliJ IDEA.
  User: alcides
  Date: 29/09/2017
  Time: 11:16
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-aulaexperimental"
              styleClass="container-bi"
              rendered="#{LoginControle.permissaoAcessoMenuVO.biAulaExperimental && !BIControle.configuracaoBI.aulaExperimental.naLixeira}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.biAulaExperimental || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.aulaExperimental.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarAulaExperimental}" reRender="containerExperimental"
                         styleClass="btn-atualizar-bi"
                         status="nenhumStatus"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"/>


        <h:panelGroup layout="block" id="containerExperimental">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.aulaExperimental.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="#{msg_aplic.bi_aula_experimental}" styleClass="bi-titulo pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="ghost">
                    <table>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                    </table>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.aulaExperimental.apresentarBoxCarregar || BIControle.biCarregado}">

                <h:panelGroup id="bi-header-aexperimental" styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="#{msg_aplic.bi_aula_experimental}" styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="tooltipster linkWiki bi-cor-cinza bi-left-align"
                                      style="float: left;margin-top: 4.4%;margin-left: 2%"
                                      value="#{SuperControle.urlBaseConhecimento}bi-aulas-experimentais-adm/"
                                      title="#{msg_aplic.bi_aula_experimental_wiki}" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarAExperimental"
                                         reRender="containerExperimental"
                                         oncomplete="montarTips();"
                                         action="#{RelatorioAgendamentosControle.novoBITela}">
                            <i title="Consultar Dados Aulas Experimentais"
                               class="lineHeight-3em tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right"></i>
                        </a4j:commandLink>
                        <h:panelGroup layout="block" styleClass="col-text-align-right pull-right calendarSemInputBI">
                            <div title="${RelatorioAgendamentosControle.dataBase_ApresentarMesDia}"
                                 style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                                 class="tooltipster dateTimeCustom alignToRight">
                                <rich:calendar id="dataInicioAExperimental"
                                               value="#{RelatorioAgendamentosControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{RelatorioAgendamentosControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{RelatorioAgendamentosControle.novoBITela}"
                                                 oncomplete="montarTips();"
                                                 reRender="containerExperimental"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>

                        <%--                    <h:panelGroup layout="block"--%>
                        <%--                                  styleClass="pull-right calendarSemInputBI" style="margin-right: 12px;">--%>
                        <%--                        Este ainda n�o filtra por colaborador--%>
                        <%--                        --%>
                        <%--                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')" action="#{BIControle.biAtualizarParam}"  reRender="colCheck, grupoMarcadoCss, tituloFiltro">--%>
                        <%--                            <i title="Filtrar Por Colaborador" class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">--%>
                        <%--                                <span class="badgeItem2Icon" data-bagde="${BIControle.qtdColAulaExp}"></span>--%>
                        <%--                            </i>--%>
                        <%--                            <a4j:actionparam name="biAtualizar" value="AULA_EXPERIMENTAL"></a4j:actionparam>--%>
                        <%--                            <a4j:actionparam name="biAtualizarAbrirConsulta" value="biAtualizarAbrirConsulta"></a4j:actionparam>--%>
                        <%--                            <a4j:actionparam name="reRenderBi" value="containerExperimental"></a4j:actionparam>--%>
                        <%--                        </a4j:commandLink>--%>
                        <%--                    </h:panelGroup>--%>

                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="gr-container-totalizador"
                              style=" border-bottom:none;padding-top: 20px">
                    <h:panelGroup layout="block" styleClass="gr-totalizador" style="height: auto;vertical-align: top;">

                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-16">
                            <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text"
                                          value="Alunos agendados"/>

                        </h:panelGroup>


                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40">
                            <a4j:commandLink styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                             oncomplete="abrirPopup('./controleOperacoesListagemAlunos.jsp', 'AulaExperimentalAlunosAgendados', 780, 595);"
                                             action="#{RelatorioAgendamentosControle.selecionarAlunosComAgendamento}">
                                <h:outputText value="#{RelatorioAgendamentosControle.dto.alunosAgendados
                            }"
                                              styleClass="gr-totalizador-text"
                                              style="font-size: 3vw !important;"/>
                            </a4j:commandLink>
                        </h:panelGroup>


                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="gr-totalizador" style="height: auto;vertical-align: top;">

                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-16">
                            <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text"
                                          value="Executaram"/>
                        </h:panelGroup>


                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40">
                            <a4j:commandLink
                                    oncomplete="abrirPopup('./controleOperacoesListagemAlunos.jsp', 'AulaExperimentalAlunosQueExecutaramAgendamento', 780, 595);"
                                    action="#{RelatorioAgendamentosControle.selecionarAlunosQueExecutaramAgendamento}">
                                <h:outputText value="#{RelatorioAgendamentosControle.dto.executaram}"
                                              styleClass="gr-totalizador-text"
                                              style="font-size: 3vw !important;">
                                </h:outputText>
                            </a4j:commandLink>
                        </h:panelGroup>

                    </h:panelGroup>


                    <h:panelGroup layout="block" styleClass="gr-totalizador" style="height: auto;vertical-align: top;">

                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-16">
                            <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text"
                                          value="Convertidos"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="gr-totalizador-item-size-40">
                            <a4j:commandLink
                                    oncomplete="abrirPopup('./controleOperacoesListagemAlunos.jsp', 'AulaExperimentalAlunosAgendadosConvertidos', 780, 595);"
                                    action="#{RelatorioAgendamentosControle.selecionarAlunosQueConvertidos}">
                                <h:outputText
                                        value="#{RelatorioAgendamentosControle.dto.convertidos}"
                                        styleClass="gr-totalizador-text"
                                        style="font-size: 3vw !important;">
                                </h:outputText>
                            </a4j:commandLink>
                        </h:panelGroup>

                    </h:panelGroup>
                    <style>
                        .segundacolunaesquerda tr td:last-of-type {
                            text-align: right;
                            padding-right: 15px;
                        }
                    </style>

                    <table style="border-top: 1px solid #dde7e7; margin-top: 30px; width: 100%;"
                           class="tabelaSimplesCustom showCellEmpty segundacolunaesquerda">
                        <tr>
                            <td><h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              value="Total de aulas agendadas"/></td>
                            <td>
                                <a4j:commandLink oncomplete="abrirPopup('agendamentos.jsp', 'indicacao', 1200, 750);"
                                                 styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                                 value="#{RelatorioAgendamentosControle.dto.aulasAgendadas}"
                                                 action="#{RelatorioAgendamentosControle.abrirAulasExperimentais}">
                                </a4j:commandLink>
                            </td>
                        </tr>
                        <tr>
                            <td><h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              value="Total de aulas executadas"/></td>
                            <td>
                                <a4j:commandLink oncomplete="abrirPopup('agendamentos.jsp', 'indicacao', 1200, 750);"
                                                 styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                                 value="#{RelatorioAgendamentosControle.dto.aulasExecutadas}"
                                                 action="#{RelatorioAgendamentosControle.abrirAulasExperimentaisExecutadas}">
                                </a4j:commandLink>
                            </td>
                        </tr>
                        <tr>
                            <td><h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              value="#{msg_aplic.aulas_aconteceram}"/></td>
                            <td><a4j:commandLink styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                                 reRender="bi-container-pendencia"
                                                 oncomplete="abrirPopup('agendamentos.jsp', 'indicacao', 1200, 750);"
                                                 action="#{RelatorioAgendamentosControle.abrirAulasExperimentaisAconteceram}"
                                                 value="#{RelatorioAgendamentosControle.dto.agendamentosAconteceram}">
                            </a4j:commandLink></td>
                        </tr>
                        <tr>
                            <td><h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              value="#{msg_aplic.aulas_acontecer}"/></td>
                            <td><a4j:commandLink styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                                 oncomplete="abrirPopup('agendamentos.jsp', 'indicacao', 1200, 750);"
                                                 action="#{RelatorioAgendamentosControle.abrirAulasExperimentaisAcontecer}"
                                                 reRender="bi-container-pendencia"
                                                 value="#{RelatorioAgendamentosControle.dto.agendamentosAcontecer}">
                            </a4j:commandLink></td>
                        </tr>
                        <tr>
                            <td><h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              value="Aulas agendadas por aluno"/></td>
                            <td>
                                <h:outputText styleClass="texto-size-16 texto-cor-cinza"
                                              title="M�dia de aulas agendadas por aluno, s� s�o considerados os alunos uma vez no c�lculo"
                                              value="#{RelatorioAgendamentosControle.dto.aulasPorAluno}"/>
                            </td>
                        </tr>
                        <tr>
                            <td><h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              value="�ndice de presen�a em aulas experimentais"/></td>
                            <td>
                                <h:outputText styleClass="texto-size-16 texto-cor-cinza"
                                              title="% das aulas agendadas que foram executadas"
                                              value="#{RelatorioAgendamentosControle.dto.indicePresencaFormatado}%"/>
                            </td>
                        </tr>
                        <tr>
                            <td><h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              value="�ndice de convers�o de aulas experimentais"/></td>
                            <td>
                                <h:outputText styleClass="texto-size-16 texto-cor-cinza"
                                              title="% de convers�es em rela��o � aulas que foram executadas"
                                              value="#{RelatorioAgendamentosControle.dto.indiceConversaoAulasFormatado}%"/>
                            </td>
                        </tr>
                        <tr>
                            <td><h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              value="�ndice de convers�o dos professores de aulas experimentais"/></td>
                            <td>
                                <a4j:commandLink
                                        oncomplete="abrirPopup('./controleOperacoesListagemProfessores.jsp', 'AulaExperimentalProfessores', 780, 595);"
                                        styleClass="texto-size-16 linkPadrao texto-cor-azul"
                                        value="#{RelatorioAgendamentosControle.dto.indiceConversaoProfessoresFormatado}%"
                                        action="#{RelatorioAgendamentosControle.selecionarProfessores}">
                                </a4j:commandLink>
                            </td>
                        </tr>
                    </table>


                </h:panelGroup>


            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>

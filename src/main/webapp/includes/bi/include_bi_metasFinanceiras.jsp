<%-- 
    Document   : include_bi_metas_financeiras
    Created on : 24/02/2016
    Author     : <PERSON>-%>
<%@include file="../imports.jsp" %>

<h:panelGroup layout="block" id="panelBIMetasFinan"
              rendered="#{(LoginControle.permissaoAcessoMenuVO.visualizaRMetaFinanceiraEmpresaBI
 									  || LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas
 									  || MetaFinanceiroBIControle.usuarioTemMeta) && (!BIControle.configuracaoBI.metasFinan.naLixeira)}"
              styleClass="container-bi">
    <a4j:commandLink action="#{BIControle.carregarMetasFinan}" reRender="containerMetasFinan"
                     onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                     status="nenhumStatus"
                     styleClass="btn-atualizar-bi"/>
    <h:panelGroup layout="block" id="containerMetasFinan">
        <h:panelGroup layout="block"
                      rendered="#{(BIControle.configuracaoBI.metasFinan.apresentarBoxCarregar) && !BIControle.biCarregado}"
                      styleClass="bi-unloaded">
            <h:panelGroup styleClass="bi-header" layout="block">
                <h:panelGroup layout="block" styleClass="bi-panel">
                    <h:outputText value="Metas Financeiras de Venda" styleClass="bi-titulo pull-left"/>
                </h:panelGroup>
            </h:panelGroup>
            <div class="ghost">
                <table>
                    <tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr>
                    <tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr>
                    <tr>
                        <td><h3 class="card-title loading"></h3></td>
                        <td><h3 class="card-title loading"></h3></td>
                    </tr>
                </table>

                <div class="loading" style="height: 120px; margin-bottom: 10px;"></div>
            </div>
        </h:panelGroup>
        <h:panelGroup layout="block"
                      rendered="#{!BIControle.configuracaoBI.metasFinan.apresentarBoxCarregar || BIControle.biCarregado}">
            <h:panelGroup id="bi-header-metasf" styleClass="bi-header" layout="block">
                <h:panelGroup layout="block" styleClass="bi-panel">
                    <h:outputText value="Metas Financeiras de Venda" styleClass="bi-titulo pull-left"/>

                    <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                  style="float: left;margin-left: 1%"
                                  value="#{SuperControle.urlBaseConhecimento}bi-metas-financeiras-de-venda-adm/"
                                  title="Clique e saiba mais: Metas Financeiras" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                    </h:outputLink>

                    <a4j:commandLink id="consultarMFV" reRender="form:containerMetasFinan"
                                     oncomplete="montarTips();"
                                     action="#{MetaFinanceiroBIControle.atualizarMetaAtingida}">
                        <i title="Consultar dados Metas Financeiros"
                           class="tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                    </a4j:commandLink>
                    <h:panelGroup layout="block"
                                  rendered="#{(LoginControle.permissaoAcessoMenuVO.visualizaRMetaFinanceiraEmpresaBI || LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas)}"
                                  styleClass="pull-right calendarSemInputBI" style="margin-right: 1px;">
                        <a4j:commandLink oncomplete="#{BIControle.atualizarColaboradoresMetaFinanceira}"
                                         action="#{BIControle.biAtualizarParam}"
                                         reRender="formPanelFiltroCol, tituloFiltro">
                            <i title="Filtrar Por Colaborador" id="todosBiMetasFin"
                               class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">
                                <span class="badgeItem1Icon" data-bagde="${BIControle.qtdColMetasFin}"></span>
                            </i>
                            <a4j:actionparam name="biAtualizar" value="METAS_FINANCEIRAS"></a4j:actionparam>
                            <a4j:actionparam name="biAtualizarAbrirConsulta"
                                             value="biAtualizarAbrirConsulta"></a4j:actionparam>
                            <a4j:actionparam name="reRenderBi" value="containerMetasFinan"></a4j:actionparam>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" style="display: inline-block;" styleClass="bi-header-calendario"
                          rendered="#{MetaFinanceiroBIControle.meta.codigo != 0}">
                <h:panelGroup layout="block" styleClass="bi-panel">
                    <h:outputText styleClass="bi-left-align texto-size-14 bi-font-family bi-cor-azul"
                                  rendered="#{MetaFinanceiroBIControle.meta.codigo > 0}"
                                  value="#{MetaFinanceiroBIControle.meta.empresa_Apresentar}"/>
                    <h:outputText styleClass="bi-right-align texto-size-14 bi-font-family bi-cor-azul"
                                  rendered="#{MetaFinanceiroBIControle.meta.codigo > 0}"
                                  value="#{MetaFinanceiroBIControle.mesAtual}"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-row bi-panel">

                <h:outputText rendered="#{MetaFinanceiroBIControle.meta.codigo != 0}"
                              styleClass="texto-size-14 bi-font-bold bi-cor-cinza" value="META ATINGIDA"/>

                <a4j:commandLink value="Consultar Meta"
                                 styleClass="pull-right texto-size-14 bi-font-bold bi-cor-azul"
                                 action="#{MetaFinanceiroBIControle.limparConsulta}"
                                 reRender="modalPanelConsultarMeta"
                                 oncomplete="Richfaces.showModalPanel('modalPanelConsultarMeta');"/>

            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-mf-caixa-resultado bi-panel"
                          rendered="#{MetaFinanceiroBIControle.meta.codigo != 0}"
                          style="background-color: #{MetaFinanceiroBIControle.meta.corMetaAtingida}">
                <h:outputText
                        styleClass="texto-size-40 bi-font-bold #{empty MetaFinanceiroBIControle.meta.corMetaAtingida ? '' : 'bi-color-branco'}"
                        value="#{MovPagamentoControle.empresaLogado.moeda} #{MetaFinanceiroBIControle.meta.metaAtingida_Apresentar}"/>
                <h:outputText styleClass="fa-icon-trophy  bi-color-branco texto-size-40"
                              rendered="#{MetaFinanceiroBIControle.meta.bateuTodosMetas}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-row bi-row-center bi-panel"
                          rendered="#{not empty MetaFinanceiroBIControle.meta.valores}"
                          style="margin-bottom: 0px;margin-top: 10px;">
                <h:outputText styleClass="texto-size-14 bi-font-bold bi-cor-cinza" value="LEGENDA"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-panel" style="height: auto;margin-bottom: 10px"
                          rendered="#{not empty MetaFinanceiroBIControle.meta.valores}">
                <h:panelGrid columns="6" width="100%" styleClass="bi-caixa-legenda">
                    <c:forEach items="#{MetaFinanceiroBIControle.meta.valores}" var="valor">
                        <h:outputText styleClass="fa-icon-circle texto-size-14" style="color: #{valor.cor};"/>
                        <h:outputText styleClass="bi-cor-cinza texto-size-14 bi-font-family"
                                      value="#{MovPagamentoControle.empresaLogado.moeda} #{valor.valor_Apresentar}"/>

                    </c:forEach>
                </h:panelGrid>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="bi-separador" style="margin: 0px"/>

            <h:panelGroup layout="block" styleClass="bi-panel bi-row bi-row-center">

                <a4j:commandLink styleClass="bi-cor-azul texto-size-16 bi-font-family"
                                 oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                 rendered="#{LoginControle.permissaoAcessoMenuVO.cadastroMetas}"
                                 actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}">
                    <f:attribute name="funcionalidade"
                                 value="METAS_FINANCEIRO_VENDA"/>
                    <h:outputText value="Cadastrar uma nova meta"/>
                </a4j:commandLink>

                <h:outputText value="� necess�rio selecionar uma empresa para calcular a meta."
                              rendered="#{MetaFinanceiroBIControle.empresaFiltroBI.codigo == 0}"
                              styleClass="bi-table-text"/>

                <a4j:commandLink id="verMaisMFV"
                                 rendered="#{MetaFinanceiroBIControle.meta.codigo != 0}"
                                 action="#{MetaFinanceiroBIControle.montarDetalhamento}"
                                 styleClass="bi-btn-refresh bi-link pull-right"
                                 oncomplete="#{MetaFinanceiroBIControle.msgAlert}">
                    Ver detalhes&nbsp<i title="Ver mais detalhes" class="tooltipster fa-icon-plus-sign "></i>
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

<%@include file="../imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>

<h:panelGroup layout="block"
              rendered="#{LoginControle.permissaoAcessoMenuVO.rotatividade}"
              id="bi-movimentacao-contrato" styleClass="container-bi">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.rotatividade || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.movContrato.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarRotatividade}" reRender="idContainerRotatividade"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         status="nenhumStatus"
                         styleClass="btn-atualizar-bi"/>

        <h:panelGroup layout="block" id="idContainerRotatividade">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.movContrato.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block"
                              style="border-bottom: none; height: 4em !important;">
                    <h:panelGroup layout="block" styleClass="bi-panel" style="line-height: 5.2em !important;">
                        <h:outputText value="Movimentação de Contratos" styleClass="bi-titulo-mc pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup styleClass="bi-separador-mc" layout="block"/>


                <div class="ghost">
                    <table>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                    </table>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.movContrato.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup styleClass="bi-header" layout="block"
                              style="border-bottom: none; height: 4em !important;">
                    <h:panelGroup layout="block" styleClass="bi-panel" style="line-height: 5.2em !important;">
                        <h:outputText value="Movimentação de Contratos" styleClass="bi-titulo-mc pull-left"/>

                        <h:panelGroup layout="block" styleClass="pull-left">
                            <div title="${PendenciaControleRel.dataBaseInicialFiltroDescricaoMesAno}"
                                 style="margin-left:0.8em; margin-top: 0.6em !important;">
                                <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                              value="#{SuperControle.urlBaseConhecimento}bi-movimentacao-de-contratos-adm/"
                                              title="Clique e saiba mais: Movimentação de Contratos" target="_blank">
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div title="${PendenciaControleRel.dataBaseInicialFiltroDescricaoMesAno}"
                                 class="divBotoesBi">
                                <a4j:commandLink id="idConsultarRotatividade"
                                                 reRender="idContainerRotatividade"
                                                 oncomplete="montarTips();"
                                                 action="#{RotatividadeAnaliticoDWControle.atualizar}">
                                    <i title="Consultar dados da Movimentação de Contratos"
                                       style="line-height: normal !important;"
                                       class="tooltipster fa-icon-refresh bi-link"></i>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div class="divBotoesBi">
                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelFiltroMovimentacaoContratos')">
                                    <i class="tooltipster fa-icon-filter bi-link"
                                       style="line-height: normal !important;"
                                       title="Filtrar" id="filtroMovimentacaoContratos"></i>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div title="${RotatividadeAnaliticoDWControle.dataBase_ApresentarMesDia}"
                                 class="tooltipster dateTimeCustom alignToRight divBotoesBi">
                                <rich:calendar id="idDataInicioRotatividade"
                                               value="#{RotatividadeAnaliticoDWControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{RotatividadeAnaliticoDWControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged" action="#{RotatividadeAnaliticoDWControle.atualizar}"
                                                 oncomplete="montarTips();" reRender="idContainerRotatividade"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup styleClass="bi-separador-mc" layout="block"/>

                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGroup styleClass="bi-row-mc" layout="block">
                        <h:outputText styleClass="bi-left-align bi-indicador-mes-referencia-mc"
                                      value="Fechamento do Mês Anterior"/>
                        <h:outputText styleClass="bi-right-align bi-mes-referencia-mc"
                                      value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.mesAnteriorFormatado}"/>
                    </h:panelGroup>

                    <h:panelGrid columns="4" styleClass="bi-mc-caixa-conteudo-panelgrid">
                        <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup"
                                      style="background-color: #29AAE2">
                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                         style="color: #FFFFFF; height: 100%;">
                                <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                 id="idCaixaContratosAtivos"
                                                 style="color: inherit !important;"
                                                 title="Ver Detalhes"
                                                 action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVigenteInicioMes}"
                                                 oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorVigenteMes}"
                                                 value=" #{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdeVigenteMesAtual}"/>
                                <h:outputText styleClass="texto-caixa-bi-mc" value="Contratos Ativos"/>
                            </h:panelGrid>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup"
                                      style="background-color: #E5E5E5;">
                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                         style="color: #333333; height: 100%;">
                                <h:outputText value=""/>
                                <h:outputText value=""/>
                                <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                 id="idCaixaContratosVencidos"
                                                 style="color: inherit !important;"
                                                 title="Ver Detalhes"
                                                 action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVencidoAnterior}"
                                                 oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorVencido}"
                                                 value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdVencido}"/>
                                <h:outputText styleClass="texto-caixa-bi-mc" value="Contratos Vencidos"/>
                                <h:outputText value=""/>
                                <h:outputText value=""/>
                            </h:panelGrid>
                        </h:panelGroup>

                        <c:if test="${RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado}">
                            <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup"
                                          style="background-color: #F7D783;">
                                <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                             style="color: #946F0A; height: 100%;">
                                    <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                     id="qtdDependentesMesAtual"
                                                     style="color: inherit !important;"
                                                     title="Ver Detalhes"
                                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorDependentesInicioMes}"
                                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDependentesInicioMes}"
                                                     value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdDependentesMesAtual}"/>


                                    <h:outputText styleClass="texto-caixa-bi-mc" value="Dependentes"/>
                                </h:panelGrid>
                            </h:panelGroup>
                        </c:if>

                        <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup"
                                      style="background-color: #ADDAB8;">
                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                         style="color: #2CAF4F; height: 100%;">
                                <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                 style="color: inherit !important;"
                                                 id="idCaixaContratosTotalMesAnterior"
                                                 title="Ver Detalhes"
                                                 action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVirgentesMesAnterior}"
                                                 oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorVigenteMes}"
                                                 value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdVigentesMesAnterior}"/>
                                <c:if test="${!RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado}">
                                    <h:outputText styleClass="texto-caixa-bi-mc" value="Ativos + Vencidos"/>
                                </c:if>
                                <c:if test="${RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado}">
                                    <h:outputText styleClass="texto-caixa-bi-mc" value="Ativos + Vencidos + Dependentes"/>
                                </c:if>

                            </h:panelGrid>
                        </h:panelGroup>
                    </h:panelGrid>

                </h:panelGroup>

                <h:panelGroup styleClass="bi-separador-mc" layout="block"/>

                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGroup styleClass="bi-row-mc" layout="block">
                        <h:outputText styleClass="bi-left-align bi-indicador-mes-referencia-mc" value="Mês Atual"/>
                        <h:outputText styleClass="bi-right-align bi-mes-referencia-mc"
                                      value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.mesAtualFormatado}"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGrid columns="3" width="100%" styleClass="panel-grid-icv"
                                 style="border-spacing: 0; margin-bottom: 1.0em;"
                                 columnClasses="col-text-align-left,col-text-align-right,col-text-align-right">
                        <h:outputText value="" styleClass="icv-periodo-text"/>
                        <h:outputText value="Hoje" styleClass="icv-periodo-text-mc"/>
                        <h:outputText value="Até Hoje" styleClass="icv-periodo-text-mc"/>

                        <h:outputText value="Matriculados" styleClass="texto-size-16 bi-cor-cinza "/>

                        <a4j:commandLink id="idMcMatriculadosHoje" styleClass="texto-size-16  bi-cor-azul tooltipster"
                                         title="Ver Detalhes"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoMatriculadosHoje}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorMatriculaHoje}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdMatriculadoHoje}"/>

                        <a4j:commandLink id="idMcMatriculadosMes" title="Ver Detalhes"
                                         styleClass="texto-size-16  bi-cor-azul tooltipster"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoMatriculados}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorMatricula}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdMatriculado}"/>


                        <h:outputText value="Rematriculados" styleClass="texto-size-16 bi-cor-cinza "/>


                        <a4j:commandLink id="idMcRematriculadosHoje" styleClass="texto-size-16  bi-cor-azul tooltipster"
                                         title="Ver Detalhes"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoRematriculaHoje}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorRematriculaHoje}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdRematriculadoHoje}"/>

                        <a4j:commandLink id="idMcRematriculadosMes" title="Ver Detalhes"
                                         styleClass="texto-size-16  bi-cor-azul tooltipster"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoRematricula}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorRematricula}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdRematriculado}"/>

                        <h:outputText value="Cancelados" styleClass="gr-text-warning bi-font-family"/>

                        <a4j:commandLink id="idMcCanceladosHoje"
                                         styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                         title="Ver Detalhes"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoCanceladoHoje}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorCanceladoHoje}"
                                         value=" #{RotatividadeAnaliticoDWControle.desconsiderarCancelamentoMudancaPlano ?
                                         RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdCanceladoHojeComFiltro
                                         : RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdCanceladoHoje}"/>

                        <a4j:commandLink id="idMcCanceladosMes" title="Ver Detalhes"
                                         styleClass="texto-size-16 texto-cor-vermelho tooltipster"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoCancelado}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorCancelado}"
                                         value=" #{RotatividadeAnaliticoDWControle.desconsiderarCancelamentoMudancaPlano ?
                                         RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdCanceladoComFiltro
                                         : RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdCancelado}"/>


                        <h:outputText value="Trancados" styleClass="gr-text-warning bi-font-family"/>


                        <a4j:commandLink id="idMcTrancadoHoje"
                                         styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                         title="Ver Detalhes"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoTrancadoHoje}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorTrancadoHoje}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdTrancamentoHoje}"/>

                        <a4j:commandLink id="idMcTrancadoMes" title="Ver Detalhes"
                                         styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoTrancado}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorTrancado}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdTrancamento}"/>

                        <h:outputText value="Desistências" styleClass="gr-text-warning bi-font-family"/>


                        <a4j:commandLink id="idMcDesistesHoje"
                                         styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                         title="Ver Detalhes"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoDesistenteHoje}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDesistenteHoje}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdDesistenteHoje}"/>

                        <a4j:commandLink id="idMcDesistesMes" title="Ver Detalhes"
                                         styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoDesistente}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDesistente}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdDesistente}"/>

                        <h:outputText value="Retorno de Trancamento" styleClass="texto-size-16 bi-cor-cinza"/>

                        <a4j:commandLink id="idMcReTrancamentoHoje" styleClass="texto-size-16  bi-cor-azul tooltipster"
                                         title="Ver Detalhes"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoRetornoTrancamentoHoje}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorRetornoTrancamentoHoje}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdRetornoTrancamentoHoje}"/>

                        <a4j:commandLink id="idMcReTrancamentoMes" title="Ver Detalhes"
                                         styleClass="texto-size-16  bi-cor-azul tooltipster"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoRetornoTrancamento}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorRetornoTrancamento}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdRetornoTrancamento}"/>

                        <h:outputText value="Contratos Transferidos" styleClass="texto-size-16 bi-cor-cinza"/>

                        <a4j:commandLink id="idMcContratoTransferidoHoje"
                                         styleClass="texto-size-16  bi-cor-azul tooltipster"
                                         title="Ver Detalhes"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoContratoTransferidoHoje}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorContratoTransferidoHoje}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdContratoTransferidoHoje}"/>

                        <a4j:commandLink id="idMcContratoTransferidoMes" title="Ver Detalhes"
                                         styleClass="texto-size-16  bi-cor-azul tooltipster"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoContratoTransferido}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorContratoTransferido}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdContratoTransferido}"/>

                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              styleClass="bi-mc-caixa-resultado-panelgroup2 #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoMes}">
                    <h:outputText
                            styleClass="texto-size-16 bi-font-bold #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoMes}"
                            style="margin-top: 0.75em; margin-left: 0.75em; float: left;"
                            value="Movimentação do Mês"/>
                    <h:outputText id="idMcSaldoMes"
                                  style="margin-top: 0.75em; margin-right: 0.75em; float: right;"
                                  styleClass="texto-size-16 bi-font-bold  #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoMes}"
                                  value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdSaldo}"/>
                    <i style="margin-top: 1.0em; margin-right: 0.75em; float: right;"
                       class="${RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.setaSaldoMes}"></i>
                </h:panelGroup>

                <c:if test="${RotatividadeAnaliticoDWControle.exibirAgregadoresMovimentacaoContrato}">
                    <h:panelGroup layout="block" styleClass="container-row">
                        <h:panelGrid columns="3" width="100%" styleClass="panel-grid-icv"
                                     style="border-spacing: 0; margin-bottom: 1.5em; margin-top: 0.75em"
                                     columnClasses="col-text-align-left,col-text-align-right,col-text-align-right">

                            <h:outputText value="Clientes do contrato com agregadores vinculados" styleClass="texto-size-16 bi-cor-cinza "/>


                            <a4j:commandLink id="idMcAgregadoresVinculadosHoje" styleClass="texto-size-16  bi-cor-azul tooltipster"
                                             title="Ver Detalhes"
                                             action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorAgregadoresVinculadosHoje}"
                                             oncomplete="#{RotatividadeAnaliticoDWControle.abrirRichModalRotatividadePorAgregadoresVinculadosHoje}"
                                             value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdAgregadoresVinculadosHoje}"/>

                            <a4j:commandLink id="idMcAgregadoresVinculadosMes" title="Ver Detalhes"
                                             styleClass="texto-size-16  bi-cor-azul tooltipster"
                                             action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorAgregadoresVinculadosMes}"
                                             oncomplete="#{RotatividadeAnaliticoDWControle.abrirRichModalRotatividadePorAgregadoresVinculadosMes}"
                                             value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdAgregadoresVinculadosMes}"/>


                        </h:panelGrid>
                    </h:panelGroup>

                </c:if>

                <c:if test="${RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado}">
                    <h:panelGroup layout="block" styleClass="container-row">
                        <h:panelGrid columns="3" width="100%" styleClass="panel-grid-icv"
                                     style="border-spacing: 0; margin-bottom: 1.0em;"
                                     columnClasses="col-text-align-left,col-text-align-right,col-text-align-right">

                            <h:outputText value="Dependentes vinculados" styleClass="texto-size-16 bi-cor-cinza "/>


                            <a4j:commandLink id="idMcDependentesVinculadosHoje" styleClass="texto-size-16  bi-cor-azul tooltipster"
                                             title="Ver Detalhes"
                                             action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorDependentesVinculadosHoje}"
                                             oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDependentesVinculadosHoje}"
                                             value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdDependentesVinculadosHoje}"/>

                            <a4j:commandLink id="idMcDependentesVinculadosMes" title="Ver Detalhes"
                                             styleClass="texto-size-16  bi-cor-azul tooltipster"
                                             action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorDependentesVinculadosMes}"
                                             oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDependentesVinculados}"
                                             value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdDependentesVinculados}"/>

                            <h:outputText value="Dependentes desvinculados" styleClass="gr-text-warning bi-font-family"/>

                            <a4j:commandLink id="idMcDependentesDesvinculadosHoje"
                                         styleClass="texto-size-16  texto-cor-vermelho tooltipster"
                                         title="Ver Detalhes"
                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorDependentesDesvinculadosHoje}"
                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDependentesDesvinculadosHoje}"
                                         value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdDependentesDesvinculadosHoje}"/>

                            <a4j:commandLink id="idMcDependentesDesvinculadosMes" title="Ver Detalhes"
                                             styleClass="texto-size-16 texto-cor-vermelho tooltipster"
                                             action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorDependentesDesvinculadosMes}"
                                             oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDependentesDesvinculados}"
                                             value=" #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdDependentesDesvinculados}"/>

                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup layout="block"
                                  styleClass="bi-mc-caixa-resultado-panelgroup2 #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoDependentesMes}">
                        <h:outputText
                                styleClass="texto-size-16 bi-font-bold #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoDependentesMes}"
                                style="margin-top: 0.75em; margin-left: 0.75em; float: left;"
                                value="Movimentação de Dependentes do Mês "/>
                        <h:outputText id="idMcSaldoMesDependentes"
                                      style="margin-top: 0.75em; margin-right: 0.75em; float: right;"
                                      styleClass="texto-size-16 bi-font-bold  #{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.corSaldoDependentesMes}"
                                      value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdSaldoDependentes}"/>
                        <i style="margin-top: 1.0em; margin-right: 0.75em; float: right;"
                           class="${RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.setaSaldoDependentesMes}"></i>
                    </h:panelGroup>
                </c:if>

                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGrid columns="5" styleClass="bi-mc-caixa-conteudo-panelgrid">

                        <h:panelGroup layout="block"
                                      styleClass="bi-mc-caixa-resultado-panelgroup bi-mc-caixa-resultado-panelgroup-border">
                            <h:panelGroup layout="block" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                          style="height: 100%;">
                                <h:panelGroup layout="block" styleClass="bi-mc-valor">
                                    <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                     id="idMcAtivosFim"
                                                     style="color: #29AAE2 !important;"
                                                     title="Ver Detalhes"
                                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVigenteMes}"
                                                     oncomplete="abrirPopup('rotatividadeClienteForm.jsp', 'RotatividadeCliente', 850, 650);"
                                                     value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdeFinalMesAtual}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="bi-mc-legenda">
                                    <h:outputText styleClass="texto-caixa-bi-mc bi-cor-cinza" value="Contratos Ativos"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block"
                                      styleClass="bi-mc-caixa-resultado-panelgroup bi-mc-caixa-resultado-panelgroup-border">
                            <h:panelGroup layout="block" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                          style="height: 100%;">
                                <h:panelGroup layout="block" styleClass="bi-mc-valor">
                                    <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                     id="idMcVencidosFim"
                                                     style="color: #333333 !important;"
                                                     title="Ver Detalhes"
                                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoVencidoMes}"
                                                     oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorVencidoMes}"
                                                     value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdVencidoMes}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="bi-mc-legenda">
                                    <h:outputText styleClass="texto-caixa-bi-mc bi-cor-cinza" value="Contratos Vencidos"/>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>

                        <c:if test="${RotatividadeAnaliticoDWControle.exibirAgregadoresMovimentacaoContrato}">
                            <h:panelGroup layout="block"
                                          styleClass="bi-mc-caixa-resultado-panelgroup bi-mc-caixa-resultado-panelgroup-border">
                                <h:panelGroup layout="block" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                              style="height: 100%;">
                                    <h:panelGroup layout="block" styleClass="bi-mc-valor">


                                        <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                         style="color: #7E5DE9 !important;"
                                                         id="qtdAgregadoresMesAtual"
                                                         title="Ver Detalhes"
                                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorAgregadoresVinculadosMes}"
                                                         oncomplete="#{RotatividadeAnaliticoDWControle.abrirRichModalRotatividadePorAgregadoresVinculadosMes}"
                                                         value="#{RotatividadeAnaliticoDWControle.rotatividadeAnaliticoDWVO.qtdAgregadoresVinculadosMes}"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="bi-mc-legenda">
                                        <h:outputText styleClass="texto-caixa-bi-mc bi-cor-cinza" value="Clientes do contrato com agregadores"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </c:if>

                        <c:if test="${RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado}">
                            <h:panelGroup layout="block"
                                          styleClass="bi-mc-caixa-resultado-panelgroup bi-mc-caixa-resultado-panelgroup-border">
                                <h:panelGroup layout="block" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                              style="height: 100%;">
                                    <h:panelGroup layout="block" styleClass="bi-mc-valor">


                                        <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                         style="color: #D6A10F !important;"
                                                         id="qtdDependentesFinalMesAtual"
                                                         title="Ver Detalhes"
                                                         action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorDependentesFinalMes}"
                                                         oncomplete="#{RotatividadeAnaliticoDWControle.abriRichModalRotatividadePorDependentesFimMes}"
                                                         value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdDependentesFinalMesAtual}"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="bi-mc-legenda">
                                        <h:outputText styleClass="texto-caixa-bi-mc bi-cor-cinza" value="Dependentes"/>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </c:if>

                        <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                            <h:panelGroup layout="block" id="block-total" styleClass="bi-mc-caixa-conteudo-panelgrid" style="height: 100%;">
                                <h:panelGroup layout="block" styleClass="bi-mc-valor">
                                    <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                     style="color: #2CAF4F !important;"
                                                     id="idMcTotalFim"
                                                     title="Ver Detalhes"
                                                     action="#{RotatividadeAnaliticoDWControle.consultarRotatividadePorSituacaoTodos}"
                                                     oncomplete="abrirPopup('rotatividadeClienteForm.jsp', 'RotatividadeCliente', 850, 650);"
                                                     value="#{RotatividadeAnaliticoDWControle.rotatividadeSinteticoDWVO.qtdTotal}"/>

                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="bi-mc-legenda">
                                    <c:if test="${!RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado and !RotatividadeAnaliticoDWControle.exibirAgregadoresMovimentacaoContrato}">
                                        <h:outputText styleClass="texto-caixa-bi-mc bi-cor-cinza"
                                                      value="Ativos + Vencidos"/>
                                    </c:if>
                                    <c:if test="${RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado and !RotatividadeAnaliticoDWControle.exibirAgregadoresMovimentacaoContrato}">
                                        <h:outputText styleClass="texto-caixa-bi-mc bi-cor-cinza"
                                                      value="Ativos + Vencidos + Dependentes"/>
                                    </c:if>
                                    <c:if test="${!RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado and RotatividadeAnaliticoDWControle.exibirAgregadoresMovimentacaoContrato}">
                                        <h:outputText styleClass="texto-caixa-bi-mc bi-cor-cinza"
                                                      value="Ativos + Vencidos + Clientes do contrato com agregadores"/>
                                    </c:if>
                                    <c:if test="${RotatividadeAnaliticoDWControle.configuracaoSistemaVO.usaPlanoRecorrenteCompartilhado and RotatividadeAnaliticoDWControle.exibirAgregadoresMovimentacaoContrato}">
                                        <h:outputText styleClass="texto-caixa-bi-mc bi-cor-cinza"
                                                      value="Ativos + Vencidos + Dependentes + Clientes do contrato com agregadores"/>
                                    </c:if>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>

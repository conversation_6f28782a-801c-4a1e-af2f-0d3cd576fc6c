<%@ taglib prefix="p" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-pendencia" styleClass="container-bi">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.pendenciaCliente || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.pendencia.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarPendencia}" reRender="bi-container-pendencia"
                         status="nenhumStatus"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         styleClass="btn-atualizar-bi"/>
        <h:panelGroup layout="block" id="bi-container-pendencia">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.pendencia.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block"
                              style="border-bottom: none; height: 4em !important;">
                    <h:panelGroup layout="block" styleClass="bi-panel" style="line-height: 5.2em !important;">
                        <h:outputText value="Pend�ncias de Clientes" styleClass="bi-titulo-mc pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup styleClass="bi-separador-mc" layout="block"/>
                <div class="ghost">
                    <table>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                    </table>
                </div>

            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="bi-pendencia"
                          rendered="#{!BIControle.configuracaoBI.pendencia.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup styleClass="bi-header" layout="block"
                              style="border-bottom: none; height: 4em !important;">
                    <h:panelGroup layout="block" styleClass="bi-panel" style="line-height: 5.2em !important;">
                        <h:outputText value="Pend�ncias de Clientes" styleClass="bi-titulo-mc pull-left"/>

                        <h:panelGroup layout="block" styleClass="pull-left">
                            <div title="${PendenciaControleRel.dataBaseInicialFiltroDescricaoMesAno}"
                                 style="margin-left:0.8em; margin-top: 0.4em !important;">
                                <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                              value="#{SuperControle.urlBaseConhecimento}bi-pendencias-de-clientes-adm/"
                                              title="Clique e saiba mais: Pend�ncias de Clientes por Carteira" target="_blank">
                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                </h:outputLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div title="${PendenciaControleRel.dataBaseInicialFiltroDescricaoMesAno}"
                                 class="divBotoesBi">
                                <a4j:commandLink id="consultarPendencia"
                                                 reRender="bi-container-pendencia"
                                                 oncomplete="montarTips();"
                                                 action="#{PendenciaControleRel.atualizarConsulta}">
                                    <i title="Consultar Dados Pend�ncia Clientes"
                                       style="line-height: normal !important;"
                                       class="tooltipster fa-icon-refresh bi-link"></i>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div title="${PendenciaControleRel.dataBase_ApresentarMesDia}"
                                 class="tooltipster dateTimeCustom alignToRight divBotoesBi">
                                <rich:calendar id="dataInicioPendencia"
                                               value="#{PendenciaControleRel.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{PendenciaControleRel.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               buttonClass="tooltipster"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{PendenciaControleRel.filtrarPorPendenciaPorEmpresa}"
                                                 oncomplete="montarTips();" reRender="bi-container-pendencia"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right"
                                      rendered="#{empty PendenciaControleRel.dataBaseInicialFiltro}">
                            <div title="Filtra os registros a partir desta data"
                                 class="tooltipster dateTimeCustom alignToRight divBotoesBi">
                                <rich:calendar id="dataLimiteItensBI"
                                               value="#{PendenciaControleRel.dataBaseInicialFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="/imagens_flat/icon-config-blue.png"
                                               buttonClass="tooltipster"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{PendenciaControleRel.atualizarDataBaseLimiteFiltroBI}"
                                                 oncomplete="montarTips();" reRender="panelAutorizacaoFuncionalidade"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right"
                                      rendered="#{not empty PendenciaControleRel.dataBaseInicialFiltro}">
                            <div title="${PendenciaControleRel.dataBaseInicialFiltroDescricaoMesAno}"
                                 class="divBotoesBi">
                                <a4j:commandLink id="botaoRemoverDataLimite"
                                                 reRender="bi-container-pendencia, panelAutorizacaoFuncionalidade"
                                                 action="#{PendenciaControleRel.removerDataBaseLimiteFiltroBI}"
                                                 oncomplete="montarTips();">
                                    <h:graphicImage url="/imagens_flat/icon-config-red.png"
                                                    style="width: 1.1em;"
                                                    styleClass="tooltipster bi-link pull-right"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right">
                            <div title="${PendenciaControleRel.dataBaseInicialFiltroDescricaoMesAno}"
                                 class="divBotoesBi">
                                <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')"
                                                 action="#{BIControle.biAtualizarParam}"
                                                 reRender="formPanelFiltroCol, tituloFiltro,filtroConversaoColaborador">
                                    <i title="Filtrar Por Colaborador"
                                       class="tooltipster fa-icon-user bi-link" style="line-height: normal !important;">
                                        <span class="badgeItem3Icon" data-bagde="${BIControle.qtdColPendencia}"></span>
                                    </i>
                                    <a4j:actionparam name="biAtualizar" value="PENDENCIA"></a4j:actionparam>
                                    <a4j:actionparam name="biAtualizarAbrirConsulta"
                                                     value="biAtualizarAbrirConsulta"></a4j:actionparam>
                                    <a4j:actionparam name="reRenderBi" value="bi-container-pendencia"></a4j:actionparam>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup styleClass="bi-separador-mc" layout="block"/>

                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGroup styleClass="bi-row-mc" layout="block">
                        <h:outputText styleClass="bi-left-align bi-indicador-mes-referencia-mc"
                                      value="Financeiro"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="container-row" style="margin-left: 0; width: 100%">
                        <h:panelGrid columns="5" styleClass="bi-mc-caixa-conteudo-panelgrid">
                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid">
                                <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                    <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                                 style="height: 100%;">
                                        <h:outputText value=""/>
                                        <h:outputText value=""/>
                                        <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                         id="pendenciaParcelaEmAtraso"
                                                         style="color: #F15858 !important;"
                                                         title="#{PendenciaControleRel.parcelasEmAtraso.tipo.hint}"
                                                         actionListener="#{PendenciaControleRel.selecionarPendenciaListener}"
                                                         oncomplete="#{PendenciaControleRel.parcelasEmAtraso.onComplete}">
                                            <h:outputText value=" #{PendenciaControleRel.empresaLogado.moeda} "/>
                                            <h:outputText value="#{PendenciaControleRel.parcelasEmAtraso.valor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                            <f:attribute name="pendencia"
                                                         value="#{PendenciaControleRel.parcelasEmAtraso}"/>
                                        </a4j:commandLink>
                                        <h:outputText styleClass="texto-caixa-bi bi-cor-cinza tooltipster"
                                                      value="#{PendenciaControleRel.parcelasEmAtraso.tipo.descricao}"/>
                                        <h:outputText
                                                style="font-size: 0.8vw; color : #3677e2"
                                                styleClass="tooltipster"
                                                title="Quantidade de alunos que possuem parcelas em atraso."
                                                value="#{PendenciaControleRel.parcelasEmAtraso.qtd}"/>
                                        <h:outputText value=""/>
                                    </h:panelGrid>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="bi-separador-pc"/>

                                <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                    <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                                 style="height: 100%;">
                                        <h:outputText value=""/>
                                        <h:outputText value=""/>
                                        <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                         id="pendenciadebitoEmContaCorrente"
                                                         style="color: #F15858 !important;"
                                                         title="#{PendenciaControleRel.debitoEmContaCorrente.tipo.hint}"
                                                         actionListener="#{PendenciaControleRel.selecionarPendenciaListener}"
                                                         oncomplete="#{PendenciaControleRel.debitoEmContaCorrente.onComplete}">
                                            <h:outputText value=" #{PendenciaControleRel.empresaLogado.moeda} "/>
                                            <h:outputText value="#{PendenciaControleRel.debitoEmContaCorrente.valor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                            <f:attribute name="pendencia"
                                                         value="#{PendenciaControleRel.debitoEmContaCorrente}"/>
                                        </a4j:commandLink>
                                        <h:outputText styleClass="texto-caixa-bi bi-cor-cinza"
                                                      value="#{PendenciaControleRel.debitoEmContaCorrente.tipo.descricao}"/>
                                        <h:outputText
                                                style="font-size: 0.8vw; color : #3677e2"
                                                styleClass="tooltipster"
                                                title="Quantidade de alunos que possuem d�bito em conta corrente."
                                                value="#{PendenciaControleRel.debitoEmContaCorrente.qtd}"/>
                                        <h:outputText value=""/>
                                    </h:panelGrid>
                                </h:panelGroup>

                            </h:panelGrid>

                            <h:panelGroup layout="block" styleClass="bi-separador-vertical-pc"/>

                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid">

                                <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                    <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                                 style="height: 100%;">
                                        <h:outputText value=""/>
                                        <h:outputText value=""/>
                                        <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                         id="pendenciaParcelaAPagar"
                                                         style="color: #F0B924 !important;"
                                                         title="#{PendenciaControleRel.parcelasAPagar.tipo.hint}"
                                                         actionListener="#{PendenciaControleRel.selecionarPendenciaListener}"
                                                         oncomplete="#{PendenciaControleRel.parcelasAPagar.onComplete}">
                                            <h:outputText value=" #{PendenciaControleRel.empresaLogado.moeda} "/>
                                            <h:outputText value="#{PendenciaControleRel.parcelasAPagar.valor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                            <f:attribute name="pendencia"
                                                         value="#{PendenciaControleRel.parcelasAPagar}"/>
                                        </a4j:commandLink>
                                        <h:outputText styleClass="texto-caixa-bi bi-cor-cinza"
                                                      value="#{PendenciaControleRel.parcelasAPagar.tipo.descricao}"/>
                                        <h:outputText
                                                style="font-size: 0.8vw; color : #3677e2"
                                                styleClass="tooltipster"
                                                title="Quantidade de alunos que possuem parcelas a pagar."
                                                value="#{PendenciaControleRel.parcelasAPagar.qtd}"/>
                                        <h:outputText value=""/>
                                    </h:panelGrid>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="bi-separador-pc"/>

                                <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                    <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                                 style="height: 100%;">
                                        <h:outputText value=""/>
                                        <h:outputText value=""/>
                                        <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                         id="pendenciacreditoEmContaCorrente"
                                                         style="color: #64AF45 !important;"
                                                         title="#{PendenciaControleRel.creditoEmContaCorrente.tipo.hint}"
                                                         actionListener="#{PendenciaControleRel.selecionarPendenciaListener}"
                                                         oncomplete="#{PendenciaControleRel.creditoEmContaCorrente.onComplete}">
                                            <h:outputText value=" #{PendenciaControleRel.empresaLogado.moeda} "/>
                                            <h:outputText
                                                    value="#{PendenciaControleRel.creditoEmContaCorrente.valor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                            <f:attribute name="pendencia"
                                                         value="#{PendenciaControleRel.creditoEmContaCorrente}"/>
                                        </a4j:commandLink>
                                        <h:outputText styleClass="texto-caixa-bi bi-cor-cinza"
                                                      value="#{PendenciaControleRel.creditoEmContaCorrente.tipo.descricao}"/>
                                        <h:outputText
                                                style="font-size: 0.8vw; color : #3677e2"
                                                styleClass="tooltipster"
                                                title="Quantidade de alunos que possuem cr�dito em conta corrente."
                                                value="#{PendenciaControleRel.creditoEmContaCorrente.qtd}"/>
                                        <h:outputText value=""/>
                                    </h:panelGrid>
                                </h:panelGroup>


                            </h:panelGrid>

                            <h:panelGroup layout="block" styleClass="bi-separador-vertical-pc"/>

                            <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid">

                                <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                    <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                                 style="height: 100%;">
                                        <h:outputText value=""/>
                                        <h:outputText value=""/>
                                        <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                         id="pendenciaColaboradoresParcelasAPagar"
                                                         style="color: #F0B924 !important;"
                                                         title="#{PendenciaControleRel.colaboradoresParcelasAPagar.tipo.hint}"
                                                         actionListener="#{PendenciaControleRel.selecionarPendenciaListener}"
                                                         oncomplete="#{PendenciaControleRel.colaboradoresParcelasAPagar.onComplete}">
                                            <h:outputText value=" #{PendenciaControleRel.empresaLogado.moeda} "/>
                                            <h:outputText
                                                    value="#{PendenciaControleRel.colaboradoresParcelasAPagar.valor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                            <f:attribute name="pendencia"
                                                         value="#{PendenciaControleRel.colaboradoresParcelasAPagar}"/>
                                        </a4j:commandLink>
                                        <h:outputText styleClass="texto-caixa-bi bi-cor-cinza"
                                                      value="#{PendenciaControleRel.colaboradoresParcelasAPagar.tipo.descricao}"/>
                                        <h:outputText
                                                style="font-size: 0.8vw; color : #3677e2"
                                                styleClass="tooltipster"
                                                title="Quantidade de alunos que possuem parcelas em atraso."
                                                value="#{PendenciaControleRel.colaboradoresParcelasAPagar.qtd}"/>
                                        <h:outputText value=""/>
                                    </h:panelGrid>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="bi-separador-pc"/>

                                <h:panelGroup layout="block" styleClass="bi-mc-caixa-resultado-panelgroup">
                                    <h:panelGrid columns="1" styleClass="bi-mc-caixa-conteudo-panelgrid"
                                                 style="height: 100%;">
                                        <h:outputText value=""/>
                                        <h:outputText value=""/>
                                        <a4j:commandLink styleClass="quantidade-caixa-bi tooltipster"
                                                         id="pendenciaprodutosVencidos"
                                                         style="color: #29AAE2 !important;"
                                                         title="#{PendenciaControleRel.produtosVencidos.tipo.hint}"
                                                         actionListener="#{PendenciaControleRel.selecionarPendenciaListener}"
                                                         oncomplete="#{PendenciaControleRel.produtosVencidos.onComplete}">
                                            <h:outputText value=" #{PendenciaControleRel.empresaLogado.moeda} "/>
                                            <h:outputText value="#{PendenciaControleRel.produtosVencidos.valor}">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:outputText>
                                            <f:attribute name="pendencia"
                                                         value="#{PendenciaControleRel.produtosVencidos}"/>
                                        </a4j:commandLink>
                                        <h:outputText styleClass="texto-caixa-bi bi-cor-cinza"
                                                      value="#{PendenciaControleRel.produtosVencidos.tipo.descricao}"/>
                                        <h:outputText
                                                style="font-size: 0.8vw; color : #3677e2"
                                                styleClass="tooltipster"
                                                title="Quantidade de alunos que possuem produtos vencidos."
                                                value="#{PendenciaControleRel.produtosVencidos.qtd}"/>
                                        <h:outputText value=""/>
                                    </h:panelGrid>
                                </h:panelGroup>

                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:panelGroup styleClass="bi-row-mc" layout="block">
                        <h:outputText styleClass="bi-left-align bi-indicador-mes-referencia-mc"
                                      style="margin-bottom: 20;"
                                      value="Operacional"/>
                    </h:panelGroup>

                    <rich:dataTable id="listaPendencia" width="100%"
                                    headerClass="consulta"
                                    columnClasses="semBorda" styleClass="tabelaSimplesCustom showCellEmpty"
                                    rows="100"
                                    value="#{PendenciaControleRel.listaPendenciaRelVOs}" var="pendencia">
                        <rich:column width="60%" rendered="#{pendencia.qtd > 0 || PendenciaControleRel.exibirTodos}">
                            <h:panelGroup>
                                <h:outputText styleClass="texto-size-16 texto-cor-cinza"
                                              style="margin-left: 0 !important;"
                                              value="#{pendencia.tipo.descricao}"/>
                            </h:panelGroup>
                        </rich:column>
                        <rich:column rendered="#{pendencia.qtd > 0 || PendenciaControleRel.exibirTodos}"
                                     styleClass="col-text-align-right">
                            <a4j:commandLink id="abrirPendencia"
                                             styleClass="texto-size-16 linkPadrao texto-cor-azul tooltipster"
                                             reRender="bi-container-pendencia"
                                             title="#{pendencia.tipo.hint}"
                                             actionListener="#{PendenciaControleRel.selecionarPendenciaListener}"
                                             oncomplete="#{pendencia.onComplete}"
                                             value="#{pendencia.qtd}">
                                <f:attribute name="pendencia" value="#{pendencia}"/>
                            </a4j:commandLink>
                        </rich:column>
                        <rich:column style="text-align: right;margin-right: 4%;"
                                     rendered="#{pendencia.qtd > 0 || PendenciaControleRel.exibirTodos}">
                            <h:panelGroup rendered="#{pendencia.valor != 0.0}">
                                <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              style="font-weight:bold;"
                                              value=" #{MovPagamentoControle.empresaLogado.moeda} "/>
                                <h:outputText styleClass="texto-size-16 linkPadrao texto-cor-cinza"
                                              style="font-weight:bold;margin-right: 13%;"
                                              value="#{pendencia.valor}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                            </h:panelGroup>
                        </rich:column>
                    </rich:dataTable>
                    <h:panelGroup layout="block" styleClass="bi-panel" style="text-align: right">
                        <a4j:commandLink value="#{!PendenciaControleRel.exibirTodos ? 'Ver mais' : 'Ver menos'}"
                                         oncomplete="montarTips();"
                                         styleClass="linkPadrao texto-font texto-size-16 texto-cor-azul tooltipster"
                                         id="verMaisMenos"
                                         title="Mostrar/Esconder indicadores sem resultado"
                                         reRender="bi-container-pendencia">
                            <h:outputText style="margin-left: 5px"
                                          styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-minus-sign"
                                          rendered="#{PendenciaControleRel.exibirTodos}"/>
                            <h:outputText style="margin-left: 5px"
                                          styleClass="texto-font texto-cor-azul texto-size-16 fa-icon-plus-sign"
                                          rendered="#{!PendenciaControleRel.exibirTodos}"/>
                            <f:setPropertyActionListener value="#{!PendenciaControleRel.exibirTodos}"
                                                         target="#{PendenciaControleRel.exibirTodos}"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>



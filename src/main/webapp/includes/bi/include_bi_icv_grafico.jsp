

<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 12/06/2015
  Time: 14:50
  To change this template use File | Settings | File Templates.
--%>

  <script type="text/javascript">
    function carregarGragicoICV() {
      var totalBV = ${IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdQuestionarioMes};
      var chart;
      var questionarioMatricula = ${IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdMatriculaMes + IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdRematriculaMes};
      var bv =  ${IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdQuestionarioMes} - questionarioMatricula;
      totalBV  = totalBV < questionarioMatricula ? questionarioMatricula : totalBV;
      bv = bv < 0 ? totalBV : bv;

      var chartData = [
        {
          "desc": "",
          "matricula": ${IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdMatriculaMes},
          "rematricula": ${IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdRematriculaMes},
          "bv": bv
        }
      ];


        // SERIAL CHART
        chart = new AmCharts.AmSerialChart();
        chart.dataProvider = chartData;
        chart.categoryField = "desc";
        chart.theme = "light";
        chart.type = "serial"
        chart.rotate = true;
        chart.maxWidth = '100%';

        // AXES
        // category
        var categoryAxis = chart.categoryAxis;
        categoryAxis.gridAlpha = 0;
        categoryAxis.axisAlpha = 0;
        categoryAxis.gridPosition = "start";
        categoryAxis.position = "left";
        categoryAxis.labelFrequency = 20;

        // value
        var valueAxis = new AmCharts.ValueAxis();
        valueAxis.stackType = "regular";
        valueAxis.gridAlpha = 0;
        valueAxis.axisAlpha = 0.5;
        valueAxis.maximum = totalBV;
        valueAxis.maxReal = totalBV;
        valueAxis.autoGridCount = false;
        valueAxis.labelFrequency = 1;
        valueAxis.gridCount = 16;
        valueAxis.labelColorField = "#29ABE2";
        chart.addValueAxis(valueAxis);

        graph = new AmCharts.AmGraph();
        graph.title = "Rematr\u00edculas";
        graph.labelText = "[[value]]";
        graph.valueField = "rematricula";
        graph.type = "column";
        graph.lineAlpha = 0;
        graph.fillAlphas = 1;
        graph.lineColor = "#00C350";
        graph.balloonText = "<span style='color:#555555;'>[[category]]</span><br><span style='font-size:14px'>[[title]]:<b>[[value]]</b></span>";
        chart.addGraph(graph);

        var graph = new AmCharts.AmGraph();
        graph.title = "${msg_aplic.prt_Bi_icv_matricula}";
        graph.labelText = "[[value]]";
        graph.colorFields ="#ffffff";
        graph.colorField = "#ffffff";
        graph.textcolor = "#ffffff";
        graph.valueField = "matricula";
        graph.type = "column";
        graph.lineAlpha = 0;
        graph.fillAlphas = 1;
        graph.lineColor = "#29ABE2";
        graph.balloonText = "<span style='color:#555555;'>[[category]]</span><br><span style='font-size:14px'>[[title]]:<b>[[value]]</b></span>";
        chart.addGraph(graph);

        var graph = new AmCharts.AmGraph();
        graph.title = "${msg_aplic.prt_Bi_icv_bv}";
        graph.labelText = '${IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdQuestionarioMes}';
        graph.colorFields ="#ffffff";
        graph.colorField = "#ffffff";
        graph.textcolor = "#ffffff";
        graph.valueField = "bv";
        graph.type = "column";
        graph.lineAlpha = 0;
        graph.fillAlphas = 1;
        graph.lineColor = "#B4B4B4";
        graph.balloonText = "<span style='color:#555555;'>[[category]]</span><br><span style='font-size:14px'>[[title]]:<b>${IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdQuestionarioMes}</b></span>";
        chart.addGraph(graph);

        chart.write("grafico-icv");
    }
</script>
<div layout="block" class="grafico-icv" style="width: 100%; height: 150px;">
<div layout="block" id="grafico-icv" style="width: 100%; height: 150px;"></div>
</div>
<script type="text/javascript">
  carregarGragicoICV();
</script>




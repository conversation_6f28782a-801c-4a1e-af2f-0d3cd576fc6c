<%-- 
    Document   : include_bi_gestao_acessos
    Created on : 21/02/2018, 15:34:58
    Author     : arthur
--%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-gestaoAcesso" styleClass="container-bi"
              rendered="#{LoginControle.permissaoAcessoMenuVO.biGestaoAcesso}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.biGestaoAcesso || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.gestaoAcesso.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarGestaoAcesso}" reRender="bi-container-gestaoAcesso"
                         status="nenhumStatus"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         styleClass="btn-atualizar-bi"/>

        <h:panelGroup layout="block" id="bi-container-gestaoAcesso">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.gestaoAcesso.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Gest�o de Acessos" styleClass="bi-titulo pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="ghost">
                    <table>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                        <tr>
                            <td><h3 class="card-title loading"></h3></td>
                            <td><h3 class="card-title loading"></h3></td>
                        </tr>
                    </table>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="bi-gestaoAcesso"
                          rendered="#{!BIControle.configuracaoBI.gestaoAcesso.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Gest�o de Acessos" styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                      style="float: left;margin-top: 4.4%;margin-left: 1%"
                                      value="#{SuperControle.urlBaseConhecimento}bi-gestao-de-acessos-adm/"
                                      title="Clique e saiba mais: Gest�o de Acessos" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarAcessos"
                                         rendered="#{!GestaoAcessoRelControle.usuarioPacto}"
                                         reRender="bi-container-gestaoAcesso,bi-gestaoAcesso"
                                         styleClass="font-size-Em tooltipster"
                                         action="#{GestaoAcessoRelControle.atualizarDataHoraDadosAcesso}">
                            <i title="Consultar acessos de clientes"
                               class="tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>

                        <a4j:commandLink id="consultarAcessos2"
                                         rendered="#{GestaoAcessoRelControle.usuarioPacto}"
                                         reRender="bi-container-gestaoAcesso,bi-gestaoAcesso"
                                         styleClass="font-size-Em tooltipster"
                                         action="#{GestaoAcessoRelControle.atualizarDadosAcesso}">
                            <i title="Consultar acessos de clientes"
                               class="tooltipster fa-icon-search bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>

                        <a4j:commandLink id="consultarAcessos3"
                                         rendered="#{GestaoAcessoRelControle.usuarioPacto}"
                                         reRender="bi-container-gestaoAcesso,bi-gestaoAcesso"
                                         styleClass="font-size-Em tooltipster"
                                         action="#{GestaoAcessoRelControle.atualizarDataHoraDadosAcesso}">
                            <i title="Atualizar data/hora e consultar acessos de clientes"
                               class="tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>

                        <h:panelGroup layout="block" styleClass="bi-btn-refresh bi-link pull-right lineHeight-3em"
                                      style="margin-top: 10px; height: 100%; width: 150px">
                            <rich:calendar rendered="#{GestaoAcessoRelControle.usuarioPacto}"
                                           styleClass="tooltipster" id="calendarDataHora"
                                           value="#{GestaoAcessoRelControle.dataHoraConsulta}"
                                           datePattern="dd/MM/yyyy HH:mm"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputSize="12"
                                           style="width:250px; float: left;margin-top: 4.4%;margin-left: 1%"/>

                            <i title="Essa data aparece apenas para os usu�rios PACTO
                        <br/>A finalidade � para confer�ncia de algum dado que aconteceu em horas passadas
                        <br/>Apenas ir� interferir no indicador <b>'Alunos em tempo real'</b>"
                               class="tooltipster fa-icon-info-circle bi-btn-refresh bi-link lineHeight-3em"
                               style="margin-top: -10px !important;"></i>
                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGrid columns="2" width="100%" styleClass="panel-grid-icv"
                                 style="border-spacing: 0; margin-bottom: 1.0em;"
                                 columnClasses="col-text-align-left,col-text-align-right">

                        <h:outputText styleClass="bi-table-text bi-left-align" value="Alunos em tempo real"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./clientesAcessoRelBI.jsp', 'Alunos Na Academia', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeClienteNaAcademia}"
                                         action="#{GestaoAcessoRelControle.processarListaClientesNaAcademia}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align" value="Alunos em aulas agendadas"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./clientesAcessoRelBIAulasAgendadas.jsp', 'Alunos na academia em aulas agendadas', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeClienteAulasAgendadas}"
                                         action="#{GestaoAcessoRelControle.consultarClientesAulasAgendadas}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align" value="Acessos bloqueados hoje"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./clientesAcessoRelBIAcessosBloqueados.jsp', 'Acessos bloqueados hoje', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeAcessosBloqueados}"
                                         action="#{GestaoAcessoRelControle.consultarAlunosAcessosBloqueados('hoje')}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align" value="Acessos realizados hoje"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./clientesAcessoRelBIAcessosRealizadosDia.jsp', 'Acessos realizados hoje', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeAcessosRealizadosDia}"
                                         action="#{GestaoAcessoRelControle.consultarAlunosAcessosRealizadosDia}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align tooltipster"
                                      title="Essa informa��o so ir� aparecer para alunos que tenham sa�da"
                                      value="Tempo m�dio de perman�ncia na academia"/>
                        <h:outputText styleClass="texto-size-16 bi-font-bold bi-right-align tooltipster"
                                      title="Essa informa��o so ir� aparecer para alunos que tenham sa�da"
                                      value="#{GestaoAcessoRelControle.tempoMedioAcademia}"/>

                    </h:panelGrid>

                    <c:if test="${GestaoAcessoRelControle.capacidadeSimultanea > 0}">
                        <h:panelGroup layout="block" styleClass="bi-separador"/>

                        <h:panelGrid columns="2" width="100%" styleClass="panel-grid-icv"
                                     style="border-spacing: 0; margin-bottom: 1.0em;"
                                     columnClasses="col-text-align-left,col-text-align-right">

                            <h:outputText styleClass="bi-table-text bi-left-align tooltipster" value="Lota��o m�xima"
                                          title="Capacidade de alunos na academia de forma simult�nea.<br/>Este n�mero � configurado em Empresa, na aba Acessos."/>
                            <h:outputText styleClass="texto-size-16 bi-font-bold bi-right-align tooltipster"
                                          value="#{GestaoAcessoRelControle.capacidadeSimultanea}"
                                          title="Capacidade de alunos na academia de forma simult�nea.<br/>Este n�mero � configurado em Empresa, na aba Acessos."/>

                            <h:outputText styleClass="bi-table-text bi-left-align tooltipster" value="Vagas dispon�veis"
                                          title="Este indicador � a poss�vel quantidade de alunos que ainda podem acessar academia at� a lota��o m�xima."/>
                            <h:outputText styleClass="texto-size-16 bi-font-bold bi-right-align tooltipster"
                                          value="#{GestaoAcessoRelControle.vagasDisponiveis}"
                                          title="Este indicador � a poss�vel quantidade de alunos que ainda podem acessar academia at� a lota��o m�xima."/>

                            <h:outputText styleClass="bi-table-text bi-left-align tooltipster"
                                          value="Vagas dispon�veis nos pr�ximos 10 minutos"
                                          title="Daqui 10 minutos, alguns alunos ter�o sa�do da academia, ent�o esse n�mero � a quantidade poss�vel de alunos para acessar a academia daqui a pouco."/>
                            <h:outputText styleClass="texto-size-16 bi-font-bold bi-right-align tooltipster"
                                          value="#{GestaoAcessoRelControle.vagasDisponiveis10min}"
                                          title="Daqui 10 minutos, alguns alunos ter�o sa�do da academia, ent�o esse n�mero � a quantidade poss�vel de alunos para acessar a academia daqui a pouco."/>

                        </h:panelGrid>
                    </c:if>

                    <h:panelGroup layout="block" styleClass="bi-separador"/>

                    <h:panelGrid columns="2" width="100%" styleClass="panel-grid-icv"
                                 style="border-spacing: 0; margin-bottom: 1.0em;"
                                 columnClasses="col-text-align-left,col-text-align-right">
                        <h:outputText styleClass="bi-table-text bi-left-align" value="Alunos de outra unidade"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./clientesAcessoRelBIOutraUnidade.jsp', 'Alunos Na Academia Outra Unidade', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeClienteOutraUnidade}"
                                         action="#{GestaoAcessoRelControle.processarListaClienteOutraUnidade}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align" value="Acessos liberados"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./liberadosAcessoRelBI.jsp', 'Alunos Na Academia Com Treino Realizado', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeAcessoLiberado}"
                                         action="#{GestaoAcessoRelControle.processarAcessoLiberado}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align"
                                      value="Acessos bloqueados do m�s"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./clientesAcessoRelBIAcessosBloqueados.jsp', 'Acessos bloqueados esse m�s', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeAcessosBloqueadosMes}"
                                         action="#{GestaoAcessoRelControle.consultarAlunosAcessosBloqueados('mes')}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align"
                                      value="Inativos com permiss�o de acesso"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./clientesAcessoRelBISituacaoInativo.jsp', 'Alunos Na Academia Situa��o Inativo', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeClienteSituacaoInativo}"
                                         action="#{GestaoAcessoRelControle.processarListaClienteInativoComAcesso}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align" value="Dia com mais acessos do m�s"/>
                        <h:outputText styleClass="texto-size-16 bi-font-bold bi-right-align tooltipster"
                                      value="#{GestaoAcessoRelControle.diaMaiorAcesso}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align" value="Hor�rio com mais acessos do m�s"/>
                        <h:outputText styleClass="texto-size-16 bi-font-bold bi-right-align tooltipster"
                                      value="#{GestaoAcessoRelControle.horaMaiorAcesso}"/>

                        <h:outputText styleClass="bi-table-text bi-left-align"
                                      value="Alunos com pend�ncias financeiras"/>
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./clientesAcessoPendenciaRelBI.jsp', 'Alunos Na Academia Com Pend�ncia', 1024, 700);"
                                         value=" #{GestaoAcessoRelControle.quantidadeClienteComPendencia}"
                                         action="#{GestaoAcessoRelControle.processarListaClientesNaAcademiaComPendencia}"/>

                    </h:panelGrid>

                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="container-row">
                    <h:panelGroup layout="block" styleClass="bi-separador"/>

                    <h:panelGroup styleClass="bi-row" layout="block">
                        <a4j:commandLink styleClass="texto-size-16  bi-cor-azul bi-font-bold bi-right-align tooltipster"
                                         title="Ver Detalhes"
                                         oncomplete="abrirPopup('./relatorio/indicadorAcessoRel.jsp', 'indicadorAcesso', 1050, 595);">
                            Ver detalhes do gr�fico
                        </a4j:commandLink>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="display: flex; justify-content: flex-end; width: 100%; margin-top: 60px;">
                        <h:panelGroup layout="block" style="width: 35%; text-align: left;">
                            <h:panelGrid columns="1" width="100%" styleClass="panel-grid-icv"
                                         style="border-spacing: 0; margin-bottom: 1.0em;"
                                         columnClasses="col-text-align-left">
                                <h:outputText rendered="#{GestaoAcessoRelControle.percentualCrescimento >= 0.0}"
                                              styleClass="texto-size-16 bi-font-bold bi-left-align tooltipster bi-titulo"
                                              value="Crescimento"/>

                                <h:outputText rendered="#{GestaoAcessoRelControle.percentualCrescimento < 0.0}"
                                              styleClass="texto-size-16 bi-font-bold bi-left-align tooltipster bi-titulo"
                                              value="Decrescimento"/>

                                <h:outputText styleClass="texto-size-30 bi-font-bold tooltipster bi-table-text"
                                              value="#{GestaoAcessoRelControle.percentualCrescimento}%"/>
                            </h:panelGrid>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <script type="text/javascript">
                    function carregarGraficoAcesso() {
                        var chart = AmCharts.makeChart("chartdivAcesso", {
                            "type": "serial",
                            "theme": "light",
                            "dataProvider": ${GestaoAcessoRelControle.listaAcessoGrafico},
                            "valueAxes": [{
                                "position": "left",
                            }],
                            "startDuration": 1,
                            "legend": {
                                "autoMargins": true,
                                "equalWidths": false,
                                "horizontalGap": 10,
                                "markerSize": 10,
                                "useGraphSettings": true,
                                "valueAlign": "left",
                                "valueWidth": 0
                            },
                            "graphs": [{
                                "balloonText": "[[title]]: <b>[[value]]</b>",
                                "fillAlphas": 0.8,
                                "lineAlpha": 0.2,
                                "columnWidth": 0.3,
                                "type": "column",
                                "valueField": "passado",
                                "fillColors": "#999999",
                                "title": "Mesmo m�s do ano passado"
                            }, {
                                "balloonText": "[[title]]: <b>[[value]]</b>",
                                "fillAlphas": 0.8,
                                "lineAlpha": 0.2,
                                "type": "column",
                                "columnWidth": 0.3,
                                "valueField": "atual",
                                "fillColors": "#074871",
                                "title": "M�s atual"
                            }],
                            "chartCursor": {
                                "categoryBalloonEnabled": false,
                                "cursorAlpha": 0,
                                "zoomable": false
                            },
                            "plotAreaFillAlphas": 0.1,
                            "categoryField": "campo",
                            "categoryAxis": {
                                "gridPosition": "start"
                            },
                            "export": {
                                "enabled": true
                            }

                        });

                    }
                </script>
                <div id="chartdivAcesso" style="width: 60%; height: 300px; margin-left: 8px;margin-top: -140px"></div>
                <script>
                    carregarGraficoAcesso();
                </script>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>
 

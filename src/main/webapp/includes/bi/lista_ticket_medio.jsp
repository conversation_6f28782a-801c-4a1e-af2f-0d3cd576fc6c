<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <script type="text/javascript" language="javascript" src="../../script/script.js"></script>
</head>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="../../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="../../beta/js/jquery.js" type="text/javascript"></script>
<script src="../../beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="../../beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="../../beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <title>${BITicketMedioControle.tituloTela}</title>

    <%-- INICIO HEADER --%>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="../../topoReduzido.jsp"/>
        </f:facet>

        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                     style="height:25px; background: url('../../imagens/fundoBarraTopo.png') repeat-x;">
            <h:outputText styleClass="tituloFormulario" value="#{BITicketMedioControle.tituloTela}">
                <h:outputLink title="Clique e saiba mais:" target="_blank" value="#{SuperControle.urlWiki}">
                    <h:graphicImage styleClass="linkWiki" url="/imagens/wiki_bco.gif"/>
                </h:outputLink>
            </h:outputText>
        </h:panelGrid>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                        <a4j:commandLink id="btnExcel"
                                         styleClass="pure-button pure-button-small"
                                         actionListener="#{BITicketMedioControle.exportarListas}"
                                         oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="3">
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="nomeApresentar=Nome,situacaoRenovacao_Apresentar=Situação,planoApresentar=Plano,previsao=Previsão,
                                         duracao=Duração,modalidades=Modalidades,planoRenovadoDate=Data da Renovação,
                                         planoRenovadoDuracao=Duração Renovada,planoRenovadoModalidades=Modalidades Renovadas"/>
                            <f:attribute name="prefixo" value="RenovacaoSintetico"/>
                            <f:attribute name="titulo" value="Renovação"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <i class="fa-icon-excel"></i> &nbsp Excel
                        </a4j:commandLink>

                        <a4j:commandLink id="btnPDF"
                                         styleClass="pure-button pure-button-small margin-h-10"
                                         actionListener="#{BITicketMedioControle.exportarListas}"
                                         oncomplete="abrirPopup('../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                         accesskey="4">
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="nomeApresentar=Nome,situacaoRenovacao_Apresentar=Situação,planoApresentar=Plano,previsao=Previsão,
                                         duracao=Duração,modalidades=Modalidades,planoRenovadoDate=Data da Renovação,
                                         planoRenovadoDuracao=Duração Renovada,planoRenovadoModalidades=Modalidades Renovadas"/>
                            <f:attribute name="prefixo" value="RenovacaoSintetico"/>
                            <f:attribute name="titulo" value="Renovação"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <i class="fa-icon-pdf"></i> &nbsp PDF
                        </a4j:commandLink>

                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tabelaTicketMedio" class="tabelaTicketMedio pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <tr>
                        <th>Código</th>
                        <th>Matrícula</th>
                        <th>Nome</th>
                        <c:choose>
                            <c:when test="${BITicketMedioControle.tipo eq 'COMPETENCIA'}">
                                <th>Contrato</th>
                            </c:when>
                            <c:when test="${BITicketMedioControle.tipo eq 'FATURAMENTO_RECEBIDO'
                                    or BITicketMedioControle.tipo eq 'RECEITA'}">
                                <th>Lançamento</th>
                            </c:when>
                        </c:choose>
                        
                        
                        <th>Valor</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>

                

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{BITicketMedioControle.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{BITicketMedioControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty BITicketMedioControle.mensagem}"
                              value=" #{BITicketMedioControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty BITicketMedioControle.mensagemDetalhada}"
                              value=" #{BITicketMedioControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="/imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>
        <a4j:status onstart="Richfaces.showModalPanel('panelStatus');"
                    onstop="Richfaces.hideModalPanel('panelStatus');"/>
    </h:panelGroup>

    <script src="../../beta/js/ext-funcs.js" type="text/javascript"></script>
     <script>
        jQuery(window).on("load", function(){
            iniciarTabela("tabelaTicketMedio", "${contexto}/prest/relatorio/ticketMedio", 1, "asc", [0], true);
        });
    </script>
 
</f:view>
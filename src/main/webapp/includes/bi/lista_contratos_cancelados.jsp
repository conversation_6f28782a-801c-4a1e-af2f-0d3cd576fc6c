<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="../include_import_minifiles.jsp"%>
</head>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="../../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="../../beta/js/jquery.js" type="text/javascript"></script>
<script src="../../beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="../../beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="../../beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <title><h:outputText value="Contratos Cancelados"/></title>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <c:set var="titulo" scope="session" value="Contratos Cancelados"/>
            <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-controle-de-operacoes-de-excecoes-adm/"/>
            <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
                    <jsp:include page="../../topo_reduzido_popUp.jsp"/>
        </f:facet>
    </h:panelGroup>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right" style="line-height: 44px;">
                        <a4j:commandLink id="btnExcel"
                                         styleClass="linkPadrao"
                                         style="margin-right: 8px"
                                         actionListener="#{RelControleOperacoesControle.exportar}"
                                         oncomplete="#{RelControleOperacoesControle.mensagemNotificar}#{RelControleOperacoesControle.msgAlert}"
                                         accesskey="3">
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="matriculaApresentar=Matrícula,nomeApresentar=Nome,contrato=Contrato,justificativa=Justificativa,colaboradorResp=Responsável,dia=Dia"/>
                            <f:attribute name="prefixo" value="Contratos Cancelados"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="btnPDF"
                                         styleClass="linkPadrao"
                                         style="margin-right: 40px"
                                         actionListener="#{RelControleOperacoesControle.exportar}"
                                         oncomplete="#{RelControleOperacoesControle.mensagemNotificar}#{RelControleOperacoesControle.msgAlert}"
                                         accesskey="4">
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="matriculaApresentar=Matrícula,nomeApresentar=Nome,contrato=Contrato,justificativa=Justificativa,colaboradorResp=Responsável,dia=Dia"/>
                            <f:attribute name="prefixo" value="Contratos Cancelados"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>

                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblContratosCancelados"
                       class="tabelaContratosCancelados pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <tr>
                        <th>CODIGO</th>
                        <th>MATRÍCULA</th>
                        <th>NOME</th>
                        <th>CONTRATO</th>
                        <th>JUSTIFICATIVA</th>
                        <th>RESPONSÁVEL</th>
                        <th>DIA</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>


                <a4j:jsFunction name="jsEditar" action="#{PendenciaControleRel.irParaTelaClienteDatatables}"
                                oncomplete="abrirPopup('../../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700)"
                                reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{PendenciaControleRel.sucesso}"
                                value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{PendenciaControleRel.erro}" value="./imagens/erro.png"/>
                <h:outputText styleClass="mensagem" rendered="#{not empty PendenciaControleRel.mensagem}"
                              value=" #{PendenciaControleRel.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada"
                              rendered="#{not empty PendenciaControleRel.mensagemDetalhada}"
                              value=" #{PendenciaControleRel.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <jsp:include page="../include_carregando_ripple.jsp" flush="true"/>
    </h:panelGroup>

    <script src="../../beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaContratosCancelados", "${contexto}/prest/relatorio/contratosCancelados", 1, "asc", "", true);
        });
    </script>
</f:view>

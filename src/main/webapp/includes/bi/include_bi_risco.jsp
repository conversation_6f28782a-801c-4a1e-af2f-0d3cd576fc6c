<%-- 
    Document   : include_bi_risco
    Created on : 20/02/2016
    Author     : <PERSON>-%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block"
              styleClass="container-bi" id="bi-grupo-risco" rendered="#{LoginControle.permissaoAcessoMenuVO.business}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.business || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.gRisco.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarGrupoRisco}" reRender="containerRisco"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         status="nenhumStatus"
                         styleClass="btn-atualizar-bi"/>

        <h:panelGroup layout="block" id="outroContainerRisco">
            <h:panelGroup layout="block" id="containerRisco">
                <h:panelGroup layout="block"
                              rendered="#{(BIControle.configuracaoBI.gRisco.apresentarBoxCarregar) && !BIControle.biCarregado}"
                              styleClass="bi-unloaded">
                    <h:panelGroup styleClass="bi-header" layout="block">
                        <h:panelGroup layout="block" styleClass="bi-panel">
                            <h:outputText value="Churn Prediction" styleClass="bi-titulo pull-left"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <div class="ghost">
                        <div class="loading" style="height: 120px"></div>

                        <h3 class="loading linha"></h3>

                        <div class="loading bloco"></div>
                        <div class="loading bloco"></div>
                        <div class="loading bloco" style="margin-right: 0"></div>
                    </div>
                </h:panelGroup>

                <h:panelGroup layout="block"
                              rendered="#{!BIControle.configuracaoBI.gRisco.apresentarBoxCarregar || BIControle.biCarregado}">
                    <h:panelGroup styleClass="bi-header" layout="block">
                        <h:panelGroup layout="block" styleClass="bi-panel">
                            <h:outputText value="Grupo de Risco" rendered="#{!BIControle.biRiscoChurn}" styleClass="bi-titulo pull-left"/>
                            <h:outputText value="Churn AI" rendered="#{BIControle.biRiscoChurn}" styleClass="bi-titulo pull-left"/>

                            <h:outputLink styleClass="linkWiki bi-cor-cinza bi-left-align tooltipster"
                                          style="float: left;margin-top: 4.4%;margin-left: 2%"
                                          value="#{SuperControle.urlBaseConhecimento}bi-grupo-de-risco-adm/"
                                          title="Clique e saiba mais: Business Intelligence Risco" target="_blank">
                                <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                            </h:outputLink>


                            <a4j:commandLink id="consultarRisco"
                                             reRender="containerRisco"
                                             rendered="#{!BIControle.biRiscoChurn}"
                                             oncomplete="montarTips();"
                                             action="#{RiscoControle.atualizar}">
                                <i title="Atualizar dados do Grupo de Risco"
                                   class="fa-icon-refresh bi-btn-refresh bi-link pull-right tooltipster lineHeight-3em"></i>
                            </a4j:commandLink>

                            <a4j:commandLink id="imprimirPDF" styleClass="botoes"
                                             rendered="#{!BIControle.biRiscoChurn}"
                                             action="#{RiscoControle.imprimirRelatorioGeral}"
                                             oncomplete="#{RiscoControle.mensagemNotificar}#{RiscoControle.msgAlert}">
                                <i class="fa-icon-print bi-btn-refresh bi-link pull-right lineHeight-3em tooltipster"
                                   title="Imprimir"></i>
                            </a4j:commandLink>


                            <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')"
                                             action="#{BIControle.biAtualizarParam}"
                                             rendered="#{!BIControle.biRiscoChurn}"
                                             reRender="formPanelFiltroCol, tituloFiltro">
                                <i title="Filtrar Por Colaborador"
                                   class="tooltipster fa-icon-user bi-btn-refresh bi-link lineHeight-3em">
                                    <span class="badgeItem2Icon" data-bagde="${BIControle.qtdColGrupoRisco}">
                                    </span>
                                </i>
                                <a4j:actionparam name="biAtualizar" value="GRUPO_RISCO"></a4j:actionparam>
                                <a4j:actionparam name="biAtualizarAbrirConsulta"
                                                 value="biAtualizarAbrirConsulta"></a4j:actionparam>
                                <a4j:actionparam name="reRenderBi" value="containerRisco"></a4j:actionparam>
                            </a4j:commandLink>

                            <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')"
                                             action="#{BIControle.biAtualizarParam}"
                                             rendered="#{BIControle.biRiscoChurn}"
                                             reRender="formPanelFiltroCol, tituloFiltro">
                                <i title="Filtrar Por Colaborador"
                                   class="tooltipster fa-icon-user bi-btn-refresh bi-link lineHeight-3em">
                                    <span class="badgeItem2Icon churn" data-bagde="${BIControle.qtdColGrupoRisco}">
                                    </span>
                                </i>
                                <a4j:actionparam name="biAtualizar" value="GRUPO_RISCO"></a4j:actionparam>
                                <a4j:actionparam name="biAtualizarAbrirConsulta"
                                                 value="biAtualizarAbrirConsulta"></a4j:actionparam>
                                <a4j:actionparam name="reRenderBi" value="containerRisco"></a4j:actionparam>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGroup>

                    <!-- CHURN -->
                    <style>
                        .risco-churn{
                            padding: 16px;
                            padding-bottom: 32px;
                        }
                        .progress-container {
                            width: 100%;
                            margin-top: 24px;
                        }
                        .progress-label {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 5px;
                        }
                        .progress-label span {
                            font-weight: 700;
                            font-size: 12px;
                            line-height: 15px;
                            font-family: Poppins, sans-serif;
                            color: #494B50;
                        }
                        .progress-label a {
                            color: #1E60FA;
                            text-decoration: none;
                            font-family: Poppins;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: 14px;
                        }
                        .progress-label a:hover {
                            text-decoration: none;
                        }
                        .progress-bar {
                            display: grid;
                            grid-template-columns: 1fr 60px;
                        }
                        .progress-bar-inner {
                            height: 12px;
                            background-color: #e0e0e0;
                            border-radius: 10px;
                            overflow: hidden;
                        }
                        .progress-z {
                            height: 100%;
                            border-radius: 10px;
                        }

                        .z-seguranca{
                            background-color: #04AF04;
                        }
                        .z-atencao{
                            background-color: #E2B736;
                        }
                        .z-risco{
                            background-color: #C99E1D;
                        }
                        .z-critica{
                            background-color: #AF0404;
                        }
                        .z-despedida{
                            background-color: #7D0303;
                        }

                        .progress-percentage {
                            font-family: Poppins;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: 14px;
                            display: grid;
                            color: #55585E;
                            padding-left: 10px;
                            text-align: right;
                        }
                        .bi-titulo {
                            font-family: Poppins, sans-serif;
                        }
                        .assertividade span{
                            font-family: Nunito Sans;
                            font-size: 14px;
                            font-weight: 400;
                            line-height: 17.5px;
                            text-align: left;
                            color: #797D86;
                        }
                        .assertividade i{
                            font-size: 16px;
                            color: #797D86;
                        }
                        .assertividade{
                            padding: 8px 16px 8px 16px;
                            gap: 8px;
                            border-radius: 5px;
                            background-color: #FAFAFA;
                            display: grid;
                            grid-template-columns: auto 1fr;
                        }
                        .churn.badgeItem2Icon:after {
                            right: 36px;
                        }
                    </style>

                    <h:panelGroup rendered="#{BIControle.biRiscoChurn}" layout="block" styleClass="risco-churn">

                        <div class="assertividade">
                            <i class="pct pct-info"></i> <span>Zonas geradas pela nossa IA.</span>
                        </div>

                        <c:forEach var="zonaRisco" items="#{RiscoControle.listaRiscoChurn}">
                            <div class="progress-container">
                                <div class="progress-label">
                                    <span>${zonaRisco.zona.descricao}</span>
                                    <a onclick="listaChurn('${zonaRisco.zona}')"  href="#">${zonaRisco.clientes} clientes</a>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-inner">
                                        <div class="progress-z z-${zonaRisco.zona}" style="width: ${zonaRisco.percentual}%"></div>
                                    </div>
                                    <div class="progress-percentage">${zonaRisco.percentualFormatado}%</div>
                                </div>
                            </div>
                        </c:forEach>


                        <a4j:jsFunction name="listaChurn"
                                        action="#{RiscoControle.mostrarListaClientesRiscoChurn}"
                                        oncomplete="abrirPopup('riscoForm.jsp', 'Lista Risco Churn',980 ,700);"
                                        reRender="mensagem">
                            <a4j:actionparam name="tipo" assignTo="#{RiscoControle.zonaListagem}"/>
                        </a4j:jsFunction>


                    </h:panelGroup>

                    <h:panelGroup rendered="#{!BIControle.biRiscoChurn}" layout="block" styleClass="gr-container-resultado">

                        <h:panelGroup layout="block" styleClass="gr-resultado">
                            <h:outputText style="display: inline;" styleClass="fa-icon-warning-sign gr-text-warning"/>
                            <h:outputText style="display: inline;" styleClass="gr-text-warning bi-font-family"
                                          value="  Entre em contato urgente com esses alunos!"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-resultado">
                            <h:outputText style="display: inline;"
                                          styleClass="gr-text-warning bi-font-family bi-font-bold"
                                          value="#{RiscoControle.percentualRisco}%"/>
                            <h:outputText style="display: inline;" styleClass="bi-table-text"
                                          value=" em ALTO risco de n�o renovar"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-resultado">
                            <a4j:commandLink styleClass="gr-text-warning bi-font-family bi-font-bold"
                                             action="#{RiscoControle.mostrarListaClientesSemContato}"
                                             style="display: inline-block;vertical-align: top"
                                             oncomplete="abrirPopup('riscoForm.jsp', 'Risco', 1024, 595);"
                                             value="#{RiscoControle.percentualContatoRisco}%"/>
                            <h:outputText styleClass="bi-table-text textoImcompleto tooltipster"
                                          style="width: 80%;display: inline-block;margin-left: 2px"
                                          title="n�o receberam contato de risco nos �ltimos 15 dias"
                                          value=" n�o receberam contato de risco nos �ltimos 15 dias"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{!BIControle.biRiscoChurn}"  styleClass="group-header" layout="block">
                        <h:outputText styleClass="bi-table-text bi-font-bold" value="Clientes em risco muito alto"/>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{!BIControle.biRiscoChurn}"  layout="block" styleClass="gr-container-totalizador">
                        <h:panelGroup layout="block" styleClass="gr-totalizador">
                            <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text"
                                          value="Peso 6"/>
                            <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                             id="btnMostrarVinculos1"
                                             action="#{RiscoControle.mostrarListaClientesPeso6}"
                                             oncomplete="abrirPopup('riscoForm.jsp', 'Risco', 1024, 595);"
                                             value="#{RiscoControle.totalRisco6} "/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="gr-totalizador">
                            <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text"
                                          value="Peso 7"/>
                            <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                             id="btnMostrarVinculos"
                                             action="#{RiscoControle.mostrarListaClientesPeso7}"
                                             oncomplete="abrirPopup('riscoForm.jsp', 'Risco', 1024, 595);"
                                             value="#{RiscoControle.totalRisco7}"/>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="gr-totalizador">
                            <h:outputText style="display: block;" styleClass="bi-font-family bi-table-text"
                                          value="Peso 8"/>
                            <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                             action="#{RiscoControle.mostrarListaClientesPeso8}"
                                             oncomplete="abrirPopup('riscoForm.jsp', 'Risco', 1024, 595);"
                                             value="#{RiscoControle.totalRisco8} "/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{!BIControle.biRiscoChurn}"  layout="block" styleClass="bi-panel">
                        <a4j:commandLink id="verMaisGR"
                                         action="#{RiscoControle.consultarClienteEmRisco}"
                                         styleClass="bi-btn-refresh bi-link pull-right"
                                         oncomplete="abrirPopup('riscoForm.jsp', 'Risco', 1024, 595);">
                            Ver Mais &nbsp<i title="Ver mais Clientes em Risco" class="tooltipster fa-icon-plus-sign "></i>
                        </a4j:commandLink>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>

<%@ taglib prefix="a4" uri="http://richfaces.org/a4j" %>
<%@include file="imports.jsp" %>

<style>
    .slide_wrapper .item {
        height: 600px;
        text-align: center;
        width: 100%;
    }

    .slide_wrapper {
        height: 600px;
        width: 100%;
        vertical-align: top;
        color: #777777;
        position: relative;
        overflow: hidden;
    }

    .pessoas .pessoa {
        vertical-align: top;
        display: inline-block;
        margin: 10px 20px;
    }

    .pessoas .pessoa .foto img {
        width: 100%;
    }

    .pessoas .pessoa .foto {
        width: 140px;
        height: 140px;
        display: block;
        overflow: hidden;
        border-radius: 50%;
    }

    .pessoas .pessoa .nome {
        width: 140px;
        font-size: 16px;
        display: block;
        text-align: center;
        overflow: hidden;
        overflow-wrap: break-word;
        font-weight: 900;
        margin-top: 20px;
    }

    .mesmafamilia {
        margin: 25px 0 15px 0px;
        display: block;
        font-size: 27px;
    }

    .slide_wrapper .causa {
        margin-bottom: 30px;
        font-size: 14px;
        display: block;
    }

    .resp a span {
        color: #474747;
        margin-top: 20px;
        font-size: 18px;
        display: block;
    }

    .resp a i {
        display: block;
        font-size: 40px;
    }

    .resp:hover {
        background-color: #e6e6e6;
    }

    .resposta {
        margin-top: 50px;
    }

    .resp {
        padding: 10px 0 10px 0;
        margin-top: 30px;
        display: inline-block;
        margin-left: 2%;
        width: 30%;
    }

    .controle {
        color: #b1b1b1 !important;
        font-size: 80px;
        position: absolute;
        top: 250px;
    }

    .controle.esq {
        left: 0;
    }

    .controle.dir {
        right: 0;
    }

    .pessoas .pessoa.tres .foto {
        width: 140px;
        height: 140px;
    }

    .pessoas .pessoa.tres .nome {
        width: 140px;
        font-size: 15px;
    }

    .pessoas .pessoa.quatro .foto {
        width: 120px;
        height: 120px;
    }

    .pessoas .pessoa.quatro .nome {
        width: 120px;
        font-size: 13px;
    }


</style>
<h:panelGroup id="formSugestaoFamilia" styleClass="font-size-Em-max">
    <div class="slide_wrapper">
        <h:panelGroup layout="block" styleClass="item">

            <span class="mesmafamilia">S�o da mesma fam�lia?</span>
            <h:outputText styleClass="causa" value="#{msg_aplic[BIFamiliaControle.causaSugestaoEnum]}"/>

            <h:panelGroup layout="block" styleClass="pessoas">
                <a4:repeat value="#{BIFamiliaControle.sugestao.integrantes}" var="i">
                    <h:panelGroup layout="block" styleClass="pessoa #{BIFamiliaControle.classe}">
                        <h:panelGroup styleClass="foto">
                            <h:graphicImage value="#{i.urlFoto}"/>
                        </h:panelGroup>
                        <span class="nome">
                                <h:outputText style="text-transform: capitalize" value="#{i.nome}"/>
                            </span>
                    </h:panelGroup>
                </a4:repeat>
            </h:panelGroup>

            <div class="resposta">


                <div class="resp">
                    <a4j:commandLink action="#{BIFamiliaControle.confirmarSugestao}" reRender="caixabifamilia">
                        <i class="fa-icon-thumbs-up " style="color: green"></i>
                        <span>Sim</span>
                    </a4j:commandLink>
                </div>


                <div class="resp" >
                    <a4j:commandLink action="#{BIFamiliaControle.negarSugestao}" reRender="caixabifamilia">
                        <i class="fa-icon-ban-circle" style="color: red"></i>
                        <span>N�o</span>
                    </a4j:commandLink>
                </div>


                <div class="resp">
                    <a4j:commandLink action="#{BIFamiliaControle.proxima}" reRender="formSugestaoFamilia">
                        <i class="fa-icon-question-sign" style="color: gray"></i>
                        <span>N�o sei</span>
                    </a4j:commandLink>
                </div>


            </div>


        </h:panelGroup>

        <a4j:commandLink styleClass="controle esq" action="#{BIFamiliaControle.anterior}"
                         reRender="formSugestaoFamilia">
            <i class="fa-icon-angle-left"></i>
        </a4j:commandLink>

        <a4j:commandLink styleClass="controle dir" action="#{BIFamiliaControle.proxima}" reRender="formSugestaoFamilia">
            <i class="fa-icon-angle-right"></i>
        </a4j:commandLink>
    </div>

</h:panelGroup>




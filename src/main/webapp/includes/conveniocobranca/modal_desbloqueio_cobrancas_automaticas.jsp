<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../../includes/imports.jsp" %>

<rich:modalPanel id="modalDesbloqueioCobrancasAutomaticas" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="600"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Desbloqueio das cobran�as autom�ticas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkModalDesbloqueioCobrancasAutomaticas"/>
            <rich:componentControl for="modalDesbloqueioCobrancasAutomaticas"
                                   attachTo="hidelinkModalDesbloqueioCobrancasAutomaticas"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalDesbloqueioCobrancas" ajaxSubmit="true">
        <h:panelGroup layout="block" style="padding: 15px">

            <div style="text-align: center">
                <h:graphicImage value="/faces/imagens/atencaoRisco.gif"
                                width="30"
                                style="font-size: 40px;"
                                styleClass="texto-size-30"/>
            </div>

            <h:panelGroup layout="block" id="panelTitleDesbloqueio"
                          style="padding-top: 7px;padding-bottom: 10px; text-align: center">
                <h:outputText value="ATEN��O"
                              style="vertical-align: middle;"
                              styleClass="texto-size-20 cinza texto-bold"/>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          style="padding-top: 10px;padding-bottom: 10px;">
                <h:outputText value="Voc� est� desbloqueando as cobran�as autom�ticas."
                              style="vertical-align: middle;"
                              styleClass="texto-cor-cinza texto-size-20"/>
                </br>
                <h:outputText
                        value="Fique ciente que o nosso sistema ir� cobrar o seguinte montante de parcelas abaixo j� na pr�xima tentativa autom�tica de cobran�a:"
                        style="vertical-align: middle;"
                        styleClass="texto-cor-cinza texto-size-20"/>

            </h:panelGroup>


            <h:panelGrid id="infoParcelas" styleClass="textsmall" columns="1">
                <rich:dataTable id="tblinfoParcelas" var="parcela" rows="10" rowKeyVar="status"
                                value="#{ConvenioCobrancaControle.movParcelaVOS}"
                                rendered="#{ConvenioCobrancaControle.movParcelaVOS_Size > 0}">
                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column sortBy="#{parcela.pessoa.nome}">
                        <f:facet name="header">
                            <h:outputText value="Aluno:"/>
                        </f:facet>
                        <h:outputText value="#{parcela.pessoa.nome}"/>
                    </rich:column>

                    <rich:column sortBy="#{parcela.descricao}">
                        <f:facet name="header" >
                            <h:outputText value="Parcela:"/>
                        </f:facet>
                        <h:outputText value="#{parcela.descricao}"/>
                    </rich:column>

                    <rich:column sortBy="#{parcela.valorParcela}">
                        <f:facet name="header" >
                            <h:outputText value="Valor:"/>
                        </f:facet>
                        <h:outputText value="R$#{parcela.valorParcela_Apresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{parcela.dataVencimento}">
                        <f:facet name="header">
                            <h:outputText value="Vencimento:"/>
                        </f:facet>
                        <h:outputText value="#{parcela.dataVencimento}"/>
                    </rich:column>

                    <rich:column sortBy="#{parcela.empresa.nome}">
                        <f:facet name="header">
                            <h:outputText value="Empresa:"/>
                        </f:facet>
                        <h:outputText value="#{parcela.empresa.nome}"/>
                    </rich:column>
                </rich:dataTable>

                <rich:datascroller align="center" for="tblinfoParcelas" maxPages="10"
                                   rendered="#{ConvenioCobrancaControle.movParcelaVOS_Size > 0}"
                                   id="sctbltblinfoParcelas"/>

                <rich:spacer height="10"/>

                <h:outputText value="Total de parcelas: #{ConvenioCobrancaControle.movParcelaVOS_Size}"
                              rendered="#{ConvenioCobrancaControle.movParcelaVOS_Size > 0}"
                              styleClass="texto-cor-cinza texto-size-20 tooltipster"
                              title="Total em quantidade de parcelas que ser�o cobradas j� na pr�xima tentativa de cobran�a que o sistema realizar automaticamente."/>
                <h:outputText value="Total em valor: R$ #{ConvenioCobrancaControle.totalMovParcela}"
                              rendered="#{ConvenioCobrancaControle.movParcelaVOS_Size > 0}"
                              styleClass="texto-cor-cinza texto-size-20 tooltipster"
                              title="Total em valor que ser� cobrado j� na pr�xima tentativa de cobran�a que o sistema realizar automaticamente."/>
            </h:panelGrid>

            <h:panelGrid id="infoParcelasVazias" styleClass="textsmall" columns="1"
                         rendered="#{ConvenioCobrancaControle.movParcelaVOS_Size == 0}">
                <h:outputText value="Nenhuma parcela deste conv�nio foi encontrada para cobrar j� na pr�xima tentativa de cobran�a."
                              styleClass="texto-cor-cinza texto-size-20"/>
            </h:panelGrid>

            <div style="padding-top: 20px; text-align-last: center;">
                <a4j:commandLink id="btnVerificaCartaoMesmo" value="Continuar"
                                 styleClass="botoes nvoBt"
                                 action="#{ConvenioCobrancaControle.gravarDesbloqueioCobrancas}"
                                 oncomplete="#{ConvenioCobrancaControle.abrirFecharModalTokenOperacao}#{ConvenioCobrancaControle.msgAlert}#{ConvenioCobrancaControle.mensagemNotificar}"
                                 reRender="form, modalTokenOperacao"/>

                <a4j:commandLink id="btnVerificaCartaoCancelarVendaPlano" value="Cancelar"
                                 action="#{ConvenioCobrancaControle.naoDesbloquearCobrancas}"
                                 styleClass="botoes nvoBt btSec"
                                 oncomplete="Richfaces.hideModalPanel('modalDesbloqueioCobrancasAutomaticas')"
                                 reRender="form"/>
            </div>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>



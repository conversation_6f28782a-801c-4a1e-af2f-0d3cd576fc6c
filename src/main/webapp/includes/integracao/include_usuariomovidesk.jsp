<%-- 
    Document   : include_usuariomovel
    Created on : 01/09/2013, 11:01:53
    Author     : Waller
--%>
<%@ page contentType="text/html;charset=UTF-8" pageEncoding="ISO-8859-1" language="java" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<h:panelGrid id="panelUsuarioMovidesk" columns="1" width="100%" headerClass="subordinado"
             columnClasses="colunaCentralizada">

    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

        <f:facet name="header">
            <h:outputText styleClass="titulo3" escape="false"
                          value="Dados do usuário no MOVIDESK"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Colaborador_UsuarioMovidesk_email} "/>
        <h:panelGroup>
            <h:outputText id="emailUsuarioMovidesk" styleClass="form" style="padding-left: 15px"
                          value="#{MovideskControle.colaboradorVO.emailMovidesk}">
            </h:outputText>
        </h:panelGroup>

    </h:panelGrid>

</h:panelGrid>
<%-- 
    Document   : include_usuariomovel
    Created on : 01/09/2013, 11:01:53
    Author     : Waller
--%>
<%@ page contentType="text/html;charset=UTF-8" pageEncoding="ISO-8859-1" language="java" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<h:panelGrid id="panelUsuarioMovel" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
        <h:outputText  styleClass="titulo3" escape="false" value="" />
        <c:if test="${ColaboradorControle.editarUsuarioMovel}">
            <h:outputText  styleClass="titulo3" escape="false" value="Defina as credenciais do usuário para Interagir com outros aplicativos" />
        </c:if>
        <c:if test="${!ColaboradorControle.editarUsuarioMovel}">
            <h:outputText  styleClass="mensagemDetalhada" escape="false" value="Sincronização desses dados deve ser feita no cadastro de usuário" />
        </c:if>
        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Cliente_UsuarioMovel_nomeUsuario}" />
        <h:panelGroup>
            <h:inputText disabled="#{!ColaboradorControle.editarUsuarioMovel}" id="nomeUsuarioMovel" size="50" maxlength="200"  onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form" value="#{UsuarioMovelControle.uMovel.nome}">
            </h:inputText>
        </h:panelGroup>

        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Cliente_UsuarioMovel_senha}" />
        <h:panelGroup>
            <h:inputSecret disabled="#{!ColaboradorControle.editarUsuarioMovel}" id="senhaUsuarioMovel"  size="50" maxlength="200"  onblur="blurinput(this);"
                           autocomplete="off"
                           onfocus="focusinput(this);"
                           styleClass="form" value="#{UsuarioMovelControle.uMovel.senha}">
            </h:inputSecret>
        </h:panelGroup>

        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Cliente_UsuarioMovel_ativo}" />
        <h:panelGroup>
            <h:selectBooleanCheckbox id="statusUsuarioMovel"  onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form" value="#{UsuarioMovelControle.uMovel.ativo}"/>
        </h:panelGroup>

        <h:panelGrid columns="1" width="100%">
            <h:outputText id="msgUsuarioMovelDet" styleClass="mensagemDetalhada" value="#{UsuarioMovelControle.mensagemDetalhada}"/>
        </h:panelGrid>

    </h:panelGrid>
</h:panelGrid>
<%-- 
    Document   : include_pagamentodigital
    Created on : 20/07/2011, 12:55:28
    Author     : waller
--%>

<%@page import="java.net.URLDecoder"%>
<%@page import="java.io.OutputStreamWriter"%>
<%@page import="java.net.HttpURLConnection"%>
<%@page import="java.net.URLConnection"%>
<%@page import="java.net.URL"%>
<%@page import="java.net.URLEncoder"%>
<%@page import="java.io.PrintWriter"%>
<%@page import="java.io.DataInputStream"%>
<%@page import="java.io.InputStreamReader"%>
<%@page import="java.io.BufferedReader"%>
<%@page import="controle.financeiro.MovPagamentoControle"%>

<%
            MovPagamentoControle movPagControl = (MovPagamentoControle) request.getSession().getAttribute("MovPagamentoControle");
            if ((request.getParameter("id_transacao") != null)
                    && (movPagControl != null)) {
                request.setCharacterEncoding("iso-8859-1");

                String token = movPagControl.getTokenPagamentoDigital();
                String id_transacao = request.getParameter("id_transacao");
                /*String data_transacao = request.getParameter("data_transacao");
                String data_credito = request.getParameter("data_credito");*/
                String valor_original = request.getParameter("valor_original");
                String valor_loja = request.getParameter("valor_loja");
                /*String valor_total = request.getParameter("valor_total");
                String desconto = request.getParameter("desconto");
                String acrescimo = request.getParameter("acrescimo");
                String tipo_pagamento = request.getParameter("tipo_pagamento");
                String parcelas = request.getParameter("parcelas");
                String cliente_nome = request.getParameter("cliente_nome");
                String cliente_email = request.getParameter("cliente_email");
                String cliente_rg = request.getParameter("cliente_rg");
                String cliente_data_emissao_rg = request.getParameter("cliente_data_emissao_rg");
                String cliente_orgao_emissor_rg = request.getParameter("cliente_orgao_emissor_rg");
                String cliente_estado_emissor_rg = request.getParameter("cliente_estado_emissor_rg");
                String cliente_cpf = request.getParameter("cliente_cpf");
                String cliente_sexo = request.getParameter("cliente_sexo");
                String cliente_data_nascimento = request.getParameter("cliente_data_nascimento");
                String cliente_endereco = request.getParameter("cliente_endereco");
                String cliente_complemento = request.getParameter("cliente_complemento");*/
                String status = URLDecoder.decode(request.getParameter("cod_status"), "iso-8859-1");
                /*String cod_status = request.getParameter("cod_status");
                String cliente_bairro = request.getParameter("cliente_bairro");
                String cliente_cidade = request.getParameter("cliente_cidade");
                String cliente_estado = request.getParameter("cliente_estado");
                String cliente_cep = request.getParameter("cliente_cep");
                String frete = request.getParameter("frete");
                String tipo_frete = request.getParameter("tipo_frete");
                String informacoes_loja = request.getParameter("informacoes_loja");
                String id_pedido = request.getParameter("id_pedido");
                String free = request.getParameter("free");

                MovPagamentoControle movPagControl = (MovPagamentoControle) request.getSession().getAttribute("MovPagamentoControle");
                if ((request.getParameter("id_transacao") != null)
                && (movPagControl != null)) {
                request.setCharacterEncoding("iso-8859-1");

                String qtde_produtos = request.getParameter("qtde_produtos");*/

                String enderecoPost = "http://www.pagamentodigital.com.br/checkout/verify/";

                String params = "?token="
                        + URLEncoder.encode(token, "iso-8859-1")
                        + "&transacao="
                        + URLEncoder.encode(id_transacao.trim(),
                        "iso-8859-1")
                        + "&cod_status="
                        + URLEncoder.encode(status.trim(), "iso-8859-1")
                        + "&valor_original="
                        + URLEncoder.encode(valor_original.trim(),
                        "iso-8859-1")
                        + "&valor_loja="
                        + URLEncoder.encode(valor_loja.trim(), "iso-8859-1");

                URL url = new URL(enderecoPost + params);

                URLConnection connection = url.openConnection();
                ((HttpURLConnection) connection).setRequestMethod("GET");

                connection.setDoOutput(true);
                connection.setDoInput(true); //para esperar resposta
                connection.setUseCaches(false); //desabilitar cache

                PrintWriter output = new PrintWriter(
                        new OutputStreamWriter(connection.getOutputStream()));

                output.flush();
                output.close();

                DataInputStream inStream = new DataInputStream(connection.getInputStream());

                // pega  a resposta

                String buffer;
                boolean verificou = false;
                String mensagemErro = "";
                BufferedReader d = new BufferedReader(
                        new InputStreamReader(inStream));
                while ((buffer = d.readLine()) != null) {
                    System.out.println(buffer);
                    if (buffer.contains("VERIFICADO")) {
                        verificou = true;
                    } else {
                        mensagemErro += buffer;
                    }
                }
                inStream.close();

                if (verificou) {
                    movPagControl.setApresentarPagamentoDigital(false);
                    movPagControl.concluirPagamentoComSucesso();
                    //movPagControl.verificarUsuarioSenhaResponsavelPagamento();
                    //movPagControl.setAbrirRichModalMensagem(true);
                    request.getSession().setAttribute(
                            "MovPagamentoControle", movPagControl);

                } else {
                    movPagControl.setErro(true);
                    movPagControl.setMensagemDetalhada("Erro na transa��o. "
                            + "Pagamento Digital Retornou -> "
                            + mensagemErro);
                    request.getSession().setAttribute(
                            "MovPagamentoControle", movPagControl);
                }
            }
%>
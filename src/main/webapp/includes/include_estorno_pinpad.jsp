<h:panelGroup id="painelEstornoPinpad">

    <h:inputHidden id="respostaPinpad"
                   value="#{EstornoReciboControle.pinPadPedidoVO.paramsRespCancel}"/>

    <script>

        function estornoGetcard() {
            try {
                alterarMsgModal('Iniciando TEF');
                exibirBotaoAbortarEstornoTrue()
                console.log('Entrei estornoGetcard');

                console.log('Montei a URL de chamada:')
                console.log('${EstornoReciboControle.urlServicoGetCardScope}/CancelarTransacaoASync');
                const data = '${EstornoReciboControle.bodyEstornoPinPadGetCard}';
                console.log('Vou usar o seguinte body na chamada:')
                console.log(data)

                jQuery.ajax({
                    type: "POST",
                    url: '${EstornoReciboControle.urlServicoGetCardScope}/CancelarTransacaoASync',
                    dataType: "json",
                    data: '${EstornoReciboControle.bodyEstornoPinPadGetCard}',
                    success: function (data) {
                        console.log('estornoGetcard --> success');
                        console.log(data);

                        try {
                            gravarLogGetCard('estornoGetcard - success', JSON.stringify(data));
                        } catch (ex) {
                            console.log(ex);
                        }

                        var idTransacao = data.idTransacao;
                        salvarIdExternoGetCard(idTransacao);

                        alterarMsgModal('Iniciando TEF');

                        console.log('estornoGetcard --> fim sucesso');
                    },
                    error: function (request, status, error) {
                        console.log('estornoGetcard --> error');
                        console.log(request);
                        console.log(status);
                        console.log(error);

                        try {
                            gravarLogGetCard('estornoGetcard - error', request);
                        } catch (ex) {
                            console.log(ex);
                        }
                        alterarMsgModal('Erro...');
                        erroGetCard('Falha ao inicializar GetCard, tente novamente e caso persista verifique se o programa controladorScope est� inicializado. IEP');
                    },
                    async: false
                });
            } catch (ex) {
                console.log(ex);
            }
        }

        function abortarOperacaoGetcard() {
            try {
                exibirBotaoAbortarEstornoFalse()
                console.log('Entrei abortarOperacaoGetcard');

                jQuery.ajax({
                    type: "POST",
                    url: '${EstornoReciboControle.urlServicoGetCardScope}/AbortarTransacao',
                    dataType: "json",
                    success: function (data) {
                        console.log('abortarOperacaoGetcard --> success');
                        console.log(data);

                        try {
                            gravarLogGetCard('abortarOperacaoGetcard - success', JSON.stringify(data));
                        } catch (ex) {
                            console.log(ex);
                        }

                        console.log('abortarOperacaoGetcard --> fim sucesso');
                    },
                    error: function (request, status, error) {
                        console.log('abortarOperacaoGetcard --> error');
                        console.log(error);
                        console.log(request);

                        try {
                            gravarLogGetCard('abortarOperacaoGetcard - error', request);
                        } catch (ex) {
                            console.log(ex);
                        }

                        var mensagem = '';
                        try {
                            mensagem = request.responseText;
                            var obj = JSON.parse(request.responseText);
                            mensagem = obj.mensagem;
                        } catch (ex) {
                            console.log('Erro GETCARD [converterJSON] ' + ex);
                        }
                        Notifier.error(mensagem, "Erro abortar GetCard");
                    },
                    async: false
                });
            } catch (ex) {
                console.log(ex);
            }
        }

        function consultarGetcard() {
            try {
                console.log('Entrei consultarGetcard');

                jQuery.ajax({
                    type: "POST",
                    url: '${EstornoReciboControle.urlServicoGetCardScope}/ResultadoTransacao?idTransacao=${EstornoReciboControle.pinPadPedidoVO.idExternoCancel}',
                    dataType: "json",
                    success: function (data) {
                        console.log('consultarGetcard --> success');
                        console.log(data);

                        try {
                            gravarLogGetCard('consultarGetcard - success', JSON.stringify(data));
                        } catch (ex) {
                            console.log(ex);
                        }

                        try {
                            if (data.response.Sucesso === true) {
                                console.log('consultarGetcard | sucesso concluirEstornoPinpadGetCard');
                                concluirEstornoPinpadGetCard(JSON.stringify(data));
                            } else if (data.response.Sucesso === false &&
                                data.response.Processando === false) {
                                erroGetCard(JSON.stringify(data));
                                console.log('consultarGetcard | erroGetCard ');
                            } else {
                                if (data.response.MensagemOperador) {
                                    alterarMsgModal(data.response.MensagemOperador.replace("Cartao Credito", "").replace("Cartao Debito", "").replace("Cancelamento", ""));
                                }
                                console.log('consultarGetcard | else ');
                            }

                        } catch (ex) {
                            console.log('Erro obter mensagem consultarGetcard GETCARD: ' + ex);
                        }
                        console.log('consultarGetcard --> fim sucesso');
                    },
                    error: function (request, status, error) {
                        console.log('consultarGetcard --> error');
                        console.log(error);
                        console.log(status);
                        console.log(request);

                        try {
                            gravarLogGetCard('consultarGetcard - error', request);
                        } catch (ex) {
                            console.log(ex);
                        }

                        var mensagem ='Falha consultar GetCard';
                        try {
                            mensagem = request.responseText;
                            var obj = JSON.parse(request.responseText);
                            mensagem = obj.response.Mensagem;
                            alterarMsgModal(mensagem);
                        } catch (ex) {
                            console.log('consultarGetcard -- > Erro GETCARD [converterJSON] ' + ex);
                        }

                        Notifier.error(mensagem, "Erro consultar GetCard");
                    },
                    async: false
                });
            } catch (ex) {
                console.log(ex);
            }
        }

        function alterarMsgModal(msg) {
            try {
                console.log('alterarMsgModal --> ' + msg)
                document.getElementById('formModalEstornoGetcard:mensagemPinPad').innerHTML = msg;
            } catch (ex) {
                console.log(ex);
            }
        }
    </script>
</h:panelGroup>

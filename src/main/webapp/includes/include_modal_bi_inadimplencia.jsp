<%@include file="imports.jsp" %>

<style>
    .classEsquerdaInadim {
        width: 10%;
        text-align: right;
        font-weight: bold;
        vertical-align: middle;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        line-height: normal;
        text-transform: none;
        color: #333;
        line-height: 150%;
    }

    .classDireitaInadim {
        width: 65%;
        text-align: left;
    }

    .margin-left-right-7-inadimplencia{
        margin-right: 7px;
        margin-left: 7px;
        height: 16px;
        width: 16px;
        margin-top: 9px;
    }

</style>

<rich:modalPanel id="modalExportarBiInadimplencia"
                 styleClass="novaModal"
                 width="600" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Exportar agrupado pelo resultado da �ltima tentativa de cobran�a"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelModalExportarBiInadimplencia"/>
            <rich:componentControl for="modalExportarBiInadimplencia"
                                   attachTo="hidelModalExportarBiInadimplencia" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formExportaBiInaNomd">

        <h:panelGroup layout="block" id="panelGeralModalBiInadimple"
                      style="align-items: center; padding: 10px;">

            <h:panelGrid columns="2" columnClasses="classEsquerdaInadim, classDireitaInadim" width="100%">

                <h:outputText styleClass="tituloCampos"
                              value="Per�odo:"/>
                <h:panelGroup layout="block" style="display: flex; align-items: center;">
                    <div class="dateTimeCustom tooltipster">
                        <rich:calendar id="dataInicioExportarBiinad"
                                       value="#{BIInadimplenciaControle.dataInicioExportar}"
                                       inputSize="10"
                                       buttonIcon="/imagens_flat/icon-calendar-check.png"
                                       buttonClass="margin-left-right-7-inadimplencia"
                                       inputClass="forcarSemBorda"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       zindex="2"
                                       showWeeksBar="false">
                            <a4j:support event="onchanged"
                                         action="#{BIInadimplenciaControle.alterouDataInicioExportar}"
                                         reRender="formExportaBiInaNomd"/>
                        </rich:calendar>
                    </div>

                    <h:outputText styleClass="tituloCampos"
                                  style="padding: 0 10px 0 8px;"
                                  value="at�"/>

                    <div class="dateTimeCustom tooltipster">
                        <rich:calendar id="dataFinalExportarBiIna"
                                       value="#{BIInadimplenciaControle.dataFinalExportar}"
                                       inputSize="10"
                                       buttonIcon="/imagens_flat/icon-calendar-check.png"
                                       buttonClass="margin-left-right-7-inadimplencia"
                                       inputClass="forcarSemBorda"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </div>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos"
                              value="Exportar:"/>
                <h:panelGroup layout="block" style="display: flex; align-items: center;">
                    <div class="cb-container"
                         style="font-size: 11px !important;">
                        <h:selectOneMenu styleClass="form"
                                         value="#{BIInadimplenciaControle.tipoParcelaDetalhado}">
                            <f:selectItems
                                    value="#{BIInadimplenciaControle.selectItemTipoParcelaDetalhado}"/>
                        </h:selectOneMenu>
                    </div>
                </h:panelGroup>
            </h:panelGrid>

            <h:panelGroup layout="block" style="padding-top: 15px; text-align: left">
                <h:outputText value="Este relat�rio te permite saber o total de inadimpl�ncia m�s a m�s,
                de forma detalhada, agrupando pela �ltimo retorno da tentativa de cobran�a da parcela.
                S� possui dados � partir do m�s 05/2020. Caso queira gerar dados antes disso,
                entre em contato com a Pacto para que possamos process�-los."
                              style="font-style: italic"
                              styleClass="texto-size-18 cinza"/>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          rendered="#{BIInadimplenciaControle.apresentarMensagemReprocessar}"
                          style="padding-top: 15px; text-align: left">
                <h:outputText
                        value="Obs: Voc� selecionou um m�s antes de 05/2020, voc� s� ter� dados caso a PACTO tenha gerado pra voc� conforme mencionado na mensagem anterior."
                        style="color: #FF5555"
                        styleClass="texto-size-18 cinza"/>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" style="width: 100%; text-align: center; padding: 10px 0 10px 0;">
            <a4j:commandButton id="exportarExcelPlanilhaAgrupadaBiInadimp"
                               styleClass="botoes nvoBt"
                               value="Exportar"
                               action="#{BIInadimplenciaControle.exportarExcelPlanilhaAgrupada}"
                               oncomplete="#{BIInadimplenciaControle.mensagemNotificar};#{BIInadimplenciaControle.onComplete}">
            </a4j:commandButton>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>


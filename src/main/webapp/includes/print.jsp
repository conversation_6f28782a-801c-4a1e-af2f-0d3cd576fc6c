<script src="${root}/script/print/print.js" type="text/javascript" ></script>
<script src="${root}/script/print/html2canvas.js" type="text/javascript" ></script>
<script src="${root}/script/print/htmlminifier.min.js" type="text/javascript" ></script>

<script>
    var contexto = "${pageContext.request.contextPath}";
    var chave = "${SuperControle.key}";
    var limitPrintsAcoesUsuario = ${PrintScreenControle.limitePrintsAcoesUsuario};
    var gerarPrintsAcoesUsuario = ${PrintScreenControle.gerarPrintsAcoesUsuario};
    var empresa = ${LoginControle.empresaLogado.codigo};
    var usuario = ${LoginControle.usuarioLogado.codigo};
</script>
<%-- 
    Document   : include_bi_grafico_ltv
    Created on : 30/03/2020, 08:50:29
    Author     : <PERSON><PERSON>
--%>
<%@include file="imports.jsp" %>
<style>
    .fontePorcentagem {
        font-family: Arial;
        font-style: normal;
        font-size: 18px;
        line-height: 16px;
        text-align: center;
        color: #000000;
    }

    .fonteSecundaria {
        font-family: Arial;
        font-style: normal;
        font-size: 12px;
        text-align: center;
        color: #9D9D9D;
    }

    .negrito {
        font-weight: bold;
    }

    .tabela td {
        border-left: solid 1px #E5E5E5;
        border-bottom: solid 1px #E5E5E5;
        text-align: center;
    }

    tr {
        border: solid 1px #E5E5E5;
    }
    .quadroComparativo{
        height: 45px; float: left; width: 244px; text-align: center; padding-top: 20px; padding-left: 50px; border-right: solid 1px #E5E5E5;
    }

</style>
<rich:modalPanel id="panelGraficoBiLtv" width="1200" minHeight="785" styleClass="novaModal" autosized="true"
                 style="max-height: 700px;">
    <f:facet name="header" >
        <h:panelGroup style="position: absolute; background-color: #114469; opacity: 100%; max-width: 1200px; min-width: 1120px; padding: 40px; margin-left: -28px; margin-top: -42px;">
            <h:outputText value="Relat�rio Customer Churn Rate" style="float: left;"/>

        </h:panelGroup>
            <span class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" style="position: absolute; float: right; margin-left: 96%; z-index: 2001 !important; margin-top: -52px;"
                  id="hidelinkFiltroConvenioCobranca" onclick="Richfaces.hideModalPanel('panelGraficoBiLtv');"></span>
    </f:facet>
        <h:form id="formPanelGraficoLtv" style="height: 100%; overflow-x: hidden; overflow-y: auto; max-width: 1180px; min-width: 1180px;">
            <h:panelGroup layout="block" styleClass="paginaFontResponsiva">
                <h:panelGroup layout="block" style="display: inline-block; padding: 20px 5px 5px 5px; width: 100%; text-transform: uppercase;">
                    <h:outputText styleClass="fontePorcentagem negrito" id="nomeEmpresaSelecionada" style="font-size: 16px; margin-left: 20px;"
                                  value="Comparativo dos �ltimos tr�s anos da empresa #{LtvControle.nomeEmpresaSelecionada}"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="container-botoes" style="float: right; height: 310px;"
                          id="painelChartChurn">
                <script type="text/javascript">
                    function carregarGraficoChurn() {
                        chart = AmCharts.makeChart("chartchurn", {
                            "type": "serial",
                            "theme": "light",
                            "dataProvider": ${LtvControle.dadosGrafico},
                            "valueAxes": [{
                                "dashLength": 0
                            }],
                            "startDuration": 1,
                            "legend": {
                                "autoMargins": true,
                                "equalWidths": false,
                                "horizontalGap": 10,
                                "markerSize": 10,
                                "useGraphSettings": true,
                                "valueAlign": "left",
                                "valueWidth": 0
                            },
                            "graphs": [{
                                "balloonText": "[[title]]: <b>[[value]]</b>%",
                                "lineAlpha": 1,
                                "lineThickness": 1,
                                "bullet": "round",
                                "type": "line",
                                "lineColor": "#9D9D9D",
                                "valueField": "anoRetrasado",
                                "title": "${LtvControle.anoRetrasado}"
                            }, {
                                "balloonText": "[[title]]: <b>[[value]]</b>%",
                                "lineAlpha": 1,
                                "lineThickness": 1,
                                "bullet": "round",
                                "type": "line",
                                "lineColor": "#29AAE2",
                                "valueField": "anoPassado",
                                "title": "${LtvControle.anoPassado}"
                            }, {
                                "balloonText": "[[title]]: <b>[[value]]</b>%",
                                "lineAlpha": 1,
                                "lineThickness": 1,
                                "bullet": "round",
                                "type": "line",
                                "lineColor": "#074871",
                                "valueField": "anoAtual",
                                "title": "${LtvControle.anoAtual}"
                            }],
                            "chartCursor": {
                                "categoryBalloonEnabled": false,
                                "cursorAlpha": 0,
                                "zoomable": false
                            },
                            "categoryField": "mesAno",
                            "categoryAxis": {
                                "gridAlpha": 0,
                                "axisAlpha": 0,
                                "tickLength": 0
                            },
                            "export": {
                                "enabled": true
                            }

                        });
                        chart.validateData();
                        chart.animateAgain();
                    }
                </script>
                <div id="chartchurn" style="width: 97%; height: 300px; margin-left: 8px;margin-top: 20px"></div>
                <script>
                    carregarGraficoChurn();
                </script>
            </h:panelGroup>
            <h:panelGroup style="display: inline-block; width: 1140px; border: solid 1px #E5E5E5; margin-left: 10px;"
                          id="painelComparativos">
                <h:panelGroup styleClass="quadroComparativo" style="padding-left: 0px; background-color: #F6F7F9;">
                    <h:outputText styleClass="fontePorcentagem negrito"
                                  id="ltvChurnMesAtual"
                                  value="#{LtvControle.mesAtual}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                    <h:outputText styleClass="fontePorcentagem negrito"
                                  value="%">
                    </h:outputText>
                    <br/>
                    <rich:spacer height="5"/>
                    <br/>
                    <h:outputText styleClass="fonteSecundaria" value="M�s atual"/>
                </h:panelGroup>
                <h:panelGroup styleClass="quadroComparativo">
                    <div style="text-align: center; float: left;">
                        <h:outputText styleClass="fontePorcentagem"
                                      value="#{LtvControle.mesAnterior}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText styleClass="fontePorcentagem"
                                      value="%">
                        </h:outputText>
                        <br/>
                        <rich:spacer height="5"/>
                        <br/>
                        <h:outputText styleClass="fonteSecundaria" value="M�s anterior"/>
                    </div>
                    <div style="text-align: center; float: left;  padding-left: 30px;">
                        <h:outputText styleClass="fontePorcentagem" value="+"
                                      style="color: #{LtvControle.corDiferencaMesAnterior}"
                                      rendered="#{LtvControle.diferencaMesAnterior > 0}"/>
                        <h:outputText styleClass="fontePorcentagem tooltipster"
                                      style="color: #{LtvControle.corDiferencaMesAnterior}"
                                      value="#{LtvControle.diferencaMesAnterior}"
                                      title="<div style='text-align: left !important'>
<h3><b>Diferen�a entre o m�s atual e o m�s anterior:</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li>Se o valor for negativo e estiver verde, significa que houve uma queda na perda de alunos.</li><br>
      <li>Se estiver vermelho e positivo, significa que sua empresa perdeu mais alunos que no m�s anterior.</li><br>
     </ul>
</div>
 ">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText styleClass="fontePorcentagem" value="%"
                                      style="color: #{LtvControle.corDiferencaMesAnterior}"/>
                    </div>
                </h:panelGroup>
                <h:panelGroup styleClass="quadroComparativo">
                    <div style="text-align: center; float: left;">
                        <h:outputText styleClass="fontePorcentagem"
                                      value="#{LtvControle.seisMeses}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText styleClass="fontePorcentagem"
                                      value="%">
                        </h:outputText>
                        <br/>
                        <rich:spacer height="5"/>
                        <br/>
                        <h:outputText styleClass="fonteSecundaria" value="6 meses"/>
                    </div>
                    <div style="text-align: center; float: left;  padding-left: 30px;">
                        <h:outputText styleClass="fontePorcentagem" value="+"
                                      style="color: #{LtvControle.corDiferencaSeisMeses}"
                                      rendered="#{LtvControle.diferencaSeisMeses > 0}"/>
                        <h:outputText styleClass="fontePorcentagem tooltipster"
                                      style="color: #{LtvControle.corDiferencaSeisMeses}"
                                      value="#{LtvControle.diferencaSeisMeses}"
                                      title="
<div style='text-align: left !important'>
<h3><b>Diferen�a entre o m�s atual e a m�dia dos �ltimos seis meses:</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li>Se o valor for negativo e estiver verde, significa que houve uma queda na perda de alunos.</li><br>
      <li>Se estiver vermelho e positivo, significa que sua empresa perdeu mais alunos que na m�dia dos seis �ltimos meses.</li><br>
     </ul>
</div>
">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText styleClass="fontePorcentagem" value="%"
                                      style="color: #{LtvControle.corDiferencaSeisMeses}"/>
                    </div>
                </h:panelGroup>
                <h:panelGroup styleClass="quadroComparativo"
                        style="border-right: 0px;">
                    <div style="text-align: center; float: left;">
                        <h:outputText styleClass="fontePorcentagem"
                                      value="#{LtvControle.dozeMeses}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText styleClass="fontePorcentagem"
                                      value="%"/>
                        <br/>
                        <rich:spacer height="5"/>
                        <br/>
                        <h:outputText styleClass="fonteSecundaria" value="12 meses"/>
                    </div>
                    <div style="text-align: center; float: left;  padding-left: 30px;">
                        <h:outputText styleClass="fontePorcentagem" value="+"
                                      style="color: #{LtvControle.corDiferencaDozeMeses}"
                                      rendered="#{LtvControle.diferencaDozeMeses > 0}"/>
                        <h:outputText styleClass="fontePorcentagem tooltipster"
                                      style="color: #{LtvControle.corDiferencaDozeMeses}"
                                      value="#{LtvControle.diferencaDozeMeses}"
                                      title="<div style='text-align: left !important'>
<h3><b>Diferen�a entre o m�s atual e a m�dia dos �ltimos doze meses:</b></h3>
    <ul style='list-style-type: lower-alpha;'>
      <li>Se o valor for negativo e estiver verde, significa que houve uma queda na perda de alunos.</li><br>
      <li>Se estiver vermelho e positivo, significa que sua empresa perdeu mais alunos que na m�dia dos doze �ltimos meses.</li><br>
     </ul>
</div>
">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText styleClass="fontePorcentagem" value="%"
                                      style="color: #{LtvControle.corDiferencaDozeMeses}"/>
                    </div>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" style="display: inline-block; width: 1140px; margin-top: 20px; padding-left: 15px;">
                <br/>
                <rich:spacer height="10"/>
                <br/>
                <table width="100%" style="border: solid 1px #E5E5E5;" cellspacing="0" cellpadding="10" class="tabela">
                    <tr style="background: #F6F7F9;">
                        <td class="negrito" style="text-align: left; background: #FFF; text-transform: uppercase;" colspan="13">
                            <h:panelGroup style="display: inline-block; width: 850px;">
                                <rich:spacer height="15"/>
                                <br/>
                                <h:outputText styleClass="fontePorcentagem negrito" style="color: #777777"
                                              value="Customer Churn Rate"/>
                                <br/>
                                <rich:spacer height="10"/>
                                <br/>
                                <h:outputText styleClass="fonteSecundaria" style="color: #9D9D9D;"
                                              value="Trata-se do percentual da taxa de cancelamento. Essa m�trica que indica o quanto sua empresa perdeu de alunos."/>
                                <br/>
                                <rich:spacer height="15"/>
                                <br/>
                                <span style="color: #28A24E; font-size: 14px;">AT� 3% = NORMAL</span> <rich:spacer width="25"/>
                                <span style="color: #F37021; font-size: 14px;">AT� 8% = PERIGO</span> <rich:spacer width="25"/>
                                <span style="color: #EF3C34; font-size: 14px;">ACIMA DE 8% = ALERTA M�XIMO.</span>
                            </h:panelGroup>
                        </td>
                    </tr>
                    <tr style="background: #F6F7F9;">
                        <td class="negrito" style="text-align: left; background: #FFF; text-transform: uppercase;">
                            Minhas
                            empresas
                        </td>
                        <td>Jan</td>
                        <td>Fev</td>
                        <td>Mar</td>
                        <td>Abr</td>
                        <td>Mai</td>
                        <td>Jun</td>
                        <td>Jul</td>
                        <td>Ago</td>
                        <td>Set</td>
                        <td>Out</td>
                        <td>Nov</td>
                        <td>Dez</td>
                    </tr>
                    <a4j:repeat id="corpoChurnEmpresa" var="churnEmpresa"
                                value="#{LtvControle.listaChurnRatePorEmpresa}">
                        <tr>
                            <td style="text-align: left; vertical-align: middle;">
                                <div style="text-transform: uppercase; float: left; color: #9D9D9D; font-size: 14px; vertical-align: middle;">
                                    <h:outputText value="#{churnEmpresa.nomeEmpresa}" style="vertical-align: middle;"/>
                                </div>
                                <div style="float: right; color: #27A9E0; font-size: 14px; vertical-align: middle;">
                                    <a4j:commandLink action="#{LtvControle.selecionarEmpresaGrafico}" value="Ver evolu��o"
                                                     title="Atualizar o gr�fico com os dados dessa empresa."
                                                     reRender="nomeEmpresaSelecionada, painelComparativos, painelChartChurn"
                                                     styleClass="tooltipster" style="vertical-align: middle;">
                                        <a4j:actionparam name="empresaConsulta" value="#{churnEmpresa.codigoEmpresa}"/>
                                        <a4j:actionparam name="nomeEmpresaSelecionada"
                                                         value="#{churnEmpresa.nomeEmpresa}"/>
                                        <img src="../imagens_flat/icon-chart-ltv.png" style="vertical-align: bottom;"
                                             height="16">
                                    </a4j:commandLink>
                                </div>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.janeiro}" style="color: #{churnEmpresa.corJaneiro}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corJaneiro}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.fevereiro}"
                                              style="color: #{churnEmpresa.corFevereiro}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corFevereiro}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.marco}" style="color: #{churnEmpresa.corMarco}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corMarco}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.abril}" style="color: #{churnEmpresa.corAbril}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corAbril}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.maio}" style="color: #{churnEmpresa.corMaio}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corMaio}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.junho}" style="color: #{churnEmpresa.corJunho}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corJunho}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.julho}" style="color: #{churnEmpresa.corJulho}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corJulho}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.agosto}" style="color: #{churnEmpresa.corAgosto}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corAgosto}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.setembro}"
                                              style="color: #{churnEmpresa.corSetembro}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corSetembro}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.outubro}" style="color: #{churnEmpresa.corOurubro}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corOurubro}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.novembro}"
                                              style="color: #{churnEmpresa.corNovembro}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corNovembro}"/>
                            </td>
                            <td>
                                <h:outputText value="#{churnEmpresa.dezembro}"
                                              style="color: #{churnEmpresa.corDezembro}">
                                    <f:converter converterId="FormatadorNumerico"/>
                                </h:outputText>
                                <h:outputText value="%" style="color: #{churnEmpresa.corDezembro}"/>
                            </td>
                        </tr>
                    </a4j:repeat>
                    <tr style="background: #F6F7F9;" class="negrito">
                        <td style="background: #FFF; text-align: left;">M�DIA</td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaJaneiro}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaFevereiro}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaMarco}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaAbril}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaMaio}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaJunho}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaJulho}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaAgosto}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaSetembro}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaOutubro}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaNovembro}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                        <td>
                            <h:outputText value="#{LtvControle.mediaDezembro}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value="%"/>
                        </td>
                    </tr>
                </table>
            </h:panelGroup>

        </h:form>
</rich:modalPanel>

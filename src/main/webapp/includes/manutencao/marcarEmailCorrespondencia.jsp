<%@include file="../imports.jsp" %>
<rich:simpleTogglePanel switchType="client" label="Marcar e-mail para Correspond�ncia" width="100%"
                        id="marcarEmailCorrespondencia" opened="false">
    <h:panelGrid styleClass="textsmall" columns="2">
        <h:outputLabel style="font-weight: bold" value="Objetivo:"/>
        <h:outputLabel escape="false"
                       value="Marcar todos e-mails de clientes para correspond�ncia de determinada empresa"/>
    </h:panelGrid>
    <rich:spacer height="10"/>

    <h:panelGrid styleClass="textsmall" columns="1" id="pnlMarcarEmailCorrespondencia">
        <h:panelGroup layout="block">
            <h:outputLabel value="Empresa: "/>
            <h:selectOneMenu id="sltMarcarEmailCorrespondencia"
                             value="#{ConfiguracaoSistemaControle.marcarEmailCorrespondencia.codEmpresa}">
                <f:selectItems value="#{ConfiguracaoSistemaControle.marcarEmailCorrespondencia.listaEmpresas}"/>
            </h:selectOneMenu>
        </h:panelGroup>

        <rich:spacer height="10"/>

        <h:panelGroup layout="block">
            <a4j:commandButton value="Buscar clientes"
                               action="#{ConfiguracaoSistemaControle.marcarEmailCorrespondencia.obterClientesParaMarcar}"
                               reRender="infoMarcarEmailCorrespondencia, msgMarcarEmailCorrespondencia"/>
        </h:panelGroup>
    </h:panelGrid>

    <rich:spacer height="10"/>

    <h:panelGrid id="infoMarcarEmailCorrespondencia" styleClass="textsmall" columns="1">
        <rich:dataTable id="tblMarcarEmailCorrespondencia" var="cliente" rows="10" rowKeyVar="status"
                        value="#{ConfiguracaoSistemaControle.marcarEmailCorrespondencia.clientesParaMarcar}"
                        rendered="#{not empty ConfiguracaoSistemaControle.marcarEmailCorrespondencia.clientesParaMarcar}">
            <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
            <rich:column sortBy="#{cliente.matricula}">
                <f:facet name="header">
                    <h:outputText value="Matr�cula"/>
                </f:facet>
                <h:outputText value="#{cliente.matricula}"/>
            </rich:column>

            <rich:column sortBy="#{cliente.nome}">
                <f:facet name="header">
                    <h:outputText value="Nome"/>
                </f:facet>
                <h:outputText value="#{cliente.nome}"/>
            </rich:column>

            <rich:column sortBy="#{cliente.emails}">
                <f:facet name="header">
                    <h:outputText value="E-mail"/>
                </f:facet>
                <h:outputText value="#{cliente.emails}"/>
            </rich:column>
        </rich:dataTable>

        <rich:datascroller align="center" for="form:tblMarcarEmailCorrespondencia" maxPages="10"
                           id="sctblMarcarEmailCorrespondencia"/>

        <rich:spacer height="10"/>
        <h:panelGroup layout="block">
            <a4j:commandButton
                    rendered="#{not empty ConfiguracaoSistemaControle.marcarEmailCorrespondencia.clientesParaMarcar}"
                    action="#{ConfiguracaoSistemaControle.marcarEmailCorrespondencia.marcarEmails}"
                    value="Marcar e-mails"
                    reRender="msgMarcarEmailCorrespondencia"/>
        </h:panelGroup>
    </h:panelGrid>

    <rich:spacer height="10"/>
    <h:panelGrid styleClass="textsmall" columns="2" id="msgMarcarEmailCorrespondencia">
        <h:outputLabel style="font-weight: bold" value="Mensagem:"/>
        <h:outputLabel style="color: red;"
                       value="#{ConfiguracaoSistemaControle.marcarEmailCorrespondencia.msgResultado}"/>
    </h:panelGrid>

    <rich:spacer height="10"/>
</rich:simpleTogglePanel>
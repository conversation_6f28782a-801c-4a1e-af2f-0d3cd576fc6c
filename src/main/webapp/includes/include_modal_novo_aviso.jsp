<%@include file="imports.jsp" %>
<rich:modalPanel id="modalGravarAviso" trimOverlayedElements="false" autosized="true"
                 shadowOpacity="true" width="800"
                 styleClass="novaModal modalDs3">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{MenuAvisosControle.codigo == null ? 'Publicar novo aviso' : 'Editar aviso'}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage url="/imagens_flat/icon-close.png" styleClass="" id="hidelink1Aviso">
            </h:graphicImage>
            <rich:componentControl for="modalGravarAviso"
                                   attachTo="hidelink1Aviso" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formModalAviso" ajaxSubmit="true">
        <div class="modal-conteudo-app-gestor">
            <h:panelGroup layout="block" styleClass="row publicar-aviso" id="form-cadastro-aviso">
                <h:panelGroup layout="block" styleClass="column col-md-6">
                    <h6><h:outputText value="Pessoas que ter�o acesso ao aviso"/></h6>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu styleClass="font14 cinza"
                                         value="#{MenuAvisosControle.itemAcesso}">
                            <f:selectItems value="#{MenuAvisosControle.itensAcesso}"/>
                            <a4j:support event="onchange" ajaxSingle="true"
                                         reRender="form-cadastro-aviso"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="column col-md-6">
                    <h6><h:outputText value="Exibir aviso at�"/></h6>
                    <h:panelGroup styleClass="dateTimeCustom" style="margin: 24px 0; display: block">
                        <rich:calendar id="dataTerminoCa"
                                       value="#{MenuAvisosControle.avisoAte}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true" showWeeksBar="false"
                                       zindex="2"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup rendered="#{MenuAvisosControle.itemAcesso == 2}"  layout="block" styleClass="column col-md-12 usuarioAutoSuggestioPanel"
                              id="usuarioAutoSuggestioPanel">
                    <h6><h:outputText value="Selecionar as pessoas"/></h6>
                    <h:panelGroup id="panelOperadorCaixa" layout="block">
                        <h:inputText id="nomeOperador" size="40" style="margin-top: 8px"
                                     maxlength="255" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="formDs3"
                                     value="#{MenuAvisosControle.nomeUsuario}"/>

                        <rich:suggestionbox height="200" width="200"
                                            for="nomeOperador"
                                            suggestionAction="#{MenuAvisosControle.executarAutocompleteUsuario}"
                                            minChars="1" rowClasses="20"
                                            status="statusHora"
                                            nothingLabel="Nenhum Operador encontrado !"
                                            var="result" id="suggestionUsuario">
                            <a4j:support event="onselect" ajaxSingle="true"
                                         reRender="usuarioAutoSuggestioPanel"
                                         action="#{MenuAvisosControle.selecionarUsuario}"/>
                            <h:column>
                                <h:outputText value="#{result.nome}"/>
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="valores" style="display: flex; flex-wrap: wrap; margin-top: 8px">
                        <a4j:repeat value="#{MenuAvisosControle.usuarios}" var="usu">
                            <h:panelGroup styleClass="usuario-aviso" layout="block">
                                <a4j:commandLink reRender="usuarioAutoSuggestioPanel" action="#{MenuAvisosControle.removerUsuario}">
                                    <h:outputText value="#{usu.nomeAbreviado}"/>
                                    <h:outputText styleClass="pct pct-x"></h:outputText>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </a4j:repeat>
                    </h:panelGroup>

                </h:panelGroup>

                <h:panelGroup rendered="#{MenuAvisosControle.itemAcesso == 3}" layout="block" styleClass="column col-md-12 usuarioAutoSuggestioPanel"
                              id="perfilAutoSuggestioPanel">
                    <h6><h:outputText value="Selecionar os perfis"/></h6>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu styleClass="font14 cinza"
                                         value="#{MenuAvisosControle.itemPerfil}">
                            <f:selectItems value="#{MenuAvisosControle.itensPerfil}"/>
                            <a4j:support event="onchange" ajaxSingle="true"
                                         reRender="perfilAutoSuggestioPanel"
                                         action="#{MenuAvisosControle.selecionarPerfil}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="valores" style="display: flex; flex-wrap: wrap; ">
                        <a4j:repeat value="#{MenuAvisosControle.perfisSelecionados}" var="per">
                            <h:panelGroup styleClass="usuario-aviso" layout="block">
                                <a4j:commandLink reRender="perfilAutoSuggestioPanel"
                                                 action="#{MenuAvisosControle.removerPerfil}">
                                    <h:outputText value="#{per.nome}"/>
                                    <h:outputText styleClass="pct pct-x"></h:outputText>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </a4j:repeat>
                    </h:panelGroup>

                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="column col-md-12">
                    <h6><h:outputText value="Mensagem de aviso"/></h6>
                    <h:inputTextarea id="texto-aviso" style="resize: none;"
                                     styleClass="text-area-aviso"
                                     onkeyup="contadorAviso()"
                                     onblur="contadorAviso()"
                                     value="#{MenuAvisosControle.aviso}"/>
                    <div id="contador-aviso" class="contador">0/290</div>
                    <script>
                        function contadorAviso() {
                            const textarea = document.getElementById('formModalAviso:texto-aviso');
                            const contador = document.getElementById('contador-aviso');
                            let currentLength = textarea.value.length;
                            if (currentLength > 290) {
                                textarea.value = textarea.value.slice(0, 290);
                                currentLength = 290;
                            }
                            contador.textContent = currentLength + '/290';

                        }
                    </script>

                    <h:panelGroup layout="block" styleClass="botoes-aviso">
                        <a4j:commandLink oncomplete="Richfaces.hideModalPanel('modalGravarAviso')"
                                         styleClass="ds3-btn outline" value="Cancelar"></a4j:commandLink>
                        <a4j:commandLink styleClass="ds3-btn primary"
                                         value="Publicar aviso"
                                         reRender="avisos-painel"
                                         action="#{MenuAvisosControle.gravarAviso}"
                                         oncomplete="#{MenuAvisosControle.msgAlert}"
                        ></a4j:commandLink>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGroup>
        </div>

    </a4j:form>
</rich:modalPanel>

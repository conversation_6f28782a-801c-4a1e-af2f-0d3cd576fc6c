

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="imports.jsp" %>

<td>


    <%@include file="/includes/include_modal_feedGestao.jsp" %>
    <a4j:jsFunction name="verificarTemNova" reRender="painelFeed"
                    status="false"
                    action="#{FeedGestaoControle.verificarTemNova}">
    </a4j:jsFunction>
    <div style="position: absolute; top: 10px; left: 175px;" class="tudo dicaFeed">


        <h:panelGroup id="painelFeed" layout="block" rendered="true">
            <a4j:commandLink oncomplete="Richfaces.showModalPanel('feedGestao'); indiceAtual = 1;"
                             reRender="feedGestao, painelFeed"
                             status="false"
                             action="#{FeedGestaoControle.abrirFeed}">
                <h:graphicImage rendered="#{FeedGestaoControle.nrMsgsNaoLidas == null or FeedGestaoControle.nrMsgsNaoLidas == 0}"
                                url="/feed_gestao/assistente_sem_notif.png" width="60" height="60"
                                title="Assistente de Gestão Pacto"/>

                <h:graphicImage rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                                url="/feed_gestao/assistente_com_notif.gif" width="60" height="60"
                                title="Assistente de Gestão Pacto"/>
            </a4j:commandLink>
            
            <h:panelGroup id="nrlidaFeed" rendered="#{FeedGestaoControle.nrMsgsNaoLidas > 0}"
                          layout="block" style="float:right;display: inline;position: absolute;">
                <div class="notificacaoAtividades">
                    <h:outputText  value="#{FeedGestaoControle.nrMsgsNaoLidas}"></h:outputText>
                    </div>
            </h:panelGroup>
            
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}ZillyonWeb:Assistente_Gestao_Pacto"
                          title="Clique e saiba mais: Feed de gestão"
                          target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
            
            
        </h:panelGroup>
        
    </div>

    <script type="text/javascript">
        window.addEventListener('load',function() {
            verificarTemNova();
        }, false);
    </script> 
</td>

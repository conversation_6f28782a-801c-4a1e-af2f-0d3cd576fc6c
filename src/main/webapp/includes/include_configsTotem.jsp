<%@include file="imports.jsp" %>
<%@page pageEncoding="ISO-8859-1"%>
<style>
    .inativo{
        opacity: .5;
    }
</style>

<table width="100%">
    <tr class="linhaPar">
        <td class="classEsquerda">
            <h:outputText styleClass="titulocampos tooltipster" value="Descrição do Totem:" title="Defina aqui o nome do terminal desejado. Cada totem da academia deve ter uma configuração separada específica para ele. Isto para que funcione a máquina de cartão pois o número do pdv muda de um terminal para outro."/>

        </td>
        <td class="classDireita">
            <h:inputText styleClass="0_id tooltipster" title="Defina aqui o nome do terminal desejado. Cada totem da academia deve ter uma configuração separada específica para ele. Isto para que funcione a máquina de cartão pois o número do pdv muda de um terminal para outro." value="#{EmpresaControle.totem.totem}"/>
        </td>
    </tr>

</table>

<h:dataTable width="100%" value="#{EmpresaControle.configsTotem}" var="cfgTtm" id="dtcfgttm"
             rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
    <h:column rendered="#{!cfgTtm.mostrarConfEmpresa}">
        <h:outputText styleClass="tituloCampos #{empty cfgTtm.hint ? '' : 'tooltipster'} #{cfgTtm.dependencia != null
                                and !EmpresaControle.totem.configs[cfgTtm.dependencia].valorAsBoolean ? 'inativo' : ''}"
                      title="#{empty cfgTtm.hint ? '' : msg_aplic[cfgTtm.hint]}"
                      value="#{msg_aplic[cfgTtm]}:"/>
    </h:column>
    <h:column rendered="#{!cfgTtm.mostrarConfEmpresa}" >
        <h:selectBooleanCheckbox rendered="#{cfgTtm.tipo eq 'boolean'}"
                                 styleClass="#{cfgTtm.id}_id #{empty cfgTtm.hint ? '' : 'tooltipster'} #{cfgTtm.dependencia != null and !EmpresaControle.totem.configs[cfgTtm.dependencia].valorAsBoolean ? 'inativo' : ''}"
                                 disabled="#{cfgTtm.dependencia != null and !EmpresaControle.totem.configs[cfgTtm.dependencia].valorAsBoolean}"
                                 value="#{EmpresaControle.totem.configs[cfgTtm].valorAsBoolean}"
                                 title="#{empty cfgTtm.hint ? '' : msg_aplic[cfgTtm.hint]}">
            <a4j:support event="onclick" reRender="dtcfgttm"/>
        </h:selectBooleanCheckbox>
        <h:selectOneMenu rendered="#{cfgTtm.tipo eq 'combo'}"
                         title="#{empty cfgTtm.hint ? '' : msg_aplic[cfgTtm.hint]}"
                         styleClass="#{cfgTtm.id}_id #{empty cfgTtm.hint ? '' : 'tooltipster'} #{cfgTtm.dependencia != null and !EmpresaControle.totem.configs[cfgTtm.dependencia].valorAsBoolean ? 'inativo' : ''}"
                         disabled="#{cfgTtm.dependencia != null and !EmpresaControle.totem.configs[cfgTtm.dependencia].valorAsBoolean}"
                         value="#{EmpresaControle.totem.configs[cfgTtm].valorAsInt}">
            <f:selectItems value="#{EmpresaControle[cfgTtm.prop]}"/>
        </h:selectOneMenu>

        <h:inputText rendered="#{cfgTtm.tipo eq 'text'}" value="#{EmpresaControle.totem.configs[cfgTtm].valor}"
                     title=" #{empty cfgTtm.hint ? '' : msg_aplic[cfgTtm.hint]}"
                     styleClass="#{cfgTtm.id}_id #{empty cfgTtm.hint ? '' : 'tooltipster'} #{cfgTtm.dependencia != null and !EmpresaControle.totem.configs[cfgTtm.dependencia].valorAsBoolean ? 'inativo' : ''}">

        </h:inputText>



    </h:column>
</h:dataTable>

<div style="text-align: center">
    <a4j:commandButton value="Novo"
                       style="vertical-align: middle;"
                       action="#{EmpresaControle.novoTotem}"
                       reRender="form"
                       styleClass="botoes nvoBt btSec"/>

    <a4j:commandButton value="Adicionar"
                       style="vertical-align: middle;"
                       action="#{EmpresaControle.adicionarTotem}"
                       reRender="form"
                       styleClass="botoes nvoBt bt"/>
</div>

<h:panelGroup id="ddsTotem" styleClass="painelDadosAluno"
              style="width: calc(98.5% - 10px); height: auto;">
    <div class="tituloPainelAluno">
        <h:outputText styleClass="negrito cinzaEscuro pl20"
                      value="Totens cadastrados"
                      style="line-height: 40px;font-size: 14px !important;"/>
    </div>
    <div class="conteudoDadosCliente" style="min-height: 50px;">
        <table width="100%" class="tblHeaderLeft" cellspacing="0" cellpadding="0">
            <a4j:repeat value="#{EmpresaControle.totens}" var="totem">
                <tr>
                    <td style="padding: 5px 10px">
                        <a4j:commandLink value="#{totem.totem}"
                                         styleClass="linkAzul"
                                         action="#{EmpresaControle.selecionarTotem}"
                                         reRender="form">
                        </a4j:commandLink>
                    </td>
                    <td style="padding: 5px 10px; text-align: right;">
                        <h:outputLink value="#{totem.linkAuto}"

                                      styleClass="linkAzul "
                                      target="_zwauto">
                            Link para o totem <i style="margin-right: 10px;" class="fa-icon-arrow-right"></i>
                        </h:outputLink>
                    </td>
                    <td style="padding: 5px 10px; text-align: right;">
                        <a4j:commandLink styleClass="linkAzul"
                                         action="#{EmpresaControle.removerTotem}"
                                         reRender="form">
                            <i style="margin-right: 10px;" class="fa-icon-trash"></i>
                        </a4j:commandLink>
                    </td>
                </tr>
            </a4j:repeat>
        </table>
    </div>
</h:panelGroup>

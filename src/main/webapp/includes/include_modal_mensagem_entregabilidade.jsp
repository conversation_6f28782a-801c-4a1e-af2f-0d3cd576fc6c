<%@include file="imports.jsp" %>

<rich:modalPanel id="mdlMensagemEntregabilidade" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{MensagemEntregabilidadeControle.titulo}"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkMensagemEntregabilidade" rendered="#{MensagemEntregabilidadeControle.mostrarBotaoFechar}"/>
            <rich:componentControl for="mdlMensagemEntregabilidade" attachTo="hidelinkMensagemEntregabilidade" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formMdlMensagemEntregabilidade">
        <h:panelGroup>
            <h:panelGroup styleClass="margin-box">
                <h:panelGroup id="mensagemApresentar" layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                    ${MensagemEntregabilidadeControle.mensagemApresentar}
                </h:panelGroup>
            </h:panelGroup>
            <h:outputText id="msgDetalhada"
                          value="#{MensagemEntregabilidadeControle.mensagemDetalhada}"/>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup rendered="#{MensagemEntregabilidadeControle.labelBotaoFecharTela == null}" styleClass="margin-box">
                    <a4j:commandLink action="#{MensagemEntregabilidadeControle.invokeBotaoSim}" value="Sim"
                                       id="sim"
                                       styleClass="botaoPrimario texto-size-16-real"
                                       oncomplete="#{MensagemEntregabilidadeControle.mensagemNotificar}; #{MensagemEntregabilidadeControle.onCompleteBotaoSim};Richfaces.hideModalPanel('mdlMensagemEntregabilidade');"
                                       reRender="mdlMensagemEntregabilidade, #{MensagemEntregabilidadeControle.reRenderComponents}"/>
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink oncomplete="#{MensagemEntregabilidadeControle.onCompleteBotaoNao};Richfaces.hideModalPanel('mdlMensagemEntregabilidade');"
                                       id="nao"
                                       styleClass="botaoSecundario texto-size-16-real"
                                       action="#{MensagemEntregabilidadeControle.invokeBotaoNao}"
                                       value="N�o"
                                       reRender="#{MensagemEntregabilidadeControle.reRenderComponents}"/>
                </h:panelGroup>
                <a4j:commandLink value="#{MensagemEntregabilidadeControle.labelBotaoFecharTela}"
                                 id="btnFecharModalEntregabilidade"
                                 rendered="#{MensagemEntregabilidadeControle.labelBotaoFecharTela != null
                                   && MensagemEntregabilidadeControle.labelBotaoFecharTela !=''}"
                                 styleClass="botaoPrimario texto-size-16-real"
                                   oncomplete="#{MensagemEntregabilidadeControle.onCompleteBotaoFechar};Richfaces.hideModalPanel('mdlMensagemEntregabilidade');"
                                   reRender="#{MensagemEntregabilidadeControle.reRenderComponents}"/>
            </h:panelGroup>
            <br/>
            <br/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

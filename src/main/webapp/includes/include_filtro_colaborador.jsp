<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 15/03/2016
  Time: 08:37
  To change this template use File | Settings | File Templates.
--%>
<%@include file="imports.jsp" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<h:panelGroup id="panelFiltroColaborador">
    <h:panelGroup id="filtro-colaborador"
                  layout="" styleClass="container-colaboradores" style="margin-top: 0px; display:none; position:fixed;">
        <h:panelGroup layout="block" styleClass="filtro-colaborador-header">
            <h:panelGroup layout="block" styleClass="filtro-colaboradores">
                <h:outputText style="width: 70%" styleClass="bi-font-family bi-font-bold texto-size-14 bi-color-branco"
                              value="Filtro de grupo de colaboradores"/>
                <a onclick="fecharFiltroColaborador();organizarSortable();montarTips();"
                   class="btn-colaboradores-fechar">
                    <h:outputText style="font-size:22px;" styleClass="fa-icon-remove-sign bi-font-bold texto-size-14 bi-color-branco"/>
                </a>
                <h:outputText styleClass="bi-font-family bi-text-11 bi-color-branco" value="Selecione quais grupos e/ou colaboradores
você quer filtrar. Feche o filtro para aplicar"/>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" style="width: 100%; margin-top: 20px;" styleClass="col-text-align-right">
            <h:outputText styleClass="botaoPrimario marcarTodosItemFiltro tooltipster linkPadrao texto-cor-azul"
                          style="font-size: 14px;"
                          value="Marcar Todos"
                          title="Clique para selecionar todos os colaboradores de todos os grupos."/>
            <rich:spacer width="15"></rich:spacer>
            <h:outputText style="margin-right: 20px; font-size: 14px;"
                          styleClass="botaoPrimario desmarcarTodosFiltro tooltipster linkPadrao texto-cor-azul"
                          value="Desmarcar Todos"
                          title="Clique para remover a seleção de todos os colaboradores de todos os grupos."/>
        </h:panelGroup>
        <br/>
        <hr/>
        <h:panelGroup layout="block" styleClass="filtro-colaboradores" id="containerFiltroColaborador"
                      style="height: 80vh; overflow-y: auto;width: 90%; margin-top: 0px;">
            <h:panelGroup layout="block" styleClass="filtro-container-grupo"
                          rendered="#{BIControle.exibirGrupoConsultorICV}">
                <h:panelGroup layout="block" styleClass="filtro-colaborador-grupo-header">
                    <h:outputText styleClass="btn-toggle-grupo fa-icon-check texto-size-14 filtro-col-icon-check"/>
                    <h:outputText value="Consultores ICV"/>
                </h:panelGroup>
                <a4j:repeat value="#{BIControle.listaColaboradoresICV}" var="participante">
                    <h:panelGroup layout="block"
                                  styleClass="filtro-colaborador-grupo-item col-icv hvr-sweep-to-right filtro-colaborador-grupo-item-click">
                        <h:panelGroup layout="block" styleClass="filtro-colaborador-img paginaFontResponsiva">
                            <a4j:mediaOutput element="img"
                                             rendered="#{!SuperControle.fotosNaNuvem}"
                                             align="left"
                                             cacheable="true" session="false"
                                             title="#{participante.pessoa.nome}"
                                             createContent="#{BIControle.paintFoto}"
                                             styleClass="shadow"
                                             value="#{ImagemData}" mimeType="image/jpeg">
                                <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                <f:param name="largura" value="32"/>
                                <f:param name="altura" value="32"/>
                                <f:param name="pessoa" value="#{participante.codigo}"></f:param>
                            </a4j:mediaOutput>
                            <h:outputText styleClass="fa-icon-ok texto-size-16 filtro-col-icon-check"/>
                            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                            width="150" height="180"
                                            url="#{BIControle.urlFotoNuvem}"/>
                            <h:selectBooleanCheckbox styleClass="checkBox-filtro-col"
                                                     value="#{participante.colaboradorEscolhido}"/>
                        </h:panelGroup>
                        <h:outputText styleClass="filtro-colaborador-grupo-item-nome tooltipster texto-size-16"
                                      value="#{participante.pessoa.primeiroNomeConcatenado}"/>
                    </h:panelGroup>
                </a4j:repeat>
            </h:panelGroup>

            <a4j:repeat value="#{BIControle.listaGrupoColaborador}" var="grupoColaborador">

                <h:panelGroup layout="block" styleClass="filtro-container-grupo"
                              rendered="#{not empty grupoColaborador.grupoColaboradorParticipanteVOs}">
                    <h:panelGroup layout="block" styleClass="filtro-colaborador-grupo-header">
                        <h:outputText styleClass="btn-toggle-grupo fa-icon-check texto-size-14 filtro-col-icon-check"/>
                        <h:outputText value="#{grupoColaborador.descricaoComQuantidade}"/>
                    </h:panelGroup>
                    <a4j:repeat value="#{grupoColaborador.grupoColaboradorParticipanteVOs}" var="participante">
                        <h:panelGroup layout="block"
                                      styleClass="filtro-colaborador-grupo-item hvr-sweep-to-right #{participante.grupoColaboradorParticipanteEscolhido ? 'filtro-colaborador-grupo-item-click' : ''}">
                            <h:panelGroup layout="block" styleClass="filtro-colaborador-img paginaFontResponsiva">
                                <a4j:mediaOutput element="img"
                                                 rendered="#{!SuperControle.fotosNaNuvem}"
                                                 align="left"
                                                 cacheable="true" session="false"
                                                 createContent="#{BIControle.paintFoto}"
                                                 styleClass="shadow"
                                                 value="#{ImagemData}" mimeType="image/jpeg">
                                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                                    <f:param name="largura" value="32"/>
                                    <f:param name="altura" value="32"/>
                                    <f:param name="pessoa"
                                             value="#{participante.colaboradorParticipante.codigo}"></f:param>
                                </a4j:mediaOutput>
                                <h:outputText styleClass="fa-icon-ok texto-size-16 filtro-col-icon-check"/>
                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                                                width="150" height="180"
                                                url="#{BIControle.urlFotoNuvem}"/>
                                <h:selectBooleanCheckbox styleClass="checkBox-filtro-col"
                                                         value="#{participante.grupoColaboradorParticipanteEscolhido}"/>
                            </h:panelGroup>
                            <h:outputText
                                    styleClass="filtro-colaborador-grupo-item-nome tooltipster #{participante.colaboradorParticipante.inativo ? 'texto-cor-vermelho' : '' }"
                                    title="#{participante.colaboradorParticipante.pessoa.nome} #{participante.colaboradorParticipante.inativo ? ' (INATIVO)' : '' }"
                                    value="#{participante.colaboradorParticipante.pessoa.primeiroNomeConcatenado}"/>
                        </h:panelGroup>
                    </a4j:repeat>
                </h:panelGroup>

            </a4j:repeat>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
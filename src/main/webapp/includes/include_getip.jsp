<%-- 
    Document   : include_getip
    Created on : 26/05/2011, 11:18:43
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="imports.jsp" %>
<a4j:form ajaxSubmit="true">
    <%
        String ipaddress = request.getHeader("HTTP_CLIENT_IP");

        if (ipaddress == null) {
            ipaddress = request.getRemoteAddr();
        }

        request.getSession().setAttribute("RemoteAddr", ipaddress);

    %>
    <a4j:jsFunction status="statusHora" name="enviarIp" action="#{SuperControle.enviar}">
        <a4j:actionparam name="tmpIP" assignTo="#{SuperControle.ip}"/>
    </a4j:jsFunction>

    <a4j:jsFunction status="statusHora" name="enviarBrowser" action="#{SuperControle.enviarBrowser}">
        <a4j:actionparam name="tmpBrowser" assignTo="#{SuperControle.browser}"/>
        <a4j:actionparam name="tmpW" assignTo="#{SuperControle.widthScreenClient}"/>
        <a4j:actionparam name="tmpH" assignTo="#{SuperControle.heightScreenClient}"/>
        <a4j:actionparam name="protocol" assignTo="#{SuperControle.protocol}"/>        
    </a4j:jsFunction>
</a4j:form>
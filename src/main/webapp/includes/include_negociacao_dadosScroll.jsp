<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 14/04/2016
  Time: 09:43
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="imports.jsp" %>
<h:panelGroup id="dados" layout="block"  styleClass="containerDetalhesNegociacao bg-cinza step2" style="text-align: center;position: relative;">
  <h:panelGroup id="detalhesNegociacao" layout="block"
                styleClass="detalhesNegociacao">
    <h:panelGroup layout="block" styleClass="tudo">

      <%--com familiares--%>
      <h:panelGroup  layout="block" styleClass="containerFotoCliente detalhesEsconder" rendered="#{false}">
        <%@include file="include_negociacao_familiares.jsp" %>
      </h:panelGroup>


      <%--sem familiares--%>
      <h:panelGroup  layout="block" styleClass="containerFotoCliente detalhesEsconder" rendered="#{true}">
        <a4j:mediaOutput element="img" id="imagemFoto" cacheable="false"
                         rendered="#{!SuperControle.fotosNaNuvem}"
                         createContent="#{ContratoControle.paintFoto}"
                         value="#{ImagemData}" mimeType="image/jpeg">
        </a4j:mediaOutput>
        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                        url="#{ContratoControle.paintFotoDaNuvem}"/>
      </h:panelGroup>

      <h:panelGroup layout="block" rendered="#{fn:length(NegociacaoFamiliaresControle.familiares) eq 0}">
        <h:outputText id="pessoaNome"
                      styleClass="texto-size-20 texto-font texto-cor-cinza-2 texto-bold"
                      value="#{ContratoControle.contratoVO.pessoa.nome}"/>
      </h:panelGroup>

      <h:panelGroup layout="block" styleClass="gridValoresContrato" id="containerValoresContrato">

        <h:panelGroup id="containerTotalProdutos" layout="block" styleClass="valorItem tooltipster">
          <span   class="texto-size-14 texto-font texto-cor-cinza-2 texto-bold "
                  onmouseover="exibirElementoTooltip(jQuery(this).parent(),jQuery('.tooltipElementoProdutos'))">TOTAL PRODUTOS</span>
          <h:outputText id="valorTotalProdutos"
                        styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                        value="#{ContratoControle.contratoVO.totalFinalProdutos}">
            <f:converter converterId="FormatadorNumerico"/>
          </h:outputText>
          <h:outputText value="#{ContratoControle.contratoVO.empresa.moeda} "
                        styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"/>
        </h:panelGroup>

        <h:panelGroup id="tabelaInvProdutos" layout="block" styleClass="tooltipElementoProdutos"
                        style="display: none;">
            <h:panelGrid columns="2"
                         columnClasses="colunaEsquerda,colunaDireita"
                         styleClass="tabelaSimplesCustom font-size-Em"
                         width="100%"
                         style="margin: 5px;display: inline-block;">
              <c:forEach items="#{ContratoControle.contratoVO.contratoPlanoProdutoSugeridoVOs}"
                         var="produto">
                <h:outputText
                        styleClass="texto-font texto-size-16 texto-cor-cinza"
                        value="#{produto.planoProdutoSugerido.produto.descricao} "/>
                <h:outputText style="text-align:right;padding-left: 20px"
                              styleClass="texto-font texto-size-16 texto-cor-verde"
                              value="#{produto.planoProdutoSugerido.valorTotalFinal}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
              </c:forEach>
                <c:if test="${ContratoControle.contratoModalidadeVO != null}">
                    <c:forEach items="#{ContratoControle.contratoModalidadeVO.modalidade.produtoSugeridoVOs}"
                               var="produto">
                        <h:outputText rendered="#{produto.produtoSugeridoEscolhida}"
                                      styleClass="texto-font texto-size-16 texto-cor-cinza"
                                      value="#{produto.produto.descricao} "/>
                        <h:outputText style="text-align:right;padding-left: 20px"
                                      rendered="#{produto.produtoSugeridoEscolhida}"
                                      styleClass="texto-font texto-size-16 texto-cor-verde"
                                      value="#{produto.produto.desconto.codigo > 0 ? produto.produto.desconto.valorProdudoComDesconto : produto.produto.valorFinal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </c:forEach>
                </c:if>
            </h:panelGrid>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="valorItem" id="detalhesValorContrato">
          <h:outputText
                        styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                        value="TOTAL DO CONTRATO"/>
          <h:outputText id="totalContrato"
                        styleClass="texto-size-16 texto-font texto-cor-cinza"
                        value="#{ContratoControle.totalContrato}">
            <f:converter converterId="FormatadorNumerico"/>
          </h:outputText>
          <h:outputText value="#{ContratoControle.contratoVO.empresa.moeda} "
                        styleClass="texto-size-16 texto-font texto-cor-cinza"/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="valorItem">
          <h:outputText
                        styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                        value="#{ContratoControle.rotuloTipoDesconto}"/>
          <h:outputText id="totalDescontos"
                        styleClass="texto-size-16 texto-font texto-cor-cinza"
                        value="#{ContratoControle.totalDesconto}">
            <f:converter converterId="FormatadorNumerico"/>
          </h:outputText>
          <h:outputText value="#{ContratoControle.contratoVO.empresa.moeda} "
                        styleClass="texto-size-16 texto-font texto-cor-cinza"/>
          <h:outputText id="totalDescontosP"
                        style="float: inherit;text-align: center"
                        styleClass="texto-size-16 texto-font texto-cor-cinza"
                        value="#{ContratoControle.totalPercentualDesconto}">
            <f:converter converterId="FormatadorNumerico"/>
          </h:outputText>
          <h:outputText style="float: inherit;text-align: center"
                        styleClass="texto-size-16 texto-font texto-cor-cinza"
                        value="%"/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="valorItem">
          <h:outputText
                        styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                        value="SUBTOTAL"/>
          <h:outputText id="subtotal"
                        styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                        value="#{ContratoControle.contratoVO.subTotalComDescontos}">
            <f:converter converterId="FormatadorNumerico"/>
          </h:outputText>
          <h:outputText value="#{ContratoControle.contratoVO.empresa.moeda} "
                        styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"/>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="valorItem">
          <h:outputText
                        styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                        value="VALOR MENSAL"/>
          <h:outputText id="valorMensalModalidade"
                        styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"
                        value="#{ContratoControle.valorMensalModalidade}">
            <f:converter converterId="FormatadorNumerico"/>
          </h:outputText>
          <h:outputText value="#{ContratoControle.contratoVO.empresa.moeda} "
                        styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold"/>
        </h:panelGroup>


      </h:panelGroup>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="tudo gridValoresContrato">
      <h:panelGroup layout="block" styleClass="boxTotalPagar bg-cinza-3">
          <h:panelGroup layout="block" styleClass="pull-left" style="margin-left: 5%">
              <h:outputText
                      styleClass="texto-size-20 texto-font texto-cor-branco texto-bold"
                      value="TOTAL"/>
          </h:panelGroup>
          <h:panelGroup layout="block" styleClass="pull-right" style="margin-right: 5%">
            <h:outputLabel  value="#{MovPagamentoControle.empresaLogado.moeda}" styleClass="texto-bold texto-size-20 texto-font texto-font texto-cor-branco  " style="padding: 3px" />
              <h:outputText
                      styleClass="texto-bold texto-size-20 texto-font texto-font texto-cor-branco "
                      id="total1"
                      value="#{ContratoControle.contratoVO.valorFinalComDescontos}">
                <f:converter converterId="FormatadorNumerico"/>
              </h:outputText>
          </h:panelGroup>
      </h:panelGroup>
    </h:panelGroup>
    <br/>

    <h:panelGroup id="tabelaInvPontos" layout="block" styleClass="tooltipElementoPontos"
                  style="display: none;">
      <h:panelGrid columns="3"
                   columnClasses="colunaEsquerda,colunaDireita,colunaDireita"
                   styleClass="tabelaSimplesCustom font-size-Em"
                   width="100%"
                   style="margin: 5px;display: inline-block;">

          <h:outputText
                  styleClass="texto-font texto-size-16 texto-cor-cinza"
                  value="#{ContratoControle.contratoVO.plano.descricao} "/>
          <h:outputText style="text-align:right;padding-left: 20px"
                        styleClass="texto-font texto-size-16 texto-cor-verde"
                        value="#{ContratoControle.contratoVO.plano.pontos}">
          </h:outputText>
          <h:outputText style="text-align:right;padding-left: 20px"
                      styleClass="texto-font texto-size-16 texto-cor-verde"
                      value="x #{ContratoControle.contratoVO.planoMaiorMultiplicadorCampanhaAtiva <= 0 ? 1 : ContratoControle.contratoVO.planoMaiorMultiplicadorCampanhaAtiva}">
          </h:outputText>

        <c:forEach items="#{ContratoControle.contratoVO.contratoPlanoProdutoSugeridoVOs}"
                   var="produto">
          <h:outputText
                  styleClass="texto-font texto-size-16 texto-cor-cinza"
                  value="#{produto.planoProdutoSugerido.produto.descricao} "/>
          <h:outputText style="text-align:right;padding-left: 20px"
                        styleClass="texto-font texto-size-16 texto-cor-verde"
                        value="#{produto.planoProdutoSugerido.produto.pontos * (produto.planoProdutoSugerido.quantidade > 1 ? produto.planoProdutoSugerido.quantidade : 1)}"/>

          <h:outputText style="text-align:right;padding-left: 20px"
                        styleClass="texto-font texto-size-16 texto-cor-verde"
                        value="x #{produto.planoProdutoSugerido.obterMultiplicadorCampanhaProduto <= 0 ? 1 : produto.planoProdutoSugerido.obterMultiplicadorCampanhaProduto}">
          </h:outputText>
        </c:forEach>

        <h:outputText
                styleClass="texto-font texto-size-16 texto-cor-cinza"
                value="#{ContratoControle.contratoVO.planoDuracao.descricaoDuracao} "/>
        <h:outputText style="text-align:right;padding-left: 20px"
                      styleClass="texto-font texto-size-16 texto-cor-verde"
                      value="#{ContratoControle.contratoVO.planoDuracao.pontos}">
        </h:outputText>
        <h:outputText style="text-align:right;padding-left: 20px"
                      styleClass="texto-font texto-size-16 texto-cor-verde"
                      value="x #{ContratoControle.contratoVO.planoDuracaoMaiorMultiplicadorCampanhaAtiva <= 0 ? 1 : ContratoControle.contratoVO.planoDuracaoMaiorMultiplicadorCampanhaAtiva}">
        </h:outputText>
      </h:panelGrid>
    </h:panelGroup>
     <br/>
    <script>
      scrollDadosNegociacao();
         //Move o painel de informações após ter feito scroll na página
      var scrollTimeout = null;
      jQuery(window).scroll(function () {
        if (scrollTimeout) clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(function () {
          scrollDadosNegociacao()
        }, 40);
      });
    </script>
    <h:panelGroup layout="block" id="containerCondicaoPagamento" styleClass="tudo">
        <h:panelGroup layout="block" id="panelVencimentoCartao" styleClass="gridValoresContrato" rendered="#{not empty ContratoControle.contratoVO.planoDuracao.planoCondicaoPagamentoVOs && ContratoControle.contratoVO.plano.regimeRecorrencia && !ContratoControle.contratoVO.plano.planoRecorrencia.gerarParcelasValorDiferente}" style="margin: 10px 0px 10px 0px;display: inline-block">
        <h:outputText
                styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                value="DIA VENCIMENTO NO CARTÃO"
                style="display: block;margin-bottom: 10px;text-align: left;"/>
        <h:panelGroup styleClass="cb-container pull-left"  layout="block">
            <h:selectOneMenu title="Selecione aqui o melhor dia do cartão de crédito para cobrança"
                             id="diasVencimentoCartao" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{ContratoControle.diaVencimentoCartaoRecorrencia}">
                <f:selectItems value="#{ContratoControle.listaDiasVencimento}" />
            </h:selectOneMenu>
        </h:panelGroup>
    </h:panelGroup>
        <h:panelGroup layout="block" id="panelCondicaoPagamento" styleClass="gridValoresContrato"
                      rendered="#{not empty ContratoControle.contratoVO.planoDuracao.planoCondicaoPagamentoVOs && !ContratoControle.contratoVO.plano.bolsa
                      or not empty ContratoControle.contratoVO.planoDuracao.planoCondicaoPagamentoVOs && ContratoControle.contratoVO.totalFinalProdutos > 0}"
                      style="margin: 10px 0px 10px 0px;display: inline-block">
      <h:outputText
              styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
              value="CONDIÇÕES DE PAGAMENTO"
              style="display: block;margin-bottom: 10px;text-align: left;"/>
      <a4j:repeat id="planoCondicaoPagamentoVO"
                  value="#{ContratoControle.contratoVO.planoDuracao.planoCondicaoPagamentoVOs}"
                  var="planoCondicaoPagamento">
        <h:panelGroup layout="block"
                      style="width: calc(50% - 10px);margin:5px;display: inline-block;text-align: left;vertical-align: top;line-height: 1em;float: left">
          <h:panelGroup  layout="block" style="float: left;width: 100%">
            <a4j:commandLink id="btnMarcarCondicaoPagamento"
                             styleClass="acaoSelecionar linkPadrao"
                             style="#{fn:length(ContratoControle.contratoVO.planoDuracao.planoCondicaoPagamentoVOs) > 1 ? '' : 'width: 100%;'}display: inline-flex"
                             action="#{ContratoControle.marcarCondicaoPagamento}"
                             reRender="panelProdutoParcela, panelParcelaProdutoMatricula,panelCondicaoPagamento,#{ContratoControle.updateComponente.condicaoPagamento}, resumoContrato, panelAutorizacoesCobrancaCliente, btnConferirNegociacao, totalDescontos, detalhesValorContrato">
              <h:outputText styleClass="#{planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida ? 'fa-icon-circle-check' : 'fa-icon-circle-blank' } padding-5 texto-size-16 texto-cor-azul"/>
              <h:outputText
                        styleClass="#{fn:length(ContratoControle.contratoVO.planoDuracao.planoCondicaoPagamentoVOs) > 1 ? 'textoimcompleto' : ''} texto-font texto-size-16 texto-cor-cinza padding-5 tooltipster"
                        style="#{fn:length(ContratoControle.contratoVO.planoDuracao.planoCondicaoPagamentoVOs) > 1 ? 'width: 10em;' : ''}font-family: Arial;display: inline-block;vertical-align: bottom;"
                        title="#{planoCondicaoPagamento.condicaoPagamento.descricaoMinusculo}"
                        value="#{planoCondicaoPagamento.condicaoPagamento.descricaoMinusculo}"/>
            </a4j:commandLink>

          </h:panelGroup>

          <h:panelGroup layout="block" style="float:left;text-align: left;margin-left: 30px"
                        rendered="#{planoCondicaoPagamento.apresentarValorEspecifico}">
            <h:outputText
                    styleClass="texto-font texto-size-16 texto-cor-cinza"
                    value="#{planoCondicaoPagamento.tipoAcrescimo ? '+' : (planoCondicaoPagamento.tipoReducao ? '-' : '')} #{ContratoControle.contratoVO.empresa.moeda} "/>
            <h:outputText
                    styleClass="texto-font texto-size-16 texto-cor-cinza"
                    value="#{planoCondicaoPagamento.valorEspecifico}">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
          </h:panelGroup>

          <h:panelGroup layout="block" style="float:left;text-align: left;margin-left: 30px"
                        rendered="#{planoCondicaoPagamento.apresentarValorDesconto}">
              <h:panelGroup layout="block" styleClass="pull-left">
                  <h:outputText
                          styleClass="texto-font texto-size-16 texto-cor-cinza"
                          value="#{planoCondicaoPagamento.tipoAcrescimo ? '+' : (planoCondicaoPagamento.tipoReducao ? '-' : '')}"/>
              </h:panelGroup>
              <h:panelGroup layout="block" styleClass="pull-right">
                  <h:outputText
                          styleClass="texto-font texto-size-16 texto-cor-cinza"
                          value="#{planoCondicaoPagamento.percentualDesconto} ">
                      <f:converter converterId="FormatadorNumerico"/>
                  </h:outputText>
                  <h:outputText
                          styleClass="texto-font texto-size-14 texto-cor-cinza"
                          value="%"/>
              </h:panelGroup>
          </h:panelGroup>

        </h:panelGroup>
      </a4j:repeat>
    </h:panelGroup>
     <%--INICIO COBRAR ADESAO RECORRENCIA SEPARADA --%>
    <h:panelGroup layout="block" id="cobrarAdesaoSeparada"
                  rendered="#{ContratoControle.contratoVO.plano.regimeRecorrencia && ContratoControle.contratoVO.plano.cobrarAdesaoSeparada && (!ContratoControle.contratoVO.usouCupomDescontoParaAdesao)}"
                  styleClass="gridValoresContrato col-text-align-left" style="margin-bottom: 10px;">
                  <h:panelGroup layout="block" style="width: 50%;display: inline-block;text-align: left;vertical-align: top;" styleClass="checkbox-fa">
                    <h:outputText
                            styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                            value="ADESÃO"
                            style="display: block;text-align: left;margin-bottom: 20px"/>
                    <a4j:commandLink id="btnMarcarAdesaoSeparado"
                                     styleClass="linkPadrao"
                                     action="#{ContratoControle.marcarAdesaoSeparado}"
                                     reRender="cobrarAdesaoSeparada,totalContrato, valorMensalModalidade ,subtotal, planoCondicaoPagamentoVO, resumoContrato, panelAutorizacoesCobrancaCliente"
                                     oncomplete="#{ContratoControle.msgAlert};#{ContratoControle.mensagemNotificar}">
                      <h:outputText styleClass="#{ContratoControle.contratoVO.gerarParcelaParaProdutos ? 'fa-icon-check' : 'fa-icon-check-empty' }  texto-size-16 texto-cor-cinza"/>
                      <h:outputText style="margin-left: 5px" value="Cobrar separado "
                                    styleClass="texto-font texto-size-16 texto-cor-cinza"/>
                    </a4j:commandLink>
                  </h:panelGroup>
              <h:panelGroup layout="block" style="width: 50%;display: inline-block" rendered="#{ContratoControle.contratoVO.gerarParcelaParaProdutos && ContratoControle.contratoVO.somaAdesao > 0}">
                  <h:outputText
                        styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                        value="PARCELAMENTO"
                        style="display: block;text-align: left;margin-bottom: 10px"/>
                  <h:panelGroup layout="block" styleClass="cb-container"
                                style="float: left;width: 100%">
                    <h:selectOneMenu style="width: 100%"
                                     value="#{ContratoControle.contratoVO.nrParcelasAdesao}"
                                     >
                      <f:selectItems
                              value="#{ContratoControle.listaVezesParcelarAdesao}"/>
                      <a4j:support id="btnSelecionarCondicaoPagamento"
                                   event="onchange"
                                   action="#{ContratoControle.selecionarCondicaoPagamento}"
                                   ajaxSingle="true"
                                   reRender="cobrarAdesaoSeparada,panelProdutoParcela,panelParcelaProdutoMatricula, planoCondicaoPagamentoVO, resumoContrato, panelAutorizacoesCobrancaCliente"/>
                    </h:selectOneMenu>
                  </h:panelGroup>
              </h:panelGroup>
    </h:panelGroup>
    <%--FIM COBRAR ADESAO RECORRENCIA SEPARADA --%>

      <%--INICIO COBRAR MATRICULA PLANO NORMAL SEPARADA --%>
      <h:panelGroup layout="block" id="cobrarMatriculaSeparada"
                    rendered="#{(!ContratoControle.contratoVO.plano.regimeRecorrencia) && (ContratoControle.contratoVO.plano.cobrarAdesaoSeparada) && (!ContratoControle.contratoVO.usouCupomDescontoParaAdesao) && (ContratoControle.contratoVO.valorMatricula > 0 ) && (ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida)}"
                    styleClass="gridValoresContrato col-text-align-left" style="margin-bottom: 10px;">
        <h:panelGroup layout="block" style="width: 50%;display: inline-block;text-align: left;vertical-align: top;" styleClass="checkbox-fa">
          <h:outputText
                  styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                  value="MATRÍCULA"
                  style="display: block;text-align: left;margin-bottom: 20px"/>
          <a4j:commandLink id="btnMarcarMatriculaSeparado"
                           styleClass="linkPadrao"
                           action="#{ContratoControle.marcarMatriculaSeparado}"
                           reRender="panelProdutoParcela,panelParcelaProdutoMatricula,cobrarMatriculaSeparada,totalContrato, valorMensalModalidade ,subtotal, planoCondicaoPagamentoVO,#{ContratoControle.updateComponente.condicaoPagamento}, resumoContrato, panelAutorizacoesCobrancaCliente">
            <h:outputText styleClass="#{ContratoControle.contratoVO.cobrarMatriculaSeparada ? 'fa-icon-check' : 'fa-icon-check-empty' }  texto-size-16 texto-cor-cinza"/>
            <h:outputText style="margin-left: 5px" value="Cobrar separado "
                          styleClass="texto-font texto-size-16 texto-cor-cinza"/>
          </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup layout="block" style="width: 50%;display: inline-block" rendered="#{ContratoControle.contratoVO.cobrarMatriculaSeparada && ContratoControle.contratoVO.valorMatricula > 0}">
          <h:outputText
                  styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                  value="PARCELAMENTO"
                  style="display: block;text-align: left;margin-bottom: 10px"/>
          <h:panelGroup layout="block" styleClass="cb-container"
                        style="float: left;width: 100%">
            <h:selectOneMenu id="matriculaSeparadoNrVezesEscolher" style="width: 100%"
                             value="#{ContratoControle.contratoVO.nrVezesParcelarMatricula}">
              <f:selectItems
                      value="#{ContratoControle.listaVezesParcelarMatricula}"/>
              <a4j:support
                           event="onchange"
                           ajaxSingle="true"
                           reRender="cobrarMatriculaSeparada,panelParcelaProdutoMatricula,totalContrato, valorMensalModalidade ,subtotal, planoCondicaoPagamentoVO,#{ContratoControle.updateComponente.condicaoPagamento}, resumoContrato, panelAutorizacoesCobrancaCliente"/>
            </h:selectOneMenu>
          </h:panelGroup>
        </h:panelGroup>
      </h:panelGroup>
      <%--FIM COBRAR MATRICULA PLANO NORMAL SEPARADA --%>


      <%--INICIO COBRAR REMATRICULA PLANO NORMAL SEPARADA --%>
      <h:panelGroup layout="block" id="cobrarRematriculaSeparada"
                    rendered="#{(!ContratoControle.contratoVO.plano.regimeRecorrencia) && (ContratoControle.contratoVO.plano.cobrarAdesaoSeparada) && (!ContratoControle.contratoVO.usouCupomDescontoParaAdesao) && (ContratoControle.contratoVO.valorRematricula > 0 ) && (ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida)}"
                    styleClass="gridValoresContrato col-text-align-left" style="margin-bottom: 10px;">
        <h:panelGroup layout="block" style="width: 50%;display: inline-block;text-align: left;vertical-align: top;" styleClass="checkbox-fa">
          <h:outputText
                  styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                  value="REMATRÍCULA"
                  style="display: block;text-align: left;margin-bottom: 20px"/>
          <a4j:commandLink id="btnMarcarRematriculaSeparado"
                           styleClass="linkPadrao"
                           action="#{ContratoControle.marcarMatriculaSeparado}"
                           reRender="panelProdutoParcela,panelParcelaProdutoMatricula,cobrarMatriculaSeparada,totalContrato, valorMensalModalidade ,subtotal, planoCondicaoPagamentoVO,#{ContratoControle.updateComponente.condicaoPagamento}, resumoContrato, panelAutorizacoesCobrancaCliente">
            <h:outputText styleClass="#{ContratoControle.contratoVO.cobrarMatriculaSeparada ? 'fa-icon-check' : 'fa-icon-check-empty' }  texto-size-16 texto-cor-cinza"/>
            <h:outputText style="margin-left: 5px" value="Cobrar separado "
                          styleClass="texto-font texto-size-16 texto-cor-cinza"/>
          </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup layout="block" style="width: 50%;display: inline-block" rendered="#{ContratoControle.contratoVO.cobrarMatriculaSeparada && ContratoControle.contratoVO.valorRematricula > 0}">
          <h:outputText
                  styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                  value="PARCELAMENTO"
                  style="display: block;text-align: left;margin-bottom: 10px"/>
          <h:panelGroup layout="block" styleClass="cb-container"
                        style="float: left;width: 100%">
            <h:selectOneMenu id="rematriculaSeparadoNrVezesEscolher" style="width: 100%"
                             value="#{ContratoControle.contratoVO.nrVezesParcelarMatricula}">
              <f:selectItems
                      value="#{ContratoControle.listaVezesParcelarMatricula}"/>
              <a4j:support
                      event="onchange"
                      ajaxSingle="true"
                      reRender="cobrarMatriculaSeparada,panelParcelaProdutoMatricula,totalContrato, valorMensalModalidade ,subtotal, planoCondicaoPagamentoVO,#{ContratoControle.updateComponente.condicaoPagamento}, resumoContrato, panelAutorizacoesCobrancaCliente"/>
            </h:selectOneMenu>
          </h:panelGroup>
        </h:panelGroup>
      </h:panelGroup>
      <%--FIM COBRAR REMATRICULA PLANO NORMAL SEPARADA --%>


      <%--INICIO COBRAR PRODUTOS SEPARADOS --%>
      <h:panelGroup layout="block" id="cobrarProdutosSeparado"
                    rendered="#{(ContratoControle.contratoVO.plano.cobrarProdutoSeparado) && (!ContratoControle.contratoVO.usouCupomDescontoParaAdesao) && (ContratoControle.contratoVO.totalProdutosCobrarSeparado > 0) && (ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida)}"
                    styleClass="gridValoresContrato col-text-align-left" style="margin-bottom: 10px;">
        <h:panelGroup layout="block" style="width: 50%;display: inline-block;text-align: left;vertical-align: top;" styleClass="checkbox-fa">
          <h:outputText
                  styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                  value="PRODUTOS"
                  style="display: block;text-align: left;margin-bottom: 20px"/>
          <a4j:commandLink id="btnMarcarProdutoSeparado"
                           styleClass="linkPadrao"
                           action="#{ContratoControle.marcarProdutoSeparado}"
                           reRender="panelProdutoParcela,panelParcelaProdutoMatricula,cobrarProdutosSeparado,totalContrato, valorMensalModalidade ,subtotal, planoCondicaoPagamentoVO,#{ContratoControle.updateComponente.condicaoPagamento}, resumoContrato, panelAutorizacoesCobrancaCliente"
                           oncomplete="#{ContratoControle.msgAlert};#{ContratoControle.mensagemNotificar}">
            <h:outputText styleClass="#{ContratoControle.contratoVO.cobrarProdutoSeparado ? 'fa-icon-check' : 'fa-icon-check-empty' }  texto-size-16 texto-cor-cinza"/>
            <h:outputText style="margin-left: 5px" value="Cobrar separado "
                          styleClass="texto-font texto-size-16 texto-cor-cinza"/>
          </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup layout="block" style="width: 50%;display: inline-block" rendered="#{ContratoControle.contratoVO.cobrarProdutoSeparado && ContratoControle.contratoVO.totalProdutosCobrarSeparado > 0}">
          <h:outputText
                  styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                  value="PARCELAMENTO"
                  style="display: block;text-align: left;margin-bottom: 10px"/>
          <h:panelGroup layout="block" styleClass="cb-container"
                        style="float: left;width: 100%">
            <h:selectOneMenu style="width: 100%"
                             value="#{ContratoControle.contratoVO.nrVezesParcelarProduto}">
              <f:selectItems
                      value="#{ContratoControle.listaVezesParcelarProdutos}"/>
              <a4j:support
                      event="onchange"
                      ajaxSingle="true"
                      reRender="cobrarProdutosSeparado,panelParcelaProdutoMatricula,totalContrato, valorMensalModalidade ,subtotal, planoCondicaoPagamentoVO,#{ContratoControle.updateComponente.condicaoPagamento}, resumoContrato, panelAutorizacoesCobrancaCliente"/>
            </h:selectOneMenu>
          </h:panelGroup>
        </h:panelGroup>
      </h:panelGroup>
      <%--FIM COBRAR PRODUTOS SEPARADOS --%>


    </h:panelGroup>

    <h:panelGroup id="panelAutorizacoesCobrancaCliente" layout="block" styleClass="tudo" >
      <h:panelGroup layout="block" rendered="#{ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.tipoConvenioCobranca.codigo != 0}">
        <jsp:include flush="true" page="cliente/include_autorizacao_cobranca_reduzido.jsp" />
      </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" id="panelProdutoParcela" styleClass="tudo">
      <h:panelGroup layout="block" styleClass="container-botoes"
                    rendered="#{(ContratoControle.contratoVO.plano.regimeRecorrencia) || ((!ContratoControle.contratoVO.cobrarProdutoSeparado) && (!ContratoControle.contratoVO.cobrarMatriculaSeparada))}"
                    style="display:inline-table;min-height: 50px;line-height: 50px;margin: 0px 0px 10px 0px;width: calc(100% - 40px); border-top: 1px solid #777; border-bottom: 1px solid #777">
        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold pull-left"
                      value="PARCELA(s)"/>
        <h:panelGroup layout="block" styleClass="col-text-align-right" >
          <h:panelGroup  layout="block" styleClass="containerAdesao"  rendered="#{ContratoControle.contratoVO.gerarParcelaParaProdutos && ContratoControle.contratoVO.somaAdesao > 0 && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
            <h:outputText id="nrCondicaoPagamento1"
                          styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                          value="Adesão #{ContratoControle.contratoVO.nrParcelasAdesao}x "/>
            <h:outputLabel  value="#{MovPagamentoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px" />
            <h:outputText id="valorCondicaoPagamento1"
                          styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold "
                          value="#{ContratoControle.contratoVO.valorParcelasAdesao}">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>

          </h:panelGroup>
          <h:panelGroup layout="block" styleClass="containerAnuidade"  rendered="#{ContratoControle.contratoVO.gerarParcelaAnuidadeSeparada && ContratoControle.contratoVO.nrParcelasAnuidades > 0}">

            <c:choose>
              <c:when test="${ContratoControle.contratoVO.plano.planoRecorrencia.parcelarAnuidade and fn:length(ContratoControle.contratoVO.plano.planoRecorrencia.parcelasAnuidade) > 0}">
                <h:outputText id="txtAnuidade"
                              styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                              value="Anuidade #{ContratoControle.anuidadeConfigurada} "/>
              </c:when>
              <c:otherwise>
                <h:outputText id="nrAnuidades"
                              styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                              value="Anuidade #{ContratoControle.contratoVO.nrParcelasAnuidades}x "/>

                <h:outputLabel value="#{ContratoControle.empresaLogado.moeda}"
                               styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold "
                               style="padding: 3px"/>

                <h:outputText id="valorAnuidade"
                              styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold "
                              value="#{ContratoControle.contratoVO.valorPorAnuidade}">
                  <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
              </c:otherwise>
            </c:choose>

          </h:panelGroup>
          <h:panelGroup layout="block" styleClass="containerParcProdutos"  rendered="#{ContratoControle.mostrarParcelamentoProdutos && ContratoControle.contratoVO.plano.regimeRecorrencia}">
            <h:outputText id="nrParcProdutos"
                          styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                          value="Produto(s) #{ContratoControle.contratoVO.nrVezesParcelarProduto}x "/>

            <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px" />
            <h:outputText id="valorParcProduto"
                          styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                          value="#{ContratoControle.contratoVO.valorParcelasProduto}">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
          </h:panelGroup>


          <h:panelGroup  layout="block" rendered="#{!ContratoControle.contratoVO.dividirProdutosNasParcelas && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
            <h:outputText id="nrCondicaoPagamento"
                          styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                          value=" 1X"/>

            <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px" />
            <h:outputText id="valorCondicaoPagamento"
                          styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                          value="#{ContratoControle.valorPrimeiraParcelaComDesconto} ">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
          </h:panelGroup>
          <h:panelGroup layout="block" rendered="#{!ContratoControle.contratoVO.dividirProdutosNasParcelas && !ContratoControle.parcelaAvistaContrato && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
            <h:outputText rendered="#{ContratoControle.quantidadeParcelasValorContrato > 0}"
                    styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                    value=" #{ContratoControle.quantidadeParcelasValorContrato}X"/>

            <h:outputLabel rendered="#{ContratoControle.quantidadeParcelasValorContrato > 0}"  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px" />
            <h:outputText rendered="#{ContratoControle.quantidadeParcelasValorContrato > 0}"
                    styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold "
                    value="#{ContratoControle.valorParcelaContrato}">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
           <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold " value="#{ContratoControle.parcelasValorDiferente}" />
          </h:panelGroup>
          <br/>
          <h:outputText escape= "false"
                        rendered="#{ContratoControle.quantidadeParcelasValorContrato > 0 && ContratoControle.totalDeParcelaComDescontoExcetoPrimeira > 0}"
                        styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold "
                        value="+ #{ContratoControle.empresaLogado.moeda} #{ContratoControle.descontoParcelaExcetoPrimeira} de desconto divididos em #{ContratoControle.totalDeParcelaComDescontoExcetoPrimeira} parcela(s)">

          </h:outputText>
          <h:panelGroup layout="block"
                  rendered="#{ContratoControle.contratoVO.dividirProdutosNasParcelas}">
            <h:outputText
                    styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                    value=" #{ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.nrParcelas}X"/>

            <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold " style="padding: 3px" />
            <h:outputText
                    styleClass="texto-size-14 texto-font texto-cor-cinza-2 texto-bold"
                    value="#{ContratoControle.valorParcelaContrato}">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>

          </h:panelGroup>
        </h:panelGroup>
      </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" id="panelParcelaProdutoMatricula" styleClass="tudo">
      <h:panelGroup layout="block" styleClass="container-botoes"
                    rendered="#{((ContratoControle.contratoVO.cobrarProdutoSeparado) || (ContratoControle.contratoVO.cobrarMatriculaSeparada)) && (!ContratoControle.contratoVO.plano.regimeRecorrencia)}"
                    style="display:inline-table;min-height: 50px;line-height: 50px;margin: 0px 0px 10px 0px;width: calc(100% - 40px); border-top: 1px solid #777; border-bottom: 1px solid #777">

        <h:panelGrid columns="1">
          <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza-2 texto-bold pull-left"
                        value="PARCELA(s)"/>

        </h:panelGrid>

        <h:panelGrid columns="2"
                     cellspacing="10px"
                     columnClasses="colunaEsquerda, colunaDireita,colunaDireita,colunaDireita">

          <%-- INICIO PARCELAS MATRICULA E REMATRICULA--%>
          <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                        rendered="#{(ContratoControle.mostrarParcelamentoMatricula) && (ContratoControle.contratoVO.valorMatricula > 0)}"
                        value="Matrícula"/>
          <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                        rendered="#{(ContratoControle.mostrarParcelamentoMatricula) && (ContratoControle.contratoVO.valorRematricula > 0)}"
                        value="Rematrícula"/>
          <h:panelGroup rendered="#{(ContratoControle.mostrarParcelamentoMatricula)}">

            <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                          style="margin-left: 10px"
                          value="#{ContratoControle.contratoVO.nrVezesParcelarMatricula}x "/>

            <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold " style="padding: 3px" />
            <h:outputText
                    rendered="#{(ContratoControle.contratoVO.valorMatricula > 0)}"
                    styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                    value="#{ContratoControle.contratoVO.valorParcelasMatricula}">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>

            <h:outputText
                    rendered="#{(ContratoControle.contratoVO.valorRematricula > 0)}"
                    styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                    value="#{ContratoControle.contratoVO.valorParcelasRematricula}">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
          </h:panelGroup>

          <%-- INICIO PARCELAS PRODUTOS --%>
          <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                        rendered="#{(ContratoControle.mostrarParcelamentoProdutos)}"
                        value="Produto(s)"/>
          <h:panelGroup rendered="#{(ContratoControle.mostrarParcelamentoProdutos)}">

            <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                          style="margin-left: 10px"
                          value="#{ContratoControle.contratoVO.nrVezesParcelarProduto}x "/>

            <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold " style="padding: 3px" />
            <h:outputText
                    styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                    value="#{ContratoControle.contratoVO.valorParcelasProduto}">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
          </h:panelGroup>

          <%-- INICIO PARCELAS PLANO --%>
          <h:outputText styleClass="texto-size-16 texto-font texto-bold texto-cor-cinza"
                        value="Plano"/>
          <h:panelGroup >


            <h:panelGroup  layout="block" rendered="#{!ContratoControle.contratoVO.dividirProdutosNasParcelas && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
              <h:outputText id="nrCondicaoPagamento11"
                            styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                            value=" 1x "/>

              <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold " style="padding: 3px" />
              <h:outputText id="valorCondicaoPagamento111"
                            styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold "
                            value="#{ContratoControle.valorPrimeiraParcelaComDesconto} ">
                <f:converter converterId="FormatadorNumerico"/>
              </h:outputText>
            </h:panelGroup>
            <h:panelGroup layout="block" rendered="#{!ContratoControle.contratoVO.dividirProdutosNasParcelas && !ContratoControle.parcelaAvistaContrato && ContratoControle.contratoVO.planoCondicaoPagamento.condicaoPagamento.condicaoPagamentoEscolhida}">
              <h:outputText
                      style="margin-left: 10px"
                      styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold"
                      value="#{ContratoControle.numeroParcelaContrato}x "/>

              <h:outputLabel  value="#{ContratoControle.empresaLogado.moeda}" styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold " style="padding: 3px" />
              <h:outputText
                      styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold "
                      value="#{ContratoControle.valorParcelaContrato}">
                <f:converter converterId="FormatadorNumerico"/>
              </h:outputText>

            </h:panelGroup>
          </h:panelGroup>

        </h:panelGrid>

      </h:panelGroup>
    </h:panelGroup>


    <h:panelGroup layout="block" id="btnConcluirNegociacao" style="margin: 0px 0px 20px 0px" styleClass="container-botoes">
      <a4j:commandLink id="btnConferirNegociacao"
                       action="#{ContratoControle.doValidarContrato}"
                       value="Conferir Negociação"
                       reRender="mdlMensagemGenerica, panelAutorizacaoFuncionalidade"
                       oncomplete="#{ContratoControle.msgAlert};#{ContratoControle.mensagemNotificar}"
                       styleClass="botaoPrimarioGrande texto-font texto-size-20 step3">
        <a4j:actionparam value="conferirNegociacao" id="conferirNegociacao" name="conferirNegociacao"></a4j:actionparam>
       </a4j:commandLink>

        <a4j:jsFunction name="abrirTelaConferir" action="tela7"/>
        <a4j:jsFunction name="abrirModalSemAutorizacaoCobranca" reRender="formSemAutorizacaoCobranca" oncomplete="Richfaces.showModalPanel('panelConfirmaContratoSemAutorizacaoCobranca');"/>
    </h:panelGroup>
  </h:panelGroup>
</h:panelGroup>

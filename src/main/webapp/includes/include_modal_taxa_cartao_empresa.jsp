<%@include file="imports.jsp" %>

<rich:modalPanel id="mdllancartaxas" styleClass="novaModal" width="800" autosized="true" shadowOpacity="true" top="50">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Taxas do cart�o - #{FormaPagamentoControle.formaPagamentoEmpresa.empresa.nome}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkmdllancartaxas"/>
            <rich:componentControl for="mdllancartaxas" attachTo="hidelinkmdllancartaxas" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formmdllancartaxas" style="height: 500px; overflow-y: auto">
        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                     columnClasses="classEsquerda, classDireita" width="100%">
            <h:panelGroup rendered="#{!FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento.equals('CD')}">
                <h:outputText styleClass="tituloCampos" value="* Nr. Parcelas:"/>
            </h:panelGroup>
            <h:panelGroup rendered="#{!FormaPagamentoControle.formaPagamentoVO.tipoFormaPagamento.equals('CD')}">
                <h:panelGroup id="grupoNumeroParcelasEmpresa" layout="block">
                    <h:panelGroup>
                        <h:inputText id="nrMeses" size="5" maxlength="3" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     style="float: left; margin-top: auto;"
                                     onkeypress="return mascara(this.form, this.id , '99', event);"
                                     value="#{FormaPagamentoControle.taxaCartaoEmpresaVO.nrmeses}">
                        </h:inputText>
                    </h:panelGroup>
                    <h:panelGroup>
                        <a4j:commandLink id="adicionarNumeroParcelasEmpresaNaListaSelecionado"
                                         action="#{FormaPagamentoControle.adicionarNumeroParcelaEmpresaNaLista}"
                                         reRender="nrMeses, grupoNumeroParcelasEmpresa"
                                         style="float: left; margin-left: 5px; margin-top: 9px"
                                         styleClass="fa-icon-plus-sign texto-size-20"/>
                        <h:panelGrid id="quantidadeNumeroParcelasEmpresa"
                                     style="float: left; margin-top: 5px; margin-left: 5px">
                            <h:panelGroup layout="block">
                                <h:outputText styleClass="tooltipster" style="color: #0090FF;"
                                              title="#{FormaPagamentoControle.quantidadeNumeroParcelaEmpresaNaListaApresentar}"
                                              value="#{FormaPagamentoControle.tamanhoListaQuantidadeNumeroParcelaEmpresa}">
                                </h:outputText>
                            </h:panelGroup>
                        </h:panelGrid>
                        <a4j:commandLink id="limparNumeroParcelasEmpresaSelecionadas"
                                         styleClass="fa-icon-trash texto-size-20"
                                         style="float: left; margin-top: 9px; margin-left: 5px"
                                         reRender="nrMeses, grupoNumeroParcelasEmpresa"
                                         action="#{FormaPagamentoControle.limparQuantidadeNumeroParcelaEmpresaNaLista}"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_FormaPagamento_taxaCartao}"/>
            <h:inputText id="taxa" size="6" maxlength="6"
                         onkeyup="return porcentagem(this);"
                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                         value="#{FormaPagamentoControle.taxaCartaoEmpresaVO.taxa}">
            </h:inputText>

            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_FormaPagamento_adquirente}:"/>
            <h:panelGroup id="grupoEmpresaAdquirentes">
                <h:panelGroup layout="block" styleClass="block cb-container"
                              style="float: left; margin-top: auto;">
                    <h:selectOneMenu id= "empresaAdquirente" value="#{FormaPagamentoControle.taxaCartaoEmpresaVO.adquirenteVO.codigo}"
                                     styleClass="form">
                        <f:selectItems value="#{FormaPagamentoControle.adquirentes}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <h:panelGroup id="quantidadeEmpresaAdquirente" styleClass="form">
                    <a4j:commandLink id="adicionarAdquirenteEmpresaNaListaSelecionado"
                                       style="float: left; margin-left: 5px; margin-top: 9px"
                                       styleClass="fa-icon-plus-sign texto-size-20"
                                       action="#{FormaPagamentoControle.adicionarAdquirenteEmpresaNaLista}"
                                       reRender="empresaAdquirente, grupoEmpresaAdquirentes, quantidadeEmpresaAdquirenteApresentar"/>

                    <h:panelGrid id="quantidadeEmpresaAdquirenteApresentar" style="float: left; margin-top: 5px; margin-left: 5px">
                        <h:panelGroup layout="block">
                            <h:outputText styleClass="tooltipster" style="color: #0090FF;" title="#{FormaPagamentoControle.adquirenteEmpresaSelecionadasApresentar}" value="#{FormaPagamentoControle.tamanhoListaAdquirentesEmpresa}">
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGrid>
                    <a4j:commandLink id="limparAdquirenteSelecionadas"
                                       styleClass="fa-icon-trash texto-size-20"
                                       style="float: left; margin-top: 9px; margin-left: 5px"
                                       reRender="empresaAdquirente, grupoEmpresaAdquirentes"
                                       action="#{FormaPagamentoControle.limparAdquirentesEmpresaNaLista}"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:outputText styleClass="tituloCampos" value="* Bandeira:"/>
            <h:panelGroup id="grupoEmpresaOperadoras">
                <h:panelGroup layout="block" styleClass="block cb-container"
                              style="float: left; margin-top: auto;">
                    <h:selectOneMenu value="#{FormaPagamentoControle.taxaCartaoEmpresaVO.bandeira.codigo}" id="bandeiraEmpresa"
                                     styleClass="form">
                        <f:selectItems value="#{FormaPagamentoControle.operadoras}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <h:panelGroup>
                    <a4j:commandLink id="adicionarbandeiraEmpresaNaListaSelecionado"
                                       action="#{FormaPagamentoControle.adicionarBanceiraEmpresaNaLista}"
                                       reRender="operadora, grupoEmpresaOperadoras"
                                       style="float: left; margin-left: 5px; margin-top: 9px"
                                       styleClass="fa-icon-plus-sign texto-size-20" />

                    <h:panelGrid id="quantidadeBandeiraEmpresa" style="float: left; margin-top: 5px; margin-left: 5px">
                        <h:panelGroup layout="block">
                            <h:outputText styleClass="tooltipster" style="color: #0090FF;" title="#{FormaPagamentoControle.bandeiraEmpresaSelecionadasApresentar}" value="#{FormaPagamentoControle.tamanhoListaBandeiraEmpresa}">
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGrid>
                    <a4j:commandLink id="limparBandeirasEmpresaSelecionadas"
                                       reRender="bandeira, grupoEmpresaOperadoras"
                                       styleClass="fa-icon-trash texto-size-20"
                                       style="float: left; margin-top: 9px; margin-left: 5px"
                                       action="#{FormaPagamentoControle.limparBandeiraEmpresaNaLista}"/>
                </h:panelGroup>
            </h:panelGroup>
            <h:outputText styleClass="tituloCampos"
                          value="* #{msg_aplic.prt_FormaPagamento_vigenciaInicial}"/>
            <h:panelGroup styleClass="dateTimeCustom" >
                <rich:calendar id="vigenciaInicialTaxa"
                               datePattern="dd/MM/yyyy"
                               buttonIcon="/imagens_flat/calendar-button.svg"
                               inputSize="8"
                               showWeekDaysBar="false"
                               showWeeksBar="false"
                               enableManualInput="true"
                               styleClass="form"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputchange="return validar_Data(this.id);"
                               value="#{FormaPagamentoControle.taxaCartaoEmpresaVO.vigenciaInicial}"/>
            </h:panelGroup>


            <h:outputText styleClass="tituloCampos"
                          value="* #{msg_aplic.prt_FormaPagamento_vigenciaFinal}"/>
            <h:panelGroup styleClass="dateTimeCustom" >
                <rich:calendar id="vigenciaFinalTaxa"
                               datePattern="dd/MM/yyyy"
                               buttonIcon="/imagens_flat/calendar-button.svg"
                               inputSize="8"
                               showWeekDaysBar="false"
                               showWeeksBar="false"
                               enableManualInput="true"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputchange="return validar_Data(this.id);"
                               styleClass="form"
                               value="#{FormaPagamentoControle.taxaCartaoEmpresaVO.vigenciaFinal}"/>
            </h:panelGroup>

        </h:panelGrid>

        <div style="text-align: center">
            <a4j:commandButton id="adicionar"
                               action="#{FormaPagamentoControle.adicionarTaxaEmpresa}"
                               value="Adicionar" oncomplete="#{FormaPagamentoControle.msgAlert}"
                               style="vertical-align: middle;"
                               reRender="formmdllancartaxas"
                               styleClass="botoes nvoBt bt"/>
        </div>

        <div class="painelDadosAluno" style="width: calc(98.5% - 10px); height: auto;">
            <div class="tituloPainelAluno">
                <h:outputText styleClass="negrito cinzaEscuro pl20"
                              value="Taxas cadastradas"
                              rendered="#{empty FormaPagamentoControle.agrupamento}"
                              style="line-height: 40px;font-size: 14px !important;"/>

                <a4j:commandLink styleClass="linkAzul pl20" style="line-height: 40px;font-size: 14px !important;"
                                 action="#{FormaPagamentoControle.limparAgrupamento}"
                                 rendered="#{not empty FormaPagamentoControle.agrupamento}"
                                 reRender="formmdllancartaxas">
                    <i class="fa-icon-arrow-left"></i>
                     Voltar
                </a4j:commandLink>
            </div>
            <div class="conteudoDadosCliente" style="min-height: 50px;">

                <h:panelGroup rendered="#{empty FormaPagamentoControle.agrupamento}">
                    <table width="100%" class="tblHeaderLeft">
                        <tr>
                            <td style="padding:10px">
                                <a4j:commandLink value="Taxas sem adquirente ou bandeira espec�ficas"
                                                 styleClass="linkAzul" rendered="#{FormaPagamentoControle.temTaxaGeral}"
                                                 action="#{FormaPagamentoControle.selecionarAgrupamentoTaxaEmpresaGeral}"
                                                 reRender="formmdllancartaxas">
                                </a4j:commandLink>
                                <h:outputText value="Nenhuma taxa cadastrada para essa empresa." rendered="#{!FormaPagamentoControle.temTaxaGeral and empty FormaPagamentoControle.agrupamentos}"/>
                            </td>
                        </tr>
                        <a4j:repeat value="#{FormaPagamentoControle.agrupamentos}" var="ag">
                            <tr>
                                <td style="padding: 5px 10px">
                                    <a4j:commandLink value="#{ag}"
                                                     styleClass="linkAzul"
                                                     action="#{FormaPagamentoControle.selecionarAgrupamentoTaxaEmpresa}"
                                                     reRender="formmdllancartaxas">
                                    </a4j:commandLink>
                                </td>
                            </tr>
                        </a4j:repeat>


                    </table>
                </h:panelGroup>




                <h:dataTable id="dtTaxasEmpresa" width="100%" styleClass="tblHeaderLeft contratoSelecionado caixainfo"
                             rowClasses="linhaImpar, linhaPar"
                             rendered="#{not empty FormaPagamentoControle.agrupamento and empty FormaPagamentoControle.taxaCartaoVigenciaEmpresa}"
                             columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                             value="#{FormaPagamentoControle.taxasOrganizadas[FormaPagamentoControle.agrupamento]}" var="taxaCartaoEmpresa">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Nr. Parcelas" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>
                        <h:outputText value="#{taxaCartaoEmpresa.nrmeses}" styleClass="texto-size-14 cinza"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Taxa Atual" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>
                        <h:outputText value="#{taxaCartaoEmpresa.taxa}" styleClass="texto-size-14 cinza">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Vigente Desde" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>
                        <h:outputText value="#{taxaCartaoEmpresa.vigenciaInicial_Apresentar}" styleClass="texto-size-14 cinza"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Vigente At�" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>
                        <h:outputText value="#{taxaCartaoEmpresa.vigenciaFinal_Apresentar}" styleClass="texto-size-14 cinza"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>

                        <a4j:commandLink id="removerTaxaEmpresa"
                                         action="#{FormaPagamentoControle.removerTaxaEmpresa}"
                                         reRender="formmdllancartaxas"
                                         style="vertical-align: middle; display: initial;"
                                         styleClass="botoes nvoBt btSec " accesskey="7">
                            <i class="fa-icon-trash"></i>
                        </a4j:commandLink>

                        <a4j:commandLink id="selecionarTaxaEmpresa"
                                           action="#{FormaPagamentoControle.selecionarTaxaEmpresa}"
                                           value="Editar / Vig�ncias"
                                           style="vertical-align: middle;display: initial;"
                                           reRender="formmdllancartaxas"
                                           styleClass="botoes nvoBt btSec"
                                           accesskey="9"/>

                    </h:column>
                </h:dataTable>

                    <%--TAXA CART�O VIGENCIA--%>
                <h:panelGrid id="panelTaxaCartaoVigenciaEMpresa"
                             rendered="#{not empty FormaPagamentoControle.agrupamento and not empty FormaPagamentoControle.taxaCartaoVigenciaEmpresa}" columns="1"
                             width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">


                    <h:outputText styleClass="titulo" style="color: red; display: block; "
                                  value="* Ao modificar a vig�ncia da taxa, as opera��es j� realizadas no Financeiro n�o ser�o modificadas."/>

                    <a4j:commandButton id="adicionarTaxaVigenciaempresa"
                                       action="#{FormaPagamentoControle.adicionarNovaTaxaVigenciaEmpresa}"
                                       reRender="formmdllancartaxas" value="Adicionar nova taxa"
                                       styleClass="botoes nvoBt bt" accesskey="5" />

                    <h:dataTable id="dtTaxasVigenciaempresa" width="100%"  styleClass="tblHeaderLeft contratoSelecionado caixainfo"
                                 rowClasses="linhaImpar, linhaPar"
                                 columnClasses="colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento, colunaAlinhamento"
                                 value="#{FormaPagamentoControle.taxaCartaoVigenciaEmpresa}" var="taxaCartaoVigenciaEmpresa">


                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Parcelas" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:outputText value="#{taxaCartaoVigenciaEmpresa.nrmeses}" styleClass="texto-size-14 cinza"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Taxa" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:inputText size="5" maxlength="5" id="taxaVigencia"
                                         onkeypress="return mascara(this.form, this.id , '9.99', event);"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{taxaCartaoVigenciaEmpresa.taxa}">
                            </h:inputText>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Adquirente" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:panelGroup layout="block" styleClass="block cb-container">
                                <h:selectOneMenu value="#{taxaCartaoVigenciaEmpresa.adquirenteVO.codigo}"
                                                 styleClass="form">
                                    <f:selectItems value="#{FormaPagamentoControle.adquirentes}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:column>

                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Bandeira" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:panelGroup layout="block" styleClass="block cb-container">
                                <h:selectOneMenu value="#{taxaCartaoVigenciaEmpresa.bandeira.codigo}"
                                                 styleClass="form">
                                    <f:selectItems value="#{FormaPagamentoControle.operadoras}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </h:column>


                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Vig. Inicial" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:panelGroup styleClass="dateTimeCustom" >
                                <rich:calendar datePattern="dd/MM/yyyy"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputSize="8"
                                               showWeekDaysBar="false"
                                               showWeeksBar="false"
                                               enableManualInput="true"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               styleClass="form"
                                               value="#{taxaCartaoVigenciaEmpresa.vigenciaInicial}"/>
                            </h:panelGroup>
                        </h:column>


                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Vig. Final" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <h:panelGroup styleClass="dateTimeCustom" >
                                <rich:calendar datePattern="dd/MM/yyyy"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               inputSize="8"
                                               showWeekDaysBar="false"
                                               showWeeksBar="false"
                                               enableManualInput="true"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               styleClass="form"
                                               value="#{taxaCartaoVigenciaEmpresa.vigenciaFinal}"/>
                            </h:panelGroup>
                        </h:column>


                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper"/>
                            </f:facet>
                            <a4j:commandLink action="#{FormaPagamentoControle.removerTaxaVigenciaEmpresa}"
                                             reRender="formmdllancartaxas" style="display: initial"
                                             accesskey="7"
                                             styleClass="botoes nvoBt btSec " >
                                <i class="fa-icon-trash"></i>
                            </a4j:commandLink>
                        </h:column>
                    </h:dataTable>


                    <a4j:commandButton id="gravarTaxaVigencia"
                                       action="#{FormaPagamentoControle.gravarTaxaVigenciaTelaEmpresa}"
                                       value="Gravar Vig�ncia da Taxa"
                                       style="vertical-align: middle"
                                       reRender="formmdllancartaxas"
                                       styleClass="botoes nvoBt btSec"/>
                </h:panelGrid>


            </div>
        </div>

        <div style="text-align: center; margin: 20px 0 10px 0;">
            <a onclick="Richfaces.hideModalPanel('mdllancartaxas')" class="botoes nvoBt bt" style="cursor: pointer">
                Concluir
            </a>
        </div>



    </a4j:form>
</rich:modalPanel>
<%-- 
    Document   : modal_grafico_comparativo_bi
    Created on : 16/02/2015, 22:22:06
    Author     : alcides
--%>

<%@include file="imports.jsp" %>
<script type="text/javascript">

    function carregarGraficoFinanceiro() {
        try {


            var chart;

    ${BIFinanceiroControle.dadosGrafico}



            // SERIAL CHART
            chart = new AmCharts.AmSerialChart();
            chart.dataProvider = chartData;
            chart.categoryField = "year";
            chart.startDuration = 0.5;
            chart.balloon.color = "#000000";

            // AXES
            // category
            var categoryAxis = chart.categoryAxis;
            categoryAxis.fillAlpha = 1;
            categoryAxis.fillColor = "#FAFAFA";
            categoryAxis.gridAlpha = 0;
            categoryAxis.axisAlpha = 0;
            categoryAxis.gridPosition = "start";
            categoryAxis.position = "top";

            // value
            var valueAxis = new AmCharts.ValueAxis();
            valueAxis.title = "Resumo financeiro";
            valueAxis.dashLength = 5;
            valueAxis.axisAlpha = 0;
            valueAxis.minimum = 1;
            //                valueAxis.maximum = 100;
            valueAxis.integersOnly = true;
            valueAxis.gridCount = 10;
            valueAxis.reversed = false; // this line makes the value axis reversed
            chart.addValueAxis(valueAxis);

            // GRAPHS
            // Italy graph
            var graph = new AmCharts.AmGraph();
            graph.title = "Receita";
            graph.valueField = "receita";
            graph.hidden = false; // this line makes the graph initially hidden
            graph.balloonText = "Receita em [[category]]: [[value]]";
            graph.lineAlpha = 1;
            graph.bullet = "round";
            chart.addGraph(graph);

            // Germany graph
            var graph = new AmCharts.AmGraph();
            graph.title = "Faturamento";
            graph.valueField = "faturamento";
            graph.balloonText = "Faturamento em [[category]]: [[value]]";
            graph.bullet = "round";
            chart.addGraph(graph);

            // United Kingdom graph
            var graph = new AmCharts.AmGraph();
            graph.title = "Despesas";
            graph.valueField = "despesas";
            graph.balloonText = "Despesas em [[category]]: [[value]]";
            graph.bullet = "round";
            chart.addGraph(graph);

            // CURSOR
            var chartCursor = new AmCharts.ChartCursor();
            chartCursor.cursorPosition = "mouse";
            chartCursor.zoomable = false;
            chartCursor.cursorAlpha = 0;
            chart.addChartCursor(chartCursor);

            // LEGEND
            var legend = new AmCharts.AmLegend();
            legend.useGraphSettings = true;
            chart.addLegend(legend);

            // WRITE
            chart.write("chartdiv");



        } catch (err) {
            alert(err);
        }
    }

</script>

<rich:modalPanel id="panelGraficoComparativoBI" width="700" height="400">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Gr�fico comparativo BI"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkpanelGraficoComparativoBI"/>
            <rich:componentControl for="panelGraficoComparativoBI" attachTo="hidelinkpanelGraficoComparativoBI" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formModalComparativo">


        <rich:panel style="width: 700px; height: 390px" >
            <div id="chartdiv" style="width: 100%; height: 100%;"></div>
        </rich:panel>


    </h:form>
</rich:modalPanel>


<%-- 
    Document   : include_consultarTurma
    Created on : 02/01/2012, 11:37:58
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@include file="imports.jsp" %>
<script type="text/javascript" language="javascript" src="script/negociacaoContrato_1.0.min.js"></script>
<rich:modalPanel id="panelConsultarTurma" autosized="true"  styleClass="novaModal vw90 noMargin" shadowOpacity="true" width="890" height="600" >
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Consultar Turma da Modalidade"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-16-real texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkConsultaTurma"/>
            <rich:componentControl for="panelConsultarTurma" attachTo="hidelinkConsultaTurma" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConsultarTurma" ajaxSubmit="true" style="overflow: hidden;" styleClass="tabelasSemHover">
        <a4j:jsFunction name="submterForm" status="semStatus" reRender="formConsultarTurma:panelHorarioTurmaConcatenado,formConsultarTurma:filtroHorario,formConsultarTurma:panelCheckBox" />
        <h:panelGroup id="containerConsultaTurma" layout="block">
            <h:panelGroup id="turma" layout="block"  styleClass="bg-cinza accordionPanel" style="widows: 174px;">
            <h:panelGroup styleClass="accordionBody semBorda" layout="block">
                <h:panelGrid columns="1" width="100%">
                    <h:panelGroup layout="block" style="width:100%;display: block;" >
                        <h:outputText value="DIAS DA SEMANA" styleClass="texto-cor-cinza texto-font texto-size-14 texto-bold"/>
                    </h:panelGroup>
                    <h:panelGrid id="panelCheckBox" columns="7" width="100%" style="margin-top: 5px;">
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.domingo}" target="#{ConsultarTurmaControle.consultarTurma.domingo}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.domingo ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Domingo"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.segunda}" target="#{ConsultarTurmaControle.consultarTurma.segunda}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.segunda ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Segunda"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.terca}" target="#{ConsultarTurmaControle.consultarTurma.terca}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.terca ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Ter?a"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.quarta}" target="#{ConsultarTurmaControle.consultarTurma.quarta}"/>
                                     <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.quarta ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Quarta"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.quinta}" target="#{ConsultarTurmaControle.consultarTurma.quinta}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.quinta ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Quinta"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.sexta}" target="#{ConsultarTurmaControle.consultarTurma.sexta}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.sexta ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="Sexta"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.sabado}" target="#{ConsultarTurmaControle.consultarTurma.sabado}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.sabado ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="S?bado"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="filtroHorario" columns="1" width="100%">
                    <h:panelGroup layout="block" style="width:100%;" >
                        <h:outputText value="HOR?RIO" styleClass="texto-cor-cinza texto-font texto-size-14 texto-bold"/>
                    </h:panelGroup>
                    <h:panelGrid columns="9" width="100%" style="margin-top:5px;">
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink  reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0001as0200}" target="#{ConsultarTurmaControle.consultarTurma.h0001as0200}"/>
                                <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0001as0200 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="00:00 - 01:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink  reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0201as0400}" target="#{ConsultarTurmaControle.consultarTurma.h0201as0400}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0201as0400 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="02:00 - 03:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink  reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0401as0600}" target="#{ConsultarTurmaControle.consultarTurma.h0401as0600}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0401as0600 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="04:00 - 05:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0601as0800}" target="#{ConsultarTurmaControle.consultarTurma.h0601as0800}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0601as0800 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="06:00 - 07:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink  reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h0801as1000}" target="#{ConsultarTurmaControle.consultarTurma.h0801as1000}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h0801as1000 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="08:00 - 9:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink  reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                        <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1001as1200}" target="#{ConsultarTurmaControle.consultarTurma.h1001as1200}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1001as1200 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="10:00 - 11:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="9" width="100%">
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1201as1400}" target="#{ConsultarTurmaControle.consultarTurma.h1201as1400}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1201as1400 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="12:00 - 13:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink  reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1401as1600}" target="#{ConsultarTurmaControle.consultarTurma.h1401as1600}"/>
                                        <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1401as1600 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="14:00 - 15:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink reRender="formConsultarTurma:panelCheckBox" styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1601as1800}" target="#{ConsultarTurmaControle.consultarTurma.h1601as1800}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1601as1800 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="16:00 - 17:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h1801as2000}" target="#{ConsultarTurmaControle.consultarTurma.h1801as2000}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h1801as2000 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="18:00 - 19:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink  styleClass="linkPadrao">
                                    <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h2001as2200}" target="#{ConsultarTurmaControle.consultarTurma.h2001as2200}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h2001as2200 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-font texto-cor-cinza texto-font texto-size-16" value="20:00 - 21:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="text-align: left;" styleClass="checkbox-fa semEventoClasse">
                            <div onclick="checkBoxClick(this,event);">
                                <a4j:commandLink  styleClass="linkPadrao">
                                     <f:setPropertyActionListener  value="#{!ConsultarTurmaControle.consultarTurma.h2201as0000}" target="#{ConsultarTurmaControle.consultarTurma.h2201as0000}"/>
                                    <h:outputText style="font-family: Arial" styleClass="#{ConsultarTurmaControle.consultarTurma.h2201as0000 ? 'fa-icon-check' : 'fa-icon-check-empty'} texto-cor-cinza texto-font texto-size-16" value="22:00 - 23:59"/>
                                </a4j:commandLink>
                            </div>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGroup>
            <div style="height: 51px;width: 100%;text-align: center;line-height: 51px;" class="accordionHeader" onclick="toggleAccordion(this,event);">
                <h:outputText styleClass="texto-font texto-size-14 texto-bold texto-cor-cinza" value="FILTROS" style="margin-left: 40px;float: left;line-height: 3.5;"/>
                <a4j:commandLink id="btnLimparFiltros" style="margin-left: 6px;padding: 10px;"
                                 value="#{ConsultarTurmaControle.rotuloLimparFiltros}"
                                 styleClass="texto-font texto-size-16 texto-cor-azul linkPadrao"
                                 action="#{ConsultarTurmaControle.limparFiltros}"
                                 reRender="panelCheckBox,btnLimparFiltros,filtroHorario"/>

                <a4j:commandLink id="botaoBuscarTurmas" style="margin-left:10px;" styleClass="botaoSecundario linkPadrao texto-font texto-size-16 "
                                 reRender="mensagemConsultaHorarioTurmaConcatenado, panelHorarioTurmaConcatenado, panelCheckBox"
                                 rendered="#{ConsultarTurmaControle.consultaTurma}"
                                 value="Aplicar filtro"
                                 action="#{ConsultarTurmaControle.consultarTurmasNegociacao}" title="consultar">
                </a4j:commandLink>
                <h:outputText styleClass="linkPadrao fa-icon-chevron-up texto-size-16 texto-cor-cinza bg-cinza-4 btnHandler rotate180"/>
            </div>

        </h:panelGroup>
            <h:panelGroup style="text-align: center;width: 100%" layout="block">
            <h:panelGroup id="painelTituloModalidade" layout="block" style="width: 100%;margin: 20px 40px 20px 40px;text-align: left">
                <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" value="SELECIONE A FREQU�NCIA PARA "/>
                <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value=" #{ConsultarTurmaControle.contratoModalidade.nomeModalidade_Apresentar} - "/>
                <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="#{ConsultarTurmaControle.vezesSemanaModalidade}"/>

                <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" value=" POR SEMANA"/>
            </h:panelGroup>
            <h:panelGroup id="panelHorarioTurmaConcatenado" layout="block" style="width: calc(100% - 80px);margin: 0px 40px 0px 40px;text-align: left">
                <rich:dataTable value="#{ConsultarTurmaControle.listaHorarioTurmaConcatenado}" rows="7"
                                var="horarioTurmaConcatenado" id="ConsultarTurma" width="100%"
                                styleClass="tabelaSimplesCustom showCellEmpty">
                    <rich:column  style="text-align: left" sortable="true" sortBy="#{horarioTurmaConcatenado.turma.identificador}" filterEvent="onkeyup" label="Nome da Turma" >
                        <f:facet name="header">
                            <h:outputText  style="float: left" styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="TURMA"/>
                        </f:facet>
                        <h:panelGroup layout="block" style="width: 100%;text-align: left">
                            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{horarioTurmaConcatenado.turma.identificador}" />
                        </h:panelGroup>
                    </rich:column>
                    <rich:column sortable="true" sortBy="#{horarioTurmaConcatenado.professor}"
                                 filterEvent="onkeyup" label="Professor">
                        <f:facet name="header">
                            <h:outputText style="float:left;" styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="PROFESSOR"/>
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{horarioTurmaConcatenado.professor}" />
                    </rich:column>
                    <rich:column sortable="true" sortBy="#{horarioTurmaConcatenado.nivel}"
                                 filterEvent="onkeyup" label="N?vel">
                        <f:facet name="header">
                            <h:outputText style="float: left;;" styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="NV."/>
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{horarioTurmaConcatenado.nivel}" />
                    </rich:column>
                    <rich:column sortable="true" sortBy="#{horarioTurmaConcatenado.ambiente}"
                                 filterEvent="onkeyup" label="Ambiente">
                        <f:facet name="header">
                            <h:outputText  style="float: left;" styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="AMBIENTE"/>
                        </f:facet>
                        <h:panelGroup layout="block" styleClass="textoImcompleto" style="width: 110px">
                            <h:outputText   title="#{horarioTurmaConcatenado.ambiente}" styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{horarioTurmaConcatenado.ambiente}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column sortable="true" sortBy="#{horarioTurmaConcatenado.mediaOcupacao}"
                                 filterEvent="onkeyup" label="Ocupacao">
                        <f:facet name="header">
                            <h:outputText  style="float: left;" styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="% OCUPA��O"/>
                        </f:facet>
                        <h:panelGroup layout="block" styleClass="textoImcompleto">
                            <h:outputText   title="#{horarioTurmaConcatenado.mediaOcupacao}" styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{horarioTurmaConcatenado.mediaOcupacao}"/>
                        </h:panelGroup>
                    </rich:column>


                    <rich:column sortable="true" sortBy="#{horarioTurmaConcatenado.horaInicial}" width="99"
                                 style="text-align: left" filterEvent="onkeyup" label="Hora In?cio">
                        <f:facet name="header">
                            <h:outputText  style="float: left;" styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="HOR�RIO"/>
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{horarioTurmaConcatenado.horaInicial}" />
                        <h:panelGroup layout="block" styleClass="bg-cinza-3" style="width: 15px;height: 1px;display:inline-block;vertical-align: super;margin: 0px 1px 0px 1px;"/>
                        <h:outputText  styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{horarioTurmaConcatenado.horaFinal}" />
                    </rich:column>

                    <rich:column id="checkHorarioTurmaDom" label="Dom"
                                 title="#{horarioTurmaConcatenado.tooltipDescDom}"
                                 style="width: 55px;text-align: center;background-color: #{null != horarioTurmaConcatenado.nivelDescontoTurmaHorarioDom && horarioTurmaConcatenado.horarioTurmaDom.quantidadeVagas > 0 ? horarioTurmaConcatenado.nivelDescontoTurmaHorarioDom.color : ''}"
                                 styleClass="celulaCheck checkHorarioTurma tooltipster ">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="DOM"/>
                        </f:facet>
                        <%--<div onclick="checkBoxClick(this,event);">--%>
                        <h:panelGroup   layout="block" styleClass="checkbox-fa semEventoClasse checkHorarioTurma" rendered="#{ConsultarTurmaControle.consultarTurma.domingo and horarioTurmaConcatenado.mostrarHorarioDom}">
                            <a4j:commandLink id="selectTurmaDomingo" styleClass="linkPadrao" reRender="checkHorarioTurmaDom">
                                <f:setPropertyActionListener value="#{!horarioTurmaConcatenado.horarioTurmaDom.horarioTurmaEscolhida}" target="#{horarioTurmaConcatenado.horarioTurmaDom.horarioTurmaEscolhida}"/>
                                <h:outputText id="marcarTurmaDomingo" style="__id_turma_horrio_#{horarioTurmaConcatenado.horarioTurmaDom.codigo}"
                                              title=""
                                              styleClass="texto-size-14 texto-cor-cinza texto-font #{horarioTurmaConcatenado.horarioTurmaDom.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaDom.quantidadeVagasDisponiveis}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaDom.quantidadeVagas > 0}"
                                />
                                <h:outputText styleClass="texto-size-14 texto-cor-vermelho texto-font fa-icon-sign-blank" value="#{horarioTurmaConcatenado.horarioTurmaDom.nrAlunoMatriculado}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaDom.quantidadeVagas <= 0}"
                                          title="Hor?rio com n?mero de alunos excedido."/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <%--<div />--%>
                    </rich:column>
                    <rich:column id="checkHorarioTurmaSeg" label="Seg"
                                 title="#{horarioTurmaConcatenado.tooltipDescSeg}"
                                 style="width: 55px;text-align: center; background-color:  #{null != horarioTurmaConcatenado.nivelDescontoTurmaHorarioSeg && horarioTurmaConcatenado.horarioTurmaSeg.quantidadeVagas > 0 ? horarioTurmaConcatenado.nivelDescontoTurmaHorarioSeg.color : ''} "
                                 styleClass="celulaCheck tooltipster ">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="SEG"/>
                        </f:facet>
                        <%--<div onclick="checkBoxClick(this,event);">--%>
                        <h:panelGroup  layout="block" styleClass="checkbox-fa semEventoClasse checkHorarioTurma " rendered="#{ConsultarTurmaControle.consultarTurma.segunda and horarioTurmaConcatenado.mostrarHorarioSeg}">
                            <a4j:commandLink id="selectTurmaSegunda" styleClass="linkPadrao" reRender="checkHorarioTurmaSeg ">
                                <f:setPropertyActionListener value="#{!horarioTurmaConcatenado.horarioTurmaSeg.horarioTurmaEscolhida}" target="#{horarioTurmaConcatenado.horarioTurmaSeg.horarioTurmaEscolhida}"/>
                                <h:outputText id="marcarTurmaSegunda" style="__id_turma_horrio_#{horarioTurmaConcatenado.horarioTurmaSeg.codigo}"
                                              styleClass="texto-size-14 texto-cor-cinza texto-font #{horarioTurmaConcatenado.horarioTurmaSeg.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaSeg.quantidadeVagasDisponiveis}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.quantidadeVagas > 0}"/>
                                <h:outputText styleClass="texto-size-14 texto-cor-vermelho texto-font #{horarioTurmaConcatenado.horarioTurmaSeg.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaSeg.nrAlunoMatriculado}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaSeg.quantidadeVagas <= 0}"
                                          title="Hor?rio com n?mero de alunos excedido."/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <%--</div>--%>
                    </rich:column>
                    <rich:column id="checkHorarioTurmaTer" label="Ter"
                                 title="#{horarioTurmaConcatenado.tooltipDescTer}"
                                 style="width: 55px;text-align: center;background-color: #{null != horarioTurmaConcatenado.nivelDescontoTurmaHorarioTer && horarioTurmaConcatenado.horarioTurmaTer.quantidadeVagas > 0 ? horarioTurmaConcatenado.nivelDescontoTurmaHorarioTer.color : ''}"
                                 styleClass="celulaCheck tooltipster ">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="TER"/>
                        </f:facet>
                        <%--<div onclick="checkBoxClick(this,event);">--%>
                        <h:panelGroup layout="block" styleClass="checkbox-fa semEventoClasse checkHorarioTurma" rendered="#{ConsultarTurmaControle.consultarTurma.terca and horarioTurmaConcatenado.mostrarHorarioTer}">
                            <a4j:commandLink  id="selectTurmaTerca" styleClass="linkPadrao" reRender="checkHorarioTurmaTer">
                                <f:setPropertyActionListener value="#{!horarioTurmaConcatenado.horarioTurmaTer.horarioTurmaEscolhida}" target="#{horarioTurmaConcatenado.horarioTurmaTer.horarioTurmaEscolhida}"/>
                                <h:outputText id="marcarTurmaTerca" style="__id_turma_horrio_#{horarioTurmaConcatenado.horarioTurmaTer.codigo}"
                                              styleClass="texto-size-14 texto-cor-cinza texto-font #{horarioTurmaConcatenado.horarioTurmaTer.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaTer.quantidadeVagasDisponiveis}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaTer.quantidadeVagas > 0}"/>
                                <h:outputText styleClass="texto-size-14 texto-cor-vermelho texto-font #{horarioTurmaConcatenado.horarioTurmaTer.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaTer.nrAlunoMatriculado}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaTer.quantidadeVagas <= 0}"
                                          title="Hor?rio com n?mero de alunos excedido."/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <%--</div>--%>
                    </rich:column>
                    <rich:column id="checkHorarioTurmaQua" label="Qua"
                                 title="#{horarioTurmaConcatenado.tooltipDescQua}"
                                 style="width: 55px;text-align: center;background-color: #{null != horarioTurmaConcatenado.nivelDescontoTurmaHorarioQua && horarioTurmaConcatenado.horarioTurmaQua.quantidadeVagas > 0 ? horarioTurmaConcatenado.nivelDescontoTurmaHorarioQua.color : ''}"
                                 styleClass="celulaCheck tooltipster ">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="QUA"/>
                        </f:facet>
                       <%-- <div onclick="checkBoxClick(this,event);">--%>
                        <h:panelGroup id="panelHorarioQuarta"  layout="block" styleClass="checkbox-fa semEventoClasse checkHorarioTurma" rendered="#{ConsultarTurmaControle.consultarTurma.quarta and horarioTurmaConcatenado.mostrarHorarioQua}">
                            <a4j:commandLink id="selectTurmaQuarta" styleClass="linkPadrao" reRender="checkHorarioTurmaQua">
                                <f:setPropertyActionListener
                                        value="#{!horarioTurmaConcatenado.horarioTurmaQua.horarioTurmaEscolhida}"
                                        target="#{horarioTurmaConcatenado.horarioTurmaQua.horarioTurmaEscolhida}"/>
                                <h:outputText id="marcarTurmaQuarta" style="__id_turma_horrio_#{horarioTurmaConcatenado.horarioTurmaQua.codigo}"
                                              styleClass="texto-size-14 texto-cor-cinza texto-font #{horarioTurmaConcatenado.horarioTurmaQua.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}"
                                              value="#{horarioTurmaConcatenado.horarioTurmaQua.quantidadeVagasDisponiveis}"
                                              rendered="#{horarioTurmaConcatenado.horarioTurmaQua.quantidadeVagas > 0}"/>
                                <h:outputText
                                        styleClass="texto-size-14 texto-cor-vermelho texto-font #{horarioTurmaConcatenado.horarioTurmaQua.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}"
                                        value="#{horarioTurmaConcatenado.horarioTurmaQua.nrAlunoMatriculado}"
                                        rendered="#{horarioTurmaConcatenado.horarioTurmaQua.quantidadeVagas <= 0}"
                                        title="Hor?rio com n?mero de alunos excedido."/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <%--</div>--%>
                    </rich:column>
                    <rich:column id="checkHorarioTurmaQui" label="Qui"
                                 title="#{horarioTurmaConcatenado.tooltipDescQui}"
                                 style="width: 55px;text-align: center;background-color: #{null != horarioTurmaConcatenado.nivelDescontoTurmaHorarioQui && horarioTurmaConcatenado.horarioTurmaQui.quantidadeVagas > 0 ? horarioTurmaConcatenado.nivelDescontoTurmaHorarioQui.color : ''}"
                                 styleClass="celulaCheck tooltipster ">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="QUI"/>
                        </f:facet>
                        <%--<div onclick="checkBoxClick(this,event);">--%>
                        <h:panelGroup  layout="block" styleClass="checkbox-fa semEventoClasse checkHorarioTurma" rendered="#{ConsultarTurmaControle.consultarTurma.quinta and horarioTurmaConcatenado.mostrarHorarioQui}">
                            <a4j:commandLink id="selectTurmaQuinta" styleClass="linkPadrao" reRender="checkHorarioTurmaQui">
                              <f:setPropertyActionListener
                                    value="#{!horarioTurmaConcatenado.horarioTurmaQui.horarioTurmaEscolhida}"
                                    target="#{horarioTurmaConcatenado.horarioTurmaQui.horarioTurmaEscolhida}"/>
                            <h:outputText id="marcarTurmaQuinta" style="__id_turma_horrio_#{horarioTurmaConcatenado.horarioTurmaQui.codigo}"
                                          styleClass="texto-size-14 texto-cor-cinza texto-font #{horarioTurmaConcatenado.horarioTurmaQui.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaQui.quantidadeVagasDisponiveis}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaQui.quantidadeVagas > 0}"/>
                            <h:outputText styleClass="texto-size-14 texto-cor-vermelho texto-font #{horarioTurmaConcatenado.horarioTurmaQui.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaQui.nrAlunoMatriculado}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaQui.quantidadeVagas <= 0}"
                                          title="Hor?rio com n?mero de alunos excedido."/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <%--</div>--%>
                    </rich:column>
                    <rich:column id="checkHorarioTurmaSex" label="Sex"
                                 title="#{horarioTurmaConcatenado.tooltipDescSex}"
                                 style="width: 55px;text-align: center;background-color: #{null != horarioTurmaConcatenado.nivelDescontoTurmaHorarioSex && horarioTurmaConcatenado.horarioTurmaSex.quantidadeVagas > 0 ? horarioTurmaConcatenado.nivelDescontoTurmaHorarioSex.color : ''}"
                                 styleClass="celulaCheck tooltipster ">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="SEX"/>
                        </f:facet>
                        <%--<div onclick="checkBoxClick(this,event);">--%>
                        <h:panelGroup layout="block" styleClass="checkbox-fa semEventoClasse checkHorarioTurma" rendered="#{ConsultarTurmaControle.consultarTurma.sexta and horarioTurmaConcatenado.mostrarHorarioSex}">
                            <a4j:commandLink  id="selectTurmaSexta" styleClass="linkPadrao" reRender="checkHorarioTurmaSex">
                               <f:setPropertyActionListener
                                       value="#{!horarioTurmaConcatenado.horarioTurmaSex.horarioTurmaEscolhida}"
                                       target="#{horarioTurmaConcatenado.horarioTurmaSex.horarioTurmaEscolhida}"/>
                               <h:outputText id="marcarTurmaSexta" style="__id_turma_horrio_#{horarioTurmaConcatenado.horarioTurmaSex.codigo}"
                                             styleClass="texto-size-14 texto-cor-cinza texto-font #{horarioTurmaConcatenado.horarioTurmaSex.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaSex.quantidadeVagasDisponiveis}"
                                             rendered="#{horarioTurmaConcatenado.horarioTurmaSex.quantidadeVagas > 0}"/>
                               <h:outputText styleClass="texto-size-14 texto-cor-vermelho texto-font #{horarioTurmaConcatenado.horarioTurmaSex.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaSex.nrAlunoMatriculado}"
                                          rendered="#{horarioTurmaConcatenado.horarioTurmaSex.quantidadeVagas <= 0}"
                                          title="Hor?rio com n?mero de alunos excedido."/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <%--</div>--%>
                    </rich:column>
                    <rich:column id="checkHorarioTurmaSab" label="S?b"
                                 title="#{horarioTurmaConcatenado.tooltipDescSab}"
                                 style="width: 55px;text-align: center;background-color: #{null != horarioTurmaConcatenado.nivelDescontoTurmaHorarioSab ? horarioTurmaConcatenado.nivelDescontoTurmaHorarioSab.color : ''}"
                                 styleClass="celulaCheck tooltipster ">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-bold" value="S�B"/>
                        </f:facet>
                        <%--<div onclick="checkBoxClick(this,event);">--%>
                        <h:panelGroup layout="block" rendered="#{ConsultarTurmaControle.consultarTurma.sabado and horarioTurmaConcatenado.mostrarHorarioSab}" styleClass="checkbox-fa semEventoClasse checkHorarioTurma">
                            <a4j:commandLink id="selectTurmaSabado" styleClass="linkPadrao" reRender="checkHorarioTurmaSab">
                               <f:setPropertyActionListener
                                       value="#{!horarioTurmaConcatenado.horarioTurmaSab.horarioTurmaEscolhida}"
                                       target="#{horarioTurmaConcatenado.horarioTurmaSab.horarioTurmaEscolhida}"/>
                               <h:outputText id="marcarTurmaSabado" style="__id_turma_horrio_#{horarioTurmaConcatenado.horarioTurmaSab.codigo}"
                                             styleClass="texto-size-14 texto-cor-cinza texto-font #{horarioTurmaConcatenado.horarioTurmaSab.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaSab.quantidadeVagasDisponiveis}"
                                             rendered="#{horarioTurmaConcatenado.horarioTurmaSab.quantidadeVagas > 0}"/>
                               <h:outputText styleClass="texto-size-14 texto-cor-vermelho texto-font #{horarioTurmaConcatenado.horarioTurmaSab.horarioTurmaEscolhida ? 'fa-icon-check' : 'fa-icon-check-empty'}" value="#{horarioTurmaConcatenado.horarioTurmaSab.nrAlunoMatriculado}"
                                             rendered="#{horarioTurmaConcatenado.horarioTurmaSab.quantidadeVagas <= 0}"
                                             title="Hor?rio com n?mero de alunos excedido."/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <%--</div>--%>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" renderIfSinglePage="false" style="margin-top: 20px" styleClass="scrollPureCustom" for="formConsultarTurma:ConsultarTurma" maxPages="10"
                                   id="scConsultaTurma" />
            </h:panelGroup>
        </h:panelGroup>
            <script>
                eventosModalidade();
            </script>
        </h:panelGroup>

        <h:panelGroup layout="block" style="width: 100%;height: 70px;line-height: 5;text-align: center;position: relative">
            <h:panelGrid rendered="#{ConsultarTurmaControle.contemNivelDesconto}"  style="float: left; margin-top: 10px" width="15%"/>

            <a4j:commandLink id="salvar" reRender="detalhesNegociacao,panelGeral,plano,planoDescricao,planoDuracaoVO,pgModalidade,totalDescontos,totalDescontosP,
                                 planoModalidadeVezesSemanaVO,planoHorarioVO,planoComposicaoVO,composicaoMarcada,turmaMarcada,totalContrato,pnlProduto,
                                 modalidadeMarcada,produtoMarcada,planoProdutoMarcada,planoProdutoVO,botaoAdicionar,total,total1,
                                 subtotal,convenioDescontoMarcada,valorMensalModalidade,duracao,horario,panelMesangem, panelEdicaoModalidade,pgModalidade,containerMensagemConsultaTurma,pgDuracaoCreditoTreinoSessao"
                             styleClass="linkpadrao botaoPrimarioGrande texto-font texto-size-16" action="#{ConsultarTurmaControle.selecionarVezesSemanaPorTurma}"
                             oncomplete="#{ConsultarTurmaControle.msgAlert}" title="Processa os horarios escolhidos">
                <h:panelGroup>

                <h:graphicImage style="margin-right: 5px" styleClass=" texto-size-16-real fa-icon-save"></h:graphicImage>
                <h:outputText style="cursor: pointer" styleClass="  texto-size-16-real" value=" Gravar"></h:outputText>
                </h:panelGroup>
            </a4j:commandLink>
            <h:panelGrid rendered="#{ConsultarTurmaControle.contemNivelDesconto}" columns="5" style="float: right; margin-top: 10px" width="15%">
                <h:panelGroup layout="block" style="width:40px;height: 30px;text-align: center; padding: 10px; background-color: #A6FFA6">Baixa Ocup.</h:panelGroup>
                <h:panelGroup layout="block" style="width:40px;height: 30px;text-align: center; padding: 10px;background-color: #A6FFFF">&nbsp;</h:panelGroup>
                <h:panelGroup layout="block" style="width:40px;height: 30px;text-align: center; padding: 10px;background-color: #FFFFA6">&nbsp;</h:panelGroup>
                <h:panelGroup layout="block" style="width:40px;height: 30px;text-align: center; padding: 10px;background-color: #FEE3BB">&nbsp;</h:panelGroup>
                <h:panelGroup layout="block" style="width:40px;height: 30px;text-align: center; padding: 10px;background-color: #FFA6A6">Alta ocup.</h:panelGroup>
            </h:panelGrid>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

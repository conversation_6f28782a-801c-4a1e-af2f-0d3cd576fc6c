<%@include file="imports.jsp" %>
<link href="${root}/dicas/css/jquery.stepy.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="${root}/dicas/js/jquery.stepy.min.js"></script>

<style>
    .btModalInativarUsers{
        font-size: 11px !important;
        background: #094771 !important;
        color: #ffffff !important;
        padding: 5px !important;
    }
    .btModalInativarUsers:hover{
        background: #073071 !important;
        color: #ffffff !important;
    }
</style>


<rich:modalPanel id="modalVerificarUsuariosAtivos"
                 showWhenRendered="#{LoginControle.apresentarModalVerificarUsuarios}"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="500">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Estes usu�rios ainda utilizam o sistema? "/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <a4j:commandLink
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    style="color: #fff"
                    status="false"
                    action="#{LoginControle.naoApresentarModalVerificarUsuarios}"
                    id="hideModalVerificarUsuariosAtivos"/>
            <rich:componentControl for="modalVerificarUsuariosAtivos" attachTo="hideModalVerificarUsuariosAtivos"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form  styleClass="pure-form" ajaxSubmit="true" id="formNaoMostrarModalInativarUserHoje">
        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="width: 100%; padding-bottom: 10px; padding-top: 10px; ">
            <h:outputText value="N�o exibir novamente este aviso hoje" style="font-weight: bold" id="txtNaoMostrarModalInativarUserHoje"/>
            <h:selectBooleanCheckbox style="margin-left: 5%" value="#{UsuarioControle.usuarioVO.modalInativar}"
                                     valueChangeListener="#{UsuarioControle.showModalInativarUsuarios}" id="checkBoxNaoMostrarModalInativarUserHoje">
                <a4j:support event="onchange"
                             oncomplete="Richfaces.hideModalPanel('modalVerificarUsuariosAtivos');"
                             action="#{UsuarioControle.alteraValor}" />
            </h:selectBooleanCheckbox>
        </h:panelGroup>
    </a4j:form>

    <a4j:form id="formVerificarUsuariosAtivos" styleClass="pure-form" ajaxSubmit="true">
        <h:panelGroup>
            <rich:dataTable id="lstUsuariosInativosPorPeriodoUmMmes"
                            value="#{UsuarioControle.lstUsuariosInativosMaisDeUmMes}" var="objUser"
                            width="100%" headerClass="consulta" rowClasses="linhaPar,linhaImpar " rows="10"
                            style="margin-bottom: 2%">
                <rich:column id="colunaNomeUsuariosAInativar">
                    <f:facet name="header">
                        <h:outputText value="Nome"/>
                    </f:facet>
                    <h:outputText value="#{objUser.nome}"/>
                </rich:column>

                <rich:column style="text-align: center" sortBy="#{objUser.ultimoAcesso}" styleClass="col-text-align-left" headerClass="col-text-align-left" id="colunaUltimoAcessoUsuariosAInativar">
                    <f:facet name="header">
                        <h:outputText value="�ltimo Acesso"/>
                    </f:facet>
                    <h:outputText value="#{objUser.ultimoAcesso}" style="text-align: center !important;">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </rich:column>

                <rich:column style="text-align: center" sortBy="#{objUser.diasSemAcessar}" styleClass="col-text-align-left" headerClass="col-text-align-left" id="colunaDiasSemAcessoUsuariosAInativar">
                    <f:facet name="header">
                        <h:outputText value="Dias sem Acesso"/>
                    </f:facet>
                    <center><h:outputText value="#{objUser.diasSemAcessar}"/></center>
                </rich:column>

                <rich:column id="colunaInativarUsuarioUsuariosAInativar">
                    <f:facet name="header">
                        <h:outputText value="Inativar Usu�rio"/>
                    </f:facet>
                    <h:selectBooleanCheckbox
                            value="#{objUser.colaboradorVO.situacaoCheckBox}" style="margin-left: 40%">
                        <a4j:support event="onchange" action="#{UsuarioControle.alteraValor}"/>
                    </h:selectBooleanCheckbox>
                </rich:column>
            </rich:dataTable>

                <rich:datascroller rendered="#{fn:length(UsuarioControle.lstUsuariosInativosMaisDeUmMes) > 9}"
                        for="lstUsuariosInativosPorPeriodoUmMmes" maxPages="10"
                        page="#{UsuarioControle.scrollerPage}"/>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 7px">
            <br/>
            <a4j:commandButton id="processarInativarUsuarios" value="#{LoginControle.msgModalUsuariosAtivos}"
                             action="#{UsuarioControle.inativarUsuarios}" oncomplete="#{UsuarioControle.mensagemNotificar}"
                             styleClass="btModalInativarUsers" immediate="true" reRender="modalVerificarUsuariosAtivos">
            </a4j:commandButton>

        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>


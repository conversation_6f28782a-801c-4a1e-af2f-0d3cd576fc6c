<%-- 
    Document   : include_recuperacao_senha
    Created on : 06/12/2011, 08:35:53
    Author     : carla
--%>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="panelRecuperacaoSenha" domElementAttachment="parent"
                     showWhenRendered="#{RecuperacaoSenhaControle.abrirModalRecuperacaoSenha}"
                     autosized="true" shadowOpacity="true" width="350" height="250">

        <f:facet name="header">
            <h:outputText value="#{msg_aplic.prt_RecuperacaoSenha_tituloForm}"/>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form>
                    <a4j:commandButton image="/imagens/close.png" style="cursor:pointer"
                                       action="#{RecuperacaoSenhaControle.fecharPanelRecuperacaoSenha}"
                                       oncomplete="#{rich:component('panelRecuperacaoSenha')}.hide();"
                                       reRender="formDescobridor:captcha"
                                       id="hidelink12"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>

        <h:form id="formRecuperacaoSenha">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columnClasses="colunaCentralizada, colunaCentralizada" columns="2">
                    <h:graphicImage url="/imagens/expirarSenha.png" height="20px"/>
                    <h:outputText value="Recupera��o de Senha" styleClass="titulo6" style="font-weight:bold;"/>
                </h:panelGrid>
                <h:panelGrid columns="2" columnClasses="colunaDireita,colunaEsquerda" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_RecuperacaoSenha_cpf}"/>
                    <h:panelGroup>
                        <h:inputText id="cfp" size="14" maxlength="14" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form" value="#{RecuperacaoSenhaControle.pesRecuperacaoSenhaVO.cfp}"
                                     onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_RecuperacaoSenha_dataNasc}"/>
                    <h:inputText id="dataNasc" value="#{RecuperacaoSenhaControle.pesRecuperacaoSenhaVO.dataNasc}"
                                 size="10" maxlength="10" styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 style="margin:5px;" onchange="return validar_Data(this.id);"
                                 onkeypress="return mascara(this.form, this.id, '99/99/9999', event);">
                        <f:convertDateTime dateStyle="short" pattern="dd/MM/yyyy" timeZone="GMT-3"/>
                    </h:inputText>

                    <a4j:commandLink value="Trocar imagem"
                                     reRender="captcha"/>
                    <a4j:mediaOutput id="captcha"
                                     element="img" cacheable="false" session="true"
                                     rendered="#{RecuperacaoSenhaControle.abrirModalRecuperacaoSenha}"
                                     createContent="#{CaptchaControle.paintCaptcha}"
                                     value="#{CaptchaControle.mediaData}"
                                     mimeType="image/jpeg"/>

                    <h:outputText styleClass="tituloCampos" value="C�digo de verifica��o "/>
                    <h:inputText size="20" maxlength="20" value="#{CaptchaControle.valor}" style="background: white;
                            border: 1px solid #CCC;
                            border-bottom-color: #999;
                            border-right-color: #999;
                            height:26px;
                            color: black;
                            margin: 3px;
                            padding: 5px 8px 0 6px;
                            padding-right: 5px;
                            vertical-align: middle;"/>

                    <h:panelGrid columnClasses="colunaCentralizada" columns="1"/>
                    <a4j:commandButton
                            id="enviarEmail"
                            image="./imagensCRM/botaoEnviarEmail.png"
                            style="margin:5px;vertical-align:middle;"
                            type="submit" reRender="panelMensagem"
                            action="#{RecuperacaoSenhaControle.enviarEmailRecuperacaoSenha}">
                    </a4j:commandButton>
                </h:panelGrid>
                <h:panelGrid id="panelMensagem" columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <f:verbatim>
                            <h:outputText value=" "/>
                        </f:verbatim>
                    </h:panelGrid>
                    <h:commandButton rendered="#{RecuperacaoSenhaControle.atencao}" image="./imagens/atencao.png"/>
                    <h:commandButton rendered="#{RecuperacaoSenhaControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{RecuperacaoSenhaControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{RecuperacaoSenhaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{RecuperacaoSenhaControle.mensagemDetalhada}"/>
                        <h:outputText styleClass="mensagem" value="#{RecuperacaoSenhaControle.mensagemRecuperaSenha}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>
<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 15/04/2016
  Time: 11:41
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="imports.jsp" %>
<h:panelGroup layout="block" id="pgPacote">
    <h:panelGroup layout="block"  rendered="#{ContratoControle.mostrarPanelPacote}">
        <h:outputText value="PACOTE" styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold" style="margin-top: 10px;margin-bottom: 10px"/>
        <h:outputLink styleClass="linkWiki tooltipster"
                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                      title="Clique e saiba mais: Negociação - Pacote"
                      target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
        <rich:dataTable id="planoComposicaoVO" width="100%" styleClass="tabelaSimplesCustom"
                        value="#{ContratoControle.contratoVO.plano.planoComposicaoVOs}" var="planoComposicao">
          <rich:column>
              <h:panelGroup layout="block" styleClass="checkbox-fa">
                  <a4j:commandLink id="btnMarcarComposicao" styleClass="linkPadrao" action="#{ContratoControle.marcarComposicao}"
                             reRender="confNegociacao,dados"
                             oncomplete="#{ContratoControle.msgAlert};#{ContratoControle.mensagemNotificar}">
                       <h:outputText styleClass="#{planoComposicao.composicao.composicaoEscolhida ? 'fa-icon-circle-check' : 'fa-icon-circle-blank'} tooltipster texto-cor-cinza texto-size-16 texto-font" rendered="#{planoComposicao.composicao.modalidadesEspecificas}" value="#{planoComposicao.composicao.descricao}" />
                      <h:outputText styleClass="#{planoComposicao.composicao.composicaoEscolhida ? 'fa-icon-circle-check' : 'fa-icon-circle-blank'} tooltipster texto-cor-cinza texto-size-16 texto-font" rendered="#{!planoComposicao.composicao.modalidadesEspecificas}" value="#{planoComposicao.composicao.descricao}" title="#{msg.msg_composicao_mensagem}"/>
                  </a4j:commandLink>
              </h:panelGroup>
          </rich:column>
          <rich:column width="40">
            <h:outputText styleClass="texto-cor-cinza texto-size-16 texto-font" value="#{planoComposicao.composicao.precoComposicao} ">
              <f:converter converterId="FormatadorNumerico"/>
            </h:outputText>
          </rich:column>
        </rich:dataTable>
    </h:panelGroup>
</h:panelGroup>
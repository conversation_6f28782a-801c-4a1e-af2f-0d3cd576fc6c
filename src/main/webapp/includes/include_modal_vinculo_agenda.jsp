<%@include file="imports.jsp" %>

<rich:modalPanel id="mdlVinculoAgenda" styleClass="novaModal" width="600" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{VinculoAgendaControle.titulo}"/>
        </h:panelGroup>
    </f:facet>


    <a4j:form prependId="true" id="forMmdlVinculoAgenda">
        <h:panelGroup>
            <h:outputText styleClass="tituloCampos" value="#{VinculoAgendaControle.mensagemSuperiorTela}"/>
            <rich:dataTable id="dtAgendas" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                            value="#{VinculoAgendaControle.agendas}" rows="5" var="agenda" rendered="#{VinculoAgendaControle.agendas != null && not empty VinculoAgendaControle.agendas}">
                <rich:column style="text-align: left;">
                    <f:facet name="header">
                        <h:outputText value="Cliente"/>
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{agenda.cliente.pessoa.nome}"/>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller align="center" for="dtAgendas" maxPages="10" rendered="#{VinculoAgendaControle.agendas != null && not empty VinculoAgendaControle.agendas}"/>

            <h:panelGrid columns="1" style="padding-top: 10px">
                <h:outputText styleClass="tituloCampos" value="#{VinculoAgendaControle.mensagemComboColaboradores == null ? 'Informe o novo consultor dos clientes acima:' : VinculoAgendaControle.mensagemComboColaboradores}"/>
                <h:panelGroup>
                    <h:selectOneMenu id="colaboradorVinculo" value="#{VinculoAgendaControle.usuarioVOSelecionado}">
                        <f:selectItem itemLabel="" itemValue="0"/>
                        <f:selectItems value="#{VinculoAgendaControle.selectItensColaboradores}" />
                    </h:selectOneMenu>
                </h:panelGroup>

            </h:panelGrid>


            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup styleClass="margin-box">
                    <a4j:commandLink action="#{VinculoAgendaControle.invokeBotaoGravar}" value="Gravar"
                                       styleClass="botaoPrimario texto-size-16-real"
                                       oncomplete="#{VinculoAgendaControle.onCompleteBotaoGravar};Richfaces.hideModalPanel('mdlVinculoAgenda');"
                                       reRender="#{VinculoAgendaControle.reRenderComponents}"/>
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink oncomplete="#{VinculoAgendaControle.onCompleteBotaoVoltar};Richfaces.hideModalPanel('mdlVinculoAgenda');"
                                       styleClass="botaoPrimario texto-size-16-real"
                                       action="#{VinculoAgendaControle.invokeBotaoVoltar}"
                                       value="Voltar"
                                       reRender="#{VinculoAgendaControle.reRenderComponents}"/>
                </h:panelGroup>
            </h:panelGroup>
            <br/>
            <br/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
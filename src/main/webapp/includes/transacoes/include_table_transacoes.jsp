<%-- 
    Document   : include_table_transacoes
    Created on : 29/09/2011, 16:39:12
    Author     : Waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<c:set var="pageEstornoOuGestao" value="${pageContext.request.requestURI}" scope="request"/>
<head>
    <link href="css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
</head>
<a4j:keepAlive beanName="ExportadorListaControle"/>
<a4j:outputPanel>
    <h:outputText value="Nenhuma transação de cartão de crédito"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 cinza pl20"
                  rendered="#{fn:length(GestaoTransacoesControle.listaTransacoes) eq 0}"/>

    <h:panelGroup layout="block"
                  rendered="#{not empty (GestaoTransacoesControle.listaTransacoes)}"
                  style="float:right; padding: 10px 0 10px 0;">
        <a4j:commandLink id="botaoCartaoExcel"
                         styleClass="btn-print-2 excel tooltipster"
                         actionListener="#{GestaoTransacoesControle.exportar}"
                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','TransacaoCartaoCredito', 800,200);#{ExportadorListaControle.msgAlert}"
                         accesskey="3" title="Exportar dados para excel">
            <f:attribute name="tipo" value="xls"/>
            <f:attribute name="lista" value="#{GestaoTransacoesControle.listaApresentar}"/>
            <f:attribute name="atributos"
                         value="codigo=Código,pessoaPagador.nome=Nome Cliente,codigoExterno=Transação,valor_Apresentar=Valor,nomePessoa=Nome Titular,sitHint=Sit.,codigoRetornoGestaoTransacao=Código Retorno,codigoRetornoGestaoTransacaoMotivo=Hint,autorizacao=Autorização,NSU=NSU,reciboPagamento=Recibo,bandeira=Bandeira,cartaoMascarado=Cartão,dataProcessamento_Apresentar=DataHora,nomeUsuario=Usuário,empresaVO.nome=Empresa"/>
            <f:attribute name="prefixo" value="TransacaoCartaoCredito"/>
            <f:attribute name="titulo" value="TransacaoCartaoCredito"/>
        </a4j:commandLink>

        <a4j:commandLink id="botaoCartaoPDF"
                         styleClass="btn-print-2 pdf tooltipster"
                         style="margin-left: 10px"
                         actionListener="#{GestaoTransacoesControle.exportar}"
                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                         accesskey="4" title="Exportar dados para pdf">
            <f:attribute name="tipo" value="pdf"/>
            <f:attribute name="lista" value="#{GestaoTransacoesControle.listaApresentar}"/>
            <f:attribute name="atributos"
                         value="codigo=Código,pessoaPagador.nome=Nome Cliente,codigoExterno=Transação,valor_Apresentar=Valor,nomePessoa=Nome Titular,sitHint=Sit.,codigoRetornoGestaoTransacao=Código Retorno,codigoRetornoGestaoTransacaoMotivo=Hint,autorizacao=Autorização,NSU=NSU,reciboPagamento=Recibo,bandeira=Bandeira,cartaoMascarado=Cartão,dataProcessamento_Apresentar=DataHora,nomeUsuario=Usuário,empresaVO.nome=Empresa"/>
            <f:attribute name="prefixo" value="TransacaoCartaoCredito"/>
            <f:attribute name="titulo" value="TransacaoCartaoCredito"/>
        </a4j:commandLink>
    </h:panelGroup>

    <h:panelGroup layout="block" id="tabelaTransacoes"
                  style="padding-top: 10px"
                  rendered="true">
        <rich:dataTable styleClass="tabelaDados font-size-Em"
                        id="tblTransacoes"
                        style="#{fn:contains(pageEstornoOuGestao, 'gestaoTransacoes') ? 'margin: 0; width: 100%;' : 'width: 97%;'}"
                        value="#{GestaoTransacoesControle.listaTransacoes}"
                        var="transacao" rowKeyVar="status"
                        rows="#{GestaoTransacoesControle.listaTransacao.limit}"
                        onRowMouseOver="this.addClassName('background-color-tabelaDados-over')"
                        onRowMouseOut="this.removeClassName('background-color-tabelaDados-over')"
                        rendered="#{fn:length(GestaoTransacoesControle.listaTransacoes) > 0}"
                        rowClasses="linhaPar,linhaImpar">

            <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

            <rich:column sortBy="#{transacao.codigo}">
                <f:facet name="header">
                    <h:outputText value="Cod."/>
                </f:facet>
                <h:outputText value="#{transacao.codigo}"
                              styleClass="tooltipster"
                              title="#{transacao.codigoExternoExibir}"/>
            </rich:column>

            <rich:column styleClass="colunaEsquerda" sortBy="#{transacao.pessoaPagador.nome}">
                <f:facet name="header">
                    <h:outputText value="Nome Cliente"/>
                </f:facet>
                <% if (request.getRequestURI().contains("impressao")) {%>
                <a4j:commandLink value="#{transacao.pessoaPagador.nome}"
                                 title="Clique para abrir o Cadastro de Cliente"
                                 styleClass="tooltipster"
                                 style="text-transform: uppercase; color: #0090FF"
                                 oncomplete="#{GestaoTransacoesControle.onCompleteDetalhes}"
                                 actionListener="#{GestaoTransacoesControle.abrirTelaClienteColaboradorTransacao}">
                    <f:attribute name="pessoa" value="#{transacao.pessoaPagador}"/>
                    <f:attribute name="impressao" value="true"/>
                </a4j:commandLink>
                <%} else {%>
                <a4j:commandLink value="#{transacao.pessoaPagador.nome}"
                                 title="Clique para abrir o Cadastro de Cliente"
                                 styleClass="tooltipster"
                                 style="text-transform: uppercase; color: #0090FF"
                                 oncomplete="#{GestaoTransacoesControle.onCompleteDetalhes}"
                                 actionListener="#{GestaoTransacoesControle.abrirTelaClienteColaboradorTransacao}">
                    <f:attribute name="pessoa" value="#{transacao.pessoaPagador}"/>
                    <f:attribute name="impressao" value="false"/>
                </a4j:commandLink>
                <%}%>
            </rich:column>

            <rich:column sortBy="#{transacao.nomePessoa}">
                <f:facet name="header">
                    <h:outputText value="TITULAR DO CARTÃO"/>
                </f:facet>
                <h:outputText value="#{transacao.nomePessoa}"
                              styleClass="tooltipster"
                              title="Nome do titular do cartão"
                              style="text-transform: uppercase"/>
            </rich:column>

            <rich:column sortBy="#{transacao.cartaoMascarado}">
                <f:facet name="header">
                    <h:outputText value="Cartão"/>
                </f:facet>
                <h:outputText value="#{transacao.cartaoMascarado}"/>
            </rich:column>

            <rich:column sortBy="#{transacao.bandeira}">
                <f:facet name="header">
                    <h:outputText value="Band."
                                  styleClass="tooltipster"
                                  title="Bandeira"/>
                </f:facet>
                <h:outputText value="#{transacao.bandeira}"
                              styleClass="tooltipster"
                              title="Bandeira #{transacao.bandeira}"
                              style="text-transform: uppercase"/>
            </rich:column>

            <rich:column sortBy="#{transacao.tipo}">
                <f:facet name="header">
                    <h:outputText value="Plataforma"/>
                </f:facet>
                <h:panelGroup style="display: inline-flex">
                    <%--AMBIENTE DE HOMOLOGACAO--%>
                    <h:outputText rendered="#{transacao.ambienteHomologacao}"
                                  style="font-size: 17px; margin-right: 10px; color: #F06D29"
                                  styleClass="fa-icon-exclamation-triangle-sign tooltipster"
                                  title="Transação realizada em ambiente de Homologação/Sandbox"/>

                    <h:outputText value="#{transacao.tipoGestaoTransacao}"
                                  style="text-transform: uppercase"/>
                </h:panelGroup>
            </rich:column>

            <rich:column sortBy="#{transacao.situacao}"
                         styleClass="col-text-align-center"
                         headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText value="Sit."
                                  styleClass="tooltipster"
                                  title="Situação da Transação"/>
                </f:facet>

                <h:panelGrid width="100%">
                    <h:column>
                        <rich:spacer style="cursor:pointer;vertical-align:middle;
                                 width: 11px; height: 11px;
                                 background-color:#{transacao.situacao.imagem}"
                                     styleClass="tooltipster"
                                     title="#{transacao.situacao_Title}">
                        </rich:spacer>
                    </h:column>
                </h:panelGrid>
            </rich:column>

            <rich:column sortBy="#{transacao.codErroExterno}" styleClass="col-text-align-center"
                         headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText value="CE"
                                  styleClass="tooltipster"
                                  title="Código do Retorno"/>
                </f:facet>
                <h:column>
                    <rich:spacer style="cursor:pointer;vertical-align:middle;">
                        <h:outputText id="codErroGestaoTransacao"
                                      styleClass="tooltipster"
                                      style="cursor:pointer;vertical-align:middle;"
                                      value="#{transacao.codigoRetornoGestaoTransacao}"
                                      title="#{transacao.codigoRetornoGestaoTransacaoTooltipster}"/>
                    </rich:spacer>
                </h:column>
            </rich:column>

            <rich:column sortBy="#{transacao.repescagemInt}" styleClass="col-text-align-center"
                         rendered="#{GestaoTransacoesControle.apresentarColunaRepescagem}"
                         headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText value="Rep."/>
                </f:facet>
                <h:outputText value="#{transacao.repescagemInt}"/>
            </rich:column>

            <rich:column sortBy="#{transacao.autorizacao}" styleClass="col-text-align-center"
                         headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText value="Aut."
                                  styleClass="tooltipster"
                                  title="Código da Autorização"/>
                </f:facet>
                <h:outputText title="#{transacao.autorizacaoGestaoTransacaoTitle}"
                              styleClass="tooltipster"
                              value="#{transacao.autorizacao}"/>
            </rich:column>

            <rich:column sortBy="#{transacao.reciboPagamento}" styleClass="col-text-align-center"
                         headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText value="Rec."
                                  styleClass="tooltipster"
                                  title="Código do recibo"/>
                </f:facet>
                <h:outputText rendered="#{transacao.reciboPagamento != 0}"
                              styleClass="tooltipster"
                              title="Recibo nº #{transacao.reciboPagamento}"
                              value="#{transacao.reciboPagamento}"/>
            </rich:column>

            <rich:column sortBy="#{transacao.dataProcessamento}">
                <f:facet name="header">
                    <h:outputText value="Data/Hora"
                                  styleClass="tooltipster"
                                  title="Data da transação"/>
                </f:facet>
                <h:outputText value="#{transacao.dataProcessamento_Apresentar}"/>
            </rich:column>

            <rich:column styleClass="colunaEsquerda" sortBy="#{transacao.usuarioResponsavel.nomeAbreviado}">
                <f:facet name="header">
                    <h:outputText value="Usuário"/>
                </f:facet>
                <h:outputText value="#{transacao.usuarioResponsavel.nomeAbreviado}"/>
            </rich:column>

            <rich:column styleClass="colunaEsquerda" sortBy="#{transacao.valor}">
                <f:facet name="header">
                    <h:outputText value="Valor"/>
                </f:facet>
                <h:outputText rendered="#{not empty transacao.valorTitleApresentar}"
                              style="font-size: 18px; margin-right: 7px; color: #0f4c36"
                              styleClass="fa-icon-question-sign tooltipster"
                              title="#{transacao.valorTitleApresentar}"/>
                <h:outputText value="#{transacao.valor_Apresentar}"/>
            </rich:column>

            <rich:column rendered="#{GestaoTransacoesControle.apresentarColunaEmpresa}"
                         styleClass="colunaEsquerda" sortBy="#{transacao.empresaVO.nome}">
                <f:facet name="header">
                    <h:outputText value="Empresa"/>
                </f:facet>
                <h:outputText value="#{transacao.empresaVO.nome}"/>
            </rich:column>

            <rich:column>
                <f:facet name="header">
                    <h:outputText value="Opções"/>
                </f:facet>
                <h:panelGroup id="panelAcoes" style="display: inline-flex">


                    <%--ENVIO--%>
                    <a4j:commandLink rendered="#{not empty transacao.paramsEnvio}"
                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                     styleClass="tooltipster"
                                     title="Visualizar os parâmetros de envio"
                                     reRender="panelDadosParametros"
                                     actionListener="#{GestaoTransacoesControle.exibirParams}">
                        <f:attribute name="params" value="envio"/>
                        <f:attribute name="transacao" value="#{transacao}"/>
                        <i class="fa-icon-share-alt"></i>
                    </a4j:commandLink>


                    <%--RESPOSTA--%>
                    <a4j:commandLink rendered="#{not empty transacao.paramsResposta}"
                                     reRender="panelDadosParametros"
                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                     styleClass="tooltipster"
                                     title="Visualizar os parâmetros de resposta"
                                     actionListener="#{GestaoTransacoesControle.exibirParams}">
                        <f:attribute name="params" value="resposta"/>
                        <f:attribute name="transacao" value="#{transacao}"/>
                        <i class="fa-icon-reply"></i>
                    </a4j:commandLink>


                    <%--DETALHES--%>
                    <a4j:commandLink actionListener="#{GestaoTransacoesControle.exibirDetalhesTransacao}"
                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                     reRender="panelDadosTransacao"
                                     styleClass="tooltipster"
                                     title="Mais detalhes sobre esta transação">
                        <f:attribute name="transacao" value="#{transacao}"/>
                        <i class="fa-icon-eye-open"></i>
                    </a4j:commandLink>

                    <%--MAIS DE UMA PARCELA--%>
                    <a4j:commandLink actionListener="#{GestaoTransacoesControle.exibirDetalhesTransacao}"
                                     rendered="#{transacao.qtdParcelasTransacao > 1}"
                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                     reRender="panelDadosTransacao"
                                     styleClass="tooltipster"
                                     title="Essa transação possui mais de uma parcela. Clique para visualizar mais detalhes.">
                        <f:attribute name="transacao" value="#{transacao}"/>
                        <i class="fa-icon-list"></i>
                    </a4j:commandLink>

                    <%--CONFIG--%>
                    <a4j:commandLink rendered="#{transacao.aprovaFacil}"
                                     reRender="panelDadosParametros"
                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                     styleClass="tooltipster"
                                     title="Visualizar o resultado da confirmação da transação junto à Administradora"
                                     actionListener="#{GestaoTransacoesControle.exibirParams}">
                        <h:graphicImage style="border:none;" value="images/search_button.png"/>
                        <f:attribute name="params" value="confirmacao"/>
                        <f:attribute name="transacao" value="#{transacao}"/>
                    </a4j:commandLink>


                    <%--SINCRONIZAR--%>
                    <a4j:commandLink id="btnSincronizarTransacao"
                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                     styleClass="botoes tooltipster fontBtnAcoes"
                                     reRender="formModalSincronizarTransacao"
                                     rendered="#{transacao.permiteSincronizar}"
                                     action="#{GestaoTransacoesControle.abrirModalSincronizarTransacao}"
                                     oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                     title="Consultar situação da transação">
                        <i class="fa-icon-refresh"></i>
                    </a4j:commandLink>


                    <%--RETENTATIVA--%>
                    <a4j:commandLink id="btnRetentativaTransacao"
                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                     styleClass="botoes tooltipster fontBtnAcoes"
                                     reRender="formModalRetentativaTransacao"
                                     rendered="#{transacao.permiteRetentativa}"
                                     action="#{GestaoTransacoesControle.abrirModalRetentativaTransacao}"
                                     oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                     title="Realizar retentativa imediata da transação">
                        <i class="fa-icon-repeat"></i>
                    </a4j:commandLink>

                    <%--COMPROVANTE CANCELAMENTO--%>
                    <h:outputLink styleClass="tooltipster"
                                  style="margin-right: 10px; color: #444; font-size: 17px"
                                  rendered="#{not empty transacao.urlComprovanteCancelamento}"
                                  value="#{transacao.urlComprovanteCancelamento}"
                                  title="Download do comprovante de cancelamento da transação"
                                  target="_blank">
                        <i class="fa-icon-file-pdf" style="color: #444;"></i>
                    </h:outputLink>


                    <%--ENVIO EMAIL COMPROVANTE CANCELAMENTO--%>
                    <a4j:commandLink style="margin-right: 10px; color: #444; font-size: 17px"
                                     styleClass="botoes tooltipster fontBtnAcoes"
                                     rendered="#{not empty transacao.urlComprovanteCancelamento}"
                                     reRender="panelEnviarEmailTransacao"
                                     action="#{GestaoTransacoesControle.abrirModalEnviarEmailComprovanteCancelamento}"
                                     oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                     title="Enviar e-mail do comprovante de cancelamento">
                        <i class="fa-icon-paper-plane"></i>
                    </a4j:commandLink>


                    <%--CANCELAR TRANSACAO--%>
                    <a4j:commandLink style="margin-right: 10px; color: darkred; font-size: 17px"
                                     rendered="#{transacao.permiteCancelar && GestaoTransacoesControle.permissaoEstronarTransacao}"
                                     styleClass="tooltipster"
                                     reRender="formModalCancelarTransacao"
                                     action="#{GestaoTransacoesControle.abrirModalCancelarTransacao}"
                                     oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                     title="Cancelar/estornar transação">
                        <i class="fa-icon-ban-circle" style="color: darkred;"></i>
                    </a4j:commandLink>

                </h:panelGroup>
            </rich:column>
        </rich:dataTable>

        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada"
                     rendered="#{fn:length(GestaoTransacoesControle.listaTransacoes) > 0}">
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tr>
                    <td align="center" valign="middle">
                        <h:panelGroup layout="block"
                                      styleClass="paginador-container">
                            <h:panelGroup styleClass="pull-left"
                                          layout="block">

                                <h:outputText styleClass="texto-size-12 texto-cor-cinza"
                                              value="Total #{GestaoTransacoesControle.listaTransacao.count} itens"/>
                            </h:panelGroup>
                            <h:panelGroup layout="block"
                                          style="align-items: center">
                                <a4j:commandLink
                                        styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                        reRender="panelConteudo"
                                        actionListener="#{GestaoTransacoesControle.primeiraPagina}">
                                    <i class="fa-icon-double-angle-left" id="primPagina"></i>
                                    <f:attribute name="tipo"
                                                 value="#{fn:contains(pageEstornoOuGestao, 'gestaoTransacoes') ? 'LISTA_APRESENTAR' : 'LISTA_TRANSACOES'}"/>
                                </a4j:commandLink>
                                <a4j:commandLink
                                        styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                        reRender="panelConteudo"
                                        actionListener="#{GestaoTransacoesControle.paginaAnterior}">
                                    <i class="fa-icon-angle-left" id="pagAnt"></i>
                                    <f:attribute name="tipo"
                                                 value="#{fn:contains(pageEstornoOuGestao, 'gestaoTransacoes') ? 'LISTA_APRESENTAR' : 'LISTA_TRANSACOES'}"/>
                                </a4j:commandLink>
                                <h:outputText
                                        styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                        value="#{msg_aplic.prt_msg_pagina} #{GestaoTransacoesControle.listaTransacao.paginaAtualApresentar}"
                                        rendered="true"/>
                                <a4j:commandLink
                                        styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                        reRender="panelConteudo"
                                        actionListener="#{GestaoTransacoesControle.proximaPagina}">
                                    <i class="fa-icon-angle-right" id="proxPag"></i>
                                    <f:attribute name="tipo"
                                                 value="#{fn:contains(pageEstornoOuGestao, 'gestaoTransacoes') ? 'LISTA_APRESENTAR' : 'LISTA_TRANSACOES'}"/>
                                </a4j:commandLink>
                                <a4j:commandLink
                                        styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                        reRender="panelConteudo"
                                        actionListener="#{GestaoTransacoesControle.ultimaPagina}">
                                    <i class="fa-icon-double-angle-right" id="ultimaPagina"></i>
                                    <f:attribute name="tipo"
                                                 value="#{fn:contains(pageEstornoOuGestao, 'gestaoTransacoes') ? 'LISTA_APRESENTAR' : 'LISTA_TRANSACOES'}"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                                <h:panelGroup styleClass="pull-right" layout="block">
                                    <h:outputText styleClass="texto-size-12 texto-cor-cinza" value="Itens por página "/>
                                </h:panelGroup>
                                &nbsp;
                                <h:panelGroup styleClass="cb-container pl20" layout="block">
                                    <h:selectOneMenu value="#{GestaoTransacoesControle.listaTransacao.limit}"
                                                     style="font-size: 14px !important;" id="qtdeItensPaginaProd">
                                        <f:selectItem itemValue="#{10}"/>
                                        <f:selectItem itemValue="#{20}"/>
                                        <f:selectItem itemValue="#{50}"/>
                                        <f:selectItem itemValue="#{100}"/>
                                        <f:selectItem itemValue="#{200}"/>
                                        <a4j:support event="onchange"
                                                     actionListener="#{GestaoTransacoesControle.atualizarNumeroItensPagina}"
                                                     reRender="panelConteudo">
                                            <f:attribute name="tipo" value="LISTA_TRANSACOES"/>
                                        </a4j:support>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                            </h:panelGroup>
                        </h:panelGroup>
                    </td>
                </tr>
            </table>
        </h:panelGrid>
    </h:panelGroup>

    <h:panelGroup styleClass="semSpinner" rendered="#{!fn:contains(pageEstornoOuGestao, 'gestaoTransacoes')}"
                  style="display: block;width: 100%;">
        <h:panelGroup
                rendered="#{fn:contains(pageEstornoOuGestao, 'gestaoTransacoes') ? (fn:length(GestaoTransacoesControle.listaApresentar) gt 10) : (fn:length(GestaoTransacoesControle.listaTransacoes) gt 10)}">
            <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada"
                         style="display: inline-block; text-align: center;">
                <rich:datascroller align="center"
                                   for="tblTransacoes" maxPages="10"
                                   id="scItensTransacoes" renderIfSinglePage="false" reRender="tblTransacoes"
                                   styleClass="scrollPureCustom"/>
            </h:panelGrid>
        </h:panelGroup>
    </h:panelGroup>
</a4j:outputPanel>

<%@page pageEncoding="ISO-8859-1" %>

<%--
* Created with IntelliJ IDEA.
* User: <PERSON>
* Date: 08/05/2023
*/--%>

<%@include file="/includes/imports.jsp" %>

<a4j:outputPanel>

    <%-- PARÂMETROS Cobrança ASAAS (Boleto/PIX)--%>
    <rich:modalPanel id="panelDadosParametrosAsaas" showWhenRendered="#{TelaClienteControle.exibirModalParametros}"
                     width="500"
                     autosized="true" styleClass="novaModal"
                     shadowOpacity="true">

        <h:panelGrid columns="2" width="100%" rendered="#{not empty TelaClienteControle.mensagemDetalhada}"
                     title="#{TelaClienteControle.mensagemDetalhada}">
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagemDetalhada"
                              value="#{TelaClienteControle.mensagem}"/>
            </h:panelGrid>
        </h:panelGrid>

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cobrança id: #{TelaClienteControle.cobrancaAsaasRetornoDTODetalhar.id}"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="linkPadraoFecharModal" />
                <rich:componentControl for="panelDadosParametrosAsaas"
                                       attachTo="linkPadraoFecharModal"
                                       operation="hide"
                                       event="onclick" />
            </h:panelGroup>
        </f:facet>

        <h:panelGroup>
            <c:if test="${empty TelaClienteControle.listaParametrosSelecionado}">
                <h:panelGrid columns="2">
                    <h:graphicImage value="images/warning.png"/>
                    <h:outputText styleClass="mensagemDetalhada" value="Não há parâmetros para esta cobrança."/>
                </h:panelGrid>
            </c:if>
            <h:panelGroup layout="block" style="height: 400px; overflow-y: scroll; overflow-x: hidden;">
                <rich:dataTable width="100%" value="#{TelaClienteControle.listaParametrosSelecionado}" var="item">
                    <f:facet name="header">
                        <c:if test="${!empty TelaClienteControle.listaParametrosSelecionado}">
                            <h:outputText value="Parâmetros utilizados"/>
                        </c:if>
                    </f:facet>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Atributo"/>
                        </f:facet>
                        <h:outputText value="#{item.atributo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText style="font-weight:bold;" value="#{item.valor}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

        </h:panelGroup>
    </rich:modalPanel>

</a4j:outputPanel>

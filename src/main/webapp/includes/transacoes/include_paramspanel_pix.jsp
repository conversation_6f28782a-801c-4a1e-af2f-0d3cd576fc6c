<%--
    Document   : include_paramspanel_pix
    Created on : 10/05/2021, 15:00:00
    Author     : Estulano
--%>

<%@include file="/includes/imports.jsp" %>

<a4j:outputPanel>


    <%-- PARÂMETROS UTILIZADOS NO PIX--%>
    <rich:modalPanel  id="panelDadosParametrosPix" showWhenRendered="#{PixRelatorioController.exibirModalParametros}" width="550"
                      autosized="true" styleClass="novaModal"
                      shadowOpacity="true">

        <h:panelGrid columns="2" width="100%" rendered="#{not empty PixRelatorioController.mensagemDetalhada}" title="#{PixRelatorioController.mensagemDetalhada}">
            <h:graphicImage value="imagens/icon-error.png" width="45"/>
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagemDetalhada"
                              value="#{PixRelatorioController.mensagem}"/>
            </h:panelGrid>
        </h:panelGrid>

        <f:facet name="header">
            <h:panelGroup rendered="#{!empty PixRelatorioController.pixVOSelecionado.codigo}">
                <h:outputText value="Pix: #{PixRelatorioController.pixVOSelecionado.codigo}"/>
                <h:outputText value="Id: #{PixRelatorioController.pixVOSelecionado.txid}" style="margin-left: 30px"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <a4j:form id="formFEPix">
                    <a4j:commandButton id="btnFecharEnvioPix" image="imagens/close.png" style="cursor:pointer"
                                       action="#{PixRelatorioController.fecharPanelDadosParametros}"
                                       oncomplete="#{rich:component('panelDadosParametrosPix')}.hide();"/>
                </a4j:form>
            </h:panelGroup>
        </f:facet>

        <h:panelGroup>
            <c:if test="${empty PixRelatorioController.listaParametrosSelecionado}">
                <h:panelGrid columns="2">
                    <h:graphicImage value="images/warning.png"/>
                    <h:outputText styleClass="mensagemDetalhada" value="Não há parâmetros para este Pix."/>
                </h:panelGrid>
            </c:if>
            <h:panelGroup layout="block" style="height: 355px; overflow-y: scroll; overflow-x: hidden;">
                <rich:dataTable  width="100%"  value="#{PixRelatorioController.listaParametrosSelecionado}" var="obj">
                    <f:facet name="header">
                        <c:if test="${!empty PixRelatorioController.listaParametrosSelecionado}">
                            <h:outputText value="Parâmetros utilizados"/>
                        </c:if>
                    </f:facet>
                    <rich:column >
                        <f:facet name="header">
                            <h:outputText value="Atributo"/>
                        </f:facet>
                        <h:outputText value="#{obj.atributo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText style="font-weight:bold;" value="#{obj.valor}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

        </h:panelGroup>
    </rich:modalPanel>
</a4j:outputPanel>

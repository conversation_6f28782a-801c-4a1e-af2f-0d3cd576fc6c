<%--
    Document   : include_table_transacoes_colaborador
    Created on : 02/03/2021, 11:30:00
    Author     : Estulano
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<c:set var="pageEstornoOuGestao" value="${pageContext.request.requestURI}" scope="request"/>
<a4j:outputPanel id="containerListaTransacoes">
    <h:outputText value="Transações de Cartão de Crédito"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 negrito cinzaEscuro pl20"/>
    <h:outputText value="Nenhuma transação de cartão de crédito"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 cinza pl20"
                  rendered="#{ColaboradorControle.listaTrasacoes.count <= 0}"/>

    <h:panelGroup rendered="#{ColaboradorControle.listaTrasacoes.count > 0}"
                  layout="block" style="float:right;">
        <h:panelGroup layout="block">
            <a4j:commandLink id="botaoCartaoExcel"
                             styleClass="pure-button pure-button-small"
                             actionListener="#{ExportadorListaControle.exportar}"
                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','TransacaoCartaoCredito', 800,200);#{ExportadorListaControle.msgAlert}"
                             accesskey="3" title="Exportar dados para excel">
                <f:attribute name="tipo" value="xls"/>
                <f:attribute name="lista" value="#{GestaoTransacoesControle.listaTransacoesExportar}"/>
                <f:attribute name="atributos" value="codigo=Código,codigoExterno=Transação,valor_Apresentar=Valor,nomePessoa=Nome Titular,sitHint=Sit.,codErroExterno=CE,repescagemInt=Rep.,autorizacao=Aut.,reciboPagamento=Rec.,bandeira=Band.,cartaoMascarado=Cartão,dataProcessamento_Apresentar=DataHora,nomeUsuario=Usuário,empresa=Emp."/>
                <f:attribute name="prefixo" value="TransacaoCartaoCredito"/>
                <f:attribute name="titulo" value="TransacaoCartaoCredito"/>
                <i class="fa-icon-excel" ></i> &nbsp Excel
            </a4j:commandLink>

            <a4j:commandLink id="botaoCartaoPDF"
                             styleClass="pure-button pure-button-small margin-h-10"
                             actionListener="#{ExportadorListaControle.exportar}"
                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                             accesskey="4" title="Exportar dados para pdf">
                <f:attribute name="tipo" value="pdf"/>
                <f:attribute name="lista" value="#{GestaoTransacoesControle.listaTransacoesExportar}"/>
                <f:attribute name="atributos" value="codigo=Código,codigoExterno=Transação,valor_Apresentar=Valor,nomePessoa=Nome Titular,sitHint=Sit.,codErroExterno=CE,repescagemInt=Rep.,autorizacao=Aut.,reciboPagamento=Rec.,bandeira=Band.,cartaoMascarado=Cartão,dataProcessamento_Apresentar=DataHora,nomeUsuario=Usuário,empresa=Emp."/>
                <f:attribute name="prefixo" value="TransacaoCartaoCredito"/>
                <f:attribute name="titulo" value="TransacaoCartaoCredito"/>
                <i class="fa-icon-pdf" ></i> &nbsp PDF
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>


    <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status" id="tblTransacoes"
                    styleClass="tabelaDados font-size-Em" style="#{fn:contains(pageEstornoOuGestao, 'gestaoTransacoes') ? 'margin: 0; width: 100%;' : 'width: 97%;'}"
                    onRowMouseOver="this.addClassName('background-color-tabelaDados-over')"
                    onRowMouseOut="this.removeClassName('background-color-tabelaDados-over')"
                    value="#{GestaoTransacoesControle.listaTransacoes}"
                    rendered="#{ColaboradorControle.listaTrasacoes.count > 0}"
                    var="transacao">

        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

        <rich:column sortBy="#{transacao.codigo}">
            <f:facet name="header">
                <h:outputText value="Cod."/>
            </f:facet>
            <h:outputText value="#{transacao.codigo}"
                          styleClass="tooltipster"
                          title="#{transacao.codigoExternoExibir}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{transacao.nomePessoa}">
            <f:facet name="header">
                <h:outputText value="Titular Cartão"/>
            </f:facet>
            <h:outputText
                    styleClass="tooltipster"
                    title="Nome do titular do cartão"
                    value="#{transacao.nomePessoa}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{transacao.descricaoParcelas}">
            <f:facet name="header">
                <h:outputText value="Parcela"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          title="#{transacao.descricaoParcelasTitle}"
                          value="#{transacao.descricaoParcelas}" escape="false"/>
        </rich:column>

        <rich:column sortBy="#{transacao.cartaoMascarado}" >
            <f:facet name="header">
                <h:outputText value="Cartão"/>
            </f:facet>
            <h:outputText value="#{transacao.cartaoMascarado}"/>

        </rich:column>

        <rich:column sortBy="#{transacao.bandeira}">
            <f:facet name="header">
                <h:outputText value="Band."/>
            </f:facet>
            <h:outputText value="#{transacao.bandeira}"/>
        </rich:column>

        <rich:column sortBy="#{transacao.tipo.descricao}">
            <f:facet name="header">
                <h:outputText value="Plataforma"/>
            </f:facet>
            <h:panelGroup style="display: inline-flex">
                <%--AMBIENTE DE HOMOLOGACAO--%>
                <h:outputText rendered="#{transacao.ambienteHomologacao}"
                              style="font-size: 17px; margin-right: 10px; color: #F06D29"
                              styleClass="fa-icon-exclamation-triangle-sign tooltipster"
                              title="Transação realizada em ambiente de Homologação/Sandbox"/>

                <h:outputText value="#{transacao.tipoGestaoTransacao}"
                              style="text-transform: uppercase"/>
            </h:panelGroup>
        </rich:column>

        <rich:column sortBy="#{transacao.situacao}"
                     styleClass="col-text-align-center"
                     headerClass="col-text-align-center">
            <f:facet name="header">
                <h:outputText value="Sit."
                              styleClass="tooltipster"
                              title="Situação da Transação"/>
            </f:facet>

            <h:panelGrid width="100%">
                <h:column>
                    <rich:spacer style="cursor:pointer;vertical-align:middle;
                                 width: 11px; height: 11px;
                                 background-color:#{transacao.situacao.imagem}"
                                 styleClass="tooltipster"
                                 title="#{transacao.situacao_Title}">
                    </rich:spacer>
                </h:column>
            </h:panelGrid>
        </rich:column>

        <rich:column sortBy="#{transacao.codigoRetornoGestaoTransacao}" styleClass="col-text-align-center" headerClass="col-text-align-center">
            <f:facet name="header">
                <h:outputText value="CE"
                              styleClass="tooltipster"
                              title="Código do Retorno"/>
            </f:facet>
            <h:column>
                <rich:spacer style="cursor:pointer;vertical-align:middle;">
                    <h:outputText id="codErroGestaoTransacao"
                                  styleClass="tooltipster"
                                  style="cursor:pointer;vertical-align:middle;"
                                  value="#{transacao.codigoRetornoGestaoTransacao}"
                                  title="#{transacao.codigoRetornoGestaoTransacaoTooltipster}"/>
                </rich:spacer>
            </h:column>
        </rich:column>

        <rich:column sortBy="#{transacao.autorizacao}" styleClass="col-text-align-center" headerClass="col-text-align-center">
            <f:facet name="header">
                <h:outputText value="Aut."/>
            </f:facet>
            <h:outputText title="#{transacao.autorizacaoGestaoTransacaoTitle}"
                          styleClass="TRANSACAO-AUTORIZACAO-CODIGO tooltipster"
                          value="#{transacao.autorizacao}"/>
        </rich:column>

        <rich:column sortBy="#{transacao.reciboPagamento}" styleClass="col-text-align-center" headerClass="col-text-align-center">
            <f:facet name="header">
                <h:outputText value="Rec."/>
            </f:facet>
            <h:outputText rendered="#{transacao.reciboPagamento != 0}"
                          styleClass="tooltipster"
                          title="Recibo nº #{transacao.reciboPagamento}"
                          value="#{transacao.reciboPagamento}"/>
        </rich:column>

        <rich:column sortBy="#{transacao.dataProcessamento}" >
            <f:facet name="header">
                <h:outputText value="Data/Hora"
                              styleClass="tooltipster"
                              title="Data da transação"/>
            </f:facet>
            <h:outputText value="#{transacao.dataProcessamento_Apresentar}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{transacao.usuarioResponsavel.nomeAbreviado}">
            <f:facet name="header">
                <h:outputText value="Usuário"/>
            </f:facet>
            <h:outputText value="#{transacao.usuarioResponsavel.nomeAbreviado}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{transacao.valor}">
            <f:facet name="header">
                <h:outputText value="Valor"/>
            </f:facet>
            <h:outputText value="#{transacao.valor_Apresentar}" styleClass="TRANSACAO-AUTORIZACAO-VALOR"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda">
            <f:facet name="header">
                <h:outputText value="Opções"/>
            </f:facet>
            <h:panelGroup style="display: inline-flex">

                <%--ENVIO--%>
                <a4j:commandLink rendered="#{not empty transacao.paramsEnvio}"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="tooltipster"
                                 title="Visualizar os parâmetros de envio"
                                 reRender="panelDadosParametros"
                                 actionListener="#{GestaoTransacoesControle.exibirParams}">
                    <f:attribute name="params" value="envio"/>
                    <f:attribute name="transacao" value="#{transacao}"/>
                    <i class="fa-icon-share-alt"></i>
                </a4j:commandLink>


                <%--RESPOSTA--%>
                <a4j:commandLink rendered="#{not empty transacao.paramsResposta}"
                                 reRender="panelDadosParametros"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="tooltipster"
                                 title="Visualizar os parâmetros de resposta"
                                 actionListener="#{GestaoTransacoesControle.exibirParams}">
                    <f:attribute name="params" value="resposta"/>
                    <f:attribute name="transacao" value="#{transacao}"/>
                    <i class="fa-icon-reply"></i>
                </a4j:commandLink>


                <%--DETALHES--%>
                <a4j:commandLink actionListener="#{GestaoTransacoesControle.exibirDetalhesTransacao}"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 reRender="panelDadosTransacao"
                                 styleClass="tooltipster"
                                 title="Mais detalhes sobre esta transação">
                    <f:attribute name="transacao" value="#{transacao}"/>
                    <i class="fa-icon-eye-open"></i>
                </a4j:commandLink>

                <%--MAIS DE UMA PARCELA--%>
                <a4j:commandLink actionListener="#{GestaoTransacoesControle.exibirDetalhesTransacao}"
                                 rendered="#{transacao.qtdParcelasTransacao > 1}"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 reRender="panelDadosTransacao"
                                 styleClass="tooltipster"
                                 title="Essa transação possui mais de uma parcela. Clique para visualizar mais detalhes.">
                    <f:attribute name="transacao" value="#{transacao}"/>
                    <i class="fa-icon-list"></i>
                </a4j:commandLink>

                <%--SINCRONIZAR--%>
                <a4j:commandLink style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 reRender="formModalSincronizarTransacao"
                                 rendered="#{LoginControle.permissaoAcessoMenuVO.gestaoTransacoes && transacao.permiteSincronizar}"
                                 action="#{GestaoTransacoesControle.abrirModalSincronizarTransacao}"
                                 oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                 title="Consultar situação da transação">
                    <i class="fa-icon-refresh"></i>
                </a4j:commandLink>


                <%--RETENTATIVA--%>
                <a4j:commandLink id="btnRetentativaTransacao"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 reRender="formModalRetentativaTransacao"
                                 rendered="#{LoginControle.permissaoAcessoMenuVO.gestaoTransacoes && transacao.permiteRetentativa}"
                                 action="#{GestaoTransacoesControle.abrirModalRetentativaTransacao}"
                                 oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                 title="Realizar retentativa imediata da transação">
                    <i class="fa-icon-repeat"></i>
                </a4j:commandLink>


                <%--COMPROVANTE CANCELAMENTO--%>
                <h:outputLink styleClass="tooltipster"
                              style="margin-right: 10px; color: #444; font-size: 17px"
                              rendered="#{not empty transacao.urlComprovanteCancelamento}"
                              value="#{transacao.urlComprovanteCancelamento}"
                              title="Download do comprovante de cancelamento da transação"
                              target="_blank">
                    <i class="fa-icon-file-pdf" style="color: #444;"></i>
                </h:outputLink>


                <%--ENVIO EMAIL COMPROVANTE CANCELAMENTO--%>
                <a4j:commandLink style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 rendered="#{not empty transacao.urlComprovanteCancelamento}"
                                 reRender="panelEnviarEmailTransacao"
                                 action="#{GestaoTransacoesControle.abrirModalEnviarEmailComprovanteCancelamento}"
                                 oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                 title="Enviar e-mail do comprovante de cancelamento">
                    <i class="fa-icon-paper-plane"></i>
                </a4j:commandLink>


                <%--CANCELAR TRANSACAO--%>
                <a4j:commandLink style="margin-right: 10px; color: darkred; font-size: 17px"
                                 rendered="#{LoginControle.permissaoAcessoMenuVO.gestaoTransacoes && transacao.permiteCancelar}"
                                 styleClass="tooltipster"
                                 reRender="formModalCancelarTransacao"
                                 action="#{GestaoTransacoesControle.abrirModalCancelarTransacao}"
                                 oncomplete="#{GestaoTransacoesControle.mensagemNotificar};#{GestaoTransacoesControle.onComplete}"
                                 title="Cancelar/estornar transação">
                    <i class="fa-icon-ban-circle" style="color: darkred;"></i>
                </a4j:commandLink>

            </h:panelGroup>
        </rich:column>
    </rich:dataTable>

    <h:panelGrid columns="1" rendered="#{ColaboradorControle.listaTrasacoes.count > 0 }" width="100%" columnClasses="colunaCentralizada">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">
                            <h:outputText
                                    styleClass="texto-size-14 cinza"
                                    value="Total #{ColaboradorControle.listaTrasacoes.count} itens"></h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaTransacoes"
                                             actionListener="#{ColaboradorControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_TRANSACOES"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaTransacoes"
                                             actionListener="#{ColaboradorControle.paginaAnterior}">
                                <i class="fa-icon-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_TRANSACOES"/>
                            </a4j:commandLink>

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                          value="#{msg_aplic.prt_msg_pagina} #{ColaboradorControle.listaTrasacoes.paginaAtualApresentar}"
                                          rendered="true"/>
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                             reRender="containerListaTransacoes"
                                             actionListener="#{ColaboradorControle.proximaPagina}">
                                <i class="fa-icon-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_TRANSACOES"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaTransacoes"
                                             actionListener="#{ColaboradorControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_TRANSACOES"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText
                                        styleClass="texto-size-14 cinza "
                                        value="Itens por página "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{ColaboradorControle.listaTrasacoes.limit}">
                                    <f:selectItem itemValue="#{6}"></f:selectItem>
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{20}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange"
                                                 actionListener="#{ColaboradorControle.atualizarNumeroItensPagina}"
                                                 reRender="containerListaTransacoes">
                                        <f:attribute name="tipo" value="LISTA_TRANSACOES"/>
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>
</a4j:outputPanel>

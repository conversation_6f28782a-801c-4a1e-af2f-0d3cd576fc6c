<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<c:set var="pageEstornoOuGestao" value="${pageContext.request.requestURI}" scope="request"/>
<a4j:outputPanel id="containerlistaMovConta"
                 rendered="#{TelaClienteControle.listaMovConta.count > 0}">
    <h:outputText value="Estorno Stone"
                  title="Aqui será exibido as transações de verificação de cartão de crédito. A transação de verificação é para saber </br> se o cartão está passível de ser realizado cobrança ou não e acontecem quando um novo cartão é adicionado ou editado"
                  style="margin-top: 20px; display: inline-block"
                  styleClass="texto-size-16 negrito cinzaEscuro pl20 tooltipster"/>

    <h:panelGroup rendered="#{TelaClienteControle.listaMovConta.count > 0}"
                  layout="block" style="float:right;">
        <h:panelGroup layout="block">
            <a4j:commandLink id="btnMovContaExcel"
                             styleClass="pure-button pure-button-small"
                             actionListener="#{ExportadorListaControle.exportar}"
                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','TransacaoCartaoCredito', 800,200);#{ExportadorListaControle.msgAlert}"
                             accesskey="3" title="Exportar dados para excel">
                <f:attribute name="tipo" value="xls"/>
                <f:attribute name="lista" value="#{TelaClienteControle.listaMovContaVo}"/>
                <f:attribute name="atributos"
                             value="codigo=Código,descricao=Descrição,observacoes=Obs.,dataLancamento=Dt. Lançamento,dataVencimento=Dt. Vencimento,valor=Valor"/>
                <f:attribute name="prefixo" value="TransacaoCartaoCredito"/>
                <f:attribute name="titulo" value="TransacaoCartaoCredito"/>
                <i class="fa-icon-excel"></i> &nbsp Excel
            </a4j:commandLink>

            <a4j:commandLink id="btnMovContaPDF"
                             styleClass="pure-button pure-button-small margin-h-10"
                             actionListener="#{ExportadorListaControle.exportar}"
                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                             accesskey="4" title="Exportar dados para pdf">
                <f:attribute name="tipo" value="pdf"/>
                <f:attribute name="lista" value="#{TelaClienteControle.listaMovContaVo}"/>
                <f:attribute name="atributos"
                             value="codigo=Código,descricao=Descrição,observacoes=Obs.,dataLancamento=Dt. Lançamento,dataVencimento=Dt. Vencimento,valor=Valor"/>
                <f:attribute name="prefixo" value="TransacaoCartaoCredito"/>
                <f:attribute name="titulo" value="TransacaoCartaoCredito"/>
                <i class="fa-icon-pdf"></i> &nbsp PDF
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>

    <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status" id="tblMovContaCliente"
                    styleClass="tabelaDados font-size-Em"
                    style="#{fn:contains(pageEstornoOuGestao, 'gestaoTransacoes') ? 'margin: 0; width: 100%;' : 'width: 97%;'}"
                    onRowMouseOver="this.addClassName('background-color-tabelaDados-over')"
                    onRowMouseOut="this.removeClassName('background-color-tabelaDados-over')"
                    value="#{TelaClienteControle.listaMovContaVo}"
                    rendered="#{TelaClienteControle.listaMovConta.count > 0}"
                    var="conta">

        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

        <rich:column sortBy="#{conta.codigo}">
            <f:facet name="header">
                <h:outputText value="Cod."/>
            </f:facet>
            <h:outputText value="#{conta.codigo}"
                          styleClass="tooltipster"
                          title="Código da conta a pagar"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{conta.descricao}">
            <f:facet name="header">
                <h:outputText value="Descrição"
                              styleClass="tooltipster"
                              title="Descrição da conta a pagar"/>
            </f:facet>
            <h:outputText
                    styleClass="tooltipster"
                    title="#{conta.observacoes_title}"
                    value="#{conta.descricao}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{conta.dataLancamento}">
            <f:facet name="header">
                <h:outputText value="Dt. Lançamento"
                              styleClass="tooltipster"
                              title="Data de lançamento da conta a pagar"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          title="Data de lançamento da conta a pagar"
                          value="#{conta.dataLancamento_Apresentar}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{conta.valor}">
            <f:facet name="header">
                <h:outputText value="Valor"/>
            </f:facet>
            <h:outputText
                    styleClass="tooltipster"
                    value="#{TelaClienteControle.empresaLogado.moeda} #{conta.valor_Apresentar}"/>
        </rich:column>
    </rich:dataTable>

    <h:panelGrid columns="1" rendered="#{TelaClienteControle.listaMovConta.count > 0 }" width="100%"
                 columnClasses="colunaCentralizada">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">
                            <h:outputText
                                    styleClass="texto-size-14 cinza"
                                    value="Total #{TelaClienteControle.listaMovConta.count} itens"></h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerlistaMovConta"
                                             actionListener="#{TelaClienteControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_MOV_CONTA"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerlistaMovConta"
                                             actionListener="#{TelaClienteControle.paginaAnterior}">
                                <i class="fa-icon-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_MOV_CONTA"/>
                            </a4j:commandLink>

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                          value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaMovConta.paginaAtualApresentar}"
                                          rendered="true"/>
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                             reRender="containerlistaMovConta"
                                             actionListener="#{TelaClienteControle.proximaPagina}">
                                <i class="fa-icon-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_MOV_CONTA"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerlistaMovConta"
                                             actionListener="#{TelaClienteControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_MOV_CONTA"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText
                                        styleClass="texto-size-14 cinza "
                                        value="Itens por página "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{TelaClienteControle.listaMovConta.limit}">
                                    <f:selectItem itemValue="#{6}"></f:selectItem>
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{20}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange"
                                                 actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}"
                                                 reRender="containerlistaMovConta">
                                        <f:attribute name="tipo" value="LISTA_MOV_CONTA"/>
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>
</a4j:outputPanel>

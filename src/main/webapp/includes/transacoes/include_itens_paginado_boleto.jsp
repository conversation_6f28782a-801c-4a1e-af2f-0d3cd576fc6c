<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<%@include file="include_paramspanel_asaas.jsp" %>
<c:set var="pageEstornoOuGestao" value="${pageContext.request.requestURI}" scope="request"/>
<a4j:outputPanel id="containerListaBoletoGeral">
    <h:outputText value="Boletos Gerados - Online"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 negrito cinzaEscuro pl20"/>
    <h:outputText value="Nenhum boleto online"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 cinza pl20"
                  rendered="#{TelaClienteControle.listaBoletoGeral.count <= 0}"/>

    <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status" id="tblTransacoesBoletoGeral"
                    styleClass="tabelaDados font-size-Em"
                    onRowMouseOver="this.addClassName('background-color-tabelaDados-over')"
                    onRowMouseOut="this.removeClassName('background-color-tabelaDados-over')"
                    value="#{TelaClienteControle.listaBoletoGeralVo}"
                    rendered="#{TelaClienteControle.listaBoletoGeral.count > 0}"
                    var="boleto">

        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

        <rich:column style="padding-right: 6px;">
            <f:facet name="header">
                <h:selectBooleanCheckbox value="#{TelaClienteControle.marcarTodosBoleto}"
                                         styleClass="tooltipster"
                                         title="Selecionar todos os boletos">
                    <a4j:support event="onclick" status="false"
                                 action="#{TelaClienteControle.acaoMarcarTodosBoleto}"
                                 reRender="tblTransacoesBoletoGeral,form:panelInferiorAcoesBoletoOnline"/>
                </h:selectBooleanCheckbox>
            </f:facet>

            <%--erro e estornado não tem como imprimir o boleto, não mostrar checkbox--%>
            <h:selectBooleanCheckbox styleClass="texto-size-14 cinza tooltipster"
                                     title="Selecionar boleto"
                                     rendered="#{!boleto.erro && !boleto.estornado}"
                                     value="#{boleto.selecionado}">
                <a4j:support event="onclick" status="false"
                             reRender="form:panelInferiorAcoesBoletoOnline"/>
            </h:selectBooleanCheckbox>
        </rich:column>

        <rich:column sortBy="#{boleto.idExterno}">
            <f:facet name="header">
                <h:outputText value="Id Externo"
                              title="Identificador"
                              styleClass="tooltipster"/>
            </f:facet>
            <h:outputText value="#{boleto.idExterno}"
                          styleClass="tooltipster"
                          title="#{boleto.informacoesTitleTelaCliente}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{boleto.linhaDigitavel}">
            <f:facet name="header">
                <h:outputText value="Linha Digitável"/>
            </f:facet>
            <h:outputText value="#{boleto.linhaDigitavel}"
                          styleClass="tooltipster"
                          style="padding: 10px"
                          title="#{boleto.informacoesTitleTelaCliente}"/>
        </rich:column>


        <rich:column styleClass="centro" headerClass="centro">
            <f:facet name="header">
                <h:outputText value="Parcelas"/>
            </f:facet>

            <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status"
                            styleClass="tabelaDados font-size-Em"
                            value="#{boleto.listaBoletoMovParcela}"
                            var="boletomovparcela">

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Parcela" style="padding-inline-end: 8px;margin-right: 10px; display: block;"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  value="#{boletomovparcela.movParcelaVO.codigo}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Descrição" style="padding-inline-end: 8px; margin-right: 10px; display: block;"/>
                    </f:facet>
                    <h:outputText styleClass="tooltipster"
                                  title="#{boletomovparcela.movParcelaVO.pessoa_Apresentar_UpperCase}"
                                  value="#{boletomovparcela.movParcelaVO.descricao}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor" style="padding-inline-end: 50px;"/>
                    </f:facet>
                    <h:outputText value="#{boleto.pago ? boletomovparcela.valorParcelaApresentar : boletomovparcela.valorTotalApresentar}"/>
                </rich:column>
            </rich:dataTable>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{boleto.valor}">
            <f:facet name="header">
                <h:outputText value="Valor"/>
            </f:facet>
            <h:outputText value="#{boleto.valorApresentar}"
                          styleClass="tooltipster"
                          title="#{boleto.informacoesTitleTelaCliente}"/>
        </rich:column>

        <rich:column sortBy="#{boleto.dataVencimento}">
            <f:facet name="header">
                <h:outputText value="Vencimento"
                              styleClass="tooltipster"
                              title="Data de vencimento do boleto"/>
            </f:facet>
            <h:outputText value="#{boleto.dataVencimentoApresentar}"
                          styleClass="tooltipster"
                          title="#{boleto.informacoesTitleTelaCliente}"/>
        </rich:column>

        <rich:column sortBy="#{boleto.reciboPagamentoVO.codigo}">
            <f:facet name="header">
                <h:outputText value="Recibo"
                              styleClass="tooltipster"/>
            </f:facet>
            <h:outputText value="#{boleto.reciboPagamentoApresentar}"
                          styleClass="tooltipster"
                          title="#{boleto.reciboPagamentoTitleTelaCliente}"/>
        </rich:column>

        <rich:column sortBy="#{boleto.situacao.descricao}">
            <f:facet name="header">
                <h:outputText value="Situação"/>
            </f:facet>
<%--            grid-template-columns: 0fr 2fr;--%>
<%--            display: grid;--%>
            <h:panelGroup style="display: grid; grid-template-columns: 2fr 2fr">
            <h:outputText value="#{boleto.situacao.descricao}"
                          title="#{boleto.situacaoTitleTelaCliente}"
                          styleClass="tooltipster"/>
            <h:outputText rendered="#{boleto.canceladoOuEstornado && not empty boleto.jsonEstorno}"
                          style="font-size: 18px; margin-left: 4px; color: #F06D29; margin-right: 12px; margin-top: -3px"
                          styleClass="fa-icon-exclamation-triangle-sign tooltipster"
                          title="#{boleto.situacaoTitleTelaCliente}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column styleClass="colunaEsquerda">
            <f:facet name="header">
                <h:outputText value="Ações"/>
            </f:facet>
            <h:panelGroup style="display: inline-flex">

                <%-- IMPRIMIR BOLETO --%>
                <a4j:commandLink id="btnImprimirBoletoIncludeItensPaginadoBoleto"
                                 style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                                 action="#{BoletoBancarioControle.validarECriarBoletoNaoRegistrado}"
                                 rendered="#{boleto.apresentarImprimirBoleto}"
                                 styleClass="tooltipster"
                                 title="Visualizar boleto"
                                 oncomplete="window.open('#{BoletoBancarioControle.itemReimpressaoBoletoVO.linkBoleto}', '_blank');"
                                 reRender="panelBoleto">
                    <img src="imagens/bandeiras/boleto.png" width="40px" height="30px">
                    <f:setPropertyActionListener value="#{boleto}" target="#{BoletoBancarioControle.itemReimpressaoBoletoVO}" />
                </a4j:commandLink>

                <%-- IMPRIMIR BOLETO BANCO BRASIL --%>
                <a4j:commandLink id="btnImprimirBoletoBancoBrasil"
                               style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                               action="#{BoletoBancarioControle.imprimirBoletoClienteBancoBrasil}"
                               rendered="#{boleto.apresentarImprimirBoletoBancoBrasil}"
                               styleClass="tooltipster"
                               title="Gerar PDF do boleto Banco do Brasil"
                               oncomplete="abrirPopupPDFImpressao('relatorio/#{BoletoBancarioControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595); #{BoletoBancarioControle.mensagemNotificar}"
                               reRender="panelMensagem, panelBotoes">
                    <img src="imagens/bandeiras/boleto.png" width="40px" height="30px">
                    <f:setPropertyActionListener value="#{boleto}" target="#{BoletoBancarioControle.itemReimpressaoBoletoVO}"/>
                </a4j:commandLink>

                <%-- IMPRIMIR RECIBO COBRANÇA ASAAS --%>
                <h:outputLink value="#{boleto.transactionReceiptUrl}"
                              rendered="#{boleto.apresentarImprimirReciboBoleto}"
                              target="_blank" id="linkReciboBoleto"
                              title="Visualizar comprovante de pagamento do Asaas"
                              style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                              styleClass="tooltipster">
                    <i class="fa-icon-file-alt"></i>
                </h:outputLink>

                <%-- DETALHES BOLETO PJBANK--%>
                <a4j:commandLink id="infBoletoGeral"
                                 rendered="#{boleto.apresentarDetalhes}"
                                 actionListener="#{TelaClienteControle.detalheBoleto}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}"
                                 style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                                 title="Detalhes do boleto"
                                 styleClass="tooltipster">
                    <f:attribute name="boletoDetalhe" value="#{boleto}"/>
                    <i class="fa-icon-eye-open"></i>
                </a4j:commandLink>

                <%-- DETALHES BOLETO ASAAS--%>
                <a4j:commandLink id="infBoletoGeralAsaas"
                                 rendered="#{boleto.apresentarDetalhesAsaas}"
                                 style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                                 reRender="panelDadosParametrosAsaas"
                                 styleClass="tooltipster"
                                 title="Visualizar detalhes do boleto em tempo real na API Asaas"
                                 actionListener="#{TelaClienteControle.detalheBoleto}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}">
                    <f:attribute name="boletoDetalhe" value="#{boleto}"/>
                    <i class="fa-icon-eye-open"></i>
                </a4j:commandLink>

                <%-- ENVIAR BOLETO EMAIL--%>
                <a4j:commandLink id="enviarEmailBoletoGeral"
                                 rendered="#{boleto.podeEnviarEmail}"
                                 actionListener="#{TelaClienteControle.enviarEmailBoleto}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}"
                                 style="margin-right: 10px; color: #444; font-size: 23px; text-decoration: none"
                                 title="Enviar boleto por email"
                                 styleClass="tooltipster">
                    <f:attribute name="boletoDetalhe" value="#{boleto}"/>
                    <i class="fa-icon-paper-plane"></i>
                </a4j:commandLink>

                <%-- ENVIAR BOLETO EMAIL BANCO BRASIL--%>
                <a4j:commandLink id="enviarEmailBoletoBancoBrasil"
                                 style="margin-right: 10px; color: #444; font-size: 23px; text-decoration: none"
                                 actionListener="#{TelaClienteControle.enviarEmailClienteBoletoBancoBrasil(boleto)}"
                                 rendered="#{boleto.apresentarImprimirBoletoBancoBrasil}"
                                 styleClass="tooltipster"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}"
                                 title="Enviar boleto Banco do Brasil por email.">
                    <i class="fa-icon-paper-plane"></i>
                </a4j:commandLink>

                <%-- CANCELAR BOLETO --%>
                <a4j:commandLink id="cancelarBoletoGeral"
                                 style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                                 title="#{boleto.titleCancelar}"
                                 rendered="#{boleto.podeCancelar}"
                                 reRender="formModalCancelarBoleto"
                                 actionListener="#{TelaClienteControle.selecionarBoletoCancelar}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}"
                                 styleClass="tooltipster">
                    <f:attribute name="boletoDetalhe" value="#{boleto}"/>
                    <i class="fa-icon-ban-circle" style="color: darkred;"></i>
                </a4j:commandLink>

                <%--SINCRONIZAR--%>
                <a4j:commandLink id="btnSincronizarBoletoGeral"
                                 style="margin-right: 10px; color: #444; font-size: 25px; text-decoration: none"
                                 styleClass="botoes tooltipster fontBtnAcoes"
                                 rendered="#{boleto.podeSincronizar}"
                                 reRender="formModalSincronizarBoleto"
                                 actionListener="#{TelaClienteControle.selecionarBoletoSincronizar}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar};#{TelaClienteControle.onCompleteBoleto}"
                                 title="Sincronizar boleto">
                    <f:attribute name="boletoDetalhe" value="#{boleto}"/>
                    <i class="fa-icon-refresh"></i>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>
    </rich:dataTable>

    <h:panelGrid columns="1" rendered="#{TelaClienteControle.listaBoletoGeral.count > 0 }" width="100%"
                 columnClasses="colunaCentralizada">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">
                            <h:outputText
                                    styleClass="texto-size-14 cinza"
                                    value="Total #{TelaClienteControle.listaBoletoGeral.count} itens"></h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaBoletoGeral"
                                             actionListener="#{TelaClienteControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_BOLETO_GERAL"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaBoletoGeral"
                                             actionListener="#{TelaClienteControle.paginaAnterior}">
                                <i class="fa-icon-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_BOLETO_GERAL"/>
                            </a4j:commandLink>

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                          value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaBoletoGeral.paginaAtualApresentar}"
                                          rendered="true"/>
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                             reRender="containerListaBoletoGeral"
                                             actionListener="#{TelaClienteControle.proximaPagina}">
                                <i class="fa-icon-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_BOLETO_GERAL"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaBoletoGeral"
                                             actionListener="#{TelaClienteControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_BOLETO_GERAL"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText
                                        styleClass="texto-size-14 cinza "
                                        value="Itens por página "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{TelaClienteControle.listaBoletoGeral.limit}">
                                    <f:selectItem itemValue="#{6}"></f:selectItem>
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{20}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange"
                                                 actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}"
                                                 reRender="containerListaBoletoGeral">
                                        <f:attribute name="tipo" value="LISTA_BOLETO_GERAL"/>
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>

    <h:panelGroup layout="block" id="panelInferiorAcoesBoletoOnline"
                  style="display: flex; justify-content: flex-end;"
                  rendered="#{TelaClienteControle.listaBoletoGeral.count > 0}">

        <a4j:commandLink id="cancelarBoletosSelecionados"
                         value="Cancelar boletos"
                         title="Cancelar boletos selecionados"
                         style="margin-right: 20px; background-color: darkred; color: #fff;"
                         reRender="formModalCancelarBoleto"
                         rendered="#{TelaClienteControle.apresentarCancelarBoletos}"
                         action="#{TelaClienteControle.prepararCancelarBoletosSelecionados}"
                         oncomplete="#{TelaClienteControle.onCompleteBoleto};#{TelaClienteControle.mensagemNotificar}"
                         styleClass="botoes nvoBt btSec btPerigo tooltipster">
        </a4j:commandLink>

        <a4j:commandLink id="imprimirBoletosOnlineSelecionados"
                         value="Imprimir boletos"
                         title="Imprimir boletos selecionados"
                         style="margin-right: 20px;"
                         reRender="form:tblTransacoesBoletoGeral"
                         rendered="#{TelaClienteControle.apresentarImprimirBoletos}"
                         action="#{TelaClienteControle.imprimirBoletosSelecionados}"
                         oncomplete="#{TelaClienteControle.onCompleteBoleto};#{TelaClienteControle.mensagemNotificar}"
                         styleClass="botoes nvoBt tooltipster">
        </a4j:commandLink>

        <a4j:commandLink id="enviarPorEmailBoletosOnlineSelecionados"
                         value="Enviar boletos por e-mail"
                         title="Enviar boletos selecionados por e-mail"
                         style="margin-right: 20px;"
                         reRender="form:tblTransacoesBoletoGeral"
                         rendered="#{TelaClienteControle.apresentarImprimirBoletos}"
                         action="#{TelaClienteControle.enviarEmailBoletoSelecionados}"
                         oncomplete="#{TelaClienteControle.onCompleteBoleto};#{TelaClienteControle.mensagemNotificar}"
                         styleClass="botoes nvoBt tooltipster">
        </a4j:commandLink>

        <a4j:commandLink id="sincronizarTodosBoletosGeral"
                         title="Sincronizar todos os boletos"
                         value="Sincronizar todos boletos"
                         style="margin-right: 20px;"
                         reRender="form:tblTransacoesBoletoGeral"
                         action="#{TelaClienteControle.sincronizarTodosBoletos}"
                         oncomplete="#{TelaClienteControle.mensagemNotificar}"
                         styleClass="botoes nvoBt btSec tooltipster">
        </a4j:commandLink>
    </h:panelGroup>
</a4j:outputPanel>

<%@include file="imports.jsp" %>

<rich:modalPanel id="mdllancartaxasboleto" styleClass="novaModal" width="800" autosized="true" shadowOpacity="true" top="50">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Taxas do boleto - #{FormaPagamentoControle.formaPagamentoEmpresa.empresa.nome}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkmdllancartaxasboleto"/>
            <rich:componentControl for="mdllancartaxasboleto" attachTo="hidelinkmdllancartaxasboleto" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formmdllancartaxasboleto" style="height: 500px; overflow-y: auto">
        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                     columnClasses="classEsquerda, classDireita" width="100%" rendered="#{FormaPagamentoControle.taxaBoletoVO.novoObj}">

            <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_FormaPagamento_taxaBoleto}"/>
            <h:inputText id="taxa" size="6" maxlength="6"
                         onkeyup="return porcentagem(this);"
                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                         value="#{FormaPagamentoControle.taxaBoletoVO.taxa}">
            </h:inputText>

            <h:outputText styleClass="tituloCampos" value="* Tipo"/>
            <h:selectOneRadio id="mudarTipoTaxa"
                              styleClass="tituloCampos" value="#{FormaPagamentoControle.taxaBoletoVO.tipo}">
                <f:selectItem itemLabel="Percentual" itemValue="1"/>
                <f:selectItem itemLabel="Valor" itemValue="2"/>
            </h:selectOneRadio>

            <h:outputText styleClass="tituloCampos"
                          value="* #{msg_aplic.prt_FormaPagamento_vigenciaInicial}"/>
            <h:panelGroup styleClass="dateTimeCustom" >
                <rich:calendar id="vigenciaInicialTaxa"
                               datePattern="dd/MM/yyyy"
                               buttonIcon="/imagens_flat/calendar-button.svg"
                               inputSize="8"
                               showWeekDaysBar="false"
                               showWeeksBar="false"
                               enableManualInput="true"
                               styleClass="form"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputchange="return validar_Data(this.id);"
                               value="#{FormaPagamentoControle.taxaBoletoVO.vigenciaInicial}"/>
            </h:panelGroup>


            <h:outputText styleClass="tituloCampos"
                          value="* #{msg_aplic.prt_FormaPagamento_vigenciaFinal}"/>
            <h:panelGroup styleClass="dateTimeCustom" >
                <rich:calendar id="vigenciaFinalTaxa"
                               datePattern="dd/MM/yyyy"
                               buttonIcon="/imagens_flat/calendar-button.svg"
                               inputSize="8"
                               showWeekDaysBar="false"
                               showWeeksBar="false"
                               enableManualInput="true"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputchange="return validar_Data(this.id);"
                               styleClass="form"
                               value="#{FormaPagamentoControle.taxaBoletoVO.vigenciaFinal}"/>
            </h:panelGroup>

        </h:panelGrid>

        <div style="text-align: center">
            <a4j:commandButton id="adicionar"
                               action="#{FormaPagamentoControle.adicionarTaxaBoleto}"
                               value="Adicionar" oncomplete="#{FormaPagamentoControle.msgAlert}"
                               style="vertical-align: middle;"
                               rendered="#{FormaPagamentoControle.taxaBoletoVO.novoObj}"
                               reRender="formmdllancartaxasboleto"
                               styleClass="botoes nvoBt bt" />
        </div>

        <div class="painelDadosAluno" style="width: calc(98.5% - 10px); height: auto;">
            <div class="tituloPainelAluno">
                <h:outputText styleClass="negrito cinzaEscuro pl20"
                              value="Taxa cadastrada"
                              style="line-height: 40px;font-size: 14px !important;"/>
            </div>
            <div class="conteudoDadosCliente" style="min-height: 50px;">

                <h:panelGroup rendered="#{FormaPagamentoControle.taxaBoletoVO.novoObj}">
                    <table width="100%" class="tblHeaderLeft">
                        <tr>
                            <td style="padding:10px">
                                <h:outputText value="Nenhuma taxa cadastrada para essa empresa."/>
                            </td>
                        </tr>
                    </table>
                </h:panelGroup>


                <h:dataTable id="dtTaxasEmpresa" width="100%" styleClass="tblHeaderLeft contratoSelecionado caixainfo"
                             rowClasses="linhaImpar, linhaPar"
                             rendered="#{!FormaPagamentoControle.taxaBoletoVO.novoObj}"
                             value="#{FormaPagamentoControle.taxaBoletoVO}" var="taxaBoleto">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Taxa Atual" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>
                        <h:outputText value="#{taxaBoleto.taxa}" styleClass="texto-size-14 cinza" rendered="#{!FormaPagamentoControle.apresentarEdicaoBoleto}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:inputText id="taxaBoletoEditar" size="6" maxlength="6"
                                     onkeyup="return porcentagem(this);"
                                     onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     rendered="#{FormaPagamentoControle.apresentarEdicaoBoleto}"
                                     value="#{taxaBoleto.taxa}">
                        </h:inputText>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Tipo" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>
                        <h:outputText rendered="#{taxaBoleto.tipo == 1 && !FormaPagamentoControle.apresentarEdicaoBoleto}" styleClass="texto-size-14 cinza" value="Percentual"/>
                        <h:outputText rendered="#{taxaBoleto.tipo == 2 && !FormaPagamentoControle.apresentarEdicaoBoleto}" styleClass="texto-size-14 cinza" value="Valor"/>
                        <h:panelGroup  styleClass="cb-container margenVertical" layout="block" rendered="#{FormaPagamentoControle.apresentarEdicaoBoleto}">
                            <h:selectOneMenu  id="tipoTaxaBoleto" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{taxaBoleto.tipo}" style="font-size: 14px !important;">
                                <f:selectItems value="#{FormaPagamentoControle.formaCalculoEnumToSelectedItens}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Vigente Desde" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>
                        <h:outputText value="#{taxaBoleto.vigenciaInicial}" styleClass="texto-size-14 cinza" rendered="#{!FormaPagamentoControle.apresentarEdicaoBoleto}"/>
                        <h:panelGroup styleClass="dateTimeCustom" rendered="#{FormaPagamentoControle.apresentarEdicaoBoleto}">
                            <rich:calendar id="vigenciaInicialTaxaBoleto"
                                           datePattern="dd/MM/yyyy"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputSize="8"
                                           showWeekDaysBar="false"
                                           showWeeksBar="false"
                                           enableManualInput="true"
                                           styleClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           value="#{taxaBoleto.vigenciaInicial}"/>
                        </h:panelGroup>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Vigente Ate" styleClass="texto-size-14 cinza negrito upper"/>
                        </f:facet>
                        <h:outputText value="#{taxaBoleto.vigenciaFinal}" styleClass="texto-size-14 cinza" rendered="#{!FormaPagamentoControle.apresentarEdicaoBoleto}"/>
                        <h:panelGroup styleClass="dateTimeCustom" rendered="#{FormaPagamentoControle.apresentarEdicaoBoleto}">
                            <rich:calendar id="vigenciaFinalTaxaBoleto"
                                           datePattern="dd/MM/yyyy"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputSize="8"
                                           showWeekDaysBar="false"
                                           showWeeksBar="false"
                                           enableManualInput="true"
                                           styleClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           value="#{taxaBoleto.vigenciaFinal}"/>
                        </h:panelGroup>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}" styleClass="texto-size-14 cinza negrito upper center"/>
                        </f:facet>

                        <a4j:commandLink id="removerTaxaEmpresaBoleto"
                                         action="#{FormaPagamentoControle.removerTaxaBoleto}"
                                         reRender="formmdllancartaxasboleto"
                                         style="vertical-align: middle; display: initial;"
                                         styleClass="botoes nvoBt btSec " accesskey="7">
                            <i class="fa-icon-trash"></i>
                        </a4j:commandLink>

                        <a4j:commandLink id="editarTaxaEmpresaBoleto"
                                         action="#{FormaPagamentoControle.selecionarTaxaBoleto}"
                                         reRender="formmdllancartaxasboleto"
                                         value="Editar"
                                         style="vertical-align: middle; display: initial;"
                                         styleClass="botoes nvoBt btSec " accesskey="7"
                                         rendered="#{!FormaPagamentoControle.apresentarEdicaoBoleto}">
                        </a4j:commandLink>

                    </h:column>
                </h:dataTable>

                <div style="text-align: center">
                    <a4j:commandButton id="gravarTaxaVigenciaBoleto"
                                       action="#{FormaPagamentoControle.alterarTaxaBoleto}"
                                       value="Salvar"
                                       style="vertical-align: middle"
                                       reRender="formmdllancartaxasboleto"
                                       styleClass="botoes nvoBt btSec"
                                       rendered="#{FormaPagamentoControle.apresentarEdicaoBoleto}"/>
                </div>

            </div>
        </div>

        <div style="text-align: center; margin: 20px 0 10px 0;">
            <a onclick="Richfaces.hideModalPanel('mdllancartaxasboleto')" class="botoes nvoBt bt" style="cursor: pointer">
                Concluir
            </a>
        </div>



    </a4j:form>
</rich:modalPanel>
<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 15/04/2016
  Time: 16:21
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="imports.jsp" %>

<script>
    function validar(){

        if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
                && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
                && document.getElementById('formConsultarCEP:bairroCEP').value == ""
                && document.getElementById('formConsultarCEP:logradouroCEP').value == ""){

            alert("Ao menos um parâmetro deve ser informado!");
            return false;
        }
        return true;
    }
</script>
<rich:modalPanel id="panelDadosPendenteCliente" autosized="true"  styleClass="novaModal noMargin" shadowOpacity="true" width="890" height="600" >
  <f:facet name="header">
    <h:panelGroup>
      <h:outputText value="Dados Pendentes Cliente"></h:outputText>
    </h:panelGroup>
  </f:facet>
  <f:facet name="controls">
    <h:panelGroup>
      <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkPanelDadosPendenteCliente"/>
      <rich:componentControl for="panelDadosPendenteCliente" attachTo="hidelinkPanelDadosPendenteCliente" operation="hide" event="onclick"/>
    </h:panelGroup>
  </f:facet>
  <a4j:form id="formDadosPendentes" ajaxSubmit="true" style="overflow-y: auto;overflow-x: hidden;height: 550px">
      <h:panelGroup layout="block" styleClass="margin-box">
         <jsp:include page="../include_form_novo_cliente.jsp" flush="true"/>
          <!-- inicio botões -->
          <h:panelGroup style="padding-bottom: 10px;height: 50px;line-height: 50px;" layout="block">
              <h:panelGroup layout="block" style="width: 100%;height: 100%;text-align: center;display: inline-block;">
                  <a4j:commandLink   rendered="#{ContratoControle.apresentarBotoesFecharEReceber}"
                                     id="botaoPagar"
                                     action="#{ContratoControle.concluirDadosPendentes}"
                                     value="#{msg_bt.btn_confirmar}"
                                     reRender="formDadosPendentes"
                                     styleClass="botaoPrimario texto-size-16"
                                     oncomplete="#{ContratoControle.mensagemNotificar}"/>
              </h:panelGroup>
          </h:panelGroup>
          <!-- fim botões -->
      </h:panelGroup>
  </a4j:form>
</rich:modalPanel>
<jsp:include  page="../include_modais_tela_cadastro_cliente.jsp" flush="true" />
<rich:modalPanel id="panelUsuarioSenha" autosized="true" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formUsuarioSenha:senha').focus()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Confirmação do Fechamento do Contrato"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkUsuarioSenha"/>
            <rich:componentControl for="panelUsuarioSenha" attachTo="hidelinkUsuarioSenha" operation="hide"  event="onclick">

            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formUsuarioSenha">
        <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
            <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="Confirmação do Fechamento do Contrato"/>
            </h:panelGrid>
            <h:panelGrid id="panelConfimacao" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                <h:panelGroup>
                    <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                    <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                </h:panelGroup>
                <h:panelGroup >
                    <h:outputText styleClass="text"  value="Código:" />
                    <h:inputText id="codigoUsuario" size="3" maxlength="100" style="margin-left:5px" value="#{ContratoControle.contratoVO.responsavelContrato.codigo}">
                        <a4j:support event="onchange" focus="formUsuarioSenha:senha" action="#{ContratoControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                    </h:inputText>
                    <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{ContratoControle.contratoVO.responsavelContrato.username}"/>
                </h:panelGroup>
                <h:panelGroup >
                    <h:outputText styleClass="text"  value="Usuário:" />
                    <h:outputText styleClass="text" style="margin-left:6px"
                                  value="#{ContratoControle.contratoVO.responsavelContrato.username}" />
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText styleClass="text"  value="Senha:"/>
                    <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                   value="#{ContratoControle.contratoVO.responsavelContrato.senha}"
                            onkeypress="validarEnterSenha(event,'formUsuarioSenha:loginCaixa')"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                          value="#{ContratoControle.mensagemDetalhada}"/>
            <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber}" id="loginCaixa" value="#{msg_bt.btn_confirmar}"
                               image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}"
                               action="#{ContratoControle.validarUsuarioResponsavelEmpresaPermissaoFecharContrato}"/>
            <a4j:commandLink style="display:none;" id="botaoInvisivelTela71"
                             action="#{ContratoControle.validarUsuarioResponsavelEmpresaPermissaoFecharContrato}"/>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="panelConfimacaoPagamento" autosized="true"
                 shadowOpacity="true" width="450" height="250"
                 styleClass="novaModal"
                 onshow="document.getElementById('formConfimacaoPagamento:senha2').focus()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Confirmação do Fechamento do Contrato"></h:outputText>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>

            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="hidelinkConfirmacaoPagamento"/>
            <rich:componentControl for="panelConfimacaoPagamento" attachTo="hidelinkConfirmacaoPagamento" operation="hide"  event="onclick">

            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formConfimacaoPagamento">
        <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
            <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="Confirmação do Fechamento do Contrato"/>
            </h:panelGrid>
            <h:panelGrid id="panelConfimacao" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                <h:panelGroup>
                    <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                    <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                </h:panelGroup>
                <h:panelGroup >
                    <h:outputText styleClass="text"  value="Código:" />
                    <h:inputText id="codigoUsuario" size="5" maxlength="7" style="margin-left:5px" value="#{ContratoControle.contratoVO.responsavelContrato.codigo}">
                        <a4j:support event="onchange" focus="formConfimacaoPagamento:senha2" action="#{ContratoControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                    </h:inputText>
                    <h:inputText id="autoCompleteHidden2" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{ContratoControle.contratoVO.responsavelContrato.username}"/>
                </h:panelGroup>
                <h:panelGroup >
                    <h:outputText styleClass="text"  value="Usuário:" />
                    <h:outputText styleClass="text" style="margin-left:6px"  value="#{ContratoControle.contratoVO.responsavelContrato.username}" />
                </h:panelGroup>
                <h:panelGroup>
                    <h:outputText  styleClass="text" value="Senha:"/>
                    <h:inputSecret autocomplete="off" id="senha2" size="14" maxlength="64" style="margin-left:8px"
                                   value="#{ContratoControle.contratoVO.responsavelContrato.senha}"
                            onkeypress="validarEnterSenha(event,'formConfimacaoPagamento:login')"/>
                </h:panelGroup>
            </h:panelGrid>
            <h:outputText id="mensagem" styleClass="mensagemDetalhada" value="#{ContratoControle.mensagemDetalhada}"/>
            <a4j:commandButton rendered="#{ContratoControle.apresentarBotoesFecharEReceber}" id="login" value="#{msg_bt.btn_confirmar}"
                               image="./imagens/btn_Confirmar.png" alt="#{msg.msg_gravar_dados}" action="#{ContratoControle.validarUsuarioResponsavelEmpresaGravarEDepoisIrTelaPagamento}"/>
            <a4j:commandLink style="display:none;" id="botaoInvisivelTela72" action="#{ContratoControle.validarUsuarioResponsavelEmpresaGravarEDepoisIrTelaPagamento}"/>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<jsp:include  page="include_modal_validacaoCpf.jsp" flush="true" />

<%@ page import="controle.arquitetura.security.AutorizacaoFuncionalidadeControle" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="../imports.jsp" %>

<script>
    function setarPermissaoFalso() {
        document.getElementById('formSenhaAutorizacao:idBtnpedirpermissaofalso').click();
    }
</script>

<rich:modalPanel id="panelAutorizacaoFuncionalidade" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="656"
                 showWhenRendered="#{AutorizacaoFuncionalidadeControle.pedirPermissao}"
                 onshow="document.getElementById('formSenhaAutorizacao:senha').focus(); setarPermissaoFalso();"
                 onhide="document.getElementById('formSenhaAutorizacao:botaoFecharHide').click()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{AutorizacaoFuncionalidadeControle.titulo}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkAutorizacao"/>
            <rich:componentControl for="panelAutorizacaoFuncionalidade" attachTo="hidelinkAutorizacao"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form prependId="true" id="formSenhaAutorizacao">
        <a4j:commandLink action="#{AutorizacaoFuncionalidadeControle.pedirPermissaoFalso}"
                         status="false"
                         id="idBtnpedirpermissaofalso"
                         style="display: none;">
        </a4j:commandLink>
        <a4j:commandButton style="display : none;" status="false" id="botaoFecharHide"
                           reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                           action="#{AutorizacaoFuncionalidadeControle.fechar}"/>
        <h:panelGroup layout="block" styleClass="margin-box">

            <h:outputText styleClass="texto-size-14-real texto-cor-cinza" style="margin-left: 5px;"
                          value="#{AutorizacaoFuncionalidadeControle.mensagemUsuario}"/>

            <h:panelGrid id="panelConfimSenhaAutorizacao" width="75%" columns="2" cellpadding="5"
                         styleClass="font-size-Em-max">
                <h:inputText style="opacity:0;height: 0px;" size="5" maxlength="7"/>
                <h:inputSecret size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                <h:outputText styleClass="rotuloCampos" value="Usuário:" style="font-weight: normal !important;"/>
                <h:outputText styleClass="rotuloCampos" value="Insira a senha ou o PIN:"
                              style="font-weight: normal !important; margin-left: 20px;"/>
                <h:inputText styleClass="texto-font texto-size-16 texto-cor-cinza" style="width: 268px;"
                             value="#{AutorizacaoFuncionalidadeControle.usuario.username}">
                    <a4j:support event="onchange" focus="formSenhaAutorizacao:senha"
                                 action="#{AutorizacaoFuncionalidadeControle.consultarUsuarioPorUserName}"
                                 reRender="panelConfimSenhaAutorizacao, mensagemAutorizacaoFuncionalidade"/>
                </h:inputText>
                <h:inputSecret autocomplete="off" id="senha" styleClass="inputTextClean" maxlength="64"
                               style="width: 268px; margin-left: 20px;"
                               value="#{AutorizacaoFuncionalidadeControle.usuario.senha}"
                               onkeypress="validarEnter(event,'formSenhaAutorizacao:btnAutorizar');"/>

            </h:panelGrid>

            <h:panelGroup layout="block" styleClass="container-botoes" style="margin-top: 25px;">
                <a4j:commandLink id="btnAutorizar" value="#{msg.msg_confirmar}"
                                 styleClass="botaoPrimario texto-size-16"
                                 reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                                 oncomplete="#{AutorizacaoFuncionalidadeControle.onComplete};#{AutorizacaoFuncionalidadeControle.mensagemNotificar};montaModulomenu();"
                                 title="#{msg.msg_gravar_dados}"
                                 action="#{AutorizacaoFuncionalidadeControle.invoke}"/>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup id="panelFinalizarAutorizacao"
                      rendered="#{AutorizacaoFuncionalidadeControle.finalizarAutorizacaoSemExibirModal}">
            <a4j:jsFunction name="finalizarAutorizacao"
                            action="#{AutorizacaoFuncionalidadeControle.finalizarAutorizacao}"
                            reRender="#{AutorizacaoFuncionalidadeControle.renderComponents}"
                            oncomplete="#{AutorizacaoFuncionalidadeControle.onComplete};#{AutorizacaoFuncionalidadeControle.mensagemNotificar}"/>
            <script>
                jQuery(document).ready(function() {
                    finalizarAutorizacao();
                });
            </script>
        </h:panelGroup>
        <h:panelGroup id="panelNotificacoes"
                      rendered="#{not empty AutorizacaoFuncionalidadeControle.mensagemNotificar and AutorizacaoFuncionalidadeControle.pedirPermissao}">
            <a4j:jsFunction name="exibirNotificacao"
                            oncomplete="#{AutorizacaoFuncionalidadeControle.mensagemNotificar};"/>
            <script>
                jQuery(document).ready(function() {
                    exibirNotificacao();
                });
            </script>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

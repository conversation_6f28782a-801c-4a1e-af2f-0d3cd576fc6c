<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 01/06/2016
  Time: 11:57
  To change this template use File | Settings | File Templates.
--%>
<%@include file="imports.jsp" %>

<rich:modalPanel id="modalPanelFotoPendente" width="300" autosized="true" showWhenRendered="#{SuperControle.fotoUsuarioPendente}"
                 styleClass="novaModal"
                 shadowOpacity="true" >
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Voc� possui foto pendente"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
           <h:outputText
                styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                id="hidelink1"/>
            <rich:componentControl for="modalPanelFotoPendente"
                                   attachTo="hidelink1" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formFotoPendente" ajaxSubmit="true" styleClass="font-size-Em">


        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink 
                actionListener="#{CapturaFotoControle.selecionarPessoa}"
                action="#{CapturaFotoControle.vazio}"
                id="btnAlterarFoto"
                oncomplete="setAttributesModalCapFoto(
                    '#{ClienteControle.key}',
                    '#{SuperControle.usuarioLogado.colaboradorVO.pessoa.codigo}',
                    '#{SuperControle.contextPath}', '');
                    Richfaces.showModalPanel('modalCapFotoHTML5');"
                title="#{msg_bt.btn_capturarfoto}" styleClass="linkPadrao texto-size-20">
                <f:attribute name="pessoa" value="#{SuperControle.usuarioLogado.colaboradorVO.pessoa.codigo}"/>
                <h:outputText styleClass="fa-icon-camera-retro fa-3x "></h:outputText>
                <h:outputText style="margin-left: 10px;" value="#{msg_bt.btn_obterFoto}"></h:outputText>
            </a4j:commandLink>

        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink action="#{SuperControle.salvarExibirModalFotoPendente}"
                             id="checkboxFotoPendente"
                             styleClass="linkPadrao texto-size-16"
                             oncomplete="Richfaces.hideModalPanel('modalPanelFotoPendente')" >
                <h:outputText styleClass="#{SuperControle.exibirModalFotoPendente ? 'fa-icon-check-empty' : 'fa-icon-check'} texto-cor-azul"/>
                <h:outputText style="margin-left: 5px;color: #777777" value="N�o exibir novamente"/>
            </a4j:commandLink>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
<jsp:include page="includes/cliente/include_modal_capfoto_html5.jsp"/>



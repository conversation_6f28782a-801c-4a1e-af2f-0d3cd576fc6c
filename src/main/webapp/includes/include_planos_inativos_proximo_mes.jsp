<%@ include file="imports.jsp" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<link href="${root}/dicas/css/jquery.stepy.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="${root}/dicas/js/jquery.stepy.min.js"></script>

<style>
    .btModalPlanosInativos {
        font-size: 11px !important;
        background: #094771 !important;
        color: #ffffff !important;
        padding: 5px !important;
    }
    .btModalPlanosInativos:hover {
        background: #073071 !important;
        color: #ffffff !important;
    }
    .inline-elements {
        display: flex;
        align-items: center;
    }
</style>

<a4j:jsFunction name="atualizarPlanos"
                action="#{LoginControle.atualizarListaPlanos}"
                reRender="formVerificarPlanosInativos:lstPlanosProximosInativar, modalVerificarPlanosInativos"
/>

<rich:modalPanel id="modalVerificarPlanosInativos"
                 showWhenRendered="#{LoginControle.apresentarModalPlanosInativos}"
                 onshow="atualizarPlanos()"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="500" height="500">

    <!-- Título do Modal -->
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Atenção: Planos que ficarão inativos no próximo mês" />
        </h:panelGroup>
    </f:facet>

    <!-- Botão de Fechar -->
    <f:facet name="controls">
        <h:panelGroup>
            <a4j:commandLink
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    style="color: #fff"
                    action="#{LoginControle.fecharModalPlanosInativos}"
                    id="hideModalPlanosInativos" />
            <rich:componentControl for="modalVerificarPlanosInativos"
                                   attachTo="hideModalPlanosInativos"
                                   operation="hide"
                                   event="onclick" />
        </h:panelGroup>
    </f:facet>

    <!-- Form para "Não exibir novamente hoje" -->
    <a4j:form styleClass="pure-form" ajaxSubmit="true" id="formNaoMostrarModalPlanosHoje">
        <h:panelGroup layout="block"
                      styleClass="paginaFontResponsiva"
                      style="width: 100%; padding-bottom: 10px; padding-top: 10px;">
            <h:panelGroup layout="inline" styleClass="inline-elements">
                <h:outputText value="Não exibir novamente este aviso hoje" style="font-weight: bold; margin-right: 10px;" />
                <h:selectBooleanCheckbox valueChangeListener="#{UsuarioControle.showModalPlanos}"
                                         style="margin-right: 10px;">
                    <a4j:support event="onchange"
                                 oncomplete="Richfaces.hideModalPanel('modalVerificarPlanosInativos');"
                                 action="#{LoginControle.naoApresentarModalPlanosInativos}" />
                </h:selectBooleanCheckbox>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}como-reativar-um-plano-que-nao-esta-mais-vigente/"
                              title="Clique e saiba mais: Reativar Plano"
                              target="_blank">
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>

    <!-- Form com a listagem dos planos inativos -->
    <a4j:form id="formVerificarPlanosInativos" styleClass="pure-form" ajaxSubmit="true">
        <h:panelGroup>
            <rich:dataTable id="lstPlanosProximosInativar"
                            value="#{LoginControle.listaPlanosProximosInativar}"
                            var="plano"
                            width="100%"
                            headerClass="consulta"
                            rowClasses="linhaPar,linhaImpar"
                            rows="10"
                            style="margin-bottom: 2%;">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Plano" />
                    </f:facet>
                    <h:outputText value="#{plano.descricao}" />
                </rich:column>
                <rich:column style="text-align: center">
                    <f:facet name="header">
                        <h:outputText value="Vigência Até" />
                    </f:facet>
                    <h:outputText value="#{plano.vigenciaAte}">
                        <f:convertDateTime pattern="dd/MM/yyyy" />
                    </h:outputText>
                </rich:column>
            </rich:dataTable>

            <rich:datascroller rendered="#{fn:length(LoginControle.listaPlanosProximosInativar) > 9}"
                               for="lstPlanosProximosInativar" maxPages="10"
                               page="#{UsuarioControle.scrollerPage}"/>
        </h:panelGroup>

        <!-- Botões de Ação -->
        <h:panelGroup layout="block"
                      styleClass="paginaFontResponsiva"
                      style="width: 100%; text-align: center; padding-bottom: 10px; padding-top: 7px">
            <br/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>

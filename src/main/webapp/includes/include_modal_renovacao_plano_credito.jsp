<%@include file="imports.jsp" %>
<rich:modalPanel id="modalRenovarContratoCredito"  styleClass="novaModal"  autosized="true" shadowOpacity="true" showWhenRendered="#{ContratoControle.mostrarModalRenovacaoCredito}" width="300" height="220">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Renova��o antecipada de plano CR�DITO para um plano N�O CR�DITO"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkRenovarContratoCredito"/>
            <rich:componentControl for="modalRenovarContratoCredito"
                                   attachTo="hidelinkRenovarContratoCredito" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>



    <a4j:form id="formRenovarContratoCredito" style="height: 100%;">
        <h:panelGrid columns="1" columnClasses="colunaEsquerda">
          <h:outputLabel
                  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                  value="Foi identificado que voc� est� renovando antecipadamente um plano do tipo cr�dito para um plano que n�o � de cr�dito."></h:outputLabel>
            <h:outputLabel
                    styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                    value="Leia atentamente as op��es abaixo, pois esta opera��o n�o poder� ser revertida."></h:outputLabel>

            <rich:spacer height="10px"></rich:spacer>
            <h:outputLabel  style="font-weight: bold" styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered" value="O que deseja fazer?"></h:outputLabel>
            <rich:spacer height="10px"></rich:spacer>

            <h:panelGroup layout="block"  styleClass="container-botoes">
                <a4j:commandLink id="btnrenovarOpcao1"
                                 reRender="modalRenovarContratoCredito, formRenovarContratoCreditoOpcao1"
                                 action="#{ContratoControle.definirDataInicioNovoContratoAposTerminoContratoOriginal}"
                                 oncomplete="Richfaces.showModalPanel('modalConfirmarRenovarContratoCreditoOpcao1');"
                                 value="Iniciar novo contrato ap�s o t�rmino do contrato atual"
                                 accesskey="2" styleClass="botaoPrimario texto-size-14-real"/>
            </h:panelGroup>
            <rich:spacer height="10px"></rich:spacer>
            <h:panelGroup layout="block"  styleClass="container-botoes">
                <a4j:commandLink id="btnrenovarOpcao2"
                                 reRender="modalRenovarContratoCredito,datanovocontrato, modalConfirmarRenovarContratoCreditoOpcao2"
                                 oncomplete="Richfaces.showModalPanel('modalConfirmarRenovarContratoCreditoOpcao2');"
                                 value="Escolher data de in�cio do novo contrato"
                                 accesskey="2" styleClass="botaoPrimario texto-size-14-real"/>
            </h:panelGroup>


        </h:panelGrid>
    </a4j:form>

</rich:modalPanel>

<rich:modalPanel id="modalConfirmarRenovarContratoCreditoOpcao1"  styleClass="novaModal"  autosized="true" shadowOpacity="true" width="600" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Iniciar novo contrato ap�s o t�rmino do contrato atual"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkConfirmarRenovarContratoCredito"/>
            <rich:componentControl for="modalConfirmarRenovarContratoCreditoOpcao1"
                                   attachTo="hidelinkConfirmarRenovarContratoCredito" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>


    <a4j:form id="formRenovarContratoCreditoOpcao1" style="height: 100%;">
        <h:panelGrid width="100%" columns="1">
            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                             value="Iniciar novo contrato ap�s o t�rmino do contrato atual:"/>

            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="T�rmino do contrato atual: #{ContratoControle.contratoVO.contratoOrigemRenovacao.vigenciaAteAjustada_Apresentar}"/>
            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="In�cio do novo contrato: #{ContratoControle.contratoVO.vigenciaDe_Apresentar}"/>

            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="Observa��es: Nesta op��o, se os cr�ditos do contrato atual acabarem antes do in�cio do novo contrato,"/>

            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="o aluno n�o conseguir� acessar a academia at� que o novo contrato se inicie."/>
        </h:panelGrid>

        <h:panelGroup layout="block"  styleClass="container-botoes">
            <a4j:commandLink id="renovarOpcao1"
                             action="#{ContratoControle.renovarContratoCreditoAntecipadoComInicioAposTerminoContratoOriginal}"
                             reRender="modalRenovarContratoCredito, modalConfirmarRenovarContratoCreditoOpcao1, linkTipoRenovacaoCredito"
                             oncomplete="RichFaces.hideModalPanel('modalConfirmarRenovarContratoCreditoOpcao1');"
                             value="CONFIRMAR"
                             accesskey="2" styleClass="botaoPrimario texto-size-14-real"/>
        </h:panelGroup>
    </a4j:form>

</rich:modalPanel>

<rich:modalPanel id="modalConfirmarRenovarContratoCreditoOpcao2" styleClass="novaModal"  autosized="true" shadowOpacity="true" width="300" height="220">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Escolher data de in�cio do novo contrato"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkConfirmarRenovarContratoCredito2"/>
            <rich:componentControl for="modalConfirmarRenovarContratoCreditoOpcao2"
                                   attachTo="hidelinkConfirmarRenovarContratoCredito2" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>


    <a4j:form  id="formRenovarContratoCreditoOpcao2" style="height: 100%;">
        <h:panelGrid width="100%" columns="1">
            <h:outputLabel id="gravarRenovacoOpcao2"
                           styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                           value="Escolher data de in�cio do novo contrato:"/>
            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="Vig�ncia do contrato atual: #{ContratoControle.contratoVO.contratoOrigemRenovacao.vigenciaDe_Apresentar} at� #{ContratoControle.contratoVO.contratoOrigemRenovacao.vigenciaAteAjustada_Apresentar}"/>
            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="Data prevista da �ltima aula do contrato atual: #{ContratoControle.dataPrevistaUltimaAula_apresentar}"/>
            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="Observa��es:"/>
            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="1 - O saldo de cr�ditos e dias do contrato atual n�o ser�o transferidos para o novo contrato."/>

            <h:outputLabel  styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"
                            value="2 - O contrato atual ter� sua vig�ncia final alterada para um dia antes do in�cio do novo contrato."/>
        </h:panelGrid>

        <h:panelGroup>
            <h:panelGrid columns="2">
                <h:outputLabel styleClass="texto-font texto-font texto-size-16 texto-cor-cinza textoImcompleto tooltipster tooltipstered"  value="Data in�cio do novo contrato:"></h:outputLabel>
                <h:panelGroup styleClass="dateTimeCustom">
                    <rich:calendar id="datanovocontrato"
                                   value="#{ContratoControle.dataPrevistaUltimaAula}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <h:message for="datanovocontrato"  styleClass="mensagemDetalhada"/>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGroup>
            </h:panelGrid>

        </h:panelGroup>
        <h:panelGroup layout="block"  styleClass="container-botoes">
            <a4j:commandLink id="renovarOpcao2"
                             action="#{ContratoControle.renovarContratoCreditoAntecipadoComInicioInformadoPeloUsuario}"
                             reRender="#{ContratoControle.reRenderTelaNegociacao}"
                             oncomplete="#{ContratoControle.msgAlert}"
                             value="CONFIRMAR"
                             accesskey="2" styleClass="botaoPrimario texto-size-14-real"/>
        </h:panelGroup>


    </a4j:form>

</rich:modalPanel>

AmCharts.addInitHandler(function(a){var b={name:"export",version:"1.1.1",libs:{async:!0,autoLoad:!0,reload:!1,path:(a.path||"")+"export/libs/",resources:[{"pdfmake/pdfmake.js":["pdfmake/vfs_fonts.js"],"jszip/jszip.js":["xlsx/xlsx.js"]},"fabric.js/fabric.js","FileSaver.js/FileSaver.js"]},config:{},setup:{},drawing:{enabled:!1,actions:["undo","redo","done","cancel"],undos:[],undo:function(){var a=b.drawing.undos.pop();a&&(b.drawing.redos.push(a),a.path.remove())},redos:[],redo:function(){var a=b.drawing.redos.pop();a&&(b.setup.fabric.add(a.path),b.drawing.undos.push(a))},done:function(){b.drawing.enabled=!1,b.drawing.undos=[],b.drawing.redos=[],b.createMenu(b.config.menu),b.setup.wrapper.setAttribute("class",b.setup.chart.classNamePrefix+"-export-canvas")}},defaults:{position:"top-right",fileName:"export",action:"download",formats:{JPG:{mimeType:"image/jpg",extension:"jpg",capture:!0},PNG:{mimeType:"image/png",extension:"png",capture:!0},SVG:{mimeType:"text/xml",extension:"svg",capture:!0},PDF:{mimeType:"application/pdf",extension:"pdf",capture:!0},CSV:{mimeType:"text/plain",extension:"csv"},JSON:{mimeType:"text/plain",extension:"json"},XLSX:{mimeType:"application/octet-stream",extension:"xlsx"}},fabric:{backgroundColor:"#FFFFFF",isDrawingMode:!1,selection:!1,removeImages:!0},pdfMake:{pageSize:"A4",pageOrientation:"portrait",images:{},content:[{image:"reference",fit:[523.28,769.89]}]},divId:null,menuReviver:null,menuWalker:null,menu:[{"class":"export-main",label:"Exportar",menu:[{label:"Salvar como ...",menu:["PNG","JPG","SVG",{format:"PDF",content:["Saved from:",window.location.href,{image:"reference",fit:[523.28,769.89]}]}]},{label:"Exportar dados ...",menu:["CSV","XLSX","JSON"]},{label:"Anotar",action:"draw",menu:[{"class":"export-drawing",menu:[{label:"Cor ...",menu:[{"class":"export-drawing-color export-drawing-color-black",label:"Preto",click:function(){this.setup.fabric.freeDrawingBrush.color="#000"}},{"class":"export-drawing-color export-drawing-color-white",label:"Branco",click:function(){this.setup.fabric.freeDrawingBrush.color="#fff"}},{"class":"export-drawing-color export-drawing-color-red",label:"Vermelho",click:function(){this.setup.fabric.freeDrawingBrush.color="#f00"}},{"class":"export-drawing-color export-drawing-color-green",label:"Verde",click:function(){this.setup.fabric.freeDrawingBrush.color="#0f0"}},{"class":"export-drawing-color export-drawing-color-blue",label:"Azul",click:function(){this.setup.fabric.freeDrawingBrush.color="#00f"}}]},"UNDO","REDO",{label:"Exportar dados ...",menu:["PNG","JPG","SVG",{format:"PDF",content:["Saved from:",window.location.href,{image:"reference",fit:[523.28,769.89]}]}]},{format:"PRINT",label:"Imprimir"},"CANCEL"]}]},{format:"PRINT",label:"Imprimir"}]}],timer:0,fallback:{text:"CTRL + C to copy the data into the clipboard.",image:"Rightclick -> Save picture as... to save the image."}},download:function(a,c,d){if(window.saveAs)b.toBlob({data:a,type:c},function(a){saveAs(a,d)});else if(b.config.fallback&&"text/plain"==c){var f=document.createElement("div"),g=document.createElement("div"),h=document.createElement("textarea");g.innerHTML=b.config.fallback.text,f.appendChild(g),f.appendChild(h),g.setAttribute("class","amcharts-export-fallback-message"),f.setAttribute("class","amcharts-export-fallback"),b.setup.chart.containerDiv.appendChild(f),h.setAttribute("readonly",""),h.value=a,h.focus(),h.select(),b.createMenu([{"class":"export-main export-close",label:"Ok",click:function(){b.createMenu(b.config.menu),b.setup.chart.containerDiv.removeChild(f)}}])}else{if(!b.config.fallback||"image"!=c.split("/")[0])throw new Error("Unable to create file. Ensure saveAs (FileSaver.js) is supported.");var f=document.createElement("div"),g=document.createElement("div"),i=b.toImage({data:a});g.innerHTML=b.config.fallback.image,f.appendChild(g),f.appendChild(i),g.setAttribute("class","amcharts-export-fallback-message"),f.setAttribute("class","amcharts-export-fallback"),b.setup.chart.containerDiv.appendChild(f),b.createMenu([{"class":"export-main export-close",label:"Ok",click:function(){b.createMenu(b.config.menu),b.setup.chart.containerDiv.removeChild(f)}}])}return a},loadResource:function(a,c){function k(){if(c)for(d=0;d<c.length;d++)b.loadResource(c[d])}var d,e,f,g,h,i,j=-1!=a.indexOf("//")?a:[b.libs.path,a].join("");for(-1!=a.indexOf(".js")?(f=document.createElement("script"),f.setAttribute("type","text/javascript"),f.setAttribute("src",j),b.libs.async&&f.setAttribute("async","")):-1!=a.indexOf(".css")&&(f=document.createElement("link"),f.setAttribute("type","text/css"),f.setAttribute("rel","stylesheet"),f.setAttribute("href",j)),d=0;d<document.head.childNodes.length;d++)if(g=document.head.childNodes[d],h=g?g.src||g.href:!1,i=g?g.tagName:!1,g&&h&&-1!=h.indexOf(a)){b.libs.reload&&document.head.removeChild(g),e=!0;break}(!e||b.libs.reload)&&(f.addEventListener("load",k),document.head.appendChild(f))},loadDependencies:function(){var a,c;if(b.libs.autoLoad)for(a=0;a<b.libs.resources.length;a++)if(b.libs.resources[a]instanceof Object)for(c in b.libs.resources[a])b.loadResource(c,b.libs.resources[a][c]);else b.loadResource(b.libs.resources[a])},pxToNumber:function(a){return Number(String(a).replace("px",""))||0},deepMerge:function(a,c,d){var e,f,g=c instanceof Array?"array":"object";for(e in c)"array"==g&&isNaN(e)||(f=c[e],(void 0==a[e]||d)&&(f instanceof Array?a[e]=new Array:f instanceof Function?a[e]=new Function:f instanceof Date?a[e]=new Date:f instanceof Object?a[e]=new Object:f instanceof Number?a[e]=new Number:f instanceof String&&(a[e]=new String)),f instanceof Function||f instanceof Date||b.isElement(f)||!(f instanceof Object||f instanceof Array)?a instanceof Array&&!d?a.push(f):a[e]=f:b.deepMerge(a[e],f,d));return a},isElement:function(a){return a instanceof Object&&a&&1===a.nodeType},capture:function(c,d){var e,f,g,h=b.deepMerge(b.deepMerge({},b.config.fabric),c||{}),i=[],j={x:0,y:0,width:b.setup.chart.divRealWidth,height:b.setup.chart.divRealHeight},k=b.setup.chart.containerDiv.getElementsByTagName("svg");for(e=0;e<k.length;e++){var l={svg:k[e],parent:k[e].parentNode,offset:{x:0,y:0},patterns:{},clippings:{}},m=k[e].getElementsByTagName("clipPath");for(f=0;f<m.length;f++){for(g=0;g<m[f].childNodes.length;g++)m[f].childNodes[g].setAttribute("fill","transparent");l.clippings[m[f].id]=m[f]}var m=k[e].getElementsByTagName("pattern");for(f=0;f<m.length;f++){var n={node:m[f],source:m[f].getAttribute("xlink:href"),width:Number(m[f].getAttribute("width")),height:Number(m[f].getAttribute("height")),repeat:"repeat"},o=m[f].getElementsByTagName("rect");if(o.length>0){var p=new Image;p.src=n.source;var q=new fabric.StaticCanvas(void 0,{width:n.width,height:n.height,backgroundColor:o[0].getAttribute("fill")}),r=new fabric.Rect({width:n.width,height:n.height,fill:new fabric.Pattern({source:p,repeat:"repeat"})});q.add(r),n.source=q.toDataURL()}l.patterns[m[f].id]=new fabric.Pattern(n)}i.push(l)}if(b.config.legend&&b.setup.chart.legend&&"outside"==b.setup.chart.legend.position){var l={svg:b.setup.chart.legend.container.container,parent:b.setup.chart.legend.container.div,offset:{x:0,y:0},legend:{type:-1!=["top","left"].indexOf(b.config.legend.position)?"unshift":"push",position:b.config.legend.position,width:b.config.legend.width?b.config.legend.width:b.setup.chart.legend.container.width,height:b.config.legend.height?b.config.legend.height:b.setup.chart.legend.container.height}};-1!=["left","right"].indexOf(l.legend.position)?(j.width+=l.legend.width,j.height=l.legend.height>j.height?l.legend.height:j.height):-1!=["top","bottom"].indexOf(l.legend.position)&&(j.height+=l.legend.height),i[l.legend.type](l)}for("stock"==b.setup.chart.type&&(b.setup.chart.leftContainer&&(j.width-=b.pxToNumber(b.setup.chart.leftContainer.style.width),b.setup.wrapper.style.paddingLeft=b.pxToNumber(b.setup.chart.leftContainer.style.width)+2*b.setup.chart.panelsSettings.panelSpacing),b.setup.chart.rightContainer&&(j.width-=b.pxToNumber(b.setup.chart.rightContainer.style.width),b.setup.wrapper.style.paddingRight=b.pxToNumber(b.setup.chart.rightContainer.style.width)+2*b.setup.chart.panelsSettings.panelSpacing),b.setup.chart.periodSelector&&-1!=["top","bottom"].indexOf(b.setup.chart.periodSelector.position)&&(j.height-=b.setup.chart.periodSelector.offsetHeight+b.setup.chart.panelsSettings.panelSpacing),b.setup.chart.dataSetSelector&&-1!=["top","bottom"].indexOf(b.setup.chart.dataSetSelector.position)&&(j.height-=b.setup.chart.dataSetSelector.offsetHeight)),b.drawing.enabled=h.isDrawingMode=h.drawing&&h.drawing.enabled?!0:"draw"==h.action,b.setup.wrapper?b.setup.wrapper.innerHTML="":(b.setup.wrapper=document.createElement("div"),b.setup.wrapper.setAttribute("class",b.setup.chart.classNamePrefix+"-export-canvas"),b.setup.wrapper.appendChild(b.setup.canvas)),b.setup.canvas=document.createElement("canvas"),b.setup.wrapper.appendChild(b.setup.canvas),b.setup.fabric=new fabric.Canvas(b.setup.canvas,b.deepMerge({width:j.width,height:j.height},h)),b.deepMerge(b.setup.fabric,h),b.setup.fabric.on("path:created",function(a){b.drawing.undos.push(a)}),b.drawing.enabled?b.setup.wrapper.setAttribute("class",b.setup.chart.classNamePrefix+"-export-canvas active"):b.setup.wrapper.setAttribute("class",b.setup.chart.classNamePrefix+"-export-canvas"),e=0;e<i.length;e++){var l=i[e];l.parent.style.top||l.parent.style.left?(l.offset.y=b.pxToNumber(l.parent.style.top),l.offset.x=b.pxToNumber(l.parent.style.left)):(l.legend?"left"==l.legend.position?j.x+=a.legend.container.width:"right"==l.legend.position?l.offset.x+=j.width-l.legend.width:"top"==l.legend.position?j.y+=l.legend.height:"bottom"==l.legend.position&&(l.offset.y+=j.height-l.legend.height):(l.offset.x=j.x,l.offset.y=j.y,j.y+=b.pxToNumber(l.parent.style.height)),l.parent&&-1!=(l.parent.getAttribute("class")||"").split(" ").indexOf("amChartsLegend")&&(j.y+=b.pxToNumber(l.parent.parentNode.parentNode.style.marginTop),l.offset.y+=b.pxToNumber(l.parent.parentNode.parentNode.style.marginTop))),fabric.parseSVGDocument(l.svg,function(a){return function(c,e){var f,g=fabric.util.groupSVGElements(c,e),j={top:a.offset.y,left:a.offset.x};for(f=0;f<g.paths.length;f++)if(g.paths[f]){if(h.removeImages&&g.paths[f]["xlink:href"]&&-1!=g.paths[f]["xlink:href"].indexOf("//")&&-1==g.paths[f]["xlink:href"].indexOf(location.origin)){g.paths.splice(f,1);continue}if(g.paths[f].fill instanceof Object)"radial"==g.paths[f].fill.type&&(g.paths[f].fill.coords.r2=-1*g.paths[f].fill.coords.r1,g.paths[f].fill.coords.r1=0),g.paths[f].set({opacity:g.paths[f].fillOpacity});else if("url"==String(g.paths[f].fill).slice(0,3)){var k=g.paths[f].fill.slice(5,-1);a.patterns[k]&&g.paths[f].set({fill:a.patterns[k],opacity:g.paths[f].fillOpacity})}if("url"==String(g.paths[f].clipPath).slice(0,3)){var k=g.paths[f].clipPath.slice(5,-1);if(a.clippings[k]){var l=a.clippings[k].childNodes[0],m=g.paths[f].svg.getAttribute("transform")||"translate(0,0)";m=m.slice(10,-1).split(","),g.paths[f].set({clipTo:function(a,b){return function(c){var d=Number(a.getAttribute("width")||"0"),e=Number(a.getAttribute("height")||"0"),f=Number(a.getAttribute("x")||"0"),g=Number(a.getAttribute("y")||"0");c.rect(-1*Number(b[0])+f,-1*Number(b[1])+g,d,e)}}(l,m)})}}}if(g.set(j),b.setup.fabric.add(g),a.svg.parentNode&&a.svg.parentNode.getElementsByTagName){var n=a.svg.parentNode.getElementsByClassName(b.setup.chart.classNamePrefix+"-balloon-div");for(f=0;f<n.length;f++)if(h.balloonFunction instanceof Function)h.balloonFunction.apply(b,[n[f],a]);else{var o=n[f],p=o.childNodes[0],q=new fabric.Text(p.innerText||p.innerHTML,{fontSize:b.pxToNumber(p.style.fontSize),fontFamily:p.style.fontFamily,fill:p.style.color,top:b.pxToNumber(o.style.top)+a.offset.y,left:b.pxToNumber(o.style.left)+a.offset.x});b.setup.fabric.add(q)}}if(a.svg.nextSibling&&"A"==a.svg.nextSibling.tagName){var q=new fabric.Text(a.svg.nextSibling.innerText||a.svg.nextSibling.innerHTML,{fontSize:b.pxToNumber(a.svg.nextSibling.style.fontSize),fontFamily:a.svg.nextSibling.style.fontFamily,fill:a.svg.nextSibling.style.color,top:b.pxToNumber(a.svg.nextSibling.style.top)+a.offset.y,left:b.pxToNumber(a.svg.nextSibling.style.left)+a.offset.x});b.setup.fabric.add(q)}i.pop(),i.length||b.handleCallback(d)}}(l),function(a,c){var d,e=a.getAttribute("class")||a.parentNode.getAttribute("class")||"",f=a.getAttribute("visibility")||a.parentNode.getAttribute("visibility")||a.parentNode.parentNode.getAttribute("visibility")||"",g=a.getAttribute("clip-path")||a.parentNode.getAttribute("clip-path")||"";if(c.className=e,c.clipPath=g,c.svg=a,"hidden"==f)c.opacity=0;else{var h=["fill","stroke"];for(d=0;d<h.length;d++){var i=h[d],j=String(a.getAttribute(i)||""),k=Number(a.getAttribute(i+"-opacity")||"1"),l=fabric.Color.fromHex(j).getSource();c.className!=b.setup.chart.classNamePrefix+"-guide-fill"||j||(k=0,l=fabric.Color.fromHex("#000000").getSource()),l&&(l.pop(),l.push(k),c[i]="rgba("+l.join()+")",c[b.capitalize(i+"-opacity")]=k)}}})}},toCanvas:function(a,c){b.deepMerge({},a||{});var e=b.setup.canvas;return b.handleCallback(c,e),e},toImage:function(a,c){var d=b.deepMerge({format:"png",quality:1,multiplier:1},a||{}),e=d.data,f=document.createElement("img");return d.data||(e=d.lossless||"svg"==d.format?b.toSVG(b.deepMerge(d,{getBase64:!0})):b.setup.fabric.toDataURL(d)),f.setAttribute("src",e),b.handleCallback(c,f),f},toBlob:function(a,c){var d=b.deepMerge({data:"empty",type:"text/plain"},a||{}),e=/^data:.+;base64,(.*)$/.exec(d.data);return e&&(d.data=e[0],d.type=d.data.slice(5,d.data.indexOf(",")-7),d.data=b.toByteArray({data:d.data.slice(d.data.indexOf(",")+1,d.data.length)})),data=d.getByteArray?d.data:new Blob([d.data],{type:d.type}),b.handleCallback(c,data),data},toJPG:function(a,c){var d=b.deepMerge({format:"jpeg",quality:1,multiplier:1},a||{}),e=b.setup.fabric.toDataURL(d);return b.handleCallback(c,e),e},toPNG:function(a,c){var d=b.deepMerge({format:"png",quality:1,multiplier:1},a||{}),e=b.setup.fabric.toDataURL(d);return b.handleCallback(c,e),e},toSVG:function(a,c){var d=b.deepMerge({},a||{}),e=b.setup.fabric.toSVG(d);return d.getBase64&&(e="data:image/svg+xml;base64,"+btoa(e)),b.handleCallback(c,e),e},toPDF:function(a,c){var d=b.deepMerge(b.deepMerge({multiplier:2},b.config.pdfMake),a||{},!0);d.images.reference=b.toPNG(d);var e=new pdfMake.createPdf(d);return c&&e.getDataUrl(function(a){return function(){a.apply(b,arguments)}}(c)),e},toPRINT:function(a,c){var d,e=b.deepMerge({delay:1,lossless:!1},a||{}),f=b.toImage(e),g=[],h=document.body.childNodes;for(f.setAttribute("style","width: 100%; max-height: 100%;"),d=0;d<h.length;d++)b.isElement(h[d])&&(g[d]=h[d].style.display,h[d].style.display="none");return document.body.appendChild(f),window.print(),setTimeout(function(){for(d=0;d<h.length;d++)b.isElement(h[d])&&(h[d].style.display=g[d]);document.body.removeChild(f),b.handleCallback(c,f)},e.delay),f},toJSON:function(a,c){var d=b.deepMerge({data:b.getChartData()},a||{},!0),e=JSON.stringify(d.data,void 0,"	");return b.handleCallback(c,e),e},toCSV:function(a,c){function j(a,b){return"string"==typeof a?a=a:b&&f.dateFormat&&a instanceof Date&&-1!=f.dateFields.indexOf(b)&&(a=AmCharts.formatDate(a,f.dateFormat)),"string"==typeof a&&(f.escape&&(a=a.replace('"','""')),f.quotes&&(a=['"',a,'"'].join(""))),a}var d,e,f=b.deepMerge({data:b.getChartData(),delimiter:",",quotes:!0,escape:!0,dateFields:[],dateFormat:b.setup.chart.dataDateFormat||"YYYY-MM-DD"},a||{},!0),g="",h=[],i=[];b.setup.chart.categoryAxis&&b.setup.chart.categoryAxis.parseDates&&b.setup.chart.categoryField&&f.dateFields.push(b.setup.chart.categoryField);for(l in f.data[0])i.push(j(l)),h.push(l);g+=i.join(f.delimiter)+"\n";for(d in f.data)if(i=[],!isNaN(d)){for(e in h)if(!isNaN(e)){var k=h[e],l=f.data[d][k];i.push(j(l,k))}g+=i.join(f.delimiter)+"\n"}return b.handleCallback(c,g),g},toXLSX:function(a,c){function g(a,b){b&&(a+=1462);var c=Date.parse(a);return(c-new Date(Date.UTC(1899,11,30)))/864e5}function h(a){for(var c={},d={s:{c:1e7,r:1e7},e:{c:0,r:0}},e=0;e!=a.length;++e)for(var f=0;f!=a[e].length;++f){d.s.r>e&&(d.s.r=e),d.s.c>f&&(d.s.c=f),d.e.r<e&&(d.e.r=e),d.e.c<f&&(d.e.c=f);var h={v:a[e][f]};if(null!=h.v){var i=XLSX.utils.encode_cell({c:f,r:e});"number"==typeof h.v?h.t="n":"boolean"==typeof h.v?h.t="b":h.v instanceof Date?(h.t="n",h.z=XLSX.SSF._table[14],h.v=g(h.v)):h.t="s",c[i]=h}}return d.s.c<1e7&&(c["!ref"]=XLSX.utils.encode_range(d)),c}var d=b.deepMerge({data:b.getChartData(),name:"amCharts",withHeader:!0},a||{},!0),e="",f={SheetNames:[],Sheets:{}};return f.SheetNames.push(d.name),f.Sheets[d.name]=h(b.toArray(d)),e=XLSX.write(f,{bookType:"xlsx",bookSST:!0,type:"base64"}),e="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"+e,b.handleCallback(c,e),e},toArray:function(a,c){var d,e,f=b.deepMerge({data:b.getChartData(),dateFields:[],dateFormat:!1,withHeader:!1},a||{},!0),g=[],h=[];if(f.withHeader){for(e in f.data[0])h.push(e);g.push(h)}for(d in f.data){var i=[];if(!isNaN(d)){for(e in h){var e=h[e],j=f.data[d][e]||"";j=f.dateFormat&&j instanceof Date&&-1!=f.dateFields.indexOf(e)?AmCharts.formatDate(j,f.dateFormat):String(j),i.push(j)}g.push(i)}}return b.handleCallback(c,g),g},toByteArray:function(a,c){function l(a){var b=a.charCodeAt(0);return b===f?62:b===g?63:h>b?-1:h+10>b?b-h+26+26:j+26>b?b-j:i+26>b?b-i+26:void 0}function m(a){function k(a){h[j++]=a}var b,c,d,f,g,h;if(a.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var i=a.length;g="="===a.charAt(i-2)?2:"="===a.charAt(i-1)?1:0,h=new e(3*a.length/4-g),d=g>0?a.length-4:a.length;var j=0;for(b=0,c=0;d>b;b+=4,c+=3)f=l(a.charAt(b))<<18|l(a.charAt(b+1))<<12|l(a.charAt(b+2))<<6|l(a.charAt(b+3)),k((16711680&f)>>16),k((65280&f)>>8),k(255&f);return 2===g?(f=l(a.charAt(b))<<2|l(a.charAt(b+1))>>4,k(255&f)):1===g&&(f=l(a.charAt(b))<<10|l(a.charAt(b+1))<<4|l(a.charAt(b+2))>>2,k(255&f>>8),k(255&f)),h}var d=b.deepMerge({},a||{}),e="undefined"!=typeof Uint8Array?Uint8Array:Array,f="+".charCodeAt(0),g="/".charCodeAt(0),h="0".charCodeAt(0),i="a".charCodeAt(0),j="A".charCodeAt(0),k=m(d.data);return b.handleCallback(c,k),k},handleCallback:function(a,c){a&&a.apply(b,[c])},getChartData:function(){var a=[];if("stock"==b.setup.chart.type)a=b.setup.chart.mainDataSet.dataProvider;else if("gantt"==b.setup.chart.type){for(var c=b.setup.chart.segmentsField,d=0;d<b.setup.chart.dataProvider.length;d++)if(b.setup.chart.dataProvider[d][c])for(var e=0;e<b.setup.chart.dataProvider[d][c].length;e++)a.push(b.setup.chart.dataProvider[d][c][e])}else a=b.setup.chart.dataProvider;return a},capitalize:function(a){return a.charAt(0).toUpperCase()+a.slice(1).toLowerCase()},createMenu:function(c,d){function f(c,d){var e,g=document.createElement("ul");for(e=0;e<c.length;e++){var h="string"==typeof c[e]?{format:c[e]}:c[e],i=document.createElement("li"),j=document.createElement("a"),k=document.createElement("img"),l=document.createElement("span"),m=String(h.action?h.action:h.format).toLowerCase();h.format=String(h.format).toUpperCase(),b.config.formats[h.format]?h=b.deepMerge({label:h.icon?"":h.format,format:h.format,mimeType:b.config.formats[h.format].mimeType,extension:b.config.formats[h.format].extension,capture:b.config.formats[h.format].capture,action:b.config.action,fileName:b.config.fileName},h):h.menu||h.items||(h.label=h.label?h.label:b.capitalize(m)),(-1==["CSV","JSON","XLSX"].indexOf(h.format)||-1==["map","gauge"].indexOf(b.setup.chart.type))&&(AmCharts.isIE&&AmCharts.IEversion<10&&"UNDEFINED"!=h.format&&h.mimeType&&"image"!=h.mimeType.split("/")[0]&&"text/plain"!=h.mimeType||(h.click||h.menu||h.items?"draw"==h.action&&(h.click=function(a){return function(){this.capture(a,function(){this.createMenu(a.menu)})}}(h)):-1!=b.drawing.actions.indexOf(m)?(h.action=m,h.click=function(a){return function(){this.drawing[a.action]()}}(h)):b.drawing.enabled?h.click=function(a){return function(){this["to"+a.format](a,function(b){this.drawing.done(),"print"!=a.action&&"PRINT"!=a.format&&this.download(b,a.mimeType,[a.fileName,a.extension].join("."))})}}(h):"UNDEFINED"!=h.format&&(h.click=function(a){return function(){if(a.capture||"print"==a.action||"PRINT"==a.format)this.capture(a,function(){this["to"+a.format](a,function(b){"download"==a.action&&this.download(b,a.mimeType,[a.fileName,a.extension].join("."))})});else{if(!this["to"+a.format])throw new Error("Invalid format. Could not determine output type.");this["to"+a.format](a,function(b){this.download(b,a.mimeType,[a.fileName,a.extension].join("."))})}}}(h)),j.setAttribute("href","#"),j.addEventListener("click",function(a){return function(c){a.apply(b,arguments),c.preventDefault()}}(h.click||function(a){a.preventDefault()})),i.appendChild(j),l.innerHTML=h.label,h["class"]&&(i.className=h["class"]),h.icon&&(k.setAttribute("src",(-1==h.icon.slice(0,10).indexOf("//")?a.pathToImages:"")+h.icon),j.appendChild(k)),h.label&&j.appendChild(l),h.title&&j.setAttribute("title",h.title),b.config.menuReviver&&(i=b.config.menuReviver.apply(b,[h,i])),(h.menu||h.items)&&"draw"!=h.action?f(h.menu||h.items,i).childNodes.length&&g.appendChild(i):g.appendChild(i)))}return d.appendChild(g)}return d||("string"==typeof b.config.divId?b.config.divId=d=document.getElementById(b.config.divId):d=b.isElement(b.config.divId)?b.config.divId:b.setup.chart.containerDiv),b.isElement(b.setup.menu)?b.setup.menu.innerHTML="":b.setup.menu=document.createElement("div"),b.setup.menu.setAttribute("class",b.setup.chart.classNamePrefix+"-export-menu "+b.setup.chart.classNamePrefix+"-export-menu-"+b.config.position+" amExportButton"),b.config.menuWalker&&(f=b.config.menuWalker),f.apply(this,[c,b.setup.menu]),d.appendChild(b.setup.menu),b.setup.menu},migrateSetup:function(a){function d(a){var b;for(b in a){var e=a[b];"export"==b.slice(0,6)&&e?c.menu.push(b.slice(6)):"userCFG"==b?d(e):"menuItems"==b?c.menu=e:"libs"==b?c.libs=e:"string"==typeof b&&(c[b]=e)}}if(a.amExport||a.exportConfig){var c=b.deepMerge({enabled:!0,migrated:!0,libs:{autoLoad:!1}},b.deepMerge(b.defaults,{menu:[]},!0));d(a.amExport||a.exportConfig),a["export"]=c}return a},init:function(){clearTimeout(b.timer),b.timer=setInterval(function(){b.setup.chart.containerDiv&&(clearTimeout(b.timer),b.setup.canvas=document.createElement("canvas"),b.setup.wrapper=document.createElement("div"),b.setup.wrapper.setAttribute("class",b.setup.chart.classNamePrefix+"-export-canvas"),b.setup.wrapper.appendChild(b.setup.canvas),b.setup.chart.containerDiv.appendChild(b.setup.wrapper),b.setup.chart.AmExport=b,b.createMenu(b.config.menu))},AmCharts.updateRate)}};b.drawing.cancel=b.drawing.done,b.setup.chart=b.migrateSetup(a),void 0!==b.setup.chart["export"]&&b.setup.chart["export"].enabled&&(b.deepMerge(b.libs,b.setup.chart["export"].libs||{},!0),b.deepMerge(b.defaults.pdfMake,b.setup.chart["export"]),b.deepMerge(b.defaults.fabric,b.setup.chart["export"]),b.config=b.deepMerge(b.defaults,b.setup.chart["export"],!0),(!(AmCharts.isIE&&AmCharts.IEversion<=9)||document.head&&b.config.fallback!==!1)&&(b.setup.chart["export"]=b,b.setup.chart.addClassNames=!0,b.loadDependencies(b.libs.resources,b.libs.reload),b.init()))},["pie","serial","xy","funnel","radar","gauge","stock","map","gantt"]);
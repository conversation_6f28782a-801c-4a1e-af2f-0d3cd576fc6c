<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

<rich:modalPanel id="panelClienteMensagem" domElementAttachment="parent" styleClass="novaModal"
                 showWhenRendered="#{ClienteControle.apresentarPanelLancaMensagem && LoginControle.permissaoAcessoMenuVO.cliente}"
                 autosized="true" shadowOpacity="false" width="300" height="300">
    <f:facet name="header">
        <h:outputText value="#{msg_aplic.prt_ClienteMensagem_tituloForm}"/>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink11"/>
            <rich:componentControl for="panelClienteMensagem"
                                   attachTo="hidelink11" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formPanelMensagem">
        <%@include file="includes/include_identificadorModuloEstudio.jsp" %>
        <rich:tabPanel switchType="client">

            <!-- Inicio - Aba Catraca -->
            <rich:tab id="abaCatraca" label="Catraca"
                      rendered="#{ClienteControle.clienteVO.abaCatraca}">
                <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                             columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ClienteMensagem_bloqueio}"/>
                    </f:facet>
                </h:panelGrid>
                <h:panelGrid columns="2"
                             columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_ClienteMensagem_dataBloqueio}"/>
                    <h:panelGroup>
                        <rich:calendar id="dataBloqueio"
                                       value="#{ClienteControle.mensagemCatraca.dataBloqueio}"
                                       inputSize="10" inputClass="form" oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2"
                                       showWeeksBar="false"/>
                        <h:message for="dataBloqueio" styleClass="mensagemDetalhada"/>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                     query="mask('99/99/9999')"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_ClienteMensagem_bloquearAcesso}"/>
                    <h:selectBooleanCheckbox id="bloquearMensagem"
                                             styleClass="campos"
                                             value="#{ClienteControle.mensagemCatraca.bloqueio}">
                    </h:selectBooleanCheckbox>
                </h:panelGrid>
                <%--   <h:outputText value="*Só funciona de imeditado no acesso online. Para acesso offline, o bloqueio só terá início no dia seguinte." style="font-size: 10px;"/>  --%>
                <h:panelGrid columns="1" width="100%"
                             columnClasses="colunaCentralizada">
                    <h:panelGrid columns="1" width="100%" headerClass="subordinado"
                                 columnClasses="colunaCentralizada">
                        <f:facet name="header">
                            <h:outputText
                                    value="#{msg_aplic.prt_ClienteMensagem_tituloForm}"/>
                        </f:facet>
                    </h:panelGrid>

                    <h:inputTextarea id="msgCatraca" value="#{ClienteControle.mensagemCatraca.mensagem}"
                                     onkeypress="somaMsgCatraca(this.value);" onkeyup="somaMsgCatraca(this.value);"
                                     rows="5" cols="100"/>

                    <h:panelGroup style="text-align: center;" layout="block">
                        <h:inputText disabled="true" size="3" title="Caracteres restantes"
                                     style="text-align: center; color: #000000" id="tamanhoMensagemRestanteCatraca"/>
                    </h:panelGroup>

                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada" styleClass="tabMensagens">
                        <h:panelGrid id="painelMensagemCatraca" columns="3" width="100%"
                                     styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <f:verbatim>
                                    <h:outputText value=" "/>
                                </f:verbatim>
                            </h:panelGrid>
                            <a4j:commandButton rendered="#{ClienteControle.sucesso}"
                                               image="./imagens/sucesso.png"/>
                            <a4j:commandButton rendered="#{ClienteControle.erro}"
                                               image="./imagens/erro.png"/>
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem"
                                              value="#{ClienteControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada"
                                              value="#{ClienteControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGroup>

                            <a4j:commandLink id="incluirCatraca"
                                             action="#{ClienteControle.confirmaInclusaoClienteCatraca}"
                                             oncomplete="#{ClienteControle.msgAlert}"
                                             reRender="panelAutorizacaoFuncionalidade"
                                             title="#{msg.msg_gravar_dados}"
                                             styleClass="pure-button pure-button-small pure-button-primary">
                                <i class="fa-icon-save"></i> &nbsp <h:outputText value="#{msg_bt.btn_gravar}"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="excluirCatraca"
                                             action="#{ClienteControle.confirmaExclusaoClienteCatraca}"
                                             oncomplete="#{ClienteControle.msgAlert}"
                                             reRender="panelAutorizacaoFuncionalidade"
                                             title="#{msg.msg_excluir_dados}"
                                             accesskey="3" style="margin: 0 10px 10px 5px;"
                                             styleClass="pure-button pure-button-small">
                                <i class="fa-icon-remove"></i> &nbsp <h:outputText value="#{msg_bt.btn_excluir}"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="fecharClienteMensagemCatraca"
                                             oncomplete="Richfaces.hideModalPanel('panelClienteMensagem')"
                                             action="#{ClienteControle.fecharRichModalClienteMensagem}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4"
                                             styleClass="pure-button pure-button-small">
                                <h:outputText value="#{msg_bt.btn_fechar}"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:tab>
            <!-- Fim - Aba Catraca -->

            <!-- Inicio - Aba Consultor -->
            <rich:tab label="Consultor"
                      rendered="#{ClienteControle.clienteVO.abaConsultor}">
                <h:panelGrid columns="1" width="100%"
                             columnClasses="colunaCentralizada">

                    <rich:editor configuration="editorpropriedades" viewMode="visual"
                                 theme="advanced" id="imputMensagemConsultor" height="250"
                                 width="700"
                                 value="#{ClienteControle.mensagemConsultor.mensagem}"/>

                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada" styleClass="tabMensagens">
                        <h:panelGrid id="panelMensagem" columns="3" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <f:verbatim>
                                    <h:outputText value=" "/>
                                </f:verbatim>
                            </h:panelGrid>
                            <a4j:commandButton rendered="#{ClienteControle.sucesso}"
                                               image="./imagens/sucesso.png"/>
                            <a4j:commandButton rendered="#{ClienteControle.erro}"
                                               image="./imagens/erro.png"/>
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem"
                                              value="#{ClienteControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada"
                                              value="#{ClienteControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGroup>

                            <a4j:commandLink id="salvarConsultor"
                                             action="#{ClienteControle.permisaoClienteMensagemConsultor}"
                                             reRender="imputMensagemConsultor, panelMensagem, panelAutorizacaoFuncionalidade, mensagemConsultor, panelClienteMensagem"
                                             styleClass="pure-button pure-button-small pure-button-primary"
                                             title="#{msg.msg_gravar_dados}" accesskey="2">
                                <i class="fa-icon-save"></i> &nbsp <h:outputText value="#{msg_bt.btn_gravar}"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="excluirConsultor" styleClass="pure-button pure-button-small"
                                             reRender="imputMensagemConsultor, panelMensagem, panelAutorizacaoFuncionalidade"
                                             action="#{ClienteControle.permisaoExcluirClienteMensagemConsultor}"
                                             oncomplete="#{ClienteControle.validarPermisaoExcluirClienteMensagemConsultor}"
                                             title="#{msg.msg_excluir_dados}" accesskey="3"
                                             style="margin: 0 10px 10;">
                                <i class="fa-icon-remove"></i> &nbsp <h:outputText value="#{msg_bt.btn_excluir}"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="fecharClienteMensagemConsultor"
                                             styleClass="pure-button pure-button-small"
                                             onclick="Richfaces.hideModalPanel('panelClienteMensagem')"
                                             action="#{ClienteControle.fecharRichModalClienteMensagem}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4">
                                <h:outputText value="#{msg_bt.btn_fechar}"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:tab>
            <!-- Fim - Aba Consultor -->

            <!-- Inicio - Aba Objetivo -->
            <rich:tab label="Objetivo"
                      rendered="#{ClienteControle.clienteVO.abaObjetivo}">
                <h:panelGrid columns="1" width="100%"
                             columnClasses="colunaCentralizada">
                    <rich:editor configuration="editorpropriedades" viewMode="visual"
                                 theme="advanced" id="imputMensagemObjetivo" height="250"
                                 width="700" value="#{ClienteControle.mensagemObjetivo.mensagem}"/>


                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada" styleClass="tabMensagens">
                        <h:panelGrid id="panelMensagemObjetivo" columns="3" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <f:verbatim>
                                    <h:outputText value=" "/>
                                </f:verbatim>
                            </h:panelGrid>
                            <a4j:commandButton rendered="#{ClienteControle.sucesso}"
                                               image="./imagens/sucesso.png"/>
                            <a4j:commandButton rendered="#{ClienteControle.erro}"
                                               image="./imagens/erro.png"/>
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem"
                                              value="#{ClienteControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada"
                                              value="#{ClienteControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGroup>
                            <f:verbatim>
                                <h:outputText value="    "/>
                            </f:verbatim>


                            <a4j:commandLink id="salvarObjetivo"
                                             action="#{ClienteControle.permisaoClienteMensagemObjetivo}"
                                             reRender="imputMensagemObjetivo,panelMensagemObjetivo,panelAutorizacaoFuncionalidade"
                                             title="#{msg.msg_gravar_dados}" accesskey="2"
                                             styleClass="pure-button pure-button-small pure-button-primary">
                                <i class="fa-icon-save"></i> &nbsp <h:outputText value="#{msg_bt.btn_gravar}"/>
                            </a4j:commandLink>


                            <a4j:commandLink id="excluirObjetivo"
                                             action="#{ClienteControle.permisaoExcluirClienteMensagemObjetivo}"
                                             reRender="imputMensagemObjetivo,panelMensagemObjetivo,panelAutorizacaoFuncionalidade"
                                             title="#{msg.msg_excluir_dados}" accesskey="3"
                                             style="margin: 0 10px 10px 5px;"
                                             styleClass="pure-button pure-button-small">
                                <i class="fa-icon-remove"></i> &nbsp <h:outputText value="#{msg_bt.btn_excluir}"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="fecharClienteMensagemObjetivo"
                                             onclick="Richfaces.hideModalPanel('panelClienteMensagem')"
                                             action="#{ClienteControle.fecharRichModalClienteMensagem}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4"
                                             styleClass="pure-button pure-button-small">
                                <h:outputText value="#{msg_bt.btn_fechar}"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:tab>
            <!-- Fim - Aba Objetivo -->

            <!-- Inicio - Aba Medico -->
            <rich:tab label="Médico"
                      rendered="#{ClienteControle.clienteVO.abaMedico}">
                <h:panelGrid columns="1" width="100%"
                             columnClasses="colunaCentralizada">

                    <rich:editor configuration="editorpropriedades" viewMode="visual"
                                 theme="advanced" id="imputMensagemMedico" height="250"
                                 width="700" value="#{ClienteControle.mensagemMedico.mensagem}"/>


                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada" styleClass="tabMensagens">
                        <h:panelGrid id="panelMensagemMedico" columns="3" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <f:verbatim>
                                    <h:outputText value=" "/>
                                </f:verbatim>
                            </h:panelGrid>
                            <a4j:commandButton rendered="#{ClienteControle.sucesso}"
                                               image="./imagens/sucesso.png"/>
                            <a4j:commandButton rendered="#{ClienteControle.erro}"
                                               image="./imagens/erro.png"/>
                            <h:panelGrid columns="1" width="100%" id="painelMensagem">
                                <h:outputText styleClass="mensagem"
                                              value="#{ClienteControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada"
                                              value="#{ClienteControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                        <h:panelGroup>


                            <a4j:commandLink id="salvarMedico"
                                             action="#{ClienteControle.permisaoClienteMensagemMedico}"
                                             reRender="imputMensagemMedico,panelMensagemMedico,panelAutorizacaoFuncionalidade"
                                             title="#{msg.msg_gravar_dados}" accesskey="2"
                                             styleClass="pure-button pure-button-small pure-button-primary">
                                <i class="fa-icon-save"></i> &nbsp <h:outputText value="#{msg_bt.btn_gravar}"/>
                            </a4j:commandLink>


                            <a4j:commandLink id="excluirMedico"
                                             action="#{ClienteControle.permisaoExcluirClienteMensagemMedico}"
                                             reRender="imputMensagemMedico,panelMensagemMedico,panelAutorizacaoFuncionalidade"
                                             title="#{msg.msg_excluir_dados}" accesskey="3"
                                             style="margin: 0 10px 10px 5px;"
                                             styleClass="pure-button pure-button-small">
                                <i class="fa-icon-remove"></i> &nbsp <h:outputText value="#{msg_bt.btn_excluir}"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="fecharClienteMensagemMedico"
                                             onclick="Richfaces.hideModalPanel('panelClienteMensagem')"
                                             action="#{ClienteControle.fecharRichModalClienteMensagem}"
                                             title="#{msg.msg_consultar_dados}" accesskey="4"
                                             styleClass="pure-button pure-button-small">
                                <h:outputText value="#{msg_bt.btn_fechar}"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </rich:tab>
            <!-- Fim - Aba Medico -->

        </rich:tabPanel>
    </a4j:form>

</rich:modalPanel>

<%-- Modal de Contrato --%>
<rich:modalPanel id="panelNovoContrato" domElementAttachment="parent"
                 styleClass="novaModal"
                 autosized="true" shadowOpacity="false" width="850" height="150">

    <f:facet name="header">
        <h:outputText value="#{msg_aplic.prt_Aviso_tituloForm}" styleClass="titulo2"/>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkpanelNovoContrato"/>
            <rich:componentControl for="panelNovoContrato" attachTo="hidelinkpanelNovoContrato" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formNovoContrato" ajaxSubmit="false">
        <%@include file="includes/include_identificadorModuloEstudio.jsp" %>
        <h:outputText value="Escolha seu plano: " style="font-weight:bold;"/>
        <h:selectOneMenu id="plano" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                         value="#{ClienteControle.contratoVO.plano.codigo}">
            <a4j:support event="onchange" reRender="formNovoContrato"
                         action="#{ClienteControle.buscarContratosConcomitantesAoPlano}"/>
            <f:selectItems value="#{ClienteControle.listaPlanoVOs}"/>
        </h:selectOneMenu>
        <h:panelGrid
                rendered="#{!empty ClienteControle.listaContratosMesmoPlanoInformado && ClienteControle.contratoVO.plano.codigo!=0}">
            <br>
            <h:outputText
                    value="Renove ou rematricule um dos contratos deste plano:" style="font-weight:bold;"
                    styleClass="textsmall"/>
            <br>
        </h:panelGrid>
        <h:dataTable style="margin: 15px 0px; width: 100%;"
                     id="listaContrato" width="100%"
                     styleClass="tabelaDados semZebra"
                     rendered="#{!empty ClienteControle.listaContratosMesmoPlanoInformado}"
                     value="#{ClienteControle.listaContratosMesmoPlanoInformado}"
                     var="contratoTbl">
            <h:column>
                <f:facet name="header">
                    <h:outputText value="Contrato"/>
                </f:facet>
                <h:outputText value="#{contratoTbl.codigo}"/>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="Plano"/>
                </f:facet>
                <h:outputText value="#{contratoTbl.plano.descricao}">
                </h:outputText>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="Data Início"/>
                </f:facet>
                <h:outputText value="#{contratoTbl.vigenciaDe}">
                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                </h:outputText>
            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText value="Data Término"/>
                </f:facet>

                <h:outputText value="#{contratoTbl.vigenciaAteAjustada}">
                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                </h:outputText>

            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText style="font-weight: bold" value="Situação"/>
                </f:facet>
                <h:graphicImage value="./imagens/botaoAtivo.png"
                                rendered="#{contratoTbl.contratoAtivo}" width="25"
                                height="24"/>
                <h:graphicImage value="./imagens/botaoCancelamento.png"
                                rendered="#{contratoTbl.contratoCancelado}" width="25"
                                height="24"/>
                <h:graphicImage value="./imagens/botaoTrancamento.png"
                                rendered="#{contratoTbl.contratoTrancado}" width="25"
                                height="24"/>
                <h:graphicImage value="./imagens/botaoInativo.png"
                                rendered="#{contratoTbl.contratoInativo}" width="25"
                                height="24"/>
                <h:graphicImage value="./imagens/botaoRenovado.png"
                                rendered="#{contratoTbl.apresentarBotaoRenovarContrato}"
                                width="25" height="24"/>

            </h:column>
            <h:column>
                <f:facet name="header">
                    <h:outputText style="font-weight: bold" value="Opção"/>
                </f:facet>
                <a4j:commandLink id="renovarContrato"
                                 rendered="#{contratoTbl.renovarContrato}"
                                 value="Renovar "
                                 styleClass="linkAzul texto-size-18 icon"
                                 oncomplete="#{ClienteControle.mensagemNotificar}"
                                 action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}"/>
                <rich:toolTip style="width:150px;" followMouse="true" for="renovarContrato"
                              value="Renovar este contrato"/>
                <a4j:commandLink id="rematricularContrato" styleClass="linkAzul texto-size-18 icon"
                                 rendered="#{contratoTbl.rematricularContrato}"
                                 title="Rematricular Contrato" value="Rematricular "
                                 oncomplete="#{ClienteControle.mensagemNotificar}"
                                 action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}"/>
                <rich:toolTip style="width:150px;" followMouse="true" for="rematricularContrato"
                              value="Rematricular este contrato"/>
            </h:column>
        </h:dataTable>
        <bR>
        <a4j:commandLink id="linkNovoContrato2" reRender="panelIncludeNovoContrato"
                         rendered="#{!empty ClienteControle.listaContratosMesmoPlanoInformado}"
                         action="#{ContratoControle.novoContratoConcomitanteViaJsf}"
                         styleClass="linkAzul texto-size-18 icon"
                         value="Obs.:Se deseja lançar contrato para uma modalidade não existente nos contratos a renovar ou rematricular clique aqui"
                         oncomplete="#{ContratoControle.mensagemNotificar}"/>
        <h:panelGrid
                rendered="#{empty ClienteControle.listaContratosMesmoPlanoInformado && ClienteControle.contratoVO.plano.codigo!=0}">
            <br>
            <a4j:commandLink styleClass="linkAzul texto-size-18 icon" id="linkNovoContrato"
                             reRender="panelIncludeNovoContrato"
                             action="#{ContratoControle.novoContratoConcomitanteViaJsf}"
                             value="Para lançar um novo contrato, clique aqui."
                             oncomplete="#{ContratoControle.mensagemNotificar}"/>
            <rich:toolTip style="width:150px;" followMouse="true" for="linkNovoContrato"
                          value="Fazer uma nova Matricula"/>

        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<%-- Modal de Lista de Contratos a Renovar ou Rematricular --%>
<rich:modalPanel id="panelListaContratosRenovarRematricular" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="930" height="200"
                 styleClass="novaModal">

    <f:facet name="header">
        <h:outputText value="#{msg_aplic.prt_Aviso_tituloForm}"/>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkpanelListaContratosRenovarRematriculars"/>
            <rich:componentControl for="panelListaContratosRenovarRematricular"
                                   attachTo="hidelinkpanelListaContratosRenovarRematriculars" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>


    </f:facet>

    <a4j:form id="formListaContratos" ajaxSubmit="false" style="padding: 25px">
        <%@include file="includes/include_identificadorModuloEstudio.jsp" %>
        <h:outputText rendered="#{!empty ClienteControle.listaContratosRenovarRematricular}"
                      styleClass="font14"
                      value="O(s) contrato(s) abaixo precisam ser renovados ou rematriculados.
                      Você pode fazer isso clicando na \"Opção\" respectiva." style="font-weight:bold;"/>
        <div style="overflow-y: auto; margin-top: 25px">
            <h:dataTable style="margin: 15px 0px; width: 100%;"
                         id="listaContrato" width="100%"
                         styleClass="tabelaDados semZebra formListaContratos-listaContrato"
                         rendered="#{!empty ClienteControle.listaContratosRenovarRematricular}"
                         value="#{ClienteControle.listaContratosRenovarRematricular}"
                         var="contratoTbl">
                <h:column>
                    <f:facet name="header">
                        <h:outputText styleClass="font14" value="Contrato"/>
                    </f:facet>
                    <h:outputText id="codigo" styleClass="font14" value="#{contratoTbl.codigo}"/>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText styleClass="font14" value="Plano"/>
                    </f:facet>
                    <h:outputText id="descricao" styleClass="font14" value="#{contratoTbl.plano.descricao}">
                    </h:outputText>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText styleClass="font14" value="Data Início"/>
                    </f:facet>
                    <h:outputText id="dataInicio" styleClass="font14" value="#{contratoTbl.vigenciaDe}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText styleClass="font14" value="Data Término"/>
                    </f:facet>

                    <h:outputText id="dataTermino" styleClass="font14" value="#{contratoTbl.vigenciaAteAjustada}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>

                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText styleClass="font14" value="Situação"/>
                    </f:facet>
                    <h:graphicImage value="./imagens/botaoAtivo.png"
                                    rendered="#{contratoTbl.contratoAtivo}" width="25"
                                    height="24"/>
                    <h:graphicImage value="./imagens/botaoCancelamento.png"
                                    rendered="#{contratoTbl.contratoCancelado}" width="25"
                                    height="24"/>
                    <h:graphicImage value="./imagens/botaoTrancamento.png"
                                    rendered="#{contratoTbl.contratoTrancado}" width="25"
                                    height="24"/>
                    <h:graphicImage value="./imagens/botaoInativo.png"
                                    rendered="#{contratoTbl.contratoInativo}" width="25"
                                    height="24"/>
                    <h:graphicImage value="./imagens/botaoRenovado.png"
                                    rendered="#{contratoTbl.apresentarBotaoRenovarContrato}"
                                    width="25" height="24"/>

                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText styleClass="font14" value="AÇÃO"/>
                    </f:facet>
                    <a4j:commandLink  id="renovarContrato"
                                      rendered="#{contratoTbl.renovarContrato}"
                                      value="Renovar"
                                      style="font-size: 14px !important"
                                      styleClass="linkAzul texto-size-18 icon"
                                      oncomplete="#{ClienteControle.mensagemNotificar}"
                                      action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}" />
                    <rich:toolTip style="width:150px;" followMouse="true" for="renovarContrato" value="Renovar este contrato"/>
                    <a4j:commandLink id="rematricularContrato"
                                     rendered="#{contratoTbl.rematricularContrato}"
                                     value="Rematricular"
                                     styleClass="linkAzul texto-size-18 icon"
                                     style="font-size: 14px !important"
                                     oncomplete="#{ClienteControle.mensagemNotificar}"
                                     action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}" />
                    <rich:toolTip style="width:150px;" followMouse="true" for="rematricularContrato" value="Rematricular este contrato"/>
                </h:column>
            </h:dataTable>
        </div>

        <h:outputText rendered="#{empty ClienteControle.listaContratosRenovarRematricular}" styleClass="font14"
                      value="Não há contrato que precise renovar ou rematricular" style="font-weight:bold;"/>
        <br><br>
        <div style="overflow-y: auto; background-color: #EFEFEF; border-radius: 10px; padding: 25px; margin-top: 85px">
            <h:outputText style="font-size: 14px; font-weight: bold; width: 60%; margin: 5px 5px;"
                          styleClass="verticallyCentered"
                          value="Porém, se mesmo assim você deseja realmente lançar um novo contrato, clique ao lado."/>
            <a4j:commandLink styleClass="botoes nvoBt font14 icon floatright" id="linkNovoContrato1"
                             action="#{ContratoControle.novoContratoConcomitanteViaJsf}"
                             value="Novo contrato"
                             oncomplete="#{ContratoControle.mensagemNotificar}#{ClienteControle.mostrarRichModalPanelNovoContrato}"/>
            <h:outputText value="    "/>
        </div>
    </a4j:form>
</rich:modalPanel>


<script type="text/javascript">

    function somaMsgCatraca() {
        var limiteChar = 214;
        var elemento = (document.getElementById('formPanelMensagem:msgCatraca'));
        var tamanhoRestante = document.getElementById('formPanelMensagem:tamanhoMensagemRestanteCatraca');

        var mais_um = eval(elemento.value.length - 1);
        mais_um++;

        if (elemento.value.length > limiteChar) {
            elemento.value = '';
            elemento.value = valor_limite;
        } else {
            valor_limite = elemento.value;
            tamanhoRestante.value = '';
            tamanhoRestante.value = (limiteChar - mais_um);
        }
        elemento.focus();
    }
</script>

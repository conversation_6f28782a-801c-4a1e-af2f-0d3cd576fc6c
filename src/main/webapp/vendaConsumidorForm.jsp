<%-- 
    Document   : vendaConsumidorForm
    Created on : 21/01/2011, 12:04:51
    Author     : carla
--%>
<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_vendaConsumidor_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <%-- INICIO HEADER --%>
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_vendaConsumidor_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-ver-as-vendas-realizadas-para-consumidor/"/>
        <f:facet name="header">

            <jsp:include page="topoReduzido_material.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{VendaConsumidorControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid  columnClasses="colunaAlinhamento">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_vendaConsumidor_nome}: #{VendaConsumidorControle.clienteVO.pessoa.nome}" />
                </h:panelGrid>

                <rich:tabPanel width="100%" activeTabClass="true" headerAlignment="rigth" >
                    <%-- Histórico de Compras--%>
                    <rich:tab label="Histórico de Compras"
                              id="abaHistoricoCompras">
                        <f:attribute name="nomeComprador"
                                     value="#{VendaConsumidorControle.clienteVO.pessoa.nome}" />
                        <div style="clear: both;">

                            <table width="98%" border="0" align="left" cellpadding="0"
                                   cellspacing="0"
                                   style="margin-right: 30px; margin-bottom: 30px; padding: 10px;">
                                <tr>
                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                        <div style="clear: both;" class="text">
                                            <p style="margin-bottom: 6px;"><img
                                                    src="images/arrow2.gif" width="16" height="16"
                                                    style="vertical-align: middle; margin-right: 6px;">
                                                <h:outputText style="font-weight: bold;"
                                                              value="#{msg_aplic.prt_HistóricoCompras}" /></p>
                                        </div>
                                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                                src="images/shim.gif"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top"><rich:dataTable
                                            id="listaHistoricoCompras" width="100%" border="0"
                                            rows="#{VendaConsumidorControle.nrPaginaMovProduto}"
                                            cellspacing="0" cellpadding="0" styleClass="textsmall"
                                            columnClasses="centralizado, centralizado, centralizado"
                                            value="#{VendaConsumidorControle.clienteVO.listaHistoricoProduto}"
                                            var="historicoCompras">
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_codigoProduto}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoCompras.codigo}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_descricao}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoCompras.descricao}" />
                                            </rich:column>

                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoCompras.dataLancamentoComHora_Apresentar}" />
                                            </rich:column>

                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_quantidade}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoCompras.quantidade}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_unitario}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoCompras.precoUnitario}">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column>

                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_desconto}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoCompras.valorDesconto}">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_totalFinal}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoCompras.totalFinal}">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:outputText>
                                            </rich:column>

                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_situacao}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold"
                                                              styleClass="#{historicoCompras.mudarCorSituacaoEmAberto}"
                                                              value="#{historicoCompras.situacao_Apresentar}" />
                                            </rich:column>
                                            <rich:column
                                                rendered="#{VendaConsumidorControle.apresentarOpcaoEstornoProduto}" style="height: 35px;">
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold" value="Opções" />
                                                </f:facet>
                                                <a4j:commandLink id="estornoMovProduto"
                                                                   rendered="#{historicoCompras.aprensetarBotaoEstorno}"
                                                                   styleClass="botoes nvoBt btSec"
                                                                   action="#{EstornoMovProdutoControle.novoParaConsumidor}"
                                                                   value="Estornar"
                                                                   oncomplete="abrirPopup('estornoMovProdutoForm.jsp', 'Produto', 950, 600);" />
                                            </rich:column>
                                        </rich:dataTable> <h:panelGrid columns="1" columnClasses="colunaCentralizada,colunaCentralizada"
                                                                       width="100%">
                                            <h:panelGroup>
                                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                                    <rich:datascroller align="center"
                                                                       for="listaHistoricoCompras" maxPages="100"
                                                                       id="scResultadoHistoricoProdutos" />
                                                    <rich:inputNumberSpinner inputSize="5" styleClass="form"
                                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                                             value="#{VendaConsumidorControle.nrPaginaMovProduto}">
                                                        <a4j:support event="onchange"
                                                                     focus="scResultadoHistoricoProdutos"
                                                                     reRender="listaHistoricoCompras,scResultadoHistoricoProdutos" />
                                                    </rich:inputNumberSpinner>
                                                </h:panelGrid>
                                            </h:panelGroup>
                                        </h:panelGrid></td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                        <div style="clear: both;" class="text">
                                            <p style="margin-bottom: 6px;"><img
                                                    src="images/arrow2.gif" width="16" height="16"
                                                    style="vertical-align: middle; margin-right: 6px;"> <h:outputText style="font-weight: bold;"
                                                              value="#{msg_aplic.prt_HistoricoParcelasGeradas}" /></p>
                                        </div>
                                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                                src="images/shim.gif"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top"><rich:dataTable
                                            id="listaHistoricoParcela" width="100%" border="0"
                                            rows="#{VendaConsumidorControle.nrPaginaMovParcela}"
                                            cellspacing="0" cellpadding="0" styleClass="textsmall"
                                            columnClasses="centralizado, centralizado, centralizado"
                                            value="#{VendaConsumidorControle.clienteVO.listaParcelas}"
                                            var="historicoParcela">

                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_codigoProd}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoParcela.codigo}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold" value="Código" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoParcela.codigo}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_descricao}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoParcela.descricao}" />
                                            </rich:column>

                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_DataLancada}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoParcela.dataRegistro_Apresentar}">

                                                </h:outputText>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_DataVencimento}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoParcela.dataVencimento_Apresentar}">

                                                </h:outputText>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_valor}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoParcela.valorParcela}">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoParcelaCliente_situacao}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold"
                                                              styleClass="#{historicoParcela.mudarCorSituacaoEmAberto}"
                                                              value="#{historicoParcela.situacao_Apresentar}" />
                                            </rich:column>
                                        </rich:dataTable> <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                                                                       width="100%">
                                            <h:panelGroup>
                                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                                    <rich:datascroller for="listaHistoricoParcela"
                                                                       maxPages="100" id="scResultadoHistoricoParcela" />
                                                    <rich:inputNumberSpinner inputSize="5" styleClass="form"
                                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                                             value="#{VendaConsumidorControle.nrPaginaMovParcela}">
                                                        <a4j:support event="onchange"
                                                                     focus="scResultadoHistoricoParcela"
                                                                     reRender="listaHistoricoParcela,scResultadoHistoricoParcela" />
                                                    </rich:inputNumberSpinner>
                                                </h:panelGrid>

                                            </h:panelGroup>
                                        </h:panelGrid></td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top" style="padding-bottom: 5px;">
                                        <div style="clear: both;" class="text">
                                            <p style="margin-bottom: 6px;"><img
                                                    src="images/arrow2.gif" width="16" height="16"
                                                    style="vertical-align: middle; margin-right: 6px;"><h:outputText style="font-weight: bold;"
                                                              value="#{msg_aplic.prt_HistoricoPagamentosEfetuados}" /></p>
                                        </div>
                                        <div class="sep" style="margin: 4px 0 10px 0;"><img
                                                src="images/shim.gif"></div>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="left" valign="top"><rich:dataTable
                                            id="listaHistoricoPagamentos" width="100%" border="0"
                                            rows="#{VendaConsumidorControle.nrPaginaMovPagamento}"
                                            cellspacing="0" cellpadding="0" styleClass="textsmall"
                                            columnClasses="centralizado, centralizado, centralizado"
                                            value="#{VendaConsumidorControle.clienteVO.listaHistoricoPagamento}"
                                            var="historicoPagamentos">
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_nrRecibo}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.reciboPagamento.codigo}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_nomePagador}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.nomePagador}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_dataLancamento}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.dataLancamento_Apresentar}">
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_formaPagamento}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.formaPagamento.tipoFormaPagamento_Apresentar}" />
                                            </rich:column>
                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_valor}" />
                                                </f:facet>
                                                <h:outputText style="font-weight: bold" styleClass="blue"
                                                              value="#{historicoPagamentos.valor}">
                                                    <f:converter converterId="FormatadorNumerico" />
                                                </h:outputText>
                                            </rich:column>

                                            <rich:column>
                                                <f:facet name="header">
                                                    <h:outputText style="font-weight: bold"
                                                                  value="#{msg_aplic.prt_HistoricoComprasCliente_recibo}" />
                                                </f:facet>
                                                    <a4j:commandLink id="imprimir"
                                                                     title="#{msg_bt.btn_ImprimirReciboPagamento}"
                                                                     actionListener="#{ReciboControle.prepareRecibo}"
                                                                     action="#{ReciboControle.imprimirReciboPDF}"
                                                                     oncomplete="abrirPopupPDFImpressao('relatorio/#{ReciboControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);">
                                                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}" />
                                                        <i class="fa-icon-print" style="font-size: 20px"></i>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink id="gerarNFCeConsumidor"
                                                                     title="NFCe"
                                                                     reRender="mdlMensagemGenerica"
                                                                     rendered="#{ClienteControle.clienteVO.empresa.usarNFCe && LoginControle.permissaoAcessoMenuVO.gestaoNFCe}"
                                                                     actionListener="#{ReciboControle.prepareRecibo}"
                                                                     action="#{ReciboControle.confirmarEmitirNFCe}"
                                                                     oncomplete="#{ReciboControle.modalMensagemGenerica}">
                                                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                        <h:graphicImage value="images/icon-NFCe.svg" style="width: 14px;"/>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink id="gerarNFSeConsumidor"
                                                                     title="NFSe"
                                                                     reRender="mdlMensagemGenerica"
                                                                     rendered="#{ClienteControle.clienteVO.empresa.usarNFSe && LoginControle.permissaoAcessoMenuVO.gestaoNotas}"
                                                                     actionListener="#{ReciboControle.prepareRecibo}"
                                                                     action="#{ReciboControle.confirmarEmitirNFSe}"
                                                                     oncomplete="#{ReciboControle.modalMensagemGenerica}">
                                                        <f:attribute name="reciboPagamentoVO" value="#{historicoPagamentos.reciboPagamento}"/>
                                                        <h:graphicImage value="images/icon-NFSe.svg" style="width: 14px;"/>
                                                    </a4j:commandLink>
                                            </rich:column>
                                        </rich:dataTable>
                                        <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                                                                       width="100%">
                                            <h:panelGroup>
                                                <h:panelGrid columns="2" columnClasses="colunaCentralizada,colunaCentralizada">
                                                    <rich:datascroller align="center"
                                                                       for="listaHistoricoPagamentos" maxPages="10"
                                                                       id="scResultadoHistoricoPgto" />
                                                    <rich:inputNumberSpinner inputSize="5" styleClass="form"
                                                                             enableManualInput="true" minValue="1" maxValue="100"
                                                                             value="#{VendaConsumidorControle.nrPaginaMovPagamento}">
                                                        <a4j:support event="onchange"
                                                                     focus="scResultadoHistoricoPgto"
                                                                     reRender="listaHistoricoPagamentos,scResultadoHistoricoPgto" />
                                                    </rich:inputNumberSpinner>
                                                </h:panelGrid>

                                            </h:panelGroup>
                                        </h:panelGrid> <h:panelGroup>

                                        </h:panelGroup></td>
                                </tr>
                            </table>
                        </div>
                    </rich:tab>



                </rich:tabPanel>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{VendaConsumidorControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{VendaConsumidorControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{VendaConsumidorControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{VendaConsumidorControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="consultar" immediate="true" action="#{VendaConsumidorControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}"  alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

<script>
    document.getElementById("form:dataRegistro").focus();
</script>

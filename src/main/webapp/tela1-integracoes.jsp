<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <jsp:include page="include_head.jsp" flush="true"/>
    <link href="css/pure1.0.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <style type="text/css">
        .titulo3:hover {
            text-decoration: none;
        }


        .titulo4 {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 11px;
            text-decoration: none;
            line-height: normal;
            font-weight: normal;
            text-transform: none;
            color: #297d92;
        }
    </style>
    <head>
        <link href="bootstrap/bootplus.min.css" rel="stylesheet">
        <style>
            /* TOPO MENU */

            .rich-ddmenu-label, .rich-ddmenu-label-disabled {
                padding: 0px !important;
            }

            /* CUSTOMIZE THE CAROUSEL
            -------------------------------------------------- */

            /* Carousel base class */
            .carousel {
                margin-bottom: 0px;
                height: 100%;
            }

            .carousel .container {
                position: relative;
                z-index: 9;
                height: 100%;
            }

            .carousel-control {
                height: 80px;
                margin-top: 0;
                font-size: 36px;
                text-shadow: 0 1px 1px rgba(0, 0, 0, .4);
                background-color: transparent;
                border: 0;
                z-index: 10;
            }

            .carousel .item {
                height: 100%
            }

            .carousel img {
                margin-left: auto;
                margin-right: auto;
            }

            .carousel-caption {
                background-color: transparent;
                position: static;
                max-width: 550px;
                padding: 0 20px;
                margin-top: 200px;
            }

            .carousel-caption h1,
            .carousel-caption .lead {
                margin: 0;
                line-height: 1.25;
                color: #fff;
                text-shadow: 0 1px 1px rgba(0, 0, 0, .4);
            }

            .carousel-caption .btn {
                margin-top: 10px;
            }

        </style>
        <!-- Le javascript -->
        <!-- Placed at the end of the document so the pages load faster -->
        <script src="bootstrap/bootstrap-transition.js"></script>
        <script src="bootstrap/bootstrap-carousel.js"></script>
    </head>


    <c:if test="${!LoginControle.apresentarLinkZW && LoginControle.apresentarLinkEstudio}">
        <%response.sendRedirect("faces/pages/estudio/indexEstudio.jsp");%>
    </c:if>
    <jsp:include page="include_modal_expiracaoSenha.jsp"/>

    <h:form id="form" style="margin-bottom: 0">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo-integracoes.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>
            <script>
                setDocumentCookie('popupsImportante', 'close', 1);
            </script>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central"
                                      style="position:relative;">
                            <c:if test="${SuperControle.bannerEmergencia}">
                                <a href="#" class="bannerPadrao" target="_blank">
                                    <img border="none" class="img-responsive imagemApresentacao"
                                         src="${LoginControle.urlBannerEmergencia}"/>
                                </a>
                            </c:if>

                            <c:if test="${not SuperControle.bannerEmergencia && SuperControle.bannerRetro}">
                                <a href="https://game.pactosolucoes.com.br/core/retrospectiva/2023/myyear.html?chave=${SuperControle.key}&empresa=${SuperControle.empresaLogado.codigo}"
                                   class="bannerPadrao" target="_blank">
                                    <img border="none" class="img-responsive imagemApresentacao"
                                         src="images/RETROSPECTIVA-2023.png"/>
                                </a>
                            </c:if>
                            <c:if test="${not SuperControle.bannerEmergencia && not SuperControle.bannerRetro}">
                                <a href="#" class="bannerPadrao">
                                    <img border="none" class="img-responsive imagem-blur-bottom imagemApresentacao"
                                         src="images/banner-respiracao.png"/>
                                </a>
                                <div id="myCarousel" class="carousel slide container-conteudo-central">
                                    <div class="carousel-inner">
                                        <c:forEach items="${SuporteControle.banners}" var="ban" varStatus="ind">
                                            <div class="item ${ind.count == 1 ? 'active' : ''}">

                                                <c:if test="${not empty ban.funcionalidade}">
                                                    <a onclick="abrirFuncionalidade('${ban.funcionalidade}')"
                                                       style="cursor: pointer">
                                                        <img border="none" style="max-width:100%"
                                                             src="${ban.urlImagem}">
                                                    </a>
                                                </c:if>

                                                <c:if test="${empty ban.funcionalidade}">
                                                    <c:if test="${not empty ban.urlLink}">
                                                        <a onclick="clickNotificar()"
                                                           href="${ban.urlLink}"
                                                           target="_blank">
                                                            <img border="none" style="max-width:100%"
                                                                 src="${ban.urlImagem}">
                                                        </a>
                                                    </c:if>
                                                    <c:if test="${empty ban.urlLink}">
                                                        <img border="none" style="max-width:100%"
                                                             src="${ban.urlImagem}">
                                                    </c:if>
                                                </c:if>
                                            </div>
                                        </c:forEach>
                                    </div>

                                    <ol class="carousel-indicators">
                                        <c:forEach items="${SuporteControle.banners}" var="ban"
                                                   varStatus="ind">
                                            <li style="cursor: pointer;" data-target="#myCarousel"
                                                data-slide-to="${ind.count -1}"
                                                class="${ind.count == 1 ? 'active' : ''}"></li>
                                        </c:forEach>
                                    </ol>
                                </div>
                                <script>
                                    validarTamanhoBanner(true);
                                </script>

                                <a4j:commandLink
                                        status="false"
                                        id="notificarClickBanner"
                                        style="display: none"
                                        action="#{SuporteControle.notificarClick}">
                                </a4j:commandLink>

                                <a4j:commandLink id="funcionalidadeAbrirClick"
                                                 style="display: none"
                                                 action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                                                 actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                                                 oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                                    <f:attribute name="funcionalidade" value="#{SuporteControle.funcionalidadeAbrir}"/>
                                </a4j:commandLink>

                                <h:inputHidden id="funcionalidadeAbrir"
                                               value="#{SuporteControle.funcionalidadeAbrir}"/>
                            </c:if>

                            <h:panelGroup layout="block" styleClass="container-cards"
                                          rendered="#{not empty BlogControle.itemsCampanhaBlog}">
                                <h:outputText styleClass="headerBlog" value="Novidades do Blog"/>
                                <a4j:repeat value="#{BlogControle.itemsCampanhaBlog}" var="itemCampanha">

                                    <h:panelGroup layout="block" styleClass="card-blog">
                                        <h:outputLink onclick="window.open('#{itemCampanha.url}','_blank')">
                                            <h:graphicImage value="#{itemCampanha.urlImagem}"/>
                                            <h:panelGroup layout="block" styleClass="conteudoArtigo">
                                                <h:outputText styleClass="titulo-artigo"
                                                              value="#{itemCampanha.titulo}"/>
                                                <h:outputText styleClass="texto-artigo" value="#{itemCampanha.texto}"/>
                                            </h:panelGroup>
                                        </h:outputLink>
                                    </h:panelGroup>

                                </a4j:repeat>
                            </h:panelGroup>
                            <a4j:commandButton style="display: none;" reRender="panelExpiracaoSenha"
                                               id="btnAtualizaPagina">
                            </a4j:commandButton>
                        </h:panelGroup>

                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-INICIO"/>
                        </jsp:include>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp"/>
        </h:panelGroup>
        <a4j:jsFunction name="updateFoto" action="#{SuperControle.recarregarFotoUsuario}"
                        oncomplete="location.reload();"/>
    </h:form>
    <%@include file="includes/include_modal_incluirFoto_usuario.jsp" %>

    <jsp:include page="include_load_configs.jsp"/>

    <jsp:include page="includes/include_modal_dicas.jsp"/>
    <jsp:include page="includes/include_modal_emailAdministrador.jsp"/>
    <jsp:include page="includes/include_usuariosInativar.jsp"/>
    <jsp:include page="includes/include_planos_inativos_proximo_mes.jsp"/>
    <jsp:include page="includes/cliente/include_atualizar_dados_usuarios.jsp"/>
    <jsp:include page="includes/cliente/include_atualizar_dados_financeiro.jsp"/>
    <jsp:include page="/pages/finan/includes/include_modal_abrirCaixa.jsp" flush="true"/>
    <jsp:include page="/pages/finan/includes/include_box_fecharCaixas.jsp" flush="true"/>


    <script>
        function clickNotificar() {
            document.getElementById('form:notificarClickBanner').click();
        }

        function abrirFuncionalidade(funcionalidade) {
            clickNotificar();
            var funcionalidadeAbrir = document.getElementById('form:funcionalidadeAbrir');
            funcionalidadeAbrir.value = funcionalidade;
            document.getElementById('form:funcionalidadeAbrirClick').click();
        }
    </script>

</f:view>


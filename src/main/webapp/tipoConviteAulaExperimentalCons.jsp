<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <title>Convite Aula Experimental</title>
    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="Convite Aula Experimental"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}controle-de-convidados-completo/"/>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>



    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                        <a4j:commandLink id="btnExcel"
                                         styleClass="exportadores margin-h-10 linkPadrao"
                                         actionListener="#{TipoConviteAulaExperimentalControle.exportar}"
                                         oncomplete="#{TipoConviteAulaExperimentalControle.mensagemNotificar};#{TipoConviteAulaExperimentalControle.msgAlert}"
                                         accesskey="3">
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="codigo=Código,descricao=Descrição,vigenciaInicial_Apresentar=Início,vigenciaFinal_Apresentar=Fim,empresa_Apresentar=Empresa"/>
                            <f:attribute name="prefixo" value="Convite"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="btnPDF"
                                         styleClass="exportadores margin-h-10 linkPadrao"
                                         actionListener="#{TipoConviteAulaExperimentalControle.exportar}"
                                         oncomplete="#{TipoConviteAulaExperimentalControle.mensagemNotificar};#{TipoConviteAulaExperimentalControle.msgAlert}"
                                         accesskey="4">
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos"
                                         value="codigo=Código,descricao=Descrição,vigenciaInicial_Apresentar=Início,vigenciaFinal_Apresentar=Fim,empresa_Apresentar=Empresa"/>
                            <f:attribute name="prefixo" value="Convite"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                        </a4j:commandLink>


                        <a4j:commandLink id="btnNovo"
                                         styleClass="pure-button pure-button-primary pure-button-small"
                                         action="#{TipoConviteAulaExperimentalControle.novo}"
                                         accesskey="1">
                            <i class="fa-icon-plus"></i> &nbsp ${msg_bt.btn_novo}
                        </a4j:commandLink>
                     </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblConvite" class="tabelaConvite pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.prt_Plano_label_codigo}</th>
                    <th>${msg_aplic.prt_Plano_label_descricao}</th>
                    <th>${msg_aplic.prt_Plano_label_vigenciaDe}</th>
                    <th>${msg_aplic.prt_Plano_label_vigenciaAte}</th>
                    <th>${msg_aplic.prt_Plano_label_empresa}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{TipoConviteAulaExperimentalControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{TipoConviteAulaExperimentalControle.sucesso}" value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{TipoConviteAulaExperimentalControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty TipoConviteAulaExperimentalControle.mensagem}"
                              value=" #{TipoConviteAulaExperimentalControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty TipoConviteAulaExperimentalControle.mensagemDetalhada}"
                              value=" #{TipoConviteAulaExperimentalControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

        <rich:modalPanel id="panelStatus" autosized="true">
            <h:panelGrid columns="2" styleClass="titulo3" columnClasses="titulo3">
                <h:graphicImage url="./imagens/carregando.gif" style="border:none"/>
                <h:outputText styleClass="titulo3" value="Carregando..."/>
            </h:panelGrid>
        </rich:modalPanel>
        <a4j:status onstart="Richfaces.showModalPanel('panelStatus');"
                    onstop="Richfaces.hideModalPanel('panelStatus');">
        </a4j:status>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaConvite", "${contexto}/prest/basico/conviteaulaexperimental", 1, "asc", "", true);
        });
    </script>

</f:view>

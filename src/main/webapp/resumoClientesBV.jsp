<%-- 
    Document   : resumoClientesBV
    Created on : 20/06/2012, 10:13:44
    Author     : carla
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo de Cliente(s)"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid columns="1" style="height=25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="tituloFormulario" value="Resumo de Cliente(s) Total:#{fn:length(RelatorioBVsControle.resumoClientesBVs)} "/>
                    </h:panelGrid>
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block">
                        <a4j:commandButton id="exportarExcel"
                                           image="/imagens/btn_excel.png"
                                           style="margin-left: 8px;"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty RelatorioBVsControle.resumoClientesBVs}"
                                           value="Excel"
                                           oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                           accesskey="2" styleClass="botoes">
                            <f:attribute name="lista" value="#{RelatorioBVsControle.resumoClientesBVs}"/>
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="itemExportacao" value="#{RelatorioBVsControle.itemEportar}"/>
                            <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,data_Apresentar=Data,questionario_Apresentar=Questionário"/>
                            <f:attribute name="prefixo" value="ResumoClientes"/>
                        </a4j:commandButton>
                        <%--BOTÃO PDF--%>
                        <a4j:commandButton id="exportarPdf"
                                           style="margin-left: 8px;"
                                           image="/imagens/imprimir.png"
                                           actionListener="#{ExportadorListaControle.exportar}"
                                           rendered="#{not empty RelatorioBVsControle.resumoClientesBVs}"
                                           value="PDF"
                                           oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                           accesskey="2" styleClass="botoes">
                            <f:attribute name="lista" value="#{RelatorioBVsControle.resumoClientesBVs}"/>
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="itemExportacao" value="relBV"/>
                            <f:attribute name="atributos" value="matricula_Apresentar=Matrícula,nome_Apresentar=Nome,data_Apresentar=Data,questionario_Apresentar=Questionário"/>
                            <f:attribute name="prefixo" value="ResumoClientes"/>
                        </a4j:commandButton>
                    </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" id="tabelaRes"
                                    value="#{RelatorioBVsControle.resumoClientesBVs}" rows="50" var="resumoCliente" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{resumoCliente.questionarioClienteVO.cliente.matricula}">
                            <f:facet name="header">
                                <h:outputText value="Matrícula" />
                            </f:facet>
                            <h:outputText value="#{resumoCliente.questionarioClienteVO.cliente.matricula}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoCliente.questionarioClienteVO.cliente.pessoa.nome}">
                            <f:facet name="header">
                                <h:outputText value="Nome" />
                            </f:facet>
                            <h:outputText value="#{resumoCliente.questionarioClienteVO.cliente.pessoa.nome}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoCliente.questionarioClienteVO.data}">
                            <f:facet name="header">
                                <h:outputText value="Data" />
                            </f:facet>
                            <h:outputText value="#{resumoCliente.questionarioClienteVO.data}" >
                                <f:convertDateTime pattern="dd/MM/yyyy" />
                            </h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{resumoCliente.questionarioClienteVO.questionario.nomeInterno}">
                            <f:facet name="header">
                                <h:outputText value="Pesquisa" />
                            </f:facet>
                            <h:outputText value="#{resumoCliente.questionarioClienteVO.questionario.nomeInterno}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoCliente.situacaoContrato}"
                                     rendered="#{!RelatorioBVsControle.relatorioPesquisa}">
                            <f:facet name="header">
                                <h:outputText value="Situação" />
                            </f:facet>
                            <h:outputText value="#{resumoCliente.situacaoContrato}" />
                        </rich:column>
                        <rich:column >
                            <f:facet name="header">
                                <h:outputText value="Opção"/>
                            </f:facet>
                            <a4j:commandButton image="../imagens/botaoVisualizar.png" action="#{RelatorioBVsControle.irParaTelaCliente}" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <f:param name="state" value="AC"/>
                            </a4j:commandButton>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>


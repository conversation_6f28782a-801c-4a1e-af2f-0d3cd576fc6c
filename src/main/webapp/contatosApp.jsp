<%@ page contentType="text/html;charset=UTF-8" pageEncoding="ISO-8859-1" language="java" %>
<%@ include file="includes/imports.jsp" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="css_pacto.css" rel="stylesheet" type="text/css">

<style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
    .alinhar{
        vertical-align: top !important;
        padding-left: 20px;
        padding-right: 20px;
    }

</style>




<f:view>

    <title>
        <h:outputText value="#{msg_aplic.prt_contatos_app_tituloForm}"/>
    </title>

    <f:facet name="header">
        <jsp:include page="topoReduzidoCRM.jsp"/>
    </f:facet>

    <head>
        <link rel="shortcut icon" href="./favicon.ico">
        <title><h:outputText value="#{msg_aplic.prt_contatos_app_tituloForm}" /></title>
        <link href="./beta/css/pure-forms.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-tables.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-buttons.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
        <link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
    </head>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form" style="border: 0; margin: 0">
        <a4j:keepAlive beanName="RelatorioContatoAppControle"/>

        <body>
            <h:panelGrid id="panelGridForm" columns="1" width="100%">
                <h:panelGrid columns="1" style="background: url('./imagens/fundoBarraTopo.png') repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:panelGroup>
                        <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_contatos_app_tituloForm}"/>
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-enviar-notificacao-para-o-app-de-um-unico-aluno/"
                                      title="Clique e saiba mais: ContatosApp" target="_blank">
                            <i class="fa-icon-question-sign linkWiki" style="font-size: 18px; margin-left: 0.2em"></i>
                        </h:outputLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </body>


        <h:panelGrid columns="2" 
                     columnClasses="alinhar, alinhar" cellpadding="20">

            <h:panelGrid columns="4" columnClasses="colunaEsquerda" border="0" id="filtrosMailing">
                <h:outputText styleClass="tituloCampos" value="Período:" />
                <h:panelGroup>
                    <rich:calendar value="#{RelatorioContatoAppControle.inicio}" id="dataInicialAgendamentoApp"
                                   inputSize="10"
                                   inputClass="form chamaBotao"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <h:outputText styleClass="tituloCampos" value="até"
                                  style="margin-left: 8px; margin-right: 8px;"/>
                    <rich:calendar value="#{RelatorioContatoAppControle.fim}" id="dataFinalAgendamentoApp"
                                   inputSize="10"
                                   inputClass="form chamaBotao"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"/>
                    <a4j:commandButton id="limparPeriodoCriacao" action="#{RelatorioContatoAppControle.limparPeriodo}"
                                       image="imagensCRM/limpar.gif" alt="#{msg_aplic.prt_limparCampo}"
                                       reRender="filtrosMailing" 
                                       style="margin-left: 8px;margin-right: 20px;"/>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="Mala direta:"/>

                <h:inputText  id="malaDireta" size="20" 
                              maxlength="255" onblur="blurinput(this);"
                              onfocus="focusinput(this);" styleClass="form chamaBotao" 
                              value="#{RelatorioContatoAppControle.malaDireta}" />

                <h:outputText styleClass="tituloCampos" value="Mensagem:"/>
                <h:inputText  id="filtroMensagem" size="40" 
                              maxlength="255" styleClass="form chamaBotao" 
                              value="#{RelatorioContatoAppControle.filtroMensagem}" />

                <h:outputText styleClass="tituloCampos" value="Nome do cliente:"/>
                <h:inputText  id="filtroNomePessoa" size="40" 
                              maxlength="255" styleClass="form chamaBotao" 
                              value="#{RelatorioContatoAppControle.filtroNomePessoa}" />
                <h:outputText styleClass="tituloCampos" value="Resposta:"/>
                <h:inputText  id="filtroResposta" size="40" 
                              maxlength="255" styleClass="form chamaBotao" 
                              value="#{RelatorioContatoAppControle.filtroResposta}" />


                <h:outputText styleClass="tituloCampos" value="Resp. contato:" />
                <h:panelGroup>
                    <h:inputText  id="nomeOperador" size="40" 
                                  maxlength="255" styleClass="form" 
                                  value="#{RelatorioContatoAppControle.responsavel.nome}" />

                    <rich:suggestionbox height="200" width="200"
                                        for="nomeOperador"
                                        fetchValue="#{result.nome}"
                                        suggestionAction="#{RelatorioContatoAppControle.executarAutocompleteConsultaUsuario}"
                                        minChars="1" rowClasses="20"
                                        status="statusHora"
                                        nothingLabel="Nenhum usuário encontrado !"
                                        var="result"  id="suggestionUsuario">
                        <a4j:support event="onselect"
                                     action="#{RelatorioContatoAppControle.selecionarUsuarioSuggestionBox}"/>
                        <h:column>
                            <h:outputText value="#{result.nome}" />
                        </h:column>
                    </rich:suggestionbox>
                    <rich:spacer width="5px"/>
                </h:panelGroup>


                <h:outputText rendered="#{RelatorioContatoAppControle.usuarioLogado.administrador}" styleClass="tituloCampos" style="margin-top: 6px" value="Empresa:"/>
                <h:selectOneMenu rendered="#{RelatorioContatoAppControle.usuarioLogado.administrador}" 
                                 styleClass="form" id="empresa" value="#{RelatorioContatoAppControle.codigoEmpresa}">
                    <f:selectItem itemLabel="" itemValue="0"/>
                    <f:selectItems value="#{RelatorioContatoAppControle.listaSelectItemEmpresa}"/>
                </h:selectOneMenu>

            </h:panelGrid>


        </h:panelGrid>
        <center>
            <a4j:commandLink id="btnConsultarCliente"
                             styleClass="pure-button pure-button-primary"
                             action="#{RelatorioContatoAppControle.consultarContatos}"
                             oncomplete="#{RelatorioContatoAppControle.mensagemNotificar}"
                             accesskey="1" 
                             reRender="form">
                <i class="fa-icon-search" ></i> &nbsp Consultar
            </a4j:commandLink>
        </center>
        <br/>

        <h:panelGrid columns="1" width="100%" id="resultado" columnClasses="alinhar">
            <h:panelGroup layout="block" rendered="#{not empty RelatorioContatoAppControle.lista}">
                <a4j:commandLink id="btnPDF"
                                 styleClass="pure-button pure-button-small"
                                 actionListener="#{ExportadorListaControle.exportar}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="3">
                    <f:attribute name="lista" value="#{RelatorioContatoAppControle.lista}"/>
                    <f:attribute name="filtro" value="#{RelatorioContatoAppControle.filtros}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos" value="diaApresenatar=Dia,matricula_Apresentar=Matrícula,nomeApresentar=Nome,codigoMalaDireta=Mala_Direta,mensagemMalaDireta=Mensagem,resposta=Resposta"/>
                    <f:attribute name="prefixo" value="ContatosAPP"/>
                    <i class="fa-icon-excel" ></i> &nbsp PDF
                </a4j:commandLink>

                <a4j:commandLink id="btnExcel"
                                 styleClass="pure-button pure-button-small margin-h-10"
                                 actionListener="#{ExportadorListaControle.exportar}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="3">
                    <f:attribute name="lista" value="#{RelatorioContatoAppControle.lista}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos" value="diaApresenatar=Dia,matricula_Apresentar=Matrícula,nomeApresentar=Nome,codigoMalaDireta=Mala_Direta,mensagemMalaDireta=Mensagem,resposta=Resposta"/>
                    <f:attribute name="prefixo" value="ContatosAPP"/>
                    <i class="fa-icon-excel" ></i> &nbsp Excel
                </a4j:commandLink>

            </h:panelGroup>
            <rich:dataTable value="#{RelatorioContatoAppControle.lista}"
                            rendered="#{fn:length(RelatorioContatoAppControle.lista) >= 1}"
                            var="contato" id="tabelaResultados"
                            styleClass="pure-table pure-table-horizontal pure-table-striped pure-table-links"
                            width="100%" rows="15">
                <rich:column sortBy="#{contato.diaApresentar}">
                    <f:facet name="header">
                        <h:outputText value="Dia" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <a4j:commandLink action="#{RelatorioContatoAppControle.irParaTelaCliente}"
                                     oncomplete="#{RelatorioContatoAppControle.msgAlert}">
                        <h:outputText value="#{contato.diaApresentar}" styleClass="text"/>    
                    </a4j:commandLink>
                </rich:column>
                <rich:column sortBy="#{contato.matricula_Apresentar}">
                    <f:facet name="header">
                        <h:outputText value="Matrícula" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <a4j:commandLink action="#{RelatorioContatoAppControle.irParaTelaCliente}"
                                     oncomplete="#{RelatorioContatoAppControle.msgAlert}">
                        <h:outputText value="#{contato.matricula_Apresentar}" styleClass="text"/>    
                    </a4j:commandLink>
                </rich:column>
                <rich:column sortBy="#{contato.nomeApresentar}">
                    <f:facet name="header">
                        <h:outputText value="Nome" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <a4j:commandLink action="#{RelatorioContatoAppControle.irParaTelaCliente}"
                                     oncomplete="#{RelatorioContatoAppControle.msgAlert}">
                        <h:outputText value="#{contato.nomeApresentar}" styleClass="text"/>    
                    </a4j:commandLink>
                </rich:column>
                <rich:column sortBy="#{contato.codigoMalaDireta}">
                    <f:facet name="header">
                        <h:outputText value="Mala Direta" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <a4j:commandLink action="#{RelatorioContatoAppControle.irParaTelaCliente}"
                                     oncomplete="#{RelatorioContatoAppControle.msgAlert}">
                        <h:outputText value="#{contato.codigoMalaDireta}" styleClass="text"/>    
                    </a4j:commandLink>
                </rich:column>
                <rich:column sortBy="#{contato.mensagemMalaDireta}">
                    <f:facet name="header">
                        <h:outputText value="Mensagem" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <a4j:commandLink action="#{RelatorioContatoAppControle.irParaTelaCliente}"
                                     oncomplete="#{RelatorioContatoAppControle.msgAlert}">
                        <h:outputText value="#{contato.mensagemMalaDireta}" styleClass="text"/>    
                    </a4j:commandLink>
                </rich:column>
                <rich:column sortBy="#{contato.resposta}">
                    <f:facet name="header">
                        <h:outputText value="Resposta" styleClass="text" style="font-weight: bold;"/>
                    </f:facet>
                    <a4j:commandLink action="#{RelatorioContatoAppControle.irParaTelaCliente}"
                                     oncomplete="#{RelatorioContatoAppControle.msgAlert}">
                        <h:outputText value="#{contato.resposta}" styleClass="text"/>    
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller for="tabelaResultados" id="scrollerResultados" 
                               rendered="#{fn:length(RelatorioContatoAppControle.lista) >= 1}"/>
        </h:panelGrid>

        <script type="text/javascript">

            jQuery(document).ready(function() {
                jQuery('.chamaBotao').keypress(function(e) {
                    var tecla = (e.keyCode ? e.keyCode : e.which);
                    /* verifica se a tecla pressionada foi o ENTER */
                    if (tecla === 13) {
                        document.getElementById('form:btnConsultarCliente').click();
                         /* impede o sumbit caso esteja dentro de um form */
                        e.preventDefault(e);
                        return false;
                    }
                });
            });
        </script>
        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </h:form>
</f:view>
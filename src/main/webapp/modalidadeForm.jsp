<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link rel="stylesheet" href="./css/croppie/croppie.css" type="text/css" />
<script type="text/javascript" src="./script/croppie/croppie.min.js"></script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Modalidade_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Modalidade_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-uma-nova-modalidade/"/>
        <rich:modalPanel id="panelProduto" autosized="true" styleClass="novaModal" shadowOpacity="true" width="450" height="250">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta do Produto"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink"/>
                <rich:componentControl for="panelProduto" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formProduto" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%"  styleClass="font-size-Em-max">
                    <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza " value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         id="consultaProduto" value="#{ModalidadeControle.campoConsultaProduto}">
                            <f:selectItems value="#{ModalidadeControle.tipoConsultaComboProduto}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaProduto" size="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ModalidadeControle.valorConsultaProduto}"/>
                    <a4j:commandLink  id="btnConsultar" reRender="formProduto"
                                      action="#{ModalidadeControle.consultarProduto}" styleClass="botaoPrimario texto-size-14-real" value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaProduto" width="100%" styleClass="tabelaSimplesCustom"
                                rendered="#{not empty ModalidadeControle.listaConsultaProduto}"
                                value="#{ModalidadeControle.listaConsultaProduto}" rows="5" var="produto">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  id="desProduto" value="#{msg_aplic.prt_Cadastro_label_descricao_maiusculo}"/>
                        </f:facet>
                            <a4j:commandLink id="desProdutolink" action="#{ModalidadeControle.selecionarProduto}" focus="descricaoProduto" reRender="form" oncomplete="Richfaces.hideModalPanel('panelProduto')" value="#{produto.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold"  id="vigProduto" value="TIPO DE VIGÊNCIA"/>
                        </f:facet>
                            <a4j:commandLink id="vigProdutolink" action="#{ModalidadeControle.selecionarProduto}" focus="tipoVigencia" reRender="form" oncomplete="Richfaces.hideModalPanel('panelProduto')" value="#{produto.tipoVigencia_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <a4j:commandLink id="opcProdutolink" action="#{ModalidadeControle.selecionarProduto}" focus="descricaoProduto" reRender="form"
                                         oncomplete="Richfaces.hideModalPanel('panelProduto')"
                                         style="display: inline-flex;"
                                         title="#{msg.msg_selecionar_dados}" styleClass="linkPadrao texto-cor-azul texto-size-16-real">
                            <span class="texto-font texto-size-14-real">Selecionar </span>
                            <span class="fa-icon-arrow-right"></span>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formProduto:resultadoConsultaProduto" maxPages="10"
                                   styleClass="scrollPureCustom" renderIfSinglePage="false"
                                   id="scResultadoProduto" />
                <h:panelGrid id="mensagemConsultaProduto" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ModalidadeControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ModalidadeControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{ModalidadeControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <rich:tabPanel width="100%" >
                    <rich:tab id="dadosModalidade" label="Dados Modalidade">
                        <h:panelGrid id="panelDadosModalidade" columns="2"  rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                            <h:outputText   value="#{msg_aplic.prt_Modalidade_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{ModalidadeControle.modalidadeVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_Modalidade_nome}" />
                            <h:panelGroup>
                                <h:inputText  id="nome"  size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ModalidadeControle.modalidadeVO.nome}" />
                                <h:message for="nome" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText value="#{msg_aplic.prt_Modalidade_tipoModalidade}"/>

                            <h:panelGroup rendered="true">
                                <h:selectOneMenu id="tipoModalidade" onblur="blurinput(this);"
                                                 onfocus="focusinput(this);" styleClass="form"
                                                 value="#{ModalidadeControle.modalidadeVO.tipo}">
                                    <f:selectItems value="#{ModalidadeControle.listaSelectItemTipoModalidade}"/>
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_tipoModalidade"
                                                   action="#{ModalidadeControle.montarListaSelectItemTipoModalidade}"
                                                   image="imagens/atualizar.png" immediate="true" ajaxSingle="true"
                                                   reRender="form:tipoModalidade"/>
                                <h:message for="tipoModalidade" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_Modalidade_ativo}" />
                            <h:selectBooleanCheckbox id="ativo" styleClass="campos" value="#{ModalidadeControle.modalidadeVO.ativo}"/>
                            <h:outputText   value="#{msg_aplic.prt_Modalidade_valorMensal}" />
                            <h:panelGroup>
                                <h:inputText  id="valorMensal"  size="20" maxlength="20" onblur="blurinput(this);" onkeypress="return Tecla(event);" onfocus="focusinput(this);" styleClass="form" value="#{ModalidadeControle.modalidadeVO.valorMensal}" >
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:inputText>
                            </h:panelGroup>
                            <h:outputText   value="#{msg_aplic.prt_Modalidade_nrVezes}" />
                            <h:inputText  id="vezesSemana"  onkeypress="return Tecla(event);" size="5" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ModalidadeControle.modalidadeVO.nrVezes}" />
                            <h:outputText value="#{msg_aplic.prt_Modalidade_frequenciasPossiveis}"
                                          rendered="#{ConfiguracaoSistemaControle.configuracaoSistemaVO.sesc && !ModalidadeControle.modalidadeVO.utilizarTurma}"/>
                            <h:inputText id="frequenciasPossiveis" onkeypress="return Tecla(event);" size="5"
                                         maxlength="8" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form"
                                         rendered="#{ConfiguracaoSistemaControle.configuracaoSistemaVO.sesc && !ModalidadeControle.modalidadeVO.utilizarTurma}"
                                         value="#{ModalidadeControle.modalidadeVO.frequenciasPossiveis}"/>
                            <h:outputText value="#{msg_aplic.prt_Modalidade_vagasDisponiveis}"
                                          rendered="#{ConfiguracaoSistemaControle.configuracaoSistemaVO.sesc && !ModalidadeControle.modalidadeVO.utilizarTurma}"/>
                            <h:inputText id="vagasDisponiveis" onkeypress="return Tecla(event);" size="5" maxlength="8"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         rendered="#{ConfiguracaoSistemaControle.configuracaoSistemaVO.sesc && !ModalidadeControle.modalidadeVO.utilizarTurma}"
                                         value="#{ModalidadeControle.modalidadeVO.vagasDisponiveis}"/>
                            <h:outputText   value="#{msg_aplic.prt_Modalidade_utilizarTurma}" />
                            <h:selectBooleanCheckbox id="utilizarTurma" styleClass="campos" value="#{ModalidadeControle.modalidadeVO.utilizarTurma}">
                                <a4j:support event="onclick" reRender="form"/>
                            </h:selectBooleanCheckbox>
                            <h:outputText   value="#{msg_aplic.prt_Modalidade_modalidadeDefault}" />
                            <h:selectBooleanCheckbox id="modalidadeDefault" styleClass="campos" value="#{ModalidadeControle.modalidadeVO.modalidadeDefault}"/>
                            
                            <h:outputText   value="#{msg_aplic.prt_Modalidade_usa_treino}:" 
                                            rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas}"/>
                            <h:selectBooleanCheckbox styleClass="campos" 
                                                     rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas}"
                                                     value="#{ModalidadeControle.modalidadeVO.usaTreino}"/>
                            
                            <h:outputText   value="#{msg_aplic.prt_Modalidade_crossfit}:" />
                            <h:selectBooleanCheckbox styleClass="campos" 
                                                     title="#{ModalidadeControle.modalidadeVO.crossfit and ModalidadeControle.temContratoVendido ? msg_aplic.prt_tela_crossfit_desabilitado_modalidade : ''}"
                                                     disabled="#{ModalidadeControle.modalidadeVO.crossfit and ModalidadeControle.temContratoVendido}"
                                                     value="#{ModalidadeControle.modalidadeVO.crossfit}"/>

                        </h:panelGrid>



                        <h:panelGrid  id="panelProdutoSugerido" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_ProdutoSugerido_tituloForm}"/>
                            </f:facet>
                            <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                                <h:outputText   value="#{msg_aplic.prt_ProdutoSugerido_produto}" />
                                <h:panelGroup>
                                    <h:inputText   readonly="true"  id="ProdutoSugerido_produto" size="40"  maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form"  value="#{ModalidadeControle.produtoSugeridoVO.produto.descricao}" />
                                    <a4j:commandButton id="consultaDadosProduto" focus="valorConsultaProduto" alt="Consulta Produto" reRender="formProduto" oncomplete="Richfaces.showModalPanel('panelProduto'), setFocus(formProduto,'formProduto:valorConsultaProduto');" image="./imagens/informacao.gif" />
                                </h:panelGroup>
                                <h:outputText   value="#{msg_aplic.prt_ProdutoSugerido_obrigatorio}" />
                                <h:selectBooleanCheckbox id="produtoSugerido_obrigatorio" styleClass="campos" value="#{ModalidadeControle.produtoSugeridoVO.obrigatorio}"/>

                            </h:panelGrid>
                            <a4j:commandButton id="addProSugerido" action="#{ModalidadeControle.adicionarProdutoSugerido}" reRender="panelProdutoSugerido, panelMensagemErro" focus="form:ProdutoSugerido_produto" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="produtoSugeridoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ModalidadeControle.modalidadeVO.produtoSugeridoVOs}" var="produtoSugerido">

                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_ProdutoSugerido_produto}" />
                                        </f:facet>
                                        <h:outputText  value="#{produtoSugerido.produto.descricao}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_ProdutoSugerido_obrigatorio}" />
                                        </f:facet>
                                        <h:outputText  value="#{produtoSugerido.obrigatorio_Apresentar}" />
                                    </h:column>
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>
                                            <a4j:commandButton id="editarItemVenda"
                                                               reRender="panelProdutoSugerido"
                                                               action="#{ModalidadeControle.editarProdutoSugerido}"
                                                               value="#{msg_bt.btn_editar}"
                                                               image="./imagens/botaoEditar.png"
                                                               accesskey="6"
                                                               styleClass="botoes"/>
                                            
                                                <h:outputText value="    "/>
                                            
                                            <a4j:commandButton id="removerItemVenda"
                                                               reRender="panelProdutoSugerido"
                                                               immediate="true"
                                                               action="#{ModalidadeControle.removerProdutoSugerido}"
                                                               value="#{msg_bt.btn_excluir}"
                                                               image="./imagens/botaoRemover.png"
                                                               accesskey="7"
                                                               styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                        <script>
                            document.getElementById("form:nome").focus();
                        </script>
                    </rich:tab>
                    <rich:tab id="dadosModalidadeEmpresa" label="Empresas">
                        <h:panelGrid id="panelModalidadeEmpresa" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                            <f:facet name="header">
                                <h:outputText value="Empresas onde a modalidade pode ser utilizada"/>
                            </f:facet>
                            <h:panelGrid columns="2"
                                         rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas and fn:length(ModalidadeControle.listaSelectItemEmpresa) > 1}"
                                         width="100%"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" >
                                <h:outputText rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas}"
                                              styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_ModalidadeEmpresa_empresa}" />
                                <h:panelGroup rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas}">
                                    <h:selectOneMenu  id="ModalidadeEmpresa_empresa"
                                                      onblur="blurinput(this);"
                                                      onfocus="focusinput(this);"
                                                      styleClass="form"
                                                      value="#{ModalidadeControle.modalidadeEmpresaVO.empresa.codigo}" >
                                        <f:selectItems  value="#{ModalidadeControle.listaSelectItemEmpresa}" />
                                    </h:selectOneMenu>
                                    <a4j:commandButton id="atualizar_ModalidadeEmpresa_empresa"
                                                       action="#{ModalidadeControle.montarListaSelectItemEmpresa}"
                                                       image="imagens/atualizar.png"
                                                       immediate="true"
                                                       ajaxSingle="true"
                                                       reRender="form:ModalidadeEmpresa_empresa"/>
                                </h:panelGroup>
                            </h:panelGrid>
                            <h:panelGrid columns="2"
                                         rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas and fn:length(ModalidadeControle.listaSelectItemEmpresa) > 1}"
                                         width="100%"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita">

                                <a4j:commandLink reRender="panelModalidadeEmpresa, panelMensagemErro"
                                                 action="#{ModalidadeControle.adicionarTodasEmpresas}"
                                                 value="Adicionar todas" />

                                <a4j:commandButton  rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas}"
                                                    reRender="panelModalidadeEmpresa, panelMensagemErro"
                                                    focus="form:ModalidadeEmpresa_empresa"
                                                    action="#{ModalidadeControle.adicionarModalidadeEmpresa}"
                                                    value="#{msg_bt.btn_adicionar}"
                                                    image= "./imagens/botaoAdicionar.png"
                                                    accesskey="6"
                                                    styleClass="botoes"/>
                            </h:panelGrid>


                            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                                <h:dataTable id="modalidadeEmpresaVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                             value="#{ModalidadeControle.modalidadeVO.modalidadeEmpresaVOs}" var="modalidadeEmpresa">
                                    <h:column>
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_aplic.prt_ModalidadeEmpresa_empresa}" />
                                        </f:facet>
                                        <h:outputText  value="#{modalidadeEmpresa.empresa.nome}" />
                                    </h:column>
                                    <h:column rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas}">
                                        <f:facet name="header">
                                            <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                        </f:facet>
                                        <h:panelGroup>

                                            
                                                <h:outputText value="    "/>
                                            
                                            <a4j:commandButton rendered="#{ModalidadeControle.permiteConsultarMultiplasEmpresas}"
                                                             id="removerItemVenda"
                                                             immediate="true"
                                                             action="#{ModalidadeControle.removerModalidadeEmpresa}"
                                                             value="#{msg_bt.btn_excluir}"
                                                             image="./imagens/botaoRemover.png"
                                                             accesskey="7"
                                                             reRender="form:panelModalidadeEmpresa, panelMensagemErro"
                                                             styleClass="botoes"/>
                                        </h:panelGroup>
                                    </h:column>
                                </h:dataTable>
                            </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                    <rich:tab id="dadosModalidadeImagem" label="Imagem da modalidade">

                        <style>
                            button,
                            a.btn {
                                background-color: #189094;
                                color: white;
                                padding: 10px 15px;
                                border-radius: 3px;
                                border: 1px solid rgba(255, 255, 255, 0.5);
                                font-size: 16px;
                                cursor: pointer;
                                text-decoration: none;
                                text-shadow: none;
                                display: inline-block;
                                cursor: pointer;
                            }
                            input[type="file"] {
                                cursor: pointer;
                            }
                            button:focus {
                                outline: 0;
                            }

                            .file-btn {
                                position: relative;
                            }
                            .file-btn input[type="file"] {
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                opacity: 0;
                            }

                            .upload-demo .upload-demo-wrap,
                            .upload-demo .upload-result,
                            .upload-demo.ready .upload-msg {
                                display: none;
                            }
                            .upload-demo.ready .upload-demo-wrap {
                                display: block;
                            }
                            .upload-demo.ready .upload-result {
                                display: inline-block;
                            }
                            .upload-demo-wrap {
                                width: 420px;
                                height: 360px;
                                margin: 0 auto;
                            }

                            .upload-msg {
                                text-align: center;
                                padding: 120px 40px;
                                font-size: 22px;
                                color: #aaa;
                                width: 420px;
                                height: 70px;
                                border: 1px solid #aaa;
                            }
                        </style>
                        <h:panelGrid id="panelImagemModalidade" style="vertical-align: top !important;" columns="2" rowClasses="linhaPar, linhaImpar" width="100%">

                            <h:panelGroup>

                                <div class="demo-wrap upload-demo">

                                    <a class="btn file-btn botoes nvoBt">
                                        <span>Upload</span>
                                        <input type="file" id="upload" value="Choose a file" accept="image/*" />
                                    </a>
                                    <button type="button" id="upload-result"
                                            class="upload-result btn file-btn botoes nvoBt">Selecionar</button>

                                    <a4j:commandLink id="deletar-img"
                                                     action="#{ModalidadeControle.confirmarExcluirImagem}" oncomplete="#{ModalidadeControle.msgAlert}"
                                                     styleClass="btn file-btn botoes nvoBt btSec" reRender="mdlMensagemGenerica"
                                                     value="Excluir Imagem"/>

                                    <div class="upload-msg">
                                        Carregue um arquivo para iniciar o recorte
                                    </div>
                                    <div class="upload-demo-wrap">
                                        <div id="upload-demo"></div>
                                    </div>
                                </div>
                                <h:inputHidden id="fileUploadTmp" value="#{ModalidadeControle.fileBase64}" />

                            </h:panelGroup>



                            <a4j:mediaOutput  id="imgUploaded" element="img" mimeType="image/jpeg"
                                              createContent="#{ModalidadeControle.paint}"
                                              style="width:220px; height:350px;" cacheable="false">
                            </a4j:mediaOutput>

                        </h:panelGrid>

                        <script>

                            function demoUpload() {
                                //var $uploadCrop;

                                function readFile(input) {
                                    if (input.files && input.files[0]) {

                                        var reader = new FileReader();

                                        reader.onload = function (e) {
                                            document.querySelector(".upload-demo").classList.add("ready");
                                            resize.bind({
                                                url:  e.target.result
                                            });

                                            /*					$('.upload-demo').addClass('ready');
                                                                $uploadCrop.croppie('bind', {
                                                                    url: e.target.result
                                                                }).then(function(){
                                                                    console.log('jQuery bind complete');
                                                                });*/

                                        }

                                        reader.readAsDataURL(input.files[0]);
                                    }
                                    else {
                                        console.log("Sorry - you're browser doesn't support the FileReader API");
                                    }
                                }

                                var el = document.getElementById('upload-demo');
                                var resize = new Croppie(el, {
                                    viewport: { width: 165, height: 255 },
                                    boundary: { width: 420, height: 310 },
                                    showZoomer: true,
                                    enableResize: false,
                                    enableOrientation: true,
                                    mouseWheelZoom: 'ctrl'
                                });

                                document.getElementById('upload').addEventListener("change", function () { readFile(this); });

                                document.getElementById('upload-result').addEventListener("click", function () {
                                    resize.result({type:'base64',size:{width:550, height:850}}).then(function(blob) {
                                        document.getElementById('form:imgUploaded').src = blob;
                                        document.getElementById('form:fileUploadTmp').value = blob;
                                    });

                                });




                                //$('#upload').on('change', function () { readFile(this); });
                                /*$('.upload-result').on('click', function (ev) {
                                    $uploadCrop.croppie('result', {
                                        type: 'canvas',
                                        size: 'viewport'
                                    }).then(function (resp) {
                                        popupResult({
                                            src: resp
                                        });
                                    });
                                });*/
                            }

                            demoUpload();

                        </script>
                    </rich:tab>
                </rich:tabPanel>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            
                                <h:outputText value=" "/>
                            
                        </h:panelGrid>
                        <h:commandButton  rendered="#{ModalidadeControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ModalidadeControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgModalidade" styleClass="mensagem"  value="#{ModalidadeControle.mensagem}"/>
                            <h:outputText id="msgModalidadeDet" styleClass="mensagemDetalhada" value="#{ModalidadeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{ModalidadeControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            
                                <h:outputText value="    "/>
                            
                            <h:commandButton id="salvar" action="#{ModalidadeControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            
                                <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica" onclick="fireElementFromAnyParent('form:btnAtualizaTempo');"
                                                   oncomplete="#{ModalidadeControle.msgAlert}" action="#{ModalidadeControle.confirmarExcluir}"   value="#{msg_bt.btn_excluir}"
                                                   alt="#{msg.msg_excluir_dados}" accesskey="3"
                                                   styleClass="botoes nvoBt btSec btPerigo" >
                                    <f:param name="metodochamar" value="excluir"/>
                                </a4j:commandButton>
                            </h:panelGroup>
                            

                                <h:outputText value="    "/>
                            
                            <h:commandButton id="consultar" immediate="true" action="#{ModalidadeControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            
                                <h:outputText value="    "/>
                            
                            <a4j:commandLink
                                    action="#{ModalidadeControle.realizarConsultaLogObjetoSelecionado}"
                                    reRender="form"
                                    oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                    title="Visualizar Log"
                                    style="display: inline-block; padding: 8px 15px;"
                                    styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>

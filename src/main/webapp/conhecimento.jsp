<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script language="javascript" src="script/telaCliente1.3.js" type="text/javascript"></script>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>

</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<jsp:include page="include_head.jsp" flush="true"/>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<script type="text/javascript">
    jQuery.noConflict();

    function baixarPainelMarcarAluno() {
        jQuery('.painelMarcarAluno').slideDown('slow');
    }

    function sumirPainelMarcarAluno() {
        jQuery('.painelMarcarAluno').slideUp();
    }

    setDocumentCookie('popupsImportante', 'close', 1);
</script>
<f:view locale="#{SuperControle.idioma}">
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:keepAlive beanName="ConsultaClienteControle"/>
    <title>
        <h:outputText value="Clientes"/>
    </title>
    <h:form id="form" styleClass="form-scroll" onkeypress="return event.keyCode != 13; ">
        <html>
        <body>
        <h:panelGroup layout="block" styleClass="bgtop topoZW">
            <jsp:include page="include_topo_novo.jsp" flush="true"/>
            <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            <rich:jQuery selector=".item4" query="addClass('menuItemAtual')"/>
            <link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>
        </h:panelGroup>

        <table width="100%" border="0" cellpadding="0" cellspacing="0">


            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="180" align="left" valign="top" class="bglateraltop" height="100%"
                                style="background-color: white;"
                                style="padding: 0 !important;">
                                <jsp:include page="include_box_menulateral.jsp" flush="true">
                                    <jsp:param name="menu" value="ADM-INICIO"/>
                                </jsp:include>
                            </td>
                            <td align="center" valign="top" height="100%">

                                <div style="margin: 1.5vw; background-color: white;padding: 20px; position: relative; width: calc(100% - 6vw);"
                                     class="container-box zw_ui">

                                    <style>
                                        .conteudo-conhecimento {
                                            text-align: left;
                                        }

                                        .conteudo-conhecimento span {
                                            margin: 15px 10px;
                                            display: block;
                                        }
                                    </style>

                                    <div class="table-wrapper pacto-shadow">
                                        <div class="conteudo-conhecimento">
                                        <span>Exemplo de hint. Ver mais com link externo
                                            <span id="conhecimento1" class="hint-pi" onmouseover="hintPi(this)"></span>
                                        </span>
                                            <span>Exemplo de hint. Ver mais com termo de pesquisa na unversidade Pacto
                                            <span id="conhecimento2"  class="hint-pi" onmouseover="hintPi(this)"></span>
                                            </span>
                                            <span>Exemplo de hint. Ver mais abrindo o conhecimento da página.
                                                <span id="conhecimento3"  class="hint-pi" onmouseover="hintPi(this)"></span>
                                            </span>
                                            <span>Exemplo de hint. Ver mais abrindo um conhecimento específico.
                                                <span id="conhecimento4"  class="hint-pi" onmouseover="hintPi(this)"></span>
                                            </span>
                                            <span>Exemplo de hint sem o ver mais.
                                                <span id="conhecimento5"  class="hint-pi" onmouseover="hintPi(this)"></span>
                                            </span>
                                            <span>Exemplo de hint ainda sem conhecimento cadastrado.
                                                <span id="conhecimento-id"  class="hint-pi" onmouseover="hintPi(this)"></span>
                                            </span>

                                        </div>
                                    </div>

                                </div>

                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td>
                    <jsp:include page="include_rodape_flat.jsp" flush="true"/>
                </td>
            </tr>
        </table>
        <rich:panel styleClass="painelMarcarAluno">

            <rich:dropSupport acceptedTypes="alunos" dropValue="aluno"
                              reRender="#{SuperControle.menuZwUi ? 'painelAlunosMarcadosNovoMenu' : 'painelAlunosMarcados'}"
                              oncomplete="carregarTooltipster();"
                              dropListener="#{ClientesMarcadosControle.processDropLista}">
            </rich:dropSupport>

            <div class="textoMarcarAluno">
                <i class="fa-icon-plus" style="margin-right: 10px;"></i>
                <h:outputText value="Marcar aluno"/>
            </div>


        </rich:panel>

        </body>
        </html>

        <script>
            carregarTooltipster();
        </script>
    </h:form>

    <%@include file="/include_load_configs.jsp" %>
</f:view>
<script type="text/javascript">
    document.getElementById("form:valorConsulta").focus();
</script>

<%-- 
    Document   : cancelamentoSessaoTransferencia
    Created on : 13/12/2012, 18:13:32
    Author     : carla
--%>
<%--
    Document   : cancelamentoSessaoForm
    Created on : 13/12/2012, 09:17:03
    Author     : carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<f:view>
        <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Transferência" />
    </title>
    <h:form id="form">
        <%@include file="includes/include_identificadorModuloEstudio.jsp" %>
        <h:panelGrid
            columns="1"
            width="100%"
            style="background-color:#FFFFFF;"
            cellpadding="0"
            cellspacing="0"
            columnClasses="colunaCentralizada">
            <f:facet name="header">
                <h:graphicImage url="/imagens/estudio/topoDemoReduzidoEstudio.png" width="100%" height="45"/>
            </f:facet>
            <h:panelGrid
                columns="1"
                style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                columnClasses="colunaCentralizada"
                width="100%">
                <h:outputText
                    styleClass="tituloFormulario"
                    value="#{msg_aplic.prt_CancelamentoSessao_tituloForm}" />
            </h:panelGrid>
            <h:panelGrid
                columns="2"
                width="100%"
                columnClasses="colunaCentralizada">
                <h:panelGrid columns="1">
                    <img src="./imagens/lateralWizardCancelamentoMaior.png" >
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" cellpadding="5">
                    <h:outputText value="Nome do Cliente: " styleClass="tituloCampos" style="font-weight: bold;"  />
                    <h:outputText id="clienteNomeAuto" styleClass="tituloCampos" value="#{CancelamentoSessaoControle.clienteVO.pessoa.nome}"/>
                    <h:outputText  style="font-weight: bold;" styleClass="tituloCampos" value="Cliente para Transferência" />
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Valor final a ser transferido: "/>
                        <h:outputText styleClass="tituloCampos" value="R$ "/>
                        <h:outputText id="valorFinal" styleClass="green" style="font-weight: bold;"
                                      value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorTransferidoCliente}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup id="panelGroupCliente" >
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_cancelamentoTransferenciaCliente_nomeCliente}:" />
                        <rich:spacer width="10"/>
                        <h:inputText  id="nomeCliente" size="40"  maxlength="50" onblur="blurinput(this);"
                                      onfocus="focusinput(this);" styleClass="form" readonly="#{CancelamentoSessaoControle.cancelamentoSessaoVO.depositaNaConta}"
                                      value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.clienteVO.pessoa.nome}" />
                        <rich:suggestionbox   height="200" width="400"
                                              for="nomeCliente"
                                              status="statusInComponent"
                                              immediate="true"
                                              suggestionAction="#{CancelamentoSessaoControle.executarAutocomplete}"
                                              minChars="1"
                                              rowClasses="linhaImpar, linhaPar"
                                              var="result"  id="suggestionResponsavel">
                            <a4j:support event="onselect"
                                         reRender="form"
                                         action="#{CancelamentoSessaoControle.selecionarCliente}">
                            </a4j:support>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Nome"  styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText styleClass="textverysmall" value="#{result.pessoa.nome}" />
                            </h:column>
                        </rich:suggestionbox>

                        <rich:spacer width="5" rendered="#{!CancelamentoSessaoControle.cancelamentoSessaoVO.depositaNaConta}"/>
                        <a4j:commandButton  rendered="#{!CancelamentoSessaoControle.cancelamentoSessaoVO.depositaNaConta}"
                                            action="#{CancelamentoSessaoControle.limparCampoCliente}"
                                            image="images/limpar.gif" alt="#{msg_aplic.prt_limparCampo}" reRender="nomeCliente"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Observação: "/>
                        <br />
                        <h:inputTextarea value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.observacao}"
                                         id="descricaoCalculo" styleClass="camposSomenteLeitura"  rows="7" cols="60"/>
                    </h:panelGroup>

                    <h:panelGrid
                        id="panelMensagem"
                        columns="3"
                        width="100%"
                        columnClasses="colunaEsquerda">
                        <h:panelGrid
                            columns="1"
                            width="100%">

                            <h:outputText value=" " />

                        </h:panelGrid>
                        <h:commandButton
                            rendered="#{CancelamentoSessaoControle.sucesso}"
                            image="./imagens/sucesso.png" />
                        <h:commandButton
                            rendered="#{CancelamentoSessaoControle.erro}"
                            image="./imagens/erro.png" />
                        <h:panelGrid
                            columns="1"
                            width="100%">
                            <h:outputText
                                styleClass="mensagem"
                                value="#{CancelamentoSessaoControle.mensagem}" />
                            <h:outputText
                                styleClass="mensagemDetalhada"
                                value="#{CancelamentoSessaoControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGroup>
                    	<a4j:commandButton
                            id="voltar"
                            value="Voltar"
                            rendered="#{CancelamentoSessaoControle.apresentarBotaoCancelar}"
                            action="#{CancelamentoSessaoControle.voltarTelaCancelamento}"
                            image="/imagens/estudio/voltar.png" />
                        <rich:spacer width="20px" />
                        <a4j:commandButton
                            id="cancelar"
                            rendered="#{CancelamentoSessaoControle.apresentarBotaoCancelar}"
                            value="Cancelar"
                            oncomplete="#{CancelamentoSessaoControle.abrirRichModal}"
                            image="/imagens/estudio/confirmar.png" />
                        <rich:spacer width="20px" />
                         <h:commandButton
                         rendered="#{!CancelamentoSessaoControle.apresentarBotaoCancelar}"
                            id="fechar"
                            alt="Fechar Janela"
                            onclick="fecharJanela();"
                            image="./imagens/estudio/fechar.png" />
                        <rich:spacer width="20px" />
                        
                    </h:panelGroup>
                </h:panelGrid> </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <rich:modalPanel
        id="panelConfirmacaoEstornar"
        autosized="true"
        shadowOpacity="true"
        width="450"
        height="250"
        onshow="document.getElementById('formConfirmacaoEstornar:senha').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação do Cancelamento de Sessões"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage
                    value="/imagens/close.png"
                    style="cursor:pointer"
                    id="hidelinkConfirmacaoEstornar" />
                <rich:componentControl
                    for="panelConfirmacaoEstornar"
                    attachTo="hidelinkConfirmacaoEstornar"
                    operation="hide"
                    event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formConfirmacaoEstornar">
            <h:panelGrid
                columns="1"
                width="100%"
                columnClasses="colunaCentralizada">
                <h:panelGrid
                    columns="1"
                    style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                    columnClasses="colunaCentralizada"
                    width="100%">
                    <h:outputText
                        styleClass="tituloFormulario"
                        value="Confirmação do Cancelamento de Sessões" />
                </h:panelGrid>
                <h:panelGrid
                    id="panelConfirmacao"
                    columns="1"
                    width="100%"
                    columnClasses="colunaEsquerda"
                    styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText
                            styleClass="text"
                            value="Código:" />
                        <h:inputText
                            id="codigoUsuario"
                            size="5" maxlength="7" style="margin-left:6px"
                            value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.responsavelCancelamento.codigo}">
                            <a4j:support
                                event="onchange"
                                focus="formConfirmacaoEstornar:senha"
                                action="#{CancelamentoSessaoControle.consultarResponsavelCancelamento}"
                                reRender="panelConfirmacao, msg" />
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.responsavelCancelamento.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText
                            styleClass="text"
                            value="Usuário:" />
                        <h:outputText
                            styleClass="text"
                            style="margin-left:5px"
                            value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.responsavelCancelamento.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText
                            value="Senha:"
                            styleClass="text" />
                        <h:inputSecret
                            id="senha"
                            size="14"
                            maxlength="64" autocomplete="off"
                            style="margin-left:8px"
                            value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.responsavelCancelamento.senha}"/>
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('login')}.onclick();return false;"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid
                    id="msg"
                    columns="2"
                    width="100%">
                    <h:panelGrid
                        columns="1"
                        width="100%">
                        <h:outputText
                            styleClass="mensagemDetalhada"
                            value="#{CancelamentoSessaoControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
                <a4j:commandButton
                    id="login"
                    value="#{msg_bt.btn_confirmar}"
                    image="./imagens/btn_Confirmar.png"
                    alt="#{msg.msg_gravar_dados}"
                    action="#{CancelamentoSessaoControle.gravar}"
                    oncomplete="#{CancelamentoSessaoControle.fecharRichModalPanelConfirmacao};fireElementFromParent('form:btnAtualizaCliente');"
                    reRender="form,panelConfirmacao,msg,panelMensagem" />
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>

<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view locale="#{SuperControle.idioma}">

    <title>
        <h:outputText value="#{msg_aplic.prt_acesso_autorizacao}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_acesso_autorizacao}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}quais-sao-as-permissoes-do-perfil-de-acesso-do-modulo-adm/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <a4j:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%">

                <h:panelGrid columns="2" rowClasses="linhaPar, linhaImpar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText styleClass="tituloCampos" rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.novoObj}"
                                  value="#{msg_aplic.prt_acesso_autorizacao_codAutorizacao}" />
                    <h:outputText styleClass="tituloCampos" rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.novoObj}"
                                  value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codigoAutorizacao}" />

                    <h:outputText rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="#{msg_aplic.prt_LocalAcesso_empresa}" />
                    <h:panelGroup rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.usuarioLogado.administrador}">
                        <h:selectOneMenu id="empresa"  onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="color:black;"
                                         styleClass="form"
                                         value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.empresaLocal.codigo}" >
                            <f:selectItems  value="#{AutorizacaoAcessoGrupoEmpresarialControle.listaEmpresas}" />
                            <a4j:support event="onchange" action="#{AutorizacaoAcessoGrupoEmpresarialControle.montarIntegracoes}"
                                         reRender="comboIntegracoes"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_integracao}" />
                    <h:panelGroup id="comboIntegracoes">
                        <h:selectOneMenu id="integracoes"  onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         style="color:black;"
                                         styleClass="form"
                                         value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.integracao.codigo}" >
                            <f:selectItems  value="#{AutorizacaoAcessoGrupoEmpresarialControle.integracoes}" />
                            <a4j:support event="onchange" action="#{AutorizacaoAcessoGrupoEmpresarialControle.selecionarIntegracao}"
                                         reRender="form"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                   <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_empresa_remota}" />
                   <h:panelGroup id="empresaRemotaNome">
                        <h:outputText styleClass="tituloCampos" value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.empresaRemota.nome}" />
                   </h:panelGroup>

                   <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_tipo}" />
                   <h:selectOneRadio id="mudarChequeCartao" tabindex="1"
                                     styleClass="tituloCampos" value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.tipoPessoa}">
                       <f:selectItems value="#{AutorizacaoAcessoGrupoEmpresarialControle.tiposPessoa}"/>
                       <a4j:support event="onchange" reRender="form" action="#{AutorizacaoAcessoGrupoEmpresarialControle.limpar}" />
                    </h:selectOneRadio>

                   <h:panelGroup id="nomeTipoPessoa">
                       <h:outputText styleClass="tituloCampos"
                                     rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.aluno}" value="#{msg_aplic.prt_acesso_autorizacao_aluno}" />
                       <h:outputText rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.aluno}"
                                     styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_colaborador}" />

                   </h:panelGroup>
                   <h:panelGroup id="escolhaTipoPessoa">
                        <h:outputText styleClass="tituloCampos"
                                      value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.nomePessoa}" />
                        &nbsp;
                       <a4j:commandLink value="#{msg_aplic.prt_acesso_autorizacao_selecionarPessoa}"
                                        oncomplete="#{AutorizacaoAcessoGrupoEmpresarialControle.msgAlert}"
                                        reRender="consultaPessoaModal"
                                        rendered="#{empty AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.nomePessoa}"
                                        action="#{AutorizacaoAcessoGrupoEmpresarialControle.validarEmpresa}"/>
                       <a4j:commandLink value="#{msg_aplic.prt_acesso_autorizacao_alterarPessoa}"
                                        oncomplete="#{AutorizacaoAcessoGrupoEmpresarialControle.msgAlert}"
                                        reRender="consultaPessoaModal"
                                        rendered="#{not empty AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.nomePessoa}"
                                        action="#{AutorizacaoAcessoGrupoEmpresarialControle.validarEmpresa}"/>
                   </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_matricula}"
                                  rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.aluno}"/>
                    <h:panelGroup rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.aluno}">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codigoMatricula}"
                                  rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.aluno && AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codigoMatricula > 0}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_cpf}" />
                    <h:outputText styleClass="tituloCampos" value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.cpf}" />

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_codigoCol}"
                                  rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.aluno}"/>
                    <h:panelGroup rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.aluno}">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codigoGenerico}"
                                  rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.aluno && AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codigoGenerico > 0}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_codAcesso}" />
                    <h:outputText styleClass="tituloCampos"
                                  value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codAcesso_Apresentar}" />

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_codAcessoAlter}" />
                    <h:outputText styleClass="tituloCampos"
                                  value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codAcessoAlternativo}" />

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_datanasc}" />
                    <h:outputText styleClass="tituloCampos"
                                  value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.dataNasc_Apresentar}" />

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_telefone}" />
                    <h:outputText styleClass="tituloCampos" value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.tel_Apresentar}" />

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_email}" />
                    <h:outputText styleClass="tituloCampos" value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.email_Apresentar}" />

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_possuiBiometriaFacial}" />
                    <h:panelGroup layout="block">
                        <h:outputText rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.possuiBiometriaFacial}"
                                      styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_sim}" />
                        <h:outputText rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.possuiBiometriaFacial}"
                                      styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_nao}" />
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_possuiBiometriaDigital}" />
                    <h:panelGroup layout="block">
                        <h:outputText rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.possuiBiometriaDigital}"
                                      styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_sim}" />
                        <h:outputText rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.possuiBiometriaDigital}"
                                      styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_nao}" />
                    </h:panelGroup>

                    <h:outputText rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codigo > 0}" styleClass="tituloCampos" value="#{msg_aplic.prt_acesso_autorizacao_senha}" />
                    <a4j:commandButton rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codigo > 0}"  id="alterarSenha"
                                       reRender="form" action="#{AutorizacaoAcessoGrupoEmpresarialControle.limparCamposSenha}"
                                   value="#{not empty AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.senhaAcesso ? msg_aplic.prt_acesso_autorizacao_alterarSenha : msg_aplic.prt_acesso_autorizacao_incluirSenha}"
                                   styleClass="botaoPrimario texto-size-16"
                                   oncomplete="Richfaces.showModalPanel('alterarSenhaAutorizacao');"/>

                    <h:outputText rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.codigo > 0 and
                    AutorizacaoAcessoGrupoEmpresarialControle.statusFotoValida > 0}"
                                  styleClass="tituloCampos" value="Foto validada" />
                    <h:graphicImage url="./images/check.png"
                                    rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.statusFotoValida == 1}"/>
                    <h:graphicImage url="./images/icon_delete.png"
                                    rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.statusFotoValida == 2}"/>

                </h:panelGrid>

                <h:panelGrid id="painelMensagem" columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">

                        <h:outputText value=" "/>

                    </h:panelGrid>
                    <h:commandButton  rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" id="mensagem2"  value="#{AutorizacaoAcessoGrupoEmpresarialControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada2" value="#{AutorizacaoAcessoGrupoEmpresarialControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>

                   <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{AutorizacaoAcessoGrupoEmpresarialControle.novo}" value="#{msg_bt.btn_novo}"  title="#{msg_bt.btn_novoLocal}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar"
                                             action="#{AutorizacaoAcessoGrupoEmpresarialControle.gravar}"
                                             value="#{msg_bt.btn_gravar}"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                             reRender="form, panelAutorizacaoFuncionalidade"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="excluir"
                                               onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                             rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.novoObj}"
                                             action="#{AutorizacaoAcessoGrupoEmpresarialControle.excluir}"
                                               reRender="panelAutorizacaoFuncionalidade"
                                             value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>

                            <h:outputText rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.novoObj}" value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{AutorizacaoAcessoGrupoEmpresarialControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                             <a4j:commandLink action="#{AutorizacaoAcessoGrupoEmpresarialControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                            <%--

                                <h:outputText value="    "/>

                            <a4j:commandButton action="#{LocalAcessoControle.realizarConsultaLogObjetoSelecionado}" reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);" image="./imagens/botalVisualizarLog.png" alt="Visualizar LOG" title="Visualizar Log" styleClass="botoes"/>
                            --%>
                        </h:panelGroup>
                    </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </h:panelGrid>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"></jsp:include>

<rich:modalPanel styleClass="novaModal" id="consultaPessoaModal" width="650" autosized="true" top="90">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_acesso_autorizacao_selecionarPessoa}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                            id="hidelink1" />
            <rich:componentControl for="consultaPessoaModal"
                                   attachTo="hidelink1" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <h:form id="formConsultaPessoaModal">
        <h:panelGrid width="100%" columns="1" columnClasses="colunaCentralizada" cellpadding="0" cellspacing="0">
            <h:panelGroup>
                <h:inputText id="paramConsulta" size="30" maxlength="255" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{AutorizacaoAcessoGrupoEmpresarialControle.paramConsulta}"/>
                <rich:hotKey selector="#paramConsulta" key="return"
                             handler="#{rich:element('Consultar')}.onclick();return false;"/>
                <rich:spacer width="10"/>
                <a4j:commandButton id="Consultar" action="#{AutorizacaoAcessoGrupoEmpresarialControle.consultarPessoas}"
                                   reRender="formConsultaPessoaModal"
                                   value="Consultar"
                                   styleClass="botaoPrimario texto-size-16"
                                   oncomplete="#{AutorizacaoAcessoGrupoEmpresarialControle.msgAlert}"/>
            </h:panelGroup>
        </h:panelGrid>
        <rich:spacer height="10"/>
        <h:outputText rendered="#{(empty AutorizacaoAcessoGrupoEmpresarialControle.pessoas) && AutorizacaoAcessoGrupoEmpresarialControle.aluno}"
                      value="#{msg_aplic.prt_acesso_autorizacao_nenhumAluno}" styleClass="tituloCampos"/>
        <h:outputText rendered="#{(empty AutorizacaoAcessoGrupoEmpresarialControle.pessoas) && !AutorizacaoAcessoGrupoEmpresarialControle.aluno}"
                      value="#{msg_aplic.prt_acesso_autorizacao_nenhumColaborador}" styleClass="tituloCampos"/>
        <rich:dataTable var="pessoa" value="#{AutorizacaoAcessoGrupoEmpresarialControle.pessoas}"
                        rendered="#{not empty AutorizacaoAcessoGrupoEmpresarialControle.pessoas}"
                        id="tablePessoas" width="100%"
                        rows="10"
                        columnClasses="centralizado, colunaEsquerda, centralizado">
            <rich:column>
                <f:facet name="header">
                    <h:outputText value="#{AutorizacaoAcessoGrupoEmpresarialControle.labelCodigo}"/>
                </f:facet>
                <a4j:commandLink reRender="form" action="#{AutorizacaoAcessoGrupoEmpresarialControle.selecionarAutorizado}" oncomplete="Richfaces.hideModalPanel('consultaPessoaModal')">
                <h:outputText rendered="#{!AutorizacaoAcessoGrupoEmpresarialControle.aluno}" value="#{pessoa.codigo}"/>
                <h:outputText rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.aluno}" value="#{pessoa.codigoMatricula}"/>
                </a4j:commandLink>
            </rich:column>

            <rich:column>
                <f:facet name="header">
                       <h:outputText value="#{msg_aplic.prt_acesso_autorizacao_nome}"/>
                </f:facet>
                <a4j:commandLink reRender="form" action="#{AutorizacaoAcessoGrupoEmpresarialControle.selecionarAutorizado}" oncomplete="Richfaces.hideModalPanel('consultaPessoaModal')">
                <h:outputText value="#{pessoa.pessoa.nome}"/>
                </a4j:commandLink>
            </rich:column>

            <rich:column>
                <f:facet name="header">
                       <h:outputText value="#{msg_aplic.prt_acesso_autorizacao_codAcesso}"/>
                </f:facet>
                <a4j:commandLink reRender="form" action="#{AutorizacaoAcessoGrupoEmpresarialControle.selecionarAutorizado}" oncomplete="Richfaces.hideModalPanel('consultaPessoaModal')">
                <h:outputText value="#{pessoa.codAcesso}"/>
                </a4j:commandLink>
            </rich:column>
    </rich:dataTable>
            <rich:datascroller for="tablePessoas" rendered="#{not empty AutorizacaoAcessoGrupoEmpresarialControle.pessoas}"/>
    </h:form>
</rich:modalPanel>
        
<rich:modalPanel styleClass="novaModal" id="alterarSenhaAutorizacao" width="650" autosized="true" top="90">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_acesso_autorizacao_definirSenha}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                            id="hidelink2" />
            <rich:componentControl for="alterarSenhaAutorizacao"
                                   attachTo="hidelink2" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <h:form id="formAlterarSenhaAutorizacao">
       <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos14" value="#{msg_aplic.prt_Pessoa_definirSenhaAcesso}">
                            <h:outputLink value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                          title="Clique e saiba mais: Definição de Senha para Catraca" target="_blank">
                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                            </h:outputLink>
                        </h:outputText>

                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                        <h:outputText  value="#{AutorizacaoAcessoGrupoEmpresarialControle.aluno ? msg_aplic.prt_acesso_autorizacao_aluno : msg_aplic.prt_acesso_autorizacao_colaborador}:" />
                        <h:outputText  value="#{AutorizacaoAcessoGrupoEmpresarialControle.autorizacao.nomePessoa}" />
                    </h:panelGrid>        
                    <h:panelGrid id="panelSenhas" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">            
                        <h:outputText value="#{msg_aplic.prt_Pessoa_senhaAcesso}" />
                        <h:inputSecret  id="senhaAcesso" size="10" maxlength="#{AutorizacaoAcessoGrupoEmpresarialControle.senha11Digitos ? '11' : '5'}" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{AutorizacaoAcessoGrupoEmpresarialControle.senhaNova}" />
                        <h:outputText value="#{msg_aplic.prt_Pessoa_confirmarSenhaAcesso}" />
                        <h:inputSecret id="ConfirmarSenhaAcesso" size="10" maxlength="#{AutorizacaoAcessoGrupoEmpresarialControle.senha11Digitos ? '11' : '5'}" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{AutorizacaoAcessoGrupoEmpresarialControle.senhaNovaConfirmar}" />

                    </h:panelGrid>

                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{AutorizacaoAcessoGrupoEmpresarialControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{AutorizacaoAcessoGrupoEmpresarialControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{AutorizacaoAcessoGrupoEmpresarialControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid id="panelBotao" columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <a4j:commandButton   id="salvarSenha"
                                           action="#{AutorizacaoAcessoGrupoEmpresarialControle.alterarSenhaAcesso}"
                                           reRender="form,formAlterarSenhaAutorizacao,panelAutorizacaoFuncionalidade"
                                           oncomplete="#{AutorizacaoAcessoGrupoEmpresarialControle.msgAlert}#{AutorizacaoAcessoGrupoEmpresarialControle.mensagemNotificar}"
                                           value="#{msg_bt.btn_gravar}"
                                           alt="#{msg.msg_gravar_dados}"  styleClass="botoes nvoBt"/>
                    </h:panelGrid>

                </h:panelGrid>
            </h:panelGrid>
    </h:form>
</rich:modalPanel>
    
        
<%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>

<%-- 
    Document   : lancarBrindeClienteForm
    Created on : 19/10/2017, 11:14:06
    Author     : arthur
--%>

<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>${msg_aplic.prt_LancaBrinde_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_LancaBrinde_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-trocar-os-pontos-por-brindes-no-clube-de-vantagens/"/>
    
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>
            
            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-2-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                            
                            <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" style="margin-right: 5px;font-weight: bold;" value="Saldo Atual:"/> <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza" style="margin-right: 25px;" id="saldoAtual" value="#{LancarBrindeClienteControle.pontosClienteSelecioando}"/>
                            <h:panelGroup layout="block" styleClass="cb-container" style="margin-right: 10px;">
                                <h:selectOneMenu id="mostrartodos" styleClass="exportadores" style="margin-right: 10px;"
                                                 value="#{LancarBrindeClienteControle.mostrarTodos}">
                                    <f:selectItems value="#{LancarBrindeClienteControle.listaMostrarTodosBrindes}"/>
                                    <a4j:support event="onchange" oncomplete="recarregarTabelaLancarBrindeCliente()" reRender="saldoAtual"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                            
                            <a4j:commandLink id="btnExcel"
                                             styleClass="exportadores"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,descricao=Descrição,ingressoAte_Apresentar=Prazo de Ingresso,vigenciaDe_Apresentar=Início,vigenciaAte_Apresentar=Fim,empresa_Apresentar=Empresa"/>
                                <f:attribute name="prefixo" value="Plano"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="#{msg_aplic.prt_exportar_form_excel}" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF"
                                             styleClass="exportadores margin-h-10"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos"
                                             value="codigo=Código,descricao=Descrição,ingressoAte_Apresentar=Prazo de Ingresso,vigenciaDe_Apresentar=Início,vigenciaAte_Apresentar=Fim,empresa_Apresentar=Empresa"/>
                                <f:attribute name="prefixo" value="Plano"/>
                                <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                <h:outputText title="#{msg_aplic.prt_exportar_form_pdf}" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 action="#{LancarBrindeClienteControle.realizarConsultaLogObjetoGeral}"
                                                        oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tabelaLancarBrindeCliente" class="tabelaLancarBrindeCliente pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${msg_aplic.CODIGO}</th>
                    <th>${msg_aplic.BRINDE}</th>
                    <th>${msg_aplic.PONTOS}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{LancarBrindeClienteControle.selecionarBrindeCliente}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <a4j:commandButton id="atualizarSaldo" value="Teste" style="visibility: hidden;" reRender="saldoAtual"/>
            
        </h:form>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>
    <script>
        function recarregarTabelaLancarBrindeCliente() {
            var mostrartodos = document.getElementById("form:mostrartodos").value;
            tabelaAtual.dataTable().fnDestroy(0);
            iniciarTabela("tabelaLancarBrindeCliente", "${contexto}/prest/relatorio/lancarbrindecliente?mostrartodos="+mostrartodos, 1, "asc", "", true);
        }
        
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaLancarBrindeCliente", "${contexto}/prest/relatorio/lancarbrindecliente?mostrartodos=${LancarBrindeClienteControle.mostrarTodos}", 1, "asc", "", true);
            carregarSaldo();
        });
        function carregarSaldo(){
            document.getElementById("form:atualizarSaldo").click();
        }
    </script>
</f:view>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/css_bi_1.4.css" rel="stylesheet" type="text/css"/>

<%@include file="/includes/imports.jsp" %>
<head>
    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="../css/telaCliente.css" rel="stylesheet" type="text/css"/>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>

    <script src="script/packJQueryPlugins.min.js" type="text/javascript" ></script>
    <link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
</head>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Vendas Online"/>
    </title>
    <h:form id="form">
        <a4j:keepAlive beanName="AdquiraVendasOnlineControle"/>
        <html>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup id="panelConteudo">


                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup id="test" layout="block" style="width: auto;">
                                        <a4j:commandLink
                                                id="bannerAdquiraVendas"
                                                reRender="modalAdquiraVendas"
                                                action="#{AdquiraVendasOnlineControle.abrilModalNegociacao}"
                                                oncomplete="#{AdquiraVendasOnlineControle.msgAlert}">
                                            <img style="position: absolute; height: 100%; width: 100%;"
                                                 src="./imagens/banner-vendas-online-ativar.png"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

        </body>
        </html>
    </h:form>
    <rich:modalPanel id="modalAdquiraVendas"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="620" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Negociação vendas online"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="closeAdquiraVendas"/>
                <rich:componentControl for="modalAdquiraVendas" attachTo="closeAdquiraVendas" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form ajaxSubmit="true">
            <h:panelGrid>
                <h:panelGroup layout="block" style="margin: 10 20 10 20;">
                    <h:outputText
                            styleClass="texto-size-16 texto-bold texto-font texto-cor-cinza"
                            value="Você concorda com à ativação do módulo de vendas online pelas condições de negociação descritas abaixo?"/>

                    <h:panelGrid id="panelProdutos" style="margin-top: 3vh;" columns="1"
                                 columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="texto-size-16 texto-bold texto-font texto-cor-cinza"
                                      value="#{AdquiraVendasOnlineControle.descProdutoPacto}"/>
                    </h:panelGrid>

                    <h:panelGrid id="panelDetalheProdutos" style="margin-top: 1vh;" columns="2" columnClasses="classEsquerda, classDireita" width="100%">
                        <h:outputText styleClass="texto-size-16 texto-bold texto-font texto-cor-cinza-3"
                                      value="ADESÃO:"/>
                            <h:outputText styleClass="texto-size-14 texto-bold texto-font texto-cor-verde" style="margin-left: 3px"
                                          value="#{AdquiraVendasOnlineControle.valorAdesaoProdutoPacto}"/>
                            <h:outputText styleClass="texto-size-16 texto-bold texto-font texto-cor-cinza-3"
                                          value="MENSALIDADE:"/>
                            <h:outputText styleClass="texto-size-14 texto-bold texto-font texto-cor-verde" style="margin-left: 3px"
                                          value="#{AdquiraVendasOnlineControle.valorMensalidadeProdutoPacto}"/>
                    </h:panelGrid>
                </h:panelGroup>

                <h:panelGroup style="position: absolute; width: 100%; bottom: 7%; text-align: center">
                    <a4j:commandLink
                            accesskey="9"
                            value="Concordo"
                            reRender="panelConfirmacaoPagamento, mensagemAdquiraVendasOnline"
                            oncomplete="Richfaces.showModalPanel('panelUsuarioSenhaAdquiraVendasOnline')"
                            styleClass="botoes nvoBt botaoPrimarioGrande"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelUsuarioSenhaAdquiraVendasOnline" styleClass="novaModal"
                     autosized="true" shadowOpacity="true" width="450" height="250"
                     onshow="document.getElementById('formUsuarioSenhaAdquiraVendasOnline:senhaAdquiraVendasOnline').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmação compra módulo vendas online"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                        id="hidelink9"/>
                <rich:componentControl
                        for="panelUsuarioSenhaAdquiraVendasOnline"
                        attachTo="hidelink9" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formUsuarioSenhaAdquiraVendasOnline">
            <h:panelGrid columns="1" width="100%"
                         columnClasses="colunaCentralizada">
                <h:panelGrid id="panelConfimacaoAdquiraVendasOnline" cellpadding="9"
                             columns="1" width="100%" columnClasses="colunaEsquerda"
                             styleClass="font-size-Em-max">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Código:" />
                        <h:inputText id="codigoUsuarioAdquiraVendasOnline" size="5"
                                     maxlength="7" style="margin-left:5px"
                                     value="#{AdquiraVendasOnlineControle.usuarioResponsavel.codigo}">
                            <a4j:support event="onchange"
                                         focus="formUsuarioSenhaAdquiraVendasOnline:senhaAdquiraVendasOnline"
                                         action="#{AdquiraVendasOnlineControle.consultarResponsavel}"
                                         reRender="usuarioAdquiraVendasOnline, mensagemAdquiraVendasOnline" />
                        </h:inputText>
                        <h:inputText id="autoCompleteHiddenObsClienteEx" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{AdquiraVendasOnlineControle.usuarioResponsavel.username}"/>
                    </h:panelGroup>
                    <h:panelGroup id="usuarioAdquiraVendasOnline">
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Usuário:" />
                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font" style="margin-left:5px"
                                       value="#{AdquiraVendasOnlineControle.usuarioResponsavel.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Senha:" />
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" styleClass="inputTextClean"
                                       style="margin-left:8px"
                                       value="#{AdquiraVendasOnlineControle.usuarioResponsavel.senha}" />
                        <rich:hotKey selector="#senha" key="return"
                                     handler="#{rich:element('loginConfirmacao')}.onclick();return false;" />
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid id="mensagemAdquiraVendasOnline"  columns="2" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%" rendered="#{not empty AdquiraVendasOnlineControle.mensagemDetalhada}">
                        <f:verbatim>
                            <h:outputText value=" " />
                        </f:verbatim>
                    </h:panelGrid>
                    <h:panelGrid columns="1"
                                 width="100%" rendered="#{not empty AdquiraVendasOnlineControle.mensagemDetalhada}">
                        <h:outputText styleClass="mensagem"
                                      value="#{AdquiraVendasOnlineControle.mensagem}" />
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{AdquiraVendasOnlineControle.mensagemDetalhada}" />
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGroup layout="block" styleClass="container-botoes">
                    <a4j:commandLink value="#{msg_bt.btn_confirmar}"
                                     styleClass="botaoPrimario texto-size-14-real"
                                     id="observacaoAdquiraVendasOnline"
                                     reRender="modalAdquiraVendas, formUsuarioSenhaAdquiraVendasOnline, mensagem, mensagemDetalhada, modalFinalizaVendas"
                                     oncomplete="#{AdquiraVendasOnlineControle.msgAlert}"
                                     action="#{AdquiraVendasOnlineControle.permisaoAdqurirModulo}" />
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalFinalizaVendas"  styleClass="novaModal noMargin" shadowOpacity="true"
                     width="747" height="324">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Conclusão negociação vendas online"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
        </f:facet>
        <a4j:form ajaxSubmit="true">
            <h:panelGrid>
                <h:panelGroup layout="block" style="margin: 10 20 10 20;">
                    <h:panelGrid id="panelDescConclusao" style="margin-top: 2vh;" columns="1"
                                 columnClasses="colunaCentralizada" width="100%">
                        <h:outputText
                                styleClass="texto-size-18 texto-bold texto-font texto-cor-verde"
                                value="#{AdquiraVendasOnlineControle.mensagemDetalhada}."/>
                        <rich:spacer width="5px"/>
                        <h:outputText
                                styleClass="texto-size-16 texto-bold texto-font texto-cor-cinza"
                                value="Em breve nosso financeiro irá entrar em contato para concretizar a compra. Enquanto isso você já pode desfrutar do novo módulo de vendas."/>
                        <rich:spacer width="5px"/>
                        <h:outputText
                                styleClass="fa-icon-exclamation-triangle-sign texto-size-16 texto-bold texto-font texto-cor-amarelo"/>
                        <h:outputText
                                      styleClass="texto-size-16 texto-bold texto-font texto-cor-cinza"
                                      value="Para acessar seu novo módulo, basta clicar em sair para que você seja deslogado, e assim que seu login for realizado novamente, você já consegue acessar o \"Gestão de Vendas Online\"."/>
                    </h:panelGrid>
                </h:panelGroup>
                <h:panelGroup style="position: absolute; width: 100%; bottom: 6%; text-align: center">
                    <a4j:jsFunction action="#{LogoutControle.updateCookiesLogado}" name="doLogout" oncomplete="document.location.href = '#{LogoutControle.redirectLogout}'" />
                    <a id="btnLogout" href="#" class="textSmallFlat" style="font-size: 21px" onclick="doLogout();">
                        <h:outputText style="vertical-align: middle;" styleClass="fa-icon-remove"/> Sair
                    </a>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>

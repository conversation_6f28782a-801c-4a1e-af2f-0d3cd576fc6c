<%--
  Created by IntelliJ IDEA.
  User: marcosandre
  Date: 29/10/2020
  Time: 01:17
  To change this template use File | Settings | File Templates.
--%>
<%@include file="/includes/imports.jsp" %>

<h:panelGroup layout="block" styleClass="paginaFontResponsiva"
              style="width: 100%; padding-bottom: 10px; padding-top: 10px; ">
    <h:outputText value="Coloborador #{ColaboradorControle.antigoColaboradorVinculo.pessoa.nome} possui vinculo(s) de  #{ColaboradorControle.tipoVinculoListaColaborador.descricao} com cliente(s). Para inativar ou retirar acesso a empresa � necess�rio retirar esse(s) v�nculo(s)" styleClass="mensagemDetalhada" id="txtColaboradorvinculos"/>
</h:panelGroup>
<rich:dataTable rowClasses="linhaImpar,linhaPar" width="100%"
                id="tblVinculosColaborador"
                rows="15"
                rowKeyVar="status"
                value="#{ColaboradorControle.listaVinculosColaborador}"
                var="vinculo">
    <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>
    <rich:column sortBy="#{vinculo.cliente.matricula}" >
        <f:facet name="header">
            <h:outputText value="matricula"/>
        </f:facet>
        <h:outputText value="#{vinculo.cliente.matricula}"/>
    </rich:column>

    <rich:column sortBy="#{vinculo.cliente.pessoa.nome}" >
        <f:facet name="header">
            <h:outputText value="Nome"/>
        </f:facet>
        <h:outputText  value="#{vinculo.cliente.pessoa.nome}"/>
    </rich:column>
    <rich:column  sortBy="#{vinculo.cliente.situacao}">
        <f:facet name="header">
            <h:outputText value="Situacao"/>
        </f:facet>
        <h:outputText  value="#{vinculo.cliente.situacao_Apresentar}"/>
    </rich:column>

    <rich:column>
        <f:facet name="header">
            <h:outputText value="Empresa"/>
        </f:facet>
        <h:outputText value="#{vinculo.colaborador.empresa.nome}"/>
    </rich:column>
</rich:dataTable>
<rich:datascroller id="scVinculosColaborador" reRender="tblVinculosColaborador"
                   maxPages="30" for="tblVinculosColaborador"/>

<h:panelGrid columns="1" cellpadding="10" style="padding-top: 10px">
    <h:panelGrid columns="2"  style="padding-top: 10px">
        <h:outputText rendered="#{ColaboradorControle.listaColaboradoresTrocarVinculosSize > 1}" styleClass="tituloCampos" value="Informe o novo #{ColaboradorControle.tipoVinculoListaColaborador.descricao} do(s) cliente(s) acima:"/>
        <h:panelGroup rendered="#{ColaboradorControle.listaColaboradoresTrocarVinculosSize > 1}">
            <h:selectOneMenu id="colaboradorVinculo" value="#{ColaboradorControle.novoColaboradorVinculo.codigo}">
                <f:selectItems value="#{ColaboradorControle.listaColaboradoresTrocarVinculos}"/>
            </h:selectOneMenu>
            <a4j:commandButton id="atualizar_colaborador"
                               action="#{ColaboradorControle.atualizarListaColaboradoresTrocarVinculo}"
                               style="vertical-align: middle;" image="imagens/atualizar.png"
                               ajaxSingle="true" reRender="form:colaboradorVinculo"/>
        </h:panelGroup>
        <h:panelGroup rendered="#{ColaboradorControle.listaColaboradoresTrocarVinculosSize == 1}">
            <h:outputText value="Empresa n�o possui outro #{ColaboradorControle.tipoVinculoListaColaborador.descricao}, impossibilitando a transfer�ncia. Caso queira realizar a transfer�ncia, atualize ao menos um colaborador para esse tipo e tente realizar essa opera��o novamente." styleClass="mensagemDetalhada" id="txtSemColaboradores"/>
        </h:panelGroup>
    </h:panelGrid>
</h:panelGrid>

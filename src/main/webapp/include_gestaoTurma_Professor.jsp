<%@include file="/includes/imports.jsp" %>
<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>
  Date: 10/04/2019
  Time: 13:27
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<h:panelGroup layout="block" id="painelSelecionados" styleClass="margin-box"
              rendered="#{GestaoTurmaControle.manutencaoProfessor}">

    <a4j:commandLink id="btnAbrirModal"
                     title="Abrir Modal"
                     styleClass="botaoPrimario texto-size-12"
                     style="float: right; margin-bottom: 10px;"
                     value="TRANSFERIR"
                     reRender="modalTransferencia"
                     oncomplete="Richfaces.showModalPanel('modalTransferencia')">
    </a4j:commandLink>

    <h:panelGrid styleClass="tituloPainelPesquisa cinza">
        <h:panelGroup>

            <h:selectOneMenu
                    value="#{GestaoTurmaControle.modalidadeSelecionada}"
                    styleClass="newComboBox noBorderRight"
                    style="vertical-align:middle; float:left;">

                <f:selectItems
                        value="#{GestaoTurmaControle.selectItensModalidade}"/>
                <a4j:support event="onchange"
                             action="#{GestaoTurmaControle.obterDadosGestaoTurma}"
                             reRender="panelConteudo, painelDadosTurma"/>
            </h:selectOneMenu>

            <h:selectOneMenu
                    value="#{GestaoTurmaControle.professorSelecionado}"
                    styleClass="newComboBox noBorderRight"
                    style="vertical-align:middle; float:left;">

                <f:selectItems
                        value="#{GestaoTurmaControle.selectItensProfessor}"/>
                <a4j:support event="onchange"
                             action="#{GestaoTurmaControle.obterDadosGestaoTurma}"
                             reRender="panelConteudo, painelDadosTurma"/>
            </h:selectOneMenu>

            <h:selectOneMenu
                    value="#{GestaoTurmaControle.turmaSelecionada}"
                    styleClass="newComboBox"
                    style="vertical-align:middle; float:left;">

                <f:selectItems
                        value="#{GestaoTurmaControle.selectItensTurma}"/>
                <a4j:support event="onchange"
                             action="#{GestaoTurmaControle.obterDadosGestaoTurma}"
                             reRender="panelConteudo, painelDadosTurma"/>
            </h:selectOneMenu>

            <h:outputText
                    style="font-size: 14px; color: #29ABE2; float:left; margin-left: 1%; margin-top: 13px;"
                    value="Somente turmas não vigentes"/>
            <a4j:commandLink
                    rendered="#{GestaoTurmaControle.situacaoSelecionada eq 'AT'}"
                    styleClass="fa-icon-toggle-off"
                    action="#{GestaoTurmaControle.atualizaSituacao}"
                    reRender="painelSelecionados, panelConteudo, painelDadosTurma"
                    style="font-size: 16px; margin-left: 5px; margin-top: 13px;float: left"/>

            <a4j:commandLink
                    rendered="#{GestaoTurmaControle.situacaoSelecionada eq 'IN'}"
                    styleClass="fa-icon-toggle-on"
                    action="#{GestaoTurmaControle.atualizaSituacao}"
                    reRender="painelSelecionados, panelConteudo, painelDadosTurma"
                    style="font-size: 16px; margin-left: 5px; margin-top: 13px;float: left"/>


        </h:panelGroup>
    </h:panelGrid>
    <h:panelGroup layout="block" id="groupDadosTurma">

        <rich:dataTable
                id="painelDadosTurma" width="100%" border="0"
                cellspacing="0" cellpadding="2"
                styleClass="tabelaDados semZebra"
                value="#{GestaoTurmaControle.listaHorarioTurma}"
                var="horarioTurma">

            <rich:column>
                <f:facet name="header">

                    <h:selectBooleanCheckbox id="selecionarHorarioTodos"
                                             value="#{GestaoTurmaControle.marcarTodosHorario}"
                                             title="Selecionar todos horarios para transferencia de professor">
                        <a4j:support event="onclick"
                                     action="#{GestaoTurmaControle.marcarTodosHorarios}"
                                     reRender="painelDadosTurma, painelSelecionados, selecionarHorario"/>
                    </h:selectBooleanCheckbox>


                </f:facet>
                <h:selectBooleanCheckbox id="selecionarHorario"
                                         value="#{horarioTurma.horarioSelecionado}"
                                         title="Selecionar horario para transferencia de professor">
                    <a4j:support event="onclick"
                                 action="#{GestaoTurmaControle.checkHorario}"
                                 reRender="painelDadosTurma, painelSelecionados, selecionarHorarioTodos"/>
                </h:selectBooleanCheckbox>

            </rich:column>

            <rich:column sortBy="#{horarioTurma.identificadorTurma}">
                <f:facet name="header">
                    <h:outputText value="ID. TURMA"/>
                </f:facet>
                <h:outputText value="#{horarioTurma.identificadorTurma}"
                              style="text-transform: capitalize;"/>
            </rich:column>

            <rich:column sortBy="#{horarioTurma.professor.pessoa.nomeAbreviado}">
                <f:facet name="header">
                    <h:outputText value="PROFESSOR"/>
                </f:facet>
                <h:outputText value="#{horarioTurma.professor.pessoa.nomeAbreviado}"
                              style="text-transform: capitalize;"/>
            </rich:column>

            <rich:column sortBy="#{horarioTurma.diaSemana_Apresentar}">
                <f:facet name="header">
                    <h:outputText value="DIA DA SEMANA"/>
                </f:facet>
                <h:outputText value="#{horarioTurma.diaSemana_Apresentar}"
                              style="text-transform: capitalize;"/>
            </rich:column>
            <rich:column sortBy="#{horarioTurma.horaInicial}">
                <f:facet name="header">
                    <h:outputText value="HORARIO"/>
                </f:facet>
                <h:outputText
                        value="#{horarioTurma.horaInicial} - #{horarioTurma.horaFinal}"
                        style="text-transform: capitalize;"/>
            </rich:column>
        </rich:dataTable>
    </h:panelGroup>
    <h:outputText value="Total de itens: "/>
    <h:outputText value="#{GestaoTurmaControle.listaHorarioTurmaSize} | "
                  style="text-transform: capitalize;"/>
    <h:outputText value="Selecionados: "/>
    <h:outputText
            value="#{(GestaoTurmaControle.marcarTodosHorario ? GestaoTurmaControle.listaConsultaSize : GestaoTurmaControle.horariosSelecionadosSize)}"
            style="text-transform: capitalize;"/>
</h:panelGroup>
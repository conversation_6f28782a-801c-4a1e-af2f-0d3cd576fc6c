<%-- 
    Document   : include_box_usuario_inline
    Created on : 17/01/2012, 14:47:37
    Author     : Waller
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@include file="/includes/imports.jsp" %>
<c:set var="root" value="${pageContext.request.contextPath}" scope="request" />
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<h:panelGrid style="font-family:Arial, Helvetica, sans-serif;
             font-size: 8pt;font-weight: bold;" styleClass="tituloCamposTop" columns="10">

    <h:outputText value="Seja bem vindo, #{LoginControle.usuario.nome}!"/>

    <a4j:commandLink rendered="#{LoginControle.usuario.permiteAlterarPropriaSenha}" type="button"
                     onclick="abrirPopup('#{root}/faces/alterarSenhaClienteForm.jsp', 'AlterarSenha', 410, 350);"
                     title="Alterar senha" styleClass="text2"
                     style="valign:middle;cursor:pointer;">
        <h:graphicImage style="border:none;" value="/images/icon_chave.png"/>
    </a4j:commandLink>
    |
    <c:if test="${LoginControle.usuario.administrador}">
        <h:outputText value="#{LoginControle.instanceName}"/>        
        <b style="vertical-align: top;" class="textsmall">Mem.Livre: <%=Runtime.getRuntime().freeMemory() / 1048576 + " MB"%></b>
    |
    </c:if>
    
    <h:outputText value="IP.: #{LoginControle.ipCliente}"/>
    <h:commandLink
        value="#{SuperControle.versaoSistema}"
        title="#{SuperControle.dataVersaoComoString}" />

    <h:outputLink value="#{SuperControle.urlWikiVersaoAtual}"
                  title="Clique e saiba mais: Novos Recursos da Versão #{SuperControle.versaoSistema}" target="_blank">
        <h:graphicImage styleClass="linkWiki" style="margin:0 0 0 0;padding:0 0 0 0;" url="/imagens/wiki_link2.gif"/>
    </h:outputLink>

    <c:if test="${LoginControle.empresa.codigo != 0}">
        |
        <h:panelGroup layout="block">
            <c:if test="${!empty LoginControle.empresa.nome}">
                <h:outputText value="Empresa: #{LoginControle.empresa.nome}"/>
            </c:if>            
        </h:panelGroup>
    </c:if>
</h:panelGrid>








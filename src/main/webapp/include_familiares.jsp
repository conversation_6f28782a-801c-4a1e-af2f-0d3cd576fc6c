<%@include file="includes/imports.jsp" %>

<style>
   .familiar{
       display: block;
       height: 50px;
       padding-left: 10px;
       margin-top: 10px;
   }
    .familiar .fotopessoa{
        border: 2px solid #094771;
    }
    .nomeFamiliar{
        vertical-align: top;
        text-transform: capitalize;
        color: #094771;
        font-weight: bold;
        display: block;
        margin-top: 5px;
        margin-left: 5px;
    }
    .dadosFamiliar{
        margin-top: 5px;
        margin-left: 5px;
        display: block;
        color: #777777;
        text-transform: capitalize;
    }
    .btnsituacao{
        margin-right: 10px;
    }
</style>

<h:panelGroup layout="block" styleClass="grupoMenuLateral" id="caixaexternafamilia">
    <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
        <i class="fa-icon-group"></i> Fam�lia
    </h:panelGroup>


    <a4j:repeat value="#{FamiliaresControle.familiares}" var="f">

        <h:outputLink value="clienteNav.jsp?page=cliente&matricula=#{f.codigoMatricula}" target="_self">
            <h:panelGroup layout="block" styleClass="familiar">
                <div style="position: relative; display: inline-block">
                    <h:graphicImage styleClass="fotopessoa" style="width:43px;height:43px; border-radius: 50%;"
                                    url="#{f.urlFoto}">
                    </h:graphicImage>

                    <div style="position: absolute; bottom: -5px; left: 0; width: 50px; display: flex">
                        <h:graphicImage styleClass="btnsituacao" id="alunoAtivo" value="/images/botaoAtivo.png" rendered="#{f.ativo}"   />
                        <h:graphicImage styleClass="btnsituacao" id="alunoInativo" value="/images/botaoInativo.png" rendered="#{f.inativo}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoVisitante" value="/images/botaoVisitante.png" rendered="#{f.visitante}"   />
                        <h:graphicImage styleClass="btnsituacao" id="alunoTrancado" value="/images/botaoTrancamento.png" rendered="#{f.trancado}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoNormal" value="/images/botaoNormal.png" rendered="#{f.ativoNormal}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoTrancadoVencido" value="/images/botaoTrancadoVencido.png" rendered="#{f.trancadoVencido}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoFreePass" value="/images/botaoFreePass.png" rendered="#{f.visitanteFreePass}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoAulaAvulsa" value="/images/botaoAulaAvulsa.png" rendered="#{f.visitanteAulaAvulsa}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoDiaria" value="/images/botaoDiaria.png" rendered="#{f.visitanteDiaria}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoCancelado" value="/images/botaoCancelamento.png" rendered="#{f.inativoCancelamento}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoDesistente" value="/images/botaoDesistente.png" rendered="#{f.inativoDesistente}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoAVencer" value="/images/botaoAvencer.png" rendered="#{f.ativoAvencer}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoVencido" value="/images/botaoVencido.png" rendered="#{f.inativoVencido}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoCarencia" value="/images/botaoCarencia.png" rendered="#{f.ativoCarencia}"  />
                        <h:graphicImage styleClass="btnsituacao" id="alunoAtestado" value="/images/botaoAtestado.png" rendered="#{f.ativoAtestado}"  />
                    </div>
                </div>



                <div style="display: inline-block;vertical-align: top;">
                <span class="nomeFamiliar">
                    <h:outputText value="#{f.primeiroNome} "/>
                </span>
                    <span class="dadosFamiliar">
                        <h:outputText rendered="#{f.vencimento ne null}" value="Venc. #{f.vencimentoApresentar}"/>
                    </span>
                </div>
            </h:panelGroup>
        </h:outputLink>
    </a4j:repeat>

    <h:panelGroup layout="block" styleClass="grupoMenuItem">
        <a4j:commandLink id="verMaisFamilia" reRender="mdlNovoMembroFamilia"
                     styleClass="linkFuncionalidade" style="margin-left: 10px"
                     oncomplete="Richfaces.showModalPanel('mdlNovoMembroFamilia')">
                      &nbsp Gerenciar fam�lia
        </a4j:commandLink>
        <h:outputLink styleClass="linkWiki"
                      value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                      title="Clique e saiba mais: Gerenciar Fam�lia"
                      target="_blank">
            <i id="wikiNovoContratoMenuIcon" class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
    </h:panelGroup>

</h:panelGroup>
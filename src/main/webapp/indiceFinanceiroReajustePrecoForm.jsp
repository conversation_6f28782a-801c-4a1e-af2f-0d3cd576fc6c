<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="script/vanilla-masker.min.js"></script>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <title><h:outputText value="Índice financeiro para reajuste de preços" /></title>
    <c:set var="titulo" scope="session" value="Índice Financeiro"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Indice_Financeiro"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">

            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>

            <h:commandLink action="#{IndiceFinanceiroReajustePrecoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">



                    <h:outputText styleClass="tituloCampos" value="Tipo índice:" />
                    <h:panelGroup>
                        <h:selectOneMenu id="comboTipoIndice" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{IndiceFinanceiroReajustePrecoControle.indiceFinanceiroReajustePrecoVO.tipoIndice}">
                            <f:selectItems value="#{IndiceFinanceiroReajustePrecoControle.listaSelectItemTipoIndice}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Mês:" />
                    <h:panelGroup>
                        <h:selectOneMenu id="comboMes" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{IndiceFinanceiroReajustePrecoControle.indiceFinanceiroReajustePrecoVO.mes}">
                            <f:selectItems value="#{IndiceFinanceiroReajustePrecoControle.listaSelectItemMes}" />
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Ano:" />
                    <h:panelGroup>
                        <h:inputText size="15" id="inputAno" maxlength="4" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{IndiceFinanceiroReajustePrecoControle.indiceFinanceiroReajustePrecoVO.ano}" />
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Percentual acumulado:" />
                    <h:panelGroup>
                        <h:inputText  onkeypress="return Tecla(event);" size="20" maxlength="10" onblur="blurinput(this);"
                                      id="idPercentualIndiceFinanceiro"
                                      onfocus="focusinput(this);" styleClass="form"
                                      value="#{IndiceFinanceiroReajustePrecoControle.indiceFinanceiroReajustePrecoVO.percentualAcumulado}" >
                            <f:converter converterId="FormatadorNumerico" />
                        </h:inputText>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Aplicar reajuste para:" />
                    <h:panelGroup>
                      <h:selectBooleanCheckbox style="vertical-align: middle;" styleClass="campos" value="#{IndiceFinanceiroReajustePrecoControle.indiceFinanceiroReajustePrecoVO.aplicarreajusterenovacaocontratorecorrencia}"/>
                        <h:outputText style="vertical-align: middle;" styleClass="tituloCampos" value="Renovação automática de contrato" />
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Tipo de plano:" />
                    <h:panelGroup>
                        <h:selectOneMenu id="comboTipoPlano" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                         value="#{IndiceFinanceiroReajustePrecoControle.indiceFinanceiroReajustePrecoVO.tipoPlano}">
                            <f:selectItems value="#{IndiceFinanceiroReajustePrecoControle.listaSelectItemTipoPlano}" />
                        </h:selectOneMenu>
                    </h:panelGroup>

                </h:panelGrid>
                <script>
                    VMasker(document.getElementById("form:idPercentualIndiceFinanceiro")).maskMoney({
                        precision: 2,
                        separator: ',',
                        delimiter: '.',
                        zeroCents: false
                    });
                    VMasker(document.getElementById("form:inputMes")).maskNumber({
                    });
                    VMasker(document.getElementById("form:inputAno")).maskNumber({
                    });
                </script>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">

                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                            <h:panelGroup>
                                <a4j:commandButton id="novo" immediate="true" action="#{IndiceFinanceiroReajustePrecoControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec" />
                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="salvar" action="#{IndiceFinanceiroReajustePrecoControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt" />
                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="excluir" action="#{IndiceFinanceiroReajustePrecoControle.confirmarExcluir}" oncomplete="#{IndiceFinanceiroReajustePrecoControle.msgAlert}" reRender="form,mdlMensagemGenerica" value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo" />
                                <f:verbatim>
                                    <h:outputText value="    " />
                                </f:verbatim>
                                <a4j:commandButton id="consultar" immediate="true" action="#{IndiceFinanceiroReajustePrecoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec" />
                                <rich:spacer width="15px"/>
                                <a4j:commandLink  action="#{IndiceFinanceiroReajustePrecoControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                                    oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                    title="Visualizar Log"
                                                  style="display: inline-block; padding: 8px 15px; margin-left: -6px;"
                                                    styleClass="botoes nvoBt btSec">
                                    <i style="text-decoration: none" class="fa-icon-list"/>
                                </a4j:commandLink>

                            </h:panelGroup>

                    </h:panelGrid>
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" " />
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{IndiceFinanceiroReajustePrecoControle.sucesso}" image="./imagens/sucesso.png" />
                        <h:commandButton rendered="#{IndiceFinanceiroReajustePrecoControle.erro}" image="./imagens/erro.png" />
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{IndiceFinanceiroReajustePrecoControle.mensagem}" />
                            <h:outputText styleClass="mensagemDetalhada" value="#{IndiceFinanceiroReajustePrecoControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:inputMes").focus();
</script>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="includes/include_import_minifiles.jsp"%>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script src="bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Resumo #{BIInadimplenciaControle.descricaoTitulo}"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <html>
            <body onload="fireElement('form:botaoAtualizarPagina')"/>
            <h:form id="form" >
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
                <c:set var="titulo" scope="session" value="BI - Resumo ${BIInadimplenciaControle.descricaoTitulo} Total Itens: ${BIInadimplenciaControle.qtdItensLista}"/>
                <c:set var="urlWiki" scope="session" value="semWiki"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup layout="block">
                            <a4j:commandLink id="exportarExcel"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty BIInadimplenciaControle.listaApresentarParcelas}"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{BIInadimplenciaControle.listaApresentarParcelas}"/>
                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="#{BIInadimplenciaControle.atributosExportar}"/>
                                <f:attribute name="prefixo" value="ListaParcelas"/>
                                <f:attribute name="titulo" value="Resumo #{BIInadimplenciaControle.descricaoTitulo}"/>
                                <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                            </a4j:commandLink>
                            <%--BOTÃO PDF--%>
                            <a4j:commandLink id="exportarPdf"
                                               style="margin-left: 8px;"
                                               actionListener="#{ExportadorListaControle.exportar}"
                                               rendered="#{not empty BIInadimplenciaControle.listaApresentarParcelas}"
                                               oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                               accesskey="2" styleClass="linkPadrao">
                                <f:attribute name="lista" value="#{BIInadimplenciaControle.listaApresentarParcelas}"/>
                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="#{BIInadimplenciaControle.atributosExportar}"/>
                                <f:attribute name="prefixo" value="ListaParcelas"/>
                                <f:attribute name="titulo" value="Resumo #{BIInadimplenciaControle.descricaoTitulo}"/>
                                <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                    <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                    value="#{BIInadimplenciaControle.listaApresentarParcelas}" rows="50" var="resumoPessoa" rowKeyVar="status">
                        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                        <rich:column sortBy="#{resumoPessoa.matricula}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="MATRÍCULA" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.matricula}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.nome}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="NOME" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.nome}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.dataMatricula}" styleClass="col-text-align-left" headerClass="col-text-align-left"
                                     rendered="#{BIInadimplenciaControle.apresentarDataMatricula}">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DT. MATRÍCULA" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.dataMatricula_Apresentar}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.situacaoParcela_Apresentar}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="SITUAÇÃO PARCELA ATUAL" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"   value="#{resumoPessoa.situacaoParcela_Apresentar}" title="#{resumoPessoa.dataCancelamentoParcela_Apresentar}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.situacaoParcelaDiaPagamento_Apresentar}" styleClass="col-text-align-left" headerClass="col-text-align-left"
                                     rendered="#{BIInadimplenciaControle.apresentarSituacaoDia}">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="SIT. PARCELA ATÉ DIA #{BIInadimplenciaControle.filtroDiaPagamento}" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.situacaoParcelaDiaPagamento_Apresentar}" />
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.contrato}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="CONTRATO" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.contrato}">
                            </h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.telefone}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="TELEFONE(S)" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.telefone}">
                            </h:outputText>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.descricaoParcela}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="PARCELA" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.descricaoParcela}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.dataVencimentoParcela}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DT. VENCIMENTO" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.dataVencimento_Apresentar}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.dataPagamentoParcela}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="DT. PAGAMENTO" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.dataPagamento_Apresentar}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.nrtentativas}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="NR. TENTATIVAS" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.nrtentativas}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.motivoretorno}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="MOTIVO RETORNO" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.motivoretorno}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.valorParcela}" styleClass="col-text-align-left" headerClass="col-text-align-left">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="VALOR" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{BIInadimplenciaControle.empresaLogado.moeda} #{resumoPessoa.valorParcela_Apresentar}"/>
                        </rich:column>
                        <rich:column sortBy="#{resumoPessoa.empresa}" styleClass="col-text-align-left"
                                     headerClass="col-text-align-left"
                                     rendered="#{BIInadimplenciaControle.permiteConsultarTodasEmpresas}">
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="EMPRESA" />
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"   value="#{resumoPessoa.empresa}"/>
                        </rich:column>
                        <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center" >
                            <a4j:commandLink styleClass="linkPadrao texto-size-16-real texto-cor-azul"
                                             action="#{BIInadimplenciaControle.irParaTelaCliente}"
                                             oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                <f:param name="state" value="AC"/>
                                <i class="fa-icon-search"></i>
                            </a4j:commandLink>
                        </rich:column>
                    </rich:dataTable>
                    <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center" for="form:tabelaRes" maxPages="10" id="sctabelaRes" />
                </h:panelGrid>
            </h:form>
        </body>
    </html>
</h:panelGrid>
</f:view>


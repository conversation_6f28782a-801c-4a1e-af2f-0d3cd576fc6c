<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_HorarioTurma_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_HorarioTurma_tituloForm}"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_HorarioTurma_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/> 
                    <h:selectOneMenu styleClass="campos" id="consulta" required="true" value="#{HorarioTurmaControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{HorarioTurmaControle.tipoConsultaCombo}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" styleClass="campos" value="#{HorarioTurmaControle.controleConsulta.valorConsulta}"/>
                    <h:commandButton id="consultar" type="submit" styleClass="botoes" value="#{msg_bt.btn_consultar}" action="#{HorarioTurmaControle.irPaginaInicial}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="2"/>
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{HorarioTurmaControle.apresentarResultadoConsulta}" binding="#{HorarioTurmaControle.apresentarLinha}">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false" binding="#{HorarioTurmaControle.apresentarPrimeiro}" action="#{HorarioTurmaControle.irPaginaInicial}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false" binding="#{HorarioTurmaControle.apresentarAnterior}" action="#{HorarioTurmaControle.irPaginaAnterior}"/> 
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{HorarioTurmaControle.paginaAtualDeTodas}" rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false" binding="#{HorarioTurmaControle.apresentarPosterior}" action="#{HorarioTurmaControle.irPaginaPosterior}"/> 
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false" binding="#{HorarioTurmaControle.apresentarUltimo}" action="#{HorarioTurmaControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{HorarioTurmaControle.listaConsulta}" rendered="#{HorarioTurmaControle.apresentarResultadoConsulta}" rows="10" var="horarioTurma">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_HorarioTurma_codigo}"/>
                        </f:facet>
                        <h:commandLink action="#{HorarioTurmaControle.editar}" id="codigo" value="#{horarioTurma.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_HorarioTurma_turma}"/>
                        </f:facet>
                        <h:commandLink action="#{HorarioTurmaControle.editar}" id="turma" value="#{horarioTurma.turma}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_HorarioTurma_identificador}"/>
                        </f:facet>
                        <h:commandLink action="#{HorarioTurmaControle.editar}" id="identificador" value="#{horarioTurma.identificador}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_HorarioTurma_horaInicial}"/>
                        </f:facet>
                        <h:commandLink action="#{HorarioTurmaControle.editar}" id="horaInicial" value="#{horarioTurma.horaInicial}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_HorarioTurma_professor}"/>
                        </f:facet>
                        <h:commandLink action="#{HorarioTurmaControle.editar}" id="professor" value="#{horarioTurma.professor.situacao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_HorarioTurma_ambiente}"/>
                        </f:facet>
                        <h:commandLink action="#{HorarioTurmaControle.editar}" id="ambiente" value="#{horarioTurma.ambiente.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_HorarioTurma_nivelTurma}"/>
                        </f:facet>
                        <h:commandLink action="#{HorarioTurmaControle.editar}" id="nivelTurma" value="#{horarioTurma.nivelTurma.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_HorarioTurma_situacao}"/>
                        </f:facet>
                        <h:commandLink action="#{HorarioTurmaControle.editar}" id="situacao" value="#{horarioTurma.situacao_Apresentar}"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:commandButton action="#{HorarioTurmaControle.editar}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" alt="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{HorarioTurmaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{HorarioTurmaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{HorarioTurmaControle.novo}" value="#{msg_bt.btn_novo}" styleClass="botoes" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:valorConsulta").focus();
</script>
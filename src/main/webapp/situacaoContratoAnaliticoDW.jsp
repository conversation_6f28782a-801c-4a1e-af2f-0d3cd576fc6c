<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><script type="text/javascript" language="javascript" src="./script/script.js"></script></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Relatório de Cliente Analítico"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <a4j:form id="form">
        <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td height="77" align="left" valign="top" class="bgtop">
                    <jsp:include page="topoReduzido.jsp"/>
                    <jsp:include page="include_head.jsp" flush="true"/>
                    <h:panelGrid columns="1" width="100%" >
                        <h:panelGrid columns="1" style=" background-image:url('../imagens/fundoBarraTopo.png');" columnClasses="colunaCentralizada" width="100%">
                            <h:outputText styleClass="tituloFormulario" style="text-align=left;" value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_tituloLista}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </td>
            </tr>
            <tr>
                <td align="center" valign="top"  >
                    <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0" >
                        <tr>
                            <td align="left" valign="top" style="padding-top:6px; padding-left:15px;">
                                <!-- inicio box -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">
                                    <tr>
                                        <td align="left" valign="top">
                                            <rich:tabPanel id="tabPanelFiltros">
                                                <rich:tab styleClass="titulo3" label="Data" >
                                                    <h:panelGrid width="100%"  style="border:0;" id="panelFiltrosUtilizadosData">
                                                        <h:outputText styleClass="tituloCamposAzul" value="Veja os Filtros Utilizados "/>
                                                        <rich:toolTip
                                                            onclick="true"
                                                            followMouse="true"
                                                            direction="top-right"
                                                            style=" background-color:#cedfff; border-color:#000000;text-align:left;"
                                                            showDelay="500"
                                                            >
                                                            ${SituacaoContratoAnaliticoDWControle.filtros}
                                                        </rich:toolTip>
                                                    </h:panelGrid>
                                                    <h:panelGrid columns="1">
                                                        <h:panelGroup rendered="#{SituacaoContratoAnaliticoDWControle.usuario.administrador}">
                                                            <h:outputText styleClass="titulo3" value="#{msg_aplic.prt_MatriculaAlunoHorarioTurma_empresa}" />
                                                            <rich:spacer width="10px" />
                                                            <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{SituacaoContratoAnaliticoDWControle.situacaoContratoAnaliticoDW.empresa.codigo}" >
                                                                <f:selectItems  value="#{SituacaoContratoAnaliticoDWControle.listaSelectItemEmpresa}" />
                                                            </h:selectOneMenu>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <h:outputText styleClass="titulo3" value="Data"/>
                                                            <rich:spacer width="5px"/>
                                                            <h:panelGroup>
                                                                <rich:calendar id="dataFinal"
                                                                               value="#{SituacaoContratoAnaliticoDWControle.dataFinal}"
                                                                               inputSize="10"
                                                                               inputClass="form"
                                                                               oninputblur="blurinput(this);"
                                                                               oninputfocus="focusinput(this);"
                                                                               oninputchange="return validar_Data(this.id);"
                                                                               datePattern="dd/MM/yyyy"
                                                                               enableManualInput="true"
                                                                               zindex="2"
                                                                               showWeeksBar="false" />
                                                                <h:message for="dataFinal"  styleClass="mensagemDetalhada"/>
                                                            </h:panelGroup>
                                                            <rich:spacer width="10px" />
                                                            <a4j:commandButton id="consultar" value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_consultar}"
                                                                               title="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_consultar}"
                                                                               reRender="form:panelRelatorioCliente, form:tabPanelFiltros"
                                                                               action="#{SituacaoContratoAnaliticoDWControle.consultarClientesPorData}"
                                                                               image="../imagens/botaoConsultar.png"/>
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <h:panelGrid id="panelMensagem"columns="3" width="100%" styleClass="tabMensagens">
                                                        <h:panelGrid columns="1" width="100%">
                                                            <h:outputText styleClass="mensagem"  value="#{SituacaoContratoAnaliticoDWControle.mensagem}"/>
                                                            <h:outputText styleClass="mensagemDetalhada" value="#{SituacaoContratoAnaliticoDWControle.mensagemDetalhada}"/>
                                                        </h:panelGrid>
                                                    </h:panelGrid>
                                                </rich:tab>
                                                <rich:tab styleClass="titulo3" label="Situação">
                                                    <h:panelGrid width="100%" style="border:0;" id="panelFiltrosUtilizadosSituacao">
                                                        <h:outputText
                                                            styleClass="tituloCamposAzul"
                                                            value="Veja os Filtros Utilizados "/>
                                                        <rich:spacer width="10px" />
                                                        <rich:toolTip
                                                            onclick="true"
                                                            followMouse="true"
                                                            direction="top-right"
                                                            style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                            showDelay="500" >
                                                            ${SituacaoContratoAnaliticoDWControle.filtros}
                                                        </rich:toolTip>
                                                    </h:panelGrid>
                                                    <rich:dataGrid  width="100%" id="resultadoConsultaSituacao" columnClasses="semBorda" styleClass="semBorda"
                                                                    value="#{SituacaoContratoAnaliticoDWControle.listaSituacao}"
                                                                    columns="#{SituacaoContratoAnaliticoDWControle.nrColunaSituacao}"
                                                                    elements="#{SituacaoContratoAnaliticoDWControle.tamanhoListaSituacao}" var="situacao">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="titulo3" value="Filtrar Por: "/>
                                                        </f:facet>
                                                        <h:panelGrid columns="2">
                                                            <h:selectBooleanCheckbox value="#{situacao.marcado}"/>
                                                            <h:outputText styleClass="titulo3" value="#{situacao.nome_Apresentar}"/>
                                                        </h:panelGrid>
                                                    </rich:dataGrid>
                                                </rich:tab>
                                                <rich:tab styleClass="titulo3" label="Plano">
                                                    <h:panelGrid width="100%" style="border:0;" id="panelFiltrosUtilizadosPlano">
                                                        <h:outputText
                                                            styleClass="tituloCamposAzul"
                                                            value="Veja os Filtros Utilizados: "/>
                                                        <rich:spacer width="10px" />
                                                        <rich:toolTip
                                                            onclick="true"
                                                            followMouse="true"
                                                            direction="top-right"
                                                            style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                            showDelay="500" >
                                                            ${SituacaoContratoAnaliticoDWControle.filtros}
                                                        </rich:toolTip>
                                                    </h:panelGrid>
                                                    <rich:dataGrid  width="100%" id="resultadoConsultaPlano" columnClasses="semBorda" styleClass="semBorda"
                                                                    value="#{SituacaoContratoAnaliticoDWControle.listaPlano}"
                                                                    columns="#{SituacaoContratoAnaliticoDWControle.nrColunaPlano}"
                                                                    elements="#{SituacaoContratoAnaliticoDWControle.tamanhoListaPlano}" var="plano">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="titulo3" value="Filtrar Por: "/>
                                                        </f:facet>
                                                        <h:panelGrid columns="2">
                                                            <h:selectBooleanCheckbox value="#{plano.marcado}"/>
                                                            <h:outputText styleClass="titulo3" value="#{plano.nome}"/>
                                                        </h:panelGrid>
                                                    </rich:dataGrid>
                                                </rich:tab>
                                                <rich:tab styleClass="titulo3" label="Vínculo de Carteira">
                                                    <h:panelGrid width="100%" style="border:0;" id="panelFiltrosUtilizadosVinculoCarteira">
                                                        <h:outputText
                                                            styleClass="tituloCamposAzul"
                                                            value="Veja os Filtros Utilizados "/>
                                                        <rich:spacer width="10px" />
                                                        <rich:toolTip
                                                            onclick="true"
                                                            followMouse="true"
                                                            direction="top-right"
                                                            style=" background-color:#cedfff; border-color:#000000;text-align: left;"
                                                            showDelay="500" >
                                                            ${SituacaoContratoAnaliticoDWControle.filtros}
                                                        </rich:toolTip>
                                                    </h:panelGrid>
                                                    <rich:dataGrid  width="100%" id="resultadoConsultaConsultor" columnClasses="semBorda" styleClass="semBorda"
                                                                    value="#{SituacaoContratoAnaliticoDWControle.listaVinculoCarteira}"
                                                                    columns="#{SituacaoContratoAnaliticoDWControle.nrColunaVinculoCarteira}"
                                                                    elements="#{SituacaoContratoAnaliticoDWControle.tamanhoListaVinculoCarteira}" var="consultor">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="titulo3" value="Filtrar Por: "/>
                                                        </f:facet>
                                                        <h:panelGrid columns="2">
                                                            <h:selectBooleanCheckbox value="#{consultor.marcado}"/>
                                                            <h:outputText styleClass="titulo3" value="#{consultor.nome}"/>
                                                        </h:panelGrid>
                                                    </rich:dataGrid>
                                                </rich:tab>
                                                <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                            </rich:tabPanel>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" valign="top" >
                                            <h:panelGrid id="panelMenuRelatorio" columns="2"  width="50%"  columnClasses="colunaCentralizada">
                                                <a4j:commandButton  title="Atualizar Lista de Cliente" action="#{SituacaoContratoAnaliticoDWControle.atualizarListaCliente}" image="../imagens/botaoAtualizarGrande.png" reRender="form:panelRelatorioCliente,form:mensagem, form:tabPanelFiltros" />
                                                <h:panelGroup>
                                                    <a4j:commandButton rendered="#{!empty SituacaoContratoAnaliticoDWControle.selectListaClientesRelatorio}" 
                                                                       id="imprimir"
                                                                       value="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_imprimir}"
                                                                       title="#{msg_aplic.prt_SituacaoContratoAnaliticoDW_imprimir}"
                                                                       action="#{SituacaoContratoAnaliticoDWControle.imprimirRelatorio}"
                                                                       oncomplete="#{SituacaoContratoAnaliticoDWControle.nomeRefPastaRelatorioRelatorioGeradoAgora}\""
                                                                       image="../imagens/imprimirContrato.png" />
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td align="center" valign="top" width="100%"  style="padding:7px 20px 0 20px;">
                                            <h:panelGroup id="panelRelatorioCliente">
                                                <table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                                    <tr>
                                                        <td width="19" height="50" align="left" valign="top"><img src="images/box_centro_top_left.gif" width="19" height="50"></td>
                                                        <td align="left" valign="top" background="images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Relatório Analítico Cliente</td>
                                                        <td width="19" align="left" valign="top"><img src="images/box_centro_top_right.gif" width="19" height="50"></td>
                                                    </tr>
                                                    <tr>
                                                        <td align="left" valign="top" background="images/box_centro_left.gif"></td>
                                                        <td align="left" valign="top" bgcolor="#ffffff" style="padding:5px 15px 5px 15px;">
                                                            <h:panelGrid >
                                                                <rich:dataTable id="item" width="100%" rowClasses="linhaPar,linhaImpar"  columnClasses="colunaCentralizada" align="center"
                                                                                value="#{SituacaoContratoAnaliticoDWControle.selectListaClientesRelatorio}" rows="50" var="situacaoContratoAnaliticoDW" rowKeyVar="status">
                                                                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
                                                                    <rich:column sortBy="#{situacaoContratoAnaliticoDW.cliente.matricula}"  filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Matrícula"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.cliente.matricula}" />
                                                                    </rich:column>
                                                                    <rich:column width="80%" sortBy="#{situacaoContratoAnaliticoDW.cliente.pessoa.nome}"  filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Nome"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.cliente.pessoa.nome}" />
                                                                    </rich:column>
                                                                    <rich:column width="80%">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Telefone"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.foneCliente}" />
                                                                    </rich:column>
                                                                    <%--
                                                                    <rich:column width="40%">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="E-mail"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.emailCliente}" />
                                                                    </rich:column>
                                                                    --%>
                                                                    <rich:column  sortBy="#{situacaoContratoAnaliticoDW.cliente.situacao_Apresentar}"  filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Situação"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.cliente.situacao_Apresentar}" />
                                                                    </rich:column>
                                                                    <rich:column sortBy="#{situacaoContratoAnaliticoDW.contrato.situacaoContrato_Apresentar}"  filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Tipo Contrato"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.contrato.situacaoContrato_Apresentar}" />
                                                                    </rich:column>
                                                                    <rich:column sortBy="#{situacaoContratoAnaliticoDW.contrato.contratoDuracao.numeroMeses}"  filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Duração"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.contrato.contratoDuracao.numeroMeses}" />
                                                                    </rich:column>
                                                                    <rich:column >
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Modalidade"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.modalidadeCliente}" />
                                                                    </rich:column>
                                                                    <%--<rich:column width="20%">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Plano"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.plano.descricao}" />
                                                                    </rich:column>--%>
                                                                    <rich:column >
                                                                        <f:facet name="header">
                                                                            <h:outputText value="DT. Vencimento"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.contrato.vigenciaAteAjustada_Apresentar}" />
                                                                    </rich:column>
                                                                    <%--<rich:column width="40%">
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Endereço"/>
                                                                        </f:facet>
                                                                        <h:outputText  value="#{situacaoContratoAnaliticoDW.enderecoCliente}" />
                                                                    </rich:column>--%>
                                                                    <rich:column >
                                                                        <f:facet name="header">
                                                                            <h:outputText value="Opção"/>
                                                                        </f:facet>
                                                                        <a4j:commandButton id="visualizarCliente" action="#{SituacaoContratoAnaliticoDWControle.irParaTelaCliente}" alt="Ir para tela de Edição do Cliente" image="../imagens/botaoVisualizar.png" oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                            <f:param name="state" value="AC"/>
                                                                        </a4j:commandButton>
                                                                    </rich:column>
                                                                </rich:dataTable>
                                                                <rich:datascroller align="center" for="form:item" maxPages="20" id="scitems" />
                                                            </h:panelGrid>
                                                        </td>
                                                        <td align="left" valign="top" background="images/box_centro_right.gif"><img src="images/shim.gif"></td>
                                                    </tr>
                                                    <tr>
                                                        <td height="20" align="left" valign="top"><img src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                                        <td align="left" valign="top" background="images/box_centro_bottom.gif"><img src="images/shim.gif"></td>
                                                        <td align="left" valign="top"><img src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                                    </tr>
                                                </table>
                                            </h:panelGroup>
                                        </td>
                                    </tr>
                                    <!-- fim item-->
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td  height="93" align="left" valign="top" class="bgrodape">
                    <jsp:include page="include_rodape_1.jsp" flush="true" />
                </td>
            </tr>
        </table>
    </a4j:form>
    <rich:modalPanel id="panelStatus5" autosized="true">
        <h:panelGrid columns="3">
            <h:graphicImage url="../imagens/carregando.gif" style="border:none"/>
            <h:outputText styleClass="titulo3" value="Carregando..."/>
        </h:panelGrid>
    </rich:modalPanel>
    <a4j:status onstart="Richfaces.showModalPanel('panelStatus5');"
                onstop="#{rich:component('panelStatus5')}.hide();">
    </a4j:status>
</f:view>



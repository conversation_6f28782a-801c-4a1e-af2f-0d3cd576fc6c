<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<!-- inicio box -->
<div class="box">
    <div class="boxtop"><img src="./images/box_top.png" alt="box_top"></div>
    <div class="boxmiddle">
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top">
                    <img style="vertical-align:middle;margin-right:9px;" src="./images/icon_cadastros.gif" width="12"
                         height="15" alt="icon_cadastros">
                    <a class="titulo2">Cadastros</a>
                </td>
            </tr>
        </table>
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">

            <tr>
                <td width="20" align="left" valign="top"><img src="./images/shim.gif"></td>
                <!-- inicio item-->
                <td align="left" valign="top">
                    <!-- inicio item-->
                    <div>
                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlWiki}Inicial:Cadastros:Clientes"
                                      title="Clique e saiba mais: Clientes"
                                      target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <a4j:commandLink styleClass="titulo3" id="A" action="#{ClienteControle.acao}"
                                         actionListener="#{ClienteControle.consultarPaginadoListener}">
                            Usu�rios
                            <f:attribute name="paginaInicial" value="paginaInicial"/>
                            <f:attribute name="letraConsultPag" value="A"/>
                            <f:attribute name="tipoConsulta" value="letra"/>
                        </a4j:commandLink>
                    </div>
                    <div class="sepmenu"><img src="./images/shim.gif"></div>
                    <!-- fim item-->

                    <!-- inicio item-->
                    <div>
                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-cliente-visitante-aluno/"
                                      title="Clique e saiba mais: Cadastrar Cliente"
                                      target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <a4j:commandLink id="linkCadastroClienteSimplificado" styleClass="titulo3"
                                         actionListener="#{ClienteControle.prepareInclusao}"
                                         action="#{ClienteControle.novo}">Empresas
                        </a4j:commandLink>
                    </div>
                    <div class="sepmenu"><img src="./images/shim.gif"></div>
                    <!-- fim item-->

                    <!-- inicio item-->
                    <div>
                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlWiki}"
                                      title="Clique e saiba mais: GerenciadorNotas"
                                      target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <a4j:commandLink styleClass="titulo3" id="B" action="#{ClienteControle.acao}"
                                         actionListener="#{ClienteControle.consultarPaginadoListener}">
                            Gerenciador de Notas
                            <f:attribute name="paginaInicial" value="paginaInicial"/>
                            <f:attribute name="letraConsultPag" value="B"/>
                            <f:attribute name="tipoConsulta" value="letra"/>
                        </a4j:commandLink>
                    </div>
                    <div class="sepmenu"><img src="./images/shim.gif"></div>
                    <!-- fim item-->
                </td>
            </tr>
        </table>

    </div>
    <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>
<!-- fim box -->
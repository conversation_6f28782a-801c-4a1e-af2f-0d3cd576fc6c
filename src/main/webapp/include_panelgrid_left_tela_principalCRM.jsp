<%@page pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
<link href="./css/crm.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<style type="text/css">
    .panel-esquerda {
        background-color: #e6e6e6;
    }

    .rich-stglpanel-header {
        background: none !important;
        padding: inherit !important;
    }

    .rich-stglpanel-body {
        border: none !important;
    }

    .rich-stglpanel-marker {
        display: none !important;
    }

    .rich-calendar-button.semIcone {
        display: none;
    }

    .rich-calendar-exterior.rich-calendar-popup.dataInicial {
        left: 11px !important;
        top: 41px !important;
    }

    .rich-calendar-exterior.rich-calendar-popup.dataFinal {
        left: 108px !important;
        top: 41px !important;
    }

    .rich-ddmenu-label-select.listaConsultorCRM {
        border: none !important;
    }
    
    .listaConsultorCRM .rich-menu-item{
        padding-right: 0px;
    }
    
    .listaConsultorCRM .grupoPai{
        width: 200px;
    }
    
    .listaConsultorCRM .grupoFilho{
        width: 450px;
    }
    
    .rich-calendar-input.listaConsultorCRM {
        text-align: center;
    }

    .lf-crm .linhaImpar {
        cursor: pointer;
    }

    .semBorda {
        position: relative !important;
    }

    .barra-progresso-meta {
        background-color: #c4d6de;
        min-height: 2px;
        left: 0;
        position: absolute;
        max-width: 100%;
    }

    .rich-table-cell {
        padding: 5px 7px 5px 7px;
    }

    .rich-table .semBorda .lf-crm .rich-table-cell {
        padding: 0;
    }

</style>

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<h:panelGroup id="panelGridLeft" layout="block" style="width: 100%; height: 100%;">

    <h:panelGroup id="metas" style="width: 100%;">

        <h:panelGroup id="totalMetas" layout="block" style="padding: 7px">
            <h:outputText
                    style="font-weight: bold; font-size: 16px; color: #333333; font-family: arial, helvetica, sans-serif; color: #333333;"
                    styleClass="tooltipster"
                    title="Agendamentos de Ligações, Indicações e as Metas Extras não entram no total de metas"
                    value="Total de Metas: #{MetaCRMControle.totalMetas}"
                    id="metaTotal"/>
            <h:outputText id="nrMetaTotal"
                    style="float: right; text-align-all: right; font-size: 16px; color: #333333; font-family: arial, helvetica, sans-serif; color: #333333"
                          value="#{MetaCRMControle.porcentagemTotalMetasRealizado_Apresentar}"/>
            <h:outputLink id="urlWikiCrmMetasTotais"
                          value="#{SuperControle.urlBaseConhecimento}fases-da-meta-diaria-do-crm/"
                          title="Clique e saiba mais: Fases da meta diária do CRM" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup id="indicadorTotal" layout="block"
                      style="height: 3px; background: #C0C0C0;  margin-left: 7px; margin-right: 7px;">

            <%--VERMELHO META < 50%--%>
            <h:panelGroup layout="block"
                          rendered="#{MetaCRMControle.porcentagemTotalMetasRealizado < 50}"
                          style="height: 3px; background: red; width: #{MetaCRMControle.porcentagemTotalMetasRealizado}%; max-width: 100%"/>

            <%--AMARELO  META >= 50% < 80%--%>
            <h:panelGroup layout="block"
                          rendered="#{MetaCRMControle.porcentagemTotalMetasRealizado >= 50 && MetaCRMControle.porcentagemTotalMetasRealizado < 80}"
                          style="height: 3px; background: yellow; width: #{MetaCRMControle.porcentagemTotalMetasRealizado}%; max-width: 100%"/>

            <%--VERDE  META >= 80%--%>
            <h:panelGroup layout="block"
                          rendered="#{MetaCRMControle.porcentagemTotalMetasRealizado >= 80}"
                          style="height: 3px; background: green; width: #{MetaCRMControle.porcentagemTotalMetasRealizado}%; max-width: 100%"/>
        </h:panelGroup>

        <rich:dataGrid id="listaMetaCRM" value="#{MetaCRMControle.listaMetaCRM}"
                       var="meta"
                       styleClass="semBorda"
                       columnClasses="semBorda"
                       style="background: #e6e6e6;"
                       width="100%" columns="1"
                       cellpadding="0"
                       cellspacing="0">

            <a4j:commandLink style="background: #e6e6e6; text-decoration: none;"
                             title="#{meta.tipoFaseCRM.codigo == 1 ? 'Agendamentos de Ligações e Indicações não entram no total de metas' : ''}"
                                    styleClass="semBorda tooltipster">

                <%--TIPO DAS FASES--%>
                    <h:panelGroup id="tipoMetas" layout="block"
                                  style="padding-left: 1px; padding-bottom: 4px; padding-top: 2px ">

                        <h:panelGroup layout="block" rendered="#{meta.tipoFaseCRM.codigo != 5}" id="tipoFaseCRM">
                            <h:outputText styleClass="semBorda"
                                          id="tipoFaseCRMDescricaoCurta"
                                          style="font-weight: bold; font-size: 16px; color: #333333; margin-left: 2%;"
                                          value="#{meta.tipoFaseCRM.descricaoCurta}"/>

                            <h:outputText rendered="#{meta.tipoFaseCRM.codigo != 4}"
                                          id="totalMetaRealizada"
                                          styleClass="semBorda"
                                          style="color: #888888; font-weight: normal; font-size: 14px; font-family: arial, helvetica, sans-serif; float: right;"
                                          value="#{meta.totalMetaRealizada}/#{meta.totalMeta} metas"/>

                            <%-- TOTAL NÃO DEVE APRESENTAR PARA OS "OUTROS"--%>
                            <h:outputText rendered="#{meta.tipoFaseCRM.codigo == 4}"
                                          styleClass="semBorda"
                                          value=""/>

                        </h:panelGroup>

                        <h:panelGroup layout="block" rendered="#{meta.tipoFaseCRM.codigo == 5}">
                            <h:panelGroup id="totalMetasExtra" layout="block">
                                <h:outputText
                                        style="font-weight: bold; font-size: 16px; color: #333333; font-family: arial, helvetica, sans-serif; color: #333333;"
                                        value="Total de Meta Extra: #{MetaCRMControle.totalMetasMetaExtra}"/>
                                <h:outputText
                                        style="float: right; text-align-all: right; font-size: 16px; color: #333333; font-family: arial, helvetica, sans-serif; color: #333333"
                                        value="#{MetaCRMControle.porcentagemTotalMetasRealizadoMetaExtra_Apresentar}"/>
                            </h:panelGroup>

                            <h:panelGroup id="indicadorTotalExtra" layout="block"
                                          rendered="#{meta.tipoFaseCRM.codigo == 5}"
                                          style="height: 3px; background: #C0C0C0; margin-top: 4px;">

                                <%--VERMELHO META < 50%--%>
                                <h:panelGroup layout="block"
                                              rendered="#{MetaCRMControle.porcentagemTotalMetasRealizadoMetaExtra < 50}"
                                              style="height: 3px; background: red; width: #{MetaCRMControle.porcentagemTotalMetasRealizadoMetaExtra}%; max-width: 100%"/>

                                <%--AMARELO  META >= 50% < 80%--%>
                                <h:panelGroup layout="block"
                                              rendered="#{MetaCRMControle.porcentagemTotalMetasRealizadoMetaExtra >= 50 && MetaCRMControle.porcentagemTotalMetasRealizado < 80}"
                                              style="height: 3px; background: yellow; width: #{MetaCRMControle.porcentagemTotalMetasRealizadoMetaExtra}%; max-width: 100%"/>

                                <%--VERDE  META >= 80%--%>
                                <h:panelGroup layout="block"
                                              rendered="#{MetaCRMControle.porcentagemTotalMetasRealizadoMetaExtra >= 80}"
                                              style="height: 3px; background: green; width: #{MetaCRMControle.porcentagemTotalMetasRealizadoMetaExtra}%; max-width: 100%"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>


                <%--LISTA DE METAS--%>
                <rich:dataGrid id="dtglistaMetaCRM" value="#{meta.listaTipoMetaVO}"
                               width="100%" columns="1"
                               styleClass="semBorda lf-crm"
                               rowClasses="linhaImpar"
                               onRowClick="document.getElementById(jQuery(this).find('.lf-crm-botao').attr('id')).click();"
                               var="tipoMeta">

                    <h:panelGroup id="tipoMetaStyleClass" layout="block" styleClass="#{tipoMeta.styleClass}">

                        <a4j:commandButton
                                styleClass="semBorda lf-crm-botao"
                                id="tipoVenda"
                                style="display: none"
                                action="#{MetaCRMControle.selecionarFaseMeta}"
                                oncomplete="mostrarTelaCentroEDireita();adicionarPlaceHolderCRM();ajustarScrollAlunoSelecionado();consultarTotalSelecionado()"
                                reRender="colunaCRMCentro, colunaCRMDireita, listaMetaCRM, superiorPrincipalCRM">
                        </a4j:commandButton>


                        <h:panelGroup layout="block">

                            <h:outputText rendered="#{!MetaCRMControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes || tipoMeta.fasesCRMEnum.sigla == 'AG'}"
                                          id="fasesCRMEnumObjetivo"
                                    value="#{tipoMeta.labelMeta}"
                                    title="#{tipoMeta.fasesCRMEnum.objetivo}">
                            </h:outputText>

                            <h:outputText rendered="#{MetaCRMControle.configuracaoSistemaCRMVO.baterMetaTodasAcoes && tipoMeta.fasesCRMEnum.sigla != 'AG'}"
                                    value="#{tipoMeta.labelMeta}"
                                    id="acaoContabilizadoMeta"
                                    title="Sistema está configurado para que toda ação seja contabilizado meta.">
                            </h:outputText>
                        </h:panelGroup>


                        <%-- PORCENTAGEM MOSTRA SOMENTE NA META QUE ESTÁ SELECIONADA --%>
                        <h:outputText id="porcentagemAparecer"
                                      rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo == 1 && meta.tipoFaseCRM.codigo != 4}"
                                      style="font-size: 12px; margin-left: 1%;"
                                      value="#{tipoMeta.porcentagemMetaRealizada_Apresentar}"/>


                        <%-- BARRA DE PROGRESSO DA META --%>
                        <%--<h:panelGroup layout="block"--%>
                        <%--rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo != 1}"--%>
                        <%--styleClass="barra-progresso-meta"--%>
                        <%--style="width: #{tipoMeta.porcentagemMetaRealizada}%"/>--%>


                        <h:panelGroup style="float: right">
                            <%--META SELECIONADA--%>
                            <h:panelGroup
                                    rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo == 1 && meta.tipoFaseCRM.codigo != 4}">
                                <h:outputText styleClass="semBorda"
                                              id="qtRenovacao"
                                              style="font-size: 14px;"
                                              value="#{tipoMeta.totalMetaRealizada}/#{tipoMeta.totalMeta}"/>
                            </h:panelGroup>

                            <%--META NÃO SELECIONADA--%>
                            <h:panelGroup
                                    rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo != 1 && meta.tipoFaseCRM.codigo != 4}">
                                <h:outputText styleClass="semBorda"
                                              id="qtRenovacao1"
                                              style="font-size: 14px; font-weight: normal; color: #888888;"
                                              value="#{tipoMeta.totalMetaRealizada}/#{tipoMeta.totalMeta}"/>
                            </h:panelGroup>


                            <%-- METAS DE INDICADORES META NÃO SELECIONADA --%>
                            <h:panelGroup
                                    rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo != 1 && meta.tipoFaseCRM.codigo == 4}">
                                <h:outputText styleClass="semBorda"
                                              id="qtRenovacao2"
                                              style="font-size: 14px; font-weight: normal; color: #888888;"
                                              value="#{tipoMeta.totalMeta}"/>
                            </h:panelGroup>
                            <%-- METAS DE INDICADORES META SELECIONADA --%>
                            <h:panelGroup
                                    rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo == 1 && meta.tipoFaseCRM.codigo == 4}">
                                <h:outputText styleClass="semBorda"
                                              id="qtRenovacao3"
                                              style="font-size: 14px;"
                                              value="#{tipoMeta.totalMeta}"/>
                            </h:panelGroup>

                            <%--ICONE DA META SELECIONADA--%>
                            <h:panelGroup styleClass="semBorda" rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo == 1}"
                                          id="iconeDaMetaSelecionada"
                                          style="padding-left: 2px; font-size: medium">
                                <i class="fa-icon-arrow-right"></i>
                            </h:panelGroup>

                            <%--ICONE DA META ATENDIDA--%>
                            <h:panelGroup styleClass="semBorda"
                                          id="iconeDaMetaAtendida"
                                          rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo == 4 && meta.tipoFaseCRM.codigo != 4}"
                                          style="padding-left: 2px; font-size: medium; color: green">
                                <i class="fa-icon-ok-sign"></i>
                            </h:panelGroup>

                            <%--ICONE DA META NÃO ATENDIDA CONTATO REALIZADO--%>
                            <h:panelGroup styleClass="semBorda"
                                          id="iconeNaoAtendida"
                                          rendered="#{tipoMeta.situacaoAtualMetaEnum.codigo == 3 && meta.tipoFaseCRM.codigo != 4}"
                                          style="padding-left: 2px; font-size: medium; color: silver">
                                <i class="fa-icon-ok-sign"></i>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGroup>
                </rich:dataGrid>
            </a4j:commandLink>
        </rich:dataGrid>
    </h:panelGroup>
</h:panelGroup>

<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<style>
    .caixaDadosAluno {
        width: 100%;
        background: #e6e6e6;
        min-height: 100px;
        padding-bottom: 10px;
    }

    .row, h1, h2, h3, h4, h5, h6 {
        margin: 0 !important;
    }
    .row{
        padding-top: 10px;
    }
    .column {
        padding-left: 10px;
        vertical-align: top;
    }

    h4 a{
        font-size: 16px;
    }
    h4, h2 {
        font-weight: bold !important;
    }
    .linhaImpar td, .linhaPar td{
        padding: 10px;
    }
</style>

<h:panelGroup layout="block" styleClass="caixaDadosAluno">
    <h:panelGroup rendered="#{MetaCRMControle.alunoMenorIdade}" layout="block" styleClass="row">
        <h:panelGroup layout="block" styleClass="column col-md-12">
            <h6><h:outputText value="#{MetaCRMControle.rotuloResponsavel}"/></h6>
            <h4><h:outputText value="#{MetaCRMControle.nomeResponsaveis}"/></h4>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="row">
        <h:panelGroup layout="block" styleClass="column col-md-5">
            <h6>Nome</h6>
            <h2><h:outputText value="#{MetaCRMControle.historicoContatoVO.nomeAbreviado}"/></h2>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-6">
            <h:panelGroup style="background-color: #cdcdcd; padding: 10px;" layout="block">
                <h:outputText
                        style="font-size: 14px;font-family: arial, helvetica, sans-serif; font-weight: bold; color: red"
                        value="Contato avulso. Não valerá como resultado de nenhuma meta do CRM Web"/>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="row" >

        <h:panelGroup layout="block" styleClass="column col-md-2">
            <h6>Matrícula</h6>
            <h4><h:outputText value="#{MetaCRMControle.historicoContatoVO.matricula_Apresentar}"/></h4>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-3">
            <h6>Cadastro</h6>
            <h4><h:outputText value="#{MetaCRMControle.historicoContatoVO.dataCadastro_Apresentar}"/></h4>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-3">
            <h6>Ult. acesso</h6>
            <h4><h:outputText value="#{MetaCRMControle.historicoContatoVO.diaUltimoAcessoComHora_Apresentar}"/></h4>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-3">
            <h6>Vencimento</h6>
            <h4><h:outputText value="#{MetaCRMControle.historicoContatoVO.vencimentoContrato_Apresentar}"/></h4>
        </h:panelGroup>

    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="row" >

        <h:panelGroup layout="block" styleClass="column col-md-1">
            <h6>LIGAÇÕES</h6>
            <h4><a4j:commandLink

                    value="#{MetaCRMControle.historicoContatoVO.totalLigacao}" ajaxSingle="true"
                    action="#{MetaCRMControle.abrirLigacoes}"
                    oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                    reRender="modalHistoricoContadorCliente, modalHistoricoContadorClienteGroup, modalHistoricoContadorClienteTable"/></h4>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-1">
            <h6>E-MAIL</h6>
            <h4><a4j:commandLink
                    value="#{MetaCRMControle.historicoContatoVO.qtdEmail}" ajaxSingle="true"
                    action="#{MetaCRMControle.abrirEmail}"
                    oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                    reRender="modalHistoricoContadorCliente"/></h4>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-1">
            <h6>PESSOAL</h6>
            <h4>
                <a4j:commandLink
                        value="#{MetaCRMControle.historicoContatoVO.qtdPessoal}" ajaxSingle="true"
                        action="#{MetaCRMControle.abrirContatoPessoal}"
                        oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                        reRender="modalHistoricoContadorCliente"/></h4>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-1">
            <h6>SMS</h6>
            <h4>
                <a4j:commandLink
                    value="#{MetaCRMControle.historicoContatoVO.qtdSMS}"
                    ajaxSingle="true"
                    action="#{MetaCRMControle.abrirSMS}"
                    oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                    reRender="modalHistoricoContadorCliente"/></h4>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-2">
            <h6>APP</h6>
            <h4> <a4j:commandLink
                    value="#{MetaCRMControle.historicoContatoVO.qtdAPP}" ajaxSingle="true"
                    action="#{MetaCRMControle.abrirAPP}"
                    oncomplete="Richfaces.showModalPanel('modalHistoricoContadorCliente')"
                    reRender="modalHistoricoContadorCliente"/></h4>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="column col-md-5" style="text-align: right">
            <a4j:commandLink style="margin-right: 10px; margin-top: 10px"
                             id="fazerIndicacao"
                             styleClass="pure-button"
                             value="Fazer Indicação"
                             action="#{MetaCRMControle.apresentarIndicacao}"
                             oncomplete="adicionarPlaceHolderCRM()"
                             reRender="panelGridRight"/>
            <%--SCRIPT DE ATENDIMENTO--%>
            <a4j:commandLink id="textoPadrao"
                             style="margin-right: 10px; margin-top: 10px"
                             styleClass="pure-button"
                             title="Texto Padrão"
                             value="Script"
                             action="#{MetaCRMControle.preencherTextoPadraoDaTelaCliente}"
                             oncomplete="#{MetaCRMControle.onComplete}"
                             reRender="mdlMensagemGenerica, observacaoHistorico, mensagemAPP, mensagemSMS, panelTextoPadraoNewRealizarContato">
            </a4j:commandLink>
            </h:panelGroup>

    </h:panelGroup>



</h:panelGroup>

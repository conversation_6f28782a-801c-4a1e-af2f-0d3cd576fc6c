<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<!-- inicio box -->
<div class="box">
  <div class="boxtop"><img src="images/box_top.png"></div>
  <div class="boxmiddle">
    <table width="146" border="0" cellpadding="0" cellspacing="0" class="text" style="padding-bottom:6px;">
      <tr>
        <td colspan="2" align="center" valign="top">
          <h:panelGroup layout="block" id="panelFoto">            
            <a4j:jsFunction name="postSave" action="#{CapturaFotoControle.foo}">
              <a4j:actionparam name="param1" assignTo="#{CapturaFotoControle.retorno}"/>
            </a4j:jsFunction>
            <a4j:mediaOutput rendered="#{!SuperControle.fotosNaNuvem}"
                             element="img" id="imagem1"  style="width:150px;height:180px "
                             cacheable="false" session="true"
                             createContent="#{ClienteControle.paintFoto}"
                             value="#{ImagemData}" mimeType="image/jpeg" >
              <f:param value="#{SuperControle.timeStamp}" name="time"/>
              <f:param name="largura" value="150"/>
              <f:param name="altura" value="180"/>
            </a4j:mediaOutput>
            <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}"
                            width="150" height="180"
                            style="width:150px;height:180px"
                            url="#{ClienteControle.paintFotoDaNuvem}">
            </h:graphicImage>
          </h:panelGroup>
        </td>
      </tr>
    </table>
    <table width="146" border="0" cellpadding="0" cellspacing="0" class="text" style="padding-bottom:6px;">
      <tr>
        <td colspan="2" align="left" valign="top">
          <h:panelGrid styleClass="titulo30" columns="1">
            <h:outputText id="clienteNomeAuto" value="#{ClienteControle.pessoaVO.nome}"/>
            <h:outputText styleClass="textsmall" value="MAT: #{ClienteControle.clienteVO.matricula}"/>
          </h:panelGrid>

          <h:dataTable id="telefoneVO" width="100%" headerClass="subordinado"
                       rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda"
                       value="#{ClienteControle.pessoaVO.telefoneVOs}" var="telefone">
            <h:column>
              <h:outputText styleClass="textsmall"  value="#{telefone.numero}" />
            </h:column>
            <h:column>
              <h:outputText styleClass="textsmall" value="#{telefone.tipoTelefone_ApresentarEspecial}"/>
              <h:outputText styleClass="textsmall" value ="#{telefone.descricaoEspecial}" title="#{telefone.tipoTelefone_Apresentar}: #{telefone.descricao}"/>
            </h:column>
          </h:dataTable>

        </td>
      </tr>
    </table>
      <table width="50" border="0" cellspacing="0" cellpadding="0" >
          <tr>
              <td>
                  <h:panelGrid columns="2">
                      <img style="vertical-align:middle;margin-right:5px;" src="images/icon_editarcadastro.gif" width="16" height="17">
                      <rich:dropDownMenu id="menuEditCadas" styleClass="titulo6" value="Editar o cadastro"
                                         submitMode="ajax" direction="bottom-right" jointPoint="tr" >

                          <rich:menuItem id="editDadosPessoais"  value="Dados Pessoais" actionListener="#{ClienteControle.abrirAba}"
                                         oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);return false;">
                              <f:attribute name="aba" value="abadadosPessoais"/>
                          </rich:menuItem>

                          <rich:menuItem id="editCobranca"  value="Cobrança" actionListener="#{ClienteControle.abrirAba}"
                                         oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);return false;">
                              <f:attribute name="aba" value="abaCobranca"/>
                          </rich:menuItem>

                          <rich:menuItem id="editEndereco"  value="Endereço" actionListener="#{ClienteControle.abrirAba}"
                                         oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);return false;">
                              <f:attribute name="aba" value="abaEndereco"/>
                          </rich:menuItem>

                          <rich:menuItem id="editEmail"  value="Email" actionListener="#{ClienteControle.abrirAba}"
                                         oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);return false;">
                              <f:attribute name="aba" value="abaEmail"/>
                          </rich:menuItem>

                          <rich:menuItem id="editDependentes" value="Dependentes" actionListener="#{ClienteControle.abrirAba}"
                                         oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);return false;">
                              <f:attribute name="aba" value="abaDependentes"/>
                          </rich:menuItem>

                      </rich:dropDownMenu>
                  </h:panelGrid>
              </td>

          </tr>
      </table>
  </div>

    <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>


<!-- Operações Cliente --!>
<!-- inicio box -->



</div>
<!-- fim box -->


<!-- fim box -->
<div class="box">
  <div class="boxtop"><img src="./images/box_top.png"></div>
  <div class="boxmiddle">
    <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
      <tr>
        <td colspan="2" align="left" valign="top" class="titulo2" style="font-size:14px">Opções</td>
      </tr>
    </table>

      <table width="50" align="left" border="0" cellspacing="0" cellpadding="0" >
          <h:outputLink styleClass="linkWikiMenu"
                        value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                        title="Clique e saiba mais: Cadastro"
                        target="_blank" >
              <i class="fa-icon-question-sign" style="font-size: 18px"></i>
          </h:outputLink>
          <rich:dropDownMenu styleClass="titulo6 marginForMenuWiki" value="Cadastro"
                             submitMode="ajax" direction="bottom-right" jointPoint="tr" >

              <rich:menuItem actionListener="#{ClienteControle.abrirAba}"
                             oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return true;">
                  <h:outputText value="Vincular a uma Carteira"/>
                  <f:attribute name="aba" value="abaVinculo"/>
              </rich:menuItem>
              <rich:menuItem actionListener="#{ClienteControle.abrirAba}"
                             oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return true;">
                  <h:outputText value="Adicionar classificação"/>
                  <f:attribute name="aba" value="abaGrupo"/>
              </rich:menuItem>
              <rich:menuItem actionListener="#{ClienteControle.abrirAba}"
                             oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return true;"  >
                  <h:outputText value="Associar a Grupos"/>
                  <f:attribute name="aba" value="abaGrupo"/>
              </rich:menuItem>
          </rich:dropDownMenu>
      </table>
      <h:panelGroup rendered="#{ClienteControle.menuContrato}">
          <div class="sepmenu"><img src="./images/shim.gif"></div>
      </h:panelGroup>
    <!-- inicio item-->
    <table width="50" border="0" cellspacing="0" cellpadding="0" >
      <h:outputLink styleClass="linkWikiMenu" value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                    title="Clique e saiba mais: Relacionamento"
                                        target="_blank" >
          <i class="fa-icon-question-sign" style="font-size: 18px"></i>
      </h:outputLink>

      <rich:dropDownMenu styleClass="titulo6 marginForMenuWiki" value="Relacionamento" submitMode="ajax"
                         direction="bottom-right" jointPoint="tr" >
        <rich:menuItem action="#{QuestionarioClienteCRMControle.novoCRM}"
                       oncomplete="abrirPopup('questionarioClienteCRMForm.jsp', 'Questionario', 780, 595); return false;">
          <h:outputText value="Ver o Último Boletim de Visita"/>

        </rich:menuItem>

        <rich:menuItem action="#{QuestionarioClienteControle.novo}"
                       oncomplete="abrirPopup('questionarioClienteForm.jsp', 'Questionario', 780, 595); return false;">
          <h:outputText value="Ver Histórico de Boletins"/>
        </rich:menuItem>

        <rich:menuItem action="#{HistoricoContatoControle.inicializarContatoAvulso}"
                       oncomplete="#{HistoricoContatoControle.executarAberturaPopupRealizarContato}" >
          <h:outputText value="Realizar Contato"/>
        </rich:menuItem>

        <rich:menuItem action="#{HistoricoContatoControle.inicializarHistoricoContato}"
                       oncomplete="abrirPopup('historicoContatoClienteForm.jsp', 'HistoricoContatoCliente', 512, 530); return false;">
          <h:outputText value="Histórico de Contatos"/>
        </rich:menuItem>

        <rich:menuItem action="#{IndicacaoControle.historicoIndicacao}"
                       oncomplete="abrirPopup('historicoIndicacao.jsp', 'HistoricoIndicacao', 512, 530); return false;">
          <h:outputText value="Histórico de Indicações"/>
        </rich:menuItem>

        <rich:menuItem value="Lançar Mensagem para Catraca"
                       action="#{ClienteControle.editarClienteMensagemCatraca}"
                       reRender="panelIncludeMensagem"
                       oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
        </rich:menuItem>

        <rich:menuItem  value="Lançar Aviso ao Consultor"
                        action="#{ClienteControle.editarClienteMensagemConsultor}"
                        reRender="panelIncludeMensagem"
                        oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
        </rich:menuItem>

        <rich:menuItem value="Lançar Aviso Médico"
                       action="#{ClienteControle.editarClienteMensagemMedico}"
                       reRender="panelIncludeMensagem"
                       oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
        </rich:menuItem>

        <rich:menuItem value="Lançar Objetivo do Aluno Academia"
                       action="#{ClienteControle.editarClienteMensagemObjetivo}"
                       reRender="panelIncludeMensagem"
                       oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
        </rich:menuItem>
      </rich:dropDownMenu>
    </table>

      </div>
  <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>





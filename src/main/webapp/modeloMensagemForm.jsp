<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
</style>

<script type="text/javascript" src="script/scriptSMS.js">

</script>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ModeloMensagem_tituloForm}"/>
    </title>

    <rich:modalPanel id="panelEmail" autosized="true" shadowOpacity="true" width="300" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Marcadores de Email"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink4" />
                <rich:componentControl for="panelEmail" attachTo="hidelink4" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <!-- INICIO - FORMULARIO DA TAG -->
        <a4j:form id="formMarcadorEmail" ajaxSubmit="true">
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <rich:dataTable id="MarcadoEmail" columns="2" width="440px" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" var="marcadorEmail" rows="40" value="#{ModeloMensagemControle.listaSelectItemMarcadoEmail}">
                    <f:facet name="header">
                        <h:outputText value="Email" />
                    </f:facet>

                    <rich:column width="170px">
                        <f:facet name="header">
                            <h:outputText value="Tags" />
                        </f:facet>
                        <h:outputText styleClass="campos" value="#{marcadorEmail.nome}" />
                    </rich:column>
                    <rich:column width="240px">
                        <f:facet name="header">
                            <h:outputText value="Opções" />
                        </f:facet>
                        <a4j:commandButton action="#{ModeloMensagemControle.executarInsercaoTag}" reRender="mensagemEmail" oncomplete="Richfaces.hideModalPanel('panelEmail');" image="./imagens/botaoAlteracaoIncluir.gif" value="Adicionar" styleClass="botoes" />
                    </rich:column>

                </rich:dataTable>
            </h:panelGrid>
        </a4j:form>
        <!-- FIM - FORMULARIO DA TAG -->

    </rich:modalPanel>

    <rich:modalPanel id="modalTagRemessa" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Tags Email - Remessa"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideModalTagRemessa" />
                <rich:componentControl for="modalTagRemessa" attachTo="hideModalTagRemessa" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formTagRemessa" ajaxSubmit="true">
            <h:panelGroup layout="block" style="padding-top: 5px; padding-bottom: 10px">
                <h:outputText styleClass="texto-cor-vermelho texto-bold texto-size-16" style="font-style: italic;"
                              value="Atenção:"/>
                <h:outputText styleClass="texto-cor-vermelho texto-size-16" style="padding-left: 5px"
                              value="As tags são compatíveis somente para remessas da CIELO."/>
            </h:panelGroup>
            <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
                <rich:dataTable columns="2" width="440px" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" rows="40"
                                columnClasses="colunaAlinhamento" var="tagRemessa"
                                value="#{ModeloMensagemControle.listaTagsEmailRemessa}">

                    <rich:column width="170px">
                        <f:facet name="header">
                            <h:outputText value="Tags" />
                        </f:facet>
                        <h:outputText value="#{tagRemessa.nome}" />
                    </rich:column>
                    <rich:column width="240px">
                        <f:facet name="header">
                            <h:outputText value="Opções"/>
                        </f:facet>
                        <a4j:commandButton action="#{ModeloMensagemControle.inserirTagRemessa}"
                                           reRender="mensagemEmail"
                                           oncomplete="Richfaces.hideModalPanel('modalTagRemessa');"
                                           image="./imagens/botaoAdicionar.png" value="Adicionar"
                                           styleClass="botoes" />
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <c:set var="titulo" scope="session" value="${msg_aplic.prt_ModeloMensagem_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWikiCRM}Cadastros:Modelo_de_Mensagem"/>
        <c:set var="modulo" scope="page" value="crm"/>

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="formF">
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background: url('./imagens/fundoBarraTopo.png') repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ModeloMensagem_tituloForm}">
                        <h:outputLink value="#{SuperControle.urlWikiCRM}Cadastros:Modelo_de_Mensagem"
                                      title="Clique e saiba mais: Modelo de Mensagem" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <%--<h:panelGrid id="panelGridMensagens1" columns="1" width="100%">--%>
                    <%--<h:outputText styleClass="mensagem"  value="#{ModeloMensagemControle.mensagem}"/>--%>
                    <%--<h:outputText styleClass="mensagemDetalhada" value="#{ModeloMensagemControle.mensagemDetalhada}"/>--%>
                <%--</h:panelGrid>--%>

                <h:panelGrid  columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_codigo}:" />
                    <h:inputText  id="codigo" size="5" styleClass="form"
                                  disabled="true"
                                  value="#{ModeloMensagemControle.modeloMensagemVO.codigo}"/>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_meioDeEnvio}" />
                    <h:selectOneMenu
                                     styleClass="form" id="meioDeEnvio"
                                     value="#{ModeloMensagemControle.modeloMensagemVO.meioDeEnvio}">
                        <f:selectItems value="#{ModeloMensagemControle.listaSelectItemMeioDeEnvio}" />
                        <a4j:support event="onchange" action="#{ModeloMensagemControle.limparCampos}" reRender="panelGridForm,panelTamanhoRestante"/>
                    </h:selectOneMenu>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_titulo}" />
                    <h:panelGroup >
                        <h:inputText  id="titulo" size="70" maxlength="100"  styleClass="form"
                                      value="#{ModeloMensagemControle.modeloMensagemVO.titulo}" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_tipoMensagem}" />
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     id="tipoMensagem"  value="#{ModeloMensagemControle.modeloMensagemVO.tipoMensagem}">
                        <f:selectItems value="#{ModeloMensagemControle.listaSelectItemTipoMensagem}"/>
                        <a4j:support event="onchange"   reRender="panelGridForm,panelTamanhoRestante"/>
                    </h:selectOneMenu>

                    <c:if test="${!ModeloMensagemControle.modeloMensagemVO.tipoMensagem eq 'EC'}">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_modeloMensagem}" />
                    <h:selectOneMenu  onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                      id="modeloMensagem"  value="#{ModeloMensagemControle.modeloMensagemVO.tipoModeloMensagem}">
                        <f:selectItems value="#{ModeloMensagemControle.listaSelectItemModeloMensagem}" />
                        <a4j:support event="onchange" actionListener="#{ModeloMensagemControle.obterModelo}" reRender="panelGridForm"/>
                    </h:selectOneMenu>
                    </c:if>
                    <h:outputText styleClass="tituloCampos" value="Ativo:" />
                    <h:selectBooleanCheckbox id="situacaoModeloMensagem" styleClass="campos" value="#{ModeloMensagemControle.modeloMensagemVO.ativo}"/>

                    <h:outputText rendered="#{ModeloMensagemControle.modeloMensagemVO.email}" styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_caminhoImagem}" />
                    <h:panelGroup rendered="#{ModeloMensagemControle.modeloMensagemVO.email}" >
                        <h:inputText id="caminhoImagem" size="70" styleClass="form" disabled="true"
                                     value="#{ModeloMensagemControle.modeloMensagemVO.caminhoImagemTemporaria}"/>
                    </h:panelGroup>
                    <c:if test="${ModeloMensagemControle.modeloMensagemVO.tipoMensagem eq 'EC'}">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_url_site}" />
                    <h:panelGroup >
                        <h:inputText  id="urlSite" size="70" maxlength="100"  styleClass="form"
                                      value="#{ModeloMensagemControle.modeloMensagemVO.urlRedirecionamento}" />
                    </h:panelGroup>
                    </c:if>

                    <h:outputText styleClass="tituloCampos" style="height: 30px;"
                                  rendered="#{!ModeloMensagemControle.modeloMensagemVO.ftp}"
                                  value="#{msg_aplic.prt_ModeloMensagem_adicionarTag}"/>

                    <h:outputText styleClass="tituloCampos" style="color: red"
                                  rendered="#{!ModeloMensagemControle.apresentarTags}"
                                  value="Para habilitar as TAGS marque o checkbox 'Envio Individual de E-mail' nas configurações do CRM na aba 'E-mail', após isso, é necessário sair e entrar novamente no sistema para validar a configuração."/>

                    <h:panelGroup layout="block"
                                  id="panelGeralTags"
                                  style="display: inline-flex;"
                                  rendered="#{ModeloMensagemControle.apresentarTags && !ModeloMensagemControle.modeloMensagemVO.ftp}">

                        <a4j:commandLink id="tagNome" action="#{ModeloMensagemControle.executarInsercaoTagNome}"
                                         reRender="mensagemSMSAPP,mensagemEmail" styleClass="botoes nvoBt btSec"
                                         value="Tag Nome" title="Tag Nome"/>

                        <a4j:commandLink id="tagPNome" action="#{ModeloMensagemControle.executarInsercaoTagPNome}"
                                         reRender="mensagemSMSAPP,mensagemEmail" styleClass="botoes nvoBt btSec"
                                         value="Tag Primeiro Nome" title="Tag Primeiro Nome"/>

                        <a4j:commandLink id="tagPesquisa" action="#{ModeloMensagemControle.incluirTagPesquisa}"
                                         reRender="mensagemSMSAPP,mensagemEmail" styleClass="botoes nvoBt btSec"
                                         value="Tag Pesquisa" title="Tag Pesquisa"/>

                        <a4j:commandLink id="tagBoleto" action="#{ModeloMensagemControle.executarInsercaoTagBoleto}"
                                         rendered="#{ModeloMensagemControle.modeloMensagemVO.email}"
                                         reRender="mensagemEmail" styleClass="botoes nvoBt btSec"
                                         value="Tag Boleto" title="Tag Boleto"/>

                        <a4j:commandLink id="tagEmail"
                                         rendered="#{ModeloMensagemControle.modeloMensagemVO.email || ModeloMensagemControle.modeloMensagemVO.ftp}"
                                         oncomplete="Richfaces.showModalPanel('panelEmail');"
                                         reRender="mensagemEmail" styleClass="botoes nvoBt btSec" value="Mais Tags"
                                         title="Mais Tags"/>

                        <a4j:commandLink id="btnModalTagRemessa"
                                         rendered="#{ModeloMensagemControle.modeloMensagemVO.email}"
                                         oncomplete="Richfaces.showModalPanel('modalTagRemessa');"
                                         reRender="mensagemEmail" styleClass="botoes nvoBt btSec"
                                         value="Tags Remessa"
                                         title="Tags Remessa"/>
                    </h:panelGroup>

                    <c:if test="${!ModeloMensagemControle.modeloMensagemVO.tipoMensagem eq 'EC'}">
                    <h:outputText styleClass="tituloCampos" style="height: 30px;"
                                  rendered="#{ModeloMensagemControle.modeloMensagemVO.email}"
                                  value="#{msg_aplic.prt_ModeloMensagem_adicionarTagVendasOnline}"/>
                    <h:panelGroup style="padding: 10px" layout="block" rendered="#{ModeloMensagemControle.modeloMensagemVO.email}">
                        <h:panelGroup layout="block" >
                            <a4j:commandLink id="tagUsuarioVendasOnline"
                                             action="#{ModeloMensagemControle.executarInsercaoTagUsuarioVendasOnline}"
                                             reRender="mensagemEmail" styleClass="botoes nvoBt btSec"
                                             title="Tag Usuário (Vendas Online)" value="Usuário"/>
                            <a4j:commandLink id="tagSenhaVendasOnline"
                                             action="#{ModeloMensagemControle.executarInsercaoTagSenhaVendasOnline}"
                                             reRender="mensagemEmail" styleClass="botoes nvoBt btSec"
                                             title="Tag Senha (Vendas Online)" value="Senha"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    </c:if>
                    <c:if test="${ModeloMensagemControle.modeloMensagemVO.tipoMensagem eq 'EC'}">
                    <h:outputText styleClass="tituloCampos" style="height: 30px;"
                                  rendered="#{ModeloMensagemControle.modeloMensagemVO.email}"
                                  value="#{msg_aplic.prt_ModeloMensagem_adicionarTagEmailConfirmacaoCompra}"/>
                    <h:panelGroup style="padding: 10px" layout="block" rendered="#{ModeloMensagemControle.modeloMensagemVO.email}">
                        <h:panelGroup layout="block" >
                            <a4j:commandLink id="tagCliqueAqui"
                                             action="#{ModeloMensagemControle.executarInsercaoTagLink}"
                                             reRender="mensagemEmail" styleClass="botoes nvoBt btSec"
                                             value="Link"/>
                            <a4j:commandLink id="tagPlano"
                                             action="#{ModeloMensagemControle.executarInsercaoTagPlano}"
                                             reRender="mensagemEmail" styleClass="botoes nvoBt btSec"
                                             value="Plano"/>
                            <a4j:commandLink id="tagNrParcelas"
                                             action="#{ModeloMensagemControle.executarInsercaoTagNrParcelas}"
                                             reRender="mensagemEmail" styleClass="botoes nvoBt btSec"
                                             value="Número de Parcelas"/>
                            <a4j:commandLink id="tagNomeEmailConfirmacao"
                                             action="#{ModeloMensagemControle.executarInsercaoTagNomeCliente}"
                                             reRender="mensagemEmail" styleClass="botoes nvoBt btSec"
                                             value="Nome Cliente"/>
                        </h:panelGroup>
                    </h:panelGroup>
                    </c:if>
                    <h:outputText rendered="#{ModeloMensagemControle.modeloMensagemVO.email}" value="Upload Imagem:" />
                    <h:panelGroup rendered="#{ModeloMensagemControle.modeloMensagemVO.email && ModeloMensagemControle.modeloMensagemVO.temImagem}" >
                        <h:outputText escape="false" style="color: #CC0000" value="Este modelo de mensagem possui ou já possuiu uma imagem e a mesma não pode ser substituída por outra. <br> Crie um novo modelo de mensagem caso queira enviar uma imagem diferente."/>
                    </h:panelGroup>

                    <h:panelGroup rendered="#{ModeloMensagemControle.modeloMensagemVO.email && !ModeloMensagemControle.modeloMensagemVO.temImagem}">
                        <h:panelGrid columns="3" rowClasses="linhaPar, linhaImpar" width="100%">
                            <h:panelGroup>
                                <rich:fileUpload listHeight="0"
                                                 listWidth="100"
                                                 noDuplicate="false"
                                                 fileUploadListener="#{ModeloMensagemControle.upload}"
                                                 maxFilesQuantity="1"
                                                 addControlLabel="Adicionar"
                                                 cancelEntryControlLabel="Cancelar"
                                                 doneLabel="Pronto"
                                                 sizeErrorLabel="Arquivo não Enviado. Tamanho Máximo Permitido 350kb"
                                                 progressLabel="Enviando"
                                                 stopControlLabel="Parar"
                                                 uploadControlLabel="Enviar"
                                                 transferErrorLabel="Falha de Transmissão"
                                                 stopEntryControlLabel="Parar"
                                                 id="upload"
                                                 immediateUpload="true"
                                                 acceptedTypes="png,jpg,jpeg,PNG,JPG,JPEG">

                                    <a4j:support event="onuploadcomplete" ajaxSingle="true"
                                                 actionListener="#{ModeloMensagemControle.definirModeloUpLoad}"
                                                 reRender="panelGridForm, form:imputMensagem, mensagemEmail, panelGridMensagens">
                                        <f:attribute name="arquivo" value="#{ModeloMensagemControle.arquivoUpLoad}" />
                                    </a4j:support>
                                </rich:fileUpload>
                                <rich:toolTip for="upload" followMouse="true" direction="top-right"  style="width:300px; height:60px; " showDelay="200">
                                    <h:outputText styleClass="tituloCampos" escape="false"
                                                  value="#{msg.msg_tip_mensagem}" />
                                </rich:toolTip>
                            </h:panelGroup>
                            <a4j:commandButton  id="removerImagem" action="#{ModeloMensagemControle.removerImagemUpload}"
                                                reRender="form, comentarioTextArea, imputMensagem, caminhoImagem" value="Remover Imagem" title="Remover Imagem" />
                            <h:outputText   styleClass="tituloCampos" value="Tamanho máximo 350 kb" />
                        </h:panelGrid>
                    </h:panelGroup>

                </h:panelGrid>


                <h:panelGrid id="mensagemEmail" rendered="#{ModeloMensagemControle.modeloMensagemVO.email || ModeloMensagemControle.modeloMensagemVO.ftp}" columns="1" styleClass="tabForm" width="100%" >

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ModeloMensagem_mensagem}" />
                    <rich:editor configuration="editorModelopropriedades" viewMode="visual" theme="advanced"
                                 id="imputMensagem" height="500" width="700" value="#{ModeloMensagemControle.modeloMensagemVO.mensagem}"/>

                </h:panelGrid>


                <h:panelGrid id="mensagemSMSAPP" columns="1" rendered="#{ModeloMensagemControle.modeloMensagemVO.sms or ModeloMensagemControle.modeloMensagemVO.app}"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%" >
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Agenda_comentario}" />
                    <rich:spacer height="20"/>
                    <h:outputLink value="#{SuperControle.urlWikiCRM}Cadastros:Modelo_de_Mensagem#SMS"
                                  rendered="#{ModeloMensagemControle.modeloMensagemVO.sms}"
                                  title="Clique e saiba mais: Modelo de Mensagem SMS" target="_blank" >
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        <h:outputText value="Modelo de Mensagem - SMS" ></h:outputText>
                    </h:outputLink>
                    <h:inputTextarea
                        style="align:center;" id="comentarioTextAreaapp" cols="100" rows="2"
                        rendered="#{ModeloMensagemControle.modeloMensagemVO.app}"
                        value="#{ModeloMensagemControle.modeloMensagemVO.mensagem}"/>
                    <h:inputTextarea
                        onkeypress="soma(this.value);" onkeyup="soma(this.value);"
                        style="align:center;" id="comentarioTextArea" cols="100" rows="2"
                        rendered="#{ModeloMensagemControle.modeloMensagemVO.sms}"
                        value="#{ModeloMensagemControle.modeloMensagemVO.mensagem}"/>
                    <h:panelGroup id="panelTamanhoRestante" style="align:center;"
                                  layout="block"
                                  rendered="#{ModeloMensagemControle.modeloMensagemVO.sms}">
                        <h:inputText style="align:center;" disabled="true" size="3" id="tamanhoRestante"/>

                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <f:verbatim>
                                    <h:outputText value=" "/>
                                </f:verbatim>
                            </h:panelGrid>
                            <h:commandButton  rendered="#{ModeloMensagemControle.sucesso}" image="./imagensCRM/sucesso.png"/>
                            <h:commandButton rendered="#{ModeloMensagemControle.erro}" image="./imagensCRM/erro.png"/>
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText id="msgGrupo" styleClass="mensagem"  value="#{ModeloMensagemControle.mensagem}"/>
                                <h:outputText  id="msgGrupodet" styleClass="mensagemDetalhada" value="#{ModeloMensagemControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="salvar" reRender="form,panelGridForm,panelTamanhoRestante" action="#{ModeloMensagemControle.gravar}"
                                               oncomplete="#{ModeloMensagemControle.msgAlert}" value="#{msg_bt.btn_gravar}"
                                               title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt" />
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>

</f:view>
<script>
    document.getElementById("form:titulo").focus();
</script>

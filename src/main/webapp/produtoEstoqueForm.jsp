<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Produtos com Controle de Estoque"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ProdutoEstoque_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Controle_de_Estoque:Configurar_Produto_Estoque"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{ProdutoEstoqueControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2"
                             rowClasses="linhaImpar, linhaPar"
                             rendered="#{ProdutoEstoqueControle.listaProdutoEstoqueVO != null}"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText rendered="#{ProdutoEstoqueControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="Empresa" />
                    <h:panelGroup rendered="#{ProdutoEstoqueControle.usuarioLogado.administrador}">
                        <h:selectOneMenu  id="ProdEst_empresa" onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          styleClass="form"
                                          value="#{ProdutoEstoqueControle.codigoEmpresa}" >
                            <f:selectItems  value="#{ProdutoEstoqueControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>

                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos"   value="Adicionar" />
                    <h:selectOneRadio id="tipoAdicionar"
                                      styleClass="tituloCampos"
                                      value="#{ProdutoEstoqueControle.tipoAdicionar}" >
                        <f:selectItem itemLabel="um produto específico" itemValue="1" ></f:selectItem>
                        <f:selectItem itemDescription="Somente os produtos do tipo 'Produto Estoque' serão adicionados. " itemLabel="vários produtos de uma categoria" itemValue="2" ></f:selectItem>
                        <a4j:support event="onclick" reRender="panelProduto1, panelProduto2, panelCategoria1, panelCategoria2" ></a4j:support>
                    </h:selectOneRadio>

                    <h:panelGroup id="panelProduto1">
                        <h:outputText  styleClass="tituloCampos" id="lblProduto" rendered="#{ProdutoEstoqueControle.tipoAdicionar == 1}" value="Produto" />
                    </h:panelGroup>
                    <h:panelGroup id="panelProduto2">
                        <h:inputText  id="nomeProdutoSelecionado"
                                      size="50"
                                      rendered="#{ProdutoEstoqueControle.tipoAdicionar == 1}"
                                      maxlength="50"
                                      onblur="blurinput(this);"
                                      onfocus="focusinput(this);"
                                      styleClass="form"
                                      value="#{ProdutoEstoqueControle.produtoSelecionado}" />

                        <rich:suggestionbox   height="200" width="400"
                                              for="nomeProdutoSelecionado"
                                              rendered="#{ProdutoEstoqueControle.tipoAdicionar == 1}"
                                              status="statusInComponent"
                                              suggestionAction="#{ProdutoEstoqueControle.executarAutocompletePesqProduto}"
                                              minChars="1"
                                              reRender="panelMensagemErro"
                                              rowClasses="linhaImpar, linhaPar"
                                              ajaxSingle="false"
                                              var="result"
                                              id="suggestionProduto">
                            <a4j:support event="onselect"
                                         reRender="form, panelMensagemErro, ProdEst_empresa"
                                         focus="form:estoqueMinimo"
                                         action="#{ProdutoEstoqueControle.selecionarProduto}">
                            </a4j:support>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Nome"  styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText styleClass="textverysmall" value="#{result.descricao}" />
                            </h:column>
                            <h:column >
                                <f:facet name="header">
                                    <h:outputText value="Categoria" styleClass="textverysmall"/>
                                </f:facet>
                                <h:outputText  styleClass="textverysmall" value="#{result.categoriaProduto.descricao}" />
                            </h:column>
                        </rich:suggestionbox>
                    </h:panelGroup>

                    <h:panelGroup id="panelCategoria1">
                        <h:outputText rendered="#{ProdutoEstoqueControle.tipoAdicionar == 2}"  id="lblCategoria" styleClass="tituloCampos" value="Categoria:" />
                    </h:panelGroup>
                    <h:panelGroup id="panelCategoria2">
                        <h:selectOneMenu  id="prodEst_Categoria" onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          rendered="#{ProdutoEstoqueControle.tipoAdicionar == 2}"
                                          styleClass="form"
                                          value="#{ProdutoEstoqueControle.codigoCategoria}" >
                            <f:selectItems  value="#{ProdutoEstoqueControle.listaSelectItemTodasCategoria}" />
                        </h:selectOneMenu>
                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="Estoque Mínimo" />
                    <h:inputText  id="estoqueMinimo"
                                  size="8"
                                  maxlength="5" onblur="blurinput(this);"
                                  onfocus="focusinput(this);"
                                  styleClass="form"
                                  value="#{ProdutoEstoqueControle.estoqueMinimo}" />

                </h:panelGrid>
                <h:panelGrid columns="1"
                             width="100%"
                             rendered="#{ProdutoEstoqueControle.listaProdutoEstoqueVO != null}"
                             styleClass="centralizado">
                    <a4j:commandButton id="btnAdicionarProduto"
                                       reRender="form"
                                       onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                       focus="form:nomeProdutoSelecionado"
                                       action="#{ProdutoEstoqueControle.adicionarProdutoEstoque}"
                                       rendered="#{ProdutoEstoqueControle.permiteGravar}"
                                       value="#{msg_bt.btn_adicionar}"
                                       image= "./imagens/botaoAdicionar.png" accesskey="6" styleClass="botoes"/>
                </h:panelGrid>


                <%--<h:panelGrid id="panelMensagemErro2" columns="3" width="100%" styleClass="tabMensagens">--%>
                    <%--<h:panelGrid columns="1" width="100%">--%>

                        <%--<h:outputText value=" "/>--%>

                    <%--</h:panelGrid>--%>
                    <%--<h:commandButton  rendered="#{ProdutoEstoqueControle.sucesso}" image="./imagens/sucesso.png"/>--%>
                    <%--<h:commandButton rendered="#{ProdutoEstoqueControle.erro}" image="./imagens/erro.png"/>--%>
                    <%--<h:panelGrid columns="1" width="100%">--%>
                        <%--<h:outputText id="msgProdutoEstoque2" styleClass="mensagem"  value="#{ProdutoEstoqueControle.mensagem}"/>--%>
                        <%--<h:outputText id="msgProdutoEstoqueDet2" styleClass="mensagemDetalhada" value="#{ProdutoEstoqueControle.mensagemDetalhada}"/>--%>
                    <%--</h:panelGrid>--%>
                <%--</h:panelGrid>--%>


                <h:panelGrid columns="1"
                             width="100%"
                             rendered="#{ProdutoEstoqueControle.listaProdutoEstoqueVO != null}"
                             styleClass="tabFormSubordinada">
                    <rich:dataTable id="tbItens" width="100%" headerClass="subordinado"
                                    rows="10"
                                    rowClasses="linhaImpar, linhaPar" columnClasses="centralizado, centralizado, centralizado, centralizado"
                                    value="#{ProdutoEstoqueControle.listaProdutoEstoqueVOList}" var="produtoEstoqueItem">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText  value="Empresa" />
                            </f:facet>
                            <h:outputText  value="#{produtoEstoqueItem.empresa.nome}" />
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText  value="Produto" />
                            </f:facet>
                            <h:outputText  value="#{produtoEstoqueItem.produto.descricao}" />
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText  value="Estoque Mínimo" />
                            </f:facet>
                            <h:outputText   value="#{produtoEstoqueItem.estoqueMinimo}" />
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText  value="Pontos Produto" />
                            </f:facet>
                            <h:outputText   value="#{produtoEstoqueItem.pontos}" />
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText  value="#{msg_bt.btn_opcoes}" />
                            </f:facet>

                            <h:panelGroup >
                                <h:outputText value="    "/>
                                <a4j:commandButton  reRender="tbItens,gridItens, panelMensagemErro"  id="removerItem"
                                                    onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                    immediate="true" action="#{ProdutoEstoqueControle.removerProdutoEstoqueDaLista}"
                                                    value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                <a4j:commandButton reRender="form"
                                                   onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                   id="linkEditarProdutoEstoqueItem"
                                                   action="#{ProdutoEstoqueControle.editarProdutoEstoqueDaLista}"
                                                   value="#{msg_bt.btn_editar}"
                                                   image="./imagens/botaoEditar.png"
                                                   alt="#{msg.msg_editar_dados}"
                                                   styleClass="botoes"/>
                            </h:panelGroup>
                        </rich:column>

                    </rich:dataTable>
                    <rich:datascroller align="center" for="form:tbItens" maxPages="10"
                                       id="scResultadoProdutoEstoque" />

                </h:panelGrid>


                <h:panelGrid columns="2"
                             rowClasses="linhaImpar, linhaPar"
                             rendered="#{ProdutoEstoqueControle.listaProdutoEstoqueVO == null}"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText styleClass="tituloCampos" value="Empresa" />
                    <h:outputText  id="idEmpresa2" styleClass="tituloCampos"
                                   value="#{ProdutoEstoqueControle.produtoEstoqueVO.empresa.nome}" />
                    <h:outputText styleClass="tituloCampos" value="Produto" />
                    <h:outputText  id="idProduto2" styleClass="tituloCampos"
                                   value="#{ProdutoEstoqueControle.produtoEstoqueVO.produto.descricao}" />

                    <h:outputText styleClass="tituloCampos" value="Estoque Mínimo"/>
                    <h:inputText  id="idEstoqueMinimo2"
                                  size="8"
                                  maxlength="5" onblur="blurinput(this);"
                                  onfocus="focusinput(this);" styleClass="form"
                                  value="#{ProdutoEstoqueControle.produtoEstoqueVO.estoqueMinimo}" />

                    <h:outputText styleClass="tituloCampos" value="1ª Data Configuração: " />
                    <h:outputText styleClass="tituloCampos" value="#{ProdutoEstoqueControle.produtoEstoqueVO.dataConfiguracaoEstoque}" >
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"></f:convertDateTime>
                    </h:outputText>

                </h:panelGrid>


                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{ProdutoEstoqueControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ProdutoEstoqueControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgProdutoEstoque" styleClass="mensagem"  value="#{ProdutoEstoqueControle.mensagem}"/>
                            <h:outputText id="msgProdutoEstoqueDet" styleClass="mensagemDetalhada" value="#{ProdutoEstoqueControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>

                            <h:panelGroup id="grupoLinkCancelarProdutoEstoque" rendered="#{ProdutoEstoqueControle.produtoEstoqueVO.situacao eq 'A'}">
                                <a4j:commandButton id="linkCancelarProdutoEstoque" reRender="mdlMensagemGenerica" onclick="fireElementFromAnyParent('form:btnAtualizaTempo');"
                                                   oncomplete="#{ProdutoEstoqueControle.msgAlert}" action="#{ProdutoEstoqueControle.confirmarExcluir}"  value="#{msg_bt.btn_cancelar}"
                                                   styleClass="botoes nvoBt btSec" style="margin-right: 5px;" title="Retirar Produto do Controle de Estoque" >
                                    <f:param name="metodochamar" value="alterarSituacaoProdutoEstoque"/>
                                </a4j:commandButton>
                            </h:panelGroup>

                            <h:panelGroup id="grupoLinkAtivarProdutoEstoque" rendered="#{ProdutoEstoqueControle.produtoEstoqueVO.situacao eq 'C'}">
                                <a4j:commandButton id="linkAtivarProdutoEstoque" reRender="mdlMensagemGenerica" onclick="fireElementFromAnyParent('form:btnAtualizaTempo');"
                                                   oncomplete="#{ProdutoEstoqueControle.msgAlert}" action="#{ProdutoEstoqueControle.confirmarExcluir}"   value="#{msg_bt.btn_ativar}"
                                                   styleClass="botoes nvoBt btSec" style="margin-right: 5px;" title="Incluir Produto ao Controle de Estoque" >
                                    <f:param name="metodochamar" value="alterarSituacaoProdutoEstoqueAtivar"/>
                                </a4j:commandButton>
                            </h:panelGroup>

                            <h:commandButton id="novo"  immediate="true" action="#{ProdutoEstoqueControle.novo}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:commandButton id="salvar" rendered="#{ProdutoEstoqueControle.permiteGravar}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             action="#{ProdutoEstoqueControle.gravar}" value="#{msg_bt.btn_gravar}"
                                             alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:commandButton id="consultar" immediate="true" action="#{ProdutoEstoqueControle.inicializarConsultar}"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             value="#{msg_bt.btn_voltar_lista}"  alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                            <a4j:commandLink rendered="#{ProdutoEstoqueControle.listaProdutoEstoqueVO == null}" action="#{ProdutoEstoqueControle.realizarConsultaLogObjetoSelecionado}"
                                             id="logProduto"
                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                             style="display: inline-block; padding: 8px 15px;">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:nomeProdutoSelecionado").focus();
</script>
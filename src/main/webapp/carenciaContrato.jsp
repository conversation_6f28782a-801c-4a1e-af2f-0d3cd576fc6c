<%--
    Document   : carenciaContrato
    Created on : 03/08/2009, 09:21:27
    Author     : pedro
--%>
<%@page contentType="text/html"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="script/Notifier.js"></script>

<script type="text/javascript" language="javascript">
    setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<script type="text/javascript" language="javascript">
    jQuery.noConflict();
  </script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>


<style type="text/css">
    .panelCarenciaContrato > tbody > tr > td:first-child{
        vertical-align:baseline;
    }
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
    .to-uper-case{
        text-transform: uppercase;
    }
</style>

<script charset="ISO-8859-1" >
    function validar_Data_Notifier(Ncampo){
        b = document.getElementById(Ncampo).value;
        if (b != "" && b != "__/__/____") {
            if (b.length == 10){
                var dia = b.substring(0,2);
                var mes = b.substring(3,5);
                var ano = b.substring(6,10);
                if ((ano < 1900) || (ano > 2099)) {
                    Notifier.error("O ano especificado � inv�lido.", "Data incorreta!");
                    document.getElementById(Ncampo).focus();
                    return false;
                }
                if ((mes <= 0) || (mes > 12)) {
                    Notifier.error("O m�s especificado � inv�lido.", "Data incorreta!");
                    document.getElementById(Ncampo).focus();
                    return false;
                }
                if (dia <= 0) {
                    Notifier.error("Dia especificado � inv�lido.", "Data incorreta!");
                    document.getElementById(Ncampo).focus();
                    return false;
                }
                if ((mes==1 || mes==3 || mes==5 || mes==7 || mes==8 || mes==10 || mes==12) && (dia > 31)) {
                    Notifier.error("O m�s especificado cont�m no m�ximo 31 dias", "Data incorreta!");
                    document.getElementById(Ncampo).focus();
                    return false;
                } else if ((mes==4 || mes==6 || mes==9 || mes==11) && (dia > 30)) {
                    Notifier.error("O m�s especificado cont�m no m�ximo 30 dias", "Data incorreta!");
                    document.getElementById(Ncampo).focus();
                    return false;
                } else {
                    if ((ano%4!=0) && (mes==2) && (dia>28)) {
                        Notifier.error("O m�s especificado cont�m no m�ximo 28 dias.", "Data incorreta!");
                        document.getElementById(Ncampo).focus();
                        return false;
                    } else{
                        if ((ano%4==0) && (mes==2) && (dia>29)) {
                            Notifier.error("O m�s especificado cont�m no m�ximo 29 dias.", "Data incorreta!");
                            document.getElementById(Ncampo).focus();
                            return false;
                        } else{
                            return true;
                        }
                    }
                }
            } else {
                Notifier.error(b + "(Exemplo de data: dd/mm/aaaa).", "Formato ou data inv�lida!");
                document.getElementById(Ncampo).focus();
                return false;
            }
        }else {
            Notifier.error(b + "(Exemplo de data: dd/mm/aaaa).", "Formato ou data inv�lida!");
            document.getElementById(Ncampo).focus();
            return false;
        }
    }
</script>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-lancar-ferias-para-aluno/"/>
        <c:set var="titulo" scope="session" value="F�rias"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="form">
        <h:panelGrid columns="2" styleClass=" panelCarenciaContrato" width="100%">
            <%--<h:panelGrid columns="1" style="width: 200px;" >--%>
            <%--</h:panelGrid>--%>
            <h:panelGrid columns="1" cellpadding="0" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                <h:outputText id="nomeAlunoCar" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font to-uper-case" value="#{CarenciaContratoControle.carenciaContratoVO.contratoVO.pessoa.nome}"/>
                <rich:dataTable id="carencia" width="100%" rowClasses="tablelistras textsmall"  columnClasses="col-text-align-left" styleClass="tabelaSimplesCustom" headerClass="col-text-align-left"
                                value="#{CarenciaContratoControle.listaContratoVOs}" var="contrato" >
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_numeroContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_plano}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.plano.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_dataInicio}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaDe_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_dataTermino}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_valorBaseContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.valorBaseCalculo}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>
                <h:panelGroup>
                    <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" 
                           class="formNovo"
                           style="margin-right:20px;margin-bottom:5px;padding:5px;margin-top: 20px;">
                        <tr>
                            <td align="left" valign="top" ><div class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" >
                                F�RIAS</div>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" valign="top">
                                <h:panelGrid id= "panelCarencia" width="100%" style="margin-top: 10px;">
                                    <h:panelGroup style="padding-left: 10px;">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Quantidade de dias permitidos:" />
                                        <rich:spacer width="10px"/>
                                        <h:outputText id="numCarenciaPer"  rendered="#{!CarenciaContratoControle.editando}"
                                                      styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.nrDiasPermitido}" />
                                        <h:panelGroup styleClass="clienteMarcado">
                                            <a4j:commandLink action="#{CarenciaContratoControle.alterarDias}"  
                                                         style="margin-left: 10px;"
                                                         id="btnAlterarDiasCarencia"
                                                         rendered="#{!CarenciaContratoControle.editando}"
                                                         reRender="panelCarencia, panelAutorizacaoFuncionalidade">
                                            <i class="fa-icon-edit texto-size-18 linkAzul tooltipster"></i> 
                                            
                                        </a4j:commandLink>
                                            <h:panelGroup layout="block"  styleClass="mensagemCmarc" style="width: 250px; margin: 15px calc(50% - 136px);">
                                                <h:outputText styleClass="texto-font texto-cor-cinza texto-size-12" 
                                                              value="Altere o n�mero de dias das F�rias para esse contrato.Voc� vai precisar da permiss�o '3.36 - Alterar manualmente a quantidade de dias de f�rias permitidos'" escape="false"/>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                        
                                        


                                        <h:inputText id="innumCarenciaPer"  rendered="#{CarenciaContratoControle.editando}"
                                                      styleClass="formNovo"  style="width: 60px;"
                                                      value="#{CarenciaContratoControle.novoDiasCarencia}" />
                                        <h:panelGroup styleClass="clienteMarcado">
                                            <a4j:commandLink action="#{CarenciaContratoControle.confirmarAlteracao}"  
                                                         style="margin-left: 10px;"
                                                         id="btnConfirmarAlteracao"
                                                         rendered="#{CarenciaContratoControle.editando}"
                                                         reRender="form">
                                            <i class="fa-icon-ok texto-size-18 linkAzul tooltipster"></i> 
                                        </a4j:commandLink>
                                             <h:panelGroup layout="block"  styleClass="mensagemCmarc" style="width: 250px; margin: 15px calc(50% - 136px);">
                                                <h:outputText styleClass="texto-font texto-cor-cinza texto-size-12" 
                                                              value="Confirme a altera��o do n�mero de dias de f�rias desse contrato." escape="false"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                            <h:panelGroup styleClass="clienteMarcado">
                                                <a4j:commandLink action="#{CarenciaContratoControle.realizarConsultaLogObjetoSelecionado}"  
                                                                 style="margin-left: 10px;" 
                                                                 reRender="panelCarencia, panelAutorizacaoFuncionalidade"
                                                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                                    <i class="fa-icon-list texto-size-18 linkAzul tooltipster"></i> 
                                                </a4j:commandLink>
                                                <h:panelGroup layout="block"  styleClass="mensagemCmarc" style="width: 250px; margin: 15px calc(50% - 136px);">
                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-12" 
                                                                  value="Visualize as altera��es no n�mero de dias de f�rias desse contrato." escape="false"/>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                    </h:panelGroup>
                                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    <h:panelGroup style="padding-left: 10px;">
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Quantidade de dias utilizados:" />
                                        <rich:spacer width="10px"/>
                                        <h:outputText id="numCarenciaUsa" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.nrDiasUsados}" />
                                    </h:panelGroup>
                                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    <h:panelGroup style="padding-left: 10px;">
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Quantidade de dias restantes:" />
                                        <rich:spacer width="10px"/>
                                        <h:outputText id="numCarenciaRes" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.nrDiasRestam}" />
                                    </h:panelGroup>
                                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    <h:panelGroup style="padding-left: 10px;">
                                        <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Quantidade m�nima de dias para solicitar f�rias:" />
                                        <rich:spacer width="10px"/>
                                        <h:outputText id="numCarenciaMin" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carencia}" />
                                    </h:panelGroup>
                                    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                </h:panelGrid>
                            </td>
                        </tr>
                    </table>
                    <br/>
                    <div class="texto-size-14-real texto-cor-vermelho texto-upper texto-font texto-bold">
                        <h:outputText id="msgCarenciaErro" value="#{CarenciaContratoControle.mensagemCarrencia}"/>
                    </div>
                    <br/>
                    <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-right:20px;margin-bottom:5px;padding:5px;display: inline">
                        <tr>
                            <td align="left" valign="top" >
                                <div class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">
                                    OPERA��ES
                                </div>
                                <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" valign="top">
                                <rich:dataTable id="listaContratoOperacao" width="100%" border="0" rows="5" cellspacing="0" cellpadding="2" columnClasses="col-text-align-left" styleClass="tabelaSimplesCustom" headerClass="col-text-align-left"
                                                value="#{CarenciaContratoControle.listaContratoOperacaoVOs}" var="contratoOperacao">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_ContratoOperacao_tipoOperacao}"/>
                                        </f:facet>
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contratoOperacao.tipoOperacao_Apresentar}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Data In�cio"/>
                                        </f:facet>
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contratoOperacao.dataInicioEfetivacaoOperacao_Apresentar}">
                                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                                        </h:outputText>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="Data T�rmino"/>
                                        </f:facet>
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contratoOperacao.dataFimEfetivacaoOperacao_Apresentar}">
                                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                                        </h:outputText>
                                    </rich:column>
                                </rich:dataTable>
                                <rich:datascroller align="center" for="listaContratoOperacao" maxPages="2"
                                                   styleClass="scrollPureCustom" renderIfSinglePage="false"
                                                   id="scResultadoContratoOperacao"/>
                                <%--<rich:datascroller align="center" for="listaContratoOperacao" maxPages="10" id="scResultadoContratoOperacao"   />--%>
                            </td>
                        </tr>
                    </table>
                </h:panelGroup>
                <h:panelGrid>
                    <div class="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">
                        PER�ODO DE F�RIAS
                    </div>
                    <h:panelGrid columns="1"  style="padding-left: 10px;">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="IN�CIO" />
                        <h:panelGroup>
                            <h:panelGroup styleClass="dateTimeCustom">
                                <rich:calendar id="dataInicio"
                                               value="#{CarenciaContratoControle.carenciaContratoVO.dataInicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               onchanged="validar_Data_Notifier(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate());"
                                               oninputchange="validar_Data_Notifier(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate());return validar_Data_Notifier(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               zindex="2"
                                               showWeeksBar="false" >
                                </rich:calendar>
                            </h:panelGroup>
                            <%-- ajax function para fazer o binding das datas inicio e fim executando os javascripts antes de cada componente--%>
                            <a4j:jsFunction name="gerarPeriodoRetorno"
                                            action="#{CarenciaContratoControle.gerarPeriodoRetornoCarencia}"
                                            reRender="form, panelAutorizacaoFuncionalidade">
                                <a4j:actionparam name="dtinicio" assignTo="#{CarenciaContratoControle.carenciaContratoVO.dataInicio}"/>
                                <a4j:actionparam name="dtfim" assignTo="#{CarenciaContratoControle.carenciaContratoVO.dataTermino}"/>
                            </a4j:jsFunction>
                        </h:panelGroup>
                        <rich:spacer height="10px;"/>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="AT�" />
                        <h:panelGroup styleClass="dateTimeCustom">
                            <rich:calendar id="dataTermino"
                                           value="#{CarenciaContratoControle.carenciaContratoVO.dataTermino}"
                                           inputSize="10"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           onchanged="validar_Data_Notifier(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate());"
                                           oninputchange="validar_Data_Notifier(this.id);gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate()); return validar_Data_Notifier(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           zindex="2"
                                           showWeeksBar="false" >
                            </rich:calendar>
                        </h:panelGroup>
                        <rich:spacer height="10px;"/>
                        <h:panelGroup>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="PER�ODO DE DIAS: " />
                            <h:outputText id="diasCarenciaSolicitado" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.periodoCarencia}"/>
                        </h:panelGroup>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="panelPeridoRetorno">
                    <h:panelGrid>
                        <h:panelGroup layout="block" >
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" value="RETORNO DA F�RIAS"/>
                        </h:panelGroup>
                        <rich:spacer height="10px;"/>
                        <h:panelGroup rendered="#{CarenciaContratoControle.carenciaContratoVO.periodoCarencia > 0}"   style="padding-left: 10px;">
                            <h:panelGrid id= "panelDadosContrato" >
                                <h:panelGroup>
                                    <h:outputText rendered="#{CarenciaContratoControle.carenciaContratoVO.contratoVO.vendaCreditoTreino}" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Os valores abaixo levam em considera��o a data de inicio da opera��o e consideram dias de b�nus:" />
                                    <h:outputText rendered="#{!CarenciaContratoControle.carenciaContratoVO.contratoVO.vendaCreditoTreino}" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Os valores abaixo levam em considera��o a data de inicio da opera��o e n�o consideram dias de b�nus positivo:" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText style="font-weight: bold;" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Dias Contrato: " />
                                    <rich:spacer width="10px"/>
                                    <h:outputText id="numDiasContrato" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.nrDiasContrato}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText style="font-weight: bold;" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Dias Utilizados Pelo Cliente: " />
                                    <rich:spacer width="10px"/>
                                    <h:outputText id="numDiasUsados" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.nrDiasUsadoContrato}" />
                                </h:panelGroup>
                                <h:panelGroup>
                                    <h:outputText style="font-weight: bold;" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Dias Restante do Contrato: " />
                                    <rich:spacer width="10px"/>
                                    <h:outputText id="numDiasRestantesCon" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.nrDiasRestanteContrato}" />
                                </h:panelGroup>
                            </h:panelGrid>
                        </h:panelGroup>
                        <h:panelGroup   style="padding-left: 10px;">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="IN�CIO:" />
                            <rich:spacer width="10"/>
                            <h:outputText id="dataInicioRetorno" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.dataInicioRetorno_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                            <rich:spacer width="10"/>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value=" AT�: " />
                            <rich:spacer width="10"/>
                            <h:outputText id="dataFimRetorno"  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CarenciaContratoControle.carenciaContratoVO.dataTerminoRetorno_Apresentar}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" >
                    <h:panelGrid>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_tipoJustificativa}" />
                        <h:panelGroup styleClass="font-size-em-max">
                            <div class="cb-container margenVertical" style="width: 300px;">
                                <h:selectOneMenu  id="justificativa"
                                                  onblur="blurinput(this);"
                                                  onfocus="focusinput(this);"
                                                  styleClass="form texto-size-12-real texto-cor-cinza texto-font"
                                                  value="#{CarenciaContratoControle.carenciaContratoVO.tipoJustificativa}" >
                                    <f:selectItems  value="#{CarenciaContratoControle.listaJustificativaOperacaoVOs}" />
                                    <a4j:support event="onchange" action="#{CarenciaContratoControle.limparMensagem}" reRender="panelMensagem"/>
                                </h:selectOneMenu>
                            </div>
                            <rich:spacer width="3"/>
                            <a4j:commandLink id="atualizar_justificativaDeOperacao" action="#{CarenciaContratoControle.montarDadosListaJustificativaOperacaoVOs}" immediate="true" ajaxSingle="true" reRender="form:justificativa">
                                <i class="fa-icon-refresh texto-size-14-real texto-cor-cinza " ></i>
                            </a4j:commandLink>
                            <rich:spacer width="5"/>
                            <a4j:commandLink id="adicionarTipoOperacao" action="#{JustificativaOperacaoControle.reset}" title="Cadastrar Tipo Justificativa Opera��o " oncomplete="abrirPopup('justificativaOperacaoCons.jsp', 'JustificativaOperacao', 800, 595);" >
                                <i class="fa-icon-plus-sign texto-size-14-real texto-cor-cinza "/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="1" >
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="OBSERVA��O: "/>
                    <h:inputTextarea value="#{CarenciaContratoControle.carenciaContratoVO.observacao}" styleClass="texto-size-14-real texto-cor-cinza texto-font" rows="3" cols="93" style="width: 100%;height: 35px"/>

                </h:panelGrid>
                <h:panelGrid id="panelMensagem" columns="4" width="100%" >
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText value=""/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid id="panelBotoes" width="100%" columns="2" >
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup rendered="#{CarenciaContratoControle.apresentarBotoes && !CarenciaContratoControle.processandoOperacao}">
                            <a4j:commandLink reRender="form,panelAutorizacaoFuncionalidade,panel"
                                             id="confirmar"
                                             action="#{CarenciaContratoControle.validarDadosCarencia}"
                                             styleClass="pure-button pure-button-primary"
                                             oncomplete="#{CarenciaContratoControle.mensagemNotificar}">
                                <i class="fa-icon-ok"></i>&nbsp;Confirmar
                            </a4j:commandLink>

                            <rich:spacer width="7"/>
                            <a4j:commandLink id="cancelar"  onclick="fecharJanela();executePostMessage({close: true});" styleClass="pure-button">
                                <i class="fa-icon-remove"></i>&nbsp;Cancelar
                            </a4j:commandLink>
                        </h:panelGroup>
                            <h:panelGroup rendered="#{!CarenciaContratoControle.apresentarBotoes && !CarenciaContratoControle.processandoOperacao}">
                            <a4j:commandLink id="comprovanteOpCa"
                                             title="Imprimir Comprovante da Opera��o de F�rias"
                                             action="#{CarenciaContratoControle.imprimirComprovanteOperacao}"
                                             oncomplete="abrirPopupPDFImpressao('relatorio/#{CarenciaContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                             styleClass="pure-button" rendered="#{CarenciaContratoControle.mensagemCarrencia == null}">
                                <i class="fa-icon-print"></i> &nbsp Imprimir Comprovante
                            </a4j:commandLink>
                            <rich:spacer width="7"/>
                            <h:commandLink id="fechar" title="Fechar Janela"
                                           onclick="fecharJanela();executePostMessage({close: true});"
                                           styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-remove"></i>&nbsp;Fechar
                            </h:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid rendered="#{CarenciaContratoControle.processandoOperacao}" columns="1">
                    <h:outputText id="msgProcessando" styleClass="mensagem"  value="Opera��o j� est� sendo processada. Dentro de alguns instantes, atualize a p�gina para verificar se opera��o j� foi conclu�da"/>
                    <rich:spacer height="7"/>
                    <a4j:commandLink id="atualizar" title="Atulaizar" onclick="window.location.reload();fireElementFromParent('form:btnAtualizaCliente');"  styleClass="pure-button pure-button-primary">
                        <i class="fa-icon-refresh"></i>&nbsp;Atualizar
                    </a4j:commandLink>
               </h:panelGrid>                
            </h:panelGrid>
        </h:panelGrid>
    </h:form>

    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

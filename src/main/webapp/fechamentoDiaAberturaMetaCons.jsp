<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
body {
	margin: 0px;
	padding: 0px;
}
</style>

<f:view>
	<jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
	<title><h:outputText value="#{msg_aplic.prt_FechamentoDia_tituloForm}" /></title>


	<rich:modalPanel id="panelJustificativa" domElementAttachment="parent" autosized="true" shadowOpacity="true" width="450" height="300" onshow="document.getElementById('formJustificativa:').focus();">
		<f:facet name="header">
			<h:panelGroup>
				<h:outputText value="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
			</h:panelGroup>
		</f:facet>
		<f:facet name="controls">
			<h:panelGroup>
				<h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer" id="hiperlinkJustificativa" />
				<rich:componentControl for="panelJustificativa" attachTo="hiperlinkJustificativa" operation="hide" event="onclick" />
			</h:panelGroup>
		</f:facet>
		<a4j:form id="formJustificativa" ajaxSubmit="true">
			<h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
				<rich:editor width="400" value="#{AberturaMetaControle.fecharMetaJustificativa.justificativa}" />
				<a4j:commandButton id="btnSucesso" rendered="#{AberturaMetaControle.apresentarBotoesGravarAdicionarJustificativa}" oncomplete="Richfaces.hideModalPanel('panelJustificativa')" reRender="form:panelMensagemJustificativa, form:panelGridForm" action="#{AberturaMetaControle.adicionarFecharMetaJustificativa}" styleClass="botoes" image="./imagensCRM/botaoAdicionarJustificativa.png" />
			</h:panelGrid>
		</a4j:form>
	</rich:modalPanel>


	<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
		<f:facet name="header">
			<jsp:include page="topoReduzidoCRM.jsp" />
		</f:facet>
		<h:form id="formAberturaMeta">
			<h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
				<h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
					<h:panelGroup>
						<h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_FechamentoDia_tituloForm}" />
						<rich:spacer width="10px;" />
						<h:outputText styleClass="tituloFormulario" value="#{AberturaMetaControle.fecharAbertura.dia_Apresentar}" />
					</h:panelGroup>
				</h:panelGrid>				

				<h:panelGrid columns="1" width="100%">
					<rich:dataTable id="itemsFecharMeta" width="100%" headerClass="consulta" columnClasses="colunaEsquerda" rowClasses="linhaImpar, linhaPar" style="border:none;" value="#{AberturaMetaControle.fecharAbertura.listaFechametoDia}" var="fecharMeta">
						<rich:column style="border:none;">
							<f:facet name="header">
								<h:outputText value="#{msg_aplic.prt_FechamentoDia_identificadorMeta}" />
							</f:facet>
							<h:outputText styleClass="camposIndicadorVendas" value="#{fecharMeta.identificadorMeta_Apresentar}" />
						</rich:column>
						<rich:column rendered="#{!fecharMeta.apresentarImagemFaturamento}" style="border:none; vertical-align: top; text-align: center;">
							<f:facet name="header">
								<h:outputText value="#{msg_aplic.prt_FechamentoDia_metas}" />
							</f:facet>
							<h:outputText styleClass="camposIndicadorVendasMeta" value="#{fecharMeta.meta}" >
								<f:convertNumber pattern="0" />
							</h:outputText>
						</rich:column>
						<rich:column rendered="#{fecharMeta.apresentarImagemFaturamento}" style="border:none; vertical-align: top; text-align: center;">
							<f:facet name="header">
								<h:outputText value="#{msg_aplic.prt_FechamentoDia_metas}" >
									<f:converter converterId="FormatadorNumerico" />
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="camposIndicadorVendasMeta" value="#{fecharMeta.meta}" />
						</rich:column>
						<rich:column rendered="#{!fecharMeta.apresentarImagemFaturamento}" style="border:none; vertical-align: top; text-align: center;">
							<f:facet name="header">
								<h:outputText value="#{msg_aplic.prt_FechamentoDia_metaAtingida}" />
							</f:facet>
							<h:outputText styleClass="camposIndicadorVendas" value="#{msg_aplic.prt_AberturaMeta_resultado}" />
							<rich:spacer width="5px" />
							<h:outputText styleClass="camposIndicadorVendasMetaAtingida" value="#{fecharMeta.metaAtingida}" >
								<f:convertNumber pattern="0" />
							</h:outputText>
						</rich:column>
						<rich:column rendered="#{fecharMeta.apresentarImagemFaturamento}" style="border:none; vertical-align: top; text-align: center;">
							<f:facet name="header">
								<h:outputText value="#{msg_aplic.prt_FechamentoDia_metaAtingida}" />
							</f:facet>
							<h:outputText styleClass="camposIndicadorVendas" value="#{msg_aplic.prt_AberturaMeta_resultado}" />
							<rich:spacer width="5px" />
							<h:outputText styleClass="camposIndicadorVendasMetaAtingida" value="#{fecharMeta.metaAtingida}" >
								<f:converter converterId="FormatadorNumerico" />
							</h:outputText>
						</rich:column>
						<rich:column style="border:none; vertical-align: top; text-align: center;">
							<f:facet name="header">
								<h:outputText value="#{msg_aplic.prt_FechamentoDia_percentual}" />
							</f:facet>
							<h:outputText value="#{fecharMeta.porcentagem}" styleClass="camposIndicadorVendasMetaPorcentagem">
								<f:converter converterId="FormatadorNumerico" />
							</h:outputText>
						</rich:column>
						<rich:column style="border:none; vertical-align: top; text-align: center;">
							<f:facet name="header">
								<h:outputText value="#{msg_aplic.prt_FechamentoDia_justificativa}" />
							</f:facet>
							<a4j:commandButton id="btnJustificativa" title="Justificativa" action="#{AberturaMetaControle.executarSelecaoFecharMetaJustificativa}" oncomplete="Richfaces.showModalPanel('panelJustificativa')" reRender="form, formJustificativa" rendered="#{fecharMeta.isMetaAtingidaJustificativa}" styleClass="botoes" image="./imagensCRM/iconeLista.png" />
						</rich:column>
						<rich:column style="border:none; vertical-align: top; text-align: center;">
							<f:facet name="header">
								<h:outputText value="#{msg_aplic.prt_FechamentoDia_status}" />
							</f:facet>
							<a4j:commandButton id="btnSucesso" rendered="#{fecharMeta.isMetaAtingidaSucesso}" styleClass="botoes" image="./imagensCRM/sucesso.png" />
						</rich:column>
					</rich:dataTable>
				</h:panelGrid>
				<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
					<h:panelGroup>
					<a4j:commandButton
						value="imprimir"
						action="#{AberturaMetaControle.executarImpressaoFechamentoDiaAberturaMeta}"
						oncomplete="abrirPopupPDFImpressao('relatorio/#{AberturaMetaControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595);"
						image="imagensCRM/botaoImprimirLista.png">
						
						</a4j:commandButton>
					</h:panelGroup>
				</h:panelGrid>
				<h:panelGroup>
					<h:outputText value="#{msg_aplic.prt_FechamentoDia_colaboradorResponsavel}" styleClass="tituloCampos" />
					<rich:spacer width="10px;" />
					<h:outputText value="#{AberturaMetaControle.fecharAbertura.colaboradorResponsavel.nome}" styleClass="tituloCampos" />
				</h:panelGroup>
				<h:panelGroup>
					<h:outputText value="#{msg_aplic.prt_FechamentoDia_responsavelCadstro}" styleClass="tituloCampos" />
					<rich:spacer width="10px;" />
					<h:outputText value="#{AberturaMetaControle.fecharAbertura.responsavelCadastro.nome}" styleClass="tituloCampos" />
				</h:panelGroup>
				<h:panelGrid id="panelMensagemJustificativa" columns="1" width="100%">
					<h:outputText styleClass="mensagem" value="#{AberturaMetaControle.mensagem}" />
					<h:outputText styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}" />
				</h:panelGrid>
			</h:panelGrid>
		</h:form>
	</h:panelGrid>
</f:view>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>

<h:panelGroup layout="block" styleClass="menuLateral">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-briefcase"></i> Config. Contrato
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.planoTextoPadrao}">
            <a4j:commandLink value="Modelo de Contrato"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="MODELO_CONTRATO"/>
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._de_Contrato:Modelo_de_Contrato_e_Recibo"
                          title="Clique e saiba mais: Modelo de Contrato" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.movProduto}">
            <a4j:commandLink value="Movimento do Produto"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="MOVIMENTO_PRODUTO"/>
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}relatorio-movimento-do-produto/"
                          title="Clique e saiba mais: Movimento de Produto" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.justificativaOperacao}">
            <a4j:commandLink value="#{msg_menu.Menu_justificativaOperacao}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="JUSTIFICATIVA_OPERACAO"/>
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._de_Contrato:Justificativa_de_Opera��o"
                          title="Clique e saiba mais: Justificativa de Opera��o" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem"
                      rendered="#{LoginControle.permissaoAcessoMenuVO.justificativaOperacao}">
            <a4j:commandLink value="#{msg_menu.Menu_impressaoReciboEmBranco}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="IMPRIME_RECIBO_BANCO"/>
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Clientes em Risco"/>
    </title>
    <html>
        <body onload="fireElement('form:botaoAtualizarPagina')">
            <h:form id="form">
                <c:set var="titulo" scope="session" value="Churn Prediction - ${RiscoControle.tituloResumoClientes}"/>
                <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-grupo-de-risco-adm/"/>
                <h:panelGroup layout="block" styleClass="pure-g-r">
                    <f:facet name="header">
                        <jsp:include page="topo_reduzido_popUp.jsp"/>
                    </f:facet>
                </h:panelGroup>
                <a4j:commandButton id="botaoAtualizarPagina" reRender="form:panelGroup,form:listaRisco" style="display:none"/>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                         <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                             <h:panelGroup layout="block">
                                 <h:panelGroup layout="block" styleClass="margin-box">
                                     <div align="right">
                                     <h:panelGroup>
                                         <rich:spacer height="5px"/>

                                         <a4j:commandLink id="imprimirPDF" styleClass="linkPadrao"
                                                            action="#{RiscoControle.imprimirRelatorio}"
                                                            style="margin-bottom: 8px"
                                                            oncomplete="#{RiscoControle.mensagemNotificar}#{RiscoControle.msgAlert}">
                                             <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                         </a4j:commandLink>

                                         <a4j:commandLink id="exportarExcel"
                                                            style="margin-left: 8px;margin-bottom: 8px;"
                                                            actionListener="#{ExportadorListaControle.exportar}"
                                                            rendered="#{not empty RiscoControle.listaRiscoVOs}"
                                                            oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                            accesskey="2" styleClass="linkPadrao">
                                             <f:attribute name="lista" value="#{RiscoControle.listaRiscoVOs}"/>
                                             <f:attribute name="tipo" value="xls"/>
                                             <f:attribute name="itemExportacao" value="biRisco"/>
                                             <f:attribute name="atributos"
                                                          value="matriculaCliente=Matrícula,nomeCliente=Nome,peso=Peso,foneCliente=Telefone"/>
                                             <f:attribute name="prefixo" value="GrupoDeRisco"/>
                                             <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                         </a4j:commandLink>
                                         </div>
                                     </h:panelGroup>

                                     <rich:dataTable id="listaRisco"
                                                     width="100%"
                                                     rows="15"
                                                     styleClass="tabelaSimplesCustom"
                                                     value="#{RiscoControle.listaRiscoVOs}"
                                                     var="risco" rowKeyVar="status">
                                         <rich:column id="matriculaCliente" headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                      width="12%" label="Matrícula" sortBy="#{risco.matriculaCliente}"
                                                      filterEvent="onkeyup">
                                             <f:facet name="header">
                                                 <h:outputText styleClass="rotuloCampos" value="MATRÍCULA"/>
                                             </f:facet>
                                             <a4j:commandLink id="matricula" styleClass="linkPadrao texto-size-16 texto-cor-cinza" action="#{RiscoControle.irParaTelaCliente}"
                                                              value="#{risco.matriculaCliente}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>
                                         </rich:column>
                                         <rich:column id="nomeCliente" headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                      label="Nome" sortBy="#{risco.nomeCliente}" filterEvent="onkeyup">
                                             <f:facet name="header">
                                                 <h:outputText styleClass="rotuloCampos" value="NOME"/>
                                             </f:facet>
                                             <a4j:commandLink id="nome" styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                              action="#{RiscoControle.irParaTelaCliente}" value="#{risco.nomeCliente}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>
                                         </rich:column>
                                         <rich:column id="peso" sortBy="#{risco.peso}" headerClass="col-text-align-center" styleClass="col-text-align-center"
                                                      filterEvent="onkeyup" width="10%" label="Peso">
                                             <f:facet name="header">
                                                 <h:outputText styleClass="rotuloCampos" value="#{BIControle.biRiscoChurn ? '% RISCO DE CHURN' : 'RISCO'}"/>
                                             </f:facet>
                                             <a4j:commandLink id="pesos" styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                              action="#{RiscoControle.irParaTelaCliente}" value="#{risco.peso}"
                                                              rendered="#{!BIControle.biRiscoChurn}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>

                                             <a4j:commandLink id="pesoschurn" styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                              action="#{RiscoControle.irParaTelaCliente}" value="#{risco.peso}%"
                                                              rendered="#{BIControle.biRiscoChurn}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>


                                         </rich:column>
                                         <rich:column id="foneCliente" headerClass="col-text-align-left" styleClass="col-text-align-left"
                                                       width="20%" label="Telefone" sortBy="#{risco.foneCliente}"
                                                      filterEvent="onkeyup">
                                             <f:facet name="header">
                                                 <h:outputText styleClass="rotuloCampos" value="TELEFONE"/>
                                             </f:facet>
                                             <a4j:commandLink id="fone" styleClass="linkPadrao texto-size-16 texto-cor-cinza"
                                                              action="#{RiscoControle.irParaTelaCliente}" value="#{risco.foneCliente}"
                                                              oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                 <f:param name="state" value="AC"/>
                                             </a4j:commandLink>
                                         </rich:column>

                                     </rich:dataTable>
                                     <rich:datascroller id="tabelaRisco" renderIfSinglePage="false" for="listaRisco" styleClass="scrollPureCustom"/>
                                <h:panelGroup layout="block" styleClass="container-botoes" style="text-align: left;">
                                    <h:outputText id="totalRisco" styleClass="texto-font texto-size-20 texto-cor-cinza" value="Total #{fn:length(RiscoControle.listaRiscoVOs)} itens"/>
                                </h:panelGroup>

                                 </h:panelGroup>
                             </h:panelGroup>
                         </h:panelGroup>

                    </h:panelGroup>
                 </h:panelGroup>
            </h:panelGroup>
         </h:form>

</body>
</html>
</f:view>

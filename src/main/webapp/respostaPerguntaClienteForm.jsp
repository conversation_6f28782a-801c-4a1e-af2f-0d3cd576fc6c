<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_RespostaPerguntaCliente_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <jsp:include page="topoReduzido.jsp"/>

        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_RespostaPerguntaCliente_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_RespostaPerguntaCliente_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{RespostaPerguntaClienteControle.respostaPerguntaClienteVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_RespostaPerguntaCliente_descricaoRespota}" />
                    <h:inputText  id="descricaoRespota" size="50" maxlength="50" styleClass="campos" value="#{RespostaPerguntaClienteControle.respostaPerguntaClienteVO.descricaoRespota}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_RespostaPerguntaCliente_perguntaCliente}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="perguntaCliente" styleClass="camposObrigatorios" value="#{RespostaPerguntaClienteControle.respostaPerguntaClienteVO.perguntaCliente.codigo}" >
                            <f:selectItems  value="#{RespostaPerguntaClienteControle.listaSelectItemPerguntaCliente}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_perguntaCliente" action="#{RespostaPerguntaClienteControle.montarListaSelectItemPerguntaCliente}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:perguntaCliente"/>
                        <h:message for="perguntaCliente" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_RespostaPerguntaCliente_respostaOpcao}" />
                    <h:selectBooleanCheckbox id="respostaOpcao"styleClass="campos"value="#{RespostaPerguntaClienteControle.respostaPerguntaClienteVO.respostaOpcao}"/>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_RespostaPerguntaCliente_respostaTextual}" />
                    <h:inputText  id="respostaTextual" size="50" maxlength="50" styleClass="campos" value="#{RespostaPerguntaClienteControle.respostaPerguntaClienteVO.respostaTextual}" />

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{RespostaPerguntaClienteControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{RespostaPerguntaClienteControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{RespostaPerguntaClienteControle.novo}" value="#{msg_bt.btn_novo}" image="./imagens/botaoNovo.png" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{RespostaPerguntaClienteControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{RespostaPerguntaClienteControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true" action="#{RespostaPerguntaClienteControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:descricaoRespota").focus();
</script>
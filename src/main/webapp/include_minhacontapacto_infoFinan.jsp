<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="tituloBoxCanalPacto">
    <h:outputText value="Informações Financeiras" style="font-size: 0.9em!important;" styleClass="texto-size-14 negrito cinzaEscuro pl20"/>

</div>

<style>
     .headGrid .rich-table-subheadercell{
         text-align: left;
         padding: 4px 4px 4px 0px;
         font-size: 1em!important;
         color: #777;
         font-weight: bold;
     }

    .text-vencido {
        color: red;
    }

</style>

<div style="width: calc(100% - 2vw);padding: 1vw;">
    <div id="carregandoParcelas" style="padding: 0 !important;display: none; margin: 0 auto; width: 300px;" >
        <h:graphicImage value="imagens/carregando.gif" style="margin-right: 10px; vertical-align: middle;" />
        <h:outputText value="Consultando dados financeiros..." styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
    </div>
    <div id="divParcelas">
        <h:panelGroup layout="block" id="pnlParcelas">
            <rich:dataTable id="tableParcelasAcademia"
                     rows="#{clienteEstudioControle.nmrPaginaItemVenda}"
                     styleClass="tabelaSimplesCustom noHover"
                     headerClass="headGrid"
                     columnClasses="colunaEsquerda,colunaEsquerda,colunaEsquerda,colunaEsquerda, colunaEsquerda"
                     value="#{CanalPactoControle.parcelasAcademia.parcelas}"
                     var="item">

            <rich:column>
                <f:facet name="header">
                    <h:outputText value="CÓDIGO"/>
                </f:facet>
                <h:outputText rendered="#{item.estaVencido}" styleClass="text-vencido" value="#{item.codigoParcela}"/>
                <h:outputText rendered="#{!item.estaVencido}" value="#{item.codigoParcela}"/>
            </rich:column>
            <rich:column>
                <f:facet name="header">
                    <h:outputText value="DESCRIÇÃO"/>
                </f:facet>
                    <h:outputText rendered="#{item.estaVencido}" styleClass="text-vencido" value="#{item.descricao}"/>
                    <h:outputText rendered="#{!item.estaVencido}" value="#{item.descricao}"/>
            </rich:column>

            <rich:column>
                <f:facet name="header">
                    <h:outputText value="VENCIMENTO"/>
                </f:facet>
                    <h:outputText rendered="#{item.estaVencido}" styleClass="text-vencido" value="#{item.dataVencimentoApresentar}"/>
                    <h:outputText rendered="#{!item.estaVencido}" value="#{item.dataVencimentoApresentar}"/>
            </rich:column>
            <rich:column>
                <f:facet name="header">
                    <h:outputText value="SITUAÇÃO"/>
                </f:facet>
                    <h:outputText rendered="#{item.estaVencido}" styleClass="text-vencido" value="#{item.situacao}"/>
                    <h:outputText rendered="#{!item.estaVencido}" value="#{item.situacao}"/>
            </rich:column>
            <rich:column>
                <f:facet name="header">
                    <h:outputText  value="VALOR"/>
                </f:facet>
                    <h:outputText rendered="#{item.estaVencido}" styleClass="text-vencido" value="#{item.valor}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>

                    <h:outputText rendered="#{!item.estaVencido}" value="#{item.valor}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </rich:column>

            <rich:column>
                <f:facet name="header">
                    <h:outputText  value="IMPRIMIR"/>
                </f:facet>
                <a4j:commandLink rendered="#{item.situacao eq 'Em Aberto'}" styleClass="tooltipster" title="Realize o download de seu Boleto"
                                 action="#{CanalPactoControle.downloadBoleto}"
                                 oncomplete="#{CanalPactoControle.msgAlert} #{CanalPactoControle.mensagemNotificar}">
                    <i class="fa-icon-barcode" style="font-size: 2em"  ></i> &nbsp BOLETO
                    <f:setPropertyActionListener value="#{item}" target="#{CanalPactoControle.parcelaSelecionada}"/>
                </a4j:commandLink>

                <a4j:commandLink rendered="#{(!(item.urlBoleto eq ''))}" styleClass="tooltipster" title="Realize o download de sua Nota Fiscal"
                                 oncomplete="#{item.urlBoleto}">
                    <i class="fa-icon-file-alt" style="font-size: 2em" ></i> &nbsp NFe
                    <f:setPropertyActionListener value="#{item}" target="#{CanalPactoControle.parcelaSelecionada}"/>
                </a4j:commandLink>
            </rich:column>
        </rich:dataTable>
        </h:panelGroup>
    </div>
</div>

<a4j:region id="extr4">
    <a4j:status id="statusParcelas"
                onstart="document.getElementById('carregandoParcelas').style.display = 'block';document.getElementById('divParcelas').style.display = 'none';"
                onstop="document.getElementById('carregandoParcelas').style.display = 'none';document.getElementById('divParcelas').style.display = 'block';carregarTooltipster();"/>
</a4j:region>

<a4j:jsFunction  name="initParcelas"
                 action="#{CanalPactoControle.consultarParcelasAcademia}"
                 reRender="pnlParcelas" status="statusParcelas"/>

<script>
    window.onload = function (ev) {
        initParcelas();
    }
</script>

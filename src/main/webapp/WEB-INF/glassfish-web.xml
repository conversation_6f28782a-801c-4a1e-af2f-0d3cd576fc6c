<?xml version="1.0" encoding="UTF-8"?>
<glassfish-web-app>    
    <class-loader delegate="false"/>
    <property name="useBundledJsf" value="true"/>

    <parameter-encoding default-charset="UTF-8"/>

<!--    <session-config>
        <session-manager persistence-type="replicated">
            <manager-properties>
                <property name="relaxCacheVersionSemantics" value="true"/>
                <property name="sessionFilename" value="sessionstate" />
                <property name="persistenceFrequency" value="web-method"/>
            </manager-properties>
            <store-properties>
                <property name="persistenceScope" value="session" />
            </store-properties>
        </session-manager>
    </session-config>-->

    

<!--    <session-config>
        <session-manager persistence-type="coherence-web" />
    </session-config>-->
</glassfish-web-app>

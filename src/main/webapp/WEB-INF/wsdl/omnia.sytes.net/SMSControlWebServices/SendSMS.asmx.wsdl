<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="SendMessage">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="remetente" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="codArea" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="fone" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="senha" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="msg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendMessageResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendMessageResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="SendMessageSoapIn">
    <wsdl:part name="parameters" element="tns:SendMessage" />
  </wsdl:message>
  <wsdl:message name="SendMessageSoapOut">
    <wsdl:part name="parameters" element="tns:SendMessageResponse" />
  </wsdl:message>
  <wsdl:portType name="SendSMSSoap">
    <wsdl:operation name="SendMessage">
      <wsdl:input message="tns:SendMessageSoapIn" />
      <wsdl:output message="tns:SendMessageSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="SendSMSSoap" type="tns:SendSMSSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SendMessage">
      <soap:operation soapAction="http://tempuri.org/SendMessage" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="SendSMSSoap12" type="tns:SendSMSSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SendMessage">
      <soap12:operation soapAction="http://tempuri.org/SendMessage" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SendSMS">
    <wsdl:port name="SendSMSSoap" binding="tns:SendSMSSoap">
      <soap:address location="http://omnia.sytes.net/SMSControlWebServices/SendSMS.asmx" />
    </wsdl:port>
    <wsdl:port name="SendSMSSoap12" binding="tns:SendSMSSoap12">
      <soap12:address location="http://omnia.sytes.net/SMSControlWebServices/SendSMS.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
<?xml version="1.0" encoding="ISO-8859-1"?>
<faces-config version="1.2" xmlns="http://java.sun.com/xml/ns/javaee"
              xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">

    <managed-bean>
        <managed-bean-name>menuLateralControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.MenuLateralControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>agendaAmbienteProfissionalControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.AgendaAmbienteProfissionalControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>    
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>disponibilidadeControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.DisponibilidadeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>pacoteControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.PacoteControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>configuracaoEstudioControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.ConfiguracaoEstudioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        
    <managed-bean>
        <managed-bean-name>agendaMensalController</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.AgendaMensalControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>agendaIndividualControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.AgendaIndividualControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>clienteEstudioControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.ClienteEstudioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>AgendaAmbienteColaboradorControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.AgendaAmbienteColaboradorControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>RelatorioComissaoControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.RelatorioComissaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioGeralAgendamentosControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.RelatorioGeralAgendamentosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>RelatorioFechamentoDiarioControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.RelatorioFechamentoDiarioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>CancelamentoSessaoControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.CancelamentoSessaoControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioClientesInativosControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.estudio.controle.RelatorioClientesInativosControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
</faces-config>



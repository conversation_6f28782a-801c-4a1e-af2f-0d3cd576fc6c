<?xml version="1.0" encoding="ISO-8859-1"?>
<faces-config version="1.2" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">

    <managed-bean>
        <managed-bean-name>NavegacaoFinanceiro</managed-bean-name>
        <managed-bean-class>controle.arquitetura.NavegacaoFinanceiro</managed-bean-class>

        <managed-bean-scope>request</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>ContaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ContaControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>TipoContaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.TipoContaControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>TipoDocumentoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.TipoDocumentoControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>PlanoContasControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.PlanoContasControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>
        
    <managed-bean>
        <managed-bean-name>RecebivelAvulsoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RecebivelAvulsoControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>CentroCustosControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.CentroCustosControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>
    <managed-bean>
        <managed-bean-name>CaixaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.CaixaControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>

        <managed-bean>
        <managed-bean-name>BloqueioCaixaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.BloqueioCaixaControle</managed-bean-class>

        <managed-bean-scope>request</managed-bean-scope></managed-bean>


    <managed-bean>
        <managed-bean-name>RateioIntegracaoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RateioIntegracaoControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>MovContaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.MovContaControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>PessoaSimplificadoControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.PessoaSimplificadoControle</managed-bean-class>

        <managed-bean-scope>session</managed-bean-scope></managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoRecebiveisControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoRecebiveisControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>FluxoCaixaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.FluxoCaixaControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>GestaoLotesControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GestaoLotesControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
        <managed-bean-name>AgendamentoFinanceiroControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.AgendamentoFinanceiroControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MetaFinanceiroControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.MetaFinanceiroControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>RelatorioOrcamentarioConfigControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RelatorioOrcamentarioConfigControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
        <managed-bean-name>MetaFinanceiroBIControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.MetaFinanceiroBIControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>


  <managed-bean>
        <managed-bean-name>DREControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.DREControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	
	  <managed-bean>
        <managed-bean-name>ChequeControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ChequeControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	
	  <managed-bean>
        <managed-bean-name>GerenciadorContaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.GerenciadorContaControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	
	<managed-bean>
        <managed-bean-name>OperacaoContaControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.OperacaoContaControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	
	<managed-bean>
        <managed-bean-name>RelatorioMovimentacaoFinanceiraControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RelatorioMovimentacaoFinanceiraControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
    <managed-bean>
        <managed-bean-name>RelatorioDevolucaoChequeControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RelatorioDevolucaoChequeControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>RelatorioOrcamentarioControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.RelatorioOrcamentarioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

	<managed-bean>
        <managed-bean-name>ConfiguracaoFinanceiroControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ConfiguracaoFinanceiroControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>

        <managed-bean>
        <managed-bean-name>BIFinanceiroControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.BIFinanceiroControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>

    <managed-bean>
        <managed-bean-name>ImportacaoFinanceiroExcelOldControle</managed-bean-name>
        <managed-bean-class>controle.financeiro.ImportacaoFinanceiroExcelOldControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
</faces-config>



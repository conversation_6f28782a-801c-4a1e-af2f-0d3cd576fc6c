<?xml version="1.0" encoding="ISO-8859-1"?>
<faces-config version="1.2" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">

	<managed-bean>
		<managed-bean-name>NavegacaoControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.basico.NavegacaoControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>ProvaConceitoControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.provaconceito.ProvaConceitoControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>ProvaConceito_Simonides_Controle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.provaconceito.simonides.ProvaConceito_Simonides_Controle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	
	<managed-bean>
		<managed-bean-name>CancelamentoEventoControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.CancelamentoEventoControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	
	<managed-bean>
		<managed-bean-name>ProvaConceitoPedroControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.provaconceito.pedro.ProvaConceitoPedroControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	
	<managed-bean>
		<managed-bean-name>TipoAmbienteControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.TipoAmbienteControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>FornecedorControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.FornecedorControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>ServicoControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.ServicoControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>PerfilEventoControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.PerfilEventoControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>CadastroInicialControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.CadastroInicialControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>ConversaControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.ConversaControle</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>ConsultaEventosControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.ConsultaEventosControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>OrcamentoDetalhadoControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.OrcamentoDetalhadoControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>ProdutoLocacaoControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.ProdutoLocacaoControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>ProspectsControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.ProspectsControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>

        <managed-bean>
		<managed-bean-name>GestaoCreditoControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.GestaoCreditoControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
        <managed-bean>
		<managed-bean-name>PesquisaGeralControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.PesquisaGeralControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>

	<managed-bean>
		<managed-bean-name>EmissaoDocumentosControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.EmissaoDocumentosControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>AgendaVisitaControle</managed-bean-name>
		<managed-bean-class>br.com.pactosolucoes.ce.controle.AgendaVisitaControle</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>

</faces-config>



<faces-config version="1.2" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">

	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/*</from-view-id>
		<navigation-case>
			<from-outcome>telaFinanceiro</from-outcome>
			<to-view-id>/pages/ce/cadastros/telaFinanceiro.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>loginCE</from-outcome>
			<to-view-id>/pages/ce/telaInicialCE.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>pesquisaGeral</from-outcome>
			<to-view-id>/pages/ce/pesquisaGeral.jsp</to-view-id>
			<redirect />
		</navigation-case>

                <navigation-case>
			<from-outcome>gestaoCredito</from-outcome>
			<to-view-id>/pages/ce/gestaoCredito.jsp</to-view-id>
			<redirect />
		</navigation-case>

		<navigation-case>
			<from-outcome>visualizarLog</from-outcome>
			<to-view-id>/pages/ce/visualizadorLog.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>telaCadastro</from-outcome>
			<to-view-id>/pages/ce/cadastros/telaCadastro.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>orcamentoDetalhado</from-outcome>
			<to-view-id>/pages/ce/cadastros/orcamentoDetalhado.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>consultaConversas</from-outcome>
			<to-view-id>/pages/ce/cadastros/conversa.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>cadastroInicial</from-outcome>
			<to-view-id>/pages/ce/cadastros/cadastroInicial.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>tipoAmbiente</from-outcome>
			<to-view-id>/pages/ce/cadastros/tipoAmbiente.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>tipoAmbienteNovo</from-outcome>
			<to-view-id>/pages/ce/cadastros/tipoAmbienteForm.jsp</to-view-id>
		</navigation-case>
		
		<navigation-case>
			<from-outcome>fornecedor</from-outcome>
			<to-view-id>/pages/ce/cadastros/fornecedor.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>fornecedorNovo</from-outcome>
			<to-view-id>/pages/ce/cadastros/fornecedorForm.jsp</to-view-id>
		</navigation-case>
		
		<navigation-case>
			<from-outcome>servico</from-outcome>
			<to-view-id>/pages/ce/cadastros/servico.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>servicoNovo</from-outcome>
			<to-view-id>/pages/ce/cadastros/servicoForm.jsp</to-view-id>
		</navigation-case>
		
		
		<navigation-case>
			<from-outcome>detalhamentoEvento</from-outcome>
			<to-view-id>/pages/ce/eventos/detalhamentoEvento.jsp</to-view-id>
			<redirect />
		</navigation-case>
		<navigation-case>
			<from-outcome>preReserva</from-outcome>
			<to-view-id>/pages/ce/cadastros/preReserva.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
		<from-outcome>cancelarEvento</from-outcome>
			<to-view-id>/pages/ce/eventos/cancelamentoEvento.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
		<from-outcome>prospects</from-outcome>
			<to-view-id>/pages/ce/cadastros/prospects.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
		<from-outcome>exibeOrcamento</from-outcome>
			<to-view-id>/pages/ce/eventos/exibeOrcamento.jsp</to-view-id>
		</navigation-case>
		<navigation-case><from-outcome>confirmacaoPagamento</from-outcome>
			<to-view-id>/pages/ce/cadastros/confirmacaoPagamentoEvento.jsp</to-view-id>
		</navigation-case>
		<navigation-case><from-outcome>fecharNegociacao</from-outcome>
			<to-view-id>/pages/ce/pagamento/fechamentoNegociacao.jsp</to-view-id>
		</navigation-case>
		<navigation-case><from-outcome>receber</from-outcome>
			<to-view-id>/pages/ce/pagamento/recebimento.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
		<from-outcome>agendaVisita</from-outcome>
			<to-view-id>/pages/ce/cadastros/agendaVisita.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>checklist</from-outcome>
			<to-view-id>/pages/ce/eventos/checkList.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>versoChecklist</from-outcome>
			<to-view-id>/pages/ce/eventos/versoCheckList.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
            <from-outcome>caixaCE</from-outcome>
            <to-view-id>/telaParcelasCE.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>pagamentoCE</from-outcome>
            <to-view-id>/pagamento.jsp</to-view-id>
        </navigation-case>
        
        <navigation-case>
			<from-outcome>telaRelatorios</from-outcome>
			<to-view-id>/pages/ce/relatorio/telaRelatorio.jsp</to-view-id>
			<redirect />
		</navigation-case>
		
	</navigation-rule>
	
	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/pages/ce/cadastros/cadastroInicial.jsp</from-view-id>
		<navigation-case>
			<from-outcome>orcamentoDetalhado</from-outcome>
			<to-view-id>/pages/ce/cadastros/orcamentoDetalhado.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>fecharNegociacao</from-outcome>
			<to-view-id>/pages/ce/cadastros/confirmacaoPagamentoEvento.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>

	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/pages/ce/cadastros/perfilEvento.jsp</from-view-id>
		<navigation-case>
			<from-outcome>perfilEvento</from-outcome>
			<to-view-id>/pages/ce/cadastros/perfilEvento.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>novo</from-outcome>
			<to-view-id>/pages/ce/cadastros/perfilEventoForm.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>alterar</from-outcome>
			<to-view-id>/pages/ce/cadastros/perfilEventoForm.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>

	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/pages/ce/cadastros/perfilEventoForm.jsp</from-view-id>
		<navigation-case>
			<from-outcome>perfilEvento</from-outcome>
			<to-view-id>/pages/ce/cadastros/perfilEvento.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>novo</from-outcome>
			<to-view-id>/pages/ce/cadastros/perfilEventoForm.jsp</to-view-id>
		</navigation-case>
		
		</navigation-rule>
	
	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/pages/ce/cadastros/orcamentoDetalhado.jsp</from-view-id>
		<navigation-case>
			<from-outcome>orcamentoDetalhado</from-outcome>
			<to-view-id>/pages/ce/cadastros/orcamentoDetalhado.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>detalhamentoCadastro</from-outcome>
			<to-view-id>/pages/ce/cadastros/detalhamentoEvento.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>cancelamentoEvento</from-outcome>
			<to-view-id>/pages/ce/eventos/cancelamentoEvento.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/pages/ce/eventos/exibeOrcamento.jsp</from-view-id>
		<navigation-case>
			<from-outcome>cancelamentoEvento</from-outcome>
			<to-view-id>/pages/ce/eventos/cancelamentoEvento.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/pages/ce/cadastros/produtoLocacao.jsp</from-view-id>
		<navigation-case>
			<from-outcome>consulta</from-outcome>
			<to-view-id>/pages/ce/cadastros/produtoLocacao.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>novo</from-outcome>
			<to-view-id>/pages/ce/cadastros/produtoLocacaoForm.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>editar</from-outcome>
			<to-view-id>/pages/ce/cadastros/produtoLocacaoForm.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/pages/ce/cadastros/produtoLocacaoForm.jsp</from-view-id>
		<navigation-case>
			<from-outcome>consulta</from-outcome>
			<to-view-id>/pages/ce/cadastros/produtoLocacao.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>novo</from-outcome>
			<to-view-id>/pages/ce/cadastros/produtoLocacaoForm.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>editar</from-outcome>
			<to-view-id>/pages/ce/cadastros/produtoLocacaoForm.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<description>Navegacao da pagina</description>
		<from-view-id>/pages/ce/eventos/detalhamentoEvento.jsp</from-view-id>
		<navigation-case>
			<from-outcome>documentos</from-outcome>
			<to-view-id>/pages/ce/eventos/emissaoDocumentos.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
</faces-config>
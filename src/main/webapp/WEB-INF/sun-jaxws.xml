<?xml version="1.0" encoding="UTF-8"?>
<endpoints version="2.0" xmlns="http://java.sun.com/xml/ns/jax-ws/ri/runtime">
  <endpoint implementation="acesso.webservice.ValidacaoAcessoWS" name="ValidacaoAcessoWS" url-pattern="/ValidacaoAcessoWS"/>
  <endpoint implementation="negocio.comuns.basico.webservice.ConviteAulaExperimentalWS" name="ConviteAulaExperimentalWS" url-pattern="/ConviteAulaExperimentalWS"/>
  <endpoint implementation="negocio.comuns.financeiro.webservice.FinanceiroWS" name="FinanceiroWS" url-pattern="/FinanceiroWS"/>
  <endpoint implementation="acesso.webservice.RegistrarAcessoWS" name="RegistrarAcessoWS" url-pattern="/RegistrarAcessoWS"/>
  <endpoint implementation="negocio.comuns.plano.webservice.NegociacaoWS" name="NegociacaoWS" url-pattern="/NegociacaoWS"/>
  <endpoint implementation="servicos.integracao.adm.AdmWS" name="AdmWS" url-pattern="/AdmWS"/>
  <endpoint implementation="negocio.comuns.basico.webservice.IntegracaoTurmasWS" name="IntegracaoTurmasWS" url-pattern="/IntegracaoTurmasWS"/>
  <endpoint implementation="br.com.pactosolucoes.ecf.cupomfiscal.ws.EmissorCupomWS" name="EmissorCupomWS" url-pattern="/EmissorCupomWS"/>
  <endpoint implementation="negocio.comuns.basico.webservice.IntegracaoCadastrosWS" name="IntegracaoCadastrosWS" url-pattern="/IntegracaoCadastrosWS"/>
  <endpoint implementation="negocio.comuns.basico.webservice.IntegracaoNFSeWS" name="IntegracaoNFSeWS" url-pattern="/IntegracaoNFSeWS"/>
</endpoints>

<div id="root-wehelp" style="display: none;"></div>
<script type="text/javascript" src="https://app.wehelpsoftware.com/js/built/production/widget.js"></script>
<script type="text/javascript">
    function carregarWeHelp() {
        var wehelpCustomerInfo = {
            survey_token: "N2RmYjhiYmJmODg3ODYzZjBhNjk1MGUxNmU4YzA3ZDYwZGM4NmU3YWUyZGVlNGFjNmY0OGY0MzdjOTM4ZDAzZnCrhK3DjnM0z10jedmaI7WtDFItF6TcapgSWy9Jn8ds",
            type: 'modal',
            message_open: 'false',
            language: 'PORTUGUESE',
            company_unit: '2', // ID da unidade da empresa
            person: {
                internal_code: '${SuperControle.key}-${SuperControle.usuarioLogado.codigo}',
                name: '${SuperControle.usuarioLogado.nomeSemAcentuacao}',
                email: '${SuperControle.usuarioLogado.email}',
                type: 'CUSTOMER',
                phone: '${SuperControle.usuarioLogado.colaboradorVO.primeiroTelefoneNaoNuloWeHelp}',
                date_of_birth: '${SuperControle.usuarioLogado.colaboradorVO.pessoa.dataNasc}',
                language: 'PORTUGUESE',
                created_at: '${SuperControle.usuarioLogado.colaboradorVO.pessoa.dataCadastro}',
                state: '${SuperControle.usuarioLogado.colaboradorVO.pessoa.estadoVO.sigla}',
                country: '${SuperControle.usuarioLogado.colaboradorVO.pessoa.pais.siglaNome}',
                gender: '${SuperControle.usuarioLogado.colaboradorVO.pessoa.sexo}',
                document: '',
                company_unit: '1'
            },
            custom_fields: [
                {
                    name: 'EMPRESA',
                    value: '${SuperControle.empresaLogado.nomeWeHelp}',

                },
                {
                    name: 'FUN��O',
                    value: '${SuperControle.usuarioLogado.colaboradorVO.pessoa.funcaoEnum}',

                },
                {
                    name: 'CARGO',
                    value: '${SuperControle.usuarioLogado.colaboradorVO.pessoa.cargoEnum}',

                },
                {
                    name: 'PERFIL DE ACESSO',
                    value: '${SuperControle.perfilUsuarioLogado.tipoPerfilFromOrdinal}',

                },
                {
                    name: 'CHAVE',
                    value: '${SuperControle.key}',

                },
                {
                    name: 'CODIGO DA EMPRESA',
                    value: '${SuperControle.empresaLogado.codigo}',

                },
                {
                    name: 'CIDADE',
                    value: '${SuperControle.empresaLogado.cidade_ApresentarSemAcentuacao}',

                },
                {
                    name: 'CODIGO DA EMPRESA FINANCEIRO',
                    value: '${SuperControle.empresaLogado.codEmpresaFinanceiro}',

                },
                {
                    name: 'REDE',
                    value: '${SuperControle.redeEmpresas}',

                },
                {
                    name: '� REDE',
                    value: '${SuperControle.isRedeEmpresas}',

                },
                {
                    name: 'CS_RESPONSAVEL',
                    value: '${SuperControle.nomeResponsavelPacto}',

                }
            ]
        };
        loadWeHelpWidgetScreen('https://app.wehelpsoftware.com', wehelpCustomerInfo, 'root-wehelp');
    }

    window.onload = carregarWeHelp();
</script>

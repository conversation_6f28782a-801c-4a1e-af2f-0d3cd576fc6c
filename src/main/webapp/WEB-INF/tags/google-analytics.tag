<!-- Google Analytics -->
<script>
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

    tela = window.location.pathname;
    ga('create', 'UA-113292205-2', 'auto');
    ga('set', '&uid', '${SuperControle.usuarioLogado.username}');
    <%--ga('send', 'pageview');--%>
    ga('send', 'event', 'popup', 'pageView', tela);
</script>
<!-- End Google Analytics -->

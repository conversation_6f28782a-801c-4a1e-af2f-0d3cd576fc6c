<jsp:useBean id="LoginControle" scope="session" type="controle.arquitetura.security.LoginControle"/>
<div id="root-octadesk" style="display: none;"></div>

<script>
    (function (o, c, t, a, d, e, s, k) {
        o.octadesk = o.octadesk || {};
        o.octadesk.chatOptions = {
            subDomain: a,
            showButton: d,
            openOnMessage: e,
            hide: s
        };
        var bd = c.getElementsByTagName("body")[0];
        var sc = c.createElement("script");
        sc.async = 1;
        sc.src = t;
        bd.appendChild(sc);
    })(window, document, 'https://cdn.octadesk.com/embed.js', 'o195992-8e1', 'true', 'true', 'false');
</script>
<script>
    // Para identificar seus usu�rios voc� deve chamar esse m�todo passando o nome e o email do usu�rio e opcionalmente informar o id de seu chat
    // Lembrando que isso � apenas um modelo teste. Voc� deve chamar esse m�todo em seu site para efetuar o login. Voc� pode autenticar seu usu�rio em qualquer momento no fluxo do seu site.

    window.addEventListener('onOctaChatReady', function () {
        octadesk.chat.login({
            name: "${LoginControle.octadeskUserTO.name}",
            email: "${LoginControle.octadeskUserTO.email}",
            customFields: [
                {id: "cnpj", value: "${LoginControle.octadeskUserTO.cnpj}", domainType: 2},
                {id: "id_favorecido", value: "${LoginControle.octadeskUserTO.idFavorecido}", domainType: 2},
                {id: "id_usuario", value: "${LoginControle.octadeskUserTO.idUsuario}", domainType: 2},
                {id: "perfil_de_acesso", value: "${LoginControle.octadeskUserTO.perfilDeAcesso}", domainType: 2},
                {id: "cpf", value: "${LoginControle.octadeskUserTO.cpf}", domainType: 2}
            ],
        });
    });


    function ocultarOctadeskChat() {
        console.log('ocultarMovChat');
        waitForElm('.octa-widget-v2').then(
            element => {
                element.style.visibility = 'hidden';
            }
        )
    }

    window.onload = ocultarOctadeskChat();

    function waitForElm(selector) {
        return new Promise(resolve => {
            if (document.querySelector(selector)) {
                return resolve(document.querySelector(selector));
            }

            const observer = new MutationObserver(mutations => {
                if (document.querySelector(selector)) {
                    resolve(document.querySelector(selector));
                    observer.disconnect();
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    }
</script>

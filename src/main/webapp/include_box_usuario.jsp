<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<!-- inicio box -->
<div class="box">
    <div class="boxtop"><img src="images/box_top.png"></div>
    <div class="boxmiddle">
        <table width="146" border="0" cellpadding="0" cellspacing="0" class="text" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="center" valign="top">
                    <h:panelGroup layout="block" id="panelFoto">                        
                        <a4j:jsFunction name="updateFoto" action="#{ClienteControle.recarregarFoto}" reRender="panelFoto"/>
                        
                        <a4j:mediaOutput rendered="#{!SuperControle.fotosNaNuvem}" 
                                         element="img" id="imagem1"  style="width:150px;height:180px "  
                                         cacheable="false" session="false"
                                         createContent="#{ClienteControle.paintFoto}"  
                                         value="#{ImagemData}" mimeType="image/jpeg" >
                            <f:param value="#{SuperControle.timeStamp}" name="time"/>
                            <f:param name="largura" value="150"/>
                            <f:param name="altura" value="180"/>
                        </a4j:mediaOutput>
                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                        width="150" height="180"                                        
                                        style="width:150px;height:180px"
                                        url="#{ClienteControle.paintFotoDaNuvem}">                            
                        </h:graphicImage>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
        <table width="146" border="0" cellpadding="0" cellspacing="0" class="text" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top">
                    <a4j:outputPanel id="panelBotaoAdicionar">
                        <a4j:commandButton 
                            actionListener="#{CapturaFotoControle.selecionarPessoa}"
                            action="#{CapturaFotoControle.vazio}"                                           
                            id="btnAlterarFoto" value="#{msg_bt.btn_capturarfoto}" image="imagens/picture.png"
                            oncomplete="setAttributesModalCapFoto(
                                '#{ClienteControle.key}',
                                '#{ClienteControle.clienteVO.pessoa.codigo}',
                                '#{ClienteControle.contextPath}', '');
                                Richfaces.showModalPanel('modalCapFotoHTML5');"
                            alt="#{msg_bt.btn_capturarfoto}" title="#{msg_bt.btn_capturarfoto}" styleClass="botoes">
                            <f:attribute name="pessoa" value="#{ClienteControle.clienteVO.pessoa.codigo}"/>
                        </a4j:commandButton>
                        
                        <h:outputLink value="#{SuperControle.urlWiki}Inicial:Informações_do_Cliente:CapturarFoto"
                                      title="Clique e saiba mais: Capturar Foto" target="_blank">
                            <h:graphicImage styleClass="linkWiki" value="imagens/wiki_link2.gif" style="padding-bottom:20px;padding-left:5px;"/>
                        </h:outputLink>
                    </a4j:outputPanel>
                    <a4j:outputPanel id="panelBotaoRemover">
                        <a4j:commandButton id="btnRemoverFoto" value="#{msg_bt.btn_removerfoto}" image="imagens/delete_photo.png"
                                           onclick="if (!confirm('Confirma exclusão do registro?')){return false;}"
                                           action="#{ClienteControle.removerFoto}"
                                           reRender="panelFoto"
                                           alt="#{msg_bt.btn_removerfoto}" title="#{msg_bt.btn_removerfoto}" styleClass="botoes"/>
                        <a4j:commandButton id="btnAtualizaCliente" image="imagens/refresh.png" title="Recarregar dados do cliente"
                                           value="Atualizar"
                                           reRender="panelRecarregar"
                                           actionListener="#{ClienteControle.atualizarCliente}" action="#{ClienteControle.acaoAjax}">
                            <f:attribute name="cliente" value="#{ClienteControle.clienteVO}"/>
                        </a4j:commandButton>
                        <a4j:commandButton style="display:none;"  id="btnRederCliente" reRender="panelRecarregar"/>

                        <h:panelGroup rendered="#{ClienteControle.clienteVO.parqPositivo}">
                            <img id="btnParqPositivo" alt="" title="Cliente Parq+
                                 OBS.: ${ClienteControle.observacaoParqPositivo}" style="width: 32px" src="imagens/alunoParqPlus.png"/>
                        </h:panelGroup>

                    </a4j:outputPanel>

                    <h:panelGrid styleClass="titulo30" columns="1">
                        <h:outputText id="clienteNomeAuto" value="#{ClienteControle.pessoaVO.nome}"/>
                        <h:outputText styleClass="textsmall" value="MAT: #{ClienteControle.clienteVO.matricula}"/>
                    </h:panelGrid>

                    <h:dataTable id="telefoneVO" width="100%" headerClass="subordinado"
                                 rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda"
                                 value="#{ClienteControle.pessoaVO.telefoneVOs}" var="telefone">
                        <h:column>
                            <h:outputText styleClass="textsmall"  value="#{telefone.numero}" />
                        </h:column>
                        <h:column>
                            <h:outputText styleClass="textsmall" value="#{telefone.tipoTelefone_ApresentarEspecial}"/>
                            <h:outputText styleClass="textsmall" value ="#{telefone.descricaoEspecial}" title="#{telefone.tipoTelefone_Apresentar}: #{telefone.descricao}"/>
                        </h:column>
                    </h:dataTable>

                </td>
            </tr>
        </table>

        <table width="50" border="0" cellspacing="0" cellpadding="0" >
            <tr>
                <td>
                    <h:panelGrid columns="2">
                        <img style="vertical-align:middle;margin-right:5px;" src="images/icon_editarcadastro.gif" width="16" height="17">
                        <rich:dropDownMenu id="menuEditCadas" styleClass="titulo6" value="Editar o cadastro"
                                           submitMode="ajax" direction="bottom-right" jointPoint="tr" >

                            <rich:menuItem id="editDadosPessoais"  value="Dados Pessoais" actionListener="#{ClienteControle.abrirAba}"
                                           oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);">
                                <f:attribute name="aba" value="abadadosPessoais"/>
                            </rich:menuItem>

                            <rich:menuItem id="editCobranca"  value="Cobrança" actionListener="#{ClienteControle.abrirAba}"
                                           oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);">
                                <f:attribute name="aba" value="abaCobranca"/>
                            </rich:menuItem>

                            <rich:menuItem id="editEndereco"  value="Endereço" actionListener="#{ClienteControle.abrirAba}"
                                           oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);">
                                <f:attribute name="aba" value="abaEndereco"/>
                            </rich:menuItem>

                            <rich:menuItem id="editEmail"  value="Email" actionListener="#{ClienteControle.abrirAba}"
                                           oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);">
                                <f:attribute name="aba" value="abaEmail"/>
                            </rich:menuItem>

                            <rich:menuItem id="editAlterarMatricula"  value="Alterar número de matrícula"
                                           rendered="#{LoginControle.permissaoAcessoMenuVO.alterarMatricula}"
                                           oncomplete="Richfaces.showModalPanel('panelAlterarMatricula')"
                                           reRender="panelAlterarMatricula"/>

                            <rich:menuItem id="editDependentes" value="Dependentes" actionListener="#{ClienteControle.abrirAba}"
                                           oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 622);">
                                <f:attribute name="aba" value="abaDependentes"/>
                            </rich:menuItem>

                            <rich:menuItem id="editSenhaAcesso"  value="Definir senha de acesso"
                                           rendered="#{LoginControle.permissaoAcessoMenuVO.adicionarAlterarSenhaAcesso}"
                                           action="#{ClienteControle.abriTelaDefinirSenhaAcesso}"
                                           oncomplete="abrirPopup('definirSenhaAcesso.jsp', 'Definir senha de acesso', 500, 300);" />
                        </rich:dropDownMenu>
                    </h:panelGrid>
                </td>

            </tr>
        </table>        
    </div>
    <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>

<!-- inicio box -->
<div class="box">
    <a4j:outputPanel id="panelOperacoesContrato">
        <h:panelGroup rendered="#{ClienteControle.clienteVO.apresentarAbaContrato}" layout="block">

            <div class="boxtop"><img src="./images/box_top.png"></div>
            <div class="boxmiddle">
                <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
                    <tr>
                        <td colspan="2" align="left" valign="top"><img style="vertical-align:middle;margin-right:9px;" src="./images/icon_cadastros.gif" width="12" height="15"><span id="tituloOperacoesContrato" class="titulo2">Operações Contrato</span></td>
                    </tr>
                </table>
                <table width="150" border="0" cellspacing="0" cellpadding="0" >

                    <%-- Início Estorno de Contrato --%>
                    <h:panelGroup rendered="#{ClienteControle.apresentarEstornoContratoNoMenuCliente}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-excluir-estornar-o-contrato-de-um-aluno-que-esta-errado/"
                                      title="Clique e saiba mais: Estorno de Contrato" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkEstornoContratro" styleClass="titulo2" reRender="formMensagemAviso,panelParcelasRemessas"
                                         oncomplete="#{ClienteControle.urlPopup}"
                                         action="#{EstornoContratoControle.novo}">Estorno Contrato
                            <f:setPropertyActionListener value="O Estorno só poderá ser feita após o retorno da remessa da(s) parcela(s) acima!" target="#{ClienteControle.tituloModalParcelaRemessa}"/>
                        </a4j:commandLink>
                        <div class="sepmenu"><img src="./images/shim.gif"></div>
                        </h:panelGroup>
                        <%-- Fim Estorno Contrato --%>

                    <%-- Início Afastamento - Férias,Atestado, Trancamento e Cancelamento --%>
                    <h:panelGroup rendered="#{ClienteControle.apresentarCarencia || ClienteControle.apresentarAtestado ||
                                              ClienteControle.apresentarTrancamento || ClienteControle.apresentarCancelamento}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                      title="Clique e saiba mais: Afastamento" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkAfastamento" styleClass="titulo2"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopupTopoPagina('afastamentoContratoForm.jsp', 'AfastamentoContratoControle', 830, 650);"
                                         action="#{AfastamentoContratoControle.novo}">Afastamento
                        </a4j:commandLink>
                        <div class="sepmenu"><img src="./images/shim.gif"></div>
                        </h:panelGroup>
                        <%-- Fim Afastamento - Férias,Atestado, Trancamento e Cancelamento  --%>

                    <%-- Início Férias --%><%--
                     <h:panelGroup rendered="#{ClienteControle.apresentarCarencia}">
                         <h:outputLink value="#{SuperControle.urlWiki}Informações_do_Cliente:Contrato:Operações_Contrato#Car.C3.AAncia"
                                       title="Clique e saiba mais: Carência" target="_blank">
                             <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                         </h:outputLink>
                         <rich:spacer width="2px"/>
                         <a4j:commandLink styleClass="titulo2"
                                          oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('carenciaContrato.jsp', 'CarenciaContratoControle', 830, 690);"
                                          action="#{CarenciaContratoControle.novo}">Carência</a4j:commandLink>
                         <div class="sepmenu"><img src="./images/shim.gif"></div>
                         </h:panelGroup>--%>
                    <%-- Fim Carência --%>

                    <%-- Início Retorno Carência --%>
                    <h:panelGroup rendered="#{ClienteControle.apresentarRetornoCarencia}">
                        <h:outputLink value="#{SuperControle.urlWiki}Informações_do_Cliente:Contrato:Operações_Contrato:Retorno_Carência"
                                      title="Clique e saiba mais: Retorno de Férias" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkRetornoCarencia" styleClass="titulo2"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('retornoCarenciaContrato.jsp', 'RetornoCarenciaContratoControle', 830, 690);"
                                         action="#{RetornoCarenciaContratoControle.novo}">Retorno Férias</a4j:commandLink>

                            <div class="sepmenu"><img src="./images/shim.gif"></div>
                        </h:panelGroup>
                        <%-- Fim Retorno Carência --%>

                    <%-- Início Atestado Médico --%><%--
                    <h:panelGroup rendered="#{ClienteControle.apresentarAtestado}">
                        <h:outputLink value="#{SuperControle.urlWiki}Informações_do_Cliente:Contrato:Operações_Contrato#Atestado"
                                      title="Clique e saiba mais: Atestado" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink styleClass="titulo2"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('atestadoContrato.jsp', 'AtestadoContratoControle', 830, 690);"
                                         action="#{AtestadoContratoControle.novo}">Atestado</a4j:commandLink>
                        <div class="sepmenu"><img src="./images/shim.gif"></div>
                        </h:panelGroup>--%>
                    <%-- Fim Atestado Médico  --%>

                    <%-- Início  Retorno Atestado Médico --%>
                    <h:panelGroup rendered="#{ClienteControle.apresentarRetornoAtestado}">
                        <h:outputLink value="#{SuperControle.urlWiki}Informações_do_Cliente:Contrato:Operações_Contrato:Retorno_Atestado"
                                      title="Clique e saiba mais: Retorno de Atestado" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkRetornoAtesMed" styleClass="titulo2"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('retornoAtestadoContrato.jsp', 'RetornoAtestadoContratoControle', 750, 550);"
                                         action="#{RetornoAtestadoContratoControle.novo}">Retorno Atestado</a4j:commandLink>
                            <div class="sepmenu"><img src="./images/shim.gif"></div>
                        </h:panelGroup>
                        <%-- Fim Retorno Atestado Médico  --%>

                    <%-- Início Bonus --%>
                    <h:panelGroup rendered="#{ClienteControle.apresentarBonus}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-lancar-bonus-para-um-aluno/"
                                      title="Clique e saiba mais: Bônus" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkBonus" styleClass="titulo2"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('bonusForm.jsp', 'BonusContratoControle', 830, 690);"
                                         action="#{BonusContratoControle.novo}">Bônus</a4j:commandLink>

                            <div class="sepmenu"><img src="./images/shim.gif"></div>
                        </h:panelGroup>
                        <%-- Fim Bonus  --%>

                    <%-- Início Trancamento --%><%--
                    <h:panelGroup rendered="#{ClienteControle.apresentarTrancamento}">
                        <h:outputLink value="#{SuperControle.urlWiki}Informações_do_Cliente:Contrato:Operações_Contrato#Trancamento"
                                      title="Clique e saiba mais: Trancamento" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink styleClass="titulo2"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('trancamentoContratoForm.jsp', 'TrancamentoContratoControle', 830, 690);"
                                         action="#{TrancamentoContratoControle.novo}">Trancamento</a4j:commandLink>
                        <div class="sepmenu"><img src="./images/shim.gif"></div>
                        </h:panelGroup>--%>
                    <%-- Fim Trancamento  --%>

                    <%-- Início Retorno Trancamento --%>
                    <h:panelGroup rendered="#{ClienteControle.apresentarRetornoTrancamento}">
                        <h:outputLink value="#{SuperControle.urlWiki}Informações_do_Cliente:Contrato:Operações_Contrato:Retorno_Trancamento"
                                      title="Clique e saiba mais: Retorno de Trancamento" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkRetornoTrancamento" styleClass="titulo2"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();"
                                         actionListener="#{RetornoTrancamentoContratoControle.novoRetornoEvent}">Retorno Trancamento</a4j:commandLink>
                            <div class="sepmenu"><img src="./images/shim.gif"></div>
                        </h:panelGroup>
                        <%-- Fim Retorno Trancamento  --%>

                    <%-- Início Manutenção Modalidade --%>
                    <h:panelGroup rendered="#{ClienteControle.apresentarManutModalidade}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-mudar-as-modalidades-de-um-aluno-sem-precisar-vender-um-novo-plano-manutencao-de-modalidade/"
                                      title="Clique e saiba mais: Manutenção de Modalidade" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkManuModalidade" styleClass="titulo2" reRender="formMensagemAviso"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();#{ClienteControle.urlPopup}"
                                         action="#{ManutencaoModalidadeControle.novo}">Manutenção Modalidade</a4j:commandLink>
                            <div class="sepmenu"><img src="./images/shim.gif"></div>

                    </h:panelGroup>
                    <%-- Fim Manutenção Modalidade  --%>

                    <%-- Início Alterar Horário --%>
                    <h:panelGroup rendered="#{ClienteControle.apresentarAlterarHorario}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-mudo-o-aluno-de-um-horario-livre-para-economico/"
                                      title="Clique e saiba mais: Alterar Horário" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkAlterarHorario" styleClass="titulo2" reRender="formMensagemAviso, mdlMensagemGenerica,panelParcelasRemessas"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();#{AlterarHorarioContratoControle.msgAlert}"
                                         action="#{AlterarHorarioContratoControle.novo}">Alterar Horário
                            <f:setPropertyActionListener value="Manutenção só poderá ser feita após o retorno da remessa da(s) parcela(s) acima!" target="#{ClienteControle.tituloModalParcelaRemessa}"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                    <%-- Início Alterar Horário --%>

                    <%-- Início Cancelamento--%><%--
                    <h:panelGroup rendered="#{ClienteControle.apresentarCancelamento}">
                        <h:outputLink value="#{SuperControle.urlWiki}Informações_do_Cliente:Contrato:Operações_Contrato#Cancelamento"
                                      title="Clique e saiba mais: Cancelamento do Contrato" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <rich:spacer width="2px"/>
                        <a4j:commandLink id="linkCancelamento" styleClass="titulo2"
                                         oncomplete="#{rich:component('panelStatus1')}.hide();abrirPopup('cancelamentoForm.jsp', 'CancelamentoContratoControle', 830, 690); return false;"
                                         action="#{CancelamentoContratoControle.novo}">Cancelamento</a4j:commandLink>
                    </h:panelGroup>--%>
                    <%-- Fim Cancelamento  --%>
            </div>
        </table>


    </div>
    <div class="boxbottom"><img src="images/box_bottom.png"></div>
    </h:panelGroup>
</a4j:outputPanel>


</div>
<!-- fim box -->


<!-- fim box -->
<div class="box">
    <div class="boxtop"><img src="./images/box_top.png"></div>
    <div class="boxmiddle">
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
            <tr>
                <td colspan="2" align="left" valign="top" class="titulo2" style="font-size:14px">Opções</td>
            </tr>
        </table>
        <!-- inicio item-->
        <table width="50" align="left" border="0" cellspacing="0" cellpadding="0" >
            <h:outputLink styleClass="linkWikiMenu" value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                          title="Clique e saiba mais: Atestado de Aptidão Física" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
            <rich:dropDownMenu id="linkaaf" styleClass="titulo6 marginForMenuWiki" value="Atestado de Aptidão Física"
                               submitMode="ajax" direction="bottom-right" jointPoint="tr" >

                <%--<rich:menuItem actionListener="#{ClienteControle.abrirAba}"--%>
                <rich:menuItem id="linkAtestadoAptFisica" value="Novo Atestado" actionListener="#{AtestadoControle.prepararAtestado}" oncomplete="abrirPopup('atestadoAptidaoFisica.jsp', 'AptidaoFisica', 880, 650);">
                    <f:attribute name="cliente" value="#{ClienteControle.clienteVO}"/>
                </rich:menuItem>
            </rich:dropDownMenu>
        </table>
        <div class="sepmenu"><img src="./images/shim.gif"></div>
        <table width="50" align="left" border="0" cellspacing="0" cellpadding="0" >
            <h:outputLink styleClass="linkWikiMenu" rendered="#{ClienteControle.menuContrato}"
                          value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                          title="Clique e saiba mais: Novo Contrato"
                          target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
            <rich:dropDownMenu id="linkContrato" styleClass="titulo6 marginForMenuWiki" rendered="#{ClienteControle.menuContrato}" value="Contrato"
                               submitMode="ajax" direction="bottom-right" jointPoint="tr" >
                <rich:menuItem id="linkNovoContrato" value="Novo Contrato"
                               action="#{ClienteControle.mostrarListaContratosARenovarEARematricular}"
                               reRender="panelIncludeMensagem"
                               oncomplete="#{ClienteControle.mensagemNotificar}#{ClienteControle.mostrarRichModalPanelListaContratosARenovarOuRematricular}"/>


            </rich:dropDownMenu>
        </table>
        <h:panelGroup rendered="#{ClienteControle.menuContrato}">
            <div class="sepmenu"><img src="./images/shim.gif"></div>
            </h:panelGroup>
        <table width="50" align="left" border="0" cellspacing="0" cellpadding="0" >
            <h:outputLink styleClass="linkWikiMenu"
                          value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                          title="Clique e saiba mais: Cadastro"
                          target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px">
            </h:outputLink>
            <rich:dropDownMenu styleClass="titulo6 marginForMenuWiki" value="Cadastro"
                               submitMode="ajax" direction="bottom-right" jointPoint="tr" >

                <rich:menuItem actionListener="#{ClienteControle.abrirAba}"
                               oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                    <h:outputText value="Vincular a uma Carteira"/>
                    <f:attribute name="aba" value="abaVinculo"/>
                </rich:menuItem>
                <rich:menuItem actionListener="#{ClienteControle.abrirAba}"
                               oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                    <h:outputText value="Adicionar classificação"/>
                    <f:attribute name="aba" value="abaGrupo"/>
                </rich:menuItem>
                <rich:menuItem actionListener="#{ClienteControle.abrirAba}"
                               oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;"  >
                    <h:outputText value="Associar a Grupos"/>
                    <f:attribute name="aba" value="abaGrupo"/>
                </rich:menuItem>
            </rich:dropDownMenu>
        </table>
        <!-- fim item-->



        <div class="sepmenu"><img src="./images/shim.gif"></div>
        <!-- inicio item-->
        <table width="50" border="0" cellspacing="0" cellpadding="0" >
            <h:outputLink styleClass="linkWikiMenu" value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                          title="Clique e saiba mais: Relacionamento"
                          target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

            <rich:dropDownMenu styleClass="titulo6 marginForMenuWiki" value="Relacionamento" submitMode="ajax"
                               direction="bottom-right" jointPoint="tr" >
                <rich:menuItem action="#{QuestionarioClienteCRMControle.novoCRM}"
                               oncomplete="abrirPopup('questionarioClienteCRMForm.jsp', 'Questionario', 780, 595); return false;">
                    <h:outputText value="Ver o Último Boletim de Visita"/>

                </rich:menuItem>

                <rich:menuItem action="#{QuestionarioClienteControle.novo}"
                               oncomplete="abrirPopup('questionarioClienteForm.jsp', 'Questionario', 780, 595); return false;">
                    <h:outputText value="Ver Histórico de Boletins"/>
                </rich:menuItem>

                <rich:menuItem action="#{MetaCRMControle.inicializarContatoAvulso}"
                               oncomplete="abrirPopup('newRealizarContatoForm.jsp', 'Realizar Contato Avulso', 850, 650);" >
                    <h:outputText value="Realizar Contato"/>
                </rich:menuItem>

                <rich:menuItem action="#{HistoricoContatoControle.inicializarHistoricoContato}"
                               oncomplete="abrirPopup('historicoContatoClienteForm.jsp', 'HistoricoContatoCliente', 512, 530); return false;">
                    <h:outputText value="Histórico de Contatos"/>
                </rich:menuItem>

                <rich:menuItem action="#{IndicacaoControle.historicoIndicacao}"
                               oncomplete="abrirPopup('historicoIndicacao.jsp', 'HistoricoIndicacao', 512, 530); return false;">
                    <h:outputText value="Histórico de Indicações"/>
            </rich:menuItem>

                <rich:menuItem value="Lançar Mensagem para Catraca"
                               action="#{ClienteControle.editarClienteMensagemCatraca}"
                               reRender="panelIncludeMensagem"
                               oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
                </rich:menuItem>

                <rich:menuItem  value="Lançar Aviso ao Consultor"                    
                                action="#{ClienteControle.editarClienteMensagemConsultor}"
                                reRender="panelIncludeMensagem"
                                oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
                </rich:menuItem>

                <rich:menuItem value="Lançar Aviso Médico"
                               action="#{ClienteControle.editarClienteMensagemMedico}"
                               reRender="panelIncludeMensagem"
                               oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
                </rich:menuItem>

                <rich:menuItem value="Lançar Objetivo do Aluno Academia"
                               action="#{ClienteControle.editarClienteMensagemObjetivo}"
                               reRender="panelIncludeMensagem"
                               oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
                </rich:menuItem>
            </rich:dropDownMenu>
        </table>

        <div class="sepmenu"><img src="./images/shim.gif"></div>
        <!-- inicio item-->
        <table width="50" border="0" cellspacing="0" cellpadding="0">
            <h:outputLink styleClass="linkWikiMenu" value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                          title="Clique e saiba mais: Venda Avulsa"
                          target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px">
            </h:outputLink>
            <rich:dropDownMenu id="linkVendaAvulsa"  styleClass="titulo6 marginForMenuWiki" value="Venda Avulsa"
                               submitMode="ajax" direction="bottom-right" jointPoint="tr" >
                <rich:menuItem id="linkVendaProduto" value="Produto ou Serviço"
                               actionListener="#{VendaAvulsaControle.prepare}"
                               action="#{VendaAvulsaControle.novoTela}"
                               oncomplete="#{VendaAvulsaControle.mensagemNotificar}" />
                <rich:menuItem id="linkVendaDiaria" value="Diária"
                               action="#{AulaAvulsaDiariaControle.novoDiariaCliente}"
                               rendered="#{LoginControle.permissaoAcessoMenuVO.aulaAvulsaDiaria}"
                               oncomplete="#{AulaAvulsaDiariaControle.mensagemNotificar}"/>
                <%-- <rich:menuItem id="linkVendaAula" value="Aula" action="#{AulaAvulsaDiariaControle.novoAulaAvulsa}"/> --%>
            </rich:dropDownMenu>
        </table>
    </div>
    <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>


<div class="box">
    <div class="boxtop"><img src="./images/box_top.png"></div>
    <div class="boxmiddle">
        <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">

            <tr>
                <td width="2" align="left" valign="top"><img src="./images/shim.gif"></td>
                <!-- inicio item-->
                <td align="left" valign="top">
                    <!-- inicio item-->
                    <h:panelGrid columns="2">
                        <a4j:commandLink id="linkVisualisarLog" styleClass="titulo2" action="#{ClienteControle.realizarConsultaLogObjetoSelecionadoPessoa}" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                            <h:outputText value="   "/>
                            <h:outputText value="Visualizar LOG"/>
                        </a4j:commandLink>						
                    </h:panelGrid>
                    <!-- fim item-->
                    <div class="sepmenu"><img src="./images/shim.gif"></div>
                    <!-- inicio item-->
                    <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
                        <tr>
                            <td colspan="2" align="left" valign="top">
                                <h:panelGrid columns="2" rendered="#{(LoginControle.permissaoAcessoMenuVO.caixaEmAberto  || LoginControle.usuarioLogado.administrador)}">
                                <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"
                                              title="Clique e saiba mais: Caixa em Aberto" target="_blank">
                                    <h:graphicImage styleClass="linkWiki" value="imagens/wiki_link2.gif"
                                                    style="padding-left:5px;"/>
                                </h:outputLink>
                                <a4j:commandLink id="linkCaixaEmAberto" styleClass="titulo2"
                                                 action="#{ClienteControle.caixa}">
                                    <h:outputText styleClass="titulo2" value="Caixa em Aberto"/>
                                </a4j:commandLink>
                                </h:panelGrid>
                            </td>
                        </tr>
                    </table>
                    <!-- fim item-->
                </td>
            </tr>
        </table>
    </div>
    <div class="boxbottom"><img src="images/box_bottom.png"></div>
</div>

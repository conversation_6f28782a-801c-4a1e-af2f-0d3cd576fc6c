<%--
    Author                           : <PERSON>�<PERSON>
    Data                             : 20/07/2011
    Objetivo da Tela                 : Pesquisar centros de custo com AutoComplete.
    Em qual tela pode ser usada      : Nas telas onde � necess�rio pesquisar centros de custo com AutoComplete.
    Exemplo 1 para importar esta tela: 
				<%@include file="include_SuggestionCentroCusto.jsp"%>
    Ex. p/ recuperar o produto selecionado: (CentroCustoTO)JSFUtilities.getManagedBeanValue("CentroCustosControle.centroEscolhido")
--%>
<%@include file="includes/imports.jsp" %>
<h:inputText  id="nomeCentroSelecionado"
           size="50"
           maxlength="50"
           onblur="blurinput(this);"
           onfocus="focusinput(this);"
           styleClass="form"
           value="#{CentroCustosControle.centroNome}" >
<a4j:support event="onchange" action="#{CentroCustosControle.setarCentroVazio}" reRender="form"/>
</h:inputText>

<rich:suggestionbox   height="200" width="400"
                      for="nomeCentroSelecionado"
                      status="statusHora"
                      immediate="true"
                      suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                      minChars="1"
                      rowClasses="linhaImpar, linhaPar"
                      var="result"  id="suggestionCentroCusto">
    <a4j:support event="onselect"
                 reRender="form"
                 action="#{CentroCustosControle.selecionarCentroCusto}" oncomplete="#{rich:element('formaPagamento')}.focus();">
    </a4j:support>
    <h:column>
        <f:facet name="header">
            <h:outputText value="Nome"  styleClass="textverysmall"/>
        </f:facet>
        <h:outputText styleClass="textverysmall" value="#{result.descricaoCurta}" />
    </h:column>
</rich:suggestionbox>

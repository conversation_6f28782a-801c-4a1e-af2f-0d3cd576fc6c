<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
</head>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>


<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
     <title>
        <h:outputText value="Alterar Senha"/>
    </title>
    <h:form id="formLogin">
        <c:set var="titulo" scope="session" value="Alterar Senha"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Inicial:Informações_do_Cliente"/>
        <h:panelGrid columns="1" width="100%"  cellpadding="0" cellspacing="0" styleClass="font-size-Em-max">

            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" >
                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                    <f:facet name="header">
                        <span class="texto-font texto-cor-cinza texto-bold texto-size-20-real texto-bold">${msg_aplic.prt_AlterarSenhaSenha_titulo_cli}</span>
                    </f:facet>

                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" cellpadding="9" cellspacing="0"
                                 columnClasses="classEsquerda, classDireita"
                                 width="100%">
                        <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_NomeUsuario_cli}"/>
                        <h:inputText id="nomeusuario" readonly="true"  
                                     size="30" maxlength="30"  onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="inputTextClean" value="#{LoginControle.nome}"/>

                        <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Username_cli}"/>
                        <h:inputText id="username" size="14" maxlength="20"  
                                     onblur="blurinput(this);"  onfocus="focusinput(this);"
                                     styleClass="inputTextClean" value="#{LoginControle.username}" disabled="true"/>

                        <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_SenhaAtual_cli}"/>
                        <h:inputSecret id="senhaatual"  size="14" maxlength="14"  onblur="blurinput(this);"  
                                       onfocus="focusinput(this);" styleClass="inputTextClean"
                                       value="#{LoginControle.senhaAtual}"/>

                        <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_NovaSenha_cli}"/>
                        <h:inputSecret id="novasenha"  size="14" maxlength="14"  onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="inputTextClean" value="#{LoginControle.senha1}"/>

                        <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_ConfirmarSenha_cli}"/>
                        <h:inputSecret id="confirmarnovasenha" size="14" maxlength="14"  onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="inputTextClean" value="#{LoginControle.senha2}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
        <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
            <h:panelGrid columns="1" width="100%">
                <f:verbatim>
                    <h:outputText value=" "/>
                </f:verbatim>
            </h:panelGrid>
            <h:commandButton  rendered="#{LoginControle.sucesso}" image="./imagens/sucesso.png"/>
            <h:commandButton rendered="#{LoginControle.erro}" image="./imagens/erro.png"/>
            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagem"  value="#{LoginControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{LoginControle.mensagemDetalhada}"/>
            </h:panelGrid>
        </h:panelGrid>

        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink id="confirmarsenha" type="submit" styleClass="botaoPrimario texto-size-14-real"
                             value="#{msg_bt.btn_alterarsenha_cli}"
                             action="#{LoginControle.alterarSenhaCliente}"
                             oncomplete="#{LoginControle.javaScriptFecharPopUpAlteracaoSenha}"
                             reRender="panelMensagemErro">
            </a4j:commandLink>
        </h:panelGroup>
    </h:form>
</f:view>
<script>
    document.getElementById("formLogin:username").focus();
</script>

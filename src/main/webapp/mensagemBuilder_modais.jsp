<%@include file="includes/imports.jsp" %>
<rich:modalPanel id="panelAmostraClientes" autosized="true" shadowOpacity="true" width="800"
                 styleClass="novaModal"
                 height="400">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{MalaDiretaControle.nrTotalAmostra}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hiperlinkAmostraClientes"/>
            <rich:componentControl for="panelAmostraClientes" attachTo="hiperlinkAmostraClientes"
                                   operation="hide" event="onclick"></rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formAmostraClientes">

        <rich:panel>
            <h:outputText escape="false" value="#{MalaDiretaControle.filtrosDescricao}"/>
            <br/>
            <rich:panel id="optinpanel" rendered="#{MalaDiretaControle.exibirDetalhesOptin}">
                <h:outputText id="opttin"  escape="false" value="Total de e-mails que n�o ir�o receber: #{MalaDiretaControle.totalSemResposta + MalaDiretaControle.totalnaoAceito} "/>
                <a4j:commandLink rendered="#{(MalaDiretaControle.totalSemResposta + MalaDiretaControle.totalnaoAceito)> 0 }" styleClass="titulo3" id="optinDetalhes"  oncomplete="abrirPopup('../faces/relatorio/optinRel.jsp', 'Optin Relatorio', 1400, 300);" >
                    <h:outputText  value="Detalhes"/>
                </a4j:commandLink>
            </rich:panel>

        </rich:panel>

        <%--BOT�O EXCEL--%>
        <a4j:commandLink id="exportarExcelMailing"
                         styleClass="tudo texto-cor-azul linkPadrao "
                         style="margin-left: 8px; float: right;"
                           actionListener="#{MalaDiretaControle.exportarAmostra}"
                           oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Mailing', 800,200);#{ExportadorListaControle.msgAlert}"
                           accesskey="2">
            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
            <f:attribute name="tipo" value="xls"/>
            <f:attribute name="atributos"
                         value="matricula=Matr�cula,nome=Nome,situacao=Situa��o,emails=E-mails,telefones=Telefones"/>
            <f:attribute name="prefixo" value="Mailing"/>
        </a4j:commandLink>
        <%--BOT�O PDF--%>
        <a4j:commandLink id="exportarPdfMailing"
                         styleClass="tudo texto-cor-azul linkPadrao "
                         style="margin-left: 8px; float: right;"
                         actionListener="#{MalaDiretaControle.exportarAmostra}"
                         oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Mailing', 800,200);#{ExportadorListaControle.msgAlert}"
                         accesskey="2">
            <i class="fa-icon-print" style="font-size: 20px;"></i>
            <f:attribute name="tipo" value="pdf"/>
            <f:attribute name="atributos"
                         value="matricula=Matr�cula,nome=Nome,situacao=Situa��o,emails=E-mails,telefones=Telefones"/>
            <f:attribute name="prefixo" value="Mailing"/>
        </a4j:commandLink>

        <br>
        <br>


        <rich:dataTable value="#{MalaDiretaControle.listaAmostra}" var="amostra" width="100%" id="itensAmostra"
                        headerClass="consulta"
                        columnClasses="" styleClass="tabelaSimplesCustom showCellEmpty"
                        rows="7">
            <rich:column sortBy="#{amostra.matricula}"
                         rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}">
                <f:facet name="header">
                    <h:outputText value="Matr�cula"/>
                </f:facet>
                <h:outputText id="matricula" value="#{amostra.matricula}"/>
            </rich:column>

            <rich:column sortBy="#{amostra.nome}" rendered="#{!MalaDiretaControle.malaDiretaVO.importarLista}">
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cliente_label_nome}"/>
                </f:facet>
                <a4j:commandLink action="#{MalaDiretaControle.irParaTelaCliente}"
                                 value="#{amostra.nome}"
                                 id="nome"
                                 rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}"
                                 oncomplete="#{MalaDiretaControle.msgAlert}"/>
                <h:outputText value="#{amostra.nome}"
                              rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}"/>
            </rich:column>

            <rich:column sortBy="#{amostra.nome}" rendered="#{MalaDiretaControle.malaDiretaVO.importarLista}">
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cliente_label_nome}"/>
                </f:facet>
                <h:outputText value="#{amostra.nome}"/>
            </rich:column>

            <rich:column sortBy="#{amostra.situacao}"
                         rendered="#{!MalaDiretaControle.malaDiretaVO.cfgEvento.indicados}">
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Cliente_label_situacao}"/>
                </f:facet>
                <h:outputText id="situacao" value="#{amostra.situacao}"/>
            </rich:column>


            <rich:column sortBy="#{amostra.idade}"
                         rendered="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.mostrarIdade && MalaDiretaControle.modoDestinatarios}">
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_OrganizadorCarteira_idadeCliente}"/>
                </f:facet>
                <h:outputText value="#{amostra.idade}"/>
            </rich:column>

            <rich:column sortBy="#{amostra.dataCadastro}"
                         rendered="#{MalaDiretaControle.malaDiretaVO.mailingFiltros.mostrarDataCadastro && MalaDiretaControle.modoDestinatarios}">
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Indicacao_dataCadastro}"/>
                </f:facet>
                <h:outputText value="#{amostra.dataCadastroApresentar}"/>
            </rich:column>

            <rich:column sortBy="#{amostra.aniversario}"
                         rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.mostrarDataAniversario && MalaDiretaControle.modoDestinatarios}">
                <f:facet name="header">
                    <h:outputText value="Anivers�rio"/>
                </f:facet>
                <h:outputText value="#{amostra.aniversario}"/>
            </rich:column>

            <rich:column sortBy="#{amostra.risco}"
                         rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.grupoRisco&& MalaDiretaControle.modoDestinatarios}">
                <f:facet name="header">
                    <h:outputText value="Risco"/>
                </f:facet>
                <h:outputText value="#{amostra.risco}"/>
            </rich:column>

            <rich:column sortBy="#{amostra.diasFalta}"
                         rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.faltosos && MalaDiretaControle.modoDestinatarios}">
                <f:facet name="header">
                    <h:outputText value="Faltas"/>
                </f:facet>
                <h:outputText value="#{amostra.diasFalta}"/>
            </rich:column>

            <rich:column rendered="#{MalaDiretaControle.SMS}">
                <f:facet name="header">
                    <h:outputText value="#{msg_aplic.prt_Agenda_telefones}"/>
                </f:facet>
                <h:outputText value="#{amostra.telefones}" escape="false"/>
            </rich:column>

            <rich:column rendered="#{MalaDiretaControle.email}">
                <f:facet name="header">
                    <h:outputText value="E-MAIL"/>
                </f:facet>
                <h:outputText value="#{amostra.emails}" escape="false"/>
            </rich:column>

            <rich:column rendered="#{MalaDiretaControle.malaDiretaVO.cfgEvento.itensNaoAprovados}">
                <f:facet name="header">
                    <h:outputText value="Cod. Retorno"/>
                </f:facet>
                <h:outputText value="#{amostra.codRetorno}" title="#{amostra.descricaoRetorno}"/>
            </rich:column>

            <rich:column rendered="#{MalaDiretaControle.modoNaoEnviados}">
                <f:facet name="header">
                    <h:outputText value="Erro"/>
                </f:facet>
                <h:outputText value="#{amostra.erro}" escape="false"/>
            </rich:column>
        </rich:dataTable>
        <rich:datascroller for="itensAmostra" status="false"/>
    </a4j:form>
</rich:modalPanel>

<rich:modalPanel id="panelTextoLivreItem" autosized="true" shadowOpacity="true" width="420" height="180" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{MalaDiretaControle.labelModal}"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkTextoLivreItem"/>
            <rich:componentControl for="panelTextoLivreItem" attachTo="hidelinkTextoLivreItem" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:outputText id="textoLivreItem" escape="false" value="#{MalaDiretaControle.logSql}" />
</rich:modalPanel>

<rich:modalPanel id="panelHistorico" autosized="true" shadowOpacity="true" width="700"
                 styleClass="novaModal"
                 height="300">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Hist�rico de envios"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hiperlinkHistorico"/>
            <rich:componentControl for="panelHistorico" attachTo="hiperlinkHistorico"
                                   operation="hide" event="onclick"></rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formHistorico">
        <rich:dataTable id="itensHistorico" value="#{MalaDiretaControle.malaDiretaVO.historico}"

                        var="historico" width="100%" rows="7"
                        headerClass="consulta"
                        columnClasses="" styleClass="tabelaSimplesCustom showCellEmpty">

            <rich:column sortBy="#{historico.dataInicio}">
                <f:facet name="header">
                    <h:outputText value="In�cio"/>
                </f:facet>
                <h:outputText value="#{historico.dataInicio}">
                    <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                </h:outputText>
            </rich:column>

            <rich:column sortBy="#{historico.dataFim}">
                <f:facet name="header">
                    <h:outputText value="Fim"/>
                </f:facet>
                <h:outputText value="#{historico.dataFim}">
                    <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                </h:outputText>
            </rich:column>


            <rich:column sortBy="#{historico.status.descricao}">
                <f:facet name="header">
                    <h:outputText value="Status"/>
                </f:facet>
                <h:outputText value="#{historico.status.descricao}">
                </h:outputText>
            </rich:column>

            <rich:column sortBy="#{historico.nrEnviados}">
                <f:facet name="header">
                    <h:outputText value="Enviados"/>
                </f:facet>
                <a4j:commandLink action="#{MalaDiretaControle.consultarEnviados}"
                                 
                                 reRender="panelAmostraClientes"
                                 rendered="#{historico.nrEnviados > 0}"
                                 oncomplete="#{MalaDiretaControle.msgAlert}">
                    <h:outputText style="font-weight: bold;" value="#{historico.nrEnviados}">
                    </h:outputText>
                </a4j:commandLink>
                <h:outputText rendered="#{historico.nrEnviados == 0}" style="font-weight: bold;"
                              value="#{historico.nrEnviados}">
                </h:outputText>
            </rich:column>

            <rich:column sortBy="#{historico.nrNaoEnviados}">
                <f:facet name="header">
                    <h:outputText value="N�o Enviados"/>
                </f:facet>

                <a4j:commandLink action="#{MalaDiretaControle.consultarNaoEnviados}"
                                 
                                 reRender="panelAmostraClientes"
                                 rendered="#{historico.nrNaoEnviados > 0}"
                                 oncomplete="#{MalaDiretaControle.msgAlert}">
                    <h:outputText style="font-weight: bold;" value="#{historico.nrNaoEnviados}">
                    </h:outputText>
                </a4j:commandLink>
                <h:outputText rendered="#{historico.nrNaoEnviados == 0}" style="font-weight: bold;"
                              value="#{historico.nrNaoEnviados}">
                </h:outputText>
            </rich:column>

            <c:if test="${not empty historico.clientesNaoEnviados}">
                <rich:column sortBy="#{historico.nrAmostraDestinatarios}">
                    <f:facet name="header">
                        <h:outputText value="Destinat�rios"/>
                    </f:facet>

                    <a4j:commandLink action="#{MalaDiretaControle.consultarNaoEnviados}"

                                     id="colunaDestinatarios"
                                     reRender="panelAmostraClientes"
                                     rendered="#{historico.nrAmostraDestinatarios > 0}"
                                     oncomplete="#{MalaDiretaControle.msgAlert}">
                        <h:outputText style="font-weight: bold;" value="#{historico.nrAmostraDestinatarios}">
                        </h:outputText>
                    </a4j:commandLink>
                    <h:outputText rendered="#{historico.nrAmostraDestinatarios == 0}" style="font-weight: bold;"
                                  value="#{historico.nrAmostraDestinatarios}">
                    </h:outputText>
                </rich:column>
            </c:if>
            <rich:column>
                <f:facet name="header">
                    <h:outputText value="LOG"/>
                </f:facet>
                <a4j:commandLink action="#{MalaDiretaControle.mostrarLog}"
                                 
                                 reRender="panelTextoLivreItem"
                                 oncomplete="Richfaces.showModalPanel('panelTextoLivreItem');">
                    <h:outputText style="font-weight: bold;" value="..." title="LOG">
                    </h:outputText>
                </a4j:commandLink>
            </rich:column>

            <rich:column rendered="#{MalaDiretaControle.usuarioLogado.administrador}">
                <f:facet name="header">
                    <h:outputText value="SQL"/>
                </f:facet>
                <a4j:commandLink action="#{MalaDiretaControle.mostrarSql}"
                                 
                                 reRender="panelTextoLivreItem"
                                 oncomplete="Richfaces.showModalPanel('panelTextoLivreItem');">
                    <h:outputText style="font-weight: bold;" value="..." title="SQL">
                    </h:outputText>
                </a4j:commandLink>
            </rich:column>

        </rich:dataTable>
        <rich:datascroller align="center"
                           style="margin-top: 10px"
                           for="itensHistorico" maxPages="100"
                           id="scResultadoHistoricoProdutos"/>

    </a4j:form>

</rich:modalPanel>


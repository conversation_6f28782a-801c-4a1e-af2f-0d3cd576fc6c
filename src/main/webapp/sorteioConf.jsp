<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_sorteio_configuracoes}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" style="height:25px; background: url('./imagens/fundoBarraTopo.png') repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_sorteio_configuracoes}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaPar, linhaImpar" columnClasses="classEsquerda, classDireita"
                             width="100%">

                    <h:outputText value="Clientes: "/>
                    <h:panelGroup layout="block">
                        <rich:dataGrid value="#{SorteioControle.situacoesCliente}" var="situacao" columns="3"
                                       columnClasses="w33,w33,w33"
                                       width="100%">
                            <h:panelGroup>
                                <h:selectBooleanCheckbox value="#{situacao.selecionado}"/>
                                <h:outputText value="#{situacao.label}"/>
                            </h:panelGroup>
                        </rich:dataGrid>
                    </h:panelGroup>

                    <h:outputText value="Planos: "/>
                    <h:panelGroup layout="block">
                        <rich:dataGrid value="#{SorteioControle.planos}" var="plano" columns="3" width="100%"
                                       columnClasses="w33,w33,w33">
                            <h:panelGroup>
                                <h:selectBooleanCheckbox value="#{plano.selecionado}"/>
                                <h:outputText value="#{plano.descricao}"/>
                            </h:panelGroup>
                        </rich:dataGrid>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid id="painelMensagem" columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">

                        <h:outputText value=" "/>

                    </h:panelGrid>
                    <h:commandButton rendered="#{SorteioControle.sucesso}"
                                     image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{SorteioControle.erro}"
                                     image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" id="mensagem2"
                                      value="#{SorteioControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" id="mensagemDetalhada2"
                                      value="#{SorteioControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup>
                        <a4j:commandButton id="salvar"
                                           action="#{SorteioControle.gravarConfiguracao}" value="#{msg_bt.btn_gravar}"
                                           alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"
                                           reRender="form"/>

                        <h:commandButton id="consultar" immediate="true"
                                         action="consultar"
                                         value="#{msg_bt.btn_consultar}" alt="#{msg.msg_consultar_dados}" accesskey="4"
                                         styleClass="botoes nvoBt btSec"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>

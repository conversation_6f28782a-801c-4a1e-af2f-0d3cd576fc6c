<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>Histórico Armário</title>

    <%-- INICIO HEADER --%>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="topoReduzido.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="pages/ce/includes/topoReduzido.jsp"/>
            </c:if>
        </f:facet>

        <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Histórico Armário">

            </h:outputText>
        </h:panelGrid>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <%
                if (request.getParameter("codigoArmarioSelecionado") != null) {
                    session.setAttribute("codigoArmarioSelecionado", request.getParameter("codigoArmarioSelecionado"));
                }
            %>

            <a4j:keepAlive beanName="GestaoArmarioControle" />
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                        <a4j:commandLink id="btnExcel"
                                         styleClass="pure-button pure-button-small"
                                         actionListener="#{GestaoArmarioControle.exportar}"
                                         oncomplete="#{GestaoArmarioControle.oncomplete}"
                                         accesskey="3">
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos" value="descricao=Descrição,nomePessoa=Nome,nomeProduto=Produto,inicio=Dt. Início,fim=Dt. Fim Ajustada,fimOriginal=Dt. Fim,nomeResponsavel=Responsável,dataCadastro=Data cadastro"/>
                            <f:attribute name="prefixo" value="GestaoArmario"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <i class="fa-icon-excel" ></i> &nbsp Excel
                        </a4j:commandLink>

                        <a4j:commandLink id="btnPDF"
                                         styleClass="pure-button pure-button-small margin-h-10"
                                         actionListener="#{GestaoArmarioControle.exportar}"
                                         oncomplete="#{GestaoArmarioControle.oncomplete}"
                                         accesskey="4">
                            <f:attribute name="tipo" value="pdf"/>
                            <f:attribute name="atributos" value="descricao=Descrição,nomePessoa=Nome,nomeProduto=Produto,inicio=Dt. Início,fim=Dt. Fim Ajustada,fimOriginal=Dt. Fim,nomeResponsavel=Responsável,dataCadastro=Data cadastro"/>
                            <f:attribute name="prefixo" value="GestaoArmario"/>
                            <f:attribute name="titulo" value="Histórico de armário"/>
                            <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                            <i class="fa-icon-pdf" ></i> &nbsp PDF
                        </a4j:commandLink>


                    </h:panelGroup>


                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblGestaoArmario" class="tabelaGestaoArmario pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>Descrição</th>
                    <th>Nome</th>
                    <th>Produto</th>
                    <th>Dt. Início</th>
                    <th>Dt. Fim</th>
                    <th>Dt. Fim Ajustada</th>
                    <th>Responsável</th>
                    <th>Data Operação</th>
                </thead>
                <tbody></tbody>
            </table>

        </h:panelGroup>
        <%-- FIM CONTENT --%>
        <br/>


        <%-- INICIO FOOTER --%>
        <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
            <h:graphicImage id="iconSucesso" rendered="#{GestaoArmarioControle.sucesso}" value="./imagens/sucesso.png" />
            <h:graphicImage id="iconErro" rendered="#{GestaoArmarioControle.erro}" value="./imagens/erro.png" />

            <h:outputText styleClass="mensagem" rendered="#{not empty GestaoArmarioControle.mensagem}" value=" #{GestaoArmarioControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty GestaoArmarioControle.mensagemDetalhada}" value=" #{GestaoArmarioControle.mensagemDetalhada}"/>
        </h:panelGroup>
        <%-- FIM FOOTER --%>
    </h:form>

</h:panelGroup>

<script src="beta/js/ext-funcs.js" type="text/javascript"></script>

<script type="text/javascript">
    jQuery(window).on("load", function() {
        iniciarTabela("tabelaGestaoArmario", "${contexto}/prest/basico/armario", 7, "desc", "", false);
    });
</script>

</f:view>
<%@page contentType="text/html;charset=UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Profissao_tituloForm}"/>
    </title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Profissao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Profissao"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
                <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">

            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>
            <%--<h:outputLink value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Profissao"--%>
            <%--title="Clique e saiba mais: Profissão" target="_blank" rendered="#{LoginControle.permissaoAcessoMenuVO.profissao}">--%>
            <%--<i class="fa-icon-question-sign" style="font-size: 18px"></i>--%>
            <%--</h:outputLink>--%>
            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                         width="100%">

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Profissao_codigo}"/>
                <h:panelGroup>
                    <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                 value="#{ProfissaoControle.profissaoVO.codigo}"/>
                    <h:message for="codigo" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Profissao_descricao}"/>
                <h:panelGroup>
                    <h:inputText id="descricao" size="45" maxlength="45" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{ProfissaoControle.profissaoVO.descricao}"/>
                    <h:message for="descricao" styleClass="mensagemDetalhada"/>
                </h:panelGroup>

            </h:panelGrid>

            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">

                        <h:outputText value=" "/>

                    </h:panelGrid>
                    <h:commandButton rendered="#{ProfissaoControle.sucesso}" image="./imagens/sucesso.png"/>
                    <h:commandButton rendered="#{ProfissaoControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgProfissao" styleClass="mensagem" value="#{ProfissaoControle.mensagem}"/>
                        <h:outputText id="msgProfissaoDet" styleClass="mensagemDetalhada"
                                      value="#{ProfissaoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <c:if test="${modulo eq 'zillyonWeb'}">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{ProfissaoControle.novo}"
                                             value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                             styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{ProfissaoControle.gravar}"
                                             value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2"
                                             styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>


                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{ProfissaoControle.msgAlert}" action="#{ProfissaoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true"
                                             action="#{ProfissaoControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                             accesskey="4" styleClass="botoes nvoBt btSec"/>

                            <rich:spacer width="15px"/>
                            <a4j:commandLink id="log"
                                             action="#{ProfissaoControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             style="display: inline-block; padding: 8px 15px;"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>


                        </h:panelGroup>
                    </c:if>
                    <c:if test="${modulo eq 'centralEventos'}">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{ProfissaoControle.novo}"
                                             value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}"
                                             accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{ProfissaoControle.gravarCE}"
                                             value="#{msg_bt.btn_gravar}"
                                             alt="#{msg.msg_gravar_dados}"
                                             accesskey="2" styleClass="botoes nvoBt"
                                             actionListener="#{ProfissaoControle.autorizacao}">
                                <!-- Entidade.profissao -->
                                <f:attribute name="entidade" value="114"/>
                                <!-- Operacao.GRAVAR -->
                                <f:attribute name="operacao" value="G"/>
                            </a4j:commandButton>


                            <h:outputText value="    "/>

                            <a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}"
                                             action="#{ProfissaoControle.excluirCE}"
                                             value="#{msg_bt.btn_excluir}"
                                             alt="#{msg.msg_excluir_dados}"
                                             accesskey="3" styleClass="botoes nvoBt btSec btPerigo"
                                             actionListener="#{ProfissaoControle.autorizacao}">
                                <!-- Entidade.profissao -->
                                <f:attribute name="entidade" value="114"/>
                                <!-- Operacao.excluir -->
                                <f:attribute name="operacao" value="E"/>
                            </a4j:commandButton>


                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar" immediate="true"
                                             action="#{ProfissaoControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_consultar}"
                                             alt="#{msg.msg_consultar_dados}"
                                             accesskey="4" styleClass="botoes nvoBt btSec"
                                             actionListener="#{ProfissaoControle.autorizacao}">
                                <!-- Entidade.profissao -->
                                <f:attribute name="entidade" value="114"/>
                                <!-- Operacao.consultar -->
                                <f:attribute name="operacao" value="C"/>
                            </a4j:commandButton>


                        </h:panelGroup>
                    </c:if>

                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>
<%@page contentType="text/html;charset=UTF-8" %>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/font-awesome-4/css/font-awesome.css" type="text/css" rel="stylesheet" />
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet" />

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<script src="beta/js/jquery.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<style type="text/css">
    .colunaEsquerda {
        vertical-align: middle;
        text-align: left;
    }

    .colunaDireita {
        vertical-align: middle;
        text-align: right;
        width: 50%;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_Gympass_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Gympass_tituloForm}" />
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Inicial:Recurso_GymPass:Cadastro_Gympass" />
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp" />
    </f:facet>
    <%-- FIM HEADER --%>
    <center>
        <span CLASS="texto-size-14-real">Lembre-se: você encontra as informações sobre valores na <a
                href="https://www.gympass.com/horarios-de-reserva/my_reservations"
                target="_blank"
                class="texto-size-14-real">GymPass.</a>
        </span>
    </center>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">


        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;" />
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="colunaDireita, colunaEsquerda"
                    width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Gympass_valormaximo}: "
                        style="font-size: medium;" />
                    <h:panelGroup>
                        <h:inputText id="valormaximo" size="8" style="text-align: right; font-size: medium;"
                            styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                            value="#{GympassControle.gympassVO.valorMaximo}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:inputText>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Gympass_datainicial}: "
                        style="font-size: medium;" />
                    <h:panelGroup>
                        <rich:calendar id="dataInicial" value="#{GympassControle.gympassVO.dataInicial}" inputSize="8"
                            inputStyle="font-size: medium; text-align: right;" oninputblur="blurinput(this);"
                            oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                            oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                            datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Gympass_datafinal}: "
                        style="font-size: medium;" />
                    <h:panelGroup>
                        <rich:calendar id="dataFinal" value="#{GympassControle.gympassVO.dataFinal}" inputSize="8"
                            inputStyle="font-size: medium; text-align: right;" oninputblur="blurinput(this);"
                            oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                            oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                            datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="9" width="100%" style="margin-top: 13px;">
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="1#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="primeiro" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[0]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="primeiro" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="2#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="segundo" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[1]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="segundo" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="3#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="terceiro" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[2]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="terceiro" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="4#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="quarto" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[3]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="quarto" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="5#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="quinto" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[4]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="quinto" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="6#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="sexto" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[5]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="sexto" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="7#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="setimo" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[6]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="setimo" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="8#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="oitavo" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[7]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="oitavo" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar">
                        <h:outputText styleClass="tituloCampos" value="9#{msg_aplic.prt_Gympass_dia}"
                            style="font-size: medium" />
                        <h:panelGroup>
                            <h:inputText id="nono" size="6" style="font-size: medium; text-align: right;"
                                styleClass="campos" onkeypress="return formatar_moeda(this,'.',',',event);"
                                value="#{GympassControle.valores[8]}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:inputText>
                            <h:message for="nono" styleClass="mensagemDetalhada" />
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup style="margin-top: 20px;">
                            <a4j:commandButton id="novo" action="#{GympassControle.novo}" value="#{msg_bt.btn_novo}"
                                alt="#{msg.msg_novo_dados}" accesskey="2" styleClass="botoes nvoBt btSec" />

                            <a4j:commandButton id="gravar" action="#{GympassControle.gravar}"
                                value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2"
                                styleClass="botoes nvoBt" oncomplete="#{GympassControle.mensagemNotificar}" />

                            <a4j:commandButton id="excluir" action="#{GympassControle.validarDadosExclusao}"
                                value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="2"
                                oncomplete="#{GympassControle.onComplete}" styleClass="botoes nvoBt btSec" />

                            <a4j:commandButton id="consultar" action="#{GympassControle.consultar}"
                                value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="2"
                                styleClass="botoes nvoBt btSec" />

                            <rich:spacer width="10px" />
                            <a4j:commandLink action="#{GympassControle.realizarConsultaLogObjetoSelecionado}"
                                reRender="form"
                                oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                style="display: inline-block; padding: 8px 15px; margin-left: -6px; text-decoration: none; font-family: 'FontAwesome';"
                                title="Visualizar Log" styleClass="botoes nvoBt btSec fa-list-ul" />
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="mdlAvisoExcluirGympass" styleClass="novaModal" autosized="true" shadowOpacity="true"
        width="450" height="210">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Exclusão de Gympass" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%" style="margin-top:16px;">
                <h:outputText value="Deseja realizar a exclusão dessa tabela Gympass?"
                    style="color:#777; font-size:16px; font-family:Arial;" />
                <h:panelGroup layout="block" style="margin-top:30px;">
                    <a4j:commandLink action="#{GympassControle.excluir}" id="confirmacaoOperacaoSim"
                        reRender="form,mdlAvisoExcluirGympass" accesskey="2" value="Sim" styleClass="botoes nvoBt"
                        oncomplete="#{GympassControle.mensagemNotificar};" />
                    <a4j:commandLink value="Não" status="false"
                        onclick="Richfaces.hideModalPanel('mdlAvisoExcluirGympass');" reRender="mdlAvisoExcluirGympass"
                        id="confirmacaoOperacaoNao" styleClass="botoes nvoBt" />
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>
<script>
    document.getElementById("form:valormaximo").focus();
</script>
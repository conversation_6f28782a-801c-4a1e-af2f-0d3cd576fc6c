<%@include file="includes/imports.jsp" %>
<h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
    <h:panelGroup styleClass="container-box-header" layout="block">
        <h:panelGroup layout="block" styleClass="margin-box">
            <h:outputText styleClass="container-header-titulo" value="Renegocia��o"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-mudar-o-valor-de-uma-parcela-ou-dividir-ela-em-varias-vezes/"
                          title="Clique e saiba mais: Renegocia��o"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
    </h:panelGroup>
    <h:panelGroup layout="block" styleClass="margin-box">
        <!-- inicio bot�es -->
        <div style="clear:both;margin-bottom:1px;"></div>
        <h:panelGroup id="pnlRenegociacao">
            <h:panelGrid width="100%" columns="2" rowClasses="linhaTop">
                <h:panelGroup>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td align="left" valign="top" colspan="2">
                                <div style="clear:both;" class="text">
                                    <p style="margin-bottom:4px;">
                                        <img src="${root}/images/arrow2.gif" width="16" height="16"
                                             style="vertical-align:middle;margin-right:6px;">
                                        Renegocia��o
                                    </p>
                                </div>
                                <rich:separator height="2px" lineType="dotted"/>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                             columnClasses="classEsquerda, classDireita"
                                             width="100%" style="margin-bottom: 12px">

                                    <h:outputText value="Nome:"/>
                                    <h:outputText value="#{RenegociacaoControle.itemParaRenegociar.nomeCliente}"/>

                                    <h:outputText value="Contrato:"/>
                                    <h:outputText value="#{RenegociacaoControle.itemParaRenegociar.contrato}"/>

                                    <h:outputText value="Descri��o:"/>
                                    <h:outputText value="#{RenegociacaoControle.itemParaRenegociar.descricao}"/>
                                </h:panelGrid>
                            </td>
                        </tr>
                        <tr>
                            <td align="left" valign="top">
                                <h:outputText styleClass="titulo2" value="Parcelas para renegociar"/>
                                <rich:dataTable id="tblParcelasParaRenegociar"
                                                value="#{RenegociacaoControle.parcelasParaRenegociar}"
                                                var="parcela">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Descri��o"/>
                                        </f:facet>
                                        <h:outputText value="#{parcela.descricao}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Data de Vencimento"/>
                                        </f:facet>
                                        <h:outputText value="#{parcela.dataVencimento_Apresentar}"/>
                                    </rich:column>
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor da Parcela"/>
                                        </f:facet>
                                        <h:outputText value="#{parcela.valorParcela_Apresentar}"/>

                                    </rich:column>
                                    <rich:column rendered="#{!RenegociacaoControle.incluirProdutosExtra}"
                                                 style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Op��es"/>
                                        </f:facet>
                                        <a4j:commandButton rendered="#{parcela.codigo == 0}" image="/imagens/botaoRemover.png"
                                                           action="#{RenegociacaoControle.removerProdutosExtras}"
                                                           reRender="tblParcelasParaRenegociar,pnlAditivos,tablepreviewtotal,totalParaRenegociar,totalLancado,residuo, idITValorParcela, totalRenegociar"/>
                                    </rich:column>
                                </rich:dataTable>

                                <h:panelGroup id="pnlAditivos">
                                    <h:panelGrid columns="1"  rendered="#{RenegociacaoControle.incluirProdutosExtra && RenegociacaoControle.apresentarTaxaDesconto}"
                                                 rowClasses="linhaImpar, linhaPar" width="100%" style="margin-top: 12px">
                                        <h:selectOneRadio id="desconto" styleClass="titulo3" value="#{RenegociacaoControle.tipoProdutoExtra}">
                                            <f:selectItem  itemLabel="Adicionar um Desconto" itemValue="DE" />
                                            <f:selectItem itemLabel="Adicionar uma Taxa/Juros" itemValue="TX"/>
                                            <a4j:support event="onchange" action="#{RenegociacaoControle.alterarTipoProdutoExtra}"
                                                         reRender="tblParcelasParaRenegociar,tablepreviewtotal,totalRenegociar,totalLancado,residuo,pnlAditivos,cobrancaAutomaticaJuros"/>
                                        </h:selectOneRadio>
                                    </h:panelGrid>

                                    <h:panelGrid columns="1" rendered="#{RenegociacaoControle.temParcelaCC}" rowClasses="linhaImpar, linhaPar" width="100%" style="margin-top: 12px">
                                        <h:outputText styleClass="mensagemDetalhada" style="font-size: 14px" value="A parcela � proveniente de Conta Corrente e por isso n�o poder� adicionar taxa ou aplicar desconto."/>
                                    </h:panelGrid>

                                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                                 rendered="#{RenegociacaoControle.incluirProdutosExtra && RenegociacaoControle.apresentarTaxaDesconto}"
                                                 width="100%" style="margin-top: 12px">

                                        <%--<c:if test="${(ComissaoControle.tipoRelatorioEscolhido eq 1) or (ComissaoControle.tipoRelatorioEscolhido eq 3)}">--%>
                                        <%--<f:selectItem itemValue="SO" itemLabel="Somente Operador"/>--%>
                                        <%--</c:if>--%>

                                        <h:outputText rendered="#{RenegociacaoControle.tipoProdutoExtra == 'TX'}"
                                                      value="TAXA/JUROS"/>
                                        <h:panelGroup rendered="#{RenegociacaoControle.tipoProdutoExtra == 'TX'}">
                                            <h:inputText value="#{RenegociacaoControle.taxa.valorParcela}"
                                                         size="10" style="position:relative; top:0;"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                                         styleClass="form">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:inputText>
                                            <a4j:commandButton value="Adicionar" action="#{RenegociacaoControle.adicionarTaxa}"  oncomplete="#{RenegociacaoControle.mensagemNotificar}"
                                                               reRender="tblParcelasParaRenegociar,tablepreviewtotal,totalRenegociar,totalLancado,residuo,pnlAditivos, idITValorParcela"/>
                                        </h:panelGroup>

                                        <h:outputText rendered="#{RenegociacaoControle.tipoProdutoExtra == 'DE'}"
                                                      value="DESCONTOS"/>
                                        <h:panelGroup rendered="#{RenegociacaoControle.tipoProdutoExtra == 'DE'}">
                                            <h:inputText id="taxa" value="#{RenegociacaoControle.desconto.valorParcela}"
                                                         size="10" style="position:relative; top:0;"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                                         styleClass="form">
                                                <f:converter converterId="FormatadorNumerico"/>
                                            </h:inputText>
                                            <a4j:commandButton id="adicionar" value="Adicionar" action="#{RenegociacaoControle.adicionarDesconto}" oncomplete="#{RenegociacaoControle.mensagemNotificar}"
                                                               reRender="tblParcelasParaRenegociar,tablepreviewtotal,totalRenegociar,totalLancado,residuo,pnlAditivos, idITValorParcela,pnlMsg"/>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                </h:panelGroup>

                            </td>
                            <td align="left" valign="top">
                                <h:outputText styleClass="titulo2" value="Parcelas renegociadas"/>
                                <rich:dataTable id="tblParcelasRenegociadas"
                                                value="#{RenegociacaoControle.parcelasRenegociadas}"
                                                var="parcelaRenegociada">
                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Descri��o"/>
                                        </f:facet>
                                        <h:outputText value="#{parcelaRenegociada.descricao}"/>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Data de Vencimento"/>
                                        </f:facet>
                                        <rich:calendar value="#{parcelaRenegociada.dataVencimento}"
                                                       inputSize="9" inputClass="form"
                                                       oninputchange="return validar_Data(this.id);"
                                                       oninputblur="blurinput(this);"
                                                       oninputfocus="focusinput(this);"
                                                       oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                                       datePattern="dd/MM/yyyy" enableManualInput="true"
                                                       zindex="2" showWeeksBar="false">
                                            <a4j:support event="oninputchange"
                                                         reRender="tblParcelasRenegociadas,tablepreviewtotal,totalRenegociar,totalLancado,residuo,pnlMsg"
                                                         action="#{RenegociacaoControle.validardataparcelas}"
                                                         focus="dataPagto"/>
                                            <a4j:support event="onchanged"
                                                         reRender="tblParcelasRenegociadas,tablepreviewtotal,totalRenegociar,totalLancado,residuo,pnlMsg"
                                                         action="#{RenegociacaoControle.validardataparcelas}"
                                                         focus="dataPagto"/>
                                        </rich:calendar>
                                    </rich:column>

                                    <rich:column>
                                        <f:facet name="header">
                                            <h:outputText value="Valor da Parcela"/>
                                        </f:facet>
                                        <h:inputText value="#{parcelaRenegociada.valorParcela}"
                                                     size="10" style="position:relative; top:0;"
                                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                                     styleClass="form"
                                                     id="idITValorParcela">
                                            <f:converter converterId="FormatadorNumerico"/>
                                            <a4j:support event="onchange"
                                                         reRender="tblParcelasRenegociadas,tablepreviewtotal,totalRenegociar,totalLancado,residuo"
                                                         action="#{RenegociacaoControle.verificarAlteracaoManual}"/>
                                        </h:inputText>
                                    </rich:column>

                                    <rich:column style="text-align: center">
                                        <f:facet name="header">
                                            <h:outputText value="Op��es"/>
                                        </f:facet>
                                        <a4j:commandButton id="btnAdicionarParcela" value="adicionar"
                                                           action="#{RenegociacaoControle.adicionarParcela}"
                                                           reRender="tblParcelasRenegociadas,tablepreviewtotal,totalRenegociar,totalLancado,residuo"
                                                           image="./images/icon_mais.gif"
                                                           title="Adicionar Parcela"/>

                                        <a4j:commandButton id="btnRemoverParcela" value="remover"
                                                           rendered="#{RenegociacaoControle.mostrarBotaoRemover}"
                                                           action="#{RenegociacaoControle.removerParcela}"
                                                           reRender="tblParcelasRenegociadas,tablepreviewtotal,totalRenegociar,totalLancado,residuo"
                                                           image="./images/icon_delete.gif"
                                                           title="Remover Parcela"/>
                                    </rich:column>
                                </rich:dataTable>
                                <h:outputText style="line-height: 10vh" value="Justificativa:"/>
                                <h:inputTextarea cols="80" style="height: 60px; width: 322px; resize: none; margin-left: 5px;margin-top: 5px" id="justificativaReneg"
                                                 value="#{RenegociacaoControle.justificativaRenegociacao}"/>
                            </td>
                        </tr>
                    </table>
                </h:panelGroup>
            </h:panelGrid>

            <table id="tablepreviewtotal" width="100%" border="0" cellspacing="0" cellpadding="0"
                   class="tablepadding2">
                <tr>
                    <td align="left" valign="top">
                        <div class="sep" style="margin: 2px 0;">
                            <img src="images/shim.gif">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top">
                        <table width="100%" border="0" cellspacing="0"
                               cellpadding="0" class="tablepreviewtotal">
                            <tr>
                                <td width="30%" align="left" valign="middle">Total para renegociar =
                        <span class="verde">R$ <h:outputText id="totalRenegociar"
                                                             value="#{RenegociacaoControle.totalParaRenegociar}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText></span>
                                </td>
                                <td width="40%" align="center" valign="middle">Total Lan&ccedil;ado =
                        <span class="verde">R$ <h:outputText id="totalLancado"
                                                             value="#{RenegociacaoControle.totalLancado}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText></span>
                                </td>
                                <td align="right" valign="middle">Res�duo =
                        <span class="verde"> R$<h:outputText id="residuo" value="#{RenegociacaoControle.residuo}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText></span>
                                </td>
                            </tr>

                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="top">
                        <div class="sep" style="margin: 2px 0;">
                            <img src="images/shim.gif">
                        </div>
                    </td>
                </tr>
            </table>

            <%--<a4j:commandButton id="btnConfirmar"--%>
            <%--style="float: right"--%>
            <%--image="images/btn_confirmar2.gif"--%>
            <%--reRender=""--%>
            <%--action="#{RenegociacaoControle.validarRenegociacao}"--%>
            <%--value="Confirmar"--%>
            <%--oncomplete="#{MovPagamentoControle.abrirRichModalConfirmaPagamento}"/>--%>

            <h:panelGroup layout="block" style="float: right; margin-bottom: 8px"
                          rendered="#{!RenegociacaoControle.renegociacaoConcluida}">
                <a4j:commandButton id="botaoCancelar"
                                   image="images/btn_cancelar.gif" action="#{RenegociacaoControle.cancelar}"
                                   reRender="pnlRenegociacao, pnlMsg"
                                   value="Cancelar"/>

                <rich:spacer width="12px"/>

                <a4j:commandButton id="btnConfirmar"
                                   image="images/btn_confirmar2.gif" action="#{RenegociacaoControle.renegociar}"
                                   reRender="pnlRenegociacao, pnlMsg"
                                   value="Confirmar"/>
            </h:panelGroup>

            <h:panelGroup layout="block" style="float: right; margin-bottom: 8px"
                          rendered="#{RenegociacaoControle.renegociacaoConcluida}">
                <a4j:commandButton id="botaoVoltar"
                                   image="images/btn_voltar.gif" action="#{RenegociacaoControle.voltar}"
                                   reRender="pnlRenegociacao, pnlMsg"
                                   value="Voltar"/>

                <rich:spacer width="12px"/>
            </h:panelGroup>

            <h:panelGrid id="pnlMsg" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                    <%-- <h:commandButton id="icSucesso" rendered="#{RenegociacaoControle.sucesso}" image="./imagens/sucesso.png"/> --%>
                    <h:commandButton id="icErro" rendered="#{RenegociacaoControle.erro}" image="./imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msg" styleClass="mensagem" value="#{RenegociacaoControle.mensagem}"/>
                        <h:outputText id="msgDet" styleClass="mensagemDetalhada" value="#{RenegociacaoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>
  Date: 04/10/2017
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="/includes/imports.jsp" %>

<style>
    .tituloConhecimentoTopoUCP:hover {
        color: white;
    }

    .tituloConhecimentoTopoUCP {
        text-align: left;
        font-size: 14px;
        color: #29AAE2 !important;
    }


    /* Buzz Out */
    @-webkit-keyframes hvr-buzz-out {

        10% {
            -webkit-transform: translateX(3px) rotate(2deg);
            transform: translateX(3px) rotate(2deg);
        }
        20% {
            -webkit-transform: translateX(-3px) rotate(-2deg);
            transform: translateX(-3px) rotate(-2deg);
        }
        30% {
            -webkit-transform: translateX(3px) rotate(2deg);
            transform: translateX(3px) rotate(2deg);
        }
        40% {
            -webkit-transform: translateX(-3px) rotate(-2deg);
            transform: translateX(-3px) rotate(-2deg);
        }
        50% {
            -webkit-transform: translateX(2px) rotate(1deg);
            transform: translateX(2px) rotate(1deg);
        }
        60% {
            -webkit-transform: translateX(-2px) rotate(-1deg);
            transform: translateX(-2px) rotate(-1deg);
        }
        70% {
            -webkit-transform: translateX(2px) rotate(1deg);
            transform: translateX(2px) rotate(1deg);
        }
        80% {
            -webkit-transform: translateX(-2px) rotate(-1deg);
            transform: translateX(-2px) rotate(-1deg);
        }
        90% {
            -webkit-transform: translateX(1px) rotate(0);
            transform: translateX(1px) rotate(0);
        }
        100% {
            -webkit-transform: translateX(-1px) rotate(0);
            transform: translateX(-1px) rotate(0);
        }
    }
    @keyframes hvr-buzz-out {
        10% {
            -webkit-transform: translateX(3px) rotate(2deg);
            transform: translateX(3px) rotate(2deg);
        }
        20% {
            -webkit-transform: translateX(-3px) rotate(-2deg);
            transform: translateX(-3px) rotate(-2deg);
        }
        30% {
            -webkit-transform: translateX(3px) rotate(2deg);
            transform: translateX(3px) rotate(2deg);
        }
        40% {
            -webkit-transform: translateX(-3px) rotate(-2deg);
            transform: translateX(-3px) rotate(-2deg);
        }
        50% {
            -webkit-transform: translateX(2px) rotate(1deg);
            transform: translateX(2px) rotate(1deg);
        }
        60% {
            -webkit-transform: translateX(-2px) rotate(-1deg);
            transform: translateX(-2px) rotate(-1deg);
        }
        70% {
            -webkit-transform: translateX(2px) rotate(1deg);
            transform: translateX(2px) rotate(1deg);
        }
        80% {
            -webkit-transform: translateX(-2px) rotate(-1deg);
            transform: translateX(-2px) rotate(-1deg);
        }
        90% {
            -webkit-transform: translateX(1px) rotate(0);
            transform: translateX(1px) rotate(0);
        }
        100% {
            -webkit-transform: translateX(-1px) rotate(0);
            transform: translateX(-1px) rotate(0);
        }
    }
    .shakeThat,.shakeThat:before{
        -webkit-animation-name: hvr-buzz-out !important;
        animation-name: hvr-buzz-out !important;
        -webkit-animation-duration: 1s !important;
        animation-duration: 1s !important;
        -webkit-animation-timing-function: linear !important;
        animation-timing-function: linear !important;
        -webkit-animation-iteration-count: 0.5 !important;
        animation-iteration-count: 0.5 !important;;
    }
</style>

<h:panelGroup layout="block" id="panelSuperiorConhecimento"
              rendered="#{UCPUsuarioControle.qtdConhecimentos > 0}"
              styleClass="col-md-4 col-md-offset-4"
              style="z-index: 100; position: absolute; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;">

    <h:panelGroup layout="block" styleClass="panelAbrirUCP"
                  id="panelAbrirUCP"
                  style="text-align: center; display: none; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;">

        <h:panelGroup layout="block"
                      id="panelResultadoTelaUCP"
                      style="text-align: left; padding: 20px 20px 20px 20px; background-color: #094771; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px; box-shadow: 0 2px 10px 0 rgba(0,0,0,0.25);">

            <a4j:repeat var="conhecimento" value="#{UCPUsuarioControle.listaTelaUCPApresentar}">
                <h:panelGroup layout="block"
                              style="padding: 5px">
                    <a4j:commandLink
                            id="linkCodigoConhecimentoUCP"
                            action="#{UCPUsuarioControle.notificarClick}"
                            oncomplete="window.open('#{conhecimento.linkPergunta}', '_blank');"
                            styleClass="tituloConhecimentoTopoUCP">
                        <i class="fa-icon-youtube-play"></i>
                        <h:outputText value="#{conhecimento.titulo}"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </a4j:repeat>

            <h:panelGroup layout="block"
                          rendered="#{UCPUsuarioControle.apresentarBtnMostrarMais}"
                          style="text-align: center; padding-top: 5px">
                <h:outputLink styleClass="btnUCP"
                              target="_blank"
                              value='#{SuperControle.contextPath}/redir?up&paginaAtualZW=#{UCPUsuarioControle.paginaAtualEncriptada}'>
                        <h:outputText value="Ver mais" style="color: white"/>
                        <h:outputText value=" #{UCPUsuarioControle.quantidadeVerMais} " style="font-weight: bold; color: white"/>
                        <h:outputText value=" na UCP" style="color: white"/>
                </h:outputLink>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="panelEsconderUCP">

        <h:panelGroup layout="block"
                      id="iconeUCP"
                      styleClass="icon-ucp col-md-6 col-md-offset-3"
                      style="text-align: center; padding: 5px; background-color: #094771; border-bottom-left-radius: 15px; border-bottom-right-radius: 15px; box-shadow: 0 2px 10px 0 rgba(0,0,0,0.25);">

            <a4j:commandLink
                    id="botaoAbrirUCP"
                    styleClass="botaoAbrirUCP"
                    status="false"
                    reRender="panelResultadoTelaUCP"
                    action="#{UCPUsuarioControle.preencherListaApresentar}"
                    style="text-decoration: none; font-size: 12px; font-weight: bold; color: #ff6e1e;"
                    ajaxSingle="true"
                    oncomplete="abrirPanelUCP()">
                <i id="iconeBotaoUCPTopo" class="fa-icon-chevron-down"></i> Aprenda mais sobre esta tela
            </a4j:commandLink>

            <a4j:commandLink
                    id="botaoFecharUCP"
                    styleClass="botaoFecharUCP"
                    status="false"
                    reRender="panelResultadoTelaUCP"
                    style="text-decoration: none; font-size: 12px; font-weight: bold; color: #ff6e1e; display: none;"
                    ajaxSingle="true"
                    onclick="fecharPanelUCP()">
                <i class=" fa-icon-chevron-up"></i> Fechar
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>


<script type="text/javascript">
    function abrirPanelUCP() {
        jQuery('.panelAbrirUCP').slideToggle("slow");
        jQuery('.botaoAbrirUCP').hide();
        jQuery('.botaoFecharUCP').show();
        //document.getElementsByClassName('botaoAbrirUCP')[0].style.display = 'none';
        //document.getElementsByClassName('botaoFecharUCP')[0].style.display = 'block';
    }

    function fecharPanelUCP() {
        //var botaoFechar = document.getElementsByClassName('botaoFecharUCP')[0];
        // var botaoAbrir =  document.getElementsByClassName('botaoAbrirUCP')[0];

        botaoFechar = jQuery('.botaoFecharUCP');
        if (botaoFechar.is(':visible')) {
            jQuery('.panelAbrirUCP').slideToggle("slow");
        }

        jQuery('.botaoFecharUCP').hide();
        jQuery('.botaoAbrirUCP').show();

        //if(botaoAbrir)
        //    botaoAbrir.style.display = 'block';
        //if(botaoFechar)
        //    botaoFechar.style.display = 'none';
    }
</script>
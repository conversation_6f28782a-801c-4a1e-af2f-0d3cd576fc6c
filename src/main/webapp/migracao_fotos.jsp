<%@page import="br.com.pactosolucoes.comuns.util.FileUtilities"%>
<%@page import="org.apache.commons.io.FileUtils"%>
<%@page import="java.io.InputStream"%>
<!DOCTYPE html>
<%-- 
    Document   : _migracao_fotos
    Created on : 30/03/2015, 10:50:34
    Author     : Waller
--%>

<%@page import="negocio.comuns.utilitarias.Calendario"%>
<%@page import="java.io.FileOutputStream"%>
<%@page import="servicos.propriedades.PropsService"%>
<%@page import="controle.arquitetura.SuperControle"%>
<%@page import="java.io.File"%>
<%@page import="java.io.FileReader"%>
<%@page import="java.util.Properties"%>
<%@page import="servicos.operacoes.midias.awss3.MigracaoFotosZWParaS3Service"%>
<%@page import="negocio.comuns.utilitarias.UteisValidacao"%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>

<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Migração Fotos ZW</title>
    </head>
    <body>        
        <%
            String empresa = request.getParameter("empresa");
            String type = request.getParameter("type");
            String urlFotosNuvem = request.getParameter("urlFotosNuvem");

            if (!UteisValidacao.emptyString(empresa) || empresa.equals("todas")) {
                response.getWriter().println(Calendario.hoje() + " Iniciando migracao...<br/>");
                Properties props = new Properties();
                try {
                    props.load(new FileReader(new File(SuperControle.class.getResource(
                            "/servicos/propriedades/SuperControle.properties").toURI())));
                    props.setProperty(PropsService.fotosParaNuvem, "true");
                    props.setProperty(PropsService.typeMidiasService, type);
                    props.setProperty(PropsService.urlFotosNuvem, urlFotosNuvem);
                    File f = new File(SuperControle.class.getResource(
                            "/servicos/propriedades/SuperControle.properties").toURI());
                    props.store(new FileOutputStream(f), "");
                    PropsService.refresh();
                    MigracaoFotosZWParaS3Service.main(new String[]{empresa});
                    //Arquivos padroes
                    try {
                        File fPessoa = new File(SuperControle.class.getResource("/br/com/pactosolucoes/comuns/util/resources/fotoPadrao.jpg").toURI());
                        File fEmpresa = new File(SuperControle.class.getResource("/br/com/pactosolucoes/comuns/util/resources/logoPadraoRelatorio.jpg").toURI());
                        String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
                        File dest = new File(path);
                        FileUtils.copyFileToDirectory(fPessoa, dest);
                        FileUtils.copyFileToDirectory(fEmpresa, dest);
                    } catch (Exception io) {
                        response.getWriter().println(io.getMessage());
                    }
                    //
                    response.getWriter().println(Calendario.hoje() + " Migração concluída!<br/>");
                } catch (Exception ex) {
                    response.getWriter().println(ex.getMessage());
                }
            }
        %>
    </body>
</html>

<%@page pageEncoding="ISO-8859-1"%>
<%@include file="include_imports.jsp" %>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

<rich:modalPanel id="mdlRenegociar" styleClass="novaModal" autosized="true" shadowOpacity="true" width="470">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Renegociar Parcelas"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideMdlRenegociar"/>
            <rich:componentControl for="mdlRenegociar" attachTo="hideMdlRenegociar" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formRenegociar" styleClass="font-size-Em-max">
        <h:panelGrid columns="1" styleClass="font-size-Em-max">
            <h:outputText styleClass="titulo3" value="Selecione as parcelas que deseja incluir na renegociação."/>
        </h:panelGrid>
        <rich:spacer height="10px"></rich:spacer>
        <h:panelGroup layout="block" style="height:150px; overflow-y:auto;">
           <h:panelGrid width="100%"  styleClass="font-size-Em-max" columnClasses="colunaCentralizada" cellpadding="3" columns="2">
                <rich:dataTable id="tabelaParcelas" width="600px"
                                columnClasses="colunaEsquerda, colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                value="#{MovParcelaControle.itemParaRenegociar.parcelas}" var="parcelaContrato"
                                rendered="#{!MovParcelaControle.itemParaRenegociar.emptyParcelas}">

                    <rich:column width="40%">
                        <f:facet name="header">
                            <h:outputText value="Parcela"/>
                        </f:facet>
                        <h:selectBooleanCheckbox id="selecionarParcela" value="#{parcelaContrato.parcelaEscolhida}"/>
                        <h:outputText value="#{parcelaContrato.descricao}"/>
                        <h:outputText style="font-weight:bold;" rendered="#{parcelaContrato.contrato.pagarComBoleto}"
                                      value=" - (Boleto Bancário)"/>
                    </rich:column>

                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Data de Lançamento"/>
                        </f:facet>
                        <h:outputText value="#{parcelaContrato.dataRegistro_Apresentar}"/>
                    </rich:column>

                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Vencimento"/>
                        </f:facet>
                        <h:outputText value="#{parcelaContrato.dataVencimento_Apresentar}"/>
                    </rich:column>

                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText value="#{parcelaContrato.valorParcela}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>
           </h:panelGrid>
        </h:panelGroup>
        <rich:spacer height="10px"></rich:spacer>
        <h:panelGrid styleClass="font-size-Em-max" columns="2"  columnClasses="classEsquerda, classDireita"
                     width="100%">
            <h:outputText value="Quantidade de parcelas a gerar"/>
            <h:inputText value="#{RenegociacaoControle.qtdParcelas}"/>

            <h:outputText value="Vencimento da primeira parcela"/>
            <rich:calendar value="#{RenegociacaoControle.dataPrimeiraParcela}"
                           inputSize="10"
                           inputClass="form"
                           oninputblur="blurinput(this);"
                           oninputfocus="focusinput(this);"
                           onchanged="validarDataPrimeiraParcela();"
                           oninputchange="validar_Data(this.id);validarDataPrimeiraParcela();"
                           datePattern="dd/MM/yyyy"
                           enableManualInput="true"
                           zindex="2"
                           showWeeksBar="false"
                           >
            </rich:calendar>
            <rich:jQuery id="mskData" selector=".rich-calendar-input"  query="mask('99/99/9999')" />
             <a4j:jsFunction name="validarDataPrimeiraParcela" oncomplete="Richfaces.showModalPanel('mdlRenegociar')"
                                            action="#{RenegociacaoControle.validardataprimeiraparcela}"
                                            reRender="mdlRenegociar,panelMensagem2"  focus="dataPagto"/>
        </h:panelGrid>


    <h:panelGrid id="panelMensagem2"  columns="1" width="100%" styleClass="tabMensagens" style="padding:15px;">
        <h:outputText styleClass="mensagem" value="#{RenegociacaoControle.mensagem}"/>
        <h:outputText styleClass="mensagemDetalhada" value="#{RenegociacaoControle.mensagemDetalhada}"/>
    </h:panelGrid>
    <rich:spacer height="10px"></rich:spacer>
    <h:panelGrid columns="1" styleClass="font-size-Em-max" width="100%" style="text-align: right">
        <a4j:commandLink id="linkRenegociar"
                         value="Renegociar"
                         oncomplete="#{MovParcelaControle.mensagemNotificar};#{MovParcelaControle.msgAlert}"
                         action="#{MovParcelaControle.prepararRenegociacao}"
                         reRender="panelMensagem2,formBoletoPendente"
                         styleClass="botoes nvoBt"/>
    </h:panelGrid>

    </h:form>
</rich:modalPanel>

<rich:modalPanel id="modalCancelarBoletosPendentes"
                 styleClass="novaModal" autosized="true"
                 shadowOpacity="true" width="550">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Boletos Pendentes"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hideModalCancelarBoletosPendentes"/>
            <rich:componentControl for="modalCancelarBoletosPendentes" attachTo="hideModalCancelarBoletosPendentes"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <h:form id="formBoletoPendente" styleClass="font-size-Em-max">
        <h:panelGroup layout="block"  styleClass="font-size-Em-max" style="padding: 10px 0 10px 0">
            <h:outputText styleClass="titulo3" style="font-size: 16px;"
                          value="Existe boleto pendente para as parcelas selecionadas. Deseja cancelar os boletos?"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="max-height:300px; overflow-y:auto;">
            <h:panelGrid width="100%" styleClass="font-size-Em-max" columnClasses="colunaCentralizada" cellpadding="3"
                         columns="2">
                <rich:dataTable id="tabelaBoletosPendentes" width="600px"
                                columnClasses="colunaEsquerda, colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                value="#{MovParcelaControle.listaBoletosPendentes}" var="boleto">

                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Dt. Registro"/>
                        </f:facet>
                        <h:outputText value="#{boleto.dataRegistroApresentar}"/>
                    </rich:column>

                    <rich:column width="40%">
                        <f:facet name="header">
                            <h:outputText value="Parcelas"/>
                        </f:facet>

                        <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status"
                                        styleClass="tabelaDados font-size-Em"
                                        style="margin: 0"
                                        value="#{boleto.listaBoletoMovParcela}"
                                        var="boletomovparcela">

<%--                            <rich:column>--%>
<%--                                <f:facet name="header">--%>
<%--                                    <h:outputText value="Parcela"--%>
<%--                                                  style="padding-inline-end: 8px;margin-right: 10px; display: block;"/>--%>
<%--                                </f:facet>--%>
<%--                                <h:outputText styleClass="tooltipster"--%>
<%--                                              value="#{boletomovparcela.movParcelaVO.codigo}"/>--%>
<%--                            </rich:column>--%>

                            <rich:column>
<%--                                <f:facet name="header">--%>
<%--                                    <h:outputText value="Descrição"--%>
<%--                                                  style="padding-inline-end: 8px; margin-right: 10px; display: block;"/>--%>
<%--                                </f:facet>--%>
                                <h:outputText styleClass="tooltipster"
                                              title="#{boletomovparcela.movParcelaVO.pessoa_Apresentar_UpperCase}"
                                              value="#{boletomovparcela.movParcelaVO.descricao}"/>
                            </rich:column>

                            <rich:column>
<%--                                <f:facet name="header">--%>
<%--                                    <h:outputText value="Valor" style="padding-inline-end: 50px;"/>--%>
<%--                                </f:facet>--%>
                                <h:outputText
                                        value="#{boleto.pago ? boletomovparcela.valorParcelaApresentar : boletomovparcela.valorTotalApresentar}"/>
                            </rich:column>
                        </rich:dataTable>
                    </rich:column>

                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Vencimento"/>
                        </f:facet>
                        <h:outputText value="#{boleto.dataVencimentoApresentar}"/>
                    </rich:column>

                    <rich:column width="20%">
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText value="#{boleto.valorApresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGrid>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="font-size-Em-max"
                     style="display: flex; justify-content: center; grid-column-gap: 40px; padding: 10px 0 10px 0;">

            <a4j:commandLink id="btnCancelarOperacaoBoletoPendente"
                             value="NÃO"
                             onclick="Notifier.cleanAll();Richfaces.hideModalPanel('modalCancelarBoletosPendentes')"
                             styleClass="botoes nvoBt btSec"/>

            <a4j:commandLink id="btnConfirmarOperacaoBoletoPendente"
                             value="SIM"
                             action="#{MovParcelaControle.confirmarCancelarBoletoPendente}"
                             oncomplete="#{MovParcelaControle.onCompleteBoletoPendente}"
                             reRender="panelMensagem2"
                             styleClass="botoes nvoBt"/>
        </h:panelGroup>
    </h:form>
</rich:modalPanel>

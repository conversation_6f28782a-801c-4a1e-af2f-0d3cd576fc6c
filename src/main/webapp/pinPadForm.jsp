<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_PinPad_tituloForm}"/>
    </title>

    <style>
        .subordinado {
            padding: 5px !important;
        }
    </style>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_PinPad_tituloForm}"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{PinPadControle.liberarBackingBeanMemoria}"
                           id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid id="gridForm" columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{!PinPadControle.pinPadVO.novoObj}"
                                  value="Codigo"/>
                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{!PinPadControle.pinPadVO.novoObj}"
                                  value="#{PinPadControle.pinPadVO.codigo}"/>

                    <h:outputText styleClass="tituloCampos"
                                  value="Descrição:"/>
                    <h:panelGroup>
                        <h:inputText id="descricao" size="50" maxlength="255" styleClass="form"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     value="#{PinPadControle.pinPadVO.descricao}"/>
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos"
                                  value="Tipo:"/>
                    <h:selectOneMenu id="tipo" styleClass="form" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     disabled="#{!PinPadControle.pinPadVO.novoObj}"
                                     value="#{PinPadControle.pinPadVO.pinpad}">
                        <f:selectItems value="#{PinPadControle.listaSelectTipoPinPad}"/>
                        <a4j:support event="onchange" reRender="form"/>
                    </h:selectOneMenu>

                    <c:if test="${PinPadControle.pinPadVO.stoneConnect || PinPadControle.pinPadVO.getCard}">
                        <h:outputText styleClass="tituloCampos"
                                      rendered="#{PinPadControle.pinPadVO.stoneConnect}"
                                      value="Serial Number:"/>
                        <h:outputText styleClass="tituloCampos"
                                      rendered="#{PinPadControle.pinPadVO.getCard}"
                                      value="PDV:"/>
                        <h:inputText id="serialNumberStone" size="20" styleClass="form"
                                     value="#{PinPadControle.pinPadVO.pdvPinpad}"/>

                        <h:outputText styleClass="tituloCampos" value="Número máximo de parcelas:"/>
                        <h:inputText onkeypress="return mascara(this.form, this.id, '99', event);"
                                     onkeyup="somenteNumeros(this);"
                                     id="nrMaxParcelasStoneConnect" size="2" maxlength="2"
                                     onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     value="#{PinPadControle.pinPadVO.nrMaxParcelas}"/>

                        <h:outputText styleClass="tituloCampos"
                                      value="Empresa:"/>
                        <h:selectOneMenu id="empresa" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         value="#{PinPadControle.pinPadVO.empresa.codigo}">
                            <f:selectItems value="#{PinPadControle.selectEmpresa}"/>
                            <a4j:support event="onchange" reRender="form"/>
                        </h:selectOneMenu>

                        <h:outputText styleClass="tituloCampos"
                                      value="Convênio Cobrança:"/>
                        <h:selectOneMenu id="convenioCobranca" styleClass="form" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         value="#{PinPadControle.pinPadVO.convenioCobranca.codigo}">
                            <f:selectItems value="#{PinPadControle.selectConvenioCobranca}"/>
                            <a4j:support event="onchange" reRender="form"/>
                        </h:selectOneMenu>
                    </c:if>
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{PinPadControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{PinPadControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{PinPadControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{PinPadControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{PinPadControle.novo}"
                                               value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                               styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="salvar"
                                               action="#{PinPadControle.gravar}"
                                               reRender="form"
                                               oncomplete="#{PinPadControle.mensagemNotificar}#{PinPadControle.msgAlert}"
                                               value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2"
                                               styleClass="botoes nvoBt"/>
                            <h:outputText value="    "/>

                            <h:panelGroup id="btnExcluir">
                                <a4j:commandButton id="excluir" reRender="mdlMensagemGenerica"
                                                   rendered="#{PinPadControle.pinPadVO.codigo > 0}"
                                                   oncomplete="#{PinPadControle.msgAlert}"
                                                   action="#{PinPadControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}"
                                                   accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>
                            <a4j:commandButton id="consultar" immediate="true"
                                               action="#{PinPadControle.inicializarConsultar}"
                                               value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}"
                                               accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandLink action="#{PinPadControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec"
                                             style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
    jQuery('.tooltipster').tooltipster({
        theme: 'tooltipster-light',
        position: 'bottom',
        animation: 'grow',
        contentAsHTML: true
    });

    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }

    function copiar(copyText) {
        var el = document.createElement('textarea');
        el.value = copyText;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        Notifier.info('Link copiado para a área de transferência.');
    }

    function somenteLetras(num) {
        var er = /[^a-zA-Z]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }

    function formatJSONPix() {
        try {
            document.getElementById("json").innerHTML = JSON.stringify(JSON.parse(document.getElementById("formPix:jsonConsultaPixJSON").value), null, 3);
        } catch (ex) {
            console.log(ex);
        }
    }

</script>

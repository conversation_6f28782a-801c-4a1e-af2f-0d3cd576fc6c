<%--
  Created by IntelliJ IDEA.
  User: anderson-pacto
  Date: 18/02/19
  Time: 10:00
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>

<%@include file="./includes/imports.jsp" %>

<rich:modalPanel id="editarParcelas" styleClass="novaModal noMargin" shadowOpacity="true"
                 width="566" height="400">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Edição de Parcelas Negociação"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <a4j:form>
            <h:panelGroup>
                <a4j:commandLink
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        style="color: #fff"
                        action="#{ContratoControle.salvarEdicaoParcelaNegociacao}"
                        reRender="mensagem, form:containerParcelas, form:panelParcelaProdutoMatricula"
                        oncomplete="#{ContratoControle.msgAlert}"
                        id="hidelink7"/>
            </h:panelGroup>
        </a4j:form>
    </f:facet>
    <a4j:form ajaxSubmit="true" id="editarParcelasForm" style="height: 305px; position: relative">

        <h:panelGroup layout="block" rendered="#{ContratoControle.contratoVO.plano.parcelamentoOperadora}">
            <h:panelGroup layout="block" style="margin: 10 20 10 20; overflow: auto" id="pnlAviso">
                <h:outputText
                        styleClass="texto-font texto-size-16 texto-cor-cinza texto-bold"
                        value="Não é possível editar as parcelas pois o plano utiliza o parcelamento por operadora."/>
            </h:panelGroup>

        </h:panelGroup>

        <h:panelGroup layout="block" rendered="#{!ContratoControle.contratoVO.plano.parcelamentoOperadora}">

            <h:panelGroup layout="block" style="margin: 10 20 10 20; max-height: 230px; overflow: auto">
                <div style="text-align: right; margin: 5px 0px; width: 32%;display: inline-block">
                    <h:outputText
                            styleClass="texto-font texto-size-16 texto-cor-cinza texto-bold"
                            value="Entrada"/>
                    <h:inputText
                            style="margin-left: 5px"
                            size="5"
                            styleClass="texto-font texto-size-16 texto-cor-verde"
                            value="#{ContratoControle.valorParcelaComProdutoContrato}">
                        <a4j:support status="false" event="onblur"
                                     action="#{ContratoControle.atualizarValoresEdicaoParcelaNegociacao}"
                                     reRender="totalParcelas, totalResiduo"/>
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:inputText>
                </div>
                <a4j:repeat value="#{ContratoControle.contratoVO.listParcelasEditadas}" var="parcelas">
                    <div style="text-align: right; margin: 5px 0px; width: 32%;display: inline-block">
                        <h:outputText
                                styleClass="texto-font texto-size-16 texto-cor-cinza texto-bold"
                                value="#{parcelas.descricao}"/>
                        <h:inputText
                                style="margin-left: 5px"
                                size="5"
                                styleClass="texto-font texto-size-16 texto-cor-verde"
                                value="#{parcelas.valorParcela}">
                            <a4j:support status="false" event="onblur"
                                         action="#{ContratoControle.atualizarValoresEdicaoParcelaNegociacao}"
                                         reRender="totalParcelas, totalResiduo"/>
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>
                    </div>
                </a4j:repeat>
            </h:panelGroup>
            <h:panelGroup
                    style="display: flex; position: absolute; bottom: 26px;margin-left: 15px;right: 0px;left: 0px;">

                <h:outputText style="margin-top: auto; display: inline-block"
                              styleClass="texto-font texto-size-16 texto-cor-cinza texto-bold"
                              value="Total:"/>
                <h:outputLabel value="#{MovPagamentoControle.empresaLogado.moeda}"
                               styleClass="texto-font texto-size-16 texto-cor-azul texto-bold"
                               style="margin-left: 3px"/>
                <h:outputText id="totalParcelas"
                              style="margin-top: auto; display: inline-block; margin-left: 2px;"
                              styleClass="texto-font texto-size-16 texto-cor-azul texto-bold"
                              value="#{ContratoControle.valorTotalParcelasEdicao}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </h:panelGroup>

            <h:panelGroup id="totalResiduo"
                          style="display: flex; position: absolute; bottom: 8px;margin-left: 10px;right: 0px;left: 0px;">
                <h:outputText
                        style="margin-top: auto; margin-left: 6px; display: inline-block"
                        styleClass="texto-font texto-size-16 texto-cor-cinza texto-bold"
                        value="Resíduo:"/>


                <h:outputLabel value="#{MovPagamentoControle.empresaLogado.moeda}"
                               styleClass="#{ContratoControle.valorResiduoParcelasEdicao < 0.0 ? 'texto-font texto-size-16 texto-cor-vermelho texto-bold' : 'texto-font texto-size-16 texto-cor-verde texto-bold'}"
                               style="margin-left: 3px"/>
                <h:outputText id="vlResidudoMenorQZero"
                              style="margin-top: auto; display: inline-block; margin-left: 2px;"
                              styleClass="#{ContratoControle.valorResiduoParcelasEdicao < 0.0 ? 'texto-font texto-size-16 texto-cor-vermelho texto-bold' : 'texto-font texto-size-16 texto-cor-verde texto-bold'}"
                              value="#{ContratoControle.valorResiduoParcelasEdicao}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>

            </h:panelGroup>

            <h:panelGroup
                    style="display: flex; position: absolute; bottom: 10px;margin-left: 300px;right: 0px;left: 0px;">
                <a4j:commandLink
                        style="text-align: center;margin-top: auto; display: inline-block; padding: 8px"
                        styleClass="botaoSecundario texto-size-16"
                        action="#{ContratoControle.limparEdicaoParcelasNegociacao}"
                        reRender="form:containerParcelas, form:panelParcelaProdutoMatricula"
                        oncomplete="#{ContratoControle.msgAlert}"
                        value="Limpar Alterações"/>

                <a4j:commandLink
                        style="margin-top: auto; margin-left: 5px; display: inline-block"
                        styleClass="botaoPrimario texto-size-16"
                        action="#{ContratoControle.salvarEdicaoParcelaNegociacao}"
                        reRender="mensagem, form:containerParcelas, form:panelParcelaProdutoMatricula"
                        oncomplete="#{ContratoControle.msgAlert}"
                        value="Confirmar"/>
            </h:panelGroup>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>



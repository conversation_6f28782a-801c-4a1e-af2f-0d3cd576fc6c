<%-- 
    Document   : include_log
    Created on : 04/11/2011, 08:42:18
    Author     : carla
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<%--
<rich:modalPanel id="panelLog" autosized="true" shadowOpacity="false" width="650" height="300">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value=" #{msg_aplic.prt_Log_tituloForm}">
            </h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink6"/>
            <rich:componentControl for="panelLog" attachTo="hidelink6" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formLog" ajaxSubmit="true">
        <h:panelGrid columns="2" styleClass="tabForm" width="100%">

            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_operacao}"/>
            <h:inputTextarea id="operacao" rows="2" cols="60" styleClass="campos" readonly="true" value="#{LogControle.logVO.operacao} #{LogControle.logVO.nomeEntidade_Apresentar}" />
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_nomeCampo}"/>
            <h:inputText id="nomeCampo" size="40" styleClass="campos" readonly="true" value="#{LogControle.logVO.nomeCampo}" />
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAnterior}"/>
            <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="valorCampoAnterior" value="#{LogControle.logVO.valorCampoAnterior}"/>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAlterado}"/>
            <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="valorCampoAlterado" value="#{LogControle.logVO.valorCampoAlterado}"/>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_responsavel}"/>
            <h:outputText id="responsavel" styleClass="tituloCampos" value="#{LogControle.logVO.responsavelAlteracao}"/>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_dataHora}"/>
            <h:outputText id="dataAlteracao" styleClass="tituloCampos" value="#{LogControle.logVO.dataAlteracao_Apresentar} ás #{LogControle.logVO.horaAlteracao_Apresentar}"/>

        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
--%>

<rich:modalPanel id="panelLog" resizeable="true"  style="overflow:scroll;"  shadowOpacity="false" width="850" height="630">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value=" #{msg_aplic.prt_Log_tituloForm}">
            </h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink6"/>
            <rich:componentControl for="panelLog" attachTo="hidelink6" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formLog">

        <h:panelGrid columns="1" styleClass="tabForm" width="100%">

            <h:panelGrid columns="2" width="100%">

                <h:outputText styleClass="tituloCampos" value="Data e Hora:"/>
                <h:inputText size="30" styleClass="campos" readonly="true"
                             value="#{LogControle.logVO.dataHoraAlteracao_Apresentar}"/>

                <h:outputText styleClass="tituloCampos" value="Entidade"/>
                <h:inputText size="20" styleClass="campos" readonly="true" value="#{LogControle.logVO.nomeEntidade}"/>

                <h:outputText styleClass="tituloCampos" value="Operação"/>
                <h:inputText size="40" styleClass="campos" readonly="true" value="#{LogControle.logVO.operacao}"/>

            </h:panelGrid>

            <h:panelGroup>
                <%--BOTÃO EXCEL--%>
                <a4j:commandButton id="exportarExcel"
                                   image="imagens/btn_excel.png"
                                   style="margin-left: 8px;"
                                   actionListener="#{ExportadorListaControle.exportar}"
                                   rendered="#{not empty LogControle.listaCampos}"
                                   value="Excel"
                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                   accesskey="2" styleClass="botoes">
                    <f:attribute name="lista" value="#{LogControle.listaCampos}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos" value="nomeCampo=Campo,valorCampoAnterior=Valor Anterior,valorCampoAlterado=Valor Alterado,nomePessoa=Pessoa"/>
                    <f:attribute name="prefixo" value="ControleLog"/>
                </a4j:commandButton>
                <%--BOTÃO PDF--%>
                <a4j:commandButton id="exportarPdf"
                                   style="margin-left: 8px;"
                                   image="/imagens/imprimir.png"
                                   actionListener="#{ExportadorListaControle.exportar}"
                                   rendered="#{not empty LogControle.listaCampos}"
                                   value="PDF"
                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                   accesskey="2" styleClass="botoes">
                    <f:attribute name="lista" value="#{LogControle.listaCampos}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos" value="nomeCampo=Campo,valorCampoAnterior=Valor Anterior,valorCampoAlterado=Valor Alterado,nomePessoa=Pessoa"/>
                    <f:attribute name="prefixo" value="ControleLog"/>
                </a4j:commandButton>
            </h:panelGroup>


            <h:dataTable id="itemLog" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                         columnClasses="colunaAlinhamento"
                         value="#{LogControle.listaCampos}"
                         rows="10" var="log">

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Campo"/>
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{log.nomeCampo}"/>
                </h:column>

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Valor Anterior"/>
                    </f:facet>
                    <pre><h:outputText styleClass="tituloCampos" value="#{log.valorCampoAnteriorSemQuebra}"/></pre>
                </h:column>

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Valor Atual"/>
                    </f:facet>
                    <pre><h:outputText styleClass="tituloCampos" value="#{log.valorCampoAlteradoSemQuebra}"/></pre>
                </h:column>

                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Pessoa"/>
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{log.nomePessoa}"/>
                </h:column>


            </h:dataTable>
            <rich:datascroller align="center" for="formLog:itemLog" id="scResultadoLogCampos"/>

        </h:panelGrid>


    </h:form>
</rich:modalPanel>


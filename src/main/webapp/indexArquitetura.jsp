<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box form-flat">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Acesso ao Sistema" styleClass="container-header-titulo"/>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" columnClasses="w100" width="100%" cellpadding="0" cellspacing="0">
                                        <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%" cellpadding="0" cellspacing="0">
                                            <h:panelGrid columns="1" style="height:1000%" width="100%" cellpadding="5" cellspacing="5" >
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="empresaDescritivo"
                                                                         styleClass="tituloCampos tituloCaixaAlta tituloCaixaAlta"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.empresa}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Empresa"
                                                                         onclick="abrirPopup('empresaCons.jsp', 'Empresa', 800, 595);">
                                                            <f:attribute name="funcionalidade" value="EMPRESA"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos tituloCaixaAlta tituloCaixaAlta" id="empresaDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.empresa}"  value="Empresa"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="O Zillyon Web trabalha com o conceito de multi-empresa, onde através de apenas um sistema será possível controlar várias filiais de uma mesma franquia. Cadastre nesta tela as empresas que utilizarão o sistema e acompanhe de perto o rendimento de cada uma delas."/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="usuarioDescritivo"
                                                                         styleClass="tituloCampos tituloCaixaAlta"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.usuario}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         onclick="abrirPopup('usuarioCons.jsp', 'Usuario', 820, 600);"
                                                                         value="Usuário">
                                                            <f:attribute name="funcionalidade" value="USUARIO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos tituloCaixaAlta" id="usuarioDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.usuario}"  value="Usuário"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Para acessar o sistema é necessário que o usuário seja devidamente cadastrado e possua Login e Senha de acesso. Este cadastro será feito por esta opção. Lembre-se que senhas são únicas e não devem ser compartilhadas."/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <h:panelGrid style="height:100%" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="perfilAcessoDescritivo"
                                                                         styleClass="tituloCampos tituloCaixaAlta"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.perfilAcesso}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         onclick="abrirPopup('perfilAcessoCons.jsp', 'PerfilAcesso', 800, 595);"
                                                                         value="Perfil de Acesso">
                                                            <f:attribute name="funcionalidade" value="PERFIL_ACESSO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos tituloCaixaAlta" id="perfilAcessoDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.perfilAcesso}"  value="Perfil de Acesso"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="O controle do que os funcionários podem acessar ou não dentro de um sistema é muito importante e através desta tela de cadastro você poderá definir perfis de acesso, limitando e permitindo que pessoas tenham acessos diferentes ao sistema.
                                                          "/>

                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <%--<h:outputText styleClass="text fa-icon-quote-left" style="font-family: FontAwesome;display: table-cell"/>--%>
                                                    <h:outputText styleClass="text quote-text"  value=" Poderíamos criar um perfil Administrador, que teria acesso a todo o sistema, enquanto os usuários do perfil Consultores teriam acesso a apenas fazer vendas. ">
                                                    <%--<h:outputText styleClass="text " style="font-family: FontAwesome;display: table-cell"/>--%>
                                                    </h:outputText>
                                                </h:panelGroup>

                                            </h:panelGrid>

                                            <h:panelGrid style="height:100%" columns="1" width="100%" cellpadding="5" cellspacing="5"  >
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <h:panelGroup  rendered="#{LoginControle.permissaoAcessoMenuVO.geradorConsultas}" >
                                                            <a class="tituloCampos tituloCaixaAlta"  id="geradorConsultasDescritivo"   href="telaGeradorConsultas.jsp">Gerador de Consultas</a>
                                                        </h:panelGroup>
                                                        <h:outputText styleClass="tituloCampos tituloCaixaAlta" id="geradorConsultasDescritivoText" rendered="#{!LoginControle.permissaoAcessoMenuVO.geradorConsultas}"  value="Gerador de Consultas"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Essa ferramenta permite que você faça consultas especificas em tópicos
                                                          pré cadastrados em formato de SQL. Alguns exemplos são:"/>

                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text" value="ACESSO -> Todos Acessos "/>
                                                    <br/>
                                                    <h:outputText styleClass="text" value="CLIENTES -> Alunos com o mesmo CPF?"/>
                                                </h:panelGroup>

                                            </h:panelGrid>

                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGroup>

                            </h:panelGroup>


                        </h:panelGroup>

                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-CADASTROS_ACESSO_SISTEMA" />
                        </jsp:include>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true" />

        </h:panelGroup>

        <!------------------------------------------------------------------------------------------------------------------------------------------        -->


    </h:form>
</f:view>

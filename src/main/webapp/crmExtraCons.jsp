<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>
<c:set var="titulo" scope="session" value="${msg_aplic.prt_Objecao_tituloForm}"/>
<c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-objecoes-no-crm/"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>${msg_aplic.prt_CRMExtra_tituloForm}</title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_CRMExtra_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-gerar-uma-meta-de-ligacao-para-o-consultor-diferente-das-que-existem-no-crm-meta-extra/"/>
    <%-- INICIO HEADER --%>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_crm.jsp"/>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto margin-v-10">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                        <%--<a4j:commandLink id="btnExcel"--%>
                                         <%--styleClass="pure-button pure-button-small"--%>
                                         <%--actionListener="#{MalaDiretaControle.exportarCRMExtra}"--%>
                                         <%--oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Meta Extra', 800,200);#{ExportadorListaControle.msgAlert}"--%>
                                         <%--accesskey="3">--%>
                            <%--<f:attribute name="tipo" value="xls"/>--%>
                            <%--<f:attribute name="atributos"--%>
                                         <%--value="codigo=Código,nome=Nome,colaboradorResp=Responsável Cadastro,dia_ApresentarRel=Dia,empresa=Empresa"/>--%>
                            <%--<f:attribute name="prefixo" value="Passivo"/>--%>
                            <%--<a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>--%>
                            <%--<i class="fa-icon-excel"></i> &nbsp Excel--%>
                        <%--</a4j:commandLink>--%>

                        <%--<a4j:commandLink id="btnPDF"--%>
                                         <%--styleClass="pure-button pure-button-small margin-h-10"--%>
                                         <%--actionListener="#{MalaDiretaControle.exportarCRMExtra}"--%>
                                         <%--oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Meta Extra', 800,200);#{ExportadorListaControle.msgAlert}"--%>
                                         <%--accesskey="4">--%>
                            <%--<f:attribute name="tipo" value="pdf"/>--%>
                            <%--<f:attribute name="atributos"--%>
                                         <%--value="codigo=Código,nome=Nome,colaboradorResp=Responsável Cadastro,dia_ApresentarRel=Dia,empresa=Empresa"/>--%>
                            <%--<f:attribute name="prefixo" value="Passivo"/>--%>
                            <%--<a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>--%>
                            <%--<i class="fa-icon-pdf"></i> &nbsp PDF--%>
                        <%--</a4j:commandLink>--%>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <a4j:commandLink id="btnNovo"
                                         styleClass="pure-button pure-button-primary pure-button-small"
                                         action="#{MalaDiretaControle.novoCRMExtra}"
                                         accesskey="1">
                            <i class="fa-icon-plus"></i> &nbsp ${msg_bt.btn_novo}
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblCRMExtra" class="tabelaCRMExtra pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${"Código"}</th>
                    <th>${"Título"}</th>
                    <th>${"Situação"}</th>
                    <th>${"Usuário Criou"}</th>
                    <th>${"Dt Criação"}</th>
                    <th>${"Dt Inicio Meta"}</th>
                    <th>${"Dt Final Meta"}</th>
                    <th>${"Qtd Alunos"}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{MalaDiretaControle.editarCRMExtra}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{MalaDiretaControle.sucesso}" value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{MalaDiretaControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty MalaDiretaControle.mensagem}"
                              value=" #{MalaDiretaControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty MalaDiretaControle.mensagemDetalhada}"
                              value=" #{MalaDiretaControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script>
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaCRMExtra", "${contexto}/prest/crm/crmExtra", 1, "asc", "", true);
        });
    </script>
</f:view>

<%-- 
    Document   : listasRelatoriosVisitantes
    Created on : 21/06/2013, 11:40:46
    Author     : <PERSON><PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<jsp:include page="include_head.jsp" flush="true"/>

<style type="text/css">
    .rich-stglpanel-header {
        color: #0f4c6b;
    }

</style>
<style type="text/css">
    .rich-stglpanel-header {
        background-image: #acbece;
    }

    .rich-stglpanel-header {
        background-color: #ACBECE;
        border-color: #ACBECE;
        font-size: 12px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Relatórios"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">

        <c:if test="${empty contexto}">

        </c:if>
        <c:if test="${not empty contexto}">
            <%@include file="pages/ce/includes/include_head.jsp" %>
        </c:if>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".itemRelatorios" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Relatório de Visitantes" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlWiki}Relatorios:Relatorios_Visitantes"
                                                      title="Clique e saiba mais: Relatório de Visitantes"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup id="panelGeralClientes" layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="text" value="Filtros"
                                                      style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>

                                    <rich:simpleTogglePanel switchType="client" label="Empresas" opened="false" onexpand="true" id="VisitantesEmpresas">
                                        <h:panelGroup id="emp">
                                            <h:panelGroup rendered="#{LRVisitantesControle.permissaoConsultaTodasEmpresas}"
                                                          style="padding-left: 10px;padding-top: 15px;padding-bottom: 15px;border: 1px solid #ACBECE;"
                                                          layout="block">
                                                <h:selectOneMenu value="#{LRVisitantesControle.filtroEmpresa}">
                                                    <f:selectItems  value="#{LRVisitantesControle.listaEmpresas}" />
                                                    <a4j:support event="onchange"
                                                                 action="#{LRVisitantesControle.recarregarTela}"
                                                                 reRender="consultores, professores"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <rich:simpleTogglePanel switchType="client" label="Dados dos visitantes"
                                                            style="margin-top: 8px" opened="true"
                                                            onexpand="false">
                                        <h:panelGrid columns="4">

                                            <h:panelGrid columns="8">
                                                <h:panelGroup id="dataBv">
                                                    <%--intervalo de datas que os clientes responderam os questionários para a consulta--%>
                                                    <h:outputText styleClass="text"
                                                                  value="Período de BV: "/>
                                                    <rich:calendar id="inicioBv"
                                                                   value="#{LRVisitantesControle.inicioBv}"
                                                                   inputSize="6"
                                                                   inputClass="form"
                                                                   oninputblur="blurinput(this);"
                                                                   oninputfocus="focusinput(this);"
                                                                   oninputchange="return validar_Data(this.id);"
                                                                   datePattern="dd/MM/yyyy"
                                                                   enableManualInput="true"
                                                                   zindex="2"
                                                                   showWeeksBar="false"/>

                                                    <h:outputText styleClass="text" value=" até "/>
                                                    <rich:calendar id="fimBv"
                                                                   value="#{LRVisitantesControle.fimBv}"
                                                                   inputSize="6"
                                                                   inputClass="form"
                                                                   oninputblur="blurinput(this);"
                                                                   oninputfocus="focusinput(this);"
                                                                   oninputchange="return validar_Data(this.id);"
                                                                   datePattern="dd/MM/yyyy"
                                                                   enableManualInput="true"
                                                                   zindex="2"
                                                                   showWeeksBar="false"/>
                                                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                    <a4j:commandButton id="limparCadastro"
                                                                       style="margin-left: 5px"
                                                                       action="#{LRVisitantesControle.limparBv}"
                                                                       image="/images/limpar.gif"
                                                                       title="Limpar período de bv."
                                                                       reRender="inicioBv, fimBv"/>

                                                    <h:panelGroup id="bvCompIncomp"
                                                                  style="margin-left: 5px">
                                                        <rich:spacer width="10px"/>
                                                        <h:outputText styleClass="text"
                                                                      value="Situação de BVs: "/>
                                                        <h:selectOneMenu id="bv"
                                                                         value="#{LRVisitantesControle.bvCompIncomp}">
                                                            <f:selectItem itemValue=""
                                                                          itemLabel="#{CElabels['operacoes.selecione']}"/>
                                                            <f:selectItems
                                                                    value="#{LRVisitantesControle.listaBvCompIncomp}"/>
                                                        </h:selectOneMenu>
                                                    </h:panelGroup>

                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </h:panelGrid>
                                    </rich:simpleTogglePanel>
                                    <%-- ------------------------------- ABA CONSULTORES   ------------------------------------ --%>

                                    <rich:simpleTogglePanel switchType="client" label="Consultores"
                                                            style="margin-bottom: 10px;" opened="false" onexpand="true">
                                        <h:panelGroup id="consultores">

                                            <rich:dataGrid value="#{LRVisitantesControle.consultores}"
                                                           var="consultor" width="100%" columns="4">

                                                <h:selectBooleanCheckbox
                                                        value="#{consultor.colaboradorEscolhido}"/>
                                                <h:outputText value="#{consultor.pessoa.nome}"/>

                                            </rich:dataGrid>
                                        </h:panelGroup>
                                    </rich:simpleTogglePanel>
                                    <h:panelGroup layout="block" styleClass="container-botoes">
                                        <a4j:commandLink styleClass="botoes nvoBt"
                                                         style="margin-left: 0px"
                                                         action="#{LRVisitantesControle.consultarRelatorioVi}"
                                                         reRender="groupResultados, imprimir">
                                            Consultar&nbsp<i class="fa-icon-search"></i>
                                        </a4j:commandLink>

                                        <a4j:commandLink style="margin-left:5px;margin-top: 8px; padding:5px 7px;"
                                                         action="#{LRVisitantesControle.limparFiltros}"
                                                         styleClass="botoes nvoBt btSec"
                                                         reRender="form">
                                            Limpar filtros&nbsp <i class="fa-icon-eraser"></i>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                    <h:panelGroup id="imprimir">

                                        <a4j:commandLink id="exportarExcelRelVisitantes"
                                                         style="margin-left:5px;margin-top: 8px;"
                                                         actionListener="#{ExportadorListaControle.exportar}"
                                                         rendered="#{not empty LRVisitantesControle.resultado}"
                                                         oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                         accesskey="2">
                                            <f:attribute name="lista"
                                                         value="#{LRVisitantesControle.resultado}"/>
                                            <f:attribute name="tipo" value="xls"/>
                                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,telefone=Telefones,email=E-mail,dataNascimentoApresentar=Nascimento,dataCadastroApresentar=Cadastro
                                                                     ,situacaoClienteApresentar=Situação,sexo=Sexo Biológico,logradouro=Logradouro,numero=Número,bairro=Bairro,cidade=Cidade,cep=CEP
                                                                     ,complemento=Complemento,estadoCivil=Estado Civil,profissao=Profissão,nomeEmpresa=Empresa"/>
                                            <f:attribute name="prefixo" value="ClientesVisitantes"/>
                                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                        </a4j:commandLink>
                                        <a4j:commandLink style="margin-left:5px;margin-top: 8px;"
                                                         value="Imprimir"
                                                         rendered="#{not empty LRVisitantesControle.resultado}"
                                                         reRender="relatorioImprimir"
                                                         action="#{LRVisitantesControle.prepararImpr}"
                                                         styleClass="botoes nvoBt btSec"
                                                         oncomplete="Richfaces.showModalPanel('relatorioImprimir');">
                                            <i class="fa-icon-print"></i>
                                        </a4j:commandLink>

                                    </h:panelGroup>
                                    <%---------------------------------TABELA DO RELATORIO---------------------------------------%>
                                    <h:panelGroup id="groupResultados">
                                        <rich:dataTable width="100%"
                                                        value="#{LRVisitantesControle.resultado}"
                                                        id="resultados"
                                                        var="item"
                                                        rows="30"
                                                        style="margin-top: 8px"
                                                        rendered="#{not empty LRVisitantesControle.resultado}">

                                            <rich:column id="nome" sortBy="#{item.nome}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nome"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nome}"
                                                        actionListener="#{LRVisitantesControle.prepareEditar}"
                                                        action="#{LRVisitantesControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="telefone" sortBy="#{item.telefone}">
                                                <f:facet name="header">
                                                    <h:outputText value="Telefone"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.telefone}"
                                                        actionListener="#{LRVisitantesControle.prepareEditar}"
                                                        action="#{LRVisitantesControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="email" sortBy="#{item.email}">
                                                <f:facet name="header">
                                                    <h:outputText value="Email"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.email}"
                                                        actionListener="#{LRVisitantesControle.prepareEditar}"
                                                        action="#{LRVisitantesControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataCadastro" sortBy="#{item.dataCadastro}">
                                                <f:facet name="header">
                                                    <h:outputText value="Cadastro"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataCadastroApresentar}"
                                                        actionListener="#{LRVisitantesControle.prepareEditar}"
                                                        action="#{LRVisitantesControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="dataNascimento"
                                                         sortBy="#{item.dataNascimento}">
                                                <f:facet name="header">
                                                    <h:outputText value="Nascimento"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.dataNascimentoApresentar}"
                                                        actionListener="#{LRVisitantesControle.prepareEditar}"
                                                        action="#{LRVisitantesControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>

                                            <rich:column id="empresa" sortBy="#{item.nomeEmpresa}">
                                                <f:facet name="header">
                                                    <h:outputText value="Empresa"/>
                                                </f:facet>
                                                <a4j:commandLink
                                                        value="#{item.nomeEmpresa}"
                                                        actionListener="#{LRVisitantesControle.prepareEditar}"
                                                        action="#{LRVisitantesControle.irParaTelaCliente}"
                                                        oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);"/>
                                            </rich:column>
                                        </rich:dataTable>
                                        <table align="right" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td align="center" valign="middle"><h5>
                                                    <h:outputText
                                                            rendered="#{not empty LRVisitantesControle.resultado}"
                                                            value=" [Itens:#{LRVisitantesControle.totalItens}]"> </h:outputText>
                                                </h5></td>
                                            </tr>
                                        </table>
                                        <rich:datascroller for="resultados"
                                                           rendered="#{not empty LRVisitantesControle.resultado}"
                                                           id="scrollResultados"></rich:datascroller>
                                    </h:panelGroup>
                                </h:panelGroup>

                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="menuRelatorio.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>


    </h:form>

    <%------------------------- MODAL 'IMPRIMIR' ----------------------------------------%>
    <rich:modalPanel id="relatorioImprimir" autosized="true" shadowOpacity="true" width="350">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Dados da impressão"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="imprimir"/>
                <rich:componentControl for="relatorioImprimir" attachTo="imprimir" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form >

            <h:outputText styleClass="text" style="font-weight: bold"
                          value="Por Favor, selecione as informações que deseja imprimir:"/><br/><br/>
            <h:selectBooleanCheckbox value="#{LRVisitantesControle.apresentarLinha1}"/>
            <h:outputText styleClass="text"  value="Dados Cadastrais"/><br/>
            <h:selectBooleanCheckbox value="#{LRVisitantesControle.apresentarLinha2}"/>
            <h:outputText styleClass="text"  value="Endereço"/><br/><br/>
            <center>
                <a4j:commandButton id="imprimirPDF" ajaxSingle="false"
                                   action="#{LRVisitantesControle.imprimirListaRelatorio}"
                                   value="Imprimir"
                                   reRender="mensagem"
                                   image="../imagens/imprimirContrato.png"
                                   oncomplete="#{LRVisitantesControle.msgAlert}"
                                   accesskey="2" styleClass="botoes"/>
            </center>
        </a4j:form>
    </rich:modalPanel>

    <%------------------------- MODAL 'CONSULTAR' ----------------------------------------%>
    <rich:modalPanel id="lrVisitantes" autosized="true" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Relátorio de Visitantes"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="relatorioVisitantes"/>
                <rich:componentControl for="lrVisitantes" attachTo="relatorioVisitantes" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <jsp:include page="topoReduzido.jsp"/>
        <br/>
        <a4j:form>
            <h:panelGrid columns="2" columnClasses="colunaDireita, colunaEsquerda">

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRVisitantesControle.itemRelatorio.nome}" value="Nome: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRVisitantesControle.itemRelatorio.nome}"
                              value="#{LRVisitantesControle.itemRelatorio.nome}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRVisitantesControle.itemRelatorio.sexo}" value="Sexo biológico: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRVisitantesControle.itemRelatorio.sexo}"
                              value="#{LRVisitantesControle.itemRelatorio.sexo}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRVisitantesControle.itemRelatorio.logradouro}" value="Endereço: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRVisitantesControle.itemRelatorio.logradouro}"
                              value="#{LRVisitantesControle.itemRelatorio.logradouro}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRVisitantesControle.itemRelatorio.bairro}" value="Bairro: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRVisitantesControle.itemRelatorio.bairro}"
                              value="#{LRVisitantesControle.itemRelatorio.bairro}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRVisitantesControle.itemRelatorio.complemento}"
                              value="Complemento: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRVisitantesControle.itemRelatorio.complemento}"
                              value="#{LRVisitantesControle.itemRelatorio.complemento}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRVisitantesControle.itemRelatorio.numero}" value="Número: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRVisitantesControle.itemRelatorio.numero}"
                              value="#{LRVisitantesControle.itemRelatorio.numero}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRVisitantesControle.itemRelatorio.cep}" value="Cep: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRVisitantesControle.itemRelatorio.cep}"
                              value="#{LRVisitantesControle.itemRelatorio.cep}"/>

                <h:outputText styleClass="text" style="font-weight: bold"
                              rendered="#{not empty LRVisitantesControle.itemRelatorio.cidade}" value="Cidade: "/>
                <h:outputText styleClass="text" rendered="#{not empty LRVisitantesControle.itemRelatorio.cidade}"
                              value="#{LRVisitantesControle.itemRelatorio.cidade}"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="tituloPainelAluno googleAnalytics">
    <h:outputText value="Dados do cliente" styleClass="texto-size-14 negrito cinzaEscuro pl20"/>
    <h:outputLink styleClass="pl5" rendered="#{ClienteControle.menuContrato}"
                  value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                  title="Clique e saiba mais: Dados do Cliente"
                  target="_blank">
        <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
    </h:outputLink>

    <c:if test="${TelaClienteControle.cliente.dataInclusaoSPC != null}">
        <h:outputText styleClass="tooltipster texto-size-14-real texto-cor-branco texto-font situacaoBallon"
                      style="background: #333333; margin-left: 8px"
                      title="#{TelaClienteControle.hintSPC}"
                      value="SPC"/>
    </c:if>

    <div style="float: right; margin: 10px;">

        <h:dataTable id="clienteSituacao"
                     value="#{TelaClienteControle.cliente.clienteSituacaoVOs}"
                     var="clienteSituacao">
            <h:column>
                <a4j:commandLink style="color: black; font-weight: bold; font-size: 14px" action="#{TelaClienteControle.irParaTitular}">
                    <h:panelGroup layout="block" id="sitDependente"
                                  style="margin-right: 12px; width: 30px; height: 30px; display: flex; float: left; background-color: #bbb; border-radius: 50%;justify-content: center;align-items: center;"
                                  rendered="#{clienteSituacao.dependentePlanoCompartilhado}">
                        <h:outputText styleClass="tooltipster" style="font-weight: bold" value="DP"
                                      title="Este aluno compartilha o plano com <b>#{TelaClienteControle.clienteTitular.nome_Apresentar}</b>"/>
                    </h:panelGroup>
                </a4j:commandLink>

                <h:graphicImage id="sitGymPass" value="./imagens/gy.png"
                                rendered="#{clienteSituacao.gymPass}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}entendendo-o-status-que-e-apresentado-no-perfil-do-cliente-e-na-situacao-do-contrato/"
                              rendered="#{clienteSituacao.gymPass}"
                              title="Clique e saiba mais: Visitantes" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitTotalPass" value="./imagens/totalpass.png"
                                rendered="#{clienteSituacao.totalPass}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}entendendo-o-status-que-e-apresentado-no-perfil-do-cliente-e-na-situacao-do-contrato/"
                              rendered="#{clienteSituacao.totalPass}"
                              title="Clique e saiba mais: Visitantes" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitFreePass" value="./imagens/botaoFreePass.png"
                                rendered="#{clienteSituacao.visitanteFreePass}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.visitanteFreePass}"
                              title="Clique e saiba mais: Visitante FreePass" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitAtivo" value="./imagens/botaoAtivo.png"
                                rendered="#{clienteSituacao.ativo}" width="30" height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.ativo}" title="Clique e saiba mais: Ativos"
                              target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>
                <h:graphicImage id="sitInativo" value="./imagens/botaoInativo.png"
                                rendered="#{clienteSituacao.inativo}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.inativo}"
                              title="Clique e saiba mais: Inativos" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>
                <h:graphicImage id="sitVisitante" value="./imagens/botaoVisitante.png"
                                rendered="#{clienteSituacao.visitante}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.visitante}"
                              title="Clique e saiba mais: Visitantes" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitTrancado" value="./imagens/botaoTrancamento.png"
                                rendered="#{clienteSituacao.trancado}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.trancado}"
                              title="Clique e saiba mais: Trancados" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <rich:spacer width="5px"/>
                <h:graphicImage id="sitAtNormal" value="./imagens/botaoNormal.png"
                                rendered="#{clienteSituacao.ativoNormal}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.ativoNormal}"
                              title="Clique e saiba mais: Ativos Normais" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>
                <h:graphicImage id="sitTraVencido" value="./imagens/botaoTrancadoVencido.png"
                                rendered="#{clienteSituacao.trancadoVencido}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.trancadoVencido}"
                              title="Clique e saiba mais: Trancados Vencidos" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitAulaAvulsa" value="./imagens/botaoAulaAvulsa.png"
                                rendered="#{clienteSituacao.visitanteAulaAvulsa}"
                                width="30" height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.visitanteAulaAvulsa}"
                              title="Clique e saiba mais: Visitante com Aula Avulsa" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitDiaria" value="./imagens/botaoDiaria.png"
                                rendered="#{clienteSituacao.visitanteDiaria}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.visitanteDiaria}"
                              title="Clique e saiba mais: Visitante com Diária" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <rich:spacer width="5px"/>
                <h:graphicImage id="sitCancelamento" value="./imagens/botaoCancelamento.png"
                                rendered="#{clienteSituacao.inativoCancelamento}"
                                width="30" height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.inativoCancelamento}"
                              title="Clique e saiba mais: Inativos Cancelados" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitDesistente" value="./imagens/botaoDesistente.png"
                                rendered="#{clienteSituacao.inativoDesistente}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.inativoDesistente}"
                              title="Clique e saiba mais: Inativos Desistentes" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitAVencer" value="./imagens/botaoAvencer.png"
                                rendered="#{clienteSituacao.ativoAvencer}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.ativoAvencer}"
                              title="Clique e saiba mais: Ativos a Vencer" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitVencido" value="./imagens/botaoVencido.png"
                                rendered="#{clienteSituacao.inativoVencido}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.inativoVencido}"
                              title="Clique e saiba mais: Inativos Vencidos" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitCarencia" value="./imagens/botaoCarencia.png"
                                rendered="#{clienteSituacao.ativoCarencia}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.ativoCarencia}"
                              title="Clique e saiba mais: Inativos" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>

                <h:graphicImage id="sitAtestado" value="./imagens/botaoAtestado.png"
                                rendered="#{clienteSituacao.ativoAtestado}" width="30"
                                height="29"/>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              rendered="#{clienteSituacao.ativoAtestado}"
                              title="Clique e saiba mais: Ativos com Atestado Médico" target="_blank">
                    <i class="fa-icon-question-sign cinzaClaro" style="font-size: 18px"></i>
                </h:outputLink>
            </h:column>
        </h:dataTable>
    </div>
    <div style="float: right; line-height: 50px;" class="googleAnalytics">
        <h:outputText title="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.dia_Apresentar}" value="Situação"
                      styleClass="texto-size-14 cinza"/>
    </div>

</div>
<div class="conteudoDadosCliente googleAnalytics" style="width: calc(100% - 2vw);padding: 1vw;"
     id="conteudoDadosCliente">
    <div class="insideCubo bordaInferiorCinza" style="padding: 10px 0;">
        <div class="colunaDadosCliente">
            <div style="width: 10%; display: table-cell;">
                <i class="fa-icon-phone-square cinza"></i>
            </div>
            <div class="contatos" style="width: 90%; display: table-cell;">
                <a4j:repeat value="#{TelaClienteControle.cliente.pessoa.telefoneVOs}" var="telefone" rows="2">
                    <div>
                        <h:outputText styleClass="texto-size-14 cinza" value="#{telefone.numeroApresentar}"/>
                        <h:outputText styleClass="texto-size-12" style="display: block; color: #9E9E9E;"
                                      value="#{telefone.tipoTelefone_ApresentarEspecial}"/>
                        <h:outputText styleClass="texto-size-12" style="display: block; color: #9E9E9E;"
                                      value="#{telefone.descricaoEspecial}"
                                      title="#{telefone.tipoTelefone_Apresentar}: #{telefone.descricao}"/>
                    </div>
                </a4j:repeat>
            </div>
        </div>

        <%---------------------------------------- BOTCONVERSA ----------------------------------------------------------%>
        <h:panelGrid id="panelBotconversa" width="100%" rendered="#{TelaClienteControle.exibBotConversa}">
            <h:panelGroup layout="block" style="padding-top: 10px; margin-bottom: 10px" >
                <a4j:commandLink id="Botconversa"
                                 action="#{TelaClienteControle.enviarFluxo}"
                                 rendered="#{TelaClienteControle.exibBotConversa}"
                                 oncomplete="#{TelaClienteControle.msgAlert}"
                                 styleClass="pure-button-botconversa  pure-button-small"
                                 value="Enviar Gymbot">
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>

        <%---------------------------------------- GYMBOT PRO ----------------------------------------------------------%>
        <h:panelGrid id="panelgGymbotPro" width="100%" rendered="#{TelaClienteControle.exibeGymbotPro}">
            <h:panelGroup layout="block" style="padding-top: 10px; margin-bottom: 10px" >
                <a4j:commandLink id="GymbotPro"
                                 action="#{TelaClienteControle.enviarFluxoGymbotPro}"
                                 rendered="#{TelaClienteControle.exibeGymbotPro}"
                                 oncomplete="#{TelaClienteControle.msgAlert}"
                                 styleClass="pure-button-gymbotpro  pure-button-small"
                                 value="Enviar Gymbot Pro">
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>

        <div class="colunaDadosCliente">
            <div style="width: 10%; display: table-cell;">
                <i class="fa-icon-envelope-square cinza"></i>
            </div>
            <div class="contatos" style="width: 90%; display: table-cell;">
                <a4j:repeat value="#{TelaClienteControle.cliente.pessoa.emailVOs}" var="email" rows="2">
                    <div>
                        <h:outputText styleClass="texto-size-14 cinza" value="#{email.email}"/>
                        <h:outputText styleClass="texto-size-12" style="display: block; color: #9E9E9E;"
                                      value="E-mail para correspondência"
                                      rendered="#{email.emailCorrespondencia}"/>
                    </div>
                </a4j:repeat>
            </div>
        </div>
        <div style="margin-top: 10px;">
            <h:outputText value="Empresa: " styleClass="texto-size-14 negrito cinza"/>
            <h:outputText value="#{TelaClienteControle.cliente.empresa.nome}" styleClass="texto-size-14 cinza"/>
            <a4j:commandLink
                    rendered="#{TelaClienteControle.permiteTransferirContrato and (TelaClienteControle.cliente.situacao == 'AT' or TelaClienteControle.cliente.situacao == 'IN')}"
                    title="Transferência de empresa"
                    styleClass="linkAzul floatright"
                    style="margin-right: 1vw;"
                    onclick="scrollLock()"
                    oncomplete="Richfaces.showModalPanel('transferenciaClienteEmpresa');"
                    action="#{TelaClienteControle.prepararClienteTransferenciaEmpresa}"
                    reRender="formExisteCliente">
                <i class="fa-icon-exchange" style="font-size: 0.8vw;"></i>
                <h:outputText value="Transferência de empresa"/>
            </a4j:commandLink>
        </div>

        <h:panelGroup layout="block"
                      rendered="#{TelaClienteControle.cliente.empresa.usarParceiroFidelidade}"
                      style="margin-top: 15px;">
            <h:outputText value="Utiliza Dotz: " styleClass="texto-size-14 negrito cinza"/>
            <h:outputText value="Não"
                          rendered="#{!TelaClienteControle.cliente.pessoa.utilizaDotz}"
                          styleClass="texto-size-14 negrito texto-cor-vermelho"/>
            <h:outputText value="Sim"
                          rendered="#{TelaClienteControle.cliente.pessoa.utilizaDotz}"
                          styleClass="texto-size-14 negrito texto-cor-verde"/>
        </h:panelGroup>

        <h:panelGroup layout="block"
                      id="panelParceiroFidelidade"
                      rendered="#{TelaClienteControle.cliente.empresa.usarParceiroFidelidade}"
                      style="margin-top: 15px;">
            <h:outputText value="Saldo Dotz: " styleClass="texto-size-14 negrito cinza"/>
            <h:outputText value="#{TelaClienteControle.cliente.saldoParceiroFidelidade}"
                          rendered="#{not empty TelaClienteControle.cliente.saldoParceiroFidelidade}"
                          style="color: #ff8305"
                          styleClass="texto-size-14 negrito"/>
            <a4j:commandLink action="#{TelaClienteControle.consultarParceiroFidelidade}" id="verSaldoDotz"
                             rendered="#{empty TelaClienteControle.cliente.saldoParceiroFidelidade}"
                             value="Ver saldo"
                             title="Clique para consultar o saldo"
                             reRender="form:panelParceiroFidelidade"
                             styleClass="texto-size-14 negrito">
                <i id="verSaldoDotzIcon" class="fa-icon-refresh"></i>
            </a4j:commandLink>
        </h:panelGroup>

        <a4j:repeat value="#{TelaClienteControle.autosCobCliente}" var="autosCobCliente">
            <h:panelGroup
                    rendered="#{autosCobCliente.vencimentoFatura != null and autosCobCliente.vencimentoFatura != 0}"
                    style="display: flex; margin-top: 15px;"
                    layout="block">
                <h:outputText value="Cart. de crédito: " styleClass="texto-size-14 negrito cinza tooltipster"
                              title="Titular do cartão de crédito"/>
                <h:outputText value="#{autosCobCliente.nomeTitularCartao}"
                              styleClass="texto-size-14 cinza upper tooltipster" title="Titular do cartão de crédito"
                              style="margin-left: 3px;"/>
            </h:panelGroup>
            <h:panelGroup
                    rendered="#{autosCobCliente.vencimentoFatura != null and autosCobCliente.vencimentoFatura != 0}"
                    style="display: flex; margin-top: 10px;">
                <h:outputText value="Vencimento da fatura: " styleClass="texto-size-14 negrito cinza"/>
                <h:outputText value="#{autosCobCliente.vencimentoFatura}" styleClass="texto-size-14 cinza"
                              style="margin-left: 3px;"/>
            </h:panelGroup>
        </a4j:repeat>

        <h:panelGroup rendered="#{TelaClienteControle.autosCobClienteTotal > 0}"
                      style="margin-top: 15px;"
                      layout="block">
            <h:outputText
                    value="#{TelaClienteControle.autosCobClienteTotal > 1 ? 'Convênios de cobrança: ' : 'Convênio de cobrança: '}"
                    styleClass="texto-size-14 negrito cinza"/>

            <a4j:commandLink id="editCobrancaAluno"
                             title="Clique para editar"
                             styleClass="texto-size-14 cinza upper tooltipster"
                             actionListener="#{ClienteControle.abrirAba}"
                             oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 1100, 595); return false;">
                <h:outputText id="editCobrancaTextAluno"
                              value="#{TelaClienteControle.autosCobClienteDescricao}"
                              styleClass="texto-size-14 cinza upper"
                              style="margin-left: 3px;"/>
                <f:attribute name="aba" value="abaCobranca"/>
            </a4j:commandLink>
        </h:panelGroup>

        <a4j:repeat value="#{TelaClienteControle.listaContratos}" var="contratoRepeat">
            <h:panelGroup rendered="#{contratoRepeat.situacao eq 'AT'}"
                          layout="block"
                          style="margin-top: 15px;">

                <h:panelGroup layout="block" style="display: flex;">
                    <h:outputText value="Plano: " styleClass="texto-size-14 negrito cinza"/>
                    <h:outputText value="#{contratoRepeat.plano.descricao}" styleClass="texto-size-14 cinza"
                                  style="margin-left: 3px;min-width: 200px;"/>
                    <div style="justify-content: flex-end; width: 100%; justify-content: flex-end;">
                        <a4j:commandLink rendered="#{TelaClienteControle.permiteTransferirPlano && contratoRepeat.regimeRecorrencia}"
                                         title="Alterar o plano do cliente"
                                         styleClass="linkAzul floatright"
                                         style="margin-right: 1vw;"
                                         action="#{TelaClienteControle.notificarRecursoEmpresaMudanca}"
                                         oncomplete="Richfaces.showModalPanel('mudancaPlano');"
                                         reRender="formMudancaPlano"
                                         actionListener="#{TelaClienteControle.selecionarContratoParaTransferenciaDePlano}">
                            <f:attribute name="codigoContrato" value="#{contratoRepeat.codigo}"/>
                            <i class="fa-icon-exchange" style="font-size: 0.8vw;"></i>
                            <h:outputText value="Mudança de plano"/>
                        </a4j:commandLink>
                    </div>
                </h:panelGroup>

                <h:panelGroup rendered="#{contratoRepeat.regimeRecorrencia}"
                              id="diaVencimentoContratoRecorrencia"
                              style="display: flex; margin-top: 15px;"
                              layout="block">
                    <h:outputText value="Dia do vencimento: " styleClass="texto-size-14 negrito cinza"
                                  style="width: 210px;"/>
                    <h:outputText value="#{contratoRepeat.contratoRecorrenciaVO.diaVencimentoCartao}"
                                  styleClass="texto-size-14 cinza" style="margin-left: 3px;"/>
                    <div style="justify-content: flex-end; width: 100%;">
                        <a4j:commandLink
                                rendered="#{TelaClienteControle.permiteAlterarDataVencimentoParcelas}"
                                title="Alterar dia de vencimento da parcela"
                                styleClass="linkAzul floatright"
                                style="margin-right: 1vw; justify-self: flex-end;"
                                oncomplete="Richfaces.showModalPanel('mudancaDiaVencimentoParcela');"
                                reRender="mudancaDiaVencimentoParcela"
                                actionListener="#{TelaClienteControle.selecionarContratoParaMudancaDiaVencimentoParcela}">
                            <f:attribute name="codigoContrato" value="#{contratoRepeat.codigo}"/>
                            <f:attribute name="diaVencimentoCartao"
                                         value="#{contratoRepeat.contratoRecorrenciaVO.diaVencimentoCartao}"/>
                            <i class="fa-icon-exchange" style="font-size: 0.8vw;"></i>
                            <h:outputText value="Mudança de vencimento"/>
                        </a4j:commandLink>
                    </div>
                </h:panelGroup>

            </h:panelGroup>
        </a4j:repeat>

        <h:panelGroup layout="block"
                      rendered="#{TelaClienteControle.apresentarBloqueioParcelas}"
                      style="margin-top: 15px;">
            <h:outputText value="Cobrança automática: "
                          styleClass="texto-size-14 negrito cinza"/>
            <c:if test="${TelaClienteControle.cliente.pessoa.dataBloqueioCobrancaAutomatica != null}">
                <a4j:commandLink value="BLOQUEADA"
                                 disabled="#{!LoginControle.permissaoAcessoMenuVO.permiteBloquearDesbloquearClienteCobrancaAutomatica}"
                                 oncomplete="Richfaces.showModalPanel('modalDesbloquearBloquearCobrancas');"
                                 reRender="formModalDesbloquearBloquearCobrancas"
                                 title="#{TelaClienteControle.bloqueioCobrancaApresentar}"
                                 style="color: #ab0000"
                                 styleClass="texto-size-14 negrito cinza tooltipster"/>
            </c:if>
            <c:if test="${TelaClienteControle.cliente.pessoa.dataBloqueioCobrancaAutomatica == null}">
                <a4j:commandLink value="LIBERADA"
                                 reRender="formModalBloqueioCobrancas"
                                 disabled="#{!LoginControle.permissaoAcessoMenuVO.permiteBloquearDesbloquearClienteCobrancaAutomatica}"
                                 action="#{TelaClienteControle.abrirBloquearCobrancasAutomatica}"
                                 oncomplete="#{TelaClienteControle.onCompleteBloqueioCobrancas};#{TelaClienteControle.mensagemNotificar}"
                                 title="#{TelaClienteControle.bloqueioCobrancaApresentar}"
                                 style="color: #2baf50"
                                 styleClass="texto-size-14 negrito cinza tooltipster"/>
            </c:if>
        </h:panelGroup>
    </div>

    <!-- CAMPO CATEGORIA -->
    <c:if test="${ConfiguracaoSistemaControle.configuracaoSistemaVO.sesiCe}">
        <div style="margin-top: 10px; margin-bottom: 10px;">
            <h:outputText value="Categoria: " styleClass="texto-size-14 negrito cinza"/>
            <h:outputText value="#{TelaClienteControle.cliente.categoria.nome}" styleClass="texto-size-14 cinza"/>
        </div>
    </c:if>
    <!--  -->

    <div class="insideCubo googleAnalytics" style="padding-top: 0.5vw;">
        <span class="texto-size-14 negrito cinza googleAnalytics">Contato</span>

        <a4j:commandLink id="atualizarBVDadosCliente" action="#{TelaClienteControle.abrirQuestionarioBV}"
                         styleClass="linkAzul floatright" rendered="#{TelaClienteControle.apresentarAtualisarBV}"
                         style="margin-left: 5px">
            <i class="fa-icon-repeat" id="atualizarBVDadosClienteIcon"
               style="font-size: 0.8vw;"></i><span> Atualizar BV</span>
        </a4j:commandLink>

        <a4j:commandLink styleClass="linkAzul floatright" action="#{MetaCRMControle.inicializarContatoAvulso}"
                         id="realizarContatoTelaCliente"
                         oncomplete="abrirPopup('newRealizarContatoForm.jsp', 'Realizar Contato Avulso', 850, 700);">
            <i class="fa-icon-paper-plane" id="realizarContatoTelaClienteIcon" style="font-size: 0.8vw;"></i><span
                id="realizarContatoTelaClienteText"> Realizar contato</span>
        </a4j:commandLink>
    </div>
    <div class="insideCubo" style="margin: 15px 0;">
        <div class="colunaDadosCliente" style="vertical-align: top;">
            <div class="colunaDadosCliente">
                <span class="texto-size-14 cinza negrito">Cadastro</span>
            </div>
            <div class="colunaDadosCliente">
                <h:outputText id="cliDataCadatro"
                              title="#{TelaClienteControle.cliente.pessoa.dataCadastroComHoraApresentar}"
                              styleClass="texto-size-14 cinza tooltipster"
                              value="#{TelaClienteControle.cliente.pessoa.dataCadastro}">
                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                </h:outputText>
            </div>

            <h:panelGroup layout="block" styleClass="colunaDadosCliente" style="margin-top: 15px;"
                          rendered="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.dataMatricula != null}">
                <span class="texto-size-14 cinza negrito">Matrícula</span>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="colunaDadosCliente"
                          rendered="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.dataMatricula != null}">
                <h:outputText styleClass="texto-size-14 cinza"
                              value="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.dataMatricula}">
                    <f:convertDateTime type="date" dateStyle="short"
                                       locale="pt" timeZone="America/Sao_Paulo"
                                       pattern="dd/MM/yyyy"/>
                </h:outputText>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="colunaDadosCliente" style="margin-top: 15px;"
                          rendered="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.dataUltimaRematricula != null}">
                <span class="texto-size-14 cinza negrito">Rematrícula</span>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="colunaDadosCliente"
                          rendered="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.dataUltimaRematricula != null}">
                <h:outputText id="cliDataRematricula" styleClass="texto-size-14 cinza"
                              value="#{TelaClienteControle.cliente.situacaoClienteSinteticoVO.dataUltimaRematricula}">
                    <f:convertDateTime type="date" dateStyle="short"
                                       locale="pt" timeZone="America/Sao_Paulo"
                                       pattern="dd/MM/yyyy"/>
                </h:outputText>
            </h:panelGroup>
        </div>
        <div class="colunaDadosCliente" style="vertical-align: top;">

            <h:dataTable id="vinculos" width="100%" cellpadding="5" rows="4"
                         rowClasses="texto-size-14" columnClasses="colunaEsquerda"
                         value="#{TelaClienteControle.cliente.vinculoVOs}"
                         var="vinculo">
                <h:column>
                    <h:outputText styleClass="cinza negrito"
                                  id="tipoVinculo"
                                  value="#{vinculo.tipoVinculo_Apresentar}"/>
                </h:column>
                <h:column>
                    <h:outputText styleClass="cinza" style="text-transform: capitalize;"
                                  id="colaboradorVinculo"
                                  value="#{vinculo.colaborador.pessoa.primeiroNomeConcatenadoMin}"/>
                </h:column>
            </h:dataTable>
            <a4j:commandLink title="Ver todos os vínculos"
                             rendered="#{fn:length(TelaClienteControle.cliente.vinculoVOs) > 4}"
                             styleClass="linkAzul floatright"
                             oncomplete="Richfaces.showModalPanel('modalTodosVinculos');">
                <h:outputText value="Ver todos"/>
                <i class="fa-icon-angle-right"></i>

            </a4j:commandLink>
        </div>
    </div>

    <h:panelGroup styleClass="insideCubo bordaSuperiorCinza" layout="block">
        <div style="padding-top: 0.5vw;">
            <span class="texto-size-14 negrito cinza googleAnalytics">Modalidades de contratos ativos</span>
        </div>
        <div class="texto-size-14 cinza">
            <h:panelGroup layout="block" id="listaModalidades">
                <ul>
                    <c:forEach var="modalidade" items="#{TelaClienteControle.modalidadesContrato}">
                        <li><h:outputText value="#{modalidade}" style="margin-top: 15px"/></li>
                    </c:forEach>
                </ul>
                <h:panelGroup layout="block" style="text-align: right"
                              rendered="#{TelaClienteControle.totalModalidadesContrato gt 2}">
                    <a4j:commandLink value="#{!TelaClienteControle.exibirTodasModalidades ? 'Ver mais' : 'Ver menos'}"
                                     styleClass="linkPadrao texto-font texto-size-14 texto-cor-azul tooltipster"
                                     id="verMaisVerMenos"
                                     title="#{!TelaClienteControle.exibirTodasModalidades ? 'Mostrar Modalidades.' : 'Esconder Modalidades.'}"
                                     reRender="listaModalidades"
                                     style="font-size: 14px !important;"
                                     action="#{TelaClienteControle.mostrarMaisModalidades}">
                        <h:outputText style="margin-left: 5px;vertical-align: middle; font-size: 14px;"
                                      styleClass="texto-font texto-cor-azul texto-size-14 #{TelaClienteControle.exibirTodasModalidades ? 'fa-icon-minus-sign' : 'fa-icon-plus-sign'}"/>
                        <f:setPropertyActionListener value="#{!TelaClienteControle.exibirTodasModalidades}"
                                                     target="#{TelaClienteControle.exibirTodasModalidades}"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
        </div>
    </h:panelGroup>

    <h:panelGroup layout="block"
                  styleClass="insideCubo bordaSuperiorCinza"
                  rendered="#{TelaClienteControle.ultimoAtestadoCliente ne null}">
        <div class="colunaDadosCliente" style="vertical-align: top;margin: 15px 0;">
            <span class="texto-size-14 cinza negrito">Atestado de aptidão física</span>
        </div>
        <div class="colunaDadosCliente cinza" style="vertical-align: top; margin: 15px 0;">
            <h:outputText style="padding-left: 5px;"
                          value="#{TelaClienteControle.ultimoAtestadoCliente.vigente ? 'ATIVO' : 'INATIVO'}"/>
        </div>
        <div class="colunaDadosCliente">
            <span class="texto-size-14 cinza negrito">Validade</span>
        </div>
        <div class="colunaDadosCliente cinza" style="white-space: nowrap;">
            <span style="padding-left: 5px;">
                <h:outputText
                        value="#{TelaClienteControle.ultimoAtestadoCliente.movProduto.dataInicioVigencia_Apresentar}"/>
                    &nbsp;até&nbsp;
                <h:outputText
                        value="#{TelaClienteControle.ultimoAtestadoCliente.movProduto.dataFinalVigencia_Apresentar}"/>
            </span>
        </div>
    </h:panelGroup>

    <h:panelGroup styleClass="insideCubo bordaSuperiorCinza" layout="block" rendered="#{TelaClienteControle.buttonAcessoConvidado}">
        <a4j:commandLink styleClass="pure-button pure-button-primary"
                         style="margin-top: 10px; margin-bottom: 10px;"
                         action="#{TelaClienteControle.ativarAcessoConvidado}"
                         oncomplete="#{TelaClienteControle.mensagemNotificar};window.location.reload();"
                         value="Ativar acesso do convidado"/>
    </h:panelGroup>

</div>
<div class="rodapePainelAluno" style="bottom: 0; height: 40px;">
    <a4j:commandLink title="Ver Histórico Vínculo" id="verHistoricoVinculoAluno"
                     styleClass="linkAzul floatright"
                     style="margin-right: 1vw;"
                     reRender="formHistoricoVinculo,panelListaHistoricoVinculo"
                     action="#{TelaClienteControle.abrirHistoricoVinculo}"
                     oncomplete="Richfaces.showModalPanel('modalPanelHistoricoVinculo');">
        <i id="verHistoricoVinculoAlunoIcon" class="fa-icon-history" style="font-size: 0.8vw;"></i>
        <h:outputText value="Histórico"/>
    </a4j:commandLink>
    <a4j:commandLink title="Anexar Arquivo ao cliente" id="anexarAquivoPainelCliente"
                     styleClass="linkAzul floatright"
                     style="margin-right: 1vw;"
                     oncomplete="Richfaces.showModalPanel('modalAnexoCliente');"
                     action="#{TelaClienteControle.montarUrlCompletaAnexo}">
        <i id="anexarAquivoPainelClienteIcon" class="fa-icon-paper-clip" style="font-size: 0.8vw;"></i>
        <h:outputText value="Anexar arquivo"/>
    </a4j:commandLink>
    <c:if test="${not param.readOnly}">
        <a4j:commandLink title="Ver Histórico Vínculo"
                         styleClass="linkAzul floatright"
                         style="margin-right: 1vw;"
                         reRender="formHistoricoVinculo,panelListaHistoricoVinculo"
                         actionListener="#{ClienteControle.abrirAba}"
                         oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
            <i id="editarVinculoIcon" class="fa-icon-edit" style="font-size: 0.8vw;"></i>
            <h:outputText value="Editar vínculos" id="editarVinculo"/>
            <f:attribute name="aba" value="abaVinculo"/>
        </a4j:commandLink>
    </c:if>
</div>

<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
-
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Horario_tituloForm}"/>
    </title>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Horario_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:PPT:Horario"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"  width="100%">

                    <h:outputText   value="#{msg_aplic.prt_Horario_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{HorarioControle.horarioVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_Horario_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao"  size="45" maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{HorarioControle.horarioVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>  
                    <h:outputText   value="#{msg_aplic.prt_Horario_livre}" />
                    <h:selectBooleanCheckbox id="livre" styleClass="campos" value="#{HorarioControle.horarioVO.livre}">
                        <a4j:support    event="onclick" focus="livre" action="#{HorarioControle.desabilitarGrade}" reRender="form" />
                    </h:selectBooleanCheckbox>
                    <h:outputText   value="#{msg_aplic.prt_Horario_horarioDefault}" />
                    <h:selectBooleanCheckbox id="horarioDefault" styleClass="campos" value="#{HorarioControle.horarioVO.horarioDefault}"/>
                    <h:outputText value="#{msg_aplic.prt_Horario_ativo}" />
                    <h:selectBooleanCheckbox id="horarioAtivo" styleClass="campos" value="#{HorarioControle.horarioVO.ativo}"/>


                    <h:outputText rendered="#{PlanoControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="#{msg_aplic.prt_correspondenciaZD}" />
                    <h:inputText rendered="#{PlanoControle.usuarioLogado.administrador}" styleClass="form" value="#{HorarioControle.horarioVO.correspondenciaZD}" />

                </h:panelGrid>

                <rich:tabPanel width="100%" rendered="#{!HorarioControle.desabilitarPreenchimento}" >

                    <rich:tab id="periodoMatutino" label="Manhã">
                        <h:dataTable id="horarioDisponibilidadeVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                     value="#{HorarioControle.horarioVO.horarioDisponibilidadeVOs}" var="horarioDisponibilidadeVO">
                            <h:column >
                                <f:facet name="header">
                                    <h:outputText  value="Todos" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="selectTodosHorManha" rendered="#{horarioDisponibilidadeVO.desenharTodos}" styleClass="campos" value="#{horarioDisponibilidadeVO.matutino}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaLinhaMatutino}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_identificador}" />
                                </f:facet>
                                <h:outputText  value="#{horarioDisponibilidadeVO.identificador}" />
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0500}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0500" styleClass="campos" disabled="#{HorarioControle.desabilitarPreenchimento}" value="#{horarioDisponibilidadeVO.hora0500}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0500}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0530}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0530" styleClass="campos" disabled="#{HorarioControle.desabilitarPreenchimento}" value="#{horarioDisponibilidadeVO.hora0530}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0530}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0600}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0600" styleClass="campos" disabled="#{HorarioControle.desabilitarPreenchimento}" value="#{horarioDisponibilidadeVO.hora0600}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0600}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0630}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0630" styleClass="campos" disabled="#{HorarioControle.desabilitarPreenchimento}" value="#{horarioDisponibilidadeVO.hora0630}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0630}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0700}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0700" styleClass="campos" disabled="#{HorarioControle.desabilitarPreenchimento}" value="#{horarioDisponibilidadeVO.hora0700}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0700}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0730}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0730" styleClass="campos" value="#{horarioDisponibilidadeVO.hora0730}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0730}" reRender="form"/>
                                </h:selectBooleanCheckbox>

                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0800}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0800" styleClass="campos" value="#{horarioDisponibilidadeVO.hora0800}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0800}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0830}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0830" styleClass="campos" value="#{horarioDisponibilidadeVO.hora0830}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0830}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0900}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0900" styleClass="campos" value="#{horarioDisponibilidadeVO.hora0900}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0900}" reRender="form"/>
                                </h:selectBooleanCheckbox>    
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0930}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora0930" styleClass="campos" value="#{horarioDisponibilidadeVO.hora0930}" disabled="#{HorarioControle.desabilitarPreenchimento}" >
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna0930}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1000}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1000" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1000}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1000}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1030}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1030" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1030}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1030}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1100}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1100" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1100}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1100}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1130}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1130" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1130}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1130}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                        </h:dataTable>
                    </rich:tab>    

                    <rich:tab id="periodoVespertino" label="Tarde">
                        <h:dataTable id="horarioDisponibilidadeVO1" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                     value="#{HorarioControle.horarioVO.horarioDisponibilidadeVOs}" var="horarioDisponibilidadeVO">
                            <h:column >
                                <f:facet name="header">
                                    <h:outputText  value="Todos" />
                                </f:facet>
                                <h:selectBooleanCheckbox rendered="#{horarioDisponibilidadeVO.desenharTodos}" styleClass="campos" value="#{horarioDisponibilidadeVO.vespertino}">
                                    <a4j:support id="selectTodosHorTarde" event="onclick" action="#{HorarioControle.MarcaTodaLinhaVespertino}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_identificador}" />
                                </f:facet>
                                <h:outputText  value="#{horarioDisponibilidadeVO.identificador}" />
                            </h:column>                              
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1200}"  />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1200" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1200}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1200}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1230}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1230" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1230}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1230}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1300}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1300" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1300}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1300}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1330}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1330" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1330}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1330}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1400}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1400" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1400}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1400}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1430}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1430" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1430}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1430}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1500}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1500" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1500}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1500}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1530}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1530" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1530}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1530}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1600}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1600" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1600}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1600}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1630}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1630" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1630}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1630}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1700}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1700" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1700}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1700}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1730}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1730" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1730}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1730}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>                               
                        </h:dataTable>
                    </rich:tab>

                    <rich:tab id="periodoNortuno" label="Noite ">
                        <h:dataTable id="horarioDisponibilidadeVO2" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                     value="#{HorarioControle.horarioVO.horarioDisponibilidadeVOs}" var="horarioDisponibilidadeVO">

                            <h:column >
                                <f:facet name="header">
                                    <h:outputText  value="Todos" />
                                </f:facet>
                                <h:selectBooleanCheckbox rendered="#{horarioDisponibilidadeVO.desenharTodos}" styleClass="campos" value="#{horarioDisponibilidadeVO.noturno}">
                                    <a4j:support id="selectTodosHorNoite" event="onclick" action="#{HorarioControle.MarcaTodaLinhaNoturno}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_identificador}" />
                                </f:facet>
                                <h:outputText  value="#{horarioDisponibilidadeVO.identificador}" />
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1800}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1800" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1800}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1800}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1830}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1830" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1830}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1830}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1900}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1900" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1900}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1900}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora1930}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora1930" styleClass="campos" value="#{horarioDisponibilidadeVO.hora1930}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna1930}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora2000}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora2000" styleClass="campos" value="#{horarioDisponibilidadeVO.hora2000}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna2000}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora2030}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora2030" styleClass="campos" value="#{horarioDisponibilidadeVO.hora2030}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna2030}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora2100}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora2100" styleClass="campos" value="#{horarioDisponibilidadeVO.hora2100}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna2100}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora2130}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora2130" styleClass="campos" value="#{horarioDisponibilidadeVO.hora2130}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna2130}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora2200}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora2200" styleClass="campos" value="#{horarioDisponibilidadeVO.hora2200}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna2200}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora2230}" />
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora2230" styleClass="campos" value="#{horarioDisponibilidadeVO.hora2230}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna2230}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_aplic.prt_HorarioDisponibilidade_hora2300}"/>
                                </f:facet>
                                <h:selectBooleanCheckbox id="hora2300" styleClass="campos" value="#{horarioDisponibilidadeVO.hora2300}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna2300}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>
                           <h:column>
                               <f:facet name="header">
                                   <h:outputText value="#{msg_aplic.prt_HorarioDisponibilidade_hora2330}"/>
                               </f:facet>
                               <h:selectBooleanCheckbox id="hora2330" styleClass="campos" value="#{horarioDisponibilidadeVO.hora2330}" disabled="#{HorarioControle.desabilitarPreenchimento}">
                                   <a4j:support event="onclick" action="#{HorarioControle.MarcaTodaColuna2330}" reRender="form"/>
                               </h:selectBooleanCheckbox>
                           </h:column>
                        </h:dataTable>
                    </rich:tab>

                    <rich:tab label="Madrugada ">
                        <h:dataTable id="periodoMadrugada"
                                     width="100%"
                                     headerClass="subordinado"
                                     styleClass="tabFormSubordinada"
                                     rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaAlinhamento"
                                     value="#{HorarioControle.horarioVO.horarioDisponibilidadeVOs}"
                                     var="horarioDisponibilidadeVO">

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText value="Todos" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        rendered="#{horarioDisponibilidadeVO.desenharTodos}"
                                        styleClass="campos checkbox-todos"
                                        value="#{horarioDisponibilidadeVO.madrugada}">
                                    <a4j:support id="selectTodosHorNoite" event="onclick" action="#{HorarioControle.marcarTodaLinhaMadrugada}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column id="identificador">
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_identificador}" />
                                </f:facet>
                                <h:outputText  value="#{horarioDisponibilidadeVO.identificador}" />
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0000}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0000}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0000}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0030}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0030}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0030}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0100}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0100}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0100}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0130}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0130}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0130}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0200}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0200}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0200}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0230}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0230}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0230}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0300}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0300}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0300}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0330}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0330}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0330}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0400}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0400}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0400}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_HorarioDisponibilidade_hora0430}" />
                                </f:facet>
                                <h:selectBooleanCheckbox
                                        styleClass="campos"
                                        value="#{horarioDisponibilidadeVO.hora0430}"
                                        disabled="#{HorarioControle.desabilitarPreenchimento}">
                                    <a4j:support event="onclick" action="#{HorarioControle.marcarTodaColuna0430}" reRender="form"/>
                                </h:selectBooleanCheckbox>
                            </h:column>

                        </h:dataTable>
                    </rich:tab>
                </rich:tabPanel>


                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton id="icHorarioSuc" rendered="#{HorarioControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton id="icHorarioFal" rendered="#{HorarioControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgHorario" styleClass="mensagem"  value="#{HorarioControle.mensagem}"/>
                            <h:outputText id="msgHorarioDet" styleClass="mensagemDetalhada" value="#{HorarioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo"  action="#{HorarioControle.novo}" value="#{msg_bt.btn_novo}"  alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="salvar" action="#{HorarioControle.gravar}" value="#{msg_bt.btn_gravar}"  oncomplete="#{HorarioControle.mensagemNotificar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{HorarioControle.msgAlert}" action="#{HorarioControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="clonar" rendered="#{HorarioControle.horarioVO.codigo > 0}" action="#{HorarioControle.clonar}" reRender="form" value="Clonar" alt="#{msg.msg_clonar_dados}" accesskey="5" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandButton id="consultar"  action="#{HorarioControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}"  alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                             <h:outputText value="    "/>
                            <a4j:commandLink action="#{HorarioControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>

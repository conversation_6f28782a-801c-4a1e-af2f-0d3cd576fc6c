<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
		padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ClienteMensagem_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ClienteMensagem_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Inicial:Informações_do_Cliente"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        
        <h:form id="form">
            <h:commandLink action="#{ClienteMensagemControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ClienteMensagem_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClienteMensagem_codigo}" />
                            <h:panelGroup>
                                <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{ClienteMensagemControle.clienteMensagemVO.codigo}" />
                                <h:message for="codigo" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClienteMensagem_mensagem}" />
                            <h:inputText  id="mensagem" size="70" maxlength="100" styleClass="campos" value="#{ClienteMensagemControle.clienteMensagemVO.mensagem}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClienteMensagem_cliente}" />
                            <h:inputText  id="cliente" size="10" maxlength="10" styleClass="campos" value="#{ClienteMensagemControle.clienteMensagemVO.cliente}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClienteMensagem_tipomensagem}" />
                            <h:inputText  id="tipomensagem" size="2" maxlength="2" styleClass="campos" value="#{ClienteMensagemControle.clienteMensagemVO.tipomensagem}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClienteMensagem_tipoaviso}" />
                            <h:inputText  id="tipoaviso" size="2" maxlength="2" styleClass="campos" value="#{ClienteMensagemControle.clienteMensagemVO.tipoaviso}" />
                            <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ClienteMensagem_usuario}" />
                            <h:inputText  id="usuario" size="10" maxlength="10" styleClass="campos" value="#{ClienteMensagemControle.clienteMensagemVO.usuario}" />

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ClienteMensagemControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ClienteMensagemControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{ClienteMensagemControle.novo}" image="./imagens/botaoNovo.png" value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="salvar" action="#{ClienteMensagemControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="excluir" onclick="return confirm('#{msg.msg_ConfirmaExclusao}');" action="#{ClienteMensagemControle.excluir}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoExcluir.png" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botaoExcluir"/>
                            <rich:spacer width="10"/>
                            <h:commandButton id="consultar" immediate="true" action="#{ClienteMensagemControle.inicializarConsultar}" value="#{msg_bt.btn_consultar}" image="./imagens/botaoConsultar.png" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:mensagem").focus();
</script>
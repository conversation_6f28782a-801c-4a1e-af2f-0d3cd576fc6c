<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@include file="includes/imports.jsp" %>
<style>
    .subordinado {
        padding: 5px !important;
    }
</style>
<h:panelGroup layout="block" id="panelGeralImportacaoFornecedor">

    <h:panelGroup layout="block" styleClass="panelObjetivo">
        <h:outputLabel value="Objetivo:" styleClass="textoObjetivo"
                       style="font-weight: bold; font-style: italic;"/>
        <h:outputLabel styleClass="textoObjetivo"
                       value="Realizar importação de fornecedores através de uma planilha modelo disponibilizada para download."/>
    </h:panelGroup>

    <h:panelGroup layout="block" style="padding: 15px;">


        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="1º passo:" styleClass="passosImportacao"/>
            <a4j:commandLink target="_blank"
                             style="padding-left: 5px; font-size: 15px;"
                             value="Baixar planilha modelo"
                             oncomplete="location.href='../DownloadSV?mimeType=application/vnd.ms-excel&diretorio=modelo&relatorio=modelo_importacao_fornecedor.xlsx'"/>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="2º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Informe as configurações:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno"
                          style="display: inline-flex; width: 100%"
                          id="panelConfiguracoesImportacaoFornecedor">
                <h:panelGrid columns="2" width="100%"
                             columnClasses="colunaEsquerdaImport, colunaDireitaImport">

                    <h:outputLabel value="DDD Padrão:"
                                   styleClass="passosImportacaoDescricao"/>
                    <h:inputText styleClass="form tooltipster"
                                 title="DDD Padrão para os números sem o DDD informado. (Somente números)"
                                 maxlength="3" size="3"
                                 onkeypress="bloquearEnter()"
                                 style="padding-left: 5px"
                                 value="#{ImportacaoControle.configFornecedorTO.padraoDDD}"/>

                    <h:outputLabel value="Validar CPF ou CNPJ já cadastrado:"
                                   styleClass="passosImportacaoDescricao tooltipster"/>
                    <h:selectBooleanCheckbox styleClass="form tooltipster"
                                             style="padding-left: 5px"
                                             title="Caso exista um aluno com o mesmo CPF o cadastro e contrato não serão importados."
                                             value="#{ImportacaoControle.configFornecedorTO.validarCpfCnpjJaCadastrado}"/>

                    <h:outputLabel value="Máscara campos de data:"
                                   styleClass="passosImportacaoDescricao"/>

                    <h:selectOneMenu onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form tooltipster"
                                     value="#{ImportacaoControle.configFornecedorTO.mascaraDataEnum}">
                        <f:selectItems value="#{ImportacaoControle.listaSelectItemMascaraData}"/>
                    </h:selectOneMenu>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="3º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Preencher a planilha seguindo as seguintes regras:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
            <br/>
            <h:panelGroup layout="block" styleClass="panelPassosInterno" style="font-size: 15px;">
                       <span>
                            <ul style="padding: 5px;margin: 0;">
                                <li style="margin-left: 2%;line-height: 15px; padding-bottom: 5px"> Campos Obrigatórios para importar o cadastro: <b>ID_EXTERNO e NOME</b></li>
                            </ul>
                       </span>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelPassos">
            <h:outputLabel value="4º passo:" styleClass="passosImportacao"/>
            <h:outputLabel value="Fazer o Upload da planilha baixada e realizar a importação:"
                           styleClass="passosImportacaoDescricao"
                           style="padding-left: 5px"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelUploadFileFornecedor" style="padding-top: 5px; padding-left: 30px">
            <rich:fileUpload
                    fileUploadListener="#{ImportacaoControle.uploadArquivoFornecedor}"
                    immediateUpload="true" id="imagemModeloUploadFornecedor"
                    acceptedTypes="xls,xlsx" allowFlash="false"
                    listHeight="58px"
                    cancelEntryControlLabel="Cancelar"
                    addControlLabel="Adicionar"
                    clearControlLabel="Remover"
                    clearAllControlLabel="Remover Todos"
                    doneLabel="Concluído"
                    sizeErrorLabel="Limite de tamanho atingido"
                    uploadControlLabel="Carregar"
                    transferErrorLabel="Erro na transferência"
                    stopControlLabel="Parar"
                    stopEntryControlLabel="Parar"
                    progressLabel="Carregando"
                    maxFilesQuantity="1">
                <a4j:support event="onerror" reRender="panelBotoesImportacaoFornecedor" action="#{ImportacaoControle.removerArquivo}"/>
                <a4j:support event="onupload" reRender="panelBotoesImportacaoFornecedor"/>
                <a4j:support event="onuploadcomplete" reRender="panelBotoesImportacaoFornecedor"/>
                <a4j:support event="onclear" reRender="panelBotoesImportacaoFornecedor, panelUploadFileFornecedor" action="#{ImportacaoControle.removerArquivo}"/>
            </rich:fileUpload>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" id="panelBotoesImportacaoFornecedor"
                  styleClass="panelBotoesImportacao">
        <a4j:commandLink id="btnImportarFornecedor" value="Ler Arquivo"
                         style="padding-left: 15px"
                         onclick="atualizarTempoImportacao()"
                         rendered="#{ImportacaoControle.apresentarImportar}"
                         action="#{ImportacaoControle.processarArquivoFornecedor}"
                         oncomplete="#{ImportacaoControle.onComplete};#{ImportacaoControle.mensagemNotificar}"
                         title="Processar Importação de Dados"
                         reRender="panelGeralModalConfirmarImportacao, formModImpo"
                         styleClass="botoes nvoBt"/>
    </h:panelGroup>
</h:panelGroup>

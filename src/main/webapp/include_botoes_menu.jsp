<%@include file="includes/imports.jsp" %>
<div style="float:left;">
    <ul class="btnazul">
        <li>
            <p class="btnleft"></p>

            <p class="btnmiddle">
                <a4j:commandLink id="linkInicialMenuSuperior" action="#{NavegacaoControle.abrirTelaInicialZW}">
                    Inicial
                </a4j:commandLink>
            </p>

            <p class="btnright"></p>
        </li>

            <li>
                <p class="btnleft"></p>

                <p class="btnmiddle">
                    <a4j:commandLink id="linkCadastroMenuSuperior" action="#{NavegacaoControle.abrirTelaCadastros}">
                    Cadastros
                    </a4j:commandLink>

                <p class="btnright"></p>
            </li>
        <li>
            <c:if test="${LoginControle.permissaoAcessoMenuVO.visualizarBI}">
                <p class="btnleft"></p>

                <p class="btnmiddle">
                    <a4j:commandLink id="linkBIMenuSuperior" action="#{LoginControle.inicializarMetodosTelaInicial}"
                                     rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarBI}">
                        Business Intelligence
                    </a4j:commandLink>
                </p>

                <p class="btnright"></p>
            </c:if>
        </li>
        
        <%--<li>--%>
        <%--<p class="btnleft"></p>--%>
        <%--<p class="btnmiddle">--%>
        <%--<a4j:commandLink id="linkRelatorioMenuSuperior" action="#{NavegacaoControle.abrirListasRelatorios}">--%>
        <%--<h:outputText value="Relat�rios"></h:outputText>--%>
        <%--</a4j:commandLink>--%>
        <%--</p>--%>
        <%--<p class="btnright"></p>--%>
        <%--</li>--%>

        <h:panelGroup
                rendered="#{LoginControle.apresentarLinkSmartbox && LoginControle.permissaoAcessoMenuVO.telaInicialSmartBox}">
            <li>
                <p class="btnleft"></p>

                <p class="btnmiddle">
                    <a4j:commandLink id="linkSmartBoxMenuSuperior" action="#{SmartBoxControle.inicializar}">
                        Pacto SmartBox &copy;
                    </a4j:commandLink>
                </p>

                <p class="btnright"></p>
            </li>
        </h:panelGroup>

        <li>
            <p class="btnleft"></p>

            <p class="btnmiddle">
                <a4j:commandLink styleClass="titulo3" id="linkClienteMenuSuperior1"
                                 rendered="#{(not LoginControle.apresentarLinkZW) && LoginControle.apresentarLinkEstudio}"
                                 actionListener="#{ConsultaClienteControle.consultarPaginadoListenerReset}"
                                 action="#{ConsultaClienteControle.acao}" value="Clientes">
                    <f:attribute name="paginaInicial" value="paginaInicial"/>
                    <f:attribute name="letraConsultPag" value="A"/>
                    <f:attribute name="tipoConsulta" value="letra"/>
                    <f:attribute name="modulo" value="4bf2add2267962ea87f029fef8f75a2f"/>
                </a4j:commandLink>

                <a4j:commandLink rendered="#{LoginControle.apresentarLinkZW}" id="linkClienteMenuSuperior"
                                 styleClass="titulo3" action="#{ConsultaClienteControle.acao}"
                                 actionListener="#{ConsultaClienteControle.consultarPaginadoListenerReset}"
                                 value="Clientes">
                    <f:attribute name="paginaInicial" value="paginaInicial"/>
                    <f:attribute name="tipoConsulta" value="detalhada"/>
                </a4j:commandLink>
            </p>

            <p class="btnright"></p>
        </li>
        
        <li>
            <p class="btnleft"></p>

            <p class="btnmiddle">
                <a4j:commandLink id="idCLIncluirCliente" action="#{PreCadastroClienteControle.novo}"
	                                  value="Incluir Cliente"  accesskey="2">
                    <c:if test="${modulo eq '4bf2add2267962ea87f029fef8f75a2f'}">
                       <f:attribute name="modulo" value="4bf2add2267962ea87f029fef8f75a2f"/>
                   </c:if>
                </a4j:commandLink>
            </p>

            <p class="btnright"></p>
            
        </li>

        <h:panelGroup rendered="#{LoginControle.apresentarLinkEstudio}">
            <li>
                <p class="btnleft"></p>

                <p class="btnmiddle">
                    <a4j:commandLink id="linkStudioMenuSuperior" action="#{LoginControle.abrirModuloEstudio}">
                        Agenda
                    </a4j:commandLink>
                </p>

                <p class="btnright"></p>
            </li>
        </h:panelGroup>
    </ul>
</div>

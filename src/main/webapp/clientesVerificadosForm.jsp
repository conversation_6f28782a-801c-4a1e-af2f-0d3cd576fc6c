<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@ taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <h:outputText value="BI - Clientes Verificados"/>
    </title>
    <html>
    <body>

    <h:form id="form">
        <c:set var="titulo" scope="session"
               value="${ClientesVerificadosRelControle.tituloLista} - ${fn:length(ClientesVerificadosRelControle.listaApresentar)} objetos"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-verificacao-de-clientes/"/>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topo_reduzido_popUp.jsp"/>
            </f:facet>
        </h:panelGroup>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;height: 100vh" styleClass="fundoBranco">

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block">
                            <h:panelGroup layout="block" styleClass="margin-box">
                                <table width="100%" align="center" height="100%" border="0" cellpadding="0"
                                       cellspacing="0">

                                    <tr>
                                        <td align="left" valign="top" width="100%" style="padding:0 0 0 0;">
                                            <h:panelGroup id="panelGroup">
                                                <table width="100%" height="100%" border="0" align="left"
                                                       cellpadding="0" cellspacing="0" class="text"
                                                       style="margin-bottom:20px;">

                                                    <tr>
                                                        <td align="left" valign="top"
                                                            style="padding:5px 15px 5px 15px;">
                                                            <h:panelGrid id="panelCliente" width="100%">
                                                                <h:outputText styleClass="mensagemDetalhada" value="#{ClientesVerificadosRelControle.mensagemDetalhada}"/>
                                                                <h:panelGrid cellpadding="0" cellspacing="0" columns="2" width="100%" style="border:none;">
                                                                    <rich:column style="border:none;" width="50" colspan="2">
                                                                        <h:selectBooleanCheckbox style="vertical-align:middle;" id="mostrarPaginacao" value="#{ClientesVerificadosRelControle.mostrarPaginacao}">
                                                                            <a4j:support event="onclick" action="#{DataScrollerControle.resetDatascroller}" reRender="panelCliente,item,panelGroup"/>
                                                                        </h:selectBooleanCheckbox>
                                                                        <h:outputText value="Mostrar Paginação" styleClass="texto-font texto-size-14-real texto-cor-cinza" style="font-weight: bold;vertical-align:middle;"/>
                                                                    </rich:column>
                                                                    <rich:column style="border:none;"
                                                                                 styleClass="colunaDireita">
                                                                        <a4j:commandLink id="exportarPDF"
                                                                                         styleClass="linkPadrao"
                                                                                         style="margin-left: 8px;"
                                                                                         actionListener="#{ExportadorListaControle.exportar}"
                                                                                         rendered="#{not empty ClientesVerificadosRelControle.listaApresentar}"
                                                                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                                         accesskey="2">
                                                                            <f:attribute name="lista" value="#{ClientesVerificadosRelControle.listaApresentar}"/>
                                                                            <f:attribute name="itemExportacao" value="#{ClientesVerificadosRelControle.itemExportacao}"/>
                                                                            <f:attribute name="tipo" value="pdf"/>
                                                                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,existeContratoRenovado=Renovou Contrato,verificadoEm_Hint=Verificado Em,usuarioVerificacao=Verificado Por"/>
                                                                            <f:attribute name="prefixo" value="ClientesVerificados"/>
                                                                            <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 PDF"/>
                                                                        </a4j:commandLink>

                                                                        <a4j:commandLink id="exportarExcel"
                                                                                         styleClass="linkPadrao"
                                                                                         style="margin-left: 8px;"
                                                                                         actionListener="#{ExportadorListaControle.exportar}"
                                                                                         rendered="#{not empty ClientesVerificadosRelControle.listaApresentar}"
                                                                                         oncomplete="#{ExportadorListaControle.mensagemNotificar}#{ExportadorListaControle.operacaoOnComplete}#{ExportadorListaControle.msgAlert}"
                                                                                         accesskey="2">
                                                                            <f:attribute name="lista" value="#{ClientesVerificadosRelControle.listaApresentar}"/>
                                                                            <f:attribute name="tipo" value="xls"/>
                                                                            <f:attribute name="itemExportacao" value="#{ClientesVerificadosRelControle.itemExportacao}"/>
                                                                            <f:attribute name="atributos" value="matricula=Matrícula,nome=Nome,existeContratoRenovado=Renovou Contrato,verificadoEm_Hint=Verificado Em,usuarioVerificacao=Verificado Por"/>
                                                                            <f:attribute name="prefixo" value="ClientesVerificados"/>
                                                                            <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                                                                        </a4j:commandLink>
                                                                    </rich:column>
                                                                </h:panelGrid>
                                                                <rich:dataTable id="item" width="100%"
                                                                                styleClass="tabelaSimplesCustom"
                                                                                value="#{ClientesVerificadosRelControle.listaApresentar}"
                                                                                rows="#{ClientesVerificadosRelControle.numeroPaginacao}" var="resumoPessoa"
                                                                                rowKeyVar="status">

                                                                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                                                                    <rich:column width="15%" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{resumoPessoa.matricula}" filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Matricula"/>
                                                                        </f:facet>
                                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.matricula}"/>
                                                                    </rich:column>

                                                                    <rich:column width="35%" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{resumoPessoa.nome}" filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Nome"/>
                                                                        </f:facet>
                                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.nome}"/>
                                                                    </rich:column>

                                                                    <rich:column width="10%" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{resumoPessoa.existeContratoRenovado}" filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="RENOVOU CONTRATO"/>
                                                                        </f:facet>
                                                                        <h:outputText rendered="#{resumoPessoa.existeContratoRenovado}" styleClass="texto-font texto-size-14-real texto-cor-cinza" value="SIM"/>
                                                                        <h:outputText rendered="#{!resumoPessoa.existeContratoRenovado}" styleClass="texto-font texto-size-14-real texto-cor-cinza" value="NÃO"/>
                                                                    </rich:column>

                                                                    <rich:column width="20%" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{resumoPessoa.verificadoEm_Ordenacao}" filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="VERIFICADO EM"/>
                                                                        </f:facet>
                                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.verificadoEm_Hint}"/>
                                                                    </rich:column>

                                                                    <rich:column width="10%" styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{resumoPessoa.usuarioVerificacao}" filterEvent="onkeyup">
                                                                        <f:facet name="header">
                                                                            <h:outputText styleClass="texto-upper texto-font texto-size-14-real texto-cor-cinza texto-bold" value="VERIFICADO POR"/>
                                                                        </f:facet>
                                                                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{resumoPessoa.usuarioVerificacao}"/>
                                                                    </rich:column>

                                                                    <rich:column width="5%"
                                                                                 headerClass="col-text-align-center"
                                                                                 styleClass="col-text-align-center">
                                                                        <a4j:commandLink id="visualizarAluno"
                                                                                         styleClass="linkPadrao texto-cor-azul texto-size-16-real"
                                                                                         action="#{ClientesVerificadosRelControle.irParaTelaCliente}"
                                                                                         oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);">
                                                                            <f:param name="state" value="AC"/>
                                                                            <i class="fa-icon-search"></i>
                                                                        </a4j:commandLink>
                                                                    </rich:column>
                                                                </rich:dataTable>

                                                                <rich:datascroller styleClass="scrollPureCustom"
                                                                                   binding="#{DataScrollerControle.dataScroller}"
                                                                                   renderIfSinglePage="false"
                                                                                   rendered="#{ClientesVerificadosRelControle.mostrarPaginacao}"
                                                                                   align="center" for="form:item"
                                                                                   maxPages="20" id="scitems"/>

                                                            </h:panelGrid>
                                                        </td>
                                                        <td align="left" valign="top"
                                                            background="images/box_centro_right.gif">
                                                            <img src="images/shim.gif">
                                                        </td>
                                                    </tr>
                                                </table>
                                            </h:panelGroup>
                                        </td>
                                    </tr>
                                </table>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </h:form>
    </body>
    </html>
</f:view>

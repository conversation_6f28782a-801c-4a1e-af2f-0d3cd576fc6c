<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
     .tabelaCancelamento > tbody > tr > td:nth-child(2){
        width: 100%;
     }

    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
     .to-uper-case{
         text-transform: uppercase;
     }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <c:set var="titulo" scope="session" value="Cancelamento"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>


    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
    <h:form id="form">
        <h:panelGrid columns="2" id="painelCancelamento" styleClass=" tabelaCancelamento" width="100%">
            <h:panelGrid columns="1" width="100%" style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{ClienteControle.clienteVO.pessoa.nome}"/>
                <%-- <h:outputText value="Contrato(s) Cancelados" styleClass="tituloCamposNegritoMaior" />--%>
                <rich:dataTable id="listaContrato" width="100%" border="0" rows="3" cellspacing="0" columnClasses="col-text-align-left" headerClass="col-text-align-left" styleClass="tabelaSimplesCustom"
                                 value="#{CancelamentoContratoControle.cancelamentoContratoVO.listaContratos}" var="contrato">
                    <rich:column >
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CONTRATO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DATA INÍCIO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaDe}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DATA TÉRMINO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold col-text-align-right" value="VALOR"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.valorBaseCalculo}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>
                <h:panelGrid id="painelTransferencia" width="100%" >
                    <h:panelGrid columns="1" width="100%"
                                 rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.transferencia
                                             || (CancelamentoContratoControle.cancelamentoContratoVO.devolucao && !CancelamentoContratoControle.contratoVO.empresa.retrocederValorMensalPlanoCancelamento)}">
                        <table id="tableDadosCancelamento" width="100%" border="0"  align="center" cellpadding="0" cellspacing="0" style="margin-right:30px;margin-bottom:30px;padding:10px;">
                            <tr>
                                <td align="center" valign="top" ><div style="clear:both;" class="text">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                                                      value="VALOR BASE PARA TRANSFERÊNCIA"
                                                      rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.devolucao}"
                                                      />
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                                                      value="VALOR BASE PARA DEVOLUÇÃO"
                                                      rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.devolucao}"
                                                      />

                                        <h:panelGrid columns="2" style="margin-top: 10px;">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR BASE #{MovPagamentoControle.empresaLogado.moeda}"/>
                                            <h:outputText id="vlrBaseTrans" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorBaseContrato}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGrid>

                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" valign="top" >
                                    <h:panelGrid  id="valorBase" rendered="#{!CancelamentoContratoControle.contratoVO.vendaCreditoTreino}" columns="1" columnClasses="left, right" width="100%" border="0" cellspacing="0" cellpadding="0" styleClass="textsmall"  >
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText value="DIAS CONTRATO: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                                            <h:outputText id="ttdiasContrato" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.nrDiasContrato}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" dias" />
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText value="DIAS UTILIZADOS PELO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                                            <h:outputText id="ttdiasUsados" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.nrDiasUtilizadosPeloClienteContrato}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" dias" />
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText value="DIAS RESTANTES: " styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" />
                                            <h:outputText id="ttdiasRestantes" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.nrDiasRestamContrato}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" dias" />
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR DIA: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                            <h:outputText id="vlrDia" styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" #{CancelamentoContratoControle.cancelamentoContratoVO.valorDiaContratoValorBase}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UTILIZADO PELO CLIENTE: " />
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                            <h:outputText id="vlrUsado" styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" #{CancelamentoContratoControle.cancelamentoContratoVO.valorUtilizadoPeloClienteBase}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.existeContratoRenovacao}">
                                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CRÉDITO CONTRATOS RENOVADOS: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalBaseContratoRenovacao}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup >
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CRÉDITO RESTANTE: "
                                                          rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.credito}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DÉBITO RESTANTE: "
                                                          rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.credito}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                            <h:outputText id="vlrRestante" styleClass="texto-size-14-real texto-cor-cinza texto-font " value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorCreditoRestanteContratoValorBase}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    </h:panelGrid>

                                    <h:panelGrid id="gridVendaCreditoTreinoTransf" columns="1" rendered="#{CancelamentoContratoControle.contratoVO.vendaCreditoTreino}" columnClasses="left, right"  width="100%" border="0" cellspacing="0" cellpadding="0" styleClass="textsmall" >
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE CRÉDITO COMPRA: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeCreditoCompra}"/>
                                        </h:panelGroup>
                                        <h:panelGroup rendered="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeTransferenciaSaldo != null}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE TRANSFERÊNCIA DE SALDO: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeTransferenciaSaldo}"/>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{!CancelamentoContratoControle.contratoVO.plano.creditoTreinoNaoCumulativo}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE CRÉDITO UTILIZADO: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeCreditoUtilizadoMensal}"/>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{CancelamentoContratoControle.contratoVO.plano.creditoTreinoNaoCumulativo}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE CRÉDITO UTILIZADO: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeCreditoUtilizado}"/>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE CRÉDITO RESTANTE: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeCreditoDisponivel}"/>
                                        </h:panelGroup>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UNITÁRIO CRÉDITO COMPRA #{MovPagamentoControle.empresaLogado.moeda}: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUnitario}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <h:panelGroup rendered="#{CancelamentoContratoControle.devolverTransferenciaSaldoCredito}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UNITÁRIO CRÉDITO TRANSFERÊNCIA SALDO #{MovPagamentoControle.empresaLogado.moeda}: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUnitarioTransferenciaSaldo}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>


                                        <h:panelGroup rendered="#{!CancelamentoContratoControle.contratoVO.plano.creditoTreinoNaoCumulativo}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UTILIZADO #{MovPagamentoControle.empresaLogado.moeda}: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUtilizadoMensal}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>

                                        </h:panelGroup>


                                        <h:panelGroup rendered="#{CancelamentoContratoControle.contratoVO.plano.creditoTreinoNaoCumulativo}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UTILIZADO #{MovPagamentoControle.empresaLogado.moeda}: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUtilizadoTotal}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>

                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CRÉDITO RESTANTE #{MovPagamentoControle.empresaLogado.moeda}: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.valorCreditoTreinoRestante}">
                                                <f:converter converterId="FormatadorNumerico" />
                                           </h:outputText>
                                        </h:panelGroup>
                                    </h:panelGrid>

                                </td>
                            </tr>
                        </table>
                    </h:panelGrid>
                
                    <h:panelGrid columns="1" width="100%" 
                                 rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.devolucao && CancelamentoContratoControle.contratoVO.empresa.retrocederValorMensalPlanoCancelamento && !CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">
                        <table id="tableDadosCancelamentoDev" width="100%" border="0" align="center" cellpadding="0" cellspacing="0" style="margin-right:30px;padding:10px;">
                            <tr>
                                <td align="left" valign="top" ><div style="clear:both;" class="text">
                                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" value="VALOR BASE PARA DEVOLUÇÃO "/>
                                        <h:panelGrid columns="2" style="margin-top: 10px;">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{CancelamentoContratoControle.labelValorBaseMensal}"/>
                                            <h:outputText id="vlrBaseDevolucao" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.valorBaseMensal}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGrid>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" valign="top">
                                    <h:panelGrid id="valorMensal" columns="1" rendered="#{!CancelamentoContratoControle.contratoVO.vendaCreditoTreino}" columnClasses="left, right"  width="100%" border="0" cellspacing="0" cellpadding="0" styleClass="textsmall" >
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DIAS CONTRATO: " style="font-weight: bold" />
                                            <h:outputText id="ttDiasContratoDev" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.nrDiasContrato}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" dias" />
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DIAS UTILIZADOS PELO CLIENTE: "  />
                                            <h:outputText id="ttDiasUsadosDev" styleClass="texto-size-14-real texto-cor-cinza texto-font " value="#{CancelamentoContratoControle.cancelamentoContratoVO.nrDiasUtilizadosPeloClienteContrato}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" dias" />
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DIAS RESTANTES: "/>
                                            <h:outputText id="ttDiasRestanteDev"  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.nrDiasRestamContrato}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value=" dias" />
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR DIA: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                            <h:outputText id="vlrDiaDev"  styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorDiaContratoValorMensal}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UTILIZADO PELO CLIENTE: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                            <h:outputText id="vlrUsadoDev" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value=" #{CancelamentoContratoControle.cancelamentoContratoVO.valorUtilizadoPeloClienteMensal}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <h:panelGroup rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.existeContratoRenovacao}">
                                            <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CRÉDITO CONTRATOS RENOVADOS: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorTotalBaseContratoRenovacao}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CRÉDITO RESTANTE:"
                                                          rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.credito}"/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="DÉBITO RESTANTE:"
                                                          rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.credito}"/>

                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                                            <h:outputText id="vlrRestanteDev" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.cancelamentoContratoVO.valorCreditoRestanteContratoValorMensal}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>
                                    </h:panelGrid>

                                    <h:panelGrid id="gridVendaCreditoTreinoDevolucao" columns="1" rendered="#{CancelamentoContratoControle.contratoVO.vendaCreditoTreino}" columnClasses="left, right"  width="100%" border="0" cellspacing="0" cellpadding="0" styleClass="textsmall" >
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE CRÉDITO COMPRA: " />
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeCreditoCompra}"/>
                                        </h:panelGroup>
                                        <h:panelGroup rendered="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeTransferenciaSaldo != null}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE TRANSFERÊNCIA DE SALDO: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeTransferenciaSaldo}"/>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{!CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.creditoTreinoNaoCumulativo}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE CRÉDITO UTILIZADO: " />
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeCreditoUtilizadoMensal}"/>
                                        </h:panelGroup>

                                        <h:panelGroup rendered="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.creditoTreinoNaoCumulativo}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE CRÉDITO UTILIZADO: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeCreditoUtilizado}"/>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="QUANTIDADE CRÉDITO RESTANTE: " />
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.quantidadeCreditoDisponivel}"/>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UNITÁRIO CRÉDITO COMPRA #{MovPagamentoControle.empresaLogado.moeda}: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUnitarioCompraOriginal}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <h:panelGroup rendered="#{CancelamentoContratoControle.devolverTransferenciaSaldoCredito}">
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UNITÁRIO CRÉDITO TRANSFERÊNCIA SALDO #{MovPagamentoControle.empresaLogado.moeda}: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUnitarioTransferenciaSaldo}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="VALOR UTILIZADO #{MovPagamentoControle.empresaLogado.moeda}: " />
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.contratoDuracaoCreditoTreinoCancelar.valorUtilizadoMensal}">
                                                <f:converter converterId="FormatadorNumerico" />
                                            </h:outputText>
                                        </h:panelGroup>
                                        <h:panelGroup>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="CRÉDITO RESTANTE #{MovPagamentoControle.empresaLogado.moeda}: "/>
                                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{CancelamentoContratoControle.valorCreditoTreinoRestante}">
                                                <f:converter converterId="FormatadorNumerico" />
                                             </h:outputText>
                                        </h:panelGroup>
                                    </h:panelGrid>
                                </td>
                            </tr>
                        </table>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid  style="top:10px;" width="100%">

                    <c:if test="${!CancelamentoContratoControle.contratoVO.cancelamentoAntecipado}">

                    <h:outputText value="O que deseja fazer com o cancelamento?" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                                  style=""/>
                    <h:selectOneRadio id="tipoDevolucaoCancelamento" styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" style="" value="#{CancelamentoContratoControle.cancelamentoContratoVO.tipoDevolucaoCancelamento}" >
                        <f:selectItems value="#{CancelamentoContratoControle.listaSelectItemTipoDevolucaoCancelamento}"/>
                        <a4j:support event="onclick" action="#{CancelamentoContratoControle.obterValoresCancelamento}"
                                     oncomplete="#{CancelamentoContratoControle.cancelamentoContratoVO.abrirRichModal}"
                                     reRender="painelTransferencia"/>
                    </h:selectOneRadio>

                    </c:if>

                    <h:panelGrid width="100%" columns="2" >
                        <%--h:panelGroup style="position:relative; top:190px; left:60px;"--%>
                        <h:panelGroup>
                            <a4j:commandLink id="proximo"
                                             action="#{CancelamentoContratoControle.validarConfirmacaoCancelamento}"
                                             style="float: right;"
                                             title="Próximo Passo" styleClass="pure-button pure-button-primary"
                                             oncomplete="#{CancelamentoContratoControle.msgAlert}#{CancelamentoContratoControle.mensagemNotificar}"
                                             reRender="mdlMensagemGenerica, mensagem, panelConteudo">
                                <i class="fa-icon-arrow-right"></i>
                                <a4j:support event="onclick" reRender="panel"/>
                            </a4j:commandLink>
                            <rich:spacer width="7" style="float: right"/>
                            <h:commandLink id="voltar" style="float: right;" title="Voltar Passo" action="#{CancelamentoContratoControle.voltarTelaCancelamento}" styleClass="pure-button">
                                <i class="fa-icon-arrow-left"></i>
                            </h:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                    </div>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>

    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>

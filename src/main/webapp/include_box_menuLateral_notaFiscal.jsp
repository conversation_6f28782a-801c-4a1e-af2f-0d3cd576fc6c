<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@ taglib uri='http://java.sun.com/jsp/jstl/core' prefix='c'%>

<style>

    .labelFiltros {
        font-weight: bold;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        text-decoration: none;
        color: #333;
        line-height: 125%;
    }

    .panelFiltros {
        display: grid;
        /*padding: 10px 10px 10px 10px;*/
        padding: 10px 10px 10px 10px;
    }

    .panelBtnConsulta {
        display: table;
        width: 100%;
        padding-bottom: 20px;
        padding-top: 20px;
        text-align: center !important;
    }

    .inputFiltroNotaFiscal {
        font-family: <PERSON><PERSON>, <PERSON>lvetica, sans-serif !important;
        font-size: 11px !important;
        height: 100% !important;
    }

    .menuLateralNota{
        background-color: #fff;
        display: inline-block;
        width: 250px;
        padding-top: 20px;
    }

    .novoZwUi {
        margin-left: 10px;
        padding-left: 266px;
        margin-top: 82px;
    }

</style>

<h:panelGroup id="menuLateralNotaFiscal" layout="block"
              style="display: #{SuperControle.menuZwUi ? 'initial' : 'none'}"
              styleClass="menuLateralNota tudo menuLateralNotaFiscal #{SuperControle.menuZwUi ? 'novoZwUi' : ''}">


    <h:panelGroup layout="block" styleClass="grupoMenuLateral grupoMenuLateralNotaFiscal"
                  id="grupoMenuLateralNotaFiscal">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo"
                      rendered="false">
            <i class="fa-icon-filter"></i>
            Filtros
        </h:panelGroup>

        <h:panelGroup layout="block"
                      id="filtroNota">

            <c:if test="${NotaFiscalControle.permissaoConsultaTodasEmpresas}">
                <h:panelGroup layout="block" styleClass="panelFiltros">
                    <h:selectOneMenu value="#{NotaFiscalControle.filtroEmpresa}"
                                     style="height: 100%; width: 100%;">
                        <f:selectItems value="#{NotaFiscalControle.listaEmpresas}"/>
                        <a4j:support event="onchange"
                                     action="#{NotaFiscalControle.carregarListaConfiguracoesNota}"
                                     reRender="form:grupoMenuLateralNotaFiscal"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </c:if>

            <h:panelGroup layout="block" styleClass="panelFiltros"
                          rendered="#{NotaFiscalControle.permissaoConsultaTodasEmpresas || NotaFiscalControle.apresentarFiltroEmpresaEnotas}">
                <h:outputText styleClass="labelFiltros"
                              value="Config. de Emissão"/>
                <h:selectOneMenu id="idEmpresaEnotas"
                                 style="height: 100%; width: 100%;"
                                 styleClass="form"
                                 value="#{NotaFiscalControle.filtro.configuracaoNotaFiscal}">
                    <f:selectItems value="#{NotaFiscalControle.listaEmpresasEnotas}"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="panelFiltros"
                          rendered="#{NotaFiscalControle.apresentarFiltroTipoNota}">
                <h:outputText styleClass="labelFiltros"
                              value="Tipo Nota"/>
                <h:selectOneMenu id="tipoNota"
                                 style="height: 100%; width: 100%;"
                                 styleClass="form"
                                 value="#{NotaFiscalControle.filtro.tipoNota}">
                    <f:selectItems value="#{NotaFiscalControle.listaTipoNotaFiscal}"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="panelFiltros">
                <h:outputText styleClass="labelFiltros"
                              value="Número Nota"/>
                <h:inputText styleClass="inputFiltroNotaFiscal"
                             value="#{NotaFiscalControle.filtro.numeroNota}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="panelFiltros">
                <h:outputText styleClass="labelFiltros"
                              value="Razão Social do Cliente"/>
                <h:inputText styleClass="inputFiltroNotaFiscal"
                             value="#{NotaFiscalControle.filtro.razaoSocial}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="panelFiltros">
                <h:outputText styleClass="labelFiltros"
                              value="CPF / CNPJ"/>
                <h:inputText styleClass="inputFiltroNotaFiscal"
                             value="#{NotaFiscalControle.filtro.cpfCnpj}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="panelFiltros">
                <h:outputText styleClass="labelFiltros"
                              style="vertical-align:middle;" value="Status"/>
                <h:selectOneMenu id="filtroStatusNota"
                                 style="height: 100%; width: 100%;"
                                 styleClass="form"
                                 value="#{NotaFiscalControle.filtro.statusNota}">
                    <f:selectItems value="#{NotaFiscalControle.listaStatusNotaFiscal}"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="panelFiltros" style="display: flex; padding-top: 0px; padding-bottom: 0px; align-items: center;">
                <h:selectBooleanCheckbox id="apresentarExcluidas"
                                         styleClass="form"
                                         value="#{NotaFiscalControle.filtro.apresentarExcluidas}"/>
                <h:outputText styleClass="labelFiltros"
                              style="padding-left: 5px"
                              value="Apresentar excluídas"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="panelFiltros" style="display: flex; padding-top: 0px; padding-bottom: 0px; align-items: center;">
                <h:selectBooleanCheckbox id="apresentarNotaAlteradaStatusManual"
                                         styleClass="form"
                                         value="#{NotaFiscalControle.filtro.apresentarNotaAlteradaStatusManual}"/>
                <h:outputText styleClass="labelFiltros"
                              style="padding-left: 5px"
                              value="Apresentar notas alteradas manualmente"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="panelFiltros">
                <h:outputText styleClass="labelFiltros" value="Dt. Registro"/>

                <h:panelGroup layout="block" id="panelDataRegFiltrosNotaFiscal" style="display: inline-flex;">
                    <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important; width: 50%;">
                        <rich:calendar id="dataRegistroInicio"
                                       value="#{NotaFiscalControle.filtro.dataRegistroInicio}"
                                       inputSize="6"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>

                    <h:panelGroup styleClass="dateTimeCustom"
                                  style="font-size: 11px !important; padding-left: 10px; width: 50%;">
                        <rich:calendar id="dataRegistroFim"
                                       value="#{NotaFiscalControle.filtro.dataRegistroFim}"
                                       inputSize="6"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="panelFiltros">
                <h:outputText styleClass="labelFiltros" value="Dt. Emissão"/>

                <h:panelGroup layout="block" id="panelDataFiltrosNotaFiscal" style="display: inline-flex;">
                    <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important; width: 50%;">
                        <rich:calendar id="dataEmissaoInicio"
                                       value="#{NotaFiscalControle.filtro.dataEmissaoInicio}"
                                       inputSize="6"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"
                                       jointPoint="top-left"
                                       direction="top-right"/>
                    </h:panelGroup>

                    <h:panelGroup styleClass="dateTimeCustom"
                                  style="font-size: 11px !important; padding-left: 10px; width: 50%;">
                        <rich:calendar id="dataEmissaoFim"
                                       value="#{NotaFiscalControle.filtro.dataEmissaoFim}"
                                       inputSize="6"
                                       inputClass="form"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"
                                       jointPoint="top-left"
                                       direction="top-right"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="panelBtnConsulta">
            <a4j:commandLink id="limparFiltros"
                             action="#{NotaFiscalControle.limparFiltros}"
                             reRender="form:grupoMenuLateralNotaFiscal"
                             value="Limpar"
                             style="margin-right: 15px;"
                             styleClass="botoes nvoBt btSec"/>

            <a4j:commandLink id="consultarEnotas"
                             action="#{NotaFiscalControle.consultarNotasFiscais}"
                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                             oncomplete="#{NotaFiscalControle.mensagemNotificar}"
                             reRender="form:panelENotas, form:grupoMenuLateralNotaFiscal, form:panelBtnSuperior"
                             value="Pesquisar"
                             styleClass="botoes nvoBt"/>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

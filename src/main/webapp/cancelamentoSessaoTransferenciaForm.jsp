<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Cancelamento" />
    </title>

    <c:set var="titulo" scope="session" value="Cancelamento"/>
    <rich:modalPanel id="panel" width="350" height="100" showWhenRendered="#{CancelamentoSessaoControle.cancelamentoSessaoVO.mensagemErro}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panel" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup>
                <h:graphicImage value="/imagens/erro.png"/>
                <rich:spacer width="10" />
                <h:outputText id="msgCancelamentoDet" styleClass="mensagemDetalhada" value="#{CancelamentoSessaoControle.mensagemDetalhada}"/>
            </h:panelGroup>
        </h:panelGrid>
    </rich:modalPanel>

    <h:form id="form">
        <h:panelGrid columns="2" width="100%" styleClass="tabMensagens">

            <h:panelGrid columns="1" >
                <img src="./imagens/lateralWizardCancelamentoMaior.png" >
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" cellpadding="2" >
                <h:panelGrid columns="2">
                    <h:outputText value="Nome do Cliente: " styleClass="tituloCampos" style="font-weight: bold;"  />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{CancelamentoSessaoControle.clienteVO.pessoa.nome}"/>
                    </h:panelGroup>
                    <h:outputText value="Valor Pago pelo Cliente " styleClass="tituloCamposNegrito" />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorPago" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorPagoPeloCliente}" style="font-weight: bold; color:green" styleClass="tituloCampos">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:outputText value="Valor Utilizado pelo Cliente " styleClass="tituloCamposNegrito" />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorCobrado" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorUtilizadoCliente}" style="font-weight: bold; color: red" styleClass="tituloCampos">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:outputText value="Saldo da Conta Corrente do Cliente " styleClass="tituloCamposNegrito" />
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="saldoCC" value="#{CancelamentoSessaoControle.clienteVO.saldoContaCorrente}" styleClass="#{CancelamentoSessaoControle.definirCorSaldoContaCorrente}" style="font-weight: bold;">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:outputText value="Valor da Transferencia: " styleClass="tituloCamposNegrito"/>
                    <h:panelGroup>
                        <rich:spacer width="12"/>
                        <h:outputText styleClass="tituloCampos" value="#{MovPagamentoControle.empresaLogado.moeda} " />
                        <h:outputText id="valorTransferido" value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.valorTransferidoCliente}" styleClass="#{CancelamentoSessaoControle.cancelamentoSessaoVO.definirCorValorTransferidoCliente}" style="font-weight: bold;">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid rendered="#{CancelamentoSessaoControle.apresentarBotaoCancelar}">
                    <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                        <h:outputText value="O que deseja fazer com o valor a ser transferido?" styleClass="tituloCampos"
                                      style="font-weight: bold"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:selectOneRadio id="tipoTransferenciaCancelamento" styleClass="tituloCampos"  value="#{CancelamentoSessaoControle.cancelamentoSessaoVO.tipoTranferenciaCancelamento}" >
                            <f:selectItems value="#{CancelamentoSessaoControle.listaSelectItemTipoTransferenciaCancelamento}"/>
                            <a4j:support event="onclick" action="#{CancelamentoSessaoControle.obterTipoTransferencia}" reRender="valorTransferido"/>
                        </h:selectOneRadio>
                    </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid
                        id="panelMensagem"
                        columns="3"
                        width="100%"
                        columnClasses="colunaEsquerda" >
                        <h:panelGrid
                            columns="1"
                            width="100%">

                            <h:outputText value=" " />

                        </h:panelGrid>
                        <h:commandButton
                            rendered="#{CancelamentoSessaoControle.sucesso}"
                            image="./imagens/sucesso.png" />
                        <h:commandButton
                            rendered="#{CancelamentoSessaoControle.erro}"
                            image="./imagens/erro.png" />
                        <h:panelGrid
                            columns="1"
                            width="100%" >
                            <h:outputText
                                styleClass="mensagem"
                                value="#{CancelamentoSessaoControle.mensagem}" />
                            <h:outputText
                                styleClass="mensagemDetalhada"
                                value="#{CancelamentoSessaoControle.mensagemDetalhada}" />
                        </h:panelGrid>
                    </h:panelGrid>
                    
                <h:panelGrid width="550px" columns="2"  style="position:relative; top:50px;" >
                    <h:panelGrid width="350px"/>
                    <h:panelGrid width="50px">
                        <%--h:panelGroup style="position:relative; top:190px; left:60px;"--%>
                        <h:panelGroup>
                            <h:commandButton id="voltar" alt="Voltar Passo" action="#{CancelamentoSessaoControle.voltarTelaCancelamento}" image="./imagens/botaoVoltar.png" />
                            <rich:spacer width="7"/>
                        <a4j:commandButton rendered="#{CancelamentoSessaoControle.apresentarBotaoCancelar}"
                            id="avancar"
                            value="Avançar"
                            action="#{CancelamentoSessaoControle.validarTransferenciaCliente}"
                            image="./imagens/botaoProximo.png" reRender="panelMensagem"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </h:panelGrid>
    </h:form>

</f:view>
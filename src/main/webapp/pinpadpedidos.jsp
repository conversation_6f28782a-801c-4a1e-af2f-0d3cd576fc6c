<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="./includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="../bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>
<title>Relatório de Transações Pix</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Relatório de pedidos Pinpad"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}integracao-com-stone-connect/"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="./topoReduzido_material.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="./pages/ce/includes/topoReduzido.jsp"/>
            </c:if>
        </f:facet>

        <h:form id="form">
            <h:panelGroup layout="block"
                          style="display: flex; justify-content: center; align-items: center; flex-direction: column;margin-top: 30px;">
                <h:panelGroup layout="block" style="display: flex; justify-content: center; align-items: center;">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_de}"/>
                    <h:panelGroup layout="block" style="padding-left: 12px; padding-right: 12px;">
                        <rich:calendar id="dataInicio"
                                       value="#{PinpadPedidoRelatorioControle.dataInicial}"
                                       inputSize="10"
                                       style="padding-left: 12px; height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ate}"/>
                    <h:panelGroup layout="block" style="padding-left: 12px;">
                        <rich:calendar id="dataTermino"
                                       value="#{PinpadPedidoRelatorioControle.dataFinal}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"/>
                        <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="container-botoes" style="margin-top: 30px;">
                    <a4j:commandLink id="btnConsultarPedidosPinpad"
                                     reRender="form"
                                     action="#{PinpadPedidoRelatorioControle.consultarRel}"
                                     oncomplete="#{PinpadPedidoRelatorioControle.onComplete}"
                                     styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                     style="margin-bottom: 20px;"
                                     value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>

                    <a4j:commandLink id="btnAtualizarTodosPedidosPinpad"
                                     reRender="form"
                                     action="#{PinpadPedidoRelatorioControle.acaoAtualizarTodosPedidos}"
                                     oncomplete="#{PinpadPedidoRelatorioControle.onComplete}"
                                     styleClass="botaoPrimario texto-size-16-real texto-cor-azul"
                                     style="margin-bottom: 20px; margin-left: 20px;"
                                     value="Atualizar Pedidos" title="Atualiza todos os Pedidos Pinpad filtrados, que estão com status Aguardando."/>
                </h:panelGroup>

                <h:panelGrid id="tabela" rendered="#{not empty PinpadPedidoRelatorioControle.lista}" columns="1"
                             width="100%" cellpadding="0" cellspacing="0">
                    <h:panelGrid columns="1" width="100%">
                        <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                        style="line-height: 45px; white-space: nowrap; margin-bottom: 45px"
                                        value="#{PinpadPedidoRelatorioControle.lista}" rows="50" var="item"
                                        rowKeyVar="status">
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Código"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{item.codigo}"/>
                            </rich:column>
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Id"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{item.idExterno}"/>
                            </rich:column>
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Nome do aluno"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                              value="#{item.pessoaVO.nome}"/>
                            </rich:column>
                            <rich:column styleClass="col-text-align-center"
                                         headerClass="col-text-align-center">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Valor"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{item.valorApresentar}">
                                </h:outputText>
                            </rich:column>
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Tipo"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{item.pinpad.nome}"/>
                            </rich:column>
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Origem"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                              value="#{item.origem.descricao}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.status.descricao}" styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Status"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                              title="#{item.status.descricao}"
                                              value="#{item.status.descricao}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.dataRegistro}" styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Data/Hora"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                                              value="#{item.dataRegistroApresentar}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.dataRegistro}" styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                                  value="Opções"/>
                                </f:facet>
                                <h:panelGroup id="panelAcoes" style="display: inline-flex">
                                    <a4j:commandLink style="margin-right: 10px; color: #444; font-size: 17px"
                                                     rendered="#{item.permiteAlterar}"
                                                     styleClass="tooltipster"
                                                     reRender="modalAlterarPedido"
                                                     action="#{PinpadPedidoRelatorioControle.atualizarIndividualPedido}"
                                                     oncomplete="#{PinpadPedidoRelatorioControle.onComplete}"
                                                     title="Alterar Pedido">
                                        <i class="fa-icon-refresh"></i>
                                    </a4j:commandLink>

                                    <%--ENVIO--%>
                                    <a4j:commandLink rendered="#{not empty item.paramsEnvio}"
                                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                                     styleClass="tooltipster"
                                                     title="Visualizar os parâmetros de envio"
                                                     reRender="modalParamsPedido"
                                                     oncomplete="#{PinpadPedidoRelatorioControle.onComplete}"
                                                     actionListener="#{PinpadPedidoRelatorioControle.exibirParams}">
                                        <f:attribute name="params" value="envio"/>
                                        <f:attribute name="item" value="#{item}"/>
                                        <i class="fa-icon-share-alt"></i>
                                    </a4j:commandLink>

                                    <%--RESPOSTA--%>
                                    <a4j:commandLink rendered="#{not empty item.paramsResp}"
                                                     style="margin-right: 10px; color: #444; font-size: 17px"
                                                     styleClass="tooltipster"
                                                     title="Visualizar os parâmetros de resposta"
                                                     reRender="modalParamsPedido"
                                                     oncomplete="#{PinpadPedidoRelatorioControle.onComplete}"
                                                     actionListener="#{PinpadPedidoRelatorioControle.exibirParams}">
                                        <f:attribute name="params" value="resposta"/>
                                        <f:attribute name="item" value="#{item}"/>
                                        <i class="fa-icon-reply"></i>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>


    <rich:modalPanel id="modalAlterarPedido" domElementAttachment="parent"
                     autosized="true" shadowOpacity="false" width="500"
                     styleClass="novaModal">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atualizar Pedidos"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalAlterarPedido"/>
                <rich:componentControl for="modalAlterarPedido" attachTo="hidelinkModalAlterarPedido"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formModalCancelarTransacao" ajaxSubmit="true">
            <h:panelGroup id="panelModalAlterarPedido"
                          layout="block" style="padding: 20px">

                <h:panelGroup layout="block"
                              style="display: flex;align-items: center;justify-content: center;padding-bottom: 15px;">
                    <h:outputText style="font-size: 12px; padding-right: 5px" value="Status:"/>
                    <h:selectOneMenu id="statusAtualizar"
                                     value="#{PinpadPedidoRelatorioControle.statusAtualizar}"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form"
                                     style="font-size: 14px;top:0px;">
                        <f:selectItems
                                value="#{PinpadPedidoRelatorioControle.listaSelectItemStatus}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelBtnAlterarPedido"
                              style="text-align: center; padding: 25px 0 20px 0;">
                    <a4j:commandLink id="btnAlterarPedido"
                                     reRender="tabela"
                                     value="Confirmar"
                                     action="#{PinpadPedidoRelatorioControle.alterarPedido}"
                                     oncomplete="#{PinpadPedidoRelatorioControle.onComplete}"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>
            </h:panelGroup>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="modalParamsPedido" width="500"
                     autosized="true" styleClass="novaModal"
                     shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Pedido: #{PinpadPedidoRelatorioControle.pinPadPedidoVO.idExterno}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalParams"/>
                <rich:componentControl for="modalParamsPedido" attachTo="hidelinkModalParams"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup>
            <c:if test="${empty PinpadPedidoRelatorioControle.listaParametrosSelecionado}">
                <h:panelGrid columns="2">
                    <h:graphicImage value="images/warning.png"/>
                    <h:outputText styleClass="mensagemDetalhada" value="Não há parâmetros."/>
                </h:panelGrid>
            </c:if>
            <h:panelGroup layout="block" style="height: 400px; overflow-y: scroll; overflow-x: hidden;">
                <rich:dataTable width="100%" value="#{PinpadPedidoRelatorioControle.listaParametrosSelecionado}"
                                var="obj">
                    <f:facet name="header">
                        <c:if test="${!empty PinpadPedidoRelatorioControle.listaParametrosSelecionado}">
                            <h:outputText value="Parâmetros utilizados"/>
                        </c:if>
                    </f:facet>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Atributo"/>
                        </f:facet>
                        <h:outputText value="#{obj.atributo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText style="font-weight:bold;" value="#{obj.valor}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

        </h:panelGroup>
    </rich:modalPanel>
</f:view>
<script>
    carregarTooltipster();
    document.getElementById("form:dataInicio").focus();
</script>

<%@include file="include_imports.jsp" %>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

<rich:modalPanel id="mdlHistoricoTurmas" styleClass="novaModal" autosized="true" shadowOpacity="true" width="670">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Hist�rico de faltas"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    style="cursor:pointer" id="hidemdlHistoricoTurmas"/>
            <rich:componentControl for="mdlHistoricoTurmas" attachTo="hidemdlHistoricoTurmas" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formHistoricoTurmas" styleClass="font-size-Em-max">

        <rich:dataTable value="#{TelaClienteControle.contratoSelecionado.historicoTurmasContrato.faltas}"
                        rows="10" columnClasses="esquerda, centralizado, centralizado, direita"
                        id="listaAlunoFaltas"
                        rowKeyVar="status"
                        width="100%"
                        var="item" styleClass="tabelaSimplesCustom">
            <rich:column sortBy="#{item.titulo}" >
                <f:facet name="header">
                    <h:outputText value="Turma"/>
                </f:facet>
                <h:outputText value="#{item.titulo}"/>
            </rich:column>



            <rich:column>
                <f:facet name="header">
                    <h:outputText value="Dia"/>
                </f:facet>
                <h:outputText value="#{item.dataAulaApresentar}"/>
            </rich:column>

            <rich:column>
                <f:facet name="header">
                    <h:outputText value="Hor�rio"/>
                </f:facet>
                <h:outputText value="#{item.horario}"/>
            </rich:column>

            <rich:column>
                <a4j:commandLink value="Desmarcar" styleClass="linkAzul"
                                 rendered="#{TelaClienteControle.contratoSelecionado.situacao == 'AT'}"
                                 ajaxSingle="true"
                                 id="demasmarAula1"
                                 actionListener="#{ClienteControle.prepararConsultaTurmaListenerDesmarcarNovo}"
                                 reRender="formAulaDesmarcada, form:panelOpracoesContrato"
                                 oncomplete="Richfaces.showModalPanel('modalPanelAulaDesmarcada');Richfaces.hideModalPanel('modalFaltasAluno');">
                    <f:attribute name="escolherTurma"
                                 value="true"/>
                    <f:attribute name="horarioTurma"
                                 value="#{item.id}"/>
                    <f:attribute name="horario"
                                 value="#{item.inicio}"/>
                </a4j:commandLink>
            </rich:column>
        </rich:dataTable>

        <h:panelGroup layout="block" style="margin: 20px auto;">
            <rich:datascroller align="center"
                               for="listaAlunoFaltas" maxPages="100"
                               id="scResultadoListaAlunoFaltas" />
        </h:panelGroup>


    </h:form>
</rich:modalPanel>
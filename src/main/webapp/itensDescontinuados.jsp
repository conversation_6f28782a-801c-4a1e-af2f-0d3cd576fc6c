<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">


<script type="text/javascript" src="bootstrap/jquery.js"></script>
<jsp:include page="include_head.jsp" flush="true"/>
<head>
    <script type="text/javascript">
        $.noConflict();
    </script>
    <link href="bootstrap/bootplus.css" rel="stylesheet">
    <style type="text/css">
        /* TOPO MENU */
        .rich-ddmenu-label, .rich-ddmenu-label-disabled {
            padding: 0 !important;
        }
    </style>

    <!-- Le javascript -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script type="text/javascript" src="bootstrap/bootstrap-transition.js"></script>
    <script type="text/javascript" src="bootstrap/bootstrap-carousel.js"></script>
    <script type="text/javascript">
        jQuery(document).ready(function ($) {
            $("#myCarousel").carousel({interval: 10000});
        });
    </script>
</head>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <c:if test="${!LoginControle.apresentarLinkZW && LoginControle.apresentarLinkEstudio}">
        <%response.sendRedirect("faces/pages/estudio/indexEstudio.jsp");%>
    </c:if>

    <%@include file="include_modal_expiracaoSenha.jsp" %>
    <h:form id="form">
        <html>

        <body class="zw">
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">

            <c:if test="${MenuControle.apresentarTopo}">
                <tr>
                    <td height="77" align="left" valign="top" class="bgtop">
                        <jsp:include page="include_top.jsp" flush="true"/>
                    </td>
                </tr>

                <tr>
                    <td height="48" align="left" valign="top" class="bgmenu">
                        <jsp:include page="include_menu.jsp" flush="true"/>
                    </td>
                </tr>
            </c:if>
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" height="100%" align="center" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">
                                <jsp:include page="include_menulateral_descontinuados.jsp" flush="true"/>
                                <jsp:include page="include_box_descricao.jsp" flush="true"/>
                            </td>
                            <td align="top" valign="top" height="100%" style="padding:7px 15px 0 20px;">
                                <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                                   id="btnAtualizaPagina"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td height="93" align="left" valign="top" class="bgrodape">
                    <jsp:include page="include_rodape.jsp" flush="true"/>
                </td>
            </tr>
        </table>
        </body>
        </html>
    </h:form>
</f:view>

<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>

<rich:modalPanel id="modalTrocaUnidade"
                 style="border-radius: 4.8px; padding-bottom: 40px !important; padding:30px; min-height: 140px; max-height: 600px; overflow: auto;"
                 domElementAttachment="parent"
                 autosized="true" styleClass="balloon-modal"
                 shadowOpacity="false" width="600"
                 moveable="false" resizeable="false"
                 onmaskclick="Richfaces.hideModalPanel('modalTrocaUnidade');">
    <h:panelGroup layout="block" id="panelModalTrocaUnidade">
        <div style="color: #51555a; margin-bottom: 20px; font-family: 'Nunito Sans', sans-serif; font-size: 24px;font-weight: 400;">
            Trocar Empresa
        </div>
        <div class="form-search-zw">
            <input class="caixa-pesquisa-troca-empresa" type="text" id="search"
                   onkeyup="filtrar()"
                   name="search" autofocus>
        </div>
        <h:panelGroup layout="block"
                      styleClass="titulo-troca-empresa">
            <div class="separador-linha-troca-empresa"></div>
            <h:outputText style="color:#51555a;vertical-align: top;text-decoration: none;"
                          value="#{InicioControle.empresaLogadoTrocaEmpresa}"/>
            <h:outputText
                    style="color: #0380E3; font-style: normal; font-weight: 700; font-size: 14px; margin-left: 25px; padding-inline-end: 7px;"
                    value="Empresa logada"/>
        </h:panelGroup>
        <div class="separador-linha-troca-empresa"></div>
        <a4j:repeat value="#{InicioControle.empresasTroca}"
                   var="perfil">
            <h:panelGroup layout="block"
                          styleClass="titulo-troca-empresa">
                <a4j:commandLink
                        rendered="#{!perfil.novo}"
                        onclick="Richfaces.hideModalPanel('modalTrocaUnidade');"
                        style="color:#51555a; vertical-align: top;text-decoration: none;font-family: 'Nunito Sans', sans-serif;font-size: 16px;font-weight: 400;"
                        value="#{perfil.nomeEmpresaApresentar}"
                        action="#{InicioControle.confirmarTrocaEmpresa}">
                    <f:setPropertyActionListener value="#{perfil.empresa}"
                                                 target="#{InicioControle.codigoEmpresa}"/>
                </a4j:commandLink>

                <a4j:commandLink
                        rendered="#{perfil.novo}"
                        style="color:#51555a; vertical-align: top;text-decoration: none;font-family: 'Nunito Sans', sans-serif;font-size: 16px;font-weight: 400;"
                        value="#{perfil.nomeEmpresaApresentar}"
                        oncomplete="#{InicioControle.onComplete}"
                        actionListener="#{InicioControle.confirmarTrocaEmpresaMultiChave}">
                    <f:attribute name="empresasTroca" value="#{perfil}"/>
                </a4j:commandLink>
                <div class="separador-linha-troca-empresa"></div>
            </h:panelGroup>
        </a4j:repeat>
        <div style="margin-top: 15px;
                                        display: flex;
                                        flex-direction: row-reverse;
                                        box-sizing: border-box;">
            <a class="botao-fechar-modal-troca-empresa"
               onclick="Richfaces.hideModalPanel('modalTrocaUnidade');" href="#">
                Fechar </a>
        </div>
    </h:panelGroup>
</rich:modalPanel>

<rich:modalPanel id="modalProcessoTrocaUnidade"
                 style="border-radius: 4.8px; padding-bottom: 40px !important; padding:30px; min-height: 140px; max-height: 600px; overflow: auto;"
                 domElementAttachment="parent"
                 autosized="true" styleClass="balloon-modal"
                 shadowOpacity="false"
                 moveable="false" resizeable="false"
                 onmaskclick="Richfaces.hideModalPanel('modalProcessoTrocaUnidade');">
    <h:panelGroup layout="block"
                  id="panelModalProcessoTrocaUnidade" style="width: max-content!important; text-align: center;">
        <div style="color: #51555a; margin-bottom: 40px; font-family: 'Nunito Sans', sans-serif; font-size: 24px;font-weight: 400;">
            Carregando unidade...
        </div>
        <h:panelGroup layout="block">
            <h:graphicImage url="/imagens/carregando.gif" style="border:none"/>
        </h:panelGroup>
    </h:panelGroup>
</rich:modalPanel>

<script>
    function filtrar() {
        var input, filter, li, a, i, txtValue;
        input = document.querySelector("#search");
        filter = input.value.toUpperCase();
        li = jQuery(".titulo-troca-empresa");
        for (i = 0; i < li.length; i++) {
            a = li[i];
            txtValue = a.textContent || a.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                li[i].style.display = "";
            } else {
                li[i].style.display = "none";
            }
        }
    }
</script>

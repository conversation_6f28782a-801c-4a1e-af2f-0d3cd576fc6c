<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@ taglib prefix="r" uri="http://mojarra.dev.java.net/mojarra_ext" %>
<%@ taglib prefix="rick" uri="http://java.sun.com/jsf/core" %>
<%@include file="includes/imports.jsp" %>

<rich:modalPanel id="modalPanelFiltrosConciliacaoContas" trimOverlayedElements="false" autosized="false"
                 shadowOpacity="true" width="974" height="620"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtros Consulta de Extrato Bancário"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkFiltro"/>
            <rich:componentControl for="modalPanelFiltrosConciliacaoContas"
                                   attachTo="hidelinkFiltro" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formFiltrosConciliacaoContas" ajaxSubmit="true">

        <%--Títulos agrupadores--%>
        <h:panelGroup id="botoesTL"
                      style="display: inline-flex; padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%;"
                      layout="block">
            <a4j:commandLink id="filtrosPrincipaisConciliacao"
                             status="false" styleClass="botaoModoTimeLine ativo filtrosPrincipais"
                             onclick="trocarBloco('.filtrosPrincipais', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Filtros principais"/>
            </a4j:commandLink>
        </h:panelGroup>

        <%--PAINEL FILTROS--%>
        <h:panelGroup styleClass="painelFiltros filtrosPrincipais visivel" id="painelfiltrosinsidemodal"
                      style="width: 100%; min-height: 50vh; ">
            <div style="width: 100%; padding: 0 15px;  min-height: 50vh;">
                <h:outputText styleClass="tituloCampos  upper flex" value="Empresa"/>
                <div class="cb-container">
                    <h:selectOneMenu id="empresa"
                                     onfocus="focusinput(this);"
                                     value="#{MovContaControle.empresaConciliacao.codigo}">
                        <f:selectItems value="#{GestaoRecebiveisControle.listaSelectItemEmpresa}"/>
                        <a4j:support action="#{GestaoRecebiveisControle.obterEmpresaEscolhida}" event="onchange"/>
                    </h:selectOneMenu>
                </div>


                <div style="padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; margin-top: 15px; width: 100%;">

                        <%--DATA DO EXTRATO--%>
                    <h:panelGroup id="panelDtExtrato" style="display: flex">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="Data do Extrato"/>
                        <i class="fa-icon-question-sign tooltipster"
                           style="font-size: 18px; margin-left: 7px; margin-top: 5px;"
                           title="Informe a data que deseja pesquisar o seu extrato bancário das suas contas conectadas."></i>
                    </h:panelGroup>
                    <h:panelGroup styleClass="flex" layout="block">
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                            <rich:calendar id="dataInicioConc"
                                           value="#{MovContaControle.dataInicioPesqExtratoConc}"
                                           inputSize="10"
                                           inputClass="form"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      style="margin: 7px 9px 0px 10px;  font-size: 11px !important;"
                                      value="até"/>
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">
                            <rich:calendar id="dataTerminoConc"
                                           value="#{MovContaControle.dataFimPesqExtratoConc}"
                                           inputSize="10"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>


                        <div id="dtPreDefinida" class="cb-container"
                             style=" font-size: 11px !important;margin-left: 15px;">
                            <h:selectOneMenu id="periodoLancamentosConc"
                                             onfocus="focusinput(this);"
                                             value="#{MovContaControle.periodoPesqExtratoConc}">
                                <f:selectItems value="#{MovContaControle.listaPeriodos}"/>
                                <a4j:support action="#{MovContaControle.alterarPeriodoExtratoConc}"
                                             event="onchange"
                                             reRender="dataInicioConc, dataTerminoConc"/>
                            </h:selectOneMenu>
                        </div>
                        <a4j:commandLink id="limparPeriodoLancamentoConc"
                                         action="#{MovContaControle.limparPeriodoPesqExtratoConc}"
                                         reRender="formFiltrosConciliacaoContas:periodoLancamentosConc"
                                         style="position:relative; top:7px; left:5px; font-size: 11px; text-decoration: none"
                                         onclick="document.getElementById('formFiltrosConciliacaoContas:dataInicioConcInputDate').value='';
                                         document.getElementById('formFiltrosConciliacaoContas:dataTerminoConcInputDate').value='';"
                                         title="Limpar período de pesquisa." status="false">
                            <i class="fa-icon-eraser tooltipster"
                               title="Limpar período de pesquisa."></i>
                        </a4j:commandLink>

                    </h:panelGroup>

                </div>

                <div style="padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; margin-top: 15px; width: 100%;">

                        <%--DATA DE VENCIMENTO CONTAS--%>
                    <h:panelGroup id="panelDtContas" style="display: flex">
                        <h:outputText styleClass="tituloCampos upper flex"
                                      value="Data Vencimento #{MovContaControle.labelContasPagarReceberConc}"/>
                        <i class="fa-icon-question-sign tooltipster"
                           style="font-size: 18px; margin-left: 7px; margin-top: 5px;"
                           title="${MovContaControle.titleDtVencimentoContasPagarConc}"></i>
                    </h:panelGroup>
                    <h:panelGroup styleClass="flex" layout="block">
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                            <rich:calendar id="dataInicioContaConc"
                                           value="#{MovContaControle.dataInicioPesquisaContasConc}"
                                           inputSize="10"
                                           inputClass="form"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>

                        <h:outputText styleClass="tituloCampos"
                                      style="margin: 7px 9px 0px 10px;  font-size: 11px !important;"
                                      value="até"/>
                        <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px;">
                            <rich:calendar id="dataTerminoContaConc"
                                           value="#{MovContaControle.dataFimPesquisaContasConc}"
                                           inputSize="10"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           inputClass="form"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputkeypress="return mascara2(this.form, this.id, '99/99/9999', event);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>


                        <div id="dtPreDefinida" class="cb-container"
                             style=" font-size: 11px !important;margin-left: 15px;">
                            <h:selectOneMenu id="periodoLancamentosContaConc"
                                             onfocus="focusinput(this);"
                                             value="#{MovContaControle.periodoPesqContasConc}">
                                <f:selectItems value="#{MovContaControle.listaPeriodosPlus}"/>
                                <a4j:support action="#{MovContaControle.alterarPeriodoLancamentoContasConc}"
                                             event="onchange"
                                             reRender="dataInicioContaConc, dataTerminoContaConc"/>
                            </h:selectOneMenu>
                        </div>
                        <a4j:commandLink id="limparPeriodoLancamentoContaConc"
                                         action="#{MovContaControle.limparPeriodoPesqContasConc}"
                                         reRender="formFiltrosConciliacaoContas:periodoLancamentosContaConc"
                                         style="position:relative; top:7px; left:5px; font-size: 11px; text-decoration: none"
                                         onclick="document.getElementById('formFiltrosConciliacaoContas:dataInicioContaConcInputDate').value='';
                                         document.getElementById('formFiltrosConciliacaoContas:dataTerminoContaConcInputDate').value='';"
                                         status="false">
                            <i class="fa-icon-eraser tooltipster"
                               title="Limpar período de pesquisa."></i>
                        </a4j:commandLink>

                    </h:panelGroup>
                </div>


                    <%--BOTÕES FILTRO IMAGEM DO BANCO--%>
                <h:panelGroup id="panelFiltrosContasModal" style="margin-top: 10px; display: block;">
                    <table id="tableFiltroContasBancariasModal">
                        <tr style="display: block; border-radius: 10px; border: 1px solid #ede4e4;">
                            <a4j:repeat value="#{MovContaControle.bancosExibirFiltroConciliacao}" var="item">
                                <td style="padding-right: 15px;" class="botaoConta">
                                    <a4j:commandLink actionListener="#{MovContaControle.prepararBancoParaFiltrar}"
                                                     style="display: flex; text-decoration: none; #{!item.selecionadoParaFiltrar ? 'opacity: 0.3;' : ''}"
                                                     styleClass="tooltipster"
                                                     oncomplete="#{MovContaControle.mensagemNotificar}"
                                                     title="#{item.titleImagemConnector}"
                                                     reRender="formFiltrosConciliacaoContas:painelfiltrosinsidemodal, panelFiltrosContas">
                                        <h:panelGroup style="display: flex">
                                            <%--Não é sandbox--%>
                                            <h:graphicImage rendered="#{!item.pluggySandbox}"
                                                            style="width: 60px; height: 60px"
                                                            url="#{item.imageUrl}">
                                            </h:graphicImage>
                                            <h:outputText rendered="#{!item.pluggySandbox}"
                                                          styleClass="tooltipster"
                                                          style="align-self: center;"
                                                          value="#{item.nameConnector}"/>
                                            <%--É sandbox--%>
                                            <h:graphicImage rendered="#{item.pluggySandbox}"
                                                            style="width: 40px; height: 40px"
                                                            url="#{item.imageUrlFacilitePay}">
                                            </h:graphicImage>
                                            <h:outputText rendered="#{item.pluggySandbox}"
                                                          styleClass="tooltipster"
                                                          style="align-self: center; margin-left: 5px;"
                                                          value="#{item.nameConnector}"/>
                                        </h:panelGroup>
                                        <f:attribute name="item" value="#{item}"/>
                                    </a4j:commandLink>
                                </td>
                            </a4j:repeat>
                        </tr>
                    </table>
                </h:panelGroup>


            </div>
        </h:panelGroup>


        <%--BOTÕES--%>
        <div class="rodapeBotoes" style="padding-bottom: 20px;">

                <%--PESQUISAR--%>
            <a4j:commandLink
                    action="#{MovContaControle.consultarConciliacao}"
                    id="btnConsultarConciliacaoContas"
                    oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}"
                    reRender="form:idconciliacao"
                    styleClass="pure-button pure-button-primary" style="margin-right:10px; margin-left: 15px;">
                <i style="font-size: 14px" class="fa-icon-search"></i> &nbsp
                <h:outputText style="font-size: 14px"
                              value="Pesquisar"/>
            </a4j:commandLink>

                <%--LIMPAR FILTROS--%>
            <a4j:commandLink id="limparFiltros"
                             reRender="formFiltrosConciliacaoContas"
                             oncomplete="#{MovContaControle.mensagemNotificar}"
                             action="#{MovContaControle.redefinirFiltrosConciliacao}"
                             styleClass="pure-button">
                <h:outputText style="font-size: 14px" value="Redefinir filtros"/>

            </a4j:commandLink>

        </div>
        <script type="text/javascript" language="javascript">
            carregarTooltipster();
        </script>
        <h:panelGroup layout="block" id="containerFuncMask">
            <script>
                carregarMaskInput();
            </script>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>



<%@page pageEncoding="ISO-8859-1"%>
<%@include file="includes/imports.jsp" %>

<h:panelGroup layout="block" id="panelTopoRF" styleClass="rich-panel-header headerSanfona">
    <h:outputLink styleClass="linkWiki"
                  value="#{SuperControle.urlBaseConhecimento}bi-resumo-financeiro-financeiro/"
                  title="Clique e saiba mais: Resumo Financeiro"
                  target="_blank">
        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
    </h:outputLink>
    <h:outputText styleClass="tituloDemonstrativo"
                  value="Período: " />

    <h:panelGroup id="panelGroupDatasRF" >
        <h:panelGroup>
            <rich:calendar id="dataInicioRF"
                           inputSize="4"
                           inputClass="form"
                           showWeeksBar="false"
                           oninputfocus="focusinput(this);"
                           oninputblur="blurinput(this);"
                           oninputchange="return validar_Data(this.id);"
                           datePattern="MM/yyyy"
                           enableManualInput="true"
                           onchanged="mostrarBotaoAtualizarRF();"
                           value="#{BIFinanceiroControle.dataInicioBiRF}">
            </rich:calendar>
            <h:message for="dataInicioRF" styleClass="mensagemDetalhada" />


        </h:panelGroup>
        <rich:spacer width="3"></rich:spacer>
        <h:outputText styleClass="tituloDemonstrativo"
                      value="#{msg_aplic.prt_ate} " />
        <h:panelGroup>

            <rich:calendar id="dataFinalRF"
                           inputSize="4"
                           inputClass="form"
                           showWeeksBar="false"
                           oninputfocus="focusinput(this);"
                           oninputblur="blurinput(this);"
                           oninputchange="return validar_Data(this.id);"
                           datePattern="MM/yyyy"
                           enableManualInput="true"
                           onchanged="mostrarBotaoAtualizarRF();"
                           reRender="btnAtualizarDadosRF"
                           value="#{BIFinanceiroControle.dataFinalBiRF}">
            </rich:calendar>
            <h:message for="dataFinalRF" styleClass="mensagemDetalhada" />
            <rich:jQuery id="mskDataRF" selector=".rich-calendar-input" timing="onload" query="mask('99/9999')" />
        </h:panelGroup>
    </h:panelGroup>
    <a4j:commandButton id="btnAtualizarDadosRF" styleClass="btnAtualizarDadosRF"
                       style="margin-left: 5px; vertical-align:middle; width:16px;height:16px;display: none"
                       status="resumostatus"
                       action="#{BIFinanceiroControle.atualizarDadosGraficoRF}"
                       reRender="panelBIFinanceiro, msg"
                       image="/images/update.png"/>

        <h:outputText styleClass="mensagemDetalhada"
                      style="margin-left: 5px"
                      id="msg"
                      value="#{BIFinanceiroControle.mensagemDetalhada}" />
</h:panelGroup>

<script type="text/javascript">

    function carregarGraficoFinanceiro() {
        try{


        var chart;

    ${BIFinanceiroControle.dadosGrafico}


            
            // SERIAL CHART
            chart = new AmCharts.AmSerialChart();
            chart.dataProvider = chartData;
            chart.categoryField = "year";
            chart.startDuration = 0.5;
            chart.balloon.color = "#000000";

            // AXES
            // category
            var categoryAxis = chart.categoryAxis;
            categoryAxis.fillAlpha = 1;
            categoryAxis.fillColor = "#FAFAFA";
            categoryAxis.gridAlpha = 0;
            categoryAxis.axisAlpha = 0;
            categoryAxis.gridPosition = "start";
            categoryAxis.position = "top";

            // value
            var valueAxis = new AmCharts.ValueAxis();
            valueAxis.title = "Resumo financeiro";
            valueAxis.dashLength = 5;
            valueAxis.axisAlpha = 0;
            valueAxis.minimum = 1;
            //                valueAxis.maximum = 100;
            valueAxis.integersOnly = true;
            valueAxis.gridCount = 10;
            valueAxis.reversed = false; // this line makes the value axis reversed
            chart.addValueAxis(valueAxis);

            // GRAPHS
            // Italy graph
            var graph = new AmCharts.AmGraph();
            graph.title = "Receita";
            graph.valueField = "receita";
            graph.hidden = false; // this line makes the graph initially hidden
            graph.balloonText = "Receita em [[category]]: [[value]]";
            graph.lineAlpha = 1;
            graph.bullet = "round";
            chart.addGraph(graph);

            // Germany graph
            var graph = new AmCharts.AmGraph();
            graph.title = "Faturamento";
            graph.valueField = "faturamento";
            graph.balloonText = "Faturamento em [[category]]: [[value]]";
            graph.bullet = "round";
            chart.addGraph(graph);

            // United Kingdom graph
            var graph = new AmCharts.AmGraph();
            graph.title = "Despesas";
            graph.valueField = "despesas";
            graph.balloonText = "Despesas em [[category]]: [[value]]";
            graph.bullet = "round";
            chart.addGraph(graph);

            var graph = new AmCharts.AmGraph();
            graph.title = "Competência";
            graph.valueField = "competencia";
            graph.balloonText = "Competência em [[category]]: [[value]]";
            graph.bullet = "round";
            chart.addGraph(graph);

            // CURSOR
            var chartCursor = new AmCharts.ChartCursor();
            chartCursor.cursorPosition = "mouse";
            chartCursor.zoomable = false;
            chartCursor.cursorAlpha = 0;
            chart.addChartCursor(chartCursor);

            // LEGEND
            var legend = new AmCharts.AmLegend();
            legend.useGraphSettings = true;
            chart.addLegend(legend);

            // WRITE
            chart.write("chartdiv");

        }catch (err){

        }
        }

        function onStartResumo(){
            document.getElementById('carregandoResumoFinanceiro').style.display = 'block';
            document.getElementById('chartdiv').style.display = 'none';
            document.getElementById('atualizarDadosGrafico').style.display = 'none';
            document.getElementById('tableVelocimetroAtualizar').style.display = 'none';
        }

        function onStopResumo(){
            document.getElementById('carregandoResumoFinanceiro').style.display = 'none';
            document.getElementById('chartdiv').style.display = 'block';
            document.getElementById('atualizarDadosGrafico').style.display = 'block';
            document.getElementById('tableVelocimetroAtualizar').style.display = 'block';
        }
</script>
</head>
<div id="chartdiv" style="width: 100%; height: 86%;"></div>
<table width="100%" id="atualizarDadosGrafico">
    <tr style="width: 100%;">
        <td width="100%" align="left">
            <h:outputText value="Dados atualizados em #{BIFinanceiroControle.dataAtualizacaoDados}" style="font-size:8pt;color:#5E5757;"  styleClass="text"/>
            <h:panelGroup id="grupoBtnAtualizaGraf">
                <a4j:commandLink id="grupoBtnAtualizaGrafLink"  reRender="mdlMensagemGenerica"
                                 oncomplete="#{BIFinanceiroControle.msgAlert}" action="#{BIFinanceiroControle.confirmarExcluir}"
                                 value=" Atualizar dados"  />
            </h:panelGroup>
        </td>
    </tr>
</table>
<div id="carregandoResumoFinanceiro" style="padding: 0px !important;display: none; margin: 18% auto 0 auto; width: 200px;" >
    <h:graphicImage value="../../imagens/carregando.gif" style="margin-right: 10px; vertical-align: middle;" />
    <h:outputText value="Atualizando dados..." styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>

</div>
<script>
    carregarGraficoFinanceiro();

    function mostrarBotaoAtualizarRF(){
        document.getElementsByClassName('btnAtualizarDadosRF')[0].style.display = "inline-block";
    }
</script>

<a4j:region id="extr3">
    <a4j:status id="resumostatus" onstart="onStartResumo()" onstop="onStopResumo()"></a4j:status>
</a4j:region>




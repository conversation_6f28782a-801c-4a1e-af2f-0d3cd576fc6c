<%-- 
    Document   : visualizarImpressaoPlanoContas
    Created on : 29/02/2012, 14:22:55
    Author     : carla
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">
<head><script type="text/javascript" language="javascript" src="../../script/script.js"></script></head>

<script type="text/javascript" src="../../script/jquery.js"></script>
<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>

<script type="text/javascript">
    function naoMostrarBotaoEditar(){
        var name = document.getElementsByName('naoMostrarBotaoJS');
        for(i=0; i<name.length;i++){
            var obj = name[i];
            obj.style.display = 'none';
        }
    }
</script>


<c:set var="moduloSession" value="1" scope="session" />



<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <table border="1" width="100%" >
        <tr>
            <td width="5px">
                <a4j:mediaOutput element="img" id="fotoRelatorio"  style="width:130px;height:80px "  cacheable="false"
                                 createContent="#{CentroCustosControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >
                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                </a4j:mediaOutput>
            </td>
            <td>
                <center>
                    <h2>Relatório Centro de Custos </h2>
                </center>
            </td>
            <td>
                <table width="100%" >
                    <tr>
                        <td>Data: <h:outputText value="#{CentroCustosControle.dataAtual}" ></h:outputText> </td>
                    <tr>
                    <tr>
                        <td>Usuário: <h:outputText value="#{CentroCustosControle.nomeUsuarioLogado}" ></h:outputText></td>
                    <tr>

                </table>
            </td>

        </tr>

    </table>
    <%@include file="include_centroCustosCons.jsp" %>
    <h:form id="formImprimir" >
        <body onload="document.getElementById('formImprimir:expandir').click();naoMostrarBotaoEditar();">
            <center>
                <h:commandLink
                    onclick="window.print();return false;">
                    <h:graphicImage value="/imagens/botoesCE/imprimir.png"  style="border: 0px; width: 65;"/>
                </h:commandLink>
            </center>
            <a4j:commandButton id="expandir" style="display:none;" styleClass="expandirCentro">
            </a4j:commandButton>
        </body>
    </h:form>

</f:view>


<%@page pageEncoding="ISO-8859-1"%>
<%--
    Document   : gestaoLotesCons
    Created on : 28/09/2011, 16:03:25
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@include file="includes/include_imports.jsp" %>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="../../script/gobackblock.js"></script>
<script type="text/javascript" language="javascript" src="../../script/Notifier.js"></script>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_tituloCons}"/>
    </title>
    <script src="../../script/packJQueryPlugins.min.js" type="text/javascript" ></script>
    <script src="../../script/gestao_Recebiveis.js" type="text/javascript" ></script>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/time_1.3.js"></script>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <html>
            <head>
                <link href="../../css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
                <link href="../../css/packcss1.0.min.css" rel="stylesheet" type="text/css">
                <link href="../../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
                <script src="../../script/all_script_3.4.min.js" type="text/javascript" ></script>
                <link href="../../beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css"/>
                <script type="text/javascript">
                    jQuery.noConflict();
                </script>
                <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
                <style>
                    .headerNumero{
                        text-align: right !important;
                        padding-right: 10px !important;
                    }

                </style>
            </head>
            <body>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW">
                        <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                        <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                    </h:panelGroup>
                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_tituloCons} " styleClass="container-header-titulo"/>
                                            <h:outputLink styleClass="linkWiki"
                                                          value="#{SuperControle.urlBaseConhecimento}gestao-de-lotes-do-modulo-financeiro/"
                                                          title="Clique e saiba mais: Gestão de Lotes"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <%-- INICIO FILTROS DE CONSULTA --%>

                                        <a onclick="Richfaces.showModalPanel('modalPanelFiltrosGestaoLotes');"
                                           id="refazerConsulta"
                                           style="cursor: pointer;"
                                           class="pure-button pure-button-primary step5 tudo">
                                            <i class="fa-icon-refresh"></i> &nbsp Refazer consulta
                                        </a>

                                        <a4j:outputPanel id="filtros">
                                            <rich:panel rendered="#{GestaoLotesControle.apresentarFiltros}"
                                            style="border-color: #eee; background-color: #eee; margin-top: 15px;">
                                                <h:outputText styleClass="tituloCamposNegrito" value="Filtros:"/>
                                                <h:outputText  styleClass="tituloDemonstrativo" value="#{GestaoLotesControle.filtros}"/>
                                            </rich:panel>
                                        </a4j:outputPanel>





                                        <%-- INICIO LISTA DE LOTES --%>
                                        <h:panelGrid width="100%" id="listaLotes" >
                                            <rich:dataTable id="itens" width="100%"
                                                            styleClass="tabelaDados dataTable no-footer"
                                                            style="width:100%; margin-left:0px; "
                                                            value="#{GestaoLotesControle.listaLotes}" var="lote">
                                                <rich:column style="border-color:#FFF" sortBy="#{lote.codigo}">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_codigo}" />
                                                    </f:facet>
                                                    <a4j:commandLink action="#{GestaoLotesControle.editarLote}">
                                                        <a4j:actionparam name="origem" value="gestaoLotes" assignTo="#{GestaoLotesControle.origem}"/>
                                                        <h:outputText value="#{lote.codigo}" />
                                                        <h:outputText rendered="#{lote.avulso}" value=" (AVULSO)"
                                                                      style="font-size:9px;"/>
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column style="border-color:#FFF" sortBy="#{lote.descricao}">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_descricao}" />
                                                    </f:facet>
                                                    <a4j:commandLink action="#{GestaoLotesControle.editarLote}">
                                                        <a4j:actionparam name="origem" value="gestaoLotes" assignTo="#{GestaoLotesControle.origem}"/>
                                                        <h:outputText value="#{lote.descricaoLower}" style="text-transform: capitalize" />
                                                    </a4j:commandLink>
                                                </rich:column>

                                                <rich:column style="border-color:#FFF" sortBy="#{lote.usuarioResponsavel.nome}">
                                                    <f:facet name="header">
                                                        <h:outputText value="Responsável" />
                                                    </f:facet>
                                                    <a4j:commandLink action="#{GestaoLotesControle.editarLote}">
                                                        <a4j:actionparam name="origem" value="gestaoLotes" assignTo="#{GestaoLotesControle.origem}"/>
                                                        <h:outputText value="#{lote.usuarioLower}" style="text-transform: capitalize"/>
                                                    </a4j:commandLink>
                                                </rich:column>

                                                <rich:column style="border-color:#FFF" sortBy="#{lote.dataLancamento}">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                                                    </f:facet>
                                                    <a4j:commandLink action="#{GestaoLotesControle.editarLote}" styleClass="tooltipster"
                                                    title="Aqui é registrado a data e hora que o lote foi lançado, exceto nos casos onde a data de trabalho do caixa é diferente da data real.<br/>Ex: Se o usuário estiver realizando movimentações hoje, porêm com a data do caixa aberto de ontem,<br/>a data de lançamento será registrado com a data de ontem, com hora zerada.">
                                                        <a4j:actionparam name="origem" value="gestaoLotes" assignTo="#{GestaoLotesControle.origem}"/>
                                                        <h:outputText value="#{lote.dataLancamento}">
                                                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm" />
                                                        </h:outputText>
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column style="border-color:#FFF" sortBy="#{lote.dataDeposito}">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{msg_aplic.prt_Finan_GestaoLotes_dataDeposito}" />
                                                    </f:facet>
                                                    <a4j:commandLink action="#{GestaoLotesControle.editarLote}"
                                                                     styleClass="tooltipster"
                                                                     title="Data em que o lote foi depositado de fato. Nem sempre será a mesma data que a de lançamento,<br/>pois o usuário pode alterar a data ao realizar o lançamento da movimentação.">
                                                        <a4j:actionparam name="origem" value="gestaoLotes" assignTo="#{GestaoLotesControle.origem}"/>
                                                        <h:outputText value="#{lote.dataDeposito}">
                                                            <f:convertDateTime pattern="dd/MM/yyyy" />
                                                        </h:outputText>
                                                    </a4j:commandLink>
                                                </rich:column>

                                                <rich:column style="border-color:#FFF" sortBy="#{lote.conta}" rendered="#{ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas}">
                                                    <f:facet name="header">
                                                        <h:outputText value="#{msg_aplic.prt_Finan_Lancamento_contaSemPonto}" />
                                                    </f:facet>
                                                    <a4j:commandLink action="#{GestaoLotesControle.editarLote}">
                                                        <a4j:actionparam name="origem" value="gestaoLotes" assignTo="#{GestaoLotesControle.origem}"/>
                                                        <h:outputText value="#{lote.contaLower}"
                                                                      style="text-transform: capitalize;">
                                                        </h:outputText>
                                                    </a4j:commandLink>
                                                </rich:column>

                                                <rich:column style="border-color:#FFF; text-align: right;" sortBy="#{lote.valor}"
                                                            headerClass="headerNumero" >
                                                    <f:facet name="header" >
                                                        <h:outputText value="#{msg_aplic.prt_Cheque_valor}" />
                                                    </f:facet>
                                                    <a4j:commandLink action="#{GestaoLotesControle.editarLote}">
                                                        <a4j:actionparam name="origem" value="gestaoLotes" assignTo="#{GestaoLotesControle.origem}"/>
                                                        <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} "/>
                                                        <h:outputText value="#{lote.valor}" style="margin-right: 10px;">
                                                            <f:converter converterId="FormatadorNumerico" />
                                                        </h:outputText>
                                                    </a4j:commandLink>
                                                </rich:column>
                                                <rich:column style="border-color:#FFF">
                                                    <f:facet name="header">
                                                        <h:outputText value="Opções"/>
                                                    </f:facet>
                                                    <a4j:commandLink action="#{GestaoLotesControle.editarLote}"
                                                                     title="Editar lote" styleClass="linkPadrao inline">
                                                        <a4j:actionparam name="origem" value="gestaoLotes" assignTo="#{GestaoLotesControle.origem}"/>
                                                        <i class="fa-icon-edit texto-cor-azul"></i>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink action="#{GestaoLotesControle.confirmarExcluir}" oncomplete="#{GestaoLotesControle.msgAlert}"
                                                                     title="Excluir lote" styleClass="linkPadrao inline"
                                                                     reRender="paneltotalizador,listaLotes,panelMensagem, mdlMensagemGenerica">
                                                        <f:setPropertyActionListener value="#{lote}" target="#{GestaoLotesControle.lote}"></f:setPropertyActionListener>
                                                        <i class="fa-icon-remove texto-cor-azul"></i>
                                                    </a4j:commandLink>
                                                </rich:column>
                                            </rich:dataTable>

                                        </h:panelGrid>
                                        <h:panelGrid width="100%" columns="2" style="margin-top:10px;" id="paneltotalizador">
                                            <h:panelGroup id="totaisregistro" >
                                                <h:outputText value="Total de registros: "/>
                                                <h:outputText value="#{fn:length(GestaoLotesControle.listaLotes)}"/>
                                            </h:panelGroup>



                                            <h:panelGroup id="totais" style="float: right">
                                                <h:outputText value="Total: "/>
                                                <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda} #{GestaoLotesControle.totalizador}"/>
                                            </h:panelGroup>

                                        </h:panelGrid>

                                        <%-- FIM LISTA DE LOTES --%>

                                        <div class="sep" style="margin:10px 0 10px 0;"></div>

                                        <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens" style="padding:15px;">
                                            <h:outputText styleClass="mensagem"  value="#{GestaoLotesControle.mensagem}"/>
                                            <h:outputText styleClass="mensagemDetalhada" value="#{GestaoLotesControle.mensagemDetalhada}"/>
                                            <script type="text/javascript" language="javascript">
                                                carregarTooltipster();
                                            </script>
                                        </h:panelGrid>
                                        <h:panelGrid width="100%" columns="1" columnClasses="colunaDireita">
                                            <a4j:commandLink oncomplete="#{GestaoLotesControle.msgAlert}"
                                                             action="#{GestaoLotesControle.realizarConsultaLogObjetoSelecionado}"
                                                             value="Log Gestão de Lotes"></a4j:commandLink>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                            <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
            </h:panelGroup>


            </body>
        </html>
    </h:form>
    <%@include file="include_modalFiltrosGestaoLotes.jsp" %>

    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="include_modalConfigGestaoRecebiveis.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
    <%@include file="includes/include_box_fecharCaixas.jsp" %>
    <jsp:include page="../../includes/include_panelMensagem_goBackBlock.jsp"/>
    <%@include file="../../includes/include_modal_mensagem_generica.jsp"%>
</f:view>

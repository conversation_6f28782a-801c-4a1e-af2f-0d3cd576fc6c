<%@page pageEncoding="ISO-8859-1"%>
<%@include file="includes/include_imports.jsp" %>
<head>
    <%@include file="../../includes/include_import_minifiles.jsp"%>
</head>
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<style>
    .rich-table-cell{
        border-bottom: none;
        border-right: none;
    }
    .rich-table-row{
        height: 35px;
    }
    .tituloCampos{
        margin-left: 0.5em;
    }
    center{
        margin-top: 25px;
    }
    /* Altera a altura da terceira row do panelGrid */
    .tableMovFin tr:nth-child(3){
        height: 40px;
    }

</style>
<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Relatório Movimentações Financeiras - Fechamento Diário"/>
    </title>
    <c:set var="titulo" scope="session" value="Movimentações - Fechamento Diário"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-de-movimentacoes-financeiras/"/>
    <f:facet name="header">
        <jsp:include page="../../topoReduzido_material.jsp"/>
    </f:facet>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">
        <hr style="border-color: #e6e6e6;"/>
        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0" styleClass="tableMovFin" >

            <h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                    <h:outputText styleClass="tituloCampos"
                                  rendered="#{RelatorioMovimentacaoFinanceiraControle.mostrarCampoEmpresa}"
                                  value="Empresa: "></h:outputText>

                    <h:selectOneMenu id="empresa" styleClass="form"
                                     rendered="#{RelatorioMovimentacaoFinanceiraControle.mostrarCampoEmpresa}"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     value="#{RelatorioMovimentacaoFinanceiraControle.empresaVO.codigo}">
                        <f:selectItems
                            value="#{RelatorioMovimentacaoFinanceiraControle.listaSelectItemEmpresa}" />
                        <a4j:support action="#{RelatorioMovimentacaoFinanceiraControle.obterEmpresaEscolhida}" event="onchange"
                                     reRender="form"/>
                    </h:selectOneMenu>
                    <h:outputText styleClass="tituloCampos" value="Período: " />
                    <h:panelGroup>
                        <rich:calendar id="dataInicioC"
                                       value="#{RelatorioMovimentacaoFinanceiraControle.inicio}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       datePattern="dd/MM/yyyy"
                                       oninputchange="return validar_Data(this.id);"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <h:outputText styleClass="tituloCampos" style="position:relative; top:0; left:10px;" value="#{msg_aplic.prt_ate}" />
                        <rich:spacer width="12px"/>
                        <rich:calendar id="dataTerminoC"
                                       value="#{RelatorioMovimentacaoFinanceiraControle.fim}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        <rich:spacer width="5px" />
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_contasApresentarBI}:" styleClass="tituloCampos"
                                  style="font-weight: bold;"/>

                    <h:outputText value="#{msg_aplic.prt_contasMarcarTodos}" styleClass="tituloCampos"
                                  style="position:relative; top:0; left:29px; font-weight: bold;"/>

                    <h:selectBooleanCheckbox value="#{RelatorioMovimentacaoFinanceiraControle.marcadoTodos}" style="position:relative; top:0; left:39px;">
                        <a4j:support event="onchange" reRender="dataGridConta" action="#{RelatorioMovimentacaoFinanceiraControle.marcarTodos}"/>
                    </h:selectBooleanCheckbox>
                </h:panelGroup>
                <rich:dataGrid id="dataGridConta" value="#{RelatorioMovimentacaoFinanceiraControle.contas}" var="conta" columns="3"
                               width="100%" style="border: none;">
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{conta.contaEscolhida}"/>
                        <h:outputText styleClass="tituloCampos" value="#{conta.descricao}"/>
                    </h:panelGroup>
                </rich:dataGrid>
            </h:panelGrid>
        </h:panelGrid>

        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink id="btnImprimirMovimentacoesFechamentoDiario"
                             action="#{RelatorioMovimentacaoFinanceiraControle.imprimir}"
                             oncomplete="#{RelatorioMovimentacaoFinanceiraControle.msgAlert}"
                             style="font-size: 20px;"
                             title="Exportar para o formato PDF"
                             styleClass="botaoPrimario texto-size-16">
                <h:outputText  styleClass="fa-icon-print"/>
                <h:outputText  styleClass="texto-size-12" value=" Imprimir PDF"/>
            </a4j:commandLink>
        </h:panelGroup>
    </h:form>
</f:view>

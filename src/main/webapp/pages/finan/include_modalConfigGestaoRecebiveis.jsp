<%@ taglib prefix="r" uri="http://mojarra.dev.java.net/mojarra_ext" %>
<%@ taglib prefix="rick" uri="http://java.sun.com/jsf/core" %>
<%@include file="includes/imports.jsp" %>
<rich:modalPanel id="modalPanelConfigGestaoRecebiveis" trimOverlayedElements="false" autosized="false"
                 shadowOpacity="true" width="974" height="620"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Configura��es de movimenta��es autom�ticas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkMGA"/>
            <rich:componentControl for="modalPanelConfigGestaoRecebiveis"
                                   attachTo="hidelinkMGA" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConfigGestaoRecebiveis" ajaxSubmit="true" >
        <h:panelGroup id="botoesTL"
                      style="display: inline-flex; padding-bottom: 10px; border-bottom:#E5E5E5 1px solid; width: 100%;"
                      layout="block">
            <a4j:commandLink status="false" styleClass="botaoModoTimeLine ativo filtrosPrincipais"
                             onclick="trocarBloco('.filtrosPrincipais', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Caixa do ZW"/>
            </a4j:commandLink>
            <a4j:commandLink status="false" styleClass="botaoModoTimeLine filtrosLote"
                             rendered="false"
                             onclick="trocarBloco('.filtrosLote', '.painelFiltros', '.botaoModoTimeLine');">
                <h:outputText value="Concilia��o da operadora"/>
            </a4j:commandLink>
        </h:panelGroup>

        <h:panelGroup styleClass="painelFiltros filtrosPrincipais visivel" id="painelfiltrosinsidemodal"
                      style="width: 100%; height: 460px; font-size: 12px; overflow-y: auto; display: block;">

            <h:panelGroup layout="block"
                          style="margin-bottom: 10px; ">
                <h:outputText styleClass="tituloCampos  upper flex"
                              value="#{msg_aplic.prt_Finan_Lancamentos_empresa}"
                              rendered="#{GestaoRecebiveisControle.mostrarCampoEmpresa}"/>
                <div class="cb-container">
                    <h:selectOneMenu onfocus="focusinput(this);"
                                     rendered="#{GestaoRecebiveisControle.mostrarCampoEmpresa}"
                                     value="#{GestaoRecebiveisControle.empresaRel.codigo}">
                        <f:selectItems value="#{GestaoRecebiveisControle.listaSelectItemEmpresa}"/>
                        <a4j:support action="#{GestaoRecebiveisControle.obterEmpresaEscolhida}" event="onchange"/>
                    </h:selectOneMenu>
                </div>
            </h:panelGroup>





            <table class="tabelaDados tudo step3" id="idconfigsma" style="width:100%; margin-left:0px; font-size: 12px">
                <thead>
                <th colspan="3">
                    <h:outputText value="ORIGEM:"/>
                </th>
                <th>
                    <h:outputText value="DESTINO:"/>
                </th>
                <th>
                </th>

                </thead>
                <thead>
                <th>
                    <h:outputText value="TIPO"/>
                </th>
                <th>
                    <h:outputText value="FORMA PGTO."/>
                </th>
                <th>
                    <h:outputText value="CONV�NIO"/>
                </th>
                <th>
                    <h:outputText value="CONTA"/>
                </th>
                <th>
                </th>

                </thead>

                <tr style="border-bottom: 1px solid #777">
                    <td>
                        <div class="cb-container">
                            <h:selectOneMenu onfocus="focusinput(this);" style="width: 150px"
                                             value="#{MovimentacaoAutomaticaControle.nova.tipoFormaPagto}">
                                <f:selectItems value="#{MovimentacaoAutomaticaControle.tipos}"/>
                            </h:selectOneMenu>
                        </div>
                    </td>

                    <td>
                        <div class="cb-container">
                            <h:selectOneMenu onfocus="focusinput(this);" style="width: 150px"
                                             value="#{MovimentacaoAutomaticaControle.nova.formaPagamento.codigo}">
                                <f:selectItems value="#{MovimentacaoAutomaticaControle.formas}"/>
                            </h:selectOneMenu>
                        </div>
                    </td>

                    <td>
                        <div class="cb-container">
                            <h:selectOneMenu onfocus="focusinput(this);" style="width: 200px"
                                             value="#{MovimentacaoAutomaticaControle.nova.convenio.codigo}">
                                <f:selectItems value="#{MovimentacaoAutomaticaControle.convenios}"/>
                            </h:selectOneMenu>
                        </div>
                    </td>

                    <td>
                        <div class="cb-container">
                            <h:selectOneMenu onfocus="focusinput(this);" style="width: 160px"
                                             value="#{MovimentacaoAutomaticaControle.nova.conta.codigo}">
                                <f:selectItems value="#{MovimentacaoAutomaticaControle.contas}"/>
                            </h:selectOneMenu>
                        </div>
                    </td>
                    <td style="text-align: center; width: 40px">
                        <a4j:commandLink
                                reRender="formConfigGestaoRecebiveis"
                                action="#{MovimentacaoAutomaticaControle.adicionarNova}"
                                oncomplete="#{MovimentacaoAutomaticaControle.msgAlert}"
                                style="margin: 0;color: #29abe2">
                            <i style="font-size: 14px" class="fa-icon-plus"></i>
                        </a4j:commandLink>
                    </td>
                </tr>

                <a4j:repeat value="#{MovimentacaoAutomaticaControle.configs}" var="cfgma" rowKeyVar="index">
                    <tr>
                        <td>
                            <h:panelGroup rendered="#{index eq 0}" layout="block" style="height: 10px"></h:panelGroup>
                            <div class="cb-container">
                                <h:selectOneMenu onfocus="focusinput(this);" style="width: 150px"
                                                 value="#{cfgma.tipoFormaPagto}">
                                    <f:selectItems value="#{MovimentacaoAutomaticaControle.tipos}"/>
                                </h:selectOneMenu>
                            </div>
                        </td>

                        <td>
                            <h:panelGroup rendered="#{index eq 0}" layout="block" style="height: 10px"></h:panelGroup>
                            <div class="cb-container">
                                <h:selectOneMenu onfocus="focusinput(this);" style="width: 150px"
                                                 value="#{cfgma.formaPagamento.codigo}">
                                    <f:selectItems value="#{MovimentacaoAutomaticaControle.formas}"/>
                                </h:selectOneMenu>
                            </div>
                        </td>

                        <td>
                            <h:panelGroup rendered="#{index eq 0}" layout="block" style="height: 10px"></h:panelGroup>
                            <div class="cb-container">
                                <h:selectOneMenu onfocus="focusinput(this);" style="width: 200px"
                                                 value="#{cfgma.convenio.codigo}">
                                    <f:selectItems value="#{MovimentacaoAutomaticaControle.convenios}"/>
                                </h:selectOneMenu>
                            </div>
                        </td>

                        <td>
                            <h:panelGroup rendered="#{index eq 0}" layout="block" style="height: 10px"></h:panelGroup>
                            <div class="cb-container">
                                <h:selectOneMenu onfocus="focusinput(this);" style="width: 160px"
                                                 value="#{cfgma.conta.codigo}">
                                    <f:selectItems value="#{MovimentacaoAutomaticaControle.contas}"/>
                                </h:selectOneMenu>
                            </div>
                        </td>
                        <td style="text-align: center; width: 40px">
                            <h:panelGroup rendered="#{index eq 0}" layout="block" style="height: 10px"></h:panelGroup>
                            <a4j:commandLink
                                    reRender="formConfigGestaoRecebiveis"
                                    action="#{MovimentacaoAutomaticaControle.remover}" oncomplete="#{MovimentacaoAutomaticaControle.msgAlert}"
                                    style="margin: 0;color: #29abe2">
                                <i style="font-size: 14px" class="fa-icon-trash"></i>
                            </a4j:commandLink>
                        </td>
                    </tr>
                </a4j:repeat>
            </table>

            <script>
                var columnsOrdersCH = [];
                var tableCFGMA = jQuery('#idconfigsma').DataTable({
                    "bPaginate": false,
                    "bInfo": false,
                    "aoColumnDefs": []
                });

            </script>


        </h:panelGroup>


        <div class="rodapeBotoes" style="padding-bottom: 20px;">
            <a4j:commandLink
                    onclick="Richfaces.hideModalPanel('modalPanelFiltrosGestaoRecebiveis');iniciarStatus(2.5);jQuery('.growl-container').empty();"
                    oncomplete="fecharStatus();#{GestaoRecebiveisControle.mensagemNotificar}"
                    reRender="idCaixaCorpo" rendered="#{GestaoRecebiveisControle.visaoConciliacao}"
                    action="#{GestaoRecebiveisControle.consultarConciliacao}"
                    styleClass="pure-button pure-button-primary" style="margin-right:10px; margin-left: 15px;"
                    title="#{msg.msg_consultar_dados}">
                <i style="font-size: 14px" class="fa-icon-search"></i> &nbsp <h:outputText style="font-size: 14px"
                                                                                           value="Pesquisar"/>
            </a4j:commandLink>


        </div>


    </a4j:form>
</rich:modalPanel>



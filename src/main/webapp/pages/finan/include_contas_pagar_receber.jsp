<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>



<rich:panel style="#{BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas ? 'height: 350px;' : 'height: 400px;'}"
            id="painelContasPR" >
    <h:panelGrid columnClasses="w50, w50" columns="2" cellspacing="0" cellpadding="0" width="100%">
        <rich:panel headerClass="headerSanfona"
                    style="#{BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas ? 'height: 350px;' : 'height: 400px;'}">
            <f:facet name="header">

                <h:panelGrid columns="2" width="100%" columnClasses="esquerda, direita" cellpadding="0" cellspacing="0">
                    <h:panelGroup>
                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlBaseConhecimento}bi-contas-a-pagar-e-contas-a-receber-financeiro/"
                                      title="Clique e saiba mais: Contas a pagar"
                                      target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <h:outputText value="Contas a pagar" id="contasPagarBI" styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
                    </h:panelGroup>
                    <a4j:commandButton id="btnAtualizarPagar" reRender="painelContasPR"
                                       title="Atualizar contas a pagar"
                                       style="vertical-align:middle; width:16px;height:16px;"
                                       status="statusInComponent"
                                       action="#{BIFinanceiroControle.atualizarContasPagar}"
                                       image="/images/update.png"/>
                </h:panelGrid>
            </f:facet>
            <div  style="overflow-x: visible; overflow-y: scroll; height: 77%; ">

                <h:panelGrid width="100%" >


                    <h:dataTable value="#{BIFinanceiroControle.contasPagar}" id="listContasPagarBI" width="100%" var="conta"
                                 rowClasses="linhaPar, linhaImpar">
                        <h:column>
                            <h:panelGrid columns="2" columnClasses="esquerda, direita" width="100%" cellpadding="4" cellspacing="4">
                                <c:if test="${MovContaControle.visualizarLancamentos}">
                                    <a4j:commandLink action="#{BIFinanceiroControle.editarLancamentoPopUp}"
                                                     oncomplete="abrirPopup('includes/include_LancamentosDF.jsp', 'TelaLançamento', 1200, 700);"
                                                     title="Data de vencimento: #{conta.codigoString}">
                                        <h:outputText value="#{conta.descricaoCurta}" style="color:#0f4c6b;font-size:9pt;font-weight:bold;"  styleClass="text" />
                                    </a4j:commandLink>
                                </c:if>
                                <c:if test="${!MovContaControle.visualizarLancamentos}">
                                        <h:outputText value="#{conta.descricaoCurta}" style="color:#0f4c6b;font-size:9pt;font-weight:bold;"  styleClass="text" />
                                </c:if>
                                <h:outputText value="#{conta.valor_Apresentar}" id="valorContaPagar" styleClass="text" style="color:red;font-size:9pt;font-weight:bold;"/>
                            </h:panelGrid>
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>
            </div>
            <rich:separator width="100%" height="1"/>
            <h:panelGrid width="100%"  columns="2"
                         columnClasses="esquerda, direita">
                <h:outputText value="Total: #{BIFinanceiroControle.totalPagar}" style="color:#0f4c6b;font-size:9pt;font-weight:bold;"  styleClass="text" />
                <h:outputText value="#{BIFinanceiroControle.valorPagar_Apresentar}"
                              id="valorContaPagarTotal"
                              styleClass="text" style="color:red;font-size:9pt;font-weight:bold;"/>
                <h:outputText value="Período: semana atual" style="font-size:8pt;color:#5E5757;"  styleClass="text"/>
                <c:if test="${MovContaControle.visualizarLancamentos}">
                    <a4j:commandLink value="Ver todas" actionListener="#{MovContaControle.consultarLancamentos}"
                                     action="#{MovContaControle.inicializarSemRedirecionar}"
                                     oncomplete="abrirPopup('telaLancamentosCons.jsp', 'TelaLançamento', 1200, 700);">
                        <f:attribute name="tipoPesquisa" value="2"/>
                    </a4j:commandLink>
                </c:if>
            </h:panelGrid>
        </rich:panel>
        <!--  FIM---------------------------------------contas a pagar-->
        <!-- ---------------------------------------contas a receber-->
        <rich:panel  headerClass="headerSanfona"
                     style="#{BIFinanceiroControle.configuracaoFinan.usarMovimentacaoContas ? 'height: 350px;' : 'height: 400px;'}">
            <f:facet name="header">

                <h:panelGrid columns="2" width="100%" columnClasses="esquerda, direita" cellpadding="0" cellspacing="0">
                    <h:panelGroup>
                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlBaseConhecimento}bi-contas-a-pagar-e-contas-a-receber-financeiro/"
                                      title="Clique e saiba mais: Contas a receber"
                                      target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <h:outputText value="Contas a receber" id="contasReceberBI" styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
                    </h:panelGroup>
                    <a4j:commandButton id="btnAtualizarReceber" reRender="painelContasPR"
                                       title="Atualizar saldos das contas"
                                       style="vertical-align:middle; width:16px;height:16px;"
                                       status="receberstatus"
                                       action="#{BIFinanceiroControle.atualizarContasReceber}"
                                       image="/images/update.png"/>
                </h:panelGrid>
            </f:facet>

            <div id="contasReceber">
            <div  style="overflow-x: visible; overflow-y: scroll; height: 77%">
                <h:panelGrid width="100%">
                    <h:dataTable value="#{BIFinanceiroControle.contasReceber}" id="listContasReceberBI" width="100%" var="conta"
                                 rowClasses="linhaPar, linhaImpar">
                        <h:column>

                            <h:panelGrid columns="2" columnClasses="esquerda, direita" width="100%" cellpadding="4" cellspacing="4">
                                <a4j:commandLink action="#{BIFinanceiroControle.editarLancamentoPopUp}"
                                                 rendered="#{conta.codigo > 0}"
                                                 title="Data de vencimento: #{conta.codigoString}"
                                                 oncomplete="abrirPopup('includes/include_LancamentosDF.jsp', 'TelaLançamento', 1200, 700);">
                                    <h:outputText value="#{conta.descricaoCurta}" style="color:#0f4c6b;font-size:9pt;font-weight:bold;"  styleClass="text" />
                                </a4j:commandLink>
                                <h:outputText value="#{conta.valor_Apresentar}" id="valorContasReceber" styleClass="text" style="color:green;font-size:9pt;font-weight:bold;"/>
                            </h:panelGrid>
                        </h:column>
                    </h:dataTable>


                </h:panelGrid>
            </div>

            

            <rich:separator width="100%" height="1" id="linhaReceber"/>
            <h:panelGrid width="100%" columns="2" columnClasses="esquerda, direita" id="rodapeReceber" >
                <h:outputText value="Total: #{BIFinanceiroControle.totalReceber}" style="color:#0f4c6b;font-size:9pt;font-weight:bold;"  styleClass="text" />
                <h:outputText value="#{BIFinanceiroControle.valorReceber_Apresentar}"
                              id="valorContaReceberTotal"
                              styleClass="text" style="color:green;font-size:9pt;font-weight:bold;"/>
                <h:outputText value="Período: semana atual" style="font-size:8pt;color:#5E5757;"  styleClass="text"/>
                <c:if test="${MovContaControle.visualizarLancamentos}">
                    <a4j:commandLink value="Ver todas" actionListener="#{MovContaControle.consultarLancamentos}"
                                     action="#{MovContaControle.inicializarSemRedirecionar}"
                                     oncomplete="abrirPopup('telaLancamentosCons.jsp', 'TelaLançamento', 1200, 700);">
                        <f:attribute name="tipoPesquisa" value="3"/>
                    </a4j:commandLink>
                </c:if>
            </h:panelGrid>
            </div>

            <div id="carregandoReceber" style="padding: 0px !important;display: none; margin: 50% auto 0 auto; width: 200px;" >
                <h:graphicImage value="../../imagens/carregando.gif" style="margin-right: 10px; vertical-align: middle;" />
                <h:outputText value="Atualizando dados..." styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>

            </div>
        </rich:panel>


    </h:panelGrid>
<a4j:region id="extr4">
    <a4j:status id="receberstatus" onstart="document.getElementById('carregandoReceber').style.display = 'block';document.getElementById('contasReceber').style.display = 'none';"
                onstop="document.getElementById('carregandoReceber').style.display = 'none';document.getElementById('contasReceber').style.display = 'block';"></a4j:status>
</a4j:region>
<a4j:jsFunction  name="initReceber"
                 action="#{BIFinanceiroControle.atualizarContasReceberPrimeiraVez}"
                 reRender="painelContasPR" status="receberstatus"/>
</rich:panel>


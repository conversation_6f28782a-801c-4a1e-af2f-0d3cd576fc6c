<%@include file="includes/include_imports.jsp" %>

<c:set var="moduloSession" value="1" scope="session"/>

<head>
    <%@include file="includes/include_head_finan.jsp" %>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
    <script type="text/javascript" src="../../bootstrap/jquery.js"></script>
    <link href="../../css/packcss1.0.min.css" rel="stylesheet" type="text/css">
    <link href="../../bootstrap/bootplus.css" rel="stylesheet">
    <style>
        /* TOPO MENU */

        .rich-ddmenu-label, .rich-ddmenu-label-disabled {
            padding: 0px !important;
        }

        /* CUSTOMIZE THE CAROUSEL
        -------------------------------------------------- */

        /* Carousel base class */
        .carousel {
            margin-bottom: 0px;
            height: 100%;
        }

        .carousel .container {
            position: relative;
            z-index: 9;
            height: 100%;
        }

        .carousel-control {
            height: 80px;
            margin-top: 0;
            font-size: 36px;
            text-shadow: 0 1px 1px rgba(0, 0, 0, .4);
            background-color: transparent;
            border: 0;
            z-index: 10;
        }

        .carousel .item {
            height: 100%
        }

        .carousel img {
            margin-left: auto;
            margin-right: auto;
        }

        .carousel-caption {
            background-color: transparent;
            position: static;
            max-width: 550px;
            padding: 0 20px;
            margin-top: 200px;
        }

        .carousel-caption h1,
        .carousel-caption .lead {
            margin: 0;
            line-height: 1.25;
            color: #fff;
            text-shadow: 0 1px 1px rgba(0, 0, 0, .4);
        }

        .carousel-caption .btn {
            margin-top: 10px;
        }

    </style>

    <!-- Le javascript -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script src="../../bootstrap/bootstrap-transition.js"></script>
    <script src="../../bootstrap/bootstrap-carousel.js"></script>
    <script>
        jQuery(document).ready(function ($) {
            $("#myCarousel").carousel({interval: 10000});
            validarTamanhoBanner(true);
        });
        setDocumentCookie('popupsImportante', 'close',1);
    </script>
</head>
<link href="${root}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="${root}/beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <jsp:include page="../../pacto_home_page.jsp"/>
                        </h:panelGroup>

                        <jsp:include page="includes/include_box_menulateral.jsp" flush="true">
                            <jsp:param name="menu" value="FIN-INICIO"/>
                        </jsp:include>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </h:form>
    <%@include file="/include_load_configs.jsp" %>
    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
    <%@include file="includes/include_box_fecharCaixas.jsp" %>
    <jsp:include page="../../include_modal_expiracaoSenha.jsp"/>
    <jsp:include page="../../includes/include_modal_emailAdministrador.jsp"/>
    <jsp:include page="../../includes/cliente/include_atualizar_dados_usuarios.jsp"/>
    <jsp:include page="../../includes/cliente/include_atualizar_dados_financeiro.jsp"/>
    <jsp:include page="../../includes/include_modal_novo_aviso.jsp" flush="true"/>

    <script>
        function clickNotificar() {
            document.getElementById('form:notificarClickBanner').click();
        }

        function abrirFuncionalidade(funcionalidade) {
            clickNotificar();
            var funcionalidadeAbrir = document.getElementById('form:funcionalidadeAbrir');
            funcionalidadeAbrir.value = funcionalidade;
            document.getElementById('form:funcionalidadeAbrirClick').click();
        }
    </script>
</f:view>

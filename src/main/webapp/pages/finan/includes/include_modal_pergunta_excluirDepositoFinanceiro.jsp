<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalPerguntaExcluirDepositoFinanceiro" width="450" autosized="true" styleClass="novaModal"
                     shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Exclusão de Depósito"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkPergExcluirDep"/>
                <rich:componentControl for="modalPerguntaExcluirDepositoFinanceiro" attachTo="hidelinkPergExcluirDep"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formPerguntaExcluirDepositoFinanceiro">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <h:panelGroup layout="block" styleClass="texto-size-16-real texto-cor-preto">
                    <h:outputText value="Deseja realmente excluir o depósito?"
                                  styleClass="texto-size-16-real texto-cor-preto"/>
                </h:panelGroup>
                <h:panelGroup styleClass="margin-box">
                    <a4j:commandLink action="#{GestaoRecebiveisControle.operacaoExclusaoDeposito}" value="Sim"
                                     id="sim"
                                     reRender="form:listaOutros"
                                     styleClass="botaoPrimario texto-size-16-real"
                                     oncomplete="#{GestaoRecebiveisControle.mensagemNotificar}; #{GestaoRecebiveisControle.abrirFecharModalPerguntaExclusaoDeposito}; #{GestaoRecebiveisControle.abrirFecharModalExclusaoDeposito}"/>
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink oncomplete="Richfaces.hideModalPanel('modalPerguntaExcluirDepositoFinanceiro')"
                                     id="nao"
                                     styleClass="botaoSecundario texto-size-16-real"
                                     value="Não"/>
                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>


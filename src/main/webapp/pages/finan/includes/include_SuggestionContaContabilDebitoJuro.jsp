<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%-- CONTA DEBITO --%>
<h:panelGroup id="labelDebitoJuro">
    <h:outputText rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarJuro}"
                  value="Conta cont�bil devedora juro:"
                  style="vertical-align: middle"
                  styleClass="tituloCampos">
    </h:outputText>
</h:panelGroup>


<h:panelGroup id="sugestionDebitoJuro">
    <h:inputText  id="nomeDebitoJuro"
                  size="50"
                  style="vertical-align: middle"
                  maxlength="50"
                  onblur="blurinput(this);"
                  rendered="#{MovContaControle.integracaoContabilAlterData  && MovContaControle.informarJuro}"
                  onfocus="focusinput(this);"
                  styleClass="form"
                  value="#{MovContaControle.movContaContabilVO.contaContabilDebitoJuro.descricao}" >
        <a4j:support event="onchange" action="#{MovContaControle.setarContaDebitoJuroVazio}" reRender="form"/>
    </h:inputText>

    <rich:suggestionbox   height="200" width="400"
                          rendered="#{MovContaControle.integracaoContabilAlterData  && MovContaControle.informarJuro}"
                          for="nomeDebitoJuro"
                          fetchValue="#{resultContaDevedoraJuro.descricao}"
                          status="true"
                          nothingLabel="Nenhum registro  encontrado!"
                          style="vertical-align: middle"
                          suggestionAction="#{MovContaControle.executarAutocompleteConsultarContaContabil}"
                          minChars="1"
                          rowClasses="linhaImpar, linhaPar"
                          var="resultContaDevedoraJuro"  id="suggestionDebitoJuro">
        <a4j:support event="onselect"
                     reRender="form"
                     ignoreDupResponses="true"
                     action="#{MovContaControle.selecionarContaDevedoraJuroSuggestionBox}">
        </a4j:support>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Descri��o"  styleClass="textverysmall"/>
            </f:facet>
            <h:outputText styleClass="textverysmall" value="#{resultContaDevedoraJuro.descricao}" />
        </h:column>
    </rich:suggestionbox>
</h:panelGroup>
<%--
    Author                           : <PERSON>
    Data                             : 20/07/2011
    Objetivo da Tela                 : Pesquisar planos de conta com tree view.
    Em qual tela pode ser usada      : Nas telas onde é necessário pesquisar planos de conta com tree view.
    Exemplo 1 para importar esta tela: 
				<%@include file="include_SuggestionPlanoConta.jsp"%>
				É necessário definir a função javascript citada abaixo.
				Essa função seleciona o elemento que será preenchido com a descrição do plano de contas selecionado. (tipicamente um input text).
				Vale lembrar que o id do campo é a concatenação de ID_DO_FORMULARIO + ":" + ID_DO_CAMPO

				A função:?
				function selecionarPlano(descricao){
					document.getElementById('formRateioEdicao:nomeProdutoSelecionado').value = descricao;
				}
    Ex. p/ recuperar o produto selecionado: (PlanoContaTO)JSFUtilities.getManagedBeanValue("PlanoContasControle.planoEscolhido")
--%>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<rich:modalPanel id="modalPlanos" autosized="true" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Selecionar Plano de Contas"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hidelinkSelecionarPlano" />
            <rich:componentControl for="modalPlanos"
                                   attachTo="hidelinkSelecionarPlano" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <!-- TREE VIEW -->
    <div id="content">
        <rich:panel id="dados">
            <h:panelGrid columns="2" columnClasses="top,top" width="100%">                
                <rich:tree style="height: 350px; width: 420px; overflow: scroll;" id="treeview"
                           iconCollapsed="/images/expandTree.gif"
                           iconExpanded="/images/collapTree.gif"
                           nodeSelectListener="#{PlanoContasControle.processSelection}"
                           nodeFace="#{treeNode.parent.parent == null ? 'node' : 'leaf'}"
                           reRender="selectedNode, dados" ajaxSubmitSelection="true"
                           switchType="client" data="descricao"
                           value="#{PlanoContasControle.treeNode}" var="item">
                    <rich:treeNode id="tNodeP" type="node" >
                        <a4j:commandLink onclick="preencherHiddenChamarBotao('formPlano:botaoSelecao','formPlano:codigoSelecao', #{item.codigo})"
                                         id="descricaoDetalhada">
                            <h:outputText styleClass="tituloCampos" value="#{item.descricaoDetalhada}">
                            </h:outputText>
                        </a4j:commandLink>
                    </rich:treeNode>
                    <rich:treeNode id="tLeafP"  type="leaf" dragType="pic">
                        <a4j:commandLink onclick="preencherHiddenChamarBotao('formPlano:botaoSelecao','formPlano:codigoSelecao', #{item.codigo})"
                                         id="codigoDescricao">
                            <h:outputText styleClass="tituloCampos" value="#{item.codigoPlano} - #{item.descricao}">
                            </h:outputText>
                        </a4j:commandLink>
                    </rich:treeNode>
                </rich:tree>
            </h:panelGrid>            
        </rich:panel>
        <a4j:form>
            <a4j:commandButton value="Atualizar" action="#{PlanoContasControle.loadTree}" reRender="treeview"
            					style="width:100px; height:30px;"
            					image="../../../imagens/botaoAtualizarGrande.png"/>
        </a4j:form>
    </div>
    <!-- fim: TREE VIEW -->
    <!-- Controles para a tree view -->
    <h:form id="formPlano">
        <%--form:nomeCentroSelecionado usado no lançamento rápido--%>
        <%--formLanc:nomeCentroSelecionado usado contas a pagar e receber--%>
        <a4j:commandButton style="visibility: hidden;" reRender="formLanc:nomePlanoSelecionadoRateio, formLanc:nomePlanoSelecionado,formTelaLancRapido:pgPlanoContas,codigoPlano,modalPlanos,panelMensagem,modalIncluirRateio,formEdicaoPlanoContas, formDeposito, form:nomePlanoSelecionado, formLancarPagamento:definirPlanoContaCentroCustoValorSuperiorGeral, form:nomePlanoSelecionadoRateioPix"
                           id="botaoSelecao" action="#{PlanoContasControle.processSelection}" oncomplete="Richfaces.hideModalPanel('modalPlanos');"></a4j:commandButton>
        <h:inputHidden id="codigoSelecao"
                       value="#{PlanoContasControle.codigoBancoPlanoContas}" /></h:form>
    <!-- fim: Controles para a tree view -->
</rich:modalPanel>

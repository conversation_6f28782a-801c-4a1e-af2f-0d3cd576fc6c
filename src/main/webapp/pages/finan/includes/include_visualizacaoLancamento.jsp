<%@include file="include_visualizacaoLancamentoDados.jsp" %>
					<br/>
					<center>
						<h:outputText styleClass="tituloDemonstrativo"
	    						  rendered="#{not empty MovContaControle.movContaVO.chequesRetirados}"
								  value="Cheques retirados: "></h:outputText>
								  
					</center>
					<br/>
					<rich:dataTable id="listaChequesRetirar" width="100%" styleClass="textverysmall"
                                 columnClasses="colunaEsquerda, centralizado, centralizado, centralizado, centralizado,
                                 centralizado, centralizado, colunaDireita, centralizado, centralizado"
                                 value="#{MovContaControle.movContaVO.chequesRetirados}" var="cheque"
                                 headerClass="subordinado" 
                                 rendered="#{not empty MovContaControle.movContaVO.chequesRetirados}">
                     <rich:column>
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_finan_Pagador}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cheque.nomePagador}" />
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Cheque_banco}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cheque.numeroBanco}" />
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Cheque_agencia}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cheque.agencia}" />
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Cheque_conta}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cheque.conta}" />
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Finan_GestaoRecebiveis_numeroCheque}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cheque.numero}" />
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_finan_compensacao}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cheque.dataCompensacao}">
                             <f:convertDateTime pattern="dd/MM/yyyy" />
                         </h:outputText>
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Cheque_valor}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cheque.valor}">
                             <f:converter converterId="FormatadorNumerico" />
                         </h:outputText>
                     </rich:column>
                 </rich:dataTable>
                                        <br/>
					<center>
						
						<h:outputText styleClass="tituloDemonstrativo"
	    						  rendered="#{not empty MovContaControle.movContaVO.cartoesRetirados}"
								  value="Cart�es retirados: "></h:outputText>
					</center>
					<br/>
                 
                 <rich:dataTable id="listaCartoesLote" width="100%" styleClass="textsmall" 
                                 columnClasses="colunaEsquerda, colunaEsquerda, centralizado, centralizado, centralizado,
                                 colunaDireita, centralizado, centralizado"
                                 headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                                 value="#{MovContaControle.movContaVO.cartoesRetirados}"
                                 rendered="#{not empty MovContaControle.movContaVO.cartoesRetirados}" var="cartao">
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cartao.nomePagador}" />
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cartao.operadora}" />
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cartao.dataLancamento}">
                             <f:convertDateTime pattern="dd/MM/yyyy" />
                         </h:outputText>
                     </rich:column>
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cartao.dataCompensacao}">
                             <f:convertDateTime pattern="dd/MM/yyyy" />
                         </h:outputText>
                     </rich:column>
                     
                      
                     
                     <rich:column  >
                         <f:facet name="header">
                             <h:outputText style="font-weight: bold; font-size:9px;"
                                           value="#{msg_aplic.prt_Cheque_valor}" />
                         </f:facet>
                         <h:outputText style="font-weight: bold; font-size:9px;"
                                       value="#{cartao.valor}">
                             <f:converter converterId="FormatadorNumerico" />
                         </h:outputText>
                     </rich:column>
                 </rich:dataTable>


<h:panelGrid  id="panelBotoes"  width="100%" columnClasses="colunaCentralizada" columns="1" >
    <h:panelGroup>
      <h:commandButton id="voltar" rendered="#{!MovContaControle.lancamentoDemonstrativo}"
                             action="#{MovContaControle.voltar}" value="Voltar" accesskey="1"
                             styleClass="botoes nvoBt btSec"/>
    </h:panelGroup>
    
    
</h:panelGrid>
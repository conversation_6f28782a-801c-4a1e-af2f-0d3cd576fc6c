<%@ taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@page pageEncoding="ISO-8859-1" %>
<a4j:outputPanel>
  <rich:modalPanel id="modalResultadoEstornoQuitacaoEmMassa" autosized="true" width="500" height="150"
                   shadowOpacity="true" styleClass="novaModal">
    <f:facet name="header">
      <h:panelGroup>
        <h:outputText value="Resultado Estorno Quitação em Conjunto"></h:outputText>
      </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
      <h:panelGroup>
        <h:outputText
                styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                id="hidelinkmodalResultadoEstornoQuitacaoEmMassa"/>
        <rich:componentControl for="modalResultadoEstornoQuitacaoEmMassa"
                               attachTo="hidelinkmodalResultadoEstornoQuitacaoEmMassa"
                               operation="hide" event="onclick"/>
      </h:panelGroup>
    </f:facet>
    <h:form id="formResultadoEstornoQuitacaoEmMassa">
      <h:panelGrid width="100%" columns="1" cellpadding="1" columnClasses="tituloCampos">

        <h:outputText styleClass="tituloDemonstrativo" style="display: block; text-align: left;"
                      value="Operação finalizada!"/>

        <rich:spacer width="100%" height="10px"/>

        <h:outputText styleClass="tituloDemonstrativo" style="display: block; text-align: left;"
                      value="Código(s) da(s) Conta(s) estornada(s) com sucesso: #{MovContaControle.codContasSucessoQuitacaoEstornadasEmConjunto}"/>

        <h:outputText styleClass="tituloDemonstrativo" style="display: block; text-align: left;"
                      value="Código(s) da(s) Conta(s) não estornadas: #{MovContaControle.codContasFalhaQuitacaoEstornadasEmConjunto}"/>

      </h:panelGrid>

      <h:panelGrid columns="1" styleClass="centralizado" width="100%" style="margin-top: 15px;">
        <h:panelGroup>

          <a4j:commandButton id="fecharModalResultado"
                             status="statusInComponent nvoBt"
                             value="Ok"
                             actionListener="#{MovContaControle.consultarPaginadoListener}"
                             oncomplete="Richfaces.hideModalPanel('modalResultadoEstornoQuitacaoEmMassa');"
                             reRender="form:pagarEmConjunto, items, painelPaginacao,containerFuncMask, painelPaginacaoTop, totalValor,totalRegistros,valorTotalReceb,valorTotalPag, panelMensagem, totalReg, valorTotal,panelTipoLancamento, filtros, checado, panelTotalizadoresTop, panelTotalizadoresButton, panelTotalizadoresTopNovo"
                             styleClass="botoes nvoBt">
            <f:attribute name="paginaInicial" value="paginaInicial" />
            <f:attribute name="tipoConsulta" value="detalhada"/>
          </a4j:commandButton>
        </h:panelGroup>
      </h:panelGrid>

    </h:form>
  </rich:modalPanel>
</a4j:outputPanel>

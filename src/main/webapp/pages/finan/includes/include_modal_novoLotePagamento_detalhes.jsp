<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalNovoLotePagamentoDetalhes" autosized="true" styleClass="novaModal"
                     minWidth="600" width="600" minHeight="300" height="300" top="100" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Novo Lote de Pagamento"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkExcluirDep"/>
                <rich:componentControl for="modalNovoLotePagamentoDetalhes" attachTo="hidelinkExcluirDep"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formNovoLotePagamentoDetalhes">
            <h:panelGroup id="panelTextoNovoLote" style="display: flex; place-content: center">
                <h:outputText styleClass="tituloCampos"
                              value="#{MovContaControle.titleTextoNovoLoteACriar}">
                </h:outputText>
            </h:panelGroup>

            <h:panelGroup id="panelTablesTotalizadores" style="display: flex; place-content: center; margin-top: 10px;">
                <%--BOLETO--%>
                <rich:dataTable value="#{MovContaControle.totalizadoresLoteBoletoTO}"
                                style="width: 20%; margin-right: 30px"
                                rendered="#{MovContaControle.exibirTotalizadoresDeNovoLoteBoleto}"
                                var="totalizadorBol">
                    <f:facet name="header">
                        <h:graphicImage
                                value="/imagens/boleto-text-logo.png">
                        </h:graphicImage>
                    </f:facet>

                    <rich:column style="text-align-last: center;">
                        <f:facet name="header">
                            <h:outputText style="text-align-last: center;" value="Qtd. Total"/>
                        </f:facet>
                        <h:outputText value="#{totalizadorBol.quantidade}"/>
                    </rich:column>

                    <rich:column style="text-align-last: center;">
                        <f:facet name="header">
                            <h:outputText value="Valor Total R$"/>
                        </f:facet>
                        <h:outputText value="#{totalizadorBol.valor_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>

                <%--BOLETO CONSUMO--%>
                <rich:dataTable value="#{MovContaControle.totalizadoresLoteBoletoConsumoTO}"
                                style="width: 20%; margin-right: 30px"
                                rendered="#{MovContaControle.exibirTotalizadoresDeNovoLoteBoletoConsumo}"
                                var="totalizadorBolConsumo">
                    <f:facet name="header">
                        <h:graphicImage
                                value="/imagens/consumo-text-logo.png">
                        </h:graphicImage>
                    </f:facet>

                    <rich:column style="text-align-last: center;">
                        <f:facet name="header">
                            <h:outputText style="text-align-last: center;" value="Qtd. Total"/>
                        </f:facet>
                        <h:outputText value="#{totalizadorBolConsumo.quantidade}"/>
                    </rich:column>

                    <rich:column style="text-align-last: center;">
                        <f:facet name="header">
                            <h:outputText value="Valor Total R$"/>
                        </f:facet>
                        <h:outputText value="#{totalizadorBolConsumo.valor_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>

                <%--PIX--%>
                <rich:dataTable value="#{MovContaControle.totalizadoresLotePixTO}"
                                style="width: 20%; margin-right: 30px"
                                rendered="#{MovContaControle.exibirTotalizadoresDeNovoLotePix}"
                                var="totalizadorPix">
                    <f:facet name="header">
                        <h:graphicImage
                                value="/imagens/pix-text-logo.png">
                        </h:graphicImage>
                    </f:facet>

                    <rich:column style="text-align-last: center;">
                        <f:facet name="header">
                            <h:outputText value="Qtd. Total"/>
                        </f:facet>
                        <h:outputText value="#{totalizadorPix.quantidade}"/>
                    </rich:column>

                    <rich:column style="text-align-last: center;">
                        <f:facet name="header">
                            <h:outputText value="Valor Total R$"/>
                        </f:facet>
                        <h:outputText value="#{totalizadorPix.valor_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>

                <%--TRANSFERÊNCIA--%>
                <rich:dataTable value="#{MovContaControle.totalizadoresLoteTransferenciaTO}"
                                style="width: 20%; margin-right: 30px"
                                rendered="#{MovContaControle.exibirTotalizadoresDeNovoLoteTransferencia}"
                                var="totalizadorTransferencia">
                    <f:facet name="header">
                        <h:graphicImage
                                value="/imagens/transfer-text-logo.png">
                        </h:graphicImage>
                    </f:facet>

                    <rich:column style="text-align-last: center;">
                        <f:facet name="header">
                            <h:outputText value="Qtd. Total"/>
                        </f:facet>
                        <h:outputText value="#{totalizadorTransferencia.quantidade}"/>
                    </rich:column>

                    <rich:column style="text-align-last: center;">
                        <f:facet name="header">
                            <h:outputText value="Valor Total R$"/>
                        </f:facet>
                        <h:outputText value="#{totalizadorTransferencia.valor_Apresentar}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

            <h:panelGroup id="botoesNvLote" style="display: flex; place-content: center; margin-top: 20px;">
                <a4j:commandButton
                        title="Voltar para a tela anterior e revisar os lançamentos"
                        id="btnVoltarParaModalNovoLotePagamento"
                        action="#{MovContaControle.fecharModalNovoLotePagamentoDetalhesEReabrirAnterior}"
                        value="Voltar"
                        style="margin: 0; vertical-align: middle; float: right"
                        styleClass="botoes nvoBt tooltipster"
                        oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamentoDetalhes}#{MovContaControle.abrirFecharModalNovoLotePagamento}"/>
                <a4j:commandButton
                        reRender="panelGroupAtualizarLancarPagamento"
                        id="btnCriarLotePgtoFinanceiro"
                        action="#{MovContaControle.criarLotePagamento}"
                        value="Criar Lote de Pagamento"
                        style="margin: 0; vertical-align: middle; float: right; background-color: #00c350 !important; margin-left: 40px"
                        styleClass="botoes nvoBt"
                        oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamentoDetalhes}"/>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

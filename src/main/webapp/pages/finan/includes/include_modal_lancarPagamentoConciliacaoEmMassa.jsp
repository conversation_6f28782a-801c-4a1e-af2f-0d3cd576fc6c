<%--/**--%>
<%--* Created with IntelliJ IDEA.--%>
<%--* User: <PERSON>--%>
<%--* Date: 01/11/2023--%>
<%--*/--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalPanelLancarPagamentoConciliacaoEmMassa" autosized="true"
                     styleClass="novaModal"
                     minWidth="400" height="190" width="650" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{MovContaControle.labelLancarPagamentoOuRecebimentoConc} Em Massa"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkPagConcEmMassa"/>
                <rich:componentControl for="modalPanelLancarPagamentoConciliacaoEmMassa"
                                       attachTo="hidelinkPagConcEmMassa"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formLancarPagamentoConciliacaoEmMassa">
            <h:panelGrid width="100%" columns="2" cellpadding="2" columnClasses="tituloCampos"
                         rowClasses="linhaPar, linhaImpar">

                <%--QTD. Total.--%>
                <h:outputText styleClass="tituloCampos tootlipster"
                              style="margin-top: 5px; display: block;"
                              title="Exibe a quantidade total de contas que foram selecionadas na tela anterior e que serão conciliadas/quitadas após a conclusão da operação."
                              value="Qtd. de contas a serem conciliadas:"/>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Exibe a quantidade total de contas que foram selecionadas na tela anterior e que serão conciliadas/quitadas após a conclusão da operação."
                              value="#{MovContaControle.qtdTotalSelecionadoParaConciliacaoEmMassa}"/>

                <%--Valor Total--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Exibe o valor total de contas que foram selecionadas na tela anterior e que serão conciliadas/quitadas após a conclusão da operação."
                              style="margin-top: 6px; display: block;"
                              value="Valor total:"/>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Exibe o valor total de contas que foram selecionadas na tela anterior e que serão conciliadas/quitadas após a conclusão da operação."
                              value="#{MovContaControle.valorTotalSelecionadoParaConciliacaoEmMassa}"/>

                <%--DATA QUITAÇÃO--%>
                <h:outputText styleClass="tituloCampos"
                              style="margin-top: 4px; display: block;"
                              value="Data Quitação:"/>

                <h:panelGroup id="valorFinal">
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="Será a mesma data de cada lançamento bancário"/>
                    </h:panelGroup>

                    <h:panelGroup style="margin-top: 6px; margin-left: 4px;">
                        <i class="fa-icon-question-sign tooltipster"
                           style="font-size: 18px;"
                           title="A data de quitação do lançamento de cada ${MovContaControle.labelContaPagarReceberConc} será a mesma data da quitação</br> que vem no extrato bancário do respectivo lançamento dela."></i>
                    </h:panelGroup>
                </h:panelGroup>

                <%--CONTA--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Todos registros/contas selecionados na tela anterior serão quitadas com essa Conta"
                              style="display: grid; margin-top: 9px;"
                              value="Conta:*"/>
                <h:panelGroup layout="block" styleClass="cb-container">
                    <h:selectOneMenu value="#{MovContaControle.codContaVOUtilizarQuitacaoEmMassa}"
                                     id="conta"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">

                        <f:selectItems value="#{MovContaControle.listaSelectItemContaPagamentoConciliacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>


                <%--FORMA DE PAGAMENTO--%>
                <h:panelGroup id="textFormaPgto">
                    <h:outputText styleClass="tituloCampos tooltipster"
                                  title="Todos registros/contas selecionados na tela anterior serão quitadas com essa Forma de Pagamento"
                                  style="display: grid; margin-top: 6px;"
                                  value="Forma Pagamento:*"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="cb-container">

                    <h:selectOneMenu value="#{MovContaControle.codFormaPgtoQuitacaoConciliacao}"
                                     id="selecionarFormaPGTO"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">
                        <f:selectItems value="#{MovContaControle.listaSelectItemFormaPagamentoConciliacao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>


                <%--PLANO DE CONTAS--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              style="display: block;"
                              title="Se você não informar nada aqui, então cada registro terá seu plano de contas mantido.
                              </br> Se você informar, então TODOS os registros/contas selecionados na tela anterior serão quitadas com esse Plano de Contas"
                              value="Plano de Contas:"/>
                <h:panelGroup id="planoContasQuitarContaEmMassa">
                    <h:inputText id="nomePlanoSelecionadoRateioQuitarContaEmMassa" size="50" maxlength="50"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{PlanoContasControle.planoNome}">
                        <a4j:support event="onchange"
                                     action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomePlanoSelecionadoRateioQuitarContaEmMassa" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Plano de Contas encontrado"
                                        suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionResponsavelRateioQuitarEmMassa">
                        <a4j:support event="onselect" reRender="planoContasCriarNovaConta"
                                     focus="nomeCentroSelecionadoRateio"
                                     action="#{PlanoContasControle.selecionarPlanoContas}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.tipoPadrao.descricao}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>


                <%--CENTRO DE CUSTOS--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Se você não informar nada aqui, então cada registro terá seu centro de custos mantido.
                              </br> Se você informar, então TODOS os registros/contas selecionados na tela anterior serão quitadas com esse Centro de Custos"
                              style="display: block;"
                              value="Centro de Custo:"/>
                <h:panelGroup id="centroCustosQuitarConta">
                    <h:inputText id="nomeCentroSelecionadoRateioQuitarConta" size="50" maxlength="50"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{CentroCustosControle.centroNome}">
                        <a4j:support event="onchange"
                                     action="#{CentroCustosControle.setarCentroVazio}" reRender="formLanc"/>
                    </h:inputText>

                    <rich:suggestionbox height="200" width="400"
                                        for="nomeCentroSelecionadoRateioQuitarConta" status="statusInComponent"
                                        immediate="true"
                                        nothingLabel="Nenhum Centro de Custos encontrado"
                                        suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                        minChars="1" rowClasses="linhaImpar, linhaPar" var="result"
                                        id="suggestionCentroCustoRateioQuitar">
                        <a4j:support event="onselect" reRender="centroCustosCriarNovaConta" focus="rateioFormaPagto"
                                     action="#{CentroCustosControle.selecionarCentroCusto}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall"
                                          value="#{result.descricaoCurta}"/>
                        </h:column>
                    </rich:suggestionbox>
                </h:panelGroup>


            </h:panelGrid>

            <%--BOTÕES--%>
            <h:panelGrid columns="1" styleClass="centralizado" width="100%">
                <h:panelGroup>
                    <a4j:commandButton id="confirmacao"
                                       action="#{MovContaControle.quitarConciliacaoEmMassa}"
                                       value="Quitar/Conciliar"
                                       reRender="form:panelGroupAtualizarLancarPagamento, checkMarcarTransaction, form:footerGeral, form:checkMarcarTodasTransaction"
                                       styleClass="botoes nvoBt"
                                       style="background-color: #00c350 !important;"
                                       oncomplete="#{MovContaControle.msgAlert}#{MovContaControle.mensagemNotificar}">
                    </a4j:commandButton>


                </h:panelGroup>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

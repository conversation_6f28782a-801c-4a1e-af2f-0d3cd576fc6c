<h:inputText id="bancoSelecionadoEdit"
             size="50"
             style="text-align-last: left; width: 90%; display: block; margin-top: 3px;"
             styleClass="cssColunaDireita"
             maxlength="50"
             onfocus="focusinput(this);"
             value="#{MovContaControle.movContaEditarLote.contaBancariaFornecedorVO.bancoVO.nome}">
</h:inputText>

<rich:suggestionbox height="200" width="400"
                    for="bancoSelecionadoEdit"
                    status="statusInComponent"
                    immediate="true"
                    suggestionAction="#{MovContaControle.executarAutocompleteConsultaBancosComISPB}"
                    minChars="1"
                    nothingLabel="Nenhum Banco encontrado"
                    rowClasses="linhaImpar, linhaPar"
                    var="bancoSelecionadoEdit" id="suggestionBancoEdit">
    <a4j:support event="onselect"
                 reRender="formNovoLotePagamentoEdicaoLancamento"
                 action="#{MovContaControle.selecionarBancoEscolhidoEdicaoPagamento}">
    </a4j:support>
    <h:column>
        <f:facet name="header">
            <h:outputText value="Codigo do banco" styleClass="textverysmall"/>
        </f:facet>
        <h:outputText style="margin-left: 35px" styleClass="textverysmall" value="#{bancoSelecionadoEdit.codigoBanco}"/>
    </h:column>
    <h:column>
        <f:facet name="header">
            <h:outputText value="Descri��o" styleClass="textverysmall"/>
        </f:facet>
        <h:outputText styleClass="textverysmall" value="#{bancoSelecionadoEdit.nome}"/>
    </h:column>
    <h:column>
        <f:facet name="header">
            <h:outputText value="ISPB" styleClass="textverysmall"/>
        </f:facet>
        <h:outputText style="margin-left: 35px" styleClass="textverysmall" value="#{bancoSelecionadoEdit.ispb}"/>
    </h:column>
</rich:suggestionbox>


<%@include file="imports.jsp" %>
<a4j:outputPanel>
	<rich:modalPanel id="modalPanelLancarPagamentoOnline" autosized="true" styleClass="novaModal"
					 minWidth="500" height="200" width="700" top="100" shadowOpacity="true">
		<f:facet name="header">
			<h:panelGroup>
				<h:outputText value="Lan�ar Pagamento Online"></h:outputText>
			</h:panelGroup>
		</f:facet>
		<f:facet name="controls">
		</f:facet>
		<h:form id="formLancarPagamentoOnline">
			<h:panelGrid width="100%" columns="2" cellpadding="2" columnClasses="tituloCampos"
						 rowClasses="linhaPar, linhaImpar">
				<h:outputText styleClass="tituloCampos"
							  value="Descri��o:"></h:outputText>
				<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}"
							  value="#{MovContaControle.contaQuitacao.descricao}"></h:outputText>

				<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}"
							  value="Vencimento:"></h:outputText>
				<h:outputText styleClass="tituloCampos" rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}"
							  value="#{MovContaControle.contaQuitacao.dataVencimento_Apresentar}"></h:outputText>

				<h:outputText styleClass="tituloCampos" style="line-height: 5vh" value="Conta:"
							  rendered="#{MovContaControle.usarMovimentacao}"></h:outputText>
				<h:panelGroup layout="block" styleClass="block cb-container" style="font-size: 12px;">
					<h:selectOneMenu value="#{MovContaControle.contaQuitacao.contaVO.codigo}" id="contaOn"
									 rendered="#{MovContaControle.usarMovimentacao}"
									 onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form">
						<f:selectItems value="#{MovContaControle.listaComboContaQuitacaoOnline}"></f:selectItems>
						<a4j:support event="onchange"
									 action="#{MovContaControle.selecionarConta}"
									 reRender="formLancarPagamentoOnline"
									 oncomplete="#{MovContaControle.msgAlert}"></a4j:support>
					</h:selectOneMenu>
				</h:panelGroup>
				<h:outputText styleClass="tituloCampos" style="line-height: 5vh;"
							  value="Data Quita��o:"></h:outputText>
				<h:panelGroup styleClass="dateTimeCustom" style="font-size: 12px;">
					<h:inputText id="dataQuitacaoOnline"
								 styleClass="form"
								 style="margin-top: -5.2%;"
								 onkeypress="return mascara2(this.form, this.id, '99/99/9999 - 99:99:99', event);"
								 onchange="validar_Data_horas_minutos(this.id);"
								 value="#{MovContaControle.contaQuitacao.dataQuitacao_Apresentar}"></h:inputText>

					<rich:calendar id="dataQuitacaoCalendarOnline"
								   value="#{MovContaControle.dataQuitacaoCalendario}"
								   buttonIcon="/imagens_flat/calendar-button.svg"
								   direction="bottom-left"
								   inputSize="10"
								   styleClass="tituloboxcentro2"
								   showInput="false"
								   inputClass="form"
								   datePattern="dd/MM/yyyy"
								   zindex="2"
								   showWeeksBar="false">
						<a4j:support event="onchanged" reRender="dataQuitacaoOnline"
									 action="#{MovContaControle.calendarioQuitacao}" />
					</rich:calendar>
				</h:panelGroup>
			</h:panelGrid>
			<rich:dataTable rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}"
					id="tableFomaPagtoOnline" value="#{MovContaControle.formasPagamentoQuitacao}"  var="formaOnline" width="100%">
				<rich:column>
					<h:panelGrid width="100%" columns="2" columnClasses="colunaDireita,colunaEsquerda" rowClasses="linhaPar, linhaImpar">
						<h:outputText styleClass="tituloCampos" value="Forma de Pagamento:"></h:outputText>

						<h:panelGrid width="100%" columns="2" columnClasses="colunaEsquerda, colunaDireita"
									 cellpadding="0" cellspacing="0">
							<h:panelGroup layout="block" styleClass="block cb-container" style="font-size: 12px;">
								<h:selectOneMenu value="#{formaOnline.formaPagamento.codigo}"
												 id="selecionarFormaPGTOOnline"
												 onblur="blurinput(this);"
												 onfocus="focusinput(this);"
												 styleClass="form">
									<f:selectItems value="#{MovContaControle.listaComboFormaPagtoOnline}"></f:selectItems>

									<a4j:support action="#{MovContaControle.selecionarFormaPagamentoOnline}"
												 reRender="formLancarPagamentoOnline"
												 event="onchange"
												 oncomplete="#{MovContaControle.msgAlert}"></a4j:support>

								</h:selectOneMenu>
							</h:panelGroup>
						</h:panelGrid>
						<h:outputText styleClass="tituloCampos"
									  value="C�digo de Barras:"></h:outputText>
						<h:outputText styleClass="tituloCampos"
									  rendered="#{MovContaControle.contaQuitacao.pagamentoSimples}"
									  value="#{MovContaControle.contaQuitacao.codigoBarras}"></h:outputText>
						<h:outputText styleClass="tituloCampos"
									  value="Valor:"></h:outputText>
						<h:outputText styleClass="tituloCampos"
									  value="#{MovContaControle.contaQuitacao.valor_Apresentar}"></h:outputText>
					</h:panelGrid>
				</rich:column>
			</rich:dataTable>

			<h:panelGrid columns="1" styleClass="centralizado" width="100%" >
				<h:panelGroup>
					<a4j:commandButton id="confirmacaoOnline"
									   action="#{MovContaControle.gravarQuitacaoOnline}"
									   onclick="if(!validar_Data_horas_minutos('formLancarPagamentoOnline:dataQuitacaoOnline')){ return false};"
									   value="Pagar"
									   reRender="form"
									   styleClass="botoes nvoBt"
									   oncomplete="Richfaces.hideModalPanel('modalPanelLancarPagamentoOnline'); #{MovContaControle.mensagemNotificar}">
					</a4j:commandButton>

					<a4j:commandButton id="btnCancelarOnline"
									   style="padding-left:10px"
									   value="Cancelar"
									   styleClass="botoes nvoBt btSec"
									   action="#{MovContaControle.cancelarQuitacaoOnline}"
									   reRender="form"
									   oncomplete="#{MovContaControle.abrirFecharModalPagto}">
					</a4j:commandButton>



				</h:panelGroup>
			</h:panelGrid>
			<script>
				var modal = document.getElementById('modalPanelLancarPagamentoOnlineCDiv');
				var tamanho = modal.offsetWidth;
				var altura = modal.offsetHeight;
				if (altura > 600) {
					modal.style.top = (window.innerHeight - altura) / 4;
				}
				modal.style.left = (window.innerWidth - tamanho) / 2;
				carregarMaskInput();
			</script>
		</h:form>
	</rich:modalPanel>
</a4j:outputPanel>
    
<%-- 
    Document   : include_DetalhamentoLancamentoZillyonWeb
    Created on : 05/10/2011, 14:30:16
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="include_imports.jsp" %>
<link href="../../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../../css/otimize.css" rel="stylesheet" type="text/css">


<f:view>
        <br>



         <h:panelGroup id="panelGridDadosCliente" rendered="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO == null}"> 
        <%--   <h:panelGroup id="panelGridDadosCliente" rendered="#{DemonstrativoFinanceiroControle.tipoRelatorioDf.codigo == 2}">--%>
           <div class="sep" style="margin: 4px 0 5px 0;"><img src="../../../images/shim.gif"></div>
           <div style="clear: both;" class="text">
               <p style="margin-bottom: 6px;"><img
                       src="../../../images/arrow2.gif" width="16" height="16"
                       style="vertical-align: middle; margin-right: 6px;"><h:outputText
                       style="font-weight: bold" value="Dados do Cliente" /></p>
               <div class="sep" style="margin: 4px 0 5px 0;"><img src="../../../images/shim.gif"></div>
           </div>
           <table width="100%">
             <tr>
                 <td style="text-align:right;" width="10%"><h:outputText  styleClass="textsmall" value="Cliente:"> </h:outputText></td>
                 <td><h:outputText styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.nomeCliente}"> </h:outputText></td>
             </tr>
             <tr>
                 <td style="text-align:right;" width="10%"><h:outputText  styleClass="textsmall" value="Matrícula:"> </h:outputText></td>
                 <td><h:outputText styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.matriculaCliente}"> </h:outputText></td>
             </tr>

             <h:panelGroup rendered="#{DemonstrativoFinanceiroControle.tipoRelatorioDf.codigo == 2}">
                 <tr>
                     <td style="text-align:right;"><h:outputText  styleClass="textsmall" value="Valor Produto: " /> </td>
                     <td><h:outputText styleClass="textsmall"  value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.valorMovProduto}" >
                          <f:converter converterId="FormatadorNumerico" />
                          </h:outputText>
                     </td>
                 </tr>
             </h:panelGroup>

           </table>
        </h:panelGroup>


        <h:panelGroup id="panelGridDadosContrato" rendered="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO != null}">
           <div class="sep" style="margin: 4px 0 5px 0;"><img src="../../../images/shim.gif"></div>
           <div style="clear: both;" class="text">
               <p style="margin-bottom: 6px;"><img
                       src="../../../images/arrow2.gif" width="16" height="16"
                       style="vertical-align: middle; margin-right: 6px;"><h:outputText
                       style="font-weight: bold" value="Dados do Contrato" /></p>
               <div class="sep" style="margin: 4px 0 5px 0;"><img src="../../../images/shim.gif"></div>
           </div>

          <table width="100%">
            <tr>
                <td width="60%">
                    <table width="100%">
                         <tr>
                             <td style="text-align:right;" width="25%"><h:outputText  styleClass="textsmall" value="Contrato:"> </h:outputText></td>
                             <td><h:outputText styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO.codigo}"> </h:outputText></td>
                         </tr>

                         <tr>
                             <td style="text-align:right;" ><h:outputText  styleClass="textsmall" value="Cliente:"> </h:outputText></td>
                             <td><h:outputText styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO.pessoa.nome}"> </h:outputText></td>
                         </tr>


                         <tr>
                             <td style="text-align:right;" ><h:outputText styleClass="textsmall" value="Plano:"> </h:outputText> </td>
                             <td><h:outputText styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO.plano.descricao}"> </h:outputText> </td>
                         </tr>

                         <tr>
                             <td style="text-align:right;"><h:outputText  styleClass="textsmall" value="Duração:"> </h:outputText> </td>
                             <td><h:outputText styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO.contratoDuracao.numeroMeses} Mês"> </h:outputText> </td>
                         </tr>
                         <tr>
                             <td style="text-align:right;"><h:outputText  styleClass="textsmall" value="Data Início:" /> </td>
                             <td><h:outputText styleClass="textsmall" id="dtInicioAuto" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO.vigenciaDe_Apresentar}" /> </td>
                         </tr>
                         <tr>
                             <td style="text-align:right;"><h:outputText  styleClass="textsmall" value="Data Término Original: " /> </td>
                             <td><h:outputText styleClass="textsmall" id="dtFimAutoOrig" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO.vigenciaAte_Apresentar}" /> </td>
                         </tr>

                         <tr>
                             <td style="text-align:right;"><h:outputText  styleClass="textsmall" value="Valor Contrato: " /> </td>
                             <td><h:outputText styleClass="textsmall" id="valorContrato" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO.valorFinal}" >
                                  <f:converter converterId="FormatadorNumerico" />
                                  </h:outputText>
                             </td>
                         </tr>
                         <tr>
                             <td style="text-align:right;"><h:outputText  styleClass="textsmall" value="Valor Plano: " /> </td>
                             <td><h:outputText styleClass="textsmall" id="valorPlano" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.contratoVO.valorBaseCalculo}" >
                                  <f:converter converterId="FormatadorNumerico" />
                                  </h:outputText>
                             </td>
                         </tr>
                         <tr>
                             <td style="text-align:right;"><h:outputText  styleClass="textsmall" value="Valor Plano Mensal: " /> </td>
                             <td><h:outputText styleClass="textsmall" id="valorMensal" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.valorPlanoMensal}" >
                                  <f:converter converterId="FormatadorNumerico" />
                                  </h:outputText>
                             </td>
                         </tr>
                    </table>
                </td>
                <td width="40%" rowspan="8">

                    <table width="100%">
                        <tr>
                        <td width="100%">
                            <h:outputText  styleClass="textsmall" value="Modalidades do Plano " />
                            <rich:dataTable width="100%" id="tableModalidades"  headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                                            columnClasses="esquerda, direita"
                                            value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.listaContratoModalidade}"
                                            var="objModalidade">

                                <rich:column  width="50%"   filterEvent="onkeyup">
                                    <f:facet   name="header">
                                        <h:outputText id="idModalidade"  value="Modalidade "/>
                                    </f:facet>
                                    <h:outputText id="idNomeMod" value="#{objModalidade.nomeModalidade}" />
                                </rich:column >
                                <rich:column style="text-align:right;" width="25%"  filterEvent="onkeyup">
                                    <f:facet  name="header">
                                        <h:outputText id="idValor"  value="Valor Mensal "/>
                                    </f:facet>
                                       <h:outputText id="valorMod" value="#{objModalidade.valor}" >
                                         <f:converter converterId="FormatadorNumerico" />
                                       </h:outputText>
                                </rich:column>

                                <rich:column width="25%" rendered="#{DemonstrativoFinanceiroControle.tipoRelatorioDf.codigo == 1}" style="text-align:right;"   filterEvent="onkeyup">
                                    <f:facet  name="header">
                                        <h:outputText id="idValorPago"  value="Valor Pago "/>
                                    </f:facet>
                                       <h:outputText id="valorModPago" value="#{objModalidade.valorPago}" >
                                         <f:converter converterId="FormatadorNumerico" />
                                       </h:outputText>
                                </rich:column>

                            </rich:dataTable>
                          </td>
                          </tr>
                          <tr>
                           <td width="100%">
                               <table width="100%">
                                   <td width="50%"><h:outputText styleClass="textsmall" value="Total: " /> </td>
                                   <td width="25%" style="text-align:right; font-family: Arial,Helvetica,sans-serif;font-size: 11px;line-height: 125%; text-decoration: none; color: black" ><h:outputText   value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.valorPlanoMensal}"> <f:converter converterId="FormatadorNumerico" /> </h:outputText> </td>
                                   <c:if test="${DemonstrativoFinanceiroControle.tipoRelatorioDf.codigo == 1}">
                                     <td width="25%" style="text-align:right; font-family: Arial,Helvetica,sans-serif;font-size: 11px;line-height: 125%; text-decoration: none; color: black"><h:outputText  value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.totalPagoPlano}"> <f:converter converterId="FormatadorNumerico" /> </h:outputText> </td>
                                   </c:if>
                               </table>
                          </td>
                        </tr>


                    </table>
                </td>

             </tr>
         </table>
     </h:panelGroup>

      <h:panelGroup id="panelGridPagamentos" rendered="#{DemonstrativoFinanceiroControle.tipoRelatorioDf.codigo == 1 && DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.movPagamento > 0 }">

            <div class="sep" style="margin: 4px 0 5px 0;"><img src="../../../images/shim.gif"></div>
            <div style="clear: both;" class="text">
                <p style="margin-bottom: 6px;"><img
                        src="../../../images/arrow2.gif" width="16" height="16"
                        style="vertical-align: middle; margin-right: 6px;"><h:outputText
                        style="font-weight: bold" value="Dados do Pagamento" /></p>
                <div class="sep" style="margin: 4px 0 5px 0;"><img src="../../../images/shim.gif"></div>
            </div>

            <table width="100%">
               <tr>
                   <td width="15%" style="text-align:right;"> <h:outputText  styleClass="textsmall" value="Recibo: " /> </td>
                   <td style="text-align:left;"> <h:outputText  styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.reciboPagamento}" /> </td>
               </tr>
               <tr>
                   <td width="15%" style="text-align:right;"> <h:outputText  styleClass="textsmall" value="Pagamento: " /> </td>
                   <td style="text-align:left;"> <h:outputText  styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.movPagamento}" /> </td>
               </tr>
               <tr>
                   <td width="15%" style="text-align:right;"> <h:outputText  styleClass="textsmall" value="Nome Pagador: " /> </td>
                   <td style="text-align:left;"> <h:outputText  styleClass="textsmall" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.pessoaPagador}" /> </td>
               </tr>
            </table>

            <table width="100%">
               <tr>

                   <td >
                        <table width="100%">
                            <tr>
                                <td>
                                    <h:outputText  styleClass="textsmall" value="Formas de Pagamento " />
                                    <rich:dataTable id="tableFormasPagto"  headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                                                    columnClasses="centralizado, centralizado, direita"
                                                    value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.listaFormaPagamento}"
                                                    var="objFormaPagto">

                                        <rich:column  width="50%"   filterEvent="onkeyup">
                                            <f:facet   name="header">
                                                <h:outputText id="idDescForma"  value="Descrição "/>
                                            </f:facet>
                                            <h:outputText id="idFormaPgto" value="#{objFormaPagto.descricao}" />
                                        </rich:column >

                                        <rich:column  width="50%"   filterEvent="onkeyup">
                                            <f:facet   name="header">
                                                <h:outputText id="idDataCompt"  value="Data Compensação "/>
                                            </f:facet>
                                            <h:outputText id="iddtaComp" value="#{objFormaPagto.dataCompensacaoApresentar}" />
                                        </rich:column >


                                        <rich:column style="text-align:right;" width="50%"  filterEvent="onkeyup">
                                            <f:facet  name="header">
                                                <h:outputText id="idValorForma"  value="Valor "/>
                                            </f:facet>
                                               <h:outputText id="valorformaPgto" value="#{objFormaPagto.valor}" >
                                                 <f:converter converterId="FormatadorNumerico" />
                                               </h:outputText>
                                        </rich:column>
                                    </rich:dataTable>
                                </td>
                            </tr>
                        </table>
                   </td>
                   <%--
                 <td>
                        <table width="100%">
                            <tr>
                                <td>
                                    <h:outputText  styleClass="textsmall" value="Parcelas Pagas" />

                                    <rich:dataTable id="tableParcelasPagas"  headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                                                    columnClasses="esquerda, direita"
                                                    value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.listaMovParcela}"
                                                    var="objMovParcela">

                                        <rich:column  width="70%"   filterEvent="onkeyup">
                                            <f:facet   name="header">
                                                <h:outputText   value="Descrição "/>
                                            </f:facet>
                                            <h:outputText value="#{objMovParcela.descricao}" />
                                        </rich:column >

                                        <rich:column style="text-align:right;" width="30%"  filterEvent="onkeyup">
                                            <f:facet  name="header">
                                                <h:outputText   value="Valor "/>
                                            </f:facet>
                                               <h:outputText value="#{objMovParcela.valorParcela}" >
                                                 <f:converter converterId="FormatadorNumerico" />
                                               </h:outputText>
                                        </rich:column>
                                    </rich:dataTable>
                                </td>
                            </tr>
                        </table>
                   </td>
                   --%>
                 <td >
                        <table width="100%">
                            <tr>
                                <td>
                                    <h:outputText  styleClass="textsmall" value="Produtos Pagos" />

                                    <rich:dataTable id="tableProdutosPagos"  headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                                                    columnClasses="esquerda, direita"
                                                    value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.listaProdutos}"
                                                    var="objProduto">

                                        <rich:column  width="50%"   filterEvent="onkeyup">
                                            <f:facet   name="header">
                                                <h:outputText   value="Descrição "/>
                                            </f:facet>
                                            <h:outputText value="#{objProduto.descricao}" />
                                        </rich:column >
                                        <rich:column style="text-align:right;" width="25%"  filterEvent="onkeyup">
                                            <f:facet  name="header">
                                                <h:outputText   value="Valor Original "/>
                                            </f:facet>
                                               <h:outputText value="#{objProduto.valorFinal}" >
                                                 <f:converter converterId="FormatadorNumerico" />
                                               </h:outputText>
                                        </rich:column>

                                        <rich:column style="text-align:right;" width="25%"  filterEvent="onkeyup">
                                            <f:facet  name="header">
                                                <h:outputText   value="Valor Pago "/>
                                            </f:facet>
                                               <h:outputText value="#{objProduto.valorBaseCalculo}" >
                                                 <f:converter converterId="FormatadorNumerico" />
                                               </h:outputText>
                                        </rich:column>

                                    </rich:dataTable>
                                </td>
                            </tr>
                        </table>
                   </td>

               </tr>

            </table>

        </h:panelGroup>

        <div style="margin: 0px 0 0px 8;">
            <h:panelGroup   id="panelGridLancamentosPagto" rendered="#{DemonstrativoFinanceiroControle.tipoRelatorioDf.codigo == 1}">
               <table   width="100%">
                   <h:outputText  styleClass="textsmall" value="Contratos Pagos" />
                    <rich:dataTable id="tableLancametnosPagtos"  headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                                    columnClasses="esquerda, esquerda, centralizado, direita"
                                    value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.listaLancamentoDF}"
                                    var="objLancamentoDF">

                        <rich:column  width="10%"   filterEvent="onkeyup">
                            <f:facet   name="header">
                                <h:outputText   value="Contrato "/>
                            </f:facet>
                            <h:outputText style="#{objLancamentoDF.style}" value="#{objLancamentoDF.contrato}" />
                        </rich:column >

                        <rich:column  width="30%"   filterEvent="onkeyup">
                            <f:facet   name="header">
                                <h:outputText   value="Cliente "/>
                            </f:facet>
                            <h:outputText style="#{objLancamentoDF.style}" value="#{objLancamentoDF.nomePessoa}" />
                        </rich:column >

                        <%--
                        <rich:column  width="30%"   filterEvent="onkeyup">
                            <f:facet   name="header">
                                <h:outputText   value="Descrição "/>
                            </f:facet>
                            <h:outputText value="#{objLancamentoDF.descricaoLancamento}" />
                        </rich:column >
                        --%>

                        <rich:column  width="30%" style="text-align:right;"   filterEvent="onkeyup">
                            <f:facet  name="header">
                                <h:outputText   value="Valor Pago"/>
                            </f:facet>
                               <h:outputText style="#{objLancamentoDF.style}" value="#{objLancamentoDF.valorLancamento}" >
                                 <f:converter converterId="FormatadorNumerico" />
                               </h:outputText>
                        </rich:column>

                    </rich:dataTable>


               </table>
            </h:panelGroup>
        </div>


        <div class="sep" style="margin: 4px 0 5px 0;"><img src="../../../images/shim.gif"></div>
        <div style="clear: both;" class="text">
            <p style="margin-bottom: 6px;"><img
                    src="../../../images/arrow2.gif" width="16" height="16"
                    style="vertical-align: middle; margin-right: 6px;"><h:outputText
                    style="font-weight: bold" value="Dados do Rateio" /></p>
            <div class="sep" style="margin: 4px 0 5px 0;"><img src="../../../images/shim.gif"></div>
        </div>

       <table width="100%">
         <tr>
            <td>
               <a4j:form>
                <table width="100%">
                    <tr>
                      <td width="100%">
                          <h:panelGroup style="text-align:left; font-family: Arial,Helvetica,sans-serif;font-size: 11px;line-height: 125%; text-decoration: none; color: blue">
                              <h:outputText value="Total Rateio:"/>
                              <h:outputText value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.valorLancamento}"> <f:converter converterId="FormatadorNumerico" /> </h:outputText>
                              <h:outputText rendered="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.apresentarValorMultaJuros}" value="(Multa e Juros: " />
                              <h:outputText rendered="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.apresentarValorMultaJuros}" value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.valorMultaJuros}"> <f:converter converterId="FormatadorNumerico" /> </h:outputText>
                              <h:outputText rendered="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.apresentarValorMultaJuros}" value=")" />
                              <h:outputText value=", para o " />
                              <h:outputText id="labelAgrupador"  value="#{DemonstrativoFinanceiroControle.labelAgrupadorSelecionado} :" />
                              <h:outputText id="nomeAgrupadorSelecionado"  value="#{DemonstrativoFinanceiroControle.nomeAgrupadorSelecionado}" />
                           </h:panelGroup>

                        <rich:dataTable width="100%" id="tableRateios"  headerClass="consulta" rowClasses="linhaPar,linhaImpar "
                                        columnClasses="esquerda, centralizado, centralizado, direita, centralizado, centralizado, direita" rows="6"
                                        value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.listaRateios}"
                                        var="objRateio">

                            <rich:column width="30%"   filterEvent="onkeyup">
                                <f:facet   name="header">
                                    <h:outputText   value="Descrição "/>
                                </f:facet>
                                <h:outputText value="#{objRateio.descricao}" />
                            </rich:column >

                            <rich:column width="20%"    filterEvent="onkeyup">
                                <f:facet   name="header">
                                    <h:outputText   value="Plano Conta "/>
                                </f:facet>
                                <h:outputText value="#{objRateio.nomePlano}" />
                            </rich:column >

                            <rich:column width="5%"   filterEvent="onkeyup">
                                <f:facet   name="header">
                                    <h:outputText   value=" % "/>
                                </f:facet>
                                <h:outputText rendered="#{objRateio.percentagem >0}" value="#{objRateio.percentagem}" />
                            </rich:column >

                            <rich:column width="10%"  style="#{objRateio.stylePlanoConta}"   filterEvent="onkeyup">
                                <f:facet  name="header">
                                    <h:outputText   value="Valor "/>
                                </f:facet>
                                <h:outputText rendered="#{objRateio.totalPlanoConta >0}" value="#{objRateio.totalPlanoConta}" >
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </rich:column>

                            <rich:column  width="20%"  filterEvent="onkeyup">
                                <f:facet   name="header">
                                    <h:outputText   value="Centro Custo "/>
                                </f:facet>
                                <h:outputText value="#{objRateio.nomeCentro}" />
                            </rich:column >

                            <rich:column  width="5%"   filterEvent="onkeyup">
                                <f:facet   name="header">
                                    <h:outputText   value=" % "/>
                                </f:facet>
                                <h:outputText rendered="#{objRateio.percentagemCentroCusto >0}" value="#{objRateio.percentagemCentroCusto}" />
                            </rich:column >

                            <rich:column width="10%"  style="#{objRateio.styleCentroCusto}"  filterEvent="onkeyup">
                                <f:facet  name="header">
                                    <h:outputText   value="Valor "/>
                                </f:facet>
                                   <h:outputText rendered="#{objRateio.totalCentroCusto >0}"  value="#{objRateio.totalCentroCusto}" >
                                     <f:converter converterId="FormatadorNumerico" />
                                   </h:outputText>
                            </rich:column>
                        </rich:dataTable>
                         <rich:datascroller for="tableRateios" maxPages="5" />
                    </td>
                    </tr>
                    <!--
                    <tr>
                        <td width="100%">
                            <table width="100%">
                             <tr>
                                 <td width="30%" style="text-align:left; font-family: Arial,Helvetica,sans-serif;font-size: 11px;line-height: 125%; text-decoration: none; color: red"> TOTAL RATEIO: <h:outputText   value="#{DemonstrativoFinanceiroControle.detalhamentoLancamentoDF_Vo.valorLancamento}"> <f:converter converterId="FormatadorNumerico" /> </h:outputText></td>
                                 <td width="20%"></td>
                                 <td width="5%" ></td>
                                 <td width="10%">  </td>
                                 <td width="20%" ></td>
                                 <td width="5%"></td>
                                 <td width="10%"></td>
                             </tr>
                            </table>
                        </td>
                    </tr> -->
                </table>
               </a4j:form>

            </td>
         </tr>
       </table>



<h:panelGrid id="panelMensagens" columns="1" width="100%"
        styleClass="tabMensagens">
        <h:outputText styleClass="mensagemDetalhada"
                id="mensagemDetalhada2"
                value="#{DemonstrativoFinanceiroControle.mensagemDetalhada}" />
</h:panelGrid>


</f:view>

<%-- 
    Document   : include_modalRateios
    Created on : 07/10/2011, 11:11:42
    Author     : carla
--%>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="include_imports.jsp" %>
<a4j:outputPanel> 
    <!-- Modal de Edição de Rateios -->
    <rich:modalPanel id="modalIncluirRateio" autosized="true"
                     showWhenRendered="#{RateioIntegracaoControle.abrirModalRateio}"
                     shadowOpacity="true" width="550" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Incluir/Editar Rateio"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <a4j:commandLink onclick="fireElement('formRateioEdicao:botaoCancelar');">
                    <h:graphicImage value="/imagens/close.png" style="border: 0px;"/>
                </a4j:commandLink>
            </h:panelGroup>
        </f:facet>


        <a4j:form id="formRateioEdicao">
            <center>
                <h:outputText styleClass="tituloBold" value="#{RateioIntegracaoControle.rateio.descricao}" />
            </center>
            <br/>
            <h:panelGrid id="dadosRateio" columns="2" width="100%" cellpadding="0" cellspacing="0"
                         style="font-size: 12px;">
                <h:panelGroup>
                    <c:out value="Tipo do Rateio : " />
                </h:panelGroup>
                <h:panelGroup>
                    <h:selectOneRadio id="tipoRateio" styleClass="tituloCampos" value="#{RateioIntegracaoControle.rateio.tipoRateio}">
                        <f:selectItems value="#{RateioIntegracaoControle.tipos}"/>
                        <a4j:support action="#{RateioIntegracaoControle.alternarTipo}" reRender="formRateioEdicao" event="onclick"></a4j:support>
                    </h:selectOneRadio>
                </h:panelGroup>
                <h:panelGroup >
                    <c:if test="${RateioIntegracaoControle.mostrarEmpresa}">
                    <c:out value="Empresa : "  />
                    </c:if>
                </h:panelGroup>
                <h:panelGroup style="padding-left: 3px;">
                    <h:selectOneMenu id="empresaRateio"  rendered="#{RateioIntegracaoControle.mostrarEmpresa}" styleClass="tituloCampos" value="#{RateioIntegracaoControle.rateio.empresa}">
                        <f:selectItems value="#{RateioIntegracaoControle.listaEmpresas}"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <!------------- suggestion box plano de contas ------------------------- -->
                <c:if test="${RateioIntegracaoControle.tipoPlano}">
                    <!-- Plano de Conta -->

                    <c:out value="Plano de Contas" />
                    <h:panelGroup>
                        <table>
                            <tr valign="top">
                                <td><h:inputText id="nomePlanoSelecionado"
                                             size="50"
                                             maxlength="50"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{PlanoContasControle.planoNome}" >
                                        <a4j:support event="onchange" action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="nomePlanoSelecionado"/>
                                    </h:inputText>
                                    <rich:suggestionbox   height="200" width="400"
                                                          for="nomePlanoSelecionado"
                                                          status="statusInComponent"
                                                          immediate="true"
                                                          suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                                          minChars="1"
                                                          rowClasses="linhaImpar, linhaPar"
                                                          var="result"  id="suggestionResponsavel" >
                                        <a4j:support event="onselect"
                                                     reRender="nomePlanoSelecionado"
                                                     action="#{PlanoContasControle.selecionarPlanoContas}">
                                        </a4j:support>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Nome"  styleClass="textverysmall"/>
                                            </f:facet>
                                            <h:outputText styleClass="textverysmall" value="#{result.descricaoCurta}" />
                                        </h:column>
                                        <h:column >
                                            <f:facet name="header">
                                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                                            </f:facet>
                                            <h:outputText styleClass="textverysmall" value="#{result.tipoPadrao.descricao}" />
                                        </h:column>
                                    </rich:suggestionbox>

                                </td>
                                <td>
                                    <a4j:commandButton id="btAddPlano" value="Consultar"
                                                       image="../../../imagens/btn_Consultar.png"
                                                       action="#{PlanoContasControle.verificarConsultaCorretaRateio}"
                                                       reRender="modalPlanos"
                                                       oncomplete="Richfaces.showModalPanel('modalPlanos')" />
                                </td>
                            </tr>
                        </table>
                    </h:panelGroup>   

                </c:if>


                <!------------- suggestion box  Centro de Custos ------------------------- -->
                <c:if test="${RateioIntegracaoControle.tipoCentro and RateioIntegracaoControle.permiteCentroCustos}">

                    <c:out value="Centro de Custo" />
                    <h:panelGroup>
                        <table>
                            <tr valign="top">
                                <td>
                                    <h:inputText  id="nomeCentroSelecionado"
                                                  size="50"
                                                  maxlength="50"
                                                  onblur="blurinput(this);"
                                                  onfocus="focusinput(this);"
                                                  styleClass="form"
                                                  value="#{CentroCustosControle.centroNome}" >
                                        <a4j:support event="onchange" action="#{CentroCustosControle.setarCentroVazio}"/>
                                    </h:inputText>

                                    <rich:suggestionbox   height="200" width="400"
                                                          for="nomeCentroSelecionado"
                                                          status="statusHora"
                                                          immediate="true"
                                                          suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                                          minChars="1"
                                                          rowClasses="linhaImpar, linhaPar"
                                                          var="result"  id="suggestionCentroCusto">
                                        <a4j:support event="onselect"
                                                     reRender="nomeCentroSelecionado"
                                                     action="#{CentroCustosControle.selecionarCentroCusto}">
                                        </a4j:support>
                                        <h:column>
                                            <f:facet name="header">
                                                <h:outputText value="Nome"  styleClass="textverysmall"/>
                                            </f:facet>
                                            <h:outputText styleClass="textverysmall" value="#{result.descricaoCurta}" />
                                        </h:column>

                                    </rich:suggestionbox>
                                </td><td>
                                    <a4j:commandButton id="btAddCentro" value="Consultar"
                                                       image="../../../imagens/btn_Consultar.png"
                                                       action="#{PlanoContasControle.verificarConsultaCorretaRateio}"
                                                       oncomplete="Richfaces.showModalPanel('modalCentros')" />
                                </td></tr>
                        </table>
                    </h:panelGroup>
                </c:if>
                <c:if test="${RateioIntegracaoControle.tipoCentro and not RateioIntegracaoControle.permiteCentroCustos}">
                    <h:panelGroup>
                        <h:outputText styleClass="mensagemDetalhada" value="Para Produtos do tipo 'Aula Avulsa' ou 'Diária' não é"></h:outputText><br/>
                        <h:outputText styleClass="mensagemDetalhada" value="permitido o rateio para centro de custos, pois será"></h:outputText><br/>
                        <h:outputText styleClass="mensagemDetalhada" value="considerado o rateio da modalidade escolhida."></h:outputText>
                    </h:panelGroup>
                </c:if>
                <c:if test="${RateioIntegracaoControle.tipoPlano or RateioIntegracaoControle.permiteCentroCustos}" >
                    <!-- Percentagem -->

                    <c:out value="Percentagem" />
                    <h:panelGroup>
                        <table>
                            <tr><td>
                                    <h:inputText id="valor" onblur="blurinput(this); "
                                                 onfocus="focusinput(this);"
                                                 onkeypress="return(currencyFormat(this,'.',',',event));"
                                                 styleClass="form" maxlength="14"
                                                 value="#{RateioIntegracaoControle.rateio.percentagemDesc}" />
                                </td></tr></table>
                            </h:panelGroup>
                        </c:if>
                    </h:panelGrid>
            <br/>

            <center>
                <!-- Ações -->
                <a4j:commandButton reRender="modalIncluirRateio"
                                   id="botaoEditarRateio"
                                   title="Salvar Edição Rateio"
                                   image="../../imagens/botaoEditar.png"
                                   rendered="#{RateioIntegracaoControle.edicaoRateio}"
                                   action="#{RateioIntegracaoControle.salvarRateioEdicao}"></a4j:commandButton>
                <c:if test="${RateioIntegracaoControle.gravarPlanoContasRateio or RateioIntegracaoControle.tipoPlano or RateioIntegracaoControle.permiteCentroCustos}" >
                    <a4j:commandButton reRender="modalIncluirRateio"
                                       title="Adicionar Rateio"
                                       image="../../imagens/botaoAdicionar.png"
                                       id="botaoAdicionarRateio" value="Adicionar"
                                       rendered="#{RateioIntegracaoControle.adicaoRateio}"
                                       action="#{RateioIntegracaoControle.adicionarRateio}"></a4j:commandButton>
                </c:if>
            </center>
            <br/>



            <!-- Mensagens -->
            <h:panelGrid columns="3" width="100%" id="tabMensagens">
                <!-- MENSAGENS -->
                <c:if test="${not empty RateioIntegracaoControle.mensagem}">
                    <h:panelGrid id="mensagem" columns="1" width="100%" styleClass="tabMensagens">
                        <h:outputText styleClass="mensagem"
                                      value="#{RateioIntegracaoControle.mensagem}" escape="false" />
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{RateioIntegracaoControle.mensagemDetalhada}"
                                      escape="false" />
                    </h:panelGrid>
                </c:if>
            </h:panelGrid>
            <!-- fim das Mensagens -->
            <!-- Lista de Rateios -->
            <h:dataTable id="listaRateios" width="100%" headerClass="subordinado"
                         rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                         value="#{RateioIntegracaoControle.rateiosEdicao}" var="rateio">
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Plano Conta" />
                    </f:facet>
                    <h:outputText value="#{rateio.codigoPlano} - #{rateio.nomePlano}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Centro Custo" />
                    </f:facet>
                    <h:outputText value="#{rateio.codigoCentro} - #{rateio.nomeCentro}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Empresa" />
                    </f:facet>
                    <h:outputText value="#{rateio.nomeEmpresa}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Percentagem" />
                    </f:facet>
                    <h:outputText value="#{rateio.percentagem}" />
                </h:column>
                <h:column rendered="#{RateioIntegracaoControle.tipoOrdemCompra}">
                    <f:facet name="header">
                        <h:outputText value="Tipo" />
                    </f:facet>
                    <h:outputText value="#{rateio.tipoES.descricao}" />
                </h:column>
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="Opções" />
                    </f:facet>
                    <h:panelGroup>
                        <a4j:commandButton id="editarRateioEdicao" 
                                           reRender="formRateioEdicao"
                                           action="#{RateioIntegracaoControle.editarRateioEdicao}"
                                           value="#{msg_bt.btn_editar}" image="../../imagens/botaoEditar.png"
                                           styleClass="botoes" />

                        <h:outputText value="    " />

                        <c:if test="${!RateioIntegracaoControle.gravarPlanoContasRateio}">
                            <a4j:commandButton id="removerRateioEdicao" reRender="listaRateios"
                                               action="#{RateioIntegracaoControle.removerRateioEdicao}"
                                               value="#{msg_bt.btn_excluir}"
                                               image="../../imagens/botaoRemover.png" styleClass="botoes" />
                        </c:if>
                        <c:if test="${RateioIntegracaoControle.gravarPlanoContasRateio}">
                            <a4j:commandButton id="removerRateioEdicao" reRender="listaRateios"
                                               action="#{RateioIntegracaoControle.removerRateioCentroCustoEdicao}"
                                               value="#{msg_bt.btn_excluir}"
                                               image="../../imagens/botaoRemover.png" styleClass="botoes" />
                        </c:if>
                    </h:panelGroup>
                </h:column>
            </h:dataTable>
            <br/>
            <!-- Fim da Lista de Rateios -->


            <a4j:commandButton value="Ok" id="botaoSalvarRateios" 
                               reRender="modalIncluirRateio,panelDfTreeView,panelDfTreeViewModalidades, panelCETreeView"
                               image="../../imagens/botaoGravar.png"
                               action="#{RateioIntegracaoControle.salvarRateios}" 
                               oncomplete="botaoatualizarViewPlanoContas();atualizarTreeViewRateio();"></a4j:commandButton>



                &nbsp;
            <a4j:commandButton value="Cancelar"
                               id="botaoCancelar"
                               image="../../imagens/Cancelar_Modal.png" 
                               reRender="modalIncluirRateio,panelDfTreeView,panelDfTreeViewModalidades, panelCETreeView"
                               action="#{RateioIntegracaoControle.fecharModalRateio}"
                               oncomplete="atualizarTreeViewRateio(); "></a4j:commandButton>



        </a4j:form>

    </rich:modalPanel>
</a4j:outputPanel> 
<!-- FIM: Modal de Edição de Rateios -->


<%@page pageEncoding="ISO-8859-1" %>
<h:panelGrid id="panelCampos" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

	<!-- empresa -->

	<h:outputText  rendered="#{MovContaControle.mostrarCampoEmpresa}" styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_empresa}" />
    <h:outputText  rendered="#{MovContaControle.mostrarCampoEmpresa}" styleClass="tituloCampos" value="#{MovContaControle.movContaVO.empresaVO.nome}" />
      
    
	
	<!-- pessoa -->
	
	<h:outputText styleClass="tituloCampos" value="#{MovContaControle.nomeCampoPessoa}" />
	<h:outputText styleClass="tituloCampos" value="#{MovContaControle.movContaVO.pessoaVO.nome}" />
	
	<h:outputText styleClass="tituloCampos" value="Responsável:" />
	<h:outputText styleClass="tituloCampos" value="#{MovContaControle.movContaVO.usuarioVO.nome}" />
 
<!-- conta  -->

	<h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_conta}" />
	<h:outputText  styleClass="tituloCampos" value="#{MovContaControle.movContaVO.contaVO.descricao}" />

 
<!-- plano de contas  -->   
    <h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && !MovContaControle.somenteVisualizacao}"  styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_planoContas}" />
    <h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && !MovContaControle.somenteVisualizacao}"  styleClass="tituloCampos" value="#{MovContaControle.movContaRateioVO.planoContaVO.descricao}" />
 <!-- centro de custos  -->  
     	
    <h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && !MovContaControle.somenteVisualizacao}" styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}" />
    <h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && !MovContaControle.somenteVisualizacao}" styleClass="tituloCampos" value="#{MovContaControle.movContaRateioVO.centroCustoVO.descricao}" />
    
        
<!-- forma de pagamento -->
	<h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && !MovContaControle.somenteVisualizacao}" styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_formaPagamento}" />
	<h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && !MovContaControle.somenteVisualizacao}" styleClass="tituloCampos" value="#{MovContaControle.movContaRateioVO.formaPagamentoVO.descricao}" />


<!-- tipo de documento -->
	<h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && !MovContaControle.somenteVisualizacao}" styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_tipoDocumento}" />
	<h:outputText rendered="#{!MovContaControle.existeMaisDeUmMovContaRateio && !MovContaControle.somenteVisualizacao}" styleClass="tituloCampos" value="#{MovContaControle.movContaRateioVO.tipoDocumentoVO.descricao}" />
    

<!-- descricao -->	
	<h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_descricao}" />
	<h:outputText  styleClass="tituloCampos" value="#{MovContaControle.movContaVO.descricao}" />
    

<!-- valor -->    
    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_valor}" />
    <h:outputText  styleClass="tituloCampos" value="#{MovContaControle.movContaVO.valor}" >
    <f:converter converterId="FormatadorNumerico" />
    </h:outputText>
    
    
<!-- data quitacao -->
    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Lancamento_dataQuitacao}"/>
    <h:outputText styleClass="tituloCampos" value="#{MovContaControle.movContaVO.dataQuitacao_Apresentar}"/>
      


<!-- observacoes -->   
    <h:outputText styleClass="tituloCampos"
                  rendered="#{not empty MovContaControle.movContaVO.observacoes_Apresentar}"
                  value="#{msg_aplic.prt_Finan_Lancamento_observacoes}" />
    <h:panelGroup styleClass="classEsquerda" rendered="#{not empty MovContaControle.movContaVO.observacoes_Apresentar}">
    <h:outputText styleClass="tituloCampos"
                  escape="false" value="#{MovContaControle.movContaVO.observacoes_Apresentar}" />
    </h:panelGroup>

<%--plano de conta--%>
    <h:outputText styleClass="tituloCampos"
                  rendered="#{MovContaControle.chequeDevolvido}"
                  value="Plano de Contas" />
    <h:panelGroup rendered="#{MovContaControle.chequeDevolvido}">
        <table cellpadding="0" cellspacing="0">
            <tr valign="top">
                <td>
                    <h:inputText id="nomePlanoSelecionado"
                                 size="50"
                                 maxlength="50"
                                 tabindex="#{PlanoContasControle.tabIndexSuggestionBoxPlanoContas}"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{PlanoContasControle.planoNome}"  >
                        <a4j:support event="onchange" action="#{PlanoContasControle.setarPlanoPaiVazio}"
                                     oncomplete="#{PlanoContasControle.onCompleteChangeSuggestionBoxPlanoContas}"/>
                    </h:inputText>

                    <rich:suggestionbox   height="200" width="400"
                                          for="nomePlanoSelecionado"
                                          status="statusInComponent"
                                          immediate="true"
                                          suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                                          minChars="1"
                                          nothingLabel="Nenhum Plano de Contas encontrado"
                                          rowClasses="linhaImpar, linhaPar"
                                          var="result"  id="suggestionResponsavel" >
                        <a4j:support event="onselect"
                                     reRender="panelCampos, nomePlanoSelecionado"
                                     focus="nomePlanoSelecionado"
                                     action="#{PlanoContasControle.selecionarPlanoContas}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome"  styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.descricaoCurtaComCodigo}" />
                        </h:column>
                        <h:column >
                            <f:facet name="header">
                                <h:outputText value="Tipo" styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.tipoPadrao.descricao}" />
                        </h:column>
                    </rich:suggestionbox>
                </td>
                <td> &nbsp;<a4j:commandLink action="#{PlanoContasControle.verificarConsultaLancamento}"
                                            reRender="modalPlanos"
                                            id="btAddPlano" value="Consultar"
                                            oncomplete="Richfaces.showModalPanel('modalPlanos')"/></td>
            </tr>
        </table>
    </h:panelGroup>

    <h:outputText rendered="#{MovContaControle.chequeDevolvido}" styleClass="tituloCampos"
                  value="#{msg_aplic.prt_Finan_Lancamento_centroCusto}"/>
    <h:panelGroup rendered="#{MovContaControle.chequeDevolvido}">
        <table cellpadding="0" cellspacing="0">
            <tr valign="top">
                <td>
                    <h:inputText  id="nomeCentroSelecionado"
                                  size="50"
                                  maxlength="50"
                                  tabindex="#{CentroCustosControle.tabIndexSuggestionBoxCentroCustos}"
                                  onblur="blurinput(this);"
                                  onfocus="focusinput(this);"
                                  styleClass="form"
                                  value="#{CentroCustosControle.centroNome}" >
                        <a4j:support event="onchange" action="#{CentroCustosControle.setarCentroVazio}"
                                     oncomplete="#{CentroCustosControle.onCompleteChangeSuggestionBoxCentroCustos}"/>
                    </h:inputText>

                    <rich:suggestionbox   height="200" width="400"
                                          for="nomeCentroSelecionado"
                                          status="statusInComponent"
                                          immediate="true"
                                          suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                          minChars="1"
                                          nothingLabel="Nenhum Centro de Custos encontrado"
                                          rowClasses="linhaImpar, linhaPar"
                                          var="result"  id="suggestionCentroCusto">
                        <a4j:support event="onselect"
                                     focus="nomeCentroSelecionado"
                                     reRender="panelCampos, nomeCentroSelecionado"
                                     action="#{CentroCustosControle.selecionarCentroCusto}">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome"  styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.descricaoCurtaComCodigo}" />
                        </h:column>
                    </rich:suggestionbox>
                </td>
                <td> &nbsp;<a4j:commandLink reRender="modalCentros"
                                            id="btAddCentro" value="Consultar"
                                            oncomplete="Richfaces.showModalPanel('modalCentros')"/></td>
            </tr>
        </table>
    </h:panelGroup>


    <h:outputText styleClass="tituloCampos" value="Cheques: "
                  rendered="#{not empty MovContaControle.movContaVO.cheques}"></h:outputText>

    <h:dataTable rendered="#{not empty MovContaControle.movContaVO.cheques}" width="600px"
                 columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, direita"
                 value="#{MovContaControle.movContaVO.cheques}" var="ch"
    >
        <h:column>
            <f:facet name="header">
                <h:outputText value="Banco" styleClass="textsmall" />
            </f:facet>
            <h:outputText styleClass="tituloCampos" value="#{ch.banco.nome}"></h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Agência" styleClass="textsmall" />
            </f:facet>

            <h:outputText styleClass="tituloCampos" value="#{ch.agencia}"></h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Conta" styleClass="textsmall" />
            </f:facet>
            <h:outputText styleClass="tituloCampos" value="#{ch.conta}"></h:outputText>

        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Número" styleClass="textsmall" />
            </f:facet>
            <h:outputText styleClass="tituloCampos" value="#{ch.numero}"></h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Compensação" styleClass="textsmall" />
            </f:facet>
            <h:outputText styleClass="tituloCampos" value="#{ch.dataCompensacao_Apresentar}"></h:outputText>
        </h:column>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Valor" styleClass="textsmall" />
            </f:facet>
            <h:outputText styleClass="tituloCampos" value="#{ch.valor}"><f:converter converterId="FormatadorNumerico" /></h:outputText>
        </h:column>

    </h:dataTable>

</h:panelGrid>

<h:panelGrid width="100%" id="gridBotoesLancamento" columnClasses="colunaCentralizada" columns="1">
    <h:panelGroup>
        <a4j:commandButton id="okDevolucaoCheque"
                           reRender="modalAlteracoesAgendamento,panelAutorizacaoFuncionalidade, panelMensagem, panelCampos,gridBotoesLancamento"
                           oncomplete="#{MovContaControle.mensagemNotificar}"
                           value="Gravar"
                           action="#{MovContaControle.verificarConfiguracaoSolicitaSenha}"
                           styleClass="botoes nvoBt btSec btPerigo">
        </a4j:commandButton>

        <a4j:commandLink id="consultarLogDevolucaoCheque" action="#{MovContaControle.realizarConsultaLogObjetoSelecionado}"
                         reRender="form"
                         rendered="#{MovContaControle.mostrarBotoesTelaLancamento}"
                         oncomplete="#{MovContaControle.oncompleteLog}"
                         style="display: inline-block; padding: 8px 15px;"
                         title="Visualizar Log" accesskey="4" styleClass="botoes nvoBt btSec">
            <i class="fa-icon-list"></i>
        </a4j:commandLink>
    </h:panelGroup>
</h:panelGrid>

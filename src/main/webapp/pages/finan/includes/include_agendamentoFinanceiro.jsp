<%-- 
    Document   : include_agendamentoFinanceiro
    Created on : 07/12/2011, 10:25:51
    Author     : <PERSON><PERSON><PERSON>
--%>

<rich:modalPanel id="agendamentoFinanceiroPanel" width="750" shadowOpacity="true" autosized="true"
                 styleClass="novaModal">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_Finan_Agendamento_tituloIncluir}"
                          rendered="#{AgendamentoFinanceiroControle.incluir}"/>
            <h:outputText value="#{msg_aplic.prt_Finan_Agendamento_tituloAlterar}"
                          rendered="#{not AgendamentoFinanceiroControle.incluir}"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink1"/>
            <rich:componentControl for="agendamentoFinanceiroPanel"
                                   attachTo="hidelink1" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <a4j:form id="agendamentoFinanceiroForm">
        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" width="100%" columnClasses="colunaEsquerda">
            <%-- c�d. agendamento --%>
            <h:outputText styleClass="tituloCampos" value="C�digo:"/>
            <h:inputText id="codAgendamentoFinan" styleClass="campos" size="2" maxlength="50"
                         disabled="true"
                         value="#{AgendamentoFinanceiroControle.agenda.codigo}"/>
            <%-- descricao --%>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_descricao}"/>
            <h:inputText id="descricao" styleClass="campos" size="40" maxlength="50"
                         value="#{AgendamentoFinanceiroControle.agenda.descricao}"/>

            <%-- frequencia --%>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_frequencia}"/>
            <h:panelGroup>
                <h:panelGroup layout="block" styleClass="cb-container" style=" font-size: 11px !important;">
                    <h:selectOneMenu value="#{AgendamentoFinanceiroControle.agenda.frequencia}"
                                     disabled="#{not AgendamentoFinanceiroControle.incluir}" styleClass="form">
                        <f:selectItems value="#{AgendamentoFinanceiroControle.listaSelectItemFrequencia}"/>
                        <a4j:support event="onchange" reRender="agendamentoFinanceiroForm" action="#{AgendamentoFinanceiroControle.selecionaFrequencia}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>



            <h:outputText styleClass="tituloCampos" value="Gerar parcela somente em dia �til:"
                          rendered="#{AgendamentoFinanceiroControle.incluir or !AgendamentoFinanceiroControle.alteracaoEmParcelasFixas}"/>
            <h:panelGroup rendered="#{AgendamentoFinanceiroControle.incluir or !AgendamentoFinanceiroControle.alteracaoEmParcelasFixas}">
                <h:panelGroup layout="block" styleClass="chk-fa-container inline ">
                    <h:selectBooleanCheckbox
                            value="#{AgendamentoFinanceiroControle.agenda.gerarApenasDiasUteis}"/>
                    <span style="    margin-top: -5px;"/>
                </h:panelGroup>
            </h:panelGroup>

            <%-- dia do vencimento --%>
            <h:outputText styleClass="tituloCampos" value="Pr�ximo Vencimento"/>
            <h:inputText id="diaVencimento" styleClass="campos" size="3" maxlength="3"
                         readonly="#{not AgendamentoFinanceiroControle.permiteEscolherData or AgendamentoFinanceiroControle.alteracaoEmParcelasFixas}"
                         value="#{AgendamentoFinanceiroControle.agenda.diaVencimento}">
                <a4j:support event="onchange" action="#{AgendamentoFinanceiroControle.alterarVencimento}" reRender="agendamentoFinanceiroForm"/>
            </h:inputText>

            <%-- proximo vencimento --%>
            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_proximoVencimento}"/>
            <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;">
                <rich:calendar id="proximoVencimento" inputSize="10" inputClass="form"
                               readonly="#{not AgendamentoFinanceiroControle.permiteEscolherData or AgendamentoFinanceiroControle.alteracaoEmParcelasFixas}"
                               value="#{AgendamentoFinanceiroControle.agenda.proximoVencimento}"
                               oninputblur="blurinput(this);" oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                               datePattern="dd/MM/yyyy" buttonIcon="/imagens_flat/calendar-button.svg"
                               enableManualInput="true" zindex="2" showWeeksBar="false"/>
                <h:message for="proximoVencimento" styleClass="mensagemDetalhada"/>
            </h:panelGroup>

            <h:outputText styleClass="tituloCampos" value="Regra:"/>
            <h:panelGroup id="panelTipoAgend">
            <h:selectOneRadio id="radioAgendamento"
                              disabled="#{not AgendamentoFinanceiroControle.incluir}"
                              styleClass="tituloCampos"
                              value="#{AgendamentoFinanceiroControle.tipoVisualizacaoAgendamentoFinanceiro}">
                <f:selectItems value="#{AgendamentoFinanceiroControle.tiposVisualizacao}" />
                <a4j:support event="onclick"
                             reRender="agendamentoFinanceiroForm"
                             action="#{AgendamentoFinanceiroControle.acoesRadioBoxTipoAgendamento}"></a4j:support>
            </h:selectOneRadio>
            </h:panelGroup>

            <%-- opcoes AT� DETERMINADO VENCIMENTO --%>
            <h:outputText styleClass="tituloCampos" value="Vencimento da �ltima parcela"
                          rendered="#{AgendamentoFinanceiroControle.tipoAteDeterminadoVencimento}"/>
            <h:panelGroup layout="block" rendered="#{AgendamentoFinanceiroControle.tipoAteDeterminadoVencimento}">
                <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;" rendered="#{not AgendamentoFinanceiroControle.incluir}">
                    <rich:calendar id="vencimentoUltima" inputSize="10" inputClass="form" buttonIcon="/imagens_flat/calendar-button.svg"
                                   readonly="#{AgendamentoFinanceiroControle.agenda.usaParcelasFixas}"
                                   value="#{AgendamentoFinanceiroControle.agenda.vencimentoUltimaParcela}"
                                   oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data_Nula(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   ondateselect="return confirm('Se a data de vencimento foi alterada ela excluir� todos os lan�amentos N�O QUITADOS posteriores a data escolhida. Deseja Continuar?');"
                                   disabled="#{AgendamentoFinanceiroControle.agenda.dataCalendarioPreenchida}"
                                   enableManualInput="true" zindex="2" showWeeksBar="false">
                    </rich:calendar>
                    <h:message for="vencimentoUltima" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
                <h:panelGroup styleClass="dateTimeCustom"  style="font-size: 11px;" rendered="#{AgendamentoFinanceiroControle.incluir}">
                    <rich:calendar id="vencimentoUltimaIncluir" inputSize="10" inputClass="form" buttonIcon="/imagens_flat/calendar-button.svg"
                                   readonly="#{AgendamentoFinanceiroControle.agenda.usaParcelasFixas}"
                                   value="#{AgendamentoFinanceiroControle.agenda.vencimentoUltimaParcela}"
                                   oninputblur="blurinput(this);" oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data_Nula(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   
                                   enableManualInput="true" zindex="2" showWeeksBar="false">
                       
                    </rich:calendar>
                    
                    <h:message for="vencimentoUltimaIncluir" styleClass="mensagemDetalhada"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGrid columns="1" width="100%" rowClasses="linhaImpar, linhaPar">
            <%-- opcoes INTERVALO DE PARCELAS --%>
            <h:panelGroup id="informacoes" rendered="#{AgendamentoFinanceiroControle.tipoIntervaloParcelas}">
                <h:panelGrid columns="1" width="100%" style="background-color: #EEEEEE;">
                    <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_Finan_Agendamento_aviso}"/>

                    <h:panelGroup style="font-size: 11px; padding-top: 10px; display: block;">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_gerar} "/>
                        <h:inputText id="qtdeParcelas" style="margin-top: -6px;margin-right: 5px;" styleClass="campos" size="2" maxlength="3"
                                     readonly="#{AgendamentoFinanceiroControle.alteracaoEmParcelasFixas}"
                                     value="#{AgendamentoFinanceiroControle.agenda.qtdeParcelasGerar}">
                            <a4j:support event="onchange" action="#{AgendamentoFinanceiroControle.alterarQtdeDiasParaGerar}" reRender="agendamentoFinanceiroForm"/>
                        </h:inputText>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_novaGeracao}"/>
                    </h:panelGroup>

                    <h:panelGroup style="font-size: 11px; padding-top: 10px; display: block;">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_faltando} "/>
                        <h:inputText id="qtdeDiasAntecedencia" style="margin-top: -6px; margin-right: 5px;" styleClass="campos" size="2" maxlength="3"
                                     readonly="#{AgendamentoFinanceiroControle.alteracaoEmParcelasFixas}"
                                     value="#{AgendamentoFinanceiroControle.agenda.qtdeDiasNovaGeracao}"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_paraVencer}"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>

            <%-- opcoes PARCELAS FIXAS --%>
            <h:panelGroup id="grupoFixo" rendered="#{AgendamentoFinanceiroControle.tipoQtdParcelasFixas}">
                <h:panelGrid columns="1" width="100%" style="background-color: #EEEEEE; padding-left:25px;">
                    <h:outputText styleClass="tituloCampos" value="* #{msg_aplic.prt_Finan_Agendamento_aviso}"/>

                    <%-- parcela inicial e parcela final --%>
                    <h:panelGroup style="font-size: 10px;">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_parcelaInicial}"/>
                        <rich:spacer width="10px"/>
                        <h:inputText id="parcIni" styleClass="campos" size="3" maxlength="3"
                                     readonly="#{not AgendamentoFinanceiroControle.incluir}"
                                     value="#{AgendamentoFinanceiroControle.agenda.parcelaInicial}"/>
                        <rich:spacer width="10px"/>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_parcelaFinal}"/>
                        <rich:spacer width="10px"/>
                        <h:inputText id="parcFim" styleClass="campos" size="3" maxlength="3"
                                     readonly="#{not AgendamentoFinanceiroControle.incluir}"
                                     value="#{AgendamentoFinanceiroControle.agenda.parcelaFinal}">
                            <a4j:support event="onchange" reRender="agendamentoFinanceiroForm" 
                                         action="#{AgendamentoFinanceiroControle.atualizaVencimento}"/>
                        </h:inputText>
                    </h:panelGroup>

                    <%-- layout --%>
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_Agendamento_layoutDescricao}"/>
                    <h:selectOneRadio styleClass="tituloCampos" value="#{AgendamentoFinanceiroControle.agenda.layoutDescricao}"
                                      disabled="#{not AgendamentoFinanceiroControle.incluir}">
                        <f:selectItems value="#{AgendamentoFinanceiroControle.listaSelectItemLayout}"/>
                    </h:selectOneRadio>
                </h:panelGrid>
            </h:panelGroup>
        </h:panelGrid>

        <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens" style="margin-top:10px;">
            <h:outputText styleClass="mensagem"  value="#{AgendamentoFinanceiroControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" value="#{AgendamentoFinanceiroControle.mensagemDetalhada}"/>

            <h:outputText styleClass="tituloCampos"
                          style="font-weight: normal"
                          value="*Em agendamentos do tipo parcela fixa, voc� s� pode alterar a descri��o."
                          rendered="#{!AgendamentoFinanceiroControle.incluir and AgendamentoFinanceiroControle.alteracaoEmParcelasFixas}"/>

        </h:panelGrid>

        <h:panelGrid columns="1" width="100%" style="margin-top:10px;" columnClasses="centralizado">
            <h:panelGroup id="panelBotoes">


                <a4j:commandButton reRender="panelMensagem, formLanc, agendamentoFinanceiroForm"  styleClass="botoes nvoBt"
                                   rendered="#{not AgendamentoFinanceiroControle.alteracaoEmParcelasFixas && not AgendamentoFinanceiroControle.incluir}"
                                   action="#{AgendamentoFinanceiroControle.agendar}"
                                   oncomplete="#{AgendamentoFinanceiroControle.fechaAgendamento}"
                                   id="gravarAgendamento" value="Gravar"/>

                <a4j:commandButton reRender="panelMensagem, formLanc, agendamentoFinanceiroForm"  styleClass="botoes nvoBt btSec"
                                   rendered="#{not AgendamentoFinanceiroControle.incluir}"
                                   action="#{AgendamentoFinanceiroControle.gravarPerpetuando}"
                                   oncomplete="#{AgendamentoFinanceiroControle.fechaAgendamento}"
                                   value="Gravar alterando vencimentos futuros"/>

                <a4j:commandButton reRender="panelMensagem, formLanc, agendamentoFinanceiroForm" styleClass="botoes nvoBt"
                                   id="gravarAgendamentoFin"
                                   rendered="#{AgendamentoFinanceiroControle.incluir}"
                                   action="#{AgendamentoFinanceiroControle.agendar}"
                                   value="Gravar"
                                   oncomplete="#{AgendamentoFinanceiroControle.fechaAgendamento}"/>
            </h:panelGroup>
        </h:panelGrid>
       <script>
           carregarMaskInput();
       </script>
    </a4j:form>
</rich:modalPanel>

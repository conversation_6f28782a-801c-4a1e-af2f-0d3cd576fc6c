     <a4j:outputPanel>
        <rich:modalPanel id="modalConfirmaExclusaoRateio" autosized="false" width="400" height="130"  shadowOpacity="true">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Confirma��o"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkmodalConfirmaExclusaoRateio"/>
                    <rich:componentControl for="modalConfirmaExclusaoRateio" attachTo="hidelinkmodalConfirmaExclusaoRateio" operation="hide"  event="onclick" />
                </h:panelGroup>
            </f:facet>
            <h:form id="formConfirmaExclusaoRateio">
                <rich:panel>
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_confirmaExclusaoRateio}"/>
                    
                </rich:panel>
                <center>
                    <a4j:commandButton id="sim"
                                       action="#{MovContaControle.removerMovRateio}"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaExclusaoRateio');"
                                       image="/imagens/botaoSim.png" 
                                       reRender="formLanc:rateioGrid"
                                       styleClass="botoes">
                    </a4j:commandButton>
                    <rich:spacer width="30px;"></rich:spacer>
                    <a4j:commandButton id="nao"  
                    				   status="statusInComponent"
                    				   image="/imagens/botaoNao.png"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaExclusaoRateio');">
					</a4j:commandButton>
              	</center>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>
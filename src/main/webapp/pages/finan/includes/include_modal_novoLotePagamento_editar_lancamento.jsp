<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalNovoLotePagamentoEdicaoLancamento" autosized="true" styleClass="novaModal"
                     minWidth="550" width="550" minHeight="300" height="300" top="100" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Editar forma de pagamento"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <h:form id="formNovoLotePagamentoEdicaoLancamento">
            <h:panelGrid width="100%" columns="2" cellpadding="2" columnClasses="classEsquerdaPadding, classDireita"
                         rowClasses="linhaPar, linhaImpar">

                <%--CÓDIGO--%>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Código interno da conta"
                              value="Código:"/>
                <h:outputText styleClass="tituloCampos tooltipster"
                              title="Código interno da conta"
                              style="margin-top: 9px;"
                              value="#{MovContaControle.movContaEditarLote.codigo}"/>

                <%--DESCRIÇÃO--%>
                <h:outputText styleClass="tituloCampos"
                              value="Descrição:"/>
                <h:outputText styleClass="tituloCampos"
                              style="margin-top: 9px;"
                              value="#{MovContaControle.movContaEditarLote.descricao}"/>

                <%--FORNECEDOR--%>
                <h:outputText styleClass="tituloCampos"
                              value="Fornecedor:"/>
                <h:outputText styleClass="tituloCampos"
                              style="margin-top: 9px;"
                              value="#{MovContaControle.movContaEditarLote.pessoaVO.nome}"/>

                <%--VALOR--%>
                <h:outputText styleClass="tituloCampos"
                              value="Valor:"/>
                <h:outputText styleClass="tituloCampos"
                              style="margin-top: 9px;"
                              value="#{MovContaControle.movContaEditarLote.valor_Apresentar}"/>

                <%--FORMA DE PAGAMENTO--%>
                <h:outputText styleClass="tituloCampos"
                              value="Forma de pagamento:"/>
                <h:panelGroup layout="block" styleClass="cb-container">
                    <h:selectOneMenu value="#{MovContaControle.movContaRateioEditarLote.formaPagamentoVO.codigo}"
                                     onblur="blurinput(this);"
                                     onfocus="focusinput(this);">
                        <f:selectItems value="#{MovContaControle.listaSelectItemFormaPagamentoNovoLote}"/>
                        <a4j:support event="onchange" action="#{MovContaControle.setarFormaPgtoEditarLancLote}"
                                     reRender="formNovoLotePagamentoEdicaoLancamento"/>
                    </h:selectOneMenu>
                </h:panelGroup>

                <!-- Payload Pix -->
                <h:outputText styleClass="tituloCampos tooltipster"
                              id="payLoadPixEdicaoLancNvLote"
                              rendered="#{MovContaControle.exibirPayloadPixEdicaoLancamento}"
                              title="Payload do pix a ser pago, conhecido também como 'Pix Copia e Cola'"
                              value="Payload do Pix:"/>
                <h:inputTextarea id="inputpPayLoadPixEdicaoLancNvLote" style="width: 300px;height: 80px"
                                 rendered="#{MovContaControle.exibirPayloadPixEdicaoLancamento}"
                                 styleClass="form" value="#{MovContaControle.movContaEditarLote.payloadPix}">
                </h:inputTextarea>

                <%--CPF/CNPJ--%>
                <h:outputText rendered="#{MovContaControle.exibirCodigoBarrasEdicaoLancamento}"
                              styleClass="tituloCampos"
                              value="CPF/CNPJ beneficiário:"/>
                <h:panelGroup rendered="#{MovContaControle.exibirCodigoBarrasEdicaoLancamento}">
                    <h:inputText styleClass="tituloCampos tooltipster"
                                 onkeypress="return mascara(this.form, this.id, '99999999999999', event);"
                                 maxlength="18"
                                 onkeyup="somenteNumeros(this);"
                                 onblur="somenteNumeros(this);"
                                 style="display: block; width: 130px;"
                                 title="Informe o número do CPF ou do CNPJ da conta bancária do fornecedor"
                                 value="#{MovContaControle.movContaEditarLote.cpfOuCnpjBeneficiario}"/>
                </h:panelGroup>

                <!-- Conta de consumo -->
                <h:outputText styleClass="tituloCampos tooltipster"
                              id="contaConsumoEdicaoLancNvLote"
                              rendered="#{MovContaControle.exibirCodigoBarrasEdicaoLancamento}"
                              title="#{MovContaControle.titleContasConsumoExplicacao}"
                              value="Conta de consumo:"/>
                <h:selectBooleanCheckbox value="#{MovContaControle.movContaEditarLote.contaDeConsumo}"
                                         styleClass="tooltipster"
                                         title="#{MovContaControle.titleContasConsumoExplicacao}"
                                         rendered="#{MovContaControle.exibirCodigoBarrasEdicaoLancamento}">
                </h:selectBooleanCheckbox>

                <!-- Codigo barras -->
                <h:outputText styleClass="tituloCampos tooltipster"
                              id="codBarrasEdicaoLancNvLote"
                              rendered="#{MovContaControle.exibirCodigoBarrasEdicaoLancamento}"
                              title="Código de barras do boleto a ser pago"
                              value="Código de barras:"/>
                <h:inputText id="inputCodBarrasEdicaoLancNvLote" onblur="blurinput(this);" onfocus="focusinput(this);"
                             style="width: 100%;"
                             rendered="#{MovContaControle.exibirCodigoBarrasEdicaoLancamento}"
                             styleClass="form" value="#{MovContaControle.movContaEditarLote.codigoBarras}">
                </h:inputText>

            </h:panelGrid>

            <%--Campos Transferência Bancária--%>
            <h:panelGroup id="formContaBancariaFornecedorEdicaoLancamento"
                          rendered="#{MovContaControle.exibirContaBancariaEdicaoLancamento}">
                <h:panelGrid width="100%"
                             columns="2" cellpadding="2" style="text-align-last: right; margin-top: 5px;"
                             id="panelGridcpfoucnpj"
                             columnClasses="classEsquerdaPadding, classDireita"
                             rowClasses="linhaPar, linhaImpar">
                    <%--CPF/CNPJ--%>
                    <h:outputText styleClass="tituloCampos" value="CPF/CNPJ:"/>
                    <h:panelGroup>
                        <h:inputText styleClass="tituloCampos tooltipster"
                                     onkeypress="return mascara(this.form, this.id, '99999999999999', event);"
                                     maxlength="18"
                                     onkeyup="somenteNumeros(this);"
                                     onblur="somenteNumeros(this);"
                                     style="display: block; width: 120px;"
                                     title="Informe o número do CPF ou do CNPJ da conta bancária do fornecedor"
                                     value="#{MovContaControle.movContaEditarLote.contaBancariaFornecedorVO.cpfOuCnpj}"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid width="100%"
                             columns="2" cellpadding="2" style="text-align-last: right; margin-top: 5px;"
                             id="panelGrid0000"
                             columnClasses="classEsquerdaPadding, classDireita"
                             rowClasses="linhaPar, linhaImpar">
                    <%--BANCO--%>
                    <h:outputText styleClass="tituloCampos" value="Banco:"/>
                    <h:panelGroup>
                        <%@include file="include_modal_novoLotePagamento_editar_lancamento_suggestion_banco.jsp" %>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid width="100%"
                             columns="2" cellpadding="2" style="margin-top: -2px;" id="panelGrid0001"
                             columnClasses="classEsquerdaPadding, classDireita">
                    <%--AGÊNCIA--%>
                    <h:outputText styleClass="tituloCampos"
                                  value="Agência:"/>
                    <h:panelGrid width="100%" columns="3" cellpadding="2"
                                 columnClasses="classEsquerdaPadding, none, classEsquerda" id="panelGrid0002">
                        <h:inputText styleClass="tituloCampos tooltipster"
                                     maxlength="5"
                                     style="margin-left: -8px; width: 113%;  margin-top: 4px;"
                                     title="Informe o número da agência bancária"
                                     value="#{MovContaControle.movContaEditarLote.contaBancariaFornecedorVO.agency_number}"/>
                        <h:outputText styleClass="tituloCampos tooltipster"
                                      style="display: flex;"
                                      value="-"/>
                        <h:inputText styleClass="tituloCampos tooltipster"
                                     maxlength="1"
                                     style="width: 30px; margin-left: -81%; display: grid; margin-top: 4px;"
                                     title="Informe o dígito da agência bancária"
                                     value="#{MovContaControle.movContaEditarLote.contaBancariaFornecedorVO.agency_digit}"/>
                    </h:panelGrid>

                    <%--CONTA--%>
                    <h:outputText styleClass="tituloCampos" style="margin-top: -16px; display: block;"
                                  value="Conta:"/>
                    <h:panelGroup id="panel000" style="margin-top: -16px; display: block;">
                        <h:panelGrid width="100%" columns="3" cellpadding="2"
                                     columnClasses="classEsquerdaPadding, none, classEsquerda" id="panelGrid0003">
                            <%--NOVO--%>
                            <h:inputText styleClass="tituloCampos tooltipster"
                                         maxlength="10"
                                         style="margin-left: -8px; width: 113%;"
                                         title="Informe o número da conta bancária"
                                         value="#{MovContaControle.movContaEditarLote.contaBancariaFornecedorVO.account_number}"/>
                            <h:outputText styleClass="tituloCampos tooltipster"
                                          style="display: flex;"
                                          value="-"/>
                            <h:inputText styleClass="tituloCampos tooltipster"
                                         maxlength="1"
                                         style="width: 30px; margin-left: -81%; display: grid;"
                                         title="Informe o dígito da conta bancária"
                                         value="#{MovContaControle.movContaEditarLote.contaBancariaFornecedorVO.account_digit}"/>
                        </h:panelGrid>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGroup>

            <h:panelGroup id="btnAtualizarLancamentoNovoLote" style="display: block">
                <h:panelGroup id="botoesLancEditar" style="display: block; text-align-last: center; margin-top: 15px;">
                    <a4j:commandButton
                            id="btnCancelarEdicaoLancLote"
                            value="Cancelar"
                            styleClass="botoes nvoBt btSec"
                            oncomplete="Richfaces.hideModalPanel('modalNovoLotePagamentoEdicaoLancamento')"/>
                    <a4j:commandButton
                            title="Salvar alterações para este lançamento"
                            reRender="formNovoLotePagamento"
                            id="btnSalvarRateioLancLote"
                            action="#{MovContaControle.salvarEdicaoLancamentoLote}"
                            value="Salvar"
                            styleClass="botoes nvoBt tooltipster"
                            oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.abrirFecharModalNovoLotePagamentoEdicaoLancamento}"/>
                </h:panelGroup>
            </h:panelGroup>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>


<style>
    .classEsquerdaPadding {
        width: 35%;
        text-align: right;
        font-weight: bold;
        vertical-align: middle;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
        text-decoration: none;
        line-height: normal;
        text-transform: none;
        color: #333;
        line-height: 150%;
        padding: 7px;
    }
</style>

<script>
    function somenteNumeros(num) {
        // Substitui tudo que não é dígito (0-9) por uma string vazia
        num.value = num.value.replace(/[^0-9]/g, '');
    }
</script>

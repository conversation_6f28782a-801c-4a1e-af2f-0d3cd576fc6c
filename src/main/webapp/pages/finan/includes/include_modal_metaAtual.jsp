<%@include file="include_imports.jsp" %>

<script type="text/javascript">
    $.noConflict();
</script>

    <rich:modalPanel id="modalPanelmetaAtual" autosized="false"
    				shadowOpacity="true" width="770" height="550">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Meta"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelink1" />
                <rich:componentControl for="modalPanelmetaAtual"
                                       attachTo="hidelink1" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formFiltrosMetaAtual" ajaxSubmit="true">
        
        
<!-- ---------------------- FIL<PERSON><PERSON> CONSULTA ------------------------------------ -->
			
			<h:panelGrid columns="9" width="100%" columnClasses="centralizado">
                    <!-- EMPRESA -->
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa}:"/>
                    <h:selectOneMenu id="empresa" styleClass="form" rendered="#{MetaFinanceiroBIControle.mostrarCampoEmpresa}"
                                     value="#{MetaFinanceiroBIControle.empresa.codigo}" >
                        <f:selectItems value="#{MetaFinanceiroBIControle.listaSelectItemEmpresa}" />
                    </h:selectOneMenu>
                    <h:outputText rendered="#{!LoginControle.permissaoAcessoMenuVO.lancarVisualizarLancamentosEmpresas}"
								  value="#{MetaFinanceiroBIControle.empresa.nome}" styleClass="tituloDemonstrativo"></h:outputText>

                    <!-- ANO -->
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_MetaFinanceiro_ano}:"/>
                    <h:inputText id="ano" styleClass="form" value="#{MetaFinanceiroBIControle.ano}" size="5"/>
                    
                    <!-- MES -->
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_MetaFinanceiro_mes}:"/>
                    <h:selectOneMenu id="mes" styleClass="form" value="#{MetaFinanceiroBIControle.mes}">
                        <f:selectItems value="#{MetaFinanceiroBIControle.listaSelectItemMeses}" />
                    </h:selectOneMenu>

                    <!-- DESCRICAO -->
                    <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_MetaFinanceiro_descricao}:"/>
                    <h:inputText id="descricao" styleClass="form" value="#{MetaFinanceiroBIControle.descricao}" />

                    <!-- BOTAO CONSULTA -->
                    <a4j:commandButton id="consultar" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                 action="#{MetaFinanceiroBIControle.consultarMetas}" 
                                 image="/imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"
                                 reRender="listaOutros"/>
                </h:panelGrid>
                <rich:spacer height="10"/>
		
		 <!-- RESULTADO DA CONSULTA -->
                <rich:dataTable id="listaOutros" width="100%" 
                				columnClasses="colunaCentralizada"
                				value="#{MetaFinanceiroBIControle.metas}" var="meta">
                    <!-- EMPRESA -->
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa}" />
                        </f:facet>
                        <h:outputText value="#{meta.empresa.nome}"/>
                    </h:column>

                    <!-- MES -->
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_mes}" />
                        </f:facet>
                        <h:outputText value="#{meta.mes.descricao}"/>
                    </h:column>

                    <!-- ANO -->
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_ano}" />
                        </f:facet>
                        <h:outputText value="#{meta.ano}"/>
                    </h:column>

                    <!-- DESCRICAO -->
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_descricao}" />
                        </f:facet>
                        <h:outputText value="#{meta.descricao}"/>
                    </h:column>

                    <!-- META 1 -->
                    <rich:column style="background-color: #{meta.valores[0].cor}; ">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta1}" />
                        </f:facet>
                        <h:outputText value="#{meta.valores[0].valor}" style="font-weight: bold; color: #{meta.valores[0].corTexto};">
	                        	<f:converter converterId="FormatadorNumerico" />
	                        </h:outputText>
                    </rich:column>

                    <!-- META 2 -->
                    <rich:column style="background-color: #{meta.valores[1].cor}; ">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta2}"/>	
                        </f:facet>
                        <h:outputText value="#{meta.valores[1].valor}" style="font-weight: bold;color: #{meta.valores[1].corTexto};">
                        	<f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>

                    <!-- META 3 -->
                    <rich:column style="background-color: #{meta.valores[2].cor}; ">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta3}"/>
                        </f:facet>
                        <h:outputText value="#{meta.valores[2].valor}" style="font-weight: bold;color: #{meta.valores[2].corTexto};">
                        	<f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>

                    <!-- META 4 -->
                    <rich:column style="background-color: #{meta.valores[3].cor}; ">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta4}"/>	
                        </f:facet>
                        <h:outputText value="#{meta.valores[3].valor}" style="font-weight: bold; color: #{meta.valores[3].corTexto};">
                        	<f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>

                    <!-- META 5 -->
                    <rich:column style="background-color: #{meta.valores[4].cor}; ">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta5}"/>
                        </f:facet>
                        <h:outputText value="#{meta.valores[4].valor}" style="font-weight: bold; color: #{meta.valores[4].corTexto};">
                        	<f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>

                    <!-- OPCOES -->
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_opcoes}" />
                        </f:facet>
                        <a4j:commandButton styleClass="botoes" image="../../imagens/botaoEditar.png"
                                           oncomplete="Richfaces.hideModalPanel('modalPanelmetaAtual');"
                                           action="#{MetaFinanceiroBIControle.selecionarMeta}"
                                           reRender="panelBIMetasFinan"/>
                    </h:column>
                </rich:dataTable>
		
		</a4j:form>
        </rich:modalPanel>



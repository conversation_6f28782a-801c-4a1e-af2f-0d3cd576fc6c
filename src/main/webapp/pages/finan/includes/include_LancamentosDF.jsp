<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:set var="contextoFinan" value="${pageContext.request.contextPath}" scope="session" />
<head>
    <script type="text/javascript" language="javascript" src="${contextoFinan}/script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="${contextoFinan}/hoverform.js"></script>
<script type="text/javascript" src="${contextoFinan}/script/demonstrativoFinan.js"></script>
<link href="${contextoFinan}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="${contextoFinan}/css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">

<link href="${contextoFinan}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>


<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<script>
    function selecionarPlano(descricao) {
        document.getElementById('form:nomePlanoSelecionado').value = descricao;
    }

    function selecionarCentro(descricao) {
        document.getElementById('form:nomeCentroSelecionado').value = descricao;
    }

</script>

<f:view>
    <c:set var="titulo" scope="session" value="${MovContaControle.tituloTelaLancamento}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-ver-lancamentos/"/>
    <c:set var="modulo" scope="request" value="financeiroWeb"/>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../../../topoReduzido_material.jsp"/>
        </f:facet>


        <a4j:form id="form">
            <!-- Inclui o elemento HEAD da página -->
            <head>
                <%@include file="include_head_finan.jsp" %>
                <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
                <script type="text/javascript" language="javascript"
                        src="${contextoFinan}/script/vanilla-masker.min.js"></script>
            </head>
            <body>
            <c:if test="${MovContaControle.somenteVisualizacao}">
                <%@include file="include_visualizacaoLancamento.jsp" %>
            </c:if>
            <c:if test="${!MovContaControle.somenteVisualizacao}">
                <%@include file="include_TelaLancamentosForm.jsp" %>
            </c:if>
            </body>

        </a4j:form>

    </h:panelGrid>
    <%@include file="../pessoaSimplificado.jsp" %>
    <%@include file="../cidade.jsp" %>
    <%@include file="include_modalSelecaoPlanoConta.jsp" %>
    <%@include file="include_modalSelecaoCentroCusto.jsp" %>
    <%@include file="include_modal_confirmaExclusaoRateio.jsp" %>
    <%@include file="include_modal_excessoValorRateio.jsp" %>
    <%@include file="include_modal_estornoPagamento.jsp" %>
    <%@include file="include_modal_lancarPagamento.jsp" %>
    <%@include file="include_modal_alteracoesAgendamento.jsp" %>
    <%@include file="include_modal_pagamentoConjunto.jsp" %>
    <%@include file="include_agendamentoFinanceiro.jsp" %>
    <jsp:include page="../../../includes/autorizacao/include_autorizacao_funcionalidade.jsp"/>
    <jsp:include page="../../../includes/include_carregando_ripple.jsp" flush="true"/>

</f:view>

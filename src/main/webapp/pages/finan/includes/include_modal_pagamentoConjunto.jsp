    <rich:modalPanel id="modalVerPagamentoConjunto" autosized="true" shadowOpacity="true" width="600" height="150">
	    <f:facet name="header">
	        <h:panelGroup>
	            <h:outputText  value="Lan�amentos pagos em conjunto"/>
	        </h:panelGroup>
	    </f:facet>

	    <f:facet name="controls">
	        <h:panelGroup>
	            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkRetirarLote" />
	            <rich:componentControl for="modalVerPagamentoConjunto" attachTo="hidelinkRetirarLote" operation="hide" event="onclick" />
	        </h:panelGroup>
	    </f:facet>

	    <a4j:form id="formRetirarChequeLote">
	    	<rich:dataTable value="#{MovContaControle.movContaVO.contasAPagarConjunto}" var="movConta" width="100%"
	    	 columnClasses="esquerda, esquerda, direita">
	    		<rich:column>
	    			<f:facet name="header">
	    				<h:outputText value="Favorecido"  ></h:outputText>
	    			</f:facet>
	    			<h:outputText value="#{movConta.pessoaVO.primeiroNomeConcatenado}"  ></h:outputText>
	    		</rich:column>
	    		<rich:column>
	    			<f:facet name="header">
	    				<h:outputText value="Descri��o"  ></h:outputText>
	    			</f:facet>
	    		 <a4j:commandLink action="#{MovContaControle.preparaEdicaoConsultando}">
                        <h:outputText value="#{movConta.descricao}" ></h:outputText>
                    </a4j:commandLink>

	    		</rich:column>
	    		<rich:column>
	    			<f:facet name="header">
	    				<h:outputText value="Valor"  ></h:outputText>
	    			</f:facet>
	    			<h:outputText value="#{movConta.valor_Apresentar}" ></h:outputText>
	    		</rich:column>

	    	</rich:dataTable>
	    </a4j:form>

    </rich:modalPanel>